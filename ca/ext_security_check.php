<?php
//Include translation table
include("/home/<USER>/public_html/crmadmin/csttranslation.php");

$aAlertRecipients = array("<EMAIL>");

$sHeaders = "From: <EMAIL>";

$sRemoteAddr = $_SERVER['REMOTE_ADDR'];
$iCustomerID = (int) $_GET['CST_ID'];

function fnIsInNetwork($sNeedle, $sHaystack){
        if (strstr($sHaystack, "/")){
                //Is CIDR
                $sNetAddress = substr($sHaystack, 0, strpos($sHaystack, "/"));
                $sNetmask = substr($sHaystack, strpos($sHaystack, "/")+1);
                $sIpBinaryString = sprintf("%032b", ip2long($sNeedle));
                $sNetBinaryString = sprintf("%032b", ip2long($sNetAddress));
                return (substr_compare($sIpBinaryString, $sNetBinaryString, 0, $sNetmask) === 0);
        } else {
                if ($sNeedle == $sHaystack){
                        return true;
                }
        }
        return false;
}

$bAllowed = false;
foreach($aCustomers as $aCustomer){
	foreach($aCustomer['ips'] as $sIP){
		if (fnIsInNetwork($sRemoteAddr, $sIP)){
			$bAllowed = true;
		}
	}
}

if (!$bAllowed){
	//This customer is not approved, let IT know!
	foreach($aAlertRecipients as $sRecipient){
		mail($sRecipient, "Possible ca_ext theft-attempt from ".$sRemoteAddr, "IP: ".$sRemoteAddr."\nURL: ".$_SERVER['REQUEST_URI']."\nTime: ".strftime("%d/%m-%Y %H:%M:%S"), $sHeaders);
	}
	die("Customer not approved, please contact Secunia.");
}
?>
