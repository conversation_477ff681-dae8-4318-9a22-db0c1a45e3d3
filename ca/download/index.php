<?php
require_once "mysql.php";
$baseURL = "/download/";
$requestedURL = $_SERVER['REQUEST_URI'];
$requestedURLbase = substr($requestedURL, strlen($baseURL));
$realmName = "Flexera Software Download Area";

#mysql_connect("192.168.100.100", "crm", ":)");
mysql_connect("hsk-commondb-1.flexinet.dk", "crm", ":)");
mysql_select_db("crm");

$query = mysql_query("SELECT file_filename, product_bw FROM download_files LEFT JOIN download_products ON product_id = file_product");
while($row = mysql_fetch_assoc($query)){
	$files[$row['file_filename']] = $row['product_bw'];
	$displayfiles[] = $row['file_filename'];
}

function triggerError($subject, $content){
	$headers = 'From: <EMAIL>';
	mail("<EMAIL>", $subject, $content, $headers);
	echo "<html><head><title>404: File not found</title></head><body><h1>File not found</h1>The file you requested does not exist.<br><br><NAME_EMAIL> for support.</body></html>";
}

function checkUser($username, $password){
	$query = mysql_query("SELECT * FROM download_users WHERE user_username='".$username."' AND user_password=OLD_PASSWORD('".$password."')");
	if (mysql_num_rows($query) == 1){
		return true;
	}
	return false;
}

if (!isset($_SERVER['PHP_AUTH_USER'])){
	header("WWW-Authenticate: Basic realm=\"".$realmName."\"");
	header("HTTP/1.0 401 Authorized");
	echo "Please authenticate";
	exit;
} else {
	if (checkUser(mysql_real_escape_string($_SERVER['PHP_AUTH_USER']), mysql_real_escape_string($_SERVER['PHP_AUTH_PW']))){
		//Login approved
		if (strstr($requestedURLbase, "/")){
			if (substr($requestedURLbase, 0, 2) == "do"){
				$filename = basename(substr($requestedURLbase, 3));
				if (file_exists("files/".$filename)){
					//Check if customer has access to the requested file
					$query = mysql_query("SELECT user_products FROM download_users WHERE user_username='".mysql_escape_string($_SERVER['PHP_AUTH_USER'])."'");
					$data = mysql_fetch_assoc($query);
					$products = (int) $data['user_products'];
					if ($files[$filename] & $products){
						header("Content-type: application/octet-stream");
						if (false === readfile("files/" . $filename)) {
							error_log("cannot open file: files/" . $filename);
						}
					} else {
						triggerError("CA-download access denied", "A customer tried downloading a file, but got access denied:\n\nIP: ".$_SERVER['REMOTE_ADDR']."\nURL: ".$_SERVER['REQUEST_URI']."\nUsername: ".$_SERVER['PHP_AUTH_USER']);
					}
				} else {
					//Mail admin
					triggerError("CA-download file not found", "A customer tried downloading a file that did not exist:\n\nIP: ".$_SERVER['REMOTE_ADDR']."\nURL: ".$_SERVER['REQUEST_URI']."\nUsername: ".$_SERVER['PHP_AUTH_USER']);
				}
			}
		} else {
			echo "<html><head><title>Flexera Software Download Area</title></head><body><h1>Flexera Software Download Area</h1>";
			$query = mysql_query("SELECT user_products FROM download_users WHERE user_username='".mysql_real_escape_string($_SERVER['PHP_AUTH_USER'])."'");
			$data = mysql_fetch_assoc($query);
			$products = (int) $data['user_products'];
			error_log("STST User access bit: ".mysql_real_escape_string($_SERVER['PHP_AUTH_USER']).": ".$products);
			foreach($displayfiles as $file){
				if ($files[$file] & $products){
					error_log("STST file OK, bit: ".$files[$file]);
					echo "<a href=\"do/".$file."\">".$file."</a><br>";
				} else {
					error_LOG("STST file NK, bit: ".$files[$file]);
				}
			}
			echo "<hr><b>All downloads are governed by these <a href=\"https://www.flexera.com/legal/clickthrough\">Terms and Conditions</a></b></body></html>";
		}
	} else {
		//Login invalid
		header("WWW-Authenticate: Basic realm=\"".$realmName."\"");
		header("HTTP/1.0 401 Authorized");
		echo "Please authenticate";
		exit;
	}
}
