// Ext.define(null, {
//     override: 'Ext.dashboard.Dashboard',
//
//     insertPortlet: function(portlet, column, row) {
//         const dashboard = this;
//         const columns = dashboard.query('dashboard-column');
//         const target  = columns[column];
// debugger;
//         if (target) {
//             return target.insert(row, portlet);
//         }
//         return null;
//     }
// });
Ext.define('Dashboard.SmartGroupCombo', {
    extend: 'Ext.form.field.ComboBox',
    xtype: 'dashboard-smartgroupcombo',
    isSmartGroupCombo: true,
    config: {
        remoteParam: null,
        portlet: null,
        smartGroupTextType: null,
        secuniaDecode: true,
        no_erase: true // custom variable so we don't erase this on refresh
    },
    allowBlank: false,
    editable: false,
    forceSelection: true,
    width: 200,
    valueField: 'id',
    displayField: 'name',
    queryMode: 'local',
    getDisplayValue: function() {
        return Ext.String.htmlDecode(this.displayTpl.apply(this.displayTplData));
    },
    listeners: {
        select: 'onComboSmartGroupSelect',
        //change: 'onComboSmartGroupChange',
        beforequery: function (qe) {
            //delete qe.combo.lastQuery; // delete the previous query in the beforequery event or set combo.lastQuery = null (this will reload the store the next time it expands)
        },
        added: function (comboBox) {
            if (comboBox.getStore().getCount()) { // If the store is empty then don't set the default group or it'll show the group's ID in the ComboBox
                comboBox.chooseDefaultPortletSmartGroup(); // When a new ComboBox is created. Select the default value.
            }
        }
    },
    store: {
        type: 'smartgroupcombostore',
        autoLoad: true
    },

    constructor: function (config) {
        var me = this;
        // Set the store depending on the SG type
        this.callParent(arguments);
        switch (me.getSmartGroupTextType()) {
            case "host" :
                me.setRemoteParam('host_smartgroup_id');
                me.getStore().getProxy().setUrl('action=smart_groups&which=menuSummary&smartGroupTextType=host&');
                break;
            case "product" :
                me.setRemoteParam('product_smartgroup_id');
                me.getStore().getProxy().setUrl('action=smart_groups&which=menuSummary&smartGroupTextType=product&');
                break;
            case "advisory" :
                me.setRemoteParam('advisory_smartgroup_id');
                me.getStore().getProxy().setUrl('action=smart_groups&which=menuSummary&smartGroupTextType=advisory&');
                break;
            default:
                console.warn("Invalid SmartGroup Portlet Type");
                break;
        }
    },

    initComponent: function () {
        var me = this;


        me.callParent(arguments);
        me.portlet = me.up('dashboard-panel');
    },

    chooseDefaultPortletSmartGroup: function () {
        var me = this,
            defaultSmartGroupValue, defaultSmartGroupTitles, position;
        if (me.portlet && me.portlet[me.remoteParam]) {
            me.setValue(parseInt(me.portlet[me.remoteParam], 10));
        } else {
            // Find the default Combobox index to select. The defaults are listed in defaultSmartGroupTitles
            defaultSmartGroupTitles = ["All Hosts", "All Products", "All Advisories"];
            position = me.getStore().findBy(function (row) {
                return (-1 !== defaultSmartGroupTitles.indexOf(row.get("name")));
            });
            if (-1 !== position) {
                // Select the default in the Combobox
                defaultSmartGroupValue = me.getStore().getAt(position).get("id");
                me.setValue(defaultSmartGroupValue);
            }
        }
    }
});