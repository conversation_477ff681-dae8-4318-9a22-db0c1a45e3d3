var terminologyLink= "<a href='http://secunia.com/community/advisories/terminology/' target='_blank'>Terminology</a>",
    criticalityTerminologySentence= "The Criticality Rating corresponds to Flexera's 5 point scale of 'Extremely, Highly, Moderately, Less', or 'Not' Critical.  See Flexera's "+ terminologyLink +" page for more on what this Criticality Rating means.",
    criticalTextArray= ['Extremely Critical', 'Highly Critical', 'Moderately Critical', 'Less Critical', 'Not Critical'],
    numWeeksForCriticalityHistory= 5 // NOTE - if changing, must also modify the corresponding value in the csiDashboard.class.php file
;

Ext.define('Dashboard.MainModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-mainmodel',

    data: {
        smartGroupCombosLoaded: [],
        defaultProfileText: ' (default)',
        defaultProfile: {},
        currentProfile: null,
        isDefaultProfile: false,
        profileName: '',
        unsavedChange: false,
        columns: 3,
        // terminologyLink: 'Terminology',
        // criticalityTerminologySentence: "The Criticality Rating corresponds to Flexera's 5 point scale of 'Extremely, Highly, Moderately, Less', or 'Not' Critical.  See Flexera's " + {'terminologyLink'} + " page for more on what this Criticality Rating means.",
        // criticalTextArray: ['Extremely Critical', 'Highly Critical', 'Moderately Critical', 'Less Critical', 'Not Critical'],
        // numWeeksForCriticalityHistory: 5, // NOTE - if changing, must also modify the corresponding value in the csiDashboard.class.php file
        portlets: [
            {
                "id": 1,
                "type": "overview",
                "portletTitle": "Overview",
                "htmlGenerator": "TODO",
                "description": "This portlet gives you an overall status of your system.  The average score is taken over all hosts.  You can further see exactly how many programs and OSs you have that are currently known to be secure, insecure or end-of-life, and you can compare these numbers with those from your system exactly 7 days ago to assist in monitoring changes."
            },
            {
                "id": 2,
                "type": "top_insecure_programs",
                "portletTitle": "Most Prevalent Insecure Software Installations",
                "htmlGenerator": "TODO",
                "description": "This portlet lists up to ten of the most prevalent software installations which are deemed to be insecure. Included is the corresponding data for each product from exactly 7 days ago to assist in monitoring changes"
            },
            {
                "id": 3,
                "type": "top_insecure_programs_score",
                "portletTitle": "Insecure Software Installations Weighted Score",
                "htmlGenerator": "TODO",
                "description": "This portlet list up to ten of the most prevalent software installations which are most critically insecure by a weighted score. This score is calculated by aggregating the advisory criticalities for all installations of a product, providing an overall status of most prevalent and most critical software installations affected by an advisory. Included is the corresponding data for each product from exactly 7 days ago to assist in monitoring changes"
            },
            {
                "id": 4,
                "type": "top_eol_programs",
                "portletTitle": "Most Prevalent End-of-Life Software Installations",
                "htmlGenerator": "TODO",
                "description": "This portlet lists up to ten of the most prevalent software installations which are deemed to be end-of-life.  Included is the corresponding data for each product from exactly 7 days ago to assist in monitoring changes"
            },
            {
                "id": 5,
                "type": "top_advisories",
                "portletTitle": "Most Critical Advisories Affecting Your Security",
                "htmlGenerator": "TODO",
                "description": "This portlet lists the top-ten most critical advisories that are relevant to software on your system.  Also included is the number of software installations affected by the given advisory, and the criticality rating of the advisory.  The rank is done first by criticality, then by the number of affected installations.  \"" + criticalityTerminologySentence + "\"  Clicking on an advisory link will show more detailed information specific to that advisory."
            },
            {
                "id": 6,
                "type": "top_latest_advisories",
                "portletTitle": "Latest Advisories Affecting Your Security",
                "htmlGenerator": "TODO",
                "description": "This portlet lists the top-ten latest released advisories that are relevant to software on your system.  Also included is the number of software installations affected by the given advisory, and the criticality rating of the advisory.  The rank is done first by criticality, then by the number of affected installations.   \"" + criticalityTerminologySentence + "\"  Clicking on an advisory link will show more detailed information specific to that advisory."
            },
            {
                "id": 19,
                "type": "top_threat_score",
                "portletTitle": "Top Threat Score Affecting Your Security",
                "htmlGenerator": "TODO",
                "description": "This portlet lists the top ten Threat Score that are relevant to software on your system.  " +
                    "Also included is the number of software installations affected by the threat score, and the criticality rating of the advisory.  " +
                    "The rank is done first by threat score, then by criticality and the number of affected installations.  " + criticalityTerminologySentence +
                    "  Clicking on an advisory link will show more detailed information specific to that advisory."
            },
            {
                "id": 7,
                "type": "chart_database_condition",
                "portletTitle": "Host Status - Time Since Last Scan",
                "htmlGenerator": "TODO",
                "description": "This portlet shows you how current the statistics for the hosts are based on the last time they were scanned.  Ideally one should strive to scan each host at least once per week."
            },
            {
                "id": 8,
                "type": "chart_software_overview_installations",
                "portletTitle": "Software Overview - Status of Installations",
                "htmlGenerator": "TODO",
                "description": "This portlet shows you the total number of software installations that are deemed to be secure, insecure or end-of-life.  We also provide the same data from exactly 7 days ago in order to monitor changes, improvements or declines in system security."
            },
            {
                "id": 9,
                "type": "chart_site_overview_installations",
                "portletTitle": "Site Overview - Insecure Software Installations",
                "htmlGenerator": "TODO",
                "description": "This portlet show the number of software installations only for the sites that have (or had last week) insecure installations present.  We display up to ten, prioritized based on higher number of insecure installations, and also include last week's data to assist in monitoring changes on a site level."
            },
            {
                "id": 10,
                "type": "chart_site_overview_system_score",
                "portletTitle": "Site Overview - Average System Score",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the average System Score of each Site, to monitor the overall security status of the systems in each Site. We display up to ten, and also include last week's data to assist in monitoring changes on a site level."
            },
            {
                "id": 11,
                "type": "chart_criticality_overview",
                "portletTitle": "Criticality Overview - Threat Profile of Vulnerabilities",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at various criticality levels.  This allows a user to see the exact number of vulnerable installations at each criticality level.  "
            },
            {
                "id": 12,
                "type": "time_to_package",
                "portletTitle": "Time from Insecure Version Detection to Update Creation",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the time elapsed since an out-of-date product was detected in a scan and the update package for the product was created and published."
            },
            {
                "id": 13,
                "type": "chart_criticality_history_level1",
                "portletTitle": "Criticality 5 Week History - Extremely Critical",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at criticality level: \"" + criticalTextArray[0] + ".\"  This allows a user to see the exact number of vulnerable installations at this criticality level.  Also included, where available, is the historical data for up to the last " + numWeeksForCriticalityHistory + " weeks.  " + criticalityTerminologySentence
            },
            {
                "id": 14,
                "type": "chart_criticality_history_level2",
                "portletTitle": "Criticality 5 Week History - Highly Critical",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at criticality level: \"" + criticalTextArray[1] + ".\"  This allows a user to see the exact number of vulnerable installations at this criticality level.  Also included, where available, is the historical data for up to the last " + numWeeksForCriticalityHistory + " weeks.  " + criticalityTerminologySentence
            },
            {
                "id": 15,
                "type": "chart_criticality_history_level3",
                "portletTitle": "Criticality 5 Week History - Moderately Critical",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at criticality level: \"" + criticalTextArray[2] + ".\"  This allows a user to see the exact number of vulnerable installations at this criticality level.  Also included, where available, is the historical data for up to the last " + numWeeksForCriticalityHistory + " weeks.  " + criticalityTerminologySentence
            },
            {
                "id": 16,
                "type": "chart_criticality_history_level4",
                "portletTitle": "Criticality 5 Week History - Less Critical",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at criticality level: \"" + criticalTextArray[3] + ".\"  This allows a user to see the exact number of vulnerable installations at this criticality level.  Also included, where available, is the historical data for up to the last " + numWeeksForCriticalityHistory + " weeks.  " + criticalityTerminologySentence
            },
            {
                "id": 17,
                "type": "chart_criticality_history_level5",
                "portletTitle": "Criticality 5 Week History - Not Critical",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the total number of installations (considering both Insecure and EOL) that are affected by advisories at criticality level: \"" + criticalTextArray[4] + ".\"  This allows a user to see the exact number of vulnerable installations at this criticality level.  Also included, where available, is the historical data for up to the last " + numWeeksForCriticalityHistory + " weeks.  " + criticalityTerminologySentence
            },
            {
                "id": 18,
                "type": "latest_activity",
                "portletTitle": "Latest Activity",
                "htmlGenerator": "TODO",
                "description": "This portlet shows the last 5 actions performed in Software Vulnerability Manager, divided by the following sections: Last Logins, Last Reports sent, Last Packages created, and Last User Management Actions"
            },
            {
                "id": 20,
                "type": "chart_vulnerability",
                "portletTitle": "Vulnerabilities Found Vs Vulnerabilities Resolved",
                "htmlGenerator": "TODO",
                "description": "This portlet shows total number of vulnerabilities found vs total number of vulnerabilities remediated for each month. Widget data is aggregated from July 2024 release."
            }
        ]
    },

    formulas: {
        canDeleteProfile: function (get) {
            if (get('dashboardProfileCombo.selection')) {
                if (get('dashboardProfileCombo.selection.default_profile')) {
                    return false;
                }
            }
            return true;
        }
    },

    stores: {
        dashboardProfiles: {
            autoLoad: true,
            pageSize: 0,
            fields: ['id', 'name', {name: 'default_profile', type: 'boolean'}, {name: 'isDefault', type: 'boolean'}],
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getProfiles&',
                reader: {
                    type: 'json',
                    rootProperty: 'profiles',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.profiles)) {
                                var response = data.profiles.map(function (item) {
                                    if (item.default_profile == "1") {
                                        item.name = item.name + ' (default)';
                                    }
                                    return item;
                                });
                                return response;
                            }
                        },
                        scope: this
                    }
                }
            }
        },
        dashboard: {
            autoLoad: false,
            pageSize: 0,
            //fields: ['id', 'name', {name: 'default_profile', type: 'boolean'}],
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getDashboard&reload=0&',
                //url: 'resources/data/getDashboard.json',//Wemerson's Dev
                reader: {
                    type: 'json',
                    rootProperty: ''
                },
                listeners: {
                    exception: function (data) {//TODO move to controller
                        //console.log('[DASHBOARD]There was a server problem fetching default portal data');
                        Ext.GlobalEvents.fireEvent('dashboard.loaded', {success: false});
                    }
                }
            },
            listeners: {
                load: 'onDashboardStoreLoad'
            }
        }
    }
});
