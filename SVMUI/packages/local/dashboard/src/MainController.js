Ext.ComponentQuery.pseudos.hasPart = function (components, selector) {
    var result = [],
        c, i, len;

    for (i = 0, len = components.length; i < len; i++) {
        c = components[i];

        if (c.config.part && c.config.part.getId() == selector) {
            result.push(c);
        }
    }

    return result;
}

Ext.define('Dashboard.MainController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-maincontroller',
    requires: [
        'Ext.window.Toast'
    ],
    config: {
        overrideDefaultProfileId: false
    },

    /*
     * Set the override default profile id i.e. the profile with this id will be
     * loaded instead of the default profile (if not loaded already)
     */
    applyOverrideDefaultProfileId: function (newValue, oldValue) {
        return isNaN(newValue) ? false : newValue;
    },

    init: function () {
        const me = this,
            LoginDetails = sfw.util.Auth.LoginDetails;

        if(LoginDetails.account.isMspUser()) {
            me.getView().down('toolbar').hide();
        } else {
            Ext.defer(function () {
                me.loadDefaultProfile();
            }, 100);
        }

    },

    loadDefaultProfile: function () {
        var me = this,
            view = me.getView(),
            vm = me.getViewModel(),
            profileName = 'No dashboard profiles defined...',
            defaultProfile = false,
            defaultProfileName = 'Select Dashboard Profile to view...',
            profileCombo = me.lookup('dashboardProfileCombo'),
            profileStore = vm.getStore('dashboardProfiles');


        view.mask('Loading...');
        Ext.Ajax.request({
            url: 'action=ajaxapi_dashboard&which=getProfiles',
            method: 'GET',
            success: function (data) {
                var response = Ext.decode(data.responseText);
                if (response.success === false) {
                    Ext.log.warn('[DASHBOARD]There was a problem fetching default portal data');
                    view.unmask();//An error occurred. Unmask //TODO: restricted user login comes here
                    return false;
                }

                if (typeof response.profiles !== 'undefined') {
                    for (var i = 0, profilesLength = response.profiles.length; i < profilesLength; i++) {
                        profileName = response.profiles[i].name;
                        if (parseInt(response.profiles[i].default_profile, 10) === 1) {

                            profileName += vm.get('defaultProfileText');
                            defaultProfileName = profileName;
                            defaultProfile = response.profiles[i];
                            defaultProfile.rawName = response.profiles[i].name;

                        }
                        profileStore.add({
                            id: parseInt(response.profiles[i].id, 10),
                            name: profileName,
                            rawName: response.profiles[i].name,
                            isDefault: parseInt(response.profiles[i].default_profile, 10) === 1 ? true : false,
                        });
                    }

                }
                // If the defaultProfileId has been set programmatically, use it instead of the actual default profile
                if (me.getOverrideDefaultProfileId() > 0) {
                    defaultProfile = profileStore.getById(me.getOverrideDefaultProfileId());
                    defaultProfileName = defaultProfile.data.name;
                }

                var defaultProfileRecord = profileStore.findRecord('isDefault', true);
                if (defaultProfileRecord) {
                    profileCombo.setValue(defaultProfileRecord.get('id'));
                    me.getStore('dashboard').load({
                        params: {
                            profileId: defaultProfileRecord.get('id')
                        }
                    });
                } else {
                    // if there's no default profile, no data will be requested and loaded
                    // wait for the user to pick an option
                    view.unmask();
                }
            }
        });
    },

    // onPortletRefreshHandler: function (event, toolEl, panelHeader, tool) {
    //     debugger;
    // },

    onPortletRefreshCallback: function (panel, tool, event) {
        panel.down('#portletChild').refreshPortletData();
    },

    onPortletHelpCallback: function (panel, tool, event) {
        var me = this,
            vm = me.getViewModel(),
            portletInfo = panel.getPortletInfo(),
            description, portletTitle;

        if (!Ext.Object.isEmpty(portletInfo)) {
            description = portletInfo.portlet.description;
            portletTitle = portletInfo.portlet.portletTitle;
        } else {
            const part = Ext.Array.findBy(vm.get('portlets'), function(p) { return p.type == panel.part.getId(); });
            description = part.description;
            portletTitle = part.portletTitle;
        }

        Ext.Msg.alert('Info: ' + portletTitle, description);
    },

    onSetProfileAsDefaultButtonHandler: function (btn) {
        var me = this,
            vm = me.getViewModel(),
            store = vm.getStore('dashboardProfiles'),
            combo = me.lookup('dashboardProfileCombo'),
            selectedProfileRecord = combo.getSelectedRecord();

        Ext.Msg.confirm('Make default Dashboard profile', 'Confirm that you want this to be your default profile,' +
            'which will show when starting the Software Vulnerability Manager:<br> <strong>' + combo.getRawValue() + '</strong>',
            function (opt) {
                if (opt == 'yes') {
                    // Disable 'Make Default'
                    btn.disable();

                    // Store server side (async)
                    Ext.Ajax.request({
                        url: 'action=ajaxapi_dashboard&default=' + selectedProfileRecord.get('id'),
                        success: function (response, opts) {
                            var record = store.findRecord('isDefault', true);
                            if (record) {
                                record.set('name', record.get('rawName'));
                                record.set('isDefault', false);
                            }

                            if (selectedProfileRecord) {
                                selectedProfileRecord.set('name', selectedProfileRecord.get('rawName') + vm.get('defaultProfileText'));
                                selectedProfileRecord.set('isDefault', true);
                                Ext.toast({
                                    html: '<strong>' + selectedProfileRecord.get('rawName') + '</strong> is now your default profile!',
                                    ui: 'success',
                                    title: 'Dashboard',
                                    width: 350,
                                    align: 'tr'
                                });
                            }
                        }
                    });
                }
            });
    },
    onSaveProfileButtonHandler: function (btn) {
        // make sure to call with null arguments here
        this.saveProfile(null, null, null, null);
    },

    saveProfile: function (name, changingProfile, id, layout) {
        var me = this,
            vm = me.getViewModel(),
            combo = me.lookup('dashboardProfileCombo'),
            // if not entered, use the default current ones
            useName = combo.getSelectedRecord().get('rawName'),
            useId = combo.getSelectedRecord().get('id'),
            useLayout = vm.get('layout'),
            store = combo.getStore(),
            record;


        me.updateLayout(); // make sure it is the most current available

        if (!changingProfile) {
            useLayout = vm.get('layout');
        }

        if (name && id && layout) {
            useName = name;
            useId = id;
            useLayout = layout;
        }

        record = store.findRecord('id', useId);
        if (useName) {
            Ext.Msg.prompt("Save Dashboard profile", "The layout for Default Profile will be saved. To change this profile's name, change it here:",
                function (opt, input) {
                    if (opt === 'ok') {
                        // We want to save the layout info for the profile in question, but if we just changed profiles, and are saving the one we
                        // are navigating away from, the new profile might have already loaded in the background.  So, use the layout variable configured
                        // previously in the refresh - this is the relevant one we want.

                        // Note:  code previously here no longer needed - we now have this.layout with the correct info

                        // Send data to server
                        Ext.Ajax.request({
                            url: 'action=ajaxapi_dashboard&save=' + useId + '&name=' + input + useLayout,
                            method: 'GET'
                        });
                        // defaults.sendSimpleGetRequest(globals.apiPath() + '&action=ajaxapi_dashboard&save=' + id + '&name=' + input + layout, 'OK', '', '');


                        if (record) {
                            if (record.get('isDefault')) {
                                record.set('name', sfw.Default.htmlSpecialChars(input) + vm.get('defaultProfileText'));
                            } else {
                                record.set('name', sfw.Default.htmlSpecialChars(input));
                            }
                            record.set('rawName', sfw.Default.htmlSpecialChars(input));
                            record.commit();
                        }

                        // No more unsaved changes
                        vm.set('unsavedChange', false);
                    }

                }, this, false, sfw.Default.htmlSpecialCharsDecodeAlsoQuot(useName));
        }
    },

    // The profile to be tested must be part of
    // the current store of the profiles combo
    isDefault: function (id) {
        var me = this,
            vm = me.getViewModel(),
            store = vm.getStore('dashboardProfiles'),
            record = store.findRecord('id', id);

        if (record && record.get('isDefault')) {
            return true;
        } else {
            return false;
        }
    },

    onDeleteProfileButtonHandler: function (btn) {
        var me = this,
            vm = me.getViewModel(),
            combo = me.lookup('dashboardProfileCombo'),
            selected = combo.getSelectedRecord();

        Ext.Msg.confirm('Delete Dashboard profile', 'Confirm that you wish to delete:<br> <strong>' + this.lookupReference('dashboardProfileCombo').getRawValue() + '</strong>',
            function (opt) {
                if (opt === 'yes') {
                    // No more unsaved changes
                    vm.set('unsavedChange', false);
                    // Remove from combo box, for instant user feedback
                    selected.drop();

                    // // Set 'page' title, disable buttons
                    // this.disableButtons( true );

                    /**
                     * Remove all the portlets from the screen
                     */
                    me.clearPortlets();

                    if (combo.getStore().getCount()) {
                        var nextToLoadrecord = combo.getStore().getAt(0);
                        me.loadProfile(nextToLoadrecord);
                        combo.setValue(nextToLoadrecord.get('id'))
                    } else {
                        combo.setValue('Deleted...');
                    }

                    // Send delete request to srv (async.)
                    Ext.Ajax.request({
                        url: 'action=ajaxapi_dashboard',
                        method: 'POST',
                        dataType: 'json',
                        params: {
                            'delete': selected.get('id'),
                            'nocache' : Math.random()
                        },
                        success: function (response, opts) {
                            Ext.toast({
                                html: 'Profile successfully deleted!',
                                ui: 'success',
                                title: 'Success',
                                width: 200,
                                align: 'tr'
                            });
                        },
                        failure: function (response, opts) {
                            //TODO
                        }
                    });
                }
            }, this);
    },

    onReloadButtonHandler: function (btn) {
        var me = this,
            combo = me.lookup('dashboardProfileCombo'),
            record = combo.getSelectedRecord();

        if (record) {
            record.set('type', 1);
            me.loadProfile(record);
        }
    },

    onNewProfileButtonHandler: function () {
        var me = this,
            vm = me.getViewModel(),
            comboProfile = me.lookup('dashboardProfileCombo'),
            profileStore = vm.getStore('dashboardProfiles'),
            profileName, record,
            win = Ext.create('Dashboard.ProfileForm', {
                listeners: {
                    profilecreated: function (win, action) {
                        profileName = action.result.name;
                        if (action.result['default']) {
                            profileName += vm.get('defaultProfileText');
                        }
                        record = profileStore.add({
                            id: parseInt(action.result.id, 10),
                            name: profileName,
                            rawName: action.result.name,
                            isDefault: action.result['default']
                        });
                        comboProfile.setValue(parseInt(action.result.id, 10));
                        me.clearPortlets();
                        vm.set('layout', '');
                        me.loadProfile(record[0]);
                    }
                }
            });

        win.show();
    },

    onPortletComboSelect: function (combo, record) {
        this.addPortlet(record);
    },

    onPortletComboAfterRender: function (combo) {
        var me = this,
            vm = me.getViewModel(),
            portlets = vm.get('portlets');

        if (!sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
            portlets = Ext.Array.filter(portlets, function (item) {
                return item.type !== 'top_threat_score';
            });
        }

        combo.setStore(Ext.create('Ext.data.Store', {data: portlets}));
    },

    onProfileComboSelect: function (combo, record) {
        this.loadProfile(record);
    },

    onProfileComboChange: function (combo, newValue, oldValue) {
        var me = this,
            vm = me.getViewModel(),
            store = vm.getStore('dashboardProfiles'),
            oldRecord = store.findRecord('id', oldValue);

        vm.set('isDefaultProfile', me.isDefault(newValue));

        // If we are changing the profile to another, but there are changes to the current, we need to save it, and flag the case.
        // If we are reloading the same profile, we should not call unsaved changes - just reload the current.
        if (oldRecord) {
            var changingProfile = true;
            me.updateLayout();
            me.confirmUnsavedChanges(oldRecord.get('name'), changingProfile, oldRecord.get('id'), vm.get('layout'));
            oldRecord.set('type', 0);
        }
    },

    loadProfile: function (record) {
        var me = this,
            view = me.getView(),
            vm = me.getViewModel(),
            dashboardProfileCombo = me.lookup('dashboardProfileCombo'),
            selectedProfileRecord = dashboardProfileCombo.getSelectedRecord();

        view.mask('Loading...');

        me.clearPortlets();
        // var typeUpdate = 0 ;
        // if (profile_.type == 1)  typeUpdate = 1 ;
        this.getStore('dashboard').load({
            params: {
                profileId: record.get('id')
            }
        });
    },
    onClosePortletCallback: function () {
        //     var po = panel.ownerCt;
        //     var pid = panel.getId();
        //
        //     panel.ownerCt.remove( panel, true );
        //     sfw.csiDashboard.addPortalBackToCombo( pid );
        //     sfw.csiDashboard.changeDetected();

        this.changeDetected();
    },
    addPortlet: function (item) {
        var me = this,
            vm = me.getViewModel(),
            dashboard = me.getView(),
            column = null,
            colIndex = 0,
            tmpColumn = null,
            lowest = null,
            noOfVisibleItems = 0,
            cols = dashboard.query('dashboard-column'),
            existingPortlet = dashboard.down(item.get('type')),
            portlet = {},
            // Enhancement: use the height of the column instead of visible items since we have porlets of different heights
            getNoOfVisibleItems = function (column) {
                var noOfItems = column.items.length;
                var noOfVisibleItems = 0;
                for (var i = 0; i < noOfItems; i++) {
                    if (column.items.items[i].isVisible()) {
                        noOfVisibleItems++;
                    }
                }
                return noOfVisibleItems;
            };


        // Automatically select the right column where it should be added
        for (var columnIndex = 0; columnIndex <= (vm.get('columns') - 1); columnIndex++) {
            if (!cols[columnIndex]) {
                column = dashboard.add(columnIndex, dashboard.createColumn({
                    columnWidth: (Ext.isArray(dashboard.columnWidths) ? dashboard.columnWidths[columnIndex] : 1)
                }));
            }
            tmpColumn = cols[columnIndex] || column;
            noOfVisibleItems = getNoOfVisibleItems(tmpColumn);
            if (noOfVisibleItems < lowest || lowest == null) {
                column = tmpColumn;
                colIndex = columnIndex;
                lowest = noOfVisibleItems;
            }
        }
        // Insert at the end of the selected column
        if (existingPortlet) {
            portlet = existingPortlet.up();
            portlet.show();
            // De-select selected in combo
            Ext.toast({
                html: 'The element selected is already part of this dashboard!',
                ui: 'warning',
                title: 'Dashboard',
                width: 200,
                shadow: true,
                align: 'tr'
            });
        } else {
            //portlet = this.availablePortlets[ record.get('id') ];
            dashboard.addNew(item.get('type'), colIndex);
            var newPortlet = dashboard.down('dashboard-panel:hasPart(' + item.get('type') + ')');

            if (newPortlet.down('#portletChild').getPortletStore().isStore) {
                newPortlet.down('#portletChild').getPortletStore().load();
            }

            // De-select selected in combo
            Ext.toast({
                html: 'Element inserted!',
                ui: 'success',
                title: 'Dashboard',
                width: 200,
                shadow: true,
                align: 'tr'
            });
        }


        me.lookup('dashboardPortletCombo').reset();

        me.changeDetected();
    },

    onDashboardStoreLoad: function (store) {
        // var typeUpdate = 0 ;
        // if (profile_.type == 1)  typeUpdate = 1 ;

        var me = this,
            vm = me.getViewModel(),
            view = me.getView(),
            record = store.getAt(0),
            portlets = record.get('portlets'),
            dashboardProfileCombo = me.lookup('dashboardProfileCombo');

        me.clearPortlets();

        // Once a profile is loaded, any changes not saved are discarded - reset unsaved changes to false
        vm.set('unsavedChange', false);

        // Clear all columns, but also we do want to create the portlets here, so call with false as optional 2nd arg
        // Note: this will also load the data needed for the combo box. The Dashboard won't be visible here if the
        // user clicked on a different page and rejected saving the unsaved Dashboard changes. In that situation we

        var columnLayout = portlets;  // used for both cases

        // Separate the two cases of regular CSI vs. static URL - some things are specific to each.
        // Essentially, we need to go through the portlets in the profile layout that need to be loaded, and construct an array of the portlet names.

        vm.set('profileName', vm.get('defaultProfile.rawName') || vm.get('defaultProfile.name'));
        // make sure the combo box has all potential portlets loaded, then we remove them as we add them to the dashboard
        //combo.store.loadData(sfw.csiDashboard.portlets);

        vm.set('layout', '');

        // Once a profile is loaded, any changes not saved are discarded - reset unsaved changes to false
        vm.set('unsavedChange', false);

        // All loaded, enable buttons
        //TODO Wemerson sfw.csiDashboard.disableButtons(false || (false !== LoginDetails.isReadOnly));

        // Select profile
        columnLayout = portlets;

        var length = columnLayout.length;
        var portletCount = 0;
        // Loop though result and generate output
        var profile = [];
        var colIndex = 0;

        // Create array if it doesn't exist
        for (var i = 1; i <= length; i++) {
            colIndex = columnLayout[portletCount].column_index;
            if (!profile[colIndex]) {
                profile[colIndex] = []; // if it doesn't exit, create it.
            }

            // Insert into array
            profile[colIndex][profile[colIndex].length] = columnLayout[portletCount];
            portletCount++;
        }
        // Insert profile portlets
        var key = ""; // used to determine which data in the response is for the specific Portlet
        var portlet = {};
        var smartGroupComboBoxParam = "";
        var smartGroupComboBoxValue = "";
        var rawPortletData;
        for (var columnIndex = 0; columnIndex <= (vm.get('columns') - 1); columnIndex++) {
            // Column
            var column = view.items.items[columnIndex];
            // Skip if no data for sfw.csiDashboard column
            if (!profile[columnIndex]) {
                continue;
            }
            // Loop through profile settings and add portlets
            for (i = 0; i < profile[columnIndex].length; i++) {
                // insert into column
                for (var j = 0, portletsCount = vm.get('portlets').length; j < portletsCount; j++) {

                    if (vm.get('portlets')[j].type === profile[columnIndex][i].portlet) {
                        portlet = profile[columnIndex][i];
                        // The key used to determine which response data is for this Portlet
                        key = portlet.portlet + "," +
                            portlet.host_smartgroup_id + "," +
                            portlet.product_smartgroup_id + "," +
                            portlet.advisory_smartgroup_id;
                        if ("undefined" !== typeof record.get('data_keyed')[key]) {
                            rawPortletData = record.get('data_keyed')[key]; // The data is stored under a key
                        } else {
                            rawPortletData = record.get('data'); // The portlet uses the common data
                        }

                        view.addNew(vm.get('portlets')[j].type, columnIndex);
                        var newPortlet = view.down('dashboard-panel:hasPart(' + vm.get('portlets')[j].type + ')');
                        //Ext.apply(newPortlet, portlet);

                        newPortlet.setPortletInfo({
                            portlet: Ext.applyIf(portlet, vm.get('portlets')[j]),
                            data: rawPortletData
                        })

                        if (newPortlet.down('#portletChild').getPortletStore().isStore) {
                            newPortlet.down('#portletChild').getPortletStore().loadRawData({data: rawPortletData});
                        }

                        // Store the layout info for when we are saving the profile info
                        smartGroupComboBoxParam = (portlet.host_smartgroup_id ? "host_smartgroup_id" : "")
                            || (portlet.product_smartgroup_id ? "product_smartgroup_id" : "")
                            || (portlet.advisory_smartgroup_id ? "advisory_smartgroup_id" : "");
                        smartGroupComboBoxValue = ("undefined" === typeof portlet[smartGroupComboBoxParam]) ? "" : portlet[smartGroupComboBoxParam];
                        // var layout = vm.get('layout');
                        //  layout += ;
                        vm.set('layout', '&layout[]=' + columnIndex + ',' + i + ',' + portlet.portlet + ',' + smartGroupComboBoxParam + ',' + smartGroupComboBoxValue)
                        // Remove from combo box of options
                        // combo.store.remove( combo.store.getById( portlet ) );

                        if ((portlet.host_smartgroup_id !== "") || (portlet.product_smartgroup_id !== "") || (portlet.advisory_smartgroup_id !== "")) {
                            vm.get('smartGroupCombosLoaded').push({
                                id: vm.get('portlets')[j].type,
                                loaded: false
                            });
                        }
                    }
                }
            }
        }
        view.unmask();
        me.fireDashboardLoadedEvent();
    },
    // Clear any loaded portlets on the dashboard
    clearPortlets: function () {
        var me = this,
            view = me.getView();

        var cols = view.query('dashboard-column');

        cols.forEach(function (element, index) {
            element.removeAll();
        });
    },

    onPartDrop: function (over) {
        this.changeDetected();
    },

    changeDetected: function () {
        var me = this,
            vm = me.getViewModel(),
            dashboardProfileCombo = me.lookup('dashboardProfileCombo');

        // Is a profile selected?
        if (dashboardProfileCombo.getValue()) {
            // Register the change
            vm.set('unsavedChange', true);

            // Enable save button if a profile is selected
            //TODO by Wemerson - use databing do enable/disable buttons
            //this.disableButtons( false || ( false !== LoginDetails.isReadOnly ) );
        }
    },

// This is called to update the this.layout variable that constantly tracks the current layout to allow for saving the correct current info to the
// database.  The layout variable is also modified/set when we do a refresh with insert, and when loading a new profile.  So, this currently is
// only called explicitly in 2 cases: when deleting a porlet, or loading a non-chart portlet.  Loading a chart portlet calls refresh which
// internally updates this.layout accordingly.
    updateLayout: function () {
        var me = this,
            vm = me.getViewModel(),
            dashboard = me.getView(),
            column,
            id,
            item,
            len,
            portlet,
            columns = dashboard.query('dashboard-column');

        vm.set('layout', '');
        if (!columns.length) {
            return;
        }
        // Loop through each column
        for (var columnIndex = 0; columnIndex <= (vm.get('columns') - 1); columnIndex++) {
            // Column
            column = columns[columnIndex];
            if (!column) {
                continue;
            }

            // Loop trough each item in this column
            len = column.items.length;
            var lay = vm.get('layout');
            for (var i = 0; i < len; i++) {
                // Item at offset(i) in column
                portlet = column.items.items[i];
                // Get Smart Group ComboBox value
                var smartGroupComboBoxParam = "",
                    smartGroupComboBoxValue = "",
                    smartGroupTypeCombo = portlet.down('combobox');
                if (smartGroupTypeCombo) {
                    //     // The Smart Group Combo is always the first item in a Smart Group Portlet
                    smartGroupComboBoxParam = smartGroupTypeCombo.remoteParam;
                    smartGroupComboBoxValue = smartGroupTypeCombo.getValue();
                }

                // Store the layout info for when we are saving the profile info
                lay += '&layout[]=' + columnIndex + ',' + i + ',' + portlet.part.getId() + ',' + smartGroupComboBoxParam + ',' + smartGroupComboBoxValue;
            }
            vm.set('layout', lay);
        }
    },


// Handle if the user is navigating away, without saving changes made
// Pass the name as an optional parameter - otherwise 'this.profileName' might have already changed in the background by the time you display the
// save window, thus displaying the wrong name for the profile you actually want to save.  This happens in the case where you make changes
// to a profile then switch to another profile.
    confirmUnsavedChanges: function (name, changingProfile, id, layout) {
        var me = this,
            vm = me.getViewModel(),
            message = 'You have unsaved changes to your current Dashboard Profile.  Do you wish to save these before proceeding?';

        if (name) {
            message = 'You have unsaved changes to the Dashboard Profile \'' + name + '\'. Do you wish to save these before proceeding?';
        }
        if (vm.get('unsavedChange')) {
            Ext.Msg.confirm('Dashboard: Unsaved Changes',
                message,
                function (clicked) {
                    if (clicked == 'yes') {
                        me.saveProfile(name, changingProfile, id, layout);
                    } else {
                        vm.set('unsavedChange', false);
                    }
                });
        }
    },

    /*
 * For taking a screenshot of the dashboard we like to fire the dashboard.loaded event, but not before all portlets
 * containing Smart-group Combo boxes have loaded their combobox values
 */
    fireDashboardLoadedEvent: function () {
        var me = this,
            vm = me.getViewModel();

        for (var i = 0; i < vm.get('smartGroupCombosLoaded').length; i++) {
            if (!vm.get('smartGroupCombosLoaded')[i].loaded) {
                return;
            }
        }
        Ext.GlobalEvents.fireEvent('dashboard.loaded', {success: true});
    },

    /*
 * A means for Smart-group combo boxes to register that they have fully loaded their combobox values
 */
    registerSmartGroupComboLoaded: function (id) {
        var me = this,
            vm = me.getViewModel();
        for (var i = 0; i < vm.get('smartGroupCombosLoaded').length; i++) {
            if (vm.get('smartGroupCombosLoaded')[i].id === id) {
                vm.get('smartGroupCombosLoaded')[i].loaded = true;
            }
        }
        me.fireDashboardLoadedEvent();
    },

    onDashboardExport: function (){
        var me = this,
            combo = me.lookup('dashboardProfileCombo'),
            record = combo.getSelectedRecord();

        window.open(sfw.util.Globals.apiPath()
            +'&action=ajaxapi_dashboard'
            + '&which=export'
            + '&profile_id=' + record.get('id')
            + '&width=1264'
            + '&height=1251')
    }
});