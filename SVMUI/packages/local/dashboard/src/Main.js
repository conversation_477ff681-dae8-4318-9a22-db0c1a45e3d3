Ext.define('Dashboard.Main', {
    extend: 'Ext.dashboard.Dashboard',
    requires: [
        'Dashboard.chart.theme.Default',
        'Dashboard.Column'
    ],
    xtype: 'sfw.csiDashboard',
    controller: 'dashboard-maincontroller',
    viewModel: 'dashboard-mainmodel',
    defaultType: 'flexera-dashboard-column',
    statics: {
        // Helper function for comparing two values (usually old and new) and changing text color if there is a difference.  We
        // also return the delta in case we want to print it out, and have an optional field for special chars for when we, for example
        // want to print (+7%) when talking about percentage.
        //
        // Default is: value1>value2 => green, value1<value2 => red, values equal => black. To flip the + or - sign on the delta, set the 4th parameter.
        // To flip the colour logic, just enter the values in the opposite order.
        //
        // The 4th optional flag will set it so we return green if both are zero - this is because if we are talking
        // about something negative like insecure programs, 0 is positive (green) even if there was no change.
        getChangesColorAndText: function (input1, input2, flip, greenZero, specialChars) {

            var textColor = "Black";
            var value1 = parseInt(input1, 10);
            var value2 = parseInt(input2, 10);
            var sign = "+";
            var deltaString = '';
            var retArray;
            var special = (specialChars) ? specialChars : '';

            // We also assume we are getting numbers in here - if non-numeric strings, just return black.
            if (isNaN(value1) || isNaN(value2)) {
                retArray = [textColor, deltaString]; // default values of black and ''
                return retArray;
            }

            if (greenZero && 0 == value1 && 0 == value2) {
                retArray = ["StringSuccess", deltaString]; // default values of ''
                return retArray;
            }

            if (value1 == value2) {
                retArray = [textColor, deltaString]; // default values, black and ''
                return retArray;
            }

            // otherwise, we have integers we can compare
            var delta = Math.abs(value1 - value2);
            if (value1 > value2) {
                textColor = "StringSuccess";
                sign = (flip ? '-' : sign);
                deltaString = "(" + sign + delta + special + ")" + '&nbsp;' + '&nbsp;';
            } else if (value1 < value2) {
                textColor = "StringError";
                sign = (flip ? '+' : '-');
                deltaString = "(" + sign + delta + special + ")" + '&nbsp;' + '&nbsp;';
            }

            retArray = [textColor, deltaString];
            return retArray;
        }
    },
    // stateful: false,
    cls: 'flexera-dashboard',
    columnWidths: [
        0.33,
        0.33,
        0.33
    ],

    listeners: {
        drop: 'onPartDrop'
    },

    title: 'Dashboard',
    tabConfig: {
        closable: false
    },
    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'combobox',
            reference: 'dashboardPortletCombo',
            width: 320,
            emptyText: 'Select dashboard element to insert',
            queryMode: 'local',
            valueField: 'type',
            displayField: 'portletTitle',
            editable: false,
            matchFieldWidth: false,
            listeners: {
                afterrender: 'onPortletComboAfterRender',
                select: 'onPortletComboSelect'
            }
        }, {
            xtype: 'combobox',
            width: 200,
            queryMode: 'local',
            valueField: 'id',
            displayField: 'name',
            editable: false,
            matchFieldWidth: false,
            reference: 'dashboardProfileCombo',
            listeners: {
                select: "onProfileComboSelect",
                change: 'onProfileComboChange'
            },
            bind: {
                store: '{dashboardProfiles}'
            }

        }, {
            xtype: 'button',
            text: 'Save',
            disabled: true,
            bind: {
                disabled: '{!dashboardProfileCombo.selection}'
            },
            handler: 'onSaveProfileButtonHandler'// make sure to call with null arguments here
        }, {
            xtype: 'button',
            text: 'Set default',
            disabled: true,
            bind: {
                disabled: '{isDefaultProfile}'
            },
            handler: 'onSetProfileAsDefaultButtonHandler'
        }, {
            xtype: 'button',
            text: 'Delete',
            disabled: true,
            bind: {
                disabled: '{!canDeleteProfile}'
            },
            handler: 'onDeleteProfileButtonHandler'
        }, {
            xtype: 'button',
            text: 'New',
            tooltip: 'Create new blank profile',
            handler: 'onNewProfileButtonHandler'
        }, {
            xtype: 'button',
            text: 'Reload',
            handler: 'onReloadButtonHandler'
        }, '->', {
            xtype: 'button',
            hidden: false,
            text: 'Fullscreen View',
            itemId: 'fullScreen',
            handler:function(){
                var mainView = Ext.ComponentQuery.query('viewport')[0];

                if(!this.fullScreen){
                    mainView.down('[region=north]').hide();
                    mainView.down('[region=west]').hide();
                    mainView.down('#contentPanel').down('tabbar').hide();
                    this.fullScreenView = true;
                    this.setText('Standard View');
                    this.fullScreen = true;
                }else{
                    mainView.down('[region=north]').show();
                    mainView.down('[region=west]').show();
                    mainView.down('#contentPanel').down('tabbar').show();
                    this.fullScreenView = false;
                    this.setText('Fullscreen View');
                    this.fullScreen = false;
                }
            }
        }, {
            xtype: 'button',
            hidden: true,
            text: 'Export',
            handler: 'onDashboardExport'
        }]
    }],

    parts: {
        overview: {
            viewTemplate: {
                height: 500,
                title: 'Overview',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: '10 0',
                frame: false,
                collapsible: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-overviewpanel',
                    itemId: 'portletChild'
                }]
            }
        },
        top_insecure_programs: {
            viewTemplate: {
                collapsible: false,
                height: 400,
                title: 'Most Prevalent Insecure Software Installations',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-topinsecureprogramsgrid',
                    itemId: 'portletChild'
                }]
            }
        },
        top_insecure_programs_score: {
            viewTemplate: {
                collapsible: false,
                height: 400,
                title: 'Insecure Software Installations Weighted Score',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-topinsecureprogramscoregrid',
                    itemId: 'portletChild'
                }]
            }
        },
        top_eol_programs: {
            viewTemplate: {
                collapsible: false,
                height: 400,
                title: 'Most Prevalent End-of-Life Software Installations',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-topeolprogramsgrid',
                    itemId: 'portletChild'
                }]
            }
        },
        top_advisories: {
            viewTemplate: {
                collapsible: false,
                minHeight: 400,
                title: 'Most Critical Advisories Affecting Your Security',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-topadvisoriesgrid',//Shared Grid
                    itemId: 'portletChild'
                }]
            }
        },
        top_latest_advisories: {
            viewTemplate: {
                collapsible: false,
                minHeight: 400,
                title: 'Latest Advisories Affecting Your Security',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-toplatestadvisoriesgrid',//Shared grid
                    itemId: 'portletChild'
                }]
            }
        },
        top_threat_score: {
            viewTemplate: {
                collapsible: false,
                minHeight: 400,
                //maxHeight: 700,
                title: 'Top Threat Score Affecting Your Security',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-topthreatscoregrid',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_database_condition: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Host Status - Time Since Last Scan',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-database-condition-chart',
                    itemId: 'portletChild'
                },{
                    xtype: 'flexera-emptytextcomponent',
                    itemId: 'emptyText'
                }]
            }
        },
        chart_software_overview_installations: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Software Overview - Status of Installations',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-software-overview-installationschart',
                    itemId: 'portletChild'
                },{
                    xtype: 'flexera-emptytextcomponent',
                    itemId: 'emptyText'
                }]
            }
        },
        chart_site_overview_installations: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Site Overview - Insecure Software Installations',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-site-overview-installationschart',
                    itemId: 'portletChild'
                },{
                    xtype: 'flexera-emptytextcomponent',
                    itemId: 'emptyText'
                }]
            }
        },
        chart_site_overview_system_score: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Site Overview - Average System Score',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-site-overview-system-scorechart',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_overview: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality Overview - Threat Profile of Vulnerabilities',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-overview-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        time_to_package: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Time from Insecure Version Detection to Update Creation',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-timetopackagegrid',
                    //html: 'TODO - Time from Insecure Version Detection to Update Creation',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_history_level1: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality 5 Week History - Extremely Critical',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-history-level1-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_history_level2: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality 5 Week History - Highly Critical',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-history-level2-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_history_level3: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality 5 Week History - Moderately Critical',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-history-level3-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_history_level4: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality 5 Week History - Less Critical',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-history-level4-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_criticality_history_level5: {
            viewTemplate: {
                collapsible: false,
                height: 500,
                title: 'Criticality 5 Week History - Not Critical',
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-criticality-history-level5-chart',
                    itemId: 'portletChild'
                }]
            }
        },
        latest_activity: {
            viewTemplate: {
                collapsible: false,
                //minHeight: 400,//auto
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Latest Activity',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-latestactivitypanel',
                    itemId: 'portletChild'
                }]
            }
        },
        chart_vulnerability: {
            viewTemplate: {
                collapsible: false,
                //minHeight: 400,//auto
                height : 500,
                ui: 'light',
                cls: 'shadow',
                border: 0,
                margin: 7,
                frame: false,
                title: 'Vulnerabilities Found Vs Vulnerabilities Resolved',
                listeners: {
                    close: {
                        fn: 'onClosePortletCallback',
                        scope: 'controller'
                    }
                },
                tools: [{
                    type: 'refresh',
                    // handler: 'onPortletRefreshHandler',
                    callback: 'onPortletRefreshCallback'
                }, {
                    type: 'help',
                    callback: 'onPortletHelpCallback'
                }],
                items: [{
                    xtype: 'dashboard-portlet-vulnerabilitychart',
                    itemId: 'portletChild'
                }]
            }
        }
    },

    insertPortlet: function (portlet, column, row) {
        const dashboard = this;
        const columns = dashboard.query('dashboard-column');
        const target = columns[column];


        if (!columns.length) {
            var column = this.createColumn();
            // item = this.addView(portlet);

            column.add(portlet);
            // column.items.push(portlet);
        }


        if (target) {
            return target.insert(row, portlet);
        }
        return null;
    }
});