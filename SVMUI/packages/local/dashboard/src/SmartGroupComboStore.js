/**
 * SmartGroup Store for Smart Group Dashboard Portlet ComboBoxes
 */
Ext.define('Dashboard.SmartGroupComboStore', {
    extend: 'Ext.data.Store',
    alias: 'store.smartgroupcombostore',
    autoLoad: false,
    pageSize: 0,
    fields: [{name: "id", type: "int"}, {name: "name", type: "string"}],
    constructor: function (config) {
        this.callParent(arguments);
    },
    proxy: {
        type: 'ajax',
        url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
        reader: {
            type: 'json',
            rootProperty: 'data'
        }
    },
    listeners: {
        // When a Store loads, set the default value for all of the Comboboxes that use it.
        load: function (store, records) {
            Ext.GlobalEvents.fireEvent('smartgroups.menustore.loaded', records);

            var dashboard = Ext.ComponentQuery.query('dashboard')[0],
                columns, combo;

            if (dashboard) {
                columns = dashboard.query('dashboard-column');
                columns.forEach(function (column, index) {

                    column.items.each(function (portlet, index) {
                        combo = portlet.down('combobox[isSmartGroupCombo]');
                        // ensure it's a Smart Group Portlet
                        if (combo) {
                            // If the Smart Group Portlet is using this store then choose it's default
                            combo.chooseDefaultPortletSmartGroup();
                            dashboard.getController().registerSmartGroupComboLoaded(portlet.part.getId());
                        }
                    })
                });
            }

            // sfw.events.fireEvent("smartgroups.menustore.loaded", records);
            //
            // if ( "undefined" === typeof sfw.csiDashboard.portal ) {
            //     return; // If there aren't any portal items then no work to do
            // }
            // sfw.csiDashboard.portal.items.each( function ( column ) { // Check each Portal column
            //     column.items.each( function ( row ) { // Check each row of the Portal column
            //         if ( "undefined" !== row.initialConfig.items[0].smartGroupTextType ) { // ensure it's a Smart Group Portlet
            //             if ( store === row.initialConfig.items[0].store ) { // If the Smart Group Portlet is using this store then choose it's default
            //                 row.initialConfig.items[0].chooseDefaultPortletSmartGroup();
            //                 sfw.csiDashboard.registerSmartGroupComboLoaded(row.id);
            //             }
            //         }
            //     } );
            // } );
        }
    }
});