Ext.define('Dashboard.ProfileForm', {
    extend: 'Ext.window.Window',
    alias: 'widget.dashboard-profile-form',
    controller: 'dashboard-profileformcontroller',
    width: 400,
    height: 150,
    title: 'New Dashboard Profile',
    defaultFocus: '#nameField',
    bodyPadding: 10,
    layout: 'fit',
    modal: true,
    keyMap: {
        ENTER: {
            fn: 'onSaveButtonHandler'
        }
    },
    items: [{
        xtype: 'form',
        reference: 'mainForm',
        items: [{
            xtype: 'textfield',
            itemId: 'nameField',
            anchor: '100%',
            name: 'new',
            allowBlank: false,
            fieldLabel: 'Name',
            labelAlign: "top",
            msgTarget: 'side',
            selectOnFocus: true,
            validateBlank: true
        }]
    }],
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            pack: 'end'
        },
        dock: 'bottom',
        items: [{
            xtype: 'button',
            text: 'Save',
            ui: 'primary',
            handler: 'onSaveButtonHandler'
        }, {
            xtype: 'button',
            text: 'Cancel',
            listeners: {
                // the "closeView" method is a helper inherited from Ext.app.ViewController.
                click: 'closeView'
            }
        }]
    }]
});