Ext.define('Dashboard.ProfileFormController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-profileformcontroller',


    onSaveButtonHandler: function () {
        var me = this,
            view = me.getView(),
            form = me.lookup('mainForm').getForm();

        if (form.isValid()) {
            view.mask('Creating Dashboard Profile...');
            form.submit({
                url: 'action=ajaxapi_dashboard',
                success: function (form, action) {
                    view.unmask();
                    var lResponse = Ext.decode(action.response.responseText).response,
                        theTitle,
                        theMsg;
                    switch (lResponse) {
                        case 1:
                            theTitle = 'Dashboard profile created successfully';
                            theMsg = 'Insert elements into your Dashboard Profile by clicking the "Select dashboard element to insert" dropdown box at the top.';
                            me.onComplete(action);
                            view.close();
                            break;
                        case 2:
                        default:
                            theTitle = "Error";
                            // TODO - is it a readOnly permissions issue?
                            theMsg = Ext.decode(action.response.responseText).reason;
                            break;
                    }
                    // Success message
                    Ext.MessageBox.show({
                        title: theTitle,
                        msg: theMsg,
                        buttons: Ext.MessageBox.OK,
                        icon: Ext.MessageBox.INFO
                    });
                },
                failure: function (form, action) {
                    view.unmask();
                    Ext.Msg.alert(
                        'Failed',
                        action.result ? action.result.message : 'No response'
                    );
                }
            });
        }
    },

    onComplete: function (action) {
        this.fireViewEvent('profilecreated', action);
    }
});