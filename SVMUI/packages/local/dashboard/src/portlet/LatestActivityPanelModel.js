Ext.define('Dashboard.portlet.LatestActivityPanelModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-latestactivitypanelmodel',

    stores: {
        lastLogins: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=latest_activity&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.lastLogins'
                }
            }
        },
        lastReportsSent: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=latest_activity&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.lastReportsSent'
                }
            }
        },
        lastPackages: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=latest_activity&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.lastPackages'
                }
            }
        },
        lastUserManagement: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=latest_activity&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.lastUserManagement'
                }
            }
        }
    }
});