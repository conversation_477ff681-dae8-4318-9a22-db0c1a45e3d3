Ext.define('Dashboard.portlet.CriticalityOverviewChartController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-criticalityoverviewchartcontroller',

    onClick: function(evt){

    },

    onCriticalityOverviewScoreLoad: function (store, records) {
        const me = this,
            view = me.getView(),
            cartesian = view.down('cartesian'),
            axes = cartesian.getAxes();

        var numeric = Ext.Array.findBy(axes, function(item) { return item.type == 'numeric'; });

        if(numeric) {
            const scores = Ext.Array.map(records, function(item) {
                var values = Ext.Object.getValues(item.data);
                values = Ext.Array.map(values, function(val) { return Ext.Number.parseInt(val); });
                return Ext.Array.max(values);
            });

            const maxScore = Ext.Array.max(scores);
            numeric.setMaximum(me.roundIntToNearest(maxScore));
            cartesian.redraw();
        }
    },

    roundIntToNearest: function (max) {
        var maxPos = (max * 1.05).toFixed(),
            powOf10 = maxPos.length > 4 ? maxPos.length-3: 2,
            divideBy = Math.pow(10, powOf10);

        if (powOf10 == 2) {
            return Math.ceil(maxPos / divideBy) * divideBy;
        } else {
            return Math.round(maxPos / divideBy) * divideBy;
        }
    }

});