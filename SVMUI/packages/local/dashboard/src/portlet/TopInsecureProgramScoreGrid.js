/**
 * Created by <PERSON><PERSON><PERSON> on 12. August 2021
 */

Ext.define('Dashboard.portlet.TopInsecureProgramScoreGrid', {
    extend: 'Ext.grid.Panel',
    alias: [
        'widget.dashboard-portlet-topinsecureprogramscoregrid',
        'widget.top_insecure_programs_score'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    controller: 'dashboard-portlet-topinsecureprogramscoregridcontroller',
    viewModel: {
        type: 'dashboard-portlet-topinsecureprogramscoregridmodel'
    },
    bind: {
        store: '{topInsecureProgramsScoreStore}'
    },
    viewConfig: {
        emptyText: 'There are no Insecure Software Installations found on your system.'
    },
    listeners: {
        // itemdblclick:'onItemDblClick',
        // listener to handle clicks on .clickable elements
        click: {
            element: 'el',
            // scope: 'this',
            delegate: '.clickable',
            fn: 'onClick'
        }
    },
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'product'

        }]
    }],
    columns: [{
        xtype: 'rownumberer',
        text: '#',
        align: 'center',
        width: 40
    }, {
        xtype: 'templatecolumn',
        cellWrap: true,
        tpl: [
            '<span class="clickable" data-id="{product_id}">{product_name}</span>'
        ],
        text: 'Software Name',
        flex: 1
    }, {
        align: 'right',
        text: 'Currently',
        width: 100,
        dataIndex: 'installations_criticality_score',
        renderer: function (value, metadata, record) {
            var histScore = record.get('hist7day_installations_criticality_score'),
                curScore = value;

            // Want red if new value bigger, green if smaller, else black, but green on both == 0, flip sign is true, no specialChars
            var delta = Dashboard.Main.getChangesColorAndText((histScore >= 0 ? histScore : 0), curScore, true, true);
            var textColor = delta[0];
            var deltaString = delta[1];

            // console.log(record);
            // var curScore = parseInt( value, 10 );
            // var curCount = parseInt( record.get('no_installations'), 10 );
            var curScoreStr = Ext.util.Format.number(parseInt( value, 10 ), '0,000');
            var curCountStr = Ext.util.Format.number(parseInt( record.get('no_installations'), 10 ), '0,000');
            var curTooltip = Ext.String.format('{0} = {1} installations x most critical advisories criticalities', curScoreStr, curCountStr);
            metadata.tdCls = textColor;
            metadata.tdAttr = 'data-qtip="'+curTooltip+'"';
            return deltaString + ' ' + value;
        }
    }, {
        align: 'right',
        text: 'Last Week',
        width: 100,
        dataIndex: 'hist7day_installations_criticality_score',
        renderer: function (value, metaData, record, rowIndex, colIndex, store) {
            var productId = parseInt(record.get('product_id'), 10),
                record = store.findRecord('product_id', productId),
                histScore = -1,
                histCount = 0,
                histTooltip = '';

            if (record) {
                histScore = parseInt(record.get('installations_criticality_score'), 10);
                histCount = parseInt(record.get('no_installations'), 10);
            }

            if ( -1 === histScore ) {
                histScoreOutput = 'No Data';
                histTooltip = '';
            } else {
                var histCountStr = Ext.util.Format.number(parseInt(histCount, 10), '0,000');
                var histScoreOutput = Ext.util.Format.number(parseInt(histScore, 10), '0,000');
                histTooltip = histScoreOutput + ' = ' + histCountStr + ' installations x most critical advisories criticalities';
            }

            metaData.tdAttr = 'data-qtip="'+histTooltip+'"';
            return value > 0 ? value : "No Data";
        }
    }, {
        align: 'right',
        text: 'Last Month',
        width: 100,
        dataIndex: 'hist1day_installations_criticality_score',
        renderer: function (value, metaData, record, rowIndex, colIndex, store) {
            var productId = parseInt(record.get('product_id'), 10),
                record = store.findRecord('product_id', productId),
                histScore = -1,
                histCount = 0,
                histTooltip = '';

            if (record) {
                histScore = parseInt(record.get('installations_criticality_score'), 10);
                histCount = parseInt(record.get('no_installations'), 10);
            }

            if ( -1 === histScore ) {
                histScoreOutput = 'No Data';
                histTooltip = '';
            } else {
                var histCountStr = Ext.util.Format.number(parseInt(histCount, 10), '0,000');
                var histScoreOutput = Ext.util.Format.number(parseInt(histScore, 10), '0,000');
                histTooltip = histScoreOutput + ' = ' + histCountStr + ' installations x most critical advisories criticalities';
            }

            metaData.tdAttr = 'data-qtip="'+histTooltip+'"';
            return value > 0 ? value : "No Data";
        }
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('topInsecureProgramsScoreStore');
    }
});