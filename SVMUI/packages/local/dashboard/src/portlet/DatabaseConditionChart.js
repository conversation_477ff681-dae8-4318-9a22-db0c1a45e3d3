Ext.define('Dashboard.portlet.DatabaseConditionChart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-database-condition-chart',
        'widget.chart_database_condition'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.series.Pie',
        'Ext.chart.interactions.Rotate'
    ],
    controller: 'dashboard-portlet-databaseconditionchartcontroller',
    viewModel: {
        type: 'dashboard-portlet-databaseconditionchartmodel'
    },
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'host'
        }]
    }, {
        xtype: 'container',
        padding: 5,
        dock: 'top',
        html: 'See how up-to-date the statistics gathered for your hosts are.  Numbers in brackets show exactly how many hosts have been scanned in the given time period.'
    }],
    items: [{
        xtype: 'polar',
        itemId: 'chart',
        bind: {
            store: '{dbConditions}'
        },
        legend: {
            docked: 'bottom',
            // position: 'float',//Not supported
            // x: 10,
            // y: 250,
            type: 'sprite'
        },
        //insetPadding: 40,
        innerPadding: 20,
        interactions: ['rotate'],
        //radius: 200,
        series: [{
            type: 'pie',
            // donut: 30,
            animation: {
                easing: 'easeOut',
                duration: 250
            },
            angleField: 'percent_hosts',
            highlight: true,
            colors: [
                '#7bd17e',//green
                '#e5d659',//yellow
                '#e17911',//orange
                '#ec4033'//red
            ],
            label: {
                field: 'time_period',
                /*calloutLine: {
                    length: 60,
                    width: 3
                    // specifying 'color' is also possible here
                }*/
                renderer: function (text, sprite, config, rendererData, index) {
                    var rec = rendererData.store.findRecord('time_period', text);
                    return rec.get('percent_hosts') + '%';
                }
            },
            tooltip: {
                trackMouse: true,
                renderer: function (tooltip, record, item) {
                    tooltip.setHtml('Hosts: ' + record.get('no_hosts'));
                }
            }
        }]
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('dbConditions');
    }
});