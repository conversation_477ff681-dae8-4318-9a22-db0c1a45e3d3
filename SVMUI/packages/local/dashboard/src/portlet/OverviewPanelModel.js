Ext.define('Dashboard.portlet.OverviewPanelModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-overviewpanelmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=host&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        overview: {
            autoLoad: true,
            pageSize: 0,
            fields: ['group', 'description', 'today', 'last_week'],
            groupField: 'group',
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=overview&'
            },
            listeners: {
                load: 'onOverviewStoreLoad'
            }
        }
    }
});