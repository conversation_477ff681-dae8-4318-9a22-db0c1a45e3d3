Ext.define('Dashboard.portlet.SoftwareOverviewInstallationsChart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-software-overview-installationschart',
        'widget.chart_software_overview_installations'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.axis.Category',
        'Ext.chart.series.Bar',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'dashboard-portlet-softwareoverviewinstallationschartcontroller',
    viewModel: {
        type: 'dashboard-portlet-softwareoverviewinstallationschartmodel'
    },

    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'host'
        }]
    }, {
        xtype: 'container',
        padding: 5,
        dock: 'top',
        html: 'Total installations of Secure, EOl, and Insecure programs across all hosts, including last week\'s data for comparison. Mouse over the bars to see exact values.'
    }],
    items: [{
        xtype: 'cartesian',
        itemId: 'chart',
        insetPadding: 25,
        innerPadding: {
            top: 20,
            left: 0,
            right: 10,
            bottom: 0
        },
        bind:{
            store: '{chart}'
        },
        axes: [{
            type: 'numeric',
            position: 'left',
            majorTickSteps: 4,
            reconcileRange: false,
            adjustByMajorUnit: true,
            minimum: 0,
            title: 'Installations',
            fields: ['today', 'last_week', 'last_month'],
            grid: true
        }, {
            type: 'category',
            position: 'bottom',
            fields: ['label'],
            grid: true
        }],
        series: {
            type: 'bar',
            //title: ['Currently', 'Last Week'],
            //title: ['Secure', 'Insecure', 'End-Of-Life],
            bind: '{chart}',
            xField: 'label',
            yField: ['today', 'last_week', 'last_month'],
            stacked: false,
            style: {
                minGapWidth: 10
            },
            highlight: false,
            renderer: function (sprite, config, data, index) {
                if (data.store.getAt(index)) {
                    var colors = {
                        secure: ['#42be5b', '#CCFF99', '#e8fad7'],
                        insecure: ['#c50221', '#FFCCCC', '#fce6e6'],
                        eol: ['#ffe300', '#FFFFCC', '#ffffe6']
                    }, chartFill = function (field, label, color) {

                        if (field == 'today') {
                            var ix = 0;
                        } else if (field == 'last_week') {
                            var ix = 1;
                        } else {
                            var ix = 2;
                        }

                        if (label === 'Secure') {
                            return color.secure[ix];
                        } else if (label === 'Insecure') {
                            return color.insecure[ix];
                        } else if (label === 'End-Of-Life') {
                            return color.eol[ix];
                        } else {
                            return '#000';
                        }
                    };

                    return {
                        opacity: 0.80,
                        fillStyle: chartFill(sprite.getField(), data.store.getAt(index).get('label'), colors)
                    };
                }
                return {
                    opacity: 0.80,
                    fillStyle: '#ccc'
                };
            },
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    if (item.field === 'today') {
                        tooltip.setHtml('Currently: ' + record.get('today'));
                    }
                    if (item.field === 'last_week') {
                        tooltip.setHtml('Last Week: ' + record.get('last_week'));
                    }
                    if (item.field === 'last_month') {
                        tooltip.setHtml('Last Month: ' + record.get('last_month'));
                    }
                }
            }
        }
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }

});