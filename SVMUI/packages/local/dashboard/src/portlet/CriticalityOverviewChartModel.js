Ext.define('Dashboard.portlet.CriticalityOverviewChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-criticalityoverviewchartmodel',

    stores: {
        chart: {
            autoLoad: true,
            pageSize: 0,
            fields: [{
                name: 'vuln_criticality'
            }, {
                name: 'no_insecure'
            }, {
                name: 'no_eol'
            }, {
                name: 'hist_vuln_criticality'
            }, {
                name: 'hist_no_insecure'
            }, {
                name: 'hist_no_eol'
            }, {
                name: 'month_vuln_criticality'
            }, {
                name: 'month_no_insecure'
            }, {
                name: 'month_no_eol'
            }],
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_criticality_overview&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data)) {
                                var historyResult = data.data.criticalityData_7DAY;
                                var historyResultMonth = data.data.criticalityData_1MONTH,
                                    currentResult = data.data.criticalityData,
                                    chartData = [],
                                    row = {};

                                for (var j = 1; j <= 5; j++) {
                                    row = {};
                                    row.vuln_criticality = j;
                                    row.no_insecure = 0;
                                    row.no_eol = 0;
                                    row.hist_no_insecure = 0;
                                    row.hist_no_eol = 0;
                                    row.month_no_insecure = 0;
                                    row.month_no_eol = 0;
                                    chartData[j - 1] = row;
                                }

                                var response = chartData.map(function (item) {
                                    for (var i = 0; i < 5; i++) {
                                        if (typeof currentResult[i] !== 'undefined' && item.vuln_criticality == currentResult[i].vuln_criticality) {
                                            item.no_insecure = currentResult[i].no_insecure;
                                            item['no_insecure_'+currentResult[i].vuln_criticality] = currentResult[i].no_insecure;
                                            item.no_eol = currentResult[i].no_eol;
                                            item['no_eol_'+currentResult[i].vuln_criticality] = currentResult[i].no_eol;
                                            //break; // break out - we found our match
                                        }
                                        if (typeof historyResult[i] !== 'undefined' && item.vuln_criticality == historyResult[i].vuln_criticality) {
                                            item.hist_no_insecure = historyResult[i].no_insecure;
                                            item['hist_no_insecure_'+historyResult[i].vuln_criticality] = historyResult[i].no_insecure;
                                            item.hist_no_eol = historyResult[i].no_eol;
                                            item['hist_no_eol_'+historyResult[i].vuln_criticality] = historyResult[i].no_eol;
                                            //break; // break out - we found our match
                                        }
                                       if (typeof historyResultMonth[i] !== 'undefined' && item.vuln_criticality == historyResultMonth[i].vuln_criticality) {
                                           item.month_no_insecure = historyResultMonth[i].no_insecure;
                                           item['month_no_insecure_' + historyResultMonth[i].vuln_criticality] = historyResultMonth[i].no_insecure;
                                           item.month_no_eol = historyResultMonth[i].no_eol;
                                           item['month_no_eol_' + historyResultMonth[i].vuln_criticality] = historyResultMonth[i].no_eol;
                                           //break; // break out - we found our match
                                       }
                                    }
                                    return item;
                                });
                                return response;

                            }
                        },
                        scope: this
                    }
                }
            },
            listeners: {
                load: 'onCriticalityOverviewScoreLoad'
            }
        }
    }
});