Ext.define('Dashboard.portlet.SiteOverviewSystemScoreChart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-site-overview-system-scorechart',
        'widget.chart_site_overview_system_score'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.axis.Category',
        'Ext.chart.series.Bar',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'dashboard-portlet-siteoverviewsystemscorechartcontroller',
    viewModel: {
        type: 'dashboard-portlet-siteoverviewsystemscorechartmodel'
    },
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'host'
        }]
    },{
        xtype: 'container',
        padding: 5,
        dock: 'top',
        html: 'Average System Score for each site, including last week\'s data for comparison.  Mouse over the bars to see exact values.'
    }],
    items: [{
        xtype: 'cartesian',
        itemId: 'chart',
        theme: 'flexera-default',
        bind: {
            store: '{chart}'
        },
        innerPadding: {
            top: 10,
            left: 0,
            right: 20,
            bottom: 10
        },
        insetPadding: 20,
        axes: [{
            type: 'numeric',
            adjustByMajorUnit: true,
            majorTickSteps: 3,
            reconcileRange: true,
            position: 'bottom',
            minimum: 0,
            fields: ['hist_month_score', 'hist_score', 'score'],
            // fields: ['no_insecure'],
            grid: true
        }, {
            type: 'category',
            position: 'left',
            // hidden: true,
            fields: ['site'],
            renderer: function (axis, label, layoutContext, lastLabel) {
                return label.length > 15 ? Ext.util.Format.ellipsis(label, 15) : label;
            },
            grid: true
        }],
        flipXY: true,
        series: [{
            type: 'bar',
            //axis: 'bottom',
            // title: ['Extremely Critical', 'Highly Critical', 'Moderately Critical', 'Less Critical', 'Not Critical'],
            stacked: false,
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    if (item.field == 'score') {
                        var score = !Ext.isEmpty(record.get('score')) ? record.get('score') + '%': 'No Data Found';
                        tooltip.setHtml(record.get('site') + ': Average System Score: ' + score);
                    }
                    if (item.field == 'hist_score') {
                        var histScore = !Ext.isEmpty(record.get('hist_score')) ? record.get('hist_score') + '%': 'No Data Found';
                        tooltip.setHtml('Last Week: Average System Score:: ' + histScore);
                    }
                    if (item.field == 'hist_month_score') {
                        var histMonthScore = !Ext.isEmpty(record.get('hist_month_score')) ? record.get('hist_month_score') + '%': 'No Data Found';
                        tooltip.setHtml('Last Month: Average System Score:: ' + histMonthScore);
                    }
                }
            },
            xField: 'site',
            yField: ['hist_month_score', 'hist_score', 'score'],
            // style: {
            //     minGapWidth: 20
            // },
            highlight: false,
            renderer: function (sprite, config, data, index) {
                if (data.store.getAt(index)) {
                    var colors = [{
                            current: '#c50221',
                            history: '#FFCCCC',
                            history_month: '#fce6e6'
                        }, {
                            current: '#ffe300',
                            history: '#FFFFCC',
                            history_month: '#ffffe6'
                        }, {
                            current: '#24a23e',
                            history: '#CCFF99',
                            history_month: '#e8fad7'
                        }, {
                            current: '#e8dfdf',
                            history: '#e8dfdf',
                            history_month: '#e8dfdf'
                        }],
                        findColorByScore = function (type) {
                            var scoreColor = {};

                            if (data.store.getAt(index).get(type) < 80) {
                                scoreColor = colors[0];
                            } else if (data.store.getAt(index).get(type) >= 80 && data.store.getAt(index).get(type) < 95) {
                                scoreColor = colors[1];
                            } else if (data.store.getAt(index).get(type) >= 95 ) {
                                scoreColor = colors[2];
                            } else {
                                scoreColor = colors[3];
                            }
                            return scoreColor;
                        };
                    getColour = function () {
                      var appColour = '#ccc';

                        if (sprite.getField() == 'score') {
                            appColour = findColorByScore('score').current;
                        } else if (sprite.getField() == 'hist_score') {
                            appColour = findColorByScore('hist_score').history;
                        } else {
                            appColour = findColorByScore('hist_month_score').history_month;
                        }

                        return appColour;
                    };



                    return {
                        opacity: 0.80,
                        fillStyle: getColour()
                    };
                }
                return {
                    //opacity: 0.80,
                    fillStyle: '#ccc'
                };
            }
        }]
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }
});