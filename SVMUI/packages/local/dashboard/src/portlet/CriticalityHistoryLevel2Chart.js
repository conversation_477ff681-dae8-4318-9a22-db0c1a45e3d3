Ext.define('Dashboard.portlet.CriticalityHistoryLevel2Chart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-criticality-history-level2-chart',
        'widget.chart_criticality_history_level2'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    viewModel: {
        stores: {
            chart: {
                sorters: [
                    {
                        property: 'order',
                        direction: 'ASC'
                    }
                ],
                fields: ['vuln_criticality', 'no_insecure', 'no_eol', 'when', 'order'],
                autoLoad: false,
                pageSize: 0,
                proxy: {
                    type: 'ajax',
                    url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_criticality_history_level2&',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        transform: {
                            fn: function (data) {
                                var result = [],
                                    buildData = function (criticalityData, vuln_criticality, extraData) {
                                        var data;

                                        for (var i = 0; i < criticalityData.length; i++) {
                                            if (criticalityData[i].vuln_criticality == vuln_criticality) {
                                                data = Ext.apply(extraData, criticalityData[i]);
                                                break; // break out - we found our match
                                            }
                                        }
                                        if (!Ext.isObject(data)) {
                                            data = Ext.apply(extraData, {
                                                no_insecure: null,
                                                no_eol: null
                                            });
                                        }

                                        return data;
                                    };
                                result.push(buildData(data.data.criticalityData, 2, {order: 6, when: 'Now'}));
                                result.push(buildData(data.data.criticalityData_7DAY, 2, {order: 5, when: '1 Week'}));
                                result.push(buildData(data.data.criticalityData_14DAY, 2, {order: 4, when: '2 Weeks'}));
                                result.push(buildData(data.data.criticalityData_21DAY, 2, {order: 3, when: '3 Weeks'}));
                                result.push(buildData(data.data.criticalityData_28DAY, 2, {order: 2, when: '4 Weeks'}));
                                result.push(buildData(data.data.criticalityData_35DAY, 2, {order: 1, when: '5 Weeks'}));

                                return result;
                            },
                            scope: this
                        },
                    }
                }
            },
        }
    },
    dockedItems: [{
        xtype: 'component',
        padding: 5,
        dock: 'top',
        html: 'History of the number of "Highly Critical" rated vulnerabilities relevant to your system.  Data covers up to the last 5 weeks where possible.  Mouse over data points to see exact values.'
    }],
    items: [{
        xtype: 'cartesian',
        itemId: 'chart',
        theme: 'flexera-default',
        bind: {
            store: '{chart}'
        },
        innerPadding: 20,
        legend: {
            docked: 'top',
            type: 'sprite'
        },
        axes: [{
            type: 'numeric',
            adjustByMajorUnit: true,
            majorTickSteps: 3,
            reconcileRange: true,
            position: 'left',
            minimum: 0,
            fields: ['no_insecure'],
            grid: true,
        }, {
            type: 'category',
            position: 'bottom',
            fields: ['when'],
            grid: true
        }],
        series: [{
            type: 'line',
            title: 'Highly Critical',
            colors: [
                '#ff810b'
            ],
            highlight: {
                fillStyle: '#ff810b',
                radius: 7,
                lineWidth: 2,
                strokeStyle: '#000'
            },
            marker: {
                radius: 4,
                lineWidth: 2
            },
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    tooltip.setHtml(record.get('when') + ': Insecure: ' + record.get('no_insecure') + ', EOL: ' + record.get('no_eol'));
                }
            },
            xField: 'when',
            yField: ['no_insecure'],
            style: {
                inGroupGapWidth: -7
            }
        }]
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }
});