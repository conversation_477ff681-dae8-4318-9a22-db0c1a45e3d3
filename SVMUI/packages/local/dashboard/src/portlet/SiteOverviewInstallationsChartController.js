Ext.define('Dashboard.portlet.SiteOverviewInstallationsChartController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-siteoverviewinstallationschartcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('chart').load({
            params: {
                host_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function(evt){
        //debugger;
    },

    onSiteOverviewScoreLoad: function (store, records) {
        const me = this,
            view = me.getView(),
            cartesian = view.down('cartesian');

        var axes= cartesian.getAxes();
        var numeric = Ext.Array.findBy(axes, function(item) { return item.type == 'numeric'; });
        if(numeric) {
            const scores = Ext.Array.map(records, function(item){

                var no_eol = parseInt(item.data.no_eol || 0, 10);
                var no_insecure = parseInt(item.data.no_insecure || 0, 10);
                var no_patched = parseInt(item.data.no_patched || 0, 10);

                return no_eol + no_insecure + no_patched;
            });
            const maxScore = Ext.Array.max(scores);
            numeric.setMaximum(me.roundIntToNearest(maxScore));
            cartesian.redraw();
        }
    },

    roundIntToNearest: function (max) {
        var maxPos = (max * 1.05).toFixed(),
            powOf10 = maxPos.length > 4 ? maxPos.length-3: 2,
            divideBy = Math.pow(10, powOf10);

        if (powOf10 == 2) {
            return Math.ceil(maxPos / divideBy) * divideBy;
        } else {
            return Math.round(maxPos / divideBy) * divideBy;
        }
    }

});