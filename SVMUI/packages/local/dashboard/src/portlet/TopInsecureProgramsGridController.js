Ext.define('Dashboard.portlet.TopInsecureProgramsGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-topinsecureprogramsgridcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('topInsecureProgramsStore').load({
            params: {
                product_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function (evt) {
        if (evt.target.dataset.id > 0) {
            var options = {};
            options.extra = evt.record;
            sfw.Default.viewInstallation(evt, evt.target, options);
        }
    }
});