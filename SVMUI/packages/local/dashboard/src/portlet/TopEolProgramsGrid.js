Ext.define('Dashboard.portlet.TopEolProgramsGrid', {
    extend: 'Ext.grid.Panel',
    alias: [
        'widget.dashboard-portlet-topeolprogramsgrid',
        'widget.top_eol_programs'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    controller: 'dashboard-portlet-topeolprogramsgridcontroller',
    viewModel: {
        type: 'dashboard-portlet-topeolprogramsgridmodel'
    },
    bind: {
        store: '{topEOLPrograms}'
    },
    viewConfig: {
        emptyText: 'There are no End-of-Life Software Installations found on your system.'
    },
    listeners: {
        // itemdblclick:'onItemDblClick',
        // listener to handle clicks on .clickable elements
        click: {
            element: 'el',
            // scope: 'this',
            delegate: '.clickable',
            fn: 'onClick'
        }
    },
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'product'
        }]
    }],
    columns: [{
        xtype: 'rownumberer',
        text: '#',
        align: 'center',
        width: 40
    }, {
        xtype: 'templatecolumn',
        tpl: [
            '<span class="clickable" data-id="{product_id}" >{product_name}</span>'
        ],
        cellWrap: true,
        text: 'Software Name',
        flex: 1
    }, {
        align: 'right',
        text: 'Currently',
        width: 100,
        dataIndex: 'no_installations',
        renderer: function (value, metadata, record) {
            var histScore = record.get('hist7day_no_installations'),
                curScore = value;

            // Want red if new value bigger, green if smaller, else black, but green on both == 0, flip sign is true, no specialChars
            delta = Dashboard.Main.getChangesColorAndText((histScore >= 0 ? histScore : 0), curScore, true, true);
            textColor = delta[0];
            deltaString = delta[1];

            metadata.tdCls = textColor;
            return deltaString + ' ' + value;
        }
    }, {
        align: 'right',
        text: 'Last Week',
        width: 100,
        dataIndex: 'hist7day_no_installations',
        renderer: function (value, metadata, record) {
            return value > 0 ? value : "No Data";
        }
    }, {
        align: 'right',
        text: 'Last Month',
        width: 100,
        dataIndex: 'hist1month_no_installations',
        renderer: function (value, metadata, record) {
            return value > 0 ? value : "No Data";
        }
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function(){
        return this.getViewModel().getStore('topEOLPrograms');
    }
});