Ext.define('Dashboard.portlet.TopAdvisoriesGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-topadvisoriesgridcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('advisoryDataCriticalStore').load({
            params: {
                product_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function (evt) {
        //evt.record.data,
        if (evt.target.dataset.id > 0) {
            if (evt.target.dataset.type === 'vuln') {
                sfw.Default.advisoryView(1, evt.target.dataset.id);
            } else if (evt.target.dataset.type === 'installations') {
                sfw.Default.renderInstallationPopup(evt.target.dataset.id);
            }
        }
    }
});