Ext.define('Dashboard.portlet.SiteOverviewSystemScoreChartController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-siteoverviewsystemscorechartcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('chart').load({
            params: {
                host_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function(evt){
        //debugger;
    },

    onSystemScoreLoad: function (store, records) {
        const me = this,
            view = me.getView(),
            cartesian = view.down('cartesian');

        var axes= cartesian.getAxes();
        var numeric = Ext.Array.findBy(axes, function(item) { return item.type == 'numeric'; });
        // if(numeric) {
        //     const scores = Ext.Array.map(records, function(item){ return parseInt(item.data.score || 0, 10); });
        //     const maxScore = Ext.Array.max(scores);
        //     numeric.setMaximum(maxScore + 20);
        //     cartesian.redraw();
        // }

    }
});