Ext.define('Dashboard.portlet.LatestActivityPanel', {
    extend: 'Ext.panel.Panel',
    alias: [
        'widget.dashboard-portlet-latestactivitypanel',
        'widget.latest_activity'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    // controller: 'dashboard-portlet-latestactivitypanelcontroller',
    viewModel: {
        type: 'dashboard-portlet-latestactivitypanelmodel'
    },
    bodyPadding: 5,
    items: [{
        xtype: 'gridpanel',
        ui: 'light',
        reference: 'lastLoginGrid',
        title: 'Last logins',
        hideHeaders: true,
        viewConfig: {
            emptyText: 'N/A'
        },
        bind: {
            store: '{lastLogins}'
        },
        columns: [{
            xtype: 'templatecolumn',
            tpl: '{username} ({hostname})',
            cellWrap: true,
            flex: 1
        }, {
            xtype: 'datecolumn',
            dataIndex: 'time',
            format: 'jS M, Y H:i',
            renderer: function (value) {
                if (value) {
                    value = sfw.Util.dateCreate(value, true);
                    return Ext.util.Format.date(value, 'jS M, Y H:i');
                } else {
                    return '-';
                }
            },
            width: 150
        }]
    }, {
        xtype: 'gridpanel',
        ui: 'light',
        reference: 'lastReportSentGrid',
        title: 'Last Reports Sent',
        hideHeaders: true,
        viewConfig: {
            emptyText: 'N/A'
        },
        bind: {
            store: '{lastReportsSent}'
        },
        columns: [{
            xtype: 'templatecolumn',
            tpl: '{activity_text} ({username})',
            cellWrap: true,
            flex: 1
        }, {
            xtype: 'datecolumn',
            dataIndex: 'time',
            format: 'jS M, Y H:i',
            renderer: function (value) {
                if (value) {
                    value = sfw.Util.dateCreate(value, true);
                    return Ext.util.Format.date(value, 'jS M, Y H:i');
                } else {
                    return '-';
                }
            },
            width: 150
        }]
    }, {
        xtype: 'gridpanel',
        ui: 'light',
        reference: 'lastPackagesGrid',
        title: 'Last Packages Created',
        hideHeaders: true,
        viewConfig: {
            emptyText: 'N/A'
        },
        bind: {
            store: '{lastPackages}'
        },
        columns: [{
            xtype: 'templatecolumn',
            tpl: '{activity_text_friendly} ({hostname})',
            cellWrap: true,
            flex: 1
        }, {
            xtype: 'datecolumn',
            dataIndex: 'time',
            format: 'jS M, Y H:i',
            renderer: function (value) {
                if (value) {
                    value = sfw.Util.dateCreate(value, true);
                    return Ext.util.Format.date(value, 'jS M, Y H:i');
                } else {
                    return '-';
                }
            },
            width: 150
        }]
    }, {
        xtype: 'gridpanel',
        ui: 'light',
        reference: 'lastUserManagementGrid',
        title: 'Last User Management Actions',
        hideHeaders: true,
        viewConfig: {
            emptyText: 'N/A'
        },
        bind: {
            store: '{lastUserManagement}'
        },
        columns: [{
            xtype: 'templatecolumn',
            tpl: '{activity_text_friendly} ({username})',
            cellWrap: true,
            flex: 1
        }, {
            xtype: 'datecolumn',
            dataIndex: 'time',
            format: 'jS M, Y H:i',
            renderer: function (value) {
                if (value) {
                    value = sfw.Util.dateCreate(value, true);
                    return Ext.util.Format.date(value, 'jS M, Y H:i');
                } else {
                    return '-';
                }
            },
            width: 150
        }]
    }],

    refreshPortletData: function () {
        this.getViewModel().getStore('lastLogins').reload();
        this.getViewModel().getStore('lastReportsSent').reload();
        this.getViewModel().getStore('lastPackages').reload();
        this.getViewModel().getStore('lastUserManagement').reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('lastLogins');
    }


});