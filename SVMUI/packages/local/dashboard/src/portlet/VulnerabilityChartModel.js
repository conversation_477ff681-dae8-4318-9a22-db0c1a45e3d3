Ext.define('Dashboard.portlet.VulnerabilityChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-vulnerability',

    stores: {

        chart: {
            fields: [{
                name: 'date'
            }, {
                name: 'cve_remediated'
            }, {
                name: 'cve_available'
            }],
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_vulnerabilty&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data.data)) {
                                var historyResult = data.data.data;

                                response = data.data.data.map(function (item) {
                                    item.date = item.date;
                                    item.cve_remediated = item.cve_remediated;
                                    item.cve_available = item.cve_available;
                                    return item;
                                });

                                return response;
                            }
                        },
                        scope: this
                    }
                }
            }
        }
    }



});