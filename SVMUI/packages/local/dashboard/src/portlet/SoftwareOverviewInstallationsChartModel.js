Ext.define('Dashboard.portlet.SoftwareOverviewInstallationsChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-softwareoverviewinstallationschartmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=host&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        chart: {
            fields: ['status', 'today', 'last_week', 'last_month'],
            autoLoad: false,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_software_overview_installations&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data)) {
                                var response = [{
                                    label: 'Secure',
                                    today: parseInt(data.data.numProgramsPatched, 10) + parseInt(data.data.numOSPatched, 10),
                                    last_week: parseInt(data.data.numProgramsPatched_7DAY, 10) + parseInt(data.data.numOSPatched_7DAY, 10),
                                    last_month: parseInt(data.data.numProgramsPatched_1MONTH, 10) + parseInt(data.data.numOSPatched_1MONTH, 10)
                                }, {
                                    label: 'Insecure',
                                    today: parseInt(data.data.numProgramsInsecure, 10) + parseInt(data.data.numOSInsecure, 10),
                                    last_week: parseInt(data.data.numProgramsInsecure_7DAY, 10) + parseInt(data.data.numOSInsecure_7DAY, 10),
                                    last_month: parseInt(data.data.numProgramsInsecure_1MONTH, 10) + parseInt(data.data.numOSInsecure_1MONTH, 10)
                                }, {
                                    label: 'End-Of-Life',
                                    today: parseInt(data.data.numProgramsEOL, 10) + parseInt(data.data.numOSEOL, 10),
                                    last_week: parseInt(data.data.numProgramsEOL_7DAY, 10) + parseInt(data.data.numOSEOL_7DAY, 10),
                                    last_month: parseInt(data.data.numProgramsEOL_1MONTH, 10) + parseInt(data.data.numOSEOL_1MONTH, 10)
                                }];
                                return response;
                            } else {
                                return {};
                            }
                        },
                        scope: this
                    }
                }
            }
        }
    }
});