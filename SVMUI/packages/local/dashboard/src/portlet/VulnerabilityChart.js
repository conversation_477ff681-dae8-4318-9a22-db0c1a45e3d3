Ext.define('Dashboard.portlet.VulnerabilityChart', {
    extend: 'Dashboard.portlet.BasePanel',
    xtype: 'column-stacked',
    alias: [
        'widget.dashboard-portlet-vulnerabilitychart',
        'widget.chart_vulnerability'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.axis.Category',
        'Ext.chart.series.Bar',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'column-stacked',

    viewModel: {
        type: 'dashboard-portlet-vulnerability'
    },

    width: 650,

   items: [{
        xtype: 'cartesian',
        reference: 'chart',
        itemId: 'chart',

        height: 460,

       /*store: {
            type: 'chart'
        },*/
       theme: 'flexera-default',
        legend: {
            type: 'sprite',
            docked: 'bottom'
        },
       insetPadding: 25,
       innerPadding: {
           top: 20,
           left: 0,
           right: 10
       },
        bind:{
            store: '{chart}'
        },
        axes: [{
            type: 'numeric',
            position: 'left',
            adjustByMajorUnit: true,
            grid: true,
            fields: ['cve_available'],
            /*renderer: 'onAxisLabelRender',*/
            minimum: 0,
            majorTickSteps: 3,
            reconcileRange: true,
        }, {
            type: 'category',
            position: 'bottom',
            grid: true,
            fields: ['date'],
            label: {
                rotate: {
                    degrees: -80
                }
            }
        }],
        series: [{
            type: 'bar',
            title: [ 'Vulnerabilities Found', 'Vulnerabilities Resolved'],
            xField: 'date',
            yField: [ 'cve_available', 'cve_remediated'],
            stacked: false,
            style: {
                opacity: 0.80
            },
            tooltip: {
                renderer: 'onBarTipRender'
            },
            colors: [
                '#f83352', '#d0fc54'
            ]
        }]
    }],

    tbar: [
        '->',
        {
            xtype: 'segmentedbutton',
            width: 200,
            defaults: { ui: 'default-toolbar' },
            items: [
                {
                    text: 'Group',
                    pressed: true
                },
                {
                    text: 'Stack'
                }
            ],
            listeners: {
                toggle: 'onStackGroupToggle'
            }
        }
    ],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }

});
