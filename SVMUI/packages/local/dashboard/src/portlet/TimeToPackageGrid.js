Ext.define('Dashboard.portlet.TimeToPackageGrid', {
    extend: 'Ext.grid.Panel',
    alias: [
        'widget.dashboard-portlet-timetopackagegrid',
        'widget.time_to_package'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    controller: 'dashboard-portlet-timetopackagegridcontroller',
    viewModel: {
        type: 'dashboard-portlet-timetopackagegridmodel'
    },
    bind: {
        store: '{timeToPackageGridStore}'
    },
    viewConfig: {
        emptyText: 'No vulnerable software package updates have been created and deployed yet. There is currently no update package data available to display.'
    },

    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        }
    }],
    columns: [{
        text: 'Time Elapsed',
        dataIndex: 'time_elapsed',
        align: 'left',
        flex: 1
    }, {
        text: 'Package',
        flex: 1,
        align: 'left',
        dataIndex: 'package',
        cellWrap: true
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function(){
        return this.getViewModel().getStore('timeToPackageGridStore');
    }
});
