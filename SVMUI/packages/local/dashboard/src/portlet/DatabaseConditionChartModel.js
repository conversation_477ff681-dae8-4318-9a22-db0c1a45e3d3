Ext.define('Dashboard.portlet.DatabaseConditionChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-databaseconditionchartmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=host&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        dbConditions: {
            autoLoad: false,
            pageSize: 0,
            fields: ['no_hosts', 'time_period'],
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_database_condition&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data)) {
                                var hosts = data.data.DBCondition.split(','),
                                    count = hosts.map(function (v) {
                                        return parseInt(v, 10);
                                    }),
                                    total = count[0] + count[1] + count[2] + count[3],
                                    value = [{
                                        time_period: '0 - 7 days' + ' (' + count[0] + ')',
                                        percent_hosts: Math.round(count[0] * 100 / total),
                                        no_hosts: count[0]
                                    }, {
                                        time_period: '8 - 14 days' + ' (' + count[1] + ')',
                                        percent_hosts: Math.round(count[1] * 100 / total),
                                        no_hosts: count[1]
                                    }, {
                                        time_period: '15 - 30 days' + ' (' + count[2] + ')',
                                        percent_hosts: Math.round(count[2] * 100 / total),
                                        no_hosts: count[2]
                                    }, {
                                        time_period: '31+ days' + ' (' + count[3] + ')',
                                        percent_hosts: Math.round(count[3] * 100 / total),
                                        no_hosts: count[3]
                                    }];
                                return value;
                            }
                        },
                        scope: this
                    }
                }
            }
        }
    }
});