Ext.define('Dashboard.portlet.CriticalityOverviewChart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-criticality-overview-chart',
        'widget.chart_criticality_overview'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.axis.Category',
        'Ext.chart.series.Bar',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'dashboard-portlet-criticalityoverviewchartcontroller',
    viewModel: {
        type: 'dashboard-portlet-criticalityoverviewchartmodel'
    },
    dockedItems: [{
        xtype: 'component',
        padding: 5,
        dock: 'top',
        html: 'Total number of installations vulnerable to the various criticality levels of vulnerability advisories.  Last week\'s data is included for comparison.  Mouse over the bars to see exact values.'
    }],
    items: [{
        xtype: 'cartesian',
        itemId: 'chart',
        theme: 'flexera-default',
        bind: {
            store: '{chart}'
        },
        interactions: [
            {
                type: 'itemhighlight'
            }
        ],
        legend: {
            docked: 'bottom'
        },
        insetPadding: 25,
        innerPadding: {
            top: 20,
            left: 0,
            right: 10,
            bottom: 10
        },
        axes: [{
            type: 'numeric',
            adjustByMajorUnit: true,
            majorTickSteps: 4,
            reconcileRange: true,
            position: 'left',
            minimum: 0,
            fields: ['no_insecure_1', 'hist_no_insecure_1', 'month_no_insecure_1', 'no_insecure_2', 'hist_no_insecure_2', 'month_no_insecure_2', 'no_insecure_3', 'hist_no_insecure_3', 'month_no_insecure_3', 'no_insecure_4', 'hist_no_insecure_4', , 'month_no_insecure_4', 'no_insecure_5', 'hist_no_insecure_5', 'month_no_insecure_5'],
            grid: true
        }, {
            type: 'category',
            position: 'bottom',
            hidden: true,
            fields: ['vuln_criticality'],
            grid: true
        }],
        series: [
            {
                type: 'bar',
                title: ['Extremely Critical'],
                stacked: false,
                tooltip: {
                    trackMouse: true,
                    showDelay: 0,
                    dismissDelay: 0,
                    hideDelay: 0,
                    renderer: function (tooltip, record, item) {
                        if (item.field == 'no_insecure_1') {
                            tooltip.setHtml(item.series.getTitle()[parseInt(record.get('vuln_criticality'), 10) - 1] + ': Insecure: ' + record.get('no_insecure_1') + ', EOL: ' + record.get('no_eol_1'));
                        }
                        if (item.field == 'hist_no_insecure_1') {
                            tooltip.setHtml('Last Week: Insecure: ' + record.get('hist_no_insecure_1') + ', EOL: ' + record.get('hist_no_eol_1'));
                        }
                        if (item.field == 'month_no_insecure_1') {
                            tooltip.setHtml('Last Month: Insecure: ' + record.get('month_no_insecure_1') + ', EOL: ' + record.get('month_no_eol_1'));
                        }
                    }
                },
                xField: 'vuln_criticality',
                yField: ['no_insecure_1', 'hist_no_insecure_1', 'month_no_insecure_1'],
                hideInLegendFields: ['hist_no_insecure_1'],
                style: {
                    minGapWidth: 10
                },
                highlight: false,
                colors: [
                    '#c50221', '#f83352', '#FFCCCC'
                ]
            }, {
                type: 'bar',
                title: ['Highly Critical'],
                stacked: false,
                tooltip: {
                    trackMouse: true,
                    showDelay: 0,
                    dismissDelay: 0,
                    hideDelay: 0,
                    renderer: function (tooltip, record, item) {
                        if (item.field == 'no_insecure_2') {
                            tooltip.setHtml(item.series.getTitle()[0] + ': Insecure: ' + record.get('no_insecure_2') + ', EOL: ' + record.get('no_eol_2'));
                        }
                        if (item.field == 'hist_no_insecure_2') {
                            tooltip.setHtml('Last Week: Insecure: ' + record.get('hist_no_insecure_2') + ', EOL: ' + record.get('hist_no_eol_2'));
                        }
                        if (item.field == 'month_no_insecure_2') {
                            tooltip.setHtml('Last Month: Insecure: ' + record.get('month_no_insecure_2') + ', EOL: ' + record.get('month_no_eol_2'));
                        }
                    }
                },
                xField: 'vuln_criticality',
                yField: ['no_insecure_2', 'hist_no_insecure_2', 'month_no_insecure_2'],
                hideInLegendFields: ['hist_no_insecure_2'],
                style: {
                    minGapWidth: 10
                },
                highlight: false,
                colors: [
                    '#ff810b', '#fca956', '#FFCC99'
                ]
            }, {
                type: 'bar',
                title: ['Moderately Critical'],
                stacked: false,
                tooltip: {
                    trackMouse: true,
                    showDelay: 0,
                    dismissDelay: 0,
                    hideDelay: 0,
                    renderer: function (tooltip, record, item) {
                        if (item.field == 'no_insecure_3') {
                            tooltip.setHtml(item.series.getTitle()[0] + ': Insecure: ' + record.get('no_insecure_3') + ', EOL: ' + record.get('no_eol_3'));
                        }
                        if (item.field == 'hist_no_insecure_3') {
                            tooltip.setHtml('Last Week: Insecure: ' + record.get('hist_no_insecure_3') + ', EOL: ' + record.get('hist_no_eol_3'));
                        }
                        if (item.field == 'month_no_insecure_3') {
                            tooltip.setHtml('Last Month: Insecure: ' + record.get('month_no_insecure_3') + ', EOL: ' + record.get('month_no_eol_3'));
                        }
                    }
                },
                xField: 'vuln_criticality',
                yField: ['no_insecure_3', 'hist_no_insecure_3', 'month_no_insecure_3'],
                hideInLegendFields: ['hist_no_insecure_3'],
                style: {
                    minGapWidth: 10
                },
                highlight: false,
                colors: [
                    '#ffe300', '#fafa50', '#f3f39f'
                ]
            }, {
                type: 'bar',
                title: ['Less Critical'],
                stacked: false,
                tooltip: {
                    trackMouse: true,
                    showDelay: 0,
                    dismissDelay: 0,
                    hideDelay: 0,
                    renderer: function (tooltip, record, item) {
                        if (item.field == 'no_insecure_4') {
                            tooltip.setHtml(item.series.getTitle()[0] + ': Insecure: ' + record.get('no_insecure_4') + ', EOL: ' + record.get('no_eol_4'));
                        }
                        if (item.field == 'hist_no_insecure_4') {
                            tooltip.setHtml('Last Week: Insecure: ' + record.get('hist_no_insecure_4') + ', EOL: ' + record.get('hist_no_eol_4'));
                        }
                        if (item.field == 'month_no_insecure_4') {
                            tooltip.setHtml('Last Month: Insecure: ' + record.get('month_no_insecure_4') + ', EOL: ' + record.get('month_no_eol_4'));
                        }
                    }
                },
                xField: 'vuln_criticality',
                yField: ['no_insecure_4', 'hist_no_insecure_4', 'month_no_insecure_4'],
                hideInLegendFields: ['hist_no_insecure_4'],
                style: {
                    minGapWidth: 10
                },
                highlight: false,
                colors: [
                    '#ace803', '#d0fc54', '#ebfac1'
                ]
            }, {
                type: 'bar',
                title: ['Not Critical'],
                stacked: false,
                tooltip: {
                    trackMouse: true,
                    showDelay: 0,
                    dismissDelay: 0,
                    hideDelay: 0,
                    renderer: function (tooltip, record, item) {
                        if (item.field == 'no_insecure_5') {
                            tooltip.setHtml(item.series.getTitle()[0] + ': Insecure: ' + record.get('no_insecure_5') + ', EOL: ' + record.get('no_eol_5'));
                        }
                        if (item.field == 'hist_no_insecure_5') {
                            tooltip.setHtml('Last Week: Insecure: ' + record.get('hist_no_insecure_5') + ', EOL: ' + record.get('hist_no_eol_5'));
                        }
                        if (item.field == 'month_no_insecure_5') {
                            tooltip.setHtml('Last Month: Insecure: ' + record.get('month_no_insecure_5') + ', EOL: ' + record.get('month_no_eol_5'));
                        }
                    }
                },
                xField: 'vuln_criticality',
                yField: ['no_insecure_5', 'hist_no_insecure_5', 'month_no_insecure_5'],
                hideInLegendFields: ['hist_no_insecure_5'],
                style: {
                    minGapWidth: 10
                },
                highlight: false,
                colors: [
                    '#04c52b', '#89c74b','#9dc971'
                ]
            }]
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }

});