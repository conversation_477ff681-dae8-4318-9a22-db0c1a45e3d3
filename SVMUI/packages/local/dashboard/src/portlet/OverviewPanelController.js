Ext.define('Dashboard.portlet.OverviewPanelController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-overviewpanelcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('overview').load({
            params: {
                host_smartgroup_id: combo.getValue()
            }
        });
    },

    onOverviewStoreLoad: function (store, records) {
        const me = this,
            view = me.getView();

        view.setHtml(me.portletOverviewHtmlGenerator(records[0].data.data));
    },

    onClick: function (evt) {
        //debugger;
    },

    portletOverviewHtmlGenerator: function( data ) {
        const me = this;

        var output = '<table class="dashboard_portlet" cellspacing="0" style="width:100%">';

        var averageScore, numHosts, numPrograms, numOSs, numAllSoftware;
        var averageScore_hist, numHosts_hist, numPrograms_hist, numOSs_hist, numAllSoftware_hist;
        var averageScore_hist_lastMonth, numHosts_hist_lastMonth, numPrograms_hist_lastMonth, numOSs_hist_lastMonth, numAllSoftware_hist_lastMonth;
        var numProgramsPatched, numProgramsInsecure, numProgramsEOL, numOSPatched, numOSInsecure, numOSEOL;
        var numProgramsPatched_hist, numProgramsInsecure_hist, numProgramsEOL_hist, numOSPatched_hist, numOSInsecure_hist, numOSEOL_hist;
        var numProgramsPatched_hist_lastMonth, numProgramsInsecure_hist_lastMonth, numProgramsEOL_hist_lastMonth, numOSPatched_hist_lastMonth, numOSInsecure_hist_lastMonth, numOSEOL_hist_lastMonth;

        averageScore = 	parseInt( data.averageScore, 10 ) | 0; // ok to parse this as int
        averageScore_hist =	data.averageScore_7DAY | 0;      // this one could be 'n/a' - parsing done in advance
        averageScore_hist_lastMonth = data.averageScore_1MONTH | 0;

        // Pulling numSites from overview (see other comment below).  Remove completely eventually.
        //var numSites = parseInt(data.numSites, 10);
        numHosts = parseInt( data.numHosts, 10 );

        numHosts_hist = parseInt( data.numHosts_7DAY, 10 );
        numHosts_hist_lastMonth = parseInt( data.numHosts_1MONTH, 10 );

        numProgramsPatched = parseInt( data.numProgramsPatched, 10 ) | 0;
        numProgramsInsecure = parseInt( data.numProgramsInsecure, 10 ) | 0;
        numProgramsEOL = parseInt( data.numProgramsEOL, 10 ) | 0;

        numProgramsPatched_hist = parseInt( data.numProgramsPatched_7DAY, 10 ) | 0;
        numProgramsInsecure_hist = parseInt( data.numProgramsInsecure_7DAY, 10 ) | 0;
        numProgramsEOL_hist = parseInt( data.numProgramsEOL_7DAY, 10 ) | 0;

        numProgramsPatched_hist_lastMonth = parseInt( data.numProgramsPatched_1MONTH, 10 ) | 0;
        numProgramsInsecure_hist_lastMonth = parseInt( data.numProgramsInsecure_1MONTH, 10 ) | 0;
        numProgramsEOL_hist_lastMonth = parseInt( data.numProgramsEOL_1MONTH, 10 ) | 0;

        numOSPatched = parseInt( data.numOSPatched, 10 ) | 0;
        numOSInsecure = parseInt( data.numOSInsecure, 10 ) | 0;
        numOSEOL = parseInt( data.numOSEOL, 10 ) | 0;

        numOSPatched_hist = parseInt( data.numOSPatched_7DAY, 10 ) | 0;
        numOSInsecure_hist = parseInt( data.numOSInsecure_7DAY, 10 ) | 0;
        numOSEOL_hist = parseInt( data.numOSEOL_7DAY, 10 ) | 0;

        numOSPatched_hist_lastMonth = parseInt( data.numOSPatched_1MONTH, 10 ) | 0;
        numOSInsecure_hist_lastMonth = parseInt( data.numOSInsecure_1MONTH, 10 ) | 0;
        numOSEOL_hist_lastMonth = parseInt( data.numOSEOL_1MONTH, 10 ) | 0;

        numPrograms = numProgramsPatched + numProgramsInsecure + numProgramsEOL;
        numOSs = numOSPatched + numOSInsecure + numOSEOL;
        numAllSoftware = numPrograms + numOSs;

        numPrograms_hist = numProgramsPatched_hist + numProgramsInsecure_hist + numProgramsEOL_hist;
        numOSs_hist = numOSPatched_hist + numOSInsecure_hist + numOSEOL_hist;
        numAllSoftware_hist = numPrograms_hist + numOSs_hist;

        numPrograms_hist_lastMonth = numProgramsPatched_hist_lastMonth + numProgramsInsecure_hist_lastMonth + numProgramsEOL_hist_lastMonth;
        numOSs_hist_lastMonth = numOSPatched_hist_lastMonth + numOSInsecure_hist_lastMonth + numOSEOL_hist_lastMonth;
        numAllSoftware_hist_lastMonth = numPrograms_hist_lastMonth + numOSs_hist_lastMonth;

        var delta = [];
        var deltaString, textColor;

        var lineBreak = '<tr><td><br /></td></tr>';

        output += '<tr><td width="44%">&nbsp;</td>';
        output += '<td width="18%" class="RightAlign"> </td>';
        output += '<td width="17%" class="RightAlign"><b>Currently (vs Last Week)</b></td>';
        output += '<td width="18%" class="RightAlign"><b>Last &nbsp; Week</b>&nbsp;</td>';
        output += '<td width="18%" class="RightAlign"><b>Last Month</b></td>';
        output += '<td width="3%">&nbsp;</td></tr>';

        output +=  lineBreak;

        // Want red if new value smaller, green if bigger, else black, flip false, specialChar is '%'
        delta = me.getChangesColorAndText( averageScore, averageScore_hist, false, false, '%' );
        textColor = delta[0];
        deltaString = delta[1];
        output += '<tr><td><b>Average Flexera System Score:</b></td>';
        output += '<td> </td>';
        output += '<td class="RightAlign"><FONT COLOR="' + textColor + '">' + deltaString + averageScore + '%</FONT></td>';
        output += '<td class="RightAlign">' + averageScore_hist + (isNaN(averageScore_hist) ? '' : '%') + '</td>';
        output += '<td class="RightAlign">' + averageScore_hist_lastMonth + (isNaN(averageScore_hist_lastMonth) ? '' : '%') + '</td>';
        output += '<td>&nbsp;</td></tr>';

        output +=  lineBreak;

        // Pulling numSites from overview - don't have history anyway - not that interesting a metric, and gives the illusion that we're somehow doing something wrong by not showing the history...
        // output += '<tr><td><b>Sites:</b></td>';
        // output += '<td> </td>';
        // output += '<td class="RightAlign">' + numSites.number_format() + '</td>';
        // output += '<td class="RightAlign"> n/a </td>'; // no history data for numSites - print "n/a" (not applicable or not available)
        // output += '<td>&nbsp;</td></tr>';
        // output +=  lineBreak;

        // Pulling numHosts from overview - The reports are in the context of smartgroups looking at software, not computers
        output += '<tr><td><b>Hosts:</b></td>';
        output += '<td> </td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numHosts, '0,000') + '</td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numHosts_hist, '0,000') + '</td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numHosts_hist_lastMonth, '0,000') + '</td>';
        output += '<td>&nbsp;</td></tr>';

        output +=  lineBreak;

        // Want red if new value bigger, green if smaller, else black, but green on both == 0.  Same for below text colours.
        delta = me.getChangesColorAndText( numProgramsInsecure_hist, numProgramsInsecure, true, true );
        textColor = delta[0];
        deltaString = delta[1];
        output += '<tr><td><b>Programs:</b></td>';
        output += '<td class="RightAlign">Insecure:</td>';
        output += '<td class="RightAlign"><FONT COLOR="' + textColor + '">' + deltaString + Ext.util.Format.number(numProgramsInsecure, '0,000') + '</FONT></td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numProgramsInsecure_hist, '0,000')+ '</td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numProgramsInsecure_hist_lastMonth, '0,000')+ '</td>';
        output += '<td>&nbsp;</td></tr>';

        delta = me.getChangesColorAndText( numProgramsEOL_hist, numProgramsEOL, true, true );
        textColor = delta[0];
        deltaString = delta[1];
        output += '<tr><td> </td>';
        output += '<td class="RightAlign">End-of-Life:</td>';
        output += '<td class="RightAlign"><FONT COLOR="' + textColor + '">' + deltaString + Ext.util.Format.number(numProgramsEOL, '0,000') + '</FONT></td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numProgramsEOL_hist, '0,000') + '</td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numProgramsEOL_hist_lastMonth, '0,000') + '</td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td> </td>';
        output += '<td class="RightAlign">Secure:</td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid black;">' + Ext.util.Format.number(numProgramsPatched, '0,000') + '</div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid black;">' + Ext.util.Format.number(numProgramsPatched_hist, '0,000') + '</div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid black;">' + Ext.util.Format.number(numProgramsPatched_hist_lastMonth, '0,000') + '</div></td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td></td><td></td><td class="RightAlign" ><b>' + Ext.util.Format.number(numPrograms, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numPrograms_hist, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numPrograms_hist_lastMonth, '0,000') + '</b></td>';
        output += '<td>&nbsp;</td></tr>';

        output +=  lineBreak;

        delta = me.getChangesColorAndText( numOSInsecure_hist, numOSInsecure, true, true );
        textColor = delta[0];
        deltaString = delta[1];
        output += '<tr><td><b>Operating Systems:</b></td>';
        output += '<td class="RightAlign">Insecure:</td>';
        output += '<td class="RightAlign"><FONT COLOR="' + textColor + '">' + deltaString + Ext.util.Format.number(numOSInsecure, '0,000') + '</FONT></td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numOSInsecure_hist, '0,000') + '</td>';
        output += '<td class="RightAlign">' + Ext.util.Format.number(numOSInsecure_hist_lastMonth, '0,000') + '</td>';
        output += '<td>&nbsp;</td></tr>';

        delta = me.getChangesColorAndText( numOSEOL_hist, numOSEOL, true, true );
        textColor = delta[0];
        deltaString = delta[1];
        output += '<tr><td> </td>';
        output += '<td class="RightAlign">End-of-Life:</td>';
        output += '<td class="RightAlign"><FONT COLOR="' + textColor + '">' + deltaString + Ext.util.Format.number(numOSEOL, '0,000') + '</FONT></td>';
        output += '<td class="RightAlign">' +  Ext.util.Format.number(numOSEOL_hist, '0,000') + '</td>';
        output += '<td class="RightAlign">' +  Ext.util.Format.number(numOSEOL_hist_lastMonth, '0,000') + '</td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td> </td>';
        output += '<td class="RightAlign">Secure:</td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid Black;">' + Ext.util.Format.number(numOSPatched, '0,000')  + '</div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid Black;">' + Ext.util.Format.number(numOSPatched_hist, '0,000') + '</div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 1px solid Black;">' + Ext.util.Format.number(numOSPatched_hist_lastMonth, '0,000') + '</div></td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td></td><td></td><td class="RightAlign" ><b>' + Ext.util.Format.number(numOSs, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numOSs_hist, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numOSs_hist_lastMonth, '0,000') + '</b></td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td> </td><td> </td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 2px solid Black;"></div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 2px solid Black;"></div></td>';
        output += '<td class="RightAlign" style="padding-left:10px;"><div style="border-bottom: 2px solid Black;"></div></td>';
        output += '<td>&nbsp;</td></tr>';

        output += '<tr><td> </td>';
        output += '<td class="RightAlign"><b>TOTAL:</b> </td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numAllSoftware, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numAllSoftware_hist, '0,000') + '</b></td>';
        output += '<td class="RightAlign"><b>' + Ext.util.Format.number(numAllSoftware_hist_lastMonth, '0,000') + '</b></td>';
        output += '<td>&nbsp;</td></tr>';

        output += '</table>';

        return output;
    },

    getChangesColorAndText: function( input1, input2, flip, greenZero, specialChars ) {

        var textColor = "Black";
        var value1 = parseInt( input1, 10 );
        var value2 = parseInt( input2, 10 );
        var sign = "+";
        var deltaString = '';
        var retArray;
        var special = (specialChars) ? specialChars : '';

        // We also assume we are getting numbers in here - if non-numeric strings, just return black.
        if ( isNaN(value1) || isNaN(value2) ) {
            retArray = [ textColor, deltaString ]; // default values of black and ''
            return retArray;
        }

        if ( greenZero && 0==value1 && 0==value2 ) {
            retArray = [ "Green", deltaString ]; // default values of ''
            return retArray;
        }

        if ( value1 == value2 ) {
            retArray = [ textColor, deltaString ]; // default values, black and ''
            return retArray;
        }

        // otherwise, we have integers we can compare
        var delta = Math.abs(value1-value2);
        if ( value1 > value2 ) {
            textColor = "Green";
            sign = (flip ? '-' : sign);
            deltaString = "(" + sign + delta + special + ")" + '&nbsp;' + '&nbsp;';
        } else if ( value1 < value2 ) {
            textColor = "Red";
            sign = (flip ? '+' : '-');
            deltaString = "(" + sign + delta + special + ")" + '&nbsp;' + '&nbsp;';
        }

        retArray = [ textColor, deltaString ];
        return retArray;
    }
});