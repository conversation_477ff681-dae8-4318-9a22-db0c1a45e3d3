Ext.define('Dashboard.portlet.BasePanel', {
    extend: 'Ext.panel.Panel',
    initComponent: function () {
        var me = this,
            store = me.getPortletStore(),
            emptyText, chart;

        me.callParent(arguments);

        emptyText = me.down('#emptyText');
        chart = me.down('#chart');

        if (store) {
            store.on('load', function (store) {
                if (!store.getCount()) {
                    me.getLayout().setActiveItem(emptyText);
                } else {
                    me.getLayout().setActiveItem(chart);
                }
            });
        }

    },
    layout: 'card'
});