Ext.define('Dashboard.portlet.OverviewPanel', {
    extend: 'Ext.panel.Panel',
    alias: [
        'widget.dashboard-portlet-overviewpanel',
        'widget.overview_panel'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    controller: 'dashboard-portlet-overviewpanelcontroller',
    viewModel: {
        type: 'dashboard-portlet-overviewpanelmodel'
    },

    bodyPadding: 10,

    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'host'
        }]
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function(){
        return this.getViewModel().getStore('overview');
    }
});