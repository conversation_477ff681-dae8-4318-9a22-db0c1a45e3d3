Ext.define('Dashboard.portlet.TimeToPackageGridModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-timetopackagegridmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        timeToPackageGridStore: {
            fields: ['activity_text',{
                name: 'package',
                convert: function (value, record) {
                    var currentPackage;
                        currentPackage = record.get('activity_text').split('|', 4);
                        if (currentPackage[2].trim()) {
                            return currentPackage[3];
                        }
                }
            },{
            name: 'time_elapsed',
               convert: function (value, record) {
                    var  timeOffsetStr = '',
                        currentPackage;
                    currentPackage = record.get('activity_text').split('|', 4);
                    if (currentPackage[2].trim()) {
                            timeOffsetStr = sfw.Util.differenceBetweenDates(
                                sfw.Util.dateCreate(currentPackage[2], true),
                                sfw.Util.dateCreate(record.get('time'), true),
                                3, {}
                            );
                            return timeOffsetStr;
                    }
               }
            }],
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=time_to_package&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.last10PackagesCreatedLogs'
                }
            },
            listeners: {
                    load: 'onStoreLoad'
            }
        }
    }
});

