Ext.define('Dashboard.portlet.TopThreatScoreGridModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-topthreatscoregridmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        topThreatScoreStore: {
            fields: ['vuln_criticality', {
                name: 'criticality',
                convert: function (value, record) {
                    return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                        20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                        0;
                }
            }],
            autoLoad: false,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=top_threat_score&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.advisoryDataThreat'
                }
            }
        }
    }
});