Ext.define('Dashboard.portlet.TimeToPackageGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-timetopackagegridcontroller',

    onStoreLoad: function(store, records) {
        var currentpackage,
        processedPackages = false,
        activity_text_remove= [];
        for(i=0;i<records.length;i++){
            currentpackage = records[i].data.activity_text.split('|', 4);
            if (currentpackage[2].trim()) {
                processedPackages = true;
            }
        }
        if (false === processedPackages) {
            store.loadData([]);
        }
        for(i=0;i<records.length;i++){
            currentpackage = records[i].data.activity_text.split('|', 4);
            if(currentpackage[2].trim() === ''){
                activity_text_remove.push(records[i]);
            }
        }
        store.remove(activity_text_remove);
    }

});

