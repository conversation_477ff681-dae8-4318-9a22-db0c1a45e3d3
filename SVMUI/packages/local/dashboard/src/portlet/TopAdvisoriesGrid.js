Ext.define('Dashboard.portlet.TopAdvisoriesGrid', {
    extend: 'Ext.grid.Panel',
    alias: [
        'widget.dashboard-portlet-topadvisoriesgrid',
        'widget.top_advisories'
    ],
    // requires: [
    //     'Flexera.widgets.SectorProgressBar'
    // ],
    controller: 'dashboard-portlet-topadvisoriesgridcontroller',
    viewModel: {
        type: 'dashboard-portlet-topadvisoriesgridmodel'
    },
    bind: {
        store: '{advisoryDataCriticalStore}'
    },
    listeners: {
        // itemdblclick:'onItemDblClick',
        // listener to handle clicks on .clickable elements
        click: {
            element: 'el',
            // scope: 'this',
            delegate: '.clickable',
            fn: 'onClick'
        },
        beforerender: function (grid) {
            if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                columns  = this.getView().getGridColumns();
                columns[4].destroy();
            }
        }
    },
    viewConfig: {
        emptyText: 'There are no Insecure Software Installations found on your system, thus no associated Vulnerability Advisories.'
    },
    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'product'
        }]
    }],
    columns: [{
        xtype: 'rownumberer',
        text: '#',
        align: 'center',
        width: 40
    }, {
        xtype: 'templatecolumn',
        cellWrap: true,
        text: 'Advisory',
        dataIndex: 'vuln_title',
        tpl: [
            '<span class="clickable" data-type="vuln" data-id="{vuln_id}">{vuln_title} (SA{vuln_id})</span>'
        ],
        flex: 1
    }, {
        xtype: 'templatecolumn',
        text: 'Installations',
        tooltip: 'Installations',
        align: 'right',
        dataIndex: 'no_installations',
        tpl: [
            '<span class="clickable" data-type="installations" data-id="{vuln_id}">{no_installations}</span>'
        ],
        width: 60
    }, {
        xtype: 'widgetcolumn',
        text: 'Criticality',
        dataIndex: 'criticality',
        align: 'center',
        sortable: true,
        width: 90,
        widget: {
            xtype: 'sectorprogress',
            height: 8
        }
    }, {
        text: 'Threat Score',
        align: 'right',
        width: 100,
        dataIndex: 'vuln_threat_score',
        renderer: function (value, metadata, record) {
            return value;//TODO number format?
        }
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function(){
        return this.getViewModel().getStore('advisoryDataCriticalStore');
    }
});