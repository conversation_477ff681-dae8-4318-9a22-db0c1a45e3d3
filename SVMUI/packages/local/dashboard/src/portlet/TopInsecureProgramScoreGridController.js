Ext.define('Dashboard.portlet.TopInsecureProgramScoreController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-topinsecureprogramscoregridcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('topInsecureProgramsScoreStore').load({
            params: {
                product_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function (evt) {
        if (evt.target.dataset.id > 0) {
            var options = {};
            options.extra = evt.record;
            sfw.Default.viewInstallation(evt, evt.target, options);
        }
    }
});