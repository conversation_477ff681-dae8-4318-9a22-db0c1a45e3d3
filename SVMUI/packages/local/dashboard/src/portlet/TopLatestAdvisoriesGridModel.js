Ext.define('Dashboard.portlet.TopLatestAdvisoriesGridModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-toplatestadvisoriesgridmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        topLatestAdvisoriesStore: {
            autoLoad: false,
            fields: ['vuln_criticality', {
                name: 'criticality',
                convert: function (value, record) {
                    return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                        20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                        0;
                }
            }],
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=top_latest_advisories&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.advisoryDataLatest'
                }
            }
        }
    }
});