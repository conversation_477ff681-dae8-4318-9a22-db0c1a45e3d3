Ext.define('Dashboard.portlet.TopInsecureProgramsModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-topinsecureprogramsgridmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        topInsecureProgramsStore: {
            fields: [{
                name: 'product_id'
            }, {
                name: 'product_name'
            }, {
                name: 'no_installations'
            }, {
                name: 'hist7day_no_installations'
            }, {
                name: 'hist1month_no_installations'
            }],
            autoLoad: false,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=top_insecure_programs&',
                reader: {
                    type: 'json',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data)) {
                                var historyResult = data.data.topInsecurePrograms_7DAY;
                                var response = data.data.topInsecurePrograms.map(function (item) {
                                    for (var i = 0; i < historyResult.length; i++) {
                                        if (item.product_id === historyResult[i].product_id) {
                                            item.hist7day_no_installations = historyResult[i].no_installations;
                                            break; // break out - we found our match
                                        }
                                    }
                                    return item;
                                });

                                var lastMonthHistoryResult = data.data.topInsecurePrograms_1MONTH;
                                var response = data.data.topInsecurePrograms.map(function (historyItem) {
                                    for (var i = 0; i < lastMonthHistoryResult.length; i++) {
                                        if (historyItem.product_id === lastMonthHistoryResult[i].product_id) {
                                            historyItem.hist1month_no_installations = lastMonthHistoryResult[i].no_installations;
                                            break; // break out - we found our match
                                        }
                                    }
                                    return historyItem;
                                });

                                return response;
                            }
                        },
                        scope: this
                    },
                    rootProperty: 'data'
                }
            }
        }
    }
});