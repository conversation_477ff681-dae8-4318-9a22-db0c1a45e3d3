Ext.define('Dashboard.portlet.SiteOverviewSystemScoreChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-siteoverviewsystemscorechartmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=host&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        chart: {
            fields: [{
                name: 'site'
            }, {
                name: 'score'
            }, {
                name: 'hist_score'
            }, {
                name: 'hist_month_score'
            }],
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_site_overview_system_score&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data)) {
                                var historyResult = data.data.siteScoreData_7DAY;
                                var historyMonthResult = data.data.siteScoreData_1MONTH,
                                    response = data.data.siteScoreData.map(function (item) {
                                        item.site = item.Site;
                                        item.score = item.Score;
                                        for (var i = 0; i < historyResult.length; i++) {
                                            if (item.Site == historyResult[i].Site) {
                                                item.hist_score = historyResult[i].Score;
                                                break; // break out - we found our match
                                            }
                                        }
                                        for (var i = 0; i < historyMonthResult.length; i++) {
                                            if (item.Site == historyMonthResult[i].Site) {
                                                item.hist_month_score = historyMonthResult[i].Score;
                                                break; // break out - we found our match
                                            }
                                        }
                                       
                                        return item;
                                    });

                                //console.log('BarChart Data after transform...');
                                //console.log(response);
                                return response;
                            }
                        },
                        scope: this
                    }

                }
            },
            listeners: {
                load: 'onSystemScoreLoad'
            }
        }
    }
});