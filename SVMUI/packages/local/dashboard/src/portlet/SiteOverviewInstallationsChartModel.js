Ext.define('Dashboard.portlet.SiteOverviewInstallationsChartModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.dashboard-portlet-siteoverviewinstallationschartmodel',

    stores: {
        smartGroupPortletStore: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=smart_groups&which=menuSummary&smartGroupTextType=host&',
                reader: {
                    type: 'json',
                    rootProperty: 'data'
                }
            }
        },
        chart: {
            fields: ['name', 'no_eol', 'no_insecure', 'no_patched', 'hist_no_eol', 'hist_no_insecure', 'hist_no_patched', 'hist_month_no_eol','hist_month_no_insecure','hist_month_no_patched'],
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_dashboard&which=getPortlet&type=chart_site_overview_installations&',
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    transform: {
                        fn: function (data) {
                            var historyResult = data.data.siteInstallationData_7DAY;
                                var historyResultMonth = data.data.siteInstallationData_1MONTH,
                                response = data.data.siteInstallationData.map(function (item) {
                                    for (var i = 0; i < historyResult.length; i++) {
                                        if (item.name == historyResult[i].name) {
                                            item.no_eol = parseInt(item.no_eol, 10);
                                            item.no_insecure = parseInt(item.no_insecure, 10);
                                            item.no_patched = parseInt(item.no_patched, 10);
                                            item.hist_no_eol = parseInt(historyResult[i].no_eol, 10);
                                            item.hist_no_insecure = parseInt(historyResult[i].no_insecure, 10);
                                            item.hist_no_patched = parseInt(historyResult[i].no_patched, 10);

                                            break; // break out - we found our match
                                        }
                                    }
                                    for (var i = 0; i < historyResultMonth.length; i++) {
                                        if (item.name == historyResultMonth[i].name) {
                                            item.hist_month_no_eol = parseInt(historyResultMonth[i].no_eol, 10);
                                            item.hist_month_no_insecure = parseInt(historyResultMonth[i].no_insecure, 10);
                                            item.hist_month_no_patched = parseInt(historyResultMonth[i].no_patched, 10);

                                            break; // break out - we found our match
                                        }
                                    }
                                    return item;
                                });

                            response = Ext.Array.filter(response, function (item) {
                                return item.hist_no_insecure > 0 || item.no_insecure > 0
                            });
                            return response;
                        },
                        scope: this
                    }
                }
            },
            listeners: {
                load: 'onSiteOverviewScoreLoad'
            }
        }
    }
});