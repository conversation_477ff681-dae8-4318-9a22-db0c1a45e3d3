Ext.define('Dashboard.portlet.TopEolProgramsGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-topeolprogramsgridcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('topEOLPrograms').load({
            params: {
                product_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function (evt) {
        if (evt.target.dataset.id > 0) {
            var options = {};
            options.extra = evt.record;
            sfw.Default.viewInstallation(evt, evt.target, options);
        }
    }
});