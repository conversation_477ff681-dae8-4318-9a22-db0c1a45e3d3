Ext.define('Dashboard.portlet.TopThreatScoreGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.dashboard-portlet-topthreatscoregridcontroller',

    onComboSmartGroupSelect: function (combo, record) {
        this.getStore('topThreatScoreStore').load({
            params: {
                product_smartgroup_id: combo.getValue()
            }
        });
    },

    onClick: function (evt) {
        if (evt.target.dataset.id > 0) {
            if (evt.target.dataset.type === 'vuln') {
                sfw.Default.advisoryView(1, evt.target.dataset.id);
            } else if (evt.target.dataset.type === 'installations') {
                sfw.Default.renderInstallationPopup(evt.target.dataset.id);
            }
        }
    }
});