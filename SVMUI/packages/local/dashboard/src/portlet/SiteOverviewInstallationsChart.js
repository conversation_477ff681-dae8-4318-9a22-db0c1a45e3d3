Ext.define('Dashboard.portlet.SiteOverviewInstallationsChart', {
    extend: 'Dashboard.portlet.BasePanel',
    alias: [
        'widget.dashboard-portlet-site-overview-installationschart',
        'widget.chart_site_overview_installations'
    ],
    mixins: [
        'Dashboard.portlet.Mixin'
    ],
    requires: [
        'Ext.chart.axis.Category',
        'Ext.chart.series.Bar',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'dashboard-portlet-siteoverviewinstallationschartcontroller',
    viewModel: {
        type: 'dashboard-portlet-siteoverviewinstallationschartmodel'
    },

    dockedItems: [{
        xtype: 'toolbar',
        layout: {
            type: 'hbox',
            pack: 'start'
        },
        items: [{
            xtype: 'dashboard-smartgroupcombo',
            smartGroupTextType: 'host'

        }]
    },{
        xtype: 'container',
        padding: 5,
        dock: 'top',
        html: 'Top sites (up to 10) with the most Insecure Software Installations.  Lower bar shows last week\'s data for each site. Mouse over the bars to see actual values.'
    }],
    items: [{
        xtype: 'cartesian',
        itemId: 'chart',
        bind: {
            store: '{chart}'
        },
        theme: 'flexera-default',
        insetPadding: 20,
        innerPadding: {
            top: 10,
            left: 0,
            right: 20,
            bottom: 10
        },
        axes: [{
            type: 'numeric',
            adjustByMajorUnit: true,
            majorTickSteps: 4,
            reconcileRange: true,
            position: 'bottom',
            minimum: 0,
            fields: ['no_patched', 'no_eol', 'no_insecure', 'hist_no_patched', 'hist_no_eol', 'hist_no_insecure', 'hist_month_no_patched', 'hist_month_no_eol', 'hist_month_no_insecure'],
            grid: true
        }, {
            type: 'category',
            position: 'left',
            // hidden: true,
            fields: ['name'],
            grid: true,
            renderer: function (axis, label, layoutContext, lastLabel) {
                return label.length > 15 ? Ext.util.Format.ellipsis(label, 15) : label;
            }
        }],
        flipXY: true,
        legend: true,
        series: [{
            type: 'bar',
            title: ['Secure', 'Insecure', 'End-Of-Life'],
            stacked: true,
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    tooltip.setHtml(record.get('name') + ': Secure: ' + record.get('no_patched') + ' Insecure: ' + record.get('no_insecure') + ' EOL: ' + record.get('no_eol'));
                }
            },
            xField: 'name',
            yField: ['no_patched', 'no_eol', 'no_insecure'],
            colors: ['#42be5b', '#ffe300', '#c50221'],
            // style: {
            //     minGapWidth: 20
            // },
            highlight: false,
            renderer: function (sprite, config, data, index) {
                if (data.store.getAt(index)) {
                    var findColor = function (field) {
                        var color;
                        switch (field) {
                            case 'no_patched':
                                color = '#42be5b';
                                break;
                            case 'no_eol':
                                color = '#ffe300';
                                break;
                            case 'no_insecure':
                                color = '#c50221';
                                break;
                        }
                        return color;
                    };

                    return Ext.apply(config, {
                        opacity: 0.90,
                        fillStyle: findColor(sprite.getField()),
                        width: 6
                        //translateX: 20
                    });
                }
                return Ext.apply(config, {
                    //opacity: 0.80,
                    fillStyle: '#ccc'
                });
            }
        }, {
            type: 'bar',
            // axis: 'bottom',
            // gutter: 5,
            //title: ['Secure', 'End-Of-Life', 'Insecure'],
            stacked: true,
            xField: 'name',
            showInLegend: false,
            yField: ['hist_no_patched', 'hist_no_eol', 'hist_no_insecure'],
            colors: ['#CCFF99', '#FFFFCC', '#FFCCCC'],
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    tooltip.setHtml(record.get('name') +' (Last Week) : Secure: ' + record.get('hist_no_patched') + ' Insecure: ' + record.get('hist_no_insecure') + ' EOL: ' + record.get('hist_no_eol'));
                }
            },
            renderer: function (sprite, config, data, index) {
                if (data.store.getAt(index)) {
                    var findColor = function (field) {
                        var color;
                        switch (field) {
                            case 'hist_no_patched':
                                color = '#CCFF99';
                                break;
                            case 'hist_no_eol':
                                color = '#FFFFCC';
                                break;
                            case 'hist_no_insecure':
                                color = '#FFCCCC';
                                break;
                        }

                        return color;
                    };

                    return Ext.apply(config, {
                        opacity: 0.90,
                        fillStyle: findColor(sprite.getField()),
                         width: 6,
                        translateX: 8
                    });
                }

                return Ext.apply(config, {
                    opacity: 0.90,
                    fillStyle: '#ccc'
                });
            }
        },{
            type: 'bar',
            // axis: 'bottom',
            // gutter: 5,
            //title: ['Secure', 'End-Of-Life', 'Insecure'],
            stacked: true,
            xField: 'name',
            showInLegend: false,
            yField: ['hist_month_no_patched', 'hist_month_no_eol', 'hist_month_no_insecure'],
            colors: ['#e8fad7', '#ffffe6', '#fce6e6'],
            tooltip: {
                trackMouse: true,
                showDelay: 0,
                dismissDelay: 0,
                hideDelay: 0,
                renderer: function (tooltip, record, item) {
                    tooltip.setHtml(record.get('name') +' (Last Month) : Secure: ' + record.get('hist_month_no_patched') + ' Insecure: ' + record.get('hist_month_no_insecure') + ' EOL: ' + record.get('hist_month_no_eol'));
                }
            },
            renderer: function (sprite, config, data, index) {
                if (data.store.getAt(index)) {
                    var findColor = function (field) {
                        var color;
                        switch (field) {
                            case 'hist_month_no_patched':
                                color = '#e8fad7';
                                break;
                            case 'hist_month_no_eol':
                                color = '#ffffe6';
                                break;
                            case 'hist_month_no_insecure':
                                color = '#fce6e6';
                                break;
                        }

                        return color;
                    };

                    return Ext.apply(config, {
                        opacity: 0.90,
                        fillStyle: findColor(sprite.getField()),
                        width: 6,
                        translateX: 16
                    });
                }

                return Ext.apply(config, {
                    opacity: 0.90,
                    fillStyle: '#ccc'
                });
            }
        }]
    }, {
        xtype: 'flexera-emptytextcomponent',
        itemId: 'emptyText'
    }],

    refreshPortletData: function () {
        this.getPortletStore().reload();
    },

    getPortletStore: function () {
        return this.getViewModel().getStore('chart');
    }
});