/**
 * Created by <PERSON><PERSON><PERSON> on 16. August 2021 (<EMAIL>)
 *
 *
 * Example of usage:
 *
 *
 * }, {
        xtype: 'widgetcolumn',
        text: 'Criticality',
        dataIndex: 'criticality',
        align: 'center',
        sortable: true,
        width: 90,
        widget: {
            xtype: 'sectorprogress',
            height: 8,
            sectors: [{
                end: 20,//1
                ui: 'green'
            }, {
                end: 40,//2
                ui: 'light-green'
            }, {
                end: 60,//3
                ui: 'yellow'
            }, {
                end: 80,//4
                ui: 'orange'
            }, {
                end: 100,//5
                ui: 'red '
            }]
        }
    }
 */

Ext.define('Flexera.widgets.SectorProgressBar', {
    extend: 'Ext.Progress',
    alias: [
        'widget.sectorprogress',
        'widget.sectorprogressbar',
        'widget.flexera-sectorprogressbar'
    ],
    maxWidth: 90,
    config: {
        sectors: [{
            end: 100,//1
            ui: 'red'
        }, {
            end: 80,//2
            ui: 'orange'
        }, {
            end: 60,//3
            ui: 'yellow'
        }, {
            end: 40,//4
            ui: 'light-green'
        }, {
            end: 20,//5
            ui: 'green'
        }]
    },
    privates: {
        applySector: function (percent) {
            var me = this,
                sectors = me.getSectors(),
                sector;

            if (!me.getSectors().length) {
                Ext.log.warn(Ext.String.format('Invalid config property "sectors" found in {0} component.', me.xtype));
                return;
            }

            for (var i = 0; i < me.getSectors().length; i++) {
                sector = sectors[i];
                if (percent === sector.end) {
                    if (sector.ui) {
                        me.setUi(sector.ui);
                    }
                    break;
                }
            }
        }
    },

    updateValue: function (value, oldValue) {
        var me = this;

        me.callParent(arguments);
        me.applySector(value * 100);
    }
});