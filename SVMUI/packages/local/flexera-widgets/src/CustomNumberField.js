Ext.define('Flexera.widgets.CustomNumberField', {
    extend: 'Ext.form.field.Number',
    alias: [
        'widget.customnumberfield',
        'widget.flexera-customnumberfield'
    ],
    config: {
        name: '',
        minNumber: '',
        maxNumber: '',
        valueNumber: ''
    },
    privates: {},
    initComponent: function () {
        var me = this,
            maxLength = Math.floor(Math.log(me.maxNumber) / Math.LN10) + 1,
            errorText = 'Valid range: ' + parseInt(me.minNumber, 10) + ' to ' + parseInt(me.maxNumber, 10);

        me.width = 58;
        me.allowBlank = false;
        me.allowNegative = false;
        me.allowDecimals = false;
        me.mouseWheelEnabled = false;
        me.keyNavEnabled = false;
        me.hideTrigger = true;
        me.setValue(me.valueNumber);
        me.maxValue = parseInt(me.maxNumber, 10);
        me.minValue = parseInt(me.minNumber, 10);
        me.maxText = errorText;
        me.minText = errorText;
        me.setInvalidText = errorText;
        me.blankText = errorText;
        me.maxLength = maxLength;
        me.disabled = true;
        me.padding = {left: 5 ,right : 5};

        me.callParent(arguments);
    }
});