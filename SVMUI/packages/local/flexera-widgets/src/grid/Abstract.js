Ext.define('Flexera.widgets.grid.Abstract', {
    extend: 'Ext.grid.Panel',
    alias: [
        'widget.flexera-abstractgrid'
    ],
    initComponent: function () {
        var me = this;


        me.callParent(arguments);


        if (me.stateful) {
            /**
             *  @Private
             *  beforestaterestore handler function that gets the initial state of
             *  the gridpanel and copies it to grid.defaultState, such that we can
             *  revert to this state.
             *  @param grid Ext.component (this)
             *
             *  @param state hash of state values returned from stateprovider.
             */
            var getDefaultState = function (grid, state) {
                grid.defaultState = Ext.apply({}, grid.getState());
                return true;
            };


            me.headerContextMenu = Ext.create('Ext.menu.Menu', {
                items: [{
                    text: 'Reset to Default View',
                    handler: function () {
                        me.resetView(me.getStateId())
                    }
                }]
            });

            me.on('headercontextmenu', me.headerContextMenuHandler);
            //Note we do overwrite any beforestaterestore listeners that might have been
            //defined.
            me.on('beforestaterestore', getDefaultState);

            // We have to set grid.defaultState here also, to make sure it is set
            // the first time the user loads the csi on a local machine. The
            // event beforestaterestore is only fired on initialisation of a component
            // if the componentState was previously saved to stateprovider hence it will
            // not be fired on first load of csi.
            me.defaultState = me.defaultState || me.getState();
        }
    },

    headerContextMenuHandler: function (ct, column, e, t, eOpts) {
        //Note stateId defaults to grid id and all our grids should have unique id.
        var me = this;
        e.stopEvent();

        me.headerContextMenu.showAt(e.getXY());
    },

    /**
     * @private
     * headerContextMenu itemclick handler resets gridView to defaultState, as
     * initially defined by programmer.
     * ENHANCEMENT: Make the resetView available to non stateful grids.
     */

    resetView: function (stateId) {
        var me = this,
            grid = me,
            sp = Ext.state.Manager.getProvider();

        sp.set(stateId, Ext.apply({}, grid.defaultState));
        grid.applyState(Ext.apply({}, grid.defaultState));

        // We have to manually reset the totalWidth to null here, as
        // grid.applyState does not reset totalWidth property of columnModel and
        // the gridView refresh function will only reapply autoexpand column if
        // totalWidth is set to null.
        // grid.getColumnModel().totalWidth = null;//TODO Wemerson

        // Once a userResizes a column the  gridView userResized property will be set
        // to true and gridView refresh will only recalculate column width if
        // userResized = false, hence we set it to false here.
        grid.getView().userResized = false;

        grid.getView().refresh(true);
        //let store use last pagesize, but reset to first page.
        //we could not use paging toolbar as not all grids have that enabled.
        //also one can not set params as config in .reload({params:{start:0}}
        //as ext does not do a deep copy of params and simply overwrites params
        //if specified as reload config as above.
        var lastOptions = grid.getStore().lastOptions;
        if (lastOptions.params !== undefined) {
            lastOptions.params.start = 0;
        }
        grid.getStore().reload();
        return true;
    }

});

