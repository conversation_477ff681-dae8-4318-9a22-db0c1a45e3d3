@if $enable-font-open-sans {
    $triton-font-path: get-resource-path('fonts', 'shared');
    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-Light"),url('#{$triton-font-path}/OpenSans-Light.ttf');
        font-weight: 300;
        font-style: normal;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-LightItalic"), url('#{$triton-font-path}/OpenSans-LightItalic.ttf');
        font-weight: 300;
        font-style: italic;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-Regular"), url('#{$triton-font-path}/OpenSans-Regular.ttf');
        font-weight: 400;
        font-style: normal;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-Italic"), url('#{$triton-font-path}/OpenSans-Italic.ttf');
        font-weight: 400;
        font-style: italic;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-Semibold"), url('#{$triton-font-path}/OpenSans-Semibold.ttf');
        font-weight: 600;
        font-style: normal;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-SemiboldItalic"), url('#{$triton-font-path}/OpenSans-SemiboldItalic.ttf');
        font-weight: 600;
        font-style: italic;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-Bold"), url('#{$triton-font-path}/OpenSans-Bold.ttf');
        font-weight: 700;
        font-style: normal;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-BoldItalic"), url('#{$triton-font-path}/OpenSans-BoldItalic.ttf');
        font-weight: 700;
        font-style: italic;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-ExtraBold"), url('#{$triton-font-path}/OpenSans-ExtraBold.ttf');
        font-weight: 800;
        font-style: normal;
    }

    @font-face {
        font-family: 'Open Sans';
        src: local("OpenSans-ExtraBoldItalic"), url('#{$triton-font-path}/OpenSans-ExtraBoldItalic.ttf');
        font-weight: 800;
        font-style: italic;
    }
}

@if $enable-font-source-sans-pro {
    $triton-font-path: get-resource-path('fonts/Source_Sans_Pro', 'shared');
    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-Light"),url('#{$triton-font-path}/SourceSansPro-Light.ttf');
        font-weight: 300;
        font-style: normal;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-LightItalic"), url('#{$triton-font-path}/SourceSansPro-LightItalic.ttf');
        font-weight: 300;
        font-style: italic;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-Regular"), url('#{$triton-font-path}/SourceSansPro-Regular.ttf');
        font-weight: 400;
        font-style: normal;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-Italic"), url('#{$triton-font-path}/SourceSansPro-Italic.ttf');
        font-weight: 400;
        font-style: italic;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-SemiBold"), url('#{$triton-font-path}/SourceSansPro-SemiBold.ttf');
        font-weight: 600;
        font-style: normal;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-SemiBoldItalic"), url('#{$triton-font-path}/SourceSansPro-SemiBoldItalic.ttf');
        font-weight: 600;
        font-style: italic;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-Bold"), url('#{$triton-font-path}/SourceSansPro-Bold.ttf');
        font-weight: 700;
        font-style: normal;
    }

    @font-face {
        font-family: 'Source Sans Pro';
        src: local("SourceSansPro-BoldItalic"), url('#{$triton-font-path}/OSourceSansPro-BoldItalic.ttf');
        font-weight: 700;
        font-style: italic;
    }

    //@font-face {
    //    font-family: 'Source Sans Pro';
    //    src: local("OpenSans-ExtraBold"), url('#{$triton-font-path}/OpenSans-ExtraBold.ttf');
    //    font-weight: 800;
    //    font-style: normal;
    //}

    //@font-face {
    //    font-family: 'Source Sans Pro';
    //    src: local("OpenSans-ExtraBoldItalic"), url('#{$triton-font-path}/OpenSans-ExtraBoldItalic.ttf');
    //    font-weight: 800;
    //    font-style: italic;
    //}
}

