
//UI Styles for buttons

@include extjs-button-small-ui(
        $ui: 'primary',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-medium-ui(
        $ui: 'primary',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-large-ui(
        $ui: 'primary',
        $line-height: 34px,
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'primary',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-toolbar-medium-ui(
        $ui: 'primary',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-small-ui(
        $ui: 'soft-blue',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-large-ui(
        $ui: 'soft-blue',
        $line-height: 34px,
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'soft-blue',
        $background-color: $color-soft-blue,
        $border-color: darken($color-soft-blue, 5%)
);

@include extjs-button-small-ui(
        $ui: 'facebook',
        $background-color: $social-facebook-btn-background,
        $border-color: darken($social-facebook-btn-background, 5%)
);
@include extjs-button-large-ui(
        $ui: 'facebook',
        $line-height: 34px,
        $background-color: $social-facebook-btn-background,
        $border-color: darken($social-facebook-btn-background, 5%)
);

@include extjs-button-small-ui(
        $ui: 'soft-cyan',
        $background-color: $color-soft-cyan,
        $border-color: darken($color-soft-cyan, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'soft-cyan',
        $background-color: $color-soft-cyan,
        $border-color: darken($color-soft-cyan, 5%)
);

@include extjs-button-small-ui(
        $ui: 'soft-green',
        $background-color: $color-soft-green,
        $border-color: darken($color-soft-green, 5%)
);

@include extjs-button-large-ui(
        $ui: 'soft-green',
        $line-height: 34px,
        $background-color: $color-soft-green,
        $border-color: darken($color-soft-green, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'soft-green',
        $background-color: $color-soft-green,
        $border-color: darken($color-soft-green, 5%)
);

@include extjs-button-small-ui(
        $ui: 'soft-red',
        $background-color: $color-soft-red,
        $border-color: darken($color-soft-red, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'soft-red',
        $background-color: $color-soft-red,
        $border-color: darken($color-soft-red, 5%)
);

@include extjs-button-small-ui(
        $ui: 'soft-purple',
        $background-color: $color-soft-purple,
        $border-color: darken($color-soft-purple, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'soft-purple',
        $background-color: $color-soft-purple,
        $border-color: darken($color-soft-purple, 5%)
);

@include extjs-button-small-ui(
        $ui: 'gray',
        $background-color: $color-gray,
        $border-color: darken($color-gray, 5%)
);
@include extjs-button-large-ui(
        $ui: 'gray',
        $line-height: 34px,
        $background-color: $color-gray,
        $border-color: darken($color-gray, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'gray',
        $background-color: $color-gray,
        $border-color: darken($color-gray, 5%)
);

@include extjs-button-small-ui(
        $ui: 'green',
        $background-color: $color-green,
        $border-color: darken($color-green, 5%),
        $border-radius: 10px
);

@include extjs-button-medium-ui(
        $ui: 'green',
        $background-color: $color-green,
        $border-color: darken($color-green, 5%),
        $border-radius: 10px
);
@include extjs-button-toolbar-small-ui(
        $ui: 'green',
        $background-color: $color-green,
        $border-color: darken($color-green, 5%),
        $border-radius: 10px
);
@include extjs-button-toolbar-medium-ui(
        $ui: 'green',
        $background-color: $color-green,
        $border-color: darken($color-green, 5%),
        $border-radius: 10px
);

@include extjs-button-small-ui(
        $ui: 'blue',
        $background-color: $color-blue,
        $border-color: darken($color-blue, 5%)
);
@include extjs-button-toolbar-small-ui(
        $ui: 'blue',
        $background-color: $color-blue,
        $border-color: darken($color-blue, 5%)
);

@include extjs-button-small-ui(
        $ui: 'header',
        $color: #999,
        $glyph-color: #919191,
        $background-color: transparent,
        $border-width: 0
);

@include extjs-button-small-ui(
        $ui: 'export',
        $color: #167abc,
        $glyph-color: #167abc,
        $arrow-glyph-color: #167abc,
        $background-color: transparent,
        $border-width: 0
);