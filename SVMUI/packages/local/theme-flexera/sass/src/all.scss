/* ----- Custom CSS Scrollbar Webkit ------ */
::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: darken(#D4D8DB,10%);
  //background-color: rgb(248, 249, 251);
  border: 1px solid rgb(248, 249, 251);
}

::-webkit-scrollbar-track-piece {
  background: transparent;
}

::-webkit-scrollbar-button {
  background: transparent;
}

$blue-text: darken($base-color, 5%);

@mixin btn-border($background-color) {
  background-color: $background-color;
  border-color: $background-color !important;

  .x-btn-inner-default-toolbar-small {
    color: $lightest-color;
  }

  &:hover {
    background-color: transparent;

    .x-btn-inner-default-toolbar-small,
    .x-btn-inner-default-small {
      color: $background-color;
    }

    .x-btn-icon-el-default-small,
    .x-btn-icon-el-default-toolbar-small {
      color: $background-color;
    }
  }
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  border-radius: $radius;
}

//@mixin box-shadow($h-shadow, $v-shadow, $blur, $spread, $shadow-color) {
//  $value: $h-shadow, $v-shadow, $blur, $spread, $shadow-color;
//  -webkit-box-shadow: $value;
//  -moz-box-shadow: $value;
//  -ms-box-shadow: $value;
//  box-shadow: #0000001a 0 4px 12px
//}
/**
 * Generates a set of style rules for the "navigation" tab UI.
 */

.shadow {
  box-shadow: rgb(175,175,175) 0px 2px 7px
}

.nav-tree-badge:after {
  position: absolute;
  height: 18px;
  width: 3.3em;
  display: inline-block;
  text-align: center;
  top: 50%;
  margin-top: -9px;
  right: 12px;
  color: #fff;
  font-weight: 600;
  font-size: 10px;
  line-height: 18px;
}

.nav-tree-badge-hot:after {
  content: "HOT";
  background-color: #e3495a;
}

.nav-tree-badge-new:after {
  content: "NEW";
  background-color: $color-soft-blue;
}

//.pop-out {
//  @include box-shadow(2px 2px 8px 2px #ccc);
//}

.circular {
  border-radius: 50%;
}


@include extjs-tab-panel-ui(
        $ui: 'pill',
        $ui-tab-background-color: #fff,
        $ui-tab-background-color-over: $neutral-highlight-color,
        $ui-tab-background-color-active: $base-color,
        $ui-tab-background-opacity-over: .9,
        $ui-tab-background-opacity-focus-over: .9,
        $ui-tab-border-color-focus: $base-color,
        $ui-tab-color: $color,
        $ui-tab-color-over: $color,
        $ui-tab-color-active: #fff,
        $ui-tab-glyph-color: $color,
        $ui-tab-glyph-color-over: $color,
        $ui-tab-glyph-color-active: #fff,
        $ui-tab-glyph-opacity: .5,
        $ui-tab-border-radius: 0,
        $ui-tab-border-width: 0,
        $ui-tab-margin: 2px,
        $ui-tab-opacity: .8,
        $ui-tab-opacity-over: 1,
        $ui-tab-opacity-active: 1,
        $ui-bar-background-color: #fff,
        $ui-bar-padding: 5px
);





//Overrides
//https://www.sencha.com/forum/showthread.php?291066-Layout-of-the-bottom-border-of-a-textarea-in-Chrome-Opera-and-Safari
.x-webkit .x-form-text {
  height: calc(100% + 6px);
}

// overriding the original "height: calc(100% + 3px);" }
.x-form-text-default.x-form-textarea {
  min-height: 36px;
}

// was 60px, or even 80px in Triton }



//Add border around html editor field
.x-html-editor-input {
  border: 1px solid #ccc;
}




.plus-trigger:before {
  content: "\f067";
}

@include extjs-tab-panel-ui(
        $ui: 'main-content',
        $ui-tab-background-color-active: $neutral-light-color,
        $ui-bar-background-color: lighten($base-color, 15%),
        $ui-tab-icon-width: 20px,
        $ui-tab-icon-height: 20px,
        $ui-tab-icon-spacing: 15px
);

@include extjs-tab-panel-ui(
        $ui: 'main-card-content',
        $ui-tab-background-color-active: $neutral-light-color,
        $ui-bar-background-color: $neutral-light-color
);



.border-radius-2 {
  @include border-radius(2px);
}

.wh-24 {
  width: 24px;
  height: 24px;
}

.wh-16 {
  width: 16px;
  height: 16px;
}

.vertical-align-middle {
  vertical-align: middle !important;

  .x-grid-cell {
    vertical-align: middle !important;
  }

}

.x-treelist-floater {
  z-index: 2000;
}



.font-normal {
  font-weight: 400;
}

.color-white {
  color: #fff;
}

.text-slim {
  font-weight: 300 !important;
}

.text-semibold {
  font-weight: 600 !important;
}

.text-bold {
  font-weight: bold !important;
}

.text-10 {
  font-size: 10px !important;
}

.text-11 {
  font-size: 11px !important;
}

.text-12 {
  font-size: 12px !important;
}

.text-13 {
  font-size: 13px !important;
}

.text-16 {
  font-size: 16px !important;
}

.text-20 {
  font-size: 20px !important;
}

.text-center {
  text-align: center;
}

.atlas-widget {
  padding: 15px;
}

.atlas-widget a {
  color: #ffffff;
}

.padding-5 {
  padding: 5px;
}

.padding-10 {
  padding: 10px;
}

.padding-15 {
  padding: 15px;
}

.padding-20 {
  padding: 20px;
}

.text-white {
  color: #FFFFFF;
}

.white-bg {
  background-color: #ffffff !important;
}

.navy-bg {
  background-color: #1ab394 !important;
  color: #ffffff;
}

.blue-bg {
  background-color: #1c84c6 !important;
  color: #ffffff;
}

.lazur-bg {
  background-color: #23c6c8 !important;
  color: #ffffff;
}

.yellow-bg {
  background-color: #f8ac59 !important;
  color: #ffffff;
}

.red-bg {
  background-color: #ed5565 !important;
  color: #ffffff;
}

.black-bg {
  background-color: #262626;
}

.medium-gray-bg {
  background-color: #E5E9EC !important;
  color: #2A6496;
}

.x-tool {
  font-size: 16px;
  color: #9E9E9E;
}

.x-tool-over {
  opacity: .5;
}

.x-tool-pressed {
  opacity: .8;
}


@-webkit-keyframes fadeInRightSmall {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}


.app-header-toolbar {
  .x-box-layout-ct, .x-box-inner {
    overflow: visible !important;
  }

  background-color: white !important;
  border-bottom: 4px solid #E7EAEC !important;
}

.app-header-toolbar-gray {
  .x-box-layout-ct, .x-box-inner {
    overflow: visible !important;
  }

  /*background-color: #F2F2F3 !important;*/
  background-color: $gray-color !important;

}


#app-header-title {
  padding: 0px 0px 0px 21px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 0 #4e691f
}


.x-form-cb-label {
  font-weight: normal;
}

legend {
  margin-bottom: 0px !important;
  /*width: initial;*/
  font-size: 15px !important;
  border-bottom: 0px !important;
}

.modal-mask-light {
  background-color: #5b5959 !important;
}

.modal-mask-default {
  background-color: #5b5959 !important;
  opacity: .4;
}

.modal-mask-dark {
  background-color: #5b5959 !important;
  opacity: .4;
}

.x-view-emptytext {
  opacity: 0.5;
  text-align: center;
  padding: 15px;
}


/*Reset css*/

.x-btn:hover, x-btn:focus {
  text-decoration: none !important;
}

/* estilo para funcionar o override do Ext.form.field.Text
http://www.learnsomethings.com/2014/01/22/adding-grid-like-ellipsis-and-textbox-with-full-text-to-your-extjs-form-text-fields/*/

.remove-text-field-elipsis {
  text-overflow: clip !important;
}

.add-text-field-elipsis {
  text-overflow: ellipsis !important;
}

/*quebra de texto nas celulas da grid individual por coluna*/
td.wrap-text div {
  white-space: normal !important;

}

.cell-label {
  text-align: center;
  display: inline;
  white-space: nowrap;
  vertical-align: baseline;
  padding: 1px 2px 1px 2px;
  line-height: 1;
  //  font-weight: 600;
  color: white;
  @include border-radius(2px)
}

.rounded-input input {
  @include border-radius(2px) ;
}

.color-blue {
  color: $blue-base-color !important;
}

.color-red {
  color: $red-base-color !important;
}

.color-green {
  color: $green-base-color !important;
}

.color-orange {
  color: $orange-base-color !important;
}

.color-gray {
  color: #5a5b5b !important;
}

.color-white {
  color: #ffffff !important;
}

.color-black {
  color: #000000 !important;
}

.color-yellow {
  color: #FFEA3C !important;
}

.green .x-grid-cell {
  background: #edf9c7 !important;
}

.app-header-info-toolbar {
  background-color: #FFF0BC !important;
  color: $color !important;
  border: 0px !important;
  // font-size: 13px !important;
  //border: 1px solid #D9B55E !important;
}
