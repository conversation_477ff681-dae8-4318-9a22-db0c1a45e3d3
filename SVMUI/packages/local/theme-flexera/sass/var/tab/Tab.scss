//Start Triton
$tab-base-color: dynamic(transparent);
$tab-base-color-focus: dynamic($tab-base-color);
$tab-base-color-over: dynamic(#000);
$tab-base-color-active: dynamic(#fff);

$tab-background-opacity-over: dynamic(.08);
$tab-background-opacity-focus-over: dynamic($tab-background-opacity-over);

$tab-color: dynamic($light-color);
$tab-color-active: dynamic($base-dark-color);
$tab-plain-color: dynamic($highlight-color);
$tab-plain-color-active: dynamic($color);
$tab-glyph-opacity: dynamic(.7);

$tab-inner-border-width-focus: dynamic(0);
$tab-inner-border-color-focus: dynamic($base-color);
$tab-inner-border-color-focus-over: dynamic($tab-inner-border-color-focus);
$tab-inner-border-color-focus-active: dynamic($tab-inner-border-color-focus);

$tab-border-radius: dynamic(0);

$tab-border-color: dynamic($tab-base-color);
$tab-border-color-focus: dynamic($tab-base-color-focus);
$tab-border-color-over: dynamic($tab-base-color-over);
$tab-border-color-active: dynamic($tab-base-color-active);
$tab-border-color-disabled: dynamic($tab-border-color);

$tab-border-width: dynamic(0);

//$tab-icon-width: dynamic(20px);
//$tab-icon-height: dynamic(20px);
//$tab-padding: dynamic(8px 10px);
//$tab-margin: dynamic(0 4px 0 0);

$tab-outline-width-focus: dynamic(1px);
$tab-outline-color-focus: dynamic($base-color);
//$tab-outline-offset-focus: dynamic(-3px);


//$tab-closable-icon-width: dynamic(16px);
//$tab-closable-icon-height: dynamic(16px);
$tab-closable-icon-glyph-color: dynamic($neutral-dark-color);
$tab-closable-icon-glyph-color-active: dynamic($neutral-dark-color);
//$tab-closable-icon-top: dynamic(0);
//$tab-closable-icon-right: dynamic(0);
//$tab-closable-icon-spacing: dynamic(0px);

$tab-plain-closable-icon-glyph-color: dynamic($neutral-dark-color);
$tab-plain-closable-icon-glyph-color-active: dynamic($tab-closable-icon-glyph-color-active);

//End Triton

$tab-base-color: dynamic($base-color);
$tab-base-color-active: dynamic($base-color);
$tab-base-color-over: dynamic($base-color);
$tab-border-color: dynamic($base-color);
$tab-border-color-over: dynamic(transparent);
$tab-border-color-active: dynamic($accent-light-color);
$tab-border-width: dynamic(0 0 5px 0);
//$tab-closable-icon-glyph-color: rgba(255, 255, 255, 0.7);
//$tab-closable-icon-glyph-color-active: dynamic(#ffffff);
$tab-color: dynamic(rgba(255, 255, 255, 0.7));
$tab-color-active: dynamic(#ffffff);
$tab-font-size: dynamic(14px);
$tab-font-size-active: dynamic(14px);
$tab-font-weight: dynamic(600);
$tab-font-weight-active: dynamic(600);
$tab-inner-border-color-focus: dynamic(transparent);
$tab-inner-border-color-focus-active: dynamic($base-color);
$tab-outline-style-focus: dynamic(none);
$tab-padding: dynamic(8px 15px);
$tab-text-opacity-disabled: dynamic(0.75);
$tab-plain-background-color: dynamic(transparent);
$tab-plain-border-color: dynamic(transparent);
$tab-plain-border-color-active: dynamic($base-color);
$tab-plain-color: dynamic($base-color);
$tab-plain-color-active: dynamic($base-color);
$tab-plain-glyph-color: dynamic($base-color);
$tab-plain-glyph-color-active: dynamic($base-color);
$tab-glyph-opacity: dynamic(1);
$tab-text-transform: dynamic(uppercase);
$tab-closable-icon-glyph: dynamic($fa-var-close 16px $font-icon-font-family); 
$tab-background-color-active: dynamic($base-color); 
