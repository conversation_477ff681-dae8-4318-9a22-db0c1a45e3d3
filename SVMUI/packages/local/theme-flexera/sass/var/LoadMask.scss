//Start Triton
$loadmask-opacity: dynamic(0.5);
$loadmask-background-color: dynamic($neutral-color);
$loadmask-page-opacity: dynamic(0.75);
$loadmask-page-background-color: dynamic(#000);
$loadmask-msg-background-color: dynamic($neutral-color);
$loadmask-msg-inner-color: dynamic($color);
//$loadmask-msg-padding: dynamic(10px);
$loadmask-msg-border-radius: dynamic(0);
//$loadmask-msg-text-padding: dynamic(25px 0 0);

//End Triton