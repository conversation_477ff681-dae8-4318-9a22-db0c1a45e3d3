//Start Triton
$grid-header-background-color: dynamic(#fff);
$grid-header-over-background-color: dynamic($grid-row-cell-over-background-color);
$grid-header-trigger-background-color-open: dynamic(mix(#000, $grid-header-open-background-color, 5%));
$grid-header-border-color: dynamic($neutral-color);
//$grid-header-padding: dynamic(7px 10px 6px);
$grid-header-trigger-glyph-color: dynamic($neutral-dark-color);
//$grid-header-sort-icon-spacing: dynamic(5px);
$grid-header-sorted-background-color: dynamic($grid-header-background-color);
$grid-header-sort-glyph-color: dynamic($neutral-dark-color);
//$grid-header-trigger-width: dynamic(24px);
//End Triton