//Start Triton

//$panel-header-font-size: dynamic(round($font-size * 1.2));
//$panel-header-line-height: dynamic(20px);
//$panel-header-padding: dynamic(11px 15px 12px);
$panel-header-font-weight: dynamic($font-weight-bold);
//$panel-frame-border-radius: dynamic(0 0 0 0);
//$panel-frame-border-width: dynamic(2px);
$panel-ignore-frame-padding: dynamic(true);

$panel-header-color: dynamic($light-color);

$panel-light-header-font-size: dynamic($panel-header-font-size);
$panel-light-header-color: dynamic($highlight-color);
$panel-light-header-background-color: dynamic(#fff);
$panel-light-border-color: dynamic($neutral-color);
$panel-light-tool-glyph-color: dynamic($neutral-dark-color);
$panel-header-icon-spacing: dynamic(8px);

$panel-header-glyph-opacity: dynamic(1);
$panel-body-border-color: dynamic($neutral-color);

//End Triton


//Start Material
//$panel-header-background-color: dynamic($base-color);
//$panel-base-color: dynamic($base-color);
//$panel-header-font-size: dynamic(18px);
//$panel-frame-border-width: 4px;
//$panel-header-padding: dynamic(16px);
//$panel-header-line-height: dynamic(22px);
//$panel-body-background-color: dynamic($reverse-color);
//$panel-frame-background-color: dynamic($reverse-color);
//$panel-frame-border-color: dynamic(#e1e1e1);
//$panel-frame-border-width: dynamic(1px);
//$panel-frame-header-border-width: dynamic(1px);
//End Material

$panel-border-color: dynamic(#ddd);
//$panel-body-background-color: dynamic(rgb(248, 249, 251));
//$panel-frame-background-color: dynamic(rgb(248, 249, 251));
$panel-frame-border-color: dynamic(#e1e1e1);
$panel-frame-border-width: dynamic(1px);
$panel-frame-header-border-width: dynamic(1px);


$panel-gray-header-color: dynamic($color);
$panel-gray-header-background-color: dynamic($gray-color);
$panel-gray-body-border-color: dynamic(darken($gray-color, 10));
$panel-gray-tool-background-image: dynamic('tools/tool-sprites');
$panel-gray-header-background-gradient: dynamic('none');
$panel-gray-frame-background-color: dynamic($gray-color);
//$panel-gray-background-color: dynamic($neutral-color);