$window-border-color: dynamic($neutral-color);
$window-header-color: dynamic($dark-color);
$window-header-glyph-color: dynamic($dark-color);
$window-border-width: dynamic(1px);
$window-header-border-width: dynamic(1px);
$window-header-background-color: dynamic(#ffffff);
$window-close-glyph-color: dynamic($dark-light-color);
$window-tool-glyph-color: dynamic($dark-light-color);
//$window-body-background-color: dynamic(rgb(248, 249, 251));


$window-gray-header-font-size: dynamic($panel-header-font-size);
$window-gray-header-color: dynamic($highlight-color);
$window-gray-header-background-color: dynamic($dark-gray-color);
$window-gray-border-color: dynamic($neutral-color);
$window-gray-tool-glyph-color: dynamic($neutral-dark-color);


//Start Material

$window-border-color: dynamic($base-color);
$window-header-glyph-color: dynamic($window-header-color);
$window-border-width: dynamic(0);
$window-header-border-width: dynamic(0);
$window-header-background-color: dynamic(transparent);
$window-close-glyph-color: dynamic($base-color);
//End Material




