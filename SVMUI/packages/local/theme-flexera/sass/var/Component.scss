// COLOR PALETTE
$base-color: dynamic(#277BC7);

$circle-border-radius:50%;
$base-border-color: #ddd;
$lightest-color:#fff;
$font-weight: 400;
$font-weight-bold: 500;

$gray-color: #F1F1F1;//Google
$dark-gray-color: #E5E5E5;//Google
$blue-color: #2e6eaf;
$red-color: #f1495b;
$green-color: #11c897;
$light-blue-color: saturate(lighten($blue-color, 53%), 70%);
$blue-base-color: #559FCA !default;
$orange-base-color: #f0ad4e !default;
$accent-light-color: $orange-base-color;
$yellow-base-color: #FED859 !default;
$red-base-color: #d9534f !default;
$green-base-color: #5bb75b !default;
$blue-baby-base-color: #5bc0de !default;
$black-base-color: #363636 !default;
$gray-base-color: #999999 !default;
$force-background-color: #56B750;
$color-soft-gray: #e9e9e9;
$color-soft-yellow: #ffc000;

//Form and Toolbar Button UI colors

$color-blue: #167abc;
$color-soft-cyan: #03b4d5;
$color-soft-blue: #2eadf5;
$color-soft-green: #0DD379;
$color-soft-orange: #ffc107;
$color-soft-red: #e44959;
$color-soft-purple: #925e8b;
$color-gray: #949495;
$color-green: #02B568;



$dark-mode: false;
$reverse-color: dynamic(if($dark-mode, #222, #fff));
$color: dynamic(if($dark-mode, #ffffff, #404040));
$primary-color: $base-color;

//$font-family: 'Source Sans Pro', 'Helvetica Neue', helvetica, arial, verdana, sans-serif;
$font-family: 'Helvetica Neue', helvetica, arial, verdana, sans-serif;
//$font-weight: 300 !default;//Admin Dashboard
//$font-weight-bold: 400 !default;//Admin Dashboard
$font-weight-bold: 500 !default;//Admin Dashboard
$base-highlight-color: mix(#fff, $base-color, 20%) !default;
$base-light-color: mix(#fff, $base-color, 60%) !default;
$base-dark-color: mix(#000, $base-color, 20%) !default;


$neutral-color: #d0d0d0;//Admin dasboard
$neutral-light-color: mix(#fff, $neutral-color, 80%) !default;
$neutral-highlight-color: mix(#fff, $neutral-color, 60%) !default;
$neutral-medium-dark-color: mix(#000, $neutral-color, 15.2%) !default;
$neutral-dark-color: mix(#000, $neutral-color, 30.4%) !default;
$body-background-color: #E5E9EC !default;
//$font-size: 13px !default;

//Custom
//$dark-color: dynamic(#2F4050);
$dark-color: dynamic(#727376);
//$dark-light-color: #303338;
$dark-light-color: #848688;
$light-color: #f0f0f0;//Admin dashboard
$white-color: #ffffff;

// lighter text color for use on light backgrounds
$highlight-color: #606060;
//
$accordion-header-background-color: $lightest-color;
$accordion-header-color: #333;
//
$social-facebook-btn-background: #167abc;
$social-twitter-btn-background: #03b4d5;
$social-google-plus-btn-background: #e44959;
$social-envelope-btn-background: #7754aa;

/*
 * This file defines variables used throughout the application for modern and
 * classic builds.
 */



$panel-navigation-background-color: dynamic(#0a1024);
//$panel-navigation-item-line-height: dynamic(64px);
$panel-navigation-item-line-height: dynamic(44px);
$panel-navigation-item-text-color: dynamic(#f1f1f1);
$panel-navigation-tool-background-image: 'tools/tool-sprites' !default;
$progress-bar-background-color: $base-color;




//Triton

//$base-highlight-color: dynamic(mix(#fff, $base-color, 20%));
//$base-light-color: dynamic(mix(#fff, $base-color, 60%));
//$base-dark-color: dynamic(mix(#000, $base-color, 20%));
//$neutral-color: dynamic(#d0d0d0);
//$neutral-light-color: dynamic(mix(#fff, $neutral-color, 80%));
//$neutral-highlight-color: dynamic(mix(#fff, $neutral-color, 60%));
//$neutral-medium-dark-color: dynamic(mix(#000, $neutral-color, 15.2%));
//$neutral-dark-color: dynamic(mix(#000, $neutral-color, 30.4%));
//$font-weight: dynamic(300);
//$font-weight-bold: dynamic(400);
//$font-family: 'Open Sans', 'Helvetica Neue', helvetica, arial, verdana, sans-serif;

$enable-font-icons: dynamic(true);

//End Triton


