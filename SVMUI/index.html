<!DOCTYPE HTML>
<html manifest="">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=10, user-scalable=yes">

    <title>Software Vulnerability Manager</title>

    <!-- The line below must be kept intact for Sencha Cmd to build your application -->
    <script id="microloader" data-app="eebfff64-23ad-42b2-9688-96abf83a2958" type="text/javascript" src="bootstrap.js"></script>

</head>
<body class="launching">
<div id="loadingSplash">
    <div id="loadingSplashTop"></div>
    <div id="loadingSplashBottom"></div>
    <div class="loading-fading-circle" id="loadingSplashCircles">
        <div class="loading-circle-1 loading-circle"></div>
        <div class="loading-circle-2 loading-circle"></div>
        <div class="loading-circle-3 loading-circle"></div>
        <div class="loading-circle-4 loading-circle"></div>
        <div class="loading-circle-5 loading-circle"></div>
        <div class="loading-circle-6 loading-circle"></div>
        <div class="loading-circle-7 loading-circle"></div>
        <div class="loading-circle-8 loading-circle"></div>
        <div class="loading-circle-9 loading-circle"></div>
        <div class="loading-circle-10 loading-circle"></div>
        <div class="loading-circle-11 loading-circle"></div>
        <div class="loading-circle-12 loading-circle"></div>
    </div>
</div>
</body>
</html>
