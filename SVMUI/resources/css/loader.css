#loadingSplash {
    z-index: 10000;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

#loadingSplashTop,
#loadingSplashBottom {
    background-color: #277BC7;
    position: absolute;
    right: 0;
    left: 0;
    height: 51%;
    transition: all 500ms;
    transition-delay: 200ms;
}

#loadingSplashTop {
    top: 0;
}

#loadingSplashBottom {
    bottom: 0;
}

/* when app has load, ready to see */
#loadingSplash.app-loaded #loadingSplashTop {
    transform: translateY(-100%);
}
#loadingSplash.app-loaded #loadingSplashBottom {
    transform: translateY(100%);
}
#loadingSplash.app-loaded #loadingSplashCircles {
    transform: scale(0);
    opacity: 0;
}

.loading-fading-circle {
    margin: auto;
    width: 40px;
    height: 40px;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    transition: all 100ms;
}

.loading-fading-circle .loading-circle {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.loading-fading-circle .loading-circle:before {
    content: '';
    display: block;
    margin: 0 auto;
    width: 15%;
    height: 15%;
    background-color: #F47216;
    border-radius: 100%;

    -webkit-animation: loading-circleFadeDelay 1.2s infinite ease-in-out both;
            animation: loading-circleFadeDelay 1.2s infinite ease-in-out both;
}
.loading-fading-circle .loading-circle-2 {
    -webkit-transform: rotate(30deg);
        -ms-transform: rotate(30deg);
            transform: rotate(30deg);
}
.loading-fading-circle .loading-circle-3 {
    -webkit-transform: rotate(60deg);
        -ms-transform: rotate(60deg);
            transform: rotate(60deg);
}
.loading-fading-circle .loading-circle-4 {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
}
.loading-fading-circle .loading-circle-5 {
    -webkit-transform: rotate(120deg);
        -ms-transform: rotate(120deg);
            transform: rotate(120deg);
}
.loading-fading-circle .loading-circle-6 {
    -webkit-transform: rotate(150deg);
        -ms-transform: rotate(150deg);
            transform: rotate(150deg);
}
.loading-fading-circle .loading-circle-7 {
    -webkit-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
            transform: rotate(180deg);
}
.loading-fading-circle .loading-circle-8 {
    -webkit-transform: rotate(210deg);
        -ms-transform: rotate(210deg);
            transform: rotate(210deg);
}
.loading-fading-circle .loading-circle-9 {
    -webkit-transform: rotate(240deg);
        -ms-transform: rotate(240deg);
            transform: rotate(240deg);
}
.loading-fading-circle .loading-circle-10 {
    -webkit-transform: rotate(270deg);
        -ms-transform: rotate(270deg);
            transform: rotate(270deg);
}
.loading-fading-circle .loading-circle-11 {
    -webkit-transform: rotate(300deg);
        -ms-transform: rotate(300deg);
            transform: rotate(300deg);
}
.loading-fading-circle .loading-circle-12 {
    -webkit-transform: rotate(330deg);
        -ms-transform: rotate(330deg);
            transform: rotate(330deg);
}

.loading-fading-circle .loading-circle-2:before {
    -webkit-animation-delay: -1.1s;
            animation-delay: -1.1s;
}
.loading-fading-circle .loading-circle-3:before {
    -webkit-animation-delay: -1s;
            animation-delay: -1s;
}
.loading-fading-circle .loading-circle-4:before {
    -webkit-animation-delay: -0.9s;
            animation-delay: -0.9s;
}
.loading-fading-circle .loading-circle-5:before {
    -webkit-animation-delay: -0.8s;
            animation-delay: -0.8s;
}
.loading-fading-circle .loading-circle-6:before {
    -webkit-animation-delay: -0.7s;
            animation-delay: -0.7s;
}
.loading-fading-circle .loading-circle-7:before {
    -webkit-animation-delay: -0.6s;
            animation-delay: -0.6s;
}
.loading-fading-circle .loading-circle-8:before {
    -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
}
.loading-fading-circle .loading-circle-9:before {
    -webkit-animation-delay: -0.4s;
            animation-delay: -0.4s;
}
.loading-fading-circle .loading-circle-10:before {
    -webkit-animation-delay: -0.3s;
            animation-delay: -0.3s;
}
.loading-fading-circle .loading-circle-11:before {
    -webkit-animation-delay: -0.2s;
            animation-delay: -0.2s;
}
.loading-fading-circle .loading-circle-12:before {
    -webkit-animation-delay: -0.1s;
            animation-delay: -0.1s;
}

/* theme color overrides start */
.ios #loadingSplashBottom,
.ios #loadingSplashTop  {
    background-color: #F47216;
}
.ios .loading-fading-circle .loading-circle:before {
    background-color: #000;
}

.neptune #loadingSplashBottom,
.neptune #loadingSplashTop,
.neptune-touch #loadingSplashBottom,
.neptune-touch #loadingSplashTop,
.modern-neptune #loadingSplashBottom,
.modern-neptune #loadingSplashTop {
    background-color: #277BC7;
}

.triton #loadingSplashBottom,
.triton #loadingSplashTop,
.modern-triton #loadingSplashBottom,
.modern-triton #loadingSplashTop {
    background-color: #277BC7;
}



/* theme color overrides end */

@-webkit-keyframes loading-circleFadeDelay {
    0%, 39%, 100% {
        opacity: 0;
    }
    40% {
        opacity: 1;
    }
}

@keyframes loading-circleFadeDelay {
    0%, 39%, 100% {
        opacity: 0;
    }
    40% {
        opacity: 1;
    }
}
