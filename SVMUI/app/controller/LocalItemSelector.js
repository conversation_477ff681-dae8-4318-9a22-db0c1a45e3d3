Ext.define('sfw.controller.LocalItemSelector', {
    extend: 'Ext.app.Controller',

    requires: [
        'sfw.view.results.smartgroup.SmartGroupGeneric',
        'sfw.view.commonpanels.LocalItemSelector',
        'sfw.view.results.smartgroup.SmartGroupItemSelector'
    ],

    refs: {
        localItemSelector: 'localItemSelector',
        smartGroupItemSelector: 'smartGroupItemSelector'
    },

    control: {
        localItemSelector: {
            beforeshow: 'beforeShowLocalItemSelector'
        }
    },

    itemSelectors: {

        // Define various stores that make sense for each supported type
        // Make sure relevant names match those returned everywhere in the backend.
        hostData: {
            nameSingular: 'Host',
            namePlural: 'Hosts',
            fields: [
                {name: 'nsi_device_id', type: 'int'},
                {name: 'host_name', type: 'string'},
                {name: 'group_name', type: 'string'},
                {name: 'ad_container', type: 'string'}
            ],
            columns: [
                {
                    // id: "host_name",
                    header: "Host",
                    flex: 3,
                    dataIndex: "host_name"
                },
                {
                    // id: "group_name",
                    header: "Site",
                    flex: 2,
                    dataIndex: "group_name",
                    // Use a special rendered for the Sites so that we can add
                    // the AD Path as a tooltip in case AD is enabled. This
                    // makes it easier to see what sites/groups are selected
                    renderer: function (value, metaData, record) {
                        return sfw.util.Default.renderADSiteName(value, record.get('ad_container'));
                    }
                }
            ],
            urlAppend: 'action=smart_groups&which=hostList&'
        },

        hostGroup: {
            nameSingular: 'Host Group',
            namePlural: 'Host Smart Groups',
            pageSize:16,
            root: 'data',
            fields: [
                {name: 'id', type: 'int'},
                {name: 'type', type: 'int'}, // smartgroup type
                {name: 'text', type: 'string'}
            ],
            columns: [
                {
                    // id: "smart_group_name",
                    header: "Host Smart Group",
                    flex: 3,
                    dataIndex: "text"
                },
                {
                    // id: "smart_group_type",
                    header: "Type",
                    flex: 2,
                    dataIndex: "type",
                    renderer: function (value, metaData, record) {
                        return sfw.util.Default.renderSmartgroupType(record.get('type'));
                    }
                }
            ],
            urlAppend: 'action=smart_groups&which=smartgroupList&withSmartGroupType=true&smartGroupTextType=host&'
        },

        siteData: {
            nameSingular: 'Site',
            namePlural: 'Sites',
            pageSize:16,
            fields: [
                {name: 'group_id', type: 'int'},
                {name: 'group_name', type: 'string'},
                {name: 'ad_container', type: 'string'}
            ],
            columns: [{
                // id: "group_name",
                header: "Site",
                dataIndex: "group_name",
                flex: 1,
                // Use a special rendered for the Sites so that we can add
                // the AD Path as a tooltip in case AD is enabled. This
                // makes it easier to see what sites/groups are selected
                renderer: function (value, metaData, record) {
                    return sfw.util.Default.renderADSiteName(value, record.get('ad_container'));
                }
            }],
            urlAppend: 'action=smart_groups&which=siteList&'
        },
        productData: {
            nameSingular: 'Product',
            namePlural: 'Products',
            fields: [
                {name: 'product_id', type: 'int'},
                {name: 'product_name', type: 'string'}
            ],
            columns: [
                {
                    // id: "product_name",
                    header: "Product",
                    flex: 1,
                    dataIndex: "product_name"
                }
            ],
            urlAppend: 'action=smart_groups&which=productList&'
        },

        /**
         * Operating Systems that exist in the Scan Results
         * @type {Object}
         */
        operatingsystemData: {
            nameSingular: "Operating System",
            namePlural: "Operating Systems",
            fields: [
                {name: "product_id", type: "int"},
                {name: "product_name", type: "string"}
            ],
            columns: [
                {
                    // id: "product_name",
                    header: "Operating System",
                    flex: 1,
                    dataIndex: "product_name"
                }
            ],
            urlAppend: "action=smart_groups&which=operatingsystemList&"
        },

        operatingsystemBuildData: {
            nameSingular: "Operating System Build",
            namePlural: "Operating System Builds",
            fields: [
                {name: "id", type: "string"},
                {name: "version", type: "string"}
            ],
            columns: [
                {
                    // id: "version",
                    header: "Operating System Build",
                    flex: 1,
                    dataIndex: "version"
                }
            ],
            urlAppend: "action=smart_groups&which=operatingsystemBuildList&"
        },

        kbData: {
            nameSingular: "KB Article",
            namePlural: "KB Articles",
            fields: [
                {name: "id", type: "int"},
                {name: "kb_article", type: "string"}
            ],
            columns: [
                {
                    // id: "kb_article",
                    header: "KB Article",
                    flex: 1,
                    dataIndex: "kb_article"
                }
            ],
            urlAppend: "action=smart_groups&which=kbList&"
        },

        cveData: {
            nameSingular: "CVE",
            namePlural: "CVEs",
            pageSize: 16,
            fields: [
                {name: "ref_id", type: "int"},
                {name: "ref_value", type: "string"}
            ],
            columns: [
                {
                    // id: "ref_value",
                    header: "CVE Number",
                    flex: 1,
                    dataIndex: "ref_value"
                }
            ],
            urlAppend: "action=smart_groups&which=cveList&"
        },
        dashboardProfileData: {
            nameSingular: 'Dashboard Profile',
            namePlural: 'Dashboard Profiles',
            pageSize: 6,
            fields: [
                {name: 'id', type: 'int'},
                {name: 'name', type: 'string'}
            ],
            columns: [
                {
                    header: "Profile",
                    flex: 1,
                    dataIndex: "name"
                }
            ],
            urlAppend: 'action=ajaxapi_dashboard&which=read&'
        },
        emailRecipientData: {
            nameSingular: 'Email Recipient',
            namePlural: 'Email Recipients',
            pageSize: 6,
            fields: [
                {name: 'recipient_account_id', type: 'int'},
                {name: 'account_name', type: 'string'},
                {name: 'contact_value', type: 'string'}
            ],
            columns: [
                {
                    header: "Name",
                    flex: 2,
                    dataIndex: "account_name"
                },
                {
                    header: "Email",
                    flex: 5,
                    dataIndex: "contact_value"
                }
            ],
            urlAppend: 'action=recipients&which=get_recipients&module=102&method=1&'
        },

        userSelectionData: {
            nameSingular: 'user',
            namePlural: 'users',
            pageSize: 10,
            fields: [
                {name: 'recipient_account_id', type: 'int'},
                {name: 'account_name', type: 'string'},
                {name: 'contact_value', type: 'string'}
            ],
            columns: [
                {
                    header: "Name",
                    flex: 2,
                    dataIndex: "account_name"
                },
                {
                    header: "Email",
                    flex: 5,
                    dataIndex: "contact_value"
                }
            ],
            urlAppend: 'action=recipients&which=get_recipients&module=101&method=1&'
        },

        selectEventsData: {
            nameSingular: 'Select Events',
            namePlural: 'Events',
            pageSize: 8,
            fields: [
                {name: 'event_type', type: 'int'},
                {name: 'event', type: 'string'}
            ],
            columns: [
                {
                    header: "Name",
                    flex: 2,
                    dataIndex: "event"
                }
            ],
            urlAppend: 'action=csi_notifications&which=getAllEvents&'
        },

        selectLanguagesData: {
            nameSingular: 'Select Package Languages',
            namePlural: 'Languages',
            pageSize: 9,
            fields: [
                {name: 'language', type: 'string'}
            ],
            columns: [
                {
                    header: "Name",
                    flex: 2,
                    dataIndex: "language"
                }
            ],
            urlAppend: 'action=vpm_package&which=getlangs&'
        },

        selectMobileRecipients: {
            nameSingular: 'Select Mobile Recipients',
            namePlural: 'Mobile',
            pageSize: 6,
            fields: [
                {name: 'recipient_account_id', type: 'int'},
                {name: 'account_name', type: 'string'},
                {name: 'contact_value', type: 'string'}
            ],
            columns: [
                {
                    header: "Name",
                    flex: 2,
                    dataIndex: "account_name"
                },
                {
                    header: "Phone",
                    flex: 5,
                    dataIndex: "contact_value"
                }
            ],
            urlAppend: 'action=recipients&which=get_recipients&module=100&method=2'
        },

        getDataFields: function (type, dataType) {
            var data, output = false;
            var CommonConstants = sfw.util.CommonConstants;
            switch (type) {
                case CommonConstants.CRITERIA_HOSTS:
                    data = this.hostData;
                    break;
                case CommonConstants.CRITERIA_HOST_GROUP:
                    data = this.hostGroup;
                    break;
                case CommonConstants.CRITERIA_SITES:
                    data = this.siteData;
                    break;
                case CommonConstants.CRITERIA_PRODUCTS:
                    data = this.productData;
                    break;
                case CommonConstants.CRITERIA_OPERATINGSYSTEMS:
                    data = this.operatingsystemData;
                    break;
                case CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD:
                    data = this.operatingsystemBuildData;
                    break;
                case CommonConstants.CRITERIA_KB_ARTICLE:
                    data = this.kbData;
                    break;
                case CommonConstants.CRITERIA_CVE_NUMBER:
                    data = this.cveData;
                    break;
                case CommonConstants.DASHBOARD_PROFILE:
                    data = this.dashboardProfileData;
                    break;
                case CommonConstants.EMAIL_RECIPIENT:
                    data = this.emailRecipientData;
                    break;
                case CommonConstants.SELECT_EVENTS:
                    data = this.selectEventsData;
                    break;
                case CommonConstants.SELECT_LANGUAGES:
                    data = this.selectLanguagesData;
                    break;
                case CommonConstants.SELECT_MOBILE_RECIPIENT:
                    data = this.selectMobileRecipients;
                    break;
                case CommonConstants.USER_SELECTION:
                    data = this.userSelectionData;
                    break;
                default:
                    return false;
                    break;
            }

            switch (dataType) {
                case 'nameSingular':
                    output = data.nameSingular;
                    break;
                case 'namePlural':
                    output = data.namePlural;
                    break;
                case 'fields':
                    output = data.fields;
                    break;
                case 'root':
                    output = data.root ? data.root : 'data.rows';
                    break;
                case 'columns':
                    output = data.columns;
                    break;
                case 'pageSize':
                    output = data.pageSize;
                    break;
                case 'urlAppend':
                    output = data.urlAppend;
                    break;
                default:
                    output = false;
                    break;
            }

            return output;
        }
    },

    // Config passed in must have the relevant fields specified
    // i.e. availableStore, namePlural
    genericSelector: function (config) {
        return Ext.apply({
            titleAvailable: 'Available ' + config.namePlural,
            titleSelected: 'Selected ' + config.namePlural
        }, config);
    },

    // Helper function for constructing most common use cases.
    // Call this one if we need to pass in the type dynamically, or use the shortcut functions below for a particular type.
    createItemSelector: function (type,moduleNumber=null) {
        var title, fields, idProperty, columns, autoExpandColumn, url, pageSize;
        title = this.itemSelectors.getDataFields(type, 'nameSingular') + ' Selection';
        fields = this.itemSelectors.getDataFields(type, 'fields');
        root = this.itemSelectors.getDataFields(type, 'root');
        idProperty = fields[0].name;
        columns = this.itemSelectors.getDataFields(type, 'columns');
        pageSize = this.itemSelectors.getDataFields(type, 'pageSize');
        autoExpandColumn = columns[0].id;

        url = sfw.util.Globals.apiPath() + this.itemSelectors.getDataFields(type, 'urlAppend');

        if(moduleNumber){
            var url = url.replace(/(module=).*?(&)/,'$1' + moduleNumber + '$2');
        }

        var storeConfig = {
            idProperty: idProperty,
            root: root,
            totalProperty: 'data.total',
            autoLoad: true,
            loaded: false,
            autoDestroy: true,
            pageSize: pageSize,
            fields: fields
        };

        var availableStore = new Ext.data.Store(
            Ext.apply(storeConfig, {
                pageSize: storeConfig.pageSize ? storeConfig.pageSize : 10,
                proxy: {
                    type: 'ajax',
                    url: url,
                    reader: {
                        type: 'json',
                        rootProperty: root,
                        totalProperty: storeConfig.totalProperty
                    }
                },
                storeId: '_availableStore'
            })
        );

        storeConfig = {
            idProperty: idProperty,
            root: root,
            totalProperty: 'data.total',
            autoLoad: true,
            loaded: false,
            autoDestroy: true,
            fields: fields
        };

        var selectedStore = new Ext.data.Store(
            Ext.apply(storeConfig, {
                storeId: '_selectedStore'
            })
        );

        var mainConfig = {
            title: title,
            height: 300,
            columns: columns,
            autoExpandColumn: autoExpandColumn,
            availableStore: availableStore,
            selectedStore: selectedStore,
            namePlural: this.itemSelectors.getDataFields(type, 'namePlural')
        };

        mainConfig = this.genericSelector(mainConfig);
        this.itemSelectorConfig = mainConfig;

        var requiredSelector = Ext.create('sfw.view.commonpanels.LocalItemSelector');
        requiredSelector.setMinHeight(mainConfig.height);
        requiredSelector.show();
        return requiredSelector;
    },

    createHostSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.CRITERIA_HOSTS);
    },

    createHostGroupSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.CRITERIA_HOST_GROUP);
    },

    createDashboardProfileSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.DASHBOARD_PROFILE);
    },

    createEmailRecipientSelector: function (moduleNumber=null) {
        return this.createItemSelector(sfw.util.CommonConstants.EMAIL_RECIPIENT,moduleNumber);
    },

    createUserSelector: function (moduleNumber=null) {
        return this.createItemSelector(sfw.util.CommonConstants.USER_SELECTION,moduleNumber);
    },

    createSelectEventSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.SELECT_EVENTS);
    },

    createLanguageSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.SELECT_LANGUAGES);
    },

    createMobileSelector: function (moduleNumber=null) {
        return this.createItemSelector(sfw.util.CommonConstants.SELECT_MOBILE_RECIPIENT, moduleNumber);
    },

    beforeShowLocalItemSelector: function (itemSelector) {
        const self = this;
        const config = self.itemSelectorConfig;
        itemSelector.config = self.itemSelectorConfig;

        var logEvent = "Events";

        var pagingtoolbar = itemSelector.down("pagingtoolbar");
        pagingtoolbar.setStore(config.availableStore);
        pagingtoolbar.setHidden(config.namePlural == 'KB Articles' ? true : false || config.namePlural == "Events" ? true : false);
        pagingtoolbar.emptyMsg = 'No Available ' + config.namePlural;
        pagingtoolbar.displayMsg = 'Displaying Available ' + config.namePlural + ' {0} - {1} of {2}';


        var toolbar = itemSelector.down("toolbar");
        toolbar.setHidden(config.namePlural == "Events" ? true : false);

        itemSelector.add([
            {
                xtype: 'gridpanel',
                height: '100%',
                width: '50%',
                itemId: 'availableGrid',
                store: config.availableStore,
                loadMask: true,
                secuniaSorting: config.namePlural == logEvent ? false : true,
                jsonSorters: true,
                selModel: {
                    selType: 'checkboxmodel'
                },
                title: config.titleAvailable,
                border: 1,
                cls: 'shadow',
                ui: 'gray',
                columns: config.columns,
                autoExpandColumn: config.autoExpandColumn,
                listeners: {
                    select: function (grid, record, rowIndex) {
                        if (-1 === config.selectedStore.findExact(config.selectedStore.config.idProperty, record.data[config.selectedStore.config.idProperty])) {
                            config.selectedStore.add(record);
                        }
                        var selectedGrid = itemSelector.down('#selectedGrid');
                        var selectionModel = selectedGrid.getSelectionModel();
                        selectionModel.selectAll();
                    },
                    deselect: function (grid, record, rowIndex) {
                        var recordToRemove = config.selectedStore.findRecord(config.columns[0].dataIndex, record.data[config.columns[0].dataIndex]);
                        config.selectedStore.remove(recordToRemove);
                    }
                }
            },
            {
                xtype: 'gridpanel',
                height: '100%',
                width: '50%',
                itemId: 'selectedGrid',
                store: config.selectedStore,
                loadMask: true,
                secuniaSorting: config.namePlural == logEvent ? false : true,
                jsonSorters: true,
                selModel: {
                    selType: 'checkboxmodel'
                },
                title: config.titleSelected,
                border: 1,
                cls: 'shadow',
                ui: 'gray',
                columns: config.columns,
                autoExpandColumn: config.autoExpandColumn,
                padding: '0 0 0 2px',
                listeners: {
                    deselect: function (grid, record, rowIndex) {
                        const availableGrid = itemSelector.down('#availableGrid');
                        const allRecords = config.availableStore.getDataSource();
                        const recordToDeselect = allRecords.find(config.columns[0].dataIndex, record.data[config.columns[0].dataIndex], 0, false, false, true);
                        const selectionModel = availableGrid.getSelectionModel();
                        selectionModel.deselect(recordToDeselect);
                        config.selectedStore.remove(record);
                    }
                }
            }
        ]);

        // First we make sure the selection
        // panel is pre-selected with the last
        // thing we actually saved in a
        // previous attempt. First clear it,
        // in case we selected sometehing and
        // cancelled without saving. We always
        // load directly from the
        // currentSelected.

        // itemSelector.availableStore.removeAll();
        // itemSelector.selectedStore.removeAll();
        // if ( rulePanel.currentSelected
        //      && rulePanel.currentSelected.data ) {
        //     rulePanel.itemSelector.selectedStore.add( rulePanel.currentSelected.data );
        // }

        // Flag the selectedStore as loaded so when
        // reloading the available store and trying to
        // highlight, we catch the overlapping records
        // there.
        // itemSelector.selectedStore.loaded = true;

        // Trigger a load of the available store, which
        // will highlight any overlap with the selected
        // store
        // itemSelector.availableStore.load();
    },

    createSiteSelector: function () {
        var siteSelector, adSelectorWindow, adButton;

        siteSelector = this.createItemSelector(sfw.util.CommonConstants.CRITERIA_SITES);

        return siteSelector;

        // To Do: Deep: "Ad Filter" button
        /*
        // Expanding common selector with AD browse functionality
        // TODO: there is some problems with hiding sfw.Window so using Ext.Window. Probably needs to be fixed.
        adSelectorWindow = new Ext.Window({
            layout: 'fit',
            id: 'ad_selectorWindow' + Ext.id(),
            title: 'Select Active Directory Node',
            width: 320,
            height: 350,
            plain: true,
            border: false,
            modal: true,
            closeAction: 'hide',
            // items: new Ext.tree.TreePanel({
            items: new Ext.tree.Panel({
                title: false,
                autoScroll: 'auto',
                loader: new Ext.tree.TreeLoader({
                    // To Do: Deep: Change url to original
                    // dataUrl: globals.apiPath() + '&action=active_directory&which=getChildrenContainers',
                    dataUrl: './static_api/&action=active_directory&which=getChildrenContainers.js',
                    listeners: {
                        load: function( loader ) {
                            delete loader.baseParams.targetNode;
                        }
                    }
                }),
                contextMenu: new Ext.menu.Menu({
                    items: [{
                        itemId: 'refresh-menu'
                        ,text: 'Refresh'
                    }]
                    ,listeners: {
                        itemclick: function() {
                            adSelectorWindow.items.items[0].loader.load( adSelectorWindow.items.items[0].getRootNode() );
                            adSelectorWindow.items.items[0].getRootNode().expand();
                        }
                    }
                }),
                root: new Ext.tree.AsyncTreeNode({
                    text: 'All Computers',
                    iconCls: 'adtree-all-computers',
                    id: '0',
                    expanded: true
                }),
                listeners: {
                    click: function( node ) {
                        adSelectorWindow.path = Ext.util.Format.htmlDecode( node.attributes.fqdn );
                        // fetching all subnodes to json store and when it's loaded transfer them to selected panel
                        var selectedStore = new Ext.data.JsonStore({
                            idProperty: sfw.itemSelectors.getDataFields( CommonConstants.CRITERIA_SITES, 'fields' )[0].name,
                            root: 'data.rows',
                            totalProperty: 'data.total',
                            loaded: false,
                            autoDestroy: true,
                            fields: sfw.itemSelectors.getDataFields( CommonConstants.CRITERIA_SITES, 'fields' ),
                            proxy: new Ext.data.HttpProxy( {
                                url: globals.apiPath() + '&action=active_directory&which=getNodeAndChildren&node=' + node.id,
                                method: "GET"
                            } ),
                            storeId: '_selectedStore',
                            listeners: {
                                load: function(self, records, options){
                                    records.map( function( record ) {
                                        if ( -1 === siteSelector.gridSelected.store.indexOfId( record.id ) ) {
                                            siteSelector.gridSelected.store.add( record );
                                        }
                                        siteSelector.gridSelected.getSelectionModel().selectRecords( [siteSelector.smSelected.grid.store.getById( record.id )], true );
                                        siteSelector.gridAvailable.getSelectionModel().selectRecords( [siteSelector.smAvailable.grid.store.getById( record.id )], true );
                                    } );
                                }
                            }
                        });
                        selectedStore.load();

                        adSelectorWindow.hide();
                    },
                    contextmenu: function( node, event ) {
                        node.select();
                        var menu = node.getOwnerTree().contextMenu;
                        menu.contextNode = node;
                        menu.showAt( event.getXY() );
                    }
                }
            })
        });

        adButton = new Ext.Button({
            text: 'AD Filter...',
            disabled: !sfw.ActiveDirectorySettings.hasADIntegration(),
            listeners: {
                click: function() {
                    adSelectorWindow.show();
                }
            }
        });

        sfw.events.addListener( "ad.on", function() {
            adButton.enable();
        } );

        sfw.events.addListener( "ad.off", function() {
            adButton.disable();
        } );

        siteSelector.getTopToolbar().add(
            { xtype: 'spacer', width: 5 },
            { xtype: 'tbseparator' },
            { xtype: 'spacer', width: 5 },
            adButton
        );

        return siteSelector;
        */
    },

    createProductSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.CRITERIA_PRODUCTS);
    },

    createOperatingSystemSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMS);
    },

    createOperatingSystemBuildSelector: function () {
        return this.createItemSelector(sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD);
    },

    searchAvailableStore: function () {
        const self = this;
        const config = self.itemSelectorConfig;
        const localItemSelector = self.getLocalItemSelector();
        const searchField = localItemSelector.down("#searchFieldItemSelector");
        config.availableStore.filter(config.columns[0].dataIndex, searchField.getValue());

        // config.availableStore.setBaseParam( "search", self.search.getValue() );
        // config.availableStore.load();
    }


});
