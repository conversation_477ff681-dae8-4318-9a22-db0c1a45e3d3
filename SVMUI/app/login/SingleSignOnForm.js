Ext.define('sfw.login.SingleSingOnForm', {
   extend: 'Ext.form.Panel',
   xtype: 'sso-form',

    cls: 'sso-form-panel',

    layout: {
        type: 'vbox'
    },

    defaults: {
        width: 310
    },

    style: {
        backgroundColor: '#0167AB !important'
    },

    border : false,
    method : 'GET',
    useEmptyText: true,

    items: [
        {
            xtype: 'label',
            height: 30,
            style: {
                color: '#fff'
            },
            html: '<span style="font-size: 18px;">Single sign-on</span>'
        },
        {
            xtype: 'textfield',
            name: 'sso_username',
            fieldLabel: '<span style="color:#fff;">Enter your email address</span>',
            labelAlign: 'top',
            itemId: 'emailId',
            height: 50,
            padding: '5 0',
            vtype: 'email',
            allowBlank : false,
            emptyText: 'Email'
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 60,
            items: [
                {
                    xtype: 'box',
                    width: 230,
                    height: 30,
                    style: {
                        paddingTop: '8px !important'
                    },
                    html: '<a class="clickable" style="color:#fff;font-weight:normal;cursor:pointer;" data-formid="login-form">I want to use password sign-in</a>',
                    listeners: {
                        click: {
                            element: 'el',
                            // scope: 'this',
                            delegate: '.clickable',
                            fn: 'onSignInFormSwitch'
                        }
                    }
                }
            ]
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 40,
            items: [
                {
                    xtype: 'button',
                    cls: 'btn-reg-login ',
                    height: 30,
                    width: 80,
                    text: 'Login',
                    style: {
                        color: '#000 !important',
                        borderRadius: '8px !important',
                        backgroundColor: '#e0c344'
                    },
                    handler: 'onSsoLoginFormSubmit'
                }
            ]
        }
    ]
});