Ext.define('sfw.login.LoginForm', {
    extend: 'Ext.container.Container',
    xtype: 'login-form',

    layout: {
        type: 'vbox'
    },

    defaults: {
        width: 310
    },
    style: {
        backgroundColor: '#0167AB' // lavender, aliceblue
    },

    items: [
        {
            xtype: 'label',
            height: 30,
            style: {
                color: '#fff'
            },
            html: '<span style="font-size: 18px;">Login</span>'
        },
        {
            xtype: 'textfield',
            name: 'userid',
            itemId: 'usernameId',
            height: 30,
            padding: '5 0',
            hideLabel: true,
            allowBlank : false,
            emptyText: 'Username',
            listeners: {
                specialkey: 'handleSpecialKeys'
            }
        },
        {
            xtype: 'textfield',
            itemId: 'passwordId',
            height: 30,
            padding: '5 0',
            hideLabel: true,
            emptyText: 'Password',
            inputType: 'password',
            name: 'password',
            allowBlank : false,
            listeners: {
                specialkey: 'handleSpecialKeys'
            }
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 60,
            items: [
                {
                    xtype: 'box',
                    width: 230,
                    height: 30,
                    style: {
                        paddingTop: '8px !important'
                    },
                    html: '<a class="clickable" style="color:#fff;font-weight:normal;cursor: pointer;" data-formid="reset-pwd-form">Forgot your password?</a>',
                    listeners: {
                        click: {
                            element: 'el',
                            delegate: '.clickable',
                            fn: 'onSignInFormSwitch'
                        }
                    }
                },
                {
                    xtype: 'button',
                    cls: 'btn-reg-login ',
                    height: 30,
                    width: 80,
                    text: 'Login',
                    style: {
                        color: '#000 !important',
                        borderRadius: '8px !important',
                        backgroundColor: '#e0c344'
                    },
                    handler: 'onLoginFormSubmit'
                }
            ]
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 40,
            items: [
                {
                    xtype: 'box',
                    width: 230,
                    height: 30,
                    style: {
                        paddingTop: '8px !important'
                    },
                    html: '<a class="clickable" style="color:#fff;font-weight: normal;cursor: pointer;" data-formid="sso-login">Use single sign-on</a>',
                    listeners: {
                        click: {
                            element: 'el',
                            delegate: '.clickable',
                            fn: 'onSignInFormSwitch'
                        }
                    }
                }
            ]
        }
    ]
});