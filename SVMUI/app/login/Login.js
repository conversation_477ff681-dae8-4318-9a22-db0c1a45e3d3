Ext.define('sfw.login.Login', {
    extend: 'Ext.container.Container',
    xtype: 'login',

    requires: [
        'Ext.container.Container',
        'Ext.form.field.Text',
        'Ext.form.field.Checkbox',
        'Ext.layout.container.VBox',
        'Ext.button.Button'
    ],

    layout: {
        type: 'border'
    },

    controller: 'login',

    listeners: {
        'switchlogin': 'onSignInFormSwitch'
    },

    viewModel: {
        formulas: {
            redirect_url: function(){
                var return_url = "https://csi7.secunia.com/csi"
                if (sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION) {
                    var ur = Ext.getWin().dom.location.protocol + '//' + Ext.getWin().dom.location.hostname;
                    return_url = ur + '/csi';
                }
                return return_url;
            }
        }
    },

    items: [
        {
            xtype: 'container',
            region: 'center',
            autoComplete: true,
            bodyPadding: '20 20',

            style: {
                background: '#FFFFFF'
            },

            width: 350,
            height: 420,

            layout: {
                type: 'vbox',
                align: 'center',
                pack: 'center'
            },

            defaults : {
                margin : '5 0'
            },

            items: [
                {
                    xtype: 'image',
                    src: 'resources/images/Flexera-Logo.svg',
                    width: 350,
                    height: 150
                },
                {
                    xtype: 'container',
                    itemId: 'secureSignInId',
                    layout: {
                        type: 'card',
                        animationPolicy: {
                            y: true,
                            height: true
                        }
                    },
                    activeItem: 0,
                    padding: 20,
                    //height: 270,
                    minHeight: 270,
                    style: {
                        backgroundColor: '#0167AB'
                    },
                    items: [
                        {
                            xtype: 'login-form',
                            id: 'login-form'
                        },
                        {
                            xtype: 'reset-pwd-form',
                            id: 'reset-pwd-form'
                        },
                        {
                            xtype: 'change-pwd-form',
                            id: 'change-pwd-form'
                        },
                        {
                            xtype: 'sso-form',
                            id: 'sso-login'
                        }
                    ]
                }
            ]
        },{
            xtype:'label',
            region:'north',
            style:'padding-top:2%',
            bind: {
                html: `<div style = 'display: flex; justify-content: center; padding: 15px 30px 13px; background-color: #00a1de; color: #fff;font-size:100%'>
               <i class="fa fa-info-circle" aria-hidden="true" style="font-size:180%"></i>&nbsp;&nbsp;<span style="line-height:1.5">Note that if you wish to use ActiveX-dependent features of SVM, like patching and software suggestions, it is necessary to <a style='color:white' href="{redirect_url}">visit our previous interface for SVM</a> running Internet Explorer with our ActiveX plug-in. Increasingly, you will see such capabilities make their way to the SVM Publisher, which will eventually replace the need for the previous Internet Explorer admin console. For details, limitations, and plans, please see our <a target="_blank" style='color:white' href="https://community.flexera.com/t5/Software-Vulnerability/SVM-User-Interface-FAQ/ba-p/215480"><b>FAQ</b></a>.</span>
                   </div>`
            }
        },
        {
            xtype: 'label',
            region: 'south',
            html: `<div class='toolbarLoginDiv'><div id='productLogo'></div></div>
                   <p class='page-footer'>
                       <span style="padding-left: 20px;">&#169; 2015 &ndash; 2025 Flexera. All rights reserved. </span>
                       <a href='https://www.flexera.com/legal/privacy-policy.html' target='_blank'>Data&nbsp;Privacy</a>
                   </p>`
        }
    ],

    initComponent: function() {
        if (sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION) {
            sfw.googleAnalytics.googleAnalytics.send(this.title, null);
        }
        this.callParent(arguments);
    }
});
