Ext.define('sfw.login.ResetPwdForm', {
    extend: 'Ext.form.Panel',
    xtype: 'reset-pwd-form',

    cls: 'sso-form-panel',

    layout: {
        type: 'vbox'
    },

    height: 460,
    style: {
        backgroundColor: '#0167AB !important'
    },

    border : false,
    method : 'POST',
    useEmptyText: true,

    defaults: {
        xtype: 'textfield',
        allowBlank : false,
        padding: '5 0',
        width: 310,
        enableKeyEvents : true,
        hideLabel: true,
        listeners : {
            keyup : 'enforceLength'
        }
    },

    items: [
        {
            xtype: 'label',
            height: 30,
            style: {
                color: '#fff'
            },
            html: '<span style="font-size: 18px;">Change Your Password</span>'
        },
        {
            name : 'recoveryusername',
            fieldLabel: '<span style="color:#fff;"><b>Step 1:</b> Request PIN codes</span>',
            labelAlign: 'top',
            hideLabel: false,
            itemId: 'recoverUsernameId',
            height: 50,
            emptyText: 'Username'
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 50,
            margin: '10 0 0 0',
            items: [
                {
                    xtype: 'button',
                    cls: 'btn-reg-login ',
                    itemId: 'getAuthPinBtnId',
                    height: 30,
                    width: 180,
                    text: 'Get Authentication Pins',
                    disabled : true,
                    style: {
                        color: '#000 !important',
                        borderRadius: '8px !important',
                        backgroundColor: '#e0c344'
                    },
                    handler: 'requestAuthenticationPin'
                }
            ]
        },
        {
            name : 'pin_email',
            fieldLabel: '<span style="color:#fff;"><b>Step 2:</b> Enter your PIN codes</span>',
            labelAlign: 'top',
            itemId: 'emailPinCodeId',
            height: 60,
            hideLabel: false,
            emptyText: 'Authentication Pin via Email'
        },
        {
            name : 'pin_sms',
            itemId: 'smsPinCodeId',
            height: 30,
            emptyText: 'Authentication Pin via SMS'
        },
        {
            name : 'new_pwd_textfield',
            inputType : 'password',
            itemId: 'newPwdId',
            height: 30,
            emptyText: 'New Password'
        },
        {
            name : 'new_pwd_conf_textfield',
            inputType : 'password',
            itemId: 'confirmPwdId',
            height: 30,
            emptyText: 'Confirm Password'
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 40,
            items: [
                {
                    xtype: 'button',
                    cls: 'btn-reg-login ',
                    height: 30,
                    width: 140,
                    itemId: 'modifyPwdBtnId',
                    text: 'Modify Password',
                    disabled : true,
                    style: {
                        color: '#000 !important',
                        borderRadius: '8px !important',
                        backgroundColor: '#e0c344'
                    },
                    handler: 'authenticateUser'
                }
            ]
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 60,
            items: [
                {
                    xtype: 'box',
                    width: 230,
                    height: 30,
                    style: {
                        paddingTop: '8px !important'
                    },
                    html: '<a class="clickable" style="color:#fff;font-weight:normal;cursor:pointer;" data-formid="login-form"><< Back</a>',
                    listeners: {
                        click: {
                            element: 'el',
                            delegate: '.clickable',
                            fn: 'onSignInFormSwitch'
                        }
                    }
                }
            ]
        }
    ]
});