Ext.define('sfw.login.LoginController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.login',

    constructor: function (config) {
        this.callParent(arguments)

        this.onSignInFormSwitch = Ext.bind(this.onSignInFormSwitch, this);
    },

    onLoginFormSubmit:  function() {
        const me = this,
            view = me.getView(),
            username = view.down('#usernameId'),
            password = view.down('#passwordId'),
            auth = sfw.util.Auth;

        me.manualLogin(username, password).then(function() {
            auth.hideMask();
        }, function() {
            //on fail
            auth.hideMask();
        });
    },

    handleSpecialKeys: function (field, e) {
        var me = this;
        if (e.getKey() == e.ENTER) {
            me.onLoginFormSubmit();
        }
    },

    //4 -- SSO form submit event
    onSsoLoginFormSubmit: function (btn) {
        const me = this,
            view = me.getView(),
            ssoForm = view.down('sso-form'),
            defaults = sfw.util.Default,
            globals = sfw.util.Globals,
            email = ssoForm.down('#emailId');

        ssoForm.submit({
            url : 'action=sso_login',
            method: 'POST',
            waitTitle: 'Connecting',
            waitMsg: 'Please wait...',
            submitEmptyText: false,
            success: function(form, answer) {
                const data = Ext.decode(answer.response.responseText),
                    response = data.response,
                    reason = data.reason,
                    type = sfw.util.Auth.constants;

                switch ( response ) {
                    case type.LOGIN_SUCCESSFUL:
                        // This 'sso' cookie only exists when we've logged in successfully.
                        // If found we will check if we're logged in via a sso_login request.
                        // This cookie is cleared in globals.clearAPIcredentials()
                        defaults.setCookie( 'sp_login', '1' );
                        window.location.assign(defaults.htmlSpecialCharsDecode(data.redirectUrl));
                        break;
                    case type.ERROR_INVALID_CREDENTIALS:
                        email.setValue('');
                        Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( reason ) );
                        break;
                    case type.SSO_ERROR:
                        email.setValue('');
                        Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( reason ) );
                        break;
                    default:
                        if(typeof response != 'undefined'){
                            Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( reason ) );
                        } else	{
                            Ext.Msg.alert('Error','Connection failed.');
                        }
                }
            },
            failure: function(form, action) {
                if (action.failureType === Ext.form.Action.CLIENT_INVALID) {
                    Ext.Msg.alert('Error','Username or password is blank');
                } else {
                    Ext.Msg.alert('Error','Connection failed.');
                }
            }
        });
    },

    //3 -- Change Password Form submit
    onChangePwdFormSubmit: function () {
        const me = this,
            view = me.getView(),
            form = view.down('change-pwd-form'),
            globals = sfw.util.Globals,
            oldPwd = form.down('#oldPwdId').getValue(),
            newPwd = form.down('#newPwdId').getValue(),
            confirmPwd = form.down('#confirmPwdId').getValue(),
            btn = form.down('#changePwdBtnId');

        //TODO
        //check if btn is enabled or disabled

        if ( oldPwd.length === 0 ) {
            Ext.Msg.show({
                title: "Error",
                icon: Ext.Msg.ERROR,
                width: 400,
                msg: "You have to enter old password.",
                buttons: Ext.Msg.OK
            });
            return;
        }
        if ( newPwd !== confirmPwd ) {
            Ext.Msg.show({
                title: "Error",
                icon: Ext.Msg.ERROR,
                width: 400,
                msg: "Passwords you have entered do not match.",
                buttons: Ext.Msg.OK
            });
            return;
        }
        if ( newPwd.length === 0 || confirmPwd.length === 0 ) {
            Ext.Msg.show({
                title: "Error",
                icon: Ext.Msg.ERROR,
                width: 400,
                msg: "You have to enter new password.",
                buttons: Ext.Msg.OK
            });
            return;
        }

        Ext.Ajax.request({
            url: globals.apiPath( globals.UserID ) + '&action=ajaxapi_reset_password',
            method: 'POST',
            params: form.getValues(),
            success: function( data ) {
                data = Ext.decode(data.responseText, true);
                if ( data.response != 0 && data.response != 1 ) {
                    sfw.util.Debug.log( 'Error trying to change password. Violation of Password Policy: ' + data.reason );
                    Ext.Msg.show({
                        title: "Error",
                        icon: Ext.Msg.ERROR,
                        msg: "Violation of Password Policy: " + data.reason,
                        buttons: Ext.Msg.OK,
                        fn: function() {
                            //sfw.loginWindow.interface.show();
                        }
                    });
                    return;
                }

                Ext.Msg.show({
                    title: 'Success',
                    msg: 'Password changed successfully.',
                    buttons: Ext.Msg.OK,
                    fn: function() {
                        // The password has been changed so load the interface
                        sfw.util.Auth.checkLogin();
                    },
                    icon: Ext.MessageBox.INFO
                });
            },
            failure: function() {
                sfw.util.Debug.log( 'Error while trying to change password.' );
            }
        });
    },

    //-- load all password policy on form show
    onChangePwdFormShow: function () {
        const me = this,
            view = me.getView(),
            globals = sfw.util.Globals,
            policyBox = view.lookup('changePwdPolicyRef');

        view.setLoading(true);
        Ext.Ajax.request({
            url: 'action=password_policy_configuration&which=get_options&',
            method: 'POST',
            success: function( data ) {
                view.setLoading(false);
                const result = Ext.decode( data.responseText );
                if ( !result.success ) {
                    sfw.util.Debug.log( 'Error while trying to retrieve the Password Policy Configuration: ' + data.msg );
                    return;
                }

                const showData = sfw.util.PasswordPolicyConfig.showPolicy( result.data );
                if (!Ext.isEmpty(showData)) {
                    policyBox.setHtml(showData[1]);
                    if(showData[0]) {
                        policyBox.show();
                    } else {
                        policyBox.hide();
                    }
                }
            },
            failure: function() {
                sfw.util.Debug.log( 'Error while trying to retrieve the Password Policy Configuration.' );
                view.setLoading(false);
            }
        });

    },

    //2- Reset your password
    //----------------------------
    requestAuthenticationPin: function (btn) {
        const me = this,
            view = me.getView(),
            form = view.down('reset-pwd-form'),
            recoverUsername = form.down('#recoverUsernameId'),
            LoginDetails = sfw.util.Auth.LoginDetails;

        //me.clearRecoverPasswordFormData();

        var userName = form.down('#recoverUsernameId').getValue(),
            emailPinCode = form.down('#emailPinCodeId').getValue(),
            smsPinCode = form.down('#smsPinCodeId').getValue(),
            newPwd = form.down('#newPwdId').getValue(),
            confirmPwd = form.down('#confirmPwdId').getValue();

        var request = {
            recoveryusername: userName,
            pin_email: emailPinCode,
            pin_sms: smsPinCode,
            new_pwd_textfield: newPwd,
            new_pwd_conf_textfield: confirmPwd
        };

        Ext.Ajax.request({
            url: 'action=send_authentication_pin&ui=1&' + Math.random(),
            method: 'POST',
            params: request,
            success: function(response) {
                const pinRequest = Ext.decode(response.responseText, true);
                if (!pinRequest) {
                    Ext.Msg.show({
                        title: "Error",
                        icon: Ext.Msg.ERROR,
                        msg: "Unknown error: invalid server response. Please try again later",
                        buttons: Ext.Msg.OK
                    });
                    return;
                }

                const expireInterval = pinRequest.expire,
                    title = 'Message';

                var andMobile = LoginDetails.isSMSEnabled ? ' and mobile number' : '';
                var msg = 'Authentication pin codes were sent to your verified email address' + andMobile +
                    '. The pin expires in ' + expireInterval + ' hours of being sent. If ' +
                    'you did not receive the pin, please contact support.';
                var width = 500;

                recoverUsername.setReadOnly( true );

                Ext.Msg.show({
                    title : title,
                    msg : msg,
                    width : width,
                    buttons : Ext.Msg.OK,
                    icon : Ext.MessageBox.INFO
                });
            },
            failure : function( response ) {
                switch ( response.failureType ) {
                    case Ext.form.Action.CLIENT_INVALID:
                        Ext.Msg.alert('Failure','Form fields do not contain valid values');
                        break;
                    case Ext.form.Action.CONNECT_FAILURE:
                        Ext.Msg.alert('Failure', 'Connection error');
                        break;
                    default:
                        Ext.Msg.alert('Failure', 'Abnormal Execution');
                        break;
                }
            }
        });
    },

    clearRecoverPasswordFormData: function () {
        const me = this,
            view = me.getView(),
            form = view.down('reset-pwd-form');

        form.getForm().reset();
    },

    authenticateUser: function () {
        const me = this,
            view = me.getView(),
            form = view.down('reset-pwd-form');

        var userName = form.down('#recoverUsernameId').getValue(),
            emailPinCode = form.down('#emailPinCodeId').getValue(),
            smsPinCode = form.down('#smsPinCodeId').getValue(),
            newPwd = form.down('#newPwdId').getValue(),
            confirmPwd = form.down('#confirmPwdId').getValue();

        const request = {
            recoveryusername: userName,
            pin_email: emailPinCode,
            pin_sms: smsPinCode,
            new_pwd_textfield: newPwd,
            new_pwd_conf_textfield: confirmPwd
        };

        Ext.Ajax.request({
            url : 'action=authenticate_user&ui=1&'+ Math.random(),
            method: 'POST',
            params: request,
            success : function(response) {
                const pwdUpdateRequest = Ext.decode(response.responseText, true);
                if (!pwdUpdateRequest) {
                    Ext.Msg.show({
                        title: "Error",
                        icon: Ext.Msg.ERROR,
                        msg: "Unknown error: invalid server response. Please try again later",
                        buttons: Ext.Msg.OK
                    });
                    return;
                }

                var response = pwdUpdateRequest.response;
                var title = 'Error';
                var msg = '';
                var width = 500;

                switch (response) {
                    case -1:
                        title = 'Abnormal Error';
                        msg = 'Unknown error!';
                        width = 200;
                        break;
                    case 0:
                        title = 'Success';
                        msg = 'Your password has been updated!';
                        width = 275;

                        me.clearRecoverPasswordFormData();
                        view.down('login-form').down('#passwordId').setValue('');
                        me.onSignInFormSwitch('login-form');

                        Ext.Msg.show({
                            title: 'Success',
                            msg: 'Password changed successfully.',
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                        break;
                    case 1:
                        msg = 'Username is empty.';
                        width = 200;
                        break;
                    case 2:
                        msg = 'Authentication Pins are of incorrect length.';
                        width = 350;
                        break;
                    case 3:
                        msg = 'The password does not meet the password policy requirements.';
                        width = 375;
                        break;
                    case 4:
                        msg = 'The passwords provided do not match.';
                        width = 325;
                        break;
                    case 9:
                        msg = 'Reset password failed. Please visit https://community.flexera.com/t5/forums/postpage/board-id/@support/  for support.';
                        width = 500;
                        break;
                    default:
                        break;
                }

                Ext.Msg.show({
                    title : title,
                    msg : msg,
                    width : width,
                    buttons : Ext.Msg.OK,
                    icon : Ext.MessageBox.INFO
                });
            },
            failure : function(response) {
                switch (response.failureType) {
                    case Ext.form.Action.CLIENT_INVALID:
                        Ext.Msg.alert('Failure','Form fields do not contain valid values');
                        break;
                    case Ext.form.Action.CONNECT_FAILURE:
                        Ext.Msg.alert('Failure', 'Connection error');
                        break;
                    default:
                        Ext.Msg.alert('Failure', 'Abnormal Execution');
                        break;
                }
            }
        });
    },

    // used for Reset Pwd Form
    enforceLength: function () {
        const me = this,
            view = me.getView(),
            changePwdForm = view.down('change-pwd-form'),
            resetPwdForm = view.down('reset-pwd-form'),

            recoverUsername = resetPwdForm.down('#recoverUsernameId'),
            getAuthPinBtn = resetPwdForm.down('#getAuthPinBtnId'),

            emailPinCode = resetPwdForm.down('#emailPinCodeId'),
            smsPinCode = resetPwdForm.down('#smsPinCodeId'),
            newPwd = resetPwdForm.down('#newPwdId'),
            confirmPwd = resetPwdForm.down('#confirmPwdId'),
            modifyPwdBtn = resetPwdForm.down('#modifyPwdBtnId'),
            LoginDetails = sfw.util.Auth.LoginDetails;

        var getAuthPinsButtonFlag = false;
        var authModifyPasswordButtonFlag = true;

        if (recoverUsername.getValue().length < 1 ) {
            getAuthPinsButtonFlag = true;
        }

        getAuthPinBtn.setDisabled( getAuthPinsButtonFlag );

        if (( emailPinCode.getValue().length === 4 )
            && (!LoginDetails.isSMSEnabled || smsPinCode.getValue().length === 4 )
            && ( newPwd.getValue().length >= 8 )
            && ( confirmPwd.getValue().length >= 8 )
            && ( recoverUsername.getValue().length >= 1 ) )
        {
            authModifyPasswordButtonFlag = false;
        } else {
            authModifyPasswordButtonFlag = true;
        }
        modifyPwdBtn.setDisabled( authModifyPasswordButtonFlag );
    },

    //----------------------------
    manualLogin: function (username, password) {
        const me = this,
            view = me.getView(),
            defaults = sfw.util.Default,
            globals = sfw.util.Globals,
            auth = sfw.util.Auth,
            LoginDetails = auth.LoginDetails,
            deferred = new Ext.Deferred();

        auth.showMask();

        Ext.Ajax.request({
            url: 'action=manuallogin&ui=1&' + Math.random(),
            method: 'POST',
            params: {
                username: username.getValue(),
                password: password.getValue()
            },
            success: function(answer) {
                const data = Ext.decode(answer.responseText),
                    lResponse = data.response,
                    lReason = data.reason;

                // TODO: add proper ext.msg instead of alerts
                var type = auth.constants; // standin var to make it easier to read

                switch ( lResponse ) {
                    case type.LOGIN_SUCCESSFUL:
                        if ( defaults.getCookie( "sp_login" ) && !defaults.getCookie( "check" )) {
                            document.cookie = 'sp_login=; expires=' + encodeURIComponent( new Date(0).toUTCString() );
                        }
                        // This 'check' cookie only exists when we've logged in successfully.
                        // If found we will check if we're logged in via a checklogin request.
                        // If not found we ask for a username/password and password again. This
                        // cookie is cleared in globals.clearAPIcredentials()
                        defaults.setCookie( 'check', '1' );
                        // Set a CSRF Token for the checklogin request
                        globals.UserID = data.uid;
                        // Show main GUI
                        //auth.interface.hide();
                        //auth.createMask( 'Please wait...##' );
                        LoginDetails.isLoggedIn = true;
                        Ext.GlobalEvents.fireEvent('manuallogin');
                        auth.checkLogin();
                        break;
                    case type.LOGIN_CHANGEPASSWORD: // LOGIN SUCCESSFUL AND SHOW CHANGE PASSWORD DIALOG
                        // This 'check' cookie only exists when we've logged in successfully.
                        // If found we will check if we're logged in via a checklogin request.
                        // If not found we ask for a username/password and password again. This
                        // cookie is cleared in globals.clearAPIcredentials()
                        defaults.setCookie( 'check', '1' );
                        // Set a CSRF Token for Changing Password requests
                        globals.UserID = data.uid;
                        // Show Change Password dialog
                        me.onSignInFormSwitch('change-pwd-form');
                        break;
                    case type.ERROR_INVALID_CREDENTIALS:
                        password.setValue('');
                        Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        auth.hideMask();
                        break;
                    case type.ERROR_UPGRADE_IN_PROGRESS:
                        Ext.Msg.alert( 'Upgrading account', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        auth.hideMask();
                        break;
                    case type.ERROR_NO_LICENSES:
                        Ext.Msg.alert( 'No license', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        auth.hideMask();
                        break;
                    case type.ERROR_PASSWORD_POLICY:
                        Ext.Msg.alert( 'Password policy', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        // This 'check' cookie only exists when we've logged in successfully.
                        // If found we will check if we're logged in via a checklogin request.
                        // If not found we ask for a username/password and password again. This
                        // cookie is cleared in globals.clearAPIcredentials()
                        defaults.setCookie( 'check', '1' );
                        // Set a CSRF Token for Changing Password requests
                        globals.UserID = data.uid;
                        auth.hideMask();
                        me.onSignInFormSwitch('change-pwd-form');
                        break;
                    case type.ERROR_ONE_TIME_PASSWORD:
                        Ext.Msg.alert( 'One time password', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        auth.hideMask();
                        break;
                    case type.ERROR_ACCOUNT_EXPIRY:
                        Ext.Msg.alert( 'Account has expired', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        auth.hideMask();
                        break;
                    default:
                        if(typeof lReason != 'undefined'){
                            Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                        } else	{
                            Ext.Msg.alert('Error','Connection failed.');
                        }
                        auth.hideMask();
                }
            },
            failure: function(form, action) {
                if (action.failureType === Ext.form.Action.CLIENT_INVALID) {
                    Ext.Msg.alert('Error','Username or password is blank');
                } else {
                    Ext.Msg.alert('Error','Connection failed.');
                }
                auth.hideMask();
            }
        });

        return deferred;
    },

    onSignInFormSwitch: function (event) {
        const me = this,
            view = me.getView(),
            formId = event.isEvent ? event.target.dataset.formid: event,
            card = view.down('#secureSignInId');

        card.getLayout().setActiveItem(formId || 'login-form');
    }

});