Ext.define('sfw.login.ChangePwdForm', {
    extend: 'Ext.form.Panel',
    xtype: 'change-pwd-form',

    cls: 'sso-form-panel',

    layout: {
        type: 'vbox'
    },

    height: 270,

    defaults: {
        xtype: 'textfield',
        inputType : 'password',
        allowBlank : false,
        padding: '5 0',
        width: 310,
        enableKeyEvents : true,
        hideLabel: true
    },

    //handler enter and validation event here
    keyMap: {

    },

    height: 420,

    style: {
        backgroundColor: '#0167AB !important'
    },

    border : false,
    method : 'POST',
    useEmptyText: true,

    listeners: {
        show: 'onChangePwdFormShow'
    },

    items: [
        {
            xtype: 'label',
            height: 30,
            style: {
                color: '#fff'
            },
            html: '<span style="font-size: 18px;">Password Expired</span>'
        },
        {
            xtype: 'box',
            hidden: true,
            style: {
                color: '#fff'
            },
            reference: 'changePwdPolicyRef'
        },
        {
            name: 'ex_pwd_textfield',
            itemId: 'oldPwdId',
            height: 30,
            emptyText: 'Old Password'
        },
        {
            name: 'new_pwd_textfield',
            itemId: 'newPwdId',
            height: 30,
            emptyText: 'New Password'
        },
        {
            name: 'new_pwd_conf_textfield',
            itemId: 'confirmPwdId',
            height: 30,
            emptyText: 'Confirm Password'
        },
        {
            xtype: 'container',
            layout: 'hbox',
            height: 40,
            items: [
                {
                    xtype: 'button',
                    cls: 'btn-reg-login ',
                    itemId: 'changePwdBtnId',
                    height: 30,
                    width: 140,
                    text: 'Change Password',
                    style: {
                        color: '#000 !important',
                        borderRadius: '8px !important',
                        backgroundColor: '#e0c344'
                    },
                    handler: 'onChangePwdFormSubmit'
                }
            ]
        }
    ]
});