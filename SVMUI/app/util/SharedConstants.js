Ext.define('sfw.util.SharedConstants', {
    singleton :true,
    alternateClassName: 'sfw.SharedConstants',

    MOD_NSI: 0x0020,
    MOD_VDB: 0x0080,
    MOD_UM: 0x0080,
    MOD_VTS: 0x0004,
    MOD_VSS: 0x0010,

    dateShortInput: 'Y-m-d',
    dateLongOutput: 'jS M, Y H:i',

    responses: {
        UNEXPECTED_ERROR: -1,
        SUCCESS: 0,
        GENERIC_ERROR: 1,
        INVALID_ARGUMENTS: 2,
        NOT_ALLOWED: 3,
        CONFIG_ERROR: 4,
        RESOURCE_UNAVAILABLE: 5,
        ERROR_NO_WRITE_PERMISSION: 50,
        ERROR_ACCESS_DENIED: 51
    },

    /**
     * These constants are used to specify which edition of the CSI is being run.
     */
    HOSTED_EDITION: 0,
    SERVER_EDITION: 1,
    // change this to set the current edition (overwritten by the Phing build) //TODO
    //EDITION: sfw.sharedConstants.HOSTED_EDITION,
    EDITION: 0,

    AUTH_TYPE_NORMAL: 1,
    AUTH_TYPE_LDAP: 2,
    AUTH_TYPE_SSO: 7
});