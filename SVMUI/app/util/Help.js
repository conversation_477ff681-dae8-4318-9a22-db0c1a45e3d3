Ext.define('sfw.util.Help', {
    singleton :true,
    alternateClassName: 'sfw.help',

    requires: ['sfw.Default', 'sfw.CommonConstants', 'sfw.SharedConstants'],

    /**
     * @property {Object}
     * @readonly
     * Contains CSI-Page-ID to Topic-ID bindings for the CSI .chm help file.
     * When a user clicks Help we look up their current page here to find
     * find Topic ID we should load when opening the .chm file.
     */
    topicBindings : {
        /* Auto generated by parsing the html help page for topics and text matching them against CSI's menu text */
        "sfw.csiVimAccounts" : "",//"Accounts_Overview"
        "sfw.CSIActivityLog" : "Activity_Log",
        "sfw.csiAdministration" : "Administration",
        "sfw.csiAdvisorySmartGroupsFolder" : "Advisory_Smart_Groups",
        "sfw.csiAgentDeployment" : "Agent_Deployment",
        "sfw.csiVimAssetLists" : "",//"Asset_Lists"
        "sfw.csiAvailablePatches" : "Available",
        "sfw.csiResetPassword" : "Change_Password",
        "sfw.csiCompletedScans" : "Completed_Scans",
        "sfw.csiConfiguration" : "Configuration",
        "sfw.csiCustomScanRules" : "Custom_Scan_Rules",
        "sfw.csiDashboard" : "The_Dashboard",
        "sfw.csiDatabaseAccess" : "Database_Access",
        "sfw.csiDatabaseCleanup" : "Database_Cleanup",
        "sfw.csiDbConsole" : "Database_Console",
        "sfw.csiPatchDeployment" : "Deployment",
        "sfw.csiLocalAgentDownload" : "Download_Local_Agent",
        "sfw.csiNetworkAgentDownload" : "Download_Network_Agent",
        "sfw.csiFilterScanResults" : "Filter_Scan_Results",
        "sfw.csiHostSmartGroupsFolder" : "Host_Smart_Groups",
        "sfw.csiLogger" : "Log_Messages",
        "sfw.csiNetworkApplianceAgents" : "Network_Appliance_Agents",
        "sfw.csiNetworkApplianceGroups" : "Network_Appliance_Groups",
        "sfw.csiPasswordPolicyConfiguration" : "Password_Policy_Configuration",
        "sfw.csiPasswordRecovery" : "Password_Recovery_Settings",
        "sfw.csiPatch" : "Patching",
        "sfw.csiProductSmartGroupsFolder" : "Product_Smart_Groups",
        "sfw.csiQuickScan" : "Quick_Scan",
        "sfw.csiRemoteScanningViaCSI" : "Remote_Scanning_Via_Software_Vulnerability_Manager__Agent_less_Scan_",
        "sfw.csiRemoteScanningViaAgents" : "Remote_Scanning_Via_Agents",
        "sfw.csiReportingConfiguration" : "Report_Configuration",
        "sfw.csiReports" : "Reporting",
        "sfw.csiResults" : "Results",
        "sfw.csiScanGroups" : "Scan_Groups",
        "sfw.csiScanProgress" : "Scan_Progress",
        "sfw.csiScanning" : "Scanning",
        "sfw.csiScheduledExports" : "Scheduled_Exports",
        "sfw.csiPassword" : "Security",
        "sfw.csiSettings" : "Settings",
        "sfw.csiSitesOverview" : "Sites",
        "sfw.csiAgentManagement" : "Single_Host_Agents",
        "sfw.csiSmartgroupNotifications" : "Smart_Group_Notifications",
        "sfw.csiSuggestSoftware" : "Suggest_Software",
        "sfw.csiSoftwareSuggestion" : "Software_Suggestions",
        "sfw.csiUserManagement" : "User_Management",
        "sfw.csiPartitionManagement" : "Partition_Management",
        "sfw.csiVimIntegration" : "",//"VIM_Integration"
        "sfw.csiScanningViaLocalAgents" : "Scanning_Via_Local_Agents",
        "sfw.csiSccm" : "System_Center_Inventory_Import",
        "sfw.csiAdvisoriesZeroDay" : "Secunia_Zero_Day_Advisories_Opt",
        "sfw.csiSmartGroupsConfigured" : "Configured_Product_Groups",
        "sfw.csiHostSmartGroupsConfigured" : "Configured_Host_Smart_Groups",
        "sfw.csiProductSmartGroupsConfigured" : "Configured_Product_Smart_Groups",
        "sfw.csiAdvisorySmartGroupsConfigured" : "View_Edit_Smart_Group_Configuration",
        "sfw.csiWSUSConfiguration" : "WSUS_System_Center_1",
        "sfw.csiHostSmartGroups" : "Overview_and_Configuration",
        "sfw.csiPsiManagement" : "Host_Configuration",
        "sfw.csiPsiInsecure" : "Approve_Updates__for_Connected_Personal_Software_Inspector_Installations_",
        "sfw.CSIPSILinkID" : "Configure_Link_ID",
        "sfw.csiPsiForWindowsFolder" : "Personal_Software_Inspector_for_Windows",
        "sfw.csiScanPaths" : "Scan_Paths",
        "sfw.csiSccmSchedules" : "System_Center_Import_Schedules__Requires_the_Software_Vulnerability_Manager",
        "sfw.csiProductSmartGroups" : "Overview_and_Configuration_1",
        "sfw.csiAdvisorySmartGroups" : "Overview_and_Configuration_2",
        "sfw.csiActiveDirectory" : "Active_Directory",
        "sfw.csiIpManagement" : "IP_Access_Management__Requires_the_Software_Vulnerability_Manager_Plug_in_",
        "sfw.csiCreatePatch" : "Flexera_Package_System__SPS_",
        "sfw.csiWsusSccm" : "WSUS_System_Center",
        "sfw.csiPatchSystemsConfigurations" : "Patch_Configuration",
        "sfw.Deployment0" : "Third_Party_Integration", // TODO: this needs to be dynamic as there can be more integration pages
        "sfw.csiZeroDay" : "Overview_and_Configuration_2",//"Zero_Day_Advisories_Optional_Mo"
        "sfw.csiCreatePatchTemplate" : "Patch_Template",
        "sfw.csiExternalPackageSigning" : "External_Package_Signing",
        "sfw.csiVendorPatchView" : "Vendor_Patch_Module",
        "sfw.csiCreatePackageDeployment" : "Patching",
        "sfw.csiNotification" : "Activity_Log_Notifications",
        "sfw.csiDownloadPatchPublisher": "Download_and_Install_the_Software_Vulnerability_Manager_Patch_Publisher",

        // missing from TOC
        // These bindings are used for the "(?)" help anchors in the Agent Download pages. See the comments for enhancement #4805
        "csinetworkagent_windows" : "Download_Network_Agent",
        "csiagent_windows" : "Download_Local_Agent",
        "csiagent_redhat" : "Installing_the_Software_Vulnerability_Manager_Agent_for_Red_Hat_Linux",
        "csiagent_macosx" : "Install_the_Mac_Agent",

        "csisettings_scanthread": "Scan_Threads",
        "csisettings_liveupdate": "Live_Updates",
        "csisettings_networkinfo": "Collect_Network_Information",
        "csisettings_zombie": "Zombie_File_Settings",
        "csisettings_msupdate": "Check_for_Missing_Microsoft_Security_Update_Settings",
        "csisettings_timestamp": "Flexera_Software_Package_System__SPS__Timestamp",
        "csisettings_wua":"Windows_Update_Settings",
        "csisettings_identifiable_paths":"Flexera_software_mask_user_paths",
        "csisettings_status_polling_api":"Configure_Agent_s_status_polling",
        "csisettings_java_configure":"Java_Assessment_Settings",
        "csisettings_site_save_configure":"Configure_Site_name_for_agents_deployed_with_custom_name",
        "csisettings_SS0_setting":"Single_Sign_On__SSO__Settings",
        "csisettings_SSO_configure":"Configure_Single_Sign_On__SSO_",
        "csisettings_hosts_delete_configure":"Host_Deletion_Settings",
        "sfw.csiInventory":"Inventory_Assessment",
        "sfw.csiDeletePatchConnections":"Patch_Publisher_Connections",
        "sfw.csiExclusionList":"Scan_Exclusion_Paths",
        "sfw.csiExtendedSupport":"Extended_Support"

    },
    /**
     * @method
     * @private
     * Returns the Topic ID assigned to the specified Page ID
     * @param {String} pageId
     * @return {String}
     *	returns undefined if the Page ID isn't found
     */
    getTopicIdByPageId: function ( pageId ) {
        if ( "undefined" !== typeof this.topicBindings[ pageId ] ) {
            return this.topicBindings[ pageId ];
        }
        // Custom Smartgroups have dynamic pageIds so check them here
        if ( "sfw.csiSmartGroup_host" === pageId.substring(0, 22) ) {
            return this.topicBindings[ "sfw.csiHostSmartGroupsConfigured" ];
        }
        if ( "sfw.csiSmartGroup_product" === pageId.substring(0, 25) ) {
            return this.topicBindings[ "sfw.csiProductSmartGroupsConfigured" ];
        }
        if ( "sfw.csiSmartGroup_advisory" === pageId.substring(0, 26) ) {
            return this.topicBindings[ "sfw.csiAdvisorySmartGroupsConfigured" ];
        }
    },

    /**
     * @method
     * @public
     * Opens the Help File to the specified Topic ID. If no Topic ID was
     * specified then it checks which menu item is currently active and
     * shows the Help Topic related to it. This is the same as showing
     * the Help Topic related to the current page except it takes in to
     * account menu folders.
     * @param {String} topicId
     *	Topic IDs are defined in the .chm file and we look them up to build our list
     */
    openHelp : function ( topicId ) {
        try {
            if ( "undefined" === typeof topicId ) {
                // If no Topic ID supplied then open to the default help page
                // which will be Help related to where the user currently is
                var topicId = this.getTopicIdByPageId( sfw.ui.activeMenuItem );
            }

            var url = '';
            if (topicId != '' && "undefined" !== typeof topicId) {
                var url = "#helplibrary/" + topicId + ".htm";
            }

            if ( sfw.isConsole ) {
                if(sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION){
                    sfw.Default.externalURL(sfw.CommonConstants.SERVER_EDITION_HELP_URL + url);
                } else {
                    sfw.Default.externalURL(sfw.CommonConstants.HOSTED_EDITION_HELP_URL + url);
                }
            } else {
                if(sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION){
                    sfw.Default.externalURL(sfw.CommonConstants.SERVER_EDITION_HELP_URL + url);
                } else {
                    window.open(sfw.CommonConstants.HOSTED_EDITION_HELP_URL + url, '_blank');
                }
                window.focus();
            }
        } catch ( ex ) {
            //LogMessage( 'Failed to open help file topic: ' + ( typeof topicId === 'string' ? topicId : 'TOC' ) );
        }
    },
    /**
     * @method
     * @public
     * Opens the Help File to the specified Page ID. If an entry for the
     * Page ID isn't found then the help file is not opened.
     * @param {String} pageId
     *	pageId can be an internal CSI page id or a custom id which has
     *	been set in the (@link sfw.help#topicBindings}.
     */
    openPageId : function ( pageId ) {
        var topicId = this.getTopicIdByPageId( pageId );
        if ( "undefined" !== typeof topicId ) {
            this.openHelp( topicId );
        }
    },
    /**
     * @method
     * @public
     * Binds the F1 key to the Help system so Help is opened when a user
     * presses F1 on their keyboard. This function needs to only be called
     * once during UI init.
     */
    bindHelpKey : function () {
        document.onhelp = new Function("return false;");
        window.onhelp = new Function("return false;");
        new Ext.util.KeyMap({
            target: window,
            binding: {
                key: Ext.event.Event.F1,
                stopEvent: true,
                handler: function (keyCode, e) {
                    e.stopPropagation();
                    sfw.help.openHelp();
                }
            }
        });
    }

});
