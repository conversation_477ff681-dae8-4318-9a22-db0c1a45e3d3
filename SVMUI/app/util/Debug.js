Ext.define('sfw.util.Debug', {
    singleton: true,

    logLevel: 3,
    logNotify: true,
    showMessage: false,

    /**
     * Called uppon an error. Depending on error log level selected, will display a Ext.Msg window or alert() containing error details.
     *
     * @param exception
     *	Object contains the exception values
     * @param name
     *	String name of the function it occured in
     * @param data
     *	String Optional additional data to be logged
     */
    trigger: function (exception, name, data) {
        if ( typeof this.logLevel === "undefined" ) {
            return;
        }

        var message = "Error: " + exception.name + ( name ? '\nFunction: ' + name : '');

        if ( !data ) {
            var data = "";
        } else {
            data = '\nData: ' + data;
        }

        switch ( this.logLevel ) {
            case 3:
                if ( ! Ext.isIE ) {
                    /* Mozilla/Gecko non-standard extension */
                    message += '\nFile: ' + exception.fileName + '\nLine: ' + exception.lineNumber;
                } else {
                    /* Internet Explorer non-standard extension */
                    message += '\nNumber: ' + exception.number;
                }
            case 2:
                message += '\nDescription: ' + exception.message;
            case 1:
                message += data;
                break;
            case 0:
                return;
        }

        if ( this.logNotify === true ) {
            this.notify( message );
        }

        this.log( message );
    },

    log: function( message ) {

        try {
            if ( sfw.isDesktop && sfw.external.fWUIGetLogging().level < 1 ) {
                return;
            }
            /*
            Remove any user ids from the log message. We are searching for 64 character
            alpha-numeric strings which are	either wrapped in quotes or preceeded by "uid=".

            Regex breakdown:
                1) The match must start with uid= or a quote
                2) The next 64 characters must be alpha-numeric.
                3) They are either followed with by a quote, ampersand or by no other character.
            */
            message = message.replace(/(uid=|\"|')([0-9a-z]{64})([&#\"']|.{0}$)/ig, "$1(HASH NOT SHOWN)$3");

            if ( sfw.isDesktop ) {
                sfw.external.fWUILogMessage( message );
            }
        } catch (ex) {
            // Silently discard message
        }

        //sfw.csiLogger.errorId++;
        try {
            sfw.csiLogger.loggerData.unshift({
                date: sfw.util.Default.returnDateFormat( new Date(), 'default', false ),
                description: sfw.util.Default.htmlSpecialChars( message )
            });

            // Keeps the data to max of 500 elements
            if ( sfw.csiLogger.loggerData.length > 500 ) {
                sfw.csiLogger.loggerData.shift();
            }

            var activePage = '';
            if(window.location.hash) {
                activePage = window.location.hash.substring(1);
            }

            const sfwCsiLogger = Ext.getCmp('sfwCsiLoggerId');
            if ( activePage === 'sfw.csiLogger' && sfwCsiLogger ) {
                const logMessagesStore = sfwCsiLogger.getStore();
                Ext.defer(function() {
                    logMessagesStore.setData(sfw.csiLogger.loggerData);
                }, 500) //after 500ms
            }
        } catch ( ex ) {
            // Silently discard message
        }
    },

    notify: function( message, position ) {
        if ( this.showMessage === false ) {
            return;
        }

        try {
            Ext.Msg.show({
                title: 'Error',
                msg: sfw.util.Util.replaceAll( "\n", "<br>", message ),
                button: Ext.Msg.OK,
                icon: Ext.MessageBox.ERROR
            });
        } catch ( ex ) {
            alert( message );
        }
    }

});