Ext.define('sfw.util.BeforeRequest', {
    requires: [
        'Ext.data.Connection',
        'Ext.util.Observable'
    ],
    singleton: true,
    maskOptions: {
        enable: false,
        msg: 'Please wait...',
        maskEl: Ext.getBody()
    },
    isURL: function (str) {
        var regexp = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
        return regexp.test(str);
    },
    constructor: function (config) {

        const me = this;
        Ext.util.Observable.observe(Ext.data.Connection, {
            scope: me,
            beforerequest: function (conn, options, eOpts) {
                const opts = Ext.apply({}, options.maskConfig || {}, this.maskOptions);
                //FIXME - remove this once we got dashboard widget integrated with backend php code
                if (!Ext.isEmpty(options.url) && Ext.String.startsWith(options.url, 'resources')) {
                    options.url = options.url;
                } else if (me.isURL(options.url)) {
                    options.url = options.url;
                } else {
                    options.url = me.getBaseUrl() + me.getApiPrefix() + options.url;
                }
                options.timeout = 1000 * 60 * 10;
                if (opts.enable) {
                    Ext.get(opts.maskEl).setMasked({
                        xtype: 'loadmask',
                        message: opts.msg
                    });
                }
                //After we login , for all requests we will add token #CSIL-11457

                if (me.checkUserLogin() == 1) {
                    Ext.Ajax.setDefaultHeaders({
                        'X-CSRF-TOKEN': sfw.util.Auth.LoginDetails.vid
                    });
                }

            },
            requestcomplete: me.onRequestComplete,
            requestexception: me.onException,
            timeout: 180000 //milliseconds (3 minutes).
        }, me);
        me.initConfig(config);
    },
    onException: function (connection, response, options, eOpts) {
        //TODO
    },
    onRequestComplete: function (connection, response, options, eOpts) {

        //TODO
    },
    getBaseUrl: function () {
        Ext.manifest.apiUrl = Ext.getWin().dom.location.protocol + '//' + Ext.getWin().dom.location.hostname + '/';
        return Ext.manifest.apiUrl;
    },
    getApiPrefix: function () {
         return 'csi/api/?';
    },
    checkUserLogin: function () {
        return Ext.util.Cookies.get('check');
    }
});