Ext.define('sfw.util.BufferedHttpProvider', {
    extend: 'Ext.state.Provider',
    alias: 'state.bufferedhttp',
    /**
     * @var {Number}
     * The number of seconds to wait for another state value to be set before
     * sending the buffered values to the server. The greater this number, the
     * less requests will be sent to the server however if any other state values
     * are set during the deferring then the timer starts over again and will
     * continue this until {@link #maxMillisecondsToDefer} is reached.
     */
    millisecondsToDefer: 1500,
    /**
     * @var {Number}
     * The number of seconds to wait for another state value to be set before.
     * sending the buffered values to the server. The greater this number is
     * the less requests will be sent.
     */
    maxMillisecondsToDefer: 10000,
    /**
     * @var {Number}
     * Timer Id used to defer sendToServer() calls. You don't need to modify this.
     */
    timerId: null,
    /**
     * @var {Number}
     * The time the buffering started which coincides with when the deferring
     * started. This is used to determine if the send request has been deferred
     * too long and should be sent. You don't need to modify this manually.
     */
    bufferStartTime: 0,
    /**
     * @var {Object}
     * Contains the key/value pair buffer being sent to the server so they can be send in batches.
     */
    remoteSaveBuffer: {},

    constructor: function () {
        var me = this;

        me.callParent(arguments);
    },

    /**
     * @method
     * Checks if the specified value exists
     * @public
     * @param {string} name
     * @return {Boolean}
     *    True if the value exists otherwise false.
     */
    has: function (name) {
        name = name.replace(/[\. ]/g, '_'); // replace spaces and periods with underscores for server-side compatibility
        return ("undefined" !== typeof this.state[name]);
    },

    /**
     * @method
     * Gets the specified value if it exists else defaultValue is returned.
     * @public
     * @todo TODO Consider compressing long column names
     * @param {string} name
     * @param mixed defaultValue
     *    defaultValue can be a String or Number
     * @return mixed
     *    Returns a String or Number
     */
    get: function (name, defaultValue) {
        var me = this;

        name = name.replace(/[\. ]/g, '_'); // replace spaces and periods with underscores for server-side compatibility
        if (me.has(name)) {
            return me.callParent(arguments);
        }
        return defaultValue;
    },

    /**
     * @method
     * Sets the specified value additionally sending it to the server. The send
     * request is deferred by {@link #millisecondsToDefer} milliseconds and if
     * another value is set with this time, the send request will be deferred further.
     * @public
     * @param {string} name
     * @param mixed value
     *    value can be a String or Number
     */
    set: function (name, value) {
        var me = this;

        if (undefined === value) {
            return;
        }
        name = name.replace(/[\. ]/g, '_'); // replace spaces and periods with underscores for server-side compatibility
        me.callParent(arguments);
        // Add the value to the buffer to be saved on the server
        me.remoteSaveBuffer[name] = me.encodeValue(value);

        // Don't defer further if we've deferred for too long
        if (me.timerId && new Date().getTime() > (me.bufferStartTime + me.maxMillisecondsToDefer)) {
            return;
        }

        // Set the buffering start time
        if (0 === me.bufferStartTime) {
            me.bufferStartTime = new Date().getTime();
        }

        // Send to the server in {@link #millisecondsToDefer} millseconds, deferring it if another value is set within that time.
        if (null !== me.timerId) {
            clearTimeout(me.timerId);
            me.timerId = null;
        }
        me.timerId = Ext.defer(me.sendToServer, me.millisecondsToDefer, me);
    },

    /**
     * @method
     * Clears a value
     * @public
     * @param {string} name
     * @param mixed value
     *    value can be a String or Number
     */
    clear: function (name) {
        var me = this;

        if (undefined === name) {
            return;
        }
        me.callParent(arguments);

        Ext.Ajax.request({
            url: "action=state_provider&which=clear",
            method: "POST",
            params: {clear: name}
        });
    },

    /**
     * @method
     * Sets the state of the provider. This is used to prime the provider with
     * the state data that was recorded on the server.
     * @public
     * @param {Object} state
     *    An array of key: value pairs
     */
    setState: function (state) {
        this.state = state;
    },

    /**
     * @method
     * Sends the contents of the BufferedHttpProvider to the server
     * @public
     */
    sendToServer: function () {
        var me = this;
        // Clear timer so next writes can be buffered
        clearTimeout(me.timerId);
        me.timerId = null;

        // Reset the buffer start time so deferring starts again
        me.bufferStartTime = 0;

        Ext.Ajax.request({
            url: "action=state_provider&which=set",
            method: "POST",
            params: (function () {
                var currentBuffer = me.remoteSaveBuffer;
                me.remoteSaveBuffer = {};
                return currentBuffer;
            })(),
            success: function (response) {
                var response = Ext.decode(response.responseText);
            },
            /**
             * @function
             * Handles the request failure
             * @private
             * @param {XMLHttpRequest} response
             * @param {Object} options
             */
            failure: function (response, options) {
                // Put the key/value pairs back on the buffer since they didn't save
                me.remoteSaveBuffer = Ext.applyIf(me.remoteSaveBuffer, options.params);
            }
        });
    }
});
