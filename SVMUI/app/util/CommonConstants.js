Ext.define('sfw.util.CommonConstants', {

    singleton: true,
    alternateClassName: 'sfw.CommonConstants',
    requires: ['sfw.Default'],

    // Pattern and replace for changing direct_download link on the fly
    spsDLSitePattern: "http://dl.secunia.com/SPS/",
    spsDLSiteReplace: "https://cst.dl.secunia.com/SPS/?action=sps_package_download&uid=",
    // Pattern and replace for changing direct_download link on the fly
    patchDownloadURLPackage: 'https://dl.csi7.secunia.com/?action=vpm_download_svm&src=vpm',
    // Link to pdf file containing help
    PDF_HELP_URL: "http://secunia.com/?action=fetch&filename=Secunia_CSI7_Technical_User_Guide.pdf",
    SERVER_EDITION_HELP_URL: "https://docs.flexera.com/csionprem/Default.htm",
    HOSTED_EDITION_HELP_URL: "https://docs.flexera.com/csi/Default.htm",
    SERVER_EDITION_RELEASE_URL: "https://docs.flexera.com/#csionprem00",
    HOSTED_EDITION_RELEASE_URL: "https://docs.flexera.com/#csicloud00",

    SMARTGROUP_TYPE_HOST: 1,

    SMARTGROUP_TYPE_PRODUCT: 2,

    SMARTGROUP_TYPE_ADVISORY: 3,

    ACTIVITYTYPECONSTANTS: sfw.Default.getActivityTypeConstants(),

    DASHBOARD_PROFILE: "CSI_DASH",
    EMAIL_RECIPIENT: "CSI_RCPT",
    USER_SELECTION: "CSI_USR",

    ACTIVITYLOGCONSTANT: [
        {event_type: "0", event: " Not Selected"},
        {event_type: "1", event: "User Login"},
        {event_type: "2", event: "User Logut"},
        {event_type: "10", event: "Password reset from Password Recovery"},
        {event_type: "11", event: "Sent Authentication Pins to recover password"},
        {event_type: "1020", event: "Add Allow List Scan Path"},
        {event_type: "1021", event: "Edit Allow List Scan Path"},
        {event_type: "1022", event: "Delete Allow List Scan Path"},
        {event_type: "1030", event: "Add Block List Scan Path"},
        {event_type: "1031", event: "Edit Block List Scan Path"},
        {event_type: "1032", event: "Delete Block List Scan Path"},
        {event_type: "1033", event: "Add Logged List Scan Path"},
        {event_type: "1034", event: "Edit Logged List Scan Path"},
        {event_type: "1035", event: "Delete Logged List Scan Path"},
        {event_type: "1050", event: "Create Scan Group"},
        {event_type: "1051", event: "Update Scan Group"},
        {event_type: "1052", event: "Delete Scan Group"},
        {event_type: "1060", event: "Create Customer Detection Rule"},
        {event_type: "1061", event: "Update Customer Detection Rule"},
        {event_type: "1062", event: "Delete Customer Detection Rule"},
        {event_type: "1070", event: "Edit Site Agents Configuration"},
        {event_type: "1071", event: "Edit Host Agent Configuration"},
        {event_type: "1072", event: "Removed Agent"},
        {event_type: "1073", event: "Agent Details Email"},
        {event_type: "1080", event: "Create Scheduled Export"},
        {event_type: "1081", event: "Update Scheduled Export"},
        {event_type: "1082", event: "Delete Scheduled Export(s)"},
        {event_type: "1083", event: "Run Scheduled Export"},
        {event_type: "1084", event: "Settings Update"},
        {event_type: "1085", event: "Settings Update"},
        {event_type: "1086", event: "Settings Update"},
        {event_type: "1087", event: "Settings Update"},
        {event_type: "1088", event: "Settings Update"},
        {event_type: "1100", event: "Settings Update"},
        {event_type: "1110", event: "Settings Update"},
        {event_type: "1111", event: "Collect Network Information"},
        {event_type: "1112", event: "Live Update"},
        {event_type: "1113", event: "Status Polling Settings"},
        {event_type: "1114", event: "Java Assessment Settings"},
        {event_type: "1115", event: "Agent Check-in Site Settings"},
        {event_type: "1116", event: "Host Deletion Settings"},
        {event_type: "1120", event: "Windows Update Settings"},
        {event_type: "1121", event: "Windows Update Settings"},
        {event_type: "1122", event: "Windows Update Settings"},
        {event_type: "1123", event: "Windows Update Settings"},
        {event_type: "1124", event: "Windows Update Settings"},
        {event_type: "1125", event: "Windows Update Settings"},
        {event_type: "1130", event: "Windows Update Proxy Settings"},
        {event_type: "1131", event: "Windows Update Proxy Settings"},
        {event_type: "1132", event: "Windows Update Proxy Settings"},
        {event_type: "1133", event: "Windows Update Proxy Settings"},
        {event_type: "2010", event: "Rename Site"},
        {event_type: "2020", event: "Delete Host"},
        {event_type: "4010", event: "Create Report"},
        {event_type: "4011", event: "Edit Report"},
        {event_type: "4020", event: "Enable Change Summary"},
        {event_type: "4021", event: "Edit Change Summary"},
        {event_type: "4050", event: "Report Sent"},
        {event_type: "5010", event: "Package Creation"},
        {event_type: "5011", event: "WSUS Package Edit"},
        {event_type: "5012", event: "WSUS Package Delete"},
        {event_type: "5020", event: "WSUS Package Approval"},
        {event_type: "5021", event: "WSUS Package Declined"},
        {event_type: "5022", event: "WSUS Package Waiting Signature"},
        {event_type: "5023", event: "WSUS Package Signed"},
        {event_type: "5030", event: "WSUS Certificate Installation"},
        {event_type: "5040", event: "WSUS Connection"},
        {event_type: "5041", event: "WSUS Upstream Server Configuration"},
        {event_type: "5042", event: "WSUS Downstream Server Configuration"},
        {event_type: "5043", event: "WSUS Updated State Changes"},
        {event_type: "5050", event: "Create Patch Template"},
        {event_type: "5051", event: "Delete Patch Template"},
        {event_type: "5052", event: "Update Patch Template"},
        {event_type: "5053", event: "SPS Patch Subscription"},
        {event_type: "5054", event: "VPM Patch Subscription"},
        {event_type: "5055", event: "SVM Patch Daemon Checkin"},
        {event_type: "5056", event: "Patch Deployment"},
        {event_type: "5057", event: "VPM QuickPatch"},
        {event_type: "5058", event: "SPS QuickPatch"},
        {event_type: "8010", event: "User Account Edit"},
        {event_type: "8011", event: "User Account Creation"},
        {event_type: "8012", event: "User Password Reset"},
        {event_type: "8013", event: "SSO User Account Creation"},
        {event_type: "8020", event: "Contact Verification"},
        {event_type: "8021", event: "Sent New Contact Verification Codes"},
        {event_type: "8030", event: "IP Access rule added"},
        {event_type: "8031", event: "IP Access rule edited"},
        {event_type: "8032", event: "IP Access rule deleted"},
        {event_type: "8033", event: "IP Access rule disabled"},
        {event_type: "8034", event: "IP Access rule enabled"},
        {event_type: "9010", event: "Password Policy changed"},
        {event_type: "9011", event: "Updated LDAPS Configuration"},
        {event_type: "9012", event: "Log Notification"},
        {event_type: "9013", event: "SSO Configuration"},
        {event_type: "9014", event: "Add Software Suggestion"},
        {event_type: "9015", event: "Delete Software Suggestion"},
        {event_type: "9016", event: "Add Inventory"},
        {event_type: "9017", event: "Process Inventory"},
        {event_type: "9018", event: "Delete Patch Publisher Connection"},
        {event_type: "9018", event: "Delete Patch Publisher"},
        {event_type: "9020", event: "Settings Update"},
        {event_type: "9021", event: "Settings Update"},
        {event_type: "9022", event: "Exclusion Path List"},
        {event_type: "9023", event: "System Score Settings"},
        {event_type: "9024", event: "Add Extended Support"},
        {event_type: "9025", event: "Edit Extended Support"},
        {event_type: "9026", event: "Delete Extended Support"}
    ],

    criteriaStoreFields: [
        {name: 'id', type: 'int'},
        {name: 'description', type: 'string'},
        {name: 'action_type', type: 'int'},
        {name: 'value_type', type: 'int'},
        {name: 'recordCls', type: 'string'},
        {name: 'rowDescription', type: 'string'}
    ],

    actionStoreFields: [
        {name: 'id', type: 'int'},
        {name: 'type', type: 'int'},
        {name: 'description', type: 'string'}
    ],

    valueStoreFields: [
        {name: 'id', type: 'int'},
        {name: 'value', type: 'int'},
        {name: 'type', type: 'int'},
        {name: 'description', type: 'string'}
    ],

    PRODUCT: ['product', 'Product', 'products', 'Products'],
    HOST: ['host', 'Host', 'hosts', 'Hosts'],
    ADVISORY: ['advisory', 'Advisory', 'advisories', 'Advisories'],

    // Actual stores/values will be loaded later. Type must match exactly the
    // lower-case-singular above.
    SMARTGROUP_TYPES: {
        product: ''
        , host: ''
        , advisory: ''
    },

    SMARTGROUP_STORES: {
        product: ''
        , host: ''
        , advisory: ''
    },

    /*
    * -------
    * Configure the potential columns we can show in any of the Smart
    * Groups wizard to configure the custom-columns checkboxes
    *
    *
    * Note: "Hidden" in this context refers only to hiding it from
    * user selection when configuring a smart group, i.e., if we call
    * it hidden, we always get it, and don't select it.
    *
    * Similarily, 'alwaysGet' means that like 'hidden', we always get
    * the field, but we also allow it to be selected as a custom column.
    * This is in case renderers of other fields depend on something. Then
    * we just always get it, but don't show it in the contents grid if it
    * wasn't explicitly selected.
    *
    * Note - sortable for each gets set to true during grid construction
    * unless explicitely set otherwise here.
    */
    // TODO - note - renderers don't work yet...
    potentialColumns: {
        productName: {
            displayName: 'Product Name',
            id: 'product_name',
            hidden: true,
            type: 'string',
            widgetConfig: {
                width: 400
            }
        },
        vendorName: {
            displayName: 'Vendor',
            id: 'vendor_name',
            hidden: true,
            type: 'string'
        },

        /*
        ,operatingsystemName: {
            displayName: 'Operating System Name'
            ,id: 'product_name'
            ,hidden: true
            ,type: 'string'
        }
        */

        patchVersion: {
            displayName: 'Patch Version',
            id: 'secure_version',
            tooltip: 'Current Patched / Supported Version of Product',
            type: 'string'
        },

        SAID: {
            displayName: 'SAID',
            id: 'vuln_id',
            tooltip: 'Secunia Advisory ID for Associated Vulnerability',
            type: 'int',
            renderer: function (val, cell, record) {
                return sfw.util.Default.renderSaid(val, record.data.vuln_title);
            }
        },

        criticality: {
            displayName: 'Criticality',
            id: 'vuln_criticality',
            tooltip: 'Secunia Advisory Criticality Rating',
            type: 'int',
            // align: 'right',
            width: 80,
            //new code
            widgetConfig: {
                xtype: 'widgetcolumn',
                align: 'center',
                widget: {
                    xtype: 'sectorprogress',
                    height: 8
                }
            }

            //renderer: sfw.util.Default.advisoryCriticalityImage
        },

        vuln_threat_score: {
            displayName: 'Threat Score',
            id: 'vuln_threat_score',
            tooltip: 'Threat Score',
            type: 'int',
            align: 'right',
            width: 80,
            renderer: sfw.util.Default.threatScoreDefault
        },

        numInsecure: {
            displayName: 'Insecure',
            id: 'num_insecure',
            tooltip: 'Total Number of Insecure Installations',
            align: 'right',
            width: 110,
            renderer: sfw.util.Default.renderNullToDash
        },

        numEol: {
            displayName: 'End-Of-Life',
            id: 'num_eol',
            tooltip: 'Total Number of End-Of-Life Installations',
            align: 'right',
            width: 110,
            renderer: sfw.util.Default.renderNullToDash
        },

        numPatched: {
            displayName: 'Secure',
            id: 'num_patched',
            tooltip: 'Total Number of Secure Installations',
            align: 'right',
            width: 110,
            renderer: sfw.util.Default.renderNullToDash
        },

        numTotal: {
            displayName: 'Total',
            id: 'num_installations',
            tooltip: 'Total Number of Installations',
            align: 'right',
            width: 150,
            renderer: sfw.util.Default.renderNullToDash
        },

        numAffectedHosts: {
            displayName: 'Affected Hosts',
            id: 'num_hosts',
            tooltip: 'Number of Hosts with Product Installed',
            type: 'int',
            align: 'right',
            width: 150
        },

        downloadLink: {
            displayName: 'Download',
            id: 'direct_download',
            tooltip: 'Direct Download Link',
            type: 'string',
            renderer: sfw.util.Default.renderLink
        },

        softType: {
            displayName: 'Product Type',
            id: 'soft_type',
            tooltip: 'Program or Operating System',
            type: 'int',
            width: 100,
            renderer: sfw.util.Default.renderOsOrProgs
        },

        hostName: {
            displayName: 'Host',
            id: 'host_name',
            hidden: true,
            type: 'string'
        },

        systemScore: {
            displayName: 'System Score',
            id: 'score',
            tooltip: 'Flexera System Score',
            type: 'int',
            align: 'right',
            width: 110,
            renderer: sfw.util.Default.renderSSS
        },

        siteName: {
            displayName: 'Site Name',
            id: 'group_name',
            tooltip: 'Site Name',
            type: 'string'
        },

        lastScan: {
            displayName: 'Last Scan',
            id: 'updated',
            width: 125,
            tooltip: 'Last Scan Time of Host',
            renderer: sfw.util.Default.gridRenderUTCDateInLocaltime
        },

        platform: {
            displayName: 'Software Platform',
            id: 'software_inspector_id',
            tooltip: 'Software Platform',
            alwaysGet: true,
            width: 110,
            renderer: function (val) {
                return sfw.util.Default.platformRenderer(val);
            }
        },

        scanEngine: {
            displayName: 'Scan Engine',
            id: 'software_inspector_version',
            tooltip: 'Software Platform / Scan Engine of Host',
            width: 120,
            renderer: function (val, cell, record) {
                return sfw.util.Default.scanEngineRenderer(val, record.data.software_inspector_id);
            }
        },

        // Advisory specific:
        advisoryTitle: {
            displayName: 'Advisory Description',
            id: 'vuln_title',
            hidden: true,
            type: 'string'
        },

        // Note - this only gets included in the type arrays below if they have the module...
        zeroDay: {
            displayName: 'Zero-Day',
            id: 'vuln_zero_day',
            tooltip: 'Flag if the Advisory is considered \"Zero-Day\"',
            type: 'int',
            align: 'center',
            width: 60,
            renderer: sfw.util.SharedFunctions.renderZeroDay
        },

        advisoryPublished: {
            displayName: 'Advisory Published',
            id: 'vuln_create_date',
            tooltip: 'Publication Date of Advisory',
            type: 'string',
            width: 110,
            renderer: sfw.util.Default.renderSaidDate
        },

        solutionStatus: {
            displayName: 'Solution Status',
            id: 'vuln_solution_status',
            tooltip: 'Solution (Patch) Status of Advisory',
            type: 'int',
            width: 110,
            renderer: sfw.util.SharedFunctions.solutionStatus
        },

        cvssBaseScoreAll: {
            displayName: 'CVSS Base Score',
            id: 'vuln_cvss_score_all',
            tooltip: 'National Vulnerability Database CVSS (Common Vulnerability Scoring System) Base Score of Advisory',
            type: 'float',
            align: 'right',
            width: 120,
            renderer: function (val, cell, record) {
                var cvss3 = '';
                cvss3 = typeof record.data.vuln_cvss3_score == 'undefined' ? '' : record.data.vuln_cvss3_score;
                var cvss4 = '';
                cvss4 = typeof record.data.vuln_cvss4_score == 'undefined' ? '' : record.data.vuln_cvss4_score;
                return sfw.util.Default.cvssSgRenderer(record.data.vuln_cvss_score, cvss3, cvss4);
            }
        },

        cvssBaseScore: {
            displayName: 'CVSS2 Base Score',
            id: 'vuln_cvss_score',
            tooltip: 'National Vulnerability Database CVSS (Common Vulnerability Scoring System) Base Score of Advisory',
            type: 'float',
            align: 'right',
            width: 120,
            hidden: true,
            widgetConfig: {
                hidden: true
            }
        },

        cvss3BaseScore: {
            displayName: 'CVSS3 Base Score',
            id: 'vuln_cvss3_score',
            tooltip: 'National Vulnerability Database CVSS3 (Common Vulnerability Scoring System) Base Score of Advisory',
            type: 'float',
            align: 'right',
            width: 120,
            hidden: true,
            widgetConfig: {
                hidden: true
            }
        },
        cvss4BaseScore: {
            displayName: 'CVSS4 Base Score',
            id: 'vuln_cvss4_score',
            tooltip: 'National Vulnerability Database CVSS4 (Common Vulnerability Scoring System) Base Score of Advisory',
            type: 'float',
            align: 'right',
            width: 120,
            hidden: true,
            widgetConfig: {
                hidden: true
            }
        },
        // TODO - we might later want to add the Secunia derived CVSS
        // score - this just means implementing the calulator for it based
        // on the attack vectore in the Agent class where we store the
        // score in nsi_devices, and adding the field everywhere else as
        // appropriate.

        whereType: {
            displayName: 'Attack Vector',
            id: 'vuln_where_type',
            tooltip: 'Attack Vector of Advisory (a.k.a. The \"From\" or \"where\" Type)',
            type: 'string',
            width: 120,
            renderer: sfw.util.Default.whereRenderer
        },

        impactType: {
            displayName: 'Impact',
            id: 'vuln_impact_type',
            tooltip: 'The Impact (Consequences) associated with an Advisory',
            type: 'string',
            width: 160,
            renderer: function( val ){
                 return sfw.util.Default.impactRenderer( val, true );
            }
        },

        numVulnerabilities: {
            displayName: 'Vulnerabilities',
            id: 'vulnerabilities',
            tooltip: 'Number of individual Vulnerabilities contained in Advisory',
            type: 'int',
            align: 'right',
            width: 90
        },

        numProducts: {
            displayName: 'Products',
            id: 'products',
            tooltip: 'Number of Products Affected by Advisory',
            type: 'int',
            align: 'right',
            width: 70,
            renderer: function (val, cell, record) {
                var vulnId = record.data.vuln_id;
                if (val > 0) {
                    return '<a href="#" onclick= "sfw.Default.renderProductPopup(' + vulnId + ')">'+ val + '</a>';
                } else {
                    return val;
                }
            }
        },

        numHosts: {
            displayName: 'Hosts',
            id: 'hosts',
            tooltip: 'Number of Hosts Affected by Advisory',
            type: 'int',
            align: 'right',
            width: 60,
            renderer: function (val, cell, record) {
                var vulnId = record.data.vuln_id;
                if (val > 0) {
                    return '<a href="#" onclick= "sfw.Default.renderHostPopup(' + vulnId + ')">'+ val + '</a>';
                } else {
                    return val;
                }
            }
        },

        numInstallations: {
            displayName: 'Installations',
            id: 'installations',
            tooltip: 'Total Number of Installations Affected by Advisory',
            type: 'int',
            align: 'right',
            width: 90,
            renderer: function (val, cell, record) {
                var vulnId = record.data.vuln_id;
                if (val > 0) {
                    return '<a href="#" onclick= "sfw.Default.renderInstallationPopup(' + vulnId + ', \'all\'' +')">'+ val + '</a>';
                } else {
                    return val;
                }
            }
        },

    },

    init: function () {
        var me = this;

        // this.loadSmartGroupConstants();

        me.hostPotentialColumns = [
            me.potentialColumns.hostName,
            me.potentialColumns.systemScore,
            me.potentialColumns.lastScan,
            me.potentialColumns.numInsecure,
            me.potentialColumns.numEol,
            me.potentialColumns.numPatched,
            me.potentialColumns.numTotal,
            me.potentialColumns.siteName,
            me.potentialColumns.scanEngine,
            me.potentialColumns.platform
        ];

        me.productPotentialColumns = [
            me.potentialColumns.productName,
            me.potentialColumns.patchVersion,
            me.potentialColumns.SAID,
            me.potentialColumns.advisoryTitle,
            me.potentialColumns.criticality,
            me.potentialColumns.cvssBaseScoreAll,
            me.potentialColumns.cvssBaseScore,
            me.potentialColumns.cvss3BaseScore,
            me.potentialColumns.cvss4BaseScore,
            me.potentialColumns.vendorName,
            me.potentialColumns.numInsecure,
            me.potentialColumns.numEol,
            me.potentialColumns.numPatched,
            me.potentialColumns.numTotal,
            me.potentialColumns.numAffectedHosts,
            me.potentialColumns.downloadLink,
            me.potentialColumns.softType
        ];

        me.advisoryPotentialColumns = [
            me.potentialColumns.SAID,
            me.potentialColumns.advisoryTitle,
            me.potentialColumns.criticality,
            me.potentialColumns.zeroDay,
            me.potentialColumns.advisoryPublished,
            me.potentialColumns.numVulnerabilities,
            me.potentialColumns.solutionStatus,
            me.potentialColumns.cvssBaseScoreAll,
            me.potentialColumns.cvssBaseScore,
            me.potentialColumns.cvss3BaseScore,
            me.potentialColumns.cvss4BaseScore,
            me.potentialColumns.whereType,
            me.potentialColumns.impactType,
            me.potentialColumns.numInstallations,
            me.potentialColumns.numProducts,
            me.potentialColumns.numHosts
        ];

        // For advisories, the SAID is a default column
        me.advisoryPotentialColumns[0].hidden = true;

        // These are the fields shared by all smartgroup grids.
        me.genericOverviewGridFields = [
            {name: 'id', type: 'int'},
            {name: 'name', type: 'string'},
            {name: 'editable', type: 'int'},
            {name: 'in_progress', type: 'int'},
            {name: 'generate_asap', type: 'int'},
            {name: 'description', type: 'string'},
            {name: 'business_impact', type: 'int'},
            {name: 'logic_type', type: 'string'},
            {name: 'custom_columns', type: 'string'},
            {name: 'all_custom_columns', type: 'int'},
            {name: 'compiled_time', type: 'string'},
            {name: 'date_modified', type: 'string'}
        ];

        // NOTE: Remember that array copy in javascript is actually by
        // reference, and we need independent unique copies of the generic
        // items - use 'slice'
        // (NB: slice is only for shallow copy - i.e. will not clone objects!)

        // Host SGs use all the same ones as products, plus average score.
        me.hostOverviewGridFields = me.genericOverviewGridFields.slice(0); // see note
        me.hostOverviewGridFields.push(
            {name: 'average_score', type: 'int'},
            {name: 'num_installations', type: 'int'},
            {name: 'hosts', type: 'int'}
        );

        // Product SGs use the base ones, plus totalNum and numAffectedHosts
        me.productOverviewGridFields = me.genericOverviewGridFields.slice(0); // see note
        me.productOverviewGridFields.push(
            {name: 'num_products', type: 'int'},
            {name: 'num_installations', type: 'int'},
            {name: 'num_hosts', type: 'int'}
        );

        me.advisoryOverviewGridFields = me.genericOverviewGridFields.slice(0); // see note
        me.advisoryOverviewGridFields.push(
            {name: 'advisories', type: 'int'},
            {name: 'installations', type: 'int'},
            {name: 'hosts', type: 'int'},
            {name: 'vulnerabilities', type: 'int'},
            {name: 'products', type: 'int'},
            {name: 'zero_day', type: 'int'} // TODO - make dynamic?
        );

        me.hostOverviewGridColumns = me.genericOverviewGridColumns.slice(0);
        me.productOverviewGridColumns = me.genericOverviewGridColumns.slice(0);
        me.advisoryOverviewGridColumns = me.genericOverviewGridColumns.slice(0);

        me.hostOverviewGridColumns.splice(6, 0,
            {
                header: 'Average Score'
                ,width: 125
                ,dataIndex: 'average_score'
                ,align: 'right'
                ,renderer: sfw.util.Default.renderSSS
                ,sortable: true
            },{
                header: 'Hosts'
               // ,id: 'hosts'
                ,dataIndex: 'hosts'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Installations'
               // ,id: 'num_installations'
                ,dataIndex: 'num_installations'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            }
        );

        me.productOverviewGridColumns.splice(5, 0,
            {
                header: 'Unique Products'
               // ,id: 'num_products'
                ,dataIndex: 'num_products'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Installations'
               // ,id: 'num_installations'
                ,dataIndex: 'num_installations'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Hosts'
               // ,id: 'num_hosts'
                ,dataIndex: 'num_hosts'
                ,align: 'right'
                ,sortable: true
                ,width: 100
            }
        );

        me.advisoryOverviewGridColumns.splice(6, 0,
            {
                header: 'Advisories'
               // ,id: 'advisories'
                ,dataIndex: 'advisories'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Vulnerabilities'
               // ,id: 'vulnerabilities'
                ,dataIndex: 'vulnerabilities'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Hosts'
               // ,id: 'hosts'
                ,dataIndex: 'hosts'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Products'
               // ,id: 'products'
                ,dataIndex: 'products'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Installations'
                //,id: 'installations'
                ,dataIndex: 'installations'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            },{
                header: 'Zero-Day Advisories'
                //,id: 'zero_day'
                ,dataIndex: 'zero_day'
                ,align: 'right'
                ,sortable: true
                ,width: 120
            }
        );

    },

    /*
    * Get the smartgroup related constants from the backend and store
    * into the necessary properties and stores
    *
    * Note - we can't call this function early as the login info needed
    * for the url is not defined yet.
    */
    loadSmartGroupConstants: function () {
        const self = this,
            globals = sfw.util.Globals;

        Ext.Ajax.request({
            url: globals.apiPath() + 'action=smart_group_constants&which=getLookupData',
            success: function (response) {
                var dataArrays = Ext.decode(response.responseText);

                // Start with the main smartgroup types
                var mainTypes = dataArrays.SMART_GROUP_TYPES;
                self.SMARTGROUP_TYPE_HOST = mainTypes.SMARTGROUP_TYPE_HOST;
                self.SMARTGROUP_TYPE_PRODUCT = mainTypes.SMARTGROUP_TYPE_PRODUCT;
                self.SMARTGROUP_TYPE_ADVISORY = mainTypes.SMARTGROUP_TYPE_ADVISORY;

                // We also store them labelled by their text names for easier lookup later
                self.SMARTGROUP_TYPES[self.HOST[0]] = self.SMARTGROUP_TYPE_HOST;
                self.SMARTGROUP_TYPES[self.PRODUCT[0]] = self.SMARTGROUP_TYPE_PRODUCT;
                self.SMARTGROUP_TYPES[self.ADVISORY[0]] = self.SMARTGROUP_TYPE_ADVISORY;

                // Then get all the criteria types
                var criteriaTypes = dataArrays.CRITERIA_TYPES;

                self.CRITERIA_PRODUCT_STATUS = criteriaTypes.CRITERIA_PRODUCT_STATUS;
                self.CRITERIA_SOFT_TYPE = criteriaTypes.CRITERIA_SOFT_TYPE;
                self.CRITERIA_CRITICALITY = criteriaTypes.CRITERIA_CRITICALITY;
                self.CRITERIA_THREATSCORE = criteriaTypes.CRITERIA_THREATSCORE;
                self.CRITERIA_HOSTS = criteriaTypes.CRITERIA_HOSTS;
                self.CRITERIA_HOST_GROUP = criteriaTypes.CRITERIA_HOST_GROUP;
                self.CRITERIA_SITES = criteriaTypes.CRITERIA_SITES;
                self.CRITERIA_SAID_DATE = criteriaTypes.CRITERIA_SAID_DATE;
                self.CRITERIA_SILENT_INSTALLATION = criteriaTypes.CRITERIA_SILENT_INSTALLATION;
                self.CRITERIA_PRODUCTS = criteriaTypes.CRITERIA_PRODUCTS;
                self.CRITERIA_OPERATINGSYSTEMS = criteriaTypes.CRITERIA_OPERATINGSYSTEMS;
                self.CRITERIA_OPERATINGSYSTEMSBUILD = criteriaTypes.CRITERIA_OPERATINGSYSTEMSBUILD;
                self.CRITERIA_KB_ARTICLE = criteriaTypes.CRITERIA_KB_ARTICLE;
                self.CRITERIA_CVE_NUMBER = criteriaTypes.CRITERIA_CVE_NUMBER;

                self.CRITERIA_SCORE = criteriaTypes.CRITERIA_SCORE;
                self.CRITERIA_PLATFORM = criteriaTypes.CRITERIA_PLATFORM;
                self.CRITERIA_LAST_SCANNED = criteriaTypes.CRITERIA_LAST_SCANNED;

                self.CRITERIA_ADVISORY_DATE = criteriaTypes.CRITERIA_ADVISORY_DATE;
                self.CRITERIA_ZERO_DAY = criteriaTypes.CRITERIA_ZERO_DAY;
                self.CRITERIA_IMPACT_TYPE = criteriaTypes.CRITERIA_IMPACT_TYPE;
                self.CRITERIA_WHERE_TYPE = criteriaTypes.CRITERIA_WHERE_TYPE;
                self.CRITERIA_CVSS_SCORE = criteriaTypes.CRITERIA_CVSS_SCORE;
                self.CRITERIA_CVSS2_SCORE = criteriaTypes.CRITERIA_CVSS2_SCORE;
                self.CRITERIA_CVSS3_SCORE = criteriaTypes.CRITERIA_CVSS3_SCORE;
                self.CRITERIA_SOLUTION_STATUS = criteriaTypes.CRITERIA_SOLUTION_STATUS;
                self.CRITERIA_PRODUCT_NAME = criteriaTypes.CRITERIA_PRODUCT_NAME;
                self.CRITERIA_HOST_NAME = criteriaTypes.CRITERIA_HOST_NAME;
                self.CRITERIA_SITE_NAME = criteriaTypes.CRITERIA_SITE_NAME;
                self.CRITERIA_VENDOR_NAME = criteriaTypes.CRITERIA_VENDOR_NAME;

                // Then get the data structure that associates which
                // criteria apply to what smartgroup types. We will use
                // this association to later construct appropriate stores
                // for the type we are using
                var criteriaAssociationData = dataArrays.CRITERIA_ASSOCIATION_DATA;
                self.CRITERIA_ASSOCIATIONS = new Ext.data.ArrayStore({
                    fields: ['type', 'criteriaArray']
                    , data: [[
                        self.SMARTGROUP_TYPE_HOST
                        , criteriaAssociationData[self.SMARTGROUP_TYPE_HOST]
                    ], [
                        self.SMARTGROUP_TYPE_PRODUCT
                        , criteriaAssociationData[self.SMARTGROUP_TYPE_PRODUCT]
                    ], [
                        self.SMARTGROUP_TYPE_ADVISORY
                        , criteriaAssociationData[self.SMARTGROUP_TYPE_ADVISORY]
                    ]]
                });

                // Get the objects that capture the relationship between Criteria,
                // Action and Value for smartgroups.
                var criteriaTypesData = dataArrays.CRITERIA_TYPES_DATA;

                self.CRITERIA_TYPES = new Ext.data.ArrayStore({
                    fields: self.criteriaStoreFields,
                    data: criteriaTypesData
                });

                var actionTypesData = dataArrays.ACTION_TYPES_DATA;
                self.ACTION_TYPES = new Ext.data.ArrayStore({
                    fields: self.actionStoreFields
                    , data: actionTypesData
                });

                var valueTypesData = dataArrays.VALUE_TYPES_DATA;
                self.VALUE_TYPES = new Ext.data.ArrayStore({
                    fields: self.valueStoreFields
                    , data: valueTypesData
                });

                self.SMARTGROUP_STORES[self.PRODUCT[0]] = self.createSmartGroupStores(self.SMARTGROUP_TYPE_PRODUCT);

                self.SMARTGROUP_STORES[self.HOST[0]] = self.createSmartGroupStores(self.SMARTGROUP_TYPE_HOST);

                self.SMARTGROUP_STORES[self.ADVISORY[0]] = self.createSmartGroupStores(self.SMARTGROUP_TYPE_ADVISORY);

            }
            , failure: function () {
                // Silent...
            }
        });
    },

    // Function to construct appropriate stores for criteria / action /
    // value for a given smartgroup type. We depend on the data in 'this'
    // existing which means the AJAX call has returned and executed
    // successfully - so we just call it from within the ajax call.
    createSmartGroupStores: function (smartGroupType) {
        const self = this;

        var i, index;
        var neededCriteria = [], neededActions = [], neededValues = [];
        var criteriaData = [], actionData = [], valueData = [];
        var critRecord, actionRecord, valueRecord, currentAction, currentValue;

        // First get the relevant criteria for this SG type
        index = self.CRITERIA_ASSOCIATIONS.find('type', smartGroupType);
        if (-1 === index) {
            return;
        }
        neededCriteria = self.CRITERIA_ASSOCIATIONS.getAt(index).data.criteriaArray;
        // For all the criteria requested, get a unique list of the action
        // and value types, and use this to construct the data stores for
        // each as we go through the data.
        for (i = 0; i < neededCriteria.length; ++i) {
            index = self.CRITERIA_TYPES.find('id', neededCriteria[i]);
            if (-1 === index) {
                continue;
            }
            critRecord = self.CRITERIA_TYPES.getAt(index).data;

            criteriaData.push(critRecord);

            currentAction = critRecord.action_type;
            currentValue = critRecord.value_type;

            // If non-null and we haven't already added it, add it
            if (currentAction
                && (-1 === neededActions.indexOf(currentAction))
            ) {
                neededActions[neededActions.length] = currentAction;
            }

            // Same for values...
            if (currentValue
                && (-1 === neededValues.indexOf(currentValue))
            ) {
                neededValues[neededValues.length] = currentValue;
            }
        }

        // Now, for actions and values, we have the distinct needed
        // lists. Now we need to load the data array with all the matching
        // items. Remember, that in these ones, the 'type' is not unique -
        // we have multiple records matching type, thus could not use
        // 'find', as that only gives you the first match.
        var rawData, j;
        for (i = 0; i < neededActions.length; ++i) {
            rawData = self.ACTION_TYPES.query('type', neededActions[i]);
            for (j = 0; j < rawData.length; ++j) {
                actionData.push(rawData.items[j].data);
            }
        }

        for (i = 0; i < neededValues.length; ++i) {
            rawData = self.VALUE_TYPES.query('type', neededValues[i]);
            for (j = 0; j < rawData.length; ++j) {
                valueData.push(rawData.items[j].data);
            }
        }

        // Now we have all the data we're going to use packed
        // appropriately - just construct stores from it
        var fields = [
            self.criteriaStoreFields,
            self.actionStoreFields,
            self.valueStoreFields
        ];
        var data = [criteriaData, actionData, valueData];
        var stores = [];

        for (i = 0; i < 3; ++i) {
            //stores[i] = new Ext.data.JsonStore({
            stores[i] = new Ext.data.Store({
                storeId: 'sgLookupData_' + smartGroupType + '_' + i,
                autoLoad: true,
                idProperty: 'id',
                fields: fields[i],
                data: data[i]
            });
        }
        return stores;
    },

    EMAIL_RECIPIENT: "CSI_RCPT",

    USER_SELECTION: "CSI_USR",
    SELECT_EVENTS : 'CSI_EVENTS',

    SELECT_LANGUAGES : 'CSI_LANGUAGES',

    SELECT_MOBILE_RECIPIENT : 'CSI_MOBILE_RCPT',

    //--- generic columns
    // These columns are shared by all SG overview grids
    genericOverviewGridColumns: [{
        header: 'Name'
        //,id: 'name'
        ,dataIndex: 'name'
        ,align: 'left'
        ,sortable: true
        ,width: 150
        ,renderer: function( value, metaData, record ) {
            // If it is not editable, show it in bold font
            var retVal = value;
            if ( !parseInt( record.get( 'editable' ), 10 ) ) {
                retVal = '<b>' + retVal + '</b>';
            }
            return retVal;
        }
    },{
        header: 'Description'
        //,id: 'description'
        ,dataIndex: 'description'
        ,align: 'left'
        ,sortable: true
       // ,renderer: sfw.util.tipRenderer
    },{
        header: 'Business Impact'
        //,id: 'business_impact'
        ,dataIndex: 'business_impact',
        //,renderer: sfw.util.Default.businessImpactImage
        xtype: 'widgetcolumn',
        align: 'center',
        sortable: true,
        width: 90,
        widget: {
            xtype: 'sectorprogress',
            height: 8
        }
    },{
        header: 'Compilation'
        //,id: 'in_progress'
        ,dataIndex: 'in_progress'
        ,align: 'left'
        ,sortable: true
        ,width: 125
        ,renderer: function( val, cell, record ) {
            var status = 'Complete';
            if ( parseInt(record.data.generate_asap, 10)) {
                status = 'Queued for compilation';
            } else if ( parseInt(record.data.in_progress, 10) ) {
                status = 'Recompiling...';
            }
            return status;
        }
    },{
        header: 'Data Last Compiled'
        //,id: 'compiled_time'
        ,dataIndex: 'compiled_time'
        ,align: 'left'
        ,sortable: true
        ,width: 120
        ,renderer: sfw.util.Default.gridRenderUTCDateInLocaltime
    },{
        header: 'Modified Date'
        //,id: 'date_modified'
        ,dataIndex: 'date_modified'
        ,align: 'left'
        ,sortable: true
        ,width: 120
        ,renderer: sfw.util.Default.gridRenderUTCDateInLocaltime
    }],

    ACTIVEX_MESSAGE_PATCHING : '<a href="https://resources.flexera.com/tools/SVM/SVMPatchPublisher.msi">Download</a> and Install SVM Patch Publisher to create patches and publish to endpoint management systems.',

    ACTIVEX_MESSAGE : 'This feature is currently available at legacy console (<a href="https://csi7.secunia.com">https://csi7.secunia.com</a>) in Internet Explorer with the IE Plugin installed and enabled.'
    
});

sfw.util.CommonConstants.init();
