Ext.define('sfw.util.Advisory', {
    singleton: true,

    formatCvssScore : function( data, formatLinks ) {
        if ( typeof data.details.vuln_cvss_score != "undefined" &&
            data.details.vuln_cvss_score != 0 ) {
            var html = ''
                + ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss_vector +	'\')">' ) : '' )
                + 'Base: ' + data.details.vuln_cvss_score + ', Overall: ' + data.details.cvss_data.OverallScore
                + ( formatLinks ? '</a> ' : ' ' )
                + data.details.vuln_cvss_vector;
            return html;
        }

        return null;
    },

    formatCvss3Score : function( data, formatLinks ) {
        if ( typeof data.details.vuln_cvss3_score != "undefined" &&
            data.details.vuln_cvss3_score != 0 ) {
            var html = ''
                + ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss3_vector +	'\')">' ) : '' )
                + 'Base: ' + data.details.vuln_cvss3_score + ', Overall: ' + data.details.cvss3_data.OverallScore
                + ( formatLinks ? '</a> ' : ' ' )
                + data.details.vuln_cvss3_vector;
            return html;
        }

        return null;
    },

    formatCvss4Score : function( data, formatLinks ) {
        if ( typeof data.details.vuln_cvss4_score != "undefined" &&
            data.details.vuln_cvss4_score != 0 ) {
            var html = ''
                + ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss4_vector +	'\')">' ) : '' )
                + 'Base: ' + data.details.vuln_cvss4_score + ', CVSS-BT: ' + data.details.cvss4_data.BaseThreatScore
                + ( formatLinks ? '</a> ' : ' ' )
                + data.details.vuln_cvss4_vector;
            return html;
        }

        return null;
    },

    formatImpact : function( impactArray ) {
        var impact = "";
        for ( var i = 0; i < impactArray.length; i++ ) {
            impact += impactArray[i].impact_type_name + "<br/>";
        }

        return impact;
    },

    makeWhere : function( whereInfo ) {
        var where = whereInfo.where_type_name;
        return where;
    },

    generateDetailsPanel : function( data, formatAdditionalDetails ) {

        var sections = [ sfw.Default.replaceURLWithHTMLLinks(data.description)
            ,0x0004 ? sfw.Default.replaceURLWithHTMLLinks(data.ratingReason) : {}
            ,sfw.Default.replaceURLWithHTMLLinks(data.solution)
            ,0x0004 ? sfw.Default.replaceURLWithHTMLLinks(data.extendedSolution) : {}
            ,sfw.Default.replaceURLWithHTMLLinks(data.credits)
            ,sfw.Default.replaceURLWithHTMLLinks(data.changelog)
            ,sfw.Default.replaceURLWithHTMLLinks(data.originalAdvisory)
            ,sfw.Default.replaceSAIDWithHTMLLinks(sfw.Default.replaceURLWithHTMLLinks(data.otherReferences))];

        return sections;
    },

    formatLanguage : function( languages, vulnId, accountId ) {
        var html = "";

        var template = '<img onClick="sfw.Default.advisoryView( :LANGUAGE_CODE '
            + ', ' + parseInt( vulnId, 10 )
            + ', ' + parseInt( accountId, 10 )
            + ');"'
            + ' src="resources/images/:FLAG_IMAGE_NAME" style="border: 1px solid black;cursor: pointer;" border="0" alt="Advisory Available in :LANGUAGE_NAME">';
        var temp;

        if ( languages.english == true ) {
            temp = template.replace( ':FLAG_IMAGE_NAME', 'english.gif' );
            temp = temp.replace( ':LANGUAGE_NAME', 'English' );
            temp = temp.replace( ':LANGUAGE_CODE', '1' );
            html += temp;
        }
        if ( languages.danish == true ) {
            temp = template.replace( ':FLAG_IMAGE_NAME', 'danish.gif' );
            temp = temp.replace( ':LANGUAGE_NAME', 'Danish' );
            temp = temp.replace( ':LANGUAGE_CODE', '2' );
            html += temp;
        }
        if ( languages.german == true ) {
            temp = template.replace( ':FLAG_IMAGE_NAME', 'german.gif' );
            temp = temp.replace( ':LANGUAGE_NAME', 'German' );
            temp = temp.replace( ':LANGUAGE_CODE', '5' );
            html += temp;
        }
        return html;
    },

    formatCveReferences : function( cveArray, formatLinks ) {
        var cve = "";
        for ( var i = 0; i < cveArray.length; i++ ) {

            if ( formatLinks ) {
                cve += "<a href='javascript:sfw.cve.view(\"" + cveArray[i].cve_reference + "\")'>" + cveArray[i].cve_reference + "</a>";
            } else {
                cve += "<a href='https://cve.mitre.org/cgi-bin/cvename.cgi?name=" + cveArray[i].cve_reference +"' target='_blank'>" + cveArray[i].cve_reference + "</a>";
            }

            if ( typeof cveArray[i].cvss_vector != "undefined" ) {
                cve += " / ";
                if ( formatLinks ) {
                    cve += "<a href='javascript:sfw.cvss.view(\"" + cveArray[i].cve_reference + "\",\"" + cveArray[i].cvss_vector + "\")'>CVSS: " + cveArray[i].BaseScore + "</a> " + cveArray[i].cvss_vector;
                } else {
                    cve += "CVSS: " + cveArray[i].BaseScore + " ";
                }
            }
            cve += ", ";
        }
        if ( cve != "" && cve != "<br/>" ) {
            return cve.substr(0, cve.length-2);
        }

        return null;
    }
});