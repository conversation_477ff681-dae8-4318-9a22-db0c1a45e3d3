Ext.define('sfw.util.SecuniaPackageSystem', {
    singleton: true,

    /**
     * @method
     * @public
     * Creates instances of all the WSUS API objects and its dependencies.
     * @param {Object} conf Has the following parameters:
     * root: The object to which to apply instances. This MUST be the global 'app' object.
     * isCreateWsusApi: Instructs SPS to create all the WSUS API instances. Without this, only the deploymentUIFactory is instantiated.
     */
    init: function (conf) {
        sfw = sfw || {};
        sfw.sps = sfw.sps || {};

        // IMPORTANT!! conf.root MUST BE app
        /*var root = conf.root;
        root.sps = root.sps || {};
        root.sps.factories = root.sps.factories || {};
        root.sps.factories.deploymentUI = new sfw.sps.DeploymentUIFactory();

        if ( conf.isCreateWsusApi ) {
            /!*
             * @todo: Part of restructuring the wsus plugin. All sps related
             * functionality should use the sps namespace.
             *
             * @see: CSI-5013
             *!/
            root.sps.util = new sfw.sps.Util();
            root.sps.download = new sfw.sps.Download();
            root.sps.script = new sfw.sps.Script();
            root.sps.pluginLoader = new sfw.sps.PluginLoader();
            root.sps.packageCreator = new sfw.sps.PackageCreator();
            root.sps.api = root.sps.api || {};
            root.sps.api.common = new sfw.sps.api.Common();
            root.sps.api.gpo = new sfw.sps.api.GPO();
            root.sps.api.system = new sfw.sps.api.System();
            root.sps.api.wsus = new sfw.sps.api.Wsus();
            root.sps.api.x509 = new sfw.sps.api.X509();

            root.sps.pluginLoader.addNames( root.sps.api.gpo.getNames() );
            root.sps.pluginLoader.addNames( root.sps.api.system.getNames() );
            root.sps.pluginLoader.addNames( root.sps.api.wsus.getNames() );
            root.sps.pluginLoader.addNames( root.sps.api.x509.getNames() );
        }*/
    },

    /**
     * @method
     * @public
     * Creates the CSI SPS-related UI pages. sfw.sps.init() must have been called before this.
     * @param {Array} pages An array with the names of pages to create. Existing page names are: 'configuration', 'available', 'deployment', 'createAgents', 'createPackages', 'patchTemplate',  'vendorPatch'
     */
    createUIPages: function (pages) {
        const me = this;
        
        for ( var i=0 ; i<pages.length ; ++i ) {
            switch ( pages[i] ) {
                case 'configuration' :
                    me.csiWSUSConfiguration();
                    break;

                case 'available' :
                    me.csiAvailablePatches();
                    break;

                case 'deployment' :
                    me.csiPatchDeployment();
                    break;

                case 'createAgents' :
                    me.csiAgentDeployment();
                    break;

                case 'createPackages' :
                    me.csiCreatePatch();
                    break;

                case 'patchTemplate' :
                    me.csiCreatePatchTemplate();
                    break;

                case 'vendorPatch' :
                    me.csiVendorPatchView();
                    break;

                case 'createPackagesNonIE' :
                    me.csiCreatePatch();
                    break;

                case 'patchTemplateNonIE' :
                    me.csiCreatePatchTemplateNonIE();
                    break;

                case 'packageDeployment' :
                    me.csiCreatePackageDeployment();
                    break;

                case 'patchPublisher' :
                    me.csiCreatePatchPublisher();
                    break;

                case 'patchConnections' :
                    me.csiDeletePatchConnections();
                    break;

                default :
                    throw { message: "Invalid page name '" + pages[i] + "' in sps.init" };
            }
        }
    },

    csiWSUSConfiguration: function () {
        sfw.csiWSUSConfiguration = {};
        sfw.ui.pages['sfw.csiWSUSConfiguration'] = sfw.csiWSUSConfiguration;
        sfw.csiWSUSConfiguration.init = function() {
            this.id = 'sfw.csiWSUSConfiguration';
            this.title = 'WSUS Configuration';
            this.parent = 'sfw.csiPatchSystemsConfigurations';
            this.menu = {
                text: 'WSUS / System Center',
                singleClickExpand: true,
                expanded: true
            };
            //this.type = 'folder';
        }
    },

    csiAvailablePatches: function () {
        sfw.csiAvailablePatches = {};
        sfw.ui.pages['sfw.csiAvailablePatches'] = sfw.csiAvailablePatches;
        sfw.csiAvailablePatches.init = function() {
            this.id = 'sfw.csiWSUSConfiguration';
            //this.title = 'WSUS Configuration';
            this.parent = 'sfw.csiWsusSccm';
            this.menu = {
                text: 'Available',
                singleClickExpand: true,
                expanded: true
            };
            this.description = 'Displays a host\'s information collected from the WSUS Server.';
            //this.type = 'folder';
        }
    },

    csiPatchDeployment: function () {
        sfw.csiPatchDeployment = {};
        sfw.ui.pages['sfw.csiPatchDeployment'] = sfw.csiPatchDeployment;
        sfw.csiPatchDeployment.init = function() {
            this.id = 'sfw.csiWSUSConfiguration';
            //this.title = 'WSUS Configuration';
            this.parent = 'sfw.csiWsusSccm';
            this.menu = {
                text: 'Deployment'
            };
            this.description = 'Displaying information on hosts collected from the WSUS Server.';
            //this.type = 'folder';
        }
    },

    csiAgentDeployment: function () {
        sfw.csiAgentDeployment = {};
        sfw.ui.pages['sfw.csiAgentDeployment'] = sfw.csiAgentDeployment;
        sfw.csiAgentDeployment.init = function() {
            this.menu = { text: "Agent Deployment" };
            this.parent = 'sfw.csiPatch';
            this.id = 'sfw.csiAgentDeployment';
            this.description = 'Displaying information on hosts collected from the WSUS Server.';
            //this.type = 'folder';
        }
    },

    csiCreatePatch: function () {
        sfw.csiCreatePatch = {};
        sfw.ui.pages['sfw.csiCreatePatch'] = sfw.csiCreatePatch;
        sfw.csiAgentDeployment.init = function() {
            this.title = '<div data-qtip="Flexera Package System (SPS)">Flexera Package System</div>';
            this.parent = 'sfw.csiPatch';
            this.id = 'sfw.csiCreatePatch';
            this.description = 'This window displays a list of products for which the Software Vulnerability Manager can automatically create an Update/Uninstall package. Packages can only be created for some of them - these are shown in blue. Products for which packages cannot be created are shown in grey.<br /><br />'
                + 'Click "Configure View" to select the criteria that will be used to display the products in this view.';
            //this.type = 'folder';
        }
    },

    csiCreatePatchTemplate: function () {
        sfw.csiCreatePatchTemplate = {};
        sfw.ui.pages['sfw.csiCreatePatchTemplate'] = sfw.csiCreatePatchTemplate;
        sfw.csiCreatePatchTemplate.init = function() {
            this.id = 'sfw.csiCreatePatchTemplate';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Patch Template' };
            this.description = "Create patch template for a product or product family";
        }
    },

    csiVendorPatchView: function () {
        sfw.csiVendorPatchView = {};
        sfw.ui.pages['sfw.csiVendorPatchView'] = sfw.csiVendorPatchView;
        sfw.csiVendorPatchView.init = function() {
            this.id = 'sfw.csiVendorPatchView';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Vendor Patch Module' };
            this.title = 'Vendor Patch Module';
            this.description = "Subscription based patches";
        }
    },

    csiCreatePatch: function () {
        sfw.csiCreatePatch = {};
        sfw.ui.pages['sfw.csiCreatePatch'] = sfw.csiCreatePatch;
        sfw.csiCreatePatch.init = function() {
            this.menu = { text: '<div data-qtip="Flexera Package System (SPS)">Flexera Package System</div>' };
            this.parent = 'sfw.csiPatch';
            this.title = 'Flexera Package System (SPS)';
            this.id = 'sfw.csiCreatePatch';
        }
    },

    csiCreatePatchTemplateNonIE: function () {
        sfw.csiCreatePatchTemplate = {};
        sfw.ui.pages['sfw.csiCreatePatchTemplate'] = sfw.csiCreatePatchTemplate;
        sfw.csiCreatePatchTemplate.init = function() {
            this.id = 'sfw.csiCreatePatchTemplate';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Patch Template' };
            this.title = 'Patch Template';
            this.description = "Create patch template for a product or product family";
        }
    },

    csiCreatePackageDeployment: function () {
        sfw.csiCreatePackageDeployment = {};
        sfw.ui.pages['sfw.csiCreatePackageDeployment'] = sfw.csiCreatePackageDeployment;
        sfw.csiCreatePackageDeployment.init = function() {
            this.id = 'sfw.csiCreatePackageDeployment';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Patch Deployment Status' };
            this.title = 'Patch Deployment Status';
            this.description = "Display Patch Deployment Status";
        }
    },

    csiCreatePatchPublisher: function () {
        sfw.csiDownloadPatchPublisher = {};
        sfw.ui.pages['sfw.csiDownloadPatchPublisher'] = sfw.csiDownloadPatchPublisher;
        sfw.csiDownloadPatchPublisher.init = function() {
            this.id = 'sfw.csiDownloadPatchPublisher';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Download Patch Publisher' };
            this.title = 'Download Patch Publisher';
            this.description = "Download Patch Publisher";
        }
    },

    csiDeletePatchConnections: function () {
        sfw.csiDeletePatchConnections = {};
        sfw.ui.pages['sfw.csiDeletePatchConnections'] = sfw.csiDeletePatchConnections;
        sfw.csiDeletePatchConnections.init = function() {
            this.id = 'sfw.csiDeletePatchConnections';
            this.parent = 'sfw.csiPatch';
            this.menu = { text: 'Patch Publisher Connections' };
            this.title = 'Patch Publisher Connections';
            this.description = "Patch Publisher Connections";
        }
    }

});