/**
 * Wrapper around options table. It utilizes local object as a cached instance of remote options table. Only stores data for local account_id.
 */
Ext.define('sfw.util.DefaultOptions', {
    //singleton: true,

    config: {
        dataProviderURL: 'action=options&which=',
        options: {}
    },

    /**
     * Fetches the value of the option.
     * @param  {string}          option        option name
     * @param  {string|Number}   defaultValue  Default value to return if option isn't set
     * @return {undefined|string}         undefined if option does not exists, or value of the option.
     */
    get: function( option, defaultValue ) {
        if ('undefined' === typeof this.options[option] && 'undefined' !== typeof defaultValue) {
            return defaultValue;
        }
        return this.options[option];
    },

    set: function( option, value ) {
        const me = this,
            globals = sfw.util.Globals;

        Ext.Ajax.request({
            url: globals.apiPath() + me.dataProviderURL + 'setOption',
            method: 'POST',
            params: {
                option: option,
                value: value
            },
            success: function( data ) {
                var response = Ext.decode( data.responseText, true );
                if ( response && response.success === true ) {
                    // update local cached version
                    me.options[option] = value;
                } else {
                    sfw.util.Debug.log( 'Failed to set option.' );
                }
            },
            failure: function() {
                sfw.util.Debug.log( 'Could not set option.' );
            }
        });
    },

    add: function(option, value) {
        var self = this;
        self.options[option] = value;
    },

    // Set options for other accounts. partition admin only.
    setForAccount: function( option, value, account ) {
        const me = this,
            globals = sfw.util.Globals;

        Ext.Ajax.request({
            url: globals.apiPath() + me.dataProviderURL + 'setOptionForAccount',
            method: 'POST',
            params: {
                option: option,
                value: value,
                account: account
            },
            success: function( data ) {
                var response = Ext.decode( data.responseText, true);
                if ( response && response.success === true ) {
                    // update local cached version
                    //self.options[option] = value;
                } else {
                    sfw.util.Debug.log( 'Failed to set option for account.', account );
                }
            },
            failure: function() {
                sfw.util.Debug.log( 'Could not set option.' );
            }
        });
    },

    // Get options for other accounts. partition admin only. Always loaded live
    getForAccount: function( option, account ) {
        const me = this,
            globals = sfw.util.Globals;

        Ext.Ajax.request({
            url: globals.apiPath() + me.dataProviderURL + 'getOptionForAccount',
            method: 'POST',
            params: {
                option: option,
                account: account
            },
            success: function( data ) {
                var response = Ext.util.JSON.decode( data.responseText, true );
                if ( response && response.success === true ) {
                    // update local cached version
                    //self.options[option] = value;
                } else {
                    sfw.util.Debug.log( 'Failed to get option for account.', account );
                }
            },
            failure: function() {
                sfw.util.Debug.log( 'Could not set option.' );
            }
        });
    },

    /**
     * @method
     * Fetches the latest configuration options from the server which are available via {@link defaults.options.get()}.
     * @public
     * @note LoginDetails.account.configuration uses defaults.options.options
     * @param {Function} callback
     *	You can optionally specify a function to be executed after the options have been fetched from the server.
     *	The specified function will be called with an object containing all the options as it's only argument.
     */
    getAllOptions: function( callback ) {
        const me = this,
            globals = sfw.util.Globals;

        Ext.Ajax.request({
            url: globals.apiPath() + me.getDataProviderURL() + 'getAllOptions',
            method: 'GET',
            success: function( data ) {
                var response = Ext.decode( data.responseText, true );
                me.setOptions(response.data);
                me.options.get = function( val ) {
                    return this[val];
                }
                self.options.set = function( key, val ) {
                    this[key] = val;
                }
                if ( 'undefined' !== typeof callback ) {
                    callback.call( me, me.getOptions() );
                }
            },
            failure: function() {
                sfw.util.Debug.log( 'Could not fetch options.' );
            }
        });
    }
});