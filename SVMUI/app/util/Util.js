Ext.define('sfw.util.Util', {
    singleton :true,
    alternateClassName: 'sfw.Util',


    dateCreate : function( input, utc ) {
        var oDate = new Date( 0 );
        try {
            var utcMode, seconds;
            var utcMiliseconds = 0;

            var defObj = new Object();
            defObj.reDate = /([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/;
            defObj.reDateTime = /([0-9]{4})-([0-9]{1,2})-([0-9]{1,2}) ([0-9]{1,2}):([0-9]{1,2}):?([0-9]{1,2})?/;
            defObj.reTime = /([0-9]{1,2}):([0-9]{1,2}):?([0-9]{1,2})?/;
            defObj.reNumber = /^([0-9]+)$/;

            utcMode = ( typeof utc === 'boolean' ? utc : false );

            if ( typeof input === 'undefined' || input === null ) {
                input = new Date();
            }

            // Convert input to a Date object
            if ( input instanceof Date ) {
                return input;
            } else {
                var str = input.toString();
                var matches = str.match(defObj.reDateTime);
                if ( matches ) {
                    seconds = ( typeof matches[6] !== 'undefined' ) ? matches[6] : 0;
                    if ( utcMode ) {
                        utcMiliseconds = Date.UTC( matches[1], parseInt( matches[2], 10 ) - 1, matches[3], matches[4], matches[5], seconds );
                        oDate = new Date( utcMiliseconds );
                    } else {
                        oDate = new Date( matches[1],parseInt(matches[2],10)-1,matches[3], matches[4],matches[5], seconds );
                    }
                } else {
                    matches = str.match(defObj.reDate);
                    if ( matches ) {
                        if ( utcMode ) {
                            utcMiliseconds = Date.UTC( matches[1], parseInt( matches[2], 10 )- 1, matches[3] );
                            oDate = new Date( utcMiliseconds );
                        } else {
                            oDate = new Date( matches[1], parseInt( matches[2], 10 ) - 1, matches[3]);
                        }
                    } else {
                        matches = str.match( defObj.reTime );
                        if ( matches ) {
                            seconds = ( typeof matches[3] !== 'undefined' ) ? matches[3] : 0;
                            oDate = new Date();
                            if ( utcMode ) {
                                oDate.setUTCHours( matches[1] );
                                oDate.setUTCMinutes( matches[2] );
                                oDate.setUTCSeconds( seconds );
                            } else {
                                oDate.setHours( matches[1] );
                                oDate.setMinutes( matches[2] );
                                oDate.setSeconds( seconds );
                            }
                        } else {
                            matches = str.match( defObj.reNumber );
                            if ( matches ) {
                                oDate = new Date( parseInt( matches[1], 10 ) * 1000 );
                            } else {
                                throw { number: 1, message: "Invalid Date" };
                            }
                        }
                    }
                }
            }
        } catch ( ex ) {

        }

        return oDate;
    },

    differenceBetweenDates : function( from, to, details, options ) {
        try {
            var dateFrom = sfw.util.Util.dateCreate( from );
            var dateTo = sfw.util.Util.dateCreate( to );
            var options = options || {};
            var left = "";

            var output;

            // Get difference in seconds
            var difference = Math.round( (dateTo.valueOf() - dateFrom.valueOf() ) / 1000 );

            // The compared dates may not have originated from the system system clock so a discrepancy
            // of a few seconds or minutes is possible and may result in a negative difference
            if ( difference < 0 ) {
                difference = 0;
            }

            if ( !details ) {
                if ( difference < 2*60 ) {
                    details = 4; // include from seconds and bigger if under 2 minutes
                } else if ( difference < 2*60*60 ) {
                    details = 3; // include from minutes and up if under 2 hours (but > 2 minutes)
                } else if ( difference < 86400 * 2 ) {
                    details = 2; // include from hours and up if under 2 days (but > 2 hours)
                } else if ( difference < 86400 * 61 ) {
                    details = 1; // only show days if under 61 days months (but > 2 days)
                } else {
                    // only show months
                    left = Math.round( difference / ( 86400*61 ) );
                    output = left + ' month' + ( left != 1 ? 's' : '' );
                    return output;
                }
            }

            // What to report
            switch ( details ) {
                case 4: // Include seconds
                    left = ( difference % 60 );
                    if ( !options.excludeEmpty || left ) {
                        output = left + ' second' + ( left !== 1 ? 's' : '' );
                    }
                case 3: // Include minutes
                    left = Math.floor( ( difference % 3600 ) / 60 );
                    if ( !options.excludeEmpty || left ) {
                        output = left + ' minute' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
                    }
                case 2: // Include hours
                    left = Math.floor( ( difference % 86400 ) / 3600 );
                    if ( !options.excludeEmpty || left ) {
                        output = left + ' hour' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
                    }
                case 1: // Include days
                    left = Math.floor( difference / 86400 );
                    if ( !options.excludeEmpty || left ) {
                        output = left + ' day' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
                    }
                    if ( options.excludeEmpty | 0 && undefined === output ) {
                        output = ''; // if excludeEmpty is enabled and no time segments were included
                    }
                    break;
                default:
                    // Note - there is now a space in: ', ' + output , as we use this for direct display in a few places.
                    // This however breaks some places where we split on the array based on ','.  Anywhere we do this,
                    // remember to strip the space before splitting!  Also note, must only strip that one space, not
                    // all whitespace, so cannot use generic function in this file, stripWhitespace
                    break;
            }
        } catch ( ex ) {
            //sfw.debug.trigger( ex, "util.differenceBetweenDates" );
        }

        return output;
    },

    tipRenderer : function ( value ) {
        return '<div qtip="' + Ext.util.Format.htmlEncode( value ) + '">' + value + '</div>';
    },

    dateCreateTodayOffset : function( iDays ) {
        var oDate = sfw.Util.dateCreate();

        if ( typeof iDays !== 'undefined' ) {
            oDate = Ext.Date.add( oDate, Ext.Date.DAY, iDays );
        }

        return oDate;
    },

    /**
     * Basic function for replacing all occurences in a string
     * @param search
     *	String text beying searched for
     * @param replace
     *	String text to replace with
     * @param string
     *	String subject
     * @return string result of the replacement
     */
    replaceAll: function( search, replace, string ) {
        try {
            while ( string.indexOf( search ) !== -1 ) {
                string = string.replace( search, replace );
            }
        } catch ( ex ) {
            sfw.util.Debug.trigger( ex, "util.replaceAll" );
        }

        return string;
    },

    /**
     *  Sets state provider for stateful components. If sfw.PersistentStateProvider
     *  is available (CSI)
     *  it will be used and otherwise a Ext.state.CookieProvider will be used
     *
     */
    setStateProvider: function () {
        var sp;
        if ( Ext.isObject( sfw.util.PersistentStateProvider ) ) {
            sp = sfw.util.PersistentStateProvider;
        } else {
            sp = new Ext.state.CookieProvider({
                expires: new Date(new Date().getTime()+(1000*60*60*24*365)) //1 year
            });
        }
        Ext.state.Manager.setProvider( sp );
        return true;
    },

    intToMacAddress : function ( macAddressInt ) {
        var str = parseInt( macAddressInt ).toString(16); // convert to hex
        str = new Array( 13 - str.length ).join( "0" ) + str; // left pad "0" to 12 chars
        return str.match(/[a-f0-9]{2}/g).join( ":" ); // insert ":"'s and return
    },

    int32ToIpv4 : function ( ipAddressInt ) {
        return Ext.String.format(
            "{0}.{1}.{2}.{3}"
            ,ipAddressInt >> 24 & 0xff
            ,ipAddressInt >> 16 & 0xff
            ,ipAddressInt >> 8 & 0xff
            ,ipAddressInt & 0xff
        );
    },

    compareVersions : function (v1, v2) {
        var a = v1.split('.').map(Number);
        var b = v2.split('.').map(Number);
        var len = Math.max(a.length, b.length);
        for (var i = 0; i < len; i++) {
            var num1 = a[i] || 0;
            var num2 = b[i] || 0;
            if (num1 > num2) return 1;
            if (num1 < num2) return -1;
        }
        return 0;
    }

});
