Ext.define('sfw.util.MenuFolders', {
    singleton: true,

    initMenuFolders: function () {
        sfw.ui = {
            activeMenuItem: ""
        };
        sfw.isReady = false;
        sfw.ui.pages = {};
        sfw.tmp = {}; // Load a generic purpose temp object

        // ENHANCEMENT: Remove this and add to a login object
        sfw.configuration = {
            beta: false,
            ui: {
                menuTitle: 'Menu',
                displayDescription: false,
                displayLogo: false, // displayed below the description panel below the menu tree
                displayDescriptionOnHover: true,
                home: 'sfw.csiDashboard',
                afterrender: function () {
                    fUpdateMenuNumberContainers(); //FIXME
                    if (LoginDetails.account.isMspUser()) {
                        document.getElementById("toolbarLoginText").innerText += " (Restricted)";
                    } else if (LoginDetails.isReadOnly) {
                        document.getElementById("toolbarLoginText").innerText += " (Read Only)";
                    }
                },
                skipCache: true,
                disableCommonToolbarIcons: true,
                tabs: false,
                tabbedKeyboardNavigation: true
            },
            APIPath: "api/"
        };

        /**
         * Provides generic module manipulation, updated on login, based on user login info.
         * //STUB
         */
        sfw.modules = {};

        /**
         * Map of available modules, to be enabled by the local login mechanism.
         */
        sfw.modules.enabled = {
            vdb: {
                enabled: false
            }
            , vm: {
                enabled: false
            }
        };

        /**
         * Current user data.
         */
        sfw.modules.user = {
            id: 0
        }

        //--------------------------------------------------------------
        //    DASHBOARD
        //--------------------------------------------------------------
        // Reserve the top position in the Menu for the Dashboard
        sfw.csiDashboard = {};
        sfw.ui.pages['sfw.csiDashboard'] = sfw.csiDashboard;
        sfw.csiDashboard.init = function () {
            this.id = 'sfw.csiDashboard';
            // No Roles permission checks are required for this page
            this.skipRoleAccessCheck = true;
            this.menu = {
                text: 'Dashboard',
                iconCls: 'x-fa fa-tachometer-alt fa-lg',
                viewType: 'sfw.csiDashboard',
                routeId: 'sfw.csiDashboard', // routeId defaults to viewType
                singleClickExpand: true
            };
            this.type = 'folder';
        };

        //--------------------------------------------------------------
        //    SCANNING
        //--------------------------------------------------------------
        sfw.csiScanning = {};
        sfw.ui.pages['sfw.csiScanning'] = sfw.csiScanning;
        sfw.csiScanning.init = function () {
            this.id = 'sfw.csiScanning';
            this.menu = {
                text: 'Scanning',
                iconCls: 'x-fab fa-searchengin fa-lg',
                expanded: false,
                selectable: false,
                singleClickExpand: true
            };
            this.type = 'folder';
        };

        // Sub folders of the previous folder - note - these are fine here as this is the order we want them in.
        if (sfw.isDesktop) {
            sfw.csiRemoteScanningViaCSI = {};
            sfw.ui.pages['sfw.csiRemoteScanningViaCSI'] = sfw.csiRemoteScanningViaCSI;
            sfw.csiRemoteScanningViaCSI.init = function () {
                this.id = 'sfw.csiRemoteScanningViaCSI';
                this.menu = {
                    text: 'Remote Scanning Via Software Vulnerability Manager',
                    singleClickExpand: true,
                    expanded: true
                };
                this.type = 'folder';
                this.parent = 'sfw.csiScanning';
            };
        }

        //Remote Scanning Via Agents
        sfw.csiRemoteScanningViaAgents = {};
        sfw.ui.pages['sfw.csiRemoteScanningViaAgents'] = sfw.csiRemoteScanningViaAgents;
        sfw.csiRemoteScanningViaAgents.init = function () {
            this.id = 'sfw.csiRemoteScanningViaAgents';
            this.menu = {
                text: 'Remote Scans Via Agents',
                singleClickExpand: true,
                expanded: true
            };
            this.type = 'folder';
            this.parent = 'sfw.csiScanning';
        };

        //children of -> Remote Scanning Via Agents
        // 01 -> Network Appliance Agents
        sfw.csiNetworkApplianceAgents = {};
        sfw.ui.pages['sfw.csiNetworkApplianceAgents'] = sfw.csiNetworkApplianceAgents;
        sfw.csiNetworkApplianceAgents.init = function () {
            this.id = 'sfw.csiNetworkApplianceAgents';
            this.parent = 'sfw.csiRemoteScanningViaAgents';
            this.menu = {text: '<div data-qtip="Network Appliance Agents">Network Agent</div>'};
            this.description = 'Displays a list of hosts that have Network Appliance Agents installed.';
        };

        // 02 -> Network Appliance Groups
        sfw.csiNetworkApplianceGroups = {};
        sfw.ui.pages['sfw.csiNetworkApplianceGroups'] = sfw.csiNetworkApplianceGroups;
        sfw.csiNetworkApplianceGroups.create = function () {
            this.id = "sfw.csiNetworkApplianceGroups";
            this.parent = 'sfw.csiRemoteScanningViaAgents';
            this.menu = {text: '<div data-qtip="Network Appliance Groups">Network Groups</div>'};
            this.description = 'Use this window to download the agents and read an explanation on how to install the Network Appliance Agent.';
        };

        // 03 -> Download Network Agent
        sfw.csiNetworkAgentDownload = {};
        sfw.ui.pages['sfw.csiNetworkAgentDownload'] = sfw.csiNetworkAgentDownload;
        sfw.csiNetworkAgentDownload.create = function () {
            this.id = 'sfw.csiNetworkAgentDownload';
            this.parent = 'sfw.csiRemoteScanningViaAgents';
            this.menu = {text: '<div data-qtip="Download Network Agent">Download Agent</div>'};
            this.description = 'Use this window to download the agents and read an explanation on how to install the Network Appliance Agent.';
        };

        //------------------------------------------------------------
        //------------------------------------------------------------

        sfw.csiScanningViaLocalAgents = {};
        sfw.ui.pages['sfw.csiScanningViaLocalAgents'] = sfw.csiScanningViaLocalAgents;
        sfw.csiScanningViaLocalAgents.init = function () {
            this.id = 'sfw.csiScanningViaLocalAgents';
            this.menu = {
                // text: 'Scans Via Local Agents',
                text: '<div data-qtip="Scans Via Local Agents">Scans Via Local Agents</div>',
                singleClickExpand: true,
                expanded: true
            };
            this.type = 'folder';
            this.parent = 'sfw.csiScanning';
        };

        // children of Scanning Via Local Agents
        // 01 -> Single Host Agents
        sfw.csiAgentManagement = {};
        sfw.ui.pages['sfw.csiAgentManagement'] = sfw.csiAgentManagement;
        sfw.csiAgentManagement.init = function () {
            this.id = 'sfw.csiAgentManagement';
            this.parent = 'sfw.csiScanningViaLocalAgents';
            this.menu = {text: "Single Host Agents"};
            this.description = 'Manage configurations and schedule scans on hosts where the Agent is installed as a service.';
        };

        // 02 -> Download Local Agent
        sfw.csiLocalAgentDownload = {};
        sfw.ui.pages['sfw.csiLocalAgentDownload'] = sfw.csiLocalAgentDownload;
        sfw.csiLocalAgentDownload.init = function () {
            this.id = 'sfw.csiLocalAgentDownload';
            this.parent = 'sfw.csiScanningViaLocalAgents';
            this.menu = {text: "Download Agent"};
            this.description = 'Use this window to download the agents and read an explanation on how to install the Flexera Agent in Single Host mode.';
        };

        //------------------------------------------------------------
        //------------------------------------------------------------
        sfw.csiFilterScanResults = {};
        sfw.ui.pages['sfw.csiFilterScanResults'] = sfw.csiFilterScanResults;
        sfw.csiFilterScanResults.init = function() {
            this.id = 'sfw.csiFilterScanResults';
            this.menu = {
                text: 'Filter Scan Results',
                singleClickExpand: true,
                expanded: true
            };
            this.type = 'folder';
            this.parent = 'sfw.csiScanning';
        };

        // children of -> Filter Scan Results
        // 01 -> Scan Paths (0)
        sfw.csiScanPaths = {};
        sfw.ui.pages['sfw.csiScanPaths'] = sfw.csiScanPaths;
        sfw.csiScanPaths.init = function() {
            this.id = 'sfw.csiScanPaths';
            this.parent = 'sfw.csiFilterScanResults';
            this.menu = {
                text: "Scan Paths"
            };
            this.description = 'Create and maintain which directory paths should be included (Allow list) or excluded (Block list) in your scans.<br><br>'
                + 'CAUTION: Using Allow lists or Block lists will exclude some of your paths from the scan. The Flexera cannot not alert you towards excluded insecure products, even if they potentially expose your hosts to security threats.<br><br>'
                + '<b>Allow list:</b> Only the paths specifically included in a Allow list will be considered by the scanner. All others will be ignored.<br><br>'
                + '<b>Block list:</b> Paths included in a Block list will be ignored.  All others will be investigated by the scanner.';
        };

        // children of -> Filter Scan Results
        // 02 -> Custom Scan Rules
        sfw.csiCustomScanRules = {};
        sfw.ui.pages['sfw.csiCustomScanRules'] = sfw.csiCustomScanRules;
        sfw.csiCustomScanRules.init = function() {
            this.id = 'sfw.csiCustomScanRules';
            this.parent = 'sfw.csiFilterScanResults';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Custom Scan Rules' )
            };
            this.description = 'Create and maintain Custom Scan Rules for scanning of Customer created programs, drivers or plugins.'
        };

        //Extended Support
        sfw.csiExtendedSupport = {};
        sfw.ui.pages['sfw.csiExtendedSupport'] = sfw.csiExtendedSupport;
        sfw.csiExtendedSupport.init = function() {
            this.id = 'sfw.csiExtendedSupport';
            this.parent = 'sfw.csiFilterScanResults';
            this.menu = { text: "Extended Support"}
            this.description = 'List products marked for Extended Support';
        };

        //Completed Scans
        sfw.csiCompletedScans = {};
        sfw.ui.pages['sfw.csiCompletedScans'] = sfw.csiCompletedScans;
        sfw.csiCompletedScans.init = function() {
            this.id = 'sfw.csiCompletedScans';
            this.parent = 'sfw.csiScanning';
            this.menu = { text: "Completed Scans" };
            this.description = 'Displays an overview of scans conducted. If you experience problems, please contact our Customer Support Center.';
        };


        //Inventory Assessments
        sfw.csiInventory = {};
        sfw.ui.pages['sfw.csiInventory'] = sfw.csiInventory;
        sfw.csiInventory.init = function() {
            this.id = 'sfw.csiInventory';
            this.parent = 'sfw.csiScanning';
            this.menu = { text: "Inventory Assessment (Beta)" };
            this.description = 'Displays an overview of inventory imported. If you experience problems, please contact our Customer Support Center.';
        };


        //--------------------------------------------------------------
        //    RESULTS
        //--------------------------------------------------------------
        sfw.csiResults = {};
        sfw.ui.pages['sfw.csiResults'] = sfw.csiResults;
        sfw.csiResults.init = function() {
            this.id = 'sfw.csiResults';
            this.menu = {
                text: 'Results',
                singleClickExpand: true,
                iconCls: 'x-fa fa-clipboard-list'
            };
            this.type = 'folder';
        };

        sfw.csiSitesOverview = {};
        sfw.ui.pages['sfw.csiSitesOverview'] = sfw.csiSitesOverview;
        sfw.csiSitesOverview.init = function() {
            this.id = 'sfw.csiSitesOverview';
            this.parent = 'sfw.csiResults';
            this.menu = { text: "Sites" };
            this.isVisible = function() {
                if (sfw.util.ActiveDirectorySettings.hasADIntegration()) {
                    return false;
                }
                if (sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                    return true;
                }
                return true;
            }
            this.description = 'Overview of the Sites scanned in your network.';
            this.type = 'folder';
        };

        sfw.csiHostSmartGroupsFolder = {};
        sfw.ui.pages['sfw.csiHostSmartGroupsFolder'] = sfw.csiHostSmartGroupsFolder;
        sfw.csiHostSmartGroupsFolder.init = function() {
            this.id = 'sfw.csiHostSmartGroupsFolder';
            this.parent = 'sfw.csiResults';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Host Smart Groups' ), // TODO - just 'Host Smart Groups' (after some date when it is no longer considered 'new')
                singleClickExpand: true,
                selectable: false,
                expanded: false
            };
            this.type = 'folder';
        };

        // Product Smartgroups is a sub folder of RESULTS
        sfw.csiProductSmartGroupsFolder = {};
        sfw.ui.pages['sfw.csiProductSmartGroupsFolder'] = sfw.csiProductSmartGroupsFolder;
        sfw.csiProductSmartGroupsFolder.init = function() {
            this.id = 'sfw.csiProductSmartGroupsFolder';
            this.parent = 'sfw.csiResults';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Product Smart Groups' ), // TODO - just 'Produt Smart Groups' (after some date when it is no longer considered 'new')
                singleClickExpand: true,
                selectable: false,
                expanded: false
            };
            this.type = 'folder';
        };

        // Advisory Smartgroups is a sub folder of RESULTS
        sfw.csiAdvisorySmartGroupsFolder = {};
        sfw.ui.pages['sfw.csiAdvisorySmartGroupsFolder'] = sfw.csiAdvisorySmartGroupsFolder;
        sfw.csiAdvisorySmartGroupsFolder.init = function() {
            this.id = 'sfw.csiAdvisorySmartGroupsFolder';
            this.parent = 'sfw.csiResults';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Advisory Smart Groups' ), // TODO - just 'Advisory Smart Groups' (after some date when it is no longer considered 'new')
                singleClickExpand: true,
                expanded: false,
                selectable: false
            };
            this.type = 'folder';
        };

        sfw.csiZeroDay = {};
        sfw.ui.pages['sfw.csiZeroDay'] = sfw.csiZeroDay;
        sfw.csiZeroDay.init = function() {
            this.id = 'sfw.csiZeroDay';
            this.parent = 'sfw.csiResults';
            this.title = 'Zero-Day Advisories';
            this.menu = { text: 'Zero-Day Advisories' };
            this.description = 'View all issued zero-day advisories relevant to your network.';
            this.modules = ['zd'];
            // Update the Menu Count
        };

        //--------------------------------------------------------------

        //--------------------------------------------------------------
        //    REPORTING
        //--------------------------------------------------------------
        sfw.csiReports = {};
        sfw.ui.pages['sfw.csiReports'] = sfw.csiReports;
        sfw.csiReports.init = function() {
            this.id = 'sfw.csiReports';
            this.menu = {
                text: 'Reporting',
                iconCls: 'x-fa fa-chart-bar fa-lg',
                expanded: false,
                selectable: false,
                singleClickExpand: true
            };
            this.type = 'folder';
        };

        //Report Configuration
        sfw.csiReportingConfiguration = {};
        sfw.ui.pages['sfw.csiReportingConfiguration'] = sfw.csiReportingConfiguration;
        sfw.csiReportingConfiguration.init = function() {
            this.id = 'sfw.csiReportingConfiguration';
            this.parent = 'sfw.csiReports';
            this.menu = { text: 'Report Configuration' };
            // enhancement: we need to a standard way of constructing the api request
            // i.e. a global stub function that is overridden by local implementations
            // Once we have that, the functions doing ajax requests can be moved to sfw.sharedReporting
            this.dataProviderURL = 'action=reporting&which=';
            this.action = {
                prepareReportDownload: 'prepare_report_download',
                downloadReport: 'download_report'
            };
            this.description = 'Configure and schedule reports for generation based on the resultant scan data from your network.';
        };

        //Smart Group Notifications
        sfw.csiSmartgroupNotifications = {};
        sfw.ui.pages['sfw.csiSmartgroupNotifications'] = sfw.csiSmartgroupNotifications;
        sfw.csiSmartgroupNotifications.init = function() {
            this.id = 'sfw.csiSmartgroupNotifications';
            this.parent = 'sfw.csiReports';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Smart Group Notifications' )
            };
            this.dataProviderURL = 'action=smartgroup_notifications&which=';
            this.action = {
                saveNotification: 'save',
                editNotification: 'edit',
                getOverview: 'overview',
                deleteNotification: 'delete'
            };
            this.formTypes = {
                NEW: 'new',
                EDIT: 'edit'
            };
            this.description = 'Configure alerts/notifications to be sent to you via email ' + (sfw.util.Auth.LoginDetails.isSMSEnabled?'and/or SMS ':'') + 'when a particular Smart Group\'s content changes.';
        };

        //Activity Log Notifications
        sfw.csiNotification = {};
        sfw.ui.pages['sfw.csiNotification'] = sfw.csiNotification;
        sfw.csiNotification.init = function() {
            this.id = 'sfw.csiNotification';
            this.parent = 'sfw.csiReports';
            this.menu = {
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Activity Log Notifications' )
            };
            this.dataProviderURL = 'action=csi_notifications&which=';
            this.action = {
                createNotification: 'create',
                getOverview: 'overview',
                deleteNotification: 'delete'
            };
            this.formTypes = {
                NEW: 'new',
                EDIT: 'edit'
            };
            this.description = 'Configure alerts/notifications to be sent to you via email when the subscribed Activity Log triggers';
        };

        //Database Access
        sfw.csiDatabaseAccess = {};
        sfw.ui.pages['sfw.csiDatabaseAccess'] = sfw.csiDatabaseAccess;
        sfw.csiDatabaseAccess.init = function() {
            this.id = 'sfw.csiDatabaseAccess';
            this.parent = 'sfw.csiReports';
            this.menu = {
                text: 'Database Access',
                // TODO - remove after some date when it is no longer considered 'new'
                singleClickExpand: true,
                expanded: true
            };
            this.type = 'folder';
        };

        //-- Database Access children
        //1.

        sfw.csiDbConsole = {};
        sfw.ui.pages['sfw.csiDbConsole'] = sfw.csiDbConsole;
        sfw.csiDbConsole.init = function() {
            this.id = 'sfw.csiDbConsole';
            this.parent = 'sfw.csiDatabaseAccess';
            this.menu = {
                text: 'Database Console'
            };
            this.description = 'The console is a simple utility that allows you to view the data contained in Flexera\'s SQL database. Right-click on a table and select Show Data, which runs a SELECT * FROM <table_name>; command.';
        };

        //2.
        sfw.csiDatabaseCleanup = {};
        sfw.ui.pages['sfw.csiDatabaseCleanup'] = sfw.csiDatabaseCleanup;
        sfw.csiDatabaseCleanup.init = function() {
            this.id = 'sfw.csiDatabaseCleanup';
            this.parent = 'sfw.csiDatabaseAccess';
            this.menu = {
                text: "Database Cleanup"
            };
            this.pageSize = 10;

            // Constants
            sfw.csiDatabaseCleanup.HOSTLIST_EMPTY = 101;
            sfw.csiDatabaseCleanup.RULE_IN_PROGRESS = 102;
            sfw.csiDatabaseCleanup.NAME_EXISTS = 103;
            sfw.csiDatabaseCleanup.RULE_EXISTS = 104;

            // Rule statuses
            sfw.csiDatabaseCleanup.STATUS_PENDING = 0;
            sfw.csiDatabaseCleanup.STATUS_RUNNING = 1;
            sfw.csiDatabaseCleanup.STATUS_COMPLETE = 2;

            this.description = 'Delete hosts from your Software Vulnerability Manager account by configuring rules that check for certain criteria. You can use this feature, for example, to delete all the hosts that have not been scanned for more than 15 days.';
        };

        //3.
        sfw.csiScheduledExports = {}
        sfw.ui.pages['sfw.csiScheduledExports'] = sfw.csiScheduledExports;
        sfw.csiScheduledExports.init = function() {
            this.id = 'sfw.csiScheduledExports';
            this.parent = 'sfw.csiDatabaseAccess';
            this.menu = {
                text: "Scheduled Exports"
            };
        };

        //--------------------------------------------------------------
        //    PATCH
        //--------------------------------------------------------------
        sfw.csiPatch = {};
        sfw.ui.pages['sfw.csiPatch'] = sfw.csiPatch;
        sfw.csiPatch.init = function() {
            this.id = 'sfw.csiPatch';
            this.menu = {
                text: 'Patching',
                iconCls: 'x-fa fa-wrench fa-lg',
                expanded: false,
                selectable: false,
                singleClickExpand: true,
                listeners: {
                    //expand: this.displayWsusStatus.createDelegate(this)
                }
            };
            this.type = 'folder';

            /*var node;
            this.displayWsusStatus = function ( node ) {
                if ( sfw.isDesktop ) {
                    if ( app.sps.wsusplugin.isPluginLoaded() && ! app.sps.wsusplugin.isConnected() ) {
                        if ( ! app.sps.wsusplugin.connectionAttempted() ) {
                            app.sps.wsusplugin.connect( { silent:true } );
                        }
                    }
                    node = sfw.ui.menu.getNodeById( 'sfw.csiWSUSConfiguration' );
                    if ( node ) {
                        // Rename the 'Patch - WSUS Configuration' sidebar item to reflect the WSUS/SCCM server connection status
                        if ( ! app.sps.wsusplugin.isConnected() ) {
                            node.setText('WSUS / System Center (Disconnected)');
                        } else {
                            node.setText('WSUS / System Center (Connected)');
                        }
                    }
                }
            };*/
        };


        //--------------------------------------------------------------
        //    ADMINISTRATION / USER MANAGEMENT
        //--------------------------------------------------------------
        sfw.csiAdministration = {};
        sfw.ui.pages['sfw.csiAdministration'] = sfw.csiAdministration;
        sfw.csiAdministration.init = function() {
            this.id = 'sfw.csiAdministration';
            this.menu = {
                text: 'Administration',
                iconCls: 'x-fa fa-user fa-lg',
                singleClickExpand: true
            };
            this.type = 'folder';
        };

        sfw.csiUserManagement = {};
        //sfw.userManagement.modules = [ 'um' ]; //FIXME
        sfw.ui.pages['sfw.csiUserManagement'] = sfw.csiUserManagement;
        sfw.csiUserManagement.init = function() {
            this.id = 'sfw.csiUserManagement';
            this.parent = 'sfw.csiAdministration';
            this.skipRoleAccessCheck = true; //todo
            this.menu = {
                text: 'User Management',
                singleClickExpand: true,
                expanded: true
            };
            this.type = 'folder';
        };

        sfw.csiPartitionManagement = {};
        sfw.ui.pages['sfw.csiPartitionManagement'] = sfw.csiPartitionManagement;
        sfw.csiPartitionManagement.init = function() {
            this.parent = 'sfw.csiAdministration';
            this.id = 'sfw.csiPartitionManagement';
            this.title = 'Partition Management';
            this.menu = {
                text: 'Partition Management'
            };
            this.isVisible = function () {
                return sfw.util.Auth.LoginDetails.isPartitionAdmin;
            };
        };

        sfw.csiActiveDirectory = {};
        sfw.ui.pages['sfw.csiActiveDirectory'] = sfw.csiActiveDirectory;
        sfw.csiActiveDirectory.init = function() {
            this.parent = 'sfw.csiAdministration';
            this.id = 'sfw.csiActiveDirectory';
            this.title = 'Active Directory';
            this.menu = {
                text: 'Active Directory'
            };
            this.isVisible = function () {
                return sfw.util.Auth.LoginDetails.isPartitionAdmin;
            };
            this.description = 'Configure the Active Directory settings for the Software Vulnerability Manager.';
        };

        sfw.csiIpManagement = {};
        sfw.ui.pages['sfw.csiIpManagement'] = sfw.csiIpManagement;
        sfw.csiIpManagement.init = function() {
            this.parent = 'sfw.csiAdministration';
            this.id = 'sfw.csiIpManagement';
            this.title = 'IP Access Management';
            this.menu = {
                //text: sfw.sharedFunctions.addNewFlagToText( 'IP Access Management' ) //FIXME
                // TODO - remove (after some date when it is no longer considered 'new')
                text: 'IP Access Management'
            };
            this.skipRoleAccessCheck = true;
            this.isVisible = function() {
                return sfw.util.Auth.LoginDetails.isPartitionAdmin;
            };
            this.description = 'Configure the IPs the Software Vulnerability Manager can run from.<br /><br />'
                + 'All IPs that have been added to allow lists will be able to use the Software Vulnerability Manager. The IPs which are not in the allow lists or that are in the block lists will not be able to connect.';
        };

        sfw.csiPasswordPolicyConfiguration = {};
        sfw.ui.pages['sfw.csiPasswordPolicyConfiguration'] = sfw.csiPasswordPolicyConfiguration;
        sfw.csiPasswordPolicyConfiguration.init = function() {
            this.id = 'sfw.csiPasswordPolicyConfiguration';
            this.parent = 'sfw.csiAdministration';
            this.menu = {text: '<div data-qtip="Password Policy Configuration">Password Policy</div>'};
            this.description = 'Configure password policy for users in your network.';
        };

        //--------------------------------------------------------------
        //    CONFIGURATION
        //--------------------------------------------------------------
        sfw.csiConfiguration = {};
        sfw.ui.pages['sfw.csiConfiguration'] = sfw.csiConfiguration;
        sfw.csiConfiguration.init = function() {
            this.id = 'sfw.csiConfiguration';
            this.menu = {
                text: 'Configuration',
                iconCls: 'x-fa fa-cog fa-lg',
                expanded: false,
                selectable: false,
                singleClickExpand: true
            };
            this.type = 'folder';
            this.skipRoleAccessCheck = true;
        };

        //Settings
        sfw.csiSettings = {};
        sfw.ui.pages['sfw.csiSettings'] = sfw.csiSettings;
        sfw.csiSettings.init = function() {
            this.id = 'sfw.csiSettings';
            this.parent = 'sfw.csiConfiguration';
            this.menu = { text: "Settings" };
            this.skipRoleAccessCheck = true;
            this.description = 'Configure various settings within the Software Vulnerability Manager.';
        };

        //Log Messages
        sfw.csiLogger = {};
        sfw.csiLogger.loggerData = [];
        sfw.ui.pages['sfw.csiLogger'] = sfw.csiLogger;
        sfw.csiLogger.init = function() {
            this.id = 'sfw.csiLogger';
            this.parent = 'sfw.csiConfiguration';
            this.menu = {
                text: 'Log Messages'
            };
            this.skipRoleAccessCheck = true;
            this.description = 'The logger is a utility that can be used to detect and fix UI problems.';
        };

        //Activity Log
        sfw.CSIActivityLog = {};
        sfw.ui.pages['sfw.CSIActivityLog'] = sfw.CSIActivityLog;
        sfw.CSIActivityLog.init = function() {
            this.id = 'sfw.CSIActivityLog';
            this.parent = 'sfw.csiConfiguration';
            this.skipRoleAccessCheck = true; //todo
            this.menu = {
                //text: 'Activity Log'
                text: sfw.util.SharedFunctions.addNewFlagToText( 'Activity Log' )
                // TODO - remove after some date when it is no longer considered 'new'
            };
            this.description = 'Displays information about all user activity within Software Vulnerability Manager.';
        };

        //Software Suggestion

        sfw.csiSoftwareSuggestion = {};
        sfw.ui.pages['sfw.csiSoftwareSuggestion'] = sfw.csiSoftwareSuggestion;
        sfw.csiSoftwareSuggestion.init = function() {
            this.id = 'sfw.csiSoftwareSuggestion';
            this.parent = 'sfw.csiConfiguration';
            this.menu = {
                text: 'Software Suggestions'
            };
            this.skipRoleAccessCheck = true;
            this.description = 'Details about software sent for including in Flexera File Signatures database.';
        };

        //Security
        sfw.csiPassword = {};
        sfw.ui.pages['sfw.csiPassword'] = sfw.csiPassword;
        sfw.csiPassword.init = function() {
            this.id = 'sfw.csiPassword';
            this.parent = 'sfw.csiConfiguration';
            this.menu = {
                text: 'Security',
                singleClickExpand: true,
                expanded: true,
                cls: 'sub-folder'
            };
            this.type = 'folder';
            this.isVisible = function() {
                //AUTH_TYPE_SSO = 7;
                return sfw.util.Auth.LoginDetails.account.authType != 7;
            };
        }

        //children of Security
        //1 - Change Password
        sfw.csiResetPassword = {};
        sfw.ui.pages['sfw.csiResetPassword'] = sfw.csiResetPassword;
        sfw.csiResetPassword.init = function() {
            this.id = 'sfw.csiResetPassword';
            this.parent = 'sfw.csiPassword';
            this.menu = { text: "Change Password" };
            this.skipRoleAccessCheck = true;
            this.description = 'Change the Flexera login password for ' + ( sfw.util.Auth.LoginDetails.loginAccountUsername ? ('the user account: <b>' + sfw.util.Auth.LoginDetails.loginAccountUsername +  '</b>') : 'the logged in user' ) + '.';
        };

        //children of Security
        //2 - Password Recovery Settings
        sfw.csiPasswordRecovery = {};
        sfw.ui.pages['sfw.csiPasswordRecovery'] = sfw.csiPasswordRecovery;
        sfw.csiPasswordRecovery.init = function() {
            this.id = 'sfw.csiPasswordRecovery';
            this.parent = 'sfw.csiPassword';
            this.menu = {text: '<div data-qtip="Password Recovery Settings">Password Recovery</div>'};
            this.skipRoleAccessCheck = true;
            this.description = 'Use this window to verify your email address '+(sfw.util.Auth.LoginDetails.isSMSEnabled?'and mobile number ':'')+'to be used for password recovery. If your password is lost, you can reset it at login using your verified email address'+sfw.util.Auth.LoginDetails.isSMSEnabled?'and mobile number.':'';
        };

    },

    constructor: function () {
        this.initMenuFolders();
    },


    /**
     * Call the create() or init() of each object beying loaded in the page array
     * @param rawPageArray
     *	Array of UI pages to be initialized
     * @return pageArray return the initialized items
     */
    buildPageArray: function(rawPageArray) {
        const me = this;
        try {
            var key = '';
            /*if ( typeof sfw.configuration.splashScreen !== "undefined" ) {
                sfw.configuration.splashScreen.init( rawPageArray );
            }*/
            var pageArray = [];
            for ( key in rawPageArray ) {
                try {
                    var ok = true;
                    if ( typeof rawPageArray[key].modules !== "undefined" ) {
                        if ( me.checkModules( rawPageArray[key].modules ) === false ) {
                            ok = false;
                        }
                    }

                    // From more complex user permissions and roles
                    // use the isVisible function
                    if ( ok && Ext.isFunction(rawPageArray[key].isVisible)) {
                        ok = rawPageArray[key].isVisible();
                    }

                    if ( ok ) {
                        var start = new Date();

                        if (Ext.isFunction(rawPageArray[key].create)) {
                            pageArray.push( rawPageArray[key].create() );
                        } else if (Ext.isFunction(rawPageArray[key].init )) {
                            rawPageArray[key].init();
                        }
                        var finish = new Date();
                        var executionTime = finish.getTime() - start.getTime();
                        if ( executionTime > 100 ) {
                            var message = 'Slow initialize in sfw.util.buildPageArray for ' + executionTime / 1000 + 's : ' + key;
                            sfw.util.Debug.log( message );
                        }
                    }
                } catch( ex ) {
                    sfw.util.Debug.log( 'Error while createing page, id: ' + key );
                }
            }
        } catch ( ex ) {
            sfw.util.Debug.trigger( ex, "util.buildPageArray", key );
        }

        return pageArray;
    },


    /**
     * Construct menu items
     * <ul>
     * <li>Prior to CSI7, we didn't build the menuItems that we didn't have permissions for.</li>
     * <li>In CSI7 we change this so all menuItems are built and the ones we lack permissions
     * for are hidden. We are then able to alter the menu afterwards</li>
     * </ul>
     * @param {Object} rawPages
     *	Array of UI pages to be loaded. The function will test for 'object.menu' and 'object.parent'
     * @return {Array} menuArray array of tree nodes
     */
    buildMenuArray: function( rawPages ) {
        var me = this,
            ok = true, // If true, menuItem is included in the menu
            key = '',
            hidden = false; // If true, the menuItem is hidden visually from the user

        var menuArray = [],
            LoginDetails = sfw.util.Auth.LoginDetails;

        for (key in rawPages) {

            if (!Ext.isDefined(rawPages[key].parent) && Ext.isDefined(rawPages[key].menu)) {
                ok = true;
                hidden = false; // by default all menu items are visible

                // Check if we have the Module permissions for the menu item
                if (Ext.isDefined(rawPages[key].modules)) {
                    if (me.checkModules( rawPages[key].modules ) !== true ) {
                        if (!Ext.isDefined(sfw.Globals.CSIVersion)) {
                            ok = false; // VIM & CSI 6
                        } else {
                            hidden = true; // CSI 7+
                        }
                    }
                }

                // Hide menu sections that are disallowed by Role restrictions
                if (Ext.isDefined(LoginDetails)) { // CSI only
                    hidden = ! LoginDetails.account.hasRolesForPage( key ); // The ! is intentional
                }

                if ( ok ) {
                    var menuItem = rawPages[key].menu;
                    menuItem.hidden = hidden; // set the menu item's hidden state
                    var tempChildren = me.buildChildren( rawPages, key );
                    if ( tempChildren.length !== 0 ) {
                        menuItem.leaf = false;
                        menuItem.children = tempChildren;
                    } else {
                        if(!menuItem.viewType) {
                            menuItem.viewType = key;
                        }
                        menuItem.leaf = true;
                    }
                    menuItem.id = key;
                    menuArray.push(menuItem);
                }
            }
        }

        /// Fire an event to notify listeners that the menu was been built or rebuilt
        /// This is called in the tail end of {@link sfw.util#buildMenuArray} because buildMenuArray() is
        ///	called by both {@link sfw.ui#create} and {@link sfw.csiSmartGroupsConfigured#reloadPages} and in CSI 6's Shadow User login.
        // TODO - see comment in ui.js - how to streamline now that shadows aren't relevant?
        //sfw.events.fireEvent( "afterbuildmenuarray", menuArray );
        return menuArray;
    },

    /**
     * Check if at least one module in an array of potential modules is enabled or not. Requires the common global
     * file modules.js to be loaded.
     * @param modules
     *	Array of modules
     * @return
     *	Boolean true if at least one module is enabled, otherwise false. The UI object will decide how to proceed based
     * on this response.
     */
    checkModules: function( modules ) {
        var i;
        if ( typeof modules !== "undefined" )  {
            for ( i = 0; i < modules.length; i++ ) {
                if ( typeof sfw.modules.enabled[modules[i]] !== "undefined" && sfw.modules.enabled[modules[i]].enabled === true ) {
                    return true;
                }
            }
        }

        return false;
    },

    /**
     * @method sfw.ui.menuChange()
     *	- Switches to the selected page
     *	- Switches the page description
     * @public
     * @param {String} pageId
     */
    menuChange: function( pageId ) {
        // Record the ID of the menu item which was clicked. This is used
        // for the Help system
        sfw.ui.activeMenuItem = pageId;

        var currentPage = sfw.ui.pages[pageId];
        if ( !currentPage ) {
            return false;
        }
        // else...
        var title = currentPage.title;

        //var switched = sfw.util.switchPage( sfw.ui.center, currentPage );

        // Also change the description panel:
        //	1.	If variable set in the configuration file
        //	2.	If the page was also successfully switched

        //FIXME - add description on bottom when page change

        /*if ( sfw.configuration.ui.displayDescription && switched ) {
            var currentPageDescription = false;
            try {
                currentPageDescription = currentPage.getDescription();
            } catch ( ex ) {
                sfw.debug.trigger( ex, 'sfw.ui.menuChange()', 'Unable to get the current page description' );
            }
            if ( currentPageDescription && title ) {
                sfw.util.switchPageDescription( sfw.ui.description, currentPageDescription, title);
            } else {
                sfw.util.switchPageDescription( sfw.ui.description, sfw.ui.getDefaultDescription(), sfw.ui.defaultDescriptionTitle );
            }
        }*/
        return true;
    },

    buildChildren: function( rawPages, parentName ) {
        var me = this,
            childArray = [],
            childrenCount = 0,
            defaultDescriptionText = 'No Description Available.',
            key;

        for ( key in rawPages ) {

            if ( rawPages[key].parent != parentName ) {
                if (! sfw.isSccmPlugin && key == 'sfw.csiDashboard') {
                    rawPages[key].menu.qtip = rawPages[key].description;
                }
                continue;
            }

            var ok = true;
            if ( typeof rawPages[key].modules !== "undefined" ) {
                if ( me.checkModules( rawPages[key].modules ) === false ) {
                    ok = false;
                }
            }
            if ( ok && Ext.isFunction(rawPages[key].isVisible)) {
                ok = rawPages[key].isVisible();
            }

            if ( ok ) {
                if ( rawPages[key] ) {
                    var menuItem = rawPages[key].menu;
                    if ( rawPages[parentName] ) {
                        // This test above fixes an exception we hit when in the csiConfiguredSmartGroups dynamically created menu item - todo - is this sufficient / optimal, or does it make more sense to create that menu item in a way that matches our other menu items so we don't need this check? I suspect this is sufficient...
                        menuItem.hidden = false; //rawPages[parentName].menu.hidden; // inherit the parent's hidden state
                        if (Ext.isDefined(menuItem)) {
                            var tempChildren = me.buildChildren( rawPages, key );
                            if ( tempChildren.length !== 0 ) {
                                menuItem.leaf = false;
                                menuItem.children = tempChildren;
                            } else {
                                menuItem.leaf = true;
                                if ( sfw.configuration.ui.displayDescriptionOnHover ) {
                                    var description = '';
                                    if (Ext.isDefined(rawPages[key].description)) {
                                        description = rawPages[key].description;
                                    } else if (Ext.isFunction(rawPages[key].getDescription)) {
                                        description = rawPages[key].getDescription().html;
                                    } else {
                                        description = defaultDescriptionText;
                                    }
                                    const isTooltipped = (menuItem.text || '').indexOf('qtip');
                                    if (isTooltipped == -1) {
                                        const tipFormat = '<span data-qtip="{0}" data-qtitle="{1}">{1}</span>';
                                        menuItem.text = Ext.String.format(tipFormat, description, menuItem.text);
                                    }
                                }
                                // menuItem.qtitle = menuItem.text;
                                // menuItem.qshowDelay = 1000;
                                if (!menuItem.viewType) {
                                    menuItem.viewType = key;
                                }
                            }
                            // Temporarily removing the icons
                            if ( !menuItem.cls ) {
                                menuItem.cls = 'x-tree-noicon';
                            }
                            menuItem.id = rawPages[key].id;

                            childArray.push( menuItem );
                            childrenCount++;
                        }
                    }
                }
            }
        }

        return childArray;
    },


    //------

    afterBuildMenuArray: function (menuArray) {
        var me = this,
            LoginDetails = sfw.util.Auth.LoginDetails;

        // Show the menu items for the enabled Role Sections
        Ext.iterate( LoginDetails.account.roleSections, function ( menuId ) {
            // Gets the index of the root menuItem whose id == menuId
            var rootIndex = Ext.each( menuArray, function ( v ) {
                return v.id !== menuId;
            });
            // If the section is a root then it was handled in buildMenuArray()
            if ( "number" !== typeof rootIndex ) {
                me.unhideMenuSection( menuArray, menuId );
            }
        });

        //console.log(menuArray);
    },

    /**
     * @function
     * Unhides a menuItem and the menuItems above it which are required to access the menuItem.
     * This is called in the Ext.iterate() below
     * @param {Object} menuArray
     * @param {String} menuId
     *	The menu item's ID
     * @return {Number}
     *	Returns the key of the root node it found the menuId in if the menuId was found
     *	otherwise it returns undefined, indicating that the menuId was not found.
     */
    unhideMenuSection: function ( menuArray, menuId ) {
        var me = this;
        return Ext.each( menuArray, function ( menuItem ) {
            if ( menuItem.id === menuId ) {
                menuItem.hidden = false;
                me.unhideMenuChildren( menuItem );
                return false;
            }
            if ( "undefined" !== typeof menuItem.children ) {
                var index = me.unhideMenuSection( menuItem.children, menuId );
                if ( "number" === typeof index ) {
                    menuItem.hidden = false;
                    return false;
                }
            }
        });
    },

    /**
     * @function
     * Unhides all Children of a menuItem. This is called in unhideMenuSection()
     * @param {Object} menuArray
     */
    unhideMenuChildren: function ( menuArray ) {
        var me = this;

        if ( "undefined" === typeof menuArray.children ) {
            return;
        }
        Ext.each( menuArray.children, function ( menuItem ) {
            menuItem.hidden = false;
            if ( "undefined" !== typeof menuItem.children ) {
                me.unhideMenuChildren( menuItem );
            }
        });
    }



});
