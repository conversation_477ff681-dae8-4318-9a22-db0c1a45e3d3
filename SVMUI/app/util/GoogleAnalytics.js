Ext.define('sfw.util.GoogleAnalytics', {
    singleton: true,
    alternateClassName: 'sfw.googleAnalytics',

    constructor : function(){
        // create a new script element
        var script = document.createElement('script');
        script.src = 'https://www.googletagmanager.com/gtag/js?id=G-VN2FMCK3QQ';
        script.async = true;

        // insert the script element into the document
        var firstScript = document.getElementsByTagName('script')[0];
        firstScript.parentNode.insertBefore(script, firstScript);

        window.dataLayer = window.dataLayer || [];
    },

    googleAnalytics : {
        property: 'G-VN2FMCK3QQ',
        optout: function() {
            document.cookie = 'ga-disable-' + this.property + '=true; expires=Thu, 31 Dec 2099 23:59:59 UTC; path=/';
        },
        send: function( url, userId ) {
            function gtag(){
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', this.property, {
                'page_title': 'SVM',
                'anonymize_ip':true,
                'user_id': userId,
                'page_location':document.location.pathname,
                'page_path': url
            });
        }
    },
});

Ext.override( Ext.Window, {
    beforeShow: function() {
        if((this.xtype !== 'messagebox') && (sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION)){
            sfw.googleAnalytics.googleAnalytics.send(this.title,LoginDetails.account.settings.cst_id);
        }
    },
});