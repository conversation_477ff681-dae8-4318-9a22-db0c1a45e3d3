Ext.define('sfw.util.External', {
    singleton: true,

    initSystemVars: function () {
        sfw = sfw || {};
        sfw.external = sfw.external || {};

        var that = this;

        try {
            var uid = window.external.fWUIGetUserID();
            sfw.external = window.external;
            sfw.external.fWUILogMessage('Detected application is running in the console');
            sfw.isBrowser = false;
            sfw.isDesktop = true;
            sfw.isConsole = true;
        } catch ( ex ) {
            try {
                var isServerGood = false;
                var servers = document.getElementsByName('CSIPlugin');
                for ( var i=0 ; i<servers.length && !isServerGood ; ++i ) {
                    try {
                        sfw.external = servers[i].GetServerObject();
                        isServerGood = true;
                    } catch ( ex ) {
                        // do nothing
                    }
                }
                if ( ! isServerGood ) {
                    throw {};
                }
                try {
                    sfw.external.fWUILogMessage('Detected SVM is running with SVM plugin');
                } catch ( ex ) {
                    sfw.external = { error: sfw.external };
                    throw ex;
                }
                sfw.isBrowser = false;
                sfw.isDesktop = true;
                sfw.isConsole = false;
                sfw.external.fWUISetLogging(2);
                var setCallback = true;
                try {
                    setCallback = ! sfw.external.fWUIHasCallback();
                } catch ( ex ) {
                    // do nothing
                }
                if ( setCallback ) {
                    var hnd = that;
                    var invoker = function(mth,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p) {
                        try {
                            return hnd[mth].call(hnd,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p);
                        } catch ( ex ) {
                            sfw.external.fWUILogMessage('Error in callback: '+ex.message);
                        }
                    }
                    try {
                        sfw.external.fWUILogMessage('Registering callback for plugin');
                        sfw.external.fWUIRegisterCallBack(invoker);
                        sfw.external.fWUILogMessage('Plugin callback successfully registered');
                    } catch ( ex ) {
                        sfw.external.fWUILogMessage('Failed to registered plugin callback');
                    }
                }
            } catch ( ex ) {
                sfw.external = sfw.external || {};
                sfw.isBrowser = true;
                sfw.isDesktop = false;
                sfw.isConsole = false;
            }
        }

    }

});