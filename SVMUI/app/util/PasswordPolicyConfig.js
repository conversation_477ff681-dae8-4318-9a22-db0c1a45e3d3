Ext.define('sfw.util.PasswordPolicyConfig', {
    singleton: true,

    // The following is used in places we return the policy data from the
    // backend and want to show it. It is only called outside of this page.
    // We return an array of [display (boolean), policy (html formatted string)]
    showPolicy: function (data) {
        const min_length = data[ 'min_length' ][ 'option_value' ],
            min_changes = data[ 'min_changes' ][ 'option_value' ],
            min_numerics = data[ 'min_numerics' ][ 'option_value' ],
            max_days = data[ 'max_days' ][ 'option_value' ],
            must_have_mixed_case = data[ 'must_have_mixed_case' ][ 'option_value' ];

        let policy = "<p>Password must confirm to the following policy:<br />";
        let display = false;

        if ( 0 < parseInt(min_length) ) {
            policy += "- Be at least " + min_length + " characters long.<br />";
            display = true;
        }

        if ( 0 < parseInt(min_numerics) ) {
            policy += "- Contain at least " + min_numerics + " digit(s).<br />";
            display = true;
        }

        if ( 0 < parseInt(must_have_mixed_case) ) {
            policy += "- Contain at least one lower case, one upper case and at least one special character.<br />";
            display = true;
        }

        if ( 0 < parseInt(min_changes) ) {
            policy += "- One cannot reuse a password for at least " + min_changes + " change(s).<br />";
            display = true;
        }

        if ( 0 < parseInt(max_days) ) {
            policy += "- Passwords must be changed every " + max_days + " days.<br />";
            display = true;
        }
        policy += "<br /></p>";

        return [ display, policy ];
    }

});