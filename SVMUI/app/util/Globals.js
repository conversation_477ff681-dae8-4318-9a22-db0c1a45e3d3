Ext.define('sfw.util.Globals', {

    singleton :true,
    alternateClassName: 'sfw.Globals',

    DATELONGHUMANOUTPUT: 'Y-m-d H:i',

    WARNUSERCREATEUPDATEERRORTHRESHOLD:21,

    WARNUSERCREATEUPDATEWARNINGTHRESHOLD:7,

    DATELONGOUTPUT : 'jS M, Y H:i',

    DATESHORTOUTPUT : 'jS M, Y',

    HOURSMINUTESOUTPUT : 'H:i',

    CSIVersion: null,
    CSIAVersion : null,
    CSIBackendVersion: null,
    CSIAWarningVersion: null,
    UserID: null,
    masterAccountUsername: 'Your Account',
    ca_domain: '',

    vimUrl: "https://ca.secunia.com/vim31/",
    vimTab: 3,
    vimTabEnabled: false,

    // CSIA Agent Management Version Thresholds (strictly less than comparison, so we can set the warning version to be the current version, unless we specifically want to flag more than one recent version as "green")
    CSIAWarningVersion: this.CSIAVersion,

    // Agents with no recorded version numbers are counted under CSIAErrorVersion
    CSIAErrorVersion: '7.0.0.0', // CSI 7 requires 7 agents

    // Manuals, FAQ's, etc
    CSIFAQLink: 'http://secunia.com/products/corporate/CSI/faq/',

    CSIRequirementsLink: 'http://secunia.com/vulnerability_scanning/corporate/system_requirements/',

    // DB Read Only
    readonly: false,

    // Paths
    api_path: '',

    CSITerminologyLink: 'http://secunia.com/community/advisories/terminology/',
    // Note, if changing, these are in 2 places. Here for the usual CSI, and in csi_static_dashboard_setup for the static dash.

    // Limit for when 'auto-interface-refresh' gets disabled
    lastInterfaceRefresh: sfw.util.Util.dateCreate(),

    // Numbers
    numScanPaths: 0,
    firstUpdateMenu: true,

    // Dates
    dateLongOutput: 'jS M, Y H:i',
    dateShortOutput: 'jS M, Y',
    dateDayMonth: 'jS M',
    dateLongInput: 'Y-m-d G:i:s',
    dateLongHumanInput: 'Y-m-d G:i',
    dateLongHumanOutput: 'Y-m-d H:i',
    dateShortInput: 'Y-m-d',
    hoursMinutesOutput: 'H:i',
    dateInvalid: '0000-00-00 00:00:00',
    sqlLiteDate: 'Y-m-d H:i:s',

    // The index of the main UI tab
    mainTabIndex: 1,
    splashScreenTabIndex: 0,

    blurTime: sfw.util.Util.dateCreate(),
    isBlurred: false,
    uiReloadInterval: 14400000, // 4 hours

    // Status bar default text
    statusBarDefault: 'Interface loaded on: ' + sfw.util.Util.dateCreate( new Date() ), //.format( this.dateLongOutput ),
    currentStatusMessage: this.statusBarDefault,

    overruleStatusMessage: null,

    // Warn the user if creating an update based on a scan older than the specified threshold in days
    warnUserCreateUpdateWarningThreshold: 7,
    warnUserCreateUpdateErrorThreshold: 21,

    architecture32Mask: 0x01,
    architecture64Mask: 0x02,

    // fetch the scan settings
    // defaults to 5 if no options has been previously configured
    numberOfScanThreads: 5,

    constructor: function () {
        this.init();
    },

    init: function() {
        var defaults = {}; // FIXME
        if (defaults && typeof defaults.options !== 'undefined') {
            // The Daemon will not execute this as the default var isn't available at this point of execution;
            this.numberOfScanThreads = parseInt( defaults.options.get( 'number_of_scan_threads', this.numberOfScanThreads ), 10 );
        }
    },

    /**
     * @method
     * Returns the base URL for our API calls.
     * @public
     * @todo TODO replace the uid with the CSRF gtoken
     * @return {String}
     *	The returned URL includes a query string so if you are appending your own
     *	parameters to the returned string omit the question mark separator and
     *	ensure your strings starts with an ampersand.
     */
    //Removed the uid part from below function for CSIL-9968
    apiPath: function() {
        const LoginDetails = sfw.util.Auth.LoginDetails;
        const ret = ( LoginDetails && LoginDetails.caDomain && LoginDetails.publicPath
            ? LoginDetails.caDomain + LoginDetails.publicPath
            : '' );
        return ret + 'api/?';
    },



    /**
     * @method
     * Clears the Account's API credentials that were set by
     * {@link globals#saveAPIcredentials()}. If a call to this method is followed by
     * an interface reload then the Account is logged out.
     * @public
     */
    clearAPIcredentials: function () {
        this.UserID = '';
        var expiredDate = new Date(0); // 1970
        //CSIL-9968-commented setcookie for uid
        //defaults.setCookie( 'uid', '', expiredDate );
        // This 'check' cookie only exists when we've logged in successfully.
        // If found it indicates we will check if we're logged in via a checklogin
        // request. If not found we ask for a username/password and password again.
        sfw.util.Default.setCookie( 'check', '', expiredDate );
        if ( sfw.isDesktop ) {
            //sfw.external.fWUIClearUserID();
        }
    },

    /**
     * @method
     * Stores the Account's API credentials which results in setting persistent
     * login until {@link globals@clearAPIcredentials()} is called.
     * @public
     * @param {String} uid
     */
    saveAPIcredentials: function( uid ) {
        if ( !uid ) {
            return;
        }
        this.UserID = uid;
        //CSIL-9968-commented setcookie for uid
        //defaults.setCookie( 'uid' , uid );
        sfw.util.Default.setClientTime();
        if ( sfw.isDesktop ) {
            // A valid UserID is either 50 or 64 chars in length
            //sfw.external.fWUISetUserID( uid ); //FIXME

        }
    },

    /**
     * @method
     * Set Host URL for ActiveX, SCCM and Daemon
     * @public
     * @param {String} Host
     */
    saveHostUrl: function( host ) {
        if ( !host ) {
            return;
        }
        if ( sfw.isDesktop ) {
            sfw.external.fWUISetServerHost( host );

        }
    },

    text: {
        sccmNoun: 'System Center'
    }

});
