Ext.define('sfw.util.Auth', {
    singleton: true,

    loginWindow: {},

    LoginDetails: {},

    constants : {
        LOGIN_SUCCESSFUL: 1
        ,LOGIN_CHANGEPASSWORD: 8 // login success + show change password form
        ,ERROR_INVALID_CREDENTIALS: 2
        ,ERROR_CSI_VERSION_MISMATCH: 5
        ,ERROR_NO_LICENSES: 3
        ,ERROR_PASSWORD_POLICY: 6
        ,ERROR_ACCOUNT_EXPIRY: 7
        ,ERROR_ONE_TIME_PASSWORD: 8
        ,ERROR_UPGRADE_IN_PROGRESS: 102 // from Upgrade (100 + 2)
        ,SSO_ERROR: 15
    },

    constructor: function () {
        //this.requestAuthenticationPin = Ext.bind(this.requestAuthenticationPin, this);
    },

    init: function () {

    },

    createMask: function (message) {
        this.mask = new Ext.LoadMask({msg: message || 'Please wait...', target: sfw.app.getMainView() });
        this.mask.show();
    },

    showMask: function () {
        if(this.mask) {
            this.mask.show();
        }
    },

    hideMask: function () {
        if(this.mask) {
            this.mask.hide();
        }
    },

    isLoggedIn: function () {
        const check = sfw.util.Default.getCookie( "check" ),
            userId = sfw.util.Globals.UserID;

        //console.log('isLoggedIn [check] >> ' + check);
        //console.log('isLoggedIn [userId] >> ' + userId);

        return "1" === check;
    },

    checkLogin: function () {
        const me = this,
            defaults = sfw.util.Default,
            globals = sfw.util.Globals,
            deferred = new Ext.Deferred();

        // This 'check' cookie only exists when we've logged in successfully.
        // If found it indicates we will check if we're logged in via a checklogin
        // request. If not found we ask for a username/password and password again.
        if (defaults.getCookie( "sp_login" ) && !defaults.getCookie( "check" )) {

            me.switchLoginWindows('sso-login', 350, 295); //FIXME
        }
        if ( "1" !== defaults.getCookie( "check" ) ) {
            // No valid login was found so show the login form
            //me.hideMask();
            //this.interface.show();
            try {
                //sfw.external.fWUISetActiveTab( globals.mainTabIndex );
            } catch( ex ) {
                /* The SCCM Plugin throws an exception here catch and proceed */
            }
            return;
        }

        me.showMask();

        Ext.Ajax.request({
            url: 'action=checklogin',
            success: function (answer, opts){
                const response = Ext.decode(answer.responseText, true);
                if(!response) {
                    me.hideMask();
                    Ext.Msg.show({
                        title: "Error",
                        msg: "Unknown error: invalid server response. Please try again later",
                        buttons: Ext.Msg.OK,
                        fn: function() {
                            //sfw.loginWindow.interface.show(); //FIXME
                            //console.log('interface show');
                        }
                    });
                    sfw.util.Globals.clearAPIcredentials(); //FIXME
                    return;
                }
                if(response.success) {
                    sfw.util.Debug.log( 'Login Successful.' ); //REMOVE it later
                    if ( defaults.getCookie( "sp_login" ) === 1 && defaults.getCookie( "check" ) === 1 ) {
                        sfw.util.ActivityLog.log( 1, 0, "SP initiated single sign-on (Software Vulnerability Manager)", 19, true );
                    }
                    if (response.terms && response.terms.enabled == true && response.terms.accepted != true ){
                        me.hideMask();
                        //tosWindowShow( response ); //FIXME
                        return true;
                    }
                    me.doLogin( response );
                    return true;
                } else {
                    if ( defaults.getCookie( "sp_login" ) == 1 && defaults.getCookie( "check" ) == 1 ) {
                        sfw.util.ActivityLog.log( 1, 1, "SP initiated single sign-on (Software Vulnerability Manager)", 19, true );
                    }
                    globals.clearAPIcredentials();
                    me.checkLogin();
                    return false;
                }
            },
            failure: function (response, opts) {
                // If the checklogin request fails due to internet outage or server problem then Logout the Account
                globals.clearAPIcredentials();
                // Restart the login process
                me.checkLogin();
            }
        });

        return deferred;
    },

    doLogin: function(response) {
        const me = this,
            defaults = sfw.util.Default,
            globals = sfw.util.Globals,
            LoginDetails = me.LoginDetails,
            deferred = new Ext.Deferred();

        // Pass the cookie to the Plugin so it has a fresh copy
        if ( sfw.isDesktop || sfw.isConsole ) {
            var cookieLog = "";
            try {
                // To work in both dev and prod we have two sids to check.
                //CSIL-8967 getting cookies 'sid' & 'vid' by response
                //var sidCookie = defaults.getCookie( String.fromCharCode(80) + "HPSE" + String.fromCharCode(83) + "SID" ) || defaults.getCookie( "sid" );
                var sidCookie = response.sid;
                cookieLog = "sid-type: " + typeof sidCookie;
                //sfw.external.fWUISetHeader( "sid", sidCookie );
                cookieLog = "sid set successfully";
                // Verification token
                //var vidCookie = defaults.getCookie( "vid" );
                var vidCookie = response.vid;
                cookieLog = "vid-type: " + typeof vidCookie;
                //sfw.external.fWUISetHeader( "vid", vidCookie );
            } catch ( ex ) {
                sfw.util.Debug.log( "Failed to pass cookie to the plugin. Plugin-based functionality may not work: " + ex.message );
                sfw.util.Debug.log( cookieLog );
            }
        }

        /*
			 * Login the Account.
			 * The CSRF token that is sent along with all of our API requests.
			 * We use uid because the infrastructure to send it along with all
			 * requests is in place.
			 */
        globals.saveAPIcredentials( response.uid );

        /*
         * Added API Setting server hosts for ActiveX,SCCM and Daemon
         */
        globals.saveHostUrl( response.host );

        // If the Account must change it's password then Logout the Account and show the Login Windows again
        if ( me.constants.LOGIN_CHANGEPASSWORD === response.response ) {
            // Logout the Account
            globals.clearAPIcredentials();
            // Show Login Window
            me.hideMask();
            const loginView = sfw.common.SfwAccessor.getLoginView();
            if (loginView) {
                loginView.show();
                loginView.fireEventArgs('login-form', [{formId: 'login-form'}]);
            }

            return;
        }

        // Set the CSI and CSI Agent verions
        globals.CSIVersion = response.CSIVersion;
        // Note - We now set the CSIAWarningVersion in globals.init() to CSIAVersion
        // and use a "<" (strictly less) comparison when showing which versions to flag
        // as "old but still ok". Update this if we want the logic to change to support multiple "newish" versions
        // TODO - could use multiple Version strings if we want/need to separate win/mac and RHEL - for now we just set them all to the same (current) value.
        globals.CSIAVersion = response.CSIAVersion;

        globals.CSIBackendVersion = response.CSIBackendVersion;

        // CSIA Agent Management Version Thresholds (strictly less than comparison, so we can set the warning version to be the current version, unless we specifically want to flag more than one recent version as "green")
        globals.CSIAWarningVersion = globals.CSIAVersion;

        LoginDetails.isReadOnly = response.is_read_only;
        LoginDetails.isAuthenticated = true;
        LoginDetails.isPasswordReset = response.isPasswordReset;
        LoginDetails.isPatchPublisherInstalled = response.isPatchPublisherInstalled;
        LoginDetails.loginAccountUsername = response.loginAccountUsername;
        LoginDetails.accountExpires = response.accountExpires;
        LoginDetails.caDomain = response.caDomain;
        LoginDetails.agentUID = response.agentUID;
        LoginDetails.host = response.host;
        LoginDetails.port = parseInt( response.port, 10 );
        // agent_host and agent_port are used by csi_sps_wizard.js for creating Agent Deployment Packages
        LoginDetails.agent_host = response.agent_host;
        LoginDetails.agent_port = parseInt( response.agent_port, 10 );
        LoginDetails.publicPath = response.publicPath;
        LoginDetails.loginAccountEmail = response.loginAccountEmail;
        LoginDetails.isBaseAccount = response.isBaseAccount;
        LoginDetails.isPartitionAdmin = response.isPartitionAdmin;
        LoginDetails.isAdmin = ( response.isAdmin || response.isPartitionAdmin );
        LoginDetails.loginAccountId = parseInt( response.accountId, 10 );
        LoginDetails.rootAccountId = parseInt( response.rootAccountId, 10 );
        LoginDetails.isSMSEnabled = response.isSMSEnabled;
        LoginDetails.checkMissingMsUpdates = response.checkMissingMsUpdates;
        LoginDetails.timestampSetting = response.timestampSetting;
        LoginDetails.oldagentdetection = response.oldagentdetection;

        LoginDetails.pathdetectionstatus = response.pathdetectionstatus;
        LoginDetails.currentlogin_ip = response.currentlogin_ip;
        LoginDetails.WMISetting = response.WMISetting;
        LoginDetails.pollingstatusapi = response.pollingstatusapi;
        LoginDetails.checkJavaScanSettings = response.checkJavaScanSettings;
        LoginDetails.checkSoftwareSuggestion = response.checkSoftwareSuggestion;
        LoginDetails.getCheckSiteChecking = response.getCheckSiteChecking;
        LoginDetails.show_clear_scanhash = response.show_clear_scanhash;
        LoginDetails.isThreatModuleEnabled = response.isThreatModuleEnabled;
        LoginDetails.isVPMModuleEnabled = response.isVPMModuleEnabled;
        LoginDetails.ldapSettings = response.ldapSettings;
        LoginDetails.settings = response.settings;
        LoginDetails.ssoGuid = response.ssoGuid;
        LoginDetails.client_state = response.client_state;
        LoginDetails.afterLoginPopupMsg = Ext.util.Format.htmlDecode(response.afterLoginPopupMsg.popup_msg);
        LoginDetails.vid = response.vid;

        //WIP - work in progress
        afterlogin_popuop = new Ext.Window({
            floating: true,
            centered: true,
            modal: true,
            width: 300,
            height: 300,
            name: 'alert_popup',
            title: Ext.util.Format.htmlDecode(response.afterLoginPopupMsg.msg_title),
            styleHtmlContent: true,
            tag : 'blockquote',
            html: LoginDetails.afterLoginPopupMsg,
            bodyStyle : 'padding : 10px;'
            /*listeners : {
                show:function(){
                    setTimeout("afterlogin_popuop.close()",10000);
                }
            }*/
        });

        var isclicked = false;

        Ext.onReady (function(){
            Ext.getBody().on('click',function(){
                if(!isclicked){
                    isclicked = true;
                    if(LoginDetails.afterLoginPopupMsg)
                        afterlogin_popuop.show();
                }
            });
        });

        // Give the settings object a get method so existing code that treats it as a row won't require changing
        if ( "undefined" !== typeof response.settings ) {
            response.settings.get = function( val ) {
                return this[val];
            }
        }
        // Give the configuration object a get method so existing code that treats it as a row won't require changing
        if ( "undefined" !== typeof response.configuration ) {
            defaults.options = Ext.create('sfw.util.DefaultOptions', { options: response.configuration });
            defaults.options = response.configuration;
            response.configuration.get = function( val ) {
                return this[val];
            };
            response.configuration.set = function( key, val ) {
                this[key] = val;
            };
        }

        // Create an Account object for the logged in Account
        LoginDetails.account =  Ext.create('sfw.util.Account', {account: response});//new sfw.Account( response );


        // Load the CommonConstants object with the
        // lookup data we need for smartgroups and
        // various related functionality
        sfw.util.CommonConstants.loadSmartGroupConstants();

        // Load the modules
        sfw.modules = response.modules;

        // Load the account options
        sfw.options = response.options;

        me.hideMask();

        //fAjaxBoot(); // Do the CSI specific boot.
        //Fire event
        sfw.common.SfwAccessor.getMainView().fireEventArgs('userloggedin', []);
        //sfw.events.fireEvent( 'user.loggedin', LoginDetails );
    },

    checkSSO: function () {
        const me = this,
            globals = sfw.util.Globals,
            defaults = sfw.util.Default,
            LoginDetails = me.LoginDetails;

        Ext.Ajax.request({
            url: 'action=check_sso&ui=1',
            success: function(action, form) {
                var data = Ext.decode(action.responseText, true);
                if (data) {
                    const response = data.response,
                        reason = data.reason,
                        type = sfw.util.Auth.constants;

                    switch ( response ) {
                        case type.LOGIN_SUCCESSFUL:
                            // This 'check' cookie only exists when we've logged in successfully.
                            // If found we will check if we're logged in via a checklogin request.
                            // If not found we ask for a username/password and password again. This
                            // cookie is cleared in globals.clearAPIcredentials()
                            defaults.setCookie( 'check', '1' );
                            // Set a CSRF Token for the checklogin request
                            globals.UserID = data.uid;
                            // Show main GUI
                            //sfw.loginWindow.interface.hide();
                            me.createMask( 'Please wait...' );
                            LoginDetails.isLoggedIn = true;
                            me.checkLogin();
                            break;
                        case type.SSO_ERROR:
                            // Show main GUI
                            //sfw.loginWindow.interface.show();
                            Ext.Msg.alert( 'Registration failed', defaults.htmlSpecialCharsDecodeAlsoQuot( lReason ) );
                            if ( defaults.getCookie( "sp_login" ) && !defaults.getCookie( "check" )) {
                                const loginVC = sfw.common.SfwAccessor.getLoginVC();
                                if (loginVC) {
                                    loginVC.onSignInFormSwitch('sso-login');
                                }
                                document.cookie = 'sp_login=; expires=' + encodeURIComponent( new Date(0).toUTCString() );
                            }
                            break;
                        default:
                            me.checkLogin();
                    }
                } else {
                    //FIXME - need message here
                    sfw.util.Debug.log( "Failed to get response from check sso " + (ex || {}).message );
                    me.checkLogin();
                }
            },
            failure: function() {
                // Restart the login process
                me.checkLogin();
            }
        });

    }

});