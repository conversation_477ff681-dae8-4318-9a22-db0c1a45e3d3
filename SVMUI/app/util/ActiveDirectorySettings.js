Ext.define('sfw.util.ActiveDirectorySettings', {

    singleton: true,
    alternateClassName: 'sfw.ActiveDirectorySettings',

    /**
     * The fqdn of the main domain controller of the forest.
     * Has to be initialized ASAP if available because it is used
     * with every LDAP query
     * @type {String}
     */
    rootDomain: '',

    /**
     * Constant used on the server side and on the client side to identify the option
     * which holds the LDAP path to scan, if empty it means that the scan should look
     * into the forest
     * @type {String}
     */
    AD_SCAN_PATH: 'AD_SCAN_PATH',

    /**
     * Constant used on the server side and on the client side to identify the option
     * which holds interval option for the AD scan task
     * @type {String}
     */
    AD_SCAN_INTERVAL: 'AD_SCAN_INTERVAL',

    /**
     * Flag used on the server side and on the client side to check if
     * the nETBIOSName is specified manually
     * @type {String}
     */
    AD_IS_MANUAL_NETBIOS: 'AD_IS_MANUAL_NETBIOS',

    /**
     * Value used on the server side and on the client side for the nETBIOSName when
     * the CSI is not able to scan the Partitions table. This is provided manually
     * by the user.
     * @type {String}
     */
    AD_NETBIOS: 'AD_NETBIOS',

    AD_LDAPSURL: 'AD_LDAPSURL',

    /**
     * Constant used on both the server side and client side.
     * Specifies whether Site names in the UI are shown as Distinguished Names (DC=ml,DC=secunia,DC=com) or just Site name.
     * @type {String}
     */
    AD_SITE_SHOW_DISTINGUISHED_NAME: 'AD_SITE_SHOW_DISTINGUISHED_NAME',

    /**
     * Special kind of accesor, for this one we are only interested in the accessor itself
     * no need to execute an queries with this one.
     * @type {ADAccessor}
     */
    rootAccessor: null,


    /**
     * Fetches the value of the AD scan path form the local configuration object
     * @return {String} the AD scan path
     */
    getADPath: function () {
        try {
            // This value previously came from local DB in CSI 6
            var adPath = sfw.util.Auth.LoginDetails.account.configuration[this.AD_SCAN_PATH];
            if (typeof adPath === 'string') {
                return adPath;
            }
        } catch (ex) {
            sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 74', 'No configuration table.');
        }
        return '';
    },

    /**
     * Indicates if the CSI has AD integration enabled
     * @return {Boolean}
     */
    hasADIntegration: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        // This value previously came from local DB in CSI 6

        if (parseInt(LoginDetails.account.settings.ad_integration, 10) === 1) {
            return true;
        }
        return false;
    },

    /**
     * Gets the local option for AD task schedule interval
     * @return {int}
     */
    getADScheduleInterval: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        try {
            // This value previously came from local DB in CSI 6
            var adScheduleInterval = LoginDetails.account.configuration[this.AD_SCAN_INTERVAL];
            if (typeof adScheduleInterval === 'string') {
                return parseInt(adScheduleInterval, 10);
            } else if (typeof adScheduleInterval === 'undefined') {
                return -1;
            }
            return adScheduleInterval;
        } catch (ex) {
            sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 109', 'The configuration table is missing.');
        }
        return -1;
    },

    isLDAPS: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;

        if (LoginDetails.ldapSettings == "1") {
            return true;
        }
        return false;
    },
    setLDAPS: function (value) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        Ext.Ajax.request({
            url:'action=set_ldap_settings&which=set_ldap_settings&value=' + value
            , method: 'GET'
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (data.success) {
                    Ext.Msg.show({
                        title: 'Configuration Saved',
                        msg: 'Configuration for LDAP connection setting saved',
                        buttons: Ext.Msg.OK
                    });
                    return;
                }else{
                    Ext.Msg.show({
                        title: 'Failed to save Configuration',
                        msg: 'Failed to save Configuration for LDAP connection setting',
                        buttons: Ext.Msg.OK
                    });
                    return;
                }
            }
            , failure: function () {
                Ext.Msg.show({
                    title: 'Failed to save Configuration',
                    msg: 'Failed to save Configuration for LDAP connection setting',
                    buttons: Ext.Msg.OK
                });
            }
        });
        LoginDetails.ldapSettings = value ? 1 : 0;
    },

    /**
     * This function gets called to toggle the AD integration on and off.
     * It will execute a callback after it is done.
     * @param {boolean}   value    true for on
     * @param {Object} callback And object containing a function and a context
     */
    setADIntegration: function (value, callback) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=set_ad_integration&'
            , method: 'POST'
            , params: {
                value: value ? 1 : 0
            }
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to switch the sites configuration: ' + data.msg);
                    if (typeof callback !== 'undefined') {
                        callback.fn.call(callback.context, {msg: 'Could not switch AD configuration'});
                    }
                    return;
                }

                LoginDetails.account.settings.ad_integration = (value ? 1 : 0);

                if (typeof callback !== 'undefined') {
                    callback.fn.call(callback.context);
                }
            }
            , failure: function () {
                if (typeof callback !== 'undefined') {
                    callback.fn.call(callback.context, {msg: 'Error while switching AD configuration'});
                }
                sfw.util.Debug.log('Error while trying to switch the sites configuration: ');
            }
        });
    },

    /**
     * This function sets the AD settings. Executes a callback after it finishes. It
     * also tries to set-up the local configuration object by itself for maximum efficiency.
     * @param {boolean}   value    true for on
     * @param {Object} callback And object containing a function and a context
     */
    saveSettings: function (options, callback) {
        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=set_ad_configuration&'
            , method: 'POST'
            , params: {
                adScanPath: options.adScanPath
                , hideEmptyOus: options.hideEmptyOus ? 1 : 0
                , adScanInterval: parseInt(options.adScanInterval, 10)
                , adIsManualNetbios: options.adIsManualNetbios ? 1 : 0
                , adManualNetbios: options.adManualNetbios
                , siteShowDistinguishedName: Number(options.siteShowDistinguishedName)
                , adLdapsURL: options.adLdapsURL
            }
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('[AD]Error while trying to save AD configuration: ' + data.msg);
                    if (typeof callback !== 'undefined') {
                        Ext.Msg.show({
                            title: 'AD Configuration',
                            msg: 'Could not save AD configuration',
                            buttons: Ext.Msg.OK
                        });
                    }
                    return;
                }

                sfw.util.Auth.LoginDetails.account.configuration.AD_SCAN_PATH = options.adScanPath;
                sfw.util.Auth.LoginDetails.account.configuration.AD_SCAN_INTERVAL = parseInt(options.adScanInterval, 10);
                sfw.util.Auth.LoginDetails.account.configuration.AD_IS_MANUAL_NETBIOS = (options.adIsManualNetbios ? 1 : 0);
                sfw.util.Auth.LoginDetails.account.configuration.AD_NETBIOS= options.adManualNetbios;
                sfw.util.Auth.LoginDetails.account.configuration.AD_SITE_SHOW_DISTINGUISHED_NAME = (options.siteShowDistinguishedName ? 1 : 0);
                sfw.util.Auth.LoginDetails.account.settings.ad_hide_empty_ous = (options.hideEmptyOus ? 1 : 0);
                sfw.util.Auth.LoginDetails.account.configuration.AD_LDAPSURL = options.adLdapsURL;

                try {
                    // disabling the scheduled active directory scanning task from csi
                    //sfw.ActiveDirectoryScheduler.startTaskIfNeeded();
                } catch (ex) {
                    sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 208', '[AD]Could not Start AD scan task.');
                }

                if (typeof callback !== 'undefined') {
                    callback.fn.call(callback.context);
                }
            }
            , failure: function () {
                if (typeof callback !== 'undefined') {
                    callback.fn.call(callback.context, {msg: 'Error while saving AD configuration'});
                }
                sfw.util.Debug.log('[AD]Error while trying to save the AD configuration.');
            }
        });
    },

    /**
     * Gets the local setting for showing or hiding the empty OUs in the interface
     * @return {Boolean}
     */
    isHideEmptyOusEnabled: function () {
        if (parseInt(sfw.util.Auth.LoginDetails.account.settings.ad_hide_empty_ous, 10) === 1) {
            return true;
        }
        return false;
    },

    /**
     * Gets the local setting for using a manual value for the nETBIOSName
     * @return {Boolean}
     */
    isManualNetbios: function () {
        try {
            var adIsManualNetbios = sfw.util.Auth.LoginDetails.account.configuration[this.AD_IS_MANUAL_NETBIOS];
            if (typeof adIsManualNetbios === 'string') {
                return parseInt(adIsManualNetbios, 10) === 1 ? true : false;
            }
            return !!adIsManualNetbios;
        } catch (ex) {
            sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 289', 'The configuration table is missing.');
        }
        return false;
    },

    /**
     * Gets the local option for the netBIOS that was manually set
     * @return {string}
     */
    getManualNetbios: function () {
        try {
            // This value previously came from local DB in CSI 6
            var adManualNetbios = sfw.util.Auth.LoginDetails.account.configuration[this.AD_NETBIOS];
            if (typeof adManualNetbios === 'string') {
                return adManualNetbios;
            }
        } catch (ex) {
            sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 309', 'The configuration table is missing.');
        }
        return '';
    },

    getLdapsURL: function () {
        try {
            // This value previously came from local DB in CSI 6
            var adLdapsURL = sfw.util.Auth.LoginDetails.account.configuration[this.AD_LDAPSURL];
            if (Ext.isEmpty(adLdapsURL)) {
                adLdapsURL = '';
                return adLdapsURL;
            } else {
                var adLdapsURL = adLdapsURL.replace("/", "");
                return adLdapsURL + "/";
            }
        } catch (ex) {
            console.log(adLdapsURL);
            //sfw.debug.trigger(ex, 'file: ActiveDirectorySettings.js line: 322', 'The configuration table is missing.');
        }
        return '';
    },

    /**
     * A utility function for splitting FQDNs
     * @param  {String} text      FQDN
     * @param  {String} character The separator
     * @return {Array}           The components as array
     */
    splitAD: function (text, character) {
        var splitDN = [];
        var start = 0;
        while (text.indexOf(character, start) !== -1) {
            var index = text.indexOf(character, start);
            if (index === 0) {
                text = text.substring(0, 0);
            }
            var scanForSlah = true;
            var numberOfSlashes = 0;
            while (scanForSlah) {
                if (
                    text.charAt(index - numberOfSlashes - 1) === '\\'
                    && index - numberOfSlashes - 1 !== 0
                ) {
                    numberOfSlashes++;
                } else {
                    scanForSlah = false;
                }
            }
            if (numberOfSlashes % 2 === 1) {
                start = index + 1;
                continue;
            }
            splitDN.push(text.substring(0, index).replace('\\' + character, character));
            text = text.substring(index + 1);
            start = 0;
        }
        splitDN.push(text.replace('\\' + character, character));
        return splitDN;
    },

    /**
     * Returns a domain controller as a full name. Otherwise, if not Domain Controller returns an empty string
     * @param  {String} dcDistinguishedName FQDN
     * @return {String}
     */
    getContainerName: function (dcDistinguishedName) {
        var components = sfw.ActiveDirectorySettings.splitAD(dcDistinguishedName, ",");
        var response = "";

        for (var i = 0; i < components.length; i++) {
            var keyValuePair = sfw.ActiveDirectorySettings.splitAD(components[i], "=");
            if (keyValuePair.length !== 2) {
                return "";
            }
            if (keyValuePair[0] !== "DC" && keyValuePair[0] !== "OU") {
                return "";
            } else if (response === "") {
                response = keyValuePair[0];
            }
        }
        return response;
    },

    /**
     * Checks weather the AD integration flag has ever been set.
     * It will return true if the flag is set to a value other null.
     * @return {Boolean}
     */
    isADSet: function () {
        var row = null;
        var isSet = false;
        try {
            // This value previously came from local DB in CSI 6
            row = sfw.util.Auth.LoginDetails.account.settings;
        } catch (ex) {
            row = null;
        }
        if (row !== null) {
            isSet =
                typeof row.get('ad_integration') === 'undefined' || row.get('ad_integration') === null
                    ? false
                    : true;
        }
        return isSet;
    },

    scanAllBranches: function () {
        if (this.getADPath().length > 0) {
            return false;
        }
        return true;
    },

    scanSpecificPath: function () {
        if (this.getADPath().length > 0) {
            return true;
        }
        return false;
    },

    isADAvailable: function () {
        if (this.rootDomain.length > 0) {
            return true;
        }
        return false;
    },

    getRootDomain: function () {
        return this.rootDomain;
    },

    setADIntegrationCallback: function(err){
        if ( typeof err === 'object' ) {
            sfw.util.Debug.log( err.msg );
        }

        Ext.ComponentQuery.query('#ad_integration')[0].setDisabled(false);

        if (sfw.ActiveDirectorySettings.hasADIntegration() ) {
            /**
             * While changing the structure, lock the AD UI
             */
            sfw.ActiveDirectorySettings.refresh();
        } else {
            /**
             * refresh the Sites panel. The AD entries were manually removed since they no longer exists.
             */
            /**
             * While changing the structure, lock the AD UI
             */

            Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(true);
            Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled(true);
            Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled(true);
            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
            Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled(true);
            Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled(true);
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled(true);
            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
            Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(true);
            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled(true);
            Ext.ComponentQuery.query('#ad_laps')[0].setDisabled(true);

        }
    },

    refresh : function(){
        Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].suspendEvents();
        Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].suspendEvents();
        Ext.ComponentQuery.query('#ad_integration')[0].suspendEvents();
        Ext.ComponentQuery.query('#ad_scan_options')[0].suspendEvents();
        Ext.ComponentQuery.query('#adScanSpecificPath')[0].suspendEvents();
        Ext.ComponentQuery.query('#adScanPath')[0].suspendEvents();
        Ext.ComponentQuery.query('#ad_frequency_unit')[0].suspendEvents();
        Ext.ComponentQuery.query('#ad_laps')[0].suspendEvents();
        Ext.ComponentQuery.query('#LDAPText')[0].suspendEvents();

        /**
         * After the even listeners have been disabled, change the values as usual
         */
        Ext.ComponentQuery.query('#ad_integration')[0].setValue( sfw.ActiveDirectorySettings.hasADIntegration() );
        Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setValue(sfw.ActiveDirectorySettings.isHideEmptyOusEnabled() );
        Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
        Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setValue( parseInt( sfw.util.Auth.LoginDetails.account.configuration.get( 'AD_SITE_SHOW_DISTINGUISHED_NAME' ), 10 ) );
        Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled( false );
        Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
        Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled( LoginDetails.isReadOnly );
        Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
        Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );

        Ext.ComponentQuery.query('#ad_laps')[0].setDisabled( false );
        if ( sfw.ActiveDirectorySettings.hasADIntegration() && sfw.ActiveDirectorySettings.isLDAPS() ) {
            Ext.ComponentQuery.query('#ad_laps')[0].setValue( true );
            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled( false );
            Ext.ComponentQuery.query('#LDAPText')[0].setValue( sfw.ActiveDirectorySettings.getLdapsURL() );
        }

        if ( sfw.ActiveDirectorySettings.scanSpecificPath() ) {
            Ext.ComponentQuery.query('#adScanSpecificPath')[0].setValue( true );
            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
            Ext.ComponentQuery.query('#adScanPath')[0].setValue(
                sfw.ActiveDirectorySettings.hasADIntegration()
                    ? (
                        sfw.ActiveDirectorySettings.scanSpecificPath()
                            ? (
                                sfw.ActiveDirectorySettings.getADPath().length > 0
                                    ? sfw.ActiveDirectorySettings.getADPath()
                                    : sfw.ActiveDirectorySettings.getRootDomain()
                            )
                            : ''
                    )
                    : ''
            );
            Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
            if ( sfw.ActiveDirectorySettings.isManualNetbios() ) {
                Ext.ComponentQuery.query('#netBiosCheck')[0].setValue( true );
                Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
                Ext.ComponentQuery.query('#netBiosNameText')[0].setValue( sfw.ActiveDirectorySettings.getManualNetbios() );
            } else {
                Ext.ComponentQuery.query('#netBiosCheck')[0].setValue( false );
                Ext.ComponentQuery.query('#netBiosNameText')[0].setValue( '' );
                Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled( true );
            }
        } else {
            Ext.ComponentQuery.query('#ad_scan_options')[0].setValue( true );
            Ext.ComponentQuery.query('#adScanPath')[0].setValue( '' );
            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled( true );
            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled( true );
            Ext.ComponentQuery.query('#netBiosNameText')[0].setValue( '' );
            Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled( true );
            Ext.ComponentQuery.query('#netBiosCheck')[0].setValue( false );
        }

        var scheduleInterval = parseInt( sfw.ActiveDirectorySettings.getADScheduleInterval(), 10 );
        if ( scheduleInterval === -1 && !sfw.ActiveDirectorySettings.hasADIntegration()) {
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled( true );
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setValue( scheduleInterval );
        } else {
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled( sfw.util.Auth.LoginDetails.isReadOnly );
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setValue( scheduleInterval );
        }

        /**
         * Start the event listeners again after setting all the values
         */

        Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].resumeEvents();
        Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].resumeEvents();
        Ext.ComponentQuery.query('#ad_integration')[0].resumeEvents();
        Ext.ComponentQuery.query('#ad_scan_options')[0].resumeEvents();
        Ext.ComponentQuery.query('#adScanSpecificPath')[0].resumeEvents();
        Ext.ComponentQuery.query('#adScanPath')[0].resumeEvents();
        Ext.ComponentQuery.query('#ad_frequency_unit')[0].resumeEvents();
        Ext.ComponentQuery.query('#ad_laps')[0].resumeEvents();
        Ext.ComponentQuery.query('#LDAPText')[0].resumeEvents();

        /**
         * Disable all the AD controls if the user does not have AD enabled
         */
        if ( !sfw.ActiveDirectorySettings.hasADIntegration() ) {

            Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled( true );
            Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled( true );
            Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled( true );
            Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled( true );
            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled( true );
            Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled( true );
            Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled( true );
            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled( true );
            Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled( true );
            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled( true );
            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled( true );
        }
    }

});
