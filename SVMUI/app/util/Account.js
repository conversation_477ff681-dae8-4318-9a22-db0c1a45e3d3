// Role constants. These coincide with the IDs from the server-side AccountsRoleBinding class.
Ext.apply( sfw, {
    /**
     * @static
     * Scanning Role
     * @type {Number}
     */
    ROLE_SCANNING: 1
    /**
     * @static
     * Results Role
     * @type {Number}
     */
    ,ROLE_RESULTS: 2
    /**
     * @static
     * Reporting Role
     * @type {Number}
     */
    ,ROLE_REPORTING: 3
    /**
     * @static
     * Patching Role
     * @type {Number}
     */
    ,ROLE_PATCHING: 4

    /**
     * @static
     * VIM Integration Role
     * @type {Number}
     */
    ,ROLE_VIM_INTEGRATION: 7

    ,ROLE_SCANNING_FILTER_SCAN_RESULTS: 8

    ,ROLE_REPORTING_DATABASE_ACCESS: 10

    // Special values, never actually in VPDB table
    ,ROLE_USER_MANAGEMENT: 6
    ,ROLE_PARTITION_ADMINISTRATOR: 20
    ,ROLE_P0_ADMINISTRATOR: 21

    /**
     * @static
     * @msp
     * MSP User Role
     * @type {Number}
     */
    ,ROLE_MSP_USER: 22
});



/**
 * @class sfw.Account
 * Create an Account
 *
 * An Account object is auto created during the login process using the logged
 * in account's data and is then assigned to {@link LoginDetails.account}.
 *
 * <div class="mdetail-params"><pre><ul>
 * <li>To check if the Account meets all of the requirements to access a specific page:
 * <code>var canAccessPage = LoginDetails.account.canAccessPage( pageId );</code></li>
 * <li>To check if the Account has the Modules required to access a specific page:
 * <code>var hasModulesForPage = LoginDetails.account.hasModulesForPage( pageId );</code></li>
 * <li>To check if the Account has the Roles required to access a specific page:
 * <code>var hasRolesForPage = LoginDetails.account.hasRolesForPage( pageId );</code></li>
 * <li>To check if the Account has a specific Role:
 * <code>var hasRole = LoginDetails.account.hasRole( sfw.ROLE_SCANNING );</code></li>
 * <li>Same as above except to check multiple Roles at the same time:
 * <code>var hasRoles = LoginDetails.account.hasRole( [ sfw.ROLE_SCANNING, sfw.ROLE_REPORTING ] );</code>
 * </ul></pre></div>
 *
 * @param {Object} config The config object
 */
Ext.define('sfw.util.Account', {

    config: {
        /**
         * @property
         * @public
         * @type {Number}
         * Account ID for this Account.
         */
        accountId: 0,
        /**
         * @property
         * @public
         * @type {Object}
         * The settings for this Account ( based on nsi base settings ).
         */
        settings: {},

        /**
         * @property
         * @public
         * @type {Object}
         * The global zombie settings for this Account's customer and partition.
         */
        zombieSettings: {},

        /**
         * @property
         * @public
         * @type {Object}
         * The configuration for this Account ( based on csi configuration ).
         *
         * To refresh the configuration at any time call defaults.options.getAllOptions()
         * This this configuration references the same object as defaults.options.options.
         * @todo TODO Make defaults.options in to a class
         */
        configuration: {},
        /**
         * @property
         * @public
         * @type {Object}
         * The menu sections enabled for this Account. The property names are the menu ids.
         */
        roleSections: {},
        /**
         * @property
         * @private
         * @type {Object}
         * Roles enabled for this Account. Object of arrays with IDs and ReadOnly properties.
         */
        roleIds: {},

        account: null
    },

    constructor : function (config) {
        // Initializes config properties
        this.initConfig(config);
    },

    setAccount: function (account) {
        if(account) {
            // Load the Account properties
            Ext.apply( this, account );
        }
    },

    /**
     * @public
     * Checks if the Account has the specified Role ID
     * @param mixed role Role ID
     *	Can be a String or an Array of Strings if more than one Role ID is being checked
     * @return {Boolean}
     */
    hasRole: function ( roleIds ) {
        if ( "number" === typeof roleIds ) {
            roleIds = [roleIds];
        }
        return this.hasRoleIds( roleIds );
    },
    /**
     * @private
     * Checks if the Account has the specified Role IDs
     * @param {Array} roles Role IDs
     * @return {Boolean}
     */
    hasRoleIds: function ( roleIds ) {
        for ( var i = 0, len = roleIds.length; i < len; i++ ) {
            if ( "undefined" === typeof this.roleIds[roleIds[i]] ) {
                return false;
            }
        }
        return true;
    },
    /**
     * @public
     * Checks if the Account has the Role Section necessary to access this page
     * @param {String} pageId
     * @return {Boolean}
     */
    hasRolesForPage: function ( pageId ) {
        var LoginDetails = sfw.util.Auth.LoginDetails;

        // If page doesn't exist then return false
        if (!Ext.isDefined(sfw.ui.pages[pageId])) {
            return false;
        }
        // Check if the page skips role access checks
        if ( sfw.ui.pages[pageId].skipRoleAccessCheck ) {
            return true;
        }
        // If the exact page has a Role then return true

        if (Ext.isDefined(LoginDetails.account.roleSections[pageId])) {
            return true;
        }

        var parentId = "";
        // Iterate thru the parents to see if any have the Role, is so then the child page is allowed thru
        while ( undefined !== parentId && "undefined" === typeof LoginDetails.account.roleSections[parentId]  ) {
            if ( "undefined" !== typeof sfw.ui.pages[pageId] ) {
                parentId = sfw.ui.pages[pageId].parent;
                if ( parentId ) {
                    pageId = parentId;
                }
            }
        }
        // Check if section is enabled
        return "undefined" !== typeof this.roleSections[pageId];
    },
    /**
     * @public
     * Checks if the Account has the Modules necessary to access this page
     * @param {String} pageId
     * @return {Boolean}
     */
    hasModulesForPage: function ( pageId ) {
        if ( "undefined" !== typeof sfw.ui.pages[pageId].modules ) {
            return sfw.util.MenuFolders.checkModules( sfw.ui.pages[pageId].modules );
        }
        return true;
    },

    /**
     * @public
     * Checks if the Account can access the specified page
     * @param {String} pageId
     * @return {Boolean}
     */
    canAccessPage: function ( pageId ) {
        // @todo: #sccm_plugin
        // temporarily allowing access to the SCCM pages
        // We need to see if the appropriate module is enabled for the customer
        if ( sfw.isSccmPlugin ) {
            return true;
        }
        var access = true;
        // Check if the Account has the required role
        access = access && this.hasRolesForPage( pageId );
        // Check if the Account has the required role
        access = access && this.hasModulesForPage( pageId );

        return access;
    },

    /**
     * @public
     * @msp
     * Checks if the Account is an MSP User
     * @return {Boolean}
     */
    isMspUser: function () {
        return this.hasRole( sfw.ROLE_MSP_USER );
    }

});
