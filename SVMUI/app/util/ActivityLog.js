Ext.define('sfw.util.ActivityLog', {
    singleton: true,

    /*
     * Use to log significant activities performed by the user
     *
     * @param activitytype
     *	Integer Code identifying the user action that we are logging
     * @param activitystatus
     *	Integer Activity specific status code
     * @param text
     *	String Optional additional data to be logged
     * @param priority
     *	Integer Priority category of the action being logged
     *
     */

    /*
     * Activity types:
     *
     * 0 - Internal CSI (like login)
     *     0001 - Login
     *     0002 - Logout
     * 1000 - Scanning
     *     1010 - Add Ignore Rule
     *     1011 - Update Ignore Rule
     *     1012 - Delete Ignore Rule
     *     1050 - Create Scan Group
     *     1051 - Update Scan Group
     *     1052 - Delete Scan Group
     * 2000 - Results
     *     2020 - Delete Host
     * 3000 - PSI Integration
     *     3020 - Change LinkID
     *     3040 - Approve PSI update
     *
     * 4000 - Reports
     *     4010 - Create Report
     *     4020 - Create a Change Summary
     *     4040 - Change Summary Sent
     *     4050 - Report Sent
     * 5000 - Patch
     *     5010 - Create Patch Package
     *     5020 - Approve WSUS update
     *     5040 - WSUS Connection
     *     5041 - Configured Upstream server
     *     5042 - Configured Downstream server
     *     5043 - Updated State Changes
     * 8000 - User Management
     *     8010 - User edited
     *     8011 - New user created
     *     8012 - User password reset to one time password
     *
     */

    /*
     * Default status codes:
     *
     * 0 - Success
     * 1 - Failure
     *
     */

    /*
     * Priority:
     *
     * 0-10: High Priority
     * 11-20: Medium Priority
     * 21-30: Low Priority
     *
    */

    log: function( activitytype, activitystatus, text, priority, sync ) {

        var loggingParameters = {};
        var postData;

        const defaults = sfw.util.Default;

        if ( typeof activitytype === 'number' ) {
            loggingParameters.activityType = activitytype;
        } else {
            loggingParameters.activityType = 0;
        }

        if ( typeof activitystatus === 'number' ) {
            loggingParameters.activityStatus = activitystatus;
        } else {
            loggingParameters.activityStatus = 0;
        }

        if ( text ) {
            loggingParameters.activityText = text;
        } else {
            loggingParameters.activityText = '';
        }

        if ( typeof priority === 'number' ) {
            loggingParameters.priority = priority;
        } else {
            loggingParameters.priority = 30;
        }

        postData = "activityType=" + encodeURIComponent( loggingParameters.activityType );
        postData += "&activityStatus=" + encodeURIComponent( loggingParameters.activityStatus );
        postData += "&activityText=" + encodeURIComponent( loggingParameters.activityText );
        postData += "&priority=" + encodeURIComponent( loggingParameters.priority );

        defaults.sendPostRequest( sfw.util.Globals.apiPath() + 'action=activitylog&which=insert', postData, 'OK', null, '', ( typeof sync === "boolean" ? sync : false ) );
    }


});