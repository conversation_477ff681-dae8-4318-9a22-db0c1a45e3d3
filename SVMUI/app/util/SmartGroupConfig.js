Ext.define('sfw.util.SmartGroupConfig', {
    singleton: true,

    hostViewConfig: function (node) {

        var data = {
            allCustomColumns: 1,//0
            smartGroupId: 1,
            customColumnData: "",
            type: sfw.util.CommonConstants.HOST[0],
            columns: sfw.util.CommonConstants.hostOverviewGridColumns,
            typeUpper: sfw.util.CommonConstants.HOST[1],
            typePlural: sfw.util.CommonConstants.HOST[2],
            typeUpperPlural: sfw.util.CommonConstants.HOST[3],
            configuredBaseFields: [{name: 'nsi_device_id', type: 'int'}],
            idProperty: 'nsi_device_id',
            expandColumn: 'host_name',
            singleSelect: false,
            fields: sfw.util.CommonConstants.hostOverviewGridFields,
            potentialColumns: sfw.util.CommonConstants.hostPotentialColumns
        };

        return data;
    },

    productViewConfig: function (node) {

        var data = {
            allCustomColumns: 1,//0
            smartGroupId: 2,
            customColumnData: "",
            type: sfw.util.CommonConstants.PRODUCT[0],
            columns: sfw.util.CommonConstants.productOverviewGridColumns,
            typeUpper: sfw.util.CommonConstants.PRODUCT[1],
            typePlural: sfw.util.CommonConstants.PRODUCT[2],
            typeUpperPlural: sfw.util.CommonConstants.PRODUCT[3],
            configuredBaseFields: [{name: 'product_id', type: 'int'}],
            idProperty: 'product_id',
            expandColumn: 'product_name',
            singleSelect: true,
            fields: sfw.util.CommonConstants.productOverviewGridFields,
            potentialColumns: sfw.util.CommonConstants.productPotentialColumns
        };

        if (sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
            const columnIds = Ext.Array.pluck(data.potentialColumns, 'id');
            if (!Ext.Array.contains(columnIds, 'vuln_threat_score')) {
                data.potentialColumns.splice(5, 0,
                    sfw.util.CommonConstants.potentialColumns.vuln_threat_score
                );
            }
        }

        return data;
    },

    advisoryViewConfig: function (node) {

        var data = {
            allCustomColumns: 1,//0
            smartGroupId: 6,
            customColumnData: "",
            type: sfw.util.CommonConstants.ADVISORY[0],
            columns: sfw.util.CommonConstants.advisoryOverviewGridColumns,
            typeUpper: sfw.util.CommonConstants.ADVISORY[1],
            typePlural: sfw.util.CommonConstants.ADVISORY[2],
            typeUpperPlural: sfw.util.CommonConstants.ADVISORY[3],
            configuredParentFolder: 'sfw.csiAdvisorySmartGroupsFolder',
            configuredBaseFields: [{name: 'vuln_id', type: 'int'}],
            idProperty: 'vuln_id',
            expandColumn: 'vuln_title',
            singleSelect: true,
            fields: sfw.util.CommonConstants.advisoryOverviewGridFields,
            potentialColumns: sfw.util.CommonConstants.advisoryPotentialColumns,
            defaultColumnsText: 'An Advisory Smart Group\'s contents grid will always show the Secunia Advisory ID and Descrition for each entry.'
        };

        if (sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
            const columnIds = Ext.Array.pluck(data.potentialColumns, 'id');
            if (!Ext.Array.contains(columnIds, 'vuln_threat_score')) {
                data.potentialColumns.splice(3, 0,
                    sfw.util.CommonConstants.potentialColumns.vuln_threat_score
                );
            }
        }

        return data;
    },

    hostOverviewConfig: function (node) {
        var data = {
            typeUpper: sfw.util.CommonConstants.HOST[1],
            type: sfw.util.CommonConstants.HOST[0],
            columns: sfw.util.CommonConstants.hostOverviewGridColumns,
            fields: sfw.util.CommonConstants.hostOverviewGridFields,
            potentialColumns: sfw.util.CommonConstants.hostPotentialColumns
        };
        return data;
    },

    productOverviewConfig: function (node) {
        var data = {
            typeUpper: sfw.util.CommonConstants.PRODUCT[1],
            type: sfw.util.CommonConstants.PRODUCT[0],
            columns: sfw.util.CommonConstants.productOverviewGridColumns,
            fields: sfw.util.CommonConstants.productOverviewGridFields,
            potentialColumns: sfw.util.CommonConstants.productPotentialColumns
        };
        return data;
    },

    advisoryOverviewConfig: function (node) {
        var data = {
            typeUpper: sfw.util.CommonConstants.ADVISORY[1],
            type: sfw.util.CommonConstants.ADVISORY[0],
            columns: sfw.util.CommonConstants.advisoryOverviewGridColumns,
            fields: sfw.util.CommonConstants.advisoryOverviewGridColumns,
            potentialColumns: sfw.util.CommonConstants.advisoryPotentialColumns
        };
        return data;
    },

    getSmartGroupViewConfig: function (type) {
        var me = this,
            //type = node.get('smartGroupType'),
            viewConfig = {};

        switch (type) {
            case 'host':
                viewConfig = me.hostViewConfig();
                break;

            case 'product':
                viewConfig = me.productViewConfig();
                break;

            case 'advisory':
                viewConfig = me.advisoryViewConfig();
                break;

            case 'hostOverview':
                viewConfig = me.hostOverviewConfig();
                break;

            case 'productOverview':
                viewConfig = me.productOverviewConfig();
                break;

            case 'advisoryOverview':
                viewConfig = me.advisoryOverviewConfig();
                break;

            default:
                viewConfig = {};
        }

        return viewConfig;
    },

    isSGOverviewUI: function (type) {
        return Ext.String.endsWith(type, 'Overview');
    }


});