Ext.define('sfw.util.Default', {
    singleton: true,
    alternateClassName: 'sfw.Default',

    options: null,

    // Set up this array here as we use it for both the platform renderer and the comboBox creator.
    // Order is based on the way we want to see it in the drop down combos.
    platformData: [
        [21, 'Windows'],
        //[  1, 'PSI' ],
        [31, 'Config Manager'],
        [11, 'Mac'],
        [41, 'Red Hat Linux'],
        //[ 61, 'CSI Debian Linux' ]
    ],

    // Scan engine data is almost identical,just slightly different text. The text is the prefix we use before the actual version number.
    scanEnginePrefixData: [
        [21, ''],
        [1, 'PSI'],
        [31, 'Config Manager'],
        [11, 'Mac Agent'],
        [41, 'RHEL'],
        [61, 'Debian']
    ],

    // Render the platform integer value as its text equivalent.
    platformRenderer: function (value) {
        value = parseInt(value, 10);
        var me = this,
            retval = '-';

        for (var i = 0; i < me.platformData.length; ++i) {
            if (value === me.platformData[i][0]) {
                retval = me.platformData[i][1];
                break;
            }
        }
        return retval;
    },

    renderSSS: function (value) {
        var score = parseInt(value, 10);
        return '<font color="' + sfw.Default.getScoreColor(score) + '">' + score + '%</font>';
    },

    businessImpactImage: function (impact) {
        if (!isNaN(impact) && impact > 0 && impact < 6) {
            return '<img src="resources/images/critlow_' + (6 - impact) + '.gif" alt="' + sfw.util.Default.businessImpactText(impact) + '" />';
        } else {
            return '-';
        }
    },

    // Return description/name of business impact level
    businessImpactText: function (crit) {
        switch (crit) {
            case 1 :
                return 'Critical';
            case 2 :
                return 'High';
            case 3 :
                return 'Medium';
            case 4 :
                return 'Minor';
            case 5 :
                return 'Low';
        }
        return '-';
    },

    // Render the scan engine as a combo of the text name and the version number
    scanEngineRenderer: function (version, platform) {
        platform = parseInt(platform, 10);
        var me = this,
            retval = '';
        for (var i = 0; i < me.scanEnginePrefixData.length; ++i) {
            if (platform === me.scanEnginePrefixData[i][0]) {
                retval = me.scanEnginePrefixData[i][1];
                break;
            }
        }
        return retval + ' ' + version;
    },

    createPlatformSelector: function (id) {
        var data = this.platformData;

        data.unshift([0, 'Show All Platforms']);

        return Ext.create({
            xtype: 'combobox',
            //id: id + '_platformCombo',TODO Wemerson review
            emptyText: 'Showing All Platforms',
            store: {
                type: 'array',
                // idIndex: 0,//FIXME removed Ext JS 7
                fields: ['id', 'name'],
                data: data,
            },
            width: 180,
            mode: 'local',
            editable: false,
            triggerAction: 'all',
            forceSelection: true,
            autoSelect: true,
            valueField: 'id',
            displayField: 'name',
            hideLabel: true
        });
    },


    getScoreColor: function (score) {
        var ret = '';
        if (score < 80) {
            ret = this.returnStatusColor(1);
        } else if (score < 95) {
            ret = this.returnStatusColor(2);
        } else {
            ret = this.returnStatusColor(3);
        }
        return ret;
    },

    returnStatusColor: function (iType) {
        switch (iType) {
            case 1: // Red
                return '#CC0000';
            case 2: // Yellow
                return '#FFDB00';
            case 3: // Green
                return '#00BF12';
            case 4: // Light Green
                return '#9FFD0B';
            case 5: // Dark Yellow
                return '#D9FC04';
            case 6: // Light Red (orange)
                return '#FF9400';
            case 7: // Dark red
                return '#FF7600';
            case 8: // grey
                return '#CCCCCC';
            case 9: // dark grey
                return '#999999';
            case 10: // secunia red
                return '#9C181E';
        }
        return '#000000';
    },

    gridRenderLastCheckInDate: function (value, meta) {
        if (value) {
            var lastCheckInDate = sfw.util.Util.dateCreate(value, true);
            var currentDate = sfw.util.Util.dateCreate();
            // Difference between the last check-in and the current date in seconds
            var dateDiff = Math.round(Math.abs((lastCheckInDate.valueOf() - currentDate.valueOf())) / 1000);

            if (dateDiff > 604800) { // 7 days = 60 * 60 * 24 * 7 = 604800 seconds
                meta.style = "color:red;";
                return '<span class="AgentError">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else if (dateDiff > 86400) { // 1 day = 60 * 60 * 24 * 1 = 86400 seconds
                meta.style = "color:orange;";
                return '<span class="AgentWarning">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else {
                meta.style = "color:green;";
                return '<span class="AgentSuccess">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            }
        } else {
            return '-';
        }
    },

    gridRenderLastScanDate: function (value, meta ) {
        if (value) {
            var lastScanDate = sfw.util.Util.dateCreate(value, true);
            var currentDate = sfw.util.Util.dateCreate();
            // Difference between the last check-in and the current date in seconds
            var dateDiff = Math.round(Math.abs((lastScanDate.valueOf() - currentDate.valueOf())) / 1000);

            if (dateDiff > 2764800) { // 32 days = 60 * 60 * 24 * 32 = 2764800 seconds8*60*
                meta.style = "color:red;";
                return '<span class="AgentError">' + Ext.util.Format.date(lastScanDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else if (dateDiff > 691200) { // 8 day = 60 * 60 * 24 * 8 = 691200 seconds
                meta.style = "color:orange;";
                return '<span class="AgentWarning">' + Ext.util.Format.date(lastScanDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else {
                meta.style = "color:green;";
                return '<span class="AgentSuccess">' + Ext.util.Format.date(lastScanDate, sfw.util.Globals.dateLongOutput) + '</span>';
            }
        } else {
            return '-';
        }
    },

    gridRenderPastDate: function (value) {
        if (value) {
            var scanDate = sfw.util.Util.dateCreate(value, true);
            var currentDate = sfw.util.Util.dateCreate();
            if (scanDate < currentDate) {
                return 'As soon as possible';
            } else {
                return Ext.util.Format.date(scanDate, sfw.util.Globals.DATELONGOUTPUT);
            }
        } else {
            return '-';
        }
    },

    gridRenderAgentVersion: function (value, meta) {
        if (value) {
            meta.style = "color:red;";
            if (sfw.util.Default.versionCompare(value, sfw.Globals.CSIAWarningVersion) >= 0) {
                meta.style = "color:green;";
            } else if (sfw.util.Default.versionCompare(value, sfw.Globals.CSIAErrorVersion) > 0) {
                meta.style = "color:orange;";
            }
            return '<span class="' + meta.tdCls + '">' + value + '</span>';
        } else {
            return '-';
        }
    },

    renderNullToDash: function (value) {
        if (value === '') {
            value = '-';
        }
        return value;
    },

    versionCompare: function (v1, v2) {

        // Make sure that the format is correct
        // e.g. if the version is 5.2, make it 5.2.0.0
        v1 += Array(5 - v1.split('.').length).join('.0');
        v2 += Array(5 - v2.split('.').length).join('.0');

        var nv1 = sfw.util.Default.normalizeAgentVersion(v1);
        var nv2 = sfw.util.Default.normalizeAgentVersion(v2);

        if (isNaN(nv1) || isNaN(nv2)) {
            return false;
        }
        if (nv1 > nv2) {
            return 1;
        } else if (nv1 < nv2) {
            return -1;
        }
        return 0;
    },

    normalizeAgentVersion: function (val) {
        var res = 0;
        var parts = val.split('.');
        if (parts.length === 4) {
            res = parseInt(parts[3], 10) + parseInt(parts[1], 10) * 1000000 + parseInt(parts[0], 10) * 100000000;
        }
        return res;
    },

    //Smart Groups

    renderSmartgroupType: function (val) {
        var smartgroupType = parseInt(val, 10);
        var retVal = '-';
        switch (smartgroupType) {
            case sfw.util.CommonConstants.SMARTGROUP_TYPE_HOST:
                retVal = 'Host';
                break;
            case sfw.util.CommonConstants.SMARTGROUP_TYPE_PRODUCT:
                retVal = 'Product';
                break;
            case  sfw.util.CommonConstants.SMARTGROUP_TYPE_ADVISORY:
                retVal = 'Advisory';
                break;
            default:
                break;

        }
        return retVal;
    },

    alwaysNotifyRenderer: function (value) {
        var input = parseInt(value, 10), returnValue = '-';

        switch (input) {
            case 0:
                returnValue = 'No';
                break;
            case 1:
                returnValue = 'Yes';
                break;
            default:
                returnValue = '-';
                break;
        }
        return returnValue;
    },

    // Function for opening a new browser window
    externalURL: function (sURL, bIE) {
        if (sfw.isConsole) {
            sfw.external.fWUIOpenExternalURL(sURL, bIE);
        } else {
            //document.location = sURL;
            window.open(sURL);
        }
    },

    advisoryCriticalityImage: function (crit) {
        if (!isNaN(crit) && crit > 0 && crit < 6) {
            return '<img src="resources/images/critlow_' + (6 - crit) + '.gif" alt="' + sfw.util.Default.advisoryCriticalityText(crit) + '" />';
        } else {
            return '-';
        }
    },

    advisoryCriticalityText: function (crit) {
        switch (crit) {
            case 1 :
                return 'Extremely Critical';
            case 2 :
                return 'Highly Critical';
            case 3 :
                return 'Moderately Critical';
            case 4 :
                return 'Less Critical';
            case 5 :
                return 'Not Critical';
        }
        return '-';
    },

    renderSaid: function (said, hoverText) {
        var html = '-';
        if (!isNaN(said) && said > 0) {
            html = '<a href="#" onclick="sfw.Default.advisoryView( 1,' + said + ')" style="color:blue;"';
            if ("string" === typeof (hoverText)) {
                html += 'title="' + hoverText + '"';
            }
            html += '>SA' + said + '</a>';
        }
        return html;
    },

    renderSaidDate: function (val) {
        if ("0000-00-00" === val || "0000-00-00 00:00:00" === val || "" == val) {
            return "-";
        }

        val = sfw.util.Util.dateCreate(val)

        return Ext.util.Format.date(val, sfw.util.Globals.DATESHORTOUTPUT)
    },

    gridRenderTimeAgo: function (value, p, record) {
        if (value) {
            var lastScanDate = sfw.util.Util.dateCreate(value, true);
            var currentDate = sfw.util.Util.dateCreate();

            if (p) {
                p.attr = 'title="Date: ' + Ext.util.Format.date(lastScanDate, sfw.util.Globals.DATELONGHUMANOUTPUT) + '"';
            }

            var outputStr = sfw.util.Util.differenceBetweenDates(lastScanDate, currentDate, 0);

            // Difference between the last scan and the current date in seconds
            var dateDiff = Math.round(Math.abs((lastScanDate.valueOf() - currentDate.valueOf())) / 1000);

            // 60 * 60 * 24 = 86400 seconds, or 1 day
            if ((dateDiff / 86400) > sfw.util.Globals.WARNUSERCREATEUPDATEERRORTHRESHOLD) {
                return '<span class="AgentError">' + outputStr + ' ago</span>';
            } else if ((dateDiff / 86400) > sfw.util.Globals.WARNUSERCREATEUPDATEWARNINGTHRESHOLD) {
                return '<span class="AgentWarning">' + outputStr + ' ago</span>';
            } else {
                return '<span class="AgentSuccess">' + outputStr + ' ago</span>';
            }
        } else {
            return '-';
        }
    },

    renderIsUninstallable: function (val) {
        if (val > 0) {
            return 'Yes';
        } else {
            return 'No';
        }
    },

    getActivityTypeConstants: function () {

        //fetching the array of Activity Log Constants
        //var logConstants = sfw.util.CommonConstants.ACTIVITYLOGCONSTANT;

        var logConstants = [
            {event_type: "0", event: " Not Selected"},
            {event_type: "1", event: "User Login"},
            {event_type: "2", event: "User Logut"},
            {event_type: "10", event: "Password reset from Password Recovery"},
            {event_type: "11", event: "Sent Authentication Pins to recover password"},
            {event_type: "1020", event: "Add Allow List Scan Path"},
            {event_type: "1021", event: "Edit Allow List Scan Path"},
            {event_type: "1022", event: "Delete Allow List Scan Path"},
            {event_type: "1030", event: "Add Block List Scan Path"},
            {event_type: "1031", event: "Edit Block List Scan Path"},
            {event_type: "1032", event: "Delete Block List Scan Path"},
            {event_type: "1033", event: "Add Logged List Scan Path"},
            {event_type: "1034", event: "Edit Logged List Scan Path"},
            {event_type: "1035", event: "Delete Logged List Scan Path"},
            {event_type: "1050", event: "Create Scan Group"},
            {event_type: "1051", event: "Update Scan Group"},
            {event_type: "1052", event: "Delete Scan Group"},
            {event_type: "1060", event: "Create Customer Detection Rule"},
            {event_type: "1061", event: "Update Customer Detection Rule"},
            {event_type: "1062", event: "Delete Customer Detection Rule"},
            {event_type: "1070", event: "Edit Site Agents Configuration"},
            {event_type: "1071", event: "Edit Host Agent Configuration"},
            {event_type: "1072", event: "Removed Agent"},
            {event_type: "1073", event: "Agent Details Email"},
            {event_type: "1080", event: "Create Scheduled Export"},
            {event_type: "1081", event: "Update Scheduled Export"},
            {event_type: "1082", event: "Delete Scheduled Export(s)"},
            {event_type: "1083", event: "Run Scheduled Export"},
            {event_type: "1084", event: "Settings Update"},
            {event_type: "1085", event: "Settings Update"},
            {event_type: "1086", event: "Settings Update"},
            {event_type: "1087", event: "Settings Update"},
            {event_type: "1088", event: "Settings Update"},
            {event_type: "1100", event: "Settings Update"},
            {event_type: "1110", event: "Settings Update"},
            {event_type: "1111", event: "Collect Network Information"},
            {event_type: "1112", event: "Live Update"},
            {event_type: "1113", event: "Status Polling Settings"},
            {event_type: "1114", event: "Java Assessment Settings"},
            {event_type: "1115", event: "Agent Check-in Site Settings"},
            {event_type: "1116", event: "Host Deletion Settings"},
            {event_type: "1120", event: "Windows Update Settings"},
            {event_type: "1121", event: "Windows Update Settings"},
            {event_type: "1122", event: "Windows Update Settings"},
            {event_type: "1123", event: "Windows Update Settings"},
            {event_type: "1124", event: "Windows Update Settings"},
            {event_type: "1125", event: "Windows Update Settings"},
            {event_type: "1130", event: "Windows Update Proxy Settings"},
            {event_type: "1131", event: "Windows Update Proxy Settings"},
            {event_type: "1132", event: "Windows Update Proxy Settings"},
            {event_type: "1133", event: "Windows Update Proxy Settings"},
            {event_type: "2010", event: "Rename Site"},
            {event_type: "2020", event: "Delete Host"},
            {event_type: "4010", event: "Create Report"},
            {event_type: "4011", event: "Edit Report"},
            {event_type: "4020", event: "Enable Change Summary"},
            {event_type: "4021", event: "Edit Change Summary"},
            {event_type: "4050", event: "Report Sent"},
            {event_type: "5010", event: "Package Creation"},
            {event_type: "5011", event: "WSUS Package Edit"},
            {event_type: "5012", event: "WSUS Package Delete"},
            {event_type: "5020", event: "WSUS Package Approval"},
            {event_type: "5021", event: "WSUS Package Declined"},
            {event_type: "5022", event: "WSUS Package Waiting Signature"},
            {event_type: "5023", event: "WSUS Package Signed"},
            {event_type: "5030", event: "WSUS Certificate Installation"},
            {event_type: "5040", event: "WSUS Connection"},
            {event_type: "5041", event: "WSUS Upstream Server Configuration"},
            {event_type: "5042", event: "WSUS Downstream Server Configuration"},
            {event_type: "5043", event: "WSUS Updated State Changes"},
            {event_type: "5050", event: "Create Patch Template"},
            {event_type: "5051", event: "Delete Patch Template"},
            {event_type: "5052", event: "Update Patch Template"},
            {event_type: "5053", event: "SPS Patch Subscription"},
            {event_type: "5054", event: "VPM Patch Subscription"},
            {event_type: "5055", event: "SVM Patch Daemon Checkin"},
            {event_type: "5056", event: "Patch Deployment"},
            {event_type: "5057", event: "VPM QuickPatch"},
            {event_type: "5058", event: "SPS QuickPatch"},
            {event_type: "8010", event: "User Account Edit"},
            {event_type: "8011", event: "User Account Creation"},
            {event_type: "8012", event: "User Password Reset"},
            {event_type: "8013", event: "SSO User Account Creation"},
            {event_type: "8020", event: "Contact Verification"},
            {event_type: "8021", event: "Sent New Contact Verification Codes"},
            {event_type: "8030", event: "IP Access rule added"},
            {event_type: "8031", event: "IP Access rule edited"},
            {event_type: "8032", event: "IP Access rule deleted"},
            {event_type: "8033", event: "IP Access rule disabled"},
            {event_type: "8034", event: "IP Access rule enabled"},
            {event_type: "9010", event: "Password Policy changed"},
            {event_type: "9011", event: "Updated LDAPS Configuration"},
            {event_type: "9012", event: "Log Notification"},
            {event_type: "9013", event: "SSO Configuration"},
            {event_type: "9014", event: "Add Software Suggestion"},
            {event_type: "9015", event: "Delete Software Suggestion"},
            {event_type: "9016", event: "Add Inventory"},
            {event_type: "9017", event: "Process Inventory"},
            {event_type: "9018", event: "Delete Patch Publisher Connection"},
            {event_type: "9019", event: "Delete Patch Publisher"},
            {event_type: "9020", event: "Settings Update"},
            {event_type: "9021", event: "Settings Update"},
            {event_type: "9022", event: "Exclusion Path List"},
            {event_type: "9023", event: "System Score Settings"},
            {event_type: "9024", event: "Add Extended Support"},
            {event_type: "9025", event: "Edit Extended Support"},
            {event_type: "9026", event: "Delete Extended Support"}
        ]

        var activityType = [];
        var eventsUniqueArray = [];
        var arrayLen = logConstants.length;

        /* Creating a new array in order to remove diplicates and converting it in the form that is requiered for the drop-down*/
        for (var i = 0; i < arrayLen; i++) {
            if (activityType.indexOf(logConstants[i]['event']) == -1) {
                eventsUniqueArray.push([logConstants[i]['event_type'], logConstants[i]['event']]);
                activityType.push(logConstants[i]['event']);
            }
        }

        /* Sort the array before display */
        eventsUniqueArray.sort(function (a, b) {
            if (a[1] === b[1]) {
                return 0;
            } else {
                return (a[1] < b[1]) ? -1 : 1;
            }
        });

        return eventsUniqueArray;
    },

    gridRenderUTCDateInLocaltime: function (value) {
        if (value) {
            value = sfw.util.Util.dateCreate(value, true);
            return Ext.util.Format.date(value, sfw.util.Globals.DATELONGOUTPUT);
        } else {
            return '-';
        }
    },

    frequencyRenderer: function (value) {
        var input = parseInt(value, 10);
        var data = sfw.util.Default.frequencyStore.data;

        for (var i = 0; i < 4; ++i) {
            if (data.items[i].data.id === input) {
                return data.items[i].data.text;
            }
        }
        // default
        return '-';
    },

    frequencyStore: new Ext.data.ArrayStore({
        fields: ['id', 'text']
        , data: [[1, 'Hourly'], [2, 'Daily'], [3, 'Weekly'], [4, 'Monthly']]
    }),

    dateConvertUTCToLocal: function (dateobj) {
        // convert the UTC date to a localtime date
        return Ext.Date.add(dateobj, Ext.Date.MINUTE, -dateobj.getTimezoneOffset());

    },

    dateConvertLocalToUTC: function (dateobj) {
        // convert the localtime date to a UTC date
        return Ext.Date.add(dateobj, Ext.Date.MINUTE, dateobj.getTimezoneOffset());
    },

    checkSubscriptionStatus: function (value, metaData, record, rowIndex, colIndex, store) {

        if (value === 1) {
            if (record.active_flag == 1) {
                return 'Yes';
            } else {
                return '<font color="red">Yes</font>';
            }
        } else {
            return 'No';
        }
    },

    cvssSgRenderer: function (cvss2Score, cvss3score, cvss4score) {
        var cvssText = '';
        var html = '';
        if (cvss4score != '' && cvss4score != 0) {
            cvssText = 'v4:' + cvss4score;
        } else if (cvss3score != '' && cvss3score != 0) {
            cvssText = 'v3:' + cvss3score;
        } else if (cvss2Score != '' && cvss2Score != 0) {
            cvssText = 'v2:' + cvss2Score;
        }
        html = sfw.Default.renderCvssText(cvssText);

        return html;
    },


    advisoryView: function (langid, said) {

        Ext.Ajax.request({
            url: 'action=advisory&which=details',
            method: 'POST',
            dataType: 'json',
            params: {
                'vuln_id': said,
                'lang_id': langid
            },
            success: function (response, opts) {
                var response = Ext.decode(response.responseText, true);
                if (response) {
                    response = sfw.Default.formatAdvisoryResponse(response);
                    var advisory = Ext.create('sfw.view.commonpopupwindows.Advisory');
                    advisory.getViewModel().set('advisorydata', response);
                    advisory.show();
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert('Error', 'Unexpected Error Occured');
            }
        });
    },

    renderCvssText: function (cvssText) {

        var scores = {};
        var cvss2color = '';
        var cvss3color = '';
        var cvss4color = '';
        var cvss2Score = '';
        var cvss2ScoreA = {};
        var cvss3Score = '';
        var cvss3ScoreA = {};
        var cvss4Score = '';
        var cvss4ScoreA = {};
        var html = '';

        if (cvssText != '') {
                scores = cvssText.split(':');
                if (scores[0] == 'v2') {
                    cvss2Score = scores[1];
                    if (cvss2Score != '' && cvss2Score != 0) {
                        cvss2color = sfw.Default.rateCvssScore(cvss2Score);
                    }
                } else if (scores[0] == 'v3') {
                    cvss3Score = scores[1];
                    if (cvss3Score != '' && cvss3Score != 0) {
                        cvss3color = sfw.Default.rateCvssScore(cvss3Score);
                    }
                } else if (scores[0] == 'v4') {
                    cvss4Score = scores[1];
                    if (cvss4Score != '' && cvss4Score != 0) {
                        cvss4color = sfw.Default.rateCvssScore(cvss4Score);
                    }
                }

            if (cvss2color != '') {
                html += '<div class="cvssDiv"><span class="' + cvss2color + '">v2:  ' + cvss2Score + '</span></div>';
            }
            if (cvss3color != '') {
                html += '<div class="cvssDiv"><span class="' + cvss3color + '">v3:  ' + cvss3Score + '</span></div>';
            }
            if (cvss4color != '') {
                html += '<div class="cvssDiv"><span class="' + cvss4color + '">v4:  ' + cvss4Score + '</span></div>';
            }

            return html;
        }

        return cvssText;
    },

    rateCvssScore: function (score) {

        var severityRatings = [{
            name: "None",
            bottom: 0,
            top: 0,
            backgroundcolor: "#53aa33",
            color: "#000000",
            style: "cvssNone"
        }, {
            name: "Low",
            bottom: 0.1,
            top: 3.9,
            backgroundcolor: "#ffcb0d",
            color: "#000000",
            style: "cvssLow"
        }, {
            name: "Medium",
            bottom: 4,
            top: 6.9,
            backgroundcolor: "#f9a009",
            color: "#000000",
            style: "cvssMedium"
        }, {
            name: "High",
            bottom: 7,
            top: 8.9,
            backgroundcolor: "#df3d03",
            color: "#000000",
            style: "cvssHigh"
        }, {
            name: "Critical",
            bottom: 9,
            top: 10,
            backgroundcolor: "#cc0500",
            color: "#000000",
            style: "cvssCritical"
        }];

        var severityRatingsLength = severityRatings.length;

        for (var i = 0; i < severityRatingsLength; i++) {

            if (score >= severityRatings[i].bottom && score <= severityRatings[i].top) {
                return severityRatings[i].style;
            }
        }

        return '';
    },

    returnDateTime: function (sInput, utcTimezone) {
        var oDate = sfw.Util.dateCreate(sInput, utcTimezone);
        return sfw.Default.dateOutputDate(oDate) + ' ' + sfw.Default.dateOutputTime(oDate);
    },

    dateOutputDate: function (oDate, utc) {
        var aMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        var aDayPrefix = new Array(31);
        aDayPrefix[1] = aDayPrefix[21] = aDayPrefix[31] = 'st';
        aDayPrefix[2] = aDayPrefix[22] = 'nd';
        aDayPrefix[3] = aDayPrefix[23] = 'rd';
        var sDate;

        if (typeof utc === 'boolean' && utc) {
            sDate = oDate.getUTCDate() + (aDayPrefix[oDate.getUTCDate()] ? aDayPrefix[oDate.getUTCDate()] : 'th') + ' ' + aMonths[oDate.getUTCMonth()] + ', ' + oDate.getUTCFullYear();
        } else {
            sDate = oDate.getDate() + (aDayPrefix[oDate.getDate()] ? aDayPrefix[oDate.getDate()] : 'th') + ' ' + aMonths[oDate.getMonth()] + ', ' + oDate.getFullYear();
        }
        return sDate;
    },

    dateOutputTime: function (oDate, utc) {
        var utcMode = (typeof utc === 'boolean' ? utc : false),
            hours = utcMode ? oDate.getUTCHours() : oDate.getHours(),
            minutes = utcMode ? oDate.getUTCMinutes() : oDate.getMinutes();

        return (hours < 10 ? '0' : '') + hours + ':' + (minutes < 10 ? '0' : '') + minutes;
    },

    scanResultOverview: function (event, target, options) {

        nsi_device_id = options.extra.data.nsi_device_id;
        var me = this;

        Ext.Ajax.request({
            url: 'action=hosts&which=get_host_scan_info',
            method: 'GET',
            dataType: 'json',
            params: {
                'nsi_device_id': options.extra.data.nsi_device_id,
                'domainname': '',
                'hostname': ''
            },
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);

                if (response.error_code == 0) {
                    var scanWindow = Ext.create("sfw.view.commonpopupwindows.ScanResultsWindow");

                    if ((response.data.host.no_patched > 0) || (response.data.host.no_eol > 0) || (response.data.host.no_insecure > 0)) {
                        sfw.Default.setScanInfodata(response.data.host.no_patched, response.data.host.no_eol, response.data.host.no_insecure)
                    }

                    if(!response.data.host.ip_address){
                        response.data.host.ip_address = "";
                    }

                    scanWindow.getViewModel().set('hostscandetails', response);
                    scanWindow.show();
                } else {
                    sfw.util.Debug.log( 'Error while fetching host information ' + response.msg );
                    Ext.Msg.alert('Error', 'Error while fetching host information ' + response.reason);
                }
            },
            failure: function (response, opts) {
                //console.log("failed");
            }
        });
    },

    setScanInfodata: function (secure, endoflife, insecure) {

        var chartData = [];
        var securescore = {};
        var endoflifescore = {};
        var insecurescore = {};
        var total = parseFloat(secure) + parseFloat(endoflife) + parseFloat(insecure);
        securescore.label = 'Secure' + ' (' + secure + ')';
        securescore.data = Math.round((secure / total) * 100);
        securescore.count = secure;

        endoflifescore.label = 'End-of-Life' + ' (' + endoflife + ')';
        endoflifescore.data = Math.round((endoflife / total) * 100);
        endoflifescore.count = endoflife;

        insecurescore.label = 'Insecure' + ' (' + insecure + ')';
        insecurescore.data = 100 - endoflifescore.data - securescore.data;
        insecurescore.count = insecure;

        chartData.push(securescore);
        chartData.push(endoflifescore);
        chartData.push(insecurescore);

        Ext.getStore('scanoverview').loadData(chartData);
    },

    scanResult: function () {
        var scanresult = Ext.getStore('scanresults');
        scanresult.getProxy().setExtraParams({
            'device_id': nsi_device_id,
            'patched': Ext.ComponentQuery.query('#securecount')[0].getValue(),
            'insecure': Ext.ComponentQuery.query('#insecurecount')[0].getValue(),
            'eol': Ext.ComponentQuery.query('#endoflifecount')[0].getValue()
        });
        scanresult.loadPage(1, {
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    awarenessResult: function () {
        var awarenessresult = Ext.getStore('awarenessresults');
        awarenessresult.getProxy().setExtraParams({
            'device_id': nsi_device_id,
            'patched': Ext.ComponentQuery.query('#securecountawareness')[0].getValue(),
            'insecure': Ext.ComponentQuery.query('#insecurecountawareness')[0].getValue(),
            'eol': Ext.ComponentQuery.query('#endoflifecountawareness')[0].getValue(),
        });
        awarenessresult.loadPage(1, {
            callback: function () {

            },
            failure: function () {

            }
        });
    },

    scanResultOverviewdblcick: function (grid, record, event) {
        if (parseInt(record.data.results_exist, 10)) {
            var options = {};
            options.extra = record;
            sfw.Default.scanResultOverview(grid, null, options);
        } else {
            //console.log("No result exists");
        }
    },

    // Function for setting a cookie
    setCookie: function (sName, sValue, oExpires) {
        // Cookie name and value
        var sCookie = sName + '=' + encodeURIComponent(sValue);

        // Expire at a specific time - if not set, it will only live for this browser session
        if (oExpires) {
            sCookie += "; expires=" + encodeURIComponent(oExpires.toUTCString());
        }

        if ((sfw.isBrowser) && (location.protocol == 'https:')) {
            sCookie += "; secure";
        }
        // Set it
        document.cookie = sCookie;
    },

    // Function for retrieving a cookie
    getCookie: function (sName) {
        // Regular expression
        var sExpression = "(?:; )?" + sName + "=([^;]*);?";
        var oRegExp = new RegExp(sExpression);

        // Does it exist
        if (oRegExp.test(document.cookie)) {
            return decodeURIComponent(RegExp["$1"]);
        } else {
            return false;
        }
    },

    htmlSpecialCharsDecodeAlsoQuot: function (str) {
        var ret = str.replace(/&quot;/g, '"');
        return this.htmlSpecialCharsDecode(ret);
    },

    htmlSpecialCharsDecode: function (str) {

        // make sure string not empty - if it is, return empty string
        if (!str) {
            return '';
        }

        // First check if str is actually an integer
        if (!isNaN(parseInt(str, 10)) && str === (parseInt(str, 10))) {
            return str;
        }

        var ret = str;
        ret = ret.replace(/&lt;/g, '<');
        ret = ret.replace(/&gt;/g, '>');
        ret = ret.replace(/&#39;/g, "'");
        ret = ret.replace(/&amp;/g, '&');
        return ret;
    },

    // Function for setting client side time in cookie (for use in time limited cookies set by from the server)
    setClientTime: function () {
        this.setCookie('client_time', sfw.util.Util.dateCreate().getTime() / 1000);
    },

    // This function is called to get the extra string added to the URL
    getHttpUrlExtraString: function () {
        try {
            var theString = ''; //sfw.external.fWUIGetHttpUrlExtraString();
            return theString;
        } catch (ex) {
            // Silently discard message
        }
    },

    initXMLHttpRequest: function() {
        var request = null;
        try {
            request = new XMLHttpRequest();
        } catch ( trymicrosoft ) {
            try {
                request = new ActiveXObject("Msxml2.XMLHTTP");
            } catch ( othermicrosoft ) {
                try {
                    request = new ActiveXObject("Microsoft.XMLHTTP");
                } catch ( failed ) {
                    request = false;
                }
            }
        }

        // Initialised?
        if ( !request ) {
            alert("Due to limitations of your browser you will not be able to use this window. If you are using Internet Explorer please enable ActiveX for https://ca.secunia.com/.");
            return null;
        }
        return request;
    },

    onReadyStateChange: function(oXML, sSuccess, sExecuteSuccess, sExecuteFail) {
        if ( oXML.readyState == 4 ) {
            // Get server response
            var sServerResponse = oXML.responseText;
            //LogMessage('ServerResponse: ' + sServerResponse);

            // Verify if succesful
            if ( sSuccess.length === 0 ) {
                return 1;
            } else if ( sServerResponse.trim() == sSuccess ) {
                // Execute success
                try {
                    eval(sExecuteSuccess);
                } catch ( anException ) { LogMessage('default.js: eval(sExecuteSuccess) failed'); }
            } else {
                try {
                    eval(sExecuteFail);
                } catch ( anException ) { LogMessage('default.js: eval(sExecuteFail) failed'); }
            }
        }
    },

    //FIXME
    // remove oXML and send through Ext.Ajax.request
    sendPostRequest: function (sUrl, sPostData, sSuccess, sExecuteSuccess, sExecuteFail, bWait) {
        try {
            // Init XMLHttpRequest Object
            var oXML = sfw.util.Default.initXMLHttpRequest();

            // What URL to fetch
            oXML.open('POST', sUrl, bWait ? false : true);

            // Set appropriate POST request headers
            oXML.setRequestHeader("Content-type", "application/x-www-form-urlencoded");

            /*
             *	Don't know why we are setting the following two headers, but this creates a problem using a browser as they are both considered unsafe.
             *	The error is: "Refused to set unsafe header [...]".
             *	@todo: check if we need those and why
             */
            if (sfw.isDesktop) {
                oXML.setRequestHeader("Content-length", sPostData.length);
                oXML.setRequestHeader("Connection", "close");
            }

            // Handler for 'onreadystatechange'
            oXML.onreadystatechange = function () {
                return sfw.util.Default.onReadyStateChange(oXML, sSuccess, sExecuteSuccess, sExecuteFail);
            };

            // Execute request
            oXML.send(sPostData);
        } catch (ex) {
            LogException("default.js: Exception loading post request " + sUrl, ex);
        }

        //
    },

    onReadyStateChange: function (oXML, sSuccess, sExecuteSuccess, sExecuteFail) {
        if (oXML.readyState == 4) {
            // Get server response
            var sServerResponse = oXML.responseText;
            //LogMessage('ServerResponse: ' + sServerResponse);

            // Verify if succesful
            if (sSuccess.length === 0) {
                return 1;
            } else if (sServerResponse.trim() == sSuccess) {
                // Execute success
                try {
                    eval(sExecuteSuccess);
                } catch (anException) {
                    LogMessage('default.js: eval(sExecuteSuccess) failed');
                }
            } else {
                try {
                    eval(sExecuteFail);
                } catch (anException) {
                    LogMessage('default.js: eval(sExecuteFail) failed');
                }
            }
        }
    },

    saveEditNotificationConfiguration: function (event, target, options) {
        edit_emailDefaults = 0;
        notificationId = 0;
        var saveEditConfigurationWindow = Ext.create("sfw.view.commonpopupwindows.NotificationWindow",
            {
                listeners: {
                    afterrender: function (saveEditConfigurationWindow) {
                        if (options) {
                            if ('undefined' !== typeof (options.extra.data.notification_id)) {
                                notificationId = parseInt(options.extra.data.notification_id, 10);
                            }

                            Ext.ComponentQuery.query('#formType')[0].setText('EDIT');
                            saveEditConfigurationWindow.down("title").setHtml("View/Edit Configuration");
                            Ext.ComponentQuery.query("#notificationfiledname")[0].setValue(options.extra.data.name);

                            var frequency = options.extra.data.frequency.split(',');

                            var frequency_type = frequency['0'];
                            var frequency_day = frequency['1'];
                            var frequency_time = frequency['2'].slice(0, -3);


                            if ('undefined' !== typeof (frequency_type)) {
                                Ext.ComponentQuery.query("#frequency_type")[0].setValue(frequency_type);
                            }

                            if (('undefined' !== typeof (frequency_day)) && (0 != frequency_day)) {
                                Ext.ComponentQuery.query("#frequency_day")[0].setValue(frequency_day);
                                Ext.ComponentQuery.query('#frequency_day')[0].show();
                            }

                            if (('undefined' !== typeof (frequency_time)) && ('00:00' != frequency_time)) {
                                var timeToDate = sfw.Util.dateCreate(frequency_time);
                                var localTime = sfw.Default.dateConvertUTCToLocal(timeToDate);
                                var frequencyTime = Ext.util.Format.date(localTime, sfw.util.Globals.hoursMinutesOutput);
                                Ext.ComponentQuery.query('#frequency_time')[0].setValue(frequencyTime);
                            } else {
                                Ext.ComponentQuery.query('#frequency_time')[0].hide();
                            }

                            if ('undefined' !== typeof (options.extra.data.notify_always)) {
                                if (parseInt(options.extra.data.notify_always, 10)) {
                                    Ext.ComponentQuery.query('#notify')[0].setValue(true);
                                }
                            }

                            Ext.Ajax.request({
                                url: sfw.util.Globals.apiPath()
                                , params: {
                                    action: 'recipients'
                                    , which: 'get_defaults'
                                    , instance_id: options.extra.data.notification_id
                                    , module_id: 106
                                }
                                , method: 'GET'
                                , success: function (responseJson) {
                                    var response = Ext.decode(responseJson.responseText);
                                    edit_emailDefaults = response.email_defaults;
                                }
                                , failure: function () {
                                    Ext.Msg.alert('Error', 'Unexpected error.');
                                }
                            });

                            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

                            const emailRecipientSelector = localItemSelector.createEmailRecipientSelector('106');
                            const emailRecipients = saveEditConfigurationWindow.down('#emailRecipients');
                            emailRecipients.add(emailRecipientSelector);
                            sfw.Default.getSelectorValues({
                                action: 'recipients',
                                which: 'get_selected_recipients',
                                module_id: 106,
                                method_id: 1,
                                instance_id: options.extra.data.notification_id
                            }, emailRecipientSelector);

                            sfw.Default.defaulEmailRecipients(options.extra.data.notification_id, '106', emailRecipients);

                            const selectEventsSelector = localItemSelector.createSelectEventSelector();
                            const selectEvents = saveEditConfigurationWindow.down('#selectEvents');
                            selectEvents.add(selectEventsSelector);
                            sfw.Default.getSelectorValues({
                                action: 'csi_notifications',
                                which: 'getSelectedEvents',
                                instance_id: options.extra.data.notification_id
                            }, selectEventsSelector);

                        } else {
                            Ext.ComponentQuery.query('#formType')[0].setText('NEW');
                            saveEditConfigurationWindow.down("title").setHtml("Configure New Log Notification");

                            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

                            const emailRecipients = saveEditConfigurationWindow.down('#emailRecipients');
                            const emailRecipientSelector = localItemSelector.createEmailRecipientSelector('106');
                            emailRecipients.add(emailRecipientSelector);

                            const selectEvents = saveEditConfigurationWindow.down('#selectEvents');
                            const selectEventsSelector = localItemSelector.createSelectEventSelector();
                            selectEvents.add(selectEventsSelector);
                        }
                    }
                }
            });

        saveEditConfigurationWindow.show();
    },

    validateNotification: function () {
        if (Ext.ComponentQuery.query('#frequency_type')[0].getValue() == '1') {
            Ext.ComponentQuery.query('#frequency_day')[0].hide();
            Ext.ComponentQuery.query('#frequency_time')[0].hide();
        } else if ((Ext.ComponentQuery.query('#frequency_type')[0].getValue() == '2') || (Ext.ComponentQuery.query('#frequency_type')[0].getValue() == '4')) {
            Ext.ComponentQuery.query('#frequency_day')[0].hide();
            Ext.ComponentQuery.query('#frequency_time')[0].show();
        } else if (Ext.ComponentQuery.query('#frequency_type')[0].getValue() == '3') {
            Ext.ComponentQuery.query('#frequency_day')[0].show();
            Ext.ComponentQuery.query('#frequency_time')[0].show();
        }

        if (Ext.ComponentQuery.query('#frequency_type')[0].getValue() != '1') {
            if (Ext.ComponentQuery.query('#frequency_time')[0].getValue() == null) {
                Ext.ComponentQuery.query('#save')[0].setDisabled(true);
            } else {
                Ext.ComponentQuery.query('#save')[0].setDisabled(false);
            }
            if (Ext.ComponentQuery.query('#frequency_type')[0].getValue() == '3') {
                if (Ext.ComponentQuery.query('#frequency_day')[0].getValue() == null) {
                    Ext.ComponentQuery.query('#save')[0].setDisabled(true);
                } else {
                    Ext.ComponentQuery.query('#save')[0].setDisabled(false);
                }
            }
        } else {
            Ext.ComponentQuery.query('#save')[0].setDisabled(false);
            Ext.ComponentQuery.query('#save')[0].setDisabled(false);
        }
    },

    zombieCountRendererText: function (val) {
        if (val == '') {
            return 'included';
        } else {
            return val;
        }
    },

    formatAdvisoryResponse: function (response) {
        var languageHtml = sfw.util.Advisory.formatLanguage(response.data.languages, response.data.details.vuln_id, 0);

        if ((response.data.affectedOs.length > 0) && (response.data.affectedSoftware.length > 0)) {
            response.data.affectedtitle = 'Affected ' + 'Operating System' + 's & ' + 'Software';
        } else if ((response.data.affectedOs.length <= 0) && (response.data.affectedSoftware.length > 0)) {
            response.data.affectedtitle = 'Affected ' + 'Software';
        } else if ((response.data.affectedOs.length > 0) && (response.data.affectedSoftware.length <= 0)) {
            response.data.affectedtitle = 'Affected ' + 'Operating System' + 's';
        }
        response.data.cve = sfw.util.Advisory.formatCveReferences(response.data.cve);
        response.data.languageHtml = languageHtml;
        response.data.affectedOsLength = response.data.affectedOs.length;
        response.data.affectedSoftwarelength = response.data.affectedSoftware.length;
        response.data.cvssScoreData = sfw.util.Advisory.formatCvssScore(response.data);
        response.data.cvss3ScoreData = sfw.util.Advisory.formatCvss3Score(response.data);
        response.data.cvss4ScoreData = sfw.util.Advisory.formatCvss4Score(response.data);
        response.data.advisoryWhereFormatted = sfw.util.Advisory.makeWhere(response.data.advisoryWhere);
        response.data.advisoryImpactFormatted = sfw.util.Advisory.formatImpact(response.data.advisoryImpact);
        response.data.details.criticalityImage = sfw.util.SharedFunctions.returnCriticalityImage(response.data.details.vuln_critical_boolean, response.data.advisoryCriticalityText);
        response.data.content = sfw.util.Advisory.generateDetailsPanel(response.data);

        return response;
    },

    dateOutputMySqlDateTime: function (oDate, utc) {
        var utcMode = (typeof utc === 'boolean') ? utc : false;
        return this.dateOutputSimpleDate(oDate, utcMode) + " " + this.dateOutputTimeSecs(oDate, utcMode);
    },

    dateOutputTimeSecs: function (oDate, utc) {
        var secs = (typeof utc === 'boolean' && utc) ? oDate.getUTCSeconds() : oDate.getSeconds();

        return sfw.Default.dateOutputTime(oDate, utc) + ":" + (secs < 10 ? '0' : '') + secs;
    },

    dateOutputSimpleDate: function (oDate, utc) {
        var utcMode = (typeof utc === 'boolean') ? utc : false,
            month = utcMode ? (oDate.getUTCMonth() + 1) : (oDate.getMonth() + 1),
            date = utcMode ? oDate.getUTCDate() : oDate.getDate();

        return (utcMode ? oDate.getUTCFullYear() : oDate.getFullYear()) + '-' + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
    },

    saveScheduleReport: function (btn) {
        var formValues = btn.up('window').down('form').getValues();

        if (formValues.creation_time === '') {
            formValues.creation_time = sfw.Default.dateOutputMySqlDateTime(new Date(), true);
        } else {
            formValues.creation_time = sfw.Default.dateOutputMySqlDateTime(sfw.Util.dateCreate(formValues.creation_time), true);
        }

        formValues.first_run_time = sfw.Default.dateOutputMySqlDateTime(sfw.Util.dateCreate(formValues.xfirstrundate + ' ' + formValues.xfirstruntime), true);


        var params = {};
        params.name = formValues.name;
        params.queryString = formValues.queryString;
        params.exportType = formValues.exportType;
        params.output_file = formValues.output_file;
        params.xfirstrundate = formValues.xfirstrundate;
        if (formValues.run_once) {
            params.run_once = formValues.run_once;
        }

        if (formValues.frequency_unit) {
            params.frequency_unit = formValues.frequency_unit;
        }

        if (formValues.id) {
            params.id = formValues.id;
        } else {
            params.id = '';
        }
        params.creation_time = formValues.creation_time;
        params.xfirst_run_time = formValues.xfirstruntime;
        params.table = Ext.ComponentQuery.query('#table')[0].getRawValue();
        params.first_run_time = formValues.first_run_time;

        Ext.Ajax.request({
            url: 'action=ajaxapi_scheduled_exports&which=save',
            method: 'POST',
            dataType: 'json',
            params: params,
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);
                if (response.success) {
                    Ext.Msg.alert("Success", "Scheduled Export was successfully saved");
                    if(typeof Ext.getCmp('scheduledExport_grid') !== 'undefined'){
                        Ext.getCmp('scheduledExport_grid').getStore().reload();
                    }
                } else {
                    Ext.Msg.alert("Failed", response.msg);
                }

                btn.up('window').destroy();
            },
            failure: function (response, opts) {
                Ext.Msg.alert("Unexpected Error", 'Unable to save Schedule Report');
            }
        });
    },

    renderOsOrProgs: function (val) {
        // indentional == because some calls supply string '1' instead of int
        if (val == 1) {
            return 'OS';
        } else {
            return 'Program';
        }
    },

    renderLink: function (value, metaData, record, rowIndex, colIndex, store) {
        var isSPS = true,
            isVPM = true;
        //CSIL-10174-Adding the below if condition to get correct download links while filtering SPS/VPM
        if(typeof Ext.ComponentQuery.query('#smartgroupcombo')[0] !== 'undefined'){
            if (typeof Ext.ComponentQuery.query('#smartgroupcombo')[0].getValue() !== "undefined") {
                if (Ext.ComponentQuery.query('#smartgroupcombo')[0].getValue()  == 'VPM') {
                    isSPS = false;
                } else if (Ext.ComponentQuery.query('#smartgroupcombo')[0].getValue() == 'SPS') {
                    isVPM = false;
                }
            }
        }

        var pattern = 'dl.secunia.com/SPS/'; // This is the part of URL to know Secunia enhanced packages
        if (value.indexOf(pattern) !== -1 && isSPS) {
            downloadSiteReplace = "https://dl.csi7.secunia.com/?action=download&token=";
            fileName = value.split("/SPS/");
            value = downloadSiteReplace + sfw.util.Globals.UserID + "&package=" + fileName[1] + '&src=sps' + '&cstid=' + sfw.util.Auth.LoginDetails.account.settings['cst_id'];
            return (value ? '<a href="#" style="color:blue;" onclick="sfw.util.Default.externalURL(\'' + value.replace(/'/g, "\\'") + '\')">' + 'Download' + '</a>' : '-');
        } else if (parseInt(record.data.vpm_id, 10) > 0 && isVPM) {
            if (false !== sfw.util.Auth.LoginDetails.isVPMModuleEnabled) {
                value = sfw.util.CommonConstants.patchDownloadURLPackage + '&token=F8859A92-C9C5-43F2-95D7-9A3E19FEC0B6&vpm_id=' + record.data.vpm_id + '&cstid=' + sfw.util.Auth.LoginDetails.account.settings['cst_id'] + '&src=vpm';
                return (value ? '<a href="#" style="color:blue;" onclick="sfw.util.Default.externalURL(\'' + value.replace(/'/g, "\\'") + '\')">' + 'Download (VPM)' + '</a>' : '-');
            } else {
                return ('<a href="#" style="color:blue;" onClick="Ext.Msg.alert( \'Vendor Patch Module\', \'Vendor Patch Module is not enabled, please contact to support.\' );">Download (VPM)</a> ');
            }
        } else {

            return (value ? '<a href="#" style="color:blue;" onclick="sfw.util.Default.externalURL(\'' + value.replace(/'/g, "\\'") + '\')">' + 'Download' + '</a>' : '-');
        }
    },

    countryCode: new Ext.data.ArrayStore({
        fields: ['countryName', 'countryCode'],
        data: [['...', ''],
            ['United States of America', '1'],
            ['Denmark', '45']
            , ['Afghanistan', '93']
            , ['Albania', '355']
            , ['Algeria', '213']
            , ['Andorra', '376']
            , ['Angola', '244']
            , ['Anguilla', '1 264']
            , ['Antarctica (Australian bases)', '6721']
            , ['Antigua and Barbuda', '1 268']
            , ['Argentina', '54']
            , ['Armenia', '374']
            , ['Aruba', '297']
            , ['Ascension', '247']
            , ['Australia', '61']
            , ['Austria', '43']
            , ['Azerbaijan', '994']
            , ['Bahamas', '1 242']
            , ['Bahrain', '973']
            , ['Bangladesh', '880']
            , ['Barbados', '1 246']
            , ['Belarus', '375']
            , ['Belgium', '32']
            , ['Belize', '501']
            , ['Benin', '229']
            , ['Bermuda', '1 441']
            , ['Bhutan', '975']
            , ['Bolivia', '591']
            , ['Bosnia and Herzegovina', '387']
            , ['Botswana', '267']
            , ['Brazil', '55']
            , ['British Indian Ocean Territory', '246']
            , ['British Virgin Islands', '1 284']
            , ['Brunei', '673']
            , ['Bulgaria', '359']
            , ['Burkina Faso', '226']
            , ['Burundi', '257']
            , ['Cambodia', '855']
            , ['Cameroon', '237']
            , ['Canada', '1']
            , ['Cape Verde', '238']
            , ['Cayman Islands', '1 345']
            , ['Central African Republic', '236']
            , ['Chad', '235']
            , ['Chile', '56']
            , ['China', '86']
            , ['Colombia', '57']
            , ['Comoros', '269']
            , ['Congo, Democratic Republic of the', '243']
            , ['Congo, Republic of the', '242']
            , ['Cook Islands', '682']
            , ['Costa Rica', '506']
            , ['Cote d\'Ivoire', '225']
            , ['Croatia', '385']
            , ['Cuba', '53']
            , ['Cyprus', '357']
            , ['Czech Republic', '420']
            , ['Djibouti', '253']
            , ['Dominica', '1 767']
            , ['Dominican Republic', '1 809']
            , ['Dominican Republic', '1 829']
            , ['Dominican Republic', '1 849']
            , ['East Timor', '670']
            , ['Ecuador', '593']
            , ['Egypt', '20']
            , ['El Salvador', '503']
            , ['Equatorial Guinea', '240']
            , ['Eritrea', '291']
            , ['Estonia', '372']
            , ['Ethiopia', '251']
            , ['Falkland Islands', '500']
            , ['Faroe Islands', '298']
            , ['Fiji', '679']
            , ['Finland', '358']
            , ['France', '33']
            , ['French Guiana', '594']
            , ['French Polynesia', '689']
            , ['Gabon', '241']
            , ['Gambia', '220']
            , ['Gaza Strip', '970']
            , ['Georgia', '995']
            , ['Germany', '49']
            , ['Ghana', '233']
            , ['Gibraltar', '350']
            , ['Greece', '30']
            , ['Greenland', '299']
            , ['Grenada', '1 473']
            , ['Guadeloupe', '590']
            , ['Guam', '1 671']
            , ['Guatemala', '502']
            , ['Guinea', '224']
            , ['Guinea-Bissau', '245']
            , ['Guyana', '592']
            , ['Haiti', '509']
            , ['Honduras', '504']
            , ['Hong Kong', '852']
            , ['Hungary', '36']
            , ['Iceland', '354']
            , ['India', '91']
            , ['Indonesia', '62']
            , ['Iraq', '964']
            , ['Iran', '98']
            , ['Ireland (Eire)', '353']
            , ['Israel', '972']
            , ['Italy', '39']
            , ['Jamaica', '1 876']
            , ['Japan', '81']
            , ['Jordan', '962']
            , ['Kazakhstan', '7']
            , ['Kenya', '254']
            , ['Kiribati', '686']
            , ['Kuwait', '965']
            , ['Kyrgyzstan', '996']
            , ['Laos', '856']
            , ['Latvia', '371']
            , ['Lebanon', '961']
            , ['Lesotho', '266']
            , ['Liberia', '231']
            , ['Libya', '218']
            , ['Liechtenstein', '423']
            , ['Lithuania', '370']
            , ['Luxembourg', '352']
            , ['Macau', '853']
            , ['Macedonia, Republic of', '389']
            , ['Madagascar', '261']
            , ['Malawi', '265']
            , ['Malaysia', '60']
            , ['Maldives', '960']
            , ['Mali', '223']
            , ['Malta', '356']
            , ['Marshall Islands', '692']
            , ['Martinique', '596']
            , ['Mauritania', '222']
            , ['Mauritius', '230']
            , ['Mayotte', '262']
            , ['Mexico', '52']
            , ['Micronesia, Federated States of', '691']
            , ['Moldova', '373']
            , ['Monaco', '377']
            , ['Mongolia', '976']
            , ['Montenegro', '382']
            , ['Montserrat', '1 664']
            , ['Morocco', '212']
            , ['Mozambique', '258']
            , ['Myanmar', '95']
            , ['Namibia', '264']
            , ['Nauru', '674']
            , ['Netherlands', '31']
            , ['Netherlands Antilles', '599']
            , ['Nepal', '977']
            , ['New Caledonia', '687']
            , ['New Zealand', '64']
            , ['Nicaragua', '505']
            , ['Niger', '227']
            , ['Nigeria', '234']
            , ['Niue', '683']
            , ['Norfolk Island', '6723']
            , ['North Korea', '850']
            , ['Northern Ireland', '44 28']
            , ['Northern Mariana Islands', '1 670']
            , ['Norway', '47']
            , ['Oman', '968']
            , ['Pakistan', '92']
            , ['Palau', '680']
            , ['Panama', '507']
            , ['Papua New Guinea', '675']
            , ['Paraguay', '595']
            , ['Peru', '51']
            , ['Philippines', '63']
            , ['Poland', '48']
            , ['Portugal', '351']
            , ['Qatar', '974']
            , ['Reunion', '262']
            , ['Romania', '40']
            , ['Russia', '7']
            , ['Rwanda', '250']
            , ['Saint-Barthélemy', '590']
            , ['Saint Helena', '290']
            , ['Saint Kitts and Nevis', '1 869']
            , ['Saint Lucia', '1 758']
            , ['Saint Martin (French side)', '590']
            , ['Saint Pierre and Miquelon', '508']
            , ['Saint Vincent and the Grenadines', '1 784']
            , ['Samoa', '685']
            , ['Sao Tome and Principe', '239']
            , ['Saudi Arabia', '966']
            , ['Senegal', '221']
            , ['Serbia', '381']
            , ['Seychelles', '248']
            , ['Sierra Leone', '232']
            , ['Singapore', '65']
            , ['Slovakia', '421']
            , ['Slovenia', '386']
            , ['Solomon Islands', '677']
            , ['Somalia', '252']
            , ['South Africa', '27']
            , ['South Korea', '82']
            , ['South Sudan', '211']
            , ['Spain', '34']
            , ['Sri Lanka', '94']
            , ['Sudan', '249']
            , ['Suriname', '597']
            , ['Swaziland', '268']
            , ['Sweden', '46']
            , ['Switzerland', '41']
            , ['Syria', '963']
            , ['Taiwan', '886']
            , ['Tajikistan', '992']
            , ['Tanzania', '255']
            , ['Thailand', '66']
            , ['Togo', '228']
            , ['Tokelau', '690']
            , ['Tonga', '676']
            , ['Trinidad and Tobago', '1 868']
            , ['Tunisia', '216']
            , ['Turkey', '90']
            , ['Turkmenistan', '993']
            , ['Turks and Caicos Islands', '1 649']
            , ['Tuvalu', '688']
            , ['Uganda', '256']
            , ['Ukraine', '380']
            , ['United Arab Emirates', '971']
            , ['United Kingdom', '44']
            , ['Uruguay', '598']
            , ['Uzbekistan', '998']
            , ['Vanuatu', '678']
            , ['Venezuela', '58']
            , ['Vietnam', '84']
            , ['U.S. Virgin Islands', '1 340']
            , ['Wallis and Futuna', '681']
            , ['West Bank', '970']
            , ['Yemen', '967']
            , ['Zambia', '260']
            , ['Zimbabwe', '263']]
    }),

    setForAccount: function (option, value, account) {
        globals = sfw.util.Globals;
        var self = this;
        Ext.Ajax.request({
            url: globals.apiPath() + 'action=options&which=setOptionForAccount'
            , method: 'POST'
            , params: {
                option: option
                , value: value
                , account: account
            }
            , success: function (data) {
                var response = {};
                response = Ext.util.JSON.decode(data.responseText);
                if (response.success === true) {
                    // update local cached version
                    //self.options[option] = value;
                } else {
                    sfw.util.Debug.log('Failed to set option for account.', account);
                }
            }
            , failure: function () {
                sfw.util.Debug.log('Could not set option.');
            }
        });
    },

    set: function (option, value) {
        const me = this,
            globals = sfw.util.Globals;
        Ext.Ajax.request({
            url: globals.apiPath() + 'action=options&which=setOption',
            method: 'POST',
            params: {
                option: option,
                value: value
            },
            success: function (data) {
                var response = Ext.decode(data.responseText, true);
                if (response && response.success === true) {
                    // update local cached version
                    me.options[option] = value;
                } else {
                    sfw.util.Debug.log('Failed to set option.');
                }
            },
            failure: function () {
                sfw.util.Debug.log('Could not set option.');
            }
        });
    },

    renderADSiteName: function (siteName, adContainer) {
        if (adContainer && sfw.ActiveDirectorySettings.hasADIntegration()) {
            // By default we show the full AD path
            if (parseInt(LoginDetails.account.configuration.get("AD_SITE_SHOW_DISTINGUISHED_NAME"), 2)) {
                // Show full AD path
                return adContainer;
            } else {
                // Show short path
                return '<div ext:qtitle="Fully Qualified Domain Name" ext:qtip="' + Ext.util.Format.htmlEncode(adContainer) + '">' + siteName + '</div>';
            }
        }
        return siteName;
    },

    refreshInstallation: function () {
        sfw.Default.reloadViewInstallations();
        sfw.Default.reloadAllAdvisories();
        var installationView = Ext.ComponentQuery.query('#installationGrid')[0];
        var product_id = installationView.productId;
        var sGCombo = Ext.ComponentQuery.query('#productSgCombo')[0].value;
        var overviewStore = Ext.getStore('installationoverview');
        overviewStore.load({
            params: {
                productId: product_id,
                smartGroupId: sGCombo,
            },
            callback(record, operation, success) {

                var overViewData = sfw.Default.setOverviewData(record, installationView)
            }
        });
    },

    reloadViewInstallations: function () {

        var installations = Ext.getStore('viewinstallations');
        var installationView = Ext.ComponentQuery.query('#installationGrid')[0];
        var sGCombo = Ext.ComponentQuery.query('#productSgCombo')[0].value;
        var secure = Ext.ComponentQuery.query('#secureCheckBox')[0].value;
        var insecure = Ext.ComponentQuery.query('#insecureCheckBox')[0].value;
        var eol = Ext.ComponentQuery.query('#eolCheckBox')[0].value;
        var searchText = Ext.ComponentQuery.query('#searchinstalations')[0].value;
        var product_id = installationView.productId;

        if(!insecure && !eol && !secure && !sGCombo && !product_id && !searchText){
            return false;
        }

        installations.getProxy().setExtraParams({
            'product_id': product_id,
            'smartGroupId': sGCombo,
            'patched': secure,
            'insecure': insecure,
            'end_of_life': eol,
            'host_name':searchText
        });
        installations.loadPage(1, {
            callback: function () {
            },
            failure: function () {
            }
        });
    },

    handleSpecialKeysInstallations: function (field, e) {

        if (e.getKey() == e.ENTER) {
            sfw.Default.reloadViewInstallations();
        }
    },

    reloadAllAdvisories: function () {

        var advisories = Ext.getStore('alladvisories');
        var installationView = Ext.ComponentQuery.query('#installationGrid')[0];
        var sGCombo = Ext.ComponentQuery.query('#productSgCombo')[0].value;
        var product_id = installationView.productId;

        advisories.getProxy().setExtraParams({
            'product_id': product_id,
            'smartGroupId': sGCombo
        });
        advisories.loadPage(1, {
            callback: function () {

            },
            failure: function () {
            }
        });

    },

    copyPath: function (event, target, options) {

        var copyPath = options.extra.data.path;

        const el = document.createElement('textarea');
        el.value = copyPath;
        el.setAttribute('readonly', '');
        el.style.position = 'absolute';
        el.style.left = '-9999px';
        document.body.appendChild(el);
        const selected =
            document.getSelection().rangeCount > 0 ? document.getSelection().getRangeAt(0) : false;
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        if (selected) {
            document.getSelection().removeAllRanges();
            document.getSelection().addRange(selected);
        }

    },

    checkThreatEnabled: function (grid) {

        var columns = grid.down('headercontainer').getGridColumns();
        if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
            columns[3].destroy();
        }
    },

    reloadOverviewTab: function () {

        var overViewTab = Ext.getStore('InstallationOverview');
        var installationView = Ext.ComponentQuery.query('#installationGrid')[0];
        var sGCombo = Ext.ComponentQuery.query('#productSgCombo')[0].value;
        var product_id = installationView.productId;

        overViewTab.getProxy().setExtraParams({
            'product_id': product_id,
            'smartGroupId': sGCombo
        });
        overViewTab.loadPage(1, {
            callback: function (record, operation, success) {
                var overViewData = sfw.Default.setOverviewData(record, installationView)
            },
            failure: function () {
            }
        });
    },
    viewInstallation: function (event, target, options) {
        var product_name = options.extra.data.product_name;
        var product_id = options.extra.data.product_id;
        var smartGroupId = options.extra.smartGroupId;
        var me = this;

        var viewInstallationsWindow = Ext.create("sfw.view.commonpopupwindows.ViewInstallations",
            {
                productId: product_id,
                smartGroupId: smartGroupId,
                listeners: {
                    afterrender: function () {

                    }
                }
            });
        var overviewStore = Ext.create("sfw.store.common.InstallationOverview");
        overviewStore.load({
            params: {
                productId: product_id,

            },
            callback(record, operation, success) {
                var overViewData = sfw.Default.setOverviewData(record, viewInstallationsWindow)
            }
        });

        viewInstallationsWindow.show();
        viewInstallationsWindow.setTitle(product_name);
    },

    setOverviewData(record, viewInstallationsWindow) {

        var missingKBsFound = record[0].data.missingKBsFound;
        var uniq_totalcount_mskbs = record[0].data.uniq_totalcount_mskbs;
        var missingMsKBsHtml = '';

        if (missingKBsFound) {
            missingMsKBsHtml = "<table width='280'><tr><td colspan='2' class='ReportTable ReportTableHeading'><b>Installations Missing Microsoft Updates</b></td></tr>";
            for (var missing_kb_update in uniq_totalcount_mskbs) {
                if (uniq_totalcount_mskbs.hasOwnProperty(missing_kb_update) && typeof uniq_totalcount_mskbs[missing_kb_update] !== 'function') {
                    missingMsKBsHtml += "<tr><td class='ReportTable ReportTableName'>" + missing_kb_update + "</td><td class='ReportTable Number' align='right'>" + uniq_totalcount_mskbs[missing_kb_update] + "</td></tr>";
                }
            }
            missingMsKBsHtml += "</table>"
            viewInstallationsWindow.getViewModel().set('kbtable', missingMsKBsHtml)
        }

        record[0].data.createdAt = sfw.Default.gridRenderUTCDateInLocaltimeFormat(record[0].data.createdAt, sfw.util.Globals.dateShortOutput);
        var piedata = sfw.Default.setInstallationPieData(record[0].data.countPatched, record[0].data.countEndOfLife, record[0].data.countInsecure)
        viewInstallationsWindow.getViewModel().set('installationdata', record[0].data)
        var store = viewInstallationsWindow.getViewModel().getStore('overviewproductpiestore');

        store.loadData(piedata);

    },

    setInstallationPieData: function (secure, endoflife, insecure) {

        var chartData = [];
        var securescore = {};
        var endoflifescore = {};
        var insecurescore = {};
        var total = parseFloat(secure) + parseFloat(endoflife) + parseFloat(insecure);
        securescore.label = 'Secure' + ' (' + secure + ')';
        securescore.data = Math.round((secure / total) * 100);
        securescore.count = secure;

        endoflifescore.label = 'End-of-Life' + ' (' + endoflife + ')';
        endoflifescore.data = Math.round((endoflife / total) * 100);
        endoflifescore.count = endoflife;

        insecurescore.label = 'Insecure' + ' (' + insecure + ')';
        insecurescore.data = Math.round((insecure / total) * 100);
        insecurescore.count = insecure;

        chartData.push(securescore);
        chartData.push(endoflifescore);
        chartData.push(insecurescore);
        return chartData;

    },

    viewInstallationdblcick: function (grid, record, event) {
        var options = {};
        options.extra = record;
        sfw.Default.viewInstallation(grid, null, options);
    },

    gridRenderUTCDateInLocaltimeFormat: function (value, format) {
        if (value) {
            var date = sfw.Util.dateCreate(value, true);
            return Ext.util.Format.date(date, format);
        } else {
            return '-';
        }
    },

    // Function for returning different standard date formats
    returnDateFormat: function (sDate, iFormat, utc) {
        if (!sDate) {
            return '-';
        }

        var oDate = sfw.util.Util.dateCreate(sDate, utc);

        // Choose format
        switch (iFormat) {
            case 1: // Example: Apr. 3, 2007 (14:25)
                //return date('M. j, Y (H:i)', $oDate);
                return this.dateOutputDate(oDate, utc) + ' (' + this.dateOutputTime(oDate, utc) + ')';

            case 2: // Example: Apr. 3 (14:25)
                //return date('M. j (H:i)', $oDate);
                return this.dateOutputDate(oDate, utc) + ' (' + this.dateOutputTime(oDate, utc) + ')';

            case 3: // Example: Apr. 3, 2007
                //return date('M. j, Y', $oDate);
                return this.dateOutputDate(oDate, utc);

            case 4 :
                return this.dateOutputSimpleDate(oDate, utc);

            case 5 : // Example: 2007-03-04 14:25
                //return date('Y-m-d H:i', $oDate);
                return this.dateOutputSimpleDate(oDate, utc) + ' ' + this.dateOutputTime(oDate, utc);

            default: // Default, Example: 2007-03-04 14:25:00
                //return date('Y-m-d H:i:s', $oDate);
                return this.dateOutputSimpleDate(oDate, utc) + ' ' + this.dateOutputTimeSecs(oDate, utc);
        }
    },

    // Custom equivalent of PHP's 'htmlSpecialChars()', however, this one also encodes "'"
    htmlSpecialChars: function (str) {
        var ret = '';
        for (var i = 0; i < str.length; ++i) {
            switch (str.charAt(i)) {
                case '&':
                    ret += '&amp;';
                    break;
                case '<':
                    ret += '&lt;';
                    break;
                case '>':
                    ret += '&gt;';
                    break;
                case "'":
                    ret += '&#39;';
                    break;
                default :
                    ret += str.charAt(i);
                    break;
            }
        }
        return ret;
    },

    deletePartionUserAccounts: function (deleteIdList, attemptedDeletes, managementtype) {
        var params = {
            'accountIdList': deleteIdList
        };
        Ext.Ajax.request({
            url: 'action=account_management&which=delete',
            method: 'POST',
            dataType: 'json',
            params: params,
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);
                var is_template_exist = response.is_template_account_exist;
                if (is_template_exist && sfw.util.Auth.LoginDetails.account.ssoSettings.template_account_id !== null) {
                    sfw.util.Auth.LoginDetails.account.ssoSettings.template_account_id = 0
                }
                var deletedAccounts = parseInt(response.data, 10);
                var msg, title;

                if (0 !== response.error_code) { // NOT-SUCCESS
                    title = 'Error';
                    // We don't actually know if some got deleted before something failed...
                    msg = "There was an error during deletion...";
                    Ext.Msg.show({
                        title: title
                        , msg: msg
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.ERROR
                    });
                } else {
                    title = 'Success';
                    // We went through normally - did we delete all selected
                    if (deletedAccounts == attemptedDeletes) {
                        msg = 'Selected account' + ((deletedAccounts > 1) ? 's were' : ' was') + ' successfully deleted.';
                    } else {
                        title = 'Possible Permissions Issue?';
                        if (1 == attemptedDeletes) {
                            msg = "The selected account could not be deleted.";
                        } else {
                            msg = (deletedAccounts ? ('Only ' + deletedAccounts) : 'None') + ' of the ' + attemptedDeletes + ' selected accounts could be deleted.';
                        }
                    }
                    Ext.Msg.show({
                        title: title
                        , msg: msg
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.INFO
                    });
                }
                if (managementtype == 'user') {
                    var userstore = Ext.getStore('usermanagement');
                    userstore.load();
                } else if (managementtype == 'partition') {
                    var partitionstore = Ext.getStore('partitionmanagement');
                    partitionstore.load();
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert('Error', 'Unexpected Error...');
            }
        });
    },

    evaluateCanDelete: function (dataRecord, managementtype) {

        var delAccountId = parseInt(dataRecord.data.account_id, 10);
        var delAccountIsAdmin = dataRecord.data.is_admin;

        if (sfw.util.Auth.LoginDetails.loginAccountId === delAccountId) {
            return false;
        }

        if (managementtype === 'user') {
            if (!sfw.util.Auth.LoginDetails.isPartitionAdmin && (delAccountIsAdmin != 0)) {
                return false;
            }
        }
        return true;
    },

    get: function (option, defaultValue) {
        if ('undefined' === typeof this.options[option] && 'undefined' !== typeof defaultValue) {
            return defaultValue;
        }
        return this.options[option];
    },

    add: function (option, value) {
        var self = this;
        self.options[option] = value;
    },

    threatScoreDefault: function (src) {
        if (src < 1) {
            return '-';
        } else {
            return src;
        }
    },

    saveLogNotification: function (btn, event) {
        var view = btn.up('window');
        var successMessage = null;
        if (Ext.ComponentQuery.query('#formType')[0].text == 'NEW') {
            successMessage = 'Notification Saved';
        } else if (Ext.ComponentQuery.query('#formType')[0].text == 'EDIT') {
            successMessage = 'Notification Updated';
        }

        var formValues = btn.up('form').getValues();

        var selectedEvents = view.down('#selectEvents').down('#selectedGrid');

        var selecteEventsData = selectedEvents.getSelection();

        var frequency_day = 0;

        if (formValues.frequency_day) {
            frequency_day = formValues.frequency_day
        }

        var frequency_time = '';
        if (formValues.frequency_time) {
            frequency_time = formValues.frequency_time
        }

        var frequency_time = sfw.Util.dateCreate(frequency_time);
        var utcFrequencyTime = sfw.Default.dateConvertLocalToUTC(frequency_time);
        var frequency_time = Ext.util.Format.date(utcFrequencyTime, sfw.util.Globals.hoursMinutesOutput)

        var notify = 0;
        if (formValues.notify) {
            notify = 1;
        }

        var email_list = [];

        var activity_list = [];

        for (i = 0; i < selecteEventsData.length; i++) {
            selectedEventId = parseInt(selecteEventsData[i].data.event_type, 10);
            activity_list.push(selectedEventId);
        }

        var params = {
            name: formValues.name,
            frequency: formValues.frequency,
            frequency_day: frequency_day,
            frequency_time: frequency_time,
            always_notify: notify,
            activity_list: Ext.util.JSON.encode(activity_list),
        };

        if (notificationId) {
            params.notification_id = notificationId;
        }

        if (formValues.defaultRecipients) {
            params.email_defaults = 1;
        } else {
            var selectedEmailrecipients = view.down('#emailRecipients').down('#selectedGrid');
            var selectedEmailrecipientsData = selectedEmailrecipients.getSelection();
            for (i = 0; i < selectedEmailrecipientsData.length; i++) {
                recipientAccountId = parseInt(selectedEmailrecipientsData[i].data.recipient_account_id, 10);
                email_list.push(recipientAccountId);
            }
            params.email_defaults = 0;
        }

        params.email_list = Ext.util.JSON.encode(email_list);

        Ext.Ajax.request({
            url: 'action=csi_notifications&which=create',
            method: 'POST',
            dataType: 'json',
            params: params,
            success: function (response) {
                var status = Ext.decode(response.responseText);
                switch (status.error) {
                    case 0:
                        btn.up('window').destroy();
                        Ext.getStore('activitylognotification').load();
                        Ext.Msg.alert('Success', successMessage);
                        break;
                    case 1:
                        Ext.Msg.alert('Error', 'Notification could not be saved.');
                        break;
                    case 40:
                        Ext.Msg.alert('Error', 'Notification could not be saved - no recipients selected');
                        break;
                    case 41:
                        Ext.Msg.alert('Error', 'Notification could not be saved - no events selected');
                        break;
                    default:
                        Ext.Msg.alert('Error', 'Unexpected error.');
                        break;
                }
            }
            , failure: function () {
                Ext.Msg.alert('Error', 'Unexpected error.');
            }
        });
    },

    renderInstallationPopup: function (vulnId, src) {
        var advisoryInstallationPopup = Ext.create("sfw.view.results.AdvisoryGridPopup", {
            vulnId: vulnId,
            org: typeof src === 'undefined' ? 0 : src
        });
        advisoryInstallationPopup.show();
        advisoryInstallationPopup.setTitle('SA' + vulnId + ' Affected Installations');
    },

    renderHostPopup: function (vulnId) {
        var advisoryHostPopup = Ext.create("sfw.view.results.AdvisoryGridHostPopup", {
            vulnId: vulnId
        });
        advisoryHostPopup.show();
        advisoryHostPopup.setTitle('SA' + vulnId + ' Affected Hosts');
    },

    renderProductPopup: function (vulnId) {
        var advisoryProductPopup = Ext.create("sfw.view.results.AdvisoryGridProductPopup", {
            vulnId: vulnId
        });
        advisoryProductPopup.show();
        advisoryProductPopup.setTitle('SA' + vulnId + ' Affected Products');
    },

    renderProductReport: function (value, metaData, record, rowIndex, colIndex, store) {

        var product_id = record.data.product_id;
        var sg_id = record.data.smartgroup_id;
        var vpm_id = record.data.vpm_id;
        var product_name = record.data.product_name;


        if (value > 0) {
            return '<a href="javascript:;"  onclick= "sfw.Default.renderProductReportView(' + product_id + ' , ' + sg_id + ', ' + vpm_id + ', \'' + product_name + '\')" >' + value + '</a>';
        } else {
            return value;
        }

    },

    getViewInstallationsVPM: function (event, target, options) {
        var product_id = options.extra.data.product_id;
        var sg_id = options.extra.data.smartgroup_id;
        var vpm_id = options.extra.data.vpm_id;
        var product_name = options.extra.data.product_name;
        sfw.Default.renderProductReportView(product_id, sg_id, vpm_id, product_name);

    },

    renderProductReportView: function (product_id, sg_id, vpm_id, product_name) {

        var productReportView = Ext.create("sfw.view.patching.VendorPatchProductReport",
            {
                productId: product_id,
                smartGroupId: sg_id,
                vpmId: vpm_id
            });

        productReportView.show();
        productReportView.setTitle(product_name);

    },

    impactRenderer: function (impactString, showHover) {

        var impactTypeTextArray = [
            'System Access'
            , 'Denial of Service'
            , 'Privilege Escalation'
            , 'Exposure of Sensitive Information'
            , 'Exposure of System Information'
            , 'Brute Force'
            , 'Manipulation of Data'
            , 'Spoofing'
            , 'Cross Site Scripting'
            , 'Security Bypass'];
        var trimmedString = impactString.replace(/(^,)|(,$)/g, "");
        var impactValues = trimmedString.split(',');
        var resultString = '';
        var index;
        for (var i = 0; i < impactValues.length; ++i) {
            index = impactValues[i] - 1;
            resultString += impactTypeTextArray[index] + ', ';
        }
        resultString = resultString.replace(/(, $)/g, "");

        var retVal = resultString;
        if (showHover) {
            retVal = sfw.Util.tipRenderer(resultString);
        }

        return retVal;
    },

    whereRenderer: function (whereValue) {
        var whereTypeTextArray = [
            'From Remote'
            , 'From Local Network'
            , 'From Local System'];

        var index = parseInt(whereValue, 10) - 1;
        var retVal = '-';
        if (0 <= index && index <= 2) {
            retVal = whereTypeTextArray[index];
        }
        return retVal;
    },

    getSelectorValues: function (apiRecord, itemSelector, selectedRecordsIds) {
        const self = this;

        var selectedGrid = itemSelector.down("#selectedGrid");
        var selectedStore = selectedGrid.getStore();
        if (typeof selectedRecordsIds !== 'undefined' && typeof selectedRecordsIds !== 'string') {
            selectedStore.add(selectedRecordsIds);
            return;
        }

        var url = "";
        if (typeof apiRecord.action !== 'undefined') {
            url += "action=" + apiRecord.action;
        }
        if (typeof apiRecord.which !== 'undefined') {
            url += "&which=" + apiRecord.which;
        }
        if (typeof apiRecord.module_id !== 'undefined') {
            url += "&module=" + apiRecord.module_id;
        }
        if (typeof apiRecord.method_id !== 'undefined') {
            url += "&method=" + apiRecord.method_id;
        }
        if (typeof apiRecord.instance_id !== 'undefined') {
            url += "&instance_id=" + apiRecord.instance_id;
        }
        if (typeof apiRecord.withSmartGroupType !== 'undefined') {
            url += "&withSmartGroupType=" + apiRecord.withSmartGroupType;
        }
        if (typeof apiRecord.smartGroupTextType !== 'undefined') {
            url += "&smartGroupTextType=" + apiRecord.smartGroupTextType;
        }
        if (typeof apiRecord.start !== 'undefined') {
            url += "&start=" + apiRecord.start;
        }
        if (typeof apiRecord.limit !== 'undefined') {
            url += "&limit=" + apiRecord.limit;
        }

        if (typeof apiRecord.product_id !== 'undefined') {
            url += "&product_id=" + apiRecord.product_id;
        }

        Ext.Ajax.request({
            url: url,
            method: 'GET',
            success: function (data) {
                var response = Ext.util.JSON.decode(data.responseText);
                var recordsToAdd = [];
                if (typeof selectedRecordsIds === 'string') {
                    selectedRecordsIds = selectedRecordsIds.split(",");
                    response.data.rows.forEach(function (data, index) {
                        if (selectedRecordsIds.indexOf("" + data[selectedStore.config.idProperty]) !== -1) {
                            recordsToAdd.push(data);
                        }
                    });
                } else {
                    recordsToAdd = response.data.rows;
                }
                selectedStore.add(recordsToAdd);
            },
            failure: function () {
                // Ext.Msg.alert("Unexpected Error", "Unable to download report.");
            }
        });

    },

    // Method for setting the value of an EXT combo and HTML docode it in the process
    setComboValueHTMLDecode: function (combo, value) {
        if (typeof value !== "undefined") {
            combo.setValue(value);
        }
        combo.setRawValue(this.htmlSpecialCharsDecodeAlsoQuot(combo.getRawValue())); // HTML Decode selected string, since this is displayed in an standard-input field
    },

    defaulEmailRecipients: function (notification_id, module_id, emailRecipients, mobileRecipients = null) {
        Ext.Ajax.request({
            url: sfw.util.Globals.apiPath()
            , params: {
                action: 'recipients'
                , which: 'get_defaults'
                , instance_id: notification_id
                , module_id: module_id
            }
            , method: 'GET'
            , success: function (responseJson) {
                var response = Ext.decode(responseJson.responseText);
                emailRecipients.down('#defaultRecipients').setValue(response.email_defaults);

                if (response.email_defaults == 1) {
                    emailRecipients.down('#localItemSelector').setDisabled(true);
                }
                if (mobileRecipients && response.mobile_defaults == 1) {
                    mobileRecipients.down('#defaultMobileRecipients').setValue(response.mobile_defaults);
                    mobileRecipients.down('#localItemSelector').setDisabled(true);
                }
            }
            , failure: function () {
                Ext.Msg.alert('Error', 'Unexpected error.');
            }
        });
    },


    /**
     * @method
     * @public
     * Stores a value which persists across CSI reloads.
     * Inserts or Replaces Value for key 'name'
     * These values are TEMPORARY as they are not sent to the server!
     * In CSI 7 a cookie is used for persistent storage whichs means we have only 4KiB available.
     * @param {String} name
     * @param Mixed value
     */
    persistentSetValue: function (name, value) {
        Ext.state.Manager.set(name, value);
        return true;
    },

// Get a value from our persistent store
    persistentGetValue: function (name) {
        return Ext.state.Manager.get(name);
    },

    persistentClearValue: function (name) {
        Ext.state.Manager.clear(name);
        return true;
    },

    productSGEditemplate:function(title,description, rules){
        Ext.ComponentQuery.query('window[xtype=sfw.smartGroupTemplateWindow]')[0].destroy();
        var rules = JSON.parse(rules.replace(/%20/g,'"'));

        var productSgWindow = Ext.ComponentQuery.query('window[xtype=smart-group-dialog]')[0];
        var productData = productSgWindow.getViewModel().get('record');

        productSgWindow.down('#smartGroupNameId').setValue(title + ' (FROM TEMPLATE - MODIFY)' );
        productSgWindow.down('#smartGroupDescriptionId').setValue(description + ' (FROM TEMPLATE - MODIFY)');
        if(!Ext.isEmpty(productData)){
            productData.data.template = true;
            productSgWindow.getViewModel().set('record', productData);
        }
        productSgWindow.down('#criterialFieldSetId').removeAll();
        productSgWindow.down('#customColumnsId').removeAll();
        productSgWindow.down('#selectOptionRGId').setValue({selectOption: 1});
        productSgWindow.down('#businessImpactId').setValue(productSgWindow.down('#businessImpactId').getStore().getAt(0));
        productSgWindow.down('#logicTypeId').setValue(productSgWindow.down('#logicTypeId').getStore().getAt(0));
        productSgWindow.getViewModel().set('rules', rules);
        productSgWindow.getController().onSGAfterRender();
    },
    activeXMessage:function(message){
        if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
            Ext.Msg.show({
                title: 'Browser Not Compatible',
                msg: 'Please use IE browser and install Software Vulnerability Manager Plugin to use this feature.',
                buttons: Ext.Msg.OK
            });
            return;
        }else{
            Ext.Msg.show({
                title: 'Browser Not Compatible',
                msg: message,
                buttons: Ext.Msg.OK
            });
            return;
        }
    },
    patchPublisherconnectionTypes: function (val) {
        if (val == '1') {
            return 'WSUS';
        } else if (val == '2') {
            return 'Intune';
        }else if (val == '3') {
            return 'Workspace ONE';
        }else if (val == '4') {
            return 'BigFix';
        }else if (val == '5') {
            return 'ConfigMgr';
        }else if (val == '6') {
            return 'Tanium';
        }
    },

    replaceURLWithHTMLLinks:function(text)
    {
        if(typeof text != 'undefined') {
            var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
            text.text_text = text.text_text.replace(exp, "<a href='$1' target='_blank'>$1</a>");
        }
        return text;
    },

    replaceSAIDWithHTMLLinks:function(text){
        if(typeof text != 'undefined') {
            text.text_text = text.text_text.replace(/\bSA\d+\b/g, (match) => {
                var said = match;
                var saidNumber = said.split('SA');
                return sfw.Default.renderSaid(saidNumber[1] );
            });
        }
        return text;
    },

    zombieFilesResult: function () {
        var zombieFilesResult = Ext.getStore('zombiefilesresults');
        zombieFilesResult.getProxy().setExtraParams({
            'device_id': nsi_device_id,
            'patched': Ext.ComponentQuery.query('#securecountzombiefiles')[0].getValue(),
            'insecure': Ext.ComponentQuery.query('#insecurecountzombiefiles')[0].getValue(),
            'eol': Ext.ComponentQuery.query('#endoflifecountzombiefiles')[0].getValue(),
        });
        zombieFilesResult.loadPage(1, {
            callback: function () {

            },
            failure: function () {

            }
        });
    },

    addToBlockList: function(event, target, options){
        var blocklistwindow = Ext.create('sfw.view.scanning.AddBlockListRuleWindow', {
            listeners: {
                afterrender: function (scanWindow) {
                    scanWindow.down('#impactListCheck').show();
                    scanWindow.down('#previewList').show();
                    blocklistwindow.down("title").setHtml("New Scan Path rule for Block List");
                    blocklistwindow.down("#scanlistpath").setValue(options.extra.data.path);
                    blocklistwindow.down('#savingType').setText('NEW');
                    blocklistwindow.down('#notFromScanPathGrid').setText('YES');
                }
            }
        });
        blocklistwindow.show();
    },

    reportFormatRenderer : function( value ) {

        if(value == 3){
            return 'PDF';
        }else if(value == 4){
            return 'CSV';
        }else {
            return '-';
        }
    },

    fileSizeRenderer: function( value ){

        if(value == 0){
            return '-';
        }
        return value + ' MB';

    },

    gridRendererTimeFormat: function( value ){

        //convert microseconds to seconds
        var timeElapsed = value/1000;

        var hours = Math.floor(timeElapsed / 3600);
        var minutes = Math.floor(timeElapsed % 3600 / 60);
        var seconds = Math.floor(timeElapsed % 3600 % 60);

        if(hours <= 0 && minutes <= 0 && seconds <= 0){
            return '-'
        }

        return hours + 'h ' + minutes + 'm ' + seconds + 's ';
    },

    addEditExtendedSupport: function(event, target, options){
        var extendedSupport = Ext.create('sfw.view.commonpopupwindows.AddExtendedSupport',{
            listeners: {
                afterrender: function (esuWindow) {

                    if(options.extra.data.esu_date){
                        extendedSupport.down("title").setHtml(options.extra.data.os_soft_name);
                        extendedSupport.down("#esuDate").setValue(options.extra.data.esu_date);
                        extendedSupport.down("#productId").setValue(options.extra.data.product_id);
                        extendedSupport.down("#productName").setValue(options.extra.data.os_soft_name);
                        extendedSupport.down("#addEdit").setValue('EDIT');

                        const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        const siteSelector = localItemSelector.createSiteSelector();
                        const selectSites = extendedSupport.down('#siteList');
                        selectSites.add(siteSelector);
                        selectSites.add(siteSelector);
                        sfw.Default.getSelectorValues({
                            action: 'esu_support',
                            which: 'getSelectedSites',
                            product_id: options.extra.data.product_id
                        }, siteSelector);

                        const hostSelector = localItemSelector.createHostSelector();
                        const selectHosts = extendedSupport.down('#hostList');
                        selectHosts.add(hostSelector);
                        sfw.Default.getSelectorValues({
                            action: 'esu_support',
                            which: 'getSelectedHosts',
                            product_id: options.extra.data.product_id
                        }, hostSelector);
                    }else{
                        extendedSupport.down("title").setHtml(options.extra.data.product_name);
                        extendedSupport.down("#productId").setValue(options.extra.data.product_id);
                        extendedSupport.down("#productName").setValue(options.extra.data.product_name);
                        extendedSupport.down("#addEdit").setValue('ADD');
                    }
                }
            }
        });
        extendedSupport.show();
    },

    addEsuData: function(btn){
        var formValues = btn.up('form').getValues();

        if(!formValues.esuDate){
            Ext.Msg.alert('Error', 'Please enter date.');
            return false;
        }

        if(!formValues.productId){
            Ext.Msg.alert('Error', 'Product ID is missing.');
            return false;
        }

        var view = btn.up('panel');
        var siteList = [];
        var groupId = [];
        var hostList = [];
        var nsiDeviceId = [];

        var selectedSites = view.down('#siteList').down('#selectedGrid');
        var selectedSitesData = selectedSites.getSelection();

        for (i = 0; i < selectedSitesData.length; i++) {
            groupId.push(parseInt(selectedSitesData[i].data.group_id, 10));
            siteList.push(selectedSitesData[i].data.group_name);
        }

        var selectedHosts = view.down('#hostList').down('#selectedGrid');
        var selectedHostsData = selectedHosts.getSelection();

        for (i = 0; i < selectedHostsData.length; i++) {
            nsiDeviceId.push(parseInt(selectedHostsData[i].data.nsi_device_id, 10));
            hostList.push(selectedHostsData[i].data.host_name);
        }

        var which = 'addESU'

        if(formValues.addEdit == 'EDIT'){
            which = 'editESU';
        }

        Ext.Ajax.request({
            url: 'action=esu_support&which='+which,
            method: 'POST',
            dataType: 'json',
            params: {
                'esu_date': formValues.esuDate,
                'product_id': formValues.productId,
                'product_name': formValues.productName,
                'site_list': Ext.util.JSON.encode(siteList),
                'group_id': Ext.util.JSON.encode(groupId),
                'host_list': Ext.util.JSON.encode(hostList),
                'nsi_device_id': Ext.util.JSON.encode(nsiDeviceId)
            },
            success: function (response, opts) {
                var response = Ext.decode(response.responseText, true);
                if(response.success && response.error_code == 0){
                    Ext.Msg.show({
                        title: 'Success',
                        msg: response.msg,
                        buttons: Ext.Msg.OK
                    });

                    if(typeof Ext.ComponentQuery.query('#extendedSupportGrid')[0] !== 'undefined'){
                        Ext.ComponentQuery.query('#extendedSupportGrid')[0].getStore().reload();
                    }

                    btn.up('window').destroy();
                }else {
                    Ext.Msg.show({
                        title: 'Error',
                        msg: response.msg,
                        buttons: Ext.Msg.OK
                    });
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert('Error', 'Unexpected Error Occured');
            }
        });
    },

    deleteExtendedSupport: function(event, target, options){

        Ext.Ajax.request({
            url: 'action=esu_support&which=deleteESU',
            method: 'POST',
            dataType: 'json',
            params: {
                'product_id': options.extra.data.product_id,
                'product_name': options.extra.data.os_soft_name
            },
            success: function (response, opts) {
                var response = Ext.decode(response.responseText, true);
                if(response.success && response.error_code == 0){
                    Ext.Msg.show({
                        title: 'Success',
                        msg: response.msg,
                        buttons: Ext.Msg.OK
                    });

                    if(typeof Ext.ComponentQuery.query('#extendedSupportGrid')[0] !== 'undefined'){
                        Ext.ComponentQuery.query('#extendedSupportGrid')[0].getStore().reload();
                    }

                }else {
                    Ext.Msg.show({
                        title: 'Error',
                        msg: response.msg,
                        buttons: Ext.Msg.OK
                    });
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert('Error', 'Unexpected Error Occured');
            }
        });

    },

    getAssignments: function(btn){

        susbcriptionDetails = btn.up('panel');
        var selectedConnectionsId = susbcriptionDetails.down('#singlePatchConnections').getValue();
        var selectedConnectionsNames = susbcriptionDetails.down('#singlePatchConnections').getRawValue();
        var selectedConnectionsNamesArray = selectedConnectionsNames.split(',');
        source_mode = 'manage_assignment';

        selectedConnectionData = [];

        for(var i=0; i<selectedConnectionsId.length; i++) {
            if(selectedConnectionsNamesArray[i].includes('Intune')) {
                selectedConnectionData.push({id: selectedConnectionsId[i], name: selectedConnectionsNamesArray[i]});
            }
        }

        if(typeof(selectedGroups) == 'undefined' ){
            selectedGroups = [];
        }

        initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));

        selectedConnectionId = null;

        isChanged = 0;

        var assignmnets = Ext.create('sfw.view.commonpanels.ManageAssignments',{
            source: 'manage_assignment',
            listeners: {
                afterrender: function (assignmnets) {
                    assignmnets.down('#connections_options').setStore(selectedConnectionData);
                    assignmnets.down('#connections_options').setValue(selectedConnectionData[0]['id']);
                    selectedConnectionId = selectedConnectionData[0]['id'];
                    assignmnets.down('#addGroups').setValue(sfw.Default.renderAddGroups(selectedConnectionData[0]['id']));
                    assignmnets.down('#addAllDevices').setValue(sfw.Default.renderAllDevices(selectedConnectionData[0]['id']));
                    assignmnets.down('#addAllUsers').setValue(sfw.Default.renderAllUsers(selectedConnectionData[0]['id']));
                    var assigmentsStore = assignmnets.getViewModel().getStore('assignments');
                    if(susbcriptionDetails.getViewModel().get('assignmentsData')){
                        if(selectedGroups.length == 0){
                            assigmentsStore.loadData(susbcriptionDetails.getViewModel().get('assignmentsData'));
                            selectedGroups = susbcriptionDetails.getViewModel().get('assignmentsData');
                            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
                        }else{
                            assigmentsStore.loadData(selectedGroups);
                            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
                        }
                        sfw.Default.filterManageAssigmentsGrid(assigmentsStore,selectedConnectionData[0]['id']);
                        sfw.Default.enableDisableDevicesUsers(assigmentsStore,selectedConnectionData[0]['id']);
                    }
                    assignmnets.down('#availableGrid').setVisible(false);
                    assignmnets.down('#uninstallGrid').setVisible(false);
                    assignmnets.down('#availableTitle').setVisible(false);
                    assignmnets.down('#uninstallTitle').setVisible(false);
                    //Remove include exclude column
                    var columns = assignmnets.down('headercontainer').getGridColumns();

                    var modeColumn = columns.find(function(column) {
                        return column.text === 'Included/Excluded';
                    });
                    if (modeColumn) {
                        modeColumn.destroy();

                    }

                }
            }
        });
        assignmnets.show();
        assignmnets.center();
        assignmnets.setTitle('Manage Assignments');
    },

    addAllDevices: function (connectionId, intent) {
        if (intent == 1) {
            var assignment = Ext.getStore('assignments');
        } else if (intent == 0) {
            var assignment = Ext.getStore('assignments_available');
        } else {
            var assignment = Ext.getStore('assignments_uninstall');
        }

        rec = assignment.add({
            group_name: 'All Devices',
            availability: 'As soon as possible',
            connection_id: connectionId,
            group_mode: 1,
            group_all: 2,
            intent: intent
        });

        Ext.ComponentQuery.query('#addAllDevices')[0].setValue(this.renderAllDevices(connectionId, true, intent));
        Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(this.renderAllDevicesAvailable(connectionId, true, intent));
        Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(this.renderAllDevicesUninstall(connectionId, true, intent));

        isChanged = 1;

    },

    addAllUsers: function (connectionId, intent) {

        if (intent == 1) {
            var assignment = Ext.getStore('assignments');
        } else if (intent == 0) {
            var assignment = Ext.getStore('assignments_available');
        } else {
            var assignment = Ext.getStore('assignments_uninstall');
        }
        rec = assignment.add({
            group_name: 'All Users',
            availability: 'As soon as possible',
            connection_id: connectionId,
            group_mode: 1,
            group_all: 1,
            intent: intent
        });

        Ext.ComponentQuery.query('#addAllUsers')[0].setValue(this.renderAllUsers(connectionId, true, intent));
        Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(this.renderAllUsers(connectionId, true, intent));
        Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(this.renderAllUsers(connectionId, true, intent));

        isChanged = 1;
    },

    renderAssignmentsDelete: function (value, meta, record) {
        var html = '-';
        var groupName = "'" + record.data.group_name + "'";
        var groupGuid = "'" + record.data.group_guid + "'";
        html = '<a href="#" style="color:dodgerblue;" onclick="sfw.Default.deleteGroups(' + groupName + ',' + groupGuid + ',' + record.data.group_all + ',' + record.data.intent + ')"><i class="fa fa-trash"></i></a>';
        return html;
    },


    addGroups: function(btn){
        var selected = Ext.ComponentQuery.query('#intunegroupsgrid')[0].getSelectionModel().getSelected();
        var connection_id = btn.up('window').getConnectionId();
        var intent = btn.up('window').getIntent();
        var storeDetails = sfw.Default.getStorebyIntent(intent);
        var assignment = storeDetails.store;
        var gridId1 = storeDetails.gridId1;
        var gridId2 = storeDetails.gridId2;



        var updatedSelectedGroups = [];

        selected.each(function(rec){
           var isRecordPresent = assignment.findBy(function (record,id){
                if(record.get('group_guid') == rec.get('guid') && record.get('connection_id') == connection_id){
                    return true;
                }
                return false;
            });

            var allData = [];
            allData = allData.concat(
                Ext.getStore('assignments').getRange(),
                Ext.getStore('assignments_available').getRange(),
                Ext.getStore('assignments_uninstall').getRange()
            );
            var isRecordPresentInAll = allData.some(function (record) {
                if (record.get('group_guid') == rec.get('guid') && record.get('connection_id') == connection_id && record.get('group_mode') == 1) {
                    return true;
                }
                return false;
            });
            var disable_include_exclude = 0;
            if (isRecordPresentInAll) {
                group_mode = 0;
                disable_include_exclude = 1;
            } else {
                 group_mode = 1;
                var gridStore1 = Ext.ComponentQuery.query(gridId1)[0].getStore();
                var gridStore2 = Ext.ComponentQuery.query(gridId2)[0].getStore();
                var record1 = gridStore1.findBy(function (record) {
                    return rec.get('guid')=== record.get('group_guid') && connection_id === record.get('connection_id');
                });

                if (record1 !== -1) {
                    sfw.Default.changeGridRowStatusEnableDisable(gridId1,gridStore1,record1,false);

                }
                var record2 = gridStore2.findBy(function (record) {
                    return rec.get('guid')=== record.get('group_guid') && connection_id === record.get('connection_id');
                });

                // Check if the record is present in the second grid

                if (record2 !== -1) {
                    sfw.Default.changeGridRowStatusEnableDisable(gridId2,gridStore2,record2,false);
                }
            }

            if (isRecordPresent == -1) {
                row = sfw.store.common.ManageAssignments({
                    group_name: rec.get('name'),
                    availability: 'As soon as possible',
                    connection_id: connection_id,
                    group_mode: group_mode,
                    group_all: 0,
                    intent: intent,
                    group_guid: rec.get('guid'),
                    disable:disable_include_exclude
                });
                assignment.insert(0, row);
            }

            var newGroups = {};
            newGroups.group_name = rec.data.name;
            newGroups.group_guid = rec.data.guid;
            newGroups.connection_id = selectedConnectionId;
            newGroups.group_all = 0;
            newGroups.availability = 'As soon as possible';
            newGroups.group_mode = group_mode;
            newGroups.intent = intent;

            var addNew = true;
            for(i = 0; i < selectedGroups.length; i++){
                if((selectedGroups[i].group_name == rec.data.name) && (selectedGroups[i].group_guid == rec.data.guid) && (selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].intent == intent)){
                    addNew = false;
                }
            }

            if(addNew){
                isChanged = 1;
                selectedGroups.push(newGroups);
            }
            updatedSelectedGroups.push(newGroups);

        });

        for(i = selectedGroups.length - 1; i >= 0; i--){
            var ifNotExists = true;
            if(updatedSelectedGroups.length > 0){
                for(j=0; j < updatedSelectedGroups.length; j++){
                    if((selectedGroups[i].group_name == updatedSelectedGroups[j].group_name) && (selectedGroups[i].group_guid == updatedSelectedGroups[j].group_guid) && (selectedGroups[i].connection_id == selectedConnectionId)){
                        ifNotExists = false;
                    }
                }

                if (ifNotExists) {
                    if ((selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].group_all == 0) && (selectedGroups[i].intent == intent)) {
                        var exists = updatedSelectedRecords.some(record => record.group_guid === selectedGroups[i].group_guid );
                        if(!exists){
                            selectedGroups.splice(i, 1);
                        }
                    }
                }
            }else{
                if((selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].group_all == 0)){
                    var exists = updatedSelectedRecords.some(record => record.group_guid === selectedGroups[i].group_guid );
                    if(!exists){
                        selectedGroups.splice(i, 1);
                    }
                }
            }
        }

        var assignmentRange = assignment.getRange();

        if(assignmentRange.length > 0) {
            for (var j = assignmentRange.length - 1; j >= 0; j--) {
                var valueNotExits = true;
                for (i = selectedGroups.length - 1; i >= 0; i--) {
                    if ((selectedGroups[i].group_name == assignmentRange[j].data.group_name) && (selectedGroups[i].group_guid == assignmentRange[j].data.group_guid) && (selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].intent == assignmentRange[j].data.intent)) {
                        valueNotExits = false;
                    }
                }
                if (valueNotExits && assignmentRange[j].data.group_all == 0) {
                    assignment.remove(assignmentRange[j]);
                    var gridStore1 = Ext.ComponentQuery.query(gridId1)[0].getStore();
                    var gridStore2 = Ext.ComponentQuery.query(gridId2)[0].getStore();
                    var record1 = gridStore1.findBy(function (record) {
                        return assignmentRange[j].data.group_guid === record.get('group_guid') && selectedConnectionId === record.get('connection_id');
                    });

                    if (record1 !== -1) {
                        sfw.Default.changeGridRowStatusEnableDisable(gridId1,gridStore1,record1,true);
                    }
                    var record2 = gridStore2.findBy(function (record) {
                        return assignmentRange[j].data.group_guid === record.get('group_guid') && selectedConnectionId === record.get('connection_id');
                    });

                    if (record2 !== -1) {
                        sfw.Default.changeGridRowStatusEnableDisable(gridId2,gridStore2,record2,true);

                    }
                }
            }
        }

        btn.up('window').close();

    },

    intuneGroups: function (connectionId, isRead = false, intent) {

        var intuneGroupsWindow = Ext.create('sfw.view.commonpopupwindows.IntuneGroupsMasterList',
            {
                connectionId: connectionId,
                intent: intent
            });
        selectedIds = new Set();
        selectedRecords = [];
        var intuneGroups = Ext.getStore('intunegroupsmasterlist');
        intuneGroups.getProxy().setExtraParams({  'connection_id': connectionId});
        intuneGroups.loadPage(1,{
            callback: function (records,operation) {
                lastScrollPosition = 0;
                if(operation.getResponse().responseJson.error_code == 2){
                    Ext.Msg.alert("Error fetching groups", "Fetching groups from Quick Patch need connection configured with Flexera Patch Publisher v7.25 and above. Please upgrade your Patch Publisher and try again.");
                } else if(operation.getResponse().responseJson.error_code == -1){
                    Ext.Msg.alert("Error fetching groups", "Fetching groups failed. Please recheck/refresh your intune credentials or try after some time.");
                } else {

                if (isRead) {
                    intuneGroupsWindow.down('#saveGroups').hide();
                } else {
                    intuneGroupsWindow.down('#saveGroups').show();
                    sfw.Default.preSelectedGroupsData(connectionId, intent);
                }

                intuneGroupsWindow.show();
                intuneGroupsWindow.center();
            }
            },
            failure: function () {
            }
        });
    },

    handleCheckChange: function (column, rowIndex, checked, record, gridId1, gridId2) {
        var gridStore1 = Ext.ComponentQuery.query(gridId1)[0].getStore();
        var gridStore2 = Ext.ComponentQuery.query(gridId2)[0].getStore();

        // Check if the record is present in the first grid
        var record1 = gridStore1.findBy(function (rec) {
            return rec.get('group_guid') === record.get('group_guid') && rec.get('connection_id') === record.get('connection_id');
        });

        // Check if the record is present in the second grid
        var record2 = gridStore2.findBy(function (rec) {
            return rec.get('group_guid') === record.get('group_guid') && rec.get('connection_id') === record.get('connection_id');
        });

        if (record1 !== -1) {

            if (checked) {
                var grid1 = Ext.ComponentQuery.query(gridId1)[0];
                var gridView1 = grid1.getView();
                var gridRecord1 = gridStore1.getAt(record1);
                var gridRow1 = gridView1.getRow(record1);
                gridRecord1.set('disable', 1);
                gridRecord1.set('group_mode', 0);
                Ext.fly(gridRow1).addCls('x-item-disabled');
                gridStore1.commitChanges();
            } else {
                sfw.Default.changeGridRowStatusEnableDisable(gridId1,gridStore1,record1,true);
            }
        }

        if (record2 !== -1) {
            var grid2 = Ext.ComponentQuery.query(gridId2)[0];
            var gridView2 = grid2.getView();
            var gridRecord2 = gridStore2.getAt(record2);
            var gridRow2 = gridView2.getRow(record2);
            if (checked) {
                gridRecord2.set('disable', 1);
                gridRecord2.set('group_mode', 0);
                Ext.fly(gridRow2).addCls('x-item-disabled');
                gridStore2.commitChanges();
            } else {
                sfw.Default.changeGridRowStatusEnableDisable(gridId2,gridStore2,record2,true);
            }
        }
        record.set('group_mode', checked ? 1 : 0);

        isChanged = 1;
    },

    groupSearch:function(grid){

        var intuneWindow = Ext.ComponentQuery.query('window[xtype=IntuneGroups]')[0];

        var intuneGroupsStore = Ext.getStore('intunegroupsmasterlist');

        sfw.Default.saveSelectedRecords();

        intuneGroupsStore.removeAll(); // Clear current records
        intuneGroupsStore.nextPageLink = null; // Reset next_page_link
        intuneGroupsStore.hasMoreData = true; // Re-enable pagination

        intuneGroupsStore.getProxy().setExtraParams({'search': Ext.ComponentQuery.query('#searchGroups')[0].value, 'connection_id' : intuneWindow.getConnectionId()});
        intuneGroupsStore.loadPage(1,{
            addRecords: true,
            callback: function () {
                sfw.Default.restoreSelectedRecords();
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    renderGroups:function(connectionId, meta, records){
        var html = '-';

        if(records.data.connection_type == 2){
            html = '<a href="#" onclick="sfw.Default.intuneGroups('+connectionId+',true)" style="color:blue;">View Groups</a>';
        }

        return html;
    },

    renderAddGroups: function (connectionId, intent = 1) {
        var html = '-';

        html = '<a href="#" onclick="sfw.Default.intuneGroups(' + connectionId + ',false,' + intent + ')" style="color:blue;">Add Groups</a>';

        return html;
    },
    renderAllDevices: function (connectionId, added = false, intent = 1) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',1)" style="color:grey;pointer-events:none">Add All Devices</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',1)" style="color:blue;">Add All Devices</a>';
        }

        return html;
    },
    renderAllDevicesAvailable: function (connectionId, added = false, intent = 0) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',0)" style="color:grey;pointer-events:none">Add All Devices</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',0)" style="color:blue;">Add All Devices</a>';
        }

        return html;
    },
    renderAllDevicesUninstall: function (connectionId, added = false, intent = 2) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',2)" style="color:grey;pointer-events:none">Add All Devices</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllDevices(' + connectionId + ',2)" style="color:blue;">Add All Devices</a>';
        }

        return html;
    },
    renderAllUsers: function (connectionId, added = false, intent = 1) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 1)" style="color:grey;pointer-events:none">Add All Users</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 1)" style="color:blue;">Add All Users</a>';
        }

        return html;
    },
    renderAllUsersAvailable: function (connectionId, added = false, intent = 1) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 0)" style="color:grey;pointer-events:none">Add All Users</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 0)" style="color:blue;">Add All Users</a>';
        }

        return html;
    },
    renderAllUsersUninstall: function (connectionId, added = false, intent = 1) {
        var html = '-';

        if (added) {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 2)" style="color:grey;pointer-events:none">Add All Users</a>';
        } else {
            html = '<a href="#" onclick="sfw.Default.addAllUsers(' + connectionId + ', 2)" style="color:blue;">Add All Users</a>';
        }

        return html;
    },

    filterManageAssigmentsGrid:function(assigmentsStore,connectionId,assignmentsAvailableStore,assignmentsUninstallStore){
        assigmentsStore.clearFilter(true);
        var myfilter = new Ext.util.Filter({
            filterFn: function(rec) {
                if(rec.get('connection_id') == connectionId){
                    return true;
                }
            }
        });

        assigmentsStore.filter(myfilter);
        if (typeof (assignmentsAvailableStore) != 'undefined') {
            assignmentsAvailableStore.clearFilter(true);
            var myfilter = new Ext.util.Filter({
                filterFn: function (rec) {
                    if (rec.get('connection_id') == connectionId) {
                        return true;
                    }
                }
            });

            assignmentsAvailableStore.filter(myfilter);
        }
        if (typeof (assignmentsUninstallStore) != 'undefined') {
            assignmentsUninstallStore.clearFilter(true);
            var myfilter = new Ext.util.Filter({
                filterFn: function (rec) {
                    if (rec.get('connection_id') == connectionId) {
                        return true;
                    }
                }
            });

            assignmentsUninstallStore.filter(myfilter);
        }
    },

    changeConnections:function(connectionId){
        selectedConnectionId = connectionId;
        Ext.ComponentQuery.query('#addGroups')[0].setValue(this.renderAddGroups(connectionId,1));
        Ext.ComponentQuery.query('#addAllDevices')[0].setValue(this.renderAllDevices(connectionId),1);
        Ext.ComponentQuery.query('#addAllUsers')[0].setValue(this.renderAllUsers(connectionId),1);
        Ext.ComponentQuery.query('#addGroupsAvailable')[0].setValue(this.renderAddGroups(connectionId,0));
        Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(this.renderAllDevicesAvailable(connectionId),false,0);
        Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(this.renderAllUsersAvailable(connectionId),false,0);
        Ext.ComponentQuery.query('#addGroupsUninstall')[0].setValue(this.renderAddGroups(connectionId,2));
        Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(this.renderAllDevicesUninstall(connectionId),false,2);
        Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(this.renderAllUsersUninstall(connectionId),false,2);
        this.filterManageAssigmentsGrid(Ext.getStore('assignments'),connectionId,Ext.getStore('assignments_available'),Ext.getStore('assignments_uninstall'));
        this.enableDisableDevicesUsers(Ext.getStore('assignments'),connectionId,Ext.getStore('assignments_available'),Ext.getStore('assignments_uninstall'));
    },

    editAssignments: function (event, target, options) {
        var task_guid = options.extra.data.task_guid;
        var connection_id = options.extra.data.connection_id;
        var connection_name = options.extra.data.connection_name;
        var wizard_id = options.extra.data.wizard_id;
        var single_patch_id = options.extra.data.single_patch_id;
        var vpm_id = options.extra.data.vpm_id;
        var subscription_id = options.extra.data.subscription_id;
        var package_name = options.extra.data.package_name;
        source_mode = 'edit_assignment';
        selectedConnectionId = connection_id;

        if (typeof (selectedGroups) == 'undefined') {
            selectedGroups = [];
        }

        isChanged = 0;

        Ext.Ajax.request({
            url: 'action=sps_package&which=get_assignments_details&',
            method: 'GET',
            params: {
                task_guid: task_guid,
                connection_id: connection_id,
                wizard_id: wizard_id,
                single_patch_id: single_patch_id,
                vpm_id: vpm_id,
                subscription_id: subscription_id
            },
            dataType: 'json',
            success: function (response) {
                var response = Ext.util.JSON.decode(response.responseText);
                if (response.success === true) {

                    // Show manageAssignments popup
                    var assignments = Ext.create('sfw.view.commonpanels.ManageAssignments', {
                        source: 'edit_assignment',
                        taskGuid: task_guid,
                        connectionId: connection_id,
                        wizardId: wizard_id,
                        singlePatchId: single_patch_id,
                        vpmId: vpm_id,
                        subscriptionId: subscription_id,
                        listeners: {
                            afterrender: function (assignments) {
                                assignments.down('#connections_options').setValue(connection_id);
                                assignments.down('#connections_options').setRawValue(connection_name);
                                assignments.down('#connections_options').setFieldLabel("Connection");

                                assignments.down('#addGroups').setValue(sfw.Default.renderAddGroups(connection_id, 1));
                                assignments.down('#addGroupsAvailable').setValue(sfw.Default.renderAddGroups(connection_id, 0));
                                assignments.down('#addGroupsUninstall').setValue(sfw.Default.renderAddGroups(connection_id, 2));
                                assignments.down('#addAllDevices').setValue(sfw.Default.renderAllDevices(connection_id, false, 1));
                                assignments.down('#addAllDevicesAvailable').setValue(sfw.Default.renderAllDevicesAvailable(connection_id, false, 0));
                                assignments.down('#addAllDevicesUninstall').setValue(sfw.Default.renderAllDevicesUninstall(connection_id, false, 2));
                                assignments.down('#addAllUsers').setValue(sfw.Default.renderAllUsers(connection_id, false, 1));
                                assignments.down('#addAllUsersAvailable').setValue(sfw.Default.renderAllUsersAvailable(connection_id, false, 0));
                                assignments.down('#addAllUsersUninstall').setValue(sfw.Default.renderAllUsersUninstall(connection_id, false, 2));
                                var assignmentsStore = assignments.getViewModel().getStore('assignments');
                                var assignmentsStoreAvailable = assignments.getViewModel().getStore('assignments_available');
                                var assignmentsStoreUninstall = assignments.getViewModel().getStore('assignments_uninstall');


                                if (Array.isArray(response.data.rows.assignment_connections)) {
                                    var assignmentsData = response.data.rows.assignment_connections;
                                    //Logic to disable records which are already present in other intent as Included
                                    var filteredIncludedRecords = Ext.Array.filter(assignmentsData, function(record) {
                                        return record.group_mode === 1;
                                    });

                                    var result = [];
                                    Ext.Array.each(filteredIncludedRecords, function(filteredRecord) {
                                        var matchingExcludedRecords = Ext.Array.filter(assignmentsData, function(record) {
                                            return record.group_guid === filteredRecord.group_guid && record.connection_id === filteredRecord.connection_id && record.group_mode !== filteredRecord.group_mode;
                                        });
                                        result = result.concat(matchingExcludedRecords);
                                    });

                                    result.forEach(function(record) {
                                        record.disable = 1;
                                    });
                                    selectedGroups = response.data.rows.assignment_connections
                                    sfw.Default.loadDataToIndividualStores(assignmentsData,assignmentsStore,assignmentsStoreAvailable,assignmentsStoreUninstall);
                                    sfw.Default.enableDisableDevicesUsers(assignmentsStore, connection_id, assignmentsStoreAvailable, assignmentsStoreUninstall);
                                }
                            }
                        }
                    });
                    assignments.show();
                    assignments.center();
                    assignments.setTitle('Manage Assignments ('+package_name+')');
                } else {
                    sfw.util.Debug.log('Error while managing assignments');
                    Ext.Msg.show({
                        title: 'Error',
                        msg: 'Managing assignments failed',
                        buttons: Ext.Msg.OK
                    });
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Managing assignments failed");
            }
        })
    },

    saveAssignments: function (btn) {
        var assignmentsDetails = btn.up('panel');
        if(typeof(btn.up('window')) !== 'undefined'){
            var source = btn.up('window').getSource();
        }else{
            var source = btn.getSource();
        }
        if (source == "manage_assignment") {
            if(typeof(assignmentsDetails) !== 'undefined'){
                var assignmentStore = assignmentsDetails.getViewModel().getStore('assignments');
            }else{
                var assignmentStore = btn.getViewModel().getStore('assignments');
            }
            assignmentStore.clearFilter(true);
            var values = assignmentStore.getRange();

        var filteredSaveData = [];

            for(j=0; j< values.length; j++){
                filteredSaveData.push(values[j].data);
            }

            susbcriptionDetails.getViewModel().set('assignmentsData', filteredSaveData);
            selectedGroups = filteredSaveData;
            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
        } else if (source == "edit_assignment") {
            if(typeof(btn.up('window')) !== 'undefined'){
                var wizard_id = btn.up('window').getWizardId();
                var task_guid = btn.up('window').getTaskGuid();
                var vpm_id = btn.up('window').getVpmId();
                var subscription_id = btn.up('window').getSubscriptionId();
                var connection_id = btn.up('window').getConnectionId();
                var single_patch_id = btn.up('window').getSinglePatchId();
            }else{
                var wizard_id = btn.getWizardId();
                var task_guid = btn.getTaskGuid();
                var vpm_id = btn.getVpmId();
                var subscription_id = btn.getSubscriptionId();
                var connection_id = btn.getConnectionId();
                var single_patch_id = btn.getSinglePatchId();
            }

            if(typeof(assignmentsDetails) !== 'undefined') {
                var assignmentStore = assignmentsDetails.getViewModel().getStore('assignments');
                var assignmentAvailableStore = assignmentsDetails.getViewModel().getStore('assignments_available');
                var assignmentUninstallStore = assignmentsDetails.getViewModel().getStore('assignments_uninstall');
            }else{
                var assignmentStore = btn.getViewModel().getStore('assignments');
                var assignmentAvailableStore = btn.getViewModel().getStore('assignments_available');
                var assignmentUninstallStore = btn.getViewModel().getStore('assignments_uninstall');
            }

            var requiredData = assignmentStore.getRange();
            var availableData = assignmentAvailableStore.getRange();
            var uninstallData = assignmentUninstallStore.getRange();

            var combinedData = requiredData.concat(availableData, uninstallData);

            var combinedSaveData = [];

            for (j = 0; j < combinedData.length; j++) {
                combinedSaveData.push(combinedData[j].data);
            }
            Ext.Ajax.request({
                url: 'action=sps_package&which=save_edit_assignments',
                method: 'POST',
                dataType: 'json',
                params: {
                    'connection_id': connection_id,
                    'subscription_id': subscription_id,
                    'wizard_id': wizard_id,
                    'single_patch_id': single_patch_id,
                    'task_guid': task_guid,
                    'vpm_id': vpm_id,
                    'assignment_connections': Ext.encode(combinedSaveData)
                },
                success: function (response, opts) {
                    var response = Ext.decode(response.responseText, true);
                    if (response.success && response.error_code == 0) {
                        Ext.Msg.show({
                            title: 'Success',
                            msg: 'Assignments saved successfully',
                            buttons: Ext.Msg.OK
                        });
                        // Refresh the parent grid with search_text
                        var parentGrid = Ext.ComponentQuery.query('#patchDeploymentGrid')[0];
                        if (parentGrid) {
                            var search_text = parentGrid.down('#search_text').getValue();
                            var combo_value = parentGrid.down('#searchTypePackageDeployment').getValue();
                            parentGrid.getStore().getProxy().setExtraParams({
                                'search': search_text,
                                'searchType_PackageDeployment': combo_value
                            });
                            parentGrid.getStore().reload();
                        }
                    } else {
                        Ext.Msg.show({
                            title: 'Error',
                            msg: "Error while saving assignments",
                            buttons: Ext.Msg.OK
                        });
                    }
                },
                failure: function (response, opts) {
                    Ext.Msg.alert('Error', 'Unexpected Error Occured');
                }
            });
            delete selectedGroups;

        } else if (source == "quick_patch_assignment") {

            if(typeof(assignmentsDetails) !== 'undefined') {
                var assignmentStore = assignmentsDetails.getViewModel().getStore('assignments');
                var assignmentAvailableStore = assignmentsDetails.getViewModel().getStore('assignments_available');
                var assignmentUninstallStore = assignmentsDetails.getViewModel().getStore('assignments_uninstall');
            }else{
                var assignmentStore = btn.getViewModel().getStore('assignments');
                var assignmentAvailableStore = btn.getViewModel().getStore('assignments_available');
                var assignmentUninstallStore = btn.getViewModel().getStore('assignments_uninstall');
            }

            assignmentStore.clearFilter(true);
            assignmentAvailableStore.clearFilter(true);
            assignmentUninstallStore.clearFilter(true);

            var requiredData = assignmentStore.getRange();
            var availableData = assignmentAvailableStore.getRange();
            var uninstallData = assignmentUninstallStore.getRange();

            var combinedData = requiredData.concat(availableData, uninstallData);

            var values = combinedData

            var filteredSaveDataQuickPatch = [];

            for (j = 0; j < values.length; j++) {
                filteredSaveDataQuickPatch.push(values[j].data);
            }

            quickPatchDetails.getViewModel().set('assignmentsDataQuickPatch', null);
            quickPatchDetails.getViewModel().set('assignmentsDataQuickPatch', filteredSaveDataQuickPatch);
            selectedGroups = filteredSaveDataQuickPatch;
            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
        }

        if(typeof(btn.up('window')) !== 'undefined'){
            btn.up('window').destroy();
        }else{
            btn.destroy();
        }
    },

    deleteGroups: function (groupName, groupGuid, groupAll, intent) {

        Ext.Msg.show({
            title:'Delete group?',
            message: 'Are you sure you want to delete this group?',
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    var storeDetails = sfw.Default.getStorebyIntent(intent);
                    var assigmentsStore = storeDetails.store;
                    var gridId1 = storeDetails.gridId1;
                    var gridId2 = storeDetails.gridId2;

                    var gridStore1 = Ext.ComponentQuery.query(gridId1)[0].getStore();
                    var gridStore2 = Ext.ComponentQuery.query(gridId2)[0].getStore();

                    assigmentsStore.each(function (record, i) {
                        if ((record.data.group_name == groupName) && (record.data.group_guid == groupGuid) && ((groupAll == 0) && (record.data.group_all == groupAll)) && (record.data.connection_id == selectedConnectionId) && (record.data.intent == intent)) {
                            if(record.data.group_mode == 1){
                                var record1 = gridStore1.findBy(function (rec) {
                                    return rec.get('group_guid') === record.get('group_guid') && rec.get('connection_id') === record.get('connection_id');
                                });

                                if (record1 !== -1) {
                                    sfw.Default.changeGridRowStatusEnableDisable(gridId1,gridStore1,record1,true)
                                }
                                var record2 = gridStore2.findBy(function (rec) {
                                    return rec.get('group_guid') === record.get('group_guid') && rec.get('connection_id') === record.get('connection_id');
                                });

                                // Check if the record is present in the second grid

                                if (record2 !== -1) {
                                    sfw.Default.changeGridRowStatusEnableDisable(gridId2,gridStore2,record2,true);
                                }


                            }
                            assigmentsStore.removeAt(i);
                        } else if (((groupAll == 1) && (record.data.group_all == groupAll) && (record.data.connection_id == selectedConnectionId)) || ((groupAll == 2) && (record.data.group_all == groupAll) && (record.data.connection_id == selectedConnectionId) && (record.data.intent == intent))) {
                            assigmentsStore.removeAt(i);
                        }
                    });


                    for (i = 0; i < selectedGroups.length; i++) {
                        if ((selectedGroups[i].group_name == groupName) && (selectedGroups[i].group_guid == groupGuid) && ((groupAll == 0) && (selectedGroups[i].group_all == groupAll)) && (selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].intent == intent)) {
                            selectedGroups.splice(i, 1);
                        } else if (((groupAll == 1) && (selectedGroups[i].group_all == groupAll) && (selectedGroups[i].connection_id == selectedConnectionId)) || ((groupAll == 2) && (selectedGroups[i].group_all == groupAll) && (selectedGroups[i].connection_id == selectedConnectionId) && (selectedGroups[i].intent == intent))) {
                            selectedGroups.splice(i, 1);
                        }
                    }

                    if (groupAll == 2) {
                        Ext.ComponentQuery.query('#addAllDevices')[0].setValue(sfw.Default.renderAllDevices(selectedConnectionId, false));
                        Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(sfw.Default.renderAllDevicesAvailable(selectedConnectionId, false));
                        Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(sfw.Default.renderAllDevicesUninstall(selectedConnectionId, false));

                    } else if (groupAll == 1) {
                        Ext.ComponentQuery.query('#addAllUsers')[0].setValue(sfw.Default.renderAllUsers(selectedConnectionId, false));
                        Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(sfw.Default.renderAllUsersAvailable(selectedConnectionId, false));
                        Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(sfw.Default.renderAllUsersUninstall(selectedConnectionId, false));
                    }

                    isChanged = 1;
                } else if (btn === 'no') {
                    return false;
                }
            }
        });

    },

    closeSubscriptionWindow: function(btn){
        delete selectedGroups;
        delete initialAssignmentData;

        if(btn){
            if(typeof(btn.up('window')) != 'undefined'){
                btn.up('window').destroy();
            }
        }
    },

    enableDisableDevicesUsers: function (assignment, connectionId, assignmentsAvailable, assignmentsUninstall, edit) {
        assignment.findBy(function (record) {
            if ((record.get('group_all') == 2) && (record.get('connection_id') == connectionId)) {
                Ext.ComponentQuery.query('#addAllDevices')[0].setValue(sfw.Default.renderAllDevices(selectedConnectionId, true, 1));
                Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(sfw.Default.renderAllDevicesAvailable(selectedConnectionId, true, 0));
                Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(sfw.Default.renderAllDevicesUninstall(selectedConnectionId, true, 2));
            } else if ((record.get('group_all') == 1) && (record.get('connection_id') == connectionId)) {
                Ext.ComponentQuery.query('#addAllUsers')[0].setValue(sfw.Default.renderAllUsers(selectedConnectionId, true, 1));
                Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(sfw.Default.renderAllUsersAvailable(selectedConnectionId, true, 0));
                Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(sfw.Default.renderAllUsersUninstall(selectedConnectionId, true, 2));
            }
        });
        if(typeof (assignmentsAvailable) != 'undefined') {
            assignmentsAvailable.findBy(function (record) {
                if ((record.get('group_all') == 2) && (record.get('connection_id') == connectionId)) {
                    Ext.ComponentQuery.query('#addAllDevices')[0].setValue(sfw.Default.renderAllDevices(selectedConnectionId, true, 1));
                    Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(sfw.Default.renderAllDevicesAvailable(selectedConnectionId, true, 0));
                    Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(sfw.Default.renderAllDevicesUninstall(selectedConnectionId, true, 2));
                } else if ((record.get('group_all') == 1) && (record.get('connection_id') == connectionId)) {
                    Ext.ComponentQuery.query('#addAllUsers')[0].setValue(sfw.Default.renderAllUsers(selectedConnectionId, true, 1));
                    Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(sfw.Default.renderAllUsersAvailable(selectedConnectionId, true, 0));
                    Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(sfw.Default.renderAllUsersUninstall(selectedConnectionId, true, 2));
                }
            });
        }
        if(typeof (assignmentsUninstall) != 'undefined') {
            assignmentsUninstall.findBy(function (record) {
                if ((record.get('group_all') == 2) && (record.get('connection_id') == connectionId)) {
                    Ext.ComponentQuery.query('#addAllDevices')[0].setValue(sfw.Default.renderAllDevices(selectedConnectionId, true, 1));
                    Ext.ComponentQuery.query('#addAllDevicesAvailable')[0].setValue(sfw.Default.renderAllDevicesAvailable(selectedConnectionId, true, 0));
                    Ext.ComponentQuery.query('#addAllDevicesUninstall')[0].setValue(sfw.Default.renderAllDevicesUninstall(selectedConnectionId, true, 2));
                } else if ((record.get('group_all') == 1) && (record.get('connection_id') == connectionId)) {
                    Ext.ComponentQuery.query('#addAllUsers')[0].setValue(sfw.Default.renderAllUsers(selectedConnectionId, true, 1));
                    Ext.ComponentQuery.query('#addAllUsersAvailable')[0].setValue(sfw.Default.renderAllUsersAvailable(selectedConnectionId, true, 0));
                    Ext.ComponentQuery.query('#addAllUsersUninstall')[0].setValue(sfw.Default.renderAllUsersUninstall(selectedConnectionId, true, 2));
                }
            });
        }
    },

    closeAssignmentWindow: function(btn){

        if(isChanged){
            Ext.Msg.confirm({
                title:'Save Changes',
                message: 'Do you want to save these changes?',
                buttons: Ext.Msg.YESNO,
                icon: Ext.Msg.QUESTION,
                fn: function(confirm) {
                    if(confirm === 'yes'){
                        sfw.Default.saveAssignments(btn);
                    }else if(confirm === 'no'){
                        if(typeof(initialAssignmentData) != 'undefined'){
                            selectedGroups = JSON.parse(JSON.stringify(initialAssignmentData));
                        }

                        if(source_mode == 'edit_assignment') {
                            delete selectedGroups;
                        }

                        if(typeof(btn) != 'undefined') {
                            if (typeof (btn.up('window')) != 'undefined') {
                                btn.up('window').destroy();
                            }else{
                                btn.destroy();
                            }
                        }
                    }
                }
            });

            return false;

        }else{
            if(typeof(initialAssignmentData) != 'undefined'){
                selectedGroups = JSON.parse(JSON.stringify(initialAssignmentData));
            }

            if(source_mode == 'edit_assignment') {
                delete selectedGroups;
            }

            if(typeof(btn) != 'undefined') {
                if (typeof (btn.up('window')) != 'undefined') {
                    btn.up('window').destroy();
                }else{
                    btn.destroy();
                }
            }
        }
    },
    getAssignmentsQuickPatch: function(btn){

        quickPatchDetails = btn.up('panel');
        var selectedConnectionsId = quickPatchDetails.down('#singlePatchConnections').getValue();
        var selectedConnectionsNames = quickPatchDetails.down('#singlePatchConnections').getRawValue();
        var selectedConnectionsNamesArray = selectedConnectionsNames.split(',');
        source_mode = 'manage_assignment';

        selectedConnectionData = [];

        for(var i=0; i<selectedConnectionsId.length; i++) {
            if(selectedConnectionsNamesArray[i].includes('Intune')) {
                selectedConnectionData.push({id: selectedConnectionsId[i], name: selectedConnectionsNamesArray[i]});
            }
        }

        if(typeof(selectedGroups) == 'undefined'){
            selectedGroups = [];
        }

        initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));

        isChanged = 0;

        selectedConnectionId = null;

        var assignmnets = Ext.create('sfw.view.commonpanels.ManageAssignments',{
            source: 'quick_patch_assignment',
            retain: true,
            listeners: {
                afterrender: function (assignments) {
                    assignments.down('#connections_options').setStore(selectedConnectionData);
                    assignments.down('#connections_options').setValue(selectedConnectionData[0]['id']);
                    selectedConnectionId = selectedConnectionData[0]['id'];
                    assignments.down('#addGroups').setValue(sfw.Default.renderAddGroups(selectedConnectionData[0]['id']));
                    assignments.down('#addAllDevices').setValue(sfw.Default.renderAllDevices(selectedConnectionData[0]['id']));
                    assignments.down('#addAllUsers').setValue(sfw.Default.renderAllUsers(selectedConnectionData[0]['id']));
                    assignments.down('#addGroups').setValue(sfw.Default.renderAddGroups(selectedConnectionData[0]['id'], 1));
                    assignments.down('#addGroupsAvailable').setValue(sfw.Default.renderAddGroups(selectedConnectionData[0]['id'], 0));
                    assignments.down('#addGroupsUninstall').setValue(sfw.Default.renderAddGroups(selectedConnectionData[0]['id'], 2));
                    assignments.down('#addAllDevices').setValue(sfw.Default.renderAllDevices(selectedConnectionData[0]['id'], false, 1));
                    assignments.down('#addAllDevicesAvailable').setValue(sfw.Default.renderAllDevicesAvailable(selectedConnectionData[0]['id'], false, 0));
                    assignments.down('#addAllDevicesUninstall').setValue(sfw.Default.renderAllDevicesUninstall(selectedConnectionData[0]['id'], false, 2));
                    assignments.down('#addAllUsers').setValue(sfw.Default.renderAllUsers(selectedConnectionData[0]['id'], false, 1));
                    assignments.down('#addAllUsersAvailable').setValue(sfw.Default.renderAllUsersAvailable(selectedConnectionData[0]['id'], false, 0));
                    assignments.down('#addAllUsersUninstall').setValue(sfw.Default.renderAllUsersUninstall(selectedConnectionData[0]['id'], false, 2));
                    var assignmentsStore = assignments.getViewModel().getStore('assignments');
                    var assignmentsAvailableStore = assignments.getViewModel().getStore('assignments_available');
                    var assignmentsUninstallStore = assignments.getViewModel().getStore('assignments_uninstall');
                    if(quickPatchDetails.getViewModel().get('assignmentsDataQuickPatch')) {
                        if (selectedGroups.length == 0) {
                            var assignmentsData = quickPatchDetails.getViewModel().get('assignmentsDataQuickPatch')

                            selectedGroups = quickPatchDetails.getViewModel().get('assignmentsDataQuickPatch');
                            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
                        } else {
                            var assignmentsData = selectedGroups;
                            initialAssignmentData = JSON.parse(JSON.stringify(selectedGroups));
                        }
                        sfw.Default.loadDataToIndividualStores(assignmentsData,assignmentsStore,assignmentsAvailableStore,assignmentsUninstallStore);
                    }
                    sfw.Default.filterManageAssigmentsGrid(assignmentsStore,selectedConnectionData[0]['id'],assignmentsAvailableStore,assignmentsUninstallStore);
                    sfw.Default.enableDisableDevicesUsers(assignmentsStore,selectedConnectionData[0]['id'],assignmentsAvailableStore,assignmentsUninstallStore);
                }
            }
        });
        assignmnets.show();
        assignmnets.center();
        assignmnets.setTitle('Manage Assignments');
    },

    loadDataToIndividualStores: function (assignmentsData, assignmentsStore, assignmentsAvailableStore, assignmentsUninstallStore) {
        var intentRequiredArray = [];
        var intentAvailableArray = [];
        var intentUninstallArray = [];

        assignmentsData.forEach(function (item) {
            switch (item.intent) {
                case 0:
                    intentAvailableArray.push(item);
                    break;
                case 1:
                    intentRequiredArray.push(item);
                    break;
                case 2:
                    intentUninstallArray.push(item);
                    break;
            }
        });
        assignmentsStore.loadData(intentRequiredArray);
        assignmentsAvailableStore.loadData(intentAvailableArray);
        assignmentsUninstallStore.loadData(intentUninstallArray);

    },

    getStorebyIntent: function (intent) {

        if (intent == 1) {
            var assigmentsStore = Ext.getStore('assignments');
            var gridId1 = '#availableGrid';
            var gridId2 = '#uninstallGrid';

        } else if (intent == 0) {
            var assigmentsStore = Ext.getStore('assignments_available');
            var gridId1 = '#requiredGrid';
            var gridId2 = '#uninstallGrid';
        } else {
            var assigmentsStore = Ext.getStore('assignments_uninstall');
            var gridId1 = '#requiredGrid';
            var gridId2 =  '#availableGrid';
        }

        return {
            store: assigmentsStore,
            gridId1: gridId1,
            gridId2: gridId2
        }

    },

    changeGridRowStatusEnableDisable: function (gridId, gridStore, record, enable ) {

        var grid1 = Ext.ComponentQuery.query(gridId)[0];
        var gridView1 = grid1.getView();
        var gridRecord = gridStore.getAt(record);
        var gridRow = gridView1.getRow(record);

        if (enable) {
            gridRecord.set('disable', 0);
            Ext.fly(gridRow).removeCls('x-item-disabled');
            Ext.fly(gridRow).addCls('x-item-enabled');
        } else {
            gridRecord.set('disable', 1);
            Ext.fly(gridRow).addCls('x-item-disabled');
            gridStore.commitChanges();
        }
        gridStore.commitChanges();
    },

    fetchMoreData: function(store){
        sfw.Default.saveSelectedRecords();
        store.loadPage(store.currentPage + 1, {
            addRecords: true,
            callback: function (records, operation, success) {
                if (success) {
                    sfw.Default.restoreSelectedRecords();
                    //console.log('Loaded more data.');
                } else {
                    //console.log('Failed to load data.');
                }
            }
        });
    },

    preSelectGroups:function(store){

        if(typeof(selectedGroups) != 'undefined') {
            const grid = Ext.ComponentQuery.query('#intunegroupsgrid')[0];

            const groupsComponent = Ext.ComponentQuery.query('window[xtype=IntuneGroups]')[0];
            var toSelect = [];
            store.each(record => {

                for (var j = 0; j < selectedGroups.length; j++) {
                    if ((selectedGroups[j].group_name == record.get('name')) && (selectedGroups[j].group_guid == record.get('guid')) && (groupsComponent.getConnectionId() == selectedGroups[j].connection_id) && (groupsComponent.getIntent() == selectedGroups[j].intent)) {
                        toSelect.push(record);
                    }
                }
            });

            Ext.ComponentQuery.query("#intunegroupsgrid")[0].getSelectionModel().select(toSelect);

            sfw.Default.saveSelectedRecords();
            sfw.Default.restoreSelectedRecords();
        }
    },

    saveScrollPosition:function() {
        const grid = Ext.ComponentQuery.query('#intunegroupsgrid')[0];
        if (grid) {
            lastScrollPosition = grid.getView().getScrollable().getPosition().y;
            //console.log('Saved Scroll Position:', lastScrollPosition);
        }
    },

    // Function to restore scroll position after data loads
    restoreScrollPosition:function() {
        const grid = Ext.ComponentQuery.query('#intunegroupsgrid')[0];
        if (grid) {
            Ext.defer(() => {
                grid.getView().getScrollable().scrollTo(0, lastScrollPosition);
                //console.log('Restored Scroll Position:', lastScrollPosition);
            }, 500); // Small delay to ensure rendering is complete
        }
    },

    saveSelectedRecords:function() {
        var grid = Ext.ComponentQuery.query('#intunegroupsgrid')[0];
        const selectedRecord = grid.getSelectionModel().getSelected();
        //selectedIds.clear();
        selectedRecord.each(function(record){
            selectedIds.add(record.get('guid'));
        });

        selectedRecords.forEach((record, key) => {
            if(selectedIds.size > 0){
                if (!selectedIds.has(record.get('guid'))) {
                    selectedRecords = selectedRecords.filter(record => selectedIds.has(record.get('guid')));
                }
            }
        });

    },

    restoreSelectedRecords:function() {
        var grid = Ext.ComponentQuery.query('#intunegroupsgrid')[0];
        const store = grid.getStore();

        store.each(record => {
            if (selectedIds.has(record.get('guid'))) {
                selectedRecords = selectedRecords.filter(sel => sel.get('guid') !== record.get('guid'));

                selectedRecords.push(record);
            }
        });

        grid.getSelectionModel().select(selectedRecords, false, true);

    },

    preSelectedGroupsData:function(connectionId, intent){
        updatedSelectedRecords = [];
        for (var j = 0; j < selectedGroups.length; j++) {
            if ((connectionId == selectedGroups[j].connection_id) && (intent == selectedGroups[j].intent)) {

                updatedSelectedRecords.push(selectedGroups[j]);

            }
        }
    }

});

// CSI Logging
function LogMessage(msg) {
    try {
        console.log( msg );
        // sfw.external.fWUILogMessage(msg);
    } catch (ex) {
        // Silently discard message
    }
}

// CSI Exception Logging
function LogException(msg,ex) {
    var message = msg + " : ";
    message += ( ex.number ? ex.number + ' , ' : '' ) + ex.message;
    LogMessage( message );
}
