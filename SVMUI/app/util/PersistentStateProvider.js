Ext.define('sfw.util.PersistentStateProvider', {
    extend: 'Ext.state.Provider',
    alias: 'state.persistent',

    constructor: function () {
        var me = this;

        me.callParent(arguments);
    },

    // private
    set: function (name, value) {
        var me = this;

        if (typeof value == "undefined" || value === null) {
            me.clear(name);
            return;
        }
        me.persistentSetValue(name, value);
        me.callParent(arguments);
    },

    // private
    clear: function (name) {
        var me = this;
        me.persistentClearValue(name);
        me.callParent(arguments);
    },

    // private
    readComponentStates: function () {
        var me = this,
            componentStates = {},
            name,
            value,
            data = defaults.persistentGetRowsWithPattern("stateOfComponentXXX-");//TODO Wemerson

        if (data.isActive && data.first()) {
            for (var i = 0; i < data.length; i++) {
                name = data.get('name');
                value = data.get('value');
                if (name && name.substring(0, 20) === "stateOfComponentXXX-") {
                    componentStates[name.substr(20)] = me.decodeValue(value);
                }
                data.next();
            }
        }
        return componentStates;
    },

    // private
    persistentSetValue: function (name, value) {
        name = "stateOfComponentXXX-" + name;
        defaults.persistentSetValue(name, this.encodeValue(value));
    },

    //private
    persistentClearValue: function (name, value) {
        name = "stateOfComponentXXX-" + name;
        defaults.persistentClearValue(name);
    }

});