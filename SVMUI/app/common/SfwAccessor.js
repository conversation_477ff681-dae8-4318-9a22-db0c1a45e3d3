Ext.define('sfw.common.SfwAccessor', {
   statics : {
       getMainView: function () {
           return sfw.app.getMainView();
       },
       getMainVC: function () {
           return sfw.app.getMainView().getController();
       },
       getLoginView: function () {
           return sfw.app.getMainView().lookup('login_ref');
       },
       getLoginVC: function () {
           const view = sfw.app.getMainView().lookup('login_ref');
           if (view) {
               return view.getController();
           }
           return null;
       },
       getView: function (hashTag) {
           const mainCard = sfw.common.SfwAccessor.getMainView().lookup('mainCardPanel');
           const existingItem = mainCard.child('component[routeId=' + hashTag + ']');
           return existingItem;
       },
       getCurrentView: function () {
           const mainCard = sfw.common.SfwAccessor.getMainView().lookup('mainCardPanel');
           return  mainCard.getLayout().getActiveItem();
       }
   }
});