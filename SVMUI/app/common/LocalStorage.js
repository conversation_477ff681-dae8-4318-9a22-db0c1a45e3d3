Ext.define('sfw.common.LocalStorage', {
    singleton: true,

    constructor: function () {
        this.findValue = Ext.bind(this.findValue, this);
    },

    findValue: function (key) {
        try {
            let value = localStorage.getItem(key);
            if (value) {
                value = JSON.parse(value);
            }
            return value;
        } catch (e) {
            return '';
        }
    },

    putValue: function (key, value) {
        value = JSON.stringify(value);
        localStorage.setItem(key, value);
    },

    remove: function (key) {
        localStorage.removeItem(key);
    },

    clear: function () {
        localStorage.clear();
    }

});