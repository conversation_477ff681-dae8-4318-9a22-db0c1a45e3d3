/**
 * The main application class. An instance of this class is created by app.js when it
 * calls Ext.application(). This is the ideal place to handle application launch and
 * initialization details.
 */
Ext.define('sfw.Application', {
    extend: 'Ext.app.Application',
    requires: [
        'Dashboard.*',
        'Flexera.widgets.*',
        'Ext.Responsive'
    ],
    name: 'sfw',
    defaultToken : 'Dashboard',
    //defaultToken: 'login',
    quickTips: false,
    platformConfig: {
        desktop: {
            quickTips: true
        }
    },
    stores: [
        'NavigationTree'
    ],
    onAppUpdate: function () {
        Ext.Msg.confirm('Application Update', 'Software Vulnerability Manager application has an update, reload?',
            function (choice) {
                if (choice === 'yes') {
                    window.location.reload();
                }
            }
        );
    },

    destroyLoader: function () {
        const top = Ext.get('loadingSplashTop'),
            wrapper = Ext.fly('loadingSplash');

        top.on('transitionend', wrapper.destroy, wrapper, {single: true});

        wrapper.addCls('app-loaded');
    },

    launch: function () {
        Ext.getApplication().getMainView().getController().onLaunch();
        this.destroyLoader();
        Ext.ariaWarn = Ext.emptyFn;//FIXME for accessibility warnings. Reduce noise in development mode
    },

    init: function() {
        // initialize state provider for saving component states
        Ext.state.Manager.setProvider(Ext.create('Ext.state.LocalStorageProvider'));
    }
});
