/*
 *  This file can be used to set variables and supply global styling.  View specific 
 *  styling should be located the view's corresponding scss file.
 */
$enable-font-awesome: dynamic(true);
$blue-text: darken($base-color, 5%);

@mixin btn-border($background-color) {
  background-color: $background-color;
  border-color: $background-color !important;

  .x-btn-inner-default-toolbar-small {
    color: $lightest-color;
  }

  &:hover {
    background-color: transparent;

    .x-btn-inner-default-toolbar-small,
    .x-btn-inner-default-small {
      color: $background-color;
    }

    .x-btn-icon-el-default-small,
    .x-btn-icon-el-default-toolbar-small {
      color: $background-color;
    }
  }
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  border-radius: $radius;
}


.x-menu-header {
  z-index: 0 !important;
}

//.x-css-shadow {
//  box-shadow: none !important;
//}


.x-fa {
  display: inline-block;
  font: normal normal normal 14px/1 $font-family;
}

.alignRight {
  float: right;
}

.boldFont {
  font-weight: bold;
}

.clickable {
  cursor: pointer;
  color: $blue-text;
  font-weight: normal;
  text-decoration: underline;
}

.sencha-dash-right-main-container {
  padding: 5px;

  .x-panel-body-default {
    background-color: transparent;
  }

}

ol, ul {
  list-style: none;
  padding: 0;
  margin: 0.3em 0 1em 0;
}

p {
  padding: 0;
  margin: 0.3em 0 1em 0;
}

.list-padding {
  padding-left: 35px;
}

.p-ol {
  margin: 0.3em 0 0 0;
}
