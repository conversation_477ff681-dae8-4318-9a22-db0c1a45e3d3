Ext.define('sfw.store.patching.VendorPatchModule', {
	extend: 'Ext.data.Store',

	alias: 'store.vendorpatch',

	storeId: 'vendorpatch',

	fields: [{
		name: 'vuln_criticality',
		convert: function (value, record) {
			return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
				20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
				0;
		}
	}],

	remoteSort: true,

	sorters: [{
		property: 'product_name',
		direction: 'ASC'
	}],

	proxy: {
		type: 'ajax',
		url: 'action=vpm_package&which=read&',
		reader: {
			type: 'json',
			rootProperty: 'data.rows',
			totalProperty: 'data.total',
			successProperty: 'success'
		}

	},
	autoLoad: true
});
