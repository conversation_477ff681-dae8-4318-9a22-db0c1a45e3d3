Ext.define('sfw.store.patching.VendorPatchProductReport', {
    extend: 'Ext.data.Store',
    alias: 'store.vpmproductreport',
    storeId : 'vpmproductreport',

    fields: [
        {name: 'product_id', type: 'int'},
        {name: 'cdsid', type: 'int'},
        {name: 'os_soft_name', type: 'string'},
        {name: 'architecture', type: 'int'},
        {name: 'num_insecure', type: 'int'},
        {name: 'num_eol', type: 'int'},
        {name: 'num_patched', type: 'int'},
        {name: 'num_hosts', type: 'int'},
        {name: 'num_total', type: 'int'}
    ],

    proxy: {
        type: 'ajax',
        url: 'action=vpm_package&which=readInstallations&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        }

    },

});
