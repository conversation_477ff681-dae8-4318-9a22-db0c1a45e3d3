
Ext.define('sfw.store.patching.PatchDeploymentStatus', {
    extend: 'Ext.data.Store',

    alias: 'store.patchdeployment',

    storeId: 'patchdeployment',

    remoteSort: true,

    sorters: [{
        property: 'updated',
        direction: 'DESC',
    }],

    proxy: {
        type: 'ajax',
        url: 'action=sps_package&which=package_deployment&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty:'data.total',
            successProperty: 'success'
        }

    },
    autoLoad : true
});
