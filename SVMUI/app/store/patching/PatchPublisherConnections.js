Ext.define('sfw.store.patching.PatchPublisherConnections', {
    extend: 'Ext.data.Store',
    alias: 'store.patchpublisherconnections',

    storeId: 'patchpublisherconnections',

    remoteSort: true,

    sorters: [{
        property: 'connection_name',
        direction: 'ASC',
    }],

    pageSize: 30,

    proxy: {
        type: 'ajax',
        url: 'action=vpm_package&which=list_patch_publisher_connections&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
})