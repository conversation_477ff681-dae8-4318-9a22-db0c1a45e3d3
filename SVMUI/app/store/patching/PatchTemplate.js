Ext.define('sfw.store.patching.PatchTemplate', {
    extend: 'Ext.data.Store',

    alias: 'store.patchtemplate',

    storeId: 'patchtemplate',

    remoteSort: true,

    sorters: [{
        property: 'template_name',
        direction: 'ASC'
    }],

    proxy: {
        type: 'ajax',
        autoLoad: true,
        url: 'action=sps_package&which=list_templates&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    }
});