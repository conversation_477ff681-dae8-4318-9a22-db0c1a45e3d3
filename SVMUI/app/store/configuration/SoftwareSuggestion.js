
Ext.define('sfw.store.configuration.SoftwareSuggestion', {
    extend: 'Ext.data.Store',

    alias: 'store.softwaresuggestion',

    storeId: 'softwaresuggestion',

    remoteSort: true,

    sorters: [{
        property: 'file_name',
        direction: 'ASC',
    }],

    proxy: {
        type: 'ajax',
        url: 'action=suggest_software&which=list&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty:'data.total',
            successProperty: 'success'
        }

    },
    autoLoad : true
});
