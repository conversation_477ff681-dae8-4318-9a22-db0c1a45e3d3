Ext.define('sfw.store.configuration.ExclusionPathList', {
    extend: 'Ext.data.Store',
    alias: 'store.exclusionpathlist',

    storeId: 'exclusionpathlist',

    proxy: {
        type: 'ajax',
        noCache : true,
        sortInfo: {
            field: 'name'
            ,direction: 'DESC'
        },
        url: 'action=ajaxapi_submit_settings&which=get_exclusion_list&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        },
    },
    listeners: {
        load: function (store,records) {
            var toSelect = []
            for (var i = 0; i < records.length; i++) {
                if (records[i].data.checked == 1) {
                    toSelect.push(records[i]);
                }
            }
            Ext.ComponentQuery.query("#exclusion_list_chkbx_col")[0].getSelectionModel().select(toSelect);
        }
    },
    autoLoad : true

});