Ext.define('sfw.store.configuration.ActivityLog',{
      extend:'Ext.data.Store',
      alias:'store.activitylog',

      storeId:'activitylog',

      pageSize:'30',

      remoteSort: true,

      sorters: [{
            property: 'time',
            direction: 'DESC'
       }],

      proxy: {
            type: 'ajax',
            url: 'action=activitylog&which=read&',
            reader: {
                type: 'json',
                rootProperty: 'data.rows',
                totalProperty:'data.total',
                successProperty: 'success'
            }
        }
});