Ext.define('sfw.store.reporting.DatabaseCleanupRule', {
	extend: 'Ext.data.Store',

	alias: 'store.databasecleanuprule',
	storeId: 'getdbcleanuprule',
	pageSize: 10,
	remoteSort: true,
    sorters: [{
        property: 'date_created_or_modified',
        direction: 'DESC',
    },{
        property: 'name',
        direction: 'ASC',
    }],

	proxy: {
		type: 'ajax',
		url: 'action=db_cleanup&which=read&',
		reader: {
			type: 'json',
			rootProperty: 'data.rows',
			totalProperty: 'data.total',
			successProperty: 'success'
		}

	},
	autoLoad : true
});

Ext.define('sfw.store.reporting.DatabaseCleanupHost', {
	extend: 'Ext.data.Store',

	alias: 'store.databasecleanuphost',
	storeId: 'getdbcleanuphost',
	pageSize: 10,
	remoteSort: true,
	 sorters: [{
        property: 'last_activity',
        direction: 'DESC'
    },{
        property: 'host',
        direction: 'ASC'
    }],

	proxy: {
		type: 'ajax',
		url: 'action=db_cleanup&which=hosts&',
		reader: {
			type: 'json',
			rootProperty: 'data.rows',
			totalProperty: 'data.total',
			successProperty: 'success'
		}

	},
	autoLoad : true
});
