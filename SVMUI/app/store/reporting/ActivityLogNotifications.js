Ext.define('sfw.store.reporting.ActivityLogNotifcations',{
    extend:'Ext.data.Store',
    alias:'store.activitylognotification',

    storeId:'activitylognotification',

    autoLoad:true,

    remoteSort: true,

    sorters: [{
        property:"name",direction:"ASC"
    }],

    proxy: {
        type: 'ajax',
        url: 'action=csi_notifications&which=overview&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    }
});