Ext.define('sfw.store.reporting.ScheduledExports', {
    extend: 'Ext.data.Store',

    alias: 'store.scheduledexports',
    model: 'sfw.model.reporting.ScheduledExports',
    storeId: 'scheduledexports',

    proxy: {
        type: 'ajax',
        url: 'action=ajaxapi_scheduled_exports&which=fetch&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty:'data.total',
            successProperty: 'success'
        }
    },
    autoLoad: true,
});