Ext.define('sfw.store.reporting.SmartGroupNotifications', {
    extend: 'Ext.data.Store',

    alias: 'store.sgnotification',

    storeId: 'sgnotification',

    remoteSort: true,

    sorters: [{
        property: 'modified_date',
        direction: 'DESC'
    }],
    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=smartgroup_notifications&which=overview&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }

    },
    autoLoad : true

});

Ext.define('sfw.store.reporting.SmartGroupList', {
    extend: 'Ext.data.Store',

    alias: 'store.notificationsglist',

    storeId: 'notificationsglist',

    proxy: {
        type: 'ajax',
        url: 'action=smart_groups&which=smartgroupList&withSmartGroupType=true',
        reader: {
            type: 'json',
            rootProperty: 'data',
            successProperty: 'success'
        }

    },
    autoLoad : true

});
