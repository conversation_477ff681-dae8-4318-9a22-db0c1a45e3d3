Ext.define('sfw.store.reporting.Reporting', {
    extend: 'Ext.data.Store',

    alias: 'store.reportingconfig',

    storeId: 'reporting',

    remoteSort: true,

    sorters: [{
        property: 'last_gen_date',
        direction: 'DESC'
    }],
    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=reporting&which=overview&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }

    },
    autoLoad : true

});
