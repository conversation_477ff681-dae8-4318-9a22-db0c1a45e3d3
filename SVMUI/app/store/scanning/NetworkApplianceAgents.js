Ext.define('sfw.store.scanning.NetworkApplianceAgents', {
    extend: 'Ext.data.Store',

    alias: 'store.networkapplianceagents',

    model: 'sfw.model.scanning.NetworkApplianceAgents',

    remoteSort: true,

    sorters: [{property: "host", direction: "ASC"}],

    storeId: 'networkapplianceagents',

    proxy: {
        type: 'ajax',
        url: 'action=ajaxapi_agent_management&which=read&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        }
    },
    autoLoad: true
});
