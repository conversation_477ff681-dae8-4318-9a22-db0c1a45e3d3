Ext.define('sfw.store.scanning.CompletedScans', {
    extend: 'Ext.data.Store',

    alias: 'store.completedscans',

    model: 'sfw.model.scanning.CompletedScans',

    storeId: 'getcompletescanresult',

    remoteSort: true,

    sorters: [{
        property: 'status_date',
        direction: 'DESC'
    }],
    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=csi_completed_scans&which=overview&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },

});
