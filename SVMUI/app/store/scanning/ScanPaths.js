Ext.define('sfw.store.scanning.ScanPaths', {
    extend: 'Ext.data.Store',

    alias: 'store.scanpaths',

    storeId: 'scanpaths',

    pageSize: 30 ,

    remoteSort: true,

    sorters: [{
        property:"name",direction:"ASC"
    },
    {
        property:"path",direction:"ASC"
    }],

    proxy: {
        type: 'ajax',
        url: 'action=ajaxapi_scan_paths&which=read&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    }
});