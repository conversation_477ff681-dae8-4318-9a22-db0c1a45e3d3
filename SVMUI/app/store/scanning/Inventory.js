Ext.define('sfw.store.scanning.Inventory', {
    extend: 'Ext.data.Store',

    alias: 'store.inventory',

    storeId: 'inventorylist',

    remoteSort: true,

    sorters: [{
        property: 'status_date',
        direction: 'DESC'
    }],
    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=inventory&which=list&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },


});

Ext.define('sfw.store.scanning.ImportedInventory', {
    extend: 'Ext.data.Store',
    alias: 'store.importedinventory',
    storeId: 'importedinventory',
    remoteSort: true,
    sorters: [{
        property: 'vuln_id',
        direction: 'DESC'
    }],
    fields: ['date', 'description'],

    pageSize: 30,
    sorters: [{property: 'date', direction: 'DESC'}],
    remoteSort: true,

    proxy: {
        type: 'memory',
        enablePaging: true
    }
});
