Ext.define('sfw.store.scanning.CustomScanRules', {
    extend: 'Ext.data.Store',

    alias: 'store.customscanrules',

    storeId: 'customscanrules',

    pageSize: 30,

    autoLoad:true,

    remoteSort: true,

    sorters: [{property:"filename",direction:"ASC"}],

    proxy: {
        type: 'ajax',
        url: 'action=ajaxapi_custom_scan_rules&which=read&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        }
    }
});