Ext.define('sfw.store.scanning.SingleHostAgents', {
    extend: 'Ext.data.Store',

    alias: 'store.singlehost',

    storeId: 'singlehost',

    remoteSort: true,

    sorters: [{
        property: 'host',
        direction: 'ASC'
    }],

    proxy: {
        type: 'ajax',
        noCache: false,
        url: 'action=ajaxapi_agent_management&which=read&storeType=single',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }

    },
    autoLoad : true

});
