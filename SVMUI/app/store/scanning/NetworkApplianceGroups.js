Ext.define('sfw.store.scanning.NetworkApplianceGroups', {
    extend: 'Ext.data.Store',

    alias: 'store.networkappliancegroups',

    model: 'sfw.model.scanning.NetworkApplianceGroups',

    remoteSort: true,

    storeId: 'networkappliancegroups',

    sorters: [{property:"scan_group_name",direction:"ASC"}],

    proxy: {
        type: 'ajax',
        url: 'action=scan_groups&which=read&storeType=networkAppliance&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        }
    },
    autoLoad: true
});