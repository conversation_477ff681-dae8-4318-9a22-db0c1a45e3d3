Ext.define('sfw.store.scanning.ExtendedSupport', {
    extend: 'Ext.data.Store',

    alias: 'store.extendedsupport',

    storeId: 'getextendedsupportlist',

    remoteSort: true,

    sorters: [{
        property: 'created_at',
        direction: 'DESC'
    }],
    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=esu_support&which=listESU&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
    autoLoad : true

});
