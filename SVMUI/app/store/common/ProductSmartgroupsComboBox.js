Ext.define('sfw.store.common.ProductSmartgroupsComboBox', {
    extend: 'Ext.data.Store',
    alias: 'store.productsgscombo',
    storeId : 'productsgscombo',

    fields: [
        {name: 'id', type: 'int'},
        {name: 'name', type: 'string'}
    ],

    proxy: {
        type: 'ajax',
        url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
        reader: {
            type: 'json',
            rootProperty: 'data',
            successProperty: 'success'
        }

    },
    autoLoad: true
});
