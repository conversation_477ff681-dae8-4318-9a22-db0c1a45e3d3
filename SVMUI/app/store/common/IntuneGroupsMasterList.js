Ext.define('sfw.store.common.IntuneGroupsMasterList', {
    extend: 'Ext.data.Store',
    alias: 'store.intunegroupsmasterlist',

    storeId: 'intunegroupsmasterlist',

    pageSize: 100,

    proxy: {
        type: 'ajax',
        noCache : true,
        sortInfo: {
            field: 'name'
            ,direction: 'ASC'
        },
        url: 'action=sps_package&which=get_intune_groups&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty:'total',
            successProperty: 'success'
        },
    },

    autoLoad : true,

    nextPageLink: null,  // Store `next_page_link`
    hasMoreData: true,  // Flag to indicate if more data exists

    listeners: {
        beforeload: function (store, operation) {
            if (!store.hasMoreData) {
                return false; // Stop further loading
            }
            if (store.nextPageLink) {
                operation.setParams({ 'next_page_link': store.nextPageLink });
            }

            sfw.Default.saveScrollPosition();
        },
        load: function (store, records, successful, operation) {
            if (successful) {
                const response = operation.getResponse();
                if (response && response.responseJson) {
                    store.nextPageLink = response.responseJson.next_page_link || null;
                    store.hasMoreData = !!store.nextPageLink; // Stop if no `next_page_link`
                }

                sfw.Default.preSelectGroups(store);
                sfw.Default.restoreScrollPosition();
            }
        }
    }

});