Ext.define('sfw.store.common.ViewInstallations', {
    extend: 'Ext.data.Store',
    alias: 'store.viewinstallations',
    storeId: 'viewinstallations',
    remoteSort: true,
    sorters: [{
        property: 'host',
        direction: 'ASC'
    }],
    fields: [{
        name: 'vuln_criticality',
        convert: function (value, record) {
            return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                0;
        }
    }],
    proxy: {
        type: 'ajax',
        url: 'action=results&which=get_installations&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }

    },
    autoLoad: false

});

Ext.define('sfw.store.common.AllAdvisories', {
    extend: 'Ext.data.Store',
    alias: 'store.alladvisories',
    storeId: 'alladvisories',
    remoteSort: true,
    sorters: [{
        property: 'vuln_id',
        direction: 'DESC'
    }],
    fields: [{
        name: 'vuln_criticality',
        convert: function (value, record) {
            return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                0;
        }
    }],
    proxy: {
        type: 'ajax',
        url: 'action=results&which=getAllAdvisories&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }

    }
});


Ext.define('sfw.store.common.InstallationsPieOverview', {
    extend: 'Ext.data.Store',
    alias: 'store.overviewpie',
    storeId: 'overviewpie',

    fields: ['label', 'data'],
    proxy: {
        type: 'memory',
        reader: {
            type: 'json',
            rootProperty: 'data'
        }
    },
    autoload: true
});

Ext.define('sfw.store.common.InstallationOverview', {
    extend: 'Ext.data.Store',
    alias: 'store.installationoverview',
    storeId: 'installationoverview',
    pageSize: 0,
    proxy: {
        type: 'ajax',
        url: 'action=results&which=installationOverview&',
        reader: {
            type: 'json',
            rootProperty: 'data',
            successProperty: 'success'
        }
    }
});

