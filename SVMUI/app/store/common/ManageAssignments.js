Ext.define('sfw.store.common.ManageAssignments', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.manageassignments',
    stores: {
        assignments: {
            storeId: 'assignments',
            fields: ['group_name', 'group_guid', 'intent'],
            data: []
        },

        assignments_available: {
            storeId: 'assignments_available',
            fields: ['group_name', 'group_guid', 'intent'],
            data: []
        },

        assignments_uninstall: {
            storeId: 'assignments_uninstall',
            fields: ['group_name', 'group_guid', 'intent'],
            data: []
        },

        selected_connections:{
            storeId: 'selected_connections',
            fields: ['id', 'name'],
            data: [],


        }
    }

});



