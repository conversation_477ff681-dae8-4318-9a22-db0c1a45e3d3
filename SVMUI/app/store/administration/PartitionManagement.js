Ext.define('sfw.store.administration.PartitionManagement', {
    extend: 'Ext.data.Store',

    alias: 'store.partitionmanagement',

    storeId: 'partitionmanagement',

    autoLoad:'true',

    remoteSort: true,

    sorters: [{
        property: 'account_name',
        direction: 'ASC'
    }],

    proxy: {
        type: 'ajax',
        url: 'action=account_management&which=overview_Partition&',
        reader: {
            type: 'json',
            totalProperty: 'data.total',
            successProperty: 'success',
        }
    }
});
