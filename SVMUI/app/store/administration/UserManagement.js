Ext.define('sfw.store.administration.UserManagement', {
    extend: 'Ext.data.Store',
    alias: 'store.sharesmartgroups',

    storeId: 'sharesmartgroups',

    fields: [
        {name: 'id', type: 'int'},
        {name: 'name', type: 'string'},
        { name: "is_shared", type: "int" },
        { name: "is_duplicate_name", type: "int" },
        {name: 'description', type: 'string'},
        {
            name: "status"
            ,type: "string"
            ,convert: function (value, record) {
                if (1 == record.data.is_shared) {
                    return "shared";
                }
                if (record.data.is_duplicate_name) {
                    return "duplicate name";
                }
                return "";
            }
        }
    ],

    proxy: {
        type: 'ajax',
        idProperty: "id",
        noCache : true,
        sortInfo: {
            field: 'name'
            ,direction: 'DESC'
        },
        url: 'action=smart_groups&which=shareList&',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        },



    },
    autoLoad : false

});

Ext.define('sfw.store.administration.UserManagementGrid', {
    extend: 'Ext.data.Store',
    alias: 'store.usermanagement',

    storeId: 'usermanagement',

    autoLoad:'true',

    remoteSort: true,

    sorters: [{
        property: 'account_name',
        direction: 'ASC',
    }],

    proxy: {
        type: 'ajax',
        url: 'action=account_management&which=overview_User&',
        reader: {
            type: 'json',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
})