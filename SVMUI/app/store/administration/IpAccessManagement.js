Ext.define('sfw.store.administration.IpAccessManagement', {
	extend: 'Ext.data.Store',

	alias: 'store.IpAccessmanagementgrid',

	storeId: 'IpAccessmanagementgrid',

	remoteSort: true,
    sorters: [{
        property: 'last_update',
        direction: 'DESC',
    }],

	proxy: {
		type: 'ajax',
		url: 'action=ajaxapi_csi_ip_access_rules&which=list&',
		reader: {
			type: 'json',
			rootProperty: 'data.rows',
			totalProperty: 'data.total',
			successProperty: 'success'
		}

	},
	autoLoad : true
});

Ext.define('sfw.store.administration.IpAccessCheckWindowCombo', {
	extend: 'Ext.data.Store',

	alias: 'store.IpAccessCheckWindowCombo',

	storeId: 'IpAccessCheckWindowCombo',

	fields: [
        { name: 'recipient_account_id', type: 'int' },
        { name: 'account_name', type: 'string' },
        { name: 'contact_value', type: 'string' }
    ],

    proxy: {
        type: 'ajax',
        url: 'action=recipients&which=get_recipients&module=101&method=1&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            successProperty: 'success'
        }

    },
    autoLoad: true
});






