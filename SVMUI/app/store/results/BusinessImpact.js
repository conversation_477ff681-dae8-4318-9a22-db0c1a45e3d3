Ext.define('sfw.store.results.BusinessImpact', {
    extend: 'Ext.data.Store',
    alias:'store.businessImpact',
    storeId: 'businessImpact',
    fields: [ 'id', 'type' ],
    // autoLoad: true,
    data: {
        items: [ 
            {
                id: 1, 
                type: 'Critical'
            },
            {
                id: 2, 
                type: 'High'
            },
            {
                id: 3, 
                type: 'Medium'
            },
            {
                id: 4, 
                type: 'Minor'
            },
            {
                id: 5, 
                type: 'Low'
            }
        ]
    },

    proxy: {
        type: 'memory',
        reader: {
            type: 'json',
            rootProperty: 'items'
        }
    }
});
