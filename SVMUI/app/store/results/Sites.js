Ext.define('sfw.store.results.Sites', {
	extend: 'Ext.data.Store',

	alias: 'store.sites',

	storeId: 'sitestsore',

	remoteSort: true,

	sorters: [{property:"group_name",direction:"ASC"}],

	proxy: {
		type: 'ajax',
		url: 'action=sites&which=sitesOverview&',
		reader: {
			type: 'json',
			rootProperty: 'data.rows',
			totalProperty: 'data.total',
			successProperty: 'success'
		}

	},
	autoLoad : true
});
