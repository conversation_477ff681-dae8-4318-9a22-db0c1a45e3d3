Ext.define('sfw.store.results.AdvisoryGridPopup', {
    extend: 'Ext.data.Store',
    alias: 'store.AdvisoryPopupGrid',
    storeId: 'AdvisoryPopupGrid',
    remoteSort: true,
    sorters: [{
        property: 'id',
        direction: 'DESC'
    }],
    proxy: {
        type: 'ajax',
        url: 'action=advisory&which=readInstallations&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
    autoLoad: true
});

Ext.define('sfw.store.results.AdvisoryGridHostPopup', {
    extend: 'Ext.data.Store',
    alias: 'store.AdvisoryGridHostPopup',
    storeId: 'AdvisoryGridHostPopup',
    remoteSort: true,
    sorters: [{
        property: 'host',
        direction: 'DESC'
    }],

    proxy: {
        type: 'ajax',
        url: 'action=advisory&which=readHosts&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
    autoLoad: true
});

Ext.define('sfw.store.results.AdvisoryGridProductPopup', {
    extend: 'Ext.data.Store',
    alias: 'store.AdvisoryGridProductPopup',
    storeId: 'AdvisoryGridProductPopup',
    remoteSort: true,
    sorters: [{
        property: 'product_name',
        direction: 'DESC'
    }],
    proxy: {
        type: 'ajax',
        url: 'action=advisory&which=readProducts&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
    autoLoad: true
});