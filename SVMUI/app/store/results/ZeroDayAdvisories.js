Ext.define('sfw.store.results.ZeroDayAdvisories', {
    extend: 'Ext.data.Store',

    alias: 'store.zerodayadvisories',
    model: 'sfw.model.results.ZeroDayAdvisories',

    remoteSort: true,

    sorters: [{property: "vuln_id", direction: "DESC"}],

    storeId: 'zerodayadvisories',
    pageSize: 30,
    fields: [{
        name: 'vuln_criticality',
        convert: function (value, record) {
            return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                0;
        }
    }],
    proxy: {
        type: 'ajax',
        url: 'action=smart_groups&which=zero_day_overview&',
        reader: {
            type: 'json',
            /*transform: {
                fn: function (data) {
                    if (!Ext.isEmpty(data.data.rows)) {
                        var response = data.data.rows.map(function (item) {
                            item.vuln_criticality = Number.isFinite(Ext.Number.parseInt(item.vuln_criticality, 10)) && Ext.Number.parseInt(item.vuln_criticality, 10) > 0 ?
                                20 * (6 - (parseInt(item.vuln_criticality, 10))) / 100 :
                                0;

                            return item;
                        });
                        return response;
                    }
                },
                scope: this
            },*/
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },
    autoLoad: true
});