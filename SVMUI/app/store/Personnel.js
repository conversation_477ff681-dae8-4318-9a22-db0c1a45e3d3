Ext.define('sfw.store.Personnel', {
    extend: 'Ext.data.Store',

    alias: 'store.personnel',

    model: 'sfw.model.Personnel',

    data: { items: [
        { name: '<PERSON>', email: "<EMAIL>", phone: "************" },
        { name: '<PERSON><PERSON>',     email: "worf.m<PERSON><PERSON><PERSON>@enterprise.com",  phone: "************" },
        { name: '<PERSON><PERSON>',   email: "<EMAIL>",    phone: "************" },
        { name: 'Data',     email: "<EMAIL>",        phone: "************" }
    ]},

    proxy: {
        type: 'memory',
        reader: {
            type: 'json',
            rootProperty: 'items'
        }
    }
});
