Ext.define('sfw.store.NavigationTree', {
    extend: 'Ext.data.TreeStore',

    storeId: 'NavigationTree',

    fields: [{
        name: 'text'
    }]//,

    /*
    root: {
        expanded: true,
        children: [
            {
                text: 'Dashboard',
                iconCls: 'x-fa fa-tachometer-alt fa-lg',
                viewType: 'dashboard-main',
                routeId: 'dashboard-main', // routeId defaults to viewType
                leaf: true
            },
            {
                text: 'Scanning',
                id: 'sfw.csiScanning',
                iconCls: 'x-fab fa-searchengin fa-lg',
                expanded: false,
                selectable: false,
                children: [{
                    text: 'Scan Paths',
                    viewType: 'sfw.scanPaths',
                    leaf: true
                }, {
                    text: 'Network Appliance Agents',
                    viewType: 'sfw.csiNetworkApplianceAgents',
                    leaf: true
                }, {
                    text: 'Completed Scans',
                    viewType: 'sfw.csiCompletedScans',
                    leaf: true
                }, {
                    text: 'Download Local Agent',
                    viewType: 'sfw.csiLocalAgentDownload',
                    leaf: true
                }, {
                    text: 'Download Network Agent',
                    viewType: 'sfw.csiNetworkAgentDownload',
                    leaf: true
                }, {
                    text: 'Single Host Agents',
                    viewType: 'sfw.csiAgentManagement',
                    leaf: true
                }, {
                    text: 'Custom Scan Rules',
                    viewType: 'sfw.csiCustomScanRules',
                    leaf: true
                }]
            },
            {
                text: 'Results',
                id: 'sfw.csiResults',
                iconCls: 'x-fa fa-clipboard-list',
                expanded: false,
                selectable: false,
                children: [{
                    text: 'Sites',
                    viewType: 'sfw.csiSitesOverview',
                    leaf: true,
                }]
            },
            {
                text: 'Reporting',
                id: 'sfw.csiReports',
                iconCls: 'x-fa fa-chart-bar fa-lg',
                expanded: false,
                selectable: false,
                children: [{
                    text: 'Report Configuration',
                    viewType: 'sfw.csiReportingConfiguration',
                    leaf: true
                }, {
                    text: 'Smart Group Notifications',
                    viewType: 'sfw.csiSmartgroupNotifications',
                    leaf: true
                }, {
                    text: 'Activity Log Notifications',
                    viewType: 'sfw.activityLognotifications',
                    leaf: true
                }, {
                    text: 'Database Console',
                    viewType: 'sfw.csiApiConsole',
                    leaf: true
                }]
            },
            {
                text: 'Patching',
                id: 'sfw.csiPatch',
                iconCls: 'x-fa fa-wrench fa-lg',
                expanded: false,
                selectable: false,
                children: [{
                    text: 'Flexera Package Patching (SPS)',
                    viewType: 'sfw.spspatching',
                    leaf: true
                }, {
                    text: 'Patch Deployment Status',
                    viewType: 'sfw.csiCreatePackageDeployment',
                    leaf: true
                }, {
                    text: 'Vendor Patch Module',
                    viewType: 'sfw.csiVendorPatchView',
                    leaf: true
                }, {
                    text: 'Agent Deployment',
                    viewType: 'sfw.csiAgentDeployment',
                    leaf: true
                },{
                    text: 'Patch Template',
                    viewType: 'sfw.patchtemplate',
                    leaf: true
                }]
            },
            {
                text: 'Administration',
                id: 'sfw.csiAdministration',
                iconCls: 'x-fa fa-user fa-lg',
                children: [{
                    text: 'Active Directory',
                    viewType: 'sfw.csiActiveDirectory',
                    leaf: true
                },{
                    text: 'User Management',
                    viewType: 'sfw.userManagement',
                    leaf: true
                },
                ]
            },
            {
                text: 'Configuration',
                iconCls: 'x-fa fa-cogs fa-lg',
                expanded: false,
                selectable: false,
                id: 'sfw.csiConfiguration',
                children: [{
                    text: 'Activity Log',
                    viewType: 'sfw.activityLog',
                    leaf: true
                }, {
                    text: 'Log Messages',
                    viewType: 'sfw.csiLogger',
                    leaf: true
                }, {
                    text: 'Change Password',
                    viewType: 'sfw.csiResetPassword',
                    leaf: true
                }, {
                    text: 'Password Policy Configuration',
                    viewType: 'sfw.csiPasswordPolicyConfiguration',
                    leaf: true
                }, {
                    text: 'Password Recovery Settings',
                    viewType: 'sfw.csiPasswordRecovery',
                    leaf: true
                }, {
                    text: 'IP Access Management',
                    viewType: 'sfw.csiIpManagement',
                    leaf: true
                }]
            }
        ]
    }*/
});
