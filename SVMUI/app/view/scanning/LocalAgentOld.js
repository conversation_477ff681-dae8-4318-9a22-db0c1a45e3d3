Ext.define('sfw.view.scanning.LocalAgentOld',{
    extend: 'Ext.window.Window',
    xtype:'sfw.localagentold',
    title: 'Download Previous Version Agent',
    width: 300,
    height: 300,

    requires: [
        'sfw.view.scanning.DownloadLocalAgentController',
    ],


    controller: 'scanning-downloadlocalagent',
    viewModel: {
        //type: 'scanning-downloadlocalagent',

        formulas:{
			getAgentIsConsoleTrue:function(){
				if(sfw.isConsole){
					return false;
				}
				else{
					return true;
				}
			},

			getAgentIsConsoleFalse:function(){
			    if(sfw.isConsole){
					return true;
				}
				else{
					return false;
				}
			},
			getOldAgentCount:function(){
			    var old_agents = sfw.util.Auth.LoginDetails.oldagentdetection;
	            var count = old_agents.data.length;
	            return count;
			}
		},
    },

    items:[{
        xytype: 'container',
        autoScroll: true,
       // bind:{
         //   data: '{getOldAgentCount}',
        //    hidden: '{getAgentIsConsoleTrue}'
        //},
        //tpl:
            //'<tpl for="data.count">',
            //'<li><a href="#" onClick="openAgentUrlPrevious('+0,old_agents.data[i]+');">Microsoft Windows</a> (ver. ' + old_agents.data[i] + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
			//'<li><a href="#" onClick="sfw.csiLocalAgentDownload.openAgentUrlPrevious('+1,old_agents.data[i]+');">Macintosh OS X</a> (ver. ' + old_agents.data[i] + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'
            //'</tpl>'
    },{
        xytype: 'panel',
        autoScroll: true,
        //bind:{
        //    data: '{getOldAgentCount}',
        //    hidden: '{getAgentIsConsoleFalse}'
        //},
        //tpl:
            //'<tpl for="data.count">',
            //'<li><a href="' + createAgentUrlPrevious(0,old_agents.data[i]) +  '">Microsoft Windows</a> (ver. ' + old_agents.data[i] + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
			//'<li><a href="' + createAgentUrlPrevious(1,old_agents.data[i]) + '">Macintosh OS X</a> (ver. ' + old_agents.data[i] + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'
            //'</tpl>'

    }]



});