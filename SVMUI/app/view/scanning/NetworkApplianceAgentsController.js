Ext.define('sfw.view.scanning.NetworkApplianceAgentsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.scanning-networkapplianceagents',

    doubleClickHandler: function(event, record){
        this.editHostConfig(null, null, {extra: record});
    },

    reloadNetworkApplianceAgentsGrid: function () {
        var networkapplianceagents = Ext.getStore('networkapplianceagents');
        networkapplianceagents.load({
            params: {
                'host': Ext.getCmp('network_appliances_search').getValue()
            },
            callback: function () {
                //console.log("Search success");
            },
            failure: function () {
                //console.log("Search failed");
            }
        });
    },

    handleSpecialKeysNetworkAppliance: function (field, e) {
        var me = this;
        if (e.getKey() == e.ENTER) {
            me.reloadNetworkApplianceAgentsGrid();
        }
    },

    updateBtnState: function (){
        var buttonDisabledFlag;
        const me = this,
            view = me.getView(),
            checkInFreq = view.down('#network_appliances_check_frequency_field'),
            checkSimInspect = view.down('#network_appliances_max_inspections_field'),
            saveBtn=view.down('#netAppAgentSaveCfgButton');
        if ( ( Ext.form.VTypes.numeric( checkInFreq.getValue() ) ) && Ext.form.VTypes.simInspect(checkSimInspect.getValue()) ) {
            buttonDisabledFlag = false;
        } else {
            buttonDisabledFlag = true;
        }
        saveBtn.setDisabled(buttonDisabledFlag);
    },

    editHostConfig: function (event, target, options) {
        agent_id = options.extra.data.nsi_device_id;
        var software_inspector_id = options.extra.data.software_inspector_id;
        var host = options.extra.data.host;
        group_id = options.extra.data.groupId;
        appliance = options.extra.data.network_appliance;
        var params = {
            groupId: ''
            , hostId: agent_id
        };

        Ext.Ajax.request({
            url: 'action=ajaxapi_agent_management&which=readAgentDetails',
            method: 'GET',
            dataType: 'json',
            params: params,
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);
                if (!Object.keys(response.data.rows).length) {
                    Ext.Msg.alert("Unexpected Error", "Unable to load agent configuration.");
                } else {
                    var values = response.data.rows[0];
                    var checkInInterval = values.check_in_interval;
                    var checkInIntervalTime = checkInInterval.substr(0, checkInInterval.length - 1);
                    var checkInIntervalUnit = checkInInterval.charAt(checkInInterval.length - 1);
                    var maxThreads = values.max_threads;

                    if (checkInIntervalUnit == 'M') {
                        checkInIntervalUnit = 'M:Minutes';
                    } else if (checkInIntervalUnit == 'H') {
                        checkInIntervalUnit = 'H:Hours';
                    } else {
                        checkInIntervalUnit = 'D:Days';
                    }

                    var editConfigWindow = Ext.create("sfw.view.scanning.NetworkApplianceAgentsConfiguration",
                        {
                            flag: 1,
                            response: response,
                            sfInspectorId: software_inspector_id
                        });
                    editConfigWindow.initComponent(agent_id);
                    editConfigWindow.show();
                    editConfigWindow.down('#network_appliances_check_frequency_unit_combo').setValue(checkInIntervalUnit);
                    editConfigWindow.down('#network_appliances_check_frequency_field').setValue(checkInIntervalTime);
                    editConfigWindow.down('#network_appliances_max_inspections_field').setValue(maxThreads);
                    editConfigWindow.setTitle("Agent Configuration for Host: " + host);
                }
            },
            failure: function (response, opts) {
            }
        });
    },

    editSiteConfig: function (event, target, options) {
        agent_id = options.extra.data.nsi_device_id;
        var software_inspector_id = options.extra.data.software_inspector_id;
        var host = options.extra.data.host;
        group_id = options.extra.data.group_id;
        appliance = options.extra.data.network_appliance;
        var group_name = options.extra.data.group_name;

        editMsg = "Confirm that you wish to edit/overwrite the scheduling settings for the standalone agents within the specified site?";

        var params = {
            groupId: group_id
            , hostId: ''
        };
        Ext.MessageBox.confirm('Confirm Edit : ' + group_name, editMsg, function (button) {
            if (button === 'yes') {
                Ext.Ajax.request({
                    url: 'action=ajaxapi_agent_management&which=readAgentDetails',
                    method: 'GET',
                    dataType: 'json',
                    params: params,
                    success: function (response, opts) {
                        var response = Ext.decode(response.responseText);
                        if (!Object.keys(response.data.rows).length) {
                            Ext.Msg.alert("Unexpected Error", "Unable to load agent configuration.");
                        } else {
                            var values = response.data.rows[0];
                            var checkInInterval = values.check_in_interval;
                            var checkInIntervalTime = checkInInterval.substr(0, checkInInterval.length - 1);
                            var checkInIntervalUnit = checkInInterval.charAt(checkInInterval.length - 1);
                            var maxThreads = values.max_threads;

                            if (checkInIntervalUnit == 'M') {
                                checkInIntervalUnit = 'M:Minutes';
                            } else if (checkInIntervalUnit == 'H') {
                                checkInIntervalUnit = 'H:Hours';
                            } else {
                                checkInIntervalUnit = 'D:Days';
                            }

                            var editConfigWindow = Ext.create("sfw.view.scanning.NetworkApplianceAgentsConfiguration",
                                {
                                    flag: 0,
                                    response: response,
                                    sfInspectorId: software_inspector_id
                                });
                            editConfigWindow.initComponent(agent_id);
                            editConfigWindow.show();
                            editConfigWindow.down('#network_appliances_check_frequency_unit_combo').setValue(checkInIntervalUnit);
                            editConfigWindow.down('#network_appliances_check_frequency_field').setValue(checkInIntervalTime);
                            editConfigWindow.down('#network_appliances_max_inspections_field').setValue(maxThreads);
                            editConfigWindow.setTitle("Agent Configuration for site: " + group_name);
                        }
                    },
                    failure: function (response, opts) {
                    }
                });
            }
        });
    },

    saveConfig: function (btn) {
        var view = btn.up('window');
        var url = 'action=ajaxapi_agent_management';
        if (view.getFlag() == 0) {
            url += '&group_id=' + group_id;
        } else {
            url += '&host_id=' + agent_id;
        }
        url += '&appliance=' + appliance;
        //console.log(url);
        // Handle agent check-in frequency
        var checkin_value = parseInt(view.down('#network_appliances_check_frequency_field').getValue(), 10);
        var checkin_type = view.down('#network_appliances_check_frequency_unit_combo').getValue();
        if (!isNaN(checkin_value)) {
            check_in_interval = (checkin_value && checkin_type ? (checkin_value + checkin_type.charAt(0)) : '10M');
        } else {
            check_in_interval = '10M';
        }

        // Handle maximum simultaneous inspection - must be > 0. Default is 10
        var max_threads = parseInt(view.down('#network_appliances_max_inspections_field').getValue(), 10);
        if (!max_threads) {
            max_threads = 10;
        }

        // Build POST data from update_vars
        var postData = {
            submit: 1,
            check_in_interval: check_in_interval,
            max_threads: max_threads
        };
        Ext.Ajax.request({
            url: url
            , method: "POST"
            , params: postData
            , dataType: 'json'
            , success: function (data) {
                var response = Ext.decode(data.responseText);
                if (response.success !== true) {
                    Ext.Msg.show({
                        title: "Error"
                        , msg: "Unable to save the configuration."
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.ERROR
                    });
                } else {
                    Ext.Msg.show({
                        title: "Success"
                        , msg: "Changes have been saved."
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.INFO
                    });
                }
            }
            , failure: function () {
                Ext.Msg.show({
                    title: "Error"
                    , msg: "Saving configuration failed."
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.ERROR
                });
            }
        });
        Ext.getCmp('networkapplianceagents_grid').getStore().reload();
        var view = this.getView();
        view.destroy();
    },

    removeAgent: function (event, target, options) {
        var deleteID = options.extra.data.nsi_device_id;
        var hostName = options.extra.data.host;
        var siteName = options.extra.data.group_name;
        var agentVersion = options.extra.data.agent_version;

        deleteMsg = "Confirm that the Network Appliance Agent is no longer installed on this host and you therefore no longer want these hosts to be shown in this window?";

        Ext.MessageBox.confirm('Confirm Deletion', deleteMsg, function (button) {
            if (button === 'yes') {
                Ext.Ajax.request({
                    url: 'action=ajaxapi_agent_management&which=deleteHosts&clean_up_id=' + deleteID + '&hostname=' + hostName + '&sitename=' + siteName + '&agent_version' + agentVersion + '&appliance=1',
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        var status = Ext.util.JSON.decode(response.responseText);
                        if (response.success === true) {
                            var count = response.data;
                            Ext.Msg.show({
                                title: 'Success'
                                , msg: 'Successfully removed Network Appliance Agents.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.INFO
                            });
                            Ext.getCmp('networkapplianceagents_grid').getStore().reload();
                        } else {
                            sfw.util.Debug.log('Error while trying to remove Network Appliance Agents(s).');
                            Ext.Msg.show({
                                title: 'Error'
                                , msg: 'Removing Network Appliance Agent(s) failed.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.ERROR
                            });
                        }
                    },
                    failure: function () {
                        sfw.util.Debug.log('Error while trying to remove Network Appliance Agents(s).');
                        Ext.Msg.alert("Unexpected Error", "Unable to delete...");
                    }
                });
            }
        });
    }

});