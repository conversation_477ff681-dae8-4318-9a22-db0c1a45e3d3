Ext.define('sfw.view.scanning.ImportInventoryWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.importInventory',
    width: 400,
    height: 300,
    modal: true,
    title: 'Import Inventory',
    layout: 'fit',
    controller: 'inventoryController',

    items: [{
        xtype: 'form',
        bodyPadding: 10,
        layout: "vbox",
        items: [{
            xtype: 'textfield',
            //width: 500,
            fieldLabel: 'Name',
            name: 'name',
            tabIndex: 1,
            allowBlank: false,
            itemId: 'inventoryname'
        },   {
            xtype: "tbspacer",
            height: "10px"
        }, {
            layout: 'hbox',
            itemId: 'idpMetadataXmlVal',
            width: 500,
            text: "Information",
            items: [
                {
                    xtype: 'fileuploadfield',
                    buttonText: 'Import CSV',
                    allowBlank: false,
                    buttonOnly: true,
                    fileUpload: true,
                    ui: 'primary',
                    itemId: 'import_button',
                    name: 'file',
                    listeners: {
                        afterrender: 'setCsv',
                        change: 'csvselected'
                    }
                },
                {
                    xtype: 'label',
                    itemId: 'csv_file_text_field',
                    padding: '0 0 0 10',
                    text: 'No file chosen',
                    margins: '5 0 0 10',
                    value : ''
                },


            ]

            },
            {
                xtype:'tbseparator'
            },
            {
            xtype: 'label',
                width : '100%',
                html: ('</br><p><b>Note</b>: CSV must include at least the following three column names: <b>Product</b>,<b>Vendor</b> and <b>Version</b>'
                     )
        }],
        buttons: [
            {
                text: 'Confirm',
                formBind: true,
                ui: 'primary',
                itemId:'confirmCsv',
                tooltip: {
                    title: 'Confirm Inventory entries',
                    text: 'Confirm Inventory entries.'
                },
                handler: "confirmInventory"
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ],

    }]

});