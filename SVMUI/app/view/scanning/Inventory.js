/**
 * This view displays the Inventory list.
 */
Ext.define('sfw.view.scanning.Inventory', {
    extend:'Ext.grid.Panel',
    xtype: 'sfw.csiInventory',
    id: 'inventory_grid',

    stateful: true,

    controller:'inventoryController',

    title: 'Inventory Assessment (Beta)',
    border: 1,
    cls: 'shadow',

   // reference: "inventory",

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data available for the selected date range and/or search criteria.'
    },

    store: {
        type: 'inventory'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'datefield',
            name: 'from_date',
            format:sfw.Globals.dateShortInput,
            value: sfw.Util.dateCreateTodayOffset(-14),
            itemId: 'from_date_inventory',
            listeners: {
                change: 'reloadInventoryGrid'
            }
        }, {
            xtype: 'datefield',
            name: 'to_date',
            itemId: 'to_date_inventory',
            format:sfw.Globals.dateShortInput,
            value: sfw.Util.dateCreateTodayOffset(),
            listeners: {
                change: 'reloadInventoryGrid'
            }
        }, {
            xtype: 'textfield',
            emptyText: 'Search ....',
            itemId: 'inventory_name'

        }, {
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler: 'reloadInventoryGrid'

        }, {
            xtype: 'button',
            text: 'Import Inventory',
            ui: 'primary',
            handler: 'inventoryImport'

        },{
            xtype: 'label',
            html: ' <a target="_blank" href="https://community.flexera.com/t5/Software-Vulnerability/SVM-Inventory-Based-Vulnerability-Assessment/ba-p/247682">Click here</a> to learn more about known limitations and future plans.',
            itemId: 'BlogText',
        },{
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }
        ]
    }],


    columns: [
        { text: 'Time', dataIndex: 'status_date', renderer: sfw.Default.gridRenderUTCDateInLocaltime, flex: 2 },
        { text: 'Inventory Name', dataIndex: 'inventory_name', width: 300 },
        { text: 'Inventory Status', dataIndex: 'status', flex: 1 ,renderer : 'getStatus'},
        { text: 'Insecure', dataIndex: 'no_insecure', flex: 1, align: 'right' },
        { text: 'End-of-Life', dataIndex: 'no_eol', flex: 1, align: 'right' },
        { text: 'Secure', dataIndex: 'no_patched', flex: 1, align: 'right' },
        { text: 'Potentially Insecure', dataIndex: 'no_potential_insecure', flex: 1, align: 'right' },
        { text: 'Unknown', dataIndex: 'no_unknown', flex: 1, align: 'right' },
        { text: 'Invalid', dataIndex: 'no_invalid', flex: 1, align: 'right' },
        { text: 'Total', dataIndex: 'no_total', flex: 1, align: 'right' }
    ],

    listeners: {
        beforerender:'reloadInventoryGrid',
        itemcontextmenu: function (grid, record, item, index, e) {
            if((typeof sfw.util.Auth.LoginDetails.account.roleIds[ sfw.ROLE_RESULTS ] !== "undefined") && (record.data.status == 2)) {
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    controller: 'inventoryController',
                    //width: 100,
                    plain: true,
                    items: [{
                        text: 'View Inventory Result',
                        listeners: {
                            click: {fn: 'InventoryResultWindow', extra: record}
                        }
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
            }
            else if (typeof sfw.util.Auth.LoginDetails.account.roleIds[ sfw.ROLE_RESULTS ] === "undefined"){
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    //width: 150,
                    plain: true,
                    items: [{
                        text: 'No Access To Results',
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                contextMenu.setDisabled(true);
            }
            else{
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    //width: 100,
                    plain: true,
                    items: [{
                        text: 'No Result Exists',
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                contextMenu.setDisabled(true);
            }
        },
        itemdblclick:'InventoryResultWindowdblcick',
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{inventory}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Inventories {0} - {1} of {2}',
        emptyMsg: "No data found."
    }
});


