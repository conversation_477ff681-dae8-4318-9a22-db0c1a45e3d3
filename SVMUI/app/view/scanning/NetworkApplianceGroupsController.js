Ext.define('sfw.view.scanning.NetworkApplianceGroupsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.scanning-networkappliancegroups',

    gridRenderWindowsUpdate: function(value){
        return (value ? 'Yes' : 'No');
    },

    gridRenderNumberTargets: function (targets){
        //Todo - require ActiveX
    },

    initViewModel: function(vm) {
        vm.bind('{selectedGroup}', 'onSelect', this);
    },

    onSelect: function(selection) {
        const me = this,
            grid = me.getView();
        var selected = grid.getSelectionModel().getSelected();
        scanGroupText = (selected.items.length > 1) ? 'Scan ' + selected.items.length + ' selected groups' : 'Scan 1 selected group';
        if (selection) {
            grid.down('#scan_selected_group').setDisabled(false);
            grid.down('#scan_selected_group').setText(scanGroupText);
        }
    },

    removeGroup : function (event, target, options){
        var _this = this;
        var ids =[],names=[], params = {};
        for (var i=0; i<options.extra.length; ++i){
            ids.push(options.extra[i].data.scan_group_id);
            names.push(options.extra[i].data.scan_group_name);
        }
        var msg = "Are you sure you want to permanently delete the scan group named "+ options.extra[0].data.scan_group_name;
        if (options.extra.length>1){
            msg = "Are you sure you want to permanently delete "+ options.extra.length + " scan groups";
        }
        params = {
            source: 'scanGroups',
            "selected_groups[]": ids,
            "selected_groups_names[]": names
        };

        Ext.Msg.show({
            title:'Confirm Deletion',
            message: msg,
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    _this.deleteSelectedGroups(params);
                } else if (btn === 'no') {
                    //console.log('NO');
                }
            }
        });
    },

    deleteSelectedGroups: function(deleteparams){
        Ext.Ajax.request({
            url: 'action=scan_groups&which=delete',
            method: 'POST',
            dataType: 'json',
            params: deleteparams,
            success: function( data ) {
                try{
                data = Ext.util.JSON.decode( data.responseText );
                switch(data.error){
                    case 0:
                        var msg = '';
                        if (data.deleted.length>1){
                            msg = 'Successfully deleted '+ data.deleted.length+' groups.';
                        }else {
                            msg = 'Successfully deleted selected group.';
                        }
                        Ext.Msg.alert('Success',msg);
                        Ext.getCmp('networkappliancegroups_grid').getStore().reload();
                        break;
                    case 3:
                        Ext.Msg.alert("Deletion Error", "Could not delete group(s)");
                        break;
                    default:
                        Ext.Msg.alert("Unexpected Error", "Unable to delete group(s)");
                        break;
                }
                }
                catch(ex){
                    //console.log('Error in deleteSelectedGroups while handling resposne', ex );
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to delete group(s)");
            }
        });
    },

    scanGroupConfig: function(event, target, options){
        var groupIds =[] ,params = {};
        for (var i=0; i<options.extra.length; ++i){
            groupIds.push(options.extra[i].data.scan_group_id);
        }
        params = {
            source: 'networkApplianceGroups',
            "selected_groups[]": groupIds
        };
        this.scanGroupAPICall(params);
    },

    scanGroupAPICall :  function (params){
        Ext.Ajax.request({
            url: 'action=scan_groups&which=scan_now',
            method: 'POST',
            dataType: 'json',
            params: params,
            success: function( data ) {
                data = Ext.util.JSON.decode( data.responseText );
                switch (data.error){
                    case 0:
                        Ext.Msg.alert('Success', 'Scanning will start as soon as possible,');
                        Ext.getCmp('networkappliancegroups_grid').getStore().reload();
                        break;
                    case 4:
                        Ext.Msg.alert('Scan Error', 'Scanning failed.,');
                        break;
                    default:
                        Ext.Msg.alert('Unexpected Error', 'Unable to start scan.');
                }

            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to start scan");
            }
        });
    },

    scanNowButton: function () {
        var selected = Ext.getCmp('networkappliancegroups_grid').getSelectionModel().getSelected();
        var groupIds =[] ,params = {};
        for (var i=0; i<selected.length; ++i){
            groupIds.push(selected.items[i].data.scan_group_id);
        }
        params = {
            source: 'networkApplianceGroups',
            "selected_groups[]": groupIds
        };
        this.scanGroupAPICall(params);
    }

});
