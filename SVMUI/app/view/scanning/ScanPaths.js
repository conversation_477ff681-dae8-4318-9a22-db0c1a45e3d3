Ext.define('sfw.view.scanning.ScanPaths', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiScanPaths',

    requires: [
        'sfw.store.scanning.ScanPaths',
        'sfw.view.scanning.ScanPathsController',
        'sfw.Util'
    ],
    stateful: true,
    stateId: 'sfw_csiScanPaths_grid',
    title: 'Scan Paths',
    border: 1,
    controller: 'scanpaths',

    store: {
        type: 'scanpaths'
    },

    selModel: {
        mode: 'MULTI'
    },
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'button',
            text: 'Add Allow List Rule',
            itemId: 'addButton',
            ui: 'primary',
            handler: function () {
                var blocklistwindow = Ext.create('sfw.view.scanning.AddBlockListRuleWindow', {
                    listeners: {
                        afterrender: function (scanWindow) {
                            scanWindow.down('#impactListCheck').show();
                            scanWindow.down('#previewList').show();
                            if (Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
                                scanWindow.down('#impactListCheck').hide();
                                scanWindow.down('#previewList').hide();
                                blocklistwindow.down("title").setHtml("New Scan Path rule for Allow List");
                            } else if(Ext.ComponentQuery.query("#blocklist")[0].getValue()){
                                blocklistwindow.down("title").setHtml("New Scan Path rule for Block List");
                            }

                            blocklistwindow.down('#savingType').setText('NEW');
                        }
                    }
                });
                blocklistwindow.show();
            }
        }, {
            xtype: 'fieldcontainer',
            defaultType: 'radiofield',
            layout: "hbox",
            items: [{
                boxLabel: 'Allow List',
                name: 'scanpathlist',
                inputValue: '0',
                itemId: 'allowlist',
                padding: '10 10 10 10',
                checked: true,
                listeners: {
                    change: 'scanListStatus'
                }
            }, {
                boxLabel: 'Block List',
                name: 'scanpathlist',
                inputValue: '1',
                itemId: 'blocklist',
                padding: '10 10 10 10',
                listeners: {
                    change: 'scanListStatus'
                }
            }]
        }, {
            xtype: 'panel',
            flex: 1
        }, {
            xtype: 'exportButton'
        }]
    }],

    columns: [
        {text: 'Name', dataIndex: 'name', flex: 2},
        {text: 'Path', dataIndex: 'path', flex: 3, renderer: sfw.Util.tipRenderer},
        {text: 'Site', dataIndex: 'site', flex: 1},
        {text: 'Logged', dataIndex: 'type', flex: 1,renderer:function(input){
            if(input == 1){
                return "No";
            }else if(input == 2){
                return "Yes";
            }
        }}
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {

            var selected = grid.getSelectionModel().getSelected(),
                typeText, additionalText;

            if (Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
                typeText = 'Allow';
            } else if(Ext.ComponentQuery.query("#blocklist")[0].getValue()) {
                typeText = 'Block';
            }

            if (selected.items.length > 1) {
                additionalText = 'Delete All Selected ' + typeText + ' List Rule';
            } else {
                additionalText = 'Delete ' + typeText + ' List Rule';
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'scanpaths',
                width: 250,
                plain: true,
                items: [{
                    text: 'View/Edit ' + typeText + ' List Rule',
                    listeners: {
                        afterRender: function () {
                            if (selected.items.length > 1) {
                                this.hide();
                            }
                        },
                        click: {fn: 'viewEditAllowListRule', extra: record}
                    }
                }, {
                    text: additionalText,
                    listeners: {
                        click: {fn: 'confirmDeleteAllowListRule', extra: selected.items}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        afterrender: 'reloadScanPathsGrid',
        itemdblclick:'viewEditAllowListRuledbClick'

    },

    bbar: {
        xtype: 'pagingtoolbar',
        store: 'scanpaths',
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Paths {0} - {1} of {2}',
        emptyMsg: "No Path configured"
    }
});


