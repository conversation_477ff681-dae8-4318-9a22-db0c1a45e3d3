/*
    TODO-
    docked item menu UI - Scan selected group,
    ActiveX - New Group and Edit Group menu API integration
 */

Ext.define('sfw.view.scanning.NetworkApplianceGroups', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiNetworkApplianceGroups',
    id: 'networkappliancegroups_grid',

    requires: [
        'sfw.view.scanning.NetworkApplianceGroupsController',
        'sfw.store.scanning.NetworkApplianceGroups',
        'sfw.Default'
    ],
    stateful: true,
    stateId: 'sfw_csiNetworkApplianceGroups_grid',
    store: {
        type: 'networkappliancegroups'
    },

    viewModel: {
        data: {
            selection: null
        }
    },

    bind: {
        selection: '{selectedGroup}'
    },

    selModel: {
        mode: 'MULTI'
    },

    viewConfig: {
        deferEmptyText: false,
        emptyText: 'No data.'
    },

    controller: 'scanning-networkappliancegroups',
    title: 'Network Appliance Groups',
    border: 1,
    cls: 'shadow',
    scrollable: true,
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },
    dockedItems: [
        {
            xtype: "toolbar",
            dock: "top",
            items: [
                {
                    xtype: 'button',
                    itemId: 'scan_selected_group',
                    ui: 'primary',
                    disabled: true,
                    handler: "scanNowButton",
                    text: 'Scan selected group'
                },
                {
                    xtype: 'button',
                    ui: 'primary',
                    disabled: true,
                    text: 'New Group'
                },
                {
                    xtype: "tbfill",
                    flex: 1
                },
                {
                    xtype: "exportButton"
                }
            ]
        }
    ],

    columns: [
        {text: "Group", dataIndex: "scan_group_name", flex: 3, sortable: true},
        {
            text: "Scan Type",
            dataIndex: "scan_type",
            align: "right",
            sortable: true
        },
        {
            text: "Check Microsoft Updates",
            flex: 1,
            renderer: 'gridRenderWindowsUpdate',
            dataIndex: "check_updates",
            align: "right",
            sortable: true
        },
        {
            text: "Number of Targets",
            flex: 1,
            renderer: 'gridRenderNumberTargets',
            dataIndex: "number_of_targets",
            align: "right",
            sortable: true
        },
        {
            text: "Last Scan",
            flex: 1,
            renderer: sfw.util.Default.gridRenderUTCDateInLocaltime,
            dataIndex: "last_inspection",
            align: "right",
            sortable: true
        },
        {
            text: "Next Scan",
            flex: 1,
            renderer: sfw.Default.gridRenderPastDate,
            dataIndex: "next_inspection",
            align: "right",
            sortable: true
        }
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            scanGroupText = (selected.items.length > 1) ? 'Scan ' + selected.items.length + ' Selected Groups' : 'Scan Group';
            deleteGroupText = (selected.items.length > 1) ? 'Delete ' + selected.items.length + ' Selected Groups' : 'Delete Group';
            var contextMenu = Ext.create("Ext.menu.Menu", {
                controller: "scanning-networkappliancegroups",
                plain: true,
                items: [
                    {
                        //TODO-Edit Group Context Menu
                        text: "Edit Group",
                        disabled: true,
                        listeners: {
                            afterRender: function () {
                                if (selected.items.length > 1) {
                                    this.hide();
                                }
                            },
                            click: {fn: 'editGroupConfig', extra: record}
                        }
                    },
                    {
                        text: scanGroupText,
                        //,disabled: LoginDetails.isReadOnly
                        listeners: {
                            afterRender: function () {
                            },
                            click: {fn: 'scanGroupConfig', extra: selected.items}
                        }
                    },
                    "-",
                    {
                        text: deleteGroupText,
                        listeners: {
                            click: {fn: 'removeGroup', extra: selected.items}
                        }
                    }
                ]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        }
    },

    bbar: {
        xtype: "pagingtoolbar",
        bind: {
            store: "{networkappliancegroups}"
        },
        displayInfo: true,
        pageSize: 30,
        displayMsg: "Displaying groups {0} - {1} of {2}",
        emptyMsg: "No groups found"
    }
});
