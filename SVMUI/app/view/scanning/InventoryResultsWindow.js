Ext.define("sfw.view.scanning.InventoryResultsWindow", {
    extend: "Ext.window.Window",
    alias: "widget.inventoryResultWindow",
    width: 1000,
    height: 700,
    bodyPadding: 0,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    maximizable: true,
    layout: "fit",

    controller: 'inventoryController',

    viewModel: {
        data: {
            'inventoryscandetails': null
        },

        formulas:{
            time: function (get) {
                if((typeof get('inventoryscandetails.inventory.status_date')) !== 'undefined'){
                    return sfw.Default.returnDateTime(get('inventoryscandetails.inventory.status_date'),true);
                }else{
                    return 'n/a';
                }
            },
            valid : function(get){
                var valid = parseInt(get('inventoryscandetails.inventory.no_patched')) + parseInt(get('inventoryscandetails.inventory.no_insecure')) + parseInt(get('inventoryscandetails.inventory.no_eol')) + parseInt(get('inventoryscandetails.inventory.no_potential_insecure')) + parseInt(get('inventoryscandetails.inventory.no_unknown'));
                return valid.toString();
            },
            totalValidInvalidRecords : function(get){
                var valid = parseInt(get('inventoryscandetails.inventory.no_patched')) + parseInt(get('inventoryscandetails.inventory.no_insecure')) + parseInt(get('inventoryscandetails.inventory.no_eol')) + parseInt(get('inventoryscandetails.inventory.no_potential_insecure')) + parseInt(get('inventoryscandetails.inventory.no_unknown'));
                var total = valid + parseInt(get('inventoryscandetails.inventory.no_invalid'));
                return total.toString();
            },
            inventoryName : function(get){
                var inventoryName = get('inventoryscandetails.inventory.inventory_name').split('.');
                return inventoryName[0];
            },
            vulnerabilities : function(get){
                if(get('inventoryscandetails.vulnerabilities.vulnerabilities') == ""){
                    return '0';
                }else{
                    return get('inventoryscandetails.vulnerabilities.vulnerabilities').toString();
                }
            }
        },

        stores: {
            inventoryresults: {
                storeId: 'inventoryresults',
                pageSize: '30',
                remoteSort: true,
                sorters: [{
                    property: "product_name",
                    direction: "ASC"
                }],

               fields: [{
                    name: 'vuln_criticality',
                    convert: function (value, record) {
                        return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                            20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                            0;
                    }
                }],

                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        read: 'GET'
                    },
                    noCache: false,
                    url: 'action=inventory&which=get_inventory_results',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                }
            },
            unknowninventoryresults: {
                storeId: 'unknowninventoryresults',
                pageSize: '30',
                remoteSort: true,
                sorters: [{
                    property: "product_name",
                    direction: "ASC"
                }],

                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        read: 'GET'
                    },
                    noCache: false,
                    url: 'action=inventory&which=get_inventory_results&unknown=true',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                }
            },
            inventorypiestore: {
                storeId: 'inventoryoverview',
                proxy: {
                    type: 'memory',
                    reader: {
                        type: 'json',
                        rootProperty: 'data'
                    }
                }
            }

        }
    },

    bind: {
        title: '{inventoryName}'
    },
    items: [{
        xtype: 'tabpanel',
        tabBar: {
            layout: {
                pack: 'center'
            }
        },
        plain: true,
        items: [{
            title: 'Overview',
            layout: 'fit',
            items: [{
                xtype: 'panel',
                bodyPadding: 0,

                layout: {
                    type: 'hbox',
                    align: 'stretch'
                },
                defaults: {
                    //bodyStyle: 'padding:20px'
                    margin: 5
                },
                items: [{
                    xtype: 'panel',
                    flex: 3,
                    ui: 'light',
                    border: 1,
                    bodyPadding: 5,
                    layout: {
                        type: 'table',
                        columns: 2
                    },
                    items: [
                        {
                            html: '<b>Inventory Details</b><br>',
                            colspan: 2
                        }, {
                            html: '&nbsp;&nbsp;Time:',
                            width: 300
                        },{
                            bind: {
                                html: '{time}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '&nbsp;&nbsp;Valid Records:'
                        }, {
                            bind: {
                                html: '{valid}'
                            }, bodyStyle: 'text-align: right;'
                        }, {
                            html: '&nbsp;&nbsp;Invalid Records:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_invalid}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '<hr>'
                        }, {
                            html: '<hr>'
                        },{
                            html: '&nbsp;&nbsp;Total Records:'
                        }, {
                            bind: {
                                html: '{totalValidInvalidRecords}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '<br>'
                        },{
                            html: '<br>'
                        },{
                            html: '<b>Inventory Advisories</b>',
                            colspan: 2
                        },{
                            html: '&nbsp;&nbsp;Advisories:'
                        },{
                            bind: {
                                html: '{vulnerabilities}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '<br>'
                        },{
                            html: '<br>'
                        },{
                            html: '<b>Inventory Assessment Details</b>',
                            colspan: 2
                        },{
                            html: '&nbsp;&nbsp;Secure:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_patched}'
                            }, bodyStyle: 'text-align: right;'
                        }, {
                            html: '&nbsp;&nbsp;Insecure:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_insecure}'
                            }, bodyStyle: 'text-align: right;'
                        }, {
                            html: '&nbsp;&nbsp;EOL:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_eol}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '&nbsp;&nbsp;Potentially Insecure:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_potential_insecure}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '&nbsp;&nbsp;Unknown:'
                        }, {
                            bind: {
                                html: '{inventoryscandetails.inventory.no_unknown}'
                            }, bodyStyle: 'text-align: right;'
                        },{
                            html: '<hr>'
                        }, {
                            html: '<hr>'
                        },{
                            html: '&nbsp;&nbsp;Total:'
                        }, {
                            bind: {
                                html: '{valid}'
                            }, bodyStyle: 'text-align: right;'
                        }
                    ]
                },{
                    flex: 3,
                    border: 1,
                    ui: 'light',
                    xtype: 'polar',
                    renderTo: document.body,
                    //width: 400,
                    //height: 400,
                    innerPadding: 20,
                    theme: 'default-gradients',
                    bind: {
                        store: '{inventorypiestore}'
                    },
                    legend: {
                        docked: 'bottom'
                    },
                    series: [{
                        type: 'pie',
                        angleField: 'data',
                        colors: [sfw.Default.returnStatusColor(3), sfw.Default.returnStatusColor(2), sfw.Default.returnStatusColor(1),sfw.Default.returnStatusColor(6),sfw.Default.returnStatusColor(8)],
                        label: {
                            field: 'label',
                            renderer: function (text, sprite, config, rendererData, index) {
                                var rec = rendererData.store.findRecord('label', text);
                                return rec.get('data') + '%';
                            }
                        },
                        highlight: true,
                        tooltip: {
                            trackMouse: true,
                            renderer: function (tooltip, record, item) {
                                tooltip.setHtml('Proudcts: ' + record.get('count'));
                            }
                        }
                    }]

                }]
            }]
        },
            {
                title: 'Inventory Results',
                // tabConfig: {
                //     listeners: {
                //         click: sfw.Default.scanResult
                //     }
                // },
                layout: 'fit',
                padding: 0,
                items: [{
                    xtype: 'gridpanel',
                    title: 'Inventory Results',
                    preventHeader: true,
                    bind: {
                        store: '{inventoryresults}'
                    },
                    viewConfig: {
                        deferEmptyText: false,
                        emptyText: 'No Products found'
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: 'solid',
                        items: [
                            {
                                xtype: 'checkboxfield',
                                itemId: 'secureinventory',
                                boxLabel: 'Secure',
                                name: 'secure',
                                checked: true,
                                handler: 'inventoryResult'
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'endoflifeinventory',
                                boxLabel: 'End-Of-life',
                                name: 'endoflife',
                                checked: true,
                                handler: 'inventoryResult'
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'insecureinventory',
                                boxLabel: 'Insecure',
                                name: 'insecure',
                                checked: true,
                                handler: 'inventoryResult'
                            }, {
                                xtype: 'checkboxfield',
                                itemId: 'potentialinsecureinventory',
                                boxLabel: 'Potentially Insecure',
                                name: 'potentialinsecure',
                                checked: true,
                                handler: 'inventoryResult'
                            }, {
                                xtype: 'panel',
                                flex: 1
                            }, {
                                xtype: 'exportButton'
                            }]
                    }],

                    columns: [
                        {text: 'Product Name', dataIndex: 'product_name', flex: 1},
                        {text: 'Version', dataIndex: 'version', flex: 1},
                        {
                            text: 'State', dataIndex: 'state', flex: 1, renderer: function (state) {
                                if (state === 'Insecure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(1) + "'>Insecure</font>";
                                } else if (state === 'End-Of-Life') {
                                    return "<font color='" + sfw.Default.returnStatusColor(2) + "'>End-Of-Life</font>";
                                } else if (state === 'Secure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(0) + "'>Secure</font>";
                                } else if (state === 'Potential Insecure'){
                                    return "<font color='" + sfw.Default.returnStatusColor(6) + "'>  Potentially Insecure</font>";
                                }
                            }
                        },
                        {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                       // { text: 'Criticality', dataIndex: 'vuln_criticality', flex: 1, renderer:sfw.Default.advisoryCriticalityImage },
                        {
                            xtype: 'widgetcolumn',
                            text: 'Criticality',
                            dataIndex: 'vuln_criticality',
                            align: 'center',
                            sortable: true,
                            width: 90,
                            widget: {
                                xtype: 'sectorprogress',
                                height: 8
                            }
                        },
                        {
                            text: 'CVSS Base Store',
                            dataIndex: 'vuln_overall_score',
                            flex: 1,
                            renderer: sfw.Default.renderCvssText
                        },
                        {
                            text: 'Threat Score',
                            dataIndex: 'vuln_threat_score',
                            align: 'right',
                            renderer: sfw.Default.threatScoreDefault
                        },
                        {text: 'CVSS2 Base Score', dataIndex: 'vuln_cvss_score', hidden: true},
                        {text: 'CVSS3 Base Score', dataIndex: 'vuln_cvss3_score', hidden: true},
                        {text: 'CVSS4 Base Score', dataIndex: 'vuln_cvss4_score', hidden: true},
                        {
                            text: 'Issued',
                            dataIndex: 'vuln_create_date',
                            flex: 1,
                            renderer: function (date, empty, record) {
                                var issued = date;
                                if ("0000-00-00" === issued || "0000-00-00 00:00:00" === issued || !issued ) {
                                    issued = "-";
                                }
                                if (issued !== "-") {
                                    var daysPassed = sfw.Util.differenceBetweenDates(issued, sfw.Util.dateCreate(), 1);
                                    daysPassed = daysPassed.replace("-", "");
                                    daysPassed = daysPassed + " ago";
                                    issued = daysPassed;
                                }

                                return issued;
                            }
                        },
                        {
                            text: 'Vulnerabilities',
                            dataIndex: 'vuln_count',
                            flex: 1,
                            align: 'right',
                            renderer: function (data, empty, record) {
                                if ((data == "0") || (data == "-")) {
                                    return "-";
                                } else {
                                    return data;
                                }
                            }
                        },
                        {text: 'Vendor Name', dataIndex: 'company_name', hidden:true},
                        {text: 'Secure Version', dataIndex: 'secure_version', hidden:true},
                        {text: 'Soft Type', dataIndex: 'soft_type', hidden:true}
                    ],

                    listeners: {

                        afterrender:'inventoryResult',
                        beforerender: function (grid) {
                            if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                                columns  = this.getView().getGridColumns();
                                columns[7].destroy();
                            }
                        }
                    },

                    // layout: 'fit',
                    margin: '2 2 0 0',

                    bbar: {
                        xtype: 'pagingtoolbar',
                        bind: {
                            store: '{inventoryresults}'
                        },
                        region: 'south',
                        displayInfo: true,
                        displayMsg: 'Displaying products {0} - {1} of {2}',
                        emptyMsg: "No products found"
                    }
                }]
            },
            {
                title: 'Unknown Results',
                layout: 'fit',
                padding: 0,
                items: [{
                    xtype: 'gridpanel',
                    title: 'Unknown Results',
                    preventHeader: true,
                    bind: {
                        store: '{unknowninventoryresults}'
                    },
                    viewConfig: {
                        deferEmptyText: false,
                        emptyText: 'No Products found'
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: 'solid',
                        items: [ {
                            xtype: 'checkboxfield',
                            itemId: 'invalidinventory',
                            boxLabel: 'Show only invalid records',
                            name: 'invalid',
                            handler: 'unknownInventoryResult'
                        },{
                            xtype: 'checkboxfield',
                            itemId: 'deleteinvalidinventory',
                            boxLabel: 'Delete all invalid records',
                            disabled : true,
                            name: 'deleteinvalid',
                            handler: 'deleteInvalidInventoryResult'
                        },
                           {
                                xtype: 'panel',
                                flex: 1
                            }, {
                                xtype: 'exportButton'
                            }]
                    }],

                    columns: [
                        {text: 'Product', dataIndex: 'product_name', flex: 1},
                        {text: 'Version', dataIndex: 'version', flex: 1},
                        {text: 'Vendor', dataIndex: 'company_name'},
                    ],

                    listeners: {
                        afterrender: 'unknownInventoryResult',
                    },

                    // layout: 'fit',
                    margin: '2 2 0 0',

                    bbar: {
                        xtype: 'pagingtoolbar',
                        bind: {
                            store: '{unknowninventoryresults}'
                        },
                        region: 'south',
                        displayInfo: true,
                        displayMsg: 'Displaying products {0} - {1} of {2}',
                        emptyMsg: "No products found"
                    }
                }]
            }
        ]
    }],

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'bottom',
        layout: {
            pack: 'end'
        },
        items: [
            {
                text: 'Close',
                ui: 'primary',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]
});
