Ext.onReady(function () {
    Ext.apply(Ext.form.VTypes, {
        numeric: function (val) {
            return /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9])$/.test(val);
        },
        numericText: 'Not a valid number. The number must be between 1-999.',
        simInspect: function (val) {
            return /^([1-9]|[1-9][0-9])$/.test(val);
        },
        simInspectText: 'Not a valid number. The number must be between 1-99.'
    });
});

Ext.define('sfw.view.scanning.NetworkApplianceAgentsConfiguration', {
    extend: "Ext.window.Window",
    bodyPadding: 5,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    controller: 'scanning-networkapplianceagents',

    config: {
        flag: 0,
        sfInspectorId: null,
        response: null
    },

    initComponent: function (agent_id) {

        Ext.applyIf(this, {
            items: [{
                xtype: 'form',
                reference: "networkapplianceagentsconfigForm",
                monitorValid: true,
                labelWidth: 75,
                autoHeight: true,
                //frame: true,
                bodyStyle: 'padding: 5px 5px 0;',
                border: false,
                defaults: {width: 600},
                defaultType: 'textfield',
                labelAlign: 'left',

                items: [
                    new Ext.FormPanel({
                        frame: true,
                        ui: 'light',
                        autoHeight: true,
                        labelAlign: 'right',
                        labelWidth: 200,
                        bodyStyle: 'padding: 5px 5px 0',
                        border: false,
                        items: [{
                            html: 'This form provides configuration options for the selected Network Appliance Agent(s). Many of the options available when configuring a Network Appliance Agent (scan type, frequency, etc.) apply to the Network Appliance Group(s) being scanned by the Network Appliance Agent, rather than the Network Appliance Agent itself.<br><br>'
                        }, {
                            xtype: 'fieldset',
                            title: 'Check-In Frequency for New Configuration',
                            items: [{
                                html: 'This setting defines the check-in frequency, which specifies how often the Network Appliance Agent will check in with the server to see if a new configuration is available or if new hosts are available to be scanned.<br><br>'
                            }, {
                                xtype: 'textfield',
                                allowBlank: false,
                                vtype: 'numeric',
                                itemId: 'network_appliances_check_frequency_field',
                                labelAlign: 'left',
                                fieldLabel: 'Check-In Frequency',
                                enableKeyEvents: true,
                                listeners: {
                                    keyup: 'updateBtnState'
                                }
                            }, {
                                xtype: 'combo',
                                itemId: 'network_appliances_check_frequency_unit_combo',
                                fieldLabel: 'Time Unit',
                                store: new Ext.data.SimpleStore({
                                    idIndex: 0,
                                    fields: ['time_unit'],
                                    data: [['M:Minutes'], ['H:Hours'], ['D:Days']]
                                }),
                                valueField: 'time_unit',
                                displayField: 'time_unit',
                                selectOnFocus: false,
                                mode: 'local',
                                typeAhead: false,
                                editable: false,
                                triggerAction: 'all'
                            }]
                        }, {
                            xtype: 'fieldset',
                            title: 'Maximum Simultaneous Inspections',
                            items: [{
                                html: 'This setting defines the number of Simultaneous Inspections that can be executed. We recommend setting it between 5 and 10, depending on the power of the computer and the network capacity available. The value may be from 1 to 99.<br><br>'
                            }, {
                                xtype: 'textfield',
                                allowBlank: false,
                                vtype: 'simInspect',
                                itemId: 'network_appliances_max_inspections_field',
                                labelAlign: 'left',
                                fieldLabel: 'Simultaneous Inspections',
                                enableKeyEvents: true,
                                listeners: {
                                    keyup: 'updateBtnState'
                                }
                            }]
                        }],
                        buttons: [{
                            text: 'Save Configuration',
                            itemId: 'netAppAgentSaveCfgButton',
                            tooltip: {
                                text: 'Save the configuration of the Network Appliance Agent that you have entered in the form above.'
                            },
                            //disabled: LoginDetails.isReadOnly,
                            handler: "saveConfig"

                        }, {
                            text: 'Cancel',
                            handler: function () {
                                this.up('window').destroy();
                            }
                        }],
                        keys: [{
                            //key: [Ext.EventObject.ENTER],
                            handler: function () {
                                /* if (false !== LoginDetails.isReadOnly) {
                                    // Readonly users can't save so return here
                                    return;
                                } */
                                //Ext.getCmp('netAppAgentSaveCfgButton' + obj.network_appliances_id).handler.call(self);
                            }
                        }]
                    })
                ]
            }]
        });
        this.callParent();
    }

});