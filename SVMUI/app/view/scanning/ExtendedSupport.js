/**
 * This view displays the Extended Support list.
 */
Ext.define('sfw.view.scanning.ExtendedSupport', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiExtendedSupport',
    itemId: 'extendedSupportGrid',

    requires: [
        'sfw.store.scanning.ExtendedSupport'
    ],

    stateful: true,
    stateId: 'sfw_csiExtendedSupport_grid',

    title: 'Extended Support',
    border: 1,
    cls: 'shadow',

    reference: "extendedsupport",

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data found.'
    },

    controller:'extendedSupportController',

    store: {
        type: 'extendedsupport'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'textfield',
            emptyText: 'Search by product name....',
            itemId: 'product_name',
            listeners: {
                specialkey: 'reloadExtendedSupportGridOnEnter'
            }

        }, {
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler: 'reloadExtendedSupportGrid'

        }, {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }
        ]
    }],

    columns: [
        { text: 'Product Name', dataIndex: 'os_soft_name', flex: 1, align: 'left' },
        { text: 'ESU Date', dataIndex: 'esu_date', flex: 1, align: 'left', renderer:function(esuDate){
                var esuDate = sfw.Util.dateCreate(esuDate);
                var dateFormat = 'M j, Y';
                var output = Ext.util.Format.date( esuDate, dateFormat );
                return output;
            } },
        { text: 'Site', dataIndex: 'name', flex: 1, align: 'left',renderer: function (site,metadata) {
                if (!site) {
                    return '-';
                }

                metadata.tdAttr = 'data-qtip="' + site + '"';
                return site;
            } },
        { text: 'Host', dataIndex: 'host', flex: 1, align: 'left',renderer: function (host,metadata) {
                if (!host) {
                    return '-';
                }

                metadata.tdAttr = 'data-qtip="' + host + '"';
                return host;
            } },
        { text: 'Date Created/Modified', dataIndex: 'created_at', flex: 1, align: 'left', renderer:sfw.Default.gridRenderUTCDateInLocaltime }
    ],

    listeners:{
        itemcontextmenu: function (grid, record, item, index, e) {
            if(!sfw.util.Auth.LoginDetails.isReadOnly && sfw.util.Auth.LoginDetails.isPartitionAdmin)  {
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    controller: 'extendedSupportController',
                    plain: true,
                    items: [{
                        text: 'Edit Extended Support',
                        listeners: {
                            click: {fn: sfw.Default.addEditExtendedSupport, extra: record}
                        }
                    },{
                        text: 'Delete Extended Support',
                        listeners: {
                            click: {fn: sfw.Default.deleteExtendedSupport, extra: record}
                        }
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
            }
        }
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{getextendedsupportlist}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Extended Support {0} - {1} of {2}',
        emptyMsg: "No data found."
    }
});


