Ext.define('sfw.view.scanning.AddBlockListRuleWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.addblocklistrule',
    width: 500,
    height: 300,
    modal: true,
    title: '',
    layout: 'fit',
    //padding: '10px 10px 10px',
    itemId: 'addblocklist',

    controller: 'scanpaths',

    items: [{
        xtype: 'form',
        bodyPadding: 10,
        layout: "form",
        items: [{
            xtype: 'label',
            itemId:'savingType',
            text:'',
            hidden:true,
            },{
            xtype: 'label',
            itemId:'notFromScanPathGrid',
            text:'',
            hidden:true,
            },{
            xtype: 'textfield',
            //width: 500,
            fieldLabel: 'Name',
            name: 'name',
            tabIndex: 1,
            allowBlank: false,
            itemId: 'scanlistname'
        }, {
            xtype: 'textfield',
            fieldLabel: '',
            tabIndex: 1,
            editable:false,
            emptyText: 'Note: Paths are case-insensitive',
            inputWrapCls:'',
            triggerWrapCls: '',
            fieldStyle: 'background:none'
        }, {
            xtype: 'textfield',
            //width: 500,
            fieldLabel: 'Path',
            name: 'path',
            tabIndex: 1,
            allowBlank: false,
            itemId: 'scanlistpath'
        }, {
            xtype: 'textfield',
            //width: 500,
            fieldLabel: 'Site (optional)',
            name: 'site',
            tabIndex: 1,
            allowBlank: true,
            itemId: 'scanlistsite'
        },{
            xtype: 'checkbox',
            boxLabel: 'Log blocked paths when found',
            itemId:'impactListCheck',
            name:'impactListCheck',
            handler: function(me) {
                if(!me.checked){
                    this.up('window').down("#previewList").setDisabled(true);
                }else{
                    this.up('window').down("#previewList").setDisabled(false);
                }
            }
        },{
            xtype: 'button',
            //,width:100
            style:"position:absolute;margin-left:88px",
            text: 'Preview Impact List',
            disabled: true,
            itemId: 'previewList',
            handler:'previewList'

        }],
        buttons: [
            {
                text: 'Save',
                itemId: 'scanPathsSaveButton',
                formBind: true,
                ui: 'primary',
                tooltip: {
                    title: 'Submit details',
                    text: 'Submit the path details.'
                },
                handler: "addBlockListRule"
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]
});