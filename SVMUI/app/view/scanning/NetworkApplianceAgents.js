/* 
    Todo ->
    Search button handler logic
    Integration of grid with API
*/

var columns = [
    {text: "Host", dataIndex: "host", flex: 3, sortable: true},
    {
        text: "Site",
        width: 130,
        dataIndex: "group_name",
        align: "left",
        sortable: true
        // Use a special rendered for the Sites so that we can add the AD Path as a tooltip
        // in case AD is enabled. This makes it easier to see what sites/groups are selected
        /*        renderer: function( value, metaData, record, rowIndex, colIndex, store ) {
               return defaults.renderADSiteName( value, record.get( 'ad_dn' ) );
           } */
    },
    {
        text: "Simultaneous Inspections",
        flex: 1,
        width: 160,
        dataIndex: "max_threads",
        align: "right",
        sortable: true
    },
    //renderer: defaults.gridRenderLastCheckInDate,
    {
        text: "Last Agent Check-In",
        flex: 1,
        renderer: sfw.util.Default.gridRenderLastCheckInDate,
        width: 130,
        dataIndex: "last_check_in",
        align: "right",
        sortable: true
    },
    {
        text: "Agent Version",
        flex: 1,
        width: 130,
        dataIndex: "agent_version",
        align: "right",
        sortable: true
    },
    {
        text: "Network Appliance",
        flex: 1,
        width: 130,
        dataIndex: "network_appliance",
        align: "right",
        hidden: true,
        sortable: true
    }
];

Ext.define("sfw.view.scanning.NetworkApplianceAgents", {
    extend: "Ext.grid.Panel",
    xtype: "sfw.csiNetworkApplianceAgents",
    id: "networkapplianceagents_grid",
    requires: [
        "sfw.model.scanning.NetworkApplianceAgents",
        "sfw.store.scanning.NetworkApplianceAgents",
        "sfw.view.scanning.NetworkApplianceAgentsController",
        "sfw.util.Util"
    ],
    title: 'Network Appliance Agents',
    border: 1,
    cls: 'shadow',
    store: {
        type: "networkapplianceagents"
    },

    viewConfig: {
        deferEmptyText: false,
        emptyText: 'No data.'
    },

    controller: "scanning-networkapplianceagents",
    viewModel: {
        type: "scanning-networkapplianceagents"
    },
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    dockedItems: [
        {
            xtype: "toolbar",
            dock: "top",
            items: [
                {
                    xtype: "textfield",
                    id: "network_appliances_search",
                    emptyText: 'Search for host name',
                    listeners: {
                        specialkey: 'handleSpecialKeysNetworkAppliance'
                    }
                },
                {
                    xtype: "button",
                    text: "Search",
                    ui: 'primary',
                    handler: 'reloadNetworkApplianceAgentsGrid'
                },
                {
                    xtype: "tbfill",
                    flex: 1
                },
                {
                    xtype: "exportButton"
                }
            ]
        }
    ],

    columns: columns,

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var agent_id = record.get("nsi_device_id");
            var software_inspector_id = record.get("software_inspector_id");
            var host = record.get("host");
            var site = record.get("group_name");
            var contextMenu = Ext.create("Ext.menu.Menu", {
                controller: "scanning-networkapplianceagents",
                width: 165,
                plain: true,
                items: [
                    {
                        text: "Edit Configuration",
                        listeners: {
                            afterRender: function () {
                            },
                            click: {fn: 'editHostConfig', extra: record}
                        }
                    },
                    {
                        text: "Edit Site Configuration",
                        //,disabled: LoginDetails.isReadOnly
                        listeners: {
                            afterRender: function () {
                            },
                            click: {fn: 'editSiteConfig', extra: record}
                        }

                    },
                    "-",
                    {
                        text: 'Remove',
                        listeners: {
                            click: {fn: 'removeAgent', extra: record}
                        }
                        //,disabled: LoginDetails.isReadOnly
                    }
                ]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'doubleClickHandler'
    },

    bbar: {
        xtype: "pagingtoolbar",
        bind: {
            store: "{networkapplianceagents}"
        },
        displayInfo: true,
        pageSize: 30,
        displayMsg: "Displaying agents {0} - {1} of {2}",
        emptyMsg: "No agents found"
    }
});