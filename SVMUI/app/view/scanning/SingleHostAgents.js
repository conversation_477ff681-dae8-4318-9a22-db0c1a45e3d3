Ext.define('sfw.view.scanning.SingleHostAgents', {
	extend: 'Ext.panel.Panel',
	xtype: 'sfw.csiAgentManagement',
	//id: 'singlehost',

	requires: [
		'sfw.view.scanning.SingleHostAgentsController',
		'sfw.view.scanning.SingleHostAgentsModel',
		'sfw.store.scanning.SingleHostAgents',
		'sfw.Default'
	],

	title: 'Single Host Agents',
	border: 1,
	cls: 'shadow',

	controller: 'singlehostagentscontroller',

	viewModel: {
		data: {
			stats_error : 0,
			stats_warning : 0,
			stats_ok : 0,
			stats_total : 0,
			configtext : 'Configure details regarding the inspection conducted on the hosts running the Agent. The changes will automatically be updated in the database and applied to the Agents the next time they check-in with Software Vulnerability Manager.<br><br>'
		}
	},

	layout: {
		type: 'vbox',
		align: 'stretch'
	},


	items: [
		{
			xtype: 'panel',
			ui: 'workbench',
			title: 'Agent Status Overview',
			flex: 1,
			margin: '0 0 10 0',
			bind: {
				html: "<table cellspacing=\'0\' cellpadding=\'0\' style=\'margin-left: auto; margin-right: auto;\' align=\'center\'><table>" +
					"<tr>" +
					"<td class='AgentEventTableHeading' width='10%'>&nbsp;</td>" +
					"<td class='AgentEventTableHeading AgentEvenTableCellAlignRight' style='padding-right: 10px;' width='20%'><b>Agents in your partition</b></td>" +
					"<td class='AgentEventTableHeading' width='80%'><b>Recommendation</b></td>" +
					"</tr>" +
					"<tr>" +
					"<td height='3' colspan='3'></td>" +
					"</tr>" +
					"<tr>" +
					"<td class='AgentEventTableHeading' width='10%'>&nbsp;</td>" +
					"<td class='AgentEventTableHeading AgentEvenTableCellAlignRight' style='padding-right: 10px;' width='10%' id='stats_error'>{stats_error}</td>" +
					"<td class='AgentEventTableHeading' width='80%'><span style='width: 15px; height: 10px; background: " + sfw.Default.returnStatusColor(1) + ";'></span><b>Error:</b> Something is wrong with the Agent, a new scan is needed, <b>or</b> a newer agent must be installed</td>" +
					"</tr>" +
					"<td height='3' colspan='3'></td>" +
					"</tr>" +
					"<tr>" +
					"<td class='AgentEventTableHeading' width='10%'>&nbsp;</td>" +
					"<td class='AgentEventTableHeading AgentEvenTableCellAlignRight' style='padding-right: 10px;' width='10%' id='" + this.id + "stats_warning'> {stats_warning} </td>" +
					"<td class='AgentEventTableHeading' width='80%'><span style='width: 15px; height: 10px; background: " + sfw.Default.returnStatusColor(2) + ";'></span><b>Warning:</b> Something may be wrong with the Agent, a new scan is suggested, <b>or</b> a newer agent should be installed</td>" +
					"</tr>" +
					"<td height='3' colspan='3'></td>" +
					"</tr>" +
					"<tr>" +
					"<td class='AgentEventTableHeading' width='10%'>&nbsp;</td>" +
					"<td class='AgentEventTableHeading AgentEvenTableCellAlignRight' style='padding-right: 10px;' width='10%' id='" + this.id + "stats_ok'>{stats_ok}</td>" +
					"<td class='AgentEventTableHeading' width='80%'><span style='width: 15px; height: 10px; background: " + sfw.Default.returnStatusColor(3) + ";'></span><b>OK: </b> Everything appears to function correctly</td>" +
					"</tr>" +
					"<td height='3' colspan='3'></td>" +
					"</tr>" +
					"<tr>" +
					"<td class='AgentEventTableHeading' width='10%'><b>Total</b></td>" +
					"<td class='AgentEventTableHeading AgentEvenTableCellAlignRight' style='padding-left: 5px;' width='10%' id='" + this.id + "stats_ok'> {stats_total}</td>" +
					"<td class='AgentEventTableHeading' width='80%'><span style='width: 15px; height: 10px;'></span><b>Agents</b></td>" +
					"</tr>" +
					"</table>"
			}

		},
		{
			title: 'Agents for this account',
			xtype: 'gridpanel',
			id : 'singlehost_grid',
			ui: 'workbench',
			flex: 3,
			store: {
				type: 'singlehost'
			},
			selModel: {
				mode: 'MULTI'
			},
			bind: {
				selection: '{selectedHost}'
			},
			viewConfig : {
                deferEmptyText: false,
                emptyText: 'No data'
            },
			dockedItems: [{
				xtype: 'toolbar',
				dock: 'top',
				items: [
					{
						xtype: 'button',
						text: 'Scan Selected Host',
						itemId : 'scan_host_button',
						ui : 'primary',
						disabled :true,
						handler: "scanNowButton"
					},
					{xtype: 'tbspacer', width: 8},
					{
						xtype: 'textfield',
						emptyText: 'Search for host name....',
						id: 'agents_search',
						itemId: 'agents_search',
						listeners: {
							specialkey: 'handleSpecialKeysSingleHost'
						}
					},
					{
						xtype: 'button',
						ui : 'primary',
						text: 'Search',
						handler: 'reloadSingleHostGrid'
					} ,{
						xtype: 'tbfill',
						flex: 1
					}, {
						xtype: 'exportButton',
					}
				]
			}],

			columns: [
				{text: 'Host', dataIndex: 'host', flex: 3},
				{text: 'Site', dataIndex: 'group_name', flex: 1},
				{
					text: 'Platform', dataIndex: 'software_inspector_id', flex: 1, renderer: function (value) {
						value = parseInt(value, 10);
						var output;
						switch (value) {
							case 11:
								output = 'MacOs';
								break;
							case 41:
								output = 'Red Hat Linux';
								break;
							default:
								output = 'Windows';
								break;
						}
						return output;
					}
				},
				{
					text: 'Last Agent Check-In',
					dataIndex: 'last_check_in',
					flex: 1,
					renderer: sfw.Default.gridRenderLastCheckInDate
				},
				{
					text: 'Last Scan',
					dataIndex: 'updated',
					flex: 1,
					renderer: sfw.Default.gridRenderLastScanDate
				},
				{text: 'Next Scan', dataIndex: 'next_inspection', flex: 1, renderer: sfw.Default.gridRenderPastDate},
				{text: 'Agent Version', dataIndex: 'agent_version', flex: 1, align: 'right', renderer: sfw.Default.gridRenderAgentVersion}],

			listeners: {
				beforerender: {
					fn: 'onBeforeRender',
				},
				itemcontextmenu: function (grid, record, item, index, e) {
					var agent_id = record.get('nsi_device_id');
					var selected = grid.getSelectionModel().getSelected();
					var software_inspector_id = record.get('software_inspector_id');
					var host = record.get('host');
					var contextMenu = Ext.create('Ext.menu.Menu', {
						controller: 'singlehostagentscontroller',
						width: 165,
						plain: true,

						items: [{
							text: 'Edit Configuration',
							listeners: {
								click: {fn: 'editHostConfig', extra: record}
							}

						}, {
							text: 'Edit Site Configuration',
							listeners: {
								click: {fn: 'editSiteConfig', extra: record}
							}
						}, {
							text: 'Scan Now',
							listeners: {
								click: {fn: 'scanNow', extra: selected.items}
							}
						}, {
							text: 'Remove',
							listeners: {
								click: {fn: 'removeHost', extra: selected.items}
							}

						}]
					});
					e.stopEvent();
					contextMenu.showAt(e.getXY());
				},
				editAgentConfig:'editAgentConfig',
				itemdblclick: 'dblclickHandler'

			},

			layout: 'fit',
			margin: '2 2 0 0',
			bbar: {
				xtype: 'pagingtoolbar',
				bind: {
					store: "{singlehost}"
				},
				region: 'south',
				displayInfo: true,
				displayMsg: 'Displaying agents {0} - {1} of {2}',
				emptyMsg: "No agents found"
			}

		}
	]
});
