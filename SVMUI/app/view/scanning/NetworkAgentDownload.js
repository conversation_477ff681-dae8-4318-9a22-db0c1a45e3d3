/*
var CSIAVersion='#';
var CSIRequirementsLink = 'http://secunia.com/vulnerability_scanning/corporate/system_requirements/';
*/

Ext.define('sfw.view.scanning.NetworkAgentDownload',{
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiNetworkAgentDownload',
    bodyStyle: 'margin: 10px; padding: 5px 3px;',

    requires: [
        'sfw.view.scanning.NetworkAgentDownloadController',
        'sfw.view.scanning.NetworkAgentDownloadModel'
    ],

    controller: 'scanning-networkagentdownload',
    viewModel: {
        //type: 'scanning-networkagentdownload'

        formulas: {
            getNWAgentIsConsoleTrue:function(){
				if(sfw.isConsole){
					return false;
				}
				else{
					return true;
				}
			},

			getNWAgentIsConsoleFalse:function(){
			    if(sfw.isConsole){
					return true;
				}
				else{
					return false;
				}
			},
        }
    },

    frame: true,
    title: 'Download Network Agent',
    bodyPadding: 10,
    scrollable: true,

    items: [{
        xtype: 'container',
        itemId: 'remotelyId',
        tpl: [
            '<b>Recommended For</b>',
            '<p>Scanning one or more networks at scheduled intervals.</p>',
            '<b>Example</b>',
            '<p>Say you administrate three networks in Germany, United States, and United Kingdom - then you can install three Software Vulnerability Manager Agents in Network Appliance mode, one within each network.  Afterwards you will be able to scan all hosts in each of the three locations at scheduled intervals from your Software Vulnerability Manager interface by simply creating the appropriate Global Scan Groups in the Scan Now window.</p>',
            '<b>Result</b>',
            '<p class="p-ol">After installing the Agent in Network Appliance mode you can:</p><ol>',
            '<li class="list-padding">1. In the Network Appliance Agents window you can right-click and edit the configuration in order to control how frequently it should check for new scans to conduct.</li>',
            '<li class="list-padding">2. In the Network Appliance Groups window you can configure a scan group that will be <a href="#" onClick="sfw.util.Default.externalURL(\'{getCSIRequirementsLinkNW}\')">remotely</a> scanned by a Network Appliance Agent.</li></ol>',
            '<b>Instructions</b>',
            '<ol>',
            '<li>1. Download the Agent using the links shown below.</li>',
	        '<li>2. Transfer the Agent to the host where it should be installed.</li>',
            '<li>3. Login to the host and install the agent. For help, press F1.</li>',
            '<li>4. Signed agents obtain the tokens from external configuration file, csia.ini </li>',
            '</ol>'
        ]
    },{
        //TODO
        xtype: 'container',
        bind: { hidden: '{getNWAgentIsConsoleTrue}'},
        itemId: 'NWAgentIsConsoleTrueId',
        tpl : '<b>Network Agent Download</b>'+
        '<ul><li><a href="#" onClick="openAgentUrl();">Microsoft Windows</a> (ver. {getCSIAVersionNW}) <a href="#" onclick="sfw.util.Help.openPageId(\'csinetworkagent_windows\');">(?)</a></li></ul>'
    },{
        xtype: 'container',
        bind: { hidden: '{getNWAgentIsConsoleFalse}'},
        itemId: 'NWAgentIsConsoleFalseId',
        tpl: '<b>Network Agent Download</b><br/>'+
        '<ul><li><a href="{agentNetworkAgentUrl}">Microsoft Windows</a> (ver. {getCSIAVersionNW}) <a href="#" onclick="sfw.util.Help.openPageId(\'csinetworkagent_windows\');">(?)</a></li></ul>'
    }],

    listeners : {
        beforerender: 'onBeforeRender'
    }
    
});
