Ext.define('sfw.view.scanning.NetworkAgentDownloadController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.scanning-networkagentdownload',

    createAgentUrl: function( platform , type){

        var agentUrl = sfw.util.Globals.apiPath( sfw.util.Auth.LoginDetails.agentUID ) + 'action=download';
        if(typeof type != "undefined" && type === "signed"){
            agentUrl += "&type=signed";
        } else if(typeof type != "undefined" && type === "config") {
            agentUrl += "&type=config";
        }
        switch ( platform ) {
        case 1: // MAC platform - 64 bit
            agentUrl += '&platform=mac';
            break;
        case 2: // Red Hat 7 Linux platform
            agentUrl += '&platform=rhl7';
            break;
        case 3: // Red Hat Linux platform
            agentUrl += '&platform=rhl6';
            break;
        case 4: // MAC platform - 32 bit
            agentUrl += '&platform=mac_x86';
            break;
        case 5:
            agentUrl += '&platform=config';
            break;
        default: // Windows platform
            break;
        }
        //defaults.externalURL( agentUrl );
        return agentUrl;
    },

    openAgentUrl: function( platform ) {
	    sfw.util.Default.externalURL( createAgentUrl( platform ) );
    },

    onBeforeRender: function() {

        var me = this;
        url = me.createAgentUrl();
        var getCSIAVersion = sfw.util.Globals.CSIAVersion;
        var getCSIRequirementsLink = sfw.util.Globals.CSIRequirementsLink;
        var remotelyId = me.getView().down('#remotelyId');
        var NWAgentIsConsoleFalseId = me.getView().down('#NWAgentIsConsoleFalseId');
        remotelyId.setData({getCSIRequirementsLinkNW: getCSIRequirementsLink});
        NWAgentIsConsoleFalseId.setData({agentNetworkAgentUrl:url, getCSIAVersionNW:getCSIAVersion  });
    },

});
