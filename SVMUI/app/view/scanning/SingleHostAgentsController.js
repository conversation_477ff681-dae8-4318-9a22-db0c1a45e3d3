Ext.define('sfw.view.scanning.SingleHostAgentsController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.singlehostagentscontroller',

    reloadSingleHostGrid:function(){

        var singlehost = Ext.getStore('singlehost');
        var view = this.getView();

        var search_text = view.down('#agents_search').getValue();

        singlehost.getProxy().setExtraParams({
            'host': search_text
           });

        singlehost.load();
    },

    handleSpecialKeysSingleHost: function (field, e) {
        var me = this;
        if (e.getKey() == e.ENTER) {
            me.reloadSingleHostGrid();
        }
    },

    dblclickHandler: function(){
        var selected = Ext.getCmp('singlehost_grid').getSelectionModel().getSelected();
        var agent_id = selected.items[0].data.nsi_device_id;
        var software_inspector_id = selected.items[0].data.software_inspector_id;
        var host = selected.items[0].data.host;

        this.editHostConfigWindow(agent_id, software_inspector_id,host);
    },

    editHostConfig: function (event, target,options) {
        var agent_id = options.extra.data.nsi_device_id;
        var software_inspector_id = options.extra.data.software_inspector_id;
        var host = options.extra.data.host;

        this.editHostConfigWindow(agent_id, software_inspector_id,host);
    },

    editHostConfigWindow: function (agent_id, software_inspector_id,host) {
        var params = {
            groupId: ''
            , hostId: agent_id
            , storeType: 'single'
        };

        Ext.Ajax.request({
            url: 'action=ajaxapi_agent_management&which=readAgentDetails',
            method: 'GET',
            dataType: 'json',
            params: params,
            success: function (response, opts) {

                var response = Ext.decode(response.responseText);
                if (response.success === false) {
                    Ext.Msg.alert("Unexpected Error", "Unable to load agent configuration.");
                    // TODO: temp fix for order of function execution.
                    // Ext.getCmp( self.tmpId ).hide();
                } else {
                    var values = response.data.rows[0];

                    var inspectionType = values.inspection_type;
                    var windowsUpdate = values.windowsupdate;
                    var checkInInterval = values.check_in_interval;
                    var checkInIntervalTime = checkInInterval.substr(0, checkInInterval.length - 1);
                    var checkInIntervalUnit = checkInInterval.charAt(checkInInterval.length - 1);
                    var frequencyDays = values.frequency_days;
                    var frequencyTime = values.frequency_time;
                    var nextInspectionRow = values.next_inspection;
                    var nextInspectionDate;
                    var currentDate;

                    if (checkInIntervalUnit == 'M') {
                        checkInIntervalUnit = 'M:Minutes';
                    } else if (checkInIntervalUnit == 'H') {
                        checkInIntervalUnit = 'H:Hours';
                    } else {
                        checkInIntervalUnit = 'D:Days';
                    }

                    var editConfigWindow = Ext.create("sfw.view.scanning.SingleHostAgentConfiguration",
                        {
                            listeners: {
                                afterrender: function () {
                                    if (inspectionType == 1) {
                                        Ext.getCmp('scan_type_1').setValue(true);
                                        Ext.getCmp('scan_type_2').setValue(false);
                                        Ext.getCmp('scan_type_3').setValue(false);
                                    } else if (inspectionType == 2) {
                                        Ext.getCmp('scan_type_1').setValue(false);
                                        Ext.getCmp('scan_type_2').setValue(true);
                                        Ext.getCmp('scan_type_3').setValue(false);
                                    } else {
                                        Ext.getCmp('scan_type_1').setValue(false);
                                        Ext.getCmp('scan_type_2').setValue(false);
                                        Ext.getCmp('scan_type_3').setValue(true);
                                    }
                                    Ext.getCmp('hostID').setValue(agent_id);
                                    Ext.getCmp('check_microsoft_secure_update').setValue(windowsUpdate);
                                    Ext.getCmp('agent_check_frequency_time').setValue(checkInIntervalTime);
                                    Ext.getCmp('agent_check_frequency_unit').setValue(checkInIntervalUnit);
                                    Ext.getCmp('days_between_scans').setValue(frequencyDays);

                                    if (frequencyTime) {
                                        var frequencyTimesub = frequencyTime.substr(0, 5);
                                        var timeToDateAgent = sfw.Util.dateCreate(frequencyTimesub);
                                        var localTimeAgent = sfw.Default.dateConvertUTCToLocal(timeToDateAgent);
                                        frequencyTime = Ext.util.Format.date(localTimeAgent, sfw.Globals.hoursMinutesOutput)
                                    }
                                    Ext.getCmp('scan_time').setValue(frequencyTime);

                                    currentDate = sfw.Util.dateCreate();
                                    nextInspectionDate = sfw.Util.dateCreate(nextInspectionRow.substr(0, 16), true);
                                    Ext.getCmp('scan_host_now').setValue(0);
                                    var nextInspectionFormatted = Ext.util.Format.date(nextInspectionDate, sfw.Globals.dateLongHumanOutput);
                                    Ext.getCmp('scan_host_time').setValue(nextInspectionFormatted);

                                }
                            }
                        });
                    editConfigWindow.initComponent(agent_id, software_inspector_id);
                    editConfigWindow.show();
                    editConfigWindow.setTitle("Agent Configuration for Host: " + host);
                }
            },
            failure: function (response, opts) {

            }
        });



    },

    editSiteConfig: function (event, target,options) {


        var agent_id = options.extra.data.nsi_device_id;
        var software_inspector_id =  options.extra.data.software_inspector_id;
        var host = options.extra.data.host;
        var group_id = options.extra.data.group_id;
        var group_name = options.extra.data.group_name;


        editMsg = "Confirm that you wish to edit/overwrite the scheduling settings for the standalone agents within the specified site?";

        var params = {
            groupId: group_id
            ,hostId: ''
            ,storeType: 'single'
        };
        Ext.MessageBox.confirm( 'Confirm Edit : ' +group_name, editMsg, function( button ) {
            if ( button === 'yes') {
                Ext.Ajax.request({
                    url: 'action=ajaxapi_agent_management&which=readAgentDetails',
                    method: 'GET',
                    dataType: 'json',
                    params: params,
                    success: function (response, opts) {

                        var response = Ext.decode(response.responseText);
                        if (response.success === false) {
                            Ext.Msg.alert("Unexpected Error", "Unable to load agent configuration.");
                            // TODO: temp fix for order of function execution.
                            // Ext.getCmp( self.tmpId ).hide();
                        } else {
                            var values = response.data.rows[0];

                            var inspectionType = values.inspection_type;
                            var windowsUpdate = values.windowsupdate;
                            var checkInInterval = values.check_in_interval;
                            var checkInIntervalTime = checkInInterval.substr(0, checkInInterval.length - 1);
                            var checkInIntervalUnit = checkInInterval.charAt(checkInInterval.length - 1);
                            var frequencyDays = values.frequency_days;
                            var frequencyTime = values.frequency_time;
                            var nextInspectionRow = values.next_inspection;
                            var nextInspectionDate;
                            var currentDate;

                            if (checkInIntervalUnit == 'M') {
                                checkInIntervalUnit = 'M:Minutes';
                            } else if (checkInIntervalUnit == 'H') {
                                checkInIntervalUnit = 'H:Hours';
                            } else {
                                checkInIntervalUnit = 'D:Days';
                            }

                            var editConfigWindow = Ext.create("sfw.view.scanning.SingleHostAgentConfiguration",
                                {
                                    listeners: {
                                        afterrender: function () {
                                            if (inspectionType == 1) {
                                                Ext.getCmp('scan_type_1').setValue(true);
                                                Ext.getCmp('scan_type_2').setValue(false);
                                                Ext.getCmp('scan_type_3').setValue(false);
                                            } else if (inspectionType == 2) {
                                                Ext.getCmp('scan_type_1').setValue(false);
                                                Ext.getCmp('scan_type_2').setValue(true);
                                                Ext.getCmp('scan_type_3').setValue(false);
                                            } else {
                                                Ext.getCmp('scan_type_1').setValue(false);
                                                Ext.getCmp('scan_type_2').setValue(false);
                                                Ext.getCmp('scan_type_3').setValue(true);
                                            }
                                            Ext.getCmp('groupID').setValue(group_id);
                                            Ext.getCmp('check_microsoft_secure_update').setValue(windowsUpdate);
                                            Ext.getCmp('agent_check_frequency_time').setValue(checkInIntervalTime);
                                            Ext.getCmp('agent_check_frequency_unit').setValue(checkInIntervalUnit);
                                            Ext.getCmp('days_between_scans').setValue(frequencyDays);

                                            if (frequencyTime) {
                                                var frequencyTimesub = frequencyTime.substr(0, 5);
                                                var timeToDateAgent = sfw.Util.dateCreate(frequencyTimesub);
                                                var localTimeAgent = sfw.Default.dateConvertUTCToLocal(timeToDateAgent);
                                                frequencyTime = Ext.util.Format.date(localTimeAgent, sfw.Globals.hoursMinutesOutput)
                                            }
                                            Ext.getCmp('scan_time').setValue(frequencyTime);

                                            currentDate = sfw.Util.dateCreate();
                                            nextInspectionDate = sfw.Util.dateCreate(nextInspectionRow.substr(0, 16), true);
                                            Ext.getCmp('scan_host_now').setValue(0);
                                            var nextInspectionFormatted = Ext.util.Format.date(nextInspectionDate, sfw.Globals.dateLongHumanOutput);
                                            Ext.getCmp('scan_host_time').setValue(nextInspectionFormatted);

                                        }
                                    }
                                });
                            editConfigWindow.initComponent(agent_id, software_inspector_id);
                            editConfigWindow.show();
                            editConfigWindow.setTitle("Agent Configuration for site: " + group_name);
                        }
                    },
                    failure: function (response, opts) {

                    }
                });
            }
        });


    },

    initViewModel: function(vm) {
        vm.bind('{selectedHost}', 'onSelect', this);
    },

    onSelect: function(selection) {
        const me = this,
            grid = me.getView();
        var disabled = true;
        var selected = Ext.getCmp('singlehost_grid').getSelectionModel().getSelected();
        scanHostText = (selected.items.length > 1) ? 'Scan ' + selected.items.length + ' selected hosts' : 'Scan 1 selected host';
        if (selection) {
            disabled = false;
        }
        grid.down('#scan_host_button').setDisabled(disabled || sfw.util.Auth.LoginDetails.isReadOnly);
        grid.down('#scan_host_button').setText(scanHostText);
    },

    onBeforeRender : function() {
        var view = this.getView();
        Ext.Ajax.request({
            url: 'action=ajaxapi_agent_management&which=readHeading',
          //  method: 'POST',
            dataType: 'json',
            params: {

            },
            success: function (response, opts) {
                var eventCount = Ext.decode(response.responseText);
                eventCount = eventCount.data;
                eventCount['total'] = parseInt( eventCount['total'], 10 );

                eventCount[1] = parseInt( eventCount[1], 10 );
                var stats_error_count = "("+ (eventCount[1] ? Math.round( eventCount[1] / eventCount['total'] * 100 ):0)+"%)" +eventCount[1]  ;


                eventCount[2] = parseInt( eventCount[2], 10 );
                var stats_warning_count = "("+ ( eventCount[1] ? Math.round( eventCount[2] / eventCount['total'] * 100 ):0)+"%)" +eventCount[2];

                eventCount[3] = parseInt( eventCount[3], 10 );
                var stats_ok_count = "("+ ( eventCount[1] ? Math.round( eventCount[3] / eventCount['total'] * 100 ):0)+"%)" +eventCount[3];

                var stats_total_count =  ( eventCount['total'] ?  eventCount['total'] :0)

                view.getViewModel().set('stats_error', stats_error_count);
                view.getViewModel().set('stats_warning', stats_warning_count);
                view.getViewModel().set('stats_ok', stats_ok_count);
                view.getViewModel().set('stats_total', stats_total_count);

            },
            failure: function (response, opts) {

            }
        });

    },

    saveConfig : function() {

        var nextInspectionDate, update_vars = [];

        // Handle agent inspection type
        if( Ext.getCmp( 'scan_type_1' ).getValue() === true ) {
            update_vars['inspection_type'] = 1;
        } else if( Ext.getCmp(  'scan_type_2' ).getValue() === true ) {
            update_vars['inspection_type'] = 2;
        } else {
            update_vars['inspection_type'] = 3;
        }

        update_vars['windowsupdate'] = ( Ext.getCmp(  'check_microsoft_secure_update' ).getValue() ? 1 : 0 );

        // Handle agent check-in frequency
        var checkin_value = parseInt( Ext.getCmp( 'agent_check_frequency_time' ).getValue(), 10 );
        var checkin_type = Ext.getCmp( 'agent_check_frequency_unit' ).getValue();
        if ( !isNaN( checkin_value ) ) {
            update_vars['check_in_interval'] = ( checkin_value && checkin_type ? (checkin_value + checkin_type.charAt(0)) : '10M' );
        } else {
            update_vars['check_in_interval'] = '10M';
        }

        // Handle agent schedule
        var frequency_days = Ext.getCmp(  'days_between_scans' ).getValue();
        update_vars['frequency_days'] = frequency_days;
        var frequency_time = Ext.getCmp(  'scan_time' ).getValue();
        if ( frequency_time) {
            var frequencyTimeAgent = sfw.Util.dateCreate( frequency_time );
            var utcFrequencyTimeAgent  = sfw.Default.dateConvertLocalToUTC( frequencyTimeAgent );
            update_vars['frequency_time'] = Ext.util.Format.date(utcFrequencyTimeAgent, sfw.Globals.hoursMinutesOutput)
        }

        // Handle next agent scan
        var inspect_now = (Ext.getCmp( 'scan_host_now' ).getValue() ? 1 : 0);
        var next = Ext.getCmp( 'scan_host_time' ).getValue();
        var validDate = true;
        if ( inspect_now ) {
            update_vars['inspect_now'] = 1;
            update_vars['next_inspection'] = "datetime('now','-48 hours')";
        } else if ( next ) {
            try {
                nextInspectionDate = sfw.Util.dateCreate( next );
            } catch ( ex ) {
                validDate = false;
            }

            if ( validDate ) {
                nextInspectionDate = sfw.Default.dateConvertLocalToUTC( nextInspectionDate );
                update_vars['inspect_now'] = 0;
                update_vars['next_inspection'] = Ext.util.Format.date(nextInspectionDate, sfw.Globals.dateLongHumanInput)
            }
        }

        // Build POST data from update_vars
        var postData = "submit=1";
        for ( var i in update_vars ) {
            if( update_vars.hasOwnProperty( i ) ) {
                if ( i == "next_inspection" && inspect_now ) {
                    // Don't submit next_inspection if inspect_now was also set
                    continue;
                }
                postData += "&" + i + "=" + update_vars[i];
            }
        }

        if(Ext.getCmp('hostID').getValue()){
            var extraParams = 'host_id='+Ext.getCmp('hostID').getValue()
        }else if(Ext.getCmp('groupID').getValue()){
            var extraParams = 'group_id='+Ext.getCmp('groupID').getValue()
        }

        Ext.Ajax.request({
            url: 'action=ajaxapi_agent_management&'+extraParams
            ,method: "POST"
            ,params: postData
                ,dataType: 'json'
            ,success: function( data ) {
                var response = Ext.decode( data.responseText );
                if ( response.success !== true ) {
                   sfw.util.Debug.log( "Error saving Agent/Site configuration (1)." );
                    Ext.Msg.show({
                        title: "Error"
                        ,msg: "Unable to save the configuration."
                        ,buttons: Ext.Msg.OK
                        ,icon: Ext.MessageBox.ERROR
                    });
                }
            }
            ,failure: function() {
                sfw.util.Debug.log( "Error saving Agent/Site configuration (2)." );
                Ext.Msg.show({
                    title: "Error"
                    ,msg: "Saving configuration failed."
                    ,buttons: Ext.Msg.OK
                    ,icon: Ext.MessageBox.ERROR
                });
            }
        });
        Ext.getCmp('singlehost_grid').getStore().reload();
       var view = this.getView();
       view.destroy();

    },

    scanNow: function (event, target, options) {
        var host_id;
        for (var i=0; i<options.extra.length; ++i){
            host_id = options.extra[i].data.nsi_device_id;
            this.scanNowAPICall(host_id);
        }
    },

    scanNowAPICall: function(host_id){
        Ext.Ajax.request({
            url: 'action=ajaxapi_agent_management&scan_now_id='+host_id,
            method: 'GET',
            dataType: 'json',
            success: function (response) {

            },
            failure: function () {

            }
        });
    },

    removeHost: function (event, target, options) {

        var selectionArray = [];
        for (var i=0; i<options.extra.length; ++i){
            selectionArray.push(options.extra[i].data.nsi_device_id);
        }

        deleteMsg = "Confirm that the Software Vulnerability Manager Agent is no longer installed on the selected hosts and you therefore no longer want these hosts to be shown in this window?";

        selectionArray = selectionArray.map(Number);
        var delParams = {
            hosts: Ext.util.JSON.encode( selectionArray )
        };

        Ext.MessageBox.confirm( 'Confirm Deletion', deleteMsg, function( button ) {
            if ( button === 'yes') {
                Ext.Ajax.request({
                    url: 'action=ajaxapi_agent_management&which=deleteHosts',
                    method: 'POST',
                    dataType: 'json',
                    params: delParams,
                    success: function( response ) {
                        var response = Ext.util.JSON.decode( response.responseText );
                        if ( response.success === true ) {
                            var count = response.data;
                            Ext.Msg.show({
                                title: 'Success'
                                ,msg: 'Successfully removed ' + count +' agents.'
                                ,buttons: Ext.Msg.OK
                                ,icon: Ext.MessageBox.INFO
                            });
                            Ext.getCmp('singlehost_grid').getStore().reload();
                        } else {
                            sfw.util.Debug.log( 'Error while trying to remove agents.' );
                            Ext.Msg.show({
                                title: 'Error'
                                ,msg: 'Removing agents failed.'
                                ,buttons: Ext.Msg.OK
                                ,icon: Ext.MessageBox.ERROR
                            });
                        }
                    },
                    failure: function() {
                        Ext.Msg.alert( "Unexpected Error", "Unable to delete..." );
                    }
                });
            }
        });
    },


    scanNowButton: function () {
        var selected = Ext.getCmp('singlehost_grid').getSelectionModel().getSelected();
        var host_id;
        for (var i=0; i<selected.length; ++i){
            host_id = selected.items[i].data.nsi_device_id;
            this.scanNowAPICall(host_id);
        }
    },

});
