


if(sfw.util.SharedConstants.EDITION == sfw.util.SharedConstants.SERVER_EDITION){
	var macVersion = '7.6.1.7';
} else {
		var macVersion = '7.6.0.7';
}

Ext.define('sfw.view.scanning.DownloadLocalAgent',{
    extend: 'Ext.panel.Panel',
    xtype:'sfw.csiLocalAgentDownload',
    bodyStyle: 'margin: 10px; padding: 5px 3px;',
    title: 'Download Local Agent',
    scrollable: true,
    frame: true,
    bodyPadding: 10,
    requires: [
        'sfw.view.scanning.DownloadLocalAgentController',
        'sfw.view.scanning.DownloadLocalAgentModel'
    ],


    controller: 'scanning-downloadlocalagent',
    viewModel: {
        //type: 'scanning-downloadlocalagent',

        formulas:{
			getAgentIsConsoleTrue:function(){
				if(sfw.isConsole){
					return false;
				}
				else{
					return true;
				}
			},

			getAgentIsConsoleFalse:function(){
			    if(sfw.isConsole){
					return true;
				}
				else{
					return false;
				}
			},
			getoldAgent:function(){
				if(sfw.util.Auth.LoginDetails.oldagentdetection.success){
					return false;
				}
				else{
					return true;
				}
			},


		},
    },

    items:[{
			xtype: 'container',
			html: "<b>Recommended For</b>"+
            "<p>Laptops and hosts that can not be scanned remotely, e.g. hosts that are not always online.</p>"+
            "<b>Example</b>"+
            "<p>Install the Agent in Single Host mode on corporate laptops. Everytime the laptops connects to the Internet they will check-in with server to verify if a new scan should be done. After scanning, the results will automatically show up in the Results Database. Thus enabling you full control to scan and view results of hosts that are not always connected to your network.</p>"+
            "<b>Result</b>"+
            "<p>Hosts scanned in Single Host mode will show in the Results Database similar to all other scan result. When and how they are scanned can be remotely controlled and configured from the Agent Management window, where the hosts automatically appear after being setup with the agent.</p>"+
            "<b>Instructions</b>"+
            "<ol>"+
            "<li>1. Download the Agent using the links shown below.</li>"+
            "<li>2. Transfer the Agent to the host where it should be installed.</li>"+
            "<li>3. Login to the host and install the agent. For help, press F1.</li>"+
            "<li>4. Signed agents obtain the tokens from external configuration file, csia.ini </li>"+
            "</ol>"
    },{
        //TODO
        xtype: 'container',
        bind: { hidden: '{getAgentIsConsoleTrue}'},
        html: '<b>Download Agents with Token</b>'+
            '<ul>'+
            '<li><a href="#" onClick="openAgentUrl();">Microsoft Windows</a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
            '<li><a href="#" onClick="openAgentUrl(1);">macOS - 64bit </a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
            '<li><a href="#" onClick="openAgentUrl(4);">macOS - 32bit</a> (ver. ' + macVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
            '<li><a href="#" onClick="openAgentUrl(2);">Red Hat Linux 7.x/Red Hat Linux 8.x/Red Hat Linux 9.x</a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_redhat\');">(?)</a></li>'+
            '<li><a href="#" onClick="openAgentUrl(3);">Red Hat Linux 6.x</a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_redhat\');">(?)</a></li>'+
            '</ul>'
    },{
        xtype: 'container',
        bind: { hidden: '{getAgentIsConsoleFalse}'},
        itemId: 'withTokenAgentId',
        tpl: '<b>Download Agents with Token</b>'+
            '<ul>'+
            '<li><a href="{agentUrlwithTokenMW}">Microsoft Windows</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
            '<li><a href="{agentUrlwithTokenMO32}">macOS - 64bit</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
            '<li><a href="{agentUrlwithTokenMO64}">macOS - 32bit</a> (ver. ' + macVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
            '<li><a href="{agentUrlwithTokenRH7}">Red Hat Linux 7.x/Red Hat Linux 8.x/Red Hat Linux 9.x</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_redhat\');">(?)</a></li>'+
            '<li><a href="{agentUrlwithTokenRH6}">Red Hat Linux 6.x</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_redhat\');">(?)</a></li>'+
            '</ul>'
    },{
        //TODO
        xtype: 'container',
        bind: { hidden: '{getAgentIsConsoleTrue}'},
        html: '<b>Download Signed Agents without Token</b>'+
            '<ul>'+
            '<li><a href="#" onClick="openAgentUrl("", "signed");">Microsoft Windows</a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
            '<li><a href="#" onClick="openAgentUrl(1, "signed");">macOS- 64bit </a> (ver. ' + sfw.util.Globals.CSIAVersion + ') <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
            '<br/>'+
            '<li>Configuration file: <a href="#" onClick="openAgentUrl(5, "config");">csia_token.ini</a></li>'+
            '</ul>'
    },{
        xtype: 'container',
        itemId: 'withoutTokenAgentId',
        bind: { hidden: '{getAgentIsConsoleFalse}'},
       tpl: '<b>Download Signed Agents without Token</b>'+
           '<ul>'+
            '<li><a href="{agentUrlwithoutTokenMW}">Microsoft Windows</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_windows\');">(?)</a></li>'+
            '<li><a href="{agentUrlwithoutTokenMO}">macOS - 64bit</a> (ver. {getwithTokenCSIAVersion}) <a href="#" onclick="sfw.util.Help.openPageId(\'csiagent_macosx\');">(?)</a></li>'+
           '<br/>'+
            '<li>Configuration file: <a href="{agentUrlwithoutTokenCT}">csia_token.ini</a></li>'+
           '</ul>'
    },{
        xtype: 'component',
        html: '<b>Email Agent details</b>'+
        '<p> <a href="javascript:;">Email agent details</a>.</p>',
        listeners: {
            element: 'el',
            click:  'handleTokenMail'
        },

    },
    //TODO
/*    {
        xtype: 'container',
        bind: { hidden: '{getoldAgent}'},
        html: '<h1>Download Old Agents</h1>'+
        '<p> <a href="javascript:;">Download Previous Version Agent</a>.</p>',
        listeners: {
        element: 'el',
        click: function(){
                    var localagentold = Ext.create("sfw.view.scanning.LocalAgentOld",
                    {

                    });
                    localagentold.show();
        }
    }
    }*/
    ],

    listeners : {
        beforerender: 'onBeforeRender'
    }


});


