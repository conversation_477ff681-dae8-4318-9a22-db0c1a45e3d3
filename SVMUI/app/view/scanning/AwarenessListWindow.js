Ext.define('sfw.view.scanning.AwarenessListWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.impactlist',
    width: 700,
    height: 400,
    modal: true,
    title: 'Impact List',
    layout: 'fit',
    itemId: 'impactListWindow',

    viewModel: {
        stores: {
            impactListResults: {
                storeId: 'impactListStore',
                pageSize: '19',
                remoteSort: true,
                sorters: [{
                    property: "path",
                    direction: "ASC"
                }],

                proxy: {
                    type: 'ajax',
                    noCache: false,
                    url: 'action=ajaxapi_scan_paths&which=impact_list',
                    reader: {
                        type: 'json',
                        rootProperty: 'data.rows',
                        successProperty: 'success',
                        totalProperty: 'data.total'
                    }
                }
            }
        },
    },

    items:[{
        xtype:'grid',
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No Paths Found.'
        },
        columns: [
            { text: 'Path', dataIndex: 'path', flex: 1 }
        ],
        bind: {
            store: '{impactListResults}'
        },
        margin: '2 2 0 0',

        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: '{impactListResults}'
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying Paths {0} - {1} of {2}',
            emptyMsg: "No products found"
        }
    }]

});