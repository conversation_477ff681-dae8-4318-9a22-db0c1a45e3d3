Ext.define('sfw.view.scanning.DownloadLocalAgentController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.scanning-downloadlocalagent',

     createAgentUrl: function( platform , type){

        var agentUrl = sfw.util.Globals.apiPath( sfw.util.Auth.LoginDetails.agentUID ) + 'action=download';
        if(typeof type != "undefined" && type === "signed"){
            agentUrl += "&type=signed";
        } else if(typeof type != "undefined" && type === "config") {
            agentUrl += "&type=config";
        }
        switch ( platform ) {
        case 1: // MAC platform - 64 bit
            agentUrl += '&platform=mac';
            break;
        case 2: // Red Hat 7 Linux platform
            agentUrl += '&platform=rhl7';
            break;
        case 3: // Red Hat Linux platform
            agentUrl += '&platform=rhl6';
            break;
        case 4: // MAC platform - 32 bit
            agentUrl += '&platform=mac_x86';
            break;
        case 5:
            agentUrl += '&platform=config';
            break;
        default: // Windows platform
            break;
        }
        //defaults.externalURL( agentUrl );
        return agentUrl;
    },

    createAgentUrlPrevious: function( platform,version ) {

        var agentUrl = sfw.util.Globals.apiPath( sfw.util.Auth.LoginDetails.agentUID ) + 'action=download&agent_uid=' + sfw.util.Auth.LoginDetails.agentUID+'&version='+version;
        switch ( platform ) {
            case 1: // MAC platform
                agentUrl += '&platform=mac';
                break;
            case 2: // Red Hat 7 Linux platform
                agentUrl += '&platform=rhl7';
                break;
            case 3: // Red Hat Linux platform
                agentUrl += '&platform=rhl6';
                break;
            default: // Windows platform
                break;
        }

        //defaults.externalURL( agentUrl );
        return agentUrl;
    },


    onBeforeRender: function() {

        var me = this;
        urlwithTokenMW = me.createAgentUrl();
        urlwithTokenMO32= me.createAgentUrl(1);
        urlwithTokenMO64 = me.createAgentUrl(4);
        urlwithTokenRH7 = me.createAgentUrl(2);
        urlwithTokenRH6 = me.createAgentUrl(3);
        urlwithoutTokenMW = me.createAgentUrl('', 'signed');
        urlwithoutTokenMO = me.createAgentUrl(1, 'signed');
        urlwithoutTokenCT = me.createAgentUrl(5, 'config');

        var getCSIAVersion = sfw.util.Globals.CSIAVersion;

        var withTokenAgentId = me.getView().down('#withTokenAgentId');
        var withoutTokenAgentId = me.getView().down('#withoutTokenAgentId');

        withTokenAgentId.setData({agentUrlwithTokenMW:urlwithTokenMW, agentUrlwithTokenMO32: urlwithTokenMO32, agentUrlwithTokenMO64:urlwithTokenMO64, agentUrlwithTokenRH7: urlwithTokenRH7, agentUrlwithTokenRH6: urlwithTokenRH6, getwithTokenCSIAVersion: getCSIAVersion });
        withoutTokenAgentId.setData({agentUrlwithoutTokenMW: urlwithoutTokenMW, agentUrlwithoutTokenMO: urlwithoutTokenMO, agentUrlwithoutTokenCT: urlwithoutTokenCT, getwithTokenCSIAVersion: getCSIAVersion });
    },

    handleTokenMail: function ( ) {

    Ext.Ajax.request({
        url: sfw.util.Globals.apiPath() + 'action=download&token_download=yes'
        ,method: 'GET'
        ,success: function( response ) {
			try {
				var response = Ext.util.JSON.decode( response.responseText );
				var status = response.flag;
				var email = response.recipient_email;
				switch( status ) {
				case 1:
					Ext.Msg.alert( "Success", "Email sent to " + email + " successfully" );
					break;
				case 2:
					Ext.Msg.alert( "Error", "No email available. Please contact your administrator." );
					break;
				default:
					Ext.Msg.alert( "Unexpected Error", "Unable to send email" );
					break;
				}
			} catch ( ex ) {
				// Silently ignore the error
			}
		}
		,failure: function() {
			Ext.Msg.alert( "Unexpected Error", "Unable to send email..." );
		}
	});

    },

    openAgentUrl: function( platform ) {
	sfw.util.Default.externalURL( createAgentUrl( platform ) );
    },


    openAgentUrlPrevious : function( platform,version ) {
	sfw.util.Default.externalURL( createAgentUrlPrevious( platform,version ) );
    },


});