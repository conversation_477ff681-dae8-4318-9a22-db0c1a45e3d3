Ext.define('sfw.view.scanning.CustomScanRulesController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.customscanrules',

    NewCustomScanRulesWindow: function () {
        if (sfw.isBrowser) {
            sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE);
        }else{
            var NewCustomScanRulesWindow = Ext.create('sfw.view.scanning.NewCustomScanRulesWindow', {
                listeners: {
                    afterrender: function () {
                        NewCustomScanRulesWindow.down("title").setHtml("Custom Scan Rule");

                    }
                }
            });
            NewCustomScanRulesWindow.show();
        }
    },

    onEditRule: function (record) {
        if (sfw.isBrowser) {
            sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE);
        }
        //Todo - NewCustomScanRuleWindow need to be call with record data
    },

    onDeleteRule: function (event, target, options) {
        if (sfw.isBrowser) {
            sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE);
        }
        //Todo- Delete API need to be called
    }
});
