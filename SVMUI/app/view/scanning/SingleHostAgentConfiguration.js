Ext.define('sfw.view.scanning.SingleHostAgentConfiguration', {
    extend: "Ext.window.Window",
    width: 650,
    height: 650,
    bodyPadding: 5,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    controller: 'singlehostagentscontroller',
    viewModel: {
        data: {
            configtext : 'Configure details regarding the inspection conducted on the hosts running the Agent. The changes will automatically be updated in the database and applied to the Agents the next time they check-in with Software Vulnerability Manager.<br><br>'
        },
    },

    initComponent: function (agent_id, software_inspector_id) {

        this.disableInspectionType = true;
        this.createScanIntervalList = function () {
            this.scanIntervalList = [];
            var index = 0;
            for (var i = 0; i < 30; i++) {
                index = i + 1;
                this.scanIntervalList[i] = [index.toString()];
            }
        };
        this.createScanIntervalList();
        if (typeof software_inspector_id !== 'undefined') {

            software_inspector_id = parseInt(software_inspector_id, 10);

            switch (software_inspector_id) {
                case 21: // csi
                    this.disableInspectionType = false;
                case 31: // SCCM
                case 100: // not scanned
                case 1: // psi
                    this.disableInspectionType = false;
                    break;

                case 11: // mac
                case 41: // Red Hat Linux
                    this.disableInspectionType = true;
                    break;

                default:
                    this.disableInspectionType = true;
                    break;
            }

        }else{
            return;
        }

        Ext.applyIf(this, {
            items: [{
                xtype: 'form',
                reference: "scheduleExportDetailForm",
                monitorValid: true,
                labelWidth: 75,
                autoHeight: true,
                //frame: true,
                bodyStyle: 'padding: 5px 5px 0;',
                border: false,
                defaults: {width: 600},
                defaultType: 'textfield',
                labelAlign: 'left',


                items: [{
                    bind: {
                        html: '{configtext}'

                    }
                },{
                    xtype:'textfield',
                    name:'hostID',
                    hidden:true,
                    id:'hostID'
                },{
                    xtype:'textfield',
                    name:'groupID',
                    hidden:true,
                    id:'groupID'
                }, {
                        xtype: 'fieldset'
                        , title: 'Inspection Type'
                        , items: [{
                            xtype: 'radiogroup'
                            , vertical: true
                            , columns: 1
                            , id: 'scan_type_group'
                            , items: [{
                                boxLabel: 'Type 1: Default Paths (Fast)'
                                , name: 'scan_type'
                                , id: 'scan_type_1'
                                , inputValue: 1
                                , disabled: this.disableInspectionType
                            }, {
                                boxLabel: 'Type 2: All Paths (Thorough)'
                                , name: 'scan_type'
                                , id: 'scan_type_2'
                                , inputValue: 2
                                , checked: true
                                , disabled: this.disableInspectionType
                            }, {
                                boxLabel: 'Type 3: Inspect all .dll, .exe, and .ocx files (Slow)'
                                , name: 'scan_type'
                                , id: 'scan_type_3'
                                , inputValue: 3
                                , disabled: this.disableInspectionType
                            }, {
                                xtype: 'checkbox'
                                , name: 'microsoft_secure_update_checkbox'
                                , boxLabel: 'Check for missing Microsoft security updates'
                                , id: 'check_microsoft_secure_update'
                                , checked: true
                                , disabled: this.disableInspectionType
                            }, {
                                xtype: 'label'
                                ,
                                html: 'Check for missing Microsoft security updates has been globally enabled by the partition administrator'
                                ,
                                id: 'check_microsoft_secure_update_enabled_warning'
                                ,
                                hidden: true
                            }, {
                                xtype: 'label'
                                ,
                                html: 'Check for missing Microsoft security updates has been globally disabled by the partition administrator'
                                ,
                                id:  'check_microsoft_secure_update_disabled_warning'
                                ,
                                hidden: true
                            }]
                        }]
                    },
                    {
                        xtype: 'fieldset'
                        , title: 'Agent Check-In Frequency'
                        , items: [{
                            xtype: 'numberfield'
                            , allowBlank: false
                            , allowDecimals: false
                            , name: 'agent_check_frequency_field'
                            , id: 'agent_check_frequency_time'
                            , width: 200
                            , labelAlign: 'left'
                            , fieldLabel: 'Agent check-in frequency'
                            , enableKeyEvents: true
                            , value: 25
                            , minValue: 1
                            , maxValue: 100
                            , autoStripChars: true
                        }, {
                            xtype: 'combo'
                            , name: 'agent_check_frequency_combo'
                            , id: 'agent_check_frequency_unit'
                            , fieldLabel: 'Time unit'
                            , store: new Ext.data.SimpleStore({
                                idIndex: 0
                                , fields: ['time_unit']
                                , data: [['M:Minutes'], ['H:Hours'], ['D:Days']]
                            })
                            , valueField: 'time_unit'
                            , displayField: 'time_unit'
                            , selectOnFocus: false
                            , mode: 'local'
                            , typeAhead: false
                            , editable: false
                            , triggerAction: 'all'
                            , listeners: {
                                // select: this.setAgentCheckInFrequencyTimeUnit.createDelegate(this)
                            }
                        }]
                    },
                    {
                        xtype: 'fieldset'
                        , title: 'Schedule Scans'
                        , items: [{
                            xtype: 'combo'
                            , name: 'days_between_scans_combo'
                            , id: 'days_between_scans'
                            , fieldLabel: 'Days between scans'
                            , store: new Ext.data.SimpleStore({
                                idIndex: 0
                                , fields: ['days']
                                , data: this.scanIntervalList
                            })
                            , valueField: 'days'
                            , displayField: 'days'
                            , selectOnFocus: false
                            , mode: 'local'
                            , typeAhead: false
                            , editable: false
                            , triggerAction: 'all'
                            , listeners: {}
                        }, {
                            xtype: 'timefield'
                            , name: 'scan_time_field'
                            , id: 'scan_time'
                            , width: 200
                            , labelAlign: 'left'
                            , fieldLabel: 'Start the scan after'
                            , format: 'H:i'
                            , increment: 30
                            , enableKeyEvents: true
                        }]
                    }, {
                        xtype: 'fieldset'
                        , title: 'Schedule Next Scan'
                        , items: [{
                            xtype: 'checkbox'
                            , name: 'scan_host_now_checkbox'
                            , boxLabel: 'Scan host as soon as possible'
                            , id: 'scan_host_now'
                            , listeners: {
                                // check: this.checkScanAsSoonAsPossible.createDelegate(this)
                            }
                        }, {
                            xtype: 'textfield'
                            , name: 'scan_host_time_field'
                            , id: 'scan_host_time'
                            , width: 300
                            , labelAlign: 'left'
                            , fieldLabel: 'Or insert date and time'
                            , emptyText: 'YYYY-MM-DD HH:MM'
                            , enableKeyEvents: true
                        }]
                    },
                ],

                buttons: [
                    {
                        text: 'Save Configuration',
                        formBind: true,
                        tooltip: {
                            title: 'Save the configuration of the agent that you have entered in the form above',
                            text: 'Save configuration'
                        },
                        handler: "saveConfig"
                    }, {
                        text: 'Cancel',
                        tabIndex: 8,
                        tooltip: {
                            title: 'Close window'
                            , text: 'Cancel all changes and close the window.'
                        },
                        handler: function () {
                            this.up('window').destroy();
                        }
                    }
                ]
            }]
        });

        this.callParent();
    }
});