/**
 * This view displays the completed scans list.
 */
Ext.define('sfw.view.scanning.CompletedScans', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiCompletedScans',

    requires: [
        'sfw.store.scanning.CompletedScans',
        'sfw.view.scanning.CompletedScanController',
        'Ext.chart.theme.DefaultGradients',
        'Ext.chart.theme.Green'
    ],

    stateful: true,
    stateId: 'sfw_csiCompletedScans_grid',

    controller:'completedScanController',

    title: 'Completed Scans',
    border: 1,
    cls: 'shadow',

    reference: "completedscans",

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data available for the selected date range and/or search criteria.'
    },

    store: {
        type: 'completedscans'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'datefield',
            name: 'from_date',
            format:sfw.Globals.dateShortInput,
            value: sfw.Util.dateCreateTodayOffset(-14),
            itemId: 'from_date',
            listeners: {
                change: 'reloadCompletedScanGrid'
            }
        }, {
            xtype: 'datefield',
            name: 'to_date',
            itemId: 'to_date',
            format:sfw.Globals.dateShortInput,
            value: sfw.Util.dateCreateTodayOffset(),
            listeners: {
                change: 'reloadCompletedScanGrid'
            }
        }, {
            xtype: 'textfield',
            emptyText: 'Search for host name....',
            itemId: 'host_name'

        }, {
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler: 'reloadCompletedScanGrid'

        }, {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }
        ]
    }],


    columns: [
        { text: 'Time', dataIndex: 'status_date', stateful: true, stateId: 'col_status_date', renderer: sfw.Default.gridRenderUTCDateInLocaltime, flex: 2 },
        { text: 'Host', dataIndex: 'host', stateful: true, stateId: 'col_host', width: 300 },
        {
            text: 'Scan Status', dataIndex: 'short_msg', flex: 1, renderer: function (sInput, meta, record) {
                //console.log(sInput);
                meta.tdCls = 'StringSuccess';
                if (sInput.indexOf('Fail') === 0) {
                    meta.tdCls = 'StringError';
                    // If the failed Scan was from SCCM then indicate it
                    if (31 == record.get('software_inspector_id')) { // 31 = SCCM Host
                        sInput += ' (System Center Host)';
                    }
                } else if (sInput == 'Partial' || sInput == 'Partial Success') {
                    meta.tdCls = 'StringWarning';
                }
                return '<span class="' + meta.tdCls + '" qtip="' + record.get('long_msg').replace(/\\n/g, '<br>') + '">' + sInput + '</span>';

            }
        },
        {
            text: 'Result Exists', dataIndex: 'results_exist', flex: 1, renderer: function (val, meta, record) {
                if (val == '1') {
                    val = 'Yes';
                    meta.tdCls = 'StringSuccess';
                    return val;
                }
                else if (val == '0') {
                    val = 'No';
                    meta.tdCls = 'StringError';
                    return val;
                }
            }
        },
        { text: 'Scan Type', dataIndex: 'scan_type', flex: 1, align: 'right', renderer:function( input ) {
                var type = parseInt( input, 10 );
                if ( type >= 0 && type < 4 ) {
                    return type;
                } else {
                    return '-';
                }
            } },
        { text: 'Zombie Files', dataIndex: 'no_zombie', flex: 1, align: 'right', renderer: sfw.Default.zombieCountRendererText },
        { text: 'Insecure', dataIndex: 'no_insecure', flex: 1, align: 'right' },
        { text: 'End-of-Life', dataIndex: 'no_eol', flex: 1, align: 'right' },
        { text: 'Secure', dataIndex: 'no_patched', flex: 1, align: 'right' },
        { text: 'Total', dataIndex: 'no_total', flex: 1, align: 'right' }
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            if((typeof sfw.util.Auth.LoginDetails.account.roleIds[ sfw.ROLE_RESULTS ] !== "undefined") && (parseInt(record.data.results_exist, 10)) ) {
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    controller: 'completedScanController',
                    //width: 100,
                    plain: true,
                    items: [{
                        text: 'View Scan Result',
                        listeners: {
                            click: {fn: sfw.Default.scanResultOverview, extra: record}
                        }
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
            }
            else if (typeof sfw.util.Auth.LoginDetails.account.roleIds[ sfw.ROLE_RESULTS ] === "undefined"){
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    width: 150,
                    plain: true,
                    items: [{
                        text: 'No Access To Results',
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                contextMenu.setDisabled(true);
            }
            else{
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    plain: true,
                    items: [{
                        text: 'No Result Exists',
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                contextMenu.setDisabled(true);
            }
        },
        itemdblclick:sfw.Default.scanResultOverviewdblcick,
        afterrender:'reloadCompletedScanGrid',
        beforerender:'hideColumnsBasedonRole'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{getcompletescanresult}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Completed Scans {0} - {1} of {2}',
        emptyMsg: "No data found."
    }
});


