Ext.define('sfw.view.scanning.InventoryResultWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.inventoryresult',
    width: 300,
    height: 300,
    modal: true,
    title: 'Inventory Records Details',
    layout: 'fit',
    id: 'sfwInventoryId',
    controller : 'inventoryController',
    viewModel: {
        data: {
            'valid_no': null,
            'invalid_no': null,
            'total_no': null,
        },
        formulas:{

            valid:function(get){
                var hostLicensesUsed = parseInt(get('valid_no'),10) ;
                return hostLicensesUsed.toString();
            },

            invalid:function(get){
                var hostLicensesUsed = parseInt(get('invalid_no'),10) ;
                return hostLicensesUsed.toString();
            },

            total:function(get){
                var hostLicensesUsed = parseInt(get('total_no'),10) ;
                return hostLicensesUsed.toString();
            },

        }


    },

    items:[{
        xtype: 'panel',
        flex: 3,
        ui: 'light',
        border: 1,
        bodyPadding: 5,
        layout: {
            type: 'table',
            columns: 2
        },
        items: [
            {
                html: '<b>Inventory Details</b><br>',
                colspan: 2
            },{
                html: '&nbsp;&nbsp;Valid Records:'
            }, {
                bind: {
                    html: '{valid}'
                }, bodyStyle: 'text-align: right;'
            }, {
                html: '&nbsp;&nbsp;Invalid Records:'
            }, {
                bind: {
                    html: '{invalid}'
                }, bodyStyle: 'text-align: right;'
            },{
                html: '<hr>'
            }, {
                html: '<hr>'
            },{
                html: '&nbsp;&nbsp;Total Records:'
            }, {
                bind: {
                    html: '{total}'
                }, bodyStyle: 'text-align: right;'
            }
        ],
        buttons: [
            {
                text: 'Submit',
                formBind: true,
                ui: 'primary',
                tooltip: {
                    title: 'Confirm Inventory entries',
                    text: 'Confirm Inventory entries.'
                },
                handler: "submitInventory"
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }],
    listeners: {
        beforerender: 'onInventoryGridBeforeRender'
    },

});