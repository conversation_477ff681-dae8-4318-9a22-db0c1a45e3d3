
Ext.define('sfw.view.scanning.InventoryController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.inventoryController',

    inventoryImport: function (event, target, options) {

        var me = this;
        var importInventory = Ext.create("sfw.view.scanning.ImportInventoryWindow",
            {

            });
        importInventory.show();
    },

    setCsv: function (fb) {
        fb.fileInputEl.set({
            accept: '.csv'
        });
    },

    CSVtoArray(text) {
    var re_valid = /^\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*(?:,\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*)*$/;
    var re_value = /(?!\s*$)\s*(?:'([^'\\]*(?:\\[\S\s][^'\\]*)*)'|"([^"\\]*(?:\\[\S\s][^"\\]*)*)"|([^,'"\s\\]*(?:\s+[^,'"\s\\]+)*))\s*(?:,|$)/g;
    // Return NULL if input string is not well formed CSV string.
    if (!re_valid.test(text)) return null;
    var a = [];                     // Initialize array to receive values.
    text.replace(re_value, // "Walk" the string using replace with callback.
        function(m0, m1, m2, m3) {
            // Remove backslash from \' in single quoted values.
            if      (m1 !== undefined) a.push(m1.replace(/\\'/g, "'"));
            // Remove backslash from \" in double quoted values.
            else if (m2 !== undefined) a.push(m2.replace(/\\"/g, '"'));
            else if (m3 !== undefined) a.push(m3);
            return ''; // Return empty string.
        });
    // Handle special case of empty last value.
    if (/,\s*$/.test(text)) a.push('');
    return a;
    },

    csvselected: function (fb, v, n) {
        Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(false);
        var extension = fb.fileInputEl.dom.files['0']['name'].split(".");
        if(extension[1] != 'csv'){
            Ext.Msg.show({
                title: 'Failed',
                msg: 'Invalid file extension. Please upload CSV File',
                buttons: Ext.Msg.OK
            });
            Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
            return;
        }

        var _this = this;
        var xml_file_text_field = Ext.ComponentQuery.query('#csv_file_text_field')[0];
        xml_file_text_field.setText(fb.fileInputEl.dom.files['0']['name']);
        var reader = new FileReader();
        var file_name = fb.fileInputEl.dom.files['0']['name'];
        var name = Ext.ComponentQuery.query('#inventoryname')[0].getValue();
        if (!name) {
            Ext.ComponentQuery.query('#inventoryname')[0].setValue(file_name);
        }
        self.csvContent = '';
        self.invalidNo = 0;
        reader.onload = function (event) {

            var content = reader.result;
            var allTextLines = content.split(/\r\n|\n/);
            allTextLines.splice(-1);

            if(typeof allTextLines[0] == 'undefined' || allTextLines[0] == ""){
                Ext.Msg.show({
                    title: 'Failed',
                    msg: 'CSV file is empty',
                    buttons: Ext.Msg.OK
                });
                Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
                return;
            }else{
                var headers = allTextLines[0].split(',');
                var regex = new RegExp( headers.join( "|" ), "i");

                if(!regex.test( 'Product' )){
                    Ext.Msg.show({
                        title: 'Failure',
                        msg: 'CSV does not include column Product',
                        buttons: Ext.Msg.OK
                    });
                    Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
                    return;
                }

                if(!regex.test( 'Vendor' )){
                    Ext.Msg.show({
                        title: 'Failure',
                        msg: 'CSV does not include column Vendor',
                        buttons: Ext.Msg.OK
                    });
                    Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
                    return;
                }

                if(!regex.test( 'Version' )){
                    Ext.Msg.show({
                        title: 'Failure',
                        msg: 'CSV does not include column Version',
                        buttons: Ext.Msg.OK
                    });
                    Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
                    return;
                }
            }

            var name_index = headers.indexOf("product");
            var vendor_index = headers.indexOf("vendor");

            var lines = [];
            var count_invalid = 0;
            var count_valid = 0;
            var count_total = 0;

            if(allTextLines.length == 1){
                Ext.Msg.show({
                    title: 'Failure',
                    msg: 'No records found',
                    buttons: Ext.Msg.OK
                });
                Ext.ComponentQuery.query('#confirmCsv')[0].setDisabled(true);
                return;
            }

            for (var i = 1; i < allTextLines.length; i++) {
                var data = _this.CSVtoArray(allTextLines[i]);

                if(data == null){
                    count_invalid++;
                    continue;
                }

                var obj = {};
                obj['valid'] = 1;
                for (var j = 0; j < headers.length; j++) {
                    if (!data[j] && j == name_index) {
                        obj['valid'] = 0;
                    }
                    if (typeof data[j] !== 'undefined') {
                        var str = data[j].trim();
                        var replaced = str.replace(/&?%:'",/g, '');
                        obj[headers[j]] = replaced;
                    } else {
                        obj[headers[j]] = data[j];
                    }
                }

                if(obj['valid'] == 0) {
                    count_invalid++;
                }else{
                    count_valid++;
                }
                count_total++;

                lines.push(obj);
            }

            self.validNo = count_valid;
            self.invalidNo = count_invalid;
            self.totalNo = count_total;
            self.csvContent = lines;
        }


        var content_text = reader.readAsText(fb.fileInputEl.dom.files['0']);

    },

    confirmInventory: function (event, target, options) {
        var me = this;
        self.inventory_name = Ext.ComponentQuery.query('#inventoryname')[0].getValue();
        var inventoryResult = Ext.create("sfw.view.scanning.InventoryResultWindow",
            {

            });

        inventoryResult.show();

        },

    reloadInventoryGrid:function(){
        var view = this.getView();

        var inventoryscans = Ext.getStore('inventorylist');
        var offset = sfw.Util.dateCreate().getTimezoneOffset()*60000;
        var fromdate = new Date( sfw.Util.dateCreate(view.down('#from_date_inventory').getValue()).getTime() + offset );
        fromdate = Ext.util.Format.date(fromdate,sfw.Globals.sqlLiteDate);

        var todate = new Date( sfw.Util.dateCreate(view.down('#to_date_inventory').getValue()).getTime() + offset + 60*60*24*1000 );
        todate = Ext.util.Format.date(todate,sfw.Globals.sqlLiteDate);

        inventoryscans.getProxy().setExtraParams({  'from': fromdate,
            'to': todate,
            'inventory': view.down('#inventory_name').getValue()})
        inventoryscans.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    onInventoryGridBeforeRender: function () {
        const me = this,
            grid = me.getView();
        viewmodel = me.getViewModel();
        viewmodel.set('valid_no', self.validNo);
        viewmodel.set('invalid_no', self.invalidNo);
        viewmodel.set('total_no', self.totalNo);

    },

    submitInventory: function (btn) {

        var view = btn.up('window');

        var params = {
             inventoryname: self.inventory_name
            , content: Ext.util.JSON.encode(self.csvContent)
            , invalidcount: self.invalidNo

        };

        Ext.Ajax.request({
            url: 'action=inventory&which=add'
            , method: "POST"
            , params: params
            , dataType: 'json'
            , success: function (data) {
            var response = Ext.decode(data.responseText);
            if (response.success !== true) {
                Ext.Msg.show({
                    title: "Error"
                    , msg: "Unable to save the inventory."
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.ERROR
                });
            } else {
                view.destroy();
                Ext.ComponentQuery.query('window[xtype=sfw.importInventory]')[0].destroy();
                Ext.ComponentQuery.query('#inventory_grid')[0].getStore().reload();
                Ext.Msg.show({
                    title: "Success"
                    , msg: "Inventory has been saved."
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.INFO
                });
            }
        }
    , failure: function () {
            Ext.Msg.show({
                title: "Error"
                , msg: "Saving inventory failed."
                , buttons: Ext.Msg.OK
                , icon: Ext.MessageBox.ERROR
            });
        }
        });

    },

    getValid: function (value, metaData, record, rowIndex, colIndex, store) {

        var value = parseInt(value);
        var valid_status = '';
        if (value == 1) {
            valid_status = 'Valid';
        } else {
            valid_status = 'Invalid';
        }

        return valid_status;
    },

    getStatus: function (value, metaData, record, rowIndex, colIndex, store) {

        var value = parseInt(value);
        var status = '';

        if (value == 0) {
            status = 'In Queue';
        } else if (value == 1){
            status = 'In Progress';
        } else if (value == 2){
            metaData.tdCls = 'StringSuccess';
            status = 'Success';
        } else if (value == 3){
            metaData.tdCls = 'StringError';
            status = 'Failed';
        }

        return '<span class="' + metaData.tdCls + '">' + status + '</span>';
    },

    InventoryResultWindow: function (event, target, options) {

        inventory_id = options.extra.data.id;
        var me = this;
        Ext.Ajax.request({
            url: 'action=inventory&which=get_inventory_scan_info',
            method: 'GET',
            dataType: 'json',
            params: {
                'inventory_id': options.extra.data.id,
            },
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);

                if (response.error_code == 0) {
                    var inventoryWindow= Ext.create("sfw.view.scanning.InventoryResultsWindow");

                    me.setInventoryInfodata(response.data.inventory.no_patched, response.data.inventory.no_eol, response.data.inventory.no_insecure,response.data.inventory.no_potential_insecure,response.data.inventory.no_unknown)

                    inventoryWindow.getViewModel().set('inventoryscandetails', response.data);
                    inventoryWindow.show();
                } else {
                    sfw.util.Debug.log( 'Error while fetching inventory information ' + response.msg );
                    Ext.Msg.alert('Error', 'Error while fetching inventory information ' + response.reason);
                }
            },
            failure: function (response, opts) {
                //console.log("failed");
            }
        });

    },

    deleteInvalidInventoryResult: function (checkbox, checked) {
        if(checked) {
            var _this = this;
            Ext.MessageBox.confirm('Confirm Deletion', 'Do you want to delete all invalid records?', function (button) {
                if (button === 'yes') {
                    Ext.Ajax.request({
                        url: 'action=inventory&which=delete_invalid',
                        method: 'POST',
                        dataType: 'json',
                        params: {
                            inventory_id: inventory_id

                        },
                        success: function (response) {
                            var status = Ext.util.JSON.decode(response.responseText);
                            if (status.success) {
                                Ext.Msg.alert("Success", "Deleted all invalid records");
                                Ext.ComponentQuery.query('#inventory_grid')[0].getStore().reload();
                                _this.unknownInventoryResult();

                            } else {
                                Ext.Msg.alert("Unexpected Error", "Unable to delete...");
                            }
                        },
                        failure: function () {
                            Ext.Msg.alert("Unexpected Error", "Unable to delete...");
                        }
                    });
                }
            });
        }else {

        }

    },

    InventoryResultWindowdblcick: function (grid, record, event) {
        var _this = this;
        if ((parseInt(record.data.status, 10) == 2) && (typeof sfw.util.Auth.LoginDetails.account.roleIds[ sfw.ROLE_RESULTS ] !== "undefined")) {
            var options = {};
            options.extra = record;
            _this.InventoryResultWindow(grid, null, options);
        } else {
            //console.log("No result exists");
        }
    },

    inventoryResult: function () {
        var inventoryresult = Ext.getStore('inventoryresults');
        inventoryresult.getProxy().setExtraParams({
            'inventory_id': inventory_id,
            'patched': Ext.ComponentQuery.query('#secureinventory')[0].getValue(),
            'insecure': Ext.ComponentQuery.query('#insecureinventory')[0].getValue(),
            'eol': Ext.ComponentQuery.query('#endoflifeinventory')[0].getValue(),
            'potential': Ext.ComponentQuery.query('#potentialinsecureinventory')[0].getValue()
        });
        inventoryresult.loadPage(1, {
            callback: function () {
                },
            failure: function () {
            }
        });
    },

    unknownInventoryResult: function () {
        if(Ext.ComponentQuery.query('#invalidinventory')[0].getValue())
        {
            Ext.ComponentQuery.query('#deleteinvalidinventory')[0].setDisabled(false);
        }else{
            Ext.ComponentQuery.query('#deleteinvalidinventory')[0].setDisabled(true);
        }
        var unknowninventoryresult = Ext.getStore('unknowninventoryresults');
        unknowninventoryresult.getProxy().setExtraParams({
            'inventory_id': inventory_id,
            'invalid': Ext.ComponentQuery.query('#invalidinventory')[0].getValue(),
            'unknown' : true
        });
        unknowninventoryresult.loadPage(1, {
            callback: function () {
                },
            failure: function () {
            }
        });
    },

    setInventoryInfodata: function(secure, endoflife, insecure, potentialinsecure, unknown){

        var chartData = [];
        var securescore = {};
        var endoflifescore = {};
        var insecurescore = {};
        var potentialinsecurescore = {};
        var unknownscore = {};

        var total = parseFloat(secure) + parseFloat(endoflife) + parseFloat(insecure) + parseFloat(potentialinsecure) + parseFloat(unknown);
        securescore.label = 'Secure' + ' (' + secure + ')';
        securescore.data = Math.round((secure / total) * 100);
        securescore.count = secure;

        endoflifescore.label = 'End-of-Life' + ' (' + endoflife + ')';
        endoflifescore.data = Math.round((endoflife / total) * 100);
        endoflifescore.count = endoflife;

        insecurescore.label = 'Insecure' + ' (' + insecure + ')';
        insecurescore.data = Math.round((insecure / total) * 100);
        insecurescore.count = insecure;

        potentialinsecurescore.label = 'Potential Insecure' + ' (' + potentialinsecure + ')';
        potentialinsecurescore.data = Math.round((potentialinsecure / total) * 100);
        potentialinsecurescore.count = potentialinsecure;

        unknownscore.label = 'Unknown' + ' (' + unknown + ')';
        unknownscore.data = 100 - securescore.data - endoflifescore.data - insecurescore.data - potentialinsecurescore.data;
        unknownscore.count = unknown;

        chartData.push(securescore);
        chartData.push(endoflifescore);
        chartData.push(insecurescore);
        chartData.push(potentialinsecurescore);
        chartData.push(unknownscore);

        Ext.getStore('inventoryoverview').loadData(chartData);
    }


});
