var formid = 'costum_ig_form_';
Ext.define('sfw.view.scanning.NewCustomScanRulesWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.newcustomscanrules',
    modal: true,
    title: 'Custom Scan Rule',
    layout: 'fit',
    id: 'newcustomscan',
    controller: 'customscanrules',

    items: [{
        xtype: 'form',
        bodyPadding: 10,
        items: [{
            xtype: 'textfield',
            fieldLabel: 'Name',
            tabIndex: 1,
            name: 'name',
            allowBlank: false,
            itemId: formid + 'name'
        }, {
            xtype: 'fieldcontainer',
            layout: 'hbox',
            items: [{
                xtype: 'textfield',
                fieldLabel: "Filename",
                hideLable: true,
                readOnly: true,
                name: 'filename',
                allowBlank: false,
                itemId: formid + 'filename'
            }, {
                xtype: 'filefield',
                name: 'filenamepath',
                itemId: formid + 'filenamepath',
                buttonOnly: true,
                hideLabel: true,
            }]
        }],
        buttons: [
            {
                text: 'Save',
                itemId: formid + 'button_save',
                formBind: true,
                ui: 'primary',
                tooltip: {
                    title: 'Submit details',
                    text: 'Submit the suggested software details to Flexera.'
                }
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]
});