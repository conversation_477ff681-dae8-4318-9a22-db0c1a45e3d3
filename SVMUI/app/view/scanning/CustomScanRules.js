Ext.define('sfw.view.scanning.CustomScanRules', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiCustomScanRules',

    requires: [
        'sfw.view.scanning.CustomScanRulesController',
        'sfw.store.scanning.CustomScanRules',
        'sfw.Util'
    ],
    stateful: true,
    stateId: 'sfw_csiCustomScanRules_grid',
    title: 'Custom Scan Rules',
    border: 1,
    cls: 'shadow',
    controller: 'customscanrules',

    store: {
        type: 'customscanrules'
    },

    selModel: {
        mode: 'MULTI'
    },
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    /*    viewModel: {
            type: 'scanning-customscanrules'
        },*/

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'button',
            text: 'New Custom Scan Rule',
            ui: 'primary',
            handler: 'NewCustomScanRulesWindow'
        }, {
            xtype: 'tbseparator'
        }, {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }]
    }],

    columns: [
        {text: 'Rule Name', dataIndex: 'name', flex: 3},
        {text: 'Scan Type 1 Path', dataIndex: 'path', flex: 1, renderer: sfw.Util.tipRenderer, hidden: true},
        {text: 'Filename', dataIndex: 'filename', flex: 1}
    ],

    listeners: {
        itemdblclick: 'onEditRule',
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            var deleteText = (selected.items.length > 1) ? 'Delete ' + selected.items.length + ' Rules' : 'Delete';
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'customscanrules',
                width: 250,
                plain: true,
                items: [{
                    text: 'Edit',
                    listeners: {
                        afterRender: function () {
                            if (selected.items.length > 1) {
                                this.hide();
                            }
                        },
                        click: {fn: 'onEditRule', extra: record}
                    }
                }, {
                    text: deleteText,
                    listeners: {
                        click: {fn: 'onDeleteRule', extra: selected.items}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        }
        //afterrender:'reloadCustomScanRulesGrid'

    },

    bbar: {
        xtype: 'pagingtoolbar',
        store: 'customscanrules',
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Completed Scans {0} - {1} of {2}',
        emptyMsg: "No Custom Scan Rules configured"
    }
});
