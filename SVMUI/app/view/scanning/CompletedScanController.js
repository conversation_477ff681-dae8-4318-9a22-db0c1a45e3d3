
Ext.define('sfw.view.scanning.CompletedScanController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.completedScanController',

    completedScanScheduleReport: function () {
        var scheduleReportWindow = Ext.create("sfw.view.commonpopupwindows.ScheduleReportSetup",
            {
                listeners:{
                    afterrender:function(){
                        Ext.getCmp('tablename').setText('Completed Scans');
                    }
                }
            });
        scheduleReportWindow.show();
    },

    reloadCompletedScanGrid:function(){
        var view = this.getView();

        var completedscans = Ext.getStore('getcompletescanresult');
        var offset = sfw.Util.dateCreate().getTimezoneOffset()*60000;
        var fromdate = new Date( sfw.Util.dateCreate(view.down('#from_date').getValue()).getTime() + offset );
        fromdate = Ext.util.Format.date(fromdate,sfw.Globals.sqlLiteDate);

        var todate = new Date( sfw.Util.dateCreate(view.down('#to_date').getValue()).getTime() + offset + 60*60*24*1000 );
        todate = Ext.util.Format.date(todate,sfw.Globals.sqlLiteDate);

        completedscans.getProxy().setExtraParams({  'from': fromdate,
            'to': todate,
            'host': view.down('#host_name').getValue()})
        completedscans.loadPage(1,{
            callback: function () {
               //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    hideColumnsBasedonRole:function(grid){
        var columns = grid.down('headercontainer').getGridColumns();
        if(!sfw.util.Auth.LoginDetails.account.hasRoleIds( [ sfw.ROLE_SCANNING, sfw.ROLE_RESULTS ] )){
            columns[6].destroy();
            columns[7].destroy();
            columns[8].destroy();
            columns[9].destroy();
        };
        if (!sfw.util.Auth.LoginDetails.account.hasRole( [ sfw.ROLE_SCANNING ] ) ) {
            columns[0].destroy();
            columns[1].destroy();
            columns[2].destroy();
            columns[3].destroy();
            columns[4].destroy();
            columns[5].destroy();
        }
    }

});
