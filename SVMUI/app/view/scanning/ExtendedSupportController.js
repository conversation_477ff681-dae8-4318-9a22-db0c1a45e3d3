
Ext.define('sfw.view.scanning.ExtendedSupportController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.extendedSupportController',

    reloadExtendedSupportGrid:function(){
        var view = this.getView();

        var extendedsupport = Ext.getStore('getextendedsupportlist');
        var productName = view.down('#product_name').getValue();

        extendedsupport.getProxy().setExtraParams({
            'product_name': productName})
        extendedsupport.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    reloadExtendedSupportGridOnEnter: function(field, e){
        if (e.getKey() == e.ENTER) {
            this.reloadExtendedSupportGrid();
        }
    }

});
