Ext.define('sfw.view.scanning.ScanPathsController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.scanpaths',

    init : function(){
        hasBlacklist = 0;
        hasWhitelist = 0;

        infoMsgOne = 'If a Allow list is configured, only the paths included in the Allow list will be investigated by the scanner.';

        infoMsgTwo = infoMsgOne + '<br><br>Note that if you configure a Allow list, it will be used instead of the Block list that is already configured. It is not possible to use both a Allow list and Block list at the same time, and Allow list takes precedence.';

        infoMsgThree = 'Both a Block list and a Allow list have been configured. Note that it is not possible to use both at the same time, and the Allow list takes precedence. The Block list will not be considered by the scanner.';

        infoMsgFour = 'If a Block list is configured, only the paths included in the Block list will be ignored by the scanner.';

        infoMsgFive = infoMsgFour + '<br><br>However, as a Allow list has already been configured, any Block list created will be ignored, as it is not possible to use both a Block list and a Allow list at the same time, and Allow list takes  precedence. If you wish for the Block list to be considered by the scanner, you must first delete all Allow lists.';

    },

    scanListStatus:function(){
        var me = this;
        var view = me.getView();
        if(view.down("#allowlist").getValue()) {
            view.down('#addButton').setText('Add Allow List Rule');
            me.handleWhitelistSelection();
        } else if(view.down("#blocklist").getValue()){
            view.down('#addButton').setText('Add Block List Rule');
            me.handleBlacklistSelection();
        }

        this.reloadScanPathsGrid();
    },

    addBlockListRule:function(btn){

        var formValues = btn.up('form').getValues();
        var _this = this;
        var type = '';
        var url = '';
        var successmessage = '';
        var notFromScanPathGrid = false;
        if(Ext.ComponentQuery.query("#notFromScanPathGrid")[0].text == 'YES'){
            type = 1;
            notFromScanPathGrid = true;
        }
        else if(Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
            type = 0;
        }
        else if(Ext.ComponentQuery.query("#blocklist")[0].getValue()){
            type = 1;
        }

        if((type == 1) && (formValues.impactListCheck)){
            type = 2;
        }

        if(Ext.ComponentQuery.query('#savingType')[0].text == 'NEW'){
            url = 'action=ajaxapi_scan_paths&path_id=new';
            successmessage = 'Rule Added successfully';
        }else if(Ext.ComponentQuery.query('#savingType')[0].text == 'EDIT'){
            url = 'action=ajaxapi_scan_paths&path_id='+editId;
            successmessage = 'Rule Edited successfully';
        }

        Ext.Ajax.request({
            url: url,
            method: 'POST',
            params: {
                "name": formValues.name,
                "path": formValues.path,
                "site": formValues.site,
                "type": type
            },
            dataType: 'json',
            success: function (response) {
                var response = Ext.util.JSON.decode( response.responseText );
                if( response.success ) {
                    Ext.Msg.alert("Success", successmessage);
                    btn.up('window').destroy();
                    if(!notFromScanPathGrid) {
                        _this.reloadScanPathsGrid();
                    }
                }
                else{
                    Ext.Msg.alert("Failure", "Failed to add");
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to Add...");
            }
        });

    },

    viewEditAllowListRule:function(event, target,options){
        editId = options.extra.data.id;
        var type = options.extra.data.type;
        var blocklistwindow = Ext.create('sfw.view.scanning.AddBlockListRuleWindow',{
            listeners: {
                afterrender: function (scanWindow) {
                    scanWindow.down('#impactListCheck').show();
                    scanWindow.down('#previewList').show();
                    if(Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
                        scanWindow.down('#impactListCheck').hide();
                        scanWindow.down('#previewList').hide();
                        blocklistwindow.down("title").setHtml("Edit Scan Path Rule for Allow LIst");
                    }
                    else if((Ext.ComponentQuery.query("#blocklist")[0].getValue()) && (type == '1')){
                        blocklistwindow.down("title").setHtml("Edit Scan Path Rule for Block LIst");
                    }else if((Ext.ComponentQuery.query("#blocklist")[0].getValue()) && (type == '2')){
                        scanWindow.down('#impactListCheck').setValue(true);
                    }
                    blocklistwindow.down('#savingType').setText('EDIT');
                    blocklistwindow.down("#scanlistname").setValue(options.extra.data.name);
                    blocklistwindow.down("#scanlistpath").setValue(options.extra.data.path);
                    blocklistwindow.down("#scanlistsite").setValue(options.extra.data.site);

                }
            }
        });
        blocklistwindow.show();

    },

    confirmDeleteAllowListRule:function(event, target,options){
        var _this = this;
        var selectionArray = [];

        if(Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
            type = 0;
            deleteType = 'Allow';
        }
        else if(Ext.ComponentQuery.query("#blocklist")[0].getValue()){
            type = 1;
            deleteType = 'Block';
        }

        if((Ext.ComponentQuery.query("#blocklist")[0].getValue()) && (options.extra.length<=1)){
            type = options.extra[0].data.type;
        }

        for ( var i=0; i < options.extra.length; ++i ) {
            selectionArray.push( options.extra[i].data.id );
        }
        deleteIdList = selectionArray.join(",");

        Ext.Msg.show({
            title:'Delete Allow List Rule',
            message: 'Please confirm that you wish to delete this Scan Path Rule from the ' + deleteType +' List',
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    _this.deleteAllowListRule(deleteIdList,options.extra.length,type);
                } else if (btn === 'no') {
                    //console.log('NO');
                }
            }
        });
    },

    deleteAllowListRule:function(deleteIdList,itemLength,type){
        var _this = this;

        Ext.Ajax.request({
            url: 'action=ajaxapi_scan_paths&which=delete&',
            method: 'POST',
            params: {
                delete_id_list: deleteIdList,
                type : type
            },
            dataType: 'json',
            success: function (response) {
                var response = Ext.util.JSON.decode( response.responseText );
                var status = response.status;
                switch( status ) {
                    case 0:
                        var numDeleted = response.numDeleted;
                        var message = '';

                        if ( numDeleted < itemLength ) {
                            message = "Only " + numDeleted + " of " + itemLength + " ";
                        }
                        message += 'Scan Path Rule' + ( ( numDeleted > 1 ) ? 's' : '' ) + ' Deleted';

                        _this.reloadScanPathsGrid();
                        Ext.Msg.alert( "Success", message );
                        break;
                    case 1:
                        Ext.Msg.alert( "Error", "ScanPath Rule Id is Incorrect" );
                        break;
                    default:
                        Ext.Msg.alert( "Unexpected Error", "Unable to Delete..." );
                        break;
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
            }
        });
    },

    reloadScanPathsGrid:function(){
        var me = this;
        var scanpathstore = Ext.getStore('scanpaths');
        var type = '';

        var switchGrid = false;

        if((this.getView()) && (this.getView().down('headercontainer'))){
            switchGrid = true;
            columns = this.getView().down('headercontainer').getGridColumns();
            headerCtr = this.getView().down('headercontainer');
            menu = headerCtr.getMenu();
        }

        if(Ext.ComponentQuery.query("#allowlist")[0].getValue()) {
            type = 0;

            if(switchGrid){
                menu.down('[text="Logged"]').hide();
                columns[3].hide();
            }
        }
        else if(Ext.ComponentQuery.query("#blocklist")[0].getValue()){
            type = 1;

            if(switchGrid){
                menu.down('[text="Logged"]').show();
                columns[3].show();
            }
        }

        scanpathstore.getProxy().setExtraParams({ 'type': type,})

        scanpathstore.loadPage(1,{
            callback: function (records, operation, success) {
                if(type == 0){
                    hasWhitelist = this.getCount();
                } else if(type == 1){
                    hasBlacklist = this.getCount();
                }
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    handleBlacklistSelection:function(){
        var info = '';

        // No lists have data
        if ( hasBlacklist <= 0 && hasWhitelist <= 0 ) {
            info = infoMsgFour;
        }

        // Only Whitelist has data
        else if (hasBlacklist <= 0 && hasWhitelist > 0 ) {
            info = infoMsgFive;
        }

        // Only Blacklist has data
        else if ( hasBlacklist > 0 && hasWhitelist <= 0 ) {
            info = infoMsgFour;
        }

        // Both lists have data
        else if ( hasBlacklist > 0 && hasWhitelist > 0 ) {
            info = infoMsgThree;
        }

        Ext.Msg.show({
            title: 'Block List Scan Configuration',
            message: info,
            buttons: Ext.Msg.OK,
            icon: Ext.Msg.INFO,
            fn: function (btn) {
                if (btn === 'ok') {
                    //console.log('Ok');
                }
            }
        });

    },

    handleWhitelistSelection : function( ) {
        var info = '';

        // No lists have data
        if (hasBlacklist <= 0 && hasWhitelist <= 0) {
            info = infoMsgOne;
        }

        // Only Whitelist has data
        else if (hasBlacklist <= 0 && hasWhitelist > 0) {
            info = infoMsgOne;
        }

        // Only Blacklist has data
        else if (hasBlacklist >= 0 && hasWhitelist <= 0) {
            info = infoMsgTwo;
        }

        // Both lists have data
        else if (hasBlacklist > 0 && hasWhitelist > 0) {
            info = infoMsgThree;
        }

        Ext.Msg.show({
            title: 'Allow List Scan Configuration',
            message: info,
            buttons: Ext.Msg.OK,
            icon: Ext.Msg.INFO,
            fn: function (btn) {
                if (btn === 'ok') {
                    //console.log('Ok');
                }
            }
        });
    },

    viewEditAllowListRuledbClick: function (grid, record, event) {
        var options = {};
        options.extra = record;
        this.viewEditAllowListRule(grid, null, options);
    },

    previewList:function(btn) {
        var me = this;
        var form = me.getView();

        if (!form.down('#scanlistpath').getValue()) {
            Ext.Msg.alert("Error", 'Please enter the Path');
            return false;
        }

        var impactListWindow = Ext.create('sfw.view.scanning.AwarenessListWindow');
        var impactStore = impactListWindow.getViewModel().getStore('impactListResults');

        impactStore.getProxy().setExtraParams({
            'path': form.down('#scanlistpath').getValue(),
            'site': form.down('#scanlistsite').getValue()})
        impactListWindow.show();
    }

});