Ext.define('sfw.view.main.Workbench', {
    extend: 'Ext.tab.Panel',
    alias: 'widget.workbench',
    plain: true,
    // flex: 1,
    cls: 'sencha-dash-right-main-container',
    //border: 1,
    defaults: {
        closable: true
    },
    plugins: [{
        ptype: 'tabclosemenu'
    }],
    // layout: {
    //     type: 'card',
    //     anchor: '100%'
    // },

    listeners: {
        tabchange: 'onTabChange' //TODO - Disabling route history
    },
    items: [{
        xtype: 'sfw.csiDashboard',
        routeId: 'Dashboard'
    }]
});