Ext.define('sfw.view.main.Header', {
    extend: 'Ext.toolbar.Toolbar',
    alias: 'widget.header-toolbar',

    cls: 'sencha-dash-dash-headerbar shadow',
    style: {
        boxShadow: '0 5px 5px -5px #afafaf;'
    },

    viewModel: {
        formulas: {
            loggedInUser: function (get) {
                return sfw.util.Auth.LoginDetails.loginAccountUsername ? sfw.util.Auth.LoginDetails.loginAccountUsername : '-';
            },
            accountDetails: function (get) {
                var loginAccountUsername = sfw.util.Auth.LoginDetails.loginAccountUsername ? sfw.util.Auth.LoginDetails.loginAccountUsername : '-';
                var accountExpires = Ext.Date.format(
                    sfw.util.Util.dateCreate(sfw.util.Auth.LoginDetails.accountExpires),
                    sfw.util.Globals.dateShortOutput
                );
                return Ext.String.format('Account: {0}. Expires: {1}', loginAccountUsername, accountExpires);
            },showAlerts: function (get) {
                if(sfw.SharedConstants.EDITION === sfw.SharedConstants.HOSTED_EDITION) {
                    return true;
                }else{
                    return false;
                }
            },
            clsIcon: function (get) {
                return (get('showVtSynMessage') || get('showNewBuildDetails'))
                    ? 'x-fa fa-bell StringError'
                    : 'x-fa fa-bell';
            }
        }
    },
    
    items: [{
        margin: '0 0 0 8',
        ui: 'header',
        iconCls: 'x-fa fa-bars',
        id: 'main-navigation-btn',
        handler: 'onToggleNavigationSize'
    },{
        xtype: 'component',
        reference: 'appLogo',
        cls: 'app-logo',
        html: '<div class="main-logo"><img src="resources/images/CSI_Logo_application.svg"></div>',
        width: 250    },  '->',  {
        ui: 'header',
        text: 'Alerts',
        tooltip: 'Alerts',
        bind:{
            iconCls:'{clsIcon}',
            hidden:'{showAlerts}'
        },
        handler: function (button) {

            var viewModel = button.up('toolbar').getViewModel();

            var lastSync = viewModel.get('lastSync');
            var maxReleaseVulnDate = viewModel.get('maxVulnReleasedate');
            var showVtSynMessage = viewModel.get('showVtSynMessage');
            var showNewBuildDetails = viewModel.get('showNewBuildDetails');
            var differenceInDays = viewModel.get('differenceInDays');

            var vtSyncTextData = '';
            if(showVtSynMessage){
                vtSyncTextData =  "<i class='fa fa-exclamation-circle' style='color:red;margin-left:-19px'></i>"+" VT out of sync.";
            }else{
                if(lastSync){
                    lastSync = sfw.Default.gridRenderUTCDateInLocaltime(lastSync, sfw.util.Globals.dateLongOutput);
                    vtSyncTextData = "<span style='margin-left:-18px;'>VT last sync: " + lastSync + "</span>";
                }else {
                    if (viewModel.get('dbExists') === false) {
                        vtSyncTextData = "<i class='fa fa-exclamation-circle' style='color:red;margin-left:-19px'></i>"+" VT database not available.";
                    }else{
                        if(maxReleaseVulnDate){
                            if(!showVtSynMessage){
                                maxReleaseVulnDate = sfw.util.Default.gridRenderUTCDateInLocaltimeFormat(maxReleaseVulnDate, sfw.util.Globals.dateLongOutput);
                                vtSyncTextData = "<span style='margin-left:-18px;'>VT last sync: " + maxReleaseVulnDate+ "</span>";
                            }else{
                                vtSyncTextData =  "<i class='fa fa-exclamation-circle' style='color:red;margin-left:-19px'></i>"+" VT out of sync.";
                            }

                        }else{
                            vtSyncTextData = "<i class='fa fa-exclamation-circle' style='color:red;margin-left:-19px'></i>"+" VT database not available.";
                        }
                    }
                }

            }

            var newBuildTextData = '';
            var newBuildNumber = '';
            if(showNewBuildDetails){
                newBuildTextData = "<span style='margin-left:-18px;'>New build " + viewModel.get('newBuildName') + " is available.</span>";
                newBuildNumber = viewModel.get('newBuildNumber');
            }else{
                newBuildTextData = "<span style='margin-left:-18px;'>No new build available.</span>";
            }

            var menu = Ext.create('Ext.menu.Menu', {
                items: [
                    {
                        text: vtSyncTextData,
                        tooltip: showVtSynMessage ? 'Click here to view details' : '',
                        handler: function(){
                            if(showVtSynMessage){
                                Ext.Msg.alert('Vt out of sync', 'The VT database was synced ' + differenceInDays+ ' days ago. Please run a resync. For guidance, refer to the <a target="_blank" href="https://community.flexera.com/s/article/the-synchronisation-process-appears-to-be-locked-in-an-inconstent-state">documentation.</a> If the issue persists after following the documentation, please contact the support team for further assistance.');
                            }else{
                                return false;
                            }
                        }
                    }, {
                        text: newBuildTextData,
                        tooltip: showNewBuildDetails ? 'Click here to view new build details' : '',
                        handler: function(){
                            if(showNewBuildDetails){
                                Ext.Msg.alert('SVM build ' + newBuildNumber, 'A new build ' + newBuildNumber + ' is available. Click <a target="_blank" href="https://ca.secunia.com/download"> here </a> to  download  the latest version.' );
                            }else{
                                return false;
                            }
                        }
                    }
                ]
            });

            // Show the menu at the button's position
            menu.showBy(button);
        }
    }, {
        iconCls: 'x-fa fa-question',
        ui: 'header',
        text: 'Help',
        tooltip: 'Help',
        handler: 'navigateToHelp'
    }, {
        iconCls: 'x-fa fa-user',
        ui: 'header',
        bind: {
            text: '{loggedInUser}',
            tooltip: '{accountDetails}'
        },
        handler: Ext.emptyFn
    }, {
        xtype: 'button',
        ui: 'header',
        tooltip: 'Logout',
        text: 'Logout',
        iconCls: 'x-fa fa-sign-out-alt',
        handler: 'onUserLogout'
    }],

    initComponent: function () {
        var me = this;
        me.callParent(arguments);

        // Make the API call to fetch Last Sync value
        if (sfw.SharedConstants.EDITION !== sfw.SharedConstants.HOSTED_EDITION) {
            Ext.Ajax.request({
                url: 'action=ajaxapi_dashboard&which=vt_sync_details',
                method: 'GET',
                success: function (response) {
                    var response = Ext.decode(response.responseText, true);
                    if (response.success) {
                        // Update the ViewModel
                        var showVtSynMessage = false;
                        var differenceInDays = null;
                        if (response.last_vt_sync_date_time) {
                            me.getViewModel().set('lastSync', response.last_vt_sync_date_time);
                            me.getViewModel().set('maxVulnReleasedate', null);
                            me.getViewModel().set('dbExists', true);
                            var parsedDate1 = new Date(response.last_vt_sync_date_time);
                            var parsedDate2 = new Date();
                            var differenceInMilliseconds = Math.abs(parsedDate1 - parsedDate2);
                            var differenceInDays = Math.ceil(differenceInMilliseconds / (1000 * 60 * 60 * 24));
                            showVtSynMessage = differenceInDays > 2;
                        } else {
                            if (response.db_exists) {
                                if (response.max_vuln_released_date) {
                                    me.getViewModel().set('maxVulnReleasedate', response.max_vuln_released_date);
                                    me.getViewModel().set('lastSync', null);
                                    me.getViewModel().set('dbExists', true);
                                    var parsedDate1 = new Date(response.max_vuln_released_date);
                                    var parsedDate2 = new Date();
                                    var differenceInMilliseconds = Math.abs(parsedDate1 - parsedDate2);
                                    var differenceInDays = Math.ceil(differenceInMilliseconds / (1000 * 60 * 60 * 24));
                                    showVtSynMessage = differenceInDays > 2;
                                }
                            } else {
                                me.getViewModel().set('dbExists', false);
                                showVtSynMessage = true;
                            }
                        }

                        me.getViewModel().set('showVtSynMessage', showVtSynMessage);
                        me.getViewModel().set('differenceInDays', differenceInDays);

                    } else {
                        me.getViewModel().set('lastSync', null);
                        me.getViewModel().set('maxVulnReleasedate', null);
                        me.getViewModel().set('dbExists', false);
                    }
                },
                failure: function () {
                    me.getViewModel().set('lastSync', null);
                    me.getViewModel().set('maxVulnReleasedate', null);
                    me.getViewModel().set('dbExists', false);
                }
            });
        }

        //build details
        if (sfw.SharedConstants.EDITION !== sfw.SharedConstants.HOSTED_EDITION) {
            Ext.Ajax.request({
                url: 'action=ajaxapi_dashboard&which=rpm_build_details',
                method: 'GET',
                success: function (response) {
                    var response = Ext.decode(response.responseText, true);

                    if (response.success) {
                        if (response.build_name && response.build_version) {
                            if (sfw.util.Util.compareVersions(sfw.util.Globals.CSIBackendVersion, response.build_version) < 0) {
                                me.getViewModel().set('showNewBuildDetails', true);
                                me.getViewModel().set('newBuildName', response.build_name);
                                me.getViewModel().set('newBuildNumber', response.build_version);
                            } else {
                                me.getViewModel().set('showNewBuildDetails', false);
                            }
                        } else {
                            me.getViewModel().set('showNewBuildDetails', false);
                        }
                    } else {
                        me.getViewModel().set('showNewBuildDetails', false); // Handle failure
                    }
                },
                failure: function () {
                    me.getViewModel().set('showNewBuildDetails', false); // Handle failure
                }
            });
        }

    }
});
