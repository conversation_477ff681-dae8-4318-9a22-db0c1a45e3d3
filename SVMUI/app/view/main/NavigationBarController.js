Ext.define('sfw.view.main.NavigationBarController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.nav-bar',

    onNavigationTreeSelectionChange: function (tree, node) {
        var me = this;

        if (node) {
            var originalText = !Ext.isEmpty(node.data.originalText) ? node.data.originalText: node.data.text;
            if (!Ext.isEmpty(originalText)) {
                const isTooltipped = originalText.indexOf('qtip');
                if (isTooltipped != -1) {
                    const dom = Ext.dom.Helper.createDom(originalText),
                        flyingElm = Ext.fly(dom);

                    if (flyingElm) {
                        originalText = flyingElm.getHtml();
                    }
                }

                sfw.util.Debug.log("Loaded page: " + Ext.htmlDecode(originalText));
            }
            sfw.ui.activeMenuItem = node.data.id;
        }

        var routeUrl = node && (node.get('routeId') || node.get('viewType'));
        if (routeUrl) {
            if (Ext.String.startsWith(routeUrl, 'sfw.csi', true)) {
                routeUrl = routeUrl.substring('sfw.csi'.length, routeUrl.length);
            }
            this.redirectTo(routeUrl);
        }
    },

    isSmartGroupClicked: function (node) {
        var data = node.data;
        return data.id === 'sfw.csiResults';
    },

    onMenuItemClick: function(list, info) {
        const me = this,
            node = info.node;

        if(node && me.isSmartGroupClicked(node)) {
            me.smartGroupContinueLoading(node);
            me.updateZeroDayAdvisoryCount();
        }
    },

    smartGroupContinueLoading: function (node) {
        var me = this;

        me.loadSmartGroupNode('host', 120000);
        me.loadSmartGroupNode('product', 120000);
        me.loadSmartGroupNode('advisory', 120000); //2 seconds
    },

    loadSmartGroupNode: function(type, timeout, createSG = false, deleteSG = false) {
        var me = this,
            resultsNode = me.getResultsNode(),
            node;

        switch (type) {
            case 'host':
                node = me.getNodeById('sfw.csiHostSmartGroupsFolder');
                break;
            case 'product':
                node = me.getNodeById('sfw.csiProductSmartGroupsFolder');
                break;
            case 'advisory':
                node = me.getNodeById('sfw.csiAdvisorySmartGroupsFolder');
                break;
            default:
                node = null;
        }

        if (!node) {
            return;
        }
        node.set('type', type); //just to recognize node quickly

        if(!createSG && !deleteSG){
            if (Ext.isDefined(node.task)) {
                return;
            }
        }

        node.task = Ext.util.TaskManager.newTask({
            run: function () {
                me.executeSmartGroupTask(node,deleteSG);
            },
            interval: timeout,
            fireOnStart: true
        });

        node.task.start();
    },

    executeSmartGroupTask: function (node,deleteSG) {
        var me = this,
            resultsNode = me.getResultsNode();

        //only update node's count when its expanded state
/*        if (resultsNode.isExpanded() &&
            (!node.isExpanded() && node.hasChildNodes() && resultsNode.id === node.get('parentId'))) {
            return;
        }*/

        Ext.Ajax.request({
            url: 'action=smart_groups&which=menuSummary&smartGroupTextType='+node.get('type'),
            dataType: 'json',
            success: function (response) {
                var data = (Ext.decode(response.responseText, true) || {}).data || [];
                if(deleteSG){
                    node.removeAll()
                }
                me.buildSmartNodeChildren(node, data, deleteSG);
            }
        });
    },

    buildSmartNodeChildren: function (node, records) {

        if (!Ext.isDefined(node.get('originalText'))) {
            node.set('originalText', node.get('text'));
        }

        if(!node.hasChildNodes()) {
            var overviewNodeId = Ext.String.format('sfw.csi{0}SmartGroups', Ext.String.capitalize(node.get('type')));
            var children = [{
                leaf: true,
                text: '<span data-qtip="Overview & Configuration">Create & Edit</span>',
                // text: 'Create & Edit',
                smartGroupType: node.get('type')+'Overview',
                viewType: Ext.String.format('sfw.csiSmartGroup{0}Overview', Ext.String.capitalize(node.get('type'))), //TODO - fixme
                id: overviewNodeId
            }];
            Ext.each(records, function(data) {
                var routeId = Ext.String.format('{0}{1}{2}', 'sfw.csiSmartGroup', Ext.String.capitalize(node.get('type')), data.id);
                var totalNum = !Ext.isEmpty(data.totalNum) ? data.totalNum: 0;
                children.push({
                    leaf: true,
                    viewType: routeId,
                    smartGroupType: node.get('type'),
                    smartGroupId: data.id,
                    originalText: data.name,
                    text: Ext.String.format('<span data-qtip="{0}">{1}</span> ({2})', Ext.htmlEncode(data.name), data.name, totalNum),
                    routeId : routeId,
                    data: data,
                    id: Ext.String.format('sfw.csiSmartGroup_{0}_{1}', node.get('type'), data.id)
                });
            });

            node.set('text', Ext.String.format('{0} ({1})', node.get('originalText'), (children.length-1))); //don't count first item
            node.appendChild(children);
        } else {
            //if node is already available then update count
            Ext.each(records, function (data) {
                var child = node.findChild('id', Ext.String.format('sfw.csiSmartGroup_{0}_{1}', node.get('type'), data.id));
                var totalNum = !Ext.isEmpty(data.totalNum) ? data.totalNum: 0;
                if (child == null) {
                    var routeId = Ext.String.format('{0}{1}{2}', 'sfw.csiSmartGroup', Ext.String.capitalize(node.get('type')), data.id);
                    var totalNum = !Ext.isEmpty(data.totalNum) ? data.totalNum: 0;
                    node.appendChild({
                        leaf: true,
                        viewType: routeId,
                        smartGroupType: node.get('type'),
                        smartGroupId: data.id,
                        originalText: data.name,
                        text: Ext.String.format('<span data-qtip="{0}">{1}</span> ({2})', Ext.htmlEncode(data.name), data.name, totalNum),
                        routeId : routeId,
                        data: data,
                        id: Ext.String.format('sfw.csiSmartGroup_{0}_{1}', node.get('type'), data.id)
                    });
                    node.set('text', Ext.String.format('{0} ({1})', node.get('originalText'), (node.childNodes.length-1)));
                }else{
                    child.set('text',Ext.String.format('<span data-qtip="{0}">{1}</span> ({2})', Ext.htmlEncode(data.name), data.name, totalNum));
                }
            });
        }
    },

    updateZeroDayAdvisoryCount: function () {
        var me = this,
            resultsNode = me.getResultsNode(),
            node = resultsNode.findChild('id', 'sfw.csiZeroDay');

        if (!node) {
            return;
        }

        if (!Ext.isDefined(node.get('originalText'))) {
            node.set('originalText', node.get('text'));
        }

        var url = 'action=smart_groups&which=zero_day_overview';

        Ext.Ajax.request({
            url: url,
            success: function( response ) {
                var result = Ext.decode(response.responseText, true) || {};
                if (Ext.isDefined(result.data)) {
                    var count = result.data.total, text = '';
                    if ( node ) {
                        if ( isNaN( count ) || count == 0 ) {
                            text = Ext.String.format('{0} ({1})', node.get('originalText'), 0);
                        } else {
                            text = Ext.String.format('<span style="color:orangered;">{0} ({1})</span>', node.get('originalText'), count);
                        }
                        node.set('text', text);
                    }
                }
            },
            failure: function() {
                //console.log('Failed to load zero_day_overview count');
            }
        });
    },

    //---- unit functions
    getResultsNode: function () {
        var me = this,
            store = me.getView().getStore();

        return store.getNodeById('sfw.csiResults');
    },

    getNodeById: function (nodeId) {
        var me = this,
            store = me.getView().getStore();

        return store.getNodeById('sfw.csiResults').findChild('id', nodeId);
    }


});