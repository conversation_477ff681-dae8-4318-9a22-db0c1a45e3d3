/**
 * This class is the main view for the application. It is specified in app.js as the
 * "mainView" property. That setting automatically applies the "viewport"
 * plugin causing this view to become the body element (i.e., the viewport).
 *
 * TODO - Replace this content of this view to suite the needs of your application.
 */

Ext.define('sfw.view.main.Main', {
    extend: 'Ext.container.Viewport',

    controller: 'main',
    viewModel: 'main',

    // cls: 'sencha-dash-viewport',
    itemId: 'mainView',

    layout: {
        type: 'border'
    },

    style: {
        backgroundColor: 'rgb(248, 249, 251)'
    },

    listeners: {
        render: 'onMainViewRender',
        userloggedin: 'onUserLoggedIn',
        afterrender: function () {
            Ext.getBody().on('contextmenu', function (ev) {
                ev.preventDefault();
            });
        }
    }

});