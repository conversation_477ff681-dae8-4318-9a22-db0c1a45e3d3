/**
 * This class is the controller for the main view for the application. It is specified as
 * the "controller" of the Main view class.
 */
var LoginDetails = {};
Ext.define('sfw.view.main.MainController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.main',

    requires: [
        'sfw.util.Auth',
        'sfw.util.MenuFolders',
        'sfw.util.SecuniaPackageSystem',
        'sfw.util.External',
        'sfw.view.main.NavigationBar',
        'sfw.view.main.Header',
        'sfw.view.main.Workbench'
    ],

    config: {
        showNavigation: true
    },

    routes: {
        '*': {
            before: 'onBeforeRouting'
        },
        ':node': {
            action: 'onRouteChange',
            conditions: {
                ':node': '([%a-zA-Z0-9\\.\\-\\_\\s,]+)'
            }
        },
        'login': {
            before: 'onBeforeLogin',
            action: 'setupLogin'
        }
    },
    listen: {
        global: {
            'manuallogin': 'onManualLogin'
        },

        controller: {
            '*': {
                // We delegate all changes of router history to this controller by firing
                // the "changeroute" event from other controllers.
                //changeroute: 'changeRoute',
                unmatchedroute: 'onUnmatchedRoute'
            }
        }
    },

    init: function () {

        window.onhashchange = function(){
            var currentPage = window.location.href.split("#");
            var fullScreenDetails = Ext.ComponentQuery.query('#fullScreen')[0];

            if((currentPage[1] !== 'Dashboard') && (fullScreenDetails.fullScreenView == true)){
                var portView = Ext.ComponentQuery.query('viewport')[0];
                portView.down('[region=north]').show();
                portView.down('[region=west]').show();
                portView.down('#contentPanel').down('tabbar').show();
                fullScreenDetails.setText('Fullscreen View');
                fullScreenDetails.fullScreen = false;
                fullScreenDetails.fullScreenView = false;
            }
        }
    },

    onLaunch: function () {
        this.originalRoute = Ext.getApplication().getDefaultToken();

        // this.restoreSession();
    },

    onBeforeLogin: function (action) {
        const me = this,
            isLoggedId = sfw.util.Auth.isLoggedIn();

        if (!isLoggedId) {
            action.resume();
        } else {
            action.destroy();
            me.redirectTo(me.originalRoute, {replace: true});
        }
    },

    onBeforeRouting: function (action) {
        const me = this,
            app = sfw.getApplication(),
            args = Ext.Array.slice(arguments),
            isLoggedId = sfw.util.Auth.isLoggedIn();

        if (app.appready) {
            if (isLoggedId) {
                action.resume();
            } else {
                action.destroy();
                //me.logout(); //FIXME
            }
        } else {
            app.on(
                'appready',
                Ext.Function.bind(me.onBeforeRouting, me, args),
                me,
                {single: true}
            );
        }

    },

    logout: function () {
        const me = this,
            view = me.getView();

        view.removeAll();

        //TODO: move all logout related stuffs here.
        if (!sfw.util.Auth.isLoggedIn()) {
            me.redirectTo('login');
            window.location.reload();
        } else {
            me.launchLoginView();
        }
    },

    onToggleNavigationSize: function () {
        var me = this,
            refs = me.getReferences(),
            navigationList = refs.navigationTreeList,
            wrapContainer = refs.mainContainerWrap,
            collapsing = !navigationList.getMicro(),
            oldWidth = collapsing ? 64 : 250;

        navigationList.setMicro(collapsing);
        wrapContainer.setWidth(oldWidth);
    },

    onMainViewRender: function () {
        const me = this,
            auth = sfw.util.Auth;

        Ext.tip.QuickTipManager.init();

        sfw.util.External.initSystemVars();

        // Binds the F1 key to the Help system
        sfw.help.bindHelpKey();

        auth.showMask();
        me.setupWorkbench();
        me.csiInit();

        auth.hideMask();
    },

    csiInit: function () {
        const me = this,
            auth = sfw.util.Auth;

        me.checkSMSState();
        auth.checkSSO();
    },

    buildSideNavigation: function () {
        const me = this,
            navigationList = me.lookup('navigationTreeList'),
            auth = sfw.util.Auth;

        sfw.util.SecuniaPackageSystem.createUIPages([
            'createPackages',
            'patchTemplate',
            //'createAgents',
            'configuration',
            //'available',
            'deployment',
            'vendorPatch',
            'packageDeployment',
            'patchPublisher',
            'patchConnections'
        ]);

        var pageArray = sfw.util.MenuFolders.buildPageArray(sfw.ui.pages);
        // Fire an event to notify listeners that the ui.create has built the page array.
        // This firing is not done at the tail end of buildMenuArray() because it's not expected
        // that buildPageArray is called at 2 separate locations like it was was in CSI6 due to Shadow Users
        // TODO - investigate potential changes now that shadow users are gone
        //sfw.events.fireEvent( "afterbuildpagearray", this.pageArray );

        var menuArray = sfw.util.MenuFolders.buildMenuArray(sfw.ui.pages);
        menuArray = me.afterBuildMenuArray(menuArray);
        me.checkMspUser(menuArray);

        var root = {
            expanded: true,
            children: menuArray
        };
        var navStore = Ext.create('Ext.data.TreeStore', {root: root});
        navigationList.setStore(navStore);
        Ext.defer(function () {
            me.applyRoleFilters(navStore);
        }, 300)

    },

    afterBuildMenuArray: function (menuArray) {
        const me = this,
            LoginDetails = sfw.util.Auth.LoginDetails,
            roleSectionKeys = Ext.Object.getKeys(LoginDetails.account.roleSections);

        const skipRoleAccessCheck = ['sfw.csiDashboard', 'sfw.csiConfiguration'];
        const parentViewAccess = ['sfw.csiPatch'];

        Ext.each(menuArray, function (node) {
            if (Ext.Array.contains(skipRoleAccessCheck, node.id)) {
                node.hidden = false;
                return;
            }

            const children = (node || {}).children || [];
            if (Ext.Array.contains(parentViewAccess, node.id) && Ext.Array.contains(roleSectionKeys, node.id)) {
                node.hidden = false;
                return;
            }

            Ext.each(children || [], function (child) {
                if (Ext.Array.contains(roleSectionKeys, child.id)) {
                    node.hidden = false;
                    child.hidden = false;
                } else {
                    child.hidden = true;
                }
            });
        });

        return menuArray;
    },

    checkMspUser: function (menuArray) {
        const me = this,
            LoginDetails = sfw.util.Auth.LoginDetails;

        if (LoginDetails.account.isMspUser()) {
            // Change the default Start Page to Host Smart Groups as the Dashboard is hidden
            sfw.configuration.ui.home = "sfw.csiHostSmartGroups";
            Ext.each( menuArray, function (menuItem) {
                // Hide the Dashboard
                if ("sfw.csiDashboard" == menuItem.id) {
                    menuItem.hidden = true;
                }
                if ("sfw.csiReports" == menuItem.id) {
                    Ext.each( menuItem.children, function ( childItem ) {
                        // Hide all child items except for the Password Pages
                        childItem.hidden = ( "sfw.csiReportingConfiguration" != childItem.id );
                    });
                }
                if ("sfw.csiConfiguration" == menuItem.id) {
                    Ext.each( menuItem.children, function ( childItem ) {
                        // Hide all child items except for the Password Pages
                        childItem.hidden = ( "sfw.csiPassword" != childItem.id );
                    });
                }
            });
        }
    },

    applyRoleFilters: function(navStore) {
        const me = this,
            LoginDetails = sfw.util.Auth.LoginDetails;

        const hasADIntegration = sfw.util.ActiveDirectorySettings.hasADIntegration();
        if (hasADIntegration) {
            const site = navStore.getNodeById('sfw.csiSitesOverview');
            if (site) {
                site.set('hidden', true);
            }
        } else {
            var siteStore = Ext.create("sfw.store.results.Sites");
            siteStore.load({
                callback: function () {
                    const site = navStore.getNodeById('sfw.csiSitesOverview');
                    var siteCount = this.getTotalCount();
                    site.set('text', Ext.String.format('{0} ({1})', 'Sites', siteCount));
                }
            });
        }

        //update Scan Path count
        const csiScanPaths = navStore.getNodeById('sfw.csiScanPaths');
        if (csiScanPaths) {
            const name = csiScanPaths.get('text');
            var scanPathCount = 0
            try {
                scanPathCount = LoginDetails.account.page.scanning.counts.scanpaths
                csiScanPaths.set('text', Ext.String.format('{0} ({1})', name, scanPathCount));
                csiScanPaths.set('originalText', name);
            } catch (e) {}
        }

        var menuText = [
            'sfw.csiConfiguration'
        ];

         if(!LoginDetails.account.isMspUser()) {
             menuText = Ext.Array.push(menuText, ['sfw.csiDashboard', 'sfw.csiLogger', 'sfw.CSIActivityLog']);
         }

        const commonMenuFilter = new Ext.util.Filter({
            filterFn: function(item) {
                return !item.data.hidden || Ext.Array.contains(menuText, item.data.id);
            }
        });

        const filters = [commonMenuFilter];
        Ext.defer(function (){
            navStore.filter(filters);
        }, 300);
    },

    checkSMSState: function () {
        const me = this,
            globals = sfw.Globals,
            LoginDetails = sfw.util.Auth.LoginDetails;

        Ext.Ajax.request({
            url: 'action=check_sms_enabled',
            success: function (answer) {
                var response = Ext.decode(answer.responseText, true);
                if (response) {
                    if (response.success === true) {
                        LoginDetails.isSMSEnabled = response.isSMSEnabled;
                        if (!LoginDetails.isSMSEnabled) {
                            //FIXME
                            //Ext.getCmp('tb_pin_sms').hide();
                        }
                        return true;
                    } else {
                        LoginDetails.isSMSEnabled = true;
                    }
                }
            }
        });
    },

    setupLogin: function (id) {
        this.launchLoginView();
    },

    setupWorkbench: function () {
        const me = this;

        const isLoggedId = sfw.util.Auth.isLoggedIn();
        if (isLoggedId) {
            //FIXME by Wemerson me.redirectTo(sfw.getApplication().getDefaultToken());
        } else {
            me.launchLoginView();
        }
    },

    launchLoginView: function () {
        const me = this,
            view = me.getView();

        view.removeAll();

        view.add({
            xtype: 'login',
            region: 'center',
            reference: 'login_ref'
        });
        //me.redirectTo('login');
    },

    launchWorkbench: function () {
        const me = this,
            view = me.getView();

        var sideBarWidth = sfw.common.LocalStorage.findValue('sidebar-width') || 220;

        view.removeAll();
        Ext.route.Router.suspend();
        view.add([
            {
                xtype: 'header-toolbar',
                height: 64,
                region: 'north',
                itemId: 'headerBar'
            },
            {
                xtype: 'maincontainerwrap',
                id: 'main-view-detail-wrap',
                reference: 'mainContainerWrap',
                // flex: 1,
                region: 'west',
                width: sideBarWidth,
                border: false,
                scrollable: 'y',
                layout: {
                    type: 'vbox',
                    align: 'stretch'
                },
                ui: 'nav',
                split: {
                    width: 5,
                    collapsible: false,
                    defaultSplitMin: 150,
                    defaultSplitMax: 300,
                    cls: 'splitter-cls',
                    style: {
                        borderRightStyle: 'solid'
                    },
                    listeners: {
                        move: function(splitter, x) {
                            sfw.common.LocalStorage.putValue('sidebar-width', x);
                        }
                    }
                },
                items: [
                    {
                        xtype: 'nav-bar',
                        /*plugins: [{
                            ptype: 'responsive'
                        }],*/
                        responsiveConfig: {
                            small: {
                                micro: true
                            },
                            medium: {
                                micro: false
                            },
                            large: {
                                micro: false
                            }

                        },
                        // Do not dismiss menu on mouseout - requires click elsewhere

                        dismissOnMouseOut: false,
                        defaults: {
                            xtype: 'customtreelistitem',
                            floaterConfig: {
                                scrollable: true
                            }
                        },
                        reference: 'navigationTreeList',
                        itemId: 'navigationTreeList'
                    },

                ]
            }, {
                xtype: 'workbench',
                region: 'center',
                reference: 'mainCardPanel',
                itemId: 'contentPanel'
            }
        ]);

        Ext.route.Router.resume();
        me.buildSideNavigation();
        sfw.util.Auth.hideMask();
        Ext.Ajax.on('requestexception', me.onRequestException);
    },

    onRequestException: function (conn, response) {
        // LoginDetails.isLoggedIn. Remove reliance on sfw.isReady.
        if (sfw.util.Auth.LoginDetails.isLoggedIn || sfw.isReady) {
            // If we are not logged in, the server responds with 400 Bad Request and 403 Forbidden in the case of an invalid CSRF Token
            response.status = parseInt(response.status, 10);
            if (response.status === 400 || response.status === 403) {
                // Stop all running tasks
                const res = Ext.decode(response.responseText, true);
                const reason = !Ext.isEmpty((res || {}).reason) ? res.reason : 'Your session has expired, please login.';

                Ext.Msg.alert("Error", reason, function () {
                    sfw.util.Globals.clearAPIcredentials();
                    document.location.replace(document.location.origin + document.location.pathname);
                    /*if (sfw.isSccmPlugin ) { //FIXME
                        logoutUser();
                    } else {
                        document.location.reload();
                    }*/
                });
            }
        }
    },

    onManualLogin: function () {
        var me = this;

        me.redirectTo(me.originalRoute, {replace: true});
    },

    onUserLoggedIn: function () {
        const me = this,
            app = sfw.getApplication();

        LoginDetails = Ext.clone(sfw.util.Auth.LoginDetails);
        // Load the state in to the state provider

        if (LoginDetails.client_state) {
            // We encoded the values before saving to the server and we have to decode them after retrieval
            Ext.iterate(LoginDetails.client_state, function (key) {
                LoginDetails.client_state[key] = Ext.state.Manager.getProvider().decodeValue(LoginDetails.client_state[key]);
            });
            Ext.state.Manager.getProvider().setState(LoginDetails.client_state);
        }
        me.launchWorkbench();
        app.appready = true;
        app.fireEvent('appready', this, LoginDetails);
    },

    onRouteChange: function (id) {
        const me = this,
            app = sfw.getApplication();

        if (app.appready === true) {
            this.setCurrentView(id);
        } else {
            console.warn('No view to add')
        }
    },

    setCurrentView: function (hashTag) {
        if (!Ext.String.startsWith(hashTag, 'sfw.csi', true)) {
            hashTag = 'sfw.csi' + hashTag;
        }
        var originalHashTag = hashTag,
            hashTag = (hashTag || '').toLowerCase();

        var LoginDetails = sfw.util.Auth.LoginDetails,
            pageView = sfw.ui.pages[originalHashTag],
            isPermitted = Ext.isEmpty(pageView) ? true : LoginDetails.account.canAccessPage(pageView.id);

        // Don't switch to the page if the required Role is missing
        if (!isPermitted) {
            Ext.Msg.show({
                title: "This page is not available",
                icon: Ext.Msg.ERROR,
                width: 400,
                msg: "Your Account does not have the permission required to access the requested page",
                buttons: Ext.Msg.OK
            });
            return false;
        }
        var me = this,
            refs = me.getReferences(),
            mainCard = refs.mainCardPanel,
            mainLayout = mainCard.getLayout(),
            navigationList = refs.navigationTreeList,
            store = navigationList.getStore(),
            node = store.findNode('routeId', hashTag) ||
                store.findNode('viewType', hashTag),
            view = (node && node.get('viewType')) || 'sfw.csiDashboard',
            isSGView = Ext.String.startsWith(view || '', 'sfw.csiSmartGroup'),
            lastView = me.lastView,
            routeId = view === "sfw.csiDashboard" ?  "Dashboard" : originalHashTag,
            existingItem = mainCard.child('component[routeId=' + routeId + ']'),
            newView;

        // Kill any previously routed window
        if (lastView && lastView.isWindow) {
            lastView.destroy();
        }

        lastView = mainLayout.getActiveItem();

        var viewConfig = {
            xtype: isSGView ? 'configuredsmartgroupsbasepanel' : view,
            routeId: originalHashTag,  // for existingItem search later
            hideMode: 'offsets'
        };

        if (isSGView) {
            hashTag = originalHashTag.substring('sfw.csi'.length, originalHashTag.length);
            viewConfig['routeId'] = hashTag;
            viewConfig['smartGroupType'] = node.get('smartGroupType');
            viewConfig['smartGroupId'] = node.get('smartGroupId');
            viewConfig['smartGroupName'] = node.get('originalText') || node.get('text');
            viewConfig['smartGroupData'] = node.get('data');

            existingItem = mainCard.child('component[routeId=' + hashTag + ']');
            if (node.get('originalText') === 'All Hosts' && !Ext.isEmpty(node.get('groupId'))) {
                viewConfig['groupId'] = node.get('groupId');
            }

            //if SG View already exist, then update config params
            if (existingItem) {
                existingItem.setGroupId(node.get('groupId'));
            }
            //unset after use
            node.set('groupId', '');
        }
        if (!existingItem) {
            newView = Ext.create(viewConfig);
        }

        if (!newView || !newView.isWindow) {
            // !newView means we have an existing view, but if the newView isWindow
            // we don't add it to the card layout.
            if (existingItem) {

                // We don't have a newView, so activate the existing view.
                if (existingItem !== lastView) {
                    mainLayout.setActiveItem(existingItem);
                }
                newView = existingItem;
            } else {

                // newView is set (did not exist already), so add it and make it the
                // activeItem.
                Ext.suspendLayouts();
                mainLayout.setActiveItem(mainCard.add(newView));
                Ext.resumeLayouts(true);
            }
        }

        navigationList.setSelection(node);

        if (newView.isFocusable(true)) {
            newView.focus();
        }

        me.lastView = newView;
        if (sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION) {
            sfw.googleAnalytics.googleAnalytics.send(newView.title, LoginDetails.account.settings.cst_id);
        }

    },

    onUserLogout: function () {
        const me = this,
            globals = sfw.Globals,
            view = me.getView();

        Ext.Msg.show({
            title: 'Logout?',
            msg: 'Are you sure you want to logout from Software Vulnerability Manager?',
            width: 450,
            buttons: Ext.Msg.YESNO,
            fn: function (button) {
                if (button === "yes") {
                    const mask = new Ext.LoadMask({target: view, msg: 'Logging out..'});
                    mask.show();
                    // Issue remote logout request
                    Ext.Ajax.request({
                        url: 'action=logout',
                        method: 'POST',
                        success: function (data) {
                            sfw.util.Debug.log('Account logout complete');
                            // Clear the API credentials
                            globals.clearAPIcredentials();
                            if (document.cookie) {
                                // Erase everything in the cookie
                                document.cookie = 'uid=; expires=' + encodeURIComponent(new Date(0).toUTCString());
                            }
                            mask.hide();
                            me.logout();
                        },
                        failure: function () {
                            // @todo: maybe prompt the user about the failure
                            mask.hide();
                            sfw.util.Debug.log('Account logout failed');
                            // log the action on the server
                            sfw.util.ActivityLog.log(2, 1, "Username: " + sfw.util.Auth.LoginDetails.loginAccountUsername, 29, true);
                        }
                    });
                }
            },
            icon: Ext.MessageBox.QUESTION
        });
    },

    navigateToHelp: function () {
        sfw.help.openHelp();
    },

    getTabRoute: function (tab) {
        return tab.routeId;
    },

    onBadRoute: function () {
        var app = sfw.app.getApplication();
        this.redirectTo(app.getDefaultToken());
    },

    onTabChange: function (mainView, newTab) {
        var me = this,
            route = newTab.routeId;


        if (route) {
            if (Ext.String.startsWith(route, 'sfw.csi', true)) {
                route = route.substring('sfw.csi'.length, route.length);
            }

            me.redirectTo(route);
        }
    },

    onUnmatchedRoute: function (token) {
        if (token) {
            this.onBadRoute();
        }
    }
});