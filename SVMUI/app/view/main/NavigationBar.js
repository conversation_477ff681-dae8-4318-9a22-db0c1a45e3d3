Ext.define('sfw.view.main.NavigationBar', {
    extend: 'Ext.list.Tree',
    alias: 'widget.nav-bar',
    ui: 'nav',
    controller: 'nav-bar',
    viewModel: {},

    id: 'mainCSImenu',
    useArrows: true,
    indent: 20,
    clearOnLoad: false,
    // cls: 'side-nav-bar',
    //scrollable: true,
    expanderFirst: false,
    expanderOnly: false,
    // highlightPath: true,

    listeners: {
        itemclick: 'onMenuItemClick',
        selectionchange: 'onNavigationTreeSelectionChange'
    }

});