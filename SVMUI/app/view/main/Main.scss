@include extjs-button-small-ui(
        $ui: 'header',
        $color: #999,
        $glyph-color: #919191,
        $background-color: transparent,
        $border-width: 0
);

$treelist-nav-ui: (
        padding: 0 10px, //0 10px
        background-color: $panel-navigation-background-color,
        toolstrip-background-color: $panel-navigation-background-color,
        tool-float-indicator-color: $panel-header-background-color,
        tool-float-indicator-width: 5px,
        tool-indicator-selected-color: $panel-header-background-color,
        tool-indicator-selected-width: 5px,
        tool-selected-background-color: mix(white, $panel-navigation-background-color, 10%),
        item-expanded-background-color: mix(white, $panel-navigation-background-color, 10%),
        item-line-height: $panel-navigation-item-line-height,
        row-over-background-color: mix(white, $panel-navigation-background-color, 5%),
        row-selected-background-color: mix(white, $panel-navigation-background-color, 10%),
        item-icon-color: $panel-navigation-item-text-color,
        item-expander-color: #f1f1f1,
        item-text-color: #f1f1f1,
        item-icon-over-color: #f1f1f1,
        item-expander-over-color: #f1f1f1,
        item-text-over-color: mix(white, $panel-navigation-item-text-color, 50%),
  //item-text-font-size: 14px,
  //item-icon-font-size: 18px,
        item-icon-width: 44px,
  // item-expander-font-size: 16px,
  //item-expander-width: 24px,
        row-indicator-width: 5px,
        row-indicator-selected-color: $panel-header-background-color,
        row-indicator-selected-over-color: lighten($panel-header-background-color, 7%),
        row-indicator-over-color: transparent
);
.x-container-nav {
  background-color: $panel-navigation-background-color,
}


.StringSuccess {
  color: green;
}

.StringError {
  color: red;
}

.StringWarning {
  color: orange;
}

.cvssMedium {
  background-color: #f9a009;
  color: black;
  padding: 3px;
}

.cvssHigh {
  background-color: #df3d03;
  color: white;
  padding: 3px;
}

.cvssCritical {
  background-color: #cc0500;
  color: white;
  padding: 3px;
}

.cvssLow {
  background-color: #ffcb0d;
  color: black;
  padding: 3px;
}

.AgentSuccess {
  color: #00BF12;
}

.AgentWarning {
  color: #FFDB00;
}

.AgentError {
  color: #CC0000;
}

.LicenseError {
  color: red;
}

.ProductNameAutoDeploy {
  color: blue;
}

.ProductNameHasDownload{
  color: black;
}

.ProductNameIsSilentParam{
  color: grey;
}

.ProductTemplateButton {
  margin-left:90%;
  background-color:#277bc7;
  border-style:solid;
  border-width:1px;
  padding:3px 3px 3px 3px;
  border-radius:0;
  border-color:#2472b9;
  color:#FFFFFF
}


//.main-container-wrap {
//  overflow: hidden scroll !important;
//}

