Ext.define('sfw.view.export.Export', {
    extend: 'Ext.Button',
    xtype: 'exportButton',
    text: 'Export',
    ui: 'export',
    menu: {
        items: [{
            text: 'Page',
            listeners: {
                click: 'buttonHandler'
            }
        }, {
            text: 'All Pages',
            listeners: {
                click: 'buttonHandler'
            }
        }, {
            text: 'Schedule Report',
            listeners: {
                click: 'buttonHandler',
                afterrender: function () {
                    if (sfw.util.Auth.LoginDetails.account.isMspUser()) {
                        this.hide();
                    }
                }
            }
        }]
    },

    controller: {
        buttonHandler: function (btn) {
            try {
                globals = sfw.Globals;

                const view = btn.up('grid');

                const store = view.store;

                var method;
                var columnheaders = [];
                var columns = [];


                Ext.each(view.down('headercontainer').getGridColumns(), function (col, index) {
                    if (!col.hidden) {
                        if(col.text){
                            columnheaders.push(col.text);
                        }
                        if(col.dataIndex){
                            columns.push(col.dataIndex);
                        }
                    }
                });

                var params = Ext.apply(
                    {
                        format: 'csv',
                        columns: Ext.util.JSON.encode(columns),
                        columnHeaders: Ext.util.JSON.encode(columnheaders),
                        limit: store.lastOptions.limit,
                        start: store.lastOptions.start,
                        page: store.lastOptions.page
                    }
                );

                var sortersData = [];
                if(store.lastOptions.sorters){
                    var sortersLength = store.sorters.items;
                    for(i=0;i<sortersLength.length;i++){
                        sorterParams = {}
                        sorterParams.field = store.sorters.items[i].config.property;
                        sorterParams.direction = store.sorters.items[i].config.direction;
                        sortersData.push(sorterParams);
                    }
                }

                if(sortersData && sortersData.length > 0){
                    params.sorters = Ext.util.JSON.encode(sortersData);
                }

                params = Ext.apply(
                    params,
                    store.proxy.extraParams
                );

                if (btn.config.text == 'All Pages' || btn.config.text == 'Schedule Report') {
                    if (typeof params.start !== 'undefined') {
                        delete params.start;
                    }
                    if (typeof params.limit !== 'undefined') {
                        delete params.limit;
                    }

                    if (typeof params.page !== 'undefined') {
                        delete params.page;
                    }
                }

                if (btn.config.text == 'Schedule Report') {
                    if (view.id == 'dbconsoleresultgrid') {

                        var scheduleReportWindow = Ext.create("sfw.view.commonpopupwindows.ScheduleReportSetup",
                            {
                                listeners: {
                                    afterrender: function () {
                                        scheduleReportWindow.down('#table').setValue(store.proxy.extraParams.table);
                                    }
                                }
                            });
                        scheduleReportWindow.show();

                        return;
                    }
                    store.proxy.url = globals.apiPath() + store.proxy.url;
                    var url = (store.proxy.url.split('?')[1]).split('&');
                    for (var i = url.length - 1; i >= 0; i--) {
                        var keyValue = url[i].split('=');
                        if (keyValue[0] != 'uid') {
                            params[keyValue[0]] = keyValue[1];
                        }
                    }
                    ;
                    params['format'] = 'csv';
                    params['scheduled'] = 'true';
                    var queryString = encodeURIComponent(Ext.urlEncode(params));

                    var scheduleReportWindow = Ext.create("sfw.view.commonpopupwindows.ScheduleReportSetup",
                        {
                            listeners: {
                                afterrender: function () {
                                    var titleText = view.title;
                                    if ( ['Rules', 'Affected Hosts'].includes(view.title)){
                                        titleText = 'Database Cleanup';
                                    }
                                    scheduleReportWindow.down('#table').setValue(titleText);
                                    scheduleReportWindow.down('#queryString').setValue(queryString);
                                }
                            }
                        });
                    scheduleReportWindow.show();

                    return;
                }

                var downloadLink;
                if (typeof view.usePost !== 'undefined' && view.usePost === true) {
                    // If the grid is configured to work with post, use form to submit the parameters in a new page
                    // Currently it is intended for database console page, since that page can have a big request, a lot of
                    // additional parameters and the query itself.
                    // Standard limit on size of GET request is 8Kb.
                    var hiddenField, key;
                    // Instead of ExtJS form elements, we are using pure JavaScript, since we don't need ExtJS.
                    var form = document.createElement("form");
                    form.setAttribute("method", "POST");
                    downloadLink = globals.apiPath() + view.store.proxy.url + '&';

                    for (key in params) {
                        if (params.hasOwnProperty(key)) {
                            hiddenField = document.createElement("input");
                            hiddenField.setAttribute("type", "hidden");
                            hiddenField.setAttribute("name", key);
                            hiddenField.setAttribute("value", params[key]);

                            form.appendChild(hiddenField);
                        }
                    }
                    form.setAttribute("action", downloadLink);
                    form.setAttribute("target", "_blank");

                    document.body.appendChild(form);
                    form.submit();
                } else {
                    downloadLink = globals.apiPath() + view.store.proxy.url + '&' + Ext.urlEncode(params);
                    window.open(downloadLink);
                }
            } catch (ex) {
                sfw.util.Debug.trigger( ex, 'Error during data export.' );
            }
        }
    }

});


