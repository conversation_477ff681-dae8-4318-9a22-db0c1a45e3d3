Ext.define('sfw.view.patching.Spspatching',{
    extend:'Ext.panel.Panel',
    xtype:'sfw.csiCreatePatch',

    itemId: 'spsPatching',
    reference:'spspatch',

    title:'Flexera Package System (SPS)',
    border: 1,
    cls: 'shadow',

    requires:[
        'sfw.view.patching.UnGroupedGrid',
        'sfw.view.patching.SpsGroupedGrid',
        'sfw.view.patching.SpsPatchingModel'
    ],

    controller:'spspatching',

    viewModel:'spspatching',

    listeners:{
        afterrender:'refreshSpsGrid',
    },

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    items:[{
        xtype: 'sfw.csiSpsUngroupedView',
    },{
        xtype: 'sfw.csiSpsGroupedView',
    }]
});