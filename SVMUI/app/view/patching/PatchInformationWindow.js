Ext.define('sfw.view.patching.PatchInformationWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.patchInformationWindow',
    scrollable: "vertical",
    modal: true,
    resizable: true,
    width:500,

    viewModel: {
        data: {
            patchWindowData:null,
            packageType:null
        },

        formulas:{
            fileSize:function(get){
                var bytes = get('patchWindowData.file_size');
                var decimals = 2;
                if (parseInt(bytes, 10) === 0) return '0 Bytes';
                var k = 1024,
                    dm = decimals <= 0 ? 0 : decimals || 2,
                    sizes = ['Bytes', 'KB', 'MB', 'GB'],
                    i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            },
            dateModified:function(get){
                return sfw.Default.gridRenderUTCDateInLocaltime(get('patchWindowData.date_modified'));
            },
            deploymentReady:function(get){
                return get('patchWindowData.auto_deploy') == 1 ? 'Yes':'No' ;
            },
            fileNameExtracted:function(get){
                return get('patchWindowData.filename_extracted') == '' ? 'No': get('patchWindowData.filename_extracted') ;
            },
            signedValue:function(get){
                var val = get('patchWindowData.signed');
                return val == '1' ? 'Yes' : 'No';
            },
            packageType:function(get){
                return 'Legacy';
            }
        }
    },

    bind:{
        title:'{patchWindowData.product_name}'
    },

    defaults: {
        bodyStyle: 'padding:20px'
    },

    items: [{
        flex: 3,
        layout: {
            type: 'table',
            columns: 2
        },
        defaults: {
            style: {
                'padding': '5px 0',
                'line-height': '20px'
            }
        },
        items: [{
            html: 'Product Name:',
            width: 150
        }, {
            bind:{
                html: '{patchWindowData.product_name}',
            },
        }, {
            html:'Vendor:',
            width: 150
        },{
            bind:{
                html: '{patchWindowData.manufacturer}',
            },
        },{
            html: 'Patched version:',
            width: 150
        }, {
            bind:{
                html: '{patchWindowData.version}',
            },
        }, {
            html:'Architecture:',
            width: 150
        }, {
            bind:{
                html: '{patchWindowData.platform}',
            },
        }, {
            html:'Extracted Filename:',
            width: 150
        }, {
            bind:{
                html: '{fileNameExtracted}',
            },
        }, {
            html: 'Digitally Signed:',
            width: 150
        }, {
            bind: {
                html: '{signedValue}',
            },
        }, {
            html: 'Silent parameters:',
            width: 150,
            tdAttrs: {
                style: 'line-height: 25px;'
            }
        }, {
            bind:{
                html: '{patchWindowData.silent_switches}',
            },
        }, {
            html:'Uninstall parameters:',
            width: 150,
            tdAttrs: {
                style: 'line-height: 25px;'
            }
        },{
            bind:{
                html: '{patchWindowData.uninstall_string}',
            },
        },{
            html: 'File size:',
            width: 150
        }, {
            bind:{
                html:'{fileSize}'
            },
        }, {
            html:'Date modified:',
            width: 150
        },{
            bind:{
                html:'{dateModified}',
            },
        },{
            html:'Deployment ready:',
            width: 150
        },{
            bind:{
                html:'{deploymentReady}'
            },
        },{
            html:'Type:',
            width: 150
        },{
            bind:{
                html:'{packageType}'
            },
        },{
            html:'Download:',
            width: 150
        },{
            xtype: 'component',
            itemId: 'downloadLinkComponent',
            width: 300,
            style: {
                'padding': '5px 0',
                'line-height': '20px'
            },
            listeners: {
                afterrender: function(component) {
                    var window = component.up('window');
                    var vm = window.getViewModel();

                    // Function to update download link
                    var updateDownloadLink = function() {
                        try {
                            var patchData = vm.get('patchWindowData');
                            if (!patchData) {
                                component.setHtml('Loading...');
                                return;
                            }

                            if (false === sfw.util.Auth.LoginDetails.isVPMModuleEnabled) {
                                component.setHtml('<a href="#" onClick="Ext.Msg.alert( \'Vendor Patch Module\', \'Vendor Patch Module is not enabled, please contact to support.\' );">Download </a>');
                                return;
                            }

                            if (!patchData.vpm_id) {
                                component.setHtml('No download available');
                                return;
                            }

                            // Check for multi-setup packages
                            if ((patchData.additional_packages && Array.isArray(patchData.additional_packages) && patchData.additional_packages.length > 0) || patchData.is_multi_setup === true) {
                                if (!patchData.additional_packages || !Array.isArray(patchData.additional_packages) || patchData.additional_packages.length === 0) {
                                    component.setHtml('Multi-setup package detected but no files available');
                                    return;
                                }
                                // Sort filenames in ascending order
                                patchData.additional_packages.sort(function(a, b) {
                                    return a.filename.localeCompare(b.filename);
                                });
                                var links = '';
                                for (var i = 0; i < patchData.additional_packages.length; i++) {
                                    var file = patchData.additional_packages[i];
                                    var value = sfw.util.CommonConstants.patchDownloadURLPackage + '&token=F8859A92-C9C5-43F2-95D7-9A3E19FEC0B6&vpm_id=' + patchData.vpm_id + 'multi_package_id=' + file.multi_package_id + '&cstid=' + sfw.util.Auth.LoginDetails.account.settings['cst_id'];
                                    links += '<div style="margin-bottom: 5px; padding-left:0;">'
                                        + '<a href="#" style="display:inline-block;" onclick="sfw.Default.externalURL(\'' + value.replace("'", "\\'") + '\')">' + file.filename + '</a>'
                                        + '</div>';
                                }
                                component.setHtml(links);
                            } else {
                                // Single package
                                var value = sfw.util.CommonConstants.patchDownloadURLPackage + '&token=F8859A92-C9C5-43F2-95D7-9A3E19FEC0B6&vpm_id=' + patchData.vpm_id + '&cstid=' + sfw.util.Auth.LoginDetails.account.settings['cst_id'];
                                var result = '<a href="#" onclick="sfw.Default.externalURL(\'' + value.replace("'", "\\'") + '\')">' + 'Download' + '</a>';
                                component.setHtml(result);
                            }

                        } catch (error) {
                            component.setHtml('Error: ' + error.message);
                        }
                    };

                    // Store reference for later use
                    component.updateDownloadLink = updateDownloadLink;

                    // Listen for patchWindowData changes
                    vm.bind('{patchWindowData}', function(data) {
                        if (data) {
                            // Small delay to ensure all data is loaded
                            Ext.defer(updateDownloadLink, 100);
                        }
                    });

                    // Initial update
                    updateDownloadLink();
                }
            }
        }]
    }]
});