Ext.define('sfw.view.patching.VendorPatchModuleController', {
	extend: 'Ext.app.ViewController',
	alias: 'controller.vendorpatchmodule',
	xtype: 'sfw.vpmcontroller',

	init:function(){
		downloadOnly = true;
		readyDeploy = true;
		macdownload = false;
		vpmHideMicrosoftProducts = false;
		vpmProductsPatched = false;
		myEnvironmentProducts = true;
		notMyEnvironmentProducts = true;
		langSelected = '';
	},

	constructor: function (config) {
		this.callParent(arguments);

		this.onSubscribeToPatchClicked = Ext.bind(this.onSubscribeToPatchClicked, this);
		this.onItemContextMenu = Ext.bind(this.onItemContextMenu, this);
	},

	checkAutoDeploy: function (value, metaData, record, rowIndex, colIndex, store) {

		var value = Ext.Number.parseInt(value);
		if (value == 1) {
			return 'Yes';
		} else {
			return 'No';
		}
	},

	checkFileSize: function (value, metaData, record, rowIndex, colIndex, store) {

		var bytes = value;
		var decimals = 2;
		if (parseInt(bytes, 10) === 0) return '0 Bytes';
		var k = 1024,
			dm = decimals <= 0 ? 0 : decimals || 2,
			sizes = ['Bytes', 'KB', 'MB', 'GB'],
			i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
	},

	checkSubscriptionStatus: function (value, metaData, record, rowIndex, colIndex, store) {
		if (value == 1) {
			if (record.data.auto_deploy) {
				return 'Yes';
			} else {
				return '<font color="red">Yes</font>';
			}
		} else if (value == 2) {
			return 'Paused';
		} else {
			return 'No';
		}
	},

	configureViewVPM: function (event, target, options) {

		var configureView = Ext.create("sfw.view.patching.VendorPatchConfigureView",
			{
				listeners: {
					afterrender: function () {
                        const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        const selectLanguges = configureView.down('#selectLanguages');
                        const selectLanguageSelector = localItemSelector.createLanguageSelector();
                        selectLanguges.add(selectLanguageSelector);
						selectLanguageSelector.down('#multiSelectPagination').hide();
                        sfw.Default.getSelectorValues({
                            action: 'vpm_package',
                            which: 'getSelLangs',
                        }, selectLanguageSelector);
					}
				}
			});
		configureView.show();

	},



	renderState: function (state) {
		if (state == 1) {
			return "<font color='" + sfw.Default.returnStatusColor(1) + "'>Insecure</font>";
		} else if (state == 2) {
			return "<font color='" + sfw.Default.returnStatusColor(2) + "'>End-Of-Life</font>";
		} else if (state == 0) {
			return "<font color='" + sfw.Default.returnStatusColor(3) + "'>Secure</font>";
		}
	},

	renderLastScanDate: function (date) {
		var dateDiff = sfw.Util.differenceBetweenDates(sfw.Util.dateCreate(date, true), sfw.Util.dateCreate(), 0, {excludeEmpty: true});
		dateDiff = dateDiff.replace("-", "");
		if ("" === dateDiff) {
			dateDiff = "seconds";
		}
		dateDiff = dateDiff + " ago";
		return dateDiff;
	},

	onItemContextMenu: function (grid, record, item, index, e) {
		var me = this,
			selection = grid.getSelection(),
			product_name = record.get("product_name"),
			template_name = record.get("template_name"),
			patched_version = record.get("patched_version"),
			subscribeMenuItemText = '';

		var disabled = LoginDetails.isReadOnly || !LoginDetails.isVPMModuleEnabled;

		var disabled = LoginDetails.isReadOnly || !LoginDetails.isVPMModuleEnabled;

		var disablePause = false;
		var disableResume = false;
		if(selection.length == 1){
			disablePause = LoginDetails.isReadOnly || !LoginDetails.isVPMModuleEnabled || record.data.subscription_status == 0 || record.data.subscription_status == 2;
			disableResume = LoginDetails.isReadOnly || !LoginDetails.isVPMModuleEnabled || record.data.subscription_status == 0 || record.data.subscription_status == 1;
		}else if(selection.length > 1){
			var unSubscribedpackages = true;
			var packagesData = [];
			for ( var i=0; i < selection.length; ++i ) {
				packagesData.push(parseInt(selection[i].data.subscription_status));
				if (selection[i].data.subscription_status != 0) {
					unSubscribedpackages = false;
				}
			}

			var subscribed = packagesData.includes(1);
			var unSubscribed = packagesData.includes(0);
			var paused = packagesData.includes(2);


			if((unSubscribedpackages) || (subscribed && unSubscribed) || (subscribed && paused) || (subscribed && unSubscribed && paused) || (unSubscribed && paused)){
				disablePause = true;
				disableResume = true;
			}

			if(subscribed && !unSubscribed && !paused){
				disablePause = false;
				disableResume = true;
			}

			if(paused && !subscribed && !unSubscribed){
				disablePause = true;
				disableResume = false;
			}
		}

		var contextMenu = Ext.create("Ext.menu.Menu", {
			width: 200,
			plain: true,
			items: []
		});

		var subscriptionButton = {
			text: 'Subscribe to Patch',
			hidden: false,
			disabled: disabled,
			handler: Ext.bind(this.onSubscribeToPatchClicked, me)
		};

		var pauseButton = {
			text: 'Pause Subscription',
			hidden: false,
			disabled: disablePause,
			handler: Ext.bind(this.onPauseSubscription, me)
		};

		var resumeButton = {
			text: 'Resume Subscription',
			hidden: false,
			disabled: disableResume,
			handler: Ext.bind(this.onResumeSubscription, me)
		};

		var singlePatchButton = {
			text: 'Publish with QuickPatch',
			hidden: false,
			disabled: disabled,
			handler: Ext.bind(this.handleSinglePatchInfo, me)
		};

		// Support multiple selection for subscription
		if ( 1 < selection.length ) {
			if((subscribed && paused) || (unSubscribed && paused) || (paused)){
				subscriptionButton['disabled'] = true;
			}
			contextMenu.add(subscriptionButton);
			contextMenu.add(pauseButton);
			contextMenu.add(resumeButton);
			e.stopEvent();
			contextMenu.showAt(e.getXY());
			return;
		} else if((1 === selection.length) && ((1 == selection[0].data.subscription_status)) || (2 == selection[0].data.subscription_status)) {
			//Change button text if package is already has been subscribed
			subscriptionButton['text'] = "Edit Subscription"
		}

		var platform = record.get('platform');
		var packageType = record.get('type');
		var updateDisabled = disabled || ((platform.indexOf("Mac") == 0)|| (packageType == 'MSU'));// only in Mac platform auto deploy hasbeen disabled
		var productId = record.get('product_id');
		var vpmId = record.get('vpm_id');
		var show_installation = true;
		var viewInstallationsCount = record.get('total');
		if((productId != '') && (viewInstallationsCount != '')){
			show_installation = false;
		}

		if (!Ext.isIE) { //
			var updateMenu = {
				text: 'Create Update Package'
				//,disabled: updateDisabled
				,listeners: {
					click: {fn: function(){
							sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE_PATCHING)
						}, extra: record}
				}
			};
			contextMenu.add(updateMenu);
		}
		contextMenu.add( '-' );
		contextMenu.add({
			text: 'View Installations'
			,disabled: show_installation
			,hidden: false
			,listeners: {
				click: {fn: sfw.Default.getViewInstallationsVPM, extra: record}
			}
		});

		contextMenu.add({
			text: 'Patch Information'
			,disabled: disabled
			,hidden: false
			,listeners: {
				click: {fn: this.patchInformation, extra: record}
			}

		});
		//Add subscription button to the context menu along with other options for single row selecton
		contextMenu.add( '-' );
		contextMenu.add(subscriptionButton);
		contextMenu.add(pauseButton);
		contextMenu.add(resumeButton);
		if ( ! sfw.isSccmPlugin && ! sfw.isSccmScheduler ) {
			contextMenu.add(singlePatchButton);
		}

		e.stopEvent();
		contextMenu.showAt(e.getXY());
	},

	handleSinglePatchInfo: function (menuItem) {
		const me = this,
			grid = me.getView(),
			selection = grid.getSelection();
		var windowTitle = 'Publish with QuickPatch';
		var uneditableChosen = 0;
		if (0 < selection.length) {
			// Add logic to support that not all row's are able to subscribe
			for (var i = 0; i < selection.length; ++i) {
				if (!parseInt(selection[i].data.auto_deploy, 10)) {
					uneditableChosen = true;
					break;
				}
			}
		}
		if (uneditableChosen) {
			Ext.Msg.alert("Not able to Send to Patch Publisher", "Feature limited to only deployment ready packages");
			return;
		}

		if (1 === selection.length) {
			windowTitle += ' - ' + selection[0].data.product_name;
			var platform = selection[0].data.platform;
			var quickpatchwindow = Ext.create("sfw.view.common.PublishQuickPatch", {
				selection: selection
			});
			if (platform.indexOf("Mac") == 0) {
				var connectionstore = quickpatchwindow.down('#singlePatchConnections').getStore();
				connectionstore.getProxy().setExtraParams({
					'type': 2

				});
			}
			quickpatchwindow.show();
			if(selection[0].data.platform.indexOf('Mac') == -1){
				quickpatchwindow.down('#macNoteQuickPatch').hide();
			}
			quickpatchwindow.setTitle(windowTitle);
		} else {
			var quickpatchwindow = Ext.create("sfw.view.common.PublishQuickPatch", {selection: selection, mode: 'vpm'});
			quickpatchwindow.show();
			quickpatchwindow.down('#macNoteQuickPatch').hide();
			quickpatchwindow.setTitle(windowTitle);
		}
	},

	onSubscribeToPatchClicked: function (menuItem) {
		const me = this,
			grid = me.getView(),
			selection = grid.getSelection();

		var uneditableChosen = 0;
		var differentChosen = 0

		if ( 0 < selection.length ) {
			// Add logic to support that not all row's are able to subscribe
			for ( var i=0; i < selection.length; ++i ) {
				if (!parseInt(selection[i].data.auto_deploy, 10)) {
					uneditableChosen = true;
					break;
				}
			}
		}
		if (uneditableChosen) {
			Ext.Msg.alert("Not able to subscribe", "Subscription limited to only deployment ready packages");
			return;
		}
		if (0 < selection.length) {
			// Add logic to not able to subscribe if multiple packages selected are of different platform
			for (var i = 0; i < selection.length; ++i) {
				var plaform1 = selection[0].data.platform;
				var currentPlatform = selection[i].data.platform;

				if (plaform1.indexOf("Mac") != currentPlatform.indexOf("Mac")) {
					differentChosen = true;
					break;
				}
			}
		}

		if (differentChosen) {
			Ext.Msg.alert("Not able to subscribe", "Please select either Windows or Mac package to do multiple subscription");
			return;
		}


		if ((1 === selection.length) && ((1 == selection[0].data.subscription_status) || (2 == selection[0].data.subscription_status)))
		{
			var vpm_id = selection[0].data.vpm_id;
			var platform = selection[0].data.platform;

			Ext.Ajax.request({
				url: 'action=vpm_package&which=get_subscription_details&vpm_id=' + vpm_id,
				method: 'GET',
				callback: function(options, success, response) {
					if (success) {
						var remoteData = Ext.decode(response.responseText, true) || {};
						remoteData = remoteData.data;
						if (!Ext.isEmpty(remoteData.rows)) {
							var editSubscription = Ext.create("sfw.view.common.ConfigureSubscription", {
								selection: selection,
								mode: 'vpm',
								record: remoteData.rows[0]
							});
							if (platform.indexOf("Mac") == 0) {
								var connectionstore = editSubscription.down('#singlePatchConnections').getStore();
								connectionstore.getProxy().setExtraParams({
									'type': 2

								});
							}
							editSubscription.show();
							if(selection[0].data.platform.indexOf('Mac') == -1){
								editSubscription.down('#macNote').hide();
							}else{
								editSubscription.down('#macNote').show();
							}
							Ext.ComponentQuery.query("#subscriptionSave")[0].setDisabled(false);
							var connection_ids = [];
							var connections_intune = [];
							for(i=0; i < remoteData.rows.length; i++){
								connection_ids.push(remoteData.rows[i].connection_id);
								if(remoteData.rows[i].connection_type == 2){
									connections_intune.push(remoteData.rows[i].connection_id)
								}
							}
							if (!Ext.isEmpty(connections_intune)) {
								editSubscription.down('#manageAssignments').setHidden(false);
							} else {
								editSubscription.down('#manageAssignments').setHidden(true);
							}
							editSubscription.down('#singlePatchConnections').setValue(connection_ids);

							if (remoteData.rows[0].always_trigger === '1'){
								editSubscription.down('#alwaysPublish').setValue(true);
							} else {
								editSubscription.down('#onlyPublish').setValue(true);
							}
							if ( remoteData.rows[0].vuln_cvss3_score !== ""){
								editSubscription.down('#cvss3ScoreItemId').setValue(remoteData.rows[0].vuln_cvss3_score);
							}
							if ( remoteData.rows[0].vuln_criticality !== ""){
								editSubscription.down('#criticalityItemId').setValue(remoteData.rows[0].vuln_criticality);
							}
							if ( remoteData.rows[0].vuln_threat_score !== ""){
								editSubscription.down('#threatScoreItemId').setValue(remoteData.rows[0].vuln_threat_score);
							}

							if ( remoteData.rows[0].version !== ""){
								editSubscription.down('#versionFieldId').setValue(remoteData.rows[0].version);
							}
							editSubscription.down('#triggerSubscriptionCurrentVersion').setValue((remoteData.rows[0].publish_asap === '1')? true : false);

							var filteredData = [];
							for(i=0; i<remoteData.rows.length ; i++){
								for(j=0; j< remoteData.rows[i].assignment_connections.length; j++){
									remoteData.rows[i].assignment_connections[j].availability = "As Soon as Possible";
									filteredData.push(remoteData.rows[i].assignment_connections[j])
								}
							}
							editSubscription.getViewModel().set('assignmentsData',filteredData);

						}
					}
				},
				scope: me
			});
		} else {
			var platform = selection[0].data.platform;
			editSubscription = Ext.create("sfw.view.common.ConfigureSubscription", { selection: selection, mode: 'vpm' });
			if (platform.indexOf("Mac") == 0) {
				var connectionstore = editSubscription.down('#singlePatchConnections').getStore();
				connectionstore.getProxy().setExtraParams({
					'type': 2

				});
			}
			editSubscription.show();
			if (selection.length >1){
				editSubscription.down('#silentParamFieldCtrId').hide();
				editSubscription.down('#macNote').hide();
			}
			if(selection.length == 1){
				if(selection[0].data.platform.indexOf('Mac') == -1){
					editSubscription.down('#macNote').hide();
				}
			}
			editSubscription.down('#versionFieldId').setValue(selection[0].data.version);

		}
	},

	onPauseSubscription:function(){
		const me = this;
		grid = me.getView();
		selection = grid.getSelection();

		var data = [];

		for ( var i=0; i < selection.length; ++i ) {
			if (selection[i].data.subscription_status == 1) {
				var pauserecords = {};
				pauserecords.vpm_id = selection[i].data.vpm_id;
				pauserecords.product_name = selection[i].data.product_name;
				data.push(pauserecords);
			}
		}

		if(data.length <= 0){
			Ext.Msg.alert("Note", 'Please select atleast one subscribed package.');
			return;
		}

		this.pauseResumeSubscription(data,2)

	},

	onResumeSubscription:function(){
		const me = this;
		grid = me.getView();
		selection = grid.getSelection();

		var data = [];

		for ( var i=0; i < selection.length; ++i ) {
			if (selection[i].data.subscription_status == 2) {
				var resumerecords = {};
				resumerecords.vpm_id = selection[i].data.vpm_id;
				resumerecords.product_name = selection[i].data.product_name;
				data.push(resumerecords);
			}
		}

		if(data.length <= 0){
			Ext.Msg.alert("Note", 'Please select atleast one paused subscription.');
			return;
		}

		this.pauseResumeSubscription(data,1)
	},

	pauseResumeSubscription:function(data,status){
		var paramData = {};
		if(status == 2){
			subscription = 'pause_subscription';
			paramData['pause_data'] = JSON.stringify(data);
		}else{
			var subscription = 'resume_subscription';
			paramData['resume_data'] = JSON.stringify(data);
		}

		paramData['status'] = status;

		Ext.Ajax.request({
			url: 'action=vpm_package&which='+subscription,
			method: 'POST',
			dataType: 'json',
			params: paramData,
			success: function (response, opts) {
				var response = Ext.decode(response.responseText, true);
				if (response.success) {
					Ext.Msg.alert("Success", response.msg);
					var currentView = sfw.common.SfwAccessor.getCurrentView();
					if (currentView) {
						currentView.getStore().load();
					}
				}else {
					Ext.Msg.alert("Failed" , "Failed to updated subscription status.");
				}
			},
			failure: function (response, opts) {
				Ext.Msg.alert('Error', 'Unexpected Error Occured');
			}
		});
	},

	/*dblclickevent: function (grid,record,event) {
		var product_name = record.data.product_name;
		var _this = this;
		var viewInstallationsWindow = Ext.create("sfw.view.commonpopupwindows.ViewInstallations",
			{
				listeners: {
					afterrender: function () {

					}
				}
			});
		var overviewStore = Ext.create("sfw.store.common.InstallationOverview");
		overviewStore.load({
			params: {},
			callback(record, operation, success) {

				var missingKBsFound = record[0].data.missingKBsFound;
				var uniq_totalcount_mskbs = record[0].data.uniq_totalcount_mskbs;

				if (missingKBsFound) {
					missingMsKBsHtml = "<table width='280'><tr><td colspan='2' class='ReportTable ReportTableHeading'><b>Installations Missing Microsoft Updates</b></td></tr>";
					for (var missing_kb_update in uniq_totalcount_mskbs) {
						if (uniq_totalcount_mskbs.hasOwnProperty(missing_kb_update) && typeof uniq_totalcount_mskbs[missing_kb_update] !== 'function') {
							missingMsKBsHtml += "<tr><td class='ReportTable ReportTableName'>" + missing_kb_update + "</td><td class='ReportTable Number' align='right'>" + uniq_totalcount_mskbs[missing_kb_update] + "</td></tr>";
						}
					}
					missingMsKBsHtml += "</table>"
				}
				viewInstallationsWindow.getViewModel().set('kbtable', missingMsKBsHtml)

				var piedata = _this.setInstallationPieData(record[0].data.countPatched, record[0].data.countEndOfLife, record[0].data.countInsecure)
				viewInstallationsWindow.getViewModel().set('installationdata', record[0].data)
				var store = viewInstallationsWindow.getViewModel().getStore('overviewpiestore');

				store.loadData(piedata);

			}
		});

		viewInstallationsWindow.show();
		viewInstallationsWindow.setTitle(product_name);
	},

	setInstallationPieData: function (secure, endoflife, insecure) {

		var chartData = [];
		var securescore = {};
		var endoflifescore = {};
		var insecurescore = {};
		var total = parseFloat(secure) + parseFloat(endoflife) + parseFloat(insecure);
		securescore.label = 'Secure' + '[' + secure + ']';
		securescore.data = Math.round((secure / total) * 100);

		endoflifescore.label = 'End-Of-Life' + '[' + endoflife + ']';
		endoflifescore.data = Math.round((endoflife / total) * 100);

		insecurescore.label = 'Insecure' + '[' + insecure + ']';
		insecurescore.data = Math.round((insecure / total) * 100);

		chartData.push(securescore);
		chartData.push(endoflifescore);
		chartData.push(insecurescore);
		return chartData;

	},*/

	patchInformation:function(event, target,options){
		Ext.Ajax.request({
			url: 'action=vpm_package&which=silent_details&',
			method: 'GET',
			dataType: 'json',
			params: {
				'vpm_id':options.extra.data.vpm_id
			},
			success: function (response, opts) {
				var response = Ext.decode(response.responseText);
				if ((response.has_download, 10) === 0)  {
					return ('Not Available');
				}
				var patchWindow = Ext.create('sfw.view.patching.PatchInformationWindow');
				patchWindow.getViewModel().set('patchWindowData',response);
				patchWindow.getViewModel().set('packageType',options.extra.data.type);
				patchWindow.show();
			},
			failure: function (response, opts) {
				Ext.Msg.alert("Unexpected Error", "Failed to get data");
				sfw.util.Debug.log( 'Error while trying to get package details.' );
			}
		});
	},

	vpmGridRefresh:function(){
		var params = {};
		params.softType = '';
		params.searchType_vpm = Ext.ComponentQuery.query('#searchType_vpm')[0].getValue();
		params.search = Ext.ComponentQuery.query('#searchvpm')[0].getValue();
		params.smartgroup_id = Ext.ComponentQuery.query('#sgcombo_vpm')[0].getValue();
		params.downloadOnly = Number((typeof sfw.Default.get('vpm.downloadOnly') !== 'undefined')?(sfw.Default.get('vpm.downloadOnly') === 'true'):true);
		params.myEnvironmentProducts = Number((typeof sfw.Default.get('vpm.myEnvironmentProducts') !== 'undefined')?(sfw.Default.get('vpm.myEnvironmentProducts') === 'true'):true);
		params.notMyEnvironmentProducts = Number((typeof sfw.Default.get('vpm.notMyEnvironmentProducts') !== 'undefined')?(sfw.Default.get('vpm.notMyEnvironmentProducts') === 'true'):true);
		params.readyDeploy = Number((typeof sfw.Default.get('vpm.readyDeploy') !== 'undefined')?(sfw.Default.get('vpm.readyDeploy') === 'true'):true);
		params.macdownload = Number((typeof sfw.Default.get('vpm.macdownload') !== 'undefined')?(sfw.Default.get('vpm.macdownload') === 'true'):false);
		params.langSelected =  langSelected ;
		params.showSolutions = '1';
		params.vpmHideMicrosoftProducts = Number((typeof sfw.Default.get('vpm.vpmHideMicrosoftProducts') !== 'undefined')?(sfw.Default.get('vpm.vpmHideMicrosoftProducts') === 'true'):false);
		params.vpmProductsPatched = Number((typeof sfw.Default.get('vpm.vpmProductsPatched') !== 'undefined')?(sfw.Default.get('vpm.vpmProductsPatched') === 'true'):false);

		var vpmstore = Ext.getStore('vendorpatch');
		vpmstore.getProxy().setExtraParams(params);
		vpmstore.loadPage(1,{
			callback: function () {
				//console.log("success");
			},
			failure: function () {
				//console.log("failed");
			}
		});
	},

	handleSpecialKeysVPM: function (field, e) {
		var me = this;
		if (e.getKey() == e.ENTER) {
			me.vpmGridRefresh();
		}
	},

	checkLicenses:function(grid) {
		var columns = grid.down('headercontainer').getGridColumns();
		if (true !== sfw.util.Auth.LoginDetails.isVPMModuleEnabled) {
			Ext.Msg.show({
				title: "License Needed!",
				 msg: "The Vendor Patch Module is an optional add-on to Software Vulnerability Manager, <a target=\"_blank\" href=\"https://www.flexera.com/svm-vpm\">click here for more information</a>.",
				 buttons: Ext.Msg.OK,
				icon: Ext.MessageBox.INFO
			});
		}else{
			Ext.ComponentQuery.query('#LicenseText')[0].hide();
		}
		if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
			columns[6].destroy();
		}
	},

	applyViewSettings:function(btn){
		var form = this.getView().down('form').getValues();

		var me = this;
		var view = me.getView();

		var params = {};

		if(form.downloadOnly){
			params.download_product_only = true;
		}
		else{
			params.download_product_only = false;
		}

		if(form.readDeploy){
			params.ready_deploy = true;
		}
		else{
			params.ready_deploy = false;
		}

		if(form.macDownload){
			params.mac_download = true;
		}
		else{
			params.mac_download = false;
		}

		if(form.hideMicrosoftProducts){
			params.vpm_msProductsHiding = true;
		}
		else{
			params.vpm_msProductsHiding = false;
		}

		if(form.highlightProducts){
			params.vpm_productspatched = true;
		}
		else{
			params.vpm_productspatched = false;
		}

		downloadOnly = params.download_product_only;
		myEnvironmentProducts = (view.down('#detected-notdetected-in-my-env').getValue() === "2") ? true : false;
		notMyEnvironmentProducts = (view.down('#detected-notdetected-in-my-env').getValue() === "3") ? true : false;
		readyDeploy = params.ready_deploy;
		macdownload = params.mac_download;
		smartgroup_id = Ext.ComponentQuery.query('#sgcombo_vpm')[0].getValue();
		vpmHideMicrosoftProducts = params.vpm_msProductsHiding;
		vpmProductsPatched = params.vpm_productspatched;

		var SelLang = '' ;
		var langRecipients = view.down('#selectLanguages').down('#selectedGrid');
		var langRecipients = langRecipients.getSelection();
		//console.log(langRecipients);
		var langRecipientsArray = [];
		for ( i = 0; i < langRecipients.length;  i++ ) {
			langRecipientsArray.push( langRecipients[i].data.language );
		}

		//console.log(langRecipientsArray);

		SelLang = langRecipientsArray.join(',') ;
		params.lang_selected = SelLang;

		if(SelLang == ''){
			params.lang_selected = 0;
		}

		langSelected = params.lang_selected;

		params.my_environment_product = (view.down('#detected-notdetected-in-my-env').getValue() === "2") ? true : false;
		params.not_my_environment_product = (view.down('#detected-notdetected-in-my-env').getValue()=== "3") ? true : false;
		params.smart_group_id = Ext.ComponentQuery.query('#sgcombo_vpm')[0].getValue() ? true : false;

		sfw.Default.add('vpm.downloadOnly', params.download_product_only.toString());
		sfw.Default.add('vpm.myEnvironmentProducts', params.my_environment_product.toString());
		sfw.Default.add('vpm.notMyEnvironmentProducts', params.not_my_environment_product.toString());
		sfw.Default.add('vpm.readyDeploy', params.ready_deploy.toString());
		sfw.Default.add('vpm.langSelected', params.lang_selected.toString());
		sfw.Default.add('vpm.macdownload', params.mac_download.toString());
		sfw.Default.add('vpm.smartgroup_id', params.smart_group_id.toString());
		sfw.Default.add('vpm.vpmHideMicrosoftProducts', params.vpm_msProductsHiding.toString());
		sfw.Default.add('vpm.vpmProductsPatched', params.vpm_productspatched.toString());

		Ext.Ajax.request({
			url: 'action=vpm_package_view_settings'
			, params: params
			, method: 'GET'
			, callback: function (options, success, response) {
				if (!success) {
					sfw.util.Debug.log('Failed storing VPM view settings.');
				}else{
					me.vpmGridRefresh();
				}
			}
			, scope: this
		});

		btn.up('window').destroy();
	},

	onProductSGRender: function (cb) {
		var me = this,
			store = me.getView().getViewModel().getStore('productSGStore');

		store.on('load', function(store) {
			var smartGroupId = me.getView().getSmartGroupId();

			if (Ext.isEmpty(smartGroupId)) {

				var defaultSG = store.findRecord('name', 'All Products');
				if (defaultSG) {
					cb.setValue(defaultSG.get('id'));
				}
			} else {
				cb.setValue(me.getView().getSmartGroupId());
			}
		});

		store.load();
	}


});
