Ext.define('sfw.view.patching.SpsPatchingCommonGrid', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiSpsCommonGrid',
    itemId:'spsCommonGrid',

    controller:'spspatching',
    stateful: true,
    stateId: 'sfw_csiSpsCommonGrid_grid',
    border: 1,
    cls: 'shadow',

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    viewModel:{
        data:{
            display:true
        }
    },

    title:'Flexera Package System (SPS)',

    preventHeader: true,

    columns: [
        {text: 'Product', dataIndex: 'product_name', flex: 1,renderer:function(value, meta, record, rowIndex, colIndex, store){
                if ( parseInt( record.get('updateable'), 10 ) ) {
                    meta.tdCls = 'color:blue';
                }else{
                    meta.tdCls = 'color:grey';
                }

                if ((parseInt(record.get('is_deployed'), 10) === 1) && (typeof sfw.util.Default.get('sps.highlightNonExisting') !== 'undefined'?(sfw.util.Default.get('sps.highlightNonExisting') === 'true'):defaulthighlightcreated)){
                    meta.tdCls = 'AgentSuccess';
                }

                return '<span style="' + meta.tdCls + '">' + value + '</span>';
            }},
        {text: 'Vendor', dataIndex: 'vendor_name', flex: 1},
        {text: 'Patched Version', dataIndex: 'patched_version', flex: 1},
        {
            text: 'Architecture',
            dataIndex: 'arch',
            flex: 1,
            renderer: function (value, metaData, record, rowIndex, colIndex, store) {
                // Bitfield format from left to right:
                // Position 0 - 32 bit
                // Position 1 - 64 bit
                // Position 2 - Intel Processor
                // Position 3 - PowerPC Processor
                // Position 4 - Microsoft system
                // Position 5 - Apple System
                var value = parseInt(value, 10);
                var disregardsArchitecture = (record.get('disregard_architecture') === '1') ? true : false;
                if (disregardsArchitecture) {
                    // Turning on 32-bit / 64-bit architectures
                    value |= 3;
                }

                var archStr = "";
                switch (parseInt(value, 10) & 0xFFFC) {
                    case 0x14:
                        archStr = "Windows";
                        break;
                    case 0x28:
                        archStr = "Mac PowerPC";
                        break;
                    case 0x24:
                        archStr = "Mac Intel";
                        break;
                    case 0x42:
                        archStr = "Red Hat Linux";
                        break;
                    default:
                        if (value) {
                            // LogMessage("New package architecture code found: " + value );
                        }
                        break;
                }

                if (archStr !== 'Red Hat Linux') {
                    switch (value & 3) {
                        case 1:
                            archStr += "32-bit";
                            break;
                        case 2:
                            archStr += "64-bit";
                            break;
                        case 3:
                            archStr += "32-bit / 64-bit";
                            break;
                    }
                }
                return archStr;
            }
        },
        {text: 'SAID', dataIndex: 'vuln_id', renderer: sfw.Default.renderSaid, flex: 1},
        {
            xtype: 'widgetcolumn',
            text: 'Criticality',
            dataIndex: 'vuln_criticality',
            align: 'center',
            sortable: true,
            width: 90,
            flex: 1,
            widget: {
                xtype: 'sectorprogress',
                height: 8
            }
        },
        {text: 'Threat Score', dataIndex: 'vuln_threat_score', renderer: sfw.Default.threatScoreDefault, flex: 1,align: 'right'},
        {text: 'Detected', dataIndex: 'updated', renderer: sfw.Default.gridRenderTimeAgo, flex: 1,align: 'right',bind:{hidden:'{!display}'}},
        {
            text: 'Advisory Published',
            dataIndex: 'vuln_create_date',
            renderer: sfw.Default.renderSaidDate,
            flex: 1,
            align: 'right'
        },
        {text: 'Insecure', dataIndex: 'no_insecure', flex: 1, align: 'right',bind:{hidden:'{!display}'}},
        {text: 'End-Of-Life', dataIndex: 'no_eol', flex: 1, align: 'right',bind:{hidden:'{!display}'}},
        {text: 'Secure', dataIndex: 'no_patched', flex: 1, align: 'right',bind:{hidden:'{!display}'}},
        {text: 'Total', dataIndex: 'no_total', flex: 1, align: 'right',bind:{hidden:'{!display}'}},
        {text: 'Hosts', dataIndex: 'no_hosts', flex: 1, align: 'right',bind:{hidden:'{!display}'}},
        {text: 'Uninstallable', dataIndex: 'uninstallable', flex: 1, align: 'right', renderer: sfw.Default.renderIsUninstallable},
        {
            text: 'Smart Group Id',
            dataIndex: 'smartgroup_id',
            flex: 1,
            hidden: true,
            align: 'right'
        }
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE_PATCHING)
        },
        beforerender:'hideSpsColumns'
    }

});

