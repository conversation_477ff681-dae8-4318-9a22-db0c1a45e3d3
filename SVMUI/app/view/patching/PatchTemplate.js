Ext.define('sfw.view.patching.PatchTemplate',{
    extend: 'Flexera.widgets.grid.Abstract',

    xtype: 'sfw.csiCreatePatchTemplate',

    title: 'Patch Template',
    border: 1,
    cls: 'shadow',
    requires:[
        'sfw.store.patching.PatchTemplate',
        'sfw.view.patching.PatchTemplateController'
    ],
    stateful: true,
    stateId: 'sfw_csiCreatePatchTemplate_grid',
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    store:{
        type:'patchtemplate'
    },

    controller: 'patchtemplate',

    selModel: {
        selType: 'rowmodel',
        mode: 'MULTI'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items:[{
            xtype:'label',
            text:'Search type:'
        },{
            xtype: "tbspacer", height: 3
        }, {
            xtype: "combo",
            itemId: "searchType_template",
            name: "searchType_template",
            store: new Ext.data.SimpleStore({
                fields: ["value", "label"],
                data: [
                    ["1", "Template Name"],
                    ["2", "Vendor"]
                ]
            }),
            valueField: "value",
            value: "1",
            displayField: "label",
            mode: "local",
            editable: false,
            triggerAction: "all",
            selectOnFocus: false,
            /*listeners: {
               select: 'patchTemplateRefresh'
            }*/
        }, {
            xtype: 'tbspacer', width: 2
        }, {
            xtype: 'textfield',
            emptyText: 'Search text....',
            itemId:'searchtemplate',
            listeners: {
                specialkey: 'handleSpecialKeysPatchTemplate'
            }
        }, {
            xtype: 'button',
            ui: 'primary',
            text: 'Search',
            handler:"patchTemplateRefresh"
        }, {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }]
    }],

    columns: [
        {
            text: 'Template Name',
            dataIndex: 'template_name',
            flex: 4,
            sortable: true,
            renderer: function (value, metaData, record) {
                if (record.data.active_flag == 1) {
                    return '<span style="color:blue">'+value+'</span>';
                } else {
                    return '<span style="color:grey">'+value+'</span>';
                }
            }
        },
        { text: 'Product Name', dataIndex: 'product_name', flex: 1},
        { text: 'Vendor', dataIndex: 'vendor_name', flex: 1},
        { text: 'Patched Version', dataIndex: 'patched_version', flex: 1},
        { text: 'Minimum', dataIndex: 'min_version', flex: 1},
        {
            text: 'Architecture',
            dataIndex: 'architecture',
            flex: 1,
            renderer: this.renderArchitectureedit = function (value, metaData, record, rowIndex, colIndex, store) {
                var value = Ext.Number.parseInt(value);
                var disregardsArchitecture = (record.disregard_arc === '1') ? true : false;
                if (disregardsArchitecture) {
                    value |= 3;
                }

                var archStr = "";
                switch (Ext.Number.parseInt(value) & 0xFFFC) {
                    case 0x14:
                        archStr = "Windows";
                        break;
                    case 0x28:
                        archStr = "Mac PowerPC";
                        break;
                    case 0x24:
                        archStr = "Mac Intel";
                        break;
                    case 0x42:
                        archStr = "Red Hat Linux";
                        break;
                    default:
                        if (value) {
                            //LogMessage("New package architecture code found: " + value);
                        }
                        break;
                }

                if (archStr !== 'Red Hat Linux') {
                    switch (value & 3) {
                        case 1:
                            archStr += "32-bit";
                            break;
                        case 2:
                            archStr += "64-bit";
                            break;
                        case 3:
                            archStr += "32-bit / 64-bit";
                            break;
                    }
                }
                return archStr;
            }
        },
        { text: 'Updated On', dataIndex: 'updated_on', flex: 1, renderer:sfw.Default.gridRenderUTCDateInLocaltime},
        {
            text: 'Subscribed',
            dataIndex: 'subscription_status',
            flex: 1,
            renderer: function (value) {
                return value == 0 ? 'No': (value == 1 ? 'Yes' : 'Paused');
            }
        },
        { text: 'Subscription', dataIndex: 'created_on', flex: 1, renderer:sfw.Default.gridRenderUTCDateInLocaltime}
    ],

    listeners: {
        afterrender: 'patchTemplateRefresh',
        itemcontextmenu: 'onItemContextMenu'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{patchtemplate}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Patch Templates {0} - {1} of {2}',
        emptyMsg: "No Templates",
    }
})