
// var createAgentStats = function() {
//     var numberOfHostsId = 'numberOfHostsId';
//     var numberOfHostsWithAgentId = 'numberOfHostsWithAgentId';
//     var numberOfHostsWithNewestAgentId = 'numberOfHostsWithNewestAgentId';
//     var numberOfHostsWithOlderAgentId = 'numberOfHostsWithOlderAgentId';
//     var numberOfHostsWithOutdatedAgentId = 'numberOfHostsWithOutdatedAgentId';
//     var numberOfHostsWithoutAgentId = 'numberOfHostsWithoutAgentId';
//     var html = '';
// Create table holding overall agents statistics

var numberOfHostsId = 0;
var numberOfHostsWithAgentId = 0;
var numberOfHostsWithoutAgentId = 0;
var numberOfHostsWithNewestAgentId = 0;
var numberOfHostsWithOlderAgentId = 0;
var numberOfHostsWithOutdatedAgentId = 0;

var overallAgentStats = function () {
    var overallHtml = "";
    overallHtml += "<h2>Overall Agent Statistics</h2>";
    overallHtml += "<table width='100%' cellspacing='0' cellpadding='0'>";
    overallHtml += "<tr>";
    overallHtml += "<td class='AgentStatsTable' width='30%'>Total Number of Hosts:</td>";
    overallHtml += "<td id='" + numberOfHostsId + "' class='AgentStatsTable' width='70%'></td>";
    overallHtml += "</tr>";
    overallHtml += "<tr>";
    overallHtml += "<td class='AgentStatsTable'>Number of Hosts with an Agent Installed:</td>";
    overallHtml += "<td id='" + numberOfHostsWithAgentId + "' class='AgentStatsTable'></td>";
    overallHtml += "</tr>";
    overallHtml += "<tr>";
    overallHtml += "<td class='AgentStatsTable'>Number of Hosts without an Agent Installed:</td>";
    overallHtml += "<td id='" + numberOfHostsWithoutAgentId + "' class='AgentStatsTable'></td>";
    overallHtml += "</tr>";
    overallHtml += "</table>";
    return overallHtml;
};
// Create table holding agent version statistics
var versionAgentStats = function () {
    var versionHtml = "";
    versionHtml += "<h2>Version Statistics for Installed Agents</h2>";
    versionHtml += "<table width='100%' cellspacing='0' cellpadding='0'>";
    versionHtml += "<tr>";
    versionHtml += "<td class='AgentStatsTable' width='30%'>Hosts with the Newest Agent Installed (&ge; " + this.CSIAVersion + ") :</td>";
    versionHtml += "<td id='" + numberOfHostsWithNewestAgentId + "' class='AgentStatsTable' width='70%'></td>";
    versionHtml += "</tr>";
    versionHtml += "<tr>";
    versionHtml += "<td class='AgentStatsTable'>Hosts with an Older Agent Installed (&ge; " + this.CSIAErrorVersion + " and &lt; " + this.CSIAWarningVersion + ") :</td>";
    versionHtml += "<td id='" + numberOfHostsWithOlderAgentId + "' class='AgentStatsTable'></td>";
    versionHtml += "</tr>";
    versionHtml += "<tr>";
    versionHtml += "<td class='AgentStatsTable'>Hosts with an Outdated Agent Installed (&lt; " + this.CSIAErrorVersion + ") :</td>";
    versionHtml += "<td id='" + numberOfHostsWithOutdatedAgentId + "' class='AgentStatsTable'></td>";
    versionHtml += "</tr>";
    versionHtml += "</table>";
    return versionHtml;
};

// 	html += overallAgentStats();
// 	html += versionAgentStats();
// 	return html;
// };


Ext.define('sfw.view.patching.AgentDeployment', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiAgentDeployment',
    bodyStyle: 'margin: 10px; padding: 5px 3px;',

    requires: [
        'sfw.view.patching.AgentDeploymentController',
        'sfw.view.patching.AgentDeploymentModel'
    ],

    controller: 'patching-agentdeployment',
    viewModel: {
        type: 'patching-agentdeployment'
    },
    title: 'Agent Deployment',
    border: 1,
    cls: 'shadow',
    items: [{
        xtype: 'label',
        html: [
            '<h1>Agent Summary</h1>',
            '<p>Below is a summary of the Software Vulnerability Manager Agents currently installed in the network.<br />',
            'NOTE: The statistics are based on scan results thus may be out of synchronisation with your WSUS/System Center server if a scan has not been recently performed.</p>',
            this.overallAgentStats(),
            this.versionAgentStats(),
            '<h2>Deploy the Software Vulnerability Manager Agent through your Microsoft WSUS/System Center Server</h2>',
            '<p>Click "Create Software Vulnerability Manager Agent Package" to start the Software Vulnerability Manager Agent Package wizard.</p>'

        ]
    }, {
        xtype: 'button',
        ui: 'primary',
        hidden: true,
        text: 'Create Software Vulnerability Manager Agent Package'
    }]

});
