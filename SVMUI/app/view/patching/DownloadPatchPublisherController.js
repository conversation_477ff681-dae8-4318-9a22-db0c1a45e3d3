Ext.define('sfw.view.patching.DownloadPatchPublisherController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.patchPublisherController',

    callBeforeRender:function(){
        var me = this;
        var patchPublisherView = me.getView().down('#patchPublisher');

        var url = sfw.util.Globals.apiPath() + 'action=sps_package&which=download_patch_publisher';

        var publisherVersion = '';
        var sha256sum = '';
        var patchPublisherURL = 'https://resources.flexera.com/tools/SVM/SVMPatchPublisher.msi';

        // if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
        //     url = sfw.util.Globals.apiPath() + 'action=sps_package&which=download_patch_publisher&version=********';
        //     patchPublisherURL = 'https://resources.flexera.com/tools/SVM/'+sfw.util.Globals.CSIAVersion+'/SVMPatchPublisher.msi';
        // }

        if(sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION){
                Ext.Ajax.request({
                    url: url
                    ,method: 'GET'
                    ,success: function( response ) {
                        try {
                            var response = Ext.util.JSON.decode( response.responseText );
                            if(response.error_code == 0 && response.data.rows.length > 0){
                                publisherVersion = response.data.rows[0].publisher_version;
                                sha256sum = response.data.rows[0].sha256;
                                patchPublisherView.setData({patchPubsliherData:patchPublisherURL,patchPublisherVersion:publisherVersion,sha256:sha256sum});
                            }else{
                                Ext.Msg.alert( "Unexpected Error", "Unable to get Version..." );
                            }
                        } catch ( ex ) {
                            Ext.Msg.alert( "Unexpected Error", "Unable to get Version..." );
                        }
                    }
                    ,failure: function() {
                        Ext.Msg.alert( "Unexpected Error", "Unable to get Version..." );
                    }
                });
        }else{
             Ext.ComponentQuery.query('#patchPublisher')[0].hide()
        }


    }, getOldVersionData:function(){

        if(sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION){
            //Ext.ComponentQuery.query('#toggleButton')[0].hide();
            Ext.ComponentQuery.query('#patchPublisherGrid')[0].hide();
            return;
        }
        var patchpublisher = Ext.getStore('getPatchPublisherOldVersion');

        patchpublisher.getProxy().setExtraParams({  'version': sfw.util.Globals.CSIBackendVersion})

        patchpublisher.loadPage(1,{
            callback: function () {
                //console.log("success");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    }
})