Ext.define('sfw.view.patching.PatchDeploymentStatusController', {
	extend: 'Ext.app.ViewController',
	alias: 'controller.patchdeploymentstatus',


	reloadPatchDeploymentStatusGrid:function(){

		var patchDeploymentStatus = Ext.getStore('patchdeployment');
		var view = this.getView();

		var combo_value = view.down('#searchTypePackageDeployment').getValue();
		var search_text = view.down('#search_text').getValue();

		patchDeploymentStatus.getProxy().setExtraParams({
			'search': search_text ,
			'searchType_PackageDeployment': combo_value});

		patchDeploymentStatus.load();
	},

	handleSpecialKeysPatchStatus : function (field, e) {
		var me = this;
		if (e.getKey() == e.ENTER) {
			me.reloadPatchDeploymentStatusGrid();
		}
	},

	getPackageType: function (value, metaData, record, rowIndex, colIndex, store) {

		var value = parseInt(value);
		package_type = '';
		if ((value == 0 || value == null) && (record.data.wizard_id)) {
			package_type = 'SPS Wizard';
		} else if (record.data.vpm_id != false && (record.data.single_patch_id)) {
			package_type = 'VPM QuickPatch';
		} else if (record.data.template_id != false && (record.data.single_patch_id)) {
			package_type = 'SPS QuickPatch';
		} else if ((value == 0 || value == null)) {
			package_type = 'SPS Subscription';
		} else if (record.data.wizard_id) {
			package_type = 'VPM Wizard';
		} else {
			package_type = 'VPM Subscription';
		}
		return package_type;
	},

	getDeploymentStatus: function (value, metaData, record, rowIndex, colIndex, store) {

		var value = parseInt(value);
		deployment_status = '';
		if (value == 0)
			deployment_status = 'Failed';
		else if (value == 1)
			deployment_status = 'Success';
		else if (value == 2)
			deployment_status = 'Added to Queue';
		else if (value == 3)
			deployment_status = 'Other';
		else if (value == 5)
			deployment_status = 'Waiting for signature';
		else if (value == 6)
			deployment_status = 'Signed';
		else if (value == 8)
			deployment_status = 'Pending Deployment';
		else if (value == 9)
			deployment_status = 'In Progress';
		else
			deployment_status = 'Unknown';

		return deployment_status;
	},

	deletePackage: function (event, target, options) {

		var triggered_package_id = options.extra.data.id;
		Ext.Ajax.request({
			url: 'action=sps_package&which=delete_record&',
			method: 'POST',
			params: {
				id: triggered_package_id
			},
			dataType: 'json',
			success: function (response) {
				var response = Ext.util.JSON.decode(response.responseText);
				if (response.success === true) {
					var count = response.data;
					Ext.Msg.show({
						title: 'Success',
						msg: 'Successfully deleted record',
						buttons: Ext.Msg.OK
					});
					Ext.getCmp('patchDeploymentGrid').getStore().load();
				} else {
					sfw.util.Debug.log('Error while deleting record');
					Ext.Msg.show({
						title: 'Error',
						msg: 'Deleting record failed',
						buttons: Ext.Msg.OK
					});
				}
			},
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Deleting record failed");
			}
		});

	}

});
