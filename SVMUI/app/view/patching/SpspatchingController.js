Ext.define('sfw.view.patching.SpspatchingController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.spspatching',

    init:function(){
        defaultviewsolutions = 'true';
        defaulteolorinsecure = 'true';
        defaultonlyupdateable = 'true';
        defaultmicrosoftproducts = 'true';
        defaulthighlightcreated = 'false';
        defaultspslite= 'false';
    },

    configureView:function(){
        var configureViewData = {};
        var configureView = Ext.create("sfw.view.commonpopupwindows.Spsconfigureview");
        configureViewData.viewSolutions = defaultviewsolutions;
        configureViewData.eolOfLifeInsecure = defaulteolorinsecure;
        configureViewData.onlyUpdateTable = defaultonlyupdateable;
        configureViewData.microSoftProducts = defaultmicrosoftproducts;
        configureViewData.highLightCreated = defaulthighlightcreated;
        configureViewData.viewSpsLitePage = defaultspslite;
        configureView.getViewModel().set('configureViewData',configureViewData);
        configureView.show();
    },

    applyConfigureView:function(btn){
        var form = this.getView().down('form').getValues();
        var params = {};
        if(form.viewsolutions){
            params.view_solutions = true;
        }
        else{
            params.view_solutions = false;
        }

        if(form.eolorinsecure){
            params.show_only_eol_or_insecure = true;
        }
        else{
            params.show_only_eol_or_insecure = false;
        }

        if(form.onlyupdateable){
            params.only_updatable = true;
        }
        else{
            params.only_updatable = false;
        }

        if(form.microsoftproducts){
            params.hide_microsoft_products = true;
        }
        else{
            params.hide_microsoft_products = false;
        }

        if(form.highlightcreated){
            params.highlight_non_existing = true;
        }
        else{
            params.highlight_non_existing = false;
        }

        if(form.spsLitePage){
            spsLitePageVal = true;
        }
        else{
            spsLitePageVal = false;
        }


        sfw.util.Default.add('sps.showOnlyEolOrInsecure', params.show_only_eol_or_insecure.toString());
        sfw.util.Default.add('sps.onlyUpdatable', params.only_updatable.toString());
        sfw.util.Default.add('sps.hideMicrosoftProducts', params.hide_microsoft_products.toString());
        sfw.util.Default.add('sps.highlightNonExisting', params.highlight_non_existing.toString());
        sfw.util.Default.add('sps.viewSolutions', params.view_solutions.toString());

        if (typeof sfw.util.Default.get('sps.litePage') !== 'undefined'){
            params.spsLitePage = spsLitePageVal ? 'true' : 'false';
            sfw.util.Default.add('sps.litePage', params.spsLitePage.toString());
        }

        var _this = this;

        Ext.Ajax.request({
            url: 'action=sps_store_view_settings',
            method: 'GET',
            dataType: 'json',
            params:params,
            success: function (response, opts) {
                var response = Ext.util.JSON.decode( response.responseText );
                var settings = _this.getConfigureSettings();
                if(response.success) {
                    if (response.error_code == 0) {
                        if (params.view_solutions == 1) {
                            var settings = _this.getConfigureSettings();
                            _this.refreshUngroupedGrid(settings);
                            var groupedview = Ext.ComponentQuery.query('#groupedgrid')[0];
                            var groupedGrid = groupedview.down('#spsCommonGrid');

                            var ungroupedview = Ext.ComponentQuery.query('#ungroupedgrid')[0];
                            var ungroupedGrid = ungroupedview.down('#spsCommonGrid');

                            if((typeof sfw.Default.options.get('sps.litePage') !== 'undefined') && (sfw.Default.options.get('sps.litePage') === 'true')){
                                _this.hideSpsColumns(groupedGrid);
                                _this.hideSpsColumns(ungroupedGrid);
                            }else{
                                _this.showSpsColumns(groupedGrid);
                                _this.showSpsColumns(ungroupedGrid);
                            }
                            groupedview.hide();
                            ungroupedview.show();
                        } else {
                            var settings = _this.getConfigureSettings();
                            _this.refreshGroupedGrid(settings);

                            var groupedview = Ext.ComponentQuery.query('#groupedgrid')[0];
                            var groupedGrid = groupedview.down('#spsCommonGrid');

                            var ungroupedview = Ext.ComponentQuery.query('#ungroupedgrid')[0];
                            var ungroupedGrid = ungroupedview.down('#spsCommonGrid');

                            if((typeof sfw.Default.options.get('sps.litePage') !== 'undefined') && (sfw.Default.options.get('sps.litePage') === 'true')){
                                _this.hideSpsColumns(groupedGrid);
                                _this.hideSpsColumns(ungroupedGrid);
                            }else{
                                _this.showSpsColumns(groupedGrid);
                                _this.showSpsColumns(ungroupedGrid);
                            }
                            ungroupedview.hide();
                            groupedview.show();
                        }
                        var productsgscombo = Ext.getStore('productsgscombo');
                        productsgscombo.load();
                    } else {
                        sfw.util.Debug.log('Failed storing SPS view settings.');
                    }
                }
                else{
                    sfw.util.Debug.log( 'Failed storing SPS view settings.' );
                    Ext.Msg.alert("Failure", "Failed Storing SPS Settings");
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert("Failure", "Failed Storing SPS Settings");
            }
        });

        btn.up('window').destroy();

    },

    refreshSpsGrid:function(){

        var _this = this;

        var settings = _this.getConfigureSettings();

        if((typeof sfw.util.Default.get('sps.viewSolutions') !== 'undefined') && (sfw.util.Default.get('sps.viewSolutions') === 'true')){
            _this.refreshUngroupedGrid(settings);
            var groupedview = Ext.ComponentQuery.query('#groupedgrid')[0];
            groupedview.hide();

        }else if((typeof sfw.util.Default.get('sps.viewSolutions') !== 'undefined') && (sfw.util.Default.get('sps.viewSolutions') === 'false')){
            _this.refreshGroupedGrid(settings);
            var ungroupedview = Ext.ComponentQuery.query('#ungroupedgrid')[0];
            ungroupedview.hide();

        }
        else{
            _this.refreshUngroupedGrid(settings);
            var groupedview = Ext.ComponentQuery.query('#groupedgrid')[0];
            groupedview.hide();
        }
    },

    refreshUngroupedGrid:function(params){
        var spsstore = Ext.getStore('ungroupedgrid');
        spsstore.getProxy().setExtraParams(params);
        spsstore.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    refreshGroupedGrid:function(params){
        var spsstore = Ext.getStore('groupedgrid');
        spsstore.getProxy().setExtraParams(params);
        spsstore.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    getConfigureSettings:function(){
        defaulteolorinsecure = (typeof sfw.util.Default.get('sps.showOnlyEolOrInsecure') !== 'undefined'?(sfw.util.Default.get('sps.showOnlyEolOrInsecure') === 'true'):(defaulteolorinsecure.toString() === 'true'));
        defaultonlyupdateable = (typeof sfw.util.Default.get('sps.onlyUpdatable') !== 'undefined'?(sfw.util.Default.get('sps.onlyUpdatable') === 'true'):(defaultonlyupdateable.toString() === 'true'));
        defaultmicrosoftproducts = (typeof sfw.util.Default.get('sps.hideMicrosoftProducts') !== 'undefined'?(sfw.util.Default.get('sps.hideMicrosoftProducts') === 'true'):(defaultmicrosoftproducts.toString() === 'true'));
        defaulthighlightcreated = (typeof sfw.util.Default.get('sps.highlightNonExisting') !== 'undefined'?(sfw.util.Default.get('sps.highlightNonExisting') === 'true'):(defaulthighlightcreated.toString() === 'true'));
        defaultviewsolutions = (typeof sfw.util.Default.get('sps.viewSolutions') !== 'undefined'?(sfw.util.Default.get('sps.viewSolutions') === 'true'):(defaultviewsolutions.toString() === 'true'));
        defaultspslite = ((typeof sfw.util.Default.get('sps.litePage') !== 'undefined') && (sfw.util.Default.get('sps.litePage') === 'true')) ? 'true' : 'false';

        var params =  {};
        params.softType = '';
        params.showOnlyEolOrInsecure=Number(defaulteolorinsecure);
        params.onlyUpdatable=Number(defaultonlyupdateable);
        params.hideMicrosoftProducts=Number(defaultmicrosoftproducts);
        if(defaultviewsolutions){
            params.showSolutions = 1;
            params.search = Ext.ComponentQuery.query('#ungroupedsearch')[0].getValue();
            params.searchType = Ext.ComponentQuery.query('#ungroupedsearchtype')[0].getValue();
            params.smartgroup_id = Ext.ComponentQuery.query('#ungroupedsmartgroupid')[0].getValue();
        }else{
            params.showSolutions = 0;
            params.search = Ext.ComponentQuery.query('#groupedsearch')[0].getValue();
            params.searchType = Ext.ComponentQuery.query('#groupedsearchtype')[0].getValue();
            params.smartgroup_id = Ext.ComponentQuery.query('#groupedsmartgroupid')[0].getValue();
        }
        params.spsLitePage=defaultspslite;

        return params;
    },

    hideSpsColumns:function(grid){
        headerCtr = grid.down('headercontainer');
        menu = headerCtr.getMenu();
        vm = grid.getViewModel();
        if((typeof sfw.Default.options.get('sps.litePage') !== 'undefined') && (sfw.Default.options.get('sps.litePage') === 'true')){
            vm.set('display',false);
            menu.down('[text="Detected"]').hide();
            menu.down('[text="Insecure"]').hide();
            menu.down('[text="End-Of-Life"]').hide();
            menu.down('[text="Secure"]').hide();
            menu.down('[text="Total"]').hide();
            menu.down('[text="Hosts"]').hide();
        }

        if ( true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled ) {
            columns = grid.down('headercontainer').getGridColumns();
            menu.down('[text="Threat Score"]').hide();
            columns[6].hide();
        }
    },

    showSpsColumns:function(grid){
        headerCtr = grid.down('headercontainer');
        menu = headerCtr.getMenu();
        vm = grid.getViewModel();
        vm.set('display',true);
        menu.down('[text="Detected"]').show();
        menu.down('[text="Insecure"]').show();
        menu.down('[text="End-Of-Life"]').show();
        menu.down('[text="Secure"]').show();
        menu.down('[text="Total"]').show();
        menu.down('[text="Hosts"]').show();
    }
});