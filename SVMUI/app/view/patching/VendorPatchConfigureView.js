Ext.define('sfw.view.patching.VendorPatchConfigureView', {
	extend: "Ext.window.Window",

	width: 650,
	height: 620,
	bodyPadding: 10,

	scrollable: "vertical",
	modal: true,
	resizable: true,
	constrainHeader: true,
	layout: "fit",
	controller: 'vendorpatchmodule',
	title: "Configure View",

	viewModel:{
		formulas:{
			downloadOnly:function(){
				return ((typeof sfw.Default.get('vpm.downloadOnly') !== 'undefined')?(sfw.Default.get('vpm.downloadOnly') === 'true'):downloadOnly)
			},
			readDeploy:function(){
				return ((typeof sfw.Default.get('vpm.readyDeploy') !== 'undefined')?(sfw.Default.get('vpm.readyDeploy') === 'true'):readyDeploy);
			},
			macDownload:function(){
				return ((typeof sfw.Default.get('vpm.macdownload') !== 'undefined')?(sfw.Default.get('vpm.macdownload') === 'true'):macdownload);
			},
			hideMicrosoftProducts:function(){
				return ((typeof sfw.Default.get('vpm.vpmHideMicrosoftProducts') !== 'undefined')?(sfw.Default.get('vpm.vpmHideMicrosoftProducts') === 'true'):vpmHideMicrosoftProducts)
			},
			hightLightProducts:function(){
				return ((typeof sfw.Default.get('vpm.vpmProductsPatched') !== 'undefined')?(sfw.Default.get('vpm.vpmProductsPatched') === 'true'):vpmProductsPatched)
			},
			envValue:function(){
				var defaultVal = '1';
				if ((typeof sfw.Default.get('vpm.myEnvironmentProducts') !== 'undefined') && (sfw.Default.get('vpm.myEnvironmentProducts') === 'true')) {
					defaultVal = '2';
				}
				if ((typeof sfw.Default.get('vpm.notMyEnvironmentProducts') !== 'undefined') && (sfw.Default.get('vpm.notMyEnvironmentProducts') === 'true')) {
					defaultVal = '3';
				}
				return defaultVal;
			}
		}
	},

	items: [{
		xtype: 'form',
		monitorValid: true,

		autoHeight: true,
		border: false,
		defaultType: 'textfield',
		labelAlign: 'left',

		items: [{
			xtype: 'combo',
			itemId: "detected-notdetected-in-my-env",
			allowBlank: false,
			fieldLabel: 'Show',
			labelWidth: 50,
			width: 480,
			padding: '0 0 10 0',
			store: new Ext.data.SimpleStore({
				idIndex: 0,
				fields: ['value', 'label'],
				data: [
					['1', 'All available Vendor patches'],
					['2', 'Only those Detected in my environment'],
					['3', 'Only those Not detected in my environment']
				]
			}),
			bind: {
				value: '{envValue}'
			},
			valueField: 'value',
			displayField: 'label',
			selectOnFocus: false,
			mode: 'local',
			editable: false,
			triggerAction: 'all',
			forceSelection: false,
			emptyText: 'Not selected ...',
		},
		{
			xtype: 'box',
			padding: '10 0',
			html: 'Add more filters'
		},
		{
			xtype: 'container',
			padding: '0 0 0 40',
			items: [
				{
					xtype: 'checkboxfield',
					boxLabel: 'Download Available',
					bind:{
						value: '{downloadOnly}'
					},
					name: 'downloadOnly',
				},{
					xtype: 'checkboxfield',
					boxLabel: 'Deployment ready patches',
					bind: {
						value:'{readDeploy}'
					},
					name:'readDeploy'
				},{
					xtype: 'checkboxfield',
					boxLabel: 'Only Mac patches',
					bind:{
						value:'{macDownload}'
					},
					name:'macDownload'
				},{
					xtype: 'checkboxfield',
					boxLabel: 'Hide Microsoft products',
					bind:{
						value:'{hideMicrosoftProducts}'
					},
					name:'hideMicrosoftProducts'
				},{
					xtype: 'checkboxfield',
					boxLabel: 'Highlight products for which packages have been created',
					bind: {
						value:'{hightLightProducts}'
					},
					name:'highlightProducts',
					hideMode: 'visibility'
				}
			]
		},
		{
			xtype:'common.languages',
			header: false,
			frame: true,
			bodyPadding: 0
		}]
	}],

	buttons: [
		{
			text: 'Apply',
			//ui: 'primary',
			formBind: true,
			tabIndex: 9,
			tooltip: {
				title: 'Apply Settings to View'
			},
			handler: "applyViewSettings"
		},
		{
			text: 'Cancel',
			tabIndex: 8,
			tooltip: {
				title: 'Close window',
				text: 'Cancel all changes and close the window.'
			},
			handler: function () {
				this.up('window').destroy();
			}
		}
	]
});
