Ext.define('sfw.store.patching.PatchPublisher', {
    extend: 'Ext.data.Store',

    alias: 'store.patchpublisher',

    storeId: 'getPatchPublisherOldVersion',

    remoteSort: true,

    sorters: [{
        property: 'publisher_version',
        direction: 'DESC'
    }],

    pageSize: 30,
    proxy: {
        type: 'ajax',
        url: 'action=sps_package&which=oldversions&',
        reader: {
            type: 'json',
            rootProperty: 'data.rows',
            totalProperty: 'data.total',
            successProperty: 'success'
        }
    },

});