Ext.define('sfw.view.patching.PatchPublisherConnectionController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.patchPublisherConnectionController',

    callAfterRender:function(){
        var self = this;
        currentValue = 0;
        previousValue = 0;
        Ext.Ajax.request({
            url: 'action=vpm_package&which=list_daemons',
            method: 'GET',
            dataType: 'json',
            success: function (response, opts) {
                var response = Ext.decode(response.responseText);
                if (response.success && response.error_code == 0) {

                    if(response.data.rows.length > 0){
                        for(var i =0; i < response.data.rows.length ; i++){
                            var daemons = response.data.rows;

                            var scriptField = {
                                xtype:"fieldset",
                                title:'',
                                itemId:'fieldset'+i,
                                margin:'10 10 10 10',
                                height:180,
                                width:320,
                                items:[{
                                    xtype:'fieldcontainer',
                                    items:[{
                                        xtype: 'displayfield',
                                        labelWidth: 150,
                                        fieldLabel: 'Last Connection Date',
                                        name: 'lastConDate',
                                        itemId: 'lastConnectionDate'+i,
                                    }]
                                },{
                                    xtype:'fieldcontainer',
                                    items:[{
                                        xtype: 'displayfield',
                                        labelWidth: 150,
                                        fieldLabel: 'Version',
                                        name: 'version',
                                        itemId: 'version'+i,
                                    }]
                                },{
                                    xtype:'fieldcontainer',
                                    items:[{
                                        xtype: 'displayfield',
                                        labelWidth: 150,
                                        fieldLabel: 'Connections',
                                        name: 'connections',
                                        itemId: 'connections'+i,
                                    }]
                                },{
                                    xtype:'fieldcontainer',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items:[{
                                        xtype:'button',
                                        margin:'5 5 5 120',
                                        text:'Get Connections',
                                        buttonAlign:'center',
                                        ui: 'primary',
                                        itemId: "getconnections-"+ daemons[i].id+"-"+i,
                                        handler: 'getConnections'
                                    },{
                                        xtype:'button',
                                        margin:'5 5 5 5',
                                        text:'Delete',
                                        buttonAlign:'center',
                                        ui: 'primary',
                                        itemId: "daemonId_"+ daemons[i].id+"_"+daemons[i].name,
                                        handler: 'deleteDaemon'
                                    }]

                                }]
                            }

                            var daemon = Ext.ComponentQuery.query('#patchdaemon')[0];
                            daemon.show();
                            daemon.add(scriptField);
                            daemon.updateLayout();
                            daemon.down('#lastConnectionDate'+i).setValue(self.getLastConnectionDate(daemons[i].last_connection_date));
                            daemon.down('#version'+i).setValue(daemons[i].version);
                            daemon.down('#connections'+i).setValue(daemons[i].connection_count);
                            daemon.down('#fieldset'+i).setTitle('<font color="#404040"><b>'+daemons[i].name+'</b></font>');

                            if(i == 0){
                                var data = [];
                                data.itemId = 'getconnections-'+daemons[i].id+"-"+i
                                self.getConnections(data);
                            }
                        }
                    }else{
                        Ext.ComponentQuery.query('#patchdaemon')[0].hide();
                    }


                } else {
                    Ext.Msg.alert("Failed", "Unable to get patch publisher list");
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert("Unexpected Error", 'Unable to get connection details');
            }
        });
    },

    getConnections: function(data){

        var connection = data.itemId.split("-");
        var deamonId = connection[1];

        previousValue = currentValue;
        currentValue = connection[2];

        var fieldSet = Ext.ComponentQuery.query('#patchdaemon')[0];
        fieldSet.down('#fieldset'+previousValue).setStyle('backgroundColor','#ffffff');
        fieldSet.down('#fieldset'+currentValue).setStyle('backgroundColor','#dddddd');

        var connectionStore = Ext.getStore('patchpublisherconnections');

        connectionStore.getProxy().setExtraParams({
            'daemon_id': deamonId,
        });
        connectionStore.loadPage(1, {
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    deleteConnections: function() {

        var self = this;
        var connectionList = [];
        var selected = Ext.ComponentQuery.query('#connection_checkbox')[0].getSelectionModel().getSelected();

        if (selected.length) {
            for (var i = 0; i < selected.length; i++) {
                connectionList.push(selected.items[i].data.id);
            }
        }else{
            Ext.Msg.alert("Note", "Please select atleast one connection to delete");
            return false;
        }

        Ext.Msg.confirm('Delete Distribution System Connection', 'Are you sure you want to delete the selected Distribution System connection/s?', function (clicked) {
            if (clicked === "yes") {
                Ext.Ajax.request({
                    url: 'action=vpm_package&which=delete_connections',
                    method: 'POST',
                    dataType: 'json',
                    params: {
                        'connection_list': JSON.stringify(connectionList)
                    },
                    success: function (data) {
                        var response = Ext.decode(data.responseText);
                        if (response.success === true && response.error_code == 0) {
                            Ext.Msg.show({
                                title: 'Success'
                                , msg: 'Connection/s deleted successfully.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.INFO
                            });

                            var daemon = Ext.ComponentQuery.query('#patchdaemon')[0].removeAll();
                            self.callAfterRender();

                            var connectionStore = Ext.getStore('patchpublisherconnections');
                            connectionStore.load();

                        } else {
                            Ext.Msg.show({
                                title: 'Error'
                                , msg: 'Failed to delete connection/s.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.ERROR
                            });
                        }
                    },
                    failure: function (response, opts) {
                        Ext.Msg.alert('Error', 'Unexpected Error Occured');
                    }
                });
            }
        });
    },

    deleteDaemon: function (data) {
        var self = this;
        var connectionID = data.itemId.split("_");
        var deamon = connectionID[1];

        Ext.Msg.confirm('Delete Patch Publisher Connection', 'Deleting a Patch Publisher connection will delete all its associated Distribution System connections. Click Yes to proceed.', function (clicked) {
            if (clicked === "yes") {
                Ext.Ajax.request({
                    url: 'action=vpm_package&which=delete_daemon',
                    method: 'POST',
                    dataType: 'json',
                    params: {
                        'daemon_id': deamon,
                        'daemon_name': connectionID[2]
                    },
                    success: function (data) {
                        var response = Ext.decode(data.responseText);
                        if (response.success === true && response.error_code == 0) {
                            Ext.Msg.show({
                                title: 'Success'
                                , msg: 'Deleted successfully.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.INFO
                            });

                            var daemon = Ext.ComponentQuery.query('#patchdaemon')[0].removeAll();
                            self.callAfterRender();

                            var connectionStore = Ext.getStore('patchpublisherconnections');
                            connectionStore.load();

                        } else {
                            Ext.Msg.show({
                                title: 'Error'
                                , msg: 'Failed to delete.'
                                , buttons: Ext.Msg.OK
                                , icon: Ext.MessageBox.ERROR
                            });
                        }
                    },
                    failure: function (response, opts) {
                        Ext.Msg.alert('Error', 'Unexpected Error Occured');
                    }
                });
            }
      });
    },

    getLastConnectionDate: function(value){
        if (value) {
            var lastCheckInDate = sfw.util.Util.dateCreate(value, true);
            var currentDate = sfw.util.Util.dateCreate();
            // Difference between the last check-in and the current date in seconds
            var dateDiff = Math.round(Math.abs((lastCheckInDate.valueOf() - currentDate.valueOf())) / 1000);

            if (dateDiff > 604800) { // 7 days = 60 * 60 * 24 * 7 = 604800 seconds
                return '<span class="AgentError">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else if (dateDiff > 86400) { // 1 day = 60 * 60 * 24 * 1 = 86400 seconds
                return '<span class="AgentWarning">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            } else {
                return '<span class="AgentSuccess">' + Ext.util.Format.date(lastCheckInDate, sfw.util.Globals.dateLongOutput) + '</span>';
            }
        } else {
            return '-';
        }
    }
});