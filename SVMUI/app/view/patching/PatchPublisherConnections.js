Ext.define('sfw.view.patching.PatchPublisherConnections', {
    extend: 'Ext.panel.Panel',

    xtype: 'sfw.csiDeletePatchConnections',
    title: 'Patch Publisher Connections',

    border: 1,
    cls: 'shadow',

    requires: [
        'sfw.store.patching.PatchPublisherConnections',
        'sfw.view.patching.PatchPublisherConnectionController'
    ],

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    controller: 'patchPublisherConnectionController',

    items:[{
           xtype:'panel',
           itemId:'patchdaemon',
           layout: 'hbox',
           autoScroll: true,
           flex:2,
           items:[],
           listeners:{
               afterrender: 'callAfterRender'
           }
         },{
            xtype: 'gridpanel',
            flex: 5,
            store: {
                type: 'patchpublisherconnections' //store alias name
            },
            viewConfig : {
                deferEmptyText: false,
                emptyText: 'No Connections'
            },
            id: 'connection_checkbox',
            itemId : 'connection_checkbox',
            selType: 'checkboxmodel',
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                items:[{
                    xtype: 'button',
                    text: 'Delete Connections',
                    ui: 'primary',
                    handler: 'deleteConnections'

                }]
            }],
            columns:[{
                text:'Connection Name', dataIndex:'connection_name', flex:1
            },{
                text:'Distribution System', dataIndex:'connection_type', renderer: sfw.Default.patchPublisherconnectionTypes,flex:1
            },{
                text:'Last Connection Date', dataIndex:'last_connection_date', renderer: sfw.Default.gridRenderLastCheckInDate, flex:1
            },{
                text:'Created Date',dataIndex:'created', renderer: sfw.Default.gridRenderUTCDateInLocaltime, flex:1
            },{
                text:'Modified Date',dataIndex:'modified', renderer: sfw.Default.gridRenderUTCDateInLocaltime, flex:1
            },{
                text:'Groups', dataIndex:'id', renderer: sfw.Default.renderGroups, flex:1
            }],
            layout: 'fit',
            margin: '2 2 0 0',
            bbar: {
                xtype: 'pagingtoolbar',
                store: 'patchpublisherconnections', //storeId
                region: 'south',
                displayInfo: true,
                displayMsg: 'Displaying Connections {0} - {1} of {2}',
                emptyMsg: "No connections to display"
            }
    }]

});
