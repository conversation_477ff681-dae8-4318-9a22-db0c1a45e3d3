Ext.define('sfw.view.patching.SpsPatchingModel', {
    extend: 'Ext.app.ViewModel',

    alias: 'viewmodel.spspatching',

    stores: {
        ungroupedgrid: {

            storeId: 'ungroupedgrid',//SPS Grouped View

            pageSize: '30',

            remoteSort: true,

            sorters: [{
                property: 'vendor_name',
                direction: 'ASC'
            }, {
                property: 'product_name',
                direction: 'ASC'
            }, {
                property: 'patched_version',
                direction: 'DESC'
            }],

            proxy: {
                type: 'ajax',
                url: 'action=sps_package&which=read&',
                reader: {
                    type: 'json',
                    transform: {
                        fn: function (data) {
                            //if (!Ext.isEmpty(data.data.rows)) {
                            if (data.data.rows) {
                                data.data.rows.map(function (item) {
                                    item.vuln_criticality = Number.isFinite(Ext.Number.parseInt(item.vuln_criticality, 10)) && Ext.Number.parseInt(item.vuln_criticality, 10) > 0 ?
                                        20 * (6 - (parseInt(item.vuln_criticality, 10))) / 100 :
                                        0;

                                    return item;
                                });
                                return data;
                            }
                        },
                        scope: this
                    },
                    rootProperty: 'data.rows',
                    successProperty: 'success',
                    totalProperty: 'data.total'
                }
            }
        },

        groupedgrid: {

            storeId: 'groupedgrid',//SPS Ungrouped View

            pageSize: '30',

            groupField: 'product_name',

            remoteSort: true,

            sorters: [{
                property: 'vendor_name',
                direction: 'ASC'
            }, {
                property: 'product_name',
                direction: 'ASC'
            }, {
                property: 'patched_version',
                direction: 'DESC'
            }],

            proxy: {
                type: 'ajax',
                url: 'action=sps_package&which=read&',
                reader: {
                    type: 'json',
                    transform: {
                        fn: function (data) {
                            //if (!Ext.isEmpty(data.data.rows)) {
                            if (data.data.rows) {
                                data.data.rows.map(function (item) {
                                    item.vuln_criticality = Number.isFinite(Ext.Number.parseInt(item.vuln_criticality, 10)) && Ext.Number.parseInt(item.vuln_criticality, 10) > 0 ?
                                        20 * (6 - (parseInt(item.vuln_criticality, 10))) / 100 :
                                        0;

                                    return item;
                                });
                                return data;
                            }
                        },
                        scope: this
                    },
                    rootProperty: 'data.rows',
                    totalProperty: 'data.total',
                    successProperty: 'success'
                }
            }
        }
    }
});
