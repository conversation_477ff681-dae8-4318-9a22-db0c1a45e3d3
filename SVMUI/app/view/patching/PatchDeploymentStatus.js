Ext.define('sfw.view.patching.PatchDeploymentStatus', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiCreatePackageDeployment',
    id: 'patchDeploymentGrid',

    requires: [
        'sfw.view.patching.PatchDeploymentStatusController',
        'sfw.store.patching.PatchDeploymentStatus',
        'sfw.util.Util',
        'sfw.util.Default'
    ],
    stateful: true,
    stateId: 'sfw_csiCreatePackageDeployment_grid',
    store: {
        type: 'patchdeployment'
    },

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    title: 'Patch Deployment Status',
    border: 1,
    cls: 'shadow',

    controller: 'patchdeploymentstatus',

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: "combo",
            itemId: "searchTypePackageDeployment",
            name: "searchTypePackageDeployment",
            store: new Ext.data.SimpleStore({
                fields: ["value", "label"],
                data: [
                    ["1", "Package"],
                    ["2", "Vendor"]

                ]
            }) ,
            fieldLabel: "Search Type",
            valueField: "value" ,
            displayField: "label" ,
            width: 210 ,
            value: "1",
            mode: "local" ,
            editable: false,
            triggerAction: "all" ,
            selectOnFocus: false,
            listeners: {

            }
        },
            { xtype: 'tbspacer', width: 8 },
            {
                xtype: 'textfield',
                emptyText: 'Search text ...',
                itemId: 'search_text',
                listeners: {
                    specialkey: 'handleSpecialKeysPatchStatus'
                }

            },
            {
                xtype: 'button',
                ui : 'primary',
                text: 'Search',
                handler: 'reloadPatchDeploymentStatusGrid'

            },
            {
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton',
            }

        ]
    }],


    columns: [
        {text: 'Package Name', dataIndex: 'package_name', flex: 3},
        {text: 'Vendor', dataIndex: 'manufacturer', flex: 1},
        {text: 'Template Name', dataIndex: 'template_name', flex: 1},
        {text: 'Type', dataIndex: 'vpm_id', flex: 1, renderer: 'getPackageType'},
        {text: 'Version', dataIndex: 'deployed_version', flex: 1},
        {text: 'Published to', dataIndex: 'published_to', flex: 1},
        {text: 'Connection Name', dataIndex: 'connection_name', flex: 1},
        {text: 'Deployed to', dataIndex: 'deployed_to', flex: 1},
        {text: 'Status', dataIndex: 'status', flex: 1, renderer: 'getDeploymentStatus'},
        {text: 'Triggered On', dataIndex: 'created', flex: 1, renderer: sfw.util.Default.gridRenderUTCDateInLocaltime},
        {
            text: 'Last status update',
            dataIndex: 'updated',
            flex: 1,
            renderer: sfw.util.Default.gridRenderUTCDateInLocaltime
        },
        {
            text: 'Message', dataIndex: 'reason', flex: 2, renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {text: 'Application Id', dataIndex: 'package_id', flex: 1, hidden: true},
        {text: 'Unsigned Path', dataIndex: 'intermediate_file', flex: 1, hidden: true},
        {text: 'Signed Path', dataIndex: 'signed_file', flex: 1, hidden: true},
        {text: 'Published from', dataIndex: 'hostname', flex: 1}
    ],


    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {

            var LoginDetails = sfw.util.Auth.LoginDetails;
            var triggered_package_status = record.data.status;
            var disabled_status = true;
            var  published_to = record.data.published_to;
            var  package_id = record.data.package_id;
            var is_package_id_absent = false;
            if (package_id == undefined || package_id == null || package_id == '') {
                is_package_id_absent = true;
            }

            if( triggered_package_status == 2 ||  (triggered_package_status == 0  && is_package_id_absent)) {
                disabled_status = false;
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'patchdeploymentstatus',
                width: 165,
                disabled : false,
                hidden : LoginDetails.isReadOnly,
                plain: true,
                items: [ {
                    text: 'Manage Assignments',
                    listeners: {
                        afterrender:function(){
                            if((triggered_package_status == 9 ) || (triggered_package_status == 0 && is_package_id_absent))
                            {
                                this.setDisabled(true);
                            }
                            if(published_to != "Intune"){
                                this.hide();
                            }
                        },
                        click: {fn: sfw.Default.editAssignments, extra: record}
                    }
                },{
                    text: 'Delete',
                    listeners: {
                        afterrender:function(){
                            if(disabled_status)
                            {
                                this.setDisabled(true);
                                this.setTooltip('Delete is not available for status - "In Progress" and "Success"');

                            }
                        },
                        click: {fn: 'deletePackage', extra: record}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        cellclick: function (view, td, cellIndex, record, tr, rowIndex, e, eOpts,field) {

            if (view.headerCt.getHeaderAtIndex(cellIndex).text == 'Message' && record.data.reason) {
                var mgs = Ext.Msg.show({
                    title: 'Message',
                    align: 'Right',
                    msg: record.data.reason,
                    buttons: Ext.Msg.OK
                });
            }
        },
        afterrender: function (grid) {
            var task = {
                run: function () {
                    if (grid!== 'undefined') {
                        var view = grid.up('panel'); // Get the parent panel
                        var combo_value = view.down('#searchTypePackageDeployment').getValue();
                        var search_text = view.down('#search_text').getValue();

                        grid.getStore().getProxy().setExtraParams({
                            'search': search_text,
                            'searchType_PackageDeployment': combo_value
                        });
                        grid.getStore().reload();
                    }
                },
                interval: 20000 //20 seconds
            };
            Ext.TaskManager.start(task);

            grid.on('destroy', function () {
                Ext.TaskManager.stop(task);
            });
        }
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{patchdeployment}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Subscriptions {0} - {1} of {2}',
        emptyMsg: "No Subscriptions"
    }
});
