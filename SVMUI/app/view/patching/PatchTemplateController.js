Ext.define('sfw.view.patching.PatchTemplateController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.patchtemplate',

    constructor: function (config) {
        this.callParent(arguments);

        this.onSubscribeToPatchClicked = Ext.bind(this.onSubscribeToPatchClicked, this);
        this.onItemContextMenu = Ext.bind(this.onItemContextMenu, this);
    },

    patchTemplateRefresh: function(){
        var view = this.getView();
        var patchtemplatestore = Ext.getStore('patchtemplate');

        patchtemplatestore.getProxy().setExtraParams({'searchType_template': view.down('#searchType_template').getValue(),
            'search': view.down('#searchtemplate').getValue()})
        patchtemplatestore.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    handleSpecialKeysPatchTemplate: function (field, e) {
        var me = this;
        if (e.getKey() == e.ENTER) {
            me.patchTemplateRefresh();
        }
    },


    onPatchTemplateGridRender: function () {
        const me = this,
            grid = me.getView();

        grid.getStore().load();
    },

    onItemContextMenu: function (grid, record, item, index, e) {
        var me = this,
            selection = grid.getSelection(),
            product_name = record.get("product_name"),
            template_name = record.get("template_name"),
            patched_version = record.get("patched_version"),
            template_id = record.get("template_id"),
            subscribeMenuItemText = '';
        var sps_data = [{
            template_id: template_id,
            version: patched_version,
            template_name: template_name,
            product_name: product_name
        }];
        var hidden_context = false;

        if (selection.length > 1)
        {
            hidden_context = true;
        }

        const isSingleSelection = !Ext.isEmpty(selection) && selection.length === 1;
        var subscribedisbaled = false;
        if (isSingleSelection && ((1 == selection[0].data.subscription_status) || (2 == selection[0].data.subscription_status))) {
            subscribeMenuItemText = 'Edit Subscription';
        } else {
            subscribeMenuItemText = 'Subscribe to Patch';
        }

        var disablePause = false;
        var disableResume = false;
        if(selection.length == 1){
            disablePause = LoginDetails.isReadOnly || record.data.subscription_status == 0 || record.data.subscription_status == 2;
            disableResume = LoginDetails.isReadOnly || record.data.subscription_status == 0 || record.data.subscription_status == 1;
        }else if(selection.length > 1){
            var unSubscribedpackages = true;
            var packagesData = [];
            for ( var i=0; i < selection.length; ++i ) {
                packagesData.push(parseInt(selection[i].data.subscription_status));
                if (selection[i].data.subscription_status != 0) {
                    unSubscribedpackages = false;
                }
            }

            var subscribed = packagesData.includes(1);
            var unSubscribed = packagesData.includes(0);
            var paused = packagesData.includes(2);


            if((unSubscribedpackages) || (subscribed && unSubscribed) || (subscribed && paused) || (subscribed && unSubscribed && paused) || (unSubscribed && paused)){
                disablePause = true;
                disableResume = true;
            }

            if(subscribed && !unSubscribed && !paused){
                disablePause = false;
                disableResume = true;
            }

            if(paused && !subscribed && !unSubscribed){
                disablePause = true;
                disableResume = false;
            }

            if((subscribed && paused) || (unSubscribed && paused) || (paused)){
                subscribedisbaled = true;
            }
        }

        var contextMenu = Ext.create("Ext.menu.Menu", {
            width: 200,
            plain: true,
            items: [
                {
                    text: "Publish/Edit Template",
                    hidden : hidden_context,
                    //,disabled: LoginDetails.isReadOnly
                    handler: function () {
                        sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE_PATCHING)
                    },

                },
                {
                    text: "Delete Template",
                    deletion_text: "Confirm Delete?",
                    hidden : hidden_context,
                    listeners: {
                        click: function () {
                            Ext.MessageBox.confirm(
                                "Confirm",
                                "Confirm delete?",
                                callbackFunction
                            );
                            function callbackFunction(btn) {
                                if (btn === "yes") {
                                    me.deleteTemplateAPICall(me);
                                } else {
                                }
                            }
                        },
                    },
                    //,disabled: LoginDetails.isReadOnly
                    handler: function () {
                    },

                },
                "-",
                {
                    text: subscribeMenuItemText,
                    disabled: subscribedisbaled,
                    record: record,
                    handler: Ext.bind(this.onSubscribeToPatchClicked, me)
                },{
                    text: "Pause Subscription",
                    disabled: disablePause,
                    handler: Ext.bind(this.onPauseSubscription, me)
                },
                {
                    text: "Resume Subscription",
                    disabled: disableResume,
                    handler: Ext.bind(this.onResumeSubscription, me)
                },
                "-",
                {
                    text: "Publish with QuickPatch",
                    hidden : hidden_context,
                    handler: Ext.bind(this.sendToSingleClickPatch, me)
                    //,disabled: LoginDetails.isReadOnly
                },
            ],
        });
        e.stopEvent();
        contextMenu.showAt(e.getXY());
    },

    deleteTemplateAPICall: function(data){
        const me = this,
            grid = me.getView(),
            selection = grid.getSelection();

        var template_id = [];
        template_id.push(parseInt(selection[0].data.template_id));
        var template_name = selection[0].data.template_name;

        Ext.Ajax.request({
            url: 'action=sps_package&which=delete_template',
            method: 'POST',
            params: {
                template_id: Ext.util.JSON.encode(template_id),
                template_name: template_name
            },
            success: function (data) {
                var response = Ext.decode(data.responseText);
                if (response.success === true) {
                    var count = response.data;
                    Ext.Msg.show({
                        title: 'Success'
                        , msg: 'Successfully deleted template.'
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.INFO
                    });
                    var currentView = sfw.common.SfwAccessor.getCurrentView();
                    if (currentView) {
                        currentView.getStore().load();
                    }
                } else {
                    Ext.Msg.show({
                        title: 'Error'
                        , msg: 'Deleting template failed.'
                        , buttons: Ext.Msg.OK
                        , icon: Ext.MessageBox.ERROR
                    });
                }
            },
            failure: function () {
                Ext.Msg.show({
                    title: 'Error'
                    , msg: 'Deleting template failed.'
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.ERROR
                });
            }
        });

    },

    quickPatchAPICall: function(sps_data){
        var params= {
            sps_data: sps_data
        };
        Ext.Ajax.request({
            url: 'action=sps_package&which=send_single_patch',
            method: 'POST',
            params: {
                subscription_data: Ext.util.JSON.encode(params)
            },
            success: function( data ) {
                var response = {};
                response = Ext.util.JSON.decode( data.responseText );
                if (response.success) {
                    if (response.error_code == 3 ) {
                        Ext.Msg.show({
                            title: 'Warning',
                            msg: "Already sent to SVM Patch Publisher",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    } else {
                        Ext.Msg.show({
                            title: 'Success',
                            msg: "Package is created and successfully scheduled for publish by the SVM Patch Publisher during its next check-in time",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                        var currentView = sfw.common.SfwAccessor.getCurrentView();
                        if (currentView) {
                            currentView.getStore().load();
                        }
                    }
                } else {
                    Ext.Msg.alert("Failure", "Not able to send to patch daemon please try again...");
                }
            },
            failure: function() {
                Ext.Msg.alert("Failure", "Not able to send to patch daemon please try again...");
            }
        });

    },
    sendToSingleClickPatch: function(menuItem){
        const me = this,
            grid = me.getView(),
            selected = grid.getSelection();
        var publishTitile = selected[0].data.product_name;
        var publishMsg = "Do you want to send to SVM Patch Publisher?";
        var sps_data = selected.map(function(element) {return {template_id:selected[0].data.template_id, version:selected[0].data.secure_version}; });
        var params = {
            sps_data: sps_data
        };
        Ext.Ajax.request({
            url: 'action=sps_package&which=check_single_patch&',
            method: 'POST',
            params: {
                patchData: Ext.util.JSON.encode(params)
            },
            success: function( data ) {
                var response = {};
                response = Ext.util.JSON.decode( data.responseText );
                if (response.success) {
                    if (response.error_code == 3 ) {
                        Ext.Msg.show({
                            title: 'Warning',
                            msg: "Already sent to SVM Patch Publisher",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    } else {
                        if (sfw.util.Auth.LoginDetails.isPatchPublisherInstalled) {
                            var quickPatchDetailsWindow = Ext.create("sfw.view.common.PublishQuickPatchPT",{
                                selection: selected,
                                record: null
                            });
                            quickPatchDetailsWindow.show();
                        } else {
                            Ext.Msg.confirm(publishTitile, publishMsg, function (clicked) {
                                if (clicked === "yes") {
                                    me.quickPatchAPICall(sps_data);
                                }
                            });
                        }
                    }
                } else {
                    Ext.Msg.alert("Failure", "Not able to send to patch Publisher please try again...");
                }
            }
            ,failure: function() {
                Ext.Msg.alert("Failure", "Not able to send to patch Publisher please try again...");
            }
            ,scope: me
        });
    },
    handlePublish: function () {
        var me = this,
            view = me.getView(),
            selected = me.getViewModel().get('selection'),
            assignmentData = me.getViewModel().get('assignmentsDataQuickPatch'),
            patch_connection_id = view.down('#singlePatchConnections').getValue();

        var sps_data = selected.map(function(element) {return {template_id:element.data.template_id, version:element.data.secure_version, template_name:element.data.template_name, product_name:element.data.product_name}; });
        if ( patch_connection_id != undefined && patch_connection_id != null) {
            var connection_id = patch_connection_id ;
        } else {
            var connection_id = null ;
        }
        var params = {
            sps_data: sps_data,
            connection_id: connection_id,
            assignment_connections:assignmentData
        };

        Ext.Ajax.request({
            url: 'action=sps_package&which=send_single_patch&',
            method: 'POST',
            params: {
                subscription_data: Ext.util.JSON.encode( params )
            },
            success: function( data ) {
                sfw.Default.closeSubscriptionWindow();
                var response = {};
                response = Ext.util.JSON.decode( data.responseText );
                if (response.success) {
                    if (response.error_code == 3 ) {
                        Ext.Msg.show({
                            title: 'Warning',
                            msg: "Already sent to SVM Patch Publisher",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    } else {
                        Ext.Msg.show({
                            title: 'Success',
                            msg: "Package is created and successfully scheduled for publish by the SVM Patch Publisher during its next check-in time",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    }
                    var currentView = sfw.common.SfwAccessor.getCurrentView();
                    if (currentView) {
                        currentView.getStore().load();
                    }
                } else {
                    Ext.Msg.alert("Failure", "Not able to send to patch Publisher please try again...");
                }
            }
            ,failure: function() {
                sfw.Default.closeSubscriptionWindow();
                Ext.Msg.alert("Failure", "Not able to send to patch Publisher please try again...");
            }
            ,scope: me
        });
        view.destroy();

    },
    onSubscribeToPatchClicked: function (menuItem) {
        const me = this,
            grid = me.getView(),
            selection = grid.getSelection();

        var uneditableChosen = 0;

        if ( 0 < selection.length ) {
            // Add logic to support that not all row's are able to subscribe
            for ( var i=0; i < selection.length; ++i ) {
                if ( selection[i].data.active_flag == 0 ) {
                    uneditableChosen = true;
                    break;
                }
            }
        }

        if (uneditableChosen) {
            Ext.Msg.alert("Not able to subscribe", "Subscription limited to valid template");
            return;
        }
        if ((1 === selection.length) && ((1 == selection[0].data.subscription_status) || (2 == selection[0].data.subscription_status)))
        {
            var template_id = selection[0].data.template_id;
            Ext.Ajax.request({
                url: 'action=sps_package&which=get_subscription_details&template_id=' + template_id,
                method: 'GET',
                callback: function(options, success, response) {
                    if (success) {
                        var remoteData = Ext.decode(response.responseText, true) || {};
                        remoteData = remoteData.data;
                        if (!Ext.isEmpty(remoteData.rows)) {
                            var editSubscription = Ext.create("sfw.view.common.ConfigureSubscription", {
                                selection: selection,
                                record: remoteData.rows[0]
                            });
                            editSubscription.show();
                            editSubscription.down('#macNote').hide();
                            Ext.ComponentQuery.query("#subscriptionSave")[0].setDisabled(false);

                            var connection_ids = [];
                            var connections_intune = [];
                            for(i=0; i < remoteData.rows.length; i++){
                                connection_ids.push(remoteData.rows[i].connection_id);
                                if(remoteData.rows[i].connection_type == 2){
                                    connections_intune.push(remoteData.rows[i].connection_id)
                                }
                            }
                            editSubscription.down('#singlePatchConnections').setValue(connection_ids);
                            if (!Ext.isEmpty(connections_intune)) {
                                editSubscription.down('#manageAssignments').setHidden(false);
                            } else {
                                editSubscription.down('#manageAssignments').setHidden(true);
                            }

                            if (remoteData.rows[0].always_trigger === '1'){
                                editSubscription.down('#alwaysPublish').setValue(true);
                            } else {
                                editSubscription.down('#onlyPublish').setValue(true);
                            }
                            if ( remoteData.rows[0].vuln_cvss3_score !== ""){
                                editSubscription.down('#cvss3ScoreItemId').setValue(remoteData.rows[0].vuln_cvss3_score);
                            }
                            if ( remoteData.rows[0].vuln_criticality !== ""){
                                editSubscription.down('#criticalityItemId').setValue(remoteData.rows[0].vuln_criticality);
                            }
                            if ( remoteData.rows[0].vuln_threat_score !== ""){
                                editSubscription.down('#threatScoreItemId').setValue(remoteData.rows[0].vuln_threat_score);
                            }

                            if ( remoteData.rows[0].version !== ""){
                                editSubscription.down('#versionFieldId').setValue(remoteData.rows[0].version);
                            }
                            editSubscription.down('#triggerSubscriptionCurrentVersion').setValue((remoteData.rows[0].publish_asap === '1')? true : false);

                            var filteredData = [];
                            for(i=0; i<remoteData.rows.length ; i++){
                                for(j=0; j< remoteData.rows[i].assignment_connections.length; j++){
                                    remoteData.rows[i].assignment_connections[j].availability = "As Soon as Possible";
                                    filteredData.push(remoteData.rows[i].assignment_connections[j])
                                }
                            }
                            editSubscription.getViewModel().set('assignmentsData',filteredData);
                            
                        }
                    }
                },
                scope: me
            });
        } else {
            var editSubscription = Ext.create("sfw.view.common.ConfigureSubscription", { selection: selection });
            editSubscription.show();
            editSubscription.down('#macNote').hide();
            editSubscription.down('#versionFieldId').setValue(selection[0].data.secure_version);
        }
    },

    onPauseSubscription:function(){
        const me = this;
        grid = me.getView();
        selection = grid.getSelection();

        var data = [];

        for ( var i=0; i < selection.length; ++i ) {
            if (selection[i].data.subscription_status == 1) {
                var pauserecords = {};
                pauserecords.template_id = selection[i].data.template_id;
                pauserecords.product_name = selection[i].data.product_name;
                data.push(pauserecords);
            }
        }

        if(data.length <= 0){
            Ext.Msg.alert("Note", 'Please select atleast one subscribed package.');
            return;
        }

        this.pauseResumeSubscription(data,2);

    },

    onResumeSubscription:function(){
        const me = this;
        grid = me.getView();
        selection = grid.getSelection();

        var data = [];

        for ( var i=0; i < selection.length; ++i ) {
            if (selection[i].data.subscription_status == 2) {
                var resumerecords = {};
                resumerecords.template_id = selection[i].data.template_id;
                resumerecords.product_name = selection[i].data.product_name;
                data.push(resumerecords);
            }
        }

        if(data.length <= 0){
            Ext.Msg.alert("Note", 'Please select atleast one paused subscription.');
            return;
        }

        this.pauseResumeSubscription(data,1);
    },

    pauseResumeSubscription:function(data,status){
        var paramData = {};
        if(status == 2){
            subscription = 'pause_subscription';
            paramData['pause_data'] = JSON.stringify(data);
        }else{
            var subscription = 'resume_subscription';
            paramData['resume_data'] = JSON.stringify(data);
        }

        paramData['status'] = status;
        Ext.Ajax.request({
            url: 'action=sps_package&which='+subscription,
            method: 'POST',
            dataType: 'json',
            params: paramData,
            success: function (response, opts) {
                var response = Ext.decode(response.responseText, true);
                if (response.success) {
                    Ext.Msg.alert("Success", response.msg);
                    var currentView = sfw.common.SfwAccessor.getCurrentView();
                    if (currentView) {
                        currentView.getStore().load();
                    }
                }else {
                    Ext.Msg.alert("Failed" , "Failed to updated subscription status.");
                }
            },
            failure: function (response, opts) {
                Ext.Msg.alert('Error', 'Unexpected Error Occured');
            }
        });
    },

});
