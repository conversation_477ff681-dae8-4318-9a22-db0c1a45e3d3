Ext.define('sfw.view.patching.VendorPatchModule', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiVendorPatchView',

    itemId: 'vendorPatch',
    stateful: true,
    stateId: 'sfw_csiVendorPatchView_grid',
    layout:'fit',
    title: 'Vendor Patch Module',
    border: 1,
    cls: 'shadow',
    autoScroll: true,

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    requires: [
        'sfw.view.patching.VendorPatchModuleController',
        'sfw.store.patching.VendorPatchModule',
    ],

    store: {
        type: 'vendorpatch'
    },

    controller: 'vendorpatchmodule',

    selModel: {
        selType: 'rowmodel',
        mode: 'MULTI'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items:[{
            xtype:'label',
            text:'Search type:'
        },{
            xtype: "tbspacer", height: 3
        }, {
            xtype: "combo",
            itemId: "searchType_vpm",
            name: "searchType_vpm",
            store: new Ext.data.SimpleStore({
                fields: ["value", "label"],
                data: [
                    ["1", "Product"],
                    ["2", "CVE"],
                    ["3", "Vendor"]
                ]
            }),
            valueField: "value",
            value: "1",
            displayField: "label",
            mode: "local",
            editable: false,
            triggerAction: "all",
            selectOnFocus: false,
            /*listeners: {
               select: 'patchTemplateRefresh'
            }*/
        }, {
            xtype: 'tbspacer', width: 2
        },{
            xtype: 'textfield',
            emptyText: 'Search text....',
            itemId:'searchvpm',
            listeners: {
                specialkey: 'handleSpecialKeysVPM'
            }
        },{
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler: 'vpmGridRefresh'
        }, {
                xtype: 'tbspacer', width: 2
            },
            {
            xtype: 'label'
            , text: ' View from the context of Smart Group: '
        },{
            xtype: 'combo',
            text: ' View from the context of Smart Group: ',
            itemId: "sgcombo_vpm",
            name: "searchType_vpm",
            queryMode: 'local',
            store: {
                type : 'productsgs',
                listeners: {
                    load: function () {
                        defaultSmartGroupIndex = this.find("name", "Not Selected", 0, false, true);
                        if (defaultSmartGroupIndex >= 0) {
                            defaultSmartGroupValue = this.getAt(defaultSmartGroupIndex).get("id");
                            Ext.ComponentQuery.query('#sgcombo_vpm')[0].setValue(defaultSmartGroupValue);
                        }
                    }
                }
            },
            editable : false,
            valueField: "id",
            displayField: "name",
            triggerAction: "all",
            selectOnFocus: false,
            listeners: {
                select: 'vpmGridRefresh'
            }
        }, {
            xtype: 'button',
            text: 'Configure View',
            ui: 'primary',
            handler:'configureViewVPM'
        }, {
            xtype: 'label',
            html: 'Feature needs license update, <a target="_blank" href="https://www.flexera.com/svm-vpm">more details</a>. ',
            itemId: 'LicenseText',
            cls : 'LicenseError',
        },{
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton',
        }]
    }],

    columns: [
        {text: 'Product', dataIndex: 'product_name', flex: 1 , renderer : function(value, metaData, record) {
                var isSilentParam = false;
                if ((record.data.silent_switches != '') || (record.data.silent_switches_generated != '')) {
                    isSilentParam = true;
                }
                if ((parseInt(record.data.is_deployed, 10) === 1) && (typeof sfw.util.Default.get('vpm.vpmProductsPatched') !== 'undefined'?(sfw.util.Default.get('vpm.vpmProductsPatched') === 'true'):vpmProductsPatched)){
                    metaData.tdCls = 'AgentSuccess';
                }
                else if (parseInt(record.data.auto_deploy, 10) === 1) {
                    metaData.tdCls = 'ProductNameAutoDeploy';
                } else if (parseInt(record.data.has_download, 10) === 1) {
                    metaData.tdCls = 'ProductNameHasDownload';
                } else if ((parseInt(record.data.auto_deploy, 10) === 0) && (parseInt(record.data.has_download, 10) === 0) && (isSilentParam === true)) {
                    metaData.tdCls = 'ProductNameIsSilentParam';
                }
                return value;
            }
        },
        {text: 'Vendor', dataIndex: 'manufacturer', flex: 1},
        {text: 'Patched Version', dataIndex: 'version', flex: 1},
        {text: 'Deployment Ready', dataIndex: 'auto_deploy', flex: 1, renderer: 'checkAutoDeploy'},
        {text: 'SAID', dataIndex: 'vuln_id', flex: 1, width: 62, renderer: sfw.Default.renderSaid},
        {xtype: 'widgetcolumn',text: 'Criticality', dataIndex: 'vuln_criticality', flex: 1, width: 66, widget: {
                xtype: 'sectorprogress',
                height: 8
            }},
        {text: 'Threat Score', dataIndex: 'vuln_threat_score', flex: 1, align: 'right',renderer:sfw.Default.threatScoreDefault},
        {text: 'Advisory Published', dataIndex: 'vuln_create_date', flex: 1, renderer: sfw.Default.renderSaidDate},
        {text: 'Architecture', dataIndex: 'platform', flex: 1},
        {text: 'Insecure', dataIndex: 'num_insecure', flex: 1 ,width: 81, align: 'right'},
        {text: 'End-Of-Life', dataIndex: 'num_eol', flex: 1 , align: 'right'},
        {text: 'Secure', dataIndex: 'num_patched', flex: 1, width: 81, align: 'right'},
        {text: 'Total', dataIndex: 'total', flex: 1 , renderer: sfw.Default.renderProductReport, align: 'right'},
        {text: 'Hosts', dataIndex: 'num_affected_hosts', flex: 1 , align: 'right'},
        {text: 'Updated On', dataIndex: 'date_modified', flex: 1, renderer: sfw.Default.gridRenderUTCDateInLocaltime},
        {text: 'Download', dataIndex: 'filename', flex: 1,renderer:function (value, metaData, record, rowIndex, colIndex, store) {
                if (parseInt(record.data.has_download, 10) === 0) {
                    return ('Not Available');
                } else if (false !== sfw.util.Auth.LoginDetails.isVPMModuleEnabled ) {
                    value = sfw.util.CommonConstants.patchDownloadURLPackage + '&token=F8859A92-C9C5-43F2-95D7-9A3E19FEC0B6&vpm_id=' + record.data.vpm_id + '&cstid=' + sfw.util.Auth.LoginDetails.account.settings['cst_id'] + '&src=vpm';
                    return (value ? '<a href="#" onclick="sfw.Default.externalURL(\'' + value.replace(/\\/g, "\\\\").replace(/'/g, "\\'") + '\')">' + 'Download' + '</a>' : '-' );
                } else {
                    return ('<a href="#" onClick="Ext.Msg.alert( \'Vendor Patch Module\', \'Vendor Patch Module is not enabled, please contact to support.\' );">Download </a> ');
                }
            }},
        {text: 'File Size', dataIndex: 'file_size', flex: 1, renderer: 'checkFileSize', align: 'right'},
        {text: 'Subscribed', dataIndex: 'subscription_status', flex: 1, width: 65,  renderer: 'checkSubscriptionStatus'},
        {text: 'Subscription Started', dataIndex: 'created_on', flex: 1, renderer: sfw.Default.gridRenderUTCDateInLocaltime}
    ],

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{vendorptach}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Vendor Patches {0} - {1} of {2}',
        emptyMsg: "No Vendor Patch"
    },

    listeners: {
        itemcontextmenu: 'onItemContextMenu',
        beforerender:'checkLicenses',
        afterrender:'vpmGridRefresh',
        itemdblclick: function(){
            sfw.Default.activeXMessage(sfw.CommonConstants.ACTIVEX_MESSAGE_PATCHING)
        },//TODO-Pending for IE browser
    }
});
