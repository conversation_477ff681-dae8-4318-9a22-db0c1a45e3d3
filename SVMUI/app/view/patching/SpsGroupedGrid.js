Ext.define('sfw.view.patching.SpsGroupedGrid',{
    extend:'Ext.panel.Panel',
    xtype: 'sfw.csiSpsGroupedView',

    itemId:'groupedgrid',
    border: 1,
    cls: 'shadow',

    requires:[
        'Ext.grid.feature.Grouping',
        'sfw.view.patching.SpspatchingController',
    ],

    flex: 1,
    layout: {
        type: 'fit',
        align: 'stretch'
    },

    items:[{
        xtype:'sfw.csiSpsCommonGrid',
        flex: 1,
        viewConfig: {
            stripeRows: false
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            itemId:'groupeddocks',
            items: [{
                xtype: 'combo',
                fieldLabel: 'Search Type',
                store: new Ext.data.SimpleStore({
                    fields: ["value", "label"],
                    data: [
                        ["1", "Product"],
                        ["2", "CVE"]
                    ]
                }),
                valueField: "value",
                value: "1",
                displayField: "label",
                mode: "local",
                editable: false,
                triggerAction: "all",
                selectOnFocus: false,
                itemId: 'groupedsearchtype',
            },
                {
                    xtype: 'textfield',
                    emptyText: 'Search text...',
                    itemId: 'groupedsearch'
                }, {
                    xtype: 'button',
                    text: 'Search',
                    ui:'primary',
                    handler: 'refreshSpsGrid'

                }, {
                    xtype: 'label'
                    , text: ' View from the context of Smart Group: '
                }, {
                    xtype: 'combo',
                    text: ' View from the context of Smart Group: ',
                    itemId: "groupedsmartgroupid",
                    name: "groupedsmartgroupid",
                    queryMode: 'local',
                    allowBlank: false,
                    editable: false,
                    forceSelection: false,
                    autoSelect: true,
                    triggerAction: 'all',
                    store: {
                        type: 'productsgscombo',
                        listeners: {
                            load: function () {
                                defaultSmartGroupIndex = this.find("name", "All Products", 0, false, true);
                                if (defaultSmartGroupIndex >= 0) {
                                    defaultSmartGroupValue = this.getAt(defaultSmartGroupIndex).get("id");
                                    Ext.ComponentQuery.query('#groupedsmartgroupid')[0].setValue(defaultSmartGroupValue);
                                }
                            }
                        }
                    },
                    valueField: "id",
                    displayField: "name",
                    triggerAction: "all",
                    selectOnFocus: false,
                    listeners: {
                        select: 'refreshSpsGrid'
                    }
                }, {
                    xtype: 'button',
                    ui: 'primary',
                    text: 'Configure View',
                    handler: 'configureView'
                }, {
                    xtype: 'tbfill',
                    flex: 1
                }, {
                    xtype: 'exportButton'
                }]
        }],
        bind:{
            store:'{groupedgrid}'
        },
        features: [{
            ftype:'grouping',
            groupHeaderTpl: '{columnName}: {name} ({rows.length} Item{[values.rows.length > 1 ? "s" : ""]})'
        }],
        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{groupedgrid}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying Products {0} - {1} of {2}',
            emptyMsg: "No vulnerable products are available to display."
        }
    }]
});