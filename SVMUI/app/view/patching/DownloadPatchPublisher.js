Ext.define('sfw.view.patching.DownloadPatchPublisher', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiDownloadPatchPublisher',
    title: 'Download Patch Publisher',
    scrollable: true,
    frame: true,
    bodyPadding: 10,
    bodyStyle: 'margin: 10px; padding: 5px 3px;',

    requires: [
        'sfw.view.patching.DownloadPatchPublisherController'
    ],

    controller: 'patchPublisherController',

    items:[{
        xtype: 'container',
        html: "<p>The Patch Daemon has taken a new form as SVM Patch Publisher with enhanced capabilities and user experience.<br>SVM Patch Publisher enables you to establish connection with the SVM supported endpoint management systems like <br>Microsoft WSUS, Intune, VMware Workspace ONE UEM, HCL BigFix etc., to publish patches.</p>"+
            "<p><a href='#' onclick='sfw.Default.externalURL(\"https://docs.flexera.com/csi/Content/helplibrary/SVM_Patch_Publisher.htm\");'>Click here</a> to learn about all the capabilities of the SVM Patch Publisher.</p>"
    }, {
        xtype: 'container',
        itemId: 'patchPublisher',
        tpl: '<ul>' +
            '<li>Click on the Download button to download the latest version of SVM Patch Publisher (ver. {patchPublisherVersion}, sha256: {sha256})</li>' +
            '<li><br></li>' +
            '<li><a href="#" onclick="sfw.Default.externalURL(\'{patchPubsliherData}\');"><button style="cursor:pointer;background-color: #2eadf5;border-color:#16a3f4;padding:3px 3px 3px 3px;border-width:1px;border-style:solid;color:white">Download</button></a> </li>' +
            '</ul>'
        // },{
        //     xtype: 'togglebutton',
        //     itemId: 'toggleButton',
        //     value: 1,
        //     style: 'margin-top: 50px;',
        //     width: 25,
        //     listeners: {
        //         changecomplete: function (slider, newValue, thumb, eOpts) {
        //             if(newValue == 1){
        //                 Ext.ComponentQuery.query('#patchPublisherGrid')[0].show();
        //             }else{
        //                 Ext.ComponentQuery.query('#patchPublisherGrid')[0].hide();
        //             }
        //         }
        //     }
        // },
    },{
        style: 'margin-top: 20px',
    },{
        xtype: 'gridpanel',
        itemId: 'patchPublisherGrid',
        store: {
            type: 'patchpublisher'
        },
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No data'
        },
        columns: [
            { text: 'Publisher Version',dataIndex:'publisher_version', flex:2},
            { text: 'SVM Version',dataIndex:'svm_version', flex:2},
            { text: 'SHA256',dataIndex:'sha256', flex:2},
            { text: 'Released Date',dataIndex:'created_at', flex:2, renderer:sfw.util.Default.renderSaidDate},
            { text: 'Download', flex:2, renderer: function (value, meta, record) {
                    url = "https://resources.flexera.com/tools/SVM/"+record.data.publisher_version +"/SVMPatchPublisher.msi";
                    return (url ? '<a href="#" onclick="sfw.Default.externalURL(\'' + url.replace("'", "\\'") + '\')">' + 'Download' + '</a>' : '-' );
                }},
        ],listeners:{
            afterrender:'getOldVersionData',
        },
        layout: 'fit',
        margin: '2 2 0 0',
        bbar: {
            xtype: 'pagingtoolbar',
            store: 'getPatchPublisherOldVersion',
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying {0} - {1} of {2}',
            emptyMsg: "No data"
        }
    }],

    listeners : {
        beforerender: 'callBeforeRender'
    }

});

