Ext.define('sfw.view.patching.VendorPatchProductReport', {
    extend: "Ext.window.Window",
    width: 850,
    height: 550,
    bodyPadding: 5,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",

    config : {
        productId : '',
        smartGroupId : '',
        vpmId : ''
    },

    viewModel : {
        stores : {
            vpmstore : {
                type : 'vpmproductreport',

            }
        }

    },
    items: [{
        // id: 'vendorPatchProductReport',
        xtype: 'grid',
        layout: 'fit',
        bind : '{vpmstore}',
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No Installations found'
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'label',
                text: ' View from the context of Smart Group: ',
                padding: '0 0 0 5'
            },{
                xtype: 'combo',
                text: ' View from the context of Smart Group: ',
                itemId: "sgcombo_common",
                name: "searchType_common",
                queryMode: 'local',
                store: {
                    type: 'productsgscombo',
                    listeners: {
                        load: function () {
                            defaultSmartGroupIndex = this.find("name", "All Products", 0, false, true);
                            if (defaultSmartGroupIndex >= 0) {
                                defaultSmartGroupValue = this.getAt(defaultSmartGroupIndex).get("id");
                                Ext.ComponentQuery.query('#sgcombo_common')[0].setValue(defaultSmartGroupValue);
                            }
                        }
                    }
                },
                valueField: "id",
                displayField: "name",
                triggerAction: "all",
                selectOnFocus: false,
                listeners: {
                    change : 'loadProductReport'
                },
            }]
        }],

        columns: [
            {text: 'Product', dataIndex: 'os_soft_name', flex: 1},
            {text: 'Architecture', dataIndex: 'architecture', flex: 1},
            {text: 'Insecure', dataIndex: 'num_insecure', flex: 1},
            {text: 'End-of-Life', dataIndex: 'num_eol', flex: 1},
            {text: 'Secure', dataIndex: 'num_patched', flex: 1},
            {text: 'Total', dataIndex: 'num_total', flex: 1},
            {text: 'Hosts', dataIndex: 'num_hosts', flex: 1},
        ],

        listeners: {
            beforerender : 'loadProductReport',
            itemdblclick: sfw.Default.viewInstallationdblcick
        },

        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{vpmproductreport}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying hosts {0} - {1} of {2}',
            emptyMsg: "No Installations found"
        }

    }],

    controller:{
        loadProductReport: function () {

            var me = this,
                view = me.getView(),
                grid = view.down('grid');
            var store = me.getViewModel().getStore('vpmstore');

            var product_id = view.getProductId();
            var sg_id = view.down('#sgcombo_common').value;
            var vpm_id = view.getVpmId();

            store.getProxy().setExtraParams({
                product_id: product_id,
                smartgroup_id: sg_id,
                vpm_id: vpm_id,

            });
            store.load();
        }
    }


});

