Ext.define('sfw.view.common.PublishQuickPatch', {
    extend: "Ext.window.Window",
    xtype: 'PublishQuickPatch',
    modal: true,
    constrainHeader: true,
    layout: 'fit',
    frame: true,
    bodyPadding: 20,
    width: 600,
    config: {
        selection: []
    },

    viewModel: {
        data: {
            selection: null,
            assignmentsDataQuickPatch: null,
            retain:false
        }
    },

    setSelection: function (selection) {
        if (!Ext.isEmpty(selection)) {
            this.getViewModel().set('selection', selection);
        }
    },

    items: [
        {
            xtype: 'fieldcontainer',
            items: [
                {
                    xtype: 'patchpublisherconnection',
                    itemId: 'patchconnectionId',
                    listeners: {
                        beforerender: 'patchPublishChecked'
                    }
                },
                {
                    xtype: 'button',
                    text: 'Manage Assignments',
                    itemId: 'manageAssignments',
                    ui: 'primary',
                    width: '30%',
                    hidden: true,
                    handler:sfw.util.Default.getAssignmentsQuickPatch
                },{
                    xtype: 'box',
                    html: '<hr style="width: 520px; height: 2px;"/>',
                    itemId: 'hrline'
                },
                {
                    xtype: 'box',
                    margin: '5 5 10 0',
                    html: 'Package configuration'
                }, {
                    xtype: 'checkbox',
                    checked: true,
                    itemId: 'customNamingId',
                    hideLabel: true,
                    boxLabel: '<span style="font-weight: normal;">Use Flexera custom naming</span>'
                }, {
                    xtype: 'fieldcontainer',
                    itemId: 'silentParamFieldCtrId',
                    layout: 'hbox',
                    items: [
                        {
                            xtype: 'checkbox',
                            itemId: 'silentParamCheckboxId',
                            labelWidth: 120,
                            boxLabel: '<span style="font-weight: normal;">Silent Parameters</span>',
                            listeners: {
                                change: 'onSilentChange'
                            }
                        },
                        {
                            xtype: 'textfield',
                            flex: 1,
                            disabled: true,
                            padding: '0 0 0 10',
                            width: 300,
                            itemId: 'silentParamTextFieldId',
                            hideLabel: true
                        }
                    ]
                },{
                    xtype: 'label',
                    itemId:'macNoteQuickPatch',
                    html: '<b><i class="fa fa-info-circle" style="font-size:118%"></i></b>&nbsp;Silent parameters are not required for Mac package'
                }
            ]
        }
    ],

    bbar: {
        layout: {
            pack: 'end'
        },
        items: [{
            text: 'Publish',
            ui: 'primary',
            handler: 'handlePublish',
            itemId:'publishQuickPatch',
            disabled:true
        }, {
            text: 'Cancel',
            handler: sfw.Default.closeSubscriptionWindow
        }]
    },

    controller: {
        patchPublishChecked: function(){
            var me = this,
                view = me.getView();
            if (!sfw.util.Auth.LoginDetails.isPatchPublisherInstalled){
                view.down('#patchconnectionId').setHidden(true);
                view.down('#hrline').setHidden(true);
            }
        },
        onSilentChange: function (cb, newVal) {
            var me = this,
                view = me.getView(),
                vm = me.getViewModel(),
                selection = vm.get('selection');
            if (newVal) {
                view.down('#silentParamTextFieldId').enable();
                if (view.down('#silentParamTextFieldId').getValue() == "") {
                    Ext.Ajax.request({
                        url: 'action=vpm_package&which=silent_details&vpm_id=' + selection[0].data.vpm_id,
                        success: function (data) {
                            var response = {};
                            response = Ext.util.JSON.decode(data.responseText);
                            view.down('#silentParamTextFieldId').setValue(response.silent_switches);
                        },
                        failure: function () {
                            sfw.util.Debug.log('Error while trying to get package details.');
                        }
                    });
                }
            } else {
                view.down('#silentParamTextFieldId').disable();
                view.down('#silentParamTextFieldId').setValue('');
            }
        },

        handlePublish: function () {
            var me = this,
                view = me.getView(),
                custom_naming = view.down('#customNamingId').getValue(),
                params_connection_id = view.down('#singlePatchConnections').getValue(),
                vm = me.getViewModel(),
                selection = vm.get('selection');
                assignmentData = vm.get('assignmentsDataQuickPatch');

            var vpm_data = selection.map(function (element) {
                return {vpm_id: element.data.vpm_id, version: element.data.version, product_name: element.data.product_name};
            });
            var silent_param = view.down('#silentParamTextFieldId').getValue();
            var isSilentParamChecked = view.down('#silentParamCheckboxId').getValue();
            if (params_connection_id != undefined  && params_connection_id != null){
                var connection_id = params_connection_id;
            } else {
                var connection_id = null;
            }
            var params = {
                custom_naming: custom_naming ? 'true' : 'false',
                vpm_data: vpm_data,
                connection_id: connection_id,
                assignment_connections: assignmentData
            };

            if ((silent_param != "") && (isSilentParamChecked)) {
                params['silent_param'] = silent_param;
            }

            Ext.Ajax.request({
                url: 'action=vpm_package&which=save_single_patch_package',
                method: 'POST',
                params: {
                    single_patch_data: Ext.util.JSON.encode(params)
                },
                success: function (data) {
                    sfw.Default.closeSubscriptionWindow();
                    var response = {};
                    response = Ext.util.JSON.decode(data.responseText);
                    if (response.success) {
                        if (response.error == 3) {
                            Ext.Msg.show({
                                title: 'Warning',
                                msg: "Already Sent to Patch Daemon",
                                buttons: Ext.Msg.OK,
                                icon: Ext.MessageBox.INFO
                            });
                        } else {
                            Ext.Msg.show({
                                title: 'Success',
                                msg: "Package is created and successfully scheduled for publish by the SVM Patch Publisher during its next check-in time",
                                buttons: Ext.Msg.OK,
                                icon: Ext.MessageBox.INFO
                            });
                            var currentView = sfw.common.SfwAccessor.getCurrentView();
                            if (currentView) {
                                currentView.getStore().load();
                            }
                        }
                    } else {
                        Ext.Msg.alert("Failure", "Not able to send to patch daemon please try again...");
                    }
                },
                failure: function () {
                    sfw.Default.closeSubscriptionWindow();
                    Ext.Msg.alert("Failure", "Not able to send to patch daemon please try again...");
                }
            });
            view.destroy();
        }
    }
});