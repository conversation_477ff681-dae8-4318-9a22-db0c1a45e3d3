Ext.define('sfw.view.common.ActiveDirectoryDialog', {
    extend: 'Ext.window.Window',

    width: 350,
    height: 450,
    scrollable: true,

    modal: true,
    resizable: false,
    layout: 'fit',

    title: 'Select Active Directory Node',

    config: {
        selectedDirectory: null
    },

    viewModel: {
        stores: {
            adStore: {
                type: 'tree',
                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        create: 'POST',
                        read: 'POST',
                        update: 'POST',
                        destroy: 'POST'
                    },
                    url: 'action=active_directory&which=getChildrenContainers&',
                    extraParams: {
                        includeNonAdNodes: true
                    }
                },
                root: {
                    text: "All computers",
                    id: "0" // The id is always a string
                }
            }
        }
    },

    items: [
        {
            xtype: 'treepanel',
            scrollable: true,
            ui: 'primary',
            bind: '{adStore}',
            listeners: {
                itemclick: 'onNodeClick'
            }
        }
    ],

    controller: {

        onNodeClick: function (panel, record) {
            var me = this,
                view = me.getView();
            view.setSelectedDirectory(record.data);
            view.close();
        }
    }

});
