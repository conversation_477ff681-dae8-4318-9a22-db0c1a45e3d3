Ext.define('sfw.view.common.PublishQuickPatchPT', {
    extend: "Ext.window.Window",
    xtype: 'PublishQuickPatchPT',
    modal: true,
    constrainHeader: true,
    layout: 'fit',
    frame: true,
    bodyPadding: 20,
    width: 500,
    height: 300,
    title: 'Publish with QuickPatch',
    requires:[
        'sfw.view.patching.PatchTemplateController'
    ],
    controller: 'patchtemplate',
    config: {
        record: null,
        selection: []
    },
    viewModel: {
        data: {
            record: null,
            selection: null,
            assignmentsDataQuickPatch: null,
            retain: false,
        },
    },
    setSelection: function (selection) {
        if (!Ext.isEmpty(selection)) {
            this.getViewModel().set('selection', selection);
        }
    },

    setRecord: function (record) {
        if (!Ext.isEmpty(record)) {
            this.getViewModel().set('record', record);
        }
    },

    items: [
        {
            xtype: 'fieldcontainer',
            items: [
                {
                    xtype: 'patchpublisherconnection'
                },
                {
                    xtype: 'button',
                    text: 'Manage Assignments',
                    itemId: 'manageAssignments',
                    ui: 'primary',
                    width: '30%',
                    hidden: true,
                    handler:sfw.util.Default.getAssignmentsQuickPatch
                }
        ]}
    ],

    buttons: [{
            text: 'Publish',
            tooltip: 'Save QuickPatch',
            handler: 'handlePublish',
            itemId:'publishQuickPatch',
            disabled:true
        }, {
            text: 'Cancel',
            handler: sfw.Default.closeSubscriptionWindow
        }]
});