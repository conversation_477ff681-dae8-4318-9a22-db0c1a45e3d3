Ext.define('sfw.view.common.ConfigureSubscription', {
    extend: "Ext.window.Window",
    xtype: 'configure-subscription',
    requires: [
        'sfw.view.commonpopupwindows.PatchPublisherConnectionComponent',
        'sfw.view.commonpanels.ManageAssignments'
    ],
    scrollable: "vertical",
    modal: true,

    resizable: false,
    constrainHeader: true,
    layout: 'auto',

    width: 550,
    //height: 500,

    bodyPadding: 20,

    config: {
        mode: 'patch',
        record: null,
        selection: []
    },

    viewModel: {
        data: {
            record: null,
            selection: null,
            assignmentsData: null
        },
        formulas: {
            productName: function (get) {
                const length = get('selection').length;
                if (length === 1) {
                    return 'Configure Subscription - ' + get('selection')[0].data.product_name;
                } else {
                    return 'Subscription For Multiple Packages';
                }
            },
            hideVersionField: function (get) {
                return get('selection').length !== 1;
            },
            versionFieldValue: function (get) {
                const selection = get('selection');
                return selection.length === 1 ? selection[0].data.secure_version : '';
            },
            hideThreatScore: function (get) {
                return sfw.util.Auth.LoginDetails.isThreatModuleEnabled;
            }
        }
    },

    bind: {
        title: '{productName}'
    },

    setSelection: function (selection) {
        if (!Ext.isEmpty(selection)) {
            this.getViewModel().set('selection', selection);
        }
    },

    setRecord: function (record) {
        if (!Ext.isEmpty(record)) {
            this.getViewModel().set('record', record);
        }
    },

    items: [
        {
            xtype: 'container',
            layout: {
                type: 'vbox'
            },
            defaults: {
                width: '100%'//,
                //padding: 5
            },

            items: [
                {
                    xtype: 'displayfield',
                    hidden: true,
                    itemId: 'subscriptionStartedOnId'
                },
                {
                    xtype: 'patchpublisherconnection',
                    itemId: 'patchconnectionId',
                    listeners: {
                        beforerender: 'patchPublishChecked'
                    }
                },
                {
                    xtype: 'button',
                    text: 'Manage Assignments',
                    itemId: 'manageAssignments',
                    ui: 'primary',
                    width: '30%',
                    hidden: true,
                    handler:sfw.util.Default.getAssignments
                },
                {
                    xtype: 'box',
                    html: '<hr style="width: 520px; height: 2px;"/>',
                    itemId: 'hrline'
                },
                {
                    xtype: 'radiogroup',
                    columns: 1,
                    itemId: 'alwaysPublishId',
                    hideLabel: true,
                    vertical: true,
                    listeners: {
                        change: 'tiggerHandler'
                    },
                    items: [
                        {
                            boxLabel: '<span style="font-weight: normal;">Always publish a new patch when a new version is available</span>',
                            name: 'publishSelection',
                            itemId: 'alwaysPublish',
                            inputValue: '1'
                        },
                        {
                            boxLabel: '<span style="font-weight: normal;">Only publish a new patch when any of the following are true</span>',
                            name: 'publishSelection',
                            inputValue: '2',
                            itemId: 'onlyPublish',
                            checked: true
                        }
                    ]
                },
                {
                    xtype: 'container',
                    padding: 5,
                    itemId: 'subsComboCtrId',
                    defaults: {
                        width: '100%'
                    },
                    items: [
                        {
                            xtype: 'combo',
                            name: 'cvss3Score',
                            itemId: 'cvss3ScoreItemId',
                            fieldLabel: 'SAID CVSS score is greater than',
                            store: new Ext.data.SimpleStore({
                                fields: ['value'],
                                data: [[0], [1], [2], [3], [4], [5], [6], [7], [8], [9]]
                            }),
                            valueField: 'value',
                            displayField: 'value',
                            selectOnFocus: false,
                            mode: 'local',
                            typeAhead: false,
                            labelWidth: 250,
                            value: 0,
                            editable: false,
                            triggerAction: 'all',
                            forceSelection: false,
                            labelSepartor: '',
                            labelStyle: 'padding: 5px 5px 5px 20px;',
                            emptyText: 'Not selected ...'
                        }, {
                            xtype: 'combo',
                            name: 'criticality',
                            itemId: 'criticalityItemId',
                            labelWidth: 250,
                            fieldLabel: 'Criticality is greater than',
                            store: new Ext.data.SimpleStore({
                                fields: ['value', 'label'],
                                data: [
                                    ['1', 'Extremely Critical'],
                                    ['2', 'Highly Critical'],
                                    ['3', 'Moderately Critical'],
                                    ['4', 'Less Critical'],
                                    ['5', 'Not Critical']
                                ]
                            }),
                            valueField: 'value',
                            displayField: 'label',
                            selectOnFocus: false,
                            mode: 'local',
                            value: 5,
                            typeAhead: false,
                            editable: false,
                            triggerAction: 'all',
                            forceSelection: false,
                            labelSepartor: '',
                            labelStyle: 'padding: 5px 5px 5px 20px;',
                            emptyText: 'Not selected ...'
                        }, {
                            xtype: 'combo',
                            name: 'threatScore',
                            itemId: 'threatScoreItemId',
                            labelWidth: 250,
                            fieldLabel: 'Threat score is greater than',
                            store: new Ext.data.SimpleStore({
                                fields: ['value'],
                                data: [[0], [10], [20], [30], [40], [50], [60], [70], [75], [80], [85], [90], [95]]
                            }),
                            valueField: 'value',
                            displayField: 'value',
                            selectOnFocus: false,
                            mode: 'local',
                            value: 0,
                            typeAhead: false,
                            editable: false,
                            triggerAction: 'all',
                            forceSelection: false,
                            labelSepartor: '',
                            labelStyle: 'padding: 5px 5px 5px 20px;',
                            emptyText: 'Not selected ...'
                        }, {
                            xtype: 'textfield',
                            name: 'version',
                            itemId: 'versionFieldId',
                            labelWidth: 250,
                            emptyText: 'Enter version number',
                            fieldLabel: 'Patched version greater than',
                            labelSepartor: ':',
                            labelStyle: 'padding: 5px 5px 5px 20px;',
                            bind: {
                                hidden: '{hideVersionField}'
                            }
                        }
                    ]
                }, {
                    xtype: 'box',
                    html: '<hr style="width: 520px; height: 2px;"/>'
                },
                {
                    xtype: 'radiogroup',
                    columns: 1,
                    itemId: 'forceTriggerId',
                    hideLabel: true,
                    vertical: true,
                    items: [
                        {
                            boxLabel: '<span style="font-weight: normal;">Trigger subscription rule above now for the current version</span>',
                            name: 'forceTriggerSelection',
                            itemId: 'triggerSubscriptionCurrentVersion',
                            inputValue: '1'
                        },
                        {
                            boxLabel: '<span style="font-weight: normal;">Trigger subscription rule above next time a new version is available</span>',
                            name: 'forceTriggerSelection',
                            inputValue: '2',
                            itemId: 'triggerSubscriptionNewVersion',
                            checked: true
                        }
                    ]
                }, {
                    xtype: 'box',
                    html: '<hr style="width: 520px; height: 2px;" />'
                }, {
                    xtype: 'box',
                    style: {
                        paddingTop: '10px'
                    },
                    html: 'Package configuration'
                }, {
                    xtype: 'checkbox',
                    checked: false,
                    itemId: 'customNamingId',
                    hideLabel: true,
                    boxLabel: '<span style="font-weight: normal;">Use Flexera custom naming</span>'
                }, {
                    xtype: 'fieldcontainer',
                    itemId: 'silentParamFieldCtrId',
                    hideLabel: true,
                    layout: 'hbox',
                    hidden: true,
                    items: [
                        {
                            xtype: 'checkbox',
                            itemId: 'silentParamCheckboxId',
                            labelWidth: 120,
                            boxLabel: '<span style="font-weight: normal;">Silent Parameters</span>',
                            listeners: {
                                change: 'onSilentChange'
                            }
                        },
                        {
                            xtype: 'textfield',
                            flex: 1,
                            disabled: true,
                            padding: '0 0 0 10',
                            itemId: 'silentParamTextFieldId',
                            hideLabel: true
                        }
                    ]
                },{
                    xtype: 'label',
                    itemId:'macNote',
                    html: '<b><i class="fa fa-info-circle" style="font-size:118%"></i></b>&nbsp;Silent parameters are not required for Mac package'
                }
            ]
        }
    ],

    buttons: [{
        text: 'Unsubscribe',
        tooltip: 'Remove Subscription Settings',
        itemId: 'unsubscribeBtnId',
        hidden: true,
        handler: 'handleUnSubscribe'
    }, {
        text: 'Save',
        tooltip: 'Save Subscription Settings',
        handler: 'handleSubscribe',
        itemId:'subscriptionSave',
        disabled:true
    }, {
        text: 'Cancel',
        handler: sfw.Default.closeSubscriptionWindow
    }],

    listeners: {
        afterrender: 'onAfterRender',
        close: sfw.Default.closeSubscriptionWindow
    },

    controller: {
        patchPublishChecked: function(){
            var me = this,
                view = me.getView();
            if (!sfw.util.Auth.LoginDetails.isPatchPublisherInstalled){
                view.down('#patchconnectionId').setHidden(true);
                view.down('#hrline').setHidden(true);
            }
        },
        onAfterRender: function (view) {
            var me = this,
                vm = me.getViewModel(),
                selection = vm.get('selection'),
                record = vm.get('record');

            if (sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                view.down('#threatScoreItemId').show();
            } else {
                view.down('#threatScoreItemId').hide();
            }

            if ((1 === selection.length) && (1 == selection[0].data.subscription_status)) {
                view.down('#unsubscribeBtnId').show();
                var createdOn = sfw.util.Default.gridRenderUTCDateInLocaltime(record.created_on);

                //Show unsubscribe button if row already subscribed
                view.down('#subscriptionStartedOnId').setHtml("<b>Subscription started on " + createdOn + "</b>");
                view.down('#subscriptionStartedOnId').show();

                view.down('#alwaysPublishId').setValue(record.always_trigger === "1" ? '1' : '2');
                if (record.always_trigger === "0") {
                    view.down('#cvss3ScoreItemId').setValue(record.vuln_cvss3_score);
                    view.down('#criticalityItemId').setValue(record.vuln_criticality);
                    view.down('#threatScoreItemId').setValue(record.vuln_threat_score);
                    if (record.version !== "") {
                        view.down('#versionFieldId').setValue(record.version);
                    }
                } else {
                    view.down('#subsComboCtrId').disable();
                }

                view.down('#forceTriggerId').setValue(record.publish_asap === "1" ? '2' : '1');
                view.down('#customNamingId').setValue(record.custom_naming);

                if (view.getMode() === 'vpm') {
                    view.down('#silentParamFieldCtrId').show();
                    if (record.silent_switches !== "") {
                        view.down('#silentParamCheckboxId').setValue(true);
                        view.down('#silentParamTextFieldId').setValue(record.silent_switches);
                        view.down('#silentParamTextFieldId').enable();
                    }
                }
            }

            if (view.getMode() === 'vpm') {
                view.down('#silentParamFieldCtrId').show();

                if (Ext.isEmpty(record)) {
                    Ext.Ajax.request({
                        url: 'action=vpm_package&which=silent_details&vpm_id=' + selection[0].data.vpm_id + '&',
                        success: function (data) {
                            const response = Ext.decode(data.responseText, true) || {};
                            view.down('#silentParamTextFieldId').setValue(response.silent_switches);
                        },
                        failure: function () {
                            sfw.util.Debug.log('Error while trying to get package details.');
                        },
                        scope: me
                    });
                }
            }
        },

        tiggerHandler: function (field) {
            var me = this,
                view = me.getView(),
                selectedValue = field.getValue()['publishSelection'];

            if (selectedValue === '2') {
                view.down('#subsComboCtrId').enable();
            } else {
                view.down('#subsComboCtrId').disable();
            }
        },

        onSilentChange: function (cb, newVal) {
            var me = this,
                view = me.getView();

            if (newVal) {
                view.down('#silentParamTextFieldId').enable();
            } else {
                view.down('#silentParamTextFieldId').disable();
            }
        },

        handleSubscribe: function () {
            var me = this,
                view = me.getView(),
                selection = me.getViewModel().get('selection'),
                data = [],
                url = '';

            var assignments = me.getViewModel().get('assignmentsData');
            var always_trigger = view.down('#alwaysPublishId').getValue()['publishSelection'],
                cvss3_score = view.down('#cvss3ScoreItemId').getValue(), //1
                vuln_criticality = view.down('#criticalityItemId').getValue(), //2
                threat_score = view.down('#threatScoreItemId').getValue(), //3
                version = view.down('#versionFieldId').getValue(), //4 textfield
                params_connection_id = view.down('#singlePatchConnections').getValue();

            var force_trigger = view.down('#forceTriggerId').getValue()['forceTriggerSelection'], //second radiofield
                custom_naming = view.down('#customNamingId').getValue();

            var params = {
                always_trigger: always_trigger === '1' ? 'true' : 'false',
                vuln_cvss3_score: cvss3_score,
                vuln_criticality: vuln_criticality,
                vuln_threat_score: threat_score,
                force_trigger: force_trigger === '1' ? 'true' : 'false',
                custom_naming: custom_naming ? 'true' : 'false'
            };

            if (view.getMode() === 'vpm') {
                data = selection.map(function (element) {
                    return {
                        vpm_id: element.data.vpm_id,
                        version: element.data.version,
                        product_name: element.data.product_name
                    };
                });
                params['vpm_data'] = data;

                var silent_param = view.down('#silentParamTextFieldId').getValue(); //4 textfield
                var isSilentParamChecked = view.down('#silentParamCheckboxId').checked;
                if ((silent_param != "") && (isSilentParamChecked)) {
                    params['silent_param'] = silent_param;
                }
                url = 'action=vpm_package&which=subscribe_package';
            } else {
                data = selection.map(function (element) {
                    return {
                        template_id: element.data.template_id,
                        version: element.data.secure_version,
                        template_name: element.data.template_name
                    };
                });
                params['sps_data'] = data;
                url = 'action=sps_package&which=subscribe_package';
            }

            if (!sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                params.vuln_threat_score = null;
            }
            if (params_connection_id != undefined  && params_connection_id != null){
                params.connection_details  = params_connection_id;
            } else {
                params.connection_details = null;
            }
            if (assignments != undefined  && assignments != null){
                params.assignment_connections  = assignments;
            }

            //Check fields which are not support multiple selection and add them if it is single selection
            //if it is single selection then those values might present otherwise not.
            if (version != "") {
                params['version'] = version;
            }

            Ext.Ajax.request({
                url: url,
                method: 'POST',
                params: {
                    subscription_data: Ext.encode(params)
                },
                success: function (data) {
                    view.close();
                    var response = Ext.decode(data.responseText, true) || {};
                    if (response.success) {
                        var currentView = sfw.common.SfwAccessor.getCurrentView();
                        if (currentView) {
                            currentView.getStore().load();
                        }

                        Ext.Msg.show({
                            title: 'Success',
                            msg: "Patch subscribed successfully",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    } else {
                        Ext.Msg.alert("Failure", "Not able to subscribe package's please try again...");
                    }
                },
                failure: function () {
                    Ext.Msg.alert("Failure", "Not able to subscribe package's please try again...");
                },
                scope: me
            });
        },

        handleUnSubscribe: function () {
            var me = this,
                view = me.getView(),
                selection = me.getViewModel().get('selection'),
                params = {},
                url = '';

            if (view.getMode() === 'vpm') {
                params = {
                    vpm_id: selection[0].data.vpm_id
                };
                url = 'action=vpm_package&which=unsubscribe_package';
            } else {
                params = {
                    template_id: selection[0].data.template_id,
                    template_name: selection[0].data.template_name
                };
                url = 'action=sps_package&which=unsubscribe_package';
            }

            Ext.Ajax.request({
                url: url,
                params: params,
                method: 'GET',
                success: function (data) {
                    view.close();
                    var response = Ext.decode(data.responseText, true) || {};
                    if (response.success) {
                        var currentView = sfw.common.SfwAccessor.getCurrentView();
                        if (currentView) {
                            currentView.getStore().load();
                        }

                        Ext.Msg.show({
                            title: 'Success',
                            msg: "Patch unsubscribed successfully",
                            buttons: Ext.Msg.OK,
                            icon: Ext.MessageBox.INFO
                        });
                    } else {
                        Ext.Msg.alert("Failure", "Not able to Unsubscribe package's please try again...");
                    }
                },
                failure: function () {
                    Ext.Msg.alert("Failure", "Not able to Unsubscribe package's please try again...");
                },
                scope: me
            });
        }
    }
});