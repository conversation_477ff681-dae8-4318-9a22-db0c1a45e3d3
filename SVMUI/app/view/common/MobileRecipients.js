Ext.define('sfw.view.common.MobileRecipients',{
    extend: 'Ext.Panel',
    xtype: 'common.mobilerecipients',
    itemId: 'selectMobileRecipient',
    layout: "auto",
    ui: 'light',
    title: "Select Mobile Recipients",
    padding: "0px 10px 10px 10px",
    frame: true,
    items:[{
        layout:{
            type:'hbox',
            aling:'stretch',
        },
        items:[{
            xtype: 'checkboxfield',
            itemId:'defaultMobileRecipients',
            checked:false,
            name:'defaultMobileRecipients',
            boxLabel:'Use default recipients defined in Settings page',
            listeners: {
                change: function(){
                    var mobilerecipients = this.up("[xtype='common.mobilerecipients']");
                    if(this.getValue()){
                        mobilerecipients.down('#localItemSelector').setDisabled(true);

                    }else{
                        mobilerecipients.down('#localItemSelector').setDisabled(false);
                    }
                }
            }
        }]
    }]
});