var columns = [
    {text: "Name", dataIndex: "name", flex: 4, sortable: true},
    {
        text: "File",
        dataIndex: "output_file",
        align: "left",
        sortable: true
    },
    {
        text: "Content",
        flex: 2,
        dataIndex: "table_name",
        align: "right",
        sortable: true
    },
    {
        text: "Frequecy",
        flex: 1,
        renderer: 'frequencyRender',
        dataIndex: "frequency_unit",
        align: "right",
        sortable: true
    },
    {
        text: "Next Run",
        renderer: 'dateRender',
        dataIndex: "first_run_time",
        align: "right",
        sortable: true
    },
    {
        text: "Last Run",
        flex: 1,
        renderer: 'dateRender',
        dataIndex: "last_run_time",
        align: "right",
        sortable: true
    },
    {
        text: "Last Execution Status",
        flex: 1,
        renderer: 'statusRender',
        dataIndex: "last_status",
        align: "right",
        sortable: true
    }
];

Ext.define('sfw.view.reporting.ScheduledExports', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiScheduledExports',
    id: 'scheduledExport_grid',
    title: 'Scheduled Exports',
    border: 1,
    cls: 'shadow',
    requires: [
        'sfw.view.reporting.ScheduledExportsController',
        'sfw.store.reporting.ScheduledExports',
        'sfw.Util'
    ],
    stateful: true,
    stateId: 'sfw_csiScheduledExports_grid',
    store: {
        type: 'scheduledexports'
    },

    selModel: {
        mode: 'MULTI'
    },

    controller: 'reporting-scheduledexports',
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No scheduled exports'
    },

    dockedItems: [
        {
            xtype: "toolbar",
            dock: "top",
            items: [
                {
                    xtype: 'label',
                    html: 'You can create scheduled export from the database console'
                },
                {
                    xtype: "tbfill",
                    flex: 1
                },
                {
                    xtype: "exportButton"
                }
            ]
        }
    ],

    columns: columns,

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            additionalText = (selected.items.length > 1) ? 'Delete ' + selected.items.length + ' Scheduled Exports' : 'Delete';
            var contextMenu = Ext.create("Ext.menu.Menu", {
                controller: "reporting-scheduledexports",
                plain: true,
                items: [
                    {
                        text: "Edit",
                        disabled:sfw.util.Auth.LoginDetails.isReadOnly,
                        listeners: {
                            afterRender: function () {
                                if (selected.items.length > 1) {
                                    this.hide();
                                }
                            },
                            click: {fn: 'viewScheduleReportWindow', extra: record}
                        }
                    },
                    {
                        text: additionalText,
                        disabled:sfw.util.Auth.LoginDetails.isReadOnly,
                        listeners: {
                            click: {fn: 'confirmDeleteSelectedScheduledExports', extra: selected.items}
                        }
                    }
                ]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'dblclickHandler'
    },

    bbar: {
        xtype: "pagingtoolbar",
        bind: {
            store: "{scheduledexports}"
        },
        displayInfo: true,
        pageSize: 30,
        displayMsg: "Displaying Scheduled Export {0} - {1} of {2}",
        emptyMsg: "No Scheduled Exports configured"
    }
});
