Ext.define('sfw.view.reporting.DatabaseConsole', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiDbConsole',
    id: 'databaseconsole',
    title: 'Database Console',
    requires: [
        'sfw.view.reporting.DatabaseConsoleController',
        'sfw.view.reporting.DatabaseConsoleModel',
        'sfw.view.reporting.DatabaseConsoleTreePanel'
    ],

    layout: 'border',

    controller: 'databaseconsolecontroller',
    viewModel: 'dbconsole',
    defaults: {
        ui: 'gray',
        border: 1
    },
    items: [{
        title: 'Results',
        region: 'south',
        xtype: 'gridpanel',
        id: 'dbconsoleresultgrid',
        height: 400,
        collapsible: true,
        margin: 5,
        viewConfig: {
            deferEmptyText: false,
            emptyText: 'No results'
        },
        bind: {
            store: "{dbconsoleresult}"
        },
        columns: [],
        usePost: true,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'panel',
                flex: 1
            }, {
                xtype: 'exportButton'
            }]
        }],
        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{dbconsoleresult}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying {0} - {1} of {2}',
            emptyMsg: "No results"
        }
    }, {
        title: 'Tables',
        region: 'west',
        xtype: 'dbconsoletreepanel',
        margin: 5,
        width: 300,
        layout: 'fit',
        // split: true,
        bbar: [
            '->',
            {
                text: 'Refresh',
                handler: "reload"
            }
        ]
    }, {
        title: 'Details',
        region: 'center',
        xtype: 'gridpanel',
        bind: {
            store: '{dbConsoleDetails}'
        },
        columns: [
            {
                text: 'Time', dataIndex: 'time', flex: 1, renderer: Ext.util.Format.dateRenderer('H:i:s')
            },
            {text: 'Status', dataIndex: 'status', flex: 1},
            {text: 'Table', flex: 1},
            {text: 'Query', dataIndex: 'query', flex: 5}],
        layout: 'fit',
        margin: 5,
        bbar: [
            '->',
            {
                xtype: 'button',
                text: 'Clear',
                handler: function () {
                    //CSIApiConsole.queryStatusStore.loadData([], false);
                    Ext.getStore('dbconsoledetails').loadData([], false);
                }
            }
        ]
    }]
});



