Ext.define('sfw.view.reporting.SmartgroupNotificationWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.smartgroupnotificationwindow',
    width: 1100,
    height: 700,
    layout: 'auto',
    padding: '10px 10px 10px',
    autoScroll: true,
    modal:true,
    maximizable: true,
    controller: 'smartgroupnotifications',
    config: {
        mode: ''
    },

    items: [{
        xtype: 'panel',
        ui: 'light',
        autoWidth: true,
        autoScroll: true,
        title: 'Notification Details',
        padding: '10px 10px 10px',
        frame: true,
        layout: "auto",
        items: [{
            xtype: 'fieldset',
            layout: 'auto',
            height: 'auto',
            title: 'Name & Applicability',
            items: [{
                html: 'You must give this notification a name (or short desription) to be used when receiving alerts.  Here you will also select the Events for which the notification will apply.'
            }, {
                layout: 'hbox',
                padding: '15 10 0 10',
                items: [{
                    layout: 'form',
                    flex: 1,
                    width: 450,
                    items: [{
                        xtype: 'textfield',
                        allowBlank: false,
                        anchor: '97%',
                        fieldLabel: 'Name',
                        enableKeyEvents: true,
                        emptyText: 'Enter a name (or short description) for this notification...',
                        itemId: 'notification_name',
                        listeners: {
                            change: "validateForm"
                        }

                    }, {
                        xtype: "combo",
                        store: {
                            type: 'notificationsglist'
                        },
                        queryMode: 'local',
                        valueField: 'id',
                        displayField: 'text',
                        mode: 'local',
                        fieldLabel: 'Smart Group',
                        allowBlank: false,
                        editable: false,
                        name: 'frequency_type',
                        hiddenName: 'frequency_type',
                        triggerAction: 'all',
                        itemId: 'smartgroup',
                        emptyText: 'Select a Smart Group',
                        listeners: {
                            select: 'smartGroupControl'
                        }
                    }]

                }]
            }]
        }, {
            xtype: 'fieldset',
            layout: 'form',
            height: 'auto',
            disabled: true,
            itemId: 'alert_conditions',
            title: 'Alert Conditions',
            items: [{
                html: 'Choose the conditions under which you will receive an Alert.'
            }, {
                layout: 'hbox',
                padding: '15 15 15 0',
                items: [{
                    xtype: "combo",
                    valueField: 'id',
                    displayField: 'text',
                    mode: 'local',
                    labelWidth: 150,
                    fieldLabel: 'ALERT me when the',
                    itemId: 'alert_options',
                    allowBlank: false,
                    editable: false,
                    name: 'frequency_type',
                    hiddenName: 'frequency_type',
                    triggerAction: 'all',
                    emptyText: 'Choose Option',
                    listeners: {
                        change: "validateForm"
                    }
                }, {
                    xtype: "combo",
                    store: {
                        fields: ['index', 'dayName'],
                        data: [[1, 'Changes'], [2, 'Increases'], [3, 'Decreases'], [4, 'Increases by ...'], [5, 'Decreases by ...']]
                    },
                    valueField: 'index',
                    displayField: 'dayName',
                    mode: 'local',
                    itemId: 'alert_actions',
                    allowBlank: false,
                    editable: false,
                    name: 'frequency_day',
                    hiddenName: 'frequency_day',
                    triggerAction: 'all',
                    emptyText: 'Choose Action',
                    margin: "0 0 0 10",
                    listeners: {
                        select: 'alertActionControl'
                    }

                }, {
                    xtype: 'textfield',
                    hidden: true,
                    width: 45,
                    maxLength: 4,
                    minValue: 1,
                    value: 10,
                    margin: "0 0 0 10",
                    itemId: 'action_value',
                    listeners: {
                        change: "validateForm"
                    }
                }, {
                    xtype: 'label',
                    hidden: true,
                    text: '%',
                    padding: '0 0 0 5',
                    itemId: 'action_percent'
                }]
            }, {
                xtype: 'combo',
                padding: '0 0 5 5',
                name: 'frequency_time',
                itemId: 'frequency',
                store: {
                    fields: ['id', 'text'],
                    data: [[1, 'Hourly'], [2, 'Daily'], [3, 'Weekly'], [4, 'Monthly']]
                },
                valueField: 'id',
                displayField: 'text',
                mode: 'local',
                fieldLabel: 'How often should this notification rule run? Period is based on when the rule is saved/modified',
                enableKeyEvents: true,
                autoSelect: true,
                editable: false,
                emptyText: 'Choose Frequency',
                listeners: {
                    change: "validateForm"
                }
            }, {
                xtype: 'checkboxfield',
                itemId: 'always_notify',
                labelAlign: 'left',
                fieldLabel: 'NOTIFY me when the conditions are NOT met. I.e., leave unchecked for a \'no news is good news\' policy.',
                name: 'notify',
                checked: false
            }]

        }, {
            xtype: 'fieldset',
            layout: 'form',
            height: 'auto',
            labelWidth: 10,
            name: 'emailrecipients',
            title: 'Recipients Selection',
            items: [
                {
                    xtype: 'reporting.emailRecipients'
                },{
                    xtype:'tbspacer',
                    height:10
                },{
                    xtype: 'common.mobilerecipients'
                }
            ]
        }]
    }],

    buttons: [
        {
            text: 'Save',
            formBind: true,
            tabIndex: 9,
            handler: "saveNotification",
            itemId: 'saveSgNotification'
        }, {
            text: 'Close',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]

});
