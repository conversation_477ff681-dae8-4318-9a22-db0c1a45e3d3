
var frequencyConstants= [
    { id: -1, text: '--' }
    ,{ id:1, text:'Hourly' }
    ,{ id:2, text:'Daily' }
    ,{ id:3, text:'Weekly' }
    ,{ id:4, text:'Monthly' }];

Ext.define('sfw.view.reporting.ScheduledExportsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.reporting-scheduledexports',

    dateRender : function(value) {
        if (value && value != '0000-00-00 00:00:00'){
            var date = sfw.Util.dateCreate(value, true);
            return Ext.util.Format.date(date,sfw.util.Globals.dateLongOutput);
        } else {
            return '-';
        }
    },

    dblclickHandler: function () {
        const self = this,
            view = self.getView();
        const record = view.getSelection()[0];
        this.viewScheduleReportWindow(null, null, {extra: record});
    },

    viewScheduleReportWindow:function(event, target, options){
        var scheduleReportWindow = Ext.create("sfw.view.commonpopupwindows.ScheduleReportSetup",{
            listeners: {
                afterrender: function () {
                    var value = options.extra.data.first_run_time;
                    if (options.extra.data.first_run_time && value != '0000-00-00 00:00:00'){
                        var date = sfw.Util.dateCreate(value, true);
                        scheduleReportWindow.down("#xfirstrundate").setValue(date);
                        scheduleReportWindow.down("#xfirstruntime").setValue(date);
                    }
                    if (options.extra.data.frequency_unit == 0){
                        scheduleReportWindow.down('#run_once').setValue(true);
                    }
                    else {
                        scheduleReportWindow.down("#frequency_unit").setValue(frequencyConstants[options.extra.data.frequency_unit]);
                    }
                    scheduleReportWindow.down("#scheduled_exports_name").setValue(options.extra.data.name);
                    scheduleReportWindow.down("#output_file_name").setValue(options.extra.data.output_file);
                    scheduleReportWindow.down("#id").setValue(options.extra.data.id);
                    scheduleReportWindow.down('#table').setValue(options.extra.data.table_name);
                }
            }
        });
        scheduleReportWindow.show();
    },

    confirmDeleteSelectedScheduledExports : function (event, target, options){
        var _this = this;
        var ids =[],names=[], params = {};
        for (var i=0; i<options.extra.length; ++i){
            ids.push(options.extra[i].data.id);
            names.push(options.extra[i].data.name);
        }
        params['ids[]']=ids;
        params['names[]']=names;
        Ext.Msg.show({
            title:'Delete Scheduled Export',
            message: 'Please confirm that you wish to delete the selected Scheduled Export',
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    _this.deleteSelectedScheduledExports(params);
                } else if (btn === 'no') {
                    //console.log('NO');
                }
            }
        });
    },

    deleteSelectedScheduledExports: function(deleteparams){
        Ext.Ajax.request({
            url: 'action=ajaxapi_scheduled_exports&which=delete',
            method: 'POST',
            dataType: 'json',
            params: deleteparams,
            success: function( data ) {
                data = Ext.util.JSON.decode( data.responseText );
                if ( data.success ) {
                    Ext.Msg.alert('Success', data.msg);
                    Ext.getCmp('scheduledExport_grid').getStore().reload();
                } else {
                    Ext.Msg.alert( 'Unexpected error', 'An unexpected error has occurred.' );
                }
                if ( parseInt( data.status, 10 ) === 0 ) {
                    self.refresh();
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Deleting record failed");
            }
        });
    },

    frequencyRender: function( value, metaData, record, rowIndex, colIndex, store ) {
        var val = Ext.num( value, -1 );
        if ( val === 0 ) {
            return 'One-Time Export';
        } else if ( val !== -1 && val !== frequencyConstants[ val ] ) {
            if ( val > 4 ) {
                val = 4;
            }
            return frequencyConstants[ val ].text;
        }
        return 'Unknown';
    },

    statusRender : function( value, cell, record ) {
        if ( record.data.last_run_time.length === 0 ) {
            return '-';
        }
        var sClass = 'StringSuccess';
        var status = 'Success';
        if ( value.length > 0 ) {
            sClass = 'StringError';
            status = 'Failed';
        }
        if ( typeof record.get === 'function' ) {
            return '<span class="' + sClass + '" ' + ( value.length > 0 ? 'qtip="' + Ext.util.Format.htmlEncode( value ) + '"' : '' ) + '>' + status + '</span>';
        }
        return status;
    }
});
