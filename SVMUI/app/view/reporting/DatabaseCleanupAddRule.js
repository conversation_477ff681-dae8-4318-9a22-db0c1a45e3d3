Ext.define('sfw.view.reporting.DatabaseCleanupAddRule',{
    extend:'Ext.window.Window',
    xtype: 'sfw.dbcleanupaddrule',

    controller: 'reporting-databasecleanup',
    itemId: 'dbcleanupaddrule',
    layout: 'fit',
    modal: true,
    bodyPadding: 10,
    title: 'New Rule',

    items:[{
        xtype: 'form',
        layout: 'anchor',

        items: [{
            xtype: 'displayfield',
            fieldLabel: 'Action ',
            labelWidth: 80,
            value: 'Delete Host',
        },{
            xtype: 'textfield',
            allowBlank: false,
            name: 'name',
            labelWidth: 80,
            fieldLabel: 'Name',
            itemId: 'dbrulename',
            listeners: {
                keyup: {
                    element: 'el',
                    fn: 'validateRule'
                }
            }
        },{
            xtype: 'container',
            layout: 'hbox',
            items: [{
                xtype: 'combobox',
                labelWidth: 80,
                fieldLabel: 'Criterion',
                displayField: 'duration',
                itemId: 'dbrulecriterion',
                editable: false,
                valueField: 'id',
                value: '1',
                store: [
                    {id: '1', duration: 'Last Scanned Time'},
                    {id: '2', duration: 'Last Check-in Time'},
                    {id: '3', duration: 'Never Scanned'}
                ],
                listeners: {
                    select: function( combo, record ) {
                        if ( parseInt( record.get( "id" ), 10 ) === 3 ) {
                            Ext.ComponentQuery.query('#dbruleperiod')[0].disable( true );
                            Ext.ComponentQuery.query('#dbruleperiod')[0].clearInvalid();
                            Ext.ComponentQuery.query('#dbruletime')[0].disable( true );
                        } else {
                            Ext.ComponentQuery.query('#dbruleperiod')[0].enable();
                            Ext.ComponentQuery.query('#dbruletime')[0].enable();
                        }
                    },
                    afterrender: "validateRule"
                }
            }, {
                xtype:'displayfield',
                value: 'More than',
                margin: "0 10 0 10"
            }, {
                xtype: 'numberfield',
                allowBlank: false,
                allowDecimals: false,
                allowNegative: false,
                name: 'name',
                minValue: 1,
                maxLength: 2,
                width: 100,
                itemId: 'dbruleperiod',
                margin: "0 10 0 10",
                listeners: {
                    keyup: {
                        element: 'el',
                        fn: 'validateRule'
                    }
                }
            },{
                xtype: 'combobox',
                displayField: 'time',
                valueField: 'id',
                itemId: 'dbruletime',
                editable: false,
                value: '3',
                width: 100,
                store:[
                    {id: '1', time: 'Days'},
                    {id: '2', time: 'Weeks'},
                    {id: '3', time: 'Months'}
                ]
            }]
        }]
    }],

    buttons: [
        {
            text: 'Save',
            itemId: 'saveRuleButton',
            formBind: true,
            //ui: 'primary',
            handler: "saveDBRule"
        },
        {
            text: 'Cancel',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]

})