Ext.define('sfw.view.reporting.ReportConfiguration', {
	extend: 'Flexera.widgets.grid.Abstract',
	xtype: 'sfw.csiReportingConfiguration',
	id: 'reportConfigGrid',

	requires: [
		'sfw.view.reporting.ReportingController',
		'sfw.store.reporting.Reporting',
		'sfw.util.Util'
	],
	stateful: true,
	stateId: 'sfw_csiReportingConfiguration_grid',
	store: {
		type: 'reportingconfig'
	},

    selModel: {
        mode: 'MULTI'
    },

	title: 'Report Configuration',
	border: 1,
	cls: 'shadow',

	controller: 'reports',
	viewConfig : {
        deferEmptyText: false,
        emptyText: 'No reports scheduled for the generation.'
    },

	dockedItems: [{
		xtype: 'toolbar',
		dock: 'top',
		items: [
			{
				xtype: 'button',
				text: 'Generate New Report',
				ui: 'primary',
				listeners: {
                    click: {fn: 'openReportConfigurationWindow', extra: {titleType: 'new'} },
					beforerender: function(){
                    	this.setDisabled(sfw.util.Auth.LoginDetails.isReadOnly);
					}
                }
			},{
				xtype: 'textfield',
				emptyText: 'Search by Report Title',
				itemId: 'report_title',
				listeners: {
					specialkey: 'reloadReportConfigurationGridOnEnter'
				}
			},{
				xtype: 'button',
				text: 'Search',
				ui: 'primary',
				handler: 'reloadReportConfigurationGrid'

			},{
				xtype:'label',
				text:'Report Format:'
			},{
				xtype: "combo",
				itemId: "report_type",
				name: "report_type",
				store: new Ext.data.SimpleStore({
					fields: ["value", "label"],
					data: [
						["", "Show all Reports"],
						["0", "Recurring"],
						["1", "One Time"],
						["3", "PDF"],
						["4", "CSV"]
					]
				}),
				valueField: "value",
				value: "",
				displayField: "label",
				mode: "local",
				editable: false,
				triggerAction: "all",
				selectOnFocus: false,
				listeners: {
					select: 'reloadReportConfigurationGrid'
				}
			}
		]
	}],

	columns: [
		{text: 'Report Title', dataIndex: 'report_title', flex: 1, renderer: 'reportTitleRenderer'},
		{text: 'Generate Next Schedule Report On', dataIndex: 'end_date', flex: 2, renderer: 'generateOnRenderer'},
		{text: 'Recurrence Schedule', dataIndex: 'recurrence_schedule', flex: 1, renderer: 'recurrenceScheduleRenderer'},
		{text: 'Last Generation', dataIndex: 'last_gen_date', flex: 1, renderer: 'gridRenderUTCDateInLocaltime'},
		{text: 'Date Created/Modified', dataIndex: 'modified_date', flex: 1, renderer: 'gridRenderUTCDateInLocaltime'},
		{text: 'Report Format', dataIndex: 'report_format', flex: 1, renderer: sfw.Default.reportFormatRenderer},
		{text: 'File Size', dataIndex: 'file_size', flex: 1, renderer: sfw.Default.fileSizeRenderer},
		{text: 'Last Execution Time', dataIndex: 'time_elapsed', flex: 1, renderer: sfw.Default.gridRendererTimeFormat},
		{text: 'Status', dataIndex: 'process_status', flex: 1, renderer: 'processStatusRenderer'},
		{text: 'Download', dataIndex: 'num_installations', flex: 1, renderer: 'downloadButtonRenderer'}

	],


    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            if(selected.items.length > 1)
            {
                additionalText = 'Delete all Selected Report Schedules';
            }else{
                additionalText = 'Delete Report Schedule';
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'reports',
                width: 250,
                plain: true,
                items: [{
                    text: 'View/Edit Report Schedule',
                    listeners: {
                    	click: { fn: 'editReportConfiguration', extra: selected }
                	},
                    afterRender: function() {
                        if (selected.items.length > 1)
                        {
                            this.hide();
                        }
                    },

                }, {
                    text: 'Generate Report Now',
                    listeners: {
                        click: {fn: 'generateReportNow', extra: record}
                    },
                    afterRender: function() {
                        if (selected.items.length > 1)
                        {
                            this.hide();
                        }
                    },
                }, {
                    text: additionalText,
                    listeners: {
                        click: {fn: 'deleteReport', extra: selected.items}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
		cellclick: function (view, td, cellIndex, record, tr, rowIndex, e, eOpts) {
			var columnName = view.getHeaderCt().getHeaderAtIndex(cellIndex).text;
			if (columnName == 'Download') {

				this.fireEvent("downloadReport", this, record);
			}
		},
		downloadReport: 'downloadReport',
		itemdblclick: 'dblclickHandler'
	},

	bbar: {
		xtype: 'pagingtoolbar',
		bind: {
			store: "{reporting}"
		},
		region: 'south',
		displayInfo: true,
		displayMsg: 'Reports {0} - {1} of {2}',
		emptyMsg: "No Data to display"
	}

});
