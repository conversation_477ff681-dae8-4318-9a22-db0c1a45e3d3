Ext.define('sfw.view.reporting.DatabaseCleanup', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiDatabaseCleanup',
    id: 'databasecleanup',
    title: 'Database Cleanup',
    layout:{
        type:'vbox',
        align: 'stretch'
    },
    border: 1,
    cls: 'shadow',
    requires: [
        'sfw.view.reporting.DatabaseCleanupController',
        'sfw.view.reporting.DatabaseCleanupModel',
        'sfw.store.reporting.DatabaseCleanupRule',
        'sfw.store.reporting.DatabaseCleanupHost',
        'sfw.Default'
    ],

    controller: 'reporting-databasecleanup',
    viewModel: {
        formulas: {
            getAddRuleButtonStatus: function () {
                if (sfw.util.Auth.LoginDetails.isReadOnly !== false) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    },

    items: [{
        title: 'Rules',
        xtype: 'gridpanel',
        flex: 2,
        cls: 'shadow',
        ui: 'gray',
        layout: 'fit',
        store: {
            type: 'databasecleanuprule',
        },
        itemId: 'dbcleanuprulegrid',
        viewConfig: {
            emptyText: 'No rules have been created yet.',
            deferEmptyText: false
        },
        bind: {
            selection: '{rulesGrid}'
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'button',
                text: 'Add Rule',
                ui: 'primary',
                bind: {
                    disabled: '{getAddRuleButtonStatus}'
                },
                handler: function () {
                    var dbcleanupaddrule = Ext.create("sfw.view.reporting.DatabaseCleanupAddRule",
                        {});
                    dbcleanupaddrule.show();
                }
            }, {
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton',
            },
            ]
        }],

        columns: [
            {text: 'Name', id: 'id', dataIndex: 'name', flex: 4, align: 'left', sortable: true}
            , {
                text: 'Criterion',
                flex: 1,
                dataIndex: 'criterion',
                align: 'left',
                sortable: true,
                renderer: function (value) {
                    var val = parseInt(value, 10);
                    if (isNaN(val)) {
                        return '-';
                    }
                    switch (val) {
                        case 1:
                            return 'Last Scan Time';
                            break;
                        case 2:
                            return 'Last Check-in Time';
                            break;
                        case 3:
                            return 'Never Scanned';
                            break;
                        default:
                            return '-';
                            break;
                    }
                }
            }
            , {
                text: 'Time Frame',
                flex: 1,
                dataIndex: 'time_frame_unit',
                align: 'left',
                sortable: false,
                renderer: function (value, metaData, record) {
                    var getTimeFrameUnitText = function (timeFrameUnit, timeFrameValue) {
                        switch (timeFrameUnit) {
                            case 1:
                                if (timeFrameValue > 1) {
                                    return 'Days';
                                } else {
                                    return 'Day';
                                }
                                break;
                            case 2:
                                if (timeFrameValue > 1) {
                                    return 'Weeks';
                                } else {
                                    return 'Week';
                                }
                                break;
                            case 3:
                                if (timeFrameValue > 1) {
                                    return 'Months';
                                } else {
                                    return 'Month';
                                }
                                break;
                            default:
                                return '';
                                break;
                        }
                    };

                    var val = parseInt(value, 10);
                    if (isNaN(val)) {
                        return '-';
                    }

                    var timeFrameValue = parseInt(record.data.time_frame_value, 10);
                    var timeFrameUnit = parseInt(record.data.time_frame_unit, 10);
                    var timeFrameUnitText = getTimeFrameUnitText(timeFrameUnit, timeFrameValue);

                    if (!timeFrameUnitText) {
                        return '-';
                    } else {
                        return timeFrameValue + ' ' + timeFrameUnitText;
                    }
                }
            }
            , {
                text: 'Date Created / Modified',
                flex: 1,
                dataIndex: 'date_created_or_modified',
                align: 'right',
                sortable: true,
                renderer: sfw.Default.gridRenderUTCDateInLocaltime
            }
            , {
                text: 'Status',
                flex: 1,
                dataIndex: 'status',
                align: 'right',
                sortable: true,
                renderer: function (value) {
                    var val = parseInt(value, 10);
                    if (isNaN(val)) {
                        return '-';
                    }

                    switch (val) {
                        case sfw.csiDatabaseCleanup.STATUS_PENDING:
                            return 'Pending';
                            break;
                        case sfw.csiDatabaseCleanup.STATUS_RUNNING:
                            return 'Running';
                            break;
                        case sfw.csiDatabaseCleanup.STATUS_COMPLETE:
                            return 'Complete';
                            break;
                        default:
                            break;
                    }

                    return '-';

                }
            }
            , {
                text: 'Execution Date',
                flex: 1,
                dataIndex: 'date_executed',
                align: 'right',
                sortable: true,
                renderer: sfw.Default.gridRenderUTCDateInLocaltime
            }
        ],
        listeners: {
            itemcontextmenu: function (grid, record, item, index, e) {
                var status = record.data.status;
                var isDisabledRunRule = false;
                if (status === 1) {
                    isDisabledRunRule = true;
                }
                var LoginDetails = sfw.util.Auth.LoginDetails;
                var contextMenu = Ext.create('Ext.menu.Menu', {

                    controller: 'reporting-databasecleanup',
                    plain: true,
                    items: [{
                        text: 'Execute Rule',
                        hidden: isDisabledRunRule || LoginDetails.isReadOnly,
                        listeners: {
                            click: {fn: 'executeDBRule', extra: record}
                        }
                    }, {
                        text: 'Delete Rule',
                        hidden: LoginDetails.isReadOnly,
                        listeners: {
                            click: {fn: 'deleteDBRule', extra: record}
                        }
                    }

                    ]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
            }
        },

        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{getdbcleanuprule}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying {0} - {1} of {2}',
            emptyMsg: "No rules configured"
        }
    }, {
        title: 'Affected Hosts',
        xtype: 'gridpanel',
        flex: 2,
        store: {
            type: 'databasecleanuphost',
        },
        itemId: 'dbcleanuphostgrid',
        viewConfig: {
            emptyText: 'No hosts are effected by the selected rule.',
            deferEmptyText: false
        },
        cls: 'shadow',
        ui: 'gray',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton',
            },
            ]
        }],

        columns: [
            {text: 'Host', dataIndex: 'host', flex: 5, align: 'left', sortable: true}
            , {text: 'Last Activity', flex: 1, dataIndex: 'last_activity', align: 'right', sortable: true}
        ],
        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{getdbcleanuphost}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying {0} - {1} of {2}',
            emptyMsg: "No hosts affected"
        }
    }
    ]

});
