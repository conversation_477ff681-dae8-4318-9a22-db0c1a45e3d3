Ext.define('sfw.view.reporting.DatabaseConsoleTreePanel', {
    extend: 'Ext.tree.Panel',
    alias: 'widget.dbconsoletreepanel',
    width: 400,
    height: 300,
    title: 'Tables',
    bind: {
        store: '{treeStore}'
    },
    rootVisible: true,
    initComponent: function () {
        var me = this;
        Ext.applyIf(this, {
            listeners: {
                itemcontextmenu: function (view, record, item, index, e) {
                    if (record.raw.leaf == false) {
                        var contextMenu = Ext.create('Ext.menu.Menu', {
                            plain: true,
                            items: [{
                                text: 'Show Data',
                                handler: function () {
                                    me.fireEvent("showData", me, record);
                                }
                            }, {
                                text: "Schedule Query",
                                disabled:sfw.util.Auth.LoginDetails.isReadOnly,
                                handler:function(){
                                    me.fireEvent("showQuery",me,record);
                                }
                            }]
                        });
                        e.stopEvent();
                        contextMenu.showAt(e.getXY());
                    }
                },
                showData: "dbConsoleDataResults",
                showQuery: "dbConsoleScheduleReport"
            }
        });
        this.callParent();
    }
});