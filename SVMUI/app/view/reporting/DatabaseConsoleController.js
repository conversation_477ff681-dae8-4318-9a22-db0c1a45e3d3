
Ext.define('sfw.view.reporting.DatabaseConsoleController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.databaseconsolecontroller',

    onConsoleStoreLoad: function (store, records) {
        this.getView().down('dbconsoletreepanel').getStore().setRootNode({
            text: 'Tables',
            expanded: false,
            children: records
        });
    },

    dbConsoleDataResults: function (view, record) {
        var me = this;
        var resultstore = Ext.getStore('dbconsoleresult');
        resultstore.getProxy().setExtraParams({'table': record.data.text});
        resultstore.loadPage(1,{
            callback: function (records,operations,success) {
                me.loadQueryStore(record.data.text, 'Success');
                if(!operations.success){
                    Ext.Msg.alert( 'Error', operations._response.responseJson.message);
                }
            },
            failure: function () {
                me.loadQueryStore(record.data.text, 'Failed');
                Ext.Msg.alert( 'Error', 'Unexpected Error...');
            }
        });
    },

    loadQueryStore: function (tablename, status) {
        var dbConsoleDetailsData = [];
        var dbConsleQueryDetails = {};
        dbConsleQueryDetails.time = new Date();
        dbConsleQueryDetails.status = status;
        dbConsleQueryDetails.table = '';
        dbConsleQueryDetails.query = 'SELECT * from ' + tablename;
        dbConsoleDetailsData.push(dbConsleQueryDetails);
        var detailstore = Ext.getStore('dbconsoledetails');
        detailstore.add(dbConsoleDetailsData);
    },

    dbConsoleScheduleReport:function(view, record)
    {
        var scheduleReportWindow = Ext.create("sfw.view.commonpopupwindows.ScheduleReportSetup",
            {
                listeners:{
                    afterrender:function(){
                        scheduleReportWindow.down('#table').setValue(record.data.text);
                    }
                }
            });

        scheduleReportWindow.show();
    },

    reload: function() {
        var viewModel = this.getViewModel();
        var store = viewModel.getStore("treeStore");
        //console.log(store);
        store.getRoot().removeAll();
        store.reload();
    }

});
