Ext.define('sfw.view.reporting.DatabaseConsoleModel', {
    extend: 'Ext.app.ViewModel',

    alias: 'viewmodel.dbconsole',

    stores: {
        treeStore: {
            //extend: 'Ext.data.TreeStore',
            type:'tree',
            storeID:'treedata',
            model: 'sfw.model.reporting.DatabaseConsole',
            root: {
                text: 'Tables',
                expanded: false,
                children:[]
            },
            autoLoad: true,
            proxy: {
                type: 'ajax',
                url: 'action=database_console&which=fetch_tables&',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    successProperty: 'success',
                    transform:{
                        fn: function(response){
                            if(!response.success){
                                sfw.util.Debug.log( 'Error while trying to fetch the list of tables: ' + response.error_code );
                            }
                            return {
                                children: response.data.tables
                            };
                        }
                    }
                }
            }

        },
        dbConsoleDetails:{
            storeId:'dbconsoledetails',
            proxy: {
                type: 'memory',
                reader: {
                    type: 'json',
                    rootProperty:'data'
                }
            },
            remoteSort: false,
            sorters: [{
                property: 'time',
                direction: 'DESC'
            }]
        },
        dbConsoleResults:{
            storeId:'dbconsoleresult',
            proxy: {
                type: 'ajax',
                url: 'action=database_console&which=run_query&',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty:'data.total',
                    successProperty: 'success'
                }
            },
            remoteSort: false,
            listeners:{
                metachange:function(store,meta){
                    var grid = Ext.getCmp('dbconsoleresultgrid');
                    var columns = [];
                    if(meta.columnInfo && meta.columnInfo.length > 0){
                        for(var i=0; i<meta.columnInfo.length; i++){
                            columns.push({
                                width:150,
                                aling:'left',
                                resizable: false,
                                sortable: false,
                                text:meta.columnInfo[i].header,
                                dataIndex:meta.columnInfo[i].dataIndex,
                                sortable:meta.columnInfo[i].sortable
                            })
                        }
                    }
                    grid.reconfigure( store, columns );
                    store.sort(meta.sortInfo.field, meta.sortInfo.direction);
                }

            }
        }
    }
});
