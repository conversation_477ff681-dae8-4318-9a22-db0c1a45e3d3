Ext.define('sfw.view.reporting.SmartGroupNotificationsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.smartgroupnotifications',

    init: function () {
        smartGroupType = 1;
        alertingOptionsData = [];
        alertingOptionsData[0] = [ // type 1 = hosts
            [1, 'Number of Hosts']
            , [2, 'Number of Installations']
        ];
        alertingOptionsData[1] = [ // type 2 = products
            [1, 'Number of Products']
            , [2, 'Number of Installations']
        ];
        alertingOptionsData[2] = [ // type 3 = advisories
            [1, 'Number of Advisories']
            , [2, 'Number of Installations']
        ];
        notificationId = 0;

        hoststore = new Ext.data.SimpleStore({
            fields: ["id", "text"],
            data: [
                [1, 'Number of Hosts'],
                [2, 'Number of Installations']

            ]
        }) ;

        productstore = new Ext.data.SimpleStore({
            fields: ["id", "text"],
            data: [
                [1, 'Number of Products'],
                [2, 'Number of Installations']

            ]
        }) ;

        advisorystore = new Ext.data.SimpleStore({
            fields: ["id", "text"],
            data: [
                [1, 'Number of Advisories'],
                [2, 'Number of Installations']

            ]
        }) ;
    },


    deleteSGNotification: function (event, target, options) {
        
        var selectionArray = [];
        var deleteIdList = '';
        if (options.extra.length > 1) {
            for (var i = 0; i < options.extra.length; ++i) {
                selectionArray.push(options.extra[i].data.id);
            }

            deleteIdList = selectionArray.join(",");
        } else {
            deleteIdList = options.extra.data.id;
        }

        Ext.Ajax.request({
            url: 'action=smartgroup_notifications&which=delete',
            method: 'POST',
            params: {
                delete_id_list: deleteIdList
            },
            dataType: 'json',
            success: function (response) {
                var response_status = Ext.decode(response.responseText);
                switch (response_status.status) {
                    case 0:
                        var numDeleted = response.numDeleted;
                        var message = '';
                        if (numDeleted < options.extra.length) {
                            message = "Only " + numDeleted + " of " + options.extra.length + " ";
                        }
                        message += 'Notification' + ((options.extra.length > 1) ? 's' : '') + ' Deleted';
                        Ext.Msg.alert("Success", message);
                        var sgnotification = Ext.getStore('sgnotification');
                        sgnotification.reload();
                        break;
                    case 1:
                        Ext.Msg.alert("Error", "Notification Id is Incorrect");
                        break;
                    default:
                        Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
                        break;
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
            }
        });

    },

    dblclickHandler: function () {
        const self = this,
            view = self.getView();
        const record = view.getSelection()[0];
        self.viewEditNotification(null, null, {extra: record});
    },

    viewEditNotification: function (event, target, options) {

        var vieweditconfigurationwindow = Ext.create("sfw.view.reporting.SmartgroupNotificationWindow",
            {
                listeners: {
                    afterrender: function (vieweditconfigurationwindow) {
                        if (options) {

                            vieweditconfigurationwindow.down("title").setHtml("View/Edit Notification");
                            notificationId = options.extra.data.id;
                            vieweditconfigurationwindow.down('#notification_name').setValue(options.extra.data.name);
                            vieweditconfigurationwindow.down('#smartgroup').setValue(options.extra.data.smartgroup_id);
                            vieweditconfigurationwindow.down('#alert_conditions').setDisabled(false);

                            if ('undefined' !== typeof (options.extra.data.criteria_id)) {
                                smartgroupType = options.extra.data.smartgroup_type;
                                var index = smartgroupType - 1;
                                if (options.extra.data.smartgroup_type == 1) {
                                    vieweditconfigurationwindow.down('#alert_options').setStore(hoststore);
                                } else if (options.extra.data.smartgroup_type == 2) {
                                    vieweditconfigurationwindow.down('#alert_options').setStore(productstore);
                                } else {
                                    vieweditconfigurationwindow.down('#alert_options').setStore(advisorystore);
                                }
                                vieweditconfigurationwindow.down('#alert_options').reset();
                                vieweditconfigurationwindow.down('#alert_options').setValue(options.extra.data.criteria_id);
                            }

                            if ('undefined' !== typeof (options.extra.data.action_id)) {
                                vieweditconfigurationwindow.down('#alert_actions').setValue(options.extra.data.action_id);

                                // Show hidden fields if in the right 'action' case
                                if (3 < options.extra.data.action_id) {
                                    vieweditconfigurationwindow.down('#action_value').show();
                                    vieweditconfigurationwindow.down('#action_percent').show();

                                    if ('undefined' !== typeof (options.extra.data.value)) {
                                        if (parseInt(options.extra.data.value, 10)) {
                                            vieweditconfigurationwindow.down('#action_value').setValue(options.extra.data.value);
                                        }
                                    }
                                } else {
                                    vieweditconfigurationwindow.down('#action_value').hide();
                                    vieweditconfigurationwindow.down('#action_percent').hide();
                                }
                            }
                            if ('undefined' !== typeof (options.extra.data.frequency)) {
                                vieweditconfigurationwindow.down('#frequency').setValue(options.extra.data.frequency);
                                //TODO
                                // this.frequencyCombo.value == 1 ? self.mobileRecipientSelector.setDisabled(true) : self.mobileRecipientSelector.setDisabled(false);
                            }

                            if ('undefined' !== typeof (options.extra.data.notify_always)) {
                                if (parseInt(options.extra.data.notify_always, 10)) {
                                    vieweditconfigurationwindow.down('#always_notify').setValue(true);
                                }
                            }

                            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

                            const emailRecipientSelector = localItemSelector.createEmailRecipientSelector('103');
                            const emailRecipients = vieweditconfigurationwindow.down('#emailRecipients');
                            emailRecipients.add(emailRecipientSelector);
                            sfw.Default.getSelectorValues({
                                action: 'recipients',
                                which: 'get_selected_recipients',
                                module_id: 103,
                                method_id: 1,
                                instance_id: options.extra.data.id
                            }, emailRecipientSelector);

                            const mobileRecipients = vieweditconfigurationwindow.down('#selectMobileRecipient');
                            if(sfw.util.Auth.LoginDetails.isSMSEnabled){
                                const mobileRecipientSelector = localItemSelector.createMobileSelector('103');
                                mobileRecipients.add(mobileRecipientSelector);

                                sfw.Default.getSelectorValues({
                                    action: 'recipients',
                                    which: 'get_selected_recipients',
                                    module_id: 103,
                                    method_id: 2,
                                    instance_id: options.extra.data.id
                                }, mobileRecipientSelector);

                            }else{
                                mobileRecipients.hide();
                            }

                            sfw.Default.defaulEmailRecipients(options.extra.id,'103',emailRecipients,mobileRecipients);

                        } else {
                            var button = Ext.ComponentQuery.query("#saveSgNotification")[0];

                            button.setDisabled(true);
                            vieweditconfigurationwindow.down("title").setHtml("Configure New Notification");

                            const emailRecipients = vieweditconfigurationwindow.down('#emailRecipients');
                            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                            const emailRecipientSelector = localItemSelector.createEmailRecipientSelector('103');
                            emailRecipients.add(emailRecipientSelector);

                            const mobileRecipients = vieweditconfigurationwindow.down('#selectMobileRecipient');
                            if(sfw.util.Auth.LoginDetails.isSMSEnabled){
                                const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                                const mobileRecipientSelector = localItemSelector.createMobileSelector('103');
                                mobileRecipients.add(mobileRecipientSelector);
                            }else{
                                mobileRecipients.hide();
                            }
                        }
                    }
                }
            });

        vieweditconfigurationwindow.show();
    },

    smartGroupControl: function () {
        var _this = this;
        var view = this.getView();
        var store = view.down('#smartgroup').getStore();
        var smartgroup = view.down('#smartgroup').getValue();
        var index = store.find('id', smartgroup);
        var record = store.getAt(index);
        smartGroupType = record.data.type;

        if (smartGroupType == 1) {

            view.down('#alert_options').setStore(hoststore);
        } else if (smartGroupType == 2) {

            view.down('#alert_options').setStore(productstore);
        } else {

            view.down('#alert_options').setStore(advisorystore);
        }

        view.down('#alert_conditions').setDisabled(false);

        _this.validateForm();


    },

    alertActionControl: function () {
        var _this = this;
        var view = this.getView();

        var alert_action = view.down('#alert_actions').getValue();

        if (alert_action == 4 || alert_action == 5) {
            view.down('#action_value').show();
            view.down('#action_percent').show();
        } else {
            view.down('#action_value').hide();
            view.down('#action_percent').hide();
        }
        _this.validateForm();


    },

    saveNotification: function (btn) {

        var nextInspectionDate, update_vars = [];
        var email_list = [];
        var mobile_list = [];


        var view = btn.up('window');

        var valueField = view.down('#action_value').isVisible() ? view.down('#action_value').getValue() : 0;

        var params = {
            name: view.down('#notification_name').getValue()
            , smartgroup_id: view.down('#smartgroup').getValue()
            , smartgroup_type: smartGroupType
            , alert_option: view.down('#alert_options').getValue()
            , alert_action: view.down('#alert_actions').getValue()
            , value: valueField
            , frequency: view.down('#frequency').getValue()
            , always_notify: view.down('#always_notify').getValue() ? 1 : 0
        };

        if(view.down('#defaultRecipients').getValue()){
            params.email_defaults = 1;
        }else{
            var selectedEmailrecipients = view.down('#emailRecipients').down('#selectedGrid');
            var selectedEmailrecipientsData = selectedEmailrecipients.getSelection();
            for (i = 0; i < selectedEmailrecipientsData.length; i++) {
                recipientAccountId = parseInt(selectedEmailrecipientsData[i].data.recipient_account_id, 10);
                email_list.push(recipientAccountId);
            }
            params.email_defaults = 0;
        }

        params.email_list = Ext.JSON.encode(email_list);

        params.sms_defaults = 0;

        if (sfw.util.Auth.LoginDetails.isSMSEnabled) {
            if(view.down('#defaultMobileRecipients').getValue()){
                params.sms_defaults = 1;
            }else{
                var selectedMobilerecipients = view.down('#selectMobileRecipient').down('#selectedGrid');
                var selectedMobilerecipientsData = selectedMobilerecipients.getSelection();
                for (i = 0; i < selectedMobilerecipientsData.length; i++) {
                    mobileRecipientAccountId = parseInt(selectedMobilerecipientsData[i].data.recipient_account_id, 10);
                    mobile_list.push(mobileRecipientAccountId);
                }
            }

        }

        params.sms_list = Ext.JSON.encode(mobile_list);

        var successMessage = 'Notification Saved';
        var which = 'save';
        if (notificationId > 0) {
            successMessage = 'Notification Updated';
            params.notification_id = notificationId;
            which = 'edit';
        }

        Ext.Ajax.request({
            url: 'action=smartgroup_notifications&which=' + which
            , method: "POST"
            , params: params
            , dataType: 'json'
            , success: function (data) {
                var status = Ext.decode(data.responseText);
                switch (status.error) {
                    case 0:
                        view.destroy();
                        var sgnotification = Ext.getStore('sgnotification');
                        sgnotification.reload();
                        Ext.Msg.alert('Success', successMessage);
                        break;
                    case 1:
                        Ext.Msg.alert('Error', 'Notification could not be saved.');
                        break;
                    case 40:
                        Ext.Msg.alert('Error', 'Notification could not be saved - no recipients selected');
                        break;
                    default:
                        Ext.Msg.alert('Error', 'Unexpected error.');
                        break;
                }
            }
            , failure: function () {

                Ext.Msg.alert('Error', 'Unexpected error failed api');
            }
        });

    },

    validateForm: function () {

        var button = Ext.ComponentQuery.query("#saveSgNotification")[0];

        button.setDisabled(true);

        // Validate alert conditions - only need that a selection
        // has been made for each combo, and that if the
        // numberfield is showing, that it is valid.

        if(Ext.ComponentQuery.query("#frequency")[0].getValue() == '1'){
            var window = Ext.ComponentQuery.query('window[xtype=sfw.smartgroupnotificationwindow]')[0];
            window.down('#selectMobileRecipient').setDisabled(true);
        }else{
            var window = Ext.ComponentQuery.query('window[xtype=sfw.smartgroupnotificationwindow]')[0];
            window.down('#selectMobileRecipient').setDisabled(false);
        }

        if (!Ext.ComponentQuery.query("#alert_options")[0].getValue()
            || !Ext.ComponentQuery.query("#alert_actions")[0].getValue()
        ) {

            return false;
        }
        if (Ext.ComponentQuery.query("#action_value")[0].isVisible()) {
            if (!Ext.ComponentQuery.query("#action_value")[0].getValue()) {
                return false;
            }
        }
        if (!Ext.ComponentQuery.query("#frequency")[0].getValue()) {
            return false;
        }

        // Validate the 'name & applicability' field
        if (!Ext.ComponentQuery.query("#notification_name")[0].getValue()
            || Ext.ComponentQuery.query("#smartgroup")[0] === '') {
            return false;
        }
        //TODO
        /*
        // If 'always notify' is checked, we require that a valid
        // email has been entered If we got this far, everything
        // else is valid, and the email is either valid or empty -
        // make sure sure it is non-empty before validating form
        if ( this.alwaysNotifyCheckbox.getValue() && this.emailSelectedStore.loaded === true && this.emailRecipientSelector.getSelections().length === 0 ) {
            // if empty, show a warning, or else it might not be
            // obvious why the 'save' is still greyed out
            Ext.Msg.alert( "Warning", "At least one email address must be selected to use the 'always notify' option you have checked." );
            return false;
        }*/

        button.setDisabled(false);
        return true;

    },


});
