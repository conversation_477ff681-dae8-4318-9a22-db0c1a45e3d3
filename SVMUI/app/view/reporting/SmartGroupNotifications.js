
Ext.define('sfw.view.reporting.SmartGroupNotifications', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiSmartgroupNotifications',
    itemId: 'sgNotificationGrid',

    requires: [
        'sfw.store.reporting.SmartGroupNotifications',
        'sfw.Default'
    ],
    stateful: true,
    stateId: 'sfw_csiSmartgroupNotifications_grid',
    store: {
        type: 'sgnotification'
    },

    selModel: {
        mode: 'MULTI'
    },

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No smart group notifications configured.'
    },

    title: 'Smart Group Notifications',
    border: 1,
    cls: 'shadow',

    controller: 'smartgroupnotifications',

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [
            {
                xtype: 'button',
                text: 'Configure New Notification',
                ui: 'primary',
                handler: 'viewEditNotification',
                listeners:{
                    beforerender:function(){
                        if(sfw.util.Auth.LoginDetails.isReadOnly){
                            this.setDisabled(true);
                        }
                    }
                }
            },{
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton',
            }
        ]
    }],

    columns: [
        {text: 'Name / Summary', dataIndex: 'name', flex: 3},
        {text: 'Smart Group Monitored', dataIndex: 'smartgroup_name', flex: 1},
        {text: 'Smart Group Type', dataIndex: 'smartgroup_type', flex: 1, renderer: sfw.Default.renderSmartgroupType},
        {text: 'Next Scheduled Check', dataIndex: 'next_scheduled', flex: 1, renderer: sfw.Default.gridRenderUTCDateInLocaltime},
        {text: 'Always Notify', dataIndex: 'notify_always', flex: 1, renderer: sfw.Default.alwaysNotifyRenderer},
        {text: 'Frequency', dataIndex: 'frequency', flex: 1, renderer: sfw.Default.frequencyRenderer},
        {text: 'Date Created/Modified', dataIndex: 'modified_date', flex: 1, renderer: sfw.Default.gridRenderUTCDateInLocaltime}

    ],


    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            var record_item = '';
            if (selected.items.length > 1) {
                additionalText = 'Delete All Selected Notifications';
                record_item = selected.items;
            } else {
                additionalText = 'Delete Notification';
                record_item = record;
            }

            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'smartgroupnotifications',
                width: 225,
                plain: true,
                items: [{
                    text: 'View/Edit Notification Configuration',
                    listeners: {
                        click: {fn: 'viewEditNotification', extra: record},
                        afterRender: function() {
                            if (selected.items.length > 1)
                            {
                                this.hide();
                            }
                        }
                    },
                }, {
                    text: additionalText,
                    listeners: {
                        click: {fn: 'deleteSGNotification', extra: record_item}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'dblclickHandler'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{sgnotification}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Notifications {0} - {1} of {2}',
        emptyMsg: "No Notifications configured"
    }
});
