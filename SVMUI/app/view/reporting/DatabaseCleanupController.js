Ext.define('sfw.view.reporting.DatabaseCleanupController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.reporting-databasecleanup',

    init: function () {
        //constants
        HOSTLIST_EMPTY = 101;
        RULE_IN_PROGRESS = 102;
        NAME_EXISTS = 103;
        RULE_EXISTS = 104;
        //Rule status
        STATUS_PENDING = 0;
        STATUS_RUNNING = 1;
        STATUS_COMPLETE = 2;
    },

    initViewModel: function (vm) {
        vm.bind('{rulesGrid}', 'onSelect', this);
    },

    onSelect: function (selection) {
        if (selection) {
            var params = {
                ruleId: selection.id
            };
            var affectedHostsStore = Ext.getStore('getdbcleanuphost');
            affectedHostsStore.getProxy().setExtraParams(params);
            affectedHostsStore.loadPage(1, {
                callback: function () {
                    //console.log("success");
                },
                failure: function () {
                    //console.log("failed");
                }
            });
        }
    },

    deleteDBRule: function (event, target, options) {
        var id = options.extra.data.id;
        var delParams = {
            rule_id: id
        };
        Ext.Ajax.request({
            url: 'action=db_cleanup&which=delete',
            method: 'POST',
            dataType: 'json',
            params: delParams,
            success: function (data) {
                var response = {};
                try {
                    response = Ext.util.JSON.decode(data.responseText);
                    switch (response.error_code) {
                        case sfw.util.SharedConstants.responses.SUCCESS:
                            Ext.ComponentQuery.query('#dbcleanuprulegrid')[0].getStore().reload();
                            Ext.ComponentQuery.query('#dbcleanuphostgrid')[0].getStore().reload();
                            Ext.Msg.alert("Success", "Rule has been successfully deleted.");
                            break;
                        case sfw.util.SharedConstants.responses.INVALID_ARGUMENTS:
                            Ext.Msg.alert("Error", "Invalid arguments.");
                            break;
                        default:
                            Ext.Msg.alert("Unexpected Error", "Unable to delete Database Cleanup rule.");
                            break;
                    }
                } catch (ex) {
                    // Silently ignore the error
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to delete the Database cleanup rule");
            }
        });
    },

    executeDBRule: function (event, target, options) {
        var id = options.extra.data.id;
        var exParams = {
            rule_id: id
        };
        Ext.Ajax.request({
            url: 'action=db_cleanup&which=run',
            method: 'POST',
            dataType: 'json',
            params: exParams,
            success: function (data) {
                var response = {};
                try {
                    response = Ext.util.JSON.decode(data.responseText);
                    switch (response.error_code) {
                        case sfw.util.SharedConstants.responses.SUCCESS:
                            Ext.Msg.alert("Success", "Rule has successfully executed.");
                            break;
                        case sfw.util.SharedConstants.responses.INVALID_ARGUMENTS:
                            Ext.Msg.alert("Error", "Invalid arguments.");
                            break;
                        case HOSTLIST_EMPTY:
                            Ext.Msg.alert("Error", "No hosts were found for the selected rule.");
                            break;
                        case RULE_IN_PROGRESS:
                            Ext.Msg.alert("Error", "Rule is currently being executed.");
                            break;
                        default:
                            Ext.Msg.alert("Unexpected Error", "Unable to run Database Cleanup rule.");
                            break;
                    }
                } catch (ex) {
                    // Silently ignore the error
                }
                Ext.ComponentQuery.query('#dbcleanuprulegrid')[0].getStore().reload();
                Ext.ComponentQuery.query('#dbcleanuphostgrid')[0].getStore().reload();
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to run Database Cleanup rule.");
            }
        });
    },

    saveDBRule: function () {

        Ext.Ajax.request({

            url: 'action=db_cleanup&which=save',
            dataType: 'json',
            params: {
                name: Ext.ComponentQuery.query('#dbrulename')[0].getValue()
                , criterion: Ext.ComponentQuery.query('#dbrulecriterion')[0].getValue()
                , time_frame_value: Ext.ComponentQuery.query('#dbruleperiod')[0].getValue()
                , time_frame_unit: Ext.ComponentQuery.query('#dbruletime')[0].getValue()
            },
            success: function (data) {
                var response = {};
                try {
                    response = Ext.util.JSON.decode(data.responseText);
                    switch (response.error_code) {
                        case sfw.util.SharedConstants.responses.SUCCESS:
                            Ext.Msg.alert("Success", "New Rule has been successfully added.");
                            Ext.ComponentQuery.query('#dbcleanuprulegrid')[0].getStore().reload();
                            Ext.ComponentQuery.query('#dbcleanuphostgrid')[0].getStore().reload();
                            Ext.ComponentQuery.query('#dbcleanupaddrule')[0].destroy();
                            break;
                        case sfw.util.SharedConstants.responses.INVALID_ARGUMENTS:
                            Ext.Msg.alert("Error", "Invalid arguments.");
                            break;
                        case sfw.util.SharedConstants.responses.ERROR_NO_WRITE_PERMISSION:
                            Ext.Msg.alert("Error", "Permission Denied.");
                            break;
                        case NAME_EXISTS:
                            Ext.Msg.alert("Error", "Rule name you have entered is already in use.");
                            break;
                        case RULE_EXISTS:
                            Ext.Msg.alert("Error", "Rule with same criteria already exists.");
                            break;
                        default:
                            Ext.Msg.alert("Unexpected Error", "Unable to save rule.");
                            break;
                    }
                } catch (ex) {
                    // Silently ignore the error
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to save rule.");
            }
        });
    },


    validateRule: function () {
        var saveRuleButton = Ext.ComponentQuery.query('#saveRuleButton')[0];
        var ruleNameField = Ext.ComponentQuery.query('#dbrulename')[0];
        var timeFrameValueField = Ext.ComponentQuery.query('#dbruleperiod')[0];

        if (parseInt(Ext.ComponentQuery.query('#dbrulecriterion')[0].getValue(), 10) === 3) {
            if (ruleNameField.isValid()) {
                saveRuleButton.setDisabled(sfw.util.Auth.LoginDetails.isReadOnly !== false);
            } else {
                saveRuleButton.setDisabled(true);
            }
        } else {
            if (timeFrameValueField.isValid() && (timeFrameValueField.getRawValue() > 0) && ruleNameField.isValid()) {
                saveRuleButton.setDisabled(sfw.util.Auth.LoginDetails.isReadOnly !== false);
            } else {
                saveRuleButton.setDisabled(true);
            }
        }
    }

});
