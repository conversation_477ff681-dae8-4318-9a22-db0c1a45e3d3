Ext.define('sfw.view.reporting.ActivityLogNotifications',{
    extend:'Ext.grid.Panel',
    xtype:'sfw.csiNotification',

    title:'Activity Log Notifications',
    border: 1,
    cls: 'shadow',
    requires:[
        'sfw.store.reporting.ActivityLogNotifcations',
        'sfw.CommonConstants',
        'sfw.Util',
        'sfw.Default',
        'sfw.Globals'
    ],
    controller: 'activitylognotification',
    selModel: {
        mode: 'MULTI'
    },

    store:{
        type:'activitylognotification'
    },

    viewConfig: {
        deferEmptyText: false,
        emptyText: 'No Notifications configured'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items:[{
            xtype: 'button',
            ui: 'primary',
            text: 'Create Notification',
            handler: sfw.Default.saveEditNotificationConfiguration
        }]
    }],

    columns: [
        { text: 'Name', dataIndex: 'name', flex: 1 },
        { text: 'Activity Log Monitored', dataIndex: 'event', flex: 3, renderer:function( value ) {

                var logConstants = sfw.CommonConstants.ACTIVITYLOGCONSTANT;
                var eventType = value.split(',');
                var eventName = [];
                var eventTypeLength = eventType.length;
                var logConstantsLen = logConstants.length;

                var eventLen = eventTypeLength < 3 ? eventTypeLength : 3;

                for(var i=0; i<eventLen; i++){
                    for(var j=0; j<logConstantsLen; j++){
                        if (logConstants[j]['event_type'] == eventType[i]) {
                            eventName.push(logConstants[j]['event']);
                            break;
                        }
                    }
                }
                var selectedEvents = eventName.toString();
                selectedEvents = selectedEvents.replace(/,/g, ", ");

                if ( eventTypeLength > 3 ) {
                    selectedEvents = selectedEvents.concat(' ', 'and more');
                }
                return selectedEvents;
            }
            },
        { text: 'Next Scheduled Check', dataIndex: 'next_scheduled_date', flex: 1, renderer:sfw.Default.gridRenderUTCDateInLocaltime},
        {
            text: 'Always Notify', dataIndex: 'notify_always', flex: 1, renderer: function (value) {
                var input = parseInt(value, 10), returnValue = '-';

                switch (input) {
                    case 0:
                        returnValue = 'No';
                        break;
                    case 1:
                        returnValue = 'Yes';
                        break;
                    default:
                        returnValue = '-';
                        break;
                }
                return returnValue;
            }
        },
        {
            text: 'Frequency', dataIndex: 'frequency', flex: 1, renderer: function (value) {
                var days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                var label = '';
                var data = value.split(',');

                var frequency = data[0];
                var day = data[1];
                var time = data[2];

                label = label.concat(sfw.Default.frequencyRenderer(frequency));
                if (frequency == 1) { //hourly
                    return label;
                }

                if (day != 0) { //day not selected
                    label = label.concat(', ', days[day - 1]);
                }

                var timeToDate = sfw.Util.dateCreate(time);

                var local_time = sfw.Default.dateConvertUTCToLocal(timeToDate);

                if (local_time != 0) { //time not selected
                    local_time = Ext.util.Format.date(local_time,sfw.Globals.HOURSMINUTESOUTPUT);
                    label = label.concat(', ', local_time);
                }

                return label;
            }
        },
        { text: 'Date Created/Modified', dataIndex: 'modified_date', flex: 1,renderer:sfw.Default.gridRenderUTCDateInLocaltime}
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {
            var selected = grid.getSelectionModel().getSelected();
            if(selected.items.length > 1)
            {
                additionalText = 'Delete All Selected Notifications';
            }else{
                additionalText = 'Delete Notification';
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller:'activitylognotification',
                width: 250,
                plain: true,
                items: [{
                    text: 'View/Edit Notification Configuration',
                    disabled:sfw.util.Auth.LoginDetails.isReadOnly,
                    listeners: {
                        afterRender: function() {
                            if (selected.items.length > 1)
                            {
                                this.hide();
                            }
                        },
                        click: {fn: sfw.Default.saveEditNotificationConfiguration, extra: record}
                    }
                },{
                    text: additionalText,
                    disabled:sfw.util.Auth.LoginDetails.isReadOnly,
                    listeners: {
                        click: {fn: 'deleteActivityLogNotification', extra: selected.items}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'dblclickHandler'

    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{activitylognotification}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Notifications {0} - {1} of {2}',
        emptyMsg: "No Notifications configured."
    }
});