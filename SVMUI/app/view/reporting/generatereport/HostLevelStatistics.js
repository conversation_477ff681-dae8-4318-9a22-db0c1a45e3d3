Ext.define('sfw.view.reporting.generatereport.HostLevelStatistics', {
    extend: 'Ext.Panel',
    xtype: 'reporting.hostLevelStatistics',

    itemId: 'hostLevelStatistics',
    layout: "auto",
    controller: 'reportgeneration',
    ui: 'light',

    title: "Host Level Statistics",
    collapsible: true,
    padding: 10,
    frame: true,

    items: [
        {
            xtype: 'box',
            html: "Specify the hosts whose data will be used for the report by selecting a smartgroup.",
            cls: "ContentPadding"
        },
        {
            xtype: 'panel',
            layout: "auto",
            padding: "0px 0px 0px 30px",
            items: [
                {
                    xtype: "tbspacer",
                    height: "10px"
                },
                {
                    xtype: 'combobox',
                    itemId: 'hostLevelStatisticsCombobox',
                    valueField: "id",
                    secuniaDecode: true,
                    updatedCombo: true,
                    displayField: "name",
                    boxMaxWidth: 500,
                    mode: "remote",
                    allowBlank: false,
                    editable: false,
                    forceSelection: true,
                    autoSelect: true,
                    triggerAction: "all",
                    name: 'CSI_HOSTS_hosts_smartgroup',
                    listeners: {
                        change: 'setComboboxValueToReportGenerate'
                    },
                    store: {
                        autoDestroy: true,
                        autoLoad: true,
                        // idProperty: "id",
                        // root: "data",
                        fields: [
                            {name: "id", type: "int"},
                            {name: "name", type: "string"}
                        ],
                        proxy: {
                            type: 'ajax',
                            url: "action=smart_groups&which=menuSummary&smartGroupTextType=host&",
                            method: "GET",
                            reader: {
                                type: 'json',
                                rootProperty: 'data'
                            }
                        },
                        listeners: {
                            // The load listener is implemented so that the correct smart group
                            // is selected after the store is loaded with data from the server side.
                            // The store is loaded automatically so no need to call it manually.
                            load: function (store, records) {
                                if(Ext.ComponentQuery.query('#hostLevelStatisticsCombobox')[0].getValue() < 0){
                                    var combo = Ext.ComponentQuery.query('#hostLevelStatisticsCombobox')[0];
                                    combo.select(store.getAt(0));
                                }
                                // sfw.events.fireEvent("smartgroups.menustore.loaded", records);

                                // var defaultSmartGroupValue, defaultSmartGroupIndex;

                                // // When the window has been loaded from the context of a smart group,
                                // // we will have a valid value for self.smartGroupId. When this is not the case,
                                // // The overview will be loaded from the context of the default smart group
                                // // which is the All Products.
                                // if ( typeof self.smartGroupId === "undefined" ) {
                                //     defaultSmartGroupIndex = this.find( "name", "All Hosts", 0, false, true );
                                //     // There should be only one match for the default smart group
                                //     if ( defaultSmartGroupIndex >= 0 ) {
                                //         defaultSmartGroupValue = this.getAt( defaultSmartGroupIndex ).get( "id" );
                                //         self.hostsSmartGroupDropDown.setValue( defaultSmartGroupValue );
                                //     }
                                // } else {
                                //     self.hostsSmartGroupDropDown.setValue( self.smartGroupId );
                                // }

                            }
                        }
                    }
                }
            ]
        },
        {
            xtype: "tbspacer",
            height: "10px"
        },
        {
            xtype: 'box',
            padding: '0 0 10 0',
            html: "Specify the statistics that will be included for each selected host in the report.",
        },
        {
            xtype: 'container',
            layout: "auto",
            padding: "0px 0px 0px 30px",
            items: [
                {
                    xtype: 'checkboxfield',
                    boxLabel: 'Overall Summary Statistics',
                    itemId: 'overallStatsCheckBox',
                    name: 'CSI_HOSTS_overall_stats',
                    handler: 'setCheckboxValueToReportGenerate',
                    checked: false,
                    disabled: false
                },
                {
                    xtype: 'checkboxfield',
                    boxLabel: 'Add Host Details',
                    checked: false,
                    itemId: 'hostDetailsCheckBox',
                    disabled: false,
                    handler: function (checkbox, checked) {
                        const self = this.up("#hostLevelStatistics");
                        if (!checked) {
                            self.down("#hostDetailsArea").setDisabled(true);
                            self.down("#insecureCheckBox").setValue(false);
                            self.down("#eolCheckBox").setValue(false);
                            self.down("#patchedCheckBox").setValue(false);

                        } else {
                            self.down("#hostDetailsArea").setDisabled(false);
                        }
                    }
                },
                {
                    xtype: "tbspacer",
                    height: "10px"
                },
                {
                    xtype: 'fieldset',
                    autoWidth: true,
                    disabled: true,
                    itemId: 'hostDetailsArea',
                    items: [
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Insecure Installation Details',
                            padding: "0px 0px 0px 10px",
                            itemId: 'insecureCheckBox',
                            name: 'CSI_HOSTS_insecure_stats',
                            checked: false,
                            disabled: false,
                            handler: function (checkbox, checked) {
                                const self = this.up("#hostLevelStatistics");
                                if (!checked) {
                                    // self.disableCritFilterParams( true );
                                    self.down("#criticalityLevel").setDisabled(true);
                                } else {
                                    self.down("#criticalityLevel").setDisabled(false);
                                    // self.disableCritFilterParams( false );
                                    self.down("#hostDetailsCheckBox").setValue(true);
                                }
                                Ext.GlobalEvents.fireEvent('setCheckboxValueToReportGenerate', checkbox, checked);
                            }
                        },
                        {
                            xtype: 'panel',
                            layout: "table",
                            itemId: 'criticalityLevel',
                            padding: "0px 0px 0px 30px",
                            border: false,
                            disabled: true,
                            layoutConfig: {
                                columns: 5
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    itemId: 'criticalityLevelPreText',
                                    text: "Additional filter: Only include insecure installations with a rating of: "
                                },
                                {
                                    xtype: "tbspacer",
                                    width: 10
                                },

                                {
                                    xtype: 'combobox',
                                    itemId: 'criticalityLevelCombobox',
                                    store: {
                                        fields: ["id", "text"],
                                        data: [
                                            {id: 0, text: 'Show All'},
                                            {id: 1, text: 'Not Critical'},
                                            {id: 2, text: 'Less Critical'},
                                            {id: 3, text: 'Moderately Critical'},
                                            {id: 4, text: 'Highly Critical'},
                                            {id: 5, text: 'Extremely Critical'}
                                        ]
                                    },
                                    width: 145,
                                    valueField: "id",
                                    value: 0,
                                    displayField: "text",
                                    mode: "local",
                                    allowBlank: false,
                                    editable: false,
                                    triggerAction: "all",
                                    autoSelect: true,
                                    name: 'CSI_HOSTS_least_crit_level',
                                    listeners: {
                                        change: 'setComboboxValueToReportGenerate'
                                    }
                                },
                                {
                                    xtype: "tbspacer",
                                    width: 10
                                },
                                {
                                    xtype: 'label',
                                    itemId: 'criticalityLevelPostText',
                                    text: " or Above."
                                }

                            ]
                        },
                        {
                            xtype: 'checkboxfield',
                            padding: "0px 0px 0px 10px",
                            itemId: 'eolCheckBox',
                            boxLabel: 'End-of-Life Installation Details',
                            checked: false,
                            disabled: false,
                            name: 'CSI_HOSTS_eol_stats',
                            handler: 'setCheckboxValueToReportGenerate'
                        },
                        {
                            xtype: 'checkboxfield',
                            padding: "0px 0px 0px 10px",
                            itemId: 'patchedCheckBox',
                            boxLabel: 'Secure Installation Details',
                            checked: false,
                            disabled: false,
                            name: 'CSI_HOSTS_patched_stats',
                            handler: 'setCheckboxValueToReportGenerate'
                        }
                    ]
                }
            ]
        }
    ]

});
