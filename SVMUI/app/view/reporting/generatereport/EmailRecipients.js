Ext.define('sfw.view.reporting.generatereport.EmailRecipients', {
    extend: 'Ext.Panel',
    xtype: 'reporting.emailRecipients',

    itemId: 'emailRecipients',
    layout: "auto",
    ui: 'light',

    title: "Select Email Recipients",
    padding: 10,
    frame: true,

    items: [
        {
            xtype: 'box',
            html: "If you wish to receive the reports via email, please specify at least one email recipient below. If you do not want to send the report via email, do not select any recipient."
        },
        {
            xtype: "tbspacer",
            height: "10px"
        },
        {
            layout: {
                type: 'hbox',
                align: 'stretch',
            },
            items:[{
                xtype: 'checkboxfield',
                itemId: 'defaultRecipients',
                checked: false,
                name: 'defaultRecipients',
                boxLabel: 'Use default recipients defined in Settings page',
                listeners: {
                    change: function(){
                        var emailRecipients = this.up("[xtype='reporting.emailRecipients']");
                        if(this.getValue()){
                            emailRecipients.down('#localItemSelector').setDisabled(true);
                        }else{
                            emailRecipients.down('#localItemSelector').setDisabled(false);
                        }
                    }
                }
            }]

        }
    ]
});