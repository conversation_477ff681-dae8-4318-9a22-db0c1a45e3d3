Ext.define('sfw.view.reporting.generatereport.ExecutiveSummaryReport', {
    extend: 'Ext.Panel',
    xtype: 'reporting.executiveSummaryReport',

    itemId: 'executiveSummaryReport',
    layout: "auto",
    controller: 'reportgeneration',
    ui: 'light',

    title: "Executive Summary Report",
    collapsible: true,
    padding: 10,
    frame: true,

    items: [
        {
            xtype: 'box',
            html: "Here you can choose to include the Executive Summary Report.  This is an overall summary document of the general state of vulnerability and patch management today, and the security state of your system in the context of current threats, methods for securing and staying secure, and the consequences and implications of a proper patch management solution versus not choosing such a solution.",
            cls: 'ContentPadding',
            border: false
        },
        {
            xtype: "panel",
            layout: 'auto',
            padding: '10px 0px 0px 30px',
            items: [
                {
                    xtype: 'checkboxfield',
                    itemId: 'includeExecutiveSummaryReport',
                    boxLabel: 'Include Executive Summary Report',
                    name: 'CSI_EXEC_include_exec_report',
                    checked: false,
                    handler: "setCheckboxValueToReportGenerate"
                }
            ]
        }
    ]

});
