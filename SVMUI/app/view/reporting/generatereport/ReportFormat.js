Ext.define('sfw.view.reporting.generatereport.ReportFormat', {
    extend: 'Ext.Panel',
    xtype: 'reporting.reportFormat',
    itemId: 'reportFormat',
    controller: 'reportgeneration',
    layout: "auto",
    ui: 'light',
    title: "Report Format",
    collapsible: true,
    padding: 10,
    frame: true,
    items: [
        {
            xtype: 'box',
            html: 'Specify the format for the report.',
            cls: 'ContentPadding',
            border: false
        },
        {
            xtype: 'panel',
            layout: 'auto',
            padding: '0px 0px 0px 30px',
            items: [
                {
                    xtype: "tbspacer",
                    height: "10px"
                },
                {
                    xtype: 'combobox',
                    itemId: 'selectReportFormat',
                    editable: true,
                    typeAhead: true,
                    selectOnFocus: true,
                    width: '16em',
                    mode: 'local',
                    store: {
                        fields: [
                            'myID',
                            'displayText'
                        ],
                        data: [[3, 'PDF'], [4, 'CSV']]
                    },
                    valueField: 'myID',
                    displayField: 'displayText',
                    emptyText: 'Select Reporting Format...',
                    triggerAction: 'all',
                    listeners: {
                        select: 'setReportFormat'
                    }
                }
            ]
        },
        {
            xtype: "tbspacer",
            height: "10px"
        },
        {
            xtype: 'label',
            itemId: 'statusTextLabel',
            text: '' // populate this elsewhere with default values
        }
    ]

});
