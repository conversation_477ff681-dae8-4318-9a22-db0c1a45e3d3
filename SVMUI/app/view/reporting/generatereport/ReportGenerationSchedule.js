Ext.define('sfw.view.reporting.generatereport.ReportGenerationSchedule', {
    extend: 'Ext.Panel',
    xtype: 'reporting.reportGenerationSchedule',

    itemId: 'reportGenerationSchedule',
    controller: 'reportgeneration',
    layout: "auto",
    ui: 'light',

    title: "Report Generation Schedule",
    collapsible: true,
    padding: 10,
    frame: true,

    items: [
        {
            xtype: 'box',
            html: 'Specify the generation schedule for the report.  Configure the details using the button to the right.  Note: a report will always use the most current data available at the time of generation.',
            cls: 'ContentPadding'
        },
        {
            xtype: 'panel',
            layout: 'auto',
            padding: '10px 0px 0px 30px',
            items: [
                {
                    xtype: 'radiofield',
                    boxLabel: 'One-Time Report - Generate only one report at a specific time.',
                    name: 'timeFrame',
                    checked: false,
                    itemId: 'oneTimeReport',
                    value: 1,
                    handler: 'setStatusTextForReportGenerationSchedule'
                },
                {
                    xtype: 'radiofield',
                    boxLabel: 'Recurring Report - Generate based on the a configured recurrence schedule.',
                    name: 'time<PERSON>rame',
                    checked: true,
                    itemId: 'recurringReport',
                    value: 2,
                    handler: 'setStatusTextForReportGenerationSchedule'
                },
                {
                    xtype: 'toolbar',
                    items: [
                        '->',
                        {
                            text: 'Configure',
                            tooltip: 'Configure the time frame to use for this report.',
                            handler: 'handleConfigure'
                        }
                    ]
                }
            ]
        },
        {
            xtype: 'label',
            itemId: 'statusTextLabel',
            text: '' // populate this elsewhere with default values
        }
    ]

});
