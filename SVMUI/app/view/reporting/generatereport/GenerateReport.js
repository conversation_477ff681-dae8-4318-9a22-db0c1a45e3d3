Ext.define('sfw.view.reporting.generatereport.GenerateReport', {
    extend: 'Ext.window.Window',
    xtype: 'reporting.generateReport',
    requires: [
        'sfw.view.reporting.generatereport.ReportGenerationController'
    ],
    controller: 'reportgeneration',
    title: '', // update title dynamically
    modal: true,
    constrain: true,
    maximizable: true,
    layout: 'fit',
    //ui: 'light',
    closeAction: 'destroy',
    listeners: {
        // show: function() {
        // 	// self.window.center();
        // 	// self.mainPanel.body.dom.scrollTop = 0;
        // },
        beforeshow: {
            fn: 'initReportForm'
        }
    }
    /*
    keys: [{ // Handle form ENTER key press
        key: [ Ext.event.Event.ENTER ],
        handler: function ( keyCode, eventObject ) {
            if ( false !== sfw.sharedFunctions.readOnly( sourceType ) ) {
                // readonly users can't save so don't handle this ENTER keypress
                return;
            }
            var tagName = eventObject.getTarget().tagName;
            if ( 'BUTTON' === tagName || 'TEXTAREA' === tagName ) {
                // Don't handle pressing ENTER on buttons or textareas (if added in future)
                return;
            }
            if ( false === Ext.getCmp( 'reportGeneratorSaveButton' ).disabled ) {
                Ext.getCmp( 'reportGeneratorSaveButton' ).handler.call();
            }
        }
    }]
    */
});