Ext.define('sfw.view.reporting.generatereport.SiteLevelStatistics', {
    extend: 'Ext.Panel',
    xtype: 'reporting.siteLevelStatistics',

    itemId: 'siteLevelStatistics',
    controller: 'reportgeneration',
    layout: "auto",
    ui: 'light',

    title: "Site Level Statistics",
    collapsible: true,
    padding: 10,
    frame: true,

    items: [
        {
            xtype: 'fieldset',
            layout: 'auto',
            title: 'Select Sites',
            height: 'auto',
            padding: 10,
            items: [
                {
                    xtype: 'box',
                    padding: '0 0 10 0',
                    html: 'Specify the sites whose data will be used for the report.'
                },
                {
                    xtype: 'container',
                    layout: 'auto',
                    padding: '0px 0px 0px 30px',
                    items: [
                        {
                            xtype: 'radiofield',
                            boxLabel: 'All sites for all selected users.',
                            name: 'siteSelection',
                            value: 'useAllRadio',
                            itemId: 'useAllRadio',
                            checked: false,
                            handler: 'handleSiteSelectionRadio'
                        },
                        {
                            xtype: 'radiofield',
                            boxLabel: 'Use a custom selected group of sites.',
                            name: 'siteSelection',
                            value: 'selectSpecificRadio',
                            itemId: 'selectSpecificRadio',
                            checked: false,
                            handler: 'handleSiteSelectionRadio'
                        },
                        {
                            xtype: 'radiofield',
                            boxLabel: 'Use a custom selected group of host-smart groups.',
                            name: 'siteSelection',
                            value: 'selectHostSmartGroupRadio',
                            itemId: 'selectHostSmartGroupRadio',
                            checked: false,
                            handler: 'handleSiteSelectionRadio'
                        },
                        {
                            xtype: 'toolbar',
                            items: [
                                '->',
                                {
                                    xtype: 'button',
                                    text: 'Select Sites',
                                    itemId: 'selectSites',
                                    disabled: true,
                                    tooltip: {
                                        text: 'Select specific sites for the report.'
                                    },
                                    handler: 'selectSitesBtnClick'
                                },
                                {
                                    xtype: 'button',
                                    text: 'Select Host Smart Group',
                                    itemId: 'selectHostSmartGroup',
                                    disabled: true,
                                    tooltip: {
                                        text: 'Select specific host smart group for the report.'
                                    },
                                    handler: 'selectHostSmartGroupBtnClick'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'label',
                    itemId: 'statusTextLabel',
                    text: 'Using data from all sites for users selected above (default).' // populate this elsewhere with default values
                }
            ]
        },
        {
            xtype: 'fieldset',
            layout: 'auto',
            title: 'Site Level Statistics to Include',
            height: 'auto',
            padding: 10,
            items: [
                {
                    xtype: 'box',
                    padding: '0 0 10 0',
                    html: 'Specify the site-level statistics that will be included in the report. If none of the statistics is selected, this section will not be included into the report.',
                },
                {
                    xtype: 'container',
                    layout: 'auto',
                    padding: '0px 0px 0px 30px',
                    items: [
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Overall Summary Statistics',
                            itemId: 'overallStatsCheckBox',
                            name: 'CSI_SITES_overall_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Overall Criticality Statistics',
                            itemId: 'critStatsCheckBox',
                            name: 'CSI_SITES_crit_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Overall Impact Statistics',
                            itemId: 'impactStatsCheckBox',
                            name: 'CSI_SITES_impact_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Overall Attack Vector Statistics',
                            itemId: 'avStatsCheckBox',
                            name: 'CSI_SITES_av_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'By-Site Statistics on Secure Products',
                            itemId: 'bySitePatchedCheckBox',
                            name: 'CSI_SITES_by_site_patched_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'By-Site Statistics on Insecure Products',
                            itemId: 'bySiteInsecureCheckBox',
                            name: 'CSI_SITES_by_site_insecure_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'By-Site Statistics on End-of-Life Products',
                            itemId: 'bySiteEndOfLifeCheckBox',
                            name: 'CSI_SITES_by_site_eol_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        },
                        {
                            xtype: 'checkboxfield',
                            boxLabel: 'Include Detailed Site Specific Data for Each Site',
                            itemId: 'siteDetailsCheckBox',
                            name: 'CSI_SITES_by_site_details_stats',
                            handler: 'setCheckboxValueToReportGenerate',
                            checked: false
                        }
                    ]
                }
            ]
        }
    ]

});
