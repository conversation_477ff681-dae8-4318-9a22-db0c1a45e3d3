Ext.define('sfw.view.reporting.generatereport.GeneralConfigurationOptions', {
    extend: 'Ext.Panel',
    xtype: 'reporting.generalConfigurationOptions',
    itemId: 'generalConfigurationOptions',
    layout: "auto",
    controller: 'reportgeneration',
    ui: 'light',
    title: "General Configuration Options",
    collapsible: true,
    padding: "0px 10px 10px 10px",
    frame: true,
    items: [
        {
            xtype: 'fieldset',
            itemId: 'fileNameArea',
            title: 'Report File Name',
            autoWidth: true,
            items: [
                {
                    html: 'Here you can specify a custom output file name for the generated report.'
                },
                {
                    padding: '5px 0 0 40px',
                    items: [
                        {
                            xtype: 'checkboxfield',
                            itemId: 'filenameCheckbox',
                            boxLabel: 'Set the file name for the PDF report file generated.',
                            checked: false,
                            listeners: {
                                change: function (checkbox, newValue) {
                                    checkbox.up("#generalConfigurationOptions").down("#filenameInput").setDisabled(!newValue);
                                }
                            }
                        }
                    ]
                },
                {
                    padding: '15px 0 0 40px',
                    items: [
                        {
                            layout: 'form',
                            items: [
                                {
                                    xtype: 'textfield',
                                    itemId: 'filenameInput',
                                    allowBlank: false,
                                    width: 300,
                                    labelAlign: 'left',
                                    fieldLabel: 'PDF Filename',
                                    vtype: 'PdfFile',
                                    enableKeyEvents: true,
                                    invalidText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.pdf\'.',
                                    value: ".pdf",
                                    disabled: true,
                                    name: 'CSI_MISC_file_name',
                                    maxLength: 95,
                                    // validator: function (value) { // custom validator to only validate if checkbox was checked
                                    //     // if ( self.setFilename ) {
                                    //     // return Ext.form.VTypes.PdfFile(value);
                                    //     // } else {
                                    //     //     return true;
                                    //     // }
                                    // },
                                    listeners: {
                                        change: 'setComboboxValueToReportGenerate'
                                        // invalid: self.validate.createDelegate(this),
                                        // valid: self.validate.createDelegate(this)
                                    }
                                }

                            ]
                        }
                    ]
                }
            ]
        },
        {
            xtype: 'fieldset',
            title: 'Report Title',
            autoWidth: true,
            items: [
                {
                    html: 'Here you can specify a custom title for the front page of the report.'
                },
                {
                    padding: '5px 0 0 40px',
                    items: [
                        {
                            xtype: 'checkboxfield',
                            itemId: 'reportTitleCheckbox',
                            boxLabel: 'Set the report title.',
                            checked: false,
                            listeners: {
                                change: function (checkbox, newValue) {
                                    checkbox.up("#generalConfigurationOptions").down("#reportTitleTextfield").setDisabled(!newValue);
                                }
                            }
                        }
                    ]
                },
                {
                    padding: '15px 0 0 40px',
                    items: [
                        {
                            layout: 'form',
                            items: [
                                {
                                    xtype: 'textfield',
                                    itemId: 'reportTitleTextfield',
                                    allowBlank: false,
                                    width: 300,
                                    labelAlign: 'left',
                                    fieldLabel: 'Report Title',
                                    enableKeyEvents: true,
                                    value: "Flexera Custom Report",
                                    disabled: true,
                                    maxLength: 95,
                                    name: 'CSI_MISC_report_title',
                                    listeners: {
                                        change: 'setComboboxValueToReportGenerate'
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            xtype: 'fieldset',
            title: 'Publish Report Parameters',
            itemId: 'publishReportParameters',
            autoWidth: true,
            items: [
                {
                    html: 'Here you can choose whether the report parameters (configured here) should be included in the report for reference.'
                },
                {
                    padding: '5px 100px 0px 40px',
                    items: [
                        {
                            xtype: 'checkboxfield',
                            itemId: 'publishReportParametersCheckbox',
                            boxLabel: 'Show Report Options and Generation Parameters',
                            checked: false,
                            name: 'CSI_MISC_show_report_params',
                            handler: 'setCheckboxValueToReportGenerate'
                        }
                    ]
                }
            ]
        }
    ]

});

Ext.onReady(function(){
    var pdfFileTest = /^(\w+)([\-][\w]+)*[\.][Pp][Dd][Ff]$/;
    Ext.apply(Ext.form.VTypes, {
        PdfFile: function(val) {
            return pdfFileTest.test(val);
        }
        ,PdfFileText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.pdf\'.'
    });
});
