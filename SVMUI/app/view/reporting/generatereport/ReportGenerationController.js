/**
 * This class is the controller for the main view for the application. It is specified as
 * the "controller" of the Main view class.
 */
Ext.define('sfw.view.reporting.generatereport.ReportGenerationController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.reportgeneration',
    requires: [
        'sfw.util.Util'
    ],

    listen: {
        global: {
            'setCheckboxValueToReportGenerate': 'setCheckboxValueToReportGenerate'
        }
    },

    initReportForm: function (reportWindow) {
        const self = this;
        // console.log(reportWindow);
        // alert(reportWindow.titleType);

        reportWindow.reportData = {};
        var saveAction = 'save';

        if (reportWindow.titleType === 'new') {
            reportWindow.setTitle('Configure New Report');
            reportWindow.reportData = self.generateReportInitialRecord(reportWindow.titleType);
        } else if ('edit') {
            saveAction = 'edit';
            reportWindow.setTitle('View/Edit Report Configuration');
            reportWindow.reportData = self.generateReportInitialRecord(reportWindow.titleType, reportWindow.configurationAllElements);
        }

        const reportElementsArray = [];
        const reportElementsUIArray = [];

        reportElements = sfw.csiReReportFormat;

        // Build the objects registered in the report elements array (sfw.reportGenerator.reportElements)
        for (var key in reportElements) {
            // Control which ones we add based on which modules a user has enabled
            var newReportElement;
            var length = 0;
            //Because the checkModules function actually requires a module, even if logically it is not needed
            //add an exception for the csi when the modules are not defined
            if (
                (reportWindow.sourceType === 'csi' && typeof reportElements[key].modules === 'undefined') ||
                sfw.util.checkModules(reportElements[key].modules)
            ) {

                //console.log("key: ", key);

                newReportElement = reportElements.create();

                // If this is a report element that should be available externally
                // (i.e., the config modules), make sure it is.
                if ('undefined' !== typeof (reportElements[key].externalKey)) {
                    var externalKey = reportElements[key].externalKey;
                    external[externalKey] = newReportElement;
                }

                reportElementsArray.push(newReportElement);
                reportElementsUIArray.push(newReportElement.interf);
                reportElementsUIArray.push(new Ext.Spacer({height: 10}));
                length += 1;
            }
        }

        // UI
        // --
        const saveButton = Ext.create('Ext.Button', {
            text: 'Save',
            tooltip: 'Save report configuration',
            //ui: 'primary',
            // disabled: sfw.util.SharedFunctions.readOnly( reportWindow.sourceType ),
            handler: function () {
                self.saveReportConfiguration(saveAction);
            }
        });

        const closeButton = Ext.create('Ext.Button', {
            text: 'Close',
            tooltip: 'Close window',
            disabled: false,
            handler: function () {
                reportWindow.destroy();
            }
        });

        // const toolbar1 = Ext.create('Ext.Toolbar', {
        //     items: [
        //         '->',
        //         saveButton,
        //         '-',
        //         closeButton
        //     ]
        // });

        const toolbar = [
            '->',
            saveButton,
            '-',
            closeButton
        ];

        const mainPanel = Ext.create('Ext.Panel', {
            layout: 'auto',
            autoScroll: true,
            items: new Ext.Panel({
                padding: 10,
                layout: 'auto',
                items: [
                    {
                        xtype: 'reporting.reportFormat'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.reportGenerationSchedule'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.executiveSummaryReport'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.dashboardProfiles',
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.siteLevelStatistics'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.hostLevelStatistics'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.productLevelStatistics'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.emailRecipients'
                    },
                    {
                        xtype: "tbspacer",
                        height: "10px"
                    },
                    {
                        xtype: 'reporting.generalConfigurationOptions'
                    }
                ]
            })
        });

        const interf = Ext.create('Ext.Panel', {
            layout: 'fit',
            items: mainPanel,
            buttons: toolbar
        });

        const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

        // General Configuration Options
        const generalConfigurationOptions = mainPanel.down("#generalConfigurationOptions");
        generalConfigurationOptions.down("#filenameCheckbox").setValue(reportWindow.reportData.CSI_MISC_file_name === ".pdf" || reportWindow.reportData.CSI_MISC_file_name === "" ? false : true);
        generalConfigurationOptions.down("#filenameInput").setValue(reportWindow.reportData.CSI_MISC_file_name);
        generalConfigurationOptions.down("#reportTitleCheckbox").setValue(reportWindow.reportData.CSI_MISC_report_title === "" ? false : true);
        generalConfigurationOptions.down("#reportTitleTextfield").setValue(reportWindow.reportData.CSI_MISC_report_title);
        generalConfigurationOptions.down("#publishReportParametersCheckbox").setValue(reportWindow.reportData.CSI_MISC_show_report_params ? true : false);
        generalConfigurationOptions.down("#publishReportParametersCheckbox").setValue(reportWindow.reportData.CSI_MISC_show_report_params ? true : false);


        // Report Format
        const reportFormat = mainPanel.down("#reportFormat");
        const selectReportFormat = reportFormat.down("#selectReportFormat");
        selectReportFormat.setValue(parseInt(reportWindow.reportData.FOR_report_format));
        if (typeof parseInt(reportWindow.reportData.FOR_report_format) === "number") {
            self.setReportFormat(selectReportFormat, parseInt(reportWindow.reportData.FOR_report_format));
        }

        // Dashboard profiles
        const dashboardProfileSelector = localItemSelector.createDashboardProfileSelector();
        const dashboardProfiles = mainPanel.down("#dashboardProfiles");
        dashboardProfiles.add(dashboardProfileSelector);
        if (reportWindow.reportData.report_id && reportWindow.reportData.CSI_DASH_profile_id) {
            self.getSelectorData({
                action: 'ajaxapi_dashboard',
                which: 'read',
                start: 0,
                limit: 0
            }, dashboardProfileSelector, reportWindow.reportData.CSI_DASH_profile_id);
        }


        // Email Recipients
        const emailRecipientSelector = localItemSelector.createEmailRecipientSelector();
        const emailRecipients = mainPanel.down("#emailRecipients");
        emailRecipients.add(emailRecipientSelector);
        if (reportWindow.reportData.report_id) {
            self.getSelectorData({
                action: 'recipients',
                which: 'get_selected_recipients',
                module_id: 102,
                method_id: 1,
                instance_id: reportWindow.reportData.report_id
            }, emailRecipientSelector);
        }

        sfw.Default.defaulEmailRecipients(reportWindow.reportData.report_id,'102',emailRecipients);

        // Report Generation Schedule
        const reportGenerationSchedule = mainPanel.down("#reportGenerationSchedule");
        const recurringReportRadioBox = reportGenerationSchedule.down("#recurringReport");
        recurringReportRadioBox.setValue(!(Number(reportWindow.reportData.SCH_one_time))  ? true : false);
        const oneTimeReportRadioBox = reportGenerationSchedule.down("#oneTimeReport");
        oneTimeReportRadioBox.setValue(reportWindow.reportData.SCH_one_time ? true : false);
        if (reportWindow.reportData.SCH_one_time) {
            self.setStatusTextForReportGenerationSchedule(oneTimeReportRadioBox);
        } else {
            self.setStatusTextForReportGenerationSchedule(recurringReportRadioBox);
        }

        // Executive Summary Report
        const executiveSummaryReport = mainPanel.down("#executiveSummaryReport");
        const includeExecutiveSummaryReport = executiveSummaryReport.down("#includeExecutiveSummaryReport");
        includeExecutiveSummaryReport.setValue(reportWindow.reportData.CSI_EXEC_include_exec_report ? true : false);

        // Site Level Statistics
        const siteLevelStatistics = mainPanel.down("#siteLevelStatistics");
        siteLevelStatistics.down("#useAllRadio").setValue(reportWindow.reportData.CSI_SITES_select_all_sites ? true : false);
        siteLevelStatistics.down("#selectSpecificRadio").setValue(reportWindow.reportData.CSI_SITES_site_selection_list ? true : false);
        siteLevelStatistics.down("#selectHostSmartGroupRadio").setValue(reportWindow.reportData.CSI_SITES_smg_selection_list ? true : false);
        siteLevelStatistics.down("#overallStatsCheckBox").setValue(reportWindow.reportData.CSI_SITES_overall_stats ? true : false);
        siteLevelStatistics.down("#critStatsCheckBox").setValue(reportWindow.reportData.CSI_SITES_crit_stats ? true : false);
        siteLevelStatistics.down("#impactStatsCheckBox").setValue(reportWindow.reportData.CSI_SITES_impact_stats ? true : false);
        siteLevelStatistics.down("#avStatsCheckBox").setValue(reportWindow.reportData.CSI_SITES_av_stats ? true : false);
        siteLevelStatistics.down("#bySitePatchedCheckBox").setValue(reportWindow.reportData.CSI_SITES_by_site_patched_stats ? true : false);
        siteLevelStatistics.down("#bySiteInsecureCheckBox").setValue(reportWindow.reportData.CSI_SITES_by_site_insecure_stats ? true : false);
        siteLevelStatistics.down("#bySiteEndOfLifeCheckBox").setValue(reportWindow.reportData.CSI_SITES_by_site_eol_stats ? true : false);
        siteLevelStatistics.down("#siteDetailsCheckBox").setValue(reportWindow.reportData.CSI_SITES_by_site_details_stats ? true : false);

        // Host Level Statistics
        const hostLevelStatistics = mainPanel.down("#hostLevelStatistics");
        hostLevelStatistics.down("#hostLevelStatisticsCombobox").setValue(reportWindow.reportData.CSI_HOSTS_hosts_smartgroup);
        hostLevelStatistics.down("#overallStatsCheckBox").setValue(reportWindow.reportData.CSI_HOSTS_overall_stats ? true : false);
        hostLevelStatistics.down("#hostDetailsCheckBox").setValue(reportWindow.reportData.CSI_HOSTS_insecure_stats || reportWindow.reportData.CSI_HOSTS_eol_stats || reportWindow.reportData.CSI_HOSTS_patched_stats ? true : false);
        hostLevelStatistics.down("#insecureCheckBox").setValue(reportWindow.reportData.CSI_HOSTS_insecure_stats ? true : false);
        hostLevelStatistics.down("#criticalityLevelCombobox").setValue(reportWindow.reportData.CSI_HOSTS_least_crit_level);
        hostLevelStatistics.down("#eolCheckBox").setValue(reportWindow.reportData.CSI_HOSTS_eol_stats ? true : false);
        hostLevelStatistics.down("#patchedCheckBox").setValue(reportWindow.reportData.CSI_HOSTS_patched_stats ? true : false);

        // Product Level Statistics
        const productLevelStatistics = mainPanel.down("#productLevelStatistics");
        productLevelStatistics.down("#productLevelStatisticsCombobox").setValue(reportWindow.reportData.CSI_PRODUCTS_products_smartgroup);
        productLevelStatistics.down("#overallStatsCheckBox").setValue(reportWindow.reportData.CSI_PRODUCTS_overall_stats ? true : false);
        productLevelStatistics.down("#productDetailsCheckBox").setValue(reportWindow.reportData.CSI_PRODUCTS_insecure_stats || reportWindow.reportData.CSI_PRODUCTS_eol_stats || reportWindow.reportData.CSI_PRODUCTS_patched_stats ? true : false);
        productLevelStatistics.down("#insecureCheckBox").setValue(reportWindow.reportData.CSI_PRODUCTS_insecure_stats ? true : false);
        productLevelStatistics.down("#criticalityLevelCombobox").setValue(reportWindow.reportData.CSI_PRODUCTS_least_crit_level);
        productLevelStatistics.down("#eolCheckBox").setValue(reportWindow.reportData.CSI_PRODUCTS_eol_stats ? true : false);
        productLevelStatistics.down("#patchedCheckBox").setValue(reportWindow.reportData.CSI_PRODUCTS_patched_stats ? true : false);


        // Get dynamic height/width in case of small browser window.
        const dynamicDimensions = sfw.util.SharedFunctions.getDynamicDimensions(900, 950, interf);
        reportWindow.setHeight(dynamicDimensions[0]);
        reportWindow.setWidth(dynamicDimensions[1]);

        reportWindow.add(interf);

    },

    generateReportInitialRecord: function (titleType, record) {
        const reportData = {
            FOR_report_format: null,
            SCH_one_time: 0,
            SCH_end_date: Ext.util.Format.date(new Date(), sfw.util.SharedConstants.dateShortInput) + "T00:00:00",
            SCH_recurrence_value: 1,
            SCH_recurrence_unit: 'month',
            CSI_EXEC_include_exec_report: 0,
            CSI_DASH_profile_id: '',
            CSI_SITES_select_all_sites: 1,
            CSI_SITES_site_selection_list: '',
            CSI_SITES_smg_selection_list: '',
            CSI_SITES_overall_stats: 0,
            CSI_SITES_crit_stats: 0,
            CSI_SITES_impact_stats: 0,
            CSI_SITES_av_stats: 0,
            CSI_SITES_by_site_patched_stats: 0,
            CSI_SITES_by_site_insecure_stats: 0,
            CSI_SITES_by_site_eol_stats: 0,
            CSI_SITES_by_site_details_stats: 0,
            CSI_HOSTS_hosts_smartgroup: -1,
            CSI_HOSTS_overall_stats: 0,
            CSI_HOSTS_insecure_stats: 0,
            CSI_HOSTS_least_crit_level: 0,
            CSI_HOSTS_eol_stats: 0,
            CSI_HOSTS_patched_stats: 0,
            CSI_PRODUCTS_products_smartgroup: -1,
            CSI_PRODUCTS_overall_stats: 0,
            CSI_PRODUCTS_insecure_stats: 0,
            CSI_PRODUCTS_least_crit_level: 0,
            CSI_PRODUCTS_eol_stats: 0,
            CSI_PRODUCTS_patched_stats: 0,
            CSI_RCPT_recipients: '',
            CSI_RCPT_defaults_recipients: false,
            CSI_MISC_file_name: '.pdf',
            CSI_MISC_report_title: 'Flexera Custom Report',
            CSI_MISC_show_report_params: 0,
            CSI_MISC_publish_detail_level: 3,
            source_type: 'csi'
        };
        if (titleType === "edit" && typeof record !== "undefined") {

            // rec = record;

            reportData.report_id = record.id;
            reportData.CSI_MISC_report_title = record.report_title;
            reportData.CSI_MISC_file_name = record.filename;

            reportData.FOR_report_format = record.report_format;
            reportData.SCH_one_time = Number(record.one_time_gen);
            reportData.SCH_end_date = Ext.util.Format.date(new Date(record.end_date), sfw.util.SharedConstants.dateShortInput) + "T00:00:00";
            if (reportData.SCH_one_time) {
                reportData.SCH_recurrence_value = 1;
                reportData.SCH_recurrence_unit = 'month';
            } else {
                const recurrenceScheduleValues = record.recurrence_schedule.split(",");
                if (recurrenceScheduleValues.length === 3) {
                    if (parseInt(recurrenceScheduleValues[0], 10)) {
                        reportData.SCH_recurrence_value = parseInt(recurrenceScheduleValues[0], 10);
                        reportData.SCH_recurrence_unit = 'day';
                    } else if (parseInt(recurrenceScheduleValues[1], 10)) {
                        reportData.SCH_recurrence_value = parseInt(recurrenceScheduleValues[1], 10);
                        reportData.SCH_recurrence_unit = 'week';
                    } else {
                        reportData.SCH_recurrence_value = parseInt(recurrenceScheduleValues[2], 10);
                        reportData.SCH_recurrence_unit = 'month';
                    }
                }
            }

            var configurations = record.configuration.split("|");

            configurations.forEach((configuration, index) => {
                var keyVal = configuration.split(":");
                if (keyVal[0] === "CSI_EXEC") {
                    reportData.CSI_EXEC_include_exec_report = keyVal[1] === "YES" ? 1 : 0;
                }

                if (keyVal[0] === "CSI_DASH") {
                    reportData.CSI_DASH_profile_id = keyVal[1].toString();
                }

                if (keyVal[0] === "CSI_SITES") {
                    if (keyVal[1] === "SITES") {
                        if (keyVal[2].split(";")[0] === "ALL") {
                            reportData.CSI_SITES_select_all_sites = 1;
                        } else if ('HSMG' === keyVal[2].substring(0, 4)) {
                            reportData.CSI_SITES_smg_selection_list = keyVal[2].substring(4).split(';')[0];
                            reportData.CSI_SITES_select_all_sites = 0;
                        } else {
                            reportData.CSI_SITES_site_selection_list = keyVal[2].split(";")[0];
                            reportData.CSI_SITES_select_all_sites = 0;
                        }
                    }

                    var checkboxes = configuration.split(";");
                    checkboxes.forEach(checkbox => {
                        switch (checkbox) {
                            case "OVR":
                                reportData.CSI_SITES_overall_stats = 1;
                                break;
                            case "CRIT":
                                reportData.CSI_SITES_crit_stats = 1;
                                break;
                            case "IMP":
                                reportData.CSI_SITES_impact_stats = 1;
                                break;
                            case "AV":
                                reportData.CSI_SITES_av_stats = 1;
                                break;
                            case "BSI":
                                reportData.CSI_SITES_by_site_insecure_stats = 1;
                                break;
                            case "BSE":
                                reportData.CSI_SITES_by_site_eol_stats = 1;
                                break;
                            case "DET":
                                reportData.CSI_SITES_by_site_details_stats = 1;
                                break;
                            case "BSP":
                                reportData.CSI_SITES_by_site_patched_stats = 1;
                                break;
                            default:
                                break;
                        }
                    });
                }

                if (keyVal[0] === "CSI_HOSTS") {
                    configuration = configuration.replace("CSI_HOSTS:", "");
                    var options = configuration.split(';');
                    //console.log("options: ", options);
                    var keyWord;
                    for (var i = 0; i < options.length; ++i) {
                        // HOSSG means HOSts SmartGroup
                        if ('HOSSG' === options[i].substring(0, 5)) {
                            //console.log("options[i].substring(6): ", options[i].substring(6));
                            reportData.CSI_HOSTS_hosts_smartgroup = options[i].substring(6);
                        } else {
                            keyWord = options[i].substring(0, 3);
                            switch (keyWord) {
                                case 'OVR':
                                    reportData.CSI_HOSTS_overall_stats = 1;
                                    break;
                                case 'INS':
                                    reportData.CSI_HOSTS_insecure_stats = 1;
                                    var critLevel = parseInt(options[i].substring(4), 10);
                                    if (0 <= critLevel && 5 >= critLevel) {
                                        reportData.CSI_HOSTS_least_crit_level = critLevel;
                                    }
                                    break;
                                case 'EOL':
                                    reportData.CSI_HOSTS_eol_stats = 1;
                                    break;
                                case 'PAT':
                                    reportData.CSI_HOSTS_patched_stats = 1;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                }


                if (keyVal[0] === "CSI_PRODUCTS") {
                    configuration = configuration.replace("CSI_PRODUCTS:", "");
                    var options = configuration.split(';');
                    //console.log("options: ", options);
                    var keyWord;
                    for (var i = 0; i < options.length; ++i) {
                        // HOSSG means HOSts SmartGroup
                        if ('PRSG' === options[i].substring(0, 4)) {
                            //console.log("options[i].substring(6): ", options[i].substring(6));
                            reportData.CSI_PRODUCTS_products_smartgroup = options[i].substring(5);
                        } else {
                            keyWord = options[i].substring(0, 3);
                            switch (keyWord) {
                                case 'OVR':
                                    reportData.CSI_PRODUCTS_overall_stats = 1;
                                    break;
                                case 'INS':
                                    reportData.CSI_PRODUCTS_insecure_stats = 1;
                                    var critLevel = parseInt(options[i].substring(4), 10);
                                    if (0 <= critLevel && 5 >= critLevel) {
                                        reportData.CSI_PRODUCTS_least_crit_level = critLevel;
                                    }
                                    break;
                                case 'EOL':
                                    reportData.CSI_PRODUCTS_eol_stats = 1;
                                    break;
                                case 'PAT':
                                    reportData.CSI_PRODUCTS_patched_stats = 1;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                }

                if (keyVal[0] === "CSI_MISC") {
                    keyVal[1].split(";").forEach(miscValue => {
                        if (miscValue === "SHOW") {
                            reportData.CSI_MISC_show_report_params = 1;
                        }
                    });
                }


            });


            // CSI_DASH_profile_id: '',
            // CSI_RCPT_recipients: '',
            // CSI_RCPT_defaults_recipients: false,


        }

        return reportData;
    },

    setValueForGenerateReport: function (key, value) {
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
            reportWindow.reportData[key] = value;
        }
    },

    setCheckboxValueToReportGenerate: function (checkbox, checked) {
        const self = this;
        self.setValueForGenerateReport(checkbox.getName(), checked ? 1 : 0);
    },

    setComboboxValueToReportGenerate: function (combobox, newValue) {
        const self = this;
        self.setValueForGenerateReport(combobox.getName(), newValue);
    },

    setStatusTextForReportGenerationSchedule: function (radiofield, checked) {
        const self = this;
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
        } else {
            return;
        }

        if (typeof checked === "boolean") {
            if (checked) {
                if (radiofield.getItemId() === "oneTimeReport") {
                    reportWindow.reportData["SCH_one_time"] = 1;
                } else {
                    reportWindow.reportData["SCH_one_time"] = 0;
                }
            }
        }
        const reportGenerationSchedule = radiofield.up("#reportGenerationSchedule");
        const statusTextLabel = reportGenerationSchedule.down("#statusTextLabel");
        var reportGenerationScheduleDisplayText = "";
        //console.log("self.reportData.SCH_end_date: ", reportWindow.reportData.SCH_end_date);
        if (reportWindow.reportData.SCH_one_time) {
            reportGenerationScheduleDisplayText = 'Report will be generated as soon as possible on or after '
                + Ext.util.Format.date(reportWindow.reportData.SCH_end_date, 'l F jS, Y') + '.';
        } else {
            // Deal with adding 's' for the display of the time frame and recurrence schedule
            var displayRecurrenceTimeUnit = reportWindow.reportData.SCH_recurrence_unit;
            if (1 < reportWindow.reportData.SCH_recurrence_value) {
                displayRecurrenceTimeUnit += 's';
            }
            reportGenerationScheduleDisplayText = 'Reports will be generated on ' +
                Ext.util.Format.date(reportWindow.reportData.SCH_end_date, 'l F jS, Y') + ',  and every ' +
                reportWindow.reportData.SCH_recurrence_value + ' ' + displayRecurrenceTimeUnit + ' thereafter.';
        }
        statusTextLabel.setText(reportGenerationScheduleDisplayText);
    },

    createAndGetOneTimeDialog: function () {
        const self = this;
        var dateVal = sfw.util.Util.dateCreate();
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];

            if (reportWindow.reportData && reportWindow.reportData.SCH_end_date) {
                dateVal = new Date(reportWindow.reportData.SCH_end_date);
            }

        }

        const oneTimeDialog = Ext.create('Ext.window.Window', {
            title: 'Select Report Generation Date',
            width: 550,
            height: 270,
            border: false,
            layout: 'fit',
            constrainHeader: true,
            modal: true,
            bodyPadding: 10,
            items: [
                {
                    xtype: 'form',
                    items: [
                        {
                            xtype: 'fieldset',
                            layout: 'column',
                            title: 'Report Generation Date',
                            height: 'auto',
                            padding: 10,
                            items: [
                                {
                                    xtype: 'box',
                                    padding: '0 0 10 0',
                                    html: 'Set the date for the report to be generated. Note: a report is always generated using the most current data available at the time of generation, regardless of if the selected generation date is in the past.  i.e., you cannot generate a report based solely on data from, for example, last week.'
                                },
                                {
                                    xtype: 'panel',
                                    layout: 'table',
                                    cls: 'ContentPadding',
                                    layoutConfig: {
                                        columns: 3
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            text: 'Choose Date: '
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            width: 10
                                        },
                                        {
                                            xtype: 'datefield',
                                            width: 'auto',
                                            value: dateVal,
                                            minValue: sfw.util.Util.dateCreate(),
                                            format: sfw.util.SharedConstants.dateShortInput,
                                            invalidText: 'Valid date format is YYYY-MM-DD.',
                                            enableKeyEvents: true,
                                            listeners: {
                                                valid: function () {
                                                    oneTimeDialog.buttons[0].setDisabled(false);
                                                },
                                                invalid: function () {
                                                    oneTimeDialog.buttons[0].setDisabled(true);
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            buttons: [
                {
                    text: 'Save',
                    // disabled: !oneTimeDialog.down("datefield").isValid(),
                    handler: function () {
                        self.saveOneTimeButton(oneTimeDialog);
                    }
                },
                {
                    text: 'Cancel',
                    handler: function () {
                        oneTimeDialog.destroy();
                    }
                }
            ]
        });

        return oneTimeDialog;
    },

    saveOneTimeButton: function (oneTimeDialog) {
        if (!oneTimeDialog.down("datefield").isValid()) {
            Ext.Msg.alert('Invalid configuration', 'The next date the report will run on cannot be in the past.');
            return false;
        } else {
            this.saveOneTimeSettings(oneTimeDialog);
        }
    },

    saveOneTimeSettings: function (oneTimeDialog) {
        const self = this;
        const oneTimeDisplayText = 'Report will be generated as soon as possible on or after '
            + Ext.util.Format.date(oneTimeDialog.down("datefield").getValue(), 'l F jS, Y') + '.';
        self.setValueForGenerateReport("SCH_end_date", Ext.util.Format.date(oneTimeDialog.down("datefield").getValue(), sfw.util.SharedConstants.dateShortInput) + "T00:00:00");
        const reportGenerationSchedule = Ext.ComponentQuery.query('panel[itemId=reportGenerationSchedule]')[0];
        const statusTextLabel = reportGenerationSchedule.down("#statusTextLabel");
        statusTextLabel.setText(oneTimeDisplayText);
        oneTimeDialog.hide();
    },

    createAndGetRecurrenceTimeDialog: function () {
        const self = this;
        var dateVal = sfw.util.Util.dateCreate();
        var generateEvery = 1;
        var unit = 'month';
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];

            if (reportWindow.reportData && reportWindow.reportData.SCH_end_date) {
                dateVal = new Date(reportWindow.reportData.SCH_end_date);
            }

            if (reportWindow.reportData && reportWindow.reportData.SCH_recurrence_value) {
                generateEvery = reportWindow.reportData.SCH_recurrence_value;
            }

            if (reportWindow.reportData && reportWindow.reportData.SCH_recurrence_unit) {
                unit = reportWindow.reportData.SCH_recurrence_unit;
            }

        }
        var recurrenceDialog = null;
        recurrenceDialog = Ext.create('Ext.window.Window', {
            title: 'Setup a Recurrence Schedule for This Report.',
            itemId: 'recurrenceDialog',
            width: 580,
            height: 350,
            border: false,
            layout: 'fit',
            constrainHeader: true,
            modal: true,
            bodyPadding: 10,
            items: [
                {
                    xtype: 'form',
                    items: [
                        {
                            xtype: 'fieldset',
                            layout: 'column',
                            title: 'Report Generation Date',
                            height: 'auto',
                            padding: 10,
                            items: [
                                {
                                    xtype: 'box',
                                    html: 'Set a date to schedule the report generation.',
                                    //html: 'Set the date for the report to be generated.  Note: a report is always generated using the most current data available at the time of generation, regardless of if the selected generation date is in the past.  I.e., you cannot generate a report based solely on data from, for example, last week.',
                                    padding: '0 24 10 0',
                                    cls: 'ContentPadding'
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    layout: 'hbox',
                                    fieldLabel: 'Choose Date',
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            hideLabel: true,
                                            value: dateVal,
                                            minValue: sfw.util.Util.dateCreate(),
                                            format: sfw.util.SharedConstants.dateShortInput,
                                            invalidText: 'Valid date format is YYYY-MM-DD.',
                                            listeners: {
                                                invalid: function () {
                                                    recurrenceDialog.buttons[0].setDisabled(true);
                                                },
                                                valid: function () {
                                                    recurrenceDialog.buttons[0].setDisabled(false);
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            title: 'Set Recurrence:',
                            height: 'auto',
                            layout: 'auto',
                            padding: 10,
                            items: [
                                {
                                    xtype: 'box',
                                    padding: '0 0 10 0',
                                    html: 'Set the period for re-generating and sending this report.',
                                    cls: 'ContentPadding'
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    layout: 'hbox',
                                    fieldLabel: 'Generate Every',
                                    items: [
                                        {
                                            xtype: 'numberfield',
                                            itemId: 'generateEvery',
                                            width: 80,
                                            value: generateEvery,
                                            maxLength: 4,
                                            minValue: 1,
                                            allowBlank: false,
                                            allowNegative: false,
                                            allowDecimals: false,
                                            autoCreate: {
                                                tag: 'input',
                                                type: 'text',
                                                size: '20',
                                                autocomplete: 'off',
                                                maxlength: '4'
                                            },
                                            listeners: {
                                                invalid: function () {
                                                    recurrenceDialog.buttons[0].setDisabled(true);
                                                },
                                                valid: function () {
                                                    recurrenceDialog.buttons[0].setDisabled(false);
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'radiogroup',
                                            width: 350,
                                            columns: 3,
                                            padding: '0 0 0 20',
                                            allowBlank: false,
                                            items: [
                                                {
                                                    xtype: 'radiofield',
                                                    boxLabel: 'Day(s)',
                                                    itemId: 'dayRecurrenceDialog',
                                                    singularName: 'Day',
                                                    name: 'radio_rg',
                                                    checked: unit === 'day' ? true : false,
                                                    value: 1
                                                },
                                                {
                                                    xtype: 'radiofield',
                                                    boxLabel: 'Week(s)',
                                                    itemId: 'weekRecurrenceDialog',
                                                    singularName: 'Week',
                                                    name: 'radio_rg',
                                                    checked: unit === 'week' ? true : false,
                                                    value: 2
                                                },
                                                {
                                                    xtype: 'radiofield',
                                                    boxLabel: 'Month(s)',
                                                    itemId: 'monthRecurrenceDialog',
                                                    singularName: 'Month',
                                                    name: 'radio_rg',
                                                    checked: unit === 'month' ? true : false,
                                                    value: 3
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                    // keys: [{ // Handle form ENTER key press
                    // 	key: [ Ext.EventObject.ENTER ],
                    // 	handler: function ( keyCode, eventObject ) {
                    // 		if ( 'BUTTON' === eventObject.getTarget().tagName ) {
                    // 			// Don't handle pressing ENTER on buttons, they have their own handlers
                    // 			return;
                    // 		}
                    // 		if ( false === Ext.getCmp( 'recurrenceDialogSaveButton' ).disabled ) {
                    // 			Ext.getCmp( 'recurrenceDialogSaveButton' ).handler.call();
                    // 		}
                    // 	}
                    // }]
                }
            ],
            buttons: [
                {
                    text: 'Save',
                    itemId: 'recurrenceDialogSaveButton',
                    // disabled: !this.recurrenceTimeFrameEndDate.isValid(),
                    handler: function () {
                        self.saveRecurrenceButton(recurrenceDialog);
                    }
                },
                {
                    text: 'Restore Default',
                    handler: function () {
                        var recurrencePopup = this.up("#recurrenceDialog");
                        recurrencePopup.down("#generateEvery").setValue(generateEvery);
                        recurrencePopup.down("datefield").setValue(dateVal);
                        recurrencePopup.down("#" + unit + "RecurrenceDialog").setValue(true);
                    }
                },
                {
                    text: 'Cancel',
                    handler: function () {
                        recurrenceDialog.hide();
                    }
                }
            ]
        });

        recurrenceDialog.down("#dayRecurrenceDialog").setValue(false);
        recurrenceDialog.down("#weekRecurrenceDialog").setValue(false);
        recurrenceDialog.down("#monthRecurrenceDialog").setValue(false);
        recurrenceDialog.down("#" + unit + "RecurrenceDialog").setValue(true);

        return recurrenceDialog;
    },

    saveRecurrenceButton: function (recurrenceDialog) {
        if (!recurrenceDialog.down("datefield").isValid()) {
            Ext.Msg.alert('Invalid configuration', 'The next date the report will run on cannot be in the past.');
            return false;
        } else {
            this.saveRecurrenceSettings(recurrenceDialog);
        }
    },

    getRecurrenceStatusText: function (recurrenceDialog) {
        // get the values chosen in the form - date, hour, minute, freq.
        var nextGenTimeDate = Ext.util.Format.date(recurrenceDialog.down("datefield").getValue(), sfw.util.SharedConstants.dateShortInput);
        var radiogroup = recurrenceDialog.down("radiogroup");

        // Deal with adding 's' for the display of the time frame and recurrence schedule
        var displayRecurrenceTimeUnit = radiogroup.getChecked()[0].config.singularName;

        if (1 < recurrenceDialog.down("#generateEvery").getValue()) {
            displayRecurrenceTimeUnit += 's';
        }

        return 'Reports will be generated on ' +
            Ext.util.Format.date(nextGenTimeDate, 'l F jS, Y') + ',  and every ' +
            recurrenceDialog.down("#generateEvery").getValue() + ' ' + displayRecurrenceTimeUnit + ' thereafter.';
    },

    saveRecurrenceSettings: function (recurrenceDialog) {
        const self = this;

        self.setValueForGenerateReport("SCH_end_date", Ext.util.Format.date(recurrenceDialog.down("datefield").getValue(), sfw.util.SharedConstants.dateShortInput) + "T00:00:00");
        self.setValueForGenerateReport("SCH_recurrence_value", recurrenceDialog.down("#generateEvery").getValue());

        var radiogroup = recurrenceDialog.down("radiogroup");
        var recurrenceTimeUnit = radiogroup.getChecked()[0].config.itemId;
        self.setValueForGenerateReport("SCH_recurrence_unit", recurrenceTimeUnit.replace("RecurrenceDialog", ""));

        // update the recurrence string displayed
        const recurrenceDisplayText = this.getRecurrenceStatusText(recurrenceDialog);

        const reportGenerationSchedule = Ext.ComponentQuery.query('panel[itemId=reportGenerationSchedule]')[0];
        const statusTextLabel = reportGenerationSchedule.down("#statusTextLabel");

        statusTextLabel.setText(recurrenceDisplayText);
        recurrenceDialog.hide();
    },

    handleConfigure: function (configureButton) {
        const self = this;
        const reportGenerationSchedule = configureButton.up("#reportGenerationSchedule");
        // Bring up the right dialog based on which radio button is checked
        if (!reportGenerationSchedule.down("[name=timeFrame]").getValue()) {
            (self.createAndGetRecurrenceTimeDialog()).show();
        } else {
            (self.createAndGetOneTimeDialog()).show();
        }
    },

    handleSiteSelectionRadio: function (checkbox, checked) {
        const self = this;
        if (checked) {
            const siteLevelStatistics = Ext.ComponentQuery.query('panel[itemId=siteLevelStatistics]')[0];
            const selectSites = siteLevelStatistics.down("#selectSites");
            const selectHostSmartGroup = siteLevelStatistics.down("#selectHostSmartGroup");

            var specificSelectedText = 'Specific sites or hosts smart groups have been selected. Use the button to the right to view or change the selection.';
            var notSelectedText = 'Specific sites or smart groups need to be selected - use the button to the right.';
            var newText = 'Using data from all sites for users selected above (default).';
            self.setValueForGenerateReport("CSI_SITES_select_all_sites", 0);
            if (checkbox.config.value === 'useAllRadio') {
                selectSites.setDisabled(true);
                selectHostSmartGroup.setDisabled(true);
                self.setValueForGenerateReport("CSI_SITES_select_all_sites", checked ? 1 : 0);
                self.setValueForGenerateReport("CSI_SITES_site_selection_list", "");
                self.setValueForGenerateReport("CSI_SITES_smg_selection_list", "");
            } else if (checkbox.config.value === 'selectSpecificRadio') {
                selectSites.setDisabled(false);
                selectHostSmartGroup.setDisabled(true);
                self.setValueForGenerateReport("CSI_SITES_smg_selection_list", "");
                // if ( this.specificSelected ) {
                // 	newText = specificSelectedText;
                // } else {
                // 	newText = notSelectedText;
                // }
            } else if (checkbox.config.value === 'selectHostSmartGroupRadio') {
                selectSites.setDisabled(true);
                selectHostSmartGroup.setDisabled(false);
                self.setValueForGenerateReport("CSI_SITES_site_selection_list", "");
                // if ( this.SMGSelected ) {
                // 	newText = specificSelectedText;
                // } else {
                // 	newText = notSelectedText;
                // }
            }

            const statusTextLabel = siteLevelStatistics.down("#statusTextLabel");
            statusTextLabel.setText(newText);
        }
    },

    selectSitesBtnClick: function () {
        const self = this;
        const smartGroupsController = sfw.app.getController("sfw.view.results.smartgroup.SmartGroups");
        var sitesSelector = smartGroupsController.createItemSelectorWindow(sfw.util.CommonConstants.CRITERIA_SITES, null, "Site Selection", "CSI_SITES_site_selection_list");
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
            if (reportWindow.reportData.CSI_SITES_site_selection_list) {
                self.getSelectorData({
                    action: 'smart_groups',
                    which: 'siteList',
                    start: 0,
                    limit: 16
                }, sitesSelector, reportWindow.reportData.CSI_SITES_site_selection_list);
            }
        }
    },

    selectHostSmartGroupBtnClick: function () {
        const self = this;
        const smartGroupsController = sfw.app.getController("sfw.view.results.smartgroup.SmartGroups");
        var hostGroupSelector = smartGroupsController.createItemSelectorWindow(sfw.util.CommonConstants.CRITERIA_HOST_GROUP, null, "Select Host Smart Group", "CSI_SITES_smg_selection_list");

        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
            if (reportWindow.reportData.CSI_SITES_smg_selection_list) {
                self.getSelectorData({
                    action: 'smart_groups',
                    which: 'smartgroupList',
                    withSmartGroupType: true,
                    smartGroupTextType: 'host',
                    start: 0,
                    limit: 16
                }, hostGroupSelector, reportWindow.reportData.CSI_SITES_smg_selection_list);
            }
        }
    },

    setReportFormat: function (combobox, record, idx) {
        const self = this;
        var format = null;
        if (typeof record === "number") {
            format = record;
        } else {
            format = record.data.myID;
        }

        self.setValueForGenerateReport("FOR_report_format", format);

        var newText = '';
        var toggle = false;

        if (typeof format !== "undefined") {
            if (format == 4) {
                toggle = true;
                newText = 'When using the CSV option for the report format, all configured sections will be rendered into a separate CSV file and then compressed into a single ZIP file.<br>Please note that some of the configuration options below will have no effect on the resulting data in the CSV files.';
            }

            const executiveSummaryReport = Ext.ComponentQuery.query('panel[itemId=executiveSummaryReport]')[0];
            executiveSummaryReport.setDisabled(toggle);

            const dashboardProfiles = Ext.ComponentQuery.query('panel[itemId=dashboardProfiles]')[0];
            dashboardProfiles.setDisabled(toggle);

            const siteLevelStatistics = Ext.ComponentQuery.query('panel[itemId=siteLevelStatistics]')[0];
            siteLevelStatistics.setDisabled(toggle);

            hostLevelStatistics = Ext.ComponentQuery.query('panel[itemId=hostLevelStatistics]')[0];
            hostLevelStatistics.down("#overallStatsCheckBox").setDisabled(toggle);

            const productLevelStatistics = Ext.ComponentQuery.query('panel[itemId=productLevelStatistics]')[0];
            productLevelStatistics.down("#overallStatsCheckBox").setDisabled(toggle);

            const generalConfigurationOptions = Ext.ComponentQuery.query('panel[itemId=generalConfigurationOptions]')[0];
            generalConfigurationOptions.down("#fileNameArea").setDisabled(toggle);
            generalConfigurationOptions.down("#publishReportParameters").setDisabled(toggle);

        }
        const reportFormat = Ext.ComponentQuery.query('panel[itemId=reportFormat]')[0];
        const statusTextLabel = reportFormat.down("#statusTextLabel");
        statusTextLabel.setHtml(newText);
    },

    saveReportConfiguration: function (saveAction) {
        const self = this;
        const actionProviderURL = 'action=reporting&which=';
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
        } else {
            return;
        }
        if (!self.validateAll()) {
            return; // The appropriate error message is displayed in the validateAll function.
        } else {
            var allParams = reportWindow.reportData;

            var dashboardProfiles = reportWindow.down("#dashboardProfiles");
            var selectedGrid = dashboardProfiles.down("#selectedGrid");
            var selectedDashboardProfiles = selectedGrid.getSelection();
            allParams["CSI_DASH_profile_id"] = "";
            selectedDashboardProfiles.forEach((data, index) => {
                if (index !== 0) {
                    allParams["CSI_DASH_profile_id"] += ",";
                }
                allParams["CSI_DASH_profile_id"] += data.data[selectedGrid.getStore().idProperty];
            });

            var emailRecipients = reportWindow.down("#emailRecipients");
            selectedGrid = emailRecipients.down("#selectedGrid");
            selectedEmailRecipients = selectedGrid.getSelection();
            allParams["CSI_RCPT_recipients"] = "";

            if(reportWindow.down("#defaultRecipients").getValue()){
                allParams['CSI_RCPT_defaults_recipients'] = true
            }
            selectedEmailRecipients.forEach((data, index) => {
                if (index !== 0) {
                    allParams["CSI_RCPT_recipients"] += ",";
                }
                allParams["CSI_RCPT_recipients"] += data.data[selectedGrid.getStore().idProperty];
            });


            var tempSelection = "";
            if (reportWindow.down("#selectSpecificRadio").getValue()) {
                // tempSelection = "";
                // reportWindow.reportData["CSI_SITES_site_selection_list"].forEach((data, index) => {
                //     if (index !== 0) {
                //         tempSelection += ",";
                //     }
                //     tempSelection += data.data["group_id"];
                // });
                allParams["CSI_SITES_site_selection_list"] = reportWindow.reportData["CSI_SITES_site_selection_list"];
            }

            if (reportWindow.down("#selectHostSmartGroupRadio").getValue()) {
                // tempSelection = "";
                // reportWindow.reportData["CSI_SITES_smg_selection_list"].forEach((data, index) => {
                //     if (index !== 0) {
                //         tempSelection += ",";
                //     }
                //     tempSelection += data.data["id"];
                // });
                allParams["CSI_SITES_smg_selection_list"] = reportWindow.reportData["CSI_SITES_smg_selection_list"];
            }


            var url = sfw.util.Globals.apiPath() + '&' + actionProviderURL + saveAction;

            var reportParams = {};
            reportParams['CSI_DASH_profile_id'] = allParams['CSI_DASH_profile_id'];
            reportParams['FOR_report_format'] = allParams['FOR_report_format'];
            reportParams['SCH_one_time'] = allParams['SCH_one_time'];
            reportParams['SCH_end_date'] = allParams['SCH_end_date'];
            reportParams['SCH_recurrence_value'] = allParams['SCH_recurrence_value'];
            reportParams['SCH_recurrence_unit'] = allParams['SCH_recurrence_unit'];
            reportParams['CSI_RCPT_recipients'] = allParams['CSI_RCPT_recipients'];
            reportParams['CSI_RCPT_defaults_recipients'] = allParams['CSI_RCPT_defaults_recipients'];
            reportParams['CSI_MISC_show_report_params'] = allParams['CSI_MISC_show_report_params'];
            reportParams['CSI_MISC_publish_detail_level'] = allParams['CSI_MISC_publish_detail_level'];
            reportParams['source_type'] = allParams['source_type'];

            var siteLevelStatistics = reportWindow.down("#siteLevelStatistics");

            if (siteLevelStatistics.down("#overallStatsCheckBox").getValue() ||
                siteLevelStatistics.down("#critStatsCheckBox").getValue() ||
                siteLevelStatistics.down("#impactStatsCheckBox").getValue()  ||
                siteLevelStatistics.down("#avStatsCheckBox").getValue() ||
                siteLevelStatistics.down("#bySitePatchedCheckBox").getValue() ||
                siteLevelStatistics.down("#bySiteInsecureCheckBox").getValue() ||
                siteLevelStatistics.down("#bySiteEndOfLifeCheckBox").getValue() ||
                siteLevelStatistics.down("#siteDetailsCheckBox").getValue())
            {
                if(allParams['CSI_SITES_select_all_sites']){
                    reportParams['CSI_SITES_select_all_sites'] = allParams['CSI_SITES_select_all_sites'];
                }
                if(allParams['CSI_SITES_site_selection_list']){
                    reportParams['CSI_SITES_site_selection_list'] = allParams['CSI_SITES_site_selection_list'];
                }
                if(allParams['CSI_SITES_smg_selection_list']){
                    reportParams['CSI_SITES_smg_selection_list'] = allParams['CSI_SITES_smg_selection_list'];
                }

                reportParams['CSI_SITES_overall_stats'] = allParams['CSI_SITES_overall_stats'];
                reportParams['CSI_SITES_crit_stats'] = allParams['CSI_SITES_crit_stats'];
                reportParams['CSI_SITES_impact_stats'] = allParams['CSI_SITES_impact_stats'];
                reportParams['CSI_SITES_av_stats'] = allParams['CSI_SITES_av_stats'];
                reportParams['CSI_SITES_by_site_patched_stats'] = allParams['CSI_SITES_by_site_patched_stats'];
                reportParams['CSI_SITES_by_site_insecure_stats'] = allParams['CSI_SITES_by_site_insecure_stats'];
                reportParams['CSI_SITES_by_site_eol_stats'] = allParams['CSI_SITES_by_site_eol_stats'];
                reportParams['CSI_SITES_by_site_details_stats'] = allParams['CSI_SITES_by_site_details_stats'];
            }

            var executiveSummaryReport = reportWindow.down("#executiveSummaryReport");
            if(executiveSummaryReport.down('#includeExecutiveSummaryReport').getValue()){
                reportParams['CSI_EXEC_include_exec_report'] = allParams['CSI_EXEC_include_exec_report'];
            }

            if(reportWindow.down("#hostLevelStatisticsCombobox").getValue()){
                var hostLevelStatistics = reportWindow.down("#hostLevelStatistics");
                if(hostLevelStatistics.down("#overallStatsCheckBox").getValue() || hostLevelStatistics.down("#insecureCheckBox").getValue()
                    || hostLevelStatistics.down("#eolCheckBox").getValue() || hostLevelStatistics.down("#patchedCheckBox").getValue()){
                    reportParams['CSI_HOSTS_hosts_smartgroup'] = allParams['CSI_HOSTS_hosts_smartgroup'];
                    reportParams['CSI_HOSTS_overall_stats'] = allParams['CSI_HOSTS_overall_stats'];
                    reportParams['CSI_HOSTS_insecure_stats'] = allParams['CSI_HOSTS_insecure_stats'];
                    if(hostLevelStatistics.down("#insecureCheckBox").getValue())
                    {
                        reportParams['CSI_HOSTS_least_crit_level'] = allParams['CSI_HOSTS_least_crit_level'];
                    }
                    reportParams['CSI_HOSTS_eol_stats'] = allParams['CSI_HOSTS_eol_stats'];
                    reportParams['CSI_HOSTS_patched_stats'] = allParams['CSI_HOSTS_patched_stats'];
                }
            }

            if(reportWindow.down("#productLevelStatisticsCombobox").getValue()){
                var productLevelStatistics = reportWindow.down("#productLevelStatistics");
                if(productLevelStatistics.down("#overallStatsCheckBox").getValue() || productLevelStatistics.down("#insecureCheckBox").getValue() ||
                    productLevelStatistics.down("#eolCheckBox").getValue() || productLevelStatistics.down("#patchedCheckBox").getValue()){
                    reportParams['CSI_PRODUCTS_products_smartgroup'] = allParams['CSI_PRODUCTS_products_smartgroup'];
                    reportParams['CSI_PRODUCTS_overall_stats'] = allParams['CSI_PRODUCTS_overall_stats'];
                    reportParams['CSI_PRODUCTS_insecure_stats'] = allParams['CSI_PRODUCTS_insecure_stats'];
                    if(productLevelStatistics.down("#insecureCheckBox").getValue()){
                        reportParams['CSI_PRODUCTS_least_crit_level'] = allParams['CSI_PRODUCTS_least_crit_level'];
                    }
                    reportParams['CSI_PRODUCTS_eol_stats'] = allParams['CSI_PRODUCTS_eol_stats'];
                    reportParams['CSI_PRODUCTS_patched_stats'] = allParams['CSI_PRODUCTS_patched_stats'];
                }
            }

            if(reportWindow.down("#filenameCheckbox").getValue()) {
                reportParams['CSI_MISC_file_name'] = allParams['CSI_MISC_file_name'];
            }

            if(reportWindow.down("#reportTitleCheckbox").getValue()) {
                reportParams['CSI_MISC_report_title'] = allParams['CSI_MISC_report_title'];
            }

            if(allParams['report_id']){
                reportParams['report_id'] = allParams['report_id'];
            }

            Ext.Ajax.request({
                url: url,
                params: reportParams,
                success: function (response) {
                    var status = Ext.decode(response.responseText).status;
                    switch (status) {
                        case 0:
                            // parent.refresh();
                            Ext.ComponentQuery.query('window[xtype=reporting.generateReport]')[0].destroy();
                            Ext.getStore('reporting').load();
                            // Construct the success message based on if this is no_email or not
                            var messageText = 'mailed';
                            if ('csi' === reportWindow.sourceType && allParams['CSI_RCPT_no_email']) {
                                messageText = 'available for download';
                            }
                            Ext.getStore('reporting').load();
                            // Note - once the no_email option exists in the vim, a similar condition should be added, or else the sourceType condition can be removed and the params type made generic, i.e., RCPT_no_email - TODO

                            Ext.Msg.alert('Success', 'Configuration saved!  Your report will be automatically generated and ' + messageText + ' when the end of the selected data time frame is reached.');
                            break;
                        case 1:
                            Ext.Msg.alert('Error', 'Report configuration could not be saved. Appropriate options must be selected to generate a non-empty report.');
                            break;
                        default:
                            Ext.Msg.alert('Error', 'Unexpected error.');
                            break;
                    }
                },
                failure: function () {
                    Ext.Msg.alert('Error', 'Unexpected error.');
                }
            });
        }
    },

    validateAll: function () {
        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
        if (reportWindow && reportWindow.length) {
            reportWindow = reportWindow[0];
        } else {
            Ext.Msg.alert('Error', 'Unexpected error.');
            return false;
        }

        var allParams = reportWindow.reportData;
        if (allParams["FOR_report_format"] !== 3 && allParams["FOR_report_format"] !== 4) {
            Ext.Msg.alert('Error', 'The format for the report is not configured.');
            return false;
        }

        // if (new Date(allParams["SCH_end_date"].replace("T00:00:00", "T23:59:00")) < new Date()) {
        //     Ext.Msg.alert('Invalid configuration', 'The next date the report will run on cannot be in the past.');
        //     return false;
        // }

        // Check if sites have been selected
        if (reportWindow.down("#selectSpecificRadio").getValue() && allParams["CSI_SITES_site_selection_list"] === "") {
            Ext.Msg.alert('Error', 'Selected sites list must be configured.');
            return false;
        }

        // Check if smgs have been selected
        if (reportWindow.down("#selectHostSmartGroupRadio").getValue() && allParams["CSI_SITES_smg_selection_list"] === "") {
            Ext.Msg.alert('Error', 'Selected hosts smart group list must be configured.');
            return false;
        }

        if(reportWindow.down("#filenameCheckbox").getValue()) {
            var pdfFileTest = /^(\w+)([\-][\w]+)*[\.][Pp][Dd][Ff]$/;
            if (!pdfFileTest.test(reportWindow.down("#filenameInput").getValue())) {
                Ext.Msg.alert('Error-Miscellaneous', 'The format for the PDF file name is incorrect.');
                return false;
            }
        }

        return true;

    },

    getSelectorData: function (apiRecord, itemSelector, selectedRecordsIds) {
        const self = this;

        var selectedGrid = itemSelector.down("#selectedGrid");
        var selectedStore = selectedGrid.getStore();
        if (typeof selectedRecordsIds !== 'undefined' && typeof selectedRecordsIds !== 'string') {
            selectedStore.add(selectedRecordsIds);
            return;
        }

        var url = "";
        if (typeof apiRecord.action !== 'undefined') {
            url += "action=" + apiRecord.action;
        }
        if (typeof apiRecord.which !== 'undefined') {
            url += "&which=" + apiRecord.which;
        }
        if (typeof apiRecord.module_id !== 'undefined') {
            url += "&module=" + apiRecord.module_id;
        }
        if (typeof apiRecord.method_id !== 'undefined') {
            url += "&method=" + apiRecord.method_id;
        }
        if (typeof apiRecord.instance_id !== 'undefined') {
            url += "&instance_id=" + apiRecord.instance_id;
        }
        if (typeof apiRecord.withSmartGroupType !== 'undefined') {
            url += "&withSmartGroupType=" + apiRecord.withSmartGroupType;
        }
        if (typeof apiRecord.smartGroupTextType !== 'undefined') {
            url += "&smartGroupTextType=" + apiRecord.smartGroupTextType;
        }
        if (typeof apiRecord.start !== 'undefined') {
            url += "&start=" + apiRecord.start;
        }
        if (typeof apiRecord.limit !== 'undefined') {
            url += "&limit=" + apiRecord.limit;
        }

        Ext.Ajax.request({
            url: url,
            method: 'GET',
            success: function (data) {
                var response = Ext.util.JSON.decode(data.responseText);
                var recordsToAdd = [];
                if (typeof selectedRecordsIds === 'string') {
                    selectedRecordsIds = selectedRecordsIds.split(",");
                    if(Array.isArray(response.data.rows)){
                        response.data.rows.forEach(function (data, index) {
                            if (selectedRecordsIds.indexOf("" + data[selectedStore.config.idProperty]) !== -1) {
                                recordsToAdd.push(data);
                            }
                        });
                    }else if(Array.isArray(response.data)){
                        response.data.forEach(function (data, index) {
                            if (selectedRecordsIds.indexOf("" + data[selectedStore.config.idProperty]) !== -1) {
                                recordsToAdd.push(data);
                            }
                        });
                    }
                } else {
                    recordsToAdd = response.data.rows;
                }
                selectedStore.add(recordsToAdd);
            },
            failure: function () {
                 Ext.Msg.alert("Error", "Unexpected Error");
            }
        });

    }

});