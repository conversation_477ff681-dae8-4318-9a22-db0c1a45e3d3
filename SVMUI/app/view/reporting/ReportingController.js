/**
 * This class is the controller for the main view for the application. It is specified as
 * the "controller" of the Main view class.
 */
Ext.define('sfw.view.reporting.ReportingController', {
	extend: 'Ext.app.ViewController',

	alias: 'controller.reports',
	requires: [
		'sfw.util.Util'
	],
	onItemSelected: function (sender, record) {
		Ext.Msg.confirm('Confirm', 'Are you sure?', 'onConfirm', this);
	},

	onConfirm: function (choice) {

		if (choice === 'yes') {
			//
		}
	},
	
	processStatusVals : {
		ALIVE: 0,
		IN_PROGRESS: 1,
		PROCESSED: 2,
		ALIVE_PROCESSED: 3,
		FAILED_TOO_BIG: 4
	},

	reportTitleRenderer : function( value ) {
		if ( !value ) {
			return 'Flexera Custom Report';
		} else {
			return value;
		}
	},

	generateOnRenderer : function( value, cell, record ) {

		// If this was a one-time report that was already generated, there is no 'next generation' date, so this is n/a
		var oneTime = parseInt( record.data.one_time_gen, 10 );
		var processStatus = parseInt( record.data.process_status, 10 );
		if ( 2 === processStatus && oneTime ) {
			return '-';
		}

		var end = sfw.Util.dateCreate(value);
		// end = Date.parseDate( value, "Y-m-d");

		if ( !end ) {
			return '-';
		}
		var dateFormat = 'M j, Y';
		var output = Ext.util.Format.date( end, dateFormat );
		return output;
	},

	recurrenceScheduleRenderer : function( value, metaData, record ) {
		if ( typeof value == 'undefined' ) {
			return '-';
		}

		if ( parseInt(record.data.one_time_gen, 10) == 1 ) {
			return 'None (One-Time Report)';
		}

		var schedule = value.split( ',' );
		if ( schedule.length !== 3 ) {
			return '-';
		}

		if ( !( typeof parseInt( schedule[0], 10 ) == 'number' ) || !( typeof parseInt( schedule[3], 10 ) == 'number' ) || !( typeof parseInt( schedule[3], 10 ) == 'number' ) ) {
			return '-';
		}

		if ( parseInt( schedule[0], 10 ) == 1 ) {
			return "Every Day";
		} else if ( parseInt( schedule[0], 10 ) > 1 ) {
			return "Every " + schedule[0] + " Days";
		} else if ( parseInt( schedule[1], 10 ) == 1 ) {
			return "Every Week";
		} else if ( parseInt( schedule[1], 10 ) > 1 ) {
			return "Every " + schedule[1]  + " Weeks";
		} else if ( parseInt( schedule[2], 10 ) == 1 ) {
			return "Every Month";
		} else if ( parseInt( schedule[2], 10 ) > 1 ) {
			return "Every " + schedule[2]  + " Months";
		}

		return '-';
	},

	processStatusRenderer : function( value, metaData, record ) {
		var status = '';
		var processStatus = parseInt( value, 10 );
		switch ( processStatus ) {
			case this.processStatusVals.ALIVE:
				status = "Alive";
				break;
			case this.processStatusVals.IN_PROGRESS:
				status = "In Progress";
				break;
			case this.processStatusVals.PROCESSED:
				status = "Processed";
				break;
			case this.processStatusVals.ALIVE_PROCESSED:
				status = "Alive";
				break;
			case this.processStatusVals.FAILED_TOO_BIG:
				status = '<div qtip="Report could not be generated due to an excessive volume of data requested. Please reconfigure report and try again.">Failed</div>';
				break;
			default:
				status = "Error";
				break;
		}
		return status;
	},

	recipientsRenderer : function( val, md, record ) {
		if ( parseInt( record.data.no_email, 10 ) === 0 ) {
			return tipRenderer( val );
		}
		return '-';
	},

	gridRenderUTCDateInLocaltime : function( value ) {
		if ( value ) {
			var date =  sfw.Util.dateCreate( value, true );
			return  Ext.Date.format(date,'jS M, Y H:i');
		} else {
			return '-';
		}
	},

	downloadButtonRenderer : function( val, md, record) {
		var link = '-';
		var processStatus = parseInt(record.data.process_status, 10);
		var reportId = parseInt(record.data.id, 10);
		if ( processStatus === this.processStatusVals.PROCESSED || processStatus === this.processStatusVals.ALIVE_PROCESSED ) {
			link = '<a href="javascript:void(0);">' +	'Download' + '</a> ';
		}
		return link;
	},

	generateReportNow: function (event, target, options) {

		var report_id = options.extra.data.id;
		Ext.Ajax.request({
			url: 'action=reporting&which=generate_report&',
			method: 'POST',
			params: {
				generate_id: report_id
			},
			dataType: 'json',
			success: function (response) {
				var status = Ext.util.JSON.decode(response.responseText);
				if (status.success) {
					Ext.getCmp('reportConfigGrid').getStore().load(); // Refresh main page
				} else {
					switch (status.error) {
						case 0:
							// no alert, because PDF generation gives a lag of few seconds and
							// popup after few seconds is not a expected UI behaviour
							break;
						case 1:
							Ext.Msg.alert("Error", "Report Id is Incorrect");
							break;
						case 2:
							Ext.Msg.alert("Error", "Configuration Id is Incorrect");
							break;
						case 3:
							Ext.Msg.alert("Report generation", "The report is being generated and will be available in a few moments.");
							break;
						default:
							Ext.Msg.alert("Unexpected Error", "Unable To Generate Report...");
							break;

					}
					Ext.getCmp('reportConfigGrid').getStore().load();
				}
			},
			failure: function () {
				if (true === response.isTimeout) {
					Ext.Msg.alert("Report generation", "The report is still being generated.<br/><br/>Refresh the Report Configuration page in a few seconds to see if it has been completed");
				} else {
					Ext.Msg.alert("Unexpected Error", "Unable To Generate Report...");
				}
			}
		});
	},

	deleteReport: function (event, target, options) {

        var selectionArray = [];
        for ( var i=0; i < options.extra.length; ++i ) {
            selectionArray.push( options.extra[i].data.id );
        }

        deleteIdList = selectionArray.join(",");
		Ext.Ajax.request({
			url: 'action=reporting&which=delete&',
			method: 'POST',
			params: {
				delete_id_list: deleteIdList
			},
			dataType: 'json',
			success: function (response) {
				var status = Ext.util.JSON.decode(response.responseText);
				switch (status.error) {
					case 0:
						var message = 'Report Configuration  Deleted';
						Ext.Msg.alert("Success", message);
						Ext.getCmp('reportConfigGrid').getStore().load();
						break;
					case 1:
						Ext.Msg.alert("Error", "Report Id is Incorrect");
						break;
					default:
						Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
						break;
				}
			},
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
			}
		});

	},

	downloadReport: function (view, record) {

		var id = record.get('id');

		if (Ext.isEmpty(id)) {
			return;
		}

		Ext.Ajax.request({
			url: 'action=reporting&which=prepare_report_download&',
			method: 'GET',
			params: {
				report_id: id
			},
			success: function (data) {
				var response = {};
				try {
					response = Ext.util.JSON.decode(data.responseText);

					switch (response.error) {
						case 1:
							Ext.Msg.alert("Error", "Incorrect Report Id.");
							break;
						case 2:
							Ext.Msg.alert("Error", "Server error.");
							break;
						case 3:
							Ext.Msg.alert("Error", "File does not exist.");
							break;
						case 0:
							var token = response.token ? response.token : '';
							if (token) {
								var reportUrl = sfw.Globals.apiPath() + 'action=download_report&token=' + token;
								window.open(reportUrl);
							} // else default case
							break;
						default:
							Ext.Msg.alert("Unexpected Error", "Unable to download report.");
							break;
					}

				} catch (ex) {
					sfw.util.Debug.trigger( ex, 'ReportingController.downloadReport() - ajax' );
				}
			},
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Unable to download report.");
			}
		});
	},

	openReportConfigurationWindow:  function( event, target, options, configurationAllElements ) {

		reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
		var generateReport = null;
		if(reportWindow && reportWindow.length){
			generateReport = reportWindow[0];
		}
		else {
			generateReport = Ext.create('sfw.view.reporting.generatereport.GenerateReport');
		}


		generateReport.titleType = options.extra.titleType;
		generateReport.sourceType = 'csi';
		//console.log("configurationAllElements: ", configurationAllElements);
		generateReport.configurationAllElements = configurationAllElements;
		generateReport.show();

		// return;

		// // Create an instance of the reportgenerator
		// // If already created, reset all the elements for new configuration
		// if ( 'undefined' === typeof( self.reportGenerator ) ) {
		// 	self.reportGenerator = new sfw.reportGenerator.create( self, sourceType );
		// 	self.reportGenerator.updateTitle( titleType );

		// } else {
		// 	self.reportGenerator.resetAll();
		// 	self.reportGenerator.updateTitle( titleType );
		// }

		// if ( !self.reportGenerator.window.isVisible() ) {
		// 	self.reportGenerator.window.show();
		// }

		// // Call this during init to create the window, but don't show it.  For this we have the 'hidden' parameter set.
		// if ( hidden === true ) {
		// 	self.reportGenerator.window.hide();
		// }
		// sfw.csiReReportFormat.create().setReportFormat(null);

	},

	editReportConfiguration: function ( event, target, options ) {
		const self = this;
		// Becuase of the way the grid is configured, we need to send more data than
		// it is actually being displayed
		const configurationAllElements = options.extra.getAt(0).data;
		configurationAllElements.id = parseInt( configurationAllElements.id, 10 );
		self.openReportConfigurationWindow(null, null, {extra: {titleType: 'edit'}}, configurationAllElements);
	},

	dblclickHandler: function () {
		const self = this;
		// Becuase of the way the grid is configured, we need to send more data than
		// it is actually being displayed
		const configurationAllElements = Ext.getCmp('reportConfigGrid').getSelectionModel().getSelected().items[0].data;
		configurationAllElements.id = parseInt( configurationAllElements.id, 10 );
		self.openReportConfigurationWindow(null, null, {extra: {titleType: 'edit'}}, configurationAllElements);
	},

	reloadReportConfigurationGrid: function(){
		var view = this.getView();

		var reportConfoguration = Ext.getStore('reporting');

		reportConfoguration.getProxy().setExtraParams({
			'report_title': view.down('#report_title').getValue(),
			'report_type': view.down('#report_type').getValue()})
		reportConfoguration.loadPage(1,{
			callback: function () {
				//console.log("sucess");
			},
			failure: function () {
				//console.log("failed");
			}
		});
	},

	reloadReportConfigurationGridOnEnter: function(field, e){
		if (e.getKey() == e.ENTER) {
			this.reloadReportConfigurationGrid();
		}
	}

});
