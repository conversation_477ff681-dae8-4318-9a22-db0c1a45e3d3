Ext.define('sfw.view.reporting.AcivityLogNotificationController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.activitylognotification',

    deleteActivityLogNotification:function(event, target,options){
        var selectionArray = [];
        for ( var i=0; i < options.extra.length; ++i ) {
            selectionArray.push( options.extra[i].data.notification_id );
        }
        deleteIdList = selectionArray.join(",");

        multipleSelect = false;

        if(options.extra.length > 1){
            multipleSelect = true;
        }

        Ext.Ajax.request({
            url: 'action=csi_notifications&which=delete&',
            method: 'POST',
            params: {
                delete_id_list: deleteIdList
            },
            dataType: 'json',
            success: function (response) {
                var response = Ext.util.JSON.decode( response.responseText );
                switch( response.status ) {
                    case 0:
                        var numDeleted = response.numDeleted;
                        var message = '';
                        if ( numDeleted < options.extra.length ) {
                            message = "Only " + numDeleted + " of " + options.extra.length + " ";
                        }
                        message += 'Notification' + (multipleSelect ? 's' : '') + ' Deleted';
                        Ext.Msg.alert( "Success", message );
                        Ext.getStore('activitylognotification').load();// reload the grid
                        break;
                    case 1:
                        Ext.Msg.alert( "Error", "Notification Id is Incorrect" );
                        break;
                    default:
                        Ext.Msg.alert( "Unexpected Error", "Unable to Delete..." );
                        break;
                }
            },
            failure: function () {
                Ext.Msg.alert("Unexpected Error", "Unable to Delete...");
            }
        });
    },

    dblclickHandler: function () {
        const self = this,
            view = self.getView();
        const record = view.getSelection()[0];
        sfw.Default.saveEditNotificationConfiguration(null, null, {extra: record});
    },
});