Ext.define('sfw.view.commonpanels.LocalItemSelector', {
    extend: 'Ext.Panel',
    xtype: 'localItemSelector',
    layout: 'hbox',
    scrollable:'vertical',
    region: 'center',
    hidden: true,
    itemId:'localItemSelector',
    layoutConfig: {
        align: 'stretch'
    },
    height: '100%',
    width: '100%',
    flex: 1,
    zIndex: 99999,
    defaults: {
        flex: 1
    },
    border: true,
    bbar: {
        xtype: 'pagingtoolbar',
        displayInfo: true,
        itemId:'multiSelectPagination',
        // displayMsg: 'Displaying Available ' + config.namePlural + ' {0} - {1} of {2}'
        listeners: {
            change: function(pagingtoolbar, pageData, eOpts) {
                var localItemSelector = pagingtoolbar.up("localItemSelector");
                var availableGrid = localItemSelector.down("#availableGrid");
                var selectedGrid = localItemSelector.down("#selectedGrid");
                var availableStore = availableGrid.getStore();
                var selectedStore = selectedGrid.getStore();

                var recordToSelect = [];

                var availableRecords = availableStore.getData().items;
                var selectedRecords = selectedStore.getData().items;

                if(selectedRecords && availableRecords) {
                    selectedRecords.forEach(selectedRec => {
                        availableRecords.forEach(availableRec => {
                            if(selectedRec.data[availableStore.config.idProperty] === availableRec.data[availableStore.config.idProperty] ) {
                                recordToSelect.push(availableRec);
                            }
                        });
                    });
                }

                var selectionModel = availableGrid.getSelectionModel();
                selectionModel.select(recordToSelect);
            }
        }
    },
    tbar: [
        {
            xtype: 'textfield',
            itemId: 'searchFieldItemSelector',
            emptyText: 'Search...',
            listeners: {
                specialkey: function(field, e) {
                    if ( e.ENTER == e.getKey() ) {
                        var store = field.up('panel').down('grid').getStore();
                        const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        const config = localItemSelector.itemSelectorConfig;
                        store.getProxy().setExtraParams({  'search': field.getValue()});
                        store.loadPage(1,{
                            callback: function () {
                                //console.log("sucess");
                            },
                            failure: function () {
                                //console.log("failed");
                            }
                        });
                    }
                }
            }
        },
        {
            xtype: 'button',
            text: 'Search',
            handler: function() {
                var store = this.up('panel').down('grid').getStore();
                const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                const config = localItemSelector.itemSelectorConfig;
                store.getProxy().setExtraParams({  'search': this.up('panel').down('#searchFieldItemSelector').getValue()});
                store.loadPage(1,{
                    callback: function () {
                        //console.log("sucess");
                    },
                    failure: function () {
                        //console.log("failed");
                    }
                });
            }
        }
    ]
});