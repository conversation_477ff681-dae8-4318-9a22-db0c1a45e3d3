Ext.define('sfw.view.commonpanels.ManageAssignments', {
    extend: "Ext.window.Window",
    width: 850,
    height: 700,
    bodyPadding: 20,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    viewModel: {
        type: 'manageassignments'
    },

    config: {
        source: null,
        vpmId: null,
        wizardId: null,
        singlePatchId: null,
        subscriptionId: null,
        connectionId: null,
        taskGuid: null
    },

    items: [
        {
            xtype: 'container',
            title: 'Manage Assignments',
            layout: {
                type: 'vbox',
                align: 'stretch'
            },
            defaults: {
                width: '95%',
                padding: 5
            },

            items: [{
                width: '70%',
                xtype: "combo",
                valueField: 'id',
                displayField: 'name',
                mode: 'local',
                labelWidth: 150,
                fieldLabel: 'Select Connection',
                itemId: 'connections_options',
                allowBlank: false,
                editable: false,
                name: 'name',
                hiddenName: 'name',
                triggerAction: 'all',
                //emptyText: 'Choose Option',
                listeners: {
                    select:function(combo,value){
                        sfw.Default.changeConnections(this.getValue());
                    }
                }
            }, {
                xtype: 'box',
                html: '<hr style="width: 100%; height: 2px;"/>',
                itemId: 'hrline'
            }, {
                xtype: 'container',
                html: '<b>Required</b>',
                margin: '2 0 2 0',
                padding: 0

            }, {
                xtype: 'grid',
                layout: 'fit',
                flex: 1,
                height: 150,
                autoScroll: true,
                itemId: 'requiredGrid',
                bind: {
                    store: '{assignments}'
                },
                style: {
                    border: '1px solid #919191' // Add outline to the grid
                },
                margin: '0 0 10 0',
                viewConfig: {
                    deferEmptyText: false
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [{
                        xtype: 'displayfield',
                        //html: '<a href="#" onclick="sfw.Default.intuneGroups(1)" style="color:blue;">Add Groups</a>',
                        itemId: 'addGroups'
                    }, {
                        xtype: "tbspacer", height: 3
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllDevices()" style="color:blue;"> Add All Devices</a>',
                        itemId: 'addAllDevices'

                    }, {
                        xtype: 'tbspacer', width: 2
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllUsers()" style="color:blue;">Add All Users</a>',
                        itemId: 'addAllUsers'
                    }]
                }],


                columns: [{
                    text: 'Group',
                    flex: 1,
                    dataIndex: 'group_name',
                    renderer: function (val, meta, record) {
                        if (!record.get('group_name')) {
                            if (record.get('group_all') == 1) {
                                return "All Users";
                            } else if (record.get('group_all') == 2) {
                                return "All Devices"
                            }
                        } else {
                            return record.get('group_name');
                        }
                    }
                }, {
                    text: 'Included/Excluded',
                    xtype: 'checkcolumn',
                    flex: 1,
                    dataIndex: 'group_mode',
                    itemId:'groupModeRequired',
                    renderer: function(val, meta, record) {
                        var excluded = false
                        if ((record.get('group_all') == 2) || (record.get('group_all') == 1) || (record.get('disable') == 1)) {
                            excluded = true;
                            meta.tdCls = 'x-item-disabled';
                        }
                        // Set the tooltip based on the value
                        meta.tdAttr = 'data-qtip="' + (excluded ? 'Since this groups is already assigned as Included in another section it will be marked as excluded' +
                            '' : '') + '"';

                        return new Ext.ux.CheckColumn().renderer(parseInt(val));
                    },
                    listeners: {
                        checkchange: function (column, rowIndex, checked, record) {
                            sfw.Default.handleCheckChange(column, rowIndex, checked, record, '#availableGrid', '#uninstallGrid');
                        }
                    }
                }, {
                    text: 'Availability',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }

                }, {
                    text: 'Installation Deadline',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }
                }, {
                    text: 'Delete',
                    tooltip: 'Delete',
                    dataIndex: 'group_name',
                    renderer: sfw.util.Default.renderAssignmentsDelete,
                    flex: 1
                }],

            }, {
                xtype: 'container',
                html: '<b>Available</b>',
                margin: '2 0 2 0',
                padding: 0,
                itemId: 'availableTitle',

            }, {
                xtype: 'grid',
                layout: 'fit',
                flex: 1,
                height: 150,
                itemId: 'availableGrid',
                autoScroll: true,
                bind: {
                    store: '{assignments_available}'
                },
                style: {
                    border: '1px solid #919191' // Add outline to the grid
                },
                margin: '0 0 10 0',
                listeners: {
                    beforerender: function (grid) {
                        grid.getStore().filter('intent', 1);
                    }
                },
                viewConfig: {
                    deferEmptyText: false
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [{
                        xtype: 'displayfield',
                        //html: '<a href="#" onclick="sfw.Default.intuneGroups(1)" style="color:blue;">Add Groups</a>',
                        itemId: 'addGroupsAvailable'
                    }, {
                        xtype: "tbspacer", height: 3
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllDevices(0)" style="color:blue;"> Add All Devices</a>',
                        itemId: 'addAllDevicesAvailable'

                    }, {
                        xtype: 'tbspacer', width: 2
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllUsers(0)" style="color:blue;">Add All Users</a>',
                        itemId: 'addAllUsersAvailable'
                    }]
                }],


                columns: [{
                    text: 'Group',
                    flex: 1,
                    dataIndex: 'group_name',
                    renderer: function (val, meta, record) {
                        if (!record.get('group_name')) {
                            if (record.get('group_all') == 1) {
                                return "All Users";
                            } else if (record.get('group_all') == 2) {
                                return "All Devices"
                            }
                        } else {
                            return record.get('group_name');
                        }
                    }
                }, {
                    text: 'Included/Excluded',
                    xtype: 'checkcolumn',
                    flex: 1,
                    dataIndex: 'group_mode',
                    renderer: function (val, meta, record) {
                        var excluded = false
                        if ((record.get('group_all') == 2) || (record.get('group_all') == 1) || (record.get('disable') == 1)) {
                            excluded = true;
                            meta.tdCls = 'x-item-disabled';
                        }
                        // Set the tooltip based on the value
                        meta.tdAttr = 'data-qtip="' + (excluded ? 'Since this groups is already assigned as Included in another section it will be marked as excluded' +
                            '' : '') + '"';

                        // Return the checkbox HTML
                        return new Ext.ux.CheckColumn().renderer(parseInt(val));
                    },
                    listeners: {
                        checkchange: function (column, rowIndex, checked, record) {
                            sfw.Default.handleCheckChange(column, rowIndex, checked, record, '#requiredGrid', '#uninstallGrid');
                        }
                    }
                }, {
                    text: 'Availability',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }

                }, {
                    text: 'Installation Deadline',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }
                }, {
                    text: 'Delete',
                    tooltip: 'Delete',
                    dataIndex: 'group_name',
                    renderer: sfw.util.Default.renderAssignmentsDelete,
                    flex: 1
                }],

            }, {
                xtype: 'container',
                html: '<b>Uninstall</b>',
                margin: '2 0 2 0',
                padding: 0,
                itemId: 'uninstallTitle',

            }, {
                xtype: 'grid',
                layout: 'fit',
                flex: 1,
                height: 150,
                autoScroll: true,
                itemId: 'uninstallGrid',
                bind: {
                    store: '{assignments_uninstall}'
                },
                style: {
                    border: '1px solid #919191' ,
                    marginBottom: '10px'// Add outline to the grid
                },
                margin: '0 0 20 0',
                listeners: {
                    beforerender: function (grid) {
                        grid.getStore().filter('intent', 2);
                    }
                },

                viewConfig: {
                    deferEmptyText: false
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [{
                        xtype: 'displayfield',
                        //html: '<a href="#" onclick="sfw.Default.intuneGroups(1)" style="color:blue;">Add Groups</a>',
                        itemId: 'addGroupsUninstall'
                    }, {
                        xtype: "tbspacer", height: 3
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllDevices(2)" style="color:blue;"> Add All Devices</a>',
                        itemId: 'addAllDevicesUninstall'

                    }, {
                        xtype: 'tbspacer', width: 2
                    }, {
                        xtype: 'displayfield',
                        value: '<a href="#" onclick="sfw.Default.addAllUsers(2)" style="color:blue;">Add All Users</a>',
                        itemId: 'addAllUsersUninstall'
                    }]
                }],


                columns: [{
                    text: 'Group',
                    flex: 1,
                    dataIndex: 'group_name',
                    renderer: function (val, meta, record){
                        if(!record.get('group_name')){
                            if(record.get('group_all') == 1){
                                return "All Users";
                            }else if(record.get('group_all') == 2){
                                return "All Devices"
                            }
                        }else{
                            return record.get('group_name');
                        }
                    }
                }, {
                    text: 'Included/Excluded',
                    xtype: 'checkcolumn',
                    flex: 1,
                    dataIndex: 'group_mode',
                    renderer: function (val, meta, record) {

                        if((record.get('group_all') == 2) || (record.get('group_all') == 1) || (record.get('disable') == 1)){
                            meta.tdCls = 'x-item-disabled';
                        }else{
                            meta.tdCls = '';
                        }

                        return new Ext.ux.CheckColumn().renderer(parseInt(val));
                    },
                    listeners: {
                        checkchange: function (column, rowIndex, checked, record) {
                            sfw.Default.handleCheckChange(column, rowIndex, checked, record, '#requiredGrid', '#availableGrid');
                        }
                    }
                }, {
                    text: 'Availability',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }

                }, {
                    text: 'Installation Deadline',
                    flex: 1,
                    dataIndex: 'availability',
                    renderer: function () {
                        return 'As soon as possible';
                    }
                }, {
                    text: 'Delete',
                    tooltip: 'Delete',
                    dataIndex:'group_name',
                    renderer: sfw.util.Default.renderAssignmentsDelete,
                    flex:1
                }],

            }],
        }
    ],
    listeners:{
        beforeclose:sfw.Default.closeAssignmentWindow
    },
    buttons: [
        {
            text: 'Save',
            tabIndex: 8,
            handler : sfw.util.Default.saveAssignments

        },
        {
            text: 'Close',
            tabIndex: 8,
            handler: sfw.Default.closeAssignmentWindow
        }
    ]
});
