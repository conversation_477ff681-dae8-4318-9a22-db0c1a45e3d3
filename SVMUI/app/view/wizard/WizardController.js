Ext.define('sfw.view.wizard.WizardController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.wizard',

    requires: [
        'Ext.History'
    ],

    onSubmitTap: function() {

    },

    onCancelTap: function() {

    },

    onPrevTap: function() {
        const me = this,
            tabs = me.lookup('tabs'),
            keys = tabs.items.keys,
            activeTab = tabs.getActiveTab();

        const indexOfActiveTab = keys.indexOf(activeTab.id);
        if (indexOfActiveTab !== 1) {
            me.lookup('next').enable();
            tabs.setActiveTab(keys[indexOfActiveTab - 1]);
        } else {
            tabs.setActiveTab(0);
            me.lookup('prev').disable();
        }
    },

    onNextTap: function() {
        const me = this,
            tabs = me.lookup('tabs'),
            keys = tabs.items.keys,
            activeTab = tabs.getActiveTab();

        const indexOfActiveTab = keys.indexOf(activeTab.id);
        if (indexOfActiveTab !== keys.length) {
            me.lookup('prev').enable();
            tabs.setActiveTab(keys[indexOfActiveTab + 1]);
        } else {
            tabs.setActiveTab(keys.length - 1);
            me.lookup('next').disable();
        }
    },

    onScreenAdded: function() {
        const me = this,
            tabs = me.lookup('tabs'),
            keys = tabs.items.keys;

        tabs.setActiveTab(0);
    },

    onScreenRemove: function(tabs) {
        if (!tabs.destroying) {
            //this.resync();
        }
    },

    onScreenActivate: function(tabs) {
        // This event is triggered when the view is being destroyed!

    },

    onBeforeTabChange: function (tabpanel, newTab) {
        //console.log('>> onBeforeTabChange');
        newTab.animate({
            duration: 300,
            from: {
                x: 650 //325
            },
            to: {
                x: 288
            }
        });
    }
});