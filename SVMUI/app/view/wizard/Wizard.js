Ext.define('sfw.view.wizard.Wizard', {
    extend: 'Ext.window.Window',

    controller: {
        type: 'wizard'
    },

    viewModel: {
        data: {
            record: null
        }
    },

    config: {
        screens: [],
        modal: true
    },

    tbar: [{
        reference: 'prev',
        handler: 'onPrevTap',
        style: {
            borderRadius: '15px'
        },
        tooltip: 'Previous Screen',
        iconCls: 'x-fas fa-angle-double-left'
    }, {
        reference: 'next',
        handler: 'onNextTap',
        style: {
            borderRadius: '15px'
        },
        tooltip: 'Next Screen',
        iconCls: 'x-fas fa-angle-double-right'
    },
    '->',
    {
        reference: 'submit',
        handler: 'onSubmitTap',
        text: 'Save',
        iconCls: 'x-fas fa-save'
    }, {
        reference: 'cancel',
        handler: 'onCancelTap',
        text: 'Cancel',
        iconCls: 'x-fas fa-times'
    }],

    modelValidation: true,
    layout: 'fit',
    height: 580,
    width: 650,
    iconCls: 'x-fa fa-plus',

    items: [{
        xtype: 'tabpanel',
        reference: 'tabs',
        activeTab: 0,
        defaults: {
            padding: 15,
            //iconAlign: 'top'
        },

        tabBar: {
            layout: {
                pack: 'center'
            }
        },

        listeners: {
            added: 'onScreenAdded',
            remove: 'onScreenRemove',
            beforetabchange: 'onBeforeTabChange',
            activeitemchange: 'onScreenActivate'
        }
    }],

    initComponent: function() {
        var me = this;

        me.callParent();
        me.lookup('tabs').add(me.getScreens());
        me.lookup('tabs').setActiveTab(0);
    },

    reset: function() {
        var me = this;
        me.callParent();
        me.fireEvent('reset');
        return me;
    },

    // [WORKAROUND] Ext.form.Panel override the setRecord and updateRecord methods in a way
    // that we can't use updateRecord to be notified when the record actually changes.
    setRecord: function(record) {
        this.getViewModel().set('record', record);
    }
});