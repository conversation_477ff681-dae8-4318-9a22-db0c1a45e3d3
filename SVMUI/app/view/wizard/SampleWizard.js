Ext.define('sfw.view.wizard.SampleWizard', {
    extend: 'sfw.view.wizard.Wizard',
    xtype: 'samplewizard',

    controller: {
        type: 'samplewizard'
    },

    viewModel: {
        type: 'samplewizard'
    },

    title: 'Add Employee',
    cls: 'person-create',

    screens: [{
        title: 'General',
        defaults: {
            flex: 1,
            width: '100%'
        },
        iconCls: 'x-fa fa-info',
        items: [{
            xtype: 'textfield',
            reference: 'firstname',
            fieldLabel: 'First Name',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'lastname',
            fieldLabel: 'Last Name',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'username',
            fieldLabel: 'Username',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'password',
            fieldLabel: 'Password',
            required: true//,

        }, {
            xtype: 'textfield',
            reference: 'password_check',
            fieldLabel: 'Confirm Password',
            disabled: true
        }]
    }, {
        title: 'Personal',
        defaults: {
            flex: 1,
            width: '100%'
        },
        iconCls: 'x-fa fa-home',
        items: [{
            xtype: 'datefield',
            reference: 'birthday',
            fieldLabel: 'Birthday',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'email',
            fieldLabel: 'Email',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'phone',
            fieldLabel: 'Phone',
            required: true
        }, {
            xtype: 'textfield',
            reference: 'skype',
            fieldLabel: 'Skype'
        }, {
            xtype: 'textfield',
            reference: 'linkedin',
            fieldLabel: 'LinkedIn'
        }]
    }, {
        title: 'Work',
        iconCls: 'x-fa fa-sitemap',
        defaults: {
            flex: 1,
            width: '100%'
        },
        items: [{
            xtype: 'textfield',
            reference: 'title',
            fieldLabel: 'Job Title',
            required: true
        }, {
            xtype: 'datefield',
            reference: 'started',
            fieldLabel: 'Entry date',
            required: true
        }, {
            xtype: 'datefield',
            reference: 'ended',
            fieldLabel: 'Exit date',
            bind: '{record.ended}'
        }, {
            xtype: 'combobox',
            fieldLabel: 'Office',
            displayField: 'name',
            valueField: 'value',
            queryMode: 'local',
            forceSelection: true,
            required: true,
            store: {
                data: [
                    {id: 1, name: 'Banglore'}
                ]
            }
        }, {
            xtype: 'combobox',
            fieldLabel: 'Organization',
            displayField: 'name',
            valueField: 'id',
            queryMode: 'local',
            forceSelection: true,
            required: true,
            store: {
                data: [
                    {id: 1, name: 'Flexera'}
                ]
            }
        }]
    }]
});