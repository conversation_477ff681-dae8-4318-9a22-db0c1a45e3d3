Ext.define('sfw.view.configuration.ActivityLog',{
    extend:'Ext.grid.Panel',
    xtype:'sfw.CSIActivityLog',

    title:'Activity Log',
    border: 1,
    cls: 'shadow',
    requires:[
        'sfw.store.configuration.ActivityLog',
        'sfw.view.configuration.AcivityLogController',
        'sfw.CommonConstants'],

    controller:'activitylog',

    store:{
        type:'activitylog'
    },

    viewConfig: {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'button',
            text: 'Show all logs',
            handler: 'showAll'
        }, {
            xtype:'tbseparator'
        },{
            xtype:'label',
            text:'From:'
        },{
            xtype: 'datefield',
            name: 'date_start',
            format:sfw.Globals.dateShortInput,
            value: Ext.Date.add(sfw.Util.dateCreate(), Ext.Date.MONTH, -3 ),
            itemId: 'date_start',
            listeners: {
                change: 'refresh'
            }
        },{
            xtype:'label',
            text:'To:'
        } ,{
            xtype: 'datefield',
            name: 'date_end',
            value:sfw.Util.dateCreateTodayOffset(),
            format:sfw.Globals.dateShortInput,
            itemId: 'date_end',
            value: Ext.Date.add(new Date(), Ext.Date.DAY),
            listeners: {
                change: 'refresh'
            }
        },{
            xtype:'tbseparator'
        }, {
            xtype:'label',
            text:'Search type:'
        },{
            xtype: 'combo',
            //emptyText: '',
            itemId:'searchabletype',
            store: new Ext.data.SimpleStore({
                fields: ["value", "label"],
                data: sfw.CommonConstants.ACTIVITYTYPECONSTANTS
            }) ,
            valueField: "value",
            displayField: "label",
            width: 240 ,
            mode: "local",
            editable: false,
            triggerAction: "all",
            selectOnFocus: false,
            listeners: {
                select: 'refresh'
            }
        },{
            xtype: 'textfield',
            emptyText: 'Search text....',
            itemId:'searchable_field'
        },{
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler:'refresh'
        },{
            xtype:'tbseparator'
        },{
            text:'Show Priorities',
            menu: new Ext.menu.Menu({
                itemId: 'priorityMenu',
                items: [{
                    text: 'High Priority',
                    itemId: "_highp",
                    checked: true,
                    checkHandler: 'checkPriority',
                    xtype: 'menucheckitem',
                    hideOnClick: false
                }, {
                    text: 'Medium Priority',
                    itemId: "_mediump",
                    checked: true,
                    checkHandler: 'checkPriority',
                    xtype: 'menucheckitem',
                    hideOnClick: false
                },{
                    text: 'Low Priority',
                    itemId: "_lowp",
                    checked: true,
                    checkHandler: 'checkPriority',
                    xtype: 'menucheckitem',
                    hideOnClick: false
                }
                ]
            })
        },{
            xtype: 'button',
            ui: 'primary',
            text: 'Create Notification',
            handler: sfw.Default.saveEditNotificationConfiguration
        },{
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton'
        }
        ]
    }],

    columns: [
        { text: 'Activity Name', dataIndex: 'activity_type', flex: 1 },
        { text: 'Activity Status', dataIndex: 'activity_status', flex: 1 },
        { text: 'User', dataIndex: 'username', flex: 1, renderer:function( value, tmp, store, index ) {
                var actType, actText;
                if ( ! value ) {
                    actType = store.data.activity_type;
                    if ( actType === 1 && store.data.activity_text ) {
                        // A username can be pulled out of activity_text data but we
                        // can't sort by it so append it to the N/A
                        return "N/A (" + store.data.activity_text.split( '|', 3 )[1] + ")";
                    } else {
                        return "N/A";
                    }
                }
                return value;}
        },
        {
            text: 'Time', dataIndex: 'time', flex: 1, renderer: function (value) {
                try {
                    value = sfw.util.Util.dateCreate(value, true);
                    return  Ext.util.Format.date(value,'H:i jS M, Y');
                } catch (ex) {
                    return 'N/A';
                }
            }
        },
        { text: 'Activity Information', dataIndex: 'activity_text_friendly', flex: 3 },
        { text: 'Host', dataIndex: 'hostname', flex: 1 , renderer:function( value, tmp, store, index ) {
                var actType, actText;
                if ( ! value ) {
                    actType = store.data.activity_type;
                    if ( actType === 1 && store.data.activity_text ) {
                        return store.data.activity_text.split( '|', 3 )[0];
                    } else {
                        return "N/A";
                    }
                }
                return value;
            }
        },
        {text: 'Prirority', dataIndex: 'priority', flex: 1, renderer: 'gridRenderPriority'}
    ],

    listeners: {
        afterrender:'refresh',
        itemcontextmenu: function (grid, record, item, index, e) {
            const me = this;
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'activitylog',
                width: 225,
                plain: true,
                items: [{
                    text: 'Copy Row Data to Clipboard',
                    listeners: {
                        click: {fn: 'copyDataToClipboard', extra: grid, rowIndex:index}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        }
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{activitylog}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Activity Log Entries {0} - {1} of {2}',
        emptyMsg: "No Activity Log entries"
    }
});