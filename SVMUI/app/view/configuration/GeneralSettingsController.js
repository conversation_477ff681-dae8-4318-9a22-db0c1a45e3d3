Ext.define('sfw.view.configuration.GeneralSettingsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.configuration-generalsettings',

    getForm: function () {
        return this.getView().getForm();
    },

    onResetClick: function () {
        this.getForm().reset();
    },

    onFormSubmit: function () {
        var form = this.getForm(),
            pretty, values, i;

        if (form.isValid()) {
            values = form.getValues(true);

            pretty = Ext.Array.map(values.split('&'), function (val) {
                val = decodeURIComponent(val);
                i = val.indexOf('=');

                return '<b>' + val.substr(0, i) + ': </b>' +
                    Ext.htmlEncode(val.substr(i + 1));
            });

            pretty = pretty.join('<br>') +
                '<br><br><b>Raw values:</b>' +
                '<pre style="overflow:auto; margin-top:0;padding:8px;">' +
                Ext.htmlEncode(values) +
                '</pre>';

            Ext.MessageBox.alert('Submitted Values', pretty);
        }
    },
    activeteLiveUpdate: function (checkbox, checked) {
        var instantAccessCheck = checked ? 1 : 0;
        var params = {'enabled': instantAccessCheck};

        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=set_instant_access'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to change the Live Update Configuration.');
                    Ext.Msg.show({
                        title: "Error"
                        ,
                        msg: "Error while trying to save the configuration. " + data.msg
                        ,
                        buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = 'disabled';
                    if (instantAccessCheck) {
                        text = 'enabled';
                    }
                    // UI client side
                    sfw.util.Auth.LoginDetails.settings.has_instant_access = (text == 'enabled') ? 1 : 0;
                    Ext.toast({
                        html: 'Live Update ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                    /*Ext.Msg.show({
                        title: 'Configuration Saved',
                        msg: 'Live Update ' + text,
                        buttons: Ext.Msg.OK
                    });*/
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying to change the Live Update Configuration.');
            }
        });
    },
    collectionNetworkInfo: function (checkbox, checked) {
        var collectNetworkInfo = checked ? 1 : 0;
        var params = {'enabled': collectNetworkInfo};
        var LoginDetails = sfw.util.Auth.LoginDetails;
        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=set_collect_network_info'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to change the Collect Network Info Configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        ,
                        msg: "Error while trying to save the configuration. " + data.reason
                        ,
                        buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = 'disabled';
                    if (collectNetworkInfo) {
                        text = 'enabled';
                    }
                    // Update the client side copy
                    LoginDetails.settings.collect_network_info = collectNetworkInfo ? "1" : "0";
                    Ext.toast({
                        html: 'Collect Network Info ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                    /*Ext.Msg.show({
                        title: 'Configuration Saved'
                        , msg: 'Collect Network Info ' + text
                        , buttons: Ext.Msg.OK
                    });*/
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying to change the Collect Network Info Configuration.');
            }
        });

    },
    hidelZombiFiles: function (checkbox, checked) {
        var val = checked ? 1 : 0;
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var params = {
            value:val
        };

        if(val == 1){
            Ext.ComponentQuery.query('#save_zombie_files')[0].setDisabled(true);
            Ext.ComponentQuery.query('#save_zombie_files')[0].setValue(false);
        }else{
            Ext.ComponentQuery.query('#save_zombie_files')[0].setDisabled(false);
        }

        Ext.Ajax.request({
            url: 'action=zombie_files&which=configure'
            ,method:'POST'
            ,params: params
            , success: function (response) {
                var res = Ext.util.JSON.decode(response.responseText);
                if (res === 1) {
                    if (val === 1) {
                        LoginDetails.account.zombieSettings = {
                            account_id: LoginDetails.account.settings.account_id,
                            is_partition_admin: LoginDetails.account.isPartitionAdmin
                        };
                        LoginDetails.account.saveZombieSettings = false;
                    } else {
                        LoginDetails.account.zombieSettings = false;
                    }
                    var text = checked ? 'enabled' : 'disabled';
                    Ext.toast({
                        html: 'Hide Zombie Files ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                    /*Ext.Msg.show({
                        title: 'Configuration Saved'
                        , msg: 'Hide Zombie Files ' + text
                        , buttons: Ext.Msg.OK
                    });*/
                } else {
                    Ext.Msg.show({
                        title: 'Forbidden'
                        ,
                        msg: 'You do not have enough privileges to perform this action.'
                        ,
                        buttons: Ext.Msg.OK
                    });
                }
            }
            , failure: function (response) {
                Ext.Msg.alert('Error', 'An error occurred while trying to update the Hide Zombie Files setting.');
            }
        });
    },
    enableSPSTimestamp: function (combo,record,index){

        var params = {'value': record.get( 'times_id' )};

        Ext.Ajax.request({
            url: 'action=set_timestamp&which=set_timestamp'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to change the Timestamp Configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        , msg: "Error while trying to save the configuration. " + data.msg
                        , buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    sfw.util.Auth.LoginDetails.timestampSetting = record.get( 'times_id' );
                    Ext.toast({
                        html: 'Your preferred timestamp setting is saved.',
                        ui: 'success',
                        title: 'Timestamp Setting Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                    /*Ext.Msg.show({
                        title: 'Timestamp Setting Saved',
                        msg: 'Your preferred timestamp setting is saved.',
                        buttons: Ext.Msg.OK
                    });*/
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying to change Timestamp provider');
            }
        });
    },
    stopAgentPolling: function (checkbox, checked) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var pollingstatusapi = checked ? 1 : 0;
        var params = {'enabled': pollingstatusapi};

        Ext.Ajax.request({
            url: 'action=set_pollingstatusapi&which=set_pollingstatusapi'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying set the polling status API configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        ,
                        msg: "Error while trying to save the polling status API configuration. " + data.msg
                        ,
                        buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = "Agent polling started";
                    LoginDetails.pollingstatusapi = false;
                    if (pollingstatusapi) {
                        text = "Agent polling stopped";
                        LoginDetails.pollingstatusapi = true;
                    }
                    Ext.toast({
                        html: text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                    /*Ext.Msg.show({
                        title: 'Configuration Saved'
                        , msg: text
                        , buttons: Ext.Msg.OK
                    });*/
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying set the polling status API.');
            }
        });

    },

    stopSiteUpdate: function (checkbox, checked) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var checksiteupdate = checked ? 1 : 0;
        var params = {'enabled': checksiteupdate};

        Ext.Ajax.request({
            url: 'action=set_groupagentcheckinsettingsapi&which=set_groupagentcheckinsettingsapi'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying set the site save  API configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        ,
                        msg: "Error while trying to save the site save  API configuration. " + data.msg
                        ,
                        buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = "Site name update disabled";
                    LoginDetails.getCheckSiteChecking = false;
                    if (checksiteupdate) {
                        text = "Site name update enabled";
                        LoginDetails.getCheckSiteChecking = true;
                    }
                    Ext.toast({
                        html: text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying set the Site Save API.');
            }
        });

    },

    checkMSSecurityConfigUpdate: function (radio, checked, n) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var params = {'value': radio.inputValue};
        if (checked) {
            Ext.Ajax.request({
                url: 'action=check_ms_updates&which=set_check_ms_updates'
                , method: 'POST'
                , params : params
                , success: function (data) {
                    result = Ext.decode(data.responseText);
                    if (!result.success) {
                        sfw.util.Debug.log('Error while configuring missing Microsoft security updates');
                        Ext.Msg.show({
                            title: 'Error',
                            msg: 'Error while configuring missing Microsoft security updates.',
                            buttons: Ext.Msg.OK
                        });
                        return;
                    } else {
                        LoginDetails.checkMissingMsUpdates = radio.inputValue;
                        Ext.toast({
                            html: 'Configuration for missing Microsoft security updates saved.',
                            ui: 'success',
                            title: 'Configuration Saved',
                            width: 200,
                            shadow: true,
                            align: 'tr'
                        });
                        /*Ext.Msg.show({
                            title: 'Configuration Saved',
                            msg: 'Configuration for missing Microsoft security updates saved.',
                            buttons: Ext.Msg.OK
                        });*/
                    }
                }
                , failure: function () {
                    sfw.util.Debug.log('Error while configuring missing Microsoft security updates.');
                }

            });

        }
    },
    setWuaServerExt: function( setting ) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
	    if ( sfw.isDesktop ) {
		    sfw.external.fWUISetWuaServerTypeExt( setting );
	    }

	// Set server type server-side
        Ext.Ajax.request({
            url: "action=ajaxapi_change_wua_server_ext"
            , method: 'POST'
            , params: {
                'servertypeext': setting
            }
        });
	// Update client side copy
	LoginDetails.account.settings.wua_server_ext = setting;
    },
    setWuaServerInput: function (filePath) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        if (sfw.isDesktop) {
            sfw.external.fWUISetWuaServerInput(filePath);
        }

        Ext.Ajax.request({
            url: "action=ajaxapi_change_wua_server_input"
            , method: 'POST'
            , params: {
                'servertypeinput': filePath
            }
        });

        // Update client side copy
        LoginDetails.settings.wua_server_input = filePath;
    },

    setWuaOtherServer: function () {
        // Two more options are introduced that fall under WuaOtherServer
        // These are microsoftUpdateServer=1 and offlineServer=2( via CabFile )
        // For that purpose a new field in the database is introduced named wua_server_ext which have default value of zero
        // this will be valid only of existing field wua_server = 3 i.e. of type ssOther
        var ssOtherExt = 0;
        var ssServerInput = "";
        var serverSelection = {ssDefault: 0, ssManagedServer: 1, ssWindowsUpdate: 2, ssOthers: 3};
        var serverSelectionExt = {ssMicrosoftUpdate: 1, ssOfflineServer: 2};


        switch (windowsUpdateOptionSelected) {
            case 'ssMicrosoftUpdate':
                ssOtherExt = serverSelectionExt.ssMicrosoftUpdate;
                break;
            case 'ssOfflineServer':
                ssServerInput = Ext.ComponentQuery.query('#cab_file_path_windows')[0].getValue();
                ssOtherExt = serverSelectionExt.ssOfflineServer;
                break;
        }

        try {
            this.setWuaServer(serverSelection.ssOthers);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssOther", ex);
        }

        try {
            this.setWuaServerExt(ssOtherExt);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssOther", ex);
        }


        if (ssOtherExt == serverSelectionExt.ssOfflineServer) { //ssServerInput is valid only if wua server option is OfflineServer
            try {
                this.setWuaServerInput(ssServerInput);
            } catch (ex) {
                // LogException('Failed to set WUA server input to ' + ssServerInput, ex);
            }
        }
    },


    checkWuaOtherServer: function () {
        var serverType = sfw.util.Auth.LoginDetails.settings.wua_server;
        var serverSelection = {ssDefault: 0, ssManagedServer: 1, ssWindowsUpdate: 2, ssOthers: 3};
        if (serverSelection.ssOthers == serverType) {
            // We have the ssOther setting - see the MSDN ServerSelection enumeration
            sfw.util.Debug.log('WUA servertype : ssOther');
            return true;
        }
        return false;
    },


    useManagedwindowsUpdateServer: function (obj, checked) {


        var el = false;
        if (checked) {
            var itemId = obj.itemId;
            if (itemId === 'use_managed_windows_update') {
                el = "ssManagedServer";
                Ext.ComponentQuery.query('#cab_file_path_windows')[0].setDisabled(true);
            } else if (itemId === 'use_official_windows_update') {
                el = "ssWindowsUpdate";
                Ext.ComponentQuery.query('#cab_file_path_windows')[0].setDisabled(true);
            } else if (itemId === 'use_official_microsoft_update') {
                el = "ssMicrosoftUpdate";
                Ext.ComponentQuery.query('#cab_file_path_windows')[0].setDisabled(true);
            } else {
                el = "ssOfflineServer";
                Ext.ComponentQuery.query('#cab_file_path_windows')[0].setDisabled(!checked);
                Ext.ComponentQuery.query('#cab_file_path_windows')[0].validate();
            }
            windowsUpdateOptionSelected = el;

        }
    },

    cabFileHandler: function () {
        windowsUpdateOptionSelected = "ssOfflineServer";
    },
    getWuaServerInput: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        return sfw.util.Auth.LoginDetails.settings.wua_server_input;
    },
    setWuaServer: function (setting) {
        if (sfw.isDesktop) {
            sfw.external.fWUISetWuaServerType(setting);
        }

        var checkWMI = sfw.csiSettings.checkWMISelected;
        var LoginDetails = sfw.util.Auth.LoginDetails;

        // Set server type server-side
        Ext.Ajax.request({
            url: "action=ajaxapi_change_wua_server"
            , method: 'POST'
            , params: {
                'servertype': setting
                , 'checkwmi': checkWMI
            }
        });

        // Update client side copy
        LoginDetails.settings.wua_server = setting;
        LoginDetails.WMISetting = checkWMI;
    },
    setOnlyWMI: function () {
        try {
            this.setWuaServer(0);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssDefault", ex);
        }
    },
    setWuaDefaultServer: function () {
        var serverSelection = { ssDefault:0, ssManagedServer:1, ssWindowsUpdate:2, ssOthers:3 };
        try {
            this.setWuaServer(serverSelection.ssDefault);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssDefault", ex);
        }
    },

    /**
     * Make the WUA use a managed server (ssManagedServer) for Microsoft updates.
     */
    setWuaManagedServer: function () {
        var serverSelection = { ssDefault:0, ssManagedServer:1, ssWindowsUpdate:2, ssOthers:3 };
        try {
            this.setWuaServer(serverSelection.ssManagedServer);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssManagedServer", ex);
        }
    },

    /**
     * Make the WUA use the windows update server (ssWindowsUpdate) for Microsoft updates.
     */
    setWuaWindowsServer: function () {
        var serverSelection = { ssDefault:0, ssManagedServer:1, ssWindowsUpdate:2, ssOthers:3 };
        try {
            this.setWuaServer(serverSelection.ssWindowsUpdate);
        } catch (ex) {
            // LogException("Failed to set WUA server type to ssWindowsUpdate", ex);
        }
    },

    wuaClearHandler: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        Ext.ComponentQuery.query('#use_managed_windows_update')[0].setValue(false);
        Ext.ComponentQuery.query('#use_official_windows_update')[0].setValue(false);
        Ext.ComponentQuery.query('#use_official_microsoft_update')[0].setValue(false);
        Ext.ComponentQuery.query('#use_official_microsoft_update')[0].setValue(false);

        LoginDetails.settings.wua_server = 0;
        windowsUpdateOptionSelected = '';

    },
    saveWindowsUpdateSettingsHandler: function () {
        var option = "";
        sfw.csiSettings.checkWMISelected = '0';
        if (Ext.ComponentQuery.query('#checkWMI')[0].checked) {
            sfw.csiSettings.checkWMISelected = '1';
        }
        if ("undefined" !== typeof windowsUpdateOptionSelected && windowsUpdateOptionSelected) {
            option = windowsUpdateOptionSelected;

        } else {
            if (Ext.ComponentQuery.query('#use_managed_windows_update')[0].checked) {
                option = "ssManagedServer";
            } else if (Ext.ComponentQuery.query('#use_official_windows_update')[0].checked) {
                option = "ssWindowsUpdate";
            } else if (Ext.ComponentQuery.query('#use_official_microsoft_update')[0].checked) {
                option = "ssMicrosoftUpdate";
            } else if (Ext.ComponentQuery.query('#use_official_microsoft_update')[0].checked) {
                option = "ssOfflineServer";
            } else {
                this.setOnlyWMI();
            }
        }
        switch (option) {
            case "ssDefault":
                this.setWuaDefaultServer();
                break;
            case "ssManagedServer":
                this.setWuaManagedServer();
                break;
            case "ssWindowsUpdate":
                this.setWuaWindowsServer();
                break;
            case "ssMicrosoftUpdate":
                this.setWuaOtherServer();
                break;
            case "ssOfflineServer":
                this.setWuaOtherServer();
                break;

        }
    },
    doNotUseProxyhandler: function (button, checked) {
         var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3}
        if (checked) {
            sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxyNone;
            Ext.ComponentQuery.query('#wua_proxy_hostport')[0].setDisabled(true);
        }
    },
    proxySameHandler: function (button, checked) {
        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3}
        if (checked) {
            sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxySame;
            Ext.ComponentQuery.query('#wua_proxy_hostport')[0].setDisabled(true);
        }
    },
    proxyCSIServerhandler: function (button, checked) {
        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3}
        if (checked) {
            sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxyCSIServer;
            Ext.ComponentQuery.query('#wua_proxy_hostport')[0].setDisabled(true);
        }
    },
    customeProxyhandler: function (button, checked) {
        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3};
        if ( checked ) {
								sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxyCustom;
							}
             Ext.ComponentQuery.query('#wua_proxy_hostport')[0].setDisabled(!checked);
        Ext.ComponentQuery.query('#wua_proxy_hostport')[0].validate();


    },
    proxyHotspotHandler: function () {
        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3};
        sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxyCustom;
    },
    saveWuaProxySetting: function (setting) {
        sfw.util.Default.set('wua_proxy_setting', parseInt(setting));
        var hostport = Ext.ComponentQuery.query('#wua_proxy_hostport')[0].getValue();
        sfw.util.Default.set('wua_proxy_hostport', hostport);
    },
    saveWUAgentSettingshandler: function () {
        var option = sfw.csiSettings.wuaProxyOptionSelected;
        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3};
        if (!option) {
            if (Ext.ComponentQuery.query('#_wua_proxy_no_proxy_radio')[0].checked) {
                option = wuaProxySetting.wuaProxyNone;
            } else if (Ext.ComponentQuery.query('#_wua_proxy_same_proxy_radio')[0].checked) {
                option = wuaProxySetting.wuaProxySame;
            } else if (Ext.ComponentQuery.query('#_wua_proxy_csi_server_proxy_radio')[0].checked) {
                option = wuaProxySetting.wuaProxyCSIServer;
            } else if (Ext.ComponentQuery.query('#_wua_proxy_custom_radio')[0].checked) {
                option = wuaProxySetting.wuaProxyCustom;
            }
        }
        switch (option) {
            case wuaProxySetting.wuaProxyNone:
            case wuaProxySetting.wuaProxySame:
            case wuaProxySetting.wuaProxyCSIServer:
            case wuaProxySetting.wuaProxyCustom:
                this.saveWuaProxySetting(option);
                break;
        }
    },
    generateKey: function () {
        const me = this,
            view = me.getView(),
            account_key = view.down('#account_key');
        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=generate_sso_key'
            , method: 'POST'
            , success: function (data) {
                result = Ext.decode(data.responseText);
                if (!result.success) {
                    sfw.util.Debug.log('Error while generating account key');
                    return;
                } else {
                    account_key.setValue(result.account_key)
                }
            }
            , failure: function () {
                sfw.util.Debug.log('Error while generating account key.');
            }
        });
    },
    setXML: function (fb) {
        fb.fileInputEl.set({
            accept: 'application/xml'
        });
    },
    fileselected: function (fb, v, n) {
                                                var xml_file_text_field = Ext.ComponentQuery.query('#xml_file_text_field')[0];
                                                var idpMetadataUrlVal = Ext.ComponentQuery.query('#idpMetadataUrlVal')[0];
                                                xml_file_text_field.setText(fb.fileInputEl.dom.files['0']['name']);
                                                var reader = new FileReader();
                                                self.importedPathData = [];
                                                self.xmlContent = '';
                                                reader.onload = function () {
                                                    var content = reader.result;
                                                    self.xmlContent = content;
                                                };
                                                idpMetadataUrlVal.setValue();
                                                reader.readAsText(fb.fileInputEl.dom.files['0']);
                                            },
    getSsoServiceConfig: function () {
        var ssoSetting = sfw.util.Auth.LoginDetails.account.ssoSettings;
        var me = this;
        Ext.ComponentQuery.query('#is_sso_enabled')[0].suspendEvents(false);
        Ext.ComponentQuery.query('#is_sso_enabled')[0].setValue(true);
        Ext.ComponentQuery.query('#is_sso_enabled')[0].resumeEvents();

        if (ssoSetting != null && ssoSetting != '') {

           Ext.ComponentQuery.query('#is_standard_login_disabled')[0].setValue(ssoSetting.disable_standard_login);
            if (ssoSetting.is_sso == 1) {
                Ext.ComponentQuery.query('#sso_content')[0].show();
            } else {
                Ext.ComponentQuery.query('#sso_content')[0].hide();
            }


            if (ssoSetting.template_account_id != 0) {
                Ext.ComponentQuery.query('#automatically_create_new_user')[0].setValue(true);
                Ext.ComponentQuery.query('#templateAccountId')[0].setValue(ssoSetting.template_account_id);
            } else {
                Ext.ComponentQuery.query('#automatically_create_new_user')[0].setValue(false);
            }
        }

        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=get_sso_meta_data'
            , success: function (data) {
                result = Ext.decode(data.responseText);
                if (!result.success) {
                    Ext.Msg.alert('Error', 'Error while fetching meta data');
                    sfw.util.Debug.log('Error while saving service provider configuration');
                    return;
                } else {
                    var metaData = result.metaData;
                    me.processMetaData(metaData);
                }
            }

        });


    },
    setSsoConfiguration: function (checkbox, checked) {
        var LoginDetails = sfw.util.Auth.LoginDetails;

        var is_sso_enabled = checked ? 1 : 0;

        if (is_sso_enabled == true || LoginDetails.account.ssoSettings.is_sso == null) {
            return false;
        }
        var params = {'enabled': is_sso_enabled};

        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=set_sso_configuration'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to change the Sso Configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        , msg: "Error while trying to save the configuration. " + data.msg
                        , buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    if (is_sso_enabled == false && data.updated == true) {
                        var text = 'Disabled';
                        Ext.Msg.show({
                            title: 'Configuration Saved'
                            , msg: 'SSO ' + text
                            , buttons: Ext.Msg.OK
                        });
                        LoginDetails.account.ssoSettings.is_sso = is_sso_enabled;
                    }
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying to change the Sso Configuration.');
            }
        });

    },
    ssoEnabledCheck: function () {
        if(LoginDetails.account.ssoSettings.is_sso == 1) {
            this.getSsoServiceConfig();
        }
    },

    ssoEnabledHandler: function (checkbox, checked) {
        this.setSsoConfiguration(checkbox, checked);
        if (checked == true) {
            this.getSsoServiceConfig();
            Ext.ComponentQuery.query('#sso_content')[0].show();
        } else {
            Ext.ComponentQuery.query('#sso_content')[0].hide();
        }

    },
    showIDPXMLMetadata: function (component) {
        component.getEl().on('click', function (e) {

            Ext.Ajax.request({
                url: 'action=ajaxapi_submit_settings&which=get_sso_meta_data'
                , method: 'GET'
                , async: false
                , success: function (data) {
                    result = Ext.decode(data.responseText);
                    if (!result.success) {
                        Ext.Msg.alert('Error', 'Error while fetching meta data');
                        sfw.util.Debug.log('Error while saving service provider configuration');
                        return;
                    } else {
                        var metaData = result.metaData;
                        if (metaData.meta_xml == '') {
                            this.idpXmlContent = '<p></p>';
                        } else {
                            this.idpXmlContent = metaData.meta_xml;
                        }
                        this.xmlWindowContent = new Ext.FormPanel({
                            labelWidth: 75
                            , frame: true
                            , bodyStyle: 'padding:5px 5px 0'
                            , defaults: {width: 200}
                            , defaultType: 'textfield'
                            , html: this.idpXmlContent


                        });

                        this.xmlWindow = new Ext.Window({
                            title: 'IdP Metadata File Content'
                            , modal: true
                            , constrain: true
                            , height: 450
                            , width: 500
                            , layout: 'auto'
                            , items: this.xmlWindowContent
                            , closeAction: 'hide'
                            , autoScroll: true
                            , id: 'xmlFileContent'


                        });
                        this.xmlWindow.show();
                    }
                }


            });

        });
    },
    idpMetadataURLHandler: function (radio, checked) {
        if (checked){
        if (radio.inputValue === 1) {
            Ext.ComponentQuery.query('#idpMetadataXmlVal')[0].show();
            Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].hide();

            //Ext.getCmp('idpMetadataUrlVal').setValue();
        } else {
            Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].show();
            Ext.ComponentQuery.query('#idpMetadataXmlVal')[0].hide();
            Ext.ComponentQuery.query('#xml_file_text_field')[0].setText("No file chosen");
            //var fileObj = Ext.getCmp('import_button');
            //fileObj.fileInput.dom.value = '';

            //Ext.getCmp('showPreviousXmlFileData').hide();
        }}
    },
    createIDPUser: function (checkbox, checked) {
        const me = this,
            view = me.getView(),
            templateAccountId = view.down('#templateAccountId');
        var LoginDetails = sfw.util.Auth.LoginDetails;
        if (checked == true) {
            Ext.ComponentQuery.query('#selectedUserList')[0].show();
            if (sfw.util.Auth.LoginDetails.account.ssoSettings.template_account_id === 0) {
                Ext.ComponentQuery.query('#templateAccountId')[0].store.load();
                Ext.ComponentQuery.query('#templateAccountId')[0].reset();
            }
        } else {
            Ext.ComponentQuery.query('#selectedUserList')[0].hide();
            Ext.ComponentQuery.query('#templateAccountId')[0].reset();
        }
    },
    processMetaData: function (metaData) {

        if (metaData.meta_url) {
            Ext.ComponentQuery.query('#idp_meta_data_url')[0].setValue(true);
            Ext.ComponentQuery.query('#idp_meta_data_xml')[0].setValue(false);
            var url = metaData.meta_url;
            Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].setValue(url);
        } else {
            Ext.ComponentQuery.query('#idp_meta_data_url')[0].setValue(false);
            Ext.ComponentQuery.query('#idp_meta_data_xml')[0].setValue(true);
            Ext.ComponentQuery.query('#idpMetadataXmlVal')[0].show();
            Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].hide();
        }

        var is_sso_enabled = Ext.ComponentQuery.query('#is_sso_enabled')[0].getValue();
        if (sfw.util.Auth.LoginDetails.account.ssoSettings.is_sso === 1 || is_sso_enabled === true) {
            var ssoMetaUrl = metaData.meta_url;
            var ssoMetaXrl = metaData.meta_xml;
            if (ssoMetaUrl == '') {
                Ext.ComponentQuery.query('#showPreviousXmlFileData')[0].show();
            }
        }

    },
    saveServiceProviderConfiguration: function (sv) {
        var idpMetadataUrlVal = Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].getValue();
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var me = this;
        if (Ext.ComponentQuery.query('#idp_meta_data_url')[0].getValue() == true) {
            if ((Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].getValue()) == null || (Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].getValue()) == '') {
                Ext.Msg.alert('Warning', 'Provide IdP meta data url.');
                return;
            }
        }

        if (Ext.ComponentQuery.query('#idp_meta_data_xml')[0].getValue() == true) {
            var fileObj = Ext.ComponentQuery.query('#import_button')[0];
            if (fileObj.fileInputEl.dom.files['0'] == null) {
                Ext.Msg.alert('Warning', 'Provide IdP meta data xml file.');
                return;
            }
        }

        if (Ext.ComponentQuery.query('#automatically_create_new_user')[0].checked == true) {
            if (Ext.ComponentQuery.query('#templateAccountId')[0].getValue() == null || Ext.ComponentQuery.query('#templateAccountId')[0].getValue() == 0) {
                Ext.Msg.alert('Warning', 'Select a template user to processed.');
                return;
            }
        }

        var isSsoEnabled = Ext.ComponentQuery.query('#is_sso_enabled')[0].checked;
        var idpMetaDataUrlVal = '';

        var templateAccountId = 0;

        if (isSsoEnabled != null || isSsoEnabled == true) {
            var isStandardLoginDisabled = Ext.ComponentQuery.query('#is_standard_login_disabled')[0].checked;
            var idpMetaDataUrl = Ext.ComponentQuery.query('#idp_meta_data_url')[0].getValue();
            if (idpMetaDataUrl != null || idpMetaDataUrl == true) {
                idpMetaDataUrlVal = Ext.ComponentQuery.query('#idpMetadataUrlVal')[0].getValue();
            }
            var xmlContent = self.xmlContent;
            var automaticallyNewUser = Ext.ComponentQuery.query('#automatically_create_new_user')[0].checked;

            if (automaticallyNewUser == true) {
                templateAccountId = Ext.ComponentQuery.query('#templateAccountId')[0].getValue()
            }

            if (Ext.ComponentQuery.query('#idpMetaDataUrl')[0] == true) {
                xmlContent = null
            }

            if (xmlContent !== null) {
                idpMetaDataUrlVal = '';
            }

            if (Ext.ComponentQuery.query('#idp_meta_data_xml')[0].getValue() == true && sfw.util.Auth.LoginDetails.account.ssoSettings.is_sso == 1) {
                var fileObj = Ext.ComponentQuery.query('#import_button')[0];
                if (fileObj.fileInputEl.dom.files['0'] == null) {
                    var fileUploaded = true;
                }
            }

            Ext.Ajax.request({
                url: 'action=ajaxapi_submit_settings&which=save_service_provider_configuration'
                , method: 'POST'
                , params: {
                    'isSsoEnabled': isSsoEnabled
                    , 'isStandardLoginDisabled': isStandardLoginDisabled
                    , 'idpMetaDataUrlVal': idpMetadataUrlVal
                    , 'templateAccountId': templateAccountId
                    , 'xmlContent': xmlContent
                    , 'fileUploaded': fileUploaded
                }
                , success: function (data) {
                    result = Ext.decode(data.responseText);
                    if (!result.success) {
                        Ext.Msg.alert('Error', 'Error while saving service provider configuration');
                        sfw.util.Debug.log('Error while saving service provider configuration');
                        if (result.error_code == 'ex' || result.error_code == sfw.csiSettings.ERROR_META_XML) {
                            Ext.Msg.alert('Error', 'Provide valid xml file');
                        } else if (result.error_code == sfw.csiSettings.ERROR_META_URL) {
                            Ext.Msg.alert('Error', 'Provide valid metadata URL');
                        }
                        return;
                    } else {
                        Ext.Msg.alert("Success", "SSO enabled and service provider configuration saved successfully");
                        LoginDetails.account.ssoSettings = {};
                        LoginDetails.account.ssoSettings.is_sso = 1;
                        LoginDetails.account.ssoSettings.template_account_id = templateAccountId;
                        LoginDetails.account.ssoSettings.disable_standard_login = isStandardLoginDisabled;
                        me.processMetaData(result.meta_data);
                        if (Ext.ComponentQuery.query('#idp_meta_data_url')[0].getValue() == true) {
                            Ext.ComponentQuery.query('#showPreviousXmlFileData')[0].hide();
                            var fileObj = Ext.ComponentQuery.query('#import_button')[0];
                            fileObj.fileInputEl.dom.value = '';
                        } else {
                            Ext.ComponentQuery.query('#showPreviousXmlFileData')[0].show();
                        }

                    }
                }
                , failure: function () {
                    sfw.util.Debug.log('Error while saving service provider configuration.');
                }
            });

        }
    },
    setPathDetectionStatus: function (checkbox, checked) {

        var pathdetectionstatus = checked ? 1 : 0;
        var params = {'enabled': pathdetectionstatus};
        Ext.Ajax.request({
            url: 'action=set_pathdetectionstatus&which=set_pathdetectionstatus'
            , method: 'POST'
            , params: params
            , success: function (data) {
                data = Ext.decode(data.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying set the masking configuration: ' + data.msg);
                    Ext.Msg.show({
                        title: "Error"
                        , msg: "Error while trying to save the masking configuration. " + data.msg
                        , buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = 'disabled';
                    if (pathdetectionstatus) {
                        text = 'enabled';
                    }
                    // UI client side
                    sfw.util.Auth.LoginDetails.pathdetectionstatus = (text == 'enabled') ? 1 : 0 ;
                    if (sfw.isDesktop) {
                        if (sfw.csiSettings.pathdetectionstatus == 1) {
                            LoginDetails.pathdetectionstatus = true;
                            sfw.external.fWUISetMaskUserPath(true);
                        } else {
                            LoginDetails.pathdetectionstatus = false;
                            sfw.external.fWUISetMaskUserPath(false);
                        }
                    }
                    Ext.toast({
                        html: 'Masking ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });

                    /*Ext.Msg.show({
                        title: 'Configuration Saved'
                        , msg: 'Masking ' + text
                        , buttons: Ext.Msg.OK
                    });*/
                }
            }
            , failure: function (data) {
                sfw.util.Debug.log('Error while trying set the masking Configuration.');
            }
        });

    },
    ssoRadioRenderer: function (fc) {
        fc.add({
                boxLabel: 'Provide IdP Metadata URL',
                name: 'idpMetadata',
                itemId: 'idp_meta_data_url',
                inputValue: 2,
                checked: true
            },
            {
                boxLabel: 'Upload IdP Metadata XML file',
                name: 'idpMetadata',
                itemId: 'idp_meta_data_xml',
                inputValue: 1
            })


    },
    liveUpdateRenderer: function (fc) {
        var check = false;
        if (sfw.util.Auth.LoginDetails.settings.has_instant_access == 1) {
            check = true
        }
        fc.add({
            xtype: 'checkboxfield',
            name: 'active_live_update',
            inputValue: 1,
            checked: check,
            itemId: 'active_live_update',
            boxLabel: 'Activate Live Update',
            handler: 'activeteLiveUpdate'


        })


    },
    collectNetworkInfoRenderer: function (fc) {
        var check = false;
        if (sfw.util.Auth.LoginDetails.settings.collect_network_info == 1) {
            check = true
        }
        fc.add({
            xtype: 'checkbox',
            name: 'allow_collection_of_network_information',
            //inputValue: 1,
            checked: check,
            boxLabel: 'Allow Collection of Network Information',
            handler: 'collectionNetworkInfo',
        })


    },
    zombiFileSettingsRenderer: function (fc) {
        var check = false,
            zombieSettings = sfw.util.Auth.LoginDetails.account.zombieSettings;
        if (typeof zombieSettings === 'object') {
            check = true;
        }
        fc.add({
            xtype: 'checkbox',
            name: 'hide_zombiefiles',
            itemId: 'ignore_zombie_files',
            margin: "0 10 10 10",
            disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
            boxLabel: 'Hide Zombie Files',
            checked: check,
            handler: 'hidelZombiFiles'
        });

        var saveZombieCheck = false,
            saveZombieSettings = sfw.util.Auth.LoginDetails.account.saveZombieSettings;
        if (typeof saveZombieSettings === 'object') {
            saveZombieCheck = true;
        }

        fc.add({
            xtype: 'checkbox',
            name: 'save_zombiefiles',
            itemId: 'save_zombie_files',
            disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin || check),
            boxLabel: 'Display Zombie Files',
            checked: saveZombieCheck,
            handler: 'saveZombieFiles'
        });
    },

    hostDeletion: function (fc) {
        var check = false;
        var hostDeletionSettings = sfw.util.Auth.LoginDetails.account.hostDeletionSettings;
        var valueNumber = 90;

        if (typeof hostDeletionSettings === 'object') {
            check = Boolean(Number(hostDeletionSettings.option_is_enabled));
            valueNumber = hostDeletionSettings.option_value;
        }
        fc.add({
            xtype: 'form',
            items:[{
            xtype: 'fieldcontainer',
            layout: 'hbox',
            items:[{
                xtype: 'checkbox',
                name: 'host_deletion',
                itemId: 'host_deletion',
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                boxLabel: 'Delete hosts after',
                checked: check,
                handler: function (checkbox, checked) {
                    var host_max_days = Ext.ComponentQuery.query('#host_max_days')[0];
                    if (checked) {
                        host_max_days.setDisabled(false);
                        host_max_days.reset();
                    } else {
                        host_max_days.setDisabled(true);
                        host_max_days.setValue('');
                        host_max_days.clearInvalid();
                    }
                }
                },{
                    xtype: 'customnumberfield',
                    name: 'host_max_days',
                    itemId: 'host_max_days',
		    maxNumber: '365',
                    minNumber: '1',
                    valueNumber: valueNumber,
                    listeners:{
                        change:function(data,val){
                            var checked = Ext.ComponentQuery.query('#host_deletion')[0].getValue();
                            var save =  Ext.ComponentQuery.query('#save_host_deletion')[0];
                            if(typeof save !== 'undefined'){
                                if((checked && val == null) || (checked && (val < 1  || val > 365))){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },afterrender:function(){
                            if (typeof hostDeletionSettings === 'object') {
                                if(hostDeletionSettings.option_is_enabled && hostDeletionSettings.option_value > 0){
                                    this.setDisabled(false);
                                }
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: 'days.',
                     disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
            }]
          },{
                xtype:'button',
                text:'Save',
                ui: 'primary',
                itemId:'save_host_deletion',
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                handler: 'EnableHostDeletion',
            }]
        });
    },
    spsTimestampRenderer: function (fc) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var msSecurityUpdateMsg = '';
        if (!LoginDetails.isPartitionAdmin) {
            msSecurityUpdateMsg = ' This setting can only be set by the partition administrator. ';
        }
        fc.add({
            xtype: 'label',
            labelWidth: 120,
            html: ('<p>This setting determines your preferred Timestamp provider. If you don\'t need any timestamp provider then select No timestamp required. ' + msSecurityUpdateMsg + '<a href="#" onclick="sfw.help.openPageId(\'csisettings_timestamp\');">(?)</a></p>')
        })


    },
       timestampComboRenderer: function (fc) {
        var check = false;
        if (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin) {
            check = true
        }
        fc.add({xtype: 'combobox'
            , value: 0
            , itemId: 'timestamp_select'
            , width: 300
            , store: new Ext.data.ArrayStore({
                idIndex: 0
                , fields: ['times_id', 'times_name']
                , data: [[0, 'No timestamp required'], [1101, 'DigiCert sha256']]
            })
            , valueField: 'times_id'
            , displayField: 'times_name'
            , disabled: check
            , typeAhead: false // Disable, due to bug with its container (or?)
            , editable: false // Disabled due to autocomplete bug
            , listeners: {select: 'enableSPSTimestamp'}


        });
        Ext.ComponentQuery.query('#timestamp_select')[0].setValue(sfw.util.Auth.LoginDetails.timestampSetting);


    },
    agentPollingRenderer: function (fc) {
        var check = false;
        if (sfw.util.Auth.LoginDetails.pollingstatusapi == 1) {
            check = true
        }
        fc.add({
            xtype: 'checkbox',
            name: 'stop_agent_polling',
            inputValue: 1,
            disabled: sfw.util.Auth.LoginDetails.isReadOnly,
            boxLabel: 'Stop agent polling',
            checked: check,
            handler: 'stopAgentPolling'
        })


    },
    siteStatusRenderer: function (fc) {
        var check = false;
        if (sfw.util.Auth.LoginDetails.getCheckSiteChecking == true) {
            check = true
        }
        fc.add({
            xtype: 'checkbox',
            name: 'site_status',
            inputValue: 1,
            disabled: sfw.util.Auth.LoginDetails.isReadOnly,
            boxLabel: 'Stop Site update',
            checked: check,
            handler: 'stopSiteUpdate'
        })


    },
    javaAssessmentRenderer: function (rf) {
        var check_java_standard = false;
        var check_java_all = false;
        var LoginDetails = sfw.util.Auth.LoginDetails;
        //LoginDetails.checkJavaScanSettings = "0";
        if (LoginDetails.checkJavaScanSettings == false) {
            check_java_standard = true
        }
        if (LoginDetails.checkJavaScanSettings == true) {
            check_java_all = true
        }
        rf.add({
                name: "java_settings_radio",
                inputValue: "0",
                boxLabel:
                    "Detect Java only in standard installation directories where it can be patched",
                checked: check_java_standard,
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                handler: 'changeJavaAssessmentSettings'
            },
            {
                name: "java_settings_radio",
                boxLabel:
                    "Detect Java in any directories where it is found, including those that cannot be directly patched",
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                inputValue: "1",
                checked: check_java_all,
                handler: 'changeJavaAssessmentSettings'
            })
    },
    changeJavaAssessmentSettings: function(radio, checked){
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var params = {'value': radio.inputValue};
        if (checked) {
            var msg = (radio.inputValue==1) ? 'Java assessment for all installation directories enabled.' : 'Java assessment for standard installation directories enabled.' ;
            Ext.Ajax.request({
                url: 'action=set_javascansettingsapi&which=set_javascansettingsapi'
                , method: 'POST'
                , params: params
                , success: function (data) {
                    result = Ext.decode(data.responseText);
                    if (!result.success) {
                        Ext.Msg.show({
                            title: 'Error',
                            msg: 'Error while configuring java settings.',
                            buttons: Ext.Msg.OK
                        });
                        return;
                    } else {
                        LoginDetails.checkJavaScanSettings = radio.inputValue==1 ? true : false ;
                        Ext.toast({
                            html: msg,
                            ui: 'success',
                            title: 'Configuration Saved',
                            width: 200,
                            shadow: true,
                            align: 'tr'
                        });
                        /*Ext.Msg.show({
                            title: 'Configuration Saved',
                            msg: msg,
                            buttons: Ext.Msg.OK
                        });*/
                    }
                }

            });

        }
    },

    maskPathRenderer: function (fc) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var check = false;
        if (LoginDetails.pathdetectionstatus == 1) {
            check = true
        }
        fc.add({
            xtype: 'checkbox'
            , id: 'set_identifiable_paths'
            , boxLabel: 'Enable Masking',
            checked: check
            , disabled: LoginDetails.isReadOnly
            , handler: 'setPathDetectionStatus'
        })


    },
    setNumberOfScanThreads: function (combo, record, index) {
        var numberOfScanThreads = parseInt(Ext.getCmp('number_of_scan_threads').getValue(), 10);

        // The Plugin must not be sent an invalid value (CSI-6278)
        if (isNaN(numberOfScanThreads)) {
            sfw.util.Debug.log("Invalid Scan Thread Count or Scan Thread Count not specified.");
        } else {
            globals.numberOfScanThreads = numberOfScanThreads;
        }

        try {
            sfw.external.WUIMaxThreads = globals.numberOfScanThreads;
            sfw.Default.options.set('number_of_scan_threads', globals.numberOfScanThreads);
            //If the Scan Progress page is rendered then it must be updated with the new number of threads
            //Cannot wait to do this when showing the page because the threads start signaling immediatly
            if (sfw.csiScanProgress.interface.rendered) {
                sfw.csiScanProgress.resetProgressBars();
            }
        } catch (ex) {
            // LogException("Failed to set number of scan threads: ", ex);
        }
    },

    enableMSSecurityRenderer: function (rf) {
        var check_en = false;
        var check_dis = false;
        var check_ind = false;
        var CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_ENABLED = 1084;
        var CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_DISABLED = 1085;
        var CHECK_FOR_MISSING_MS_UPDATES_CHECK_INDIVIDUALLY = 1088;
        var LoginDetails = sfw.util.Auth.LoginDetails;
        if (parseInt(LoginDetails.checkMissingMsUpdates) === CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_ENABLED) {
            check_en = true
        }
        if (parseInt(LoginDetails.checkMissingMsUpdates) === CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_DISABLED) {
            check_dis = true
        }
        if (parseInt(LoginDetails.checkMissingMsUpdates) === CHECK_FOR_MISSING_MS_UPDATES_CHECK_INDIVIDUALLY) {
            check_ind = true
        }
        rf.add({
                name: "enable_check_ms_security_updates",
                inputValue: CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_ENABLED,
                boxLabel:
                    "Enable check for Microsoft Security Updates",
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                checked: check_en,
                handler: 'checkMSSecurityConfigUpdate'
            },
            {
                name: "enable_check_ms_security_updates",
                boxLabel:
                    "Disable check for Microsoft Security Updates",
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                inputValue: CHECK_FOR_MISSING_MS_UPDATES_GLOBALLY_DISABLED,
                checked: check_dis,
                handler: 'checkMSSecurityConfigUpdate'
            },
            {
                name: "enable_check_ms_security_updates",
                boxLabel:
                    "Use Individual Configuration",
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                inputValue: CHECK_FOR_MISSING_MS_UPDATES_CHECK_INDIVIDUALLY,
                checked: check_ind,
                handler: 'checkMSSecurityConfigUpdate'
            })


    },
    windowsUpdateRenderer: function (rf) {
        var check_m = false;
        var check_off = false;
        var check_mu = false;
        var check_o = false;
        var serverType = sfw.util.Auth.LoginDetails.settings.wua_server;
        var extServerType = sfw.util.Auth.LoginDetails.settings.wua_server_ext;
        var serverSelection = {ssDefault: 0, ssManagedServer: 1, ssWindowsUpdate: 2, ssOthers: 3};
        var serverSelectionExt = {ssMicrosoftUpdate: 1, ssOfflineServer: 2};

        if (serverSelection.ssManagedServer === parseInt(serverType)) {
            // We have the ssManagedServer setting - see the MSDN ServerSelection enumeration
            sfw.util.Debug.log('WUA servertype : ssManagedServer');
            check_m = true
        }
        if (serverSelection.ssWindowsUpdate === parseInt(serverType)) {
            // We have the ssWindowsUpdate setting - see the MSDN ServerSelection enumeration
            sfw.util.Debug.log('WUA servertype : ssWindowsUpdate');
            check_off = true
        }
        if (serverSelection.ssOthers === parseInt(serverType) && serverSelectionExt.ssMicrosoftUpdate === parseInt(extServerType)) {
            // We have the ssOther setting -> with Microsoft Update Server
            sfw.util.Debug.log('WUA servertype : Microsoft Update Server');
            check_mu = true
        }
        if (serverSelection.ssOthers === parseInt(serverType) && serverSelectionExt.ssOfflineServer === parseInt(extServerType)) {
            // We have the ssOther setting -> with offline cab file
            sfw.util.Debug.log('WUA servertype : Offline cab file');
            check_o = true
        }
        rf.add({
                name: "use_a_managed_windows_update_server",
                itemId: 'use_managed_windows_update',
                boxLabel:
                    "Use a managed Windows Update server",
                checked: check_m,
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                handler: 'useManagedwindowsUpdateServer'
            },
            {
                name: "use_a_managed_windows_update_server",
                itemId: 'use_official_windows_update',
                boxLabel:
                    "Use the official Windows Update server",
                checked: check_off,
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                handler: 'useManagedwindowsUpdateServer'
            },
            {
                name: "use_a_managed_windows_update_server",
                itemId: 'use_official_microsoft_update',
                boxLabel:
                    "Use the official Microsoft Update server",
                checked: check_mu,
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                handler: 'useManagedwindowsUpdateServer'
            },
            {
                name: "use_a_managed_windows_update_server",
                itemId: 'use_offline_update',
                boxLabel:
                    "Use offline method: path to .CAB file",
                checked: check_o,
                disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                handler: 'useManagedwindowsUpdateServer'
            },
        )


    },
    cabFileWindowsRenderer: function (fc) {
        var check_o = false;
        var disabled = true;

        var serverType = sfw.util.Auth.LoginDetails.account.settings.wua_server;
        var extServerType = sfw.util.Auth.LoginDetails.account.settings.wua_server_ext;
        var serverSelection = {ssDefault: 0, ssManagedServer: 1, ssWindowsUpdate: 2, ssOthers: 3};
        var serverSelectionExt = {ssMicrosoftUpdate: 1, ssOfflineServer: 2};
        if (serverSelection.ssOthers === parseInt(serverType) && serverSelectionExt.ssOfflineServer === parseInt(extServerType)) {
            // We have the ssOther setting -> with offline cab file
            check_o = true;
        }
        if (!sfw.util.Auth.LoginDetails.isReadOnly && check_o){
            disabled = false
        }

        fc.add({

            xtype: 'textfield'
            ,
            allowBlank: false
            ,
            itemId: 'cab_file_path_windows'
            ,
            name: 'cab_file_path_win_textfield'
            // ,
            // vtype: 'FilePath'
            ,
            emptyText: 'Enter the path of cab file ... '
            ,
            regex: /^([a-zA-Z]\:|\\\\[^\/\\:*?"<>|]+\\[^\/\\:*?"<>|]+)(\\[^\/\\:*?"<>|]+)+(\.[^\/\\:*?"<>|]+)*.cab$/,
            value: sfw.util.Auth.LoginDetails.settings.wua_server_input,
            disabled: disabled,
            width: 285

        })


    },
    ssoURL: function (tb) {
        tb.add(
            {
                xtype: 'textfield'
                , value: sfw.util.Auth.LoginDetails.caDomain + '/sso/saml/' + sfw.util.Auth.LoginDetails.ssoGuid
                , readOnly: true
                , width: 500
                , hideLabel: true,
            }
        )

    },
    ssoMetadataURL: function (tb) {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var ssoMetaUrl = LoginDetails.caDomain + '/sso/metadata/' + LoginDetails.ssoGuid;
        tb.add(
            {
                xtype: 'label',
                labelWidth: 120,
                html: ('<p>Note: This key is not stored on the Software Vulnerability Manager server, please make sure that you keep it in a safe place. If lost, you may regenerate the key but doing so will invalidate the old key.<br>' +
                    '<p>Service Provider Metadata URL <a href="#" onclick="sfw.Default.externalURL(\'' + ssoMetaUrl + '\');">' + ssoMetaUrl + '</a></p>')
            }
        )

    },
    enableWMIRenderer: function (fc) {
        var check = false;
        var LoginDetails = sfw.util.Auth.LoginDetails;

        if (parseInt(LoginDetails.WMISetting) === 1) {
            check = true;
        } else {
            check = false;
        }
        fc.add({
            xtype: 'checkbox',
            name: 'enable_wmi',
            itemId: 'checkWMI',
            inputValue: 1,
            boxLabel: 'Enable WMI Check',
            disabled: sfw.util.Auth.LoginDetails.isReadOnly,
            checked: check
        })


    },
    proxyRenderer: function (rf) {
        var check_n = false;
        var check_s = false;
        var check_c = false;
        var check_cus = false;

        var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3};
        var wuaProxySettingopt = sfw.Default.options.get('wua_proxy_setting');
        var ed = sfw.util.SharedConstants.EDITION;
        var sed = sfw.util.SharedConstants.SERVER_EDITION;
        var hidden = true;
        if (ed !== sed){
            hidden = false
        }
        if (wuaProxySetting.wuaProxyNone == parseInt(wuaProxySettingopt) || !wuaProxySettingopt) {
            sfw.util.Debug.log("No WUA Proxy set");
            check_n = true;
        } else {
            check_n = false;
        }
        if (wuaProxySetting.wuaProxySame == parseInt(wuaProxySettingopt)) {
            sfw.util.Debug.log("WUA Proxy set to CSIA proxy");
            check_s = true;
        } else {
            check_s = false;
        }
        if (wuaProxySetting.wuaProxyCSIServer == parseInt(wuaProxySettingopt)) {
            sfw.util.Debug.log("WUA Proxy set to SVM Server");
            check_c = true;
        } else {
            check_c = false;
        }
        if (wuaProxySetting.wuaProxyCustom == parseInt(wuaProxySettingopt)) {
            sfw.util.Debug.log("WUA Proxy set to custom");
            check_cus = true;
        } else {
            check_cus = false;
        }
        rf.add({
                                    name: "use_same_proxy_server_as_SVM_agent",
                                    itemId: '_wua_proxy_no_proxy_radio',
                                    boxLabel:
                                        "Do not use a proxy server for the Windows Update Agent",
                                    checked: check_n,
                                    disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                                    listeners: {change: 'doNotUseProxyhandler'}
                                    // , handler: 'doNotUseProxyhandler'
                                },
                                {
                                    name: "use_same_proxy_server_as_SVM_agent",
                                    itemId: '_wua_proxy_same_proxy_radio',
                                    boxLabel:
                                        "Use the same proxy server for the Windows Update Agent as the Software Vulnerability Manager Agent uses",
                                    disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                                    checked: check_s,
                                    listeners: {change: 'proxySameHandler'}
                                    // handler: 'proxySameHandler'
                                },
                                {
                                    name: "use_same_proxy_server_as_SVM_agent",
                                    itemId: '_wua_proxy_csi_server_proxy_radio',
                                    boxLabel:
                                        "Use the Software Vulnerability Manager Server as the proxy server for the Windows Update Agent",
                                    disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                                    hidden: (sfw.SharedConstants.EDITION === sfw.SharedConstants.HOSTED_EDITION),
                                    checked: check_c,
                                    listeners: {change: 'proxyCSIServerhandler'}

                                    // handler: 'proxyCSIServerhandler'
                                },
                                {
                                    // xtype: 'radio',
                                    boxLabel: 'Use a custom proxy server for the Windows Update Agent',
                                    name: 'use_same_proxy_server_as_SVM_agent',
                                    itemId: '_wua_proxy_custom_radio',
                                    inputValue: '4',
                                    disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                                    checked: check_cus,
                                    listeners: {change: 'customeProxyhandler'}
                                    // , handler: 'customeProxyhandler'
                                })


    },

    isWUAProxyCustom: function() {
	var wuaProxySetting = sfw.Default.options.get('wua_proxy_setting');
	if ( this.wuaProxySetting.wuaProxyCustom == parseInt(wuaProxySetting) ) {
        sfw.util.Debug.log("WUA Proxy set to custom");
		return true;
	} else {
		return false;
	}
},
    proxyHostPortRender: function (rf) {
        rf.add({
                            xtype: 'textfield'
                            ,allowBlank: false
                            ,itemId: 'wua_proxy_hostport'
                            ,name: 'wua_proxy_hostport_textfield'
                            ,emptyText: 'Enter the proxy host:port ... '
                            ,regex:/^([a-zA-Z0-9._-]+:[0-9]+)$/
                            ,regexText:'Enter the proxy as: host:port'
                            ,value: sfw.Default.options.get('wua_proxy_hostport')
                            ,disabled: 'isWUAProxyCustom' || sfw.util.Auth.LoginDetails.isReadOnly
                            ,width: 285
                            ,listeners: {
                                change: function() {
                                    var wuaProxySetting = {wuaProxyNone: 0, wuaProxySame: 1, wuaProxyCSIServer: 2, wuaProxyCustom: 3};
                                    sfw.csiSettings.wuaProxyOptionSelected = wuaProxySetting.wuaProxyCustom;
                                }
                            }
                        })


    },

    saveSelectedRecipientsSettings:function(btn){
        var view = btn.up('panel');
        var selectedEmailrecipients = view.down('#emailRecipients').down('#selectedGrid');
        var selectedEmailrecipientsData = selectedEmailrecipients.getSelection();
        var email_list = [];

        for (i = 0; i < selectedEmailrecipientsData.length; i++) {
            recipientAccountId = parseInt(selectedEmailrecipientsData[i].data.recipient_account_id, 10);
            email_list.push(recipientAccountId);
        }

        var mobile_list = [];

        if (sfw.util.Auth.LoginDetails.isSMSEnabled) {
            var selectedMobilerecipients = view.down('#selectMobileRecipient').down('#selectedGrid');
            var selectedMobilerecipientsData = selectedMobilerecipients.getSelection();
            for (i = 0; i < selectedMobilerecipientsData.length; i++) {
                mobileRecipientAccountId = parseInt(selectedMobilerecipientsData[i].data.recipient_account_id, 10);
                mobile_list.push(mobileRecipientAccountId);
            }
        }

        Ext.Ajax.request({
            url: 'action=recipients&which=save_defaults',
            method: 'POST',
            params: {
                email_recipients: Ext.util.JSON.encode(email_list),
                mobile_recipients: Ext.util.JSON.encode(mobile_list)
            },
            dataType: 'json',
            success: function (data) {
                var status = Ext.decode(data.responseText);
                switch (status.error) {
                    case 0:
                        Ext.Msg.alert('Success', 'Default recipients successfully updated.');
                        break;
                    default:
                        Ext.Msg.alert('Error', 'Unexpected error.');
                        break;
                }
            },
            failure: function () {
                Ext.Msg.show({
                    title: 'Submit Status'
                    , msg: 'Default recipients failed to be updated.'
                    , icon: Ext.Msg.ERROR
                    , buttons: Ext.MessageBox.OK
                });
            }
        });
    },

    EnableHostDeletion: function () {
        var me = this;
        var view = me.getView();

        var val = view.down('#host_deletion').getValue() ? 1 : 0;
        var hostDeletionDays = view.down('#host_max_days').getValue();

        if(val && (hostDeletionDays == null || hostDeletionDays == 0)){
            Ext.Msg.alert("Failure", "Enter number of days");
            return false;
        }
        var params = {
            enabled:val,
            hostDeletionDays:hostDeletionDays
        };
        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=host_deletion'
            ,method:'POST'
            ,params: params
            , success: function (response) {
                var data = Ext.decode(response.responseText);
                if (!data.success) {
                    sfw.util.Debug.log('Error while trying to change the Host Deletion Configuration.');
                    Ext.Msg.show({
                        title: "Error",
                        msg: "Error while trying to save the Host Deletion configuration. " + data.msg,
                        buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    var text = 'disabled';
                    if (val) {
                        text = 'enabled';
                    }

                    sfw.util.Auth.LoginDetails.account.hostDeletionSettings.option_is_enabled = (text == 'enabled') ? 1 : 0 ;
                    sfw.util.Auth.LoginDetails.account.hostDeletionSettings.option_value = hostDeletionDays;

                    Ext.toast({
                        html: 'Host Deletion ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });
                }
            }
            , failure: function (response) {
                Ext.Msg.alert('Error', 'An error occurred while trying to update the Host Deletion setting.');
                sfw.util.Debug.log('Error while trying to change the Host Deletion Configuration.');
            }
        });
    },

    saveZombieFiles: function(checkbox, checked){

        if(Ext.ComponentQuery.query('#ignore_zombie_files')[0].getValue()){
            return false;
        }

        var val = checked ? 1 : 0;
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var params = {
            value:val,
            save_zombie_file:true
        };

        Ext.Ajax.request({
            url: 'action=zombie_files&which=configure'
            ,method:'POST'
            ,params: params
            , success: function (response) {
                var res = Ext.util.JSON.decode(response.responseText);
                if (res === 1) {
                    if (val === 1) {
                        LoginDetails.account.saveZombieSettings = {
                            account_id: LoginDetails.account.settings.account_id,
                            is_partition_admin: LoginDetails.account.isPartitionAdmin
                        };
                    } else {
                        LoginDetails.account.saveZombieSettings = false;
                    }
                    var text = checked ? 'enabled' : 'disabled';
                    Ext.toast({
                        html: 'Display Zombie Files ' + text,
                        ui: 'success',
                        title: 'Configuration Saved',
                        width: 200,
                        shadow: true,
                        align: 'tr'
                    });

                } else {
                    Ext.Msg.show({
                        title: 'Forbidden',
                        msg: 'You do not have enough privileges to perform this action.',
                        buttons: Ext.Msg.OK
                    });
                }
            }
            , failure: function (response) {
                Ext.Msg.alert('Error', 'An error occurred while trying to update the Display Zombie Files setting.');
            }
        });
    },

    saveExclusionList: function(btn){
        var unselected =  [];
        var exclusionList = [];

        Ext.getStore('exclusionpathlist').each(function (record) {
            var excludedPath = {};
            excludedPath['id'] = record.get("id");
            excludedPath['name'] = record.get("name");
            exclusionList.push(excludedPath);
        });

        var selected = Ext.ComponentQuery.query('#exclusion_list_chkbx_col')[0].getSelectionModel().getSelected();

        if (selected.length) {
            for (var i = 0; i < selected.length; i++) {
                for(var j = 0; j < exclusionList.length; j++){
                    if(exclusionList[j].id == selected.items[i].data.id){
                        exclusionList.splice(j,1)
                    }
                }
            }
        }

        var params = {'exclusion_list':JSON.stringify(exclusionList)}

        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=save_exclusion_list'
            ,method:'POST'
            ,params: params
            , success: function (response) {
                var response = Ext.util.JSON.decode(response.responseText);
                if (response.success && response.error_code == 0) {
                    Ext.getStore('exclusionpathlist').load();
                    Ext.Msg.show({
                        title: 'Success',
                        msg: 'Exclusion list saved successfully',
                        buttons: Ext.Msg.OK
                    });
                }else {
                    Ext.Msg.show({
                        title: 'Forbidden',
                        msg: 'You do not have enough privileges to perform this action.',
                        buttons: Ext.Msg.OK
                    });
                }
            }
            , failure: function (response) {
                Ext.Msg.alert('Error', 'An error occurred while trying to save Exclusion List.');
            }
        });
    },

    systemScoreSettings: function(fc){

        var systemScoreSettings = sfw.util.Auth.LoginDetails.account.systemScoreSettings;

        var secureProductsValueNumber = 100;
        var zeroDayValueNumber = 0;
        var threatScoreValueNumber = 0;
        var cvssScoreValueNumber = 0;
        var criticalityValueNumber = 0;

        if (typeof systemScoreSettings === 'object') {
            secureProductsValueNumber = systemScoreSettings.secure_products;
            zeroDayValueNumber = systemScoreSettings.zero_day;
            threatScoreValueNumber = systemScoreSettings.threat_score;
            cvssScoreValueNumber = systemScoreSettings.cvss_score;
            criticalityValueNumber = systemScoreSettings.criticality;
        }

        fc.add({
            xtype: 'form',
            items:[{
                xtype: 'fieldcontainer',
                layout: 'hbox',
                items:[{
                    xtype: 'label',
                    name: 'labelsystemScoreSecureProducts',
                    html: 'Secure Products'
                },{
                    xtype: 'customnumberfield',
                    name: 'systemScoreSecureProducts',
                    itemId: 'systemScoreSecureProducts',
                    valueNumber: secureProductsValueNumber,
                    maxNumber: '100',
                    minNumber: '0',
                    listeners:{
                        change:function(data,val){
                            var save =  Ext.ComponentQuery.query('#saveSystemScore')[0];
                            if(typeof save !== 'undefined'){
                                if(val < 0  || val > 100){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },
                        afterrender: function(){
                            if((sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)){
                                this.setDisabled(true);
                            }else{
                                this.setDisabled(false);
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: '%',
                    disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
                }]
            },{
                xtype: 'fieldcontainer',
                layout: 'hbox',
                items:[{
                    xtype: 'label',
                    name: 'labelZeroDay',
                    html: 'Zero Day &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'

                },{
                    xtype: 'customnumberfield',
                    name: 'systemScoreZeroDay',
                    itemId: 'systemScoreZeroDay',
                    valueNumber: zeroDayValueNumber,
                    maxNumber: '100',
                    minNumber: '0',
                    listeners:{
                        change:function(data,val){
                            var save =  Ext.ComponentQuery.query('#saveSystemScore')[0];
                            if(typeof save !== 'undefined'){
                                if(val < 0  || val > 100){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },
                        afterrender: function(){
                            if((sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)){
                                this.setDisabled(true);
                            }else{
                                this.setDisabled(false);
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: '%',
                    disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
                }]
            },{
                xtype: 'fieldcontainer',
                layout: 'hbox',
                items:[{
                    xtype: 'label',
                    name: 'labelThreatScore',
                    html: 'Threat Score &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'

                },{
                    xtype: 'customnumberfield',
                    name: 'systemScoreTI',
                    itemId: 'systemScoreTI',
                    valueNumber: threatScoreValueNumber,
                    maxNumber: '100',
                    minNumber: '0',
                    listeners:{
                        change:function(data,val){
                            var save =  Ext.ComponentQuery.query('#saveSystemScore')[0];
                            if(typeof save !== 'undefined'){
                                if(val < 0  || val > 100){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },
                        afterrender: function(){
                            if(((sfw.util.Auth.LoginDetails.isReadOnly) || (!sfw.util.Auth.LoginDetails.isPartitionAdmin) || (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled))){
                                this.setDisabled(true);
                                if(true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled){
                                    this.setValue(0);
                                }
                            }else{
                                this.setDisabled(false);
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: '%',
                    disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
                }]
            },{
                xtype: 'fieldcontainer',
                layout: 'hbox',
                items:[{
                    xtype: 'label',
                    name: 'labelCvssScore',
                    html: 'CVSS Score &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'

                },{
                    xtype: 'customnumberfield',
                    name: 'systemScoreCvss',
                    itemId: 'systemScoreCvss',
                    valueNumber: cvssScoreValueNumber,
                    maxNumber: '100',
                    minNumber: '0',
                    listeners:{
                        change:function(data,val){
                            var save =  Ext.ComponentQuery.query('#saveSystemScore')[0];
                            if(typeof save !== 'undefined'){
                                if(val < 0  || val > 100){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },
                        afterrender: function(){
                            if((sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)){
                                this.setDisabled(true);
                            }else{
                                this.setDisabled(false);
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: '%',
                    disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
                }]
            },{
                xtype: 'fieldcontainer',
                layout: 'hbox',
                items:[{
                    xtype: 'label',
                    name: 'labelCriticality',
                    html: 'Criticality  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'

                },{
                    xtype: 'customnumberfield',
                    name: 'systemScoreCriticality',
                    itemId: 'systemScoreCriticality',
                    valueNumber: criticalityValueNumber,
                    maxNumber: '100',
                    minNumber: '0',
                    listeners:{
                        change:function(data,val){
                            var save =  Ext.ComponentQuery.query('#saveSystemScore')[0];
                            if(typeof save !== 'undefined'){
                                if(val < 0  || val > 100){
                                    save.setDisabled(true);
                                }else{
                                    save.setDisabled(false);
                                }
                            }
                        },
                        afterrender: function(){
                            if((sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)){
                                this.setDisabled(true);
                            }else{
                                this.setDisabled(false);
                            }
                        }
                    }
                },{
                    xtype: 'displayfield',
                    value: '%',
                    disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin)
                }]
            },{
                xtype:'button',
                text:'Save',
                ui: 'primary',
                itemId:'saveSystemScore',
                disabled: (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin),
                handler: 'saveSystemScore',
            }]
        });
    },

    saveSystemScore: function(){
        var me = this;
        var view = me.getView();


        var secureProducts = 0;
        var zeroDay = 0;
        var threatScore = 0;
        var cvssScore = 0;
        var criticality = 0;

        if(view.down('#systemScoreSecureProducts').getValue()){
            secureProducts = parseInt(view.down('#systemScoreSecureProducts').getValue());
        }

        if(view.down('#systemScoreZeroDay').getValue()){
            zeroDay = parseInt(view.down('#systemScoreZeroDay').getValue());
        }

        if(view.down('#systemScoreTI').getValue()){
            threatScore = parseInt(view.down('#systemScoreTI').getValue());
        }

        if(view.down('#systemScoreCvss').getValue()){
            cvssScore = parseInt(view.down('#systemScoreCvss').getValue());
        }

        if(view.down('#systemScoreCriticality').getValue()){
            criticality = parseInt(view.down('#systemScoreCriticality').getValue());
        }

        if((secureProducts < 0) || (zeroDay < 0)  || (threatScore < 0) || (cvssScore < 0) || (criticality < 0)){
            Ext.Msg.show({
                title: "Note",
                msg: "Please enter positive numbers.",
                buttons: Ext.Msg.OK
            });
            return false;
        }

        var sumOfSystemScore = secureProducts + zeroDay + threatScore + cvssScore + criticality;

        if(sumOfSystemScore != 100){
            Ext.Msg.show({
                title: "Note",
                msg: "Sum of the entered values should be equal to 100.",
                buttons: Ext.Msg.OK
            });
            return false;
        }

        var systemScoreValues = {};
        systemScoreValues['secure_products'] = secureProducts;
        systemScoreValues['zero_day'] = zeroDay;
        systemScoreValues['threat_score'] = threatScore;
        systemScoreValues['cvss_score'] = cvssScore;
        systemScoreValues['criticality'] = criticality;

        var params = {'system_score_values':JSON.stringify(systemScoreValues)}

        Ext.Ajax.request({
            url: 'action=ajaxapi_submit_settings&which=save_system_score'
            ,method:'POST'
            ,params: params
            , success: function (response) {

                var response = Ext.util.JSON.decode(response.responseText);
                if (response.success && response.error_code == 0) {
                    var systemScoreSettings = sfw.util.Auth.LoginDetails.account.systemScoreSettings;

                    systemScoreSettings.secure_products = secureProducts;
                    systemScoreSettings.zero_day = zeroDay;
                    systemScoreSettings.threat_score = threatScore;
                    systemScoreSettings.cvss_score = cvssScore;
                    systemScoreSettings.criticality = criticality;

                    Ext.Msg.show({
                        title: 'Success',
                        msg: 'System Score settings saved successfully.',
                        buttons: Ext.Msg.OK
                    });
                }else {
                    Ext.Msg.show({
                        title: 'Failure',
                        msg: response.msg,
                        buttons: Ext.Msg.OK
                    });
                }
            }
            , failure: function (response) {
                Ext.Msg.alert('Error', 'An error occurred while saving System Score settings.');
                sfw.util.Debug.log('An error occurred while saving System Score settings.');
            }
        });
    }



});
