Ext.define('sfw.view.configuration.LogMessagesModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.logmessages',

    stores: {
        /*
        A declaration of Ext.data.Store configurations that are first processed as binds to produce an effective
        store configuration. For example:

        users: {
            model: 'LogMessages',
            autoLoad: true
        }
        */
    },

    data: {
        /* This object holds the arbitrary data that populates the ViewModel and is then available for binding. */
    }
});
