Ext.define('sfw.view.configuration.LogMessages', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiLogger',

    id: 'sfwCsiLoggerId',

    title: 'Log Messages',
    border: 1,
    cls: 'shadow',
    stateful: true,
    stateId: 'sfw_csiLogger_grid',
    layout: 'fit',
    scrollable: true,
    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    tbar: [
        {
            text: 'Clear',
            ui: 'primary',
            handler: 'clearLogMessages'
        }
    ],

    columns: [
        { text: 'Date', dataIndex: 'date', flex: 1 },
        { text: 'Message', dataIndex: 'description', flex: 5 }
    ],

    bbar: {
        xtype: 'pagingtoolbar',
        displayInfo: true,
        displayMsg: 'Displaying Log Entries {0} - {1} of {2} (max. 500)',
        emptyMsg: "No Log entries"
    },

    listeners: {
        rowdblclick: 'onRowDblClick',
        beforerender: 'onLogGridBeforeRender',
        itemcontextmenu: 'onItemContextMenu'
    },

    controller: {

        clearLogMessages: function () {
            const me = this,
                grid = me.getView();

            sfw.csiLogger.loggerData.length = 0;
            grid.down('pagingtoolbar').moveFirst();
            grid.getStore().removeAll();
        },

        onLogGridBeforeRender: function () {
            const me = this,
                grid = me.getView();

            var store = Ext.create('sfw.store.configuration.LogMessages', {data: sfw.csiLogger.loggerData});
            grid.setStore(store);
            grid.down('pagingtoolbar').setStore(store);
        },

        onItemContextMenu: function (grid, record, item, index, e) {
            const me = this;
            var contextMenu = Ext.create('Ext.menu.Menu', {
                width: 225,
                plain: true,
                items: [{
                    text: 'Copy Row Data to Clipboard',
                    handler: function () {
                        me.copyDataToClipboard(record);
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },

        onRowDblClick: function (grid, record) {
            this.copyDataToClipboard(record);
        },

        copyDataToClipboard: function (record) {
            const defaults = sfw.util.Default;
            var message = '"' + record.get( 'date' ) + '","' + defaults.htmlSpecialCharsDecode( record.get( 'description' ) ).replace( /\"/g, '""' ) + '"';

            if (window.clipboardData) {
                window.clipboardData.setData( 'Text', message );
            } else if (Ext.isGecko || Ext.isChrome) {
                function listener(e) {
                    e.clipboardData.setData("text/plain", message);
                    e.preventDefault();
                }
                document.addEventListener("copy", listener);
                document.execCommand("copy");
                document.removeEventListener("copy", listener);
            } else {
                console.log('ERROR: sfw.csiLogger export data to clipboard: Cannot read property \'setData\' of undefined');
            }
        }
    }

});
