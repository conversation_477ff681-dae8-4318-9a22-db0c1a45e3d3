Ext.define('sfw.view.configuration.GeneralSettingsModel', {
    extend: 'Ext.app.ViewModel',
    alias: 'viewmodel.configuration-generalsettings',
    data: {
        name: 'sfw'
    },
    stores: {
        fetchUser: {
            autoLoad: true,
            pageSize: 0,
            proxy: {
                type: 'ajax',
                url: 'action=ajaxapi_submit_settings&which=get_user_list&',
                reader: {
                    type: 'json',
                    rootProperty: 'data.rows'
                }
            }
        },
    },


});
