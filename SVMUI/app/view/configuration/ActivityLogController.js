Ext.define('sfw.view.configuration.AcivityLogController',{
    extend:'Ext.app.ViewController',
    alias:'controller.activitylog',

    init:function(){
        highPriority = true;
        mediumPriority = true;
        lowPriority = true;
    },

    updatePriority:function(checked,priorityType){
        if(priorityType == '_highp')
        {
            highPriority = checked;
        }

        if(priorityType == '_mediump')
        {
            mediumPriority = checked;
        }

        if(priorityType == '_lowp')
        {
            lowPriority = checked;
        }
    },

    checkPriority:function(item,checked){
        if ( item.config.itemId === "_highp" ) {
            this.updatePriority(checked,'_highp');
        } else if ( item.config.itemId ===  "_mediump" ) {
            this.updatePriority(checked,'_mediump');
        } else if ( item.config.itemId ===  "_lowp" ) {
            this.updatePriority(checked,'_lowp');
        }

        this.refresh();
    },

    showAll : function () {

        var view = this.getView();

        this.fromDate = null;
        view.down('#date_end').setValue(sfw.Util.dateCreateTodayOffset())

        view.down('#searchable_field').setValue('');

        view.down('#searchabletype').setValue('');

        this.highPriority = true;
        this.mediumPriority = true;
        this.lowPriority = true;

        view.down("#_highp").setChecked(true);
        view.down("#_mediump").setChecked(true);
        view.down("#_lowp").setChecked(true);

        this.refresh();
    },

    refresh:function() {

        var view = this.getView();

        var activitystore = Ext.getStore('activitylog');

        var offset = sfw.Util.dateCreate().getTimezoneOffset()*60000;

        var fromdate = new Date( sfw.Util.dateCreate(view.down('#date_start').getValue()).getTime() + offset );
        fromdate = Ext.util.Format.date(fromdate,sfw.Globals.sqlLiteDate);

        var todate = new Date( sfw.Util.dateCreate(view.down('#date_end').getValue()).getTime() + offset + 60*60*24*1000 );
        todate = Ext.util.Format.date(todate,sfw.Globals.sqlLiteDate);

        activitystore.getProxy().setExtraParams({
            'date_start': fromdate,
            'date_end': todate,
            'searchable_field': view.down('#searchable_field').getValue(),
            'searchable_type': view.down('#searchabletype').getValue(),
            'high_priority': highPriority,
            'medium_priority': mediumPriority,
            'low_priority': lowPriority,})

        if(view.down('#searchable_field').getValue() == ''){
            delete activitystore.proxy.extraParams.searchable_field
        }

        if(view.down('#searchabletype').getValue() == null){
            delete activitystore.proxy.extraParams.searchable_type
        }

        activitystore.loadPage(1,{
            callback: function () {
                //console.log("sucess");
            },
            failure: function () {
                //console.log("failed");
            }
        });
    },

    copyDataToClipboard: function (event, target, options) {
        try {
            var me = this;
            var columns = [];
            var columnValue = "";
            var columnTitle = "";

            var columnNames = options.extra.config.grid.columns;
            var rowIndex = options.rowIndex;
            var grid = options.extra;

            // Right pads the text using tabs
            var tabPadRight = function ( text, padCount ) {
                return text + Array( Math.ceil( (8 * padCount - text.length) / 8 ) + 1).join( "\t" );
            };

            /**
             * Loop through each of the columns of the activity log
             * and fetch the data from the store corresponding to
             * the respective column. Create a concatenated string from
             * all the values
             * @param  {Ext.grid.Column} column the column being processed
             * @return {void}
             */
            columnNames.map( function( column ) {
                /**
                 * Get the value of the column located on the row corresponding
                 * to the double click event target.
                 * @type {String}
                 */

                columnValue = grid.store.data.items[ rowIndex ].get( column.dataIndex );
                switch ( column.dataIndex ) {
                    /**
                     * The priority is a int that needs to be translated
                     * into human readable code
                     */
                    case 'priority':
                        columnValue = me.gridRenderPriority( columnValue );
                        break;
                    default:
                        columnValue = columnValue.replace( /\r\n/g, "\r\n\t\t\t" );
                        break;
                }
                columnTitle = tabPadRight( column.text + ": ", 3 );
                columns.push( columnTitle + columnValue );
            } );
            var message = sfw.Default.htmlSpecialCharsDecode( columns.join("\r\n") );

            if (window.clipboardData) {
                window.clipboardData.setData( 'Text', message );
            } else if (Ext.isGecko || Ext.isChrome) {
                function listener(e) {
                    e.clipboardData.setData("text/plain", message);
                    e.preventDefault();
                }
                document.addEventListener("copy", listener);
                document.execCommand("copy");
                document.removeEventListener("copy", listener);
            } else {
                console.log('ERROR: sfw.csiLogger export data to clipboard: Cannot read property \'setData\' of undefined');
            }
        } catch ( ex ) {
            console.log( ex, 'file: csi_activity_log.js line: 107', 'CSIActivityLog export data to clipboard' );
        }
    },

    gridRenderPriority:function(value){
        if ((value > 0) && (value <= 10)) {
            return "High";
        } else if ((value > 10) && (value <= 20)) {
            return "Medium";
        } else {
            return "Low";
        }
    }
})