Ext.define('sfw.view.configuration.PasswordRecoveryController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.configuration-passwordrecovery',

    onBeforeRender: function () {
        var _this = this;
        Ext.Ajax.request({
            url: 'action=contact_verification&which=read',
            method: 'GET',
            success: function (data) {
                response = Ext.decode(data.responseText);
                if (response.success === false) {
                    sfw.util.Debug.log('[CONTACT-VERIFICATION] There was a problem fetching the contact verfication data');
                }
                _this.updateStatus(response.data.email_address, response.data.mobile_number, response.data.verified);
            },
            failure: function (data) {
                sfw.util.Debug.log('[CONTACT-VERIFICATION] There was a server problem fetching the contact verification data.');
            }
        });
    },

    updateButtonState: function() {
        var buttonDisabledFlag, verified = parseInt(response.data.verified, 10);
        const me = this,
            view = me.getView(),
            countryCodeSelectorField = view.down('#countryCodeSelectorField'),
            mobileNumberField = view.down('#mobileNumberField'),
            emailAddress = view.down('#email_address'),
            currentPwdField = view.down('#currentPwdField'),
            codeSentEmailField=view.down('#codeSentEmailField'),
            codeSentSMSField=view.down('#codeSentSMSField'),
            verifyCodesButton=view.down('#verifyCodesButton'),
            sendVerificationCodesButton=view.down('#sendVerificationCodesButton');
        if ( ( Ext.form.VTypes.email( emailAddress.getValue() ) )  ) {
            if (LoginDetails.isSMSEnabled && (!(countryCodeSelectorField.getRawValue().length > 0 ) ||
                !(mobileNumberField.getRawValue().length >= 6 ))) {
                buttonDisabledFlag = true;
            } else {
                buttonDisabledFlag = false;
            }
        } else {
            buttonDisabledFlag = true;
        }
        sendVerificationCodesButton.setDisabled( buttonDisabledFlag );

        // There's no constraint on the password
        if ( !verified && ( currentPwdField.getValue().length >= 1 ) &&
            ( codeSentEmailField.getValue().length === 4 ) ) {
            if (LoginDetails.isSMSEnabled && !(codeSentSMSField.getValue().length === 4)) {
                buttonDisabledFlag = true;
            } else {
                buttonDisabledFlag = false;
            }
        } else {
            buttonDisabledFlag = true;
        }
        verifyCodesButton.setDisabled( buttonDisabledFlag );
    },

    updateStatus: function (emailAddress, mobileNumber, verified) {
        const me = this,
            view = me.getView(),
            verificationStatusLabel = view.down('#verificationStatusLabel');
        var status = '<p style="margin:0px 0px 3px 10px">Contact information is not verified.</p>';

        var contactInfoVerifyMobileText = LoginDetails.isSMSEnabled ? '<p style="margin:0px 0px 3px 10px">Mobile number: <b>00' + mobileNumber + ' </b></p>' : '';
        var contactInfoVerifyMobileText2 = LoginDetails.isSMSEnabled ? '</b></p><p style="margin:0px 10px">Mobile number: <b>00' + mobileNumber + '</b></p>' : '';
        if (emailAddress && (!LoginDetails.isSMSEnabled || mobileNumber)) {
            verified = parseInt(verified, 10);

            if (parseInt(verified, 10)) {
                status = '<p style="margin:0px 0px 3px 10px">Contacts are verified!</p>'
                    + '<p style="margin:0px 0px 3px 10px">Email address: <b>' + emailAddress + '</b></p>'
                    + contactInfoVerifyMobileText;
            } else {
                status += '<p style="margin:10px 5px 5px 10px">Verification codes were sent to:'
                    + '<p style="margin:0px 10px 10px 10px">Email address: <b>' + emailAddress
                    + contactInfoVerifyMobileText2;
            }
        }
        verificationStatusLabel.setHtml(status);
    },

    clearVerifyMobileNumberFormData: function() {
        const me = this,
            view = me.getView(),
            currentPwdField = view.down('#currentPwdField'),
            codeSentEmailField=view.down('#codeSentEmailField'),
            codeSentSMSField=view.down('#codeSentSMSField');
        currentPwdField.setValue('');
        codeSentEmailField.setValue('');
        if(LoginDetails.isSMSEnabled){
            codeSentSMSField.setValue('');
        }
    },

    submitMobileNumber: function () {
        var sendCodesResponse, params={};
        this.clearVerifyMobileNumberFormData();
        const me = this,
            view = me.getView(),
            emailAddress=view.down('#email_address').getValue(),
            countryCodeSelectorField = view.down('#countryCodeSelectorField').getValue(),
            mobileNumberField = view.down('#mobileNumberField').getValue();
        params = {
            email_address: emailAddress,
            country_code_selector: countryCodeSelectorField,
            mobile_number: mobileNumberField
        };
        Ext.Ajax.request({
            url: 'action=ajaxapi_send_verification_codes',
            waitMsg: 'Sending verification codes...',
            params: params,
            success: function (action) {
                try {
                    sendCodesResponse = Ext.decode(action.responseText);
                } catch (ex) {
                    Ext.Msg.show({
                        title: "Error"
                        , msg: "Unknown error: invalid server response. Please try again later"
                        , buttons: Ext.Msg.OK
                    });
                    return;
                }
                var lResponse = sendCodesResponse.response;
                var lEmail = sendCodesResponse.email;
                var lMobile = sendCodesResponse.mobile;
                var lExpireInterval = sendCodesResponse.expire;
                var lVerified = sendCodesResponse.verified;
                var lReason = sendCodesResponse.reason;

                var title = 'Error';
                var msg = '';
                var width = 500;

                var successMobileNrText = ' and mobile number: +' + lMobile + '!';
                if (!LoginDetails.isSMSEnabled) {
                    successMobileNrText = '';
                }
                // Previously we synchronized contact_verification here (blocking sync)

                switch (lResponse) {

                    case -1:
                        title = 'Abnormal Error';
                        msg = 'Unkown error!';
                        width = 200;
                        break;
                    case 0:
                        title = 'Success';
                        msg = 'Verification codes sent to email address: ' + lEmail + successMobileNrText;
                        if (LoginDetails.isSMSEnabled) {
                            sfw.util.Debug.log('Trying to send Verification Code to mobile number: +' + lMobile + '.');
                        }
                        width = 500;
                        break;
                    case 1:
                        msg = 'Internal error.';
                        width = 175;
                        break;
                    case 2:
                        msg = 'Email Address is empty.';
                        width = 225;
                        break;
                    case 3:
                        msg = 'Mobile Number is empty.';
                        width = 225;
                        break;
                    case 4:
                        msg = 'Mobile Number can only contain numerics.';
                        width = 300;
                        break;
                    case 5:
                        msg = 'Mobile Number should be 14 digits max.';
                        width = 300;
                        break;
                    case 6:
                        msg = 'Error sending SMS' + (lReason ? ': ' + lReason : '') + '. Check mobile number and retry.';
                        sfw.util.Debug.log('Trying to send Verification Code to mobile number: +' + lMobile + '.');
                        width = 350;
                        break;
                    case 7:
                        msg = 'Contact verification is already in process for the account. Verification Codes are valid for ' + lExpireInterval + ' hours.';
                        width = 500;
                        break;
                    case 8:
                        msg = 'Data Issue. Verification Failed.';
                        width = 275;
                        break;
                    case 9:
                        msg = 'Country Calling Code is empty. ';
                        width = 300;
                        break;
                    case 10:
                        msg = 'Country Code can only contain numerics.';
                        width = 300;
                        break;
                    case 11:
                        msg = 'Country Code must have between 1 and 4 digits.';
                        width = 300;
                        break;
                    case 12:
                        msg = 'Incorrect password.';
                        width = 300;
                        break;
                    default:
                        break;
                }
                Ext.Msg.show({
                    title: title
                    , msg: msg
                    , width: width
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.INFO
                });

                me.updateStatus(lEmail, lMobile, lVerified);

            }
            , failure: function (form, action) {
                switch (action.failureType) {
                    case Ext.form.Action.CLIENT_INVALID:
                        Ext.Msg.alert('Failure', 'Form fields do not contain valid values');
                        break;
                    case Ext.form.Action.CONNECT_FAILURE:
                        Ext.Msg.alert('Failure', 'Connection error');
                        break;
                    default:
                        Ext.Msg.alert('Failure', 'Abnormal Execution');
                        break;
                }
            }
        });
    },

    submitVerificationCodes: function() {
        var codeVerificationResponse, params={}, _this=this;
        const me = this,
            view = me.getView(),
            currentPwdField = view.down('#currentPwdField').getValue(),
            codeSentEmailField=view.down('#codeSentEmailField').getValue(),
            codeSentSMSField=view.down('#codeSentSMSField').getValue();
        params = {
            verification_code_email: codeSentEmailField,
            verification_code_sms: codeSentSMSField,
            current_password: currentPwdField,
        };
        Ext.Ajax.request({
            url: 'action=ajaxapi_verify_contact_information',
            waitMsg: 'Verifying codes...',
            params: params,
            success: function( action ) {
                try {
                    codeVerificationResponse = Ext.decode( action.responseText );
                } catch ( ex ) {
                    Ext.Msg.show({
                        title: "Error"
                        ,msg: "Unknown error: invalid server response. Please try again later"
                        ,buttons: Ext.Msg.OK
                    });
                    return;
                }
                var lResponse = codeVerificationResponse.response;
                var lEmail = codeVerificationResponse.email;
                var lMobile = codeVerificationResponse.mobile;
                var lVerified = codeVerificationResponse.verified;

                var title = 'Error';
                var msg = '';
                var width = 500;

                var successMobileNrText2 = ' and mobile number: +' + lMobile + ' are now verified successfully!';
                var successMobileNrText3 = ' and mobile number: +' + lMobile + ' are already verified.';

                if(!LoginDetails.isSMSEnabled)
                {
                    successMobileNrText2 = ' is now verified successfully!';
                    successMobileNrText3 = ' is already verified.';
                }

                // Previously we synchronized contact_verification here (blocking sync)

                switch (lResponse) {

                    case -1:
                        title = 'Abnormal Error';
                        msg = 'Unkown error!';
                        width = 200;
                        break;
                    case 0:
                        title = 'Success';
                        msg = 'Email address: ' + lEmail + successMobileNrText2;
                        width = 500;
                        me.clearVerifyMobileNumberFormData();
                        break;
                    case 1:
                        msg = 'Internal error.';
                        width = 175;
                        break;
                    case 2:
                        msg = 'Verification codes do not have the correct length.';
                        width = 375;
                        break;
                    case 3:
                        msg = 'Verification codes must be alphanumeric.';
                        width = 300;
                        break;
                    case 4:
                        msg = 'Email address: ' + lEmail + successMobileNrText3;
                        width = 500;
                        me.clearVerifyMobileNumberFormData();
                        break;
                    case 5:
                        msg = 'Error - no user/password match in database.  Please try again.';
                        width = 400;
                        break;
                    case 6:
                        msg = 'Verification code'+(LoginDetails.isSMSEnabled?'s':'') +' incorrect.';
                        width = 275;
                        break;
                    case 7:
                        msg = 'Verification code'+(LoginDetails.isSMSEnabled?'s have':' has') +'expired.';
                        width = 275;
                        break;
                    case 8:
                        msg = 'Data Issue. Verification Failed.';
                        width = 275;
                        break;
                    case 9:
                        msg = 'Country Calling Code is empty. ';
                        width = 300;
                        break;
                    case 10:
                        msg = 'Country Code can only contain numerics.';
                        width = 300;
                        break;
                    case 11:
                        msg = 'Country Code must have between 1 and 4 digits.';
                        width = 300;
                        break;
                    case 12:
                        msg = 'Incorrect password.';
                        width = 300;
                        break;
                    default:
                        break;
                }
                Ext.Msg.show({
                    title: title
                    ,msg: msg
                    ,width: width
                    ,buttons: Ext.Msg.OK
                    ,icon: Ext.MessageBox.INFO
                });

                _this.updateStatus( lEmail, lMobile, lVerified );

            }
            ,failure: function(form, action) {
                switch (action.failureType) {
                    case Ext.form.Action.CLIENT_INVALID:
                        Ext.Msg.alert('Failure', 'Form fields do not contain valid values');
                        break;
                    case Ext.form.Action.CONNECT_FAILURE:
                        Ext.Msg.alert('Failure', 'Connection error');
                        break;
                    default:
                        Ext.Msg.alert('Failure', 'Abnormal Execution');
                        break;
                }
            }
        });
    }

});
