Ext.define('sfw.view.configuration.ChangePassword', {
    extend: 'Ext.form.Panel',
    xtype: 'sfw.csiResetPassword',
    itemId:'changePassowrdWindow',

    title: 'Change Password',

    requires: [
        'sfw.view.configuration.ChangePasswordController',
        'sfw.view.configuration.ChangePasswordModel'
    ],

    controller: 'configuration-changepassword',
    viewModel: {
        type: 'configuration-changepassword'
    },

    bodyPadding: 10,
    scrollable: true,
    layout: 'auto',

    items: [{
        xtype: 'panel',
        title: 'Change Stored Password Data',
        ui: 'light',
        layout : 'vbox',
        bodyPadding: 10,
        frame : true,
        width: 600,

        defaults: {
            labelWidth: 150,
            flex: 1,
            width: '80%',
            msgTarget: 'side'
        },
        defaultType: 'textfield',

        items: [{
            xtype: 'label',
            width: '100%',
            itemId: 'passwordPolicy',
            html: ''
        },{
            allowBlank: false,
            fieldLabel: 'Existing Password',
            itemId: 'existing_pwd',
            name: 'ex_pwd_textfield',
            inputType: 'password'
        }, {
            allowBlank: false,
            fieldLabel: 'Enter New Password',
            itemId: 'new_pwd',
            name: 'new_pwd_textfield',
            inputType: 'password'
        }, {
            allowBlank: false,
            fieldLabel: 'Confirm New Password',
            itemId: 'new_pwd_conf',
            name: 'new_pwd_conf_textfield',
            inputType: 'password',
            margin: '0 0 20 0'
        }],

        buttons: [
            {
                xtype: 'button',
                text: 'Clear',
                //ui: 'primary',
                margin: '0 10 0 0',
                handler: "onResetClick",
                tooltip: 'Clear all input fields in the above form.'
            },{
                xtype: 'button',
                text: 'Change Password',
                //ui: 'primary',
                handler: 'submitFormData',
                tooltip: 'Change your password with Flexera.'
            }
        ]
    }],

    listeners: {
        beforerender: 'refresh'
    }

});
