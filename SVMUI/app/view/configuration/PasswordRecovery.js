//Other part of code
var contactVerifText = '<p>Please enter the verification codes sent to your email address and mobile as well as your current password, and click "Verify" to complete the verification process.</p>';
var contactRegistrationText = '<p>Please enter your email address and mobile phone number (including country code) and click "Send Verification Codes". You will get two verification codes: one sent to your email address and another sent to your mobile.</p>';
var contactRegistrationPhoneLabel = new Ext.form.Label({
    fieldLabel: ' ', labelSeparator: '', cls: 'SecondaryGreyText', html: '<i> e.g. ***********</i><br />'
});

Ext.define('sfw.view.configuration.PasswordRecovery', {
    extend: 'Ext.form.Panel',
    xtype: 'sfw.csiPasswordRecovery',

    title: 'Password Recovery Settings',

    requires: [
        'sfw.view.configuration.PasswordRecoveryController',
        'sfw.view.configuration.PasswordRecoveryModel'
    ],

    controller: 'configuration-passwordrecovery',

    frame: false,
    bodyPadding: 10,
    scrollable: true,

    items: [{
        xtype: 'panel',
        ui: 'light',
        frame: true,
        title: 'Contact Registration',
        defaultType: 'textfield',
        layout: 'form',
        bodyPadding: 10,

        items: [{
            xtype: 'box',
            padding: '0 0 10 0',
            itemId: 'verificationStatusLabel',
            html: 'Contact information is not verified.'
        }, {
            xtype: 'fieldset',
            title: 'Contact Details',
            width: 700,
            defaults: {
                labelWidth: 160,
                flex: 1,
                width: '70%',
                msgTarget: 'side'
            },
            items: [{
                xtype: 'label',
                labelWidth: 120,
                html: this.contactRegistrationText
            }, {
                fieldLabel: 'Email Address:',
                //labelAlign: 'top',
                xtype: 'textfield',
                name: 'email_address',
                itemId: 'email_address',
                enableKeyEvents: true,
                vtype: 'email',
                listeners: {
                    keyup: 'updateButtonState'
                }
            }, {
                xtype: 'fieldcontainer',
                layout: 'hbox',
                fieldLabel: 'Mobile Phone',
                name: 'mobile_number',
                //labelAlign: 'top',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '(',
                        padding: '0 5 0 0'
                        //width: 10
                    },
                    {
                        xtype: 'combobox',
                        store: sfw.Default.countryCode,
                        valueField: 'countryCode',
                        displayField: 'countryName',
                        hidelLabel: true,
                        width: 80,
                        editable:false,
                        itemId: 'countryCodeSelectorField',
                        tpl: Ext.create('Ext.XTemplate',
                            '<ul class="x-list-plain"><tpl for=".">',
                            '<li role="option" class="x-boundlist-item">{countryName} <span style="float:right;color:#878787;">(+{countryCode})</span></li>',
                            '</tpl></ul>'
                        ),
                        displayTpl: Ext.create('Ext.XTemplate',
                            '<tpl for=".">{countryCode}</tpl>'
                        ),
                        listConfig: {
                            width: 300,
                            minWidth: 300
                        },
                        listeners: {
                            select: 'updateButtonState'
                        }
                    },
                    {
                        xtype: 'displayfield',
                        value: ')',
                        padding: '0 10 0 5'
                    },
                    {
                        xtype: 'textfield',
                        itemId: 'mobileNumberField',
                        enableKeyEvents: true,
                        maskRe: /[0-9.]/,
                        maxLength: 15,
                        autoComplete: false,
                        minLength: 6,
                        width: 190,
                        listeners: {
                            keyup: 'updateButtonState'
                        }
                    }
                ],
                listeners: {
                    afterRender: function () {
                        if (!LoginDetails.isSMSEnabled) {
                            this.hide();
                        }
                    }
                }
            }, {
                html: this.contactRegistrationPhoneLabel,
                listeners: {
                    afterRender: function () {
                        if (!LoginDetails.isSMSEnabled) {
                            this.hide();
                        }
                    }
                }
            }, {
                xtype: 'button',
                disabled: 'true',
                margin: '10 0 10 0',
                //ui: 'primary',
                width: 180,
                text: 'Send Verification Codes',
                itemId: 'sendVerificationCodesButton',
                handler: 'submitMobileNumber'
            }]
        }, {
            xtype: 'fieldset',
            title: 'Contact Verification',
            width: 700,
            defaults: {
                labelWidth: 160,
                flex: 1,
                width: '70%',
                msgTarget: 'side'
            },
            items: [{
                xtype: 'label',
                html: this.contactVerifText
            }, {
                xtype: "textfield",
                fieldLabel: 'Enter code sent via Email:',
                name: 'verification_code_email',
                vtype: 'alphanum',
                itemId: 'codeSentEmailField',
                minLength: 4,
                enableKeyEvents: true,
                listeners: {
                    keyup: 'updateButtonState'
                }
            }, {
                xtype: "textfield",
                fieldLabel: 'Enter code sent via SMS:',
                //labelAlign: 'top',
                // labelWidht: 100,
                name: 'verification_code_sms',
                vtype: 'alphanum',
                minLength: 4,
                itemId: 'codeSentSMSField',
                enableKeyEvents: true,
                listeners: {
                    keyup: 'updateButtonState',
                    afterRender: function () {
                        if (!LoginDetails.isSMSEnabled) {
                            this.hide();
                        }
                    }
                }
            }, {
                xtype: "textfield",
                fieldLabel: 'Enter current password:',
                //labelAlign: 'top',
                name: 'current_password',
                inputType: 'password',
                enableKeyEvents: true,
                itemId: 'currentPwdField',
                listeners: {
                    keyup: 'updateButtonState'
                }
            }, {
                xtype: 'button',
                disabled: 'true',
                text: 'Verify',
                width: 100,
                margin: '10 0 10 0',
                //ui: 'primary',
                itemId: 'verifyCodesButton',
                handler: 'submitVerificationCodes'
            }]
        }],

        listeners: {
            beforerender: {
                fn: 'onBeforeRender'
            }
        }
    }]
});
