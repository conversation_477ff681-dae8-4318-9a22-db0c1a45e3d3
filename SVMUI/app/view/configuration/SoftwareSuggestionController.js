Ext.define('sfw.view.configuration.SoftwareSuggestionController', {
	extend: 'Ext.app.ViewController',
	alias: 'controller.softwaresuggestion',


	getStatus: function (value, metaData, record, rowIndex, colIndex, store) {

		let status = parseInt(value, 10);
		let suggestion_status = '';
		if (status === 0)
			suggestion_status = 'Request Sent';
		else if (status === 1)
			suggestion_status = 'Completed';
		else if (status === 2)
			suggestion_status = 'Needs Clarification';
		else if (status === 3)
			suggestion_status = 'Pending Review';
		else if (status === 4)
			suggestion_status = 'Rejected';
		else if (status === 5)
			suggestion_status = 'In Progress';
		else
			suggestion_status = 'Request Sent';

		return suggestion_status;
	},

	deleteSuggestion: function (event, target, options) {

		var selectionArray = [];
		var deleteIdList = '';
		if (options.extra.length > 1) {
			for (var i = 0; i < options.extra.length; ++i) {
				selectionArray.push(options.extra[i].data.suggestion_id);
			}
			deleteIdList = selectionArray.join(",");
		} else {
			deleteIdList = options.extra.data.suggestion_id;
		}

		Ext.Ajax.request({
			url: 'action=suggest_software&which=delete&',
			method: 'POST',
			params: {
				suggestion_id: deleteIdList,
			},
			dataType: 'json',
			success: function (response) {
				var response = Ext.util.JSON.decode(response.responseText);
				if (response.success === true) {
					//var count = response.data;
					Ext.Msg.show({
						title: 'Success',
						msg: 'Successfully deleted record',
						buttons: Ext.Msg.OK
					});
					Ext.getCmp('csiSoftwareSuggestion').getStore().load();
				} else {
					sfw.util.Debug.log('Error while deleting record');
					Ext.Msg.show({
						title: 'Error',
						msg: 'Deleting record failed',
						buttons: Ext.Msg.OK
					});
				}
			},
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Deleting record failed");
			}
		});

	},

	getAverageTtr: function () {
		var view = this.getView(),
			averagettr = view.down('#averagettr');

		Ext.Ajax.request({
			url: 'action=suggest_software&which=get_averagettr',
			method: 'GET',
			dataType: 'json',
			success: function (response, opts) {
				var response = Ext.decode(response.responseText);
				if (response.success && response.error_code == 0) {
					averagettr.setHidden(false);
					var average = response.data.average_ttr;
					if (average == 0) {
						average = 2;
					} else {
						average = average.toFixed(1);
					}
					averagettr.setValue('Average time to resolution over last 30 days : <b>'+ average +'</b> days. ');
				} else {
					averagettr.setHidden(true);
				}
			},
			failure: function (response, opts) {
				averagettr.setHidden(true);
			}
		});

	},



});
