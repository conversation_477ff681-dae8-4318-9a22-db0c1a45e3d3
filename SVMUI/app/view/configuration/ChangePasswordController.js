Ext.define('sfw.view.configuration.ChangePasswordController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.configuration-changepassword',

    getForm: function() {
        return this.getView().getForm();
    },

    onResetClick: function() {
        this.getForm().reset();
    },

    logout: function () {
        const me = this,
            view = me.getView();

        //console.log('logout....>> ' + sfw.util.Auth.isLoggedIn());

        view.removeAll();

        //TODO: move all logout related stuffs here.
        if (!sfw.util.Auth.isLoggedIn()) {
            me.redirectTo('login');
            window.location.reload();
        } else {
            me.launchLoginView();
        }
    },


    forecedLogout: function() {
        var me = this;
        var globals = sfw.Globals;
        var view = me.getView();
        var mask = new Ext.LoadMask({target: view, msg: 'Logging out..'});
        mask.show();
        Ext.Ajax.request( {
            url: 'action=logout'
            ,method: 'POST'
            ,success: function( data ) {
            sfw.util.Debug.log( 'Account logout complete' );
            // Clear the API credentials
            globals.clearAPIcredentials();
            if ( document.cookie ) {
                // Erase everything in the cookie
                document.cookie = 'uid=; expires=' + encodeURIComponent( new Date(0).toUTCString() );
            }
            mask.hide();
            me.logout();
            }
            ,failure: function() {
            mask.hide();
            sfw.util.Debug.log( 'Account logout failed' );
            // log the action on the server
            sfw.util.ActivityLog.log( 2, 1, "Username: " + sfw.util.Auth.LoginDetails.loginAccountUsername, 29, true );
            }
        } );
    },

    submitFormData: function() {
        // code to submit the data to the php handler on the server side
        // will get a success returned
        var me = this;
        Ext.Ajax.request({

            url:  'action=ajaxapi_reset_password'
            ,params: {
                ex_pwd_textfield: Ext.ComponentQuery.query('#existing_pwd')[0].getValue()
			    ,new_pwd_textfield: Ext.ComponentQuery.query('#new_pwd')[0].getValue()
			    ,new_pwd_conf_textfield: Ext.ComponentQuery.query('#new_pwd_conf' )[0].getValue()
            }
            ,waitMsg: 'Changing password...'
            ,success: function( data ) {
                var lResponse = Ext.decode(data.responseText).response;
                var lReason = Ext.decode(data.responseText).reason;
                var title = '';
                switch ( lResponse ) {
                case 0:
                    Ext.Msg.show({
                        title: 'Password changed'
                        ,msg: lReason + '<br><br>' +
                            'A verified email address and mobile number can be used if your password is lost.' +
                            ' Kindly verify your contact information using the Password Recovery Settings form.'
                        ,buttons: Ext.Msg.OK
                        ,icon: Ext.MessageBox.INFO
                    });
                    //Update the current Requirements Setup progress
                    try {
                        sfw.util.Auth.LoginDetails.isPasswordReset = true;
                    } catch ( ex ) {
                        sfw.debug.trigger( ex, 'submitFormData()' );
                    }
                    //Logging out after sucessful password reset
                    me.forecedLogout();
                    /*if ( "undefined" !== typeof(sfw.configuration.ui.home) && sfw.configuration.ui.home ) {
                        sfw.util.jumpTo( sfw.configuration.ui.home );
                    } else {
                        sfw.util.jumpTo('sfw.csiDashboard');
                    }
                    */
                    break;
                case 1:
                    Ext.Msg.show({
                        title: 'Password changed'
                        ,msg: lReason
                        ,buttons: Ext.Msg.OK
                        ,icon: Ext.MessageBox.INFO
                    });
                    //Update the current Requirements Setup progress
                    try {
                        sfw.util.Auth.LoginDetails.isPasswordReset = true;
                    } catch ( ex ) {
                        sfw.debug.trigger( ex, 'submitFormData()' );
                    }
                    //Logging out after sucessful password reset
                    me.forecedLogout();
                    /*
                    if ( "undefined" !== typeof(sfw.configuration.ui.home) && sfw.configuration.ui.home ) {
                        sfw.util.jumpTo( sfw.configuration.ui.home );
                    } else {
                        sfw.util.jumpTo('sfw.csiDashboard');
                    }
                    */
                    break;
                case 2:
                case 3:
                default:
                    Ext.Msg.show({
                        title: 'Error'
                        ,msg: lReason
                        ,buttons: Ext.Msg.OK
                        ,icon: Ext.MessageBox.INFO
                    });
                    break;
                }
                me.onResetClick();
            }
            ,failure: function() { Ext.Msg.alert('Error','Connection failed.'); }
        });
    },
    showPolicy: function( data ) {
        var display = false;

        var min_length = data[ 'min_length' ][ 'option_value' ];
        var min_changes = data[ 'min_changes' ][ 'option_value' ];
        var min_numerics = data[ 'min_numerics' ][ 'option_value' ];
        var max_days = data[ 'max_days' ][ 'option_value' ];
        var must_have_mixed_case = data[ 'must_have_mixed_case' ][ 'option_value' ];

        var policy = "<p>Password must confirm to the following policy:<br />";

        if ( 0 < min_length ) {
            policy += "- Be at least " + min_length + " characters long.<br />";
            display = true;
        }

        if ( 0 < min_numerics ) {
            policy += "- Contain at least " + min_numerics + " digit(s).<br />";
            display = true;
        }

        if ( 0 < must_have_mixed_case ) {
            policy += "- Contain at least one lower case, one upper case and at least one special character.<br />";
            display = true;
        }

        if ( 0 < min_changes ) {
            policy += "- One cannot reuse a password for at least " + min_changes + " change(s).<br />";
            display = true;
        }

        if ( 0 < max_days ) {
            policy += "- Passwords must be changed every " + max_days + " days.<br />";
            display = true;
        }
        policy += "<br /></p>";

        return [ display, policy ];
    },

    refresh: function() {
        var me = this;
        // There's no point in making this request if this is not the active page
        // This request will be made anyway when the page gets displayed. Moreover
        // it will break becuase of the call to update on a non rendered container.
/*        console.log('heeeeey');
        if ( sfw.util.activePage !== 'sfw.csiResetPassword' ) {
            return;
        }*/
        // @todo client side password validation (using Password Policy)
        Ext.Ajax.request({
            url: 'action=password_policy_configuration&which=get_options'
            ,method: 'POST'
            ,success: function( data ) {
                result = Ext.decode( data.responseText );
                if ( !result.success ) {
                    sfw.util.Debug.log( 'Error while trying to retrieve the Password Policy Configuration: ' + data.msg );
                    return;
                }
                var showData = me.showPolicy( result.data );
                if ( showData[0] ) {
                    Ext.ComponentQuery.query('#passwordPolicy')[0].setHtml( showData[1] );
                } else {
                    Ext.ComponentQuery.query('#passwordPolicy')[0].setHtml('');
                }
            }
            ,failure: function() {
                sfw.util.Debug.log( 'Error while trying to retrieve the Password Policy Configuration.' );
            }
        });
    },

});
