Ext.define('sfw.view.configuration.SoftwareSuggestion', {
    extend:'Ext.grid.Panel',
    xtype: 'sfw.csiSoftwareSuggestion',
    id: 'csiSoftwareSuggestion',

    title: 'Software Suggestions',
    border: 1,
    cls: 'shadow',
    stateful: true,
    layout: 'fit',
    scrollable: true,
    store: {
        type: 'softwaresuggestion'
    },

    selModel: {
        mode: 'MULTI'
    },

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No data'
    },

    controller: 'softwaresuggestion',

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'displayfield',
            value: '',
            hidden: false,
            itemId: 'averagettr'
        }, {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton',
        },
        ]
    }],


    columns: [
        {text: 'Name', dataIndex: 'file_name', flex: 1},
        {text: 'Version', dataIndex: 'version', flex: 1},
        {text: 'URL', dataIndex: 'url', flex: 1},
        {text: 'Email', dataIndex: 'email', flex: 1},
        {text: 'Status', dataIndex: 'status', flex: 1 , renderer : 'getStatus'},
        {text: 'Comment', dataIndex: 'comment', flex: 1},
        {text: 'Created', dataIndex: 'created_at', flex: 1, renderer: sfw.util.Default.gridRenderUTCDateInLocaltime},
    ],


    listeners: {
        beforerender:'getAverageTtr',
        itemcontextmenu: function (grid, record, item, index, e) {

            var LoginDetails = sfw.util.Auth.LoginDetails;

            var account_id = record.data.account_id;
            var disabled_status = false;

            var selected = grid.getSelectionModel().getSelected();
            var record_item = '';
            if (selected.items.length > 1) {
                additionalText = 'Delete All Selected Softwares';
                record_item = selected.items;
            } else {
                additionalText = 'Delete Software';
                record_item = record;
            }

            if( LoginDetails.loginAccountId != account_id ) {
                disabled_status = true;
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'softwaresuggestion',
                width: 165,
                disabled : disabled_status,
                hidden : LoginDetails.isReadOnly,
                plain: true,
                items: [{
                    text: additionalText,
                    listeners: {
                        click: {fn: 'deleteSuggestion', extra: record_item}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{softwaresuggestion}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Suggestions {0} - {1} of {2}',
        emptyMsg: "No Suggestions"
    }

});
