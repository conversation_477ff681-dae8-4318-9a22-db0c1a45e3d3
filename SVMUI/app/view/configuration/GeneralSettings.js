var SETTING_TIMESTAMP_URL1 = 1101;
sfw.csiSettings = {};

var LoginDetails = sfw.util.Globals.LoginDetails;

Ext.define('sfw.view.configuration.GeneralSettings', {
    extend: 'Ext.TabPanel',
    xtype: 'sfw.csiSettings',
    title: 'Settings',

    fullscreen: true,
    scrollable: "vertical",
    requires: [
        'sfw.view.configuration.GeneralSettingsController',
        'sfw.view.configuration.GeneralSettingsModel',
        'sfw.SharedConstants'
    ],

    controller: 'configuration-generalsettings',
    viewModel: {
        type: 'configuration-generalsettings'
    },
    //tabPosition: 'left',
    //tabRotation: 'default',


    defaults: {

        styleHtmlContent: true

    },


    items: [

        {
            xtype: 'panel',
            autoScroll: true,
            overflowY: true,
            autoWidth: true,

            title: 'General',

            items: [
                {
                    xtype: 'fieldset',
                    title: 'Live Update',
                    itemId: 'liveUpdateId',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>I want my scan results to be updated in real time as new Vulnerability Intelligence pertaining to my existing scan results emerges. I understand and accept that this is not a replacement for regular scheduled scanning, and could lead to my shown scan results not being the most accurate representation of the current state of my network. <a href="#" onclick="sfw.help.openPageId(\'csisettings_liveupdate\');">(?)</a></p>')
                    },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'liveUpdateRenderer'
                            },

                        }
                    ]
                },
                {
                    xtype: 'fieldset',
                    title: 'Collect Network Information',
                    itemId: 'collectNetworkInfoId',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>I want to allow the collection and storage of network information, such as assigned IPs and MAC addresses, when I scan devices.  <a href="#" onclick="sfw.help.openPageId(\'csisettings_networkinfo\');">(?)</a></p>')
                    },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'collectNetworkInfoRenderer'
                            }
                        }
                    ]
                },
                {
                    xtype: 'fieldset',
                    title: 'Zombie File Settings',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>This setting determines the behaviour of the scan engine for handling zombie files. If you choose to hide the zombie files, they will not be included in any of the scan results. If you choose to display zombie files, they will be displayed under the Zombie File Results tab. <a href="#" onclick="sfw.help.openPageId(\'csisettings_zombie\');">(?)</a></p>')
                    },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'zombiFileSettingsRenderer'
                            }
                        }]
                },
                {
                    xtype: 'fieldset',
                    title: 'Host Deletion Settings',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>I want the host and its scan data to be deleted automatically if the host is either not scanned or checked-in for more than the number of days configured in this setting. <a href="#" onclick="sfw.help.openPageId(\'csisettings_hosts_delete_configure\');">(?)</a></p>')
                    }, {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'hostDeletion'
                            }
                    }]
                },

                {
                    xtype: 'fieldset',
                    title: 'Flexera SPS Timestamp',
                    items: [{
                        listeners: {
                        beforerender: 'spsTimestampRenderer',
                    }
                    },
                    {
                        xtype: 'fieldcontainer',

                    listeners: {
                        beforerender: 'timestampComboRenderer',
                                change: 'enableSPSTimestamp'
                            }


        }],
                },
                {
                    xtype: 'fieldset',
                    title: 'Mask Paths That Show User Names',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>This setting (not applicable for SCCM imports) is used to ensure windows environment variable names are used instead of user name for the paths that contain profile names. <a href="#" onclick="sfw.help.openPageId(\'csisettings_identifiable_paths\');">(?)</a></p>')
                    },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'maskPathRenderer'
                            }
                        }
                    ]
                },
                {
                    xtype: 'fieldset',
                    title: 'Configure Agents',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>Configure Agents Status Polling</p>' +
                            '<p>After submitting scan to the server, agent polls the server to figure out if the processing is completed. ' +
                            'You can reduce the server traffic by stopping agent from polling. <a href="#" onclick="sfw.help.openPageId(\'csisettings_status_polling_api\');">(?)' +
                            '</a></p>')
                    },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'agentPollingRenderer'
                            }
                        },
                        { xtype: 'box', autoEl: {tag: 'hr'}},
                        {
                            xtype: 'label',
                            labelWidth: 120,
                            html: ('<p>Configure Site name for agents deployed with custom name</p>' +
                                '<p>Stop Site name update on agent check in.  ' +
                                '<a href="#" onclick="sfw.help.openPageId(\'csisettings_site_save_configure\');">(?)' +
                                '</a></p>')
                        },
                        {
                            xtype: 'fieldcontainer',
                            layout: 'hbox',
                            listeners: {
                                beforerender: 'siteStatusRenderer'
                            }
                        },
                        { xtype: 'box', autoEl: {tag: 'hr'}},

                        {
                            xtype: 'label',
                            labelWidth: 120,
                            html: ('<p>Java Assessment Settings</p>' +
                                '<p>I want to detect all instances of Java, ' +
                                'including those included with other applications which cannot be remediated by a Java patch. ' +
                                'By default, only standard installation directories are considered as only they can be directly patched.  ' +
                                'To patch vulnerable versions of Java embedded in other applications, such applications need to be patched versus trying to update Java directly. ' +
                                'If enabling detection anywhere Java is found, look to the detected paths to determine which can or cannot be patched directly.' +
                                '<a href="#" onclick="sfw.help.openPageId(\'csisettings_java_configure\');">(?)</a></p>')
                        },
                        {
                            xtype: 'radiogroup',
                            columns: 1,
                            vertical: true,
                            listeners: {
                                beforerender: 'javaAssessmentRenderer'
                            }
                        },
                    ]
                },{
                    xtype: 'fieldset',
                    title: 'Scan Exclusion Paths',
                    items: [
                        {
                            xtype: 'label',
                            labelWidth: 120,
                            html: ('<p>Below are the list of file paths or folders match added by default to exclude from scan result. Desired approach is to select all items in the list below. <a href="#" onclick="sfw.help.openPageId(\'sfw.csiExclusionList\');">(?)</a></p>')
                        },{
                            xtype: 'grid',
                            layout: 'fit',
                            selType: 'checkboxmodel',
                            viewConfig : {
                                deferEmptyText: false,
                                emptyText: 'No Paths Found'
                            },
                            store: {type: 'exclusionpathlist'},
                            itemId: 'exclusion_list_chkbx_col',
                            columns: [
                                {text: 'File path match', dataIndex: 'name', flex: 1},
                                {text: 'Regex', dataIndex: 'regex', flex: 1}
                            ]
                        },{
                            buttonAlign: 'left',
                            buttons: [
                                {
                                    text: 'Save',
                                    tooltip: {text: 'Save Exclusion List.'},
                                    ui: 'primary',
                                    align:'right',
                                    minWidth:45,
                                    listeners: {
                                        beforerender: function () {
                                            if (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                                                this.hide(true);
                                            }
                                        }
                                    },
                                    handler: "saveExclusionList"
                                }
                            ]
                        }
                    ]
                },{
                    xtype: 'fieldset',
                    title: 'System Score Settings',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>The System Score for a host is calculated based on the following attributes. The percentage assigned to each attribute will dictate its influence on the overall calculation of the system score. The sum of all weights cannot exceed 100. <a href="#" onclick="sfw.help.openPageId(\'sfw.csiSettings\');">(?)</a></p>')
                    }, {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        listeners: {
                            beforerender: 'systemScoreSettings'
                        }
                    }]
                }
            ]

        },

        {

            title: 'Windows Update',
            xtype: 'panel',
            autoScroll: true,
            overflowY: true,
            autoWidth: true,

            items: [
                {
                    xtype: 'fieldset',
                    title: 'Check For Microsoft Security Update Setting',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>When enabled, agent scans will collect and report Microsoft Security Updates. <a href="#" onclick="sfw.help.openPageId(\'csisettings_msupdate\');">(?)</a></p>')
                    },
                        {
                            xtype: 'radiogroup',
                            columns: 1,
                            vertical: true,
                            listeners: {
                                beforerender: 'enableMSSecurityRenderer',
                                activate: 'checkMSSecurityConfigUpdate'
                            },

                        }
                    ]
                },
                {
                    xtype: 'fieldset',
                    title: 'Windows Update Settings',
                    itemId: 'windowsUpdateSettings',
                    hidden: sfw.util.Auth.LoginDetails.isReadOnly,
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>Configure the behaviour of the Windows Update Agent (WUA). <a href="#" onclick="sfw.help.openPageId(\'csisettings_wua\');">(?)</a></p>')
                    },


                        {
                            xtype: 'radiogroup',
                            columns: 1,
                            vertical: true,
                            listeners: {
                                beforerender: 'windowsUpdateRenderer',
                                change: 'useManagedwindowsUpdateServer'
                            },
                        },
                        {
                            xtype: 'fieldcontainer',
                            listeners: {
                                beforerender: 'cabFileWindowsRenderer'
                            }
                        },
                        {
                            xtype: 'fieldcontainer',
                            listeners: {
                                beforerender: 'enableWMIRenderer',
                            }
                        },{
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                            margin: '10 10 10 0',
                        items:[
                            {
                                itemCls: 'toolbar-dock-left',
                                items:
                                [
                                    {
                            xtype: 'button',
                            ui: 'primary',
                            text: 'Clear'
                            , itemId: 'wua_clear',
                            align: 'left'
                            , disabled: sfw.util.Auth.LoginDetails.isReadOnly
                            , hidden: false
                            , tooltip: {
                                title: 'Clear'
                                , text: 'Clear all input fields in the above form.'
                            }
                            , handler: 'wuaClearHandler'
                        }
                                ]
                            },
                            {xtype: 'splitter'},
                            {
                                itemCls: 'toolbar-dock-right',
                                items:[
                                    {
                            xtype: 'button'
                            , ui: 'primary'
                            , disabled: sfw.util.Auth.LoginDetails.isReadOnly
                            , itemId: 'saveWindowsUpdatesSettingsButton'
                            , text: 'Save Windows Updates Settings'
                            , handler: 'saveWindowsUpdateSettingsHandler'
                        }
                                ]
                            }
                        ]},


                    ],

                },
                {
                    xtype: 'fieldset',
                    title: 'Windows Update Proxy Settings',
                    items: [{
                        xtype: 'label',
                        labelWidth: 120,
                        html: ('<p>Configure whether the Windows Update Agent uses a proxy server.</p>')
                    },
                        {
                            xtype: 'radiogroup',
                            columns: 1,
                            vertical: true,
                            listeners: {
                                beforerender: 'proxyRenderer'
                            }

                        },
                        {
                            xtype: 'fieldcontainer',
                            listeners: {
                                beforerender: 'proxyHostPortRender'
                            }
                        },
                        {xtype: 'splitter'},
                        {
                            xtype: 'button'
                            , ui: 'primary'
                            , disabled: sfw.util.Auth.LoginDetails.isReadOnly
                            , itemId: 'saveWindowsUpdateProxySettingsButton'
                            , text: 'Save Windows Update Agent Proxy Settings'
                            , align: 'left',
                            margin: '10 10 10 0',
                            handler: 'saveWUAgentSettingshandler'
                        }


                    ],

                }
            ]

        },
        {

            title: 'Single Sign-On',
            xtype: 'panel',
            itemId: 'ssoItemId',
            autoScroll: true,
            overflowY: true,
            autoWidth: true,
            items: [
                {
                    xtype: 'fieldset',
                    title: 'IDP Configuration Instructions',

                    items: [
                        {
                            xtype: 'label',
                            labelWidth: 120,
                            html: ('<p>Single Sign-On URL (Same with Destination URL and Recipient URL) <a href="#" onclick="sfw.help.openPageId(\'csisettings_SS0_setting\');">(?)</a></p>')
                        },
                        {
                            xtype: 'fieldcontainer',
                            listeners: {
                                beforerender: 'ssoURL',
                            },
                        },
                        {
                            xtype: 'label',
                            labelWidth: 120,
                            html: ('<p>Account key \n set the below value in your Identety Provider(IDP) a SAML attribute named "accountKey"</p>')
                        },
                        {
                            xtype: 'textfield'
                            , allowBlank: false
                            , itemId: 'account_key'
                            , name: ''
                            , value: '************'
                            , type: 'password'
                            , readOnly: true
                            , width: 500
                            , hideLabel: true
                            , margins: '0 0 5 0'

                        },
                        {
                            xtype: 'button',
                            ui: 'primary',
                            name: 'generate_key_textfield',
                            text: 'Generate Key',
                            align: 'left',
                            margin: '10 10 10 0',
                            handler: 'generateKey'
                        },
                        {
                            xtype: 'fieldcontainer',
                            listeners: {
                                beforerender: 'ssoMetadataURL',
                            },
                        },
                    ]
                },


                {
                    xtype: 'fieldset',
                    title: 'Service Provider Configuration',

                    items: [{
                        items: [{
                            xtype: 'checkbox',
                            name: 'sso_enabled',
                            itemId: 'is_sso_enabled',
                            boxLabel: 'SSO Enabled  <a href="#" onclick="sfw.help.openPageId(\'csisettings_SSO_configure\');">(?)</a>',
                            listeners: {beforerender: 'ssoEnabledCheck'},
                            handler: 'ssoEnabledHandler'
                        }]
                    }, {
                        itemId: 'sso_content',
                        hidden: true,
                        items: [

                            {
                                xtype: 'checkbox',
                                name: 'disable_standard_login',
                                itemId: 'is_standard_login_disabled',
                                inputValue: 1,
                                boxLabel: 'Disable Standard Login (Ensure SSO is working first, to prevent lockout)',
                                checked: false
                            },
                            {
                                xtype: 'panel'
                                , itemId: 'showPreviousXmlFileData'
                                , hidden: true
                                , html: ('<a href="#"><p><b>Show previous xml file data</b></p></a>'),
                                listeners: {
                                    afterrender: 'showIDPXMLMetadata'
                                }
                            },
                            {
                                xtype: 'radiogroup',
                                vertical: 'true',
                                columns: 1,
                                items: [{
                                    boxLabel: 'Provide IdP Metadata URL',
                                    name: 'idpMetadata',
                                    itemId: 'idp_meta_data_url',
                                    inputValue: 2,
                                    checked: true,
                                    handler: 'idpMetadataURLHandler'
                                },
                                    {
                                        boxLabel: 'Upload IdP Metadata XML file',
                                        name: 'idpMetadata',
                                        itemId: 'idp_meta_data_xml',
                                        inputValue: 1,
                                        handler: 'idpMetadataURLHandler'
                                    }]
                            },
                            {
                                xtype: 'textfield'
                                , allowBlank: false
                                , itemId: 'idpMetadataUrlVal'
                                , name: 'meta-id1'
                                , width: 500
                                , hidden: false
                            },
                            {
                                layout: 'hbox'
                                , itemId: 'idpMetadataXmlVal'
                                , width: 500
                                , text: "Information"
                                , hidden: true
                                , items: [
                                    {
                                        xtype: 'fileuploadfield',
                                        buttonText: 'Import XML'
                                        , buttonOnly: true
                                        , fileUpload: true,
                                        ui: 'primary'
                                        , itemId: 'import_button'
                                        , name: 'file',
                                        listeners: {
                                            afterrender: 'setXML'
                                            , change: 'fileselected'
                                        }
                                    },
                                    {
                                        xtype: 'label',
                                        itemId: 'xml_file_text_field',
                                        text: 'No file chosen',
                                        margins: '5 0 0 10'
                                    }

                                ]
                            },
                            {
                                xtype: 'checkbox'
                                , itemId: 'automatically_create_new_user'
                                , checked: false
                                , boxLabel: 'Automatically create new user'
                                , handler: 'createIDPUser'
                            },
                            {
                                align: 'stretch'
                                , itemId: 'selectedUserList'
                                , hidden: true
                                , items: [{
                                    align: 'stretch'
                                    , cls: 'ContentPadding'
                                    , html: (
                                        '<p><b>Copy permission from selected user</b></p>')
                                },
                                    {
                                        xtype: 'combobox',
                                        matchFieldWidth: false,
                                        width: 200,
                                        valueField: 'account_id',
                                        itemId: 'templateAccountId',
                                        displayField: 'user_info',
                                        editable: false,
                                        bind: {
                                            store: '{fetchUser}'
                                        }

                                    }
                                ]
                            },

                            {
                                xtype: 'button',
                                ui: 'primary',
                                name: 'save_config_button',
                                text: 'Save Service Configuration',
                                margin : '10 10 10 0',
                                handler: 'saveServiceProviderConfiguration'
                            }]
                    }
                    ]

                }],

        },
        {

            title: 'Email/SMS Recipients',
            xtype: 'panel',
            autoScroll: true,
            overflowY: true,
            autoWidth: true,

            items: [{
                xtype: 'fieldset',
                title: 'Default Recipient Settings',
                items: [{
                    xtype: 'label',
                    labelWidth: 120,
                    html: '<p>These settings define the default email/SMS recipient lists used throughout the Software Vulnerability Manager User Interface in various ways, including generating reports and configuring Smart Group notifications. Select email addresses/SMS numbers and click "Save" to update your default setting.</p>',
                }, {
                    xtype: 'label',
                    labelWidth: 120,
                    html: '<p>Note: When configuring a report or a notification, if desired, a user can provide a select recipient list to use other than the default ones defined here.</p>',
                }, {
                    xtype: 'reporting.emailRecipients',
                    listeners: {
                        beforerender: function () {
                            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                            const emailRecipientSelector = localItemSelector.createEmailRecipientSelector('100');
                            this.add(emailRecipientSelector);
                            this.down('#defaultRecipients').hide();
                            sfw.Default.getSelectorValues({
                                action: 'recipients',
                                which: 'get_selected_recipients',
                                module_id: 100,
                                method_id: 1,
                            }, emailRecipientSelector);
                        }
                    }
                },{
                    xtype:'tbspacer',
                    height:10
                },{
                    xtype: 'common.mobilerecipients',
                    listeners: {
                        beforerender: function () {
                            if (sfw.util.Auth.LoginDetails.isSMSEnabled) {
                                const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                                const mobileRecipientSelector = localItemSelector.createMobileSelector('100');
                                this.add(mobileRecipientSelector);
                                this.down('#defaultMobileRecipients').hide();
                                sfw.Default.getSelectorValues({
                                    action: 'recipients',
                                    which: 'get_selected_recipients',
                                    module_id: 100,
                                    method_id: 2,
                                }, mobileRecipientSelector);
                            } else {
                                this.hide();
                            }
                        }
                    }
                }],
            }],

            buttons: [
                {
                    text: 'Save',
                    tooltip: {text: 'Save default recipient lists.'},
                    ui: 'primary',
                    listeners: {
                        beforerender: function () {
                            if (sfw.util.Auth.LoginDetails.isReadOnly) {
                                this.setDisabled(true);
                            }
                        }
                    },
                    handler: "saveSelectedRecipientsSettings"
                }
            ]
        }


    ],

    listeners: {
        beforerender: function () {
            if (sfw.util.Auth.LoginDetails.isReadOnly) {
                Ext.ComponentQuery.query('#ssoItemId')[0].tab.setHidden(true);
                Ext.ComponentQuery.query('#wua_clear')[0].setDisabled(true);
                Ext.ComponentQuery.query('#saveWindowsUpdatesSettingsButton')[0].setDisabled(true);
                Ext.ComponentQuery.query('#saveWindowsUpdateProxySettingsButton')[0].setDisabled(true);
                Ext.ComponentQuery.query('#collectNetworkInfoId')[0].setHidden(true);
            }
            if (!sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                Ext.ComponentQuery.query('#collectNetworkInfoId')[0].setHidden(true);
            }
            if (sfw.util.Auth.LoginDetails.isPartitionAdmin == 0 || sfw.util.Auth.LoginDetails.account.sso_enable == 0){
                Ext.ComponentQuery.query('#ssoItemId')[0].tab.setHidden(true);
            }
            if (typeof sfw.util.Auth.LoginDetails.account.roleIds[sfw.ROLE_SCANNING] === "undefined"){
                Ext.ComponentQuery.query('#windowsUpdateSettings')[0].setHidden(true);
            }
            if (!sfw.util.Auth.LoginDetails.account.isAdmin){
                Ext.ComponentQuery.query('#liveUpdateId')[0].setHidden(true);
            }
        }
    },

});