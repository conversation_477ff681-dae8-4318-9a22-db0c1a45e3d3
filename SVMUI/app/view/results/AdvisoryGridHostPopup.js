Ext.define('sfw.view.results.AdvisoryGridHostPopup', {
    extend: "Ext.window.Window",
    width: 850,
    height: 550,
    bodyPadding: 5,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    config: {
        vulnId: ''
    },
    maximizable: true,

    controller: 'configuredsmartgroupsbasepanelcontroller',

    items: [{
        xtype: 'grid',
        layout: 'fit',

        store: {
            type: 'AdvisoryGridHostPopup'
        },
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No data'
        },

        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton'
            }]
        }],

        columns: [
            {text: 'Host', dataIndex: 'host', flex: 1}

        ],

        listeners: {
            //itemdblclick: sfw.Default.viewInstallationdblcick,
            beforerender:'vulnIdFetchHost'
        },

        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{AdvisoryGridHostPopup}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying Hosts Affected By Advisory {0} - {1} of {2}',
            emptyMsg: "No hosts"
        }

    }]

});

