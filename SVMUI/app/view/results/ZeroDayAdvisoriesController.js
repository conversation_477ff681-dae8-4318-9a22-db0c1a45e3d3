Ext.define('sfw.view.results.ZeroDayAdvisoriesController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.results-zerodayadvisories',

    dblClickHandler: function (){
        const me = this,
            grid = me.getView(),
            selection = grid.getSelection();
        sfw.Default.advisoryView(1, selection[0].data.vuln_id);
    },

    checkThreatScore: function (grid) {

        var columns = grid.down('headercontainer').getGridColumns();
        if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
            columns[4].destroy();
        }
    },

    dataTypeHandler: function () {
        var dType;
        var me = this,
            view = me.getView(),
            selectedValue = view.down('#zeroDayScopeData').getValue(),
            affectedInstall = view.down('#num_installationsId');
        if (selectedValue.zd_data === '1') {
            dType= 'current';
            affectedInstall.setHidden(false);
        } else {
            dType= 'historic';
            affectedInstall.setHidden(true);
        }
        var zerodayadvisories = Ext.getStore('zerodayadvisories');
        zerodayadvisories.load({
            params: {
                dataType: dType
            }
        });
    }
});
