Ext.define('sfw.view.results.Sites', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiSitesOverview',
    stateful: true,
    stateId: 'sfw_csiSitesOverview_grid',
    id: 'sitesGrid',
    controller : 'sites',
    store : { type : 'sites' },

    title: 'Sites',
    border: 1,
    cls: 'shadow',

    selModel: {
        mode: 'MULTI'
    },

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No sites to display'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: "combo",
            itemId: "platformComboBox",
            name: "platformComboBox",
            store: new Ext.data.SimpleStore({
                fields: ["value", "label"],
                data: [
                    [0, 'Show All Platforms'],
                    [21, 'Windows'],
                    [31, 'Config Manager'],
                    [11, 'Mac'],
                    [41, 'Red Hat Linux']
                ]
            }) ,
            fieldLabel: "",
            valueField: "value" ,
            displayField: "label" ,
            width: 180 ,
            mode: "local" ,
            editable: false,
            triggerAction: "all" ,
            selectOnFocus: false,
            emptyText: "Showing All Platforms",
            listeners: {
                change: 'reloadSitesGrid'
            }
        },
        { xtype: 'tbspacer', width: 8 },
        {
            xtype: 'textfield',
            emptyText: 'Search for site name....',
            id: 'site_name',
            listeners: {
                specialkey: 'handleSpecialKeysSites'
            }
        },
        {
            xtype: 'button',
            text: 'Search',
            ui: 'primary',
            handler: 'reloadSitesGrid'
        },
        {
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton',
        }]
    }],

    columns: [
        {text:'Site',dataIndex:'group_name',flex:3 },
        {text:'Hosts',dataIndex:'hosts',flex:1, align: 'right',xtype: 'numbercolumn', format: '000,000,000'},
        {text:'Average Score',dataIndex:'average_score',flex:1, align: 'right',renderer:'renderSSS'},
        {text:'Insecure Products',dataIndex:'num_insecure',flex:1, align: 'right',xtype: 'numbercolumn', format: '000,000,000'},
        {text:'End-Of-Life Products',dataIndex:'num_eol',flex:1, align: 'right',xtype: 'numbercolumn', format: '000,000,000'},
        {text:'Secure Products',dataIndex:'num_patched',flex:1, align: 'right',xtype: 'numbercolumn', format: '000,000,000'},
        {text:'Total Products',dataIndex:'num_installations',flex:1, align: 'right',xtype: 'numbercolumn', format: '000,000,000'}
    ],

    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {

            var selected = grid.getSelectionModel().getSelected();
            if(selected.items.length > 1)
            {
                additionalText = 'Delete all selected Sites';
            } else {
                additionalText = 'Delete Site';
            }
            var LoginDetails = sfw.util.Auth.LoginDetails;

            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'sites',
                width: 250,
                plain: true,
                items: [{
                    text: 'View Hosts in Site',
                    record: record,
                    handler: 'viewHostsInSite',
                    afterRender: function() {
                        if (selected.items.length > 1)
                        {
                            this.hide();
                        }
                    }
                }, {
                    text: additionalText,
                    hidden : LoginDetails.isReadOnly,
                    listeners: {
                        click: {fn: 'deleteSite', extra: selected.items}
                    }
                }]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'doubleClickHandlerSites'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind:{
            store:"{sitestsore}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Sites {0} - {1} of {2}',
        emptyMsg: "No Sites found."
    }

});
