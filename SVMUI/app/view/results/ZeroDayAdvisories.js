Ext.define('sfw.view.results.ZeroDayAdvisories', {
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiZeroDay',
    id: 'zerodayadvisories_grid',
    title: 'Zero-Day Advisories',
    border: 1,
    cls: 'shadow',
    store: {
        type: "zerodayadvisories"
    },
    stateful: true,
    stateId: 'sfw_csiZeroDay_grid',
    requires: [
        'sfw.view.results.ZeroDayAdvisoriesController',
        'sfw.store.results.ZeroDayAdvisories',
        'Flexera.widgets.SectorProgressBar'
    ],

    controller: 'results-zerodayadvisories',

    viewConfig : {
        deferEmptyText: false,
        emptyText: 'No Zero-Day Advisories Exist.'
    },

    dockedItems: [
        {
            xtype: "toolbar",
            dock: "top",
            items: [
                {
                    xtype: "displayfield",
                    value: 'Scope of Data: '
                },
                {
                    xtype: 'radiogroup',
                    columns: 2,
                    hideLabel: true,
                    itemId: 'zeroDayScopeData',
                    vertical: true,
                    layout: 'hbox',
                    listeners: {
                        change: 'dataTypeHandler'
                    },
                    items: [
                        {
                            boxLabel: '<span style="font-weight: normal;">Advisories that Affected You</span>',
                            name: 'zd_data',
                            width: 250,
                            inputValue: '1',
                            checked: true
                        },
                        {
                            boxLabel: '<span style="font-weight: normal;">All Advisories</span>',
                            name: 'zd_data',
                            inputValue: '2'
                        }
                    ]
                },
                {
                    xtype: "tbfill"
                },
                {
                    xtype: "exportButton"
                }
            ]
        }
    ],

    columns: [
        {
            dataIndex: 'vuln_id',
            text: 'Zero-Day SAID',
            align: 'left',
            sortable: true,
            renderer: sfw.Default.renderSaid
        },
        {
            dataIndex: 'vuln_title',
            text: 'Advisory Description',
            align: 'left',
            flex: 4,
            sortable: true
        },
        {
            xtype: 'widgetcolumn',
            dataIndex: 'vuln_criticality',
            text: 'Criticality',
            align: 'center',
            sortable: true,
            width: 90,
            widget: {
                xtype: 'sectorprogress',
                height: 8
            }
        },
        {
            dataIndex: 'vuln_create_date',
            text: 'Advisory Published',
            flex: 1,
            sortable: true,
            renderer: sfw.Default.renderSaidDate
        },
        {
            dataIndex: 'vuln_threat_score',
            text: 'Threat Score',
            align: 'right',
            flex: 1,
            sortable: true,
            renderer: sfw.Default.threatScoreDefault
        },
        {
            dataIndex: 'vulnerabilities',
            text: 'Vulnerabilities',
            align: 'right',
            flex: 1,
            sortable: true
        },
        {
            dataIndex: 'num_installations',
            text: 'Affected Installations',
            itemId: 'num_installationsId',
            align: 'right',
            flex: 1,
            sortable: true
        }
    ],

    listeners: {
        beforerender : 'checkThreatScore',
        itemdblclick: 'dblClickHandler'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        bind: {
            store: "{zerodayadvisories}"
        },
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying Zero-Day Advisories {0} - {1} of {2}',
        emptyMsg: "No Zero-Day Advisories Exist"
    }
});
