Ext.define('sfw.view.results.SitesController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.sites',

    requires: [
        'sfw.Default'
    ],

    reloadSitesGrid:function(){

        var sites = Ext.getStore('sitestsore');
        var view = this.getView();

        var platform_value = view.down('#platformComboBox').getValue();
        var search_text = view.down('#site_name').getValue();

        sites.getProxy().setExtraParams({
            'software_inspector_id': platform_value ,
            'search': search_text});

        sites.load();
    },

    handleSpecialKeysSites: function (field, e) {
        var me = this;
        if (e.getKey() == e.ENTER) {
            me.reloadSitesGrid();
        }
    },

    renderSSS : function( value ) {
        var score = parseInt(value, 10);
        return '<span style="color:'+ sfw.Default.getScoreColor( score ) + '">' + score + '%</span>';
    },

    doubleClickHandlerSites: function(view, record){
        var me = this,
            menuItem = { record: record };

        me.viewHostsInSite(menuItem);
    },

    viewHostsInSite: function (menuItem) {
        var me = this,
            record = menuItem.record,
            mainView = sfw.common.SfwAccessor.getMainView(),
            navBar = mainView.down('nav-bar'),
            navStore = navBar.getStore(),
            allHostNode;

        const hostSmartGroups = navStore.findRecord('id', 'sfw.csiHostSmartGroupsFolder');
        if (hostSmartGroups) {
            //allHostNode = hostSmartGroups.findChild('id', 'sfw.csiSmartGroup_host_1');
            allHostNode = hostSmartGroups.findChild('originalText', 'All Hosts');
        }

        if (allHostNode) {
            var routeUrl = allHostNode && (allHostNode.get('viewType') || allHostNode.get('routeId'));
            if (Ext.String.startsWith(routeUrl, 'sfw.csi', true)) {
                routeUrl = routeUrl.substring('sfw.csi'.length, routeUrl.length);
            }
            allHostNode.set('groupId', record.get('group_id'));
            me.redirectTo(routeUrl);
        }
    },

    deleteSite: function (event, target,options) {

        var selectionArray = [];
        for ( var i=0; i < options.extra.length; ++i ) {
            selectionArray.push( options.extra[i].data.group_id );
        }
        deleteIdList = selectionArray.join(",");

        if(options.extra.length > 1)
        {
            deleteMsg = "Are you sure you want to permanently delete the " + options.extra.length +  " selected Sites and all associated Hosts?";
        }
        else{
            var name = options.extra[0].data.group_name;
            deleteMsg = "Are you sure you want to remove the Site '" + name + "' and all hosts in it?";
        }
        var delParams = {
            delete_id_list: deleteIdList
        };
        Ext.MessageBox.confirm( 'Confirm Deletion', deleteMsg, function( button ) {
            if ( button === 'yes') {
                Ext.Ajax.request({
                    url: 'action=sites&which=deleteSites&',
                    method: 'POST',
                    dataType: 'json',
                    params: delParams,
                    success: function( response ) {
                        var status = Ext.util.JSON.decode( response.responseText );
                        if ( status.success ) {
                            Ext.getCmp('sitesGrid').getStore().reload();
                        } else {
                            switch ( status.error ) {
                                case 50: // NO_WRITE_PERMISSION
                                    Ext.Msg.alert( "Permission Denied", "You do not have the required permissions to delete Sites." );
                                    break;
                                default:
                                    Ext.Msg.alert( "Unexpected Error", "Unable to delete..." );
                            }
                        }
                    },
                    failure: function() {
                        Ext.Msg.alert( "Unexpected Error", "Unable to delete..." );
                    }
                });
            }
        });
    }

});
