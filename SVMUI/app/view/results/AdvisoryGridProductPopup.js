Ext.define('sfw.view.results.AdvisoryGridProductPopup', {
    extend: "Ext.window.Window",
    width: 850,
    height: 550,
    bodyPadding: 5,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    config: {
        vulnId: ''
    },
    maximizable: true,
    
    controller: 'configuredsmartgroupsbasepanelcontroller',


    items: [{
        xtype: 'grid',
        layout: 'fit',

        store: {
            type: 'AdvisoryGridProductPopup'
        },
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No data'
        },

        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'tbfill',
                flex: 1
            }, {
                xtype: 'exportButton'
            }]
        }],

        columns: [
            {text: 'Product', dataIndex: 'product_name', flex: 1}
        ],

        listeners: {
            itemdblclick: function (grid, record, item, index, e, eOpts) {
                var options = {};
                options.extra = record;
                sfw.Default.viewInstallation(e, e.target, options);
            },
            beforerender:'vulnIdFetchProduct'
        },

        bbar: {
            xtype: 'pagingtoolbar',
            bind: {
                store: "{AdvisoryGridProductPopup}"
            },
            region: 'south',
            displayInfo: true,
            displayMsg: 'Displaying Products Affected By Advisory {0} - {1} of {2}',
            emptyMsg: "No products"
        }

    }]

});

