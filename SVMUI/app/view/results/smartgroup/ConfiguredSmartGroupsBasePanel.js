Ext.define('sfw.view.results.smartgroup.ConfiguredSmartGroupsBasePanel', {
    extend: 'Ext.panel.Panel',
    alias: [
        'widget.configuredsmartgroupsbasepanel'
    ],
    controller: 'configuredsmartgroupsbasepanelcontroller',
    viewModel: {
        type: 'configuredsmartgroupsbasepanelmodel'
    },

    config: {
        smartGroupType: 'host', //host, product, advisory
        smartGroupName: '',
        smartGroupId: '',
        groupId: null,
        smartGroupData: null,
        smartGroupContentGrid: null
    },

    bind: {
        title: '{smartGroupTabName}'
    },

    tabConfig: {
        bind: {
            tooltip: '{sgTooltip}'
        }
    },

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    defaults: {
        border: false
    },

    refreshUI: function () {
        var me = this,
            vm = me.getViewModel();

        if (vm.get('dirty')) {
            vm.getStore('configuredSmartGroupStore').reload();
        }
        vm.set('dirty', false);
    },

    refresh: function () {
        var me = this,
            vm = me.getViewModel();

        vm.set('dirty', true);

        if (me.isVisible()) {
            me.refreshUI();
        }
        me.refreshADSpecific();
    },

    refreshADSpecific: function () {
        var me = this,
            vm = me.getViewModel(),
            treePanelContainer = me.down('treepanel');

        if (sfw.util.Auth.LoginDetails.isPartitionAdmin && treePanelContainer) {
            treePanelContainer.setVisible(vm.get('adActive'));
            //vm.set('adActive', null);
        }
    },

    initComponent: function () {
        var me = this,
            vm = me.getViewModel(),
            type = me.getSmartGroupType(),
            sgViewConfig = sfw.util.SmartGroupConfig;

        me.callParent(arguments);

        var viewConfig = sgViewConfig.getSmartGroupViewConfig(type),
            smartGroupData = me.getSmartGroupData();

        if (!Ext.isEmpty(smartGroupData)) {
            viewConfig.allCustomColumns = parseInt(smartGroupData.all_custom_columns, 10);
            viewConfig.customColumnData = smartGroupData.custom_columns;
        }

        vm.setData(viewConfig);
        vm.set('smartGroupName', me.getSmartGroupName());
        vm.set('smartGroupId', me.getSmartGroupId());
        vm.set('isDefaultGroup', me.getSmartGroupName() === 'All Hosts');

        var defaultSortColumn = type === 'host' ? 'host_name': (type === 'product' ? 'product_name' : 'vuln_title');
        vm.set('defaultSortColumn', defaultSortColumn);

        if (sgViewConfig.isSGOverviewUI(me.getSmartGroupType())) {
            Ext.defer(function () {
                me.setSmartGroupContentGrid(me.createOverviewGrid());
                me.add(me.getSmartGroupContentGrid());
            }, 300);
        } else {
            Ext.defer(function () {
                me.setSmartGroupContentGrid(me.createGrid());
                me.add(me.getSmartGroupContentGrid());

                if (me.canPopulateAD()) {
                    me.prepareADLayout(me); //TODO- Host AD tree
                }
            }, 50)
        }

        me.on('activate', me.onActivate, me, { buffer: 100 });
    },

    canPopulateAD: function() {
        const me = this,
            vm = me.getViewModel(),
            isAllHosts = vm.get('isDefaultGroup'),
            isHostType = vm.get('type') === sfw.util.CommonConstants.HOST[0],
            isMspUser = sfw.util.Auth.LoginDetails.account.isMspUser(),
            isPartitionAdmin = sfw.util.Auth.LoginDetails.isPartitionAdmin,
            hasADIntegration = sfw.ActiveDirectorySettings.hasADIntegration();

        if (isMspUser || !isHostType) {
            return false;
        }

        if (hasADIntegration) {
            return isAllHosts ? true : !isPartitionAdmin;
        }

        return false;
    },

    onActivate: function() {
        var me = this,
            vm = me.getViewModel(),
            isDefaultGroup = vm.get('isDefaultGroup'),
            gridStore = vm.getStore('configuredSmartGroupStore');

        if (isDefaultGroup && !Ext.isEmpty(me.getGroupId())) {
            Ext.defer(function () {
                me.down('combobox').setValue(me.getGroupId());
                gridStore.getProxy().setExtraParam('group_id', me.getGroupId());
                me.refresh();
                me.setGroupId(null);
            }, 300); //300 ms
        }
    },

    prepareADLayout: function (pagePanel) {
        var me = this,
            panel,
            vm = me.getViewModel();

        // We know we're in a Check to see if this is the default All Hosts smartgroup
        // and that we have AD
        if (sfw.ActiveDirectorySettings.hasADIntegration() || sfw.util.Auth.LoginDetails.isPartitionAdmin) {
            // The west id
            //this.adTreeId = this.id + "_adTree"; //TODO Wemerson to review
            // Add west to the panel only for All hosts and only when AD is activated
            // for non-partition admins
            panel = pagePanel.insert(0, {
                ui: 'light',
                border: 1,
                width: 250,
                collapsible: true,
                layout: "fit",
                split: true,
                style: {
                    backgroundColor: '#FFFFFF'
                },
                items: []
            });
            // Inject the tree in west
            me.injectADTree(panel);

            // Events only make sense for the partition admin
            if (sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                Ext.on("ad.off", function () {
                    vm.set('adActive', false);
                });
                Ext.on("ad.on", function () {
                    vm.set('adActive', true);
                });
                vm.set('adActive', sfw.ActiveDirectorySettings.hasADIntegration());
            }
        }
    },

    injectADTree: function (panel) {
        var me = this,
            vm = me.getViewModel(),
            adTree,
            refreshAdTree = function () {
                adTree.getStore().load(adTree.getRootNode());//TODO Wemerson to review
            };

        if (sfw.ActiveDirectorySettings.hasADIntegration()) {
            Ext.on("ad.scanfinished", refreshAdTree);
            Ext.on("ad.toggle_show_empty_ous", refreshAdTree);
        }

        Ext.on("ad.off", function () {
            refreshAdTree();
            Ext.un("ad.scanfinished", refreshAdTree);
            Ext.un("ad.toggle_show_empty_ous", refreshAdTree);
            vm.set('dirty', true);
            me.refresh();
        });

        Ext.on("ad.on", function () {
            refreshAdTree();
            Ext.on("ad.scanfinished", refreshAdTree);
            Ext.on("ad.toggle_show_empty_ous", refreshAdTree);
        });

        /**
         * All the AD tree functions now sit in here
         */
        adTree = Ext.create('Ext.tree.Panel', {
            scrollable: true,
            ui: 'primary',
            reference: 'adTree',
            contextMenu: Ext.create('Ext.menu.Menu', {
                items: [
                    {itemId: "refresh-menu", text: "Refresh"}
                ],
                listeners: {
                    click: function (menu, item, e, eOpts) {
                        // var selectedNode = item.parentMenu.contextNode;
                        var selectedNode = menu.contextNode;
                        switch (item.getItemId()) {
                            case "refresh-menu":
                            default:
                                adTree.getStore().getProxy().setExtraParam('targetNode', selectedNode.id);
                                // adTree.getStore().load(adTree.getRootNode());
                                adTree.getStore().load();
                                break;
                        }
                    }
                }
            }),
            store: {
                type: 'tree',
                listeners: {
                    load: function (store) {

                        //TODO Wemerson to review
                        if (typeof store.getProxy().getExtraParams['targetNode'] !== "undefined") {
                            /**
                             * Expand the root
                             */
                            adTree.getRootNode().expand();
                            /**
                             * After the tree data comes back from the server, erase any targetNode parameter
                             * that might have been used. All subsequent request made while the tree is already
                             * opened do not need that parameter set.
                             */
                            delete store.getProxy().extraParams.targetNode;
                        }
                    }
                },
                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        create: 'POST',
                        read: 'POST',
                        update: 'POST',
                        destroy: 'POST'
                    },
                    url: 'action=active_directory&which=getChildrenContainers&',
                    extraParams: {
                        includeNonAdNodes: true
                    }
                },
                root: {
                    text: "All computers",
                    //iconCls: "adtree-all-computers", //TODO Wemerson to review
                    id: "0" // The id is always a string
                },
                // folderSort: true,
                // sorters: [{
                //     property: 'text',
                //     direction: 'ASC'
                // }]
            },
            // ,id: this.adTreeId + "_tree",

            listeners: {
                itemclick: function (tree, record, item, index, e, eOpts) {
                    vm.getStore('configuredSmartGroupStore').getProxy().setExtraParam('group_id', record.id);
                    vm.set('dirty', true);
                    me.refresh();
                },
                itemcontextmenu: function (tree, record, item, index, e, eOpts) {
                    e.stopEvent();
                    // tree.select(record); //TODO Wemerson to review
                    var menu = tree.ownerCt.contextMenu;
                    menu.contextNode = record;
                    menu.showAt(e.getXY());
                }
            }
        });

        panel.add(adTree);
    },

    createGrid: function (gridConfig) {
        var me = this,
            allXGroupTopbar = me.buildFilterToolbar(),
            vm = me.getViewModel();

        return Ext.create({
            xtype: 'flexera-abstractgrid',
            smartgroupStateId : me.routeId,
            //id: this.id + '_smartGroupContentGrid',//TODO why this?
            // autoExpandColumn: me.getExpandColumn(), TODO Wemerson to Review
            // autoExpandMax: 4000, TODo Wemerson to review
            secuniaSorting: true,
            jsonSorters: true,
            cls: 'smart-group-grid-cls',
            //defaultSorters: [{field: vm.get('expandColumn'), direction: 'ASC'}],
            secuniaExportable: {
                getFromStore: true
            },
            stateful: true,
            selModel: {
                mode: 'MULTI'
            },
            itemId:'configuredGrid',
            title:'',
            header: false,
            listeners: {
                itemdblclick: 'doubleClickOption',
                itemcontextmenu: 'itemContextMenu'
            },

            viewConfig: {
                forceFit: true,
                emptyText: 'No ' + vm.get('typePlural') + ' in this Smart Group',
                deferEmptyText: false
            },
            //region: 'center',
            flex: 1,//FIXME
            bind: {
                store: '{configuredSmartGroupStore}'
            },
            // selModel: selModel, //TODO Wemerson to review
            columns: me.buildColumns(),
            dockedItems: [{
                xtype: 'pagingtoolbar',
                dock: 'bottom',
                bind: {
                    store: '{configuredSmartGroupStore}'
                },
                displayInfo: true,
                displayMsg: 'Displaying Smart Group'+"'"+'s ' + vm.get('typeUpperPlural')+ ' {0} - {1} of {2}',
                emptyMsg: 'No ' + vm.get('typeUpperPlural') + ' Found'
            }, allXGroupTopbar]
        });
    },

    createOverviewGrid: function (gridConfig) {
        var me = this,
            allXGroupTopbar = me.buildOverviewToolbar(),
            vm = me.getViewModel();

        return Ext.create({
            xtype: 'flexera-abstractgrid',
            smartgroupStateId : me.routeId,
            listeners: {
                itemdblclick: 'overviewItemDoubleClick',
                selectionchange: 'onSelectionChange',
                itemcontextmenu: 'onRowContextMenu'
            },
            itemId:'sgOverViewGrid',
            title:'',
            header: false,
            viewConfig: {
                forceFit: true,
                deferEmptyText: false,
                emptyText: 'No ' + vm.get('typeUpper') + ' Smart Groups available',
            },
            selModel: {
                selType: 'checkboxmodel',
                mode: 'MULTI'
            },
            flex: 1,
            bind: {
                store: '{smartGroupOverviewStore}'
            },
            columns: me.buildOverviewGridColumns(),
            dockedItems: [{
                xtype: 'pagingtoolbar',
                dock: 'bottom',
                bind: {
                    store: '{smartGroupOverviewStore}'
                },
                displayInfo: true,
                displayMsg: 'Displaying ' + vm.get('typeUpper')+ ' Smart Groups {0} - {1} of {2}',
                emptyMsg: 'No ' + vm.get('typeUpper')+ ' Smart Group Configured.'
            }, allXGroupTopbar]
        });
    },

    buildColumns: function () {
        var me = this,
            vm = me.getViewModel(),
            potentialColumns = vm.get('potentialColumns'),
            // Note - we passed in the configuredBaseFields which always
            // have the main id that we we don't display in the grid, but
            // it must be in by default, rather than be part of the
            // potentialColumns array.
            fields = vm.get('configuredBaseFields'),
            columns = [],
            thisId, index, record,
            // We must construct the fields/column data using the
            // 'this.customColumnData' we got from the backend for this
            // smartgroup, unless we 'selectedAll'.
            columnsIdsToInclude = [], i;

        vm.set('searchTerm', null);
        vm.set('selectedValue', 1);

        if (vm.get('allCustomColumns')) {
            for (i = 0; i < potentialColumns.length; ++i) {
                record = potentialColumns[i];
                if (false === sfw.util.Auth.LoginDetails.isThreatModuleEnabled && record.id == 'vuln_threat_score') {
                    continue;
                }
                columnsIdsToInclude.push(record.id);
            }
        } else {
            columnsIdsToInclude = vm.get('customColumnData').split(',');
        }

        // Deal with the 'alwaysGet' feature we included for renderer
        // dependency. If alwaysGet is true for a field, and it is not
        // in the chosen list, add it to the store fields we retreive.
        // We can skip this if allCustomColumns is set.
        for (i = 0; !vm.get('allCustomColumns') && i < potentialColumns.length; ++i) {
            record = potentialColumns[i];
            if (record.alwaysGet && -1 === columnsIdsToInclude.indexOf(record.id)) {
                fields.push({name: record.id, type: record.type});
            }
        }

        // Now add the requested fields to both 'fields' and 'columns'
        for (i = 0; i < columnsIdsToInclude.length; ++i) {
            thisId = columnsIdsToInclude[i];

            // Get the index (if exists) - we need the below since we
            // are digging one level deep into an array of objects
            index = potentialColumns.map(
                function (x) {
                    return x.id;
                }).indexOf(thisId);

            if (-1 !== index) {
                // Then the item at position 'index' is a desired (and valid) column
                record = potentialColumns[index];
                fields.push({name: record.id, type: record.type});

                columns.push(Ext.apply({
                    header: record.displayName,
                    // id: record.id,//TODO Wemerson to Review, why id?
                    dataIndex: record.id,
                    flex: 1, //TODO- fixme
                    align: record.align === 'right'? 'right' : record.align === 'center'? 'center': 'left',
                    sortable: record.sortable ? record.sortable : true,
                    width: record.width ? record.width : 'auto'
                }, record.widgetConfig));


                //TODO Wemerson to review NEXT phase
                if (record.renderer) {
                    // TODO - for some reason, some renderers don't work...
                    columns[i].renderer = record.renderer;
                }
            }
        }
        vm.getStore('configuredSmartGroupStore').setFields(fields);
        vm.getStore('configuredSmartGroupStore').load()

        return columns
    },

    buildOverviewGridColumns: function () {
        var me = this,
            vm = me.getViewModel(),
            columns = vm.get('columns'),
            fields = vm.get('fields');

        Ext.each(columns, function (column) {
            column.flex = 1;
        });

        vm.getStore('smartGroupOverviewStore').setFields(fields);
        vm.getStore('smartGroupOverviewStore').load()

        return columns
    },

    createSitesCombo: function () {
        var me = this,
            vm = me.getViewModel(),
            fields,
            sitesCombo;

        if (vm.get('type') == sfw.util.CommonConstants.HOST[0]
            && !sfw.ActiveDirectorySettings.hasADIntegration()
        ) {
            fields = [
                {name: 'group_id', type: 'int'},
                {name: 'group_name', type: 'string'}
            ];

            sitesCombo = Ext.create({
                xtype: 'combobox',
                store: {
                    fields: fields,
                    pageSize: 0,
                    proxy: {
                        type: 'ajax',
                        url: 'action=smart_groups&which=siteList&start=0&limit=500',//TODO  Wemerson to review
                        method: 'GET',
                        reader: {
                            type: 'json',
                            rootProperty: 'data.rows'
                        }
                    },
                    listeners: {
                        load: function (store) {
                            var firstRecord = { group_id: 0, group_name: 'Showing All Sites' };
                            store.insert(0, firstRecord);
                            // First, add a "blank" entry at the start for "All Sites"
                            // var firstRecord = new store.recordType({group_id: 0, group_name: 'Showing All Sites'});
                            // store.insert(0, firstRecord);
                        }
                    }
                },
                updatedCombo: true,
                width: 150,
                valueField: 'group_id',
                displayField: 'group_name',
                mode: 'remote',
                // allowBlank: allowBlank,
                editable: false,
                triggerAction: 'all',
                emptyText: 'Showing All Sites',
            });


            // Add logic specific to the partition admin with respect
            // to this combo
            if (sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                // If AD is already on, disable
                if (sfw.ActiveDirectorySettings.hasADIntegration()) {
                    sitesCombo.disable();
                }

                // If we toggle AD, respond appropriately
                Ext.on("ad.off", function () {
                    sitesCombo.enable();
                });
                Ext.on("ad.on", function () {
                    sitesCombo.disable();
                });
            }
        }
        return sitesCombo;
    },

    buildFilterToolbar: function () {
        var me = this,
            vm = me.getViewModel(),
            items = [],
            searchField,
            isDefaultGroup = vm.get('isDefaultGroup'),
            gridStore = vm.getStore('configuredSmartGroupStore');

        me.sitesCombo = me.createSitesCombo();
        me.platformComboBox = sfw.util.Default.createPlatformSelector(vm.get('configuredFolder'));

        // We only create the Sites and Platform ComboBoxes if we are in the "ALL_X"
        // non-editable default group

        // Construct the Platform Combo box, and the dynamic sites
        // combobox. The platform box potentially goes in all
        // pages. The sites selector only goes in All Hosts.

        if (isDefaultGroup) {
            if ('undefined' !== typeof me.sitesCombo) {
                items.push(me.sitesCombo);
                me.sitesCombo.on('select', function (combo, record) {
                    gridStore.getProxy().setExtraParam('group_id', record.get("group_id"));
                    me.refresh();
                }, me);
            }
            if ('undefined' !== typeof me.platformComboBox && vm.get('type') != 'advisory') {
                items.push(me.platformComboBox);
                me.platformComboBox.on('select', function (combo, record) {
                    gridStore.getProxy().setExtraParam('software_inspector_id', record.id);
                    me.refresh();
                }, me);
            }
        }

        //added filter for VPM/SPS for All products grid.
        if (vm.get('type') === sfw.util.CommonConstants.PRODUCT[0]) {
            items.push({
                xtype: "combobox",
                emptyText: "Showing All Packages",
                name: "filterVpmSps",
                store: {
                    fields: ["value", "label"],
                    data: [
                        ["All", "Show All Packages"],
                        ["SPS", "SPS Package"],
                        ["VPM", "VPM Package"]
                    ]
                },
                //fieldLabel: "Showing All VPM/SPS",
                valueField: "value",
                itemId: 'smartgroupcombo',
                value: "All",
                displayField: "label",
                width: 150,
                mode: "local",
                editable: false,
                triggerAction: "all",
                selectOnFocus: false,
                listeners: {
                    select: function (combo) {
                        gridStore.getProxy().setExtraParam('package', combo.getValue());
                        vm.set('package', combo.getValue());
                        //TODO Wemerson review
                        // sfw.csiSmartGroupContentTemplate.package = combo.getValue();
                        me.refresh();
                    }
                }
            });
        }

        // Add a Search field for Product and Host Smart Groups
        if (vm.get('type') === sfw.util.CommonConstants.HOST[0]
            || vm.get('type') === sfw.util.CommonConstants.PRODUCT[0]
            || vm.get('type') === sfw.util.CommonConstants.ADVISORY[0]) {
            searchField = Ext.create({
                xtype: 'textfield',
                reference: 'searchField',
                listeners: {
                    specialkey: function (field, e) {
                        if (e.ENTER == e.getKey()) {
                            vm.set('searchTerm', field.getValue());
                            gridStore.loadPage(1);
                        }
                    }
                }
            });

            if (vm.get('type') === sfw.util.CommonConstants.ADVISORY[0]) {
                items.push({
                    xtype: "combobox",
                    //id: "searchTypeAdvisories",
                    name: "searchTypeAdvisories",
                    store: {
                        fields: ["value", "label"],
                        data: [
                            ["1", "CVE"],
                            ["2", "SAID"],
                            ["3", "Description"]
                        ]
                    },
                    fieldLabel: "Search Type",
                    labelWidth: 80,
                    valueField: "value",
                    value: "1",
                    displayField: "label",
                    width: 220,
                    mode: "local",
                    editable: false,
                    triggerAction: "all",
                    selectOnFocus: false,
                    listeners: {
                        select: function (combo) {
                            vm.set('selectedValue', combo.getValue());
                        }
                    }
                });
            }
            items.push(searchField);
            items.push({
                xtype: 'button',
                text: 'Search',
                handler: function () {
                    vm.set('searchTerm', searchField.getValue());
                    if (vm.get('type') === sfw.util.CommonConstants.ADVISORY[0]) {
                        vm.set('searchType', vm.get('selectedValue'));
                    }
                    gridStore.loadPage(1);
                }
            });

            //Last Compiled time
            items.push({ xtype: 'tbspacer', width: 10 });
            items.push({
                xtype: 'displayfield',
                //fieldLabel: 'Last Compiled:',
                hidden: true,
                itemId: 'lastCompiledId'
            });

            items.push('->');
            items.push({
                xtype:'exportButton'
            });
        }

        return Ext.create({
            xtype: 'toolbar',
            dock: 'top',
            items: items
        });
    },

    buildOverviewToolbar: function () {
        var me = this,
            vm = me.getViewModel(),
            items = [];

        items.push({
            xtype: 'button',
            ui: 'primary',
            text: 'Create New Smart Group',
            disabled: sfw.util.Auth.LoginDetails.isReadOnly,
            handler: 'createNewSmartGroup'
        });

        items.push({
            xtype: 'button',
            ui: 'primary',
            text: 'Queue For Compilation',
            disabled: true,
            itemId: 'queueToRecompileId',
            handler: 'queueToRecompile'
        });

        items.push({
            xtype: 'tbfill',
            flex: 1
        });

        items.push({
            xtype: 'exportButton'
        });

        return Ext.create({
            xtype: 'toolbar',
            dock: 'top',
            items: items
        });
    }
});