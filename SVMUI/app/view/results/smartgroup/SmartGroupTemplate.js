Ext.define('sfw.view.results.smartgroup.SmartGroupTemplate', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.smartGroupTemplateWindow',
    width: 1000,
    height: 700,
    bodyPadding: 10,
    scrollable: "vertical",
    modal: true,

    title:'View/Edit Smart Group',

    viewModel:{
        data:{
            templateData:[]
        },
        formulas:{
            show:function(get){
                console.log(get('templateData'));
            }
        }
    },

    defaults: {
        style: {
            border: '1px solid #d0d0d0',
            padding: '10px',
            marginBottom: '10px'
        },
        autoScroll: false,
        border: true,
        defaultBindProperty: 'data'
    },

    defaultType: 'container',

    items:[{
        bind:{
            data:'{templateData}'
        },
        tpl: [
            '<tpl for=".">',
            '<p><b>Use case {[xindex]} - {title}</b></p>',
            '<p>{description}<p>',
            '<p>{critDetails}<p>',
            '<button class="ProductTemplateButton" title="Create a Product Smart Group based on this Use Case" onClick="sfw.Default.productSGEditemplate(\'{title}\',\'{description}\',\'{rules}\')">Use Template</button>',
            '<tpl if="xindex !== xcount">',
            '<hr>',
            '</tpl>',
            '</tpl>'
        ]
    }],

    buttons: [
        '->',
        {
            text: 'Close',
            ui : 'primary',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]
});