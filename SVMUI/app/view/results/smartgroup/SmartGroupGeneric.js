Ext.define('sfw.view.results.smartgroup.SmartGroupGeneric', {
    extend: 'Ext.window.Window',
    xtype: 'smartGroupGeneric',

    baseValue: '',

    modal: true,
    layout: 'fit',
    scrollable:'vertical',
    items: [
        {
            xtype: 'panel',
            itemId: 'mainDefinitionPanel',
            padding: '10px 10px 10px',
            layout: 'auto',
            autoScroll: true,
            border: false,
            items: [
                {
                    xtype: 'form',
                    padding: 5,
                    border: false,
                    layoutConfig: {
                        pack: 'center',
                        align: 'middle'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            allowBlank: false,
                            fieldLabel: 'Smart Group Name',
                            itemId: 'smartGroupName',
                            listeners: {
                                change: function( field, newValue, oldValue, eOpts ) {
                                    if ( '' !== field.getValue() ) {
                                        /*const smartGroupsController = sfw.app.getController("sfw.controller.SmartGroups");
                                        smartGroupsController.smartGroupSaveButtonControl();*/
                                    } else {
                                        // Ext.getCmp('#smartGroupName').disable();
                                        Ext.ComponentQuery.query('smartGroupGeneric #smartGroupsSaveButton')[0].disable();
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'textarea',
                            autoHeight: true,
                            itemId: 'smartGroupDescription',
                            anchor: '100%',
                            emptyText: 'Enter an (optional) description for this Smart Group...',
                            fieldLabel: 'Description'
                        },
                        {
                            xtype: 'combobox',
                            margin: '0 8 0 8',
                            itemId: 'smartGroupBusinessImpact',
                            mode: 'local',
                            width: 80,
                            anchor:'30%',
                            editable: false,
                            triggerAction: 'all',
                            forceSelection: true,
                            autoSelect: true,
                            store: Ext.create('sfw.store.results.BusinessImpact'),
                            valueField: 'id',
                            displayField: 'type',
                            value: 1,
                            fieldLabel: 'Business Impact'
                        },
                        {
                            xtype: 'panel',
                            layout: {
                                type: 'hbox'
                            },
                            anchor: '0',
                            padding:'5',
                            flex: 1,
                            items: [
                                {
                                    xtype: 'label',
                                    itemId: 'containsMatch',
                                    text: ' Contains that match',
                                    margin: '5 0 0 0'
                                },
                                {
                                    xtype: 'combobox',
                                    margin: '0 8 0 8',
                                    width: 81,
                                    itemId: 'smartGroupContainsMatchAndOr',
                                    mode: 'local',
                                    editable: false,
                                    triggerAction: 'all',
                                    forceSelection: true,
                                    autoSelect: true,
                                    store: new Ext.data.Store({
                                        fields: [ 'id', 'type' ],
                                        data: [
                                            {
                                                id: 1,
                                                type: 'all'
                                            },
                                            {
                                                id: 2,
                                                type: 'any'
                                            }
                                        ]
                                    }),
                                    valueField: 'id',
                                    displayField: 'type',
                                    value: 1,
                                    hideLabel: true
                                },
                                {
                                    xtype: 'label',
                                    text: 'of the following criteria:',
                                    margin: '5 0 0 0'
                                }
                            ]
                        },
                        
                    ],
                    keys: [{ // Handle form ENTER key press
                        key: [ Ext.event.Event.ENTER ],
                        handler: function ( keyCode, eventObject ) {
                            // Don't fire the save handler if ENTER was
                            // pressed on a button or in a textarea
                            var tagName = eventObject.getTarget().tagName;
                            if ( 'TEXTAREA' !== tagName && 'BUTTON' !== tagName ) {
                                // Call save button handler,and be
                                // sure to pass it the button
                                // itself as parameter
                                var saveButton = Ext.getCmp( self.id + '_smartGroupsSaveButton' );
                                saveButton.handler( saveButton );
                            }
                        }
                    }]
                },
                {
                    xtype: 'fieldset',
                    itemId: 'criteriaPanel',
                    name: 'criteriaPanel',
                    title: 'Criteria',
                    collapsible: false
                },
                {
                    xtype: 'fieldset',
                    height: 'auto',
                    width: 'auto',
                    title: 'Customize Columns',
                    collapsible: false,
                    items: [
                        {
                            xtype: 'panel',
                            itemId: 'additionalColumnLabel',
                            html: ' Use this form to control which additional columns are shown in the grid view. Mouseover a checkbox for the column description.<br><br>',
                            border: false,
                            cls: 'ContentPadding'
                        },
                        {
                            xtype: 'radiogroup',
                            itemId: 'colSelectionRadioGroup',
                            hideLabel: true,
                            width: 300,
                            columns: 2,
                            allowBlank: false,
                            items: [
                                {
                                    xtype: 'radiofield',
                                    boxLabel: 'Select All',
                                    itemId: 'selectAll',
                                    name: 'sg_radio_customCol',
                                    checked: true
                                },
                                {
                                    xtype: 'radiofield',
                                    boxLabel: 'Select Custom',
                                    itemId: 'selectCustom',
                                    name: 'sg_radio_customCol',
                                    checked: false
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            layout: 'table',
                            itemId: 'customColumnsPanel',
                            border: false,
                            disabled: true,
                            padding: '0px 0px 10px 5px',
                            cls: 'ContentPadding',
                            layoutConfig: {
                                tableAttrs: {
                                    style: {
                                        width: '100%' // space them out to use the whole window available
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        }
    ],
    closeAction: 'hide',
    buttonAlign: 'left',
    buttons: [
        // {
        //     xtype: 'button',
        //     text: 'Templates',
        //     tooltip: 'Templates with description',
        //     // id: this.id + '_templatesButton',
        //     // hidden: this.templateExamples.length ? false : true,
        //     disabled: false,
        //     handler: function () {
        //         self.helpWindow.show();
        //     }
        // },
        '->',
        {
            xtype: 'button',
            text: 'Save',
            itemId: 'smartGroupsSaveButton',
            tooltip: 'Save Smart Group',
            disabled: true, // disable by default - we will enable it as appropriate
            handler: function ( button ) {
                // Make sure button is enabled before we do anything
                /*const smartGroupsController = sfw.app.getController("sfw.controller.SmartGroups");
                if ( !button.disabled ) {
                    smartGroupsController.saveSmartGroup();
                }*/
            }
        },
        {
            xtype: 'button',
            text: 'Close',
            itemId: 'closeButton',
            tooltip: 'Close window',
            disabled: false,
            handler: function (self) {
                self.up("smartGroupGeneric").hide()
            }
        }
    ]

    
    
});