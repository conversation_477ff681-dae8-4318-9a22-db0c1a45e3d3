Ext.define('sfw.view.results.smartgroup.ConfiguredSmartGroupsBasePanelController', {
    extend: 'Ext.app.ViewController',
    alias: [
        'controller.configuredsmartgroupsbasepanelcontroller'
    ],
    // listen: {
    //     // Global events are always firedfrom the same object, so there are no selectors
    //     global: {
    //         'ad.off': 'onAdOff',
    //         'ad.on': 'onAddOn',
    //     }
    // },

    onSGStoreBeforeLoad: function (store, params) {
        var me = this,
            vm = me.getViewModel(),
            proxy = store.getProxy();

        switch (vm.get('type')) {
            case sfw.util.CommonConstants.HOST[0]:
                proxy.setExtraParam('host_name', vm.get('searchTerm'));
                break;
            case sfw.util.CommonConstants.PRODUCT[0]:
                proxy.setExtraParam('product_name', vm.get('searchTerm'));
                break;
            case sfw.util.CommonConstants.ADVISORY[0]:
                //store.setBaseParam( 'ref_value', self.searchTerm );
                proxy.setExtraParam('searchText', vm.get('searchTerm'));
                proxy.setExtraParam('searchType', vm.get('selectedValue'));
                break;
        }
    },

    onSGStoreLoad: function (store, records, successful, operation) {
        var view = this.getView(),
            lastCompiled = view.down('#lastCompiledId');
            vm= view.getViewModel();
            configuredgrid = view.down('#configuredGrid');

        if (lastCompiled) {
            var message = (operation.getResultSet() || {}).message;
            var formattedTime = sfw.util.Default.gridRenderUTCDateInLocaltimeFormat(message, sfw.util.Globals.dateLongOutput);
            configuredgrid.setTitle('Smart Group: '+ '"'+vm.data.smartGroupName+'" - Last Compiled: '+sfw.util.Default.gridRenderUTCDateInLocaltimeFormat(message,sfw.util.Globals.dateLongInput));
            //var outputStr = sfw.util.Util.differenceBetweenDates(formattedTime, sfw.util.Util.dateCreate(), 0);
            lastCompiled.setValue('Last Compiled: ' + formattedTime);
            lastCompiled.setHidden(false);
        }

        // Reload the sites store (if it exists) or it
        // will never get updated from the initial
        // creation/load of it
        if ('undefined' !== typeof view.sitesCombo) {
            view.sitesCombo.getStore().load({
                callback: function () {
                    view.sitesCombo.collapse();
                    // While in here, set the value
                    // explicitly if we're in the '0'
                    // default case that for some reason
                    // doesn't render well...
                    if (!view.sitesCombo.getValue()) {
                        view.sitesCombo.setValue(0);
                    }
                }
            });
        }

    },
    removeHosts : function (item, target){
        var me = this;
        var selectionArray =[];
        for (var i=0; i<item.extra.length; ++i){
            selectionArray.push(item.extra[i].data.nsi_device_id);

        }
        //console.log(options);
        var hostName = item.extra[0].data.host_name;
        var msg = 'Are you sure you want to delete the host: ' + hostName + ' and all associated data?';
        if (item.extra.length>1){
            msg = 'Are you sure you want to delete the '+ selectionArray.length + ' selected Hosts and all associated data?';
        }

        Ext.Msg.show({
            title:'Confirm Deletion',
            message: msg,
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    me.deleteHosts([selectionArray], function( err, data ) {
										if ( err !== null || !data.success ) {
											Ext.Msg.alert( "Error", "Unable to delete all selected hosts");
											return;
										}
										me.getViewModel().getStore('configuredSmartGroupStore').load();
                });
                }
            }
        });
    },

    deleteHosts: function( hosts, callback ) {
        var me =this;
		Ext.Ajax.request( {
			 url: "action=ajaxapi_delete_host&hostsArr=" + hosts.join()
			,success: function( response ) {
			 	/**
				 * After delete host recompile host group to reflect changes
				 */

				 Ext.Ajax.request( {
					 url: 'action=smart_groups&which=overview&smartGroupTextType=host',
					 method: 'GET'
					,success: function( response ) {
						var hostgroups = Ext.util.JSON.decode( response.responseText);
						var smartGroupIds = [];
						Ext.each( hostgroups.data.rows, function ( grp ) {
							smartGroupIds.push( grp.id ) ;
						} );
						me.recompile( { smartGroupIds: smartGroupIds } );
					}
					,failure: function( response ) {
						Ext.Msg.alert('Failure', 'Not able to compile host smart group please try again...');
					}
				});
				callback( null, Ext.util.JSON.decode( response.responseText ) );
			}
			,failure: function( response ) {
				callback( Ext.util.JSON.decode( response.responseText ) );
			}
		});
	},

    itemContextMenu: function (grid, record, item, index, e, eOpts) {
       var me = this;
       vm = me.getViewModel();

            switch (vm.get('type')) {
                case sfw.util.CommonConstants.HOST[0]:
                var selected = grid.getSelectionModel().getSelected();
                deleteGroupText = (selected.items.length > 1) ? 'Delete all selected Hosts' : 'Delete Host';

                    var contextMenu = Ext.create('Ext.menu.Menu', {
                        width: 160,
                        plain: true,
                        items: [{
                            text: 'View Scan Result',
                            listeners: {
                                afterRender: function () {
                                    if (selected.items.length > 1) {
                                        this.hide();
                                    }
                            },
                                click: {fn: sfw.Default.scanResultOverview, extra: record}
                            }
                        },{
                            text: deleteGroupText,
                            disabled: sfw.util.Auth.LoginDetails.isReadOnly,
                            extra: selected.items,
                            handler: Ext.bind(me.removeHosts, me)
                        }]
                    });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                break;
                case sfw.util.CommonConstants.PRODUCT[0]:
                     var contextMenu = Ext.create('Ext.menu.Menu', {
                        plain: true,
                        items: [{
                            text: 'View Installations',
                            listeners: {
                                click: {fn: sfw.Default.viewInstallation, extra: record}
                            }
                        },{
                            text: 'Add Extended Support',
                            listeners: {
                                click: {fn: sfw.Default.addEditExtendedSupport, extra: record},
                                beforerender: function () {
                                    if (sfw.util.Auth.LoginDetails.isReadOnly || !sfw.util.Auth.LoginDetails.isPartitionAdmin) {
                                        this.hide();
                                    }
                                }
                            }
                        }]
                    });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
                break;
/*                case sfw.util.CommonConstants.ADVISORY[0]:
                    break;*/
            }
    },

    doubleClickOption: function (grid, record) {
            var me = this,
                vm = me.getViewModel(),
                sgPanel = grid.up('configuredsmartgroupsbasepanel'),
                smartGroupId = sgPanel.getViewModel().get('smartGroupId'),
                options = { extra: Ext.apply(record, {smartGroupId: smartGroupId}) };

            switch (vm.get('type')) {
                case sfw.util.CommonConstants.HOST[0]:
                    sfw.util.Default.scanResultOverview(grid, null, options) ;
                    break;
                case sfw.util.CommonConstants.PRODUCT[0]:
                    //console.log('bye');
                    sfw.util.Default.viewInstallation(grid, null, options);
                    break;
            }
    },

    //----- onSGSOverviewStoreBeforeLoad
    onSGSOverviewStoreBeforeLoad: function (store) {
        var me = this,
            vm = me.getViewModel(),
            proxy = store.getProxy();

        proxy.url = 'action=smart_groups&which=overview&smartGroupTextType='+vm.get('type')+'&';
    },

    onSGOverviewStoreLoad: function (store) {
        var view = this.getView(),
        vm= view.getViewModel();
        sgOverViewGrid = view.down('#sgOverViewGrid');
        sgOverViewGrid.setTitle(vm.data.type.charAt(0).toUpperCase()+vm.data.type.slice(1)+ ' Smart Groups: Overview & Configuration');
        //console.log('>> onSGOverviewStoreLoad');
    },

    onRowContextMenu: function (grid, record, item, rowIndex, e) {
        var me = this,
            vm = me.getViewModel(),
            selection = grid.getSelection();

        if (sfw.util.Auth.LoginDetails.account.isMspUser()) {
            return;
        }

        var additionalDeleteText = 'Smart Group';
        var multipleSelect = false;
        var chosenIdList; // Can be one int or a comma separated string of ints
        var selectionArray = [];
        var deleteMsg;
        var uneditableChosen = 0;

        var contextMenu = Ext.create("Ext.menu.Menu", {
            width: 280,
            plain: true
        });

        // Support multiple selection only for delete
        if ( 1 < selection.length ) {
            multipleSelect = true;
            // Add logic to support that not all groups are editable
            for ( var i=0; i < selection.length; ++i ) {
                if ( !selection[i].data.editable ) {
                    uneditableChosen = true;
                } else {
                    // Only push the ones that are editable
                    selectionArray.push( selection[i].id );
                }
            }
            chosenIdList = selectionArray.join(",");
            additionalDeleteText = 'all selected ' + (uneditableChosen ? '(editable)' : '') + ' Smart Groups';
        }

        if ( !multipleSelect ) {
            chosenIdList = grid.getStore().getAt( rowIndex ).data.id;
            uneditableChosen = Ext.Number.parseInt(record.get('editable')) === 0;
            var sgName = grid.getStore().getAt( rowIndex ).data.name;
            deleteMsg = "Are you sure you want to permanently delete the Smart Group '" + sgName + "' and all related notifications?";
            contextMenu.add(
                [{
                    text: 'View' + (uneditableChosen ? '' : '/Edit') + ' Smart Group Configuration',
                    record: record,
                    handler: Ext.bind(me.viewEditSmartGroup, me)
                },{
                    text: 'View Smart Group Contents',
                    record: record,
                    handler: Ext.bind(me.viewSmartGroupContents, me)
                },'-']
            );
        }

        var smartGroupIds = [];
        Ext.each(selection, function ( row ) {
                smartGroupIds.push( row.id ) ;
            }
        );

        contextMenu.add([{
            text: "Queue Smart Group" + ( multipleSelect ? "s" : "" ) + ' For Compilation',
            smartGroupIds: smartGroupIds,
            handler: Ext.bind(me.recompile, me)
        },'-']);

        // Add appropriate delete message if at least one editable
        // group was chosen, else add appropriate message about
        // editability
        var itemText;
        var setDisabled = true; // true by default, set false only if needed
        var setHandler; // null by default, define only if needed
        if ( !multipleSelect && uneditableChosen ) {
            // We chose one item, and it is uneditable
            itemText = 'Selected Smart Group cannot be deleted.';
        } else if ( multipleSelect && 0 === selectionArray.length ) {
            // We chose several items, ALL of which are uneditable
            itemText = 'None of the selected Smart Groups can be deleted.';
        } else {
            itemText = 'Delete ' + additionalDeleteText;
            setDisabled = sfw.util.Auth.LoginDetails.isReadOnly
            setHandler = function() {
                if ( multipleSelect ) {
                    // set up the deleteMsg
                    deleteMsg = 'Are you sure you want to permanently delete the '
                        + selectionArray.length + ' selected '
                        + (uneditableChosen ? '(editable)' : '')
                        + ' Smart Group'
                        + (selectionArray.length > 1 ? 's' : '')
                        + ' and all related notifications?';
                }

                // Note, there is no back end returned warning for
                // trying to delete uneditable groups, we just filter
                // the list UI side and only pass editable ones to the
                // backend. The backend, does, of course, filter any
                // list it gets so it only deletes editable ones, it
                // just doesn't return any flag saying it could not
                // delete something because it was uneditable.
                if ( chosenIdList ) {
                    Ext.MessageBox.confirm(
                        'Confirm Deletion',
                        deleteMsg,
                        function( button ) {
                            var smartGroupType = sfw.util.CommonConstants.SMARTGROUP_TYPES[vm.get('type')];
                            var delParams = {
                                delete_id_list: chosenIdList,
                                smartGroupType: smartGroupType
                            };
                            if ( button === 'yes') {
                                Ext.Ajax.request({
                                    url: 'action=smart_groups&which=delete&',
                                    params: delParams,
                                    success: function( response ) {
                                        var status = Ext.decode( response.responseText );
                                        switch ( status.error ) {
                                            case 0:
                                                vm.getStore('smartGroupOverviewStore').load();
                                                mainView = sfw.common.SfwAccessor.getMainView();
                                                navBar = mainView.down('nav-bar');
                                                navBar.getController().loadSmartGroupNode(vm.get('type'), 120000, false, true);
                                                var message = 'Smart Group' + (1 < selectionArray.length ? 's' : '') + ' deleted.';
                                                Ext.Msg.alert( "Success", message );
                                                break;
                                            case 50: // NO_WRITE_PERMISSION
                                                Ext.Msg.alert( "Permission Denied", "You do not have the required permissions to delete Smart Groups." );
                                                break;
                                            default:
                                                Ext.Msg.alert( "Unexpected Error", "Unable to delete..." );
                                                break;
                                        }
                                    },
                                    failure: function() {
                                        Ext.Msg.alert( "Unexpected Error", "Unable to delete..." );
                                    }
                                });
                            }
                        });
                }
            }
        }

        contextMenu.add({
            text: itemText,
            disabled: setDisabled,
            handler: setHandler //Ext.bind(me.handleDeleteSmartGroups, me);
        });

        e.stopEvent();
        contextMenu.showAt(e.getXY());
    },

    recompile: function (menuItem) {
        const me = this,
            vm = me.getViewModel(),
            smartGroupIdArray = menuItem.smartGroupIds;

        if ( !smartGroupIdArray || !smartGroupIdArray.length ) {
            return;
        }

        var params = "&id[]=" + smartGroupIdArray.join( "&id[]=" );

        Ext.Ajax.request({
            url: 'action=smart_groups&which=recompile_asap&',
            params: params,
            success: function( response ) {
                response = Ext.decode( response.responseText, true );
                if (response) {
                    switch ( response.error ) {
                        case 0: // SUCCESS
                            Ext.Msg.alert( "Compilation Queued", "The data for the selected Smart Groups will be updated as soon as possible." );
                            vm.getStore('smartGroupOverviewStore').load();
                            break;
                        default:
                            throw { number: 1, message: "SG set gen_asap failed with response.error: " + response.error };
                    }
                } else {
                    Ext.Msg.alert( "Unexpected error", "Unable to queue Smart Group(s) for generation..." );
                    return;
                }
            },
            failure: function() {
                Ext.Msg.alert( "Unexpected error", "Unable to queue Smart Group(s) for generation..." );
            }
        });
    },

    viewSmartGroupContents: function (menuItem) {
        var me = this,
            record = menuItem.record,
            vm = me.getViewModel();

        var routeUrl = Ext.String.format('SmartGroup{0}{1}', Ext.String.capitalize(vm.get('type')), record.get('id'));

        if (routeUrl) {
            me.redirectTo(routeUrl);
        }
    },

    onSelectionChange: function (selModel, selected) {
        var me = this,
            grid = me.getView().down('grid');

        if (selected.length) {
            grid.down('#queueToRecompileId').enable();
        } else {
            grid.down('#queueToRecompileId').disable();
        }
    },

    queueToRecompile: function () {
        var me = this,
            grid = me.getView().down('grid');

        var smartGroupIds = [];
        Ext.each( grid.getSelectionModel().getSelection(), function(row) {
            smartGroupIds.push( row.id ) ;
        });
        me.recompile({ smartGroupIds: smartGroupIds });
    },

    createNewSmartGroup: function () {
        var me = this,
            vm = me.getViewModel();

        var dialog = Ext.create('sfw.view.results.smartgroup.SmartGroupDialog', {
            type: vm.get('type'),
            listeners: {
                beforerender:function(){
                    if(vm.get('type') != 'product'){
                        dialog.down('#smartGroupTemplate').destroy();
                    }
                },
                close: function () {
                    vm.getStore('smartGroupOverviewStore').load();
                }
            }
        });
        dialog.show();
    },

    overviewItemDoubleClick: function (view, record) {
        var me = this,
            view = me.getView(),
            vm = me.getViewModel();

        view.setLoading(true);

        Ext.Ajax.request({
            url: 'action=smart_groups&which=getRules&',
            params: {
                smartGroupId: record.get('id')
            },
            success: function( response ) {
                view.setLoading(false);
                var response = Ext.decode(response.responseText, true);
                if (response) {
                    var dialog = Ext.create('sfw.view.results.smartgroup.SmartGroupDialog', {
                        type: vm.get('type'),
                        listeners: {
                            beforerender:function(){
                                if(vm.get('type') != 'product'){
                                    dialog.down('#smartGroupTemplate').destroy();
                                }
                            },
                            close: function () {
                                vm.getStore('smartGroupOverviewStore').load();
                            }
                        }
                    });
                    dialog.getViewModel().set('rules', response.data);
                    dialog.getViewModel().set('record', record);
                    dialog.show();
                }
            },
            failure: function() {
                view.setLoading(false);
                Ext.Msg.alert( 'Error', 'Unexpected error - could not retrieve Smart Group metadata.' );
            }
        });

    },

    viewEditSmartGroup: function(menuItem) {
        var me = this,
            record = menuItem.record;

        me.overviewItemDoubleClick(me.getView(), record);
    },

    vulnIdFetch: function () {
        var me = this,
        view = me.getView();
        var vulnId = view.getVulnId();
        var org = view.getOrg();

        var advisorypopupgrid = Ext.getStore('AdvisoryPopupGrid');
        advisorypopupgrid.getProxy().setExtraParams({
            'vulnId': vulnId,
            'show' : org
        });
    },

    vulnIdFetchHost: function () {
        var me = this,
        view = me.getView();
        var vulnId = view.getVulnId();

        var advisorypopuphostgrid = Ext.getStore('AdvisoryGridHostPopup');
        advisorypopuphostgrid.getProxy().setExtraParams({
            'vulnId': vulnId
        });
    },

    vulnIdFetchProduct: function () {
        var me = this,
        view = me.getView();
        var vulnId = view.getVulnId();

        var advisorypopupproductgrid = Ext.getStore('AdvisoryGridProductPopup');
        advisorypopupproductgrid.getProxy().setExtraParams({
            'vulnId': vulnId
        });
    }

});