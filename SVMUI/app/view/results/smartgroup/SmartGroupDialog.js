Ext.define('sfw.view.results.smartgroup.SmartGroupDialog', {
    extend: 'Ext.window.Window',
    xtype: 'smart-group-dialog',

    title: 'Configure New Smart Group',

    //width: 1250,
    height: 700,
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch',
    },

    bind: {
        title: '{smartGroupDialogTitle}'
    },

    config: {
        type: 'host' //default
    },

    viewModel: {
        data: {
            firstCriteriaId: null,
            rules: null, //edit
            record: null,
            criteriaStore: {},
            criteriaValuesStore: {},
            criteriaActionsStore: {}
        },
        formulas: {
            smartGroupText: function (get) {
                return !Ext.isEmpty(get('record')) ? Ext.htmlDecode(get('record.name')) : '';
            },
            smartGroupDescriptionText: function (get) {
                return !Ext.isEmpty(get('record')) ? Ext.htmlDecode(get('record.description')) : '';
            },
            saveBtnText: function (get) {
                return !Ext.isEmpty(get('record')) ? 'Update' : 'Save';
            },
            smartGroupDialogTitle: function (get) {
                var title = '';
                if (Ext.isEmpty(get('record'))) {
                    title = 'Configure New Smart Group';
                } else {
                    const isEditable = Ext.Number.parseInt(get('record').data.editable) === 0;
                    title = isEditable ? 'View Smart Group  (Editing Prohibited)': 'Save/Edit Smart Group';
                }
                return title;
            }
        }
    },

    scrollable: 'vertical',

    items: [
        {
            xtype: 'panel',
            itemId: 'mainDefinitionPanel',
            padding: '10px 10px 10px',
            layout: {
                type: 'form',
                labelWidth: 150
            },
            autoScroll: true,
            border: false,
            items: [
                {
                    xtype: 'textfield',
                    allowBlank: false,
                    itemId: 'smartGroupNameId',
                    fieldLabel: 'Smart Group Name',
                    bind: '{smartGroupText}',
                    listeners: {
                        change: function( field, newValue, oldValue, eOpts ) {
                            if ( '' !== field.getValue() ) {
                            } else {
                            }
                        }
                    }
                },
                {
                    xtype: 'textarea',
                    autoHeight: true,
                    itemId: 'smartGroupDescriptionId',
                    bind: '{smartGroupDescriptionText}',
                    emptyText: 'Enter an (optional) description for this Smart Group...',
                    fieldLabel: 'Description'
                }
            ]
        }, {
            xtype: 'fieldcontainer',
            hideLabel: true,
            layout: 'hbox',
            padding: '0 0 0 15',
            itemId: 'businessImpactFCId',
            items: [{
                xtype: 'combobox',
                width: 300,
                labelWidth: 150,
                store: Ext.create('sfw.store.results.BusinessImpact'),
                valueField: 'id',
                displayField: 'type',
                value: 1,
                fieldLabel: 'Business Impact',
                itemId: 'businessImpactId',
                mode: 'local',
                editable: false,
                triggerAction: 'all',
                forceSelection: true,
                autoSelect: true
            }]
        }, {
            xtype: 'fieldcontainer',
            hideLabel: true,
            layout: 'hbox',
            padding: '0 0 0 15',
            itemId: 'matchingItemsId',
            items: [{
                xtype: 'combobox',
                width: 300,
                labelWidth: 150,
                fieldLabel: 'Contains that match',
                itemId: 'logicTypeId',
                mode: 'local',
                editable: false,
                triggerAction: 'all',
                forceSelection: true,
                autoSelect: true,
                store: {
                    fields: [ 'id', 'type' ],
                    data: [
                        { id: 1, type: 'all' },
                        { id: 2, type: 'any' }
                    ]
                },
                valueField: 'id',
                displayField: 'type',
                value: 1
            }, {
                xtype: 'label',
                text: 'of the following criteria:',
                margin: '4 0 0 0',
                padding: '0 0 0 10px'
            }]
        }, {
            xtype: 'fieldset',
            title: 'Criteria',
            padding: 10,
            margin: 10,
            itemId: 'criterialFieldSetId',
            collapsible: false,
            style: {
                backgroundColor: '#fff'
            },
            defaults: {
                anchor: '100%',
                layout: 'hbox'
            }
        }, {
            xtype: 'fieldset',
            title: 'Customize Columns',
            padding: 10,
            margin: 10,
            itemId: 'customizeFieldSetId',
            collapsible: false,
            style: {
                backgroundColor: '#fff'
            },
            defaults: {
                anchor: '100%',
                layout: 'hbox'
            },
            items: [{
                xtype: 'box',
                flex: 1,
                bodyPadding: 10,
                border: false,
                itemId: 'additionalColumnLabel',
                tpl: 'Use this form to control which additional columns are shown in the grid view. Mouseover a checkbox for the column description.<br><br>'
            }, {
                xtype: 'radiogroup',
                hideLabel: true,
                itemId: 'selectOptionRGId',
                items: [
                    { boxLabel: 'Select All', name: 'selectOption', width: 120, inputValue: 1, checked: true },
                    { boxLabel: 'Select Custom', name: 'selectOption', inputValue: 0 }
                ],
                listeners: {
                    change: 'onSelectOptionChange'
                }
            }, {
                xtype: 'checkboxgroup',
                itemId: 'customColumnsId',
                hideLabel: true,
                height: 40,
                disabled: true,
                margin: '10px 0px 0px 0px',
                name: 'customColumns',
                style: {
                    borderRadius: '4px',
                    backgroundColor: '#e8e8e8'
                }
            }]
        }
    ],
    buttons: [
        {
            xtype: 'button',
            text: 'Templates',
            tooltip: 'Templates with description',
            //disabled: true,
            handler: 'smartGroupTemplateWindow',
            itemId:'smartGroupTemplate'
        },
        '->',
        {
            xtype: 'button',
            bind: {
                text: '{saveBtnText}'
            },
            itemId: 'smartGroupsSaveButton',
            //tooltip: 'Save Smart Group',
            disabled: false, // disable by default - we will enable it as appropriate
            handler: 'saveSmartGroup'
        },
        {
            xtype: 'button',
            text: 'Close',
            itemId: 'closeButton',
            //tooltip: 'Close window',
            disabled: false,
            handler: 'closeDialog'
        }
    ],

    listeners: {
        afterrender: 'onSGAfterRender'
    },

    controller: {

        getCriteriaPanel: function () {
            return this.getView().down('#criterialFieldSetId');
        },

        getCustomColumnsGroup: function() {
            return this.getView().down('#customColumnsId');
        },

        getSelectOptionRG: function () {
            return this.getView().down('#selectOptionRGId');
        },

        closeDialog: function () {
            this.getView().close();
        },

        onSelectOptionChange: function (rg, newVal) {
            var me = this,
                view = me.getView();

            if (newVal.selectOption == 1) {
                view.down('#customColumnsId').disable();
            } else {
                view.down('#customColumnsId').enable();
            }
        },

        //---
        onSGAfterRender: function (window) {
            var me = this,
                view = me.getView(),
                vm = me.getViewModel(),
                record = vm.get('record');

            var relevantLookupStores = sfw.util.CommonConstants.SMARTGROUP_STORES[view.getType()];
            vm.set('criteriaStore', relevantLookupStores[0]);
            vm.set('criteriaActionsStore', relevantLookupStores[1]);
            vm.set('criteriaValuesStore', relevantLookupStores[2]);

            vm.set('firstCriteriaId', vm.get('criteriaStore').getAt(0).id);

            var potentialColumns = sfw.util.SmartGroupConfig.getSmartGroupViewConfig(view.getType()).potentialColumns;

            var customColumns = view.down('#customColumnsId');
            var checkboxes = [];

            Ext.each(potentialColumns, function (column) {
                checkboxes.push({
                    boxLabel: column.displayName,
                    hidden: column.hidden,
                    padding: 5,
                    inputValue: column.id,
                    name: 'customColumns',
                    checked: true
                });
            });
            customColumns.add(checkboxes);
            if (!Ext.isEmpty(record)) {
                if(!record.get('template')) {
                    var businessImpactValue = 6 - 5 * parseFloat(record.get('business_impact'));
                    view.down('#businessImpactId').setValue(businessImpactValue);
                    var logicType = sfw.util.Default.htmlSpecialCharsDecodeAlsoQuot(record.get('logic_type'));
                    view.down('#logicTypeId').setValue(logicType === 'any' ? 2 : 1);

                    me.populateCustomColumnPanel();
                }

                const isEditable = Ext.Number.parseInt(record.get('editable')) === 0;
                if (isEditable) {
                    if (view.body) {
                        view.body.mask();
                    }
                    view.down('#smartGroupsSaveButton').setDisabled(true);
                    if(view.down('#smartGroupTemplate')){
                        view.down('#smartGroupTemplate').setDisabled(true);
                    }
                }
            }

            me.populateCriterias();

        },

        saveSmartGroup: function (btn) {
            var me = this,
                vm = me.getViewModel(),
                view = me.getView(),
                columnPanel = me.getSelectOptionRG(),
                criteriaPanel = me.getCriteriaPanel(),
                customColumnsGroup = me.getCustomColumnsGroup();

            var name = view.down('#smartGroupNameId').getValue();

            // We know the name exists or we couldn't have got in here. Validate anyway.
            if ( !name ) {
                Ext.Msg.alert('Smart Group name missing', 'Please provide a name for the Smart Group.');
                return;
            }

            var description = view.down('#smartGroupDescriptionId').getValue();
            var logicTypeValue = view.down('#logicTypeId').getValue();
            var businessImpactValue = view.down('#businessImpactId').getValue();

            // Get custom columns - if we select all by default,
            // or if we chose our own list
            var colRadioGroup = columnPanel.getValue();
            var customColumns = '';
            if ( colRadioGroup.selectOption === 0) {
                customColumns = customColumnsGroup.getValue().customColumns.join(',');
            }

            var allParams = {};

            // Get the descriptive meta-data fields
            allParams["description[name]"] = name;
            allParams["description[description]"] = description;
            allParams["description[andOr]"] = logicTypeValue;
            allParams["description[businessImpact]"] = businessImpactValue;
            allParams["description[allCustomColumns]"] = colRadioGroup.selectOption;
            allParams["description[customColumns]"] = customColumns;

            var ruleCtrs = criteriaPanel.query('container[type=rules]');
            var validateRule = false;
            Ext.each(ruleCtrs, function(ruleCtr, idx) {
                var fields = ruleCtr.query('field');
                Ext.each(fields, function(field) {
                    var type = field.getName();
                    var value = field.getValue();
                    allParams["rules[" + idx + "][" + type + "]"] = value;
                });

                if ( typeof ruleCtr.currentSelected !== "undefined" ) {
                    listData = ruleCtr.currentSelected.data;//console.log(listData);
                    // Ensure something was actually selected. If so,
                    // compile it into an array, else, break out now with
                    // a warning message
                    if ( listData && 0 < listData.length ) {
                        idProperty = ruleCtr.currentSelected.idProperty;
                        csvSelected = [];
                        for ( j = 0; j < listData.length; ++j ) {
                            datum = listData[j].data;
                            if ( typeof datum[idProperty] !== 'undefined' ) {
                                csvSelected.push( datum[idProperty] );
                            }
                            // Otherwise data is invalid...
                        }

                        // Add the list compiled above to the rule
                        allParams["rules[" + idx + "][value]"] = csvSelected.join(',');
                    } else {
                        // Pull the type out from the main criteria combo box
                        ruleCriteria = ruleCtr.items.items[0].rawValue;
                        Ext.Msg.alert(
                            'No ' + ruleCriteria + 's Selected'
                            ,'Please select at least one item for Criteria ' + ( idx + 1 ) + ' (' + ruleCriteria + ') or delete this Criteria from your Smart Group.'
                        );
                        validateRule = true;
                    }
                } else if (allParams["rules[" + idx + "][value]"] === '') {
                    ruleCriteria = ruleCtr.items.items[0].rawValue;
                    Ext.Msg.alert(
                        'No ' + ruleCriteria + 's Selected'
                        ,'Please enter a value for Criteria ' + ( idx + 1 ) + ' (' + ruleCriteria + ') or delete this Criteria from your Smart Group.'
                    );
                    validateRule = true;
                }
            });

            if(validateRule){
                return false;
            }

            var url = 'action=smart_groups&which=';
            var successMsg = 'Smart Group saved.';

            if (Ext.isEmpty(vm.get('record'))) {
                url += 'add&';
            } else {
                url += 'edit&';
                successMsg = 'Smart Group Edited.';
                allParams['smartGroupId'] = vm.get('record').get('id');
            }

            allParams['smartGroupType'] = sfw.util.CommonConstants.SMARTGROUP_TYPES[view.getType()];

            view.setLoading(true);

            Ext.Ajax.request({
                url: url,
                params: allParams,
                success: function( response ) {
                    view.setLoading(false);
                    var status = Ext.decode(response.responseText, true) || {};
                    switch (status.error) {
                        case 0:
                            me.closeDialog();
                            mainView = sfw.common.SfwAccessor.getMainView();
                            navBar = mainView.down('nav-bar');
                            navBar.getController().loadSmartGroupNode(view.getType(), 120000, true, false);
                            Ext.Msg.alert('Success', successMsg);
                            break;

                        case 6:
                            Ext.Msg.alert( 'Smart Group name exists', 'Please provide a new (unique) name for the Smart Group.' );
                            break;

                        case 50: // NO_WRITE_PERMISSION
                            Ext.Msg.alert( 'Permission Denied', 'You do not have the required permissions to save Smart Groups.' );
                            break;

                        default:
                            // Note, there are a ton of potential return codes
                            // from the backend, but they are all safety
                            // mechanisms, i.e. none should actually be
                            // possible... Don't bother coding for them all
                            // here.
                            Ext.Msg.alert( 'Error', 'Unexpected error.' );
                            break;
                    }
                },
                failure: function() {
                    me.closeDialog();
                    Ext.Msg.alert( 'Error', 'Unexpected error.' );
                }
            });

        },

        smartGroupTemplateWindow:function(){
            var templateWindow = Ext.create('sfw.view.results.smartgroup.SmartGroupTemplate');
            templateWindow.setTitle('Product Smart Group Example Use Cases');

            var useCaseRuleOne = JSON.stringify([{
                criteria: sfw.util.CommonConstants.CRITERIA_PRODUCT_STATUS
                ,value: 1 // insecure
            },{
                criteria: sfw.util.CommonConstants.CRITERIA_SAID_DATE
                ,action: 7 // older than
                ,value_text: '30'
            }]);

            useCaseRuleOne = useCaseRuleOne.replace(/"/g,'%20');

            var useCaseRuleTwo =  JSON.stringify([{
                criteria: sfw.util.CommonConstants.CRITERIA_PRODUCT_STATUS
                ,value: 1 // insecure
            },{
                criteria: sfw.util.CommonConstants.CRITERIA_CRITICALITY
                ,action: 0 // at least
                ,value: 5 // highly critical - remember the values here are: 4-8 in our value lists maps to 1-5 criticality wise
            },{
                criteria: sfw.util.CommonConstants.CRITERIA_SAID_DATE
                ,action: 7 // older than
                ,value_text: '7'
            }]);

            useCaseRuleTwo = useCaseRuleTwo.replace(/"/g,'%20');

            var useCaseRuleThree = JSON.stringify([{
                criteria: sfw.util.CommonConstants.CRITERIA_PRODUCT_STATUS
                ,value: 1 // insecure
            },{
                criteria: sfw.util.CommonConstants.CRITERIA_CRITICALITY
                ,action: 0 // at least
                ,value: 4 // extremely critical
            },{
                criteria: sfw.util.CommonConstants.CRITERIA_SAID_DATE
                ,action: 8 // within
                ,value_text: '7'
            }]);

            useCaseRuleThree = useCaseRuleThree.replace (/"/g,'%20');

            templateExampleArray = [{
                title: 'Regulatory Compliance'
                ,description: 'This Smart Group can be modified to comply with any laws and regulations required for your business (e.g., PCI-DSS, GLBA, FISMA, HIPAA, or NERC compliance). For example, PCI-DSS requires organisations to install security patches within one month of release. This example template reflects this requirement - whenever this Smart Group is non-empty, the policy is violated. Modify this as needed with specific criteria for your environment and compliance requirements.'
                ,critDetails: 'Smart Group Criteria:<br>  1) Product Status: Insecure<br>  2) SAID Publication Date: Older than 30 days.'
                ,rules:useCaseRuleOne
            },{
                title: '7-Day Critical Vulnerability Compliance'
                ,description: "This will show all insecure products rated &quot;Highly&quot; or &quot;Extremely Critical&quot;, with an Advisory publication date older than 7 days. If your internal compliance policy dictates that a critical vulnerability must never go unpatched for more than 7 days, then whenever this Smart Group is non-empty, the policy is violated."
                ,critDetails: 'Smart Group Criteria:<br>  1) Product Status: Insecure<br>  2) Criticality: At least Highly Critical<br> 3) SAID Publication Date: Older than 7 days.'
                ,rules:useCaseRuleTwo

            },{
                title: '7-Day Extremely Critical Watchlist'
                ,description: 'This will show all &quot;Extremely Critical&quot; products with an Advisory publication date from the past week. Use this to track new and emerging threats to your system.'
                ,critDetails: 'Smart Group Criteria:<br>  1) Product Status: Insecure<br>  2) Criticality: Extremely Critical<br> 3) SAID Publication Date: Within 7 days.'
                ,rules:useCaseRuleThree
            }];
            templateWindow.getViewModel();
            templateWindow.getViewModel().set('templateData', templateExampleArray);
            templateWindow.show();
        },

        populateCustomColumnPanel: function () {
            var me = this,
                vm = me.getViewModel(),
                columnPanel = me.getSelectOptionRG(),
                customColumnsGroup = me.getCustomColumnsGroup(),
                record = vm.get('record');

            var allCustomColumns = record.get('all_custom_columns');
            var customColumnData = 0;
            if (allCustomColumns == 0) {
                customColumnData = record.get('custom_columns');
            }

            if (allCustomColumns == 1) {
                columnPanel.setValue({selectOption: 1});
                return;
            }

            customColumnsGroup.reset();
            customColumnsGroup.setValue({ customColumns:  customColumnData.split(',') });
            columnPanel.setValue({selectOption: 0});
        },

        populateCriterias: function () {
            var me = this,
                vm = me.getViewModel(),
                criteriaPanel = me.getCriteriaPanel(),
                rules = vm.get('rules');

            if (Ext.isEmpty(rules)) {
                //create new rule
                criteriaPanel.add(me.createRule());
            } else {
                //edit rules
                Ext.each(rules, function (rule) {
                    var ruleCtr = me.createRule(rule);
                    me.populateSmartGroupContainer(rule, ruleCtr);
                    criteriaPanel.add(ruleCtr);
                });
            }
        },

        populateSmartGroupContainer: function (rule, ruleCtr) {
            var criteria = parseInt(rule.criteria, 10); // criteria is mandatory
            var action = rule.action ? parseInt(rule.action, 10 ) : 0;
            var value = rule.value ? parseInt(rule.value, 10 ) : 0;
            var valueText = rule.value_text ? sfw.util.Default.htmlSpecialCharsDecodeAlsoQuot(rule.value_text) : '';

            // This will only exist for the hosts/sites/products criteria types
            var listData = rule.listData ? rule.listData : [];
            var CommonConstants = sfw.util.CommonConstants;

            switch(criteria) {
                case CommonConstants.CRITERIA_PRODUCT_STATUS:
                case CommonConstants.CRITERIA_SILENT_INSTALLATION:
                case CommonConstants.CRITERIA_PLATFORM:
                case CommonConstants.CRITERIA_SOFT_TYPE:
                case CommonConstants.CRITERIA_ZERO_DAY:
                case CommonConstants.CRITERIA_WHERE_TYPE:
                case CommonConstants.CRITERIA_SOLUTION_STATUS:
                    ruleCtr.items.items[2].setValue( value );
                    break;

                case CommonConstants.CRITERIA_CRITICALITY:
                case CommonConstants.CRITERIA_SCORE:
                case CommonConstants.CRITERIA_IMPACT_TYPE:
                case CommonConstants.CRITERIA_CVSS_SCORE:
                case CommonConstants.CRITERIA_THREATSCORE:
                case CommonConstants.CRITERIA_CVSS2_SCORE:
                case CommonConstants.CRITERIA_CVSS3_SCORE:
                    ruleCtr.items.items[2].setValue( action );
                    ruleCtr.items.items[3].setValue( value );
                    break;

                case CommonConstants.CRITERIA_HOSTS:
                case CommonConstants.CRITERIA_SITES:
                case CommonConstants.CRITERIA_PRODUCTS:
                case CommonConstants.CRITERIA_OPERATINGSYSTEMS:
                case CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD:
                case CommonConstants.CRITERIA_KB_ARTICLE:
                case CommonConstants.CRITERIA_CVE_NUMBER:
                    // In these cases, we have additional data returned to us.
                    // We need to pack the data into the same format the store
                    // is expecting.

                    // Set the action
                    records = [];
                    if ( listData.length ) {
                        const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        fields = localItemSelector.itemSelectors.getDataFields( criteria, 'fields' );
                        idProperty = fields[0].name;
                        //myRecordType = new Ext.data.Model.create( fields );

                        if(typeof myRecordType == 'undefined'){
                            myRecordType = new Ext.define('SmartGroupModel', {
                                extend  : 'Ext.data.Model',
                                fields  : fields,
                            });
                        }else{
                            myRecordType.removeFields = true;
                            myRecordType.addFields(fields);
                        }

                        for ( j=0; j < listData.length; ++j ) {
                            thisRecord = new myRecordType( listData[j] );
                            thisRecord.id = listData[j][idProperty];
                            records.push( thisRecord );
                        }

                        ruleCtr.currentSelected = {
                            data: records,
                            idProperty: idProperty
                        };
                    }
                    ruleCtr.items.items[3].setText('(' + listData.length + ' selected...)');
                    break;

                case CommonConstants.CRITERIA_SAID_DATE:
                case CommonConstants.CRITERIA_LAST_SCANNED:
                case CommonConstants.CRITERIA_ADVISORY_DATE:
                    ruleCtr.items.items[2].setValue( action );
                    var combo = ruleCtr.items.items[2];
                    var record = combo.findRecord( combo.valueField, action );
                    var index = combo.store.indexOf( record );
                    ruleCtr.items.items[2].fireEvent( 'select', combo, record, index );
                    // Set the date / number of days (both stored as valueText)
                    ruleCtr.items.items[3].setValue( valueText );
                    break;
                case CommonConstants.CRITERIA_PRODUCT_NAME:
                case CommonConstants.CRITERIA_HOST_NAME:
                case CommonConstants.CRITERIA_SITE_NAME:
                case CommonConstants.CRITERIA_VENDOR_NAME:
                    ruleCtr.items.items[2].setValue( action );
                    ruleCtr.items.items[3].setValue( valueText );
                    break;
            }
        },

        createRule: function(rule) {
            var me = this,
                view = me.getView(),
                vm = me.getViewModel(),
                rules = vm.get('rules');

            var container = Ext.create('Ext.Container', {
                layout: 'hbox',
                type: 'rules',
                padding: 5,
                items: []
            });

            if (Ext.isEmpty(rule)) {
                container.add(me.createRuleItems(vm.get('firstCriteriaId'), container));
            } else {
                container.add(me.createRuleItems(rule.criteria, container));
            }

            return container;
        },

        // Helper function: Create the window from which we select
        // individual hosts / sites / product
        createItemSelectorWindow: function( criteriaType, rulePanel, customTitle, fieldName ) {

            if(!rulePanel) {
                rulePanel = {};
            }

            // If we already generated an item selector and modal window
            // for this rulePanel with this criteriaType, we skip the
            // generation of both. Otherwise, generate them both. If we
            // changed criteriaType within the same rulePanel, remove
            // them (if they exist) to ensure we regenerate them both.

            // Note: By definition, rulePanel.criteriaType = criteriaType,
            // since we set that explicitly before we call this
            // function. So if we want to ensure that we are creating the
            // right kind of selectorWindow here, we must check that we
            // are not using a window from a past criteria type
            if (rulePanel && rulePanel.modalWindow &&
                rulePanel.modalWindow.criteriaType != criteriaType ) {
                delete rulePanel.itemSelector;
            }

            if ( !rulePanel.itemSelector ) {
                const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

                switch( criteriaType ){
                    case sfw.util.CommonConstants.CRITERIA_HOSTS:
                        rulePanel.itemSelector = localItemSelector.createHostSelector();
                        break;
                    case sfw.util.CommonConstants.CRITERIA_SITES:
                        rulePanel.itemSelector = localItemSelector.createSiteSelector();
                        break;
                    case sfw.util.CommonConstants.CRITERIA_PRODUCTS:
                        rulePanel.itemSelector = localItemSelector.createProductSelector();
                        break;
                    default:
                        rulePanel.itemSelector = localItemSelector.createItemSelector( criteriaType );
                        break;
                }

                // if we need a new itemSelector, we need a new window
                if (rulePanel.modalWindow ) {
                    rulePanel.modalWindow.close();
                    delete rulePanel.modalWindow;
                }
            }

            if ( !rulePanel.modalWindow ) {
                rulePanel.modalWindow = new Ext.window.Window({
                    width: 800,
                    criteriaType: criteriaType,
                    //id: rulePanel.id + '_itemSelectorWindow',
                    title: typeof customTitle === "undefined" ? "" : customTitle,
                    height: 550,
                    border: false,
                    style: {
                        zIndex: 99999,
                    },
                    layout: 'border',
                    closeAction: 'hide',
                    modal: true,
                    items: [
                        rulePanel.itemSelector
                    ],

                    listeners: {
                        // Loads the store for the 'Available'
                        // grid when the window is shown so the
                        // body property exists for the
                        // ItemSelector to reference.
                        beforeshow: function () {
                            // First we make sure the selection
                            // panel is pre-selected with the last
                            // thing we actually saved in a
                            // previous attempt. First clear it,
                            // in case we selected sometehing and
                            // cancelled without saving. We always
                            // load directly from the
                            // currentSelected.

                            // rulePanel.itemSelector.availableStore.removeAll();
                            // rulePanel.itemSelector.selectedStore.removeAll();
                            if ( rulePanel.currentSelected
                                && rulePanel.currentSelected.data ) {
                                // setTimeout(() => {
                                //console.log("rulePanel.currentSelected.data:  ", rulePanel.currentSelected.data);
                                rulePanel.itemSelector.config.selectedStore.add( rulePanel.currentSelected.data );
                                // rulePanel.itemSelector.down("#selectedGrid").getStore().add( rulePanel.currentSelected.data );
                                // }, 2000);


                            }

                            rulePanel.itemSelector.config.selectedStore.loaded = true;

                            rulePanel.itemSelector.config.availableStore.load();


                            // Flag the selectedStore as loaded so when
                            // reloading the available store and trying to
                            // highlight, we catch the overlapping records
                            // there.

                            // rulePanel.itemSelector.selectedStore.loaded = true;

                            // Trigger a load of the available store, which
                            // will highlight any overlap with the selected
                            // store

                            // rulePanel.itemSelector.availableStore.load();
                        }
                    },

                    buttons: [{
                        text: 'Save',
                        // id: rulePanel.id + '_saveRulesButton',
                        handler: function() {
                            // We keep the current selections of the
                            // itemSelector in a 'currentSelected'
                            // variable so we can load from it on
                            // subsequent reopenings of the selector
                            // window.
                            const selectedGrid = rulePanel.itemSelector.down("#selectedGrid");


                            rulePanel.currentSelected = {
                                data: selectedGrid.getSelection(),
                                idProperty: selectedGrid.getStore().idProperty
                            };

                            // Update the button text
                            var count = rulePanel.currentSelected.data.length;
                            rulePanel.down("#selectionButton").setText( '(' + count + ' selected...)' );

                            // var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
                            // if(reportWindow && reportWindow.length){
                            //     reportWindow = reportWindow[0];
                            //     reportWindow.reportData[fieldName] = rulePanel.currentSelected.data;
                            // }
                            // else {
                            //     rulePanel.down("#selectionButton").setText( '(' + count + ' selected...)' );
                            // }

                            rulePanel.modalWindow.hide();
                        }
                    },{
                        text: 'Cancel',
                        handler: function() {
                            // Clear the available store here. We'll reload selected and available next time we open the window - for now we want to clear it so we don't have artifacts leftover froma cancel.
                            // rulePanel.itemSelector.smAvailable.clearSelections( false );
                            rulePanel.itemSelector.down('#availableGrid').getSelectionModel().deselectAll()
                            rulePanel.modalWindow.hide();
                        }
                    }],
                    keys: [{ // Handle form ENTER key press
                        key: [ Ext.event.Event.ENTER ],
                        handler: function ( keyCode, eventObject ) {
                            var tagName = eventObject.getTarget().tagName;
                            if ( 'A' === tagName || 'BUTTON' === tagName ) {
                                // Don't handle pressing ENTER on a select list item or button
                                return;
                            }
                            // Else, call handler for the local save-rules
                            // button. Note, this doesn't save anything to
                            // the backend, just saves the current
                            // selections locally. We handle weather or
                            // not we can save the actual smartgroup
                            // (based on our read-only vs. read-write
                            // access) separately. At this point, it isn't
                            // relevant.
                            Ext.getCmp( rulePanel.id + '_saveRulesButton' ).handler.call();
                        }
                    }]
                });
            }

            rulePanel.modalWindow.show();
        },

        createRuleItems: function(criteriaId, rulePanel) {
            const me = this,
                vm = me.getViewModel(),
                view = me.getView();

            criteriaId = parseInt(criteriaId, 10);

            // Recall from comments in 'createRule': we must set the criteria
            // type here so we capture anytime we re-generate the panel as the
            // result of a change of criteria via the combo-box. We must do
            // this to propogate necessary changes in itemSelector windows /
            // buttons (rulePanel.modalWindow)
            rulePanel.criteriaType = criteriaId;

            // Get the possible action / value lists associated with the
            // given criteria type and create / add the appropriate combo
            // boxes, text fields, etc.
            var index, record, actionData = [], valueData = [], actionStore, valueStore;

            // For the given passed in criteriaId, get the associated action and value data
            index = vm.get('criteriaStore').find( 'id', criteriaId );
            if ( -1 === index ) {
                // criteria doesn't exist - we can't carry on... something is horribly wrong.
                return;
            }

            record = vm.get('criteriaStore').getAt(index).data;

            if ( 0 < record.action_type ) {
                // Pull out the actions corresponding to this action_type
                actionData = vm.get('criteriaActionsStore').query( 'type', record.action_type );
                // actionData is a mixedCollection - put it into a store format
                actionStore = me.convertActionValueDataToStore( actionData, sfw.util.CommonConstants.actionStoreFields );
            }

            if ( 0 < record.value_type ) {
                valueData = vm.get('criteriaValuesStore').query( 'type', record.value_type );
                valueStore = me.convertActionValueDataToStore( valueData, sfw.util.CommonConstants.valueStoreFields );
            }

            var items = [];
            var criteriaCombo = me.createCombo( rulePanel, 'criteria', vm.get('criteriaStore'));
            criteriaCombo.setValue( record.id);
            items.push( criteriaCombo );
            items.push({ xtype: 'label', text: ' is ', margin: '5 5 0 5' });

            if ( actionData.length ) {
                var actionCombo = me.createCombo( rulePanel, 'action', actionStore, criteriaId );
                // Set the combo to the first data item available
                //actionCombo.setValue( actionData.keys[0] );
                actionCombo.setValue( actionData.items[0] );
                items.push( actionCombo );
            }

            if ( valueData.length ) {
                var valueCombo = me.createCombo(rulePanel, 'value', valueStore);
                valueCombo.setValue( valueData.items[0] );
                //items.push({ xtype: 'tbspacer', width: 5 });
                items.push(valueCombo);
            } else {
                switch ( criteriaId ) {
                    case sfw.util.CommonConstants.CRITERIA_SAID_DATE:
                    case sfw.util.CommonConstants.CRITERIA_LAST_SCANNED:
                    case sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE:
                        //items.push({ xtype: 'tbspacer', width: 5 });
                        items.push( me.createValueDateField() );
                        break;
                    case sfw.util.CommonConstants.CRITERIA_HOSTS:
                    case sfw.util.CommonConstants.CRITERIA_SITES:
                    case sfw.util.CommonConstants.CRITERIA_CVE_NUMBER:
                    case sfw.util.CommonConstants.CRITERIA_PRODUCTS:
                    case sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMS:
                    case sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD:
                    case sfw.util.CommonConstants.CRITERIA_KB_ARTICLE:
                        rulePanel.currentSelected = {}; // define so it exists - then if nothing is selected, we can better identify this case when we try to save the SG
                        //items.push({ xtype: 'tbspacer', width: 5 });
                        items.push( me.createItemSelectorButton(criteriaId, rulePanel));
                        break;
                    case sfw.util.CommonConstants.CRITERIA_SCORE:
                    case sfw.util.CommonConstants.CRITERIA_CVSS_SCORE:
                    case sfw.util.CommonConstants.CRITERIA_CVSS2_SCORE:
                    case sfw.util.CommonConstants.CRITERIA_CVSS3_SCORE:
                    case sfw.util.CommonConstants.CRITERIA_THREATSCORE:
                        //items.push({ xtype: 'tbspacer', width: 5 });
                        items.push( me.createValueNumberField(criteriaId));
                        break;
                    case sfw.util.CommonConstants.CRITERIA_PRODUCT_NAME:
                    case sfw.util.CommonConstants.CRITERIA_HOST_NAME:
                    case sfw.util.CommonConstants.CRITERIA_SITE_NAME:
                    case sfw.util.CommonConstants.CRITERIA_VENDOR_NAME:
                        //items.push({ xtype: 'tbspacer', width: 5 });
                        items.push( me.createValueTextField(criteriaId));
                        break;
                    default:
                        break;
                }
            }

            items.push( { xtype: 'tbspacer', flex:1 } );
            items.push( me.createPlusMinusButton('+', rulePanel) );
            items.push( { xtype: 'tbspacer', width: 5 } );
            items.push( me.createPlusMinusButton('-', rulePanel) );

            return items;
        },

        /*
        * Helper funciton to create the combobox for either
        * criteria or action / value
        *
        * RulePanel is the current rulePanel we are in.  Combotype
        * is one of 'criteria', 'action', 'value', the data store
        * is passed in, and critType is an optional 3rd parameter
        * that is only used for the speical cases
        * (i.e. crit=said_date)
        */
        createCombo: function( rulePanel, comboType, store, critType ) {

            const self = this;

            // This is the generic combo based on input. If we are a
            // 'value' combo, or most types of 'action', we're done
            // here. We add some logic below for 'criteriaType' combos
            // and some special cases of 'action'

            var combo = Ext.create('Ext.form.ComboBox', {
                width: 150,
                mode: 'local',
                name: comboType,
                editable: false,
                triggerAction: 'all',
                forceSelection: true,
                autoSelect: true,
                store: store,
                valueField: 'id',
                displayField: 'description',
                hideLabel: true,
                listeners: {
                    beforeselect: function( combo, record, index ) {
                        if ( record.get( 'recordCls' ) == 'x-combo-list-item-disabled' ) {
                            return false;
                        }
                    }
                }
            });

            if ( comboType === 'criteria' ) {
                combo.on({
                    select: function( combo, record, index ) {
                        // determine if the value changed from the old
                        // value so we know if we need to reset/delete all
                        // existing creations vs. do nothing
                        if ( combo.originalValue != record.id ) {
                            var criteriaPanel = rulePanel.ownerCt;
                            criteriaPanel.suspendEvents();
                            rulePanel.removeAll();
                            if ( 'undefined' !== typeof rulePanel.currentSelected ) {
                                delete rulePanel.currentSelected;
                            }
                            rulePanel.add( self.createRuleItems( record.id, rulePanel ) );
                            criteriaPanel.resumeEvents();
                            criteriaPanel.fireEvent('add', criteriaPanel);
                            // criteriaPanel.doLayout();
                        }
                        // Else, don't do anything...
                    }
                });

            } else if ( comboType === 'action'
                && ( critType === sfw.util.CommonConstants.CRITERIA_SAID_DATE
                    || critType === sfw.util.CommonConstants.CRITERIA_LAST_SCANNED
                    || critType === sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE )
            ) {

                // Set the initial "old value" as the first selectable item in the combobox
                combo.oldValue = parseInt( combo.store.data.items[0].data.id, 10 );

                combo.on({
                    // Listen for 'select' so we can change the form if
                    // needed. Recall that if we switch from a 'relative'
                    // to an 'absolute' date type (or vice-versa) we must
                    // modify the layout of the form accordingly
                    'select': function( combo, record, index ) {

                        var newValue = parseInt( record.data.id, 10 );
                        if ( newValue === combo.oldValue ) {
                            // didn't change type, don't change form - just break out.
                            return;
                        }

                        // Logic - a value of 7 or 8 is a 'relative to
                        // today' date type, else we have an absolute date
                        // type. If we've changed type, modify the
                        // form. I.e., the 'relative to today' dates have
                        // the number field and "days" string attached
                        // instead of just a datefield.
                        var newRelative = (newValue === 7 || newValue === 8);
                        var oldRelative = (combo.oldValue === 7 || combo.oldValue === 8);

                        // We "change" iff XOR of these is true
                        if ( newRelative ^ oldRelative ) { // bit-wise XOR
                            if ( newRelative ) {
                                rulePanel.remove( rulePanel.items.items[3] ); // date field
                                rulePanel.insert( 3, self.createValueNumberField( critType ) );
                                rulePanel.insert( 4, new Ext.form.Label( { text: ' days', margin: '5 5 0 0' } ) );
                            } else {
                                rulePanel.remove( rulePanel.items.items[4] );
                                rulePanel.remove( rulePanel.items.items[3] );
                                rulePanel.insert( 3, self.createValueDateField() );
                            }
                            // rulePanel.doLayout();
                        }
                        combo.oldValue = newValue;
                    }
                });
            }

            return combo;
        },

        // Helper function: create the "+" or "-" button for adding or
        // removing a criteria. Type muyt be one of '+' or '-'.
        createPlusMinusButton: function( type, rulePanel ) {
            const me = this,
                view = me.getView(),
                criteriaPanel = me.getCriteriaPanel();

            var button = Ext.create('Ext.Button', {
                text: type,
                width: 25,
                disabled: '-' === type && Ext.isEmpty(criteriaPanel.query('container[type=rules]')),
                cls: 'plus-minus-btn'
            });

            if ( '+' === type ) {
                button.handler = function() {
                    criteriaPanel.add(me.createRule());
                };
            } else {
                button.itemId =  'deleteButton';
                button.handler = function() {
                    criteriaPanel.remove(rulePanel);
                };
            }

            return button;
        },

        // Helper Function: Extract action/value data from the mixed
        // collection and convert to an ArrayStore for use in a combo box
        convertActionValueDataToStore: function( rawData, fields ) {
            var data = [], i, j;
            var datum;
            for ( i=0; i < rawData.length; ++i ) {
                datum = rawData.items[i].data;
                retrievedData = [];
                for ( j=0; j < fields.length; ++j ) {
                    retrievedData[j] = datum[fields[j].name];
                }
                data[i] = retrievedData;
            }

            return  Ext.create('Ext.data.ArrayStore', {
                fields: fields,
                data: data
            });
        },

        // Helper function: create the button we use to create an
        // ItemSelector. It is only more complicated than a standard
        // button because we have the dynamic text inside the button
        // with how many are already selected.
        createItemSelectorButton: function( criteriaType, rulePanel ) {
            const me = this,
                selectorButton = Ext.create('Ext.Button', {
                itemId: 'selectionButton',
                criteriaType: criteriaType,
                text: '( 0 selected...)', // initial value - will change upon selection
                width: 150,
                margin: '0 5 0 10',
                handler: function() {
                    me.createItemSelectorWindow( criteriaType, rulePanel );
                }
            });
            return selectorButton;
        },

        // Generic number field for value entries based on
        // criteria type
        createValueNumberField: function( criteriaId ) {

            // Init generic properties here - change or add
            // anything needed for specific criteria below
            var fieldConfig = {
                allowBlank: false,
                name: 'value',
                fieldLabel: '',
                anchor: '100%',
                allowDecimals: false,
                allowNegative: false,
                width: 81,
                margin: '0 5 0 10',
                autoCreate: { tag: 'input', type: 'text', size: '20', autocomplete: 'off' }
            };

            // This is percentage value, so set relevant fields
            if ( sfw.util.CommonConstants.CRITERIA_SCORE === criteriaId ) {
                fieldConfig.blankText = 'Score(%)';
                fieldConfig.emptyText = '(%)';
                //fieldConfig.vtype = 'percentageTest';
                fieldConfig.value = 80;
                fieldConfig.maxValue = 100;
                fieldConfig.minValue = 0;
                fieldConfig.maxLength = 3;
                fieldConfig.autoCreate.maxLength = '3';
                fieldConfig.enforceMaxLength= true;
            }

            // This is percentage value, so set relevant fields
            if ( sfw.util.CommonConstants.CRITERIA_CVSS_SCORE === criteriaId ) {
                fieldConfig.blankText = 'Value from 0-10';
                fieldConfig.emptyText = 'Value from 0-10';
                fieldConfig.value = 7;
                fieldConfig.maxValue = 10;
                fieldConfig.minValue = 0;
                fieldConfig.maxLength = 2;
                fieldConfig.autoCreate.maxLength = '2';
                fieldConfig.enforceMaxLength= true;
            }

            // This is percentage value, so set relevant fields
            if ( sfw.util.CommonConstants.CRITERIA_CVSS2_SCORE === criteriaId ) {
                fieldConfig.blankText = 'Value from 0-10';
                fieldConfig.emptyText = 'Value from 0-10';
                fieldConfig.value = 7;
                fieldConfig.maxValue = 10;
                fieldConfig.minValue = 0;
                fieldConfig.maxLength = 2;
                fieldConfig.autoCreate.maxLength = '2';
                fieldConfig.enforceMaxLength= true;
                //fieldConfig.autoCreate = {maxLength:'2', size: '2'};
            }

            // This is percentage value, so set relevant fields
            if ( sfw.util.CommonConstants.CRITERIA_CVSS3_SCORE === criteriaId ) {
                fieldConfig.blankText = 'Value from 0-10';
                fieldConfig.emptyText = 'Value from 0-10';
                fieldConfig.value = 7;
                fieldConfig.maxValue = 10;
                fieldConfig.minValue = 0;
                fieldConfig.maxLength = 2;
                fieldConfig.autoCreate.maxLength = '2';
                fieldConfig.enforceMaxLength= true;
            }

            if ( sfw.util.CommonConstants.CRITERIA_THREATSCORE === criteriaId ) {
                fieldConfig.blankText = 'Value from 0-100';
                fieldConfig.emptyText = 'Value from 0-100';
                fieldConfig.value = 70;
                fieldConfig.maxValue = 100;
                fieldConfig.minValue = 0;
                fieldConfig.maxLength = 3;
                fieldConfig.autoCreate.maxLength = '3';
                fieldConfig.enforceMaxLength= true;
            }

            if ( sfw.util.CommonConstants.CRITERIA_SAID_DATE === criteriaId
                || sfw.util.CommonConstants.CRITERIA_LAST_SCANNED === criteriaId
                || sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE === criteriaId ) {
                fieldConfig.maxValue = 9999;
                fieldConfig.minValue = 1;
                fieldConfig.maxLength = 4;
                fieldConfig.autoCreate.maxLength = '4';
                fieldConfig.enforceMaxLength= true;
            }

            var valueNumberField = new Ext.form.NumberField( fieldConfig );

            return valueNumberField;
        },

        // Generic text field for value entries based on
        // criteria type
        createValueTextField: function( criteriaId ) {

            // Init generic properties here - change or add
            // anything needed for specific criteria below
            var textFieldConfig = {
                allowBlank: false,
                name: 'value',
                fieldLabel: '',
                anchor: '100%',
                allowNegative: false,
                width: 120,
                margin: '0 5 0 10',
                autoCreate: { tag: 'input', type: 'text', size: '100', autocomplete: 'off' }
            };

            // This is percentage value, so set relevant fields
            if ( sfw.util.CommonConstants.CRITERIA_PRODUCT_NAME === criteriaId ) {
                //textFieldConfig.blankText = 'Enter Product Name';
                textFieldConfig.emptyText = 'Enter Product Name';
                //textFieldConfig.value = 'Enter Product Name';
            }

            if ( sfw.util.CommonConstants.CRITERIA_HOST_NAME === criteriaId ) {
                //textFieldConfig.blankText = 'Enter Host Name';
                textFieldConfig.emptyText = 'Enter Host Name';
                //textFieldConfig.value = 'Enter Host Name';
            }

            if ( sfw.util.CommonConstants.CRITERIA_SITE_NAME === criteriaId ) {
                //textFieldConfig.blankText = 'Enter Site Name';
                textFieldConfig.emptyText = 'Enter Site Name';
                //textFieldConfig.value = 'Enter Site Name';
            }

            if ( sfw.util.CommonConstants.CRITERIA_VENDOR_NAME === criteriaId ) {
                //textFieldConfig.blankText = 'Enter Vendor Name';
                textFieldConfig.emptyText = 'Enter Vendor Name';
                //textFieldConfig.value = 'Enter Vendor Name';
            }

            var valueTextField = new Ext.form.TextField( textFieldConfig );

            return valueTextField;
        },

        // Helper function: create the datefield.
        createValueDateField: function() {
            var dateField = new Ext.form.DateField({
                value: sfw.util.Util.dateCreateTodayOffset(),
                name: 'value',
                format: sfw.util.Globals.dateShortInput,
                hideLabel: true,
                margin: '0 0 0 10',
                minValue: new Date(1970,0,1),
                maxValue: new Date(2037,11,31),
                validator: function( value ) {
                    var date = Ext.Date.parseDate(value, 'Y-m-d', true);
                    var beginDate = new Date(1970,0,1);
                    var endDate = new Date(2037,11,31);
                    if( !date || ( date < beginDate ) || ( date > endDate ) ) {
                        return "Please enter a valid date between 1970-01-01 and 2037-12-31";
                    }
                    return true;
                },
                listeners: {
                    blur: function( thisField ) {
                        if( ! this.isValid() ) {
                            this.setValue( new Date() );
                        }
                    }
                }
            });
            return dateField;
        }

    }

});