Ext.define('sfw.view.results.smartgroup.SmartGroups', {
    extend: 'Ext.app.Controller',

    requires: [
        'sfw.view.results.smartgroup.SmartGroupGeneric'
    ],

    refs: {
        smartGroupGeneric: 'smartGroupGeneric',
        criteriaPanel: '#criteriaPanel',
        smartGroupRadioGroup: 'smartGroupGeneric #colSelectionRadioGroup',
        smartGroupSelectAll: 'smartGroupGeneric #selectAll',
        smartGroupSelectCustom: 'smartGroupGeneric #selectCustom'
    },

    control: {
        smartGroupGeneric: {
            show: 'onShowSmartGroup',
            beforeshow: 'beforeShowSmartGroup'
        },
        criteriaPanel: {
            add: 'addRemoveRuleListener',
            remove: 'addRemoveRuleListener'
        },
        smartGroupSelectAll: {
            change: 'onSelectAll'
        },
        smartGroupSelectCustom: {
            change: 'onSelectCustom'
        }
    },

    initSmartGroup: function(smartGroupWindow) {
        smartGroupWindow.menu = { text: "Overview & Configuration" };
        // this.parent = config.parent;
        // this.id = config.id;

        // Get the 'type' (host, product, advisory, etc.) plus related
        // text we will need at various points
        
        smartGroupWindow.smartGroupType = sfw.util.CommonConstants.SMARTGROUP_TYPES[smartGroupWindow.config.type];
        smartGroupWindow.type = smartGroupWindow.config.type;
        smartGroupWindow.typeUpper = smartGroupWindow.config.typeUpper;
        smartGroupWindow.typePlural = smartGroupWindow.config.typePlural;
        smartGroupWindow.typeUpperPlural = smartGroupWindow.config.typeUpperPlural;

        smartGroupWindow.title = smartGroupWindow.typeUpper + ' Smart Groups: Overview & Configuration';

        smartGroupWindow.dirty = false;

        // this.fields = config.fields;
        // this.columns = config.columns;
        // this.exportColumns = config.exportColumns;

        var actionProviderURL = 'action=smart_groups&which=';
        var baseUrl = actionProviderURL;

        smartGroupWindow.urlOverview = baseUrl + 'overview' + '&smartGroupTextType=' + this.type;
        smartGroupWindow.urlAdd = baseUrl + 'add';
        smartGroupWindow.urlEdit = baseUrl + 'edit';
        smartGroupWindow.urlDelete = baseUrl + 'delete';
        smartGroupWindow.urlGetRules = baseUrl + 'getRules'; // type independent
        smartGroupWindow.urlCompileAsap = baseUrl + 'recompile_asap';

        smartGroupWindow.smartGroupId = 0;
        smartGroupWindow.smartGroupEditable = 1; // default

        // Template Examples
        smartGroupWindow.templateExamplesExist = false;
        // We need to call the function to get the examples, but
        // they load using CommonConstants data that we need to
        // wait for a backend call to retrieve, so just set up the
        // function now and make the call later.
        if ( 'undefined' !== typeof smartGroupWindow.config.constructTemplateExamples
             && 'function' === typeof smartGroupWindow.config.constructTemplateExamples ) {
            smartGroupWindow.templateExamplesExist = true;
            smartGroupWindow.templateExamplesFunction = smartGroupWindow.config.constructTemplateExamples.createDelegate( this );
        }

        smartGroupWindow.description = 'Get an overview of existing configured ' + smartGroupWindow.typeUpper + ' Smart Groups, and configure new Smart Groups.';
    },

    onShowSmartGroup: function(page) {
        // Make sure window always opens scrolled to the top
        page.center();
        if(page.down("#mainDefinitionPanel")){
            page.down("#mainDefinitionPanel").body.dom.scrollTop = 0;    
        }

        page.setTitle("Configure New Smart Group");
        page.smartGroupId = 0;
        page.smartGroupEditable = 1;

        page.down("#additionalColumnLabel").setHtml(page.config.defaultColumnsText + " Use this form to control which additional columns are shown in the grid view. Mouseover a checkbox for the column description.<br><br>");
        page.down("#containsMatch").setHtml(' Contains ' + page.config.typePlural + ' that match');

        potentialColumns = page.config.potentialColumns;

        // Load the data stores we will need
        page.smartGroupType = sfw.util.CommonConstants.SMARTGROUP_TYPES[page.config.type];
        //console.log("-=-=-=-=-=-=-=-=-=-page.smartGroupType: ", page.smartGroupType);
        var relevantLookupStores = sfw.util.CommonConstants.SMARTGROUP_STORES[page.config.type];
        page.criteriaStore = relevantLookupStores[0];
        page.criteriaActionsStore = relevantLookupStores[1];
        page.criteriaValuesStore = relevantLookupStores[2];

        page.firstCritItemId = page.criteriaStore.data.items[0].id;

        var columnComboBoxes = [];

        var createColumnSelectionCheckbox = function( displayName, columnId, hidden, toolTip ) {
            // var boxId = this.id + '_columnCheckbox_' + columnId;
            var boxId = '_columnCheckbox_' + columnId;
            var newBox = new Ext.form.Checkbox({
                //id: boxId,
                boxLabel: displayName,
                checked: true,
                // Note - the ones hidden are considered checked
                // by default, but we always check by default, and
                // the user can onlyuncheck the non-hidden ones
                hidden: hidden,
                columnId: columnId
            });
            newBox.on('render', function(c) {
                Ext.QuickTips.register({
                    target: c.getEl(),
                    text: toolTip
                });
            });
            return newBox;
        };

        for ( var i=0; i < potentialColumns.length; ++i ) {
            var thisCol = potentialColumns[i];
            var hidden = thisCol.hidden ? true : false;
            var toolTip = thisCol.tooltip ? thisCol.tooltip : false;
            var newBox = createColumnSelectionCheckbox(
                thisCol.displayName,
                thisCol.id,
                hidden,
                toolTip
            );
            columnComboBoxes.push( newBox );
        }

        var customColumnsPanel = page.down("#customColumnsPanel");
        customColumnsPanel.add(columnComboBoxes);

        this.openSmartGroupConfigurationWindow(page.newEditValue);

    },

    addRemoveRuleListener: function( container, component, index ) {
        // We also must only act on the criteriaPanel, not child items
        // (for whom an add/remove also triggers this
        if ( container.name !== 'criteriaPanel' ) {
            return;
        }

        // Make sure we have a panel here - on initial load for an
        // 'edit', it is empty the first time we add something.
        if ( 0 < container.items.length && 0 < container.items.items[0].items.length ) {
            var firstMinusButton = container.items.items[0].items.map.deleteButton;
        } else {
            return;
        }
        if ( 1 === container.items.length ) {
            firstMinusButton.setDisabled(true);
        } else {
            firstMinusButton.setDisabled(false);
        }
    },

    beforeShowSmartGroup: function(page) {
        this.initSmartGroup(page);
        var dynamicDimensions = sfw.util.SharedFunctions.getDynamicDimensions( 600, 900 );
        var interfaceHeight = dynamicDimensions[0];
        var interfaceWidth = dynamicDimensions[1];
        page.setHeight(interfaceHeight);
        page.setWidth(interfaceWidth);

        var businessImpactStore = new Ext.data.ArrayStore({
            fields: [ 'id', 'type' ]
            ,data: [ [1, 'Critical'], [2, 'High'], [3, 'Medium'], [4, 'Minor'], [5, 'Low'] ]
            ,id: 1
        });
    },

    onSelectAll: function(field, newValue, oldValue) {
        if ( newValue ) {
            // Select all and grey out the selection area
            this.getSmartGroupGeneric().down("#customColumnsPanel").setDisabled(true);
            customColumnsPanel = this.getSmartGroupGeneric().down("#customColumnsPanel");
            customColumnsPanel.items.items.forEach((checkbox) => {
                checkbox.setValue(true);
            });
        }
    },

    onSelectCustom: function(field, newValue, oldValue) {
        if ( newValue ) {
            // Enable the selection area, but don't check or uncheck anything
            this.getSmartGroupGeneric().down("#customColumnsPanel").setDisabled(false);
        }
    },

    /*
    * Helper functionto populate the smart group criteria panel
    * based on passed in data. This can be passed in from either
    * the ajax call return, or from our template use cases.
    *
    * Note: Not all fields are necessarily present in the example
    * use cases.
    */
    populateSmartGroupPanel: function( data ) {

        var i, j, criteria, action, value, valueText, thisRulePanel;
        var listData, fields, idProperty, myRecordType, thisRecord, records= [];

        for ( i=0; i < data.length; ++i ) {
            var thisRow = data[i];
            criteria = parseInt( thisRow.criteria, 10 ); // criteria is mandatory
            action = thisRow.action ? parseInt( thisRow.action, 10 ) : 0;
            value = thisRow.value ? parseInt( thisRow.value, 10 ) : 0;
            valueText = thisRow.value_text ? defaults.htmlSpecialCharsDecodeAlsoQuot( thisRow.value_text ) : '';

            // This will only exist for the hosts/sites/products criteria types
            listData = thisRow.listData ? thisRow.listData : [];

            this.criteriaPanel.add( this.createRule(criteria) );
            thisRulePanel = this.criteriaPanel.items.items[i];

            switch( criteria ) {
            case CommonConstants.CRITERIA_PRODUCT_STATUS:
            case CommonConstants.CRITERIA_SILENT_INSTALLATION:
            case CommonConstants.CRITERIA_PLATFORM:
            case CommonConstants.CRITERIA_SOFT_TYPE:
            case CommonConstants.CRITERIA_ZERO_DAY:
            case CommonConstants.CRITERIA_WHERE_TYPE:
            case CommonConstants.CRITERIA_SOLUTION_STATUS:
                thisRulePanel.items.items[2].setValue( value );
                break;

            case CommonConstants.CRITERIA_CRITICALITY:
            case CommonConstants.CRITERIA_SCORE:
            case CommonConstants.CRITERIA_IMPACT_TYPE:
            case CommonConstants.CRITERIA_CVSS_SCORE:
            case CommonConstants.CRITERIA_THREATSCORE:
            case CommonConstants.CRITERIA_CVSS2_SCORE:
            case CommonConstants.CRITERIA_CVSS3_SCORE:
                thisRulePanel.items.items[2].setValue( action );
                thisRulePanel.items.items[3].setValue( value );
                break;

            case CommonConstants.CRITERIA_HOSTS:
            case CommonConstants.CRITERIA_SITES:
            case CommonConstants.CRITERIA_PRODUCTS:
            case CommonConstants.CRITERIA_OPERATINGSYSTEMS:
            case CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD:
            case CommonConstants.CRITERIA_KB_ARTICLE:
            case CommonConstants.CRITERIA_CVE_NUMBER:
                // In these cases, we have additional data returned to us.
                // We need to pack the data into the same format the store
                // is expecting.

                // Set the action
                thisRulePanel.items.items[2].setValue( action );

                records= []; // reset
                if ( listData.length ) {
                    fields = sfw.itemSelectors.getDataFields( criteria, 'fields' );
                    idProperty = fields[0].name;
                    myRecordType = new Ext.data.Record.create( fields );
                    for ( j=0; j < listData.length; ++j ) {
                        thisRecord = new myRecordType( listData[j] );
                        thisRecord.id = listData[j][idProperty];
                        records.push( thisRecord );
                    }

                    thisRulePanel.currentSelected = {
                        data: records
                        ,idProperty: idProperty
                    };
                }

                thisRulePanel.items.items[3].setText('(' + records.length + ' selected...)');
                break;

            case CommonConstants.CRITERIA_SAID_DATE:
            case CommonConstants.CRITERIA_LAST_SCANNED:
            case CommonConstants.CRITERIA_ADVISORY_DATE:

                thisRulePanel.items.items[2].setValue( action );

                // As of ExtJs 3.4.0,setValue doesn't fire the select
                // event, so we do it manually.  Note- this might be
                // updated in a future release, as it is an enhancement
                // request within extjs
                var combo = thisRulePanel.items.items[2];
                var record = combo.findRecord( combo.valueField, action );
                var index = combo.store.indexOf( record );
                thisRulePanel.items.items[2].fireEvent( 'select', combo, record, index );

                // Set the date / number of days (both stored as valueText)
                thisRulePanel.items.items[3].setValue( valueText );
                break;
            case CommonConstants.CRITERIA_PRODUCT_NAME:
            case CommonConstants.CRITERIA_HOST_NAME:
            case CommonConstants.CRITERIA_SITE_NAME:
            case CommonConstants.CRITERIA_VENDOR_NAME:
                thisRulePanel.items.items[2].setValue( action );
                thisRulePanel.items.items[3].setValue( valueText );
                break;
            }
        }
        this.criteriaPanel.doLayout();
    },

    // Now configure the window itself and associated buttons

    /*
    * Populates the Smart Group creation modal window with
    * supplied rules, title, and description based on the data
    * from our Template Examples.
    *
    * Note: Up to now, we have not included changing the logic type or
    * business impact in our canonical examples, so there is no support
    * for these fields in this function. Add only if needed.
    *
    * @param {string} Smartgroup Name (title)
    * @param {descrition} Smartgroup description field text
    * @param {array} Array of the rules
    */
    loadSmartGroupTemplateData: function( title, description, rules ) {
        // First reload/reset all data in the form and update the window title
        this.resetAll();

        // First deal with setting the descriptive meta-data
        this.smartGroupNameField.setValue( title + ' (FROM TEMPLATE - MODIFY)' );
        this.smartGroupDescriptionField.setValue(  description + ' (FROM TEMPLATE - MODIFY)' );
        /*
        Add to params if needed:
        - this.logicTypeCombo.setValue( logic_type );
        - this.businessImpactCombo.setValue( business_impact );
        */

        this.populateSmartGroupPanel( rules );

        self.helpWindow.hide();
    },

    // --- Associated Functionality for Criteria Panel / Rules ---

    /*
    * Helper funciton to create the combobox for either
    * criteria or action / value
    *
    * RulePanel is the current rulePanel we are in.  Combotype
    * is one of 'criteria', 'action', 'value', the data store
    * is passed in, and critType is an optional 3rd parameter
    * that is only used for the speical cases
    * (i.e. crit=said_date)
    */
    createCombo: function( rulePanel, comboType, store, critType ) {

        const self = this;

        // This is the generic combo based on input. If we are a
        // 'value' combo, or most types of 'action', we're done
        // here. We add some logic below for 'criteriaType' combos
        // and some special cases of 'action'
        
        var combo = new Ext.form.ComboBox({
            width: 150,
            mode: 'local',
            name: comboType,
            editable: false,
            triggerAction: 'all',
            forceSelection: true,
            autoSelect: true,
            store: store,
            valueField: 'id',
            displayField: 'description',
            hideLabel: true,
            // tpl: '<tpl for="."><div ext:qtip="{rowDescription}" class="x-combo-list-item {recordCls}">{description}</div></tpl>',
            listeners: {
                beforeselect: function( combo, record, index ) {
                    if ( record.get( 'recordCls' ) == 'x-combo-list-item-disabled' ) {
                        return false;
                    }
                }
            }
        });

        if ( comboType === 'criteria' ) {
            combo.on({
                select: function( combo, record, index ) {
                    // determine if the value changed from the old
                    // value so we know if we need to reset/delete all
                    // existing creations vs. do nothing
                    if ( combo.originalValue != record.id ) {
                        var criteriaPanel = rulePanel.ownerCt;
                        criteriaPanel.suspendEvents();
                        rulePanel.removeAll();
                        if ( 'undefined' !== typeof rulePanel.currentSelected ) {
                            delete rulePanel.currentSelected;
                        }
                        rulePanel.add( self.createRuleItems( record.id, rulePanel ) );
                        criteriaPanel.resumeEvents();
                        criteriaPanel.fireEvent('add', criteriaPanel);
                        // criteriaPanel.doLayout();
                    }
                    // Else, don't do anything...
                }
            });

        } else if ( comboType === 'action'
                    && ( critType === sfw.util.CommonConstants.CRITERIA_SAID_DATE
                         || critType === sfw.util.CommonConstants.CRITERIA_LAST_SCANNED
                         || critType === sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE )
                  ) {

            // Set the initial "old value" as the first selectable item in the combobox
            combo.oldValue = parseInt( combo.store.data.items[0].data.id, 10 );

            combo.on({
                // Listen for 'select' so we can change the form if
                // needed. Recall that if we switch from a 'relative'
                // to an 'absolute' date type (or vice-versa) we must
                // modify the layout of the form accordingly
                'select': function( combo, record, index ) {

                    var newValue = parseInt( record.data.id, 10 );
                    if ( newValue === combo.oldValue ) {
                        // didn't change type, don't change form - just break out.
                        return;
                    }

                    // Logic - a value of 7 or 8 is a 'relative to
                    // today' date type, else we have an absolute date
                    // type. If we've changed type, modify the
                    // form. I.e., the 'relative to today' dates have
                    // the number field and "days" string attached
                    // instead of just a datefield.
                    var newRelative = (newValue === 7 || newValue === 8);
                    var oldRelative = (combo.oldValue === 7 || combo.oldValue === 8);

                    // We "change" iff XOR of these is true
                    if ( newRelative ^ oldRelative ) { // bit-wise XOR
                        if ( newRelative ) {
                            rulePanel.remove( rulePanel.items.items[3] ); // date field
                            rulePanel.items.insert( 3, self.createValueNumberField( critType ) );
                            rulePanel.items.insert( 4, new Ext.form.Label( { text: ' days', margins: '5 0 0 0' } ) );
                        } else {
                            rulePanel.remove( rulePanel.items.items[4] );
                            rulePanel.remove( rulePanel.items.items[3] );
                            rulePanel.items.insert( 3, self.createValueDateField() );
                        }
                        // rulePanel.doLayout();
                    }
                    combo.oldValue = newValue;
                }
            });
        }

        return combo;
    },

    // Helper function: create the "+" or "-" button for adding or
    // removing a criteria. Type muyt be one of '+' or '-'.
    createPlusMinusButton: function( type, rulePanel ) {
        const self = this;
        const smartGroupWindow = this.getSmartGroupGeneric();
        const criteriaPanel = self.getCriteriaPanel();

        var button = new Ext.Button({
            text: type,
            width: 25
        });

        if ( '+' === type ) {
            button.handler = function() {
                criteriaPanel.add( self.createRule( smartGroupWindow.firstCritItemId) );
                // criteriaPanel.doLayout();
            };
        } else {
            button.itemId =  'deleteButton';
            button.handler = function() {
                criteriaPanel.remove( rulePanel );
            };
        }

        return button;
    },

    // Helper function: create the datefield.
    createValueDateField: function() {
        var dateField = new Ext.form.DateField({
            value: sfw.util.Util.dateCreateTodayOffset(),
            name: 'value',
            format: sfw.util.Globals.dateShortInput,
            hideLabel: true,
            margins: '0 0 0 10',
            minValue: new Date(1970,0,1),
            maxValue: new Date(2037,11,31),
            validator: function( value ) {
                var date = Ext.Date.parseDate(value, 'Y-m-d', true);
                var beginDate = new Date(1970,0,1);
                var endDate = new Date(2037,11,31);
                if( !date || ( date < beginDate ) || ( date > endDate ) ) {
                    return "Please enter a valid date between 1970-01-01 and 2037-12-31";
                }
                return true;
            },
            listeners: {
                blur: function( thisField ) {
                    if( ! this.isValid() ) {
                        this.setValue( new Date() );
                    }
                }
            }
        });
        return dateField;
    },

    // Generic number field for value entries based on
    // criteria type
    createValueNumberField: function( criteriaId ) {

        // Init generic properties here - change or add
        // anything needed for specific criteria below
        var fieldConfig = {
            allowBlank: false,
            name: 'value',
            fieldLabel: '',
            anchor: '100%',
            allowDecimals: false,
            allowNegative: false,
            width: 81,
            margin:'0 5 0 10',
            autoCreate: { tag: 'input', type: 'text', size: '20', autocomplete: 'off' }
        };

        // This is percentage value, so set relevant fields
        if ( sfw.util.CommonConstants.CRITERIA_SCORE === criteriaId ) {
            fieldConfig.blankText = 'Score(%)';
            fieldConfig.emptyText = '(%)';
            //fieldConfig.vtype = 'percentageTest';
            fieldConfig.value = 80;
            fieldConfig.maxValue = 100;
            fieldConfig.minValue = 0;
            fieldConfig.maxLength = 3;
            fieldConfig.autoCreate.maxlength = '3';
        }

        // This is percentage value, so set relevant fields
        if ( sfw.util.CommonConstants.CRITERIA_CVSS_SCORE === criteriaId ) {
            fieldConfig.blankText = 'Value from 0-10';
            fieldConfig.emptyText = 'Value from 0-10';
            fieldConfig.value = 7;
            fieldConfig.maxValue = 10;
            fieldConfig.minValue = 0;
            fieldConfig.maxLength = 2;
            fieldConfig.autoCreate.maxlength = '2';
        }

        // This is percentage value, so set relevant fields
        if ( sfw.util.CommonConstants.CRITERIA_CVSS2_SCORE === criteriaId ) {
            fieldConfig.blankText = 'Value from 0-10';
            fieldConfig.emptyText = 'Value from 0-10';
            fieldConfig.value = 7;
            fieldConfig.maxValue = 10;
            fieldConfig.minValue = 0;
            fieldConfig.maxLength = 2;
            fieldConfig.autoCreate.maxlength = '2';
        }

        // This is percentage value, so set relevant fields
        if ( sfw.util.CommonConstants.CRITERIA_CVSS3_SCORE === criteriaId ) {
            fieldConfig.blankText = 'Value from 0-10';
            fieldConfig.emptyText = 'Value from 0-10';
            fieldConfig.value = 7;
            fieldConfig.maxValue = 10;
            fieldConfig.minValue = 0;
            fieldConfig.maxLength = 2;
            fieldConfig.autoCreate.maxlength = '2';
        }

        if ( sfw.util.CommonConstants.CRITERIA_THREATSCORE === criteriaId ) {
            fieldConfig.blankText = 'Value from 0-100';
            fieldConfig.emptyText = 'Value from 0-100';
            fieldConfig.value = 70;
            fieldConfig.maxValue = 100;
            fieldConfig.minValue = 0;
            fieldConfig.maxLength = 3;
            fieldConfig.autoCreate.maxlength = '3';
        }

        if ( sfw.util.CommonConstants.CRITERIA_SAID_DATE === criteriaId
             || sfw.util.CommonConstants.CRITERIA_LAST_SCANNED === criteriaId
             || sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE === criteriaId ) {
            fieldConfig.maxValue = 9999;
            fieldConfig.minValue = 1;
            fieldConfig.maxLength = 4;
            fieldConfig.autoCreate.maxlength = '4';
        }

        var valueNumberField = new Ext.form.NumberField( fieldConfig );

        return valueNumberField;
    },

    // Generic text field for value entries based on
    // criteria type
    createValueTextField: function( criteriaId ) {

        // Init generic properties here - change or add
        // anything needed for specific criteria below
        var textFieldConfig = {
            allowBlank: false,
            name: 'value',
            fieldLabel: '',
            anchor: '100%',
            allowNegative: false,
            width: 120,
            margin:'0 5 0 10',
            autoCreate: { tag: 'input', type: 'text', size: '100', autocomplete: 'off' }
        };

        // This is percentage value, so set relevant fields
        if ( sfw.util.CommonConstants.CRITERIA_PRODUCT_NAME === criteriaId ) {
            textFieldConfig.blankText = 'Enter Product Name';
            textFieldConfig.emptyText = 'Enter Product Name';
            textFieldConfig.value = 'Enter Product Name';
        }

        if ( sfw.util.CommonConstants.CRITERIA_HOST_NAME === criteriaId ) {
            textFieldConfig.blankText = 'Enter Host Name';
            textFieldConfig.emptyText = 'Enter Host Name';
            textFieldConfig.value = 'Enter Host Name';
        }

        if ( sfw.util.CommonConstants.CRITERIA_SITE_NAME === criteriaId ) {
            textFieldConfig.blankText = 'Enter Site Name';
            textFieldConfig.emptyText = 'Enter Site Name';
            textFieldConfig.value = 'Enter Site Name';
        }

        if ( sfw.util.CommonConstants.CRITERIA_VENDOR_NAME === criteriaId ) {
            textFieldConfig.blankText = 'Enter Vendor Name';
            textFieldConfig.emptyText = 'Enter Vendor Name';
            textFieldConfig.value = 'Enter Vendor Name';
        }


        var valueTextField = new Ext.form.TextField( textFieldConfig );

        return valueTextField;
    },

    // Helper function: Create the window from which we select
    // individual hosts / sites / product
    createItemSelectorWindow: function( criteriaType, rulePanel, customTitle, fieldName ) {

        if(!rulePanel) {
            rulePanel = {};
        }

        // If we already generated an item selector and modal window
        // for this rulePanel with this criteriaType, we skip the
        // generation of both. Otherwise, generate them both. If we
        // changed criteriaType within the same rulePanel, remove
        // them (if they exist) to ensure we regenerate them both.

        // Note: By definition, rulePanel.criteriaType = criteriaType,
        // since we set that explicitly before we call this
        // function. So if we want to ensure that we are creating the
        // right kind of selectorWindow here, we must check that we
        // are not using a window from a past criteria type
        if (rulePanel && rulePanel.modalWindow &&
             rulePanel.modalWindow.criteriaType != criteriaType ) {
            delete rulePanel.itemSelector;
        }

        if ( !rulePanel.itemSelector ) {
            const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");

            switch( criteriaType ){
                case sfw.util.CommonConstants.CRITERIA_HOSTS:
                    rulePanel.itemSelector = localItemSelector.createHostSelector();
                break;
                case sfw.util.CommonConstants.CRITERIA_SITES:
                    rulePanel.itemSelector = localItemSelector.createSiteSelector();
                break;
                case sfw.util.CommonConstants.CRITERIA_PRODUCTS:
                    rulePanel.itemSelector = localItemSelector.createProductSelector();
                break;
                default:
                    rulePanel.itemSelector = localItemSelector.createItemSelector( criteriaType );
                break;
            }

            // if we need a new itemSelector, we need a new window
            if (rulePanel.modalWindow ) {
                rulePanel.modalWindow.close();
                delete rulePanel.modalWindow;
            }
        }

        if(customTitle === 'Site Selection' && rulePanel.itemSelector) {
            const toolbar = rulePanel.itemSelector.getDockedItems('toolbar[dock="top"]')[0];
            if (toolbar) {
                const hasADIntegration = !sfw.ActiveDirectorySettings.hasADIntegration();
                toolbar.add({
                    text: 'AD Filter...',
                    disabled: hasADIntegration,
                    handler: 'handleADSelect'
                });
            }
        }

        if ( !rulePanel.modalWindow ) {
            rulePanel.modalWindow = new Ext.window.Window({
                width: 800,
                criteriaType: criteriaType,
                //id: rulePanel.id + '_itemSelectorWindow',
                title: typeof customTitle === "undefined" ? "" : customTitle,
                height: 550,
                border: false,
                style: {
                    zIndex: 99999,    
                },
                layout: 'border',
                closeAction: 'hide',
                modal: true,
                items: [
                    rulePanel.itemSelector
                ],

                controller: {

                    handleADSelect: function (btn) {
                        const selectedGrid = this.getView().down('#selectedGrid'),
                        selectedStore = selectedGrid.getStore();
                        console.log("selectedStore");console.log(selectedStore);

                        const availableGrid = this.getView().down('#availableGrid');
                        availableStore = availableGrid.getStore();
                        console.log("availableStore");console.log(availableStore);

                        var adDialog = Ext.create('sfw.view.common.ActiveDirectoryDialog', {
                            animateTarget: btn,
                            listeners: {
                                close: function (dialog) {
                                    var node = dialog.getSelectedDirectory();console.log("node");console.log(node);

                                    if (node) {
                                        Ext.Ajax.request({
                                            url: 'action=active_directory&which=getNodeAndChildren&',
                                            method: 'GET',
                                            dataType: 'json',
                                            params: {
                                                'node': node.id
                                            },
                                            success: function (response, opts) {
                                                var chidData = Ext.decode(response.responseText, true);
                                                if(chidData.success){
                                                    var nodeDetails = chidData.data.rows;
                                                    var availableselection = [];
                                                    nodeDetails.map( function( nodeDetails ) {
                                                        if ( null === selectedStore.findRecord(selectedStore.config.idProperty, nodeDetails.group_id) ) {
                                                            selectedStore.add(nodeDetails);
                                                        }
                                                        if ( null !== availableStore.findRecord(availableStore.config.idProperty, nodeDetails.group_id) ) {
                                                            availableselection.push(availableStore.findRecord(availableStore.config.idProperty, nodeDetails.group_id));
                                                        }
                                                    });
                                                    selectedGrid.getSelectionModel().selectAll();
                                                    availableGrid.getSelectionModel().select(availableselection);
                                                }
                                            },
                                            failure: function (response, opts) {
                                            }
                                        });
                                    }
                                }
                            }
                        });

                        adDialog.show();
                    }

                },
                
                listeners: {
                    // Loads the store for the 'Available'
                    // grid when the window is shown so the
                    // body property exists for the
                    // ItemSelector to reference.
                    beforeshow: function () {
                        // First we make sure the selection
                        // panel is pre-selected with the last
                        // thing we actually saved in a
                        // previous attempt. First clear it,
                        // in case we selected sometehing and
                        // cancelled without saving. We always
                        // load directly from the
                        // currentSelected.

                        // rulePanel.itemSelector.availableStore.removeAll();
                        // rulePanel.itemSelector.selectedStore.removeAll();
                        if ( rulePanel.currentSelected
                             && rulePanel.currentSelected.data ) {
                            // setTimeout(() => {
                                //console.log("rulePanel.currentSelected.data:  ", rulePanel.currentSelected.data);
                                 rulePanel.itemSelector.config.selectedStore.add( rulePanel.currentSelected.data );
                                // rulePanel.itemSelector.down("#selectedGrid").getStore().add( rulePanel.currentSelected.data );    
                            // }, 2000);
                            
                            
                        }

                        // Flag the selectedStore as loaded so when
                        // reloading the available store and trying to
                        // highlight, we catch the overlapping records
                        // there.

                        // rulePanel.itemSelector.selectedStore.loaded = true;

                        // Trigger a load of the available store, which
                        // will highlight any overlap with the selected
                        // store

                        // rulePanel.itemSelector.availableStore.load();
                    }
                },
                
                buttons: [{
                    text: 'Save',
                    // id: rulePanel.id + '_saveRulesButton',
                    handler: function() {
                        // We keep the current selections of the
                        // itemSelector in a 'currentSelected'
                        // variable so we can load from it on
                        // subsequent reopenings of the selector
                        // window.
                        const selectedGrid = rulePanel.itemSelector.down("#selectedGrid");


                        rulePanel.currentSelected = {
                            data: selectedGrid.getSelection(),
                            idProperty: selectedGrid.getStore().idProperty
                        };

                        // Update the button text
                        var count = rulePanel.currentSelected.data.length;

                        var reportWindow = Ext.ComponentQuery.query('window[xtype=reporting.generateReport]');
                        if(reportWindow && reportWindow.length){
                            reportWindow = reportWindow[0];
                            if(fieldName == 'CSI_SITES_site_selection_list'){
                                var siteSelectionList = '';
                                for (i = 0; i < rulePanel.currentSelected.data.length; i++) {
                                    if(i>0){
                                        if (rulePanel.currentSelected.data[i].data.group_id){
                                            siteSelectionList += ','+rulePanel.currentSelected.data[i].data.group_id;
                                        }
                                    }else{
                                        if (rulePanel.currentSelected.data[i].data.group_id){
                                            siteSelectionList += rulePanel.currentSelected.data[i].data.group_id;
                                        }
                                    }
                                }
                                reportWindow.reportData[fieldName] = siteSelectionList;
                            }else if(fieldName == 'CSI_SITES_smg_selection_list'){
                                var hostSelectionList = '';
                                for (i = 0; i < rulePanel.currentSelected.data.length; i++){
                                    if(i>0){
                                        if(rulePanel.currentSelected.data[i].data.id){
                                            hostSelectionList += ','+rulePanel.currentSelected.data[i].data.id;
                                        }
                                    }else{
                                        if(rulePanel.currentSelected.data[i].data.id){
                                            hostSelectionList += rulePanel.currentSelected.data[i].data.id;
                                        }
                                    }
                                }
                                reportWindow.reportData[fieldName] = hostSelectionList;
                            }
                        }
                        else {
                            rulePanel.down("#selectionButton").setText( '(' + count + ' selected...)' );
                        }

                        rulePanel.modalWindow.hide();
                    }
                },{
                    text: 'Cancel',
                    handler: function() {
                        // Clear the available store here. We'll reload selected and available next time we open the window - for now we want to clear it so we don't have artifacts leftover froma cancel.
                        // rulePanel.itemSelector.smAvailable.clearSelections( false );
                        rulePanel.itemSelector.down('#availableGrid').getSelectionModel().deselectAll()
                        rulePanel.modalWindow.hide();
                    }
                }],
                keys: [{ // Handle form ENTER key press
                    key: [ Ext.event.Event.ENTER ],
                    handler: function ( keyCode, eventObject ) {
                        var tagName = eventObject.getTarget().tagName;
                        if ( 'A' === tagName || 'BUTTON' === tagName ) {
                            // Don't handle pressing ENTER on a select list item or button
                            return;
                        }
                        // Else, call handler for the local save-rules
                        // button. Note, this doesn't save anything to
                        // the backend, just saves the current
                        // selections locally. We handle weather or
                        // not we can save the actual smartgroup
                        // (based on our read-only vs. read-write
                        // access) separately. At this point, it isn't
                        // relevant.
                        Ext.getCmp( rulePanel.id + '_saveRulesButton' ).handler.call();
                    }
                }]
            });
        }

        rulePanel.modalWindow.show();
        return rulePanel.modalWindow;
    },

    // Helper function: create the button we use to create an
    // ItemSelector. It is only more complicated than a standard
    // button because we have the dynamic text inside the button
    // with how many are already selected.
    createItemSelectorButton: function( criteriaType, rulePanel ) {
        const self = this;
        const selectorButton = new Ext.Button({
            itemId: 'selectionButton',
            criteriaType: criteriaType,
            text: '( 0 selected...)', // initial value - will change upon selection
            width: 150,
            handler: function() {
                self.createItemSelectorWindow( criteriaType, rulePanel );
            }
        });
        return selectorButton;
    },

    // Helper Function: Extract action/value data from the mixed
    // collection and convert to an ArrayStore for use in a combo box
    convertActionValueDataToStore: function( rawData, fields ) {
        var data = [], i, j;
        var datum;
        for ( i=0; i < rawData.length; ++i ) {
            datum = rawData.items[i].data;
            retrievedData = [];
            for ( j=0; j < fields.length; ++j ) {
                retrievedData[j] = datum[fields[j].name];
            }
            data[i] = retrievedData;
        }

        var store = new Ext.data.ArrayStore({
            fields: fields,
            data: data
        });

        return store;
    },

    // Create the line for each criteria item entry, i.e. based on the
    // selection of criteriaId, we must dynamically populate the
    // action/value combo boxes, and other dynamic displays based on
    // selections
    createRuleItems: function( criteriaId, rulePanel ) {
        const self = this;
        const smartGroupWindow = this.getSmartGroupGeneric();

        criteriaId = parseInt( criteriaId, 10 );

        // Recall from comments in 'createRule': we must set the criteria
        // type here so we capture anytime we re-generate the panel as the
        // result of a change of criteria via the combo-box. We must do
        // this to propogate necessary changes in itemSelector windows /
        // buttons (rulePanel.modalWindow)
        rulePanel.criteriaType = criteriaId;

        // Get the possible action / value lists associated with the
        // given criteria type and create / add the appropriate combo
        // boxes, text fields, etc.
        var index, record, actionData = [], valueData = [], actionStore, valueStore;

        // For the given passed in criteriaId, get the associated action and value data
        index = smartGroupWindow.criteriaStore.find( 'id', criteriaId );
        if ( -1 === index ) {
            // criteria doesn't exist - we can't carry on... something is horribly wrong.
            return;
        }

        // Otherwise...
        record = smartGroupWindow.criteriaStore.getAt(index).data;

        if ( 0 < record.action_type ) {
            // Pull out the actions corresponding to this action_type
            actionData = smartGroupWindow.criteriaActionsStore.query( 'type', record.action_type );
            // actionData is a mixedCollection - put it into a store format
            actionStore = self.convertActionValueDataToStore( actionData, sfw.util.CommonConstants.actionStoreFields );
        }

        if ( 0 < record.value_type ) {
            valueData = smartGroupWindow.criteriaValuesStore.query( 'type', record.value_type );
            valueStore = self.convertActionValueDataToStore( valueData, sfw.util.CommonConstants.valueStoreFields );
        }

        var items = [];
        var criteriaCombo = self.createCombo( rulePanel, 'criteria', smartGroupWindow.criteriaStore );
        criteriaCombo.setValue( record.id);
        items.push( criteriaCombo );

        items.push( { xtype: 'label', text: ' is ', margin:'5 5 0 5' } );

        if ( actionData.length ) {
            var actionCombo = self.createCombo( rulePanel, 'action', actionStore, criteriaId );
            // Set the combo to the first data item available
            //actionCombo.setValue( actionData.keys[0] );
            actionCombo.setValue( actionData.items[0] );
            items.push( actionCombo );
        }

        if ( valueData.length ) {
            var valueCombo = self.createCombo( rulePanel, 'value', valueStore );
            // valueCombo.setValue( valueData.keys[0] );
            valueCombo.setValue( valueData.items[0] );
            items.push( valueCombo );

        } else {
            switch ( criteriaId ) {
            case sfw.util.CommonConstants.CRITERIA_SAID_DATE:
            case sfw.util.CommonConstants.CRITERIA_LAST_SCANNED:
            case sfw.util.CommonConstants.CRITERIA_ADVISORY_DATE:
                items.push( self.createValueDateField() );
                break;
            case sfw.util.CommonConstants.CRITERIA_HOSTS:
            case sfw.util.CommonConstants.CRITERIA_SITES:
            case sfw.util.CommonConstants.CRITERIA_CVE_NUMBER:
            case sfw.util.CommonConstants.CRITERIA_PRODUCTS:
            case sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMS:
            case sfw.util.CommonConstants.CRITERIA_OPERATINGSYSTEMSBUILD:
            case sfw.util.CommonConstants.CRITERIA_KB_ARTICLE:
                rulePanel.currentSelected = {}; // define so it exists - then if nothing is selected, we can better identify this case when we try to save the SG
                items.push( self.createItemSelectorButton( criteriaId, rulePanel ) );
                break;
            case sfw.util.CommonConstants.CRITERIA_SCORE:
            case sfw.util.CommonConstants.CRITERIA_CVSS_SCORE:
            case sfw.util.CommonConstants.CRITERIA_CVSS2_SCORE:

            case sfw.util.CommonConstants.CRITERIA_CVSS3_SCORE:
            case sfw.util.CommonConstants.CRITERIA_THREATSCORE:
                items.push( self.createValueNumberField( criteriaId ) );
                break;
            case sfw.util.CommonConstants.CRITERIA_PRODUCT_NAME:
            case sfw.util.CommonConstants.CRITERIA_HOST_NAME:
            case sfw.util.CommonConstants.CRITERIA_SITE_NAME:
            case sfw.util.CommonConstants.CRITERIA_VENDOR_NAME:
                items.push( self.createValueTextField( criteriaId ) );
                break;
            default:
                break;
            }
        }

        items.push( { xtype: 'tbspacer', flex:1 } );
        items.push( self.createPlusMinusButton('+', rulePanel) );
        items.push( self.createPlusMinusButton('-', rulePanel) );

        return items;
    },

    createRule: function( criteriaId ) {

        // Each rulepanel must have the criteria type with the rulePanel
        // so that we know to regenerate the itemSelector if we change
        // criteria, i.e., from 'hosts' to 'sites'.

        // We reset these values in 'createRuleItems' to capure
        // regenerations due to combo-box selection of different criteria
        // Set initial values here.

        // Set unique id to include the sg_id plus the rule position
        // We need to distinguish individual rule panels within a criteria panel
        // var smartGroupIdString = '_' + this.mainScope.smartGroupId.toString();
        var lengthString = '_0';
        if ( this.getCriteriaPanel().items ) {
            lengthString = '_' + this.getCriteriaPanel().items.length.toString();
        }

        var rulePanel = new Ext.form.FormPanel({
            //id: this.id + '_rulePanel' + smartGroupIdString + lengthString
            criteriaType: criteriaId,
            border: false,
            padding: 5,
            anchor:'100%',
            layout:'hbox',
            layoutConfig: {
                padding: '5 0 0 0',
                margin:'0 0 0 0'
            },
            defaults: {
                margin:'0 5 0 5'
            }
        });

        // Add the actual content and logic to the rulePanel
        rulePanel.add( this.createRuleItems( criteriaId, rulePanel ) );

        return rulePanel;
    },

    // ----- END of Functionality for Criteria Panel / Rules -----

    // ------- Associated Functionality for the Main Grid -------

    // Helper function to populate the custom column panel with
    // data. This just involves checking the appropriate
    // checkboxes based on the passed in data. The first parameter
    // controls if we check the selectAll radio button or not. If
    // so, the 2nd parameter is always 0.
    populateCustomColumnPanel: function ( allCustomColumns, customColumnData ) {

        var columnPanel = this.smartGroupWindow.columnSelectionPanel;

        if ( allCustomColumns ) {
            // Check the select all radio and let the handlers do
            // the rest.
            columnPanel.items.items[1].items.items[0].setValue(true);
            return;
            // Shortcut out of this function - we're done here.
        }

        // Else...

        // Clear all checkboxes, check the 'selectCustom' radio,
        // and populate based on input
        this.smartGroupWindow.clearCustomColumnCheckboxes();
        columnPanel.items.items[1].items.items[1].setValue(true);

        // The column data is passed in as a string - we must parse out each column
        var columnsToCheck = customColumnData.split(',');
        // Now ensure that each column actually corresponds to a checkbox.
        var columnCheckBoxes = columnPanel.items.items[2].items.items;

        // Go through each checkbox in the panel, and if its ID matches a
        // string in columnsToCheck, check it.
        var aBox, index;
        for ( var i=0; i < columnCheckBoxes.length; ++i ) {
            aBox = columnCheckBoxes[i];
            index = columnsToCheck.indexOf( aBox.columnId );
            if ( -1 !== index ) {
                // Then we have overlap, check this box
                aBox.setValue(true);
                // remove this string from the columnsToCheck array to
                // speed up the sequential search on subsequent loops
                // through the for
                columnsToCheck.splice( index, 1 );
            }
        }
    },

    // Helper function for opening the SG edit/creation
    // window. Titletype is 'new' or 'edit'
    openSmartGroupConfigurationWindow: function( titleType ) {
        const self = this;
        const smartGroupWindow = this.getSmartGroupGeneric();

        /**
         * @msp
         * MSP Users must not have access to Scan Results
         */
        // if (LoginDetails.account.isMspUser()) {
        //     return;
        // }

        // If we haven't created the window before, do so now.
        if (  "undefined" === typeof smartGroupWindow ) {
            const smartGroupGeneric = Ext.create('sfw.view.commonpopupwindows.SmartGroupGeneric').show();
        } else {
            self.resetAll();
        }

        // If this is a new window, load the first default rule. In
        // the case of an edit, let the loader take care of
        // everything.  Also update the title as appropriate, as well
        // as en/dis-abling the templates button based on 'editable'.

        // this.smartGroupWindow.templatesButton.enable();
        if ( 'new' === titleType ) {
            smartGroupWindow.setTitle( 'Configure New Smart Group' );
            this.getCriteriaPanel().add(
                //smartGroupWindow.createRule(this.firstCritItemId)
                self.createRule(smartGroupWindow.firstCritItemId)
            );
        } else {
            // Deal with some special cases for non-editable smartgroups:
            //  - Remove 'edit' from window title
            //  - Disable templates button as well
            var title = 'View/Edit Smart Group';
            if ( !smartGroupWindow.smartGroupEditable ) {
                title = 'View Smart Group  (Editing Prohibited)';
                this.smartGroupWindow.templatesButton.disable();
            }
            this.smartGroupWindow.smartGroupDetailsWindow.setTitle( title );
        }

        // By now modal window created and not visible - show it
        // this.smartGroupWindow.smartGroupDetailsWindow.show();
    },

    // Helper function to load the metadata for an existing
    // smartgroup (saved in this.smartGroupId) into the creation
    // wizard window. By construction,the window is fresh and
    // empty at this point.
    loadSmartGroupConfiguration:  function() {

        var self = this;

        // Get index of smartgroup in the store so we know it exists
        var index = this.mainStore.find( 'id', this.smartGroupId );
        if ( -1 === index ) {
            return false;
        }

        // Retreive all the SG metadata
        var metaData = {};
        var record = this.mainStore.getAt( index ).data;
        metaData.name = defaults.htmlSpecialCharsDecodeAlsoQuot( record.name );
        metaData.description = defaults.htmlSpecialCharsDecodeAlsoQuot( record.description );
        metaData.business_impact = record.business_impact;
        metaData.logic_type = defaults.htmlSpecialCharsDecodeAlsoQuot( record.logic_type );
        if ( 'any' === metaData.logic_type ) {
            metaData.logic_type = 2;
        } else {
            metaData.logic_type = 1;
        }

        // Set the fields of the SG with the metadata found above
        this.smartGroupWindow.smartGroupNameField.setValue( metaData.name );
        this.smartGroupWindow.smartGroupDescriptionField.setValue( metaData.description );
        this.smartGroupWindow.logicTypeCombo.setValue( metaData.logic_type );
        this.smartGroupWindow.businessImpactCombo.setValue( metaData.business_impact );

        // Set the custom columns that were previously chosen for this SG.
        var allCustomColumns = record.all_custom_columns;
        var customColumnData = 0;
        if ( !allCustomColumns ) {
            customColumnData = record.custom_columns;
        }
        this.populateCustomColumnPanel( allCustomColumns, customColumnData );

        // Get the existing rules for this SG from the DB, and once
        // gotten, populate the form
        var url = this.urlGetRules;
        Ext.Ajax.request({
            url: url
            ,params: {
                smartGroupId: self.smartGroupId
            }
            ,success: function( response ) {
                var responseData = Ext.util.JSON.decode( response.responseText );
                var data = responseData.data;
                self.smartGroupWindow.populateSmartGroupPanel( data );
            }
            ,failure: function() {
                Ext.Msg.alert( 'Error', 'Unexpected error - could not retrieve Smart Group metadata.' );
            }
        });

        this.smartGroupWindow.mainDefinitionPanel.setDisabled( !smartGroupWindow.smartGroupEditable );

    },

    // Helper funciton to clear all data in the main definition
    // panel of a smart group
    resetAll: function() {

        const self = this;
        const smartGroupWindow = this.getSmartGroupGeneric();
        const criteriaPanel = self.getCriteriaPanel();

        // Clear/reset each item in the description part of the window
        smartGroupWindow.down("#smartGroupName").reset();
        smartGroupWindow.down("#smartGroupDescription").reset();
        smartGroupWindow.down("#smartGroupBusinessImpact").reset();
        smartGroupWindow.down("#smartGroupContainsMatchAndOr").reset();

        // Clear/reset each item in the Criteria part of the window
        // Note - with the dynamic itemSelector / modalWindow we
        // create in some cases, these must be explicitly deleted /
        // closed so we don't pick up references to them on subsequent
        // attempts.
        // -----
        // Disclaimer: it is conceivable there is a way to set up the
        // various objects so that they auto-destroy... it wasn't working
        // after a lot of messing around with extjs, so we do it manually.
        if ( criteriaPanel.items && criteriaPanel.items.length ) {
            var rulePanels = criteriaPanel.items.items;
            count = criteriaPanel.items.length;
            for ( i = 0; i < count; i++ ) {
                if ( rulePanels[i].modalWindow ) {
                    rulePanels[i].modalWindow.close();
                }
                if ( rulePanels[i].itemSelector ) {
                    delete rulePanels[i].itemSelector;
                }
            }
        }
        criteriaPanel.removeAll();
        // criteriaPanel.doLayout();

        // Check the 'selectAll' radio by default
        self.getSmartGroupSelectAll().setValue(true);
    },

    // Triggered when we press the button for 'save' for a given smart group.
    saveSmartGroup: function() {
        const self = this;
        const smartGroupWindow = self.getSmartGroupGeneric();

        var i, count;
        var rulePanels = self.getCriteriaPanel().items.items;
        var name = smartGroupWindow.down("#smartGroupName").getValue();
        // We know the name exists or we couldn't have got in here. Validate anyway.
        if ( !name ) {
            Ext.Msg.alert( 'Smart Group name missing', 'Please provide a name for the Smart Group.' );
        }
        var description = smartGroupWindow.down("#smartGroupDescription").getValue();
        var logicTypeValue = smartGroupWindow.down("#smartGroupContainsMatchAndOr").getValue();
        var businessImpactValue = smartGroupWindow.down("#smartGroupBusinessImpact").getValue();

        // Get custom columns - if we select all by default,
        // or if we chose our own list
        var isSelectAll = smartGroupWindow.down("#selectAll").getValue();
        var colRadioGroup = Ext.getCmp( this.id + '_colSelectionRadioGroup' );
        
        
        var allCustomColumns = 0;
        if ( isSelectAll ) {
            allCustomColumns = 1;
        }

        var customColumns = '';
        if ( !allCustomColumns ) {
            // Then we need to put together the list selected
            
            var customColumnsPanel = smartGroupWindow.down("#customColumnsPanel");
            var columnComboBoxes = customColumnsPanel.items.items
            var aBox, columnIds = [];
            for( i = 0; i < columnComboBoxes.length; ++i ) {
                var aBox = columnComboBoxes[i];
                if ( aBox.getValue() ) {
                    columnIds.push( aBox.columnId );
                }
            }
            customColumns = columnIds.join(',');
        }

        var allParams = {};

        // Get the descriptive meta-data fields
        allParams["description[name]"] = name;
        allParams["description[description]"] = description;
        allParams["description[andOr]"] = logicTypeValue;
        allParams["description[businessImpact]"] = businessImpactValue;
        allParams["description[allCustomColumns]"] = allCustomColumns;
        allParams["description[customColumns]"] = customColumns;

        // Get the rules/criteria
        var i, j, numberOfRules, rule, ruleDetails, ruleVariables, type, value;
        var listData, idProperty, csvSelected, datum;
        var ruleCriteria;

        numberOfRules = rulePanels.length;

        for ( i = 0; i < numberOfRules; ++i ) {
            rule = rulePanels[i];
            // ruleDetails = rule.findByType('field');
            ruleDetails = rule.items.items;
            aa = ruleDetails;

            ruleVariables = ruleDetails.length;
            for ( j = 0; j < ruleVariables; ++j ) {
                if(ruleDetails[j].name && (ruleDetails[j].name === "criteria" || ruleDetails[j].name === "action" || ruleDetails[j].name === "value")) {
                    type = ruleDetails[j].name;
                    value = ruleDetails[j].getValue();
                    allParams["rules[" + i + "][" + type + "]"] = value;    
                }
                
            }

            /* Handle itemSelector data
            *
            * If it exists Recall - for our selectors, we
            * immediately define currentSelected as an empty
            * object, so if we should have it, it at least
            * exists, regardless of if we actually selected
            * anything or not...
            */
            if ( typeof rule.currentSelected !== "undefined" ) {
                listData = rule.currentSelected.data;
                // Ensure something was actually selected. If so,
                // compile it into an array, else, break out now with
                // a warning message
                if ( listData && 0 < listData.length ) {
                    idProperty = rule.currentSelected.idProperty;
                    csvSelected = [];
                    for ( j = 0; j < listData.length; ++j ) {
                        datum = listData[j].data;
                        if ( typeof datum[idProperty] !== 'undefined' ) {
                            csvSelected.push( datum[idProperty] );
                        }
                        // Otherwise data is invalid...
                    }

                    // Add the list compiled above to the rule
                    allParams["rules[" + i + "][value]"] = csvSelected.join(',');
                } else {
                    // Pull the type out from the main criteria combo box
                    ruleCriteria = rule.items.items[0].lastSelectionText;
                    Ext.Msg.alert(
                        'No ' + ruleCriteria + 's Selected'
                        ,'Please select at least one item for Criteria ' + ( i + 1 ) + ' (' + ruleCriteria + ') or delete this Criteria from your Smart Group.'
                    );
                    return;
                }
            } else if (allParams["rules[" + i + "][value]"] === '') {
                ruleCriteria = rule.items.items[0].lastSelectionText;
                Ext.Msg.alert(
                    'No ' + ruleCriteria + 's Selected'
                    ,'Please enter a value for Criteria ' + ( i + 1 ) + ' (' + ruleCriteria + ') or delete this Criteria from your Smart Group.'
                );
                return;
            }
        }

        // A smartGroupId with the initial value of 0 means 'new', if
        // it is a real value, edit.
        if ( !smartGroupWindow.smartGroupId ) {
            url = smartGroupWindow.urlAdd;
        } else {
            url = smartGroupWindow.urlEdit;
            allParams['smartGroupId'] = smartGroupWindow.smartGroupId;
        }
        allParams['smartGroupType'] = smartGroupWindow.smartGroupType;

        Ext.Ajax.request({
            url: url
            ,params: allParams
            ,success: function( response ) {
                var status = Ext.util.JSON.decode( response.responseText );
                switch ( status.error ) {
                case 0:
                    self.mainScope.refresh(); // refresh main grid
                    self.smartGroupDetailsWindow.hide();
                    config.configuredGroupsObject.reloadPages(); // refresh left menu numbers
                    Ext.Msg.alert( 'Success', 'Smart Group saved.' );
                    break;

                case 6:
                    Ext.Msg.alert( 'Smart Group name exists', 'Please provide a new (unique) name for the Smart Group.' );
                    break;

                case 50: // NO_WRITE_PERMISSION
                    Ext.Msg.alert( 'Permission Denied', 'You do not have the required permissions to save Smart Groups.' );
                    break;

                default:
                    // Note, there are a ton of potential return codes
                    // from the backend, but they are all safety
                    // mechanisms, i.e. none should actually be
                    // possible... Don't bother coding for them all
                    // here.
                    Ext.Msg.alert( 'Error', 'Unexpected error.' );
                    break;
                }
            }
            ,failure: function() {
                Ext.Msg.alert( 'Error', 'Unexpected error.' );
            }
        });
    },

    // Helper function within this window:
    // Anytime we try to enable the save button, pass it through here
    // so we can first determine if the user is allowed to edit.  Edit
    // is denied if the chosen SG is non-editable, or if the user is
    // read-only.
    smartGroupSaveButtonControl: function() {
        const smartGroupWindow = this.getSmartGroupGeneric();
        // Start by disabling, and enable if appropriate.
        Ext.ComponentQuery.query('smartGroupGeneric #smartGroupsSaveButton')[0].disable();
        // if ( smartGroupWindow.smartGroupEditable && !LoginDetails.isReadOnly ) {
            Ext.ComponentQuery.query('smartGroupGeneric #smartGroupsSaveButton')[0].enable();
        // }
    }

});
