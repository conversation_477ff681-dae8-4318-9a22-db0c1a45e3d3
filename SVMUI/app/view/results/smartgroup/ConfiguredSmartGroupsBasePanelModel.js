Ext.define('sfw.view.results.smartgroup.ConfiguredSmartGroupsBasePanelModel', {
    extend: 'Ext.app.ViewModel',
    alias: [
        'viewmodel.configuredsmartgroupsbasepanelmodel'
    ],

    data: {
        adActive: false,
        smartGroupName: '',
        smartGroupContentGrid: null,
        dirty: false,
        smartGroupId: '',
        //create config
        isDefaultGroup: true,
        searchTerm: null,
        selectedValue: null,
        configuredFolder: null,
        configuredParentFolder: null,
        type: '',
        potentialColumns: [],
        configuredBaseFields: [],
        idProperty: 'id',
        expandColumn: 'id',
        typePlural: null,
        typeUpperPlural: null,
        typeUpper: null,
        singleSelect: true,
        fields: [],
        columns: [],
        defaultSortColumn: 'host_name', //product_name, vuln_title
        allCustomColumns: 1,//0
        customColumnData: ""//"product_name,vuln_id,vuln_title,vuln_cvss_score,vuln_cvss3_score,vendor_name,num_eol,num_patched",
    },

    formulas: {
        smartGroupTabName: function (get) {
            var type = Ext.String.capitalize(get('type')),
                name = Ext.String.ellipsis(get('smartGroupName') || '', 15);

            return Ext.String.format('{0}: {1}', type, name);
        },
        sgTooltip: function (get) {
            var type = Ext.String.capitalize(get('type')),
                name = get('smartGroupName');
            return Ext.String.format('{0}: {1}', type, name);
        }
    },
    stores: {
        configuredSmartGroupStore: {
            fields: [{
                name: 'vuln_criticality',
                convert: function (value, record) {
                    return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                        20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                        0;
                }
            }],
            remoteSort: true,
            // sorters: [{
            //     property: '{expandColumn}',
            //     direction: 'ASC'
            // }],
            sorters: '{defaultSortColumn}',
            pageSize: 30,
            proxy: {
                url: 'action=smart_groups&which=getSmartGroupContents&smartGroupTextType={type}&smartGroupId={smartGroupId}&',
                // directionParam: 'direction',
                type: 'ajax',
                timeout: 600000, //10 minutes
                reader: {
                    rootProperty: 'data.rows',
                    totalProperty: 'data.total',
                    messageProperty: 'compiledTime'
                }
            },
            listeners: {
                beforeload: 'onSGStoreBeforeLoad',
                load: 'onSGStoreLoad'
            }
        },
        smartGroupOverviewStore: {
            fields: [{
                name: 'business_impact',
                convert: function (value, record) {
                    return Number.isFinite(Ext.Number.parseInt(record.get('business_impact'), 10)) && Ext.Number.parseInt(record.get('business_impact'), 10) > 0 ?
                        20 * (6 - (parseInt(record.get('business_impact'), 10))) / 100 :
                        0;
                }
            }],
            remoteSort: true,
            sorters: 'name',
            pageSize: 30,
            proxy: {
                url: 'action=smart_groups&which=overview&smartGroupTextType={type}&',
                type: 'ajax',
                reader: {
                    type: 'json',
                   /*transform: {
                        fn: function (data) {
                            if (!Ext.isEmpty(data.data.rows)) {
                                var response = data.data.rows.map(function (item) {
                                    item.business_impact = Number.isFinite(Ext.Number.parseInt(item.business_impact, 10)) && Ext.Number.parseInt(item.business_impact, 10) > 0 ?
                                        20 * (6 - (parseInt(item.business_impact, 10))) / 100 :
                                        0;

                                    return item;
                                });
                                return response;
                            }
                        },
                        scope: this
                    },*/
                    rootProperty: 'data.rows',
                    totalProperty: 'data.total',
                    messageProperty: 'compiledTime'
                }
            },
            listeners: {
                beforeload: 'onSGSOverviewStoreBeforeLoad',
                load: 'onSGOverviewStoreLoad'
            }
        }

    }

});