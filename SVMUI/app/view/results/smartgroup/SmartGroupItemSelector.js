Ext.define('sfw.view.results.smartgroup.SmartGroupItemSelector', {
    extend: 'Ext.window.Window',
    xtype: 'smartGroupItemSelector',
    layout: 'fit',

    // id: 'ad_selectorWindow' + Ext.id(),
    title: 'Select Active Directory Node',
    width: 320,
    height: 350,

    plain: true,
    border: false,
    modal: true,

    scrollable:'vertical',

    items: [
        {
            xtype: 'localItemSelector',
        }
    ],

    buttonAlign: 'left',

    buttons: [
        '->',
        {
            xtype: 'button',
            text: 'Save',
            // itemId: 'smartGroupsSaveButton',
            // tooltip: 'Save Smart Group',
            // disabled: true, // disable by default - we will enable it as appropriate
            // handler: function ( button ) {
            //     // Make sure button is enabled before we do anything
            //     const smartGroupsController = sfw.app.getController("sfw.controller.SmartGroups");
            //     if ( !button.disabled ) {
            //         smartGroupsController.saveSmartGroup();
            //     }
            // }
        },
        {
            xtype: 'button',
            text: 'Close',
            itemId: 'closeButton',
            tooltip: 'Close window',
            disabled: false,
            handler: function (self) {
                self.up("smartGroupItemSelector").hide()
            }
        }
    ]

    
    
});