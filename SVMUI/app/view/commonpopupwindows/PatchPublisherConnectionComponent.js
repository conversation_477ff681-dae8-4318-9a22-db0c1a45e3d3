Ext.define('sfw.view.commonpopupwindows.PatchPublisherConnectionComponent', {
    extend: 'Ext.panel.Panel',
    alias: [
        'widget.patchpublisherconnection'
    ],
    xtype: 'sfw.patchpublisherconnection',
    padding: 5,
    items: [
         {
            xtype: 'combo',
            itemId: 'singlePatchConnections',
            valueField: 'id',
            labelWidth: 250,
            displayField: 'name',
            matchFieldWidth: false,
            editable: false,
            mode: 'remote',
            triggerAction: 'all',
            allowBlank: false,
            autoSelect: true,
            fieldLabel: 'Select connections to publish*:',
            enableShiftSelect: true,
            multiSelect: true,
            // tpl: new Ext.XTemplate('<tpl for=".">', '<div class="x-boundlist-item">', '<input type="checkbox" />', '{name}', '</div>', '</tpl>'),
            store: Ext.data.Store({
                fields: [
                    {name: 'id', type: 'int'},
                    {name: 'name', type: 'string'}
                ],
                autoDestroy: true,
                proxy: {
                    type: 'ajax',
                    url: 'action=sps_package&which=get_connections_details&',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                },
                loaded: false

            }),
            listeners: {
                afterrender: function (cb){
                    cb.store.load({
                        callback: function(records){
                            if (cb.getValue() == null){
                                if(typeof records[0] !== 'undefined'){
                                    cb.setValue(records[0].data.id);
                                }
                            }
                        }
                    });
                },
                select:function(){
                    var connectionId = this.getValue();
                    var connectionNames = this.getRawValue();
                    if(!Ext.isEmpty(connectionId)){
                        if(Ext.ComponentQuery.query("#subscriptionSave")[0]){
                            Ext.ComponentQuery.query("#subscriptionSave")[0].setDisabled(false);
                            var selectedConnectionsNamesArray = connectionNames.split(',');

                            selectedConnectionsData = [];

                            for(var i=0; i<connectionId.length; i++) {
                                if(selectedConnectionsNamesArray[i].includes('Intune')) {
                                    selectedConnectionsData.push({id: connectionId[i], name: selectedConnectionsNamesArray[i]});
                                }
                            }
                            if (!Ext.isEmpty(selectedConnectionsData)) {
                                Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(false);
                            } else {
                                Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(true);
                            }

                        }else if(Ext.ComponentQuery.query("#publishQuickPatch")[0]){
                            Ext.ComponentQuery.query("#publishQuickPatch")[0].setDisabled(false);
                            var selectedConnectionsNamesArray = connectionNames.split(',');

                            selectedConnectionsData = [];

                            for(var i=0; i<connectionId.length; i++) {
                                if(selectedConnectionsNamesArray[i].includes('Intune')) {
                                    selectedConnectionsData.push({id: connectionId[i], name: selectedConnectionsNamesArray[i]});
                                }
                            }
                            if (!Ext.isEmpty(selectedConnectionsData)) {
                                Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(false);
                            } else {
                                Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(true);
                            }
                        }
                    }else{
                        if(Ext.ComponentQuery.query("#subscriptionSave")[0]){
                            Ext.ComponentQuery.query("#subscriptionSave")[0].setDisabled(true);
                            Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(true);
                        }else if(Ext.ComponentQuery.query("#publishQuickPatch")[0]){
                            Ext.ComponentQuery.query("#publishQuickPatch")[0].setDisabled(true);
                            Ext.ComponentQuery.query("#manageAssignments")[0].setHidden(true);
                        }

                    }
                }
            },
            labelSeparator: '',
            //labelStyle: 'padding: 5px 5px 0 0;',
            style:"width:100%;padding-bottom:1%"
        },{
            xtype: 'label',
            html: '<b>Note</b>: To select multiple connections hold Shift key.'
        }]

});