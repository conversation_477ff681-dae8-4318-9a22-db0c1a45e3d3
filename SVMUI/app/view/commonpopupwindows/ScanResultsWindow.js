Ext.define("sfw.view.commonpopupwindows.ScanResultsWindow", {
    extend: "Ext.window.Window",
    alias: "widget.scanResultWindow",
    width: 1000,
    height: 700,
    bodyPadding: 0,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    maximizable: true,
    layout: "fit",

    controller: 'completedScanController',

    viewModel: {
        data: {
            'hostscandetails': null
        },

        formulas: {
            lastscandate: function (get) {
                if((typeof get('hostscandetails.data.host.updated')) !== 'undefined'){
                    return sfw.Default.returnDateTime(get('hostscandetails.data.host.updated'),true);
                }else{
                    return 'n/a';
                }
            },
            zombiefiles: function (get) {
                if (get('hostscandetails.data.host.no_zombie') > 0) {
                    return '+ ' + get('hostscandetails.data.host.no_zombie') + ' Zombie Files';
                } else {
                    return '';
                }
            },
            site:function (get){
                return sfw.Default.renderADSiteName( get('hostscandetails.data.group.name'), get('hostscandetails.data.group.ad_dn') );
            },
            insecure:function(get){
                if ((typeof get('hostscandetails.data.host.no_insecure')) !== 'undefined') {
                    if (get('hostscandetails.data.host.no_insecure') == '' || get('hostscandetails.data.host.no_insecure') == 0) {
                        return '0';
                    } else {
                        return get('hostscandetails.data.host.no_insecure');
                    }
                } else {
                    return 'n/a';
                }
            },
            eol:function(get){
                if ((typeof get('hostscandetails.data.host.no_eol')) !== 'undefined') {
                    if (get('hostscandetails.data.host.no_eol') == '' || get('hostscandetails.data.host.no_eol') == 0) {
                        return '0';
                    } else {
                        return get('hostscandetails.data.host.no_eol');
                    }
                } else {
                    return 'n/a';
                }
            },
            patched:function(get){
                if ((typeof get('hostscandetails.data.host.no_patched')) !== 'undefined') {
                    if (get('hostscandetails.data.host.no_patched') == '' || get('hostscandetails.data.host.no_patched') == 0) {
                        return '0';
                    } else {
                        return get('hostscandetails.data.host.no_patched');
                    }
                } else {
                    return 'n/a';
                }
            },
            total:function(get){
                if ((typeof get('hostscandetails.data.host.no_total')) !== 'undefined') {
                    if (get('hostscandetails.data.host.no_total') == '' || get('hostscandetails.data.host.no_total') == 0) {
                        return '0';
                    } else {
                        return get('hostscandetails.data.host.no_total');
                    }
                } else {
                    return 'n/a';
                }
            },
            macAddress:function(get){
                return sfw.Util.intToMacAddress( get('hostscandetails.data.host.mac_address'));
            },
            ipAddress:function(get){
                return sfw.Util.int32ToIpv4( get('hostscandetails.data.host.ip_address'));
            },
            isMac:function(get){
                if(get('hostscandetails.data.host.mac_address')){
                    return false;
                }else {
                    return true;
                }
            },
            isIP:function(get){
                if(get('hostscandetails.data.host.ip_address')){
                    return false;
                }else {
                    return true;
                }
            },
            vulnerabilities:function(get){
                if(get('hostscandetails.data.vulnerabilities') == ''){
                    return '0';

                }else{
                    return get('hostscandetails.data.vulnerabilities');
                }
            }
        },

        stores: {
            overviewpiestore: {
                storeId: 'scanoverview',
                proxy: {
                    type: 'memory',
                    reader: {
                        type: 'json',
                        rootProperty: 'data'
                    }
                }
            },
            scanresults: {
                storeId: 'scanresults',
                pageSize: '30',
                remoteSort: true,
                sorters: [{
                    property: "product_name",
                    direction: "ASC"
                }],

                fields: [{
                    name: 'vuln_criticality',
                    convert: function (value, record) {
                        return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                            20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                            0;
                    }
                }],

                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        read: 'GET'
                    },
                    noCache: false,
                    url: 'action=hosts&which=get_host_scan_results',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                }
            },
            awarenessresults: {
                storeId: 'awarenessresults',
                pageSize: '30',
                remoteSort: true,
                sorters: [{
                    property: "product_name",
                    direction: "ASC"
                }],

                fields: [{
                    name: 'vuln_criticality',
                    convert: function (value, record) {
                        return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                            20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                            0;
                    }
                }],

                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        read: 'GET'
                    },
                    noCache: false,
                    url: 'action=hosts&which=get_host_scan_results&awareness=true',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                }
            },
            zombiefilesresults: {
                storeId: 'zombiefilesresults',
                pageSize: '30',
                remoteSort: true,
                sorters: [{
                    property: "product_name",
                    direction: "ASC"
                }],

                fields: [{
                    name: 'vuln_criticality',
                    convert: function (value, record) {
                        return Number.isFinite(Ext.Number.parseInt(record.get('vuln_criticality'), 10)) && Ext.Number.parseInt(record.get('vuln_criticality'), 10) > 0 ?
                            20 * (6 - (parseInt(record.get('vuln_criticality'), 10))) / 100 :
                            0;
                    }
                }],

                proxy: {
                    type: 'ajax',
                    actionMethods: {
                        read: 'GET'
                    },
                    noCache: false,
                    url: 'action=hosts&which=get_host_scan_results&zombiefiles=true',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                }
            }
        }
    },

    bind: {
        title: '{hostscandetails.data.host.host}'
    },
    items: [{
        xtype: 'tabpanel',
        tabBar: {
            layout: {
                pack: 'center'
            }
        },
        plain: true,
        items: [{
            title: 'Overview',
            layout: 'fit',
            items: [{
                xtype: 'panel',
                bodyPadding: 0,

                layout: {
                    type: 'hbox',
                    align: 'stretch'
                },
                defaults: {
                    //bodyStyle: 'padding:20px'
                    margin: 5
                },
                items: [{
                    xtype: 'panel',
                    flex: 3,
                    ui: 'light',
                    border: 1,
                    bodyPadding: 5,
                    layout: {
                        type: 'table',
                        columns: 2
                    },
                    items: [{
                        html: '<b>Host Details</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Host:',
                        width: 300
                    }, {
                        bind: {
                            html: '{hostscandetails.data.host.host}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Domain'
                    }, {
                        bind: {
                            html: '{hostscandetails.data.host.langroup}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Site'
                    }, {
                        bind: {
                            html: '{site}'
                        }, bodyStyle: 'text-align: right;'
                    },{
                        html: '&nbsp;&nbsp;Mac Address',
                        bind:{
                            hidden:'{isMac}'
                        }
                    }, {
                        bind: {
                            html: '{macAddress}',
                            hidden:'{isMac}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;IP Address',
                        bind:{
                            hidden:'{isIP}'
                        }
                    }, {
                        bind: {
                            html: '{ipAddress}',
                            hidden:'{isIP}'
                        }, bodyStyle: 'text-align: right;'
                    },{
                        html: '<b>Scan Details</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Last Scan:'
                    }, {
                        bind: {
                            html: '{lastscandate}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Last Scan Type'
                    }, {
                        bind: {
                            html: '{hostscandetails.data.host.scan_type}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Scans Conducted'
                    }, {
                        bind: {
                            html: '{hostscandetails.data.host.no_scans}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '<b>Scan & Vulnerabilities</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Flexera System Score:'
                    }, {
                        bind: {
                            html: '{hostscandetails.data.host.score}%'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Vulnerabilities'
                    }, {
                        bind: {
                            html: '{vulnerabilities}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '<b>State of Product</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Insecure:'
                    }, {
                        bind: {
                            html: '{insecure}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;End-of-Life'
                    }, {
                        bind: {
                            html: '{eol}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '&nbsp;&nbsp;Secure'
                    }, {
                        bind: {
                            html: '{patched}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: '<hr>'
                    }, {
                        html: '<hr>'
                    }, {
                        html: '<b>Total</b>'
                    }, {
                        bind: {
                            html: '{total}'
                        }, bodyStyle: 'text-align: right;'
                    }, {
                        html: ''
                    }, {
                        bind: {
                            html: '{zombiefiles}'
                        }, bodyStyle: 'text-align: right;'
                    }

                    ]
                },
                    {
                        flex: 3,
                        border: 1,
                        ui: 'light',
                        xtype: 'polar',
                        renderTo: document.body,
                        //width: 400,
                        //height: 400,
                        innerPadding: 20,
                        theme: 'default-gradients',
                        bind: {
                            store: '{overviewpiestore}'
                        },
                        legend: {
                            docked: 'bottom'
                        },
                        series: [{
                            type: 'pie',
                            angleField: 'data',
                            colors: [sfw.Default.returnStatusColor(3), sfw.Default.returnStatusColor(2), sfw.Default.returnStatusColor(1)],
                            label: {
                                field: 'label',
                                renderer: function (text, sprite, config, rendererData, index) {
                                    var rec = rendererData.store.findRecord('label', text);
                                    return rec.get('data') + '%';
                                }
                            },
                            highlight: true,
                            tooltip: {
                                trackMouse: true,
                                renderer: function (tooltip, record, item) {
                                    tooltip.setHtml('Products: ' + record.get('count'));
                                }
                            }
                        }]
                    }]
            }]
        },
            {
                title: 'Scan Results',
                // tabConfig: {
                //     listeners: {
                //         click: sfw.Default.scanResult
                //     }
                // },
                layout: 'fit',
                padding: 0,
                items: [{
                    xtype: 'gridpanel',
                    title: 'Scan Result',
                    preventHeader: true,
                    bind: {
                        store: '{scanresults}'
                    },
                    viewConfig: {
                        deferEmptyText: false,
                        emptyText: 'No Products found'
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: 'solid',
                        items: [
                            {
                                xtype: 'checkboxfield',
                                itemId: 'securecount',
                                boxLabel: 'Secure',
                                name: 'secure',
                                checked: true,
                                handler: sfw.Default.scanResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'endoflifecount',
                                boxLabel: 'End-Of-life',
                                name: 'endoflife',
                                checked: true,
                                handler: sfw.Default.scanResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'insecurecount',
                                boxLabel: 'Insecure',
                                name: 'insecure',
                                checked: true,
                                handler: sfw.Default.scanResult
                            }, {
                                xtype: 'panel',
                                flex: 1
                            }, {
                                xtype: 'exportButton'
                            }]
                    }],

                    columns: [
                        {text: 'Name', dataIndex: 'product_name', flex: 1},
                        {text: 'Version', dataIndex: 'version', flex: 1},
                        {
                            text: 'State', dataIndex: 'state', flex: 1, renderer: function (state) {
                                if (state === 'Insecure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(1) + "'>Insecure</font>";
                                } else if (state === 'End-Of-Life') {
                                    return "<font color='" + sfw.Default.returnStatusColor(2) + "'>End-Of-Life</font>";
                                } else if (state === 'Secure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(0) + "'>Secure</font>";
                                }
                            }
                        },
                        {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                        //{ text: 'Criticality', dataIndex: 'vuln_criticality', flex: 1, renderer:sfw.Default.advisoryCriticalityImage },
                        {
                            xtype: 'widgetcolumn',
                            text: 'Criticality',
                            dataIndex: 'vuln_criticality',
                            align: 'center',
                            sortable: true,
                            width: 90,
                            widget: {
                                xtype: 'sectorprogress',
                                height: 8
                            }
                        },
                        {
                            text: 'CVSS Base Store',
                            dataIndex: 'vuln_cvss_score_all',
                            flex: 1,
                            renderer: sfw.Default.renderCvssText
                        },
                        {
                            text: 'Threat Score',
                            dataIndex: 'vuln_threat_score',
                            align: 'right',
                            renderer: sfw.Default.threatScoreDefault
                        },
                        {text: 'CVSS2 Base Score', dataIndex: 'vuln_cvss_score', hidden: true},
                        {text: 'CVSS3 Base Score', dataIndex: 'vuln_cvss3_score', hidden: true},
                        {text: 'CVSS4 Base Score', dataIndex: 'vuln_cvss4_score', hidden: true},
                        {
                            text: 'Issued',
                            dataIndex: 'vuln_create_date',
                            flex: 1,
                            renderer: function (date, empty, record) {
                                var issued = date;
                                if ("0000-00-00" === issued || "0000-00-00 00:00:00" === issued) {
                                    issued = "-";
                                }
                                if (issued !== "-") {
                                    var daysPassed = sfw.Util.differenceBetweenDates(issued, sfw.Util.dateCreate(), 1);
                                    daysPassed = daysPassed.replace("-", "");
                                    daysPassed = daysPassed + " ago";
                                    issued = daysPassed;
                                }

                                return issued;
                            }
                        },
                        {
                            text: 'Vulnerabilities',
                            dataIndex: 'vuln_count',
                            flex: 1,
                            align: 'right',
                            renderer: function (data, empty, record) {
                                if ((data == "0") || (data == "-")) {
                                    return "-";
                                } else {
                                    return data;
                                }
                            }
                        },
                        {text: 'Path', dataIndex: 'path', hidden: true},
                        {text: 'Vendor name', dataIndex: 'vendor_name', hidden: true},
                        {text: 'Direct download', dataIndex: 'direct_download', hidden: true, renderer:sfw.Default.renderLink},
                        {text: 'Secure Version', dataIndex: 'secure_version', hidden: true},
                        {text: 'Missing MS KB', dataIndex: 'missing_ms_kb', hidden: true},
                        {text: 'Soft type', dataIndex: 'soft_type', hidden: true}
                    ],

                    listeners: {
                        itemcontextmenu: function (grid, record, item, index, e) {
                            var contextMenu = Ext.create('Ext.menu.Menu', {
                                plain: true,
                                items: [{
                                    text: 'View Installations',
                                    listeners: {
                                        click: {fn: sfw.Default.viewInstallation, extra: record}
                                    }
                                },{
                                    text: 'Add to Block List',
                                    listeners: {
                                        click: {fn: sfw.Default.addToBlockList, extra: record}
                                    }
                                }]
                            });
                            e.stopEvent();
                            contextMenu.showAt(e.getXY());
                        },
                        itemdblclick: sfw.Default.viewInstallationdblcick,
                        afterrender:sfw.Default.scanResult,
                        beforerender: function (grid) {
                            if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                                columns  = this.getView().getGridColumns();
                                columns[6].destroy();
                            }
                        }
                    },

                    // layout: 'fit',
                    margin: '2 2 0 0',

                    bbar: {
                        xtype: 'pagingtoolbar',
                        bind: {
                            store: '{scanresults}'
                        },
                        region: 'south',
                        displayInfo: true,
                        displayMsg: 'Displaying products {0} - {1} of {2}',
                        emptyMsg: "No products found"
                    }
                }]
            },
            {
                title: 'Blocked Results',
                layout: 'fit',
                padding: 0,
                items: [{
                    xtype: 'gridpanel',
                    title: 'Blocked Result',
                    preventHeader: true,
                    bind: {
                        store: '{awarenessresults}'
                    },
                    viewConfig: {
                        deferEmptyText: false,
                        emptyText: 'No Products found'
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: 'solid',
                        items: [
                            {
                                xtype: 'checkboxfield',
                                itemId: 'securecountawareness',
                                boxLabel: 'Secure',
                                name: 'secure',
                                checked: true,
                                handler: sfw.Default.awarenessResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'endoflifecountawareness',
                                boxLabel: 'End-Of-life',
                                name: 'endoflife',
                                checked: true,
                                handler: sfw.Default.awarenessResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'insecurecountawareness',
                                boxLabel: 'Insecure',
                                name: 'insecure',
                                checked: true,
                                handler: sfw.Default.awarenessResult
                            }, {
                                xtype: 'panel',
                                flex: 1
                            }, {
                                xtype: 'exportButton'
                            }]
                    }],

                    columns: [
                        {text: 'Name', dataIndex: 'product_name', flex: 1},
                        {text: 'Version', dataIndex: 'version', flex: 1},
                        {
                            text: 'State', dataIndex: 'state', flex: 1, renderer: function (state) {
                                if (state === 'Insecure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(1) + "'>Insecure</font>";
                                } else if (state === 'End-Of-Life') {
                                    return "<font color='" + sfw.Default.returnStatusColor(2) + "'>End-Of-Life</font>";
                                } else if (state === 'Secure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(0) + "'>Secure</font>";
                                }
                            }
                        },
                        {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                        //{ text: 'Criticality', dataIndex: 'vuln_criticality', flex: 1, renderer:sfw.Default.advisoryCriticalityImage },
                        {
                            xtype: 'widgetcolumn',
                            text: 'Criticality',
                            dataIndex: 'vuln_criticality',
                            align: 'center',
                            sortable: true,
                            width: 90,
                            widget: {
                                xtype: 'sectorprogress',
                                height: 8
                            }
                        },
                        {
                            text: 'CVSS Base Store',
                            dataIndex: 'vuln_cvss_score_all',
                            flex: 1,
                            renderer: sfw.Default.renderCvssText
                        },
                        {
                            text: 'Threat Score',
                            dataIndex: 'vuln_threat_score',
                            align: 'right',
                            renderer: sfw.Default.threatScoreDefault
                        },
                        {text: 'CVSS2 Base Score', dataIndex: 'vuln_cvss_score', hidden: true},
                        {text: 'CVSS3 Base Score', dataIndex: 'vuln_cvss3_score', hidden: true},
                        {text: 'CVSS4 Base Score', dataIndex: 'vuln_cvss4_score', hidden: true},
                        {
                            text: 'Issued',
                            dataIndex: 'vuln_create_date',
                            flex: 1,
                            renderer: function (date, empty, record) {
                                var issued = date;
                                if ("0000-00-00" === issued || "0000-00-00 00:00:00" === issued) {
                                    issued = "-";
                                }
                                if (issued !== "-") {
                                    var daysPassed = sfw.Util.differenceBetweenDates(issued, sfw.Util.dateCreate(), 1);
                                    daysPassed = daysPassed.replace("-", "");
                                    daysPassed = daysPassed + " ago";
                                    issued = daysPassed;
                                }

                                return issued;
                            }
                        },
                        {
                            text: 'Vulnerabilities',
                            dataIndex: 'vuln_count',
                            flex: 1,
                            align: 'right',
                            renderer: function (data, empty, record) {
                                if ((data == "0") || (data == "-")) {
                                    return "-";
                                } else {
                                    return data;
                                }
                            }
                        },
                        {text: 'Path', dataIndex: 'path', hidden: true},
                        {text: 'Vendor name', dataIndex: 'vendor_name', hidden: true},
                        {text: 'Direct download', dataIndex: 'direct_download', hidden: true, renderer:sfw.Default.renderLink},
                        {text: 'Secure Version', dataIndex: 'secure_version', hidden: true},
                        {text: 'Missing MS KB', dataIndex: 'missing_ms_kb', hidden: true},
                        {text: 'Soft type', dataIndex: 'soft_type', hidden: true}
                    ],

                    listeners: {
                        itemcontextmenu: function (grid, record, item, index, e) {
                            var contextMenu = Ext.create('Ext.menu.Menu', {
                                plain: true,
                                items: [{
                                    text: 'View Installations',
                                    listeners: {
                                        click: {fn: sfw.Default.viewInstallation, extra: record}
                                    }
                                }]
                            });
                            e.stopEvent();
                            contextMenu.showAt(e.getXY());
                        },
                        itemdblclick: sfw.Default.viewInstallationdblcick,
                        afterrender:sfw.Default.awarenessResult,
                        beforerender: function (grid) {
                            if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                                columns  = this.getView().getGridColumns();
                                columns[6].destroy();
                            }
                        }
                    },

                    // layout: 'fit',
                    margin: '2 2 0 0',

                    bbar: {
                        xtype: 'pagingtoolbar',
                        bind: {
                            store: '{awarenessresults}'
                        },
                        region: 'south',
                        displayInfo: true,
                        displayMsg: 'Displaying products {0} - {1} of {2}',
                        emptyMsg: "No products found"
                    }
                }]
            },
            {
                title: 'Zombie File Results',
                layout: 'fit',
                padding: 0,
                items: [{
                    xtype: 'gridpanel',
                    title: 'Zombie File Results',
                    preventHeader: true,
                    bind: {
                        store: '{zombiefilesresults}'
                    },
                    viewConfig: {
                        deferEmptyText: false,
                        emptyText: 'No Products found'
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: 'solid',
                        items: [
                            {
                                xtype: 'checkboxfield',
                                itemId: 'securecountzombiefiles',
                                boxLabel: 'Secure',
                                name: 'secure',
                                checked: true,
                                handler: sfw.Default.zombieFilesResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'endoflifecountzombiefiles',
                                boxLabel: 'End-Of-life',
                                name: 'endoflife',
                                checked: true,
                                handler: sfw.Default.zombieFilesResult
                            },
                            {
                                xtype: 'checkboxfield',
                                itemId: 'insecurecountzombiefiles',
                                boxLabel: 'Insecure',
                                name: 'insecure',
                                checked: true,
                                handler: sfw.Default.zombieFilesResult
                            }, {
                                xtype: 'panel',
                                flex: 1
                            }, {
                                xtype: 'exportButton'
                            }]
                    }],

                    columns: [
                        {text: 'Name', dataIndex: 'product_name', flex: 1},
                        {text: 'Version', dataIndex: 'version', flex: 1},
                        {
                            text: 'State', dataIndex: 'state', flex: 1, renderer: function (state) {
                                if (state === 'Insecure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(1) + "'>Insecure</font>";
                                } else if (state === 'End-Of-Life') {
                                    return "<font color='" + sfw.Default.returnStatusColor(2) + "'>End-Of-Life</font>";
                                } else if (state === 'Secure') {
                                    return "<font color='" + sfw.Default.returnStatusColor(0) + "'>Secure</font>";
                                }
                            }
                        },
                        {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                        //{ text: 'Criticality', dataIndex: 'vuln_criticality', flex: 1, renderer:sfw.Default.advisoryCriticalityImage },
                        {
                            xtype: 'widgetcolumn',
                            text: 'Criticality',
                            dataIndex: 'vuln_criticality',
                            align: 'center',
                            sortable: true,
                            width: 90,
                            widget: {
                                xtype: 'sectorprogress',
                                height: 8
                            }
                        },
                        {
                            text: 'CVSS Base Store',
                            dataIndex: 'vuln_cvss_score_all',
                            flex: 1,
                            renderer: sfw.Default.renderCvssText
                        },
                        {
                            text: 'Threat Score',
                            dataIndex: 'vuln_threat_score',
                            align: 'right',
                            renderer: sfw.Default.threatScoreDefault
                        },
                        {text: 'CVSS2 Base Score', dataIndex: 'vuln_cvss_score', hidden: true},
                        {text: 'CVSS3 Base Score', dataIndex: 'vuln_cvss3_score', hidden: true},
                        {text: 'CVSS4 Base Score', dataIndex: 'vuln_cvss4_score', hidden: true},
                        {
                            text: 'Issued',
                            dataIndex: 'vuln_create_date',
                            flex: 1,
                            renderer: function (date, empty, record) {
                                var issued = date;
                                if ("0000-00-00" === issued || "0000-00-00 00:00:00" === issued) {
                                    issued = "-";
                                }
                                if (issued !== "-") {
                                    var daysPassed = sfw.Util.differenceBetweenDates(issued, sfw.Util.dateCreate(), 1);
                                    daysPassed = daysPassed.replace("-", "");
                                    daysPassed = daysPassed + " ago";
                                    issued = daysPassed;
                                }

                                return issued;
                            }
                        },
                        {
                            text: 'Vulnerabilities',
                            dataIndex: 'vuln_count',
                            flex: 1,
                            align: 'right',
                            renderer: function (data, empty, record) {
                                if ((data == "0") || (data == "-")) {
                                    return "-";
                                } else {
                                    return data;
                                }
                            }
                        },
                        {text: 'Path', dataIndex: 'path', hidden: true},
                        {text: 'Vendor name', dataIndex: 'vendor_name', hidden: true},
                        {text: 'Direct download', dataIndex: 'direct_download', hidden: true, renderer:sfw.Default.renderLink},
                        {text: 'Secure Version', dataIndex: 'secure_version', hidden: true},
                        {text: 'Missing MS KB', dataIndex: 'missing_ms_kb', hidden: true},
                        {text: 'Soft type', dataIndex: 'soft_type', hidden: true}
                    ],

                    listeners: {
                        afterrender:sfw.Default.zombieFilesResult,
                        beforerender: function (grid) {
                            if (true !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled) {
                                columns  = this.getView().getGridColumns();
                                columns[6].destroy();
                            }
                        }
                    },

                    margin: '2 2 0 0',

                    bbar: {
                        xtype: 'pagingtoolbar',
                        bind: {
                            store: '{zombiefilesresults}'
                        },
                        region: 'south',
                        displayInfo: true,
                        displayMsg: 'Displaying products {0} - {1} of {2}',
                        emptyMsg: "No products found"
                    }
                }]
            }
        ]
    }],

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'bottom',
        layout: {
            pack: 'end'
        },
        items: [
            {
                text: 'Close',
                ui: 'primary',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]
});
