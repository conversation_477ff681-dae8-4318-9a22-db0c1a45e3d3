Ext.define("sfw.view.commonpopupwindows.ScheduleReportSetup", {
    extend: "Ext.window.Window",
    alias: "schedulereportwindow",

    width: 580,
    height: 500,
    bodyPadding: 10,

    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    title: 'Export Schedule Setup',
    name: 'scheduleExport',

    items: [{
        xtype: 'form',
        reference: "scheduleExportDetailForm",
        monitorValid: true,
        labelWidth: 75,
        autoHeight: true,

        defaultType: 'textfield',

        items: [
            {
                xtype: 'fieldset',
                title: 'Action',
                padding: 10,
                items: [
                    {
                        xtype: 'textfield',
                        width: 500,
                        fieldLabel: 'Name',
                        name: 'name',
                        itemId: 'scheduled_exports_name',
                        tabIndex: 1,
                        labelWidth: 100,
                        allowBlank: false,
                        emptyText: 'Enter a name (or short description) for this scheduled export.',
                        listeners: {
                            change: 'updateBtnState'
                        }
                    },
                    {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        items: [
                            // {
                            //     xtype: 'displayfield',
                            //     value: 'Table:',
                            //     labelWidth: 100
                            // },
                            {
                                xtype: 'displayfield',
                                grow: true,
                                growMax: 100,
                                labelWidth: 100,
                                fieldLabel: 'Table',
                                name: 'table',
                                tabIndex: 2,
                                itemId: 'table',
                                allowBlank: false,
                                emptyText: 'Enter a SQL query. Queries can first be tested in the Local Database Console.'
                            },
                            {
                                xtype: 'hidden',
                                name: 'queryString',
                                itemId:  'queryString'
                            },
                            {
                                xtype: 'hidden',
                                name: 'exportType',
                                itemId: 'exportType'
                            }
                        ]

                    }
                ]
            },
            {
                xtype: 'fieldset',
                title: 'Output',
                padding: 10,
                items: [
                    {
                        xtype: 'textfield',
                        width: 500,
                        fieldLabel: 'Filename',
                        name: 'output_file',
                        itemId: 'output_file_name',
                        tabIndex: 1,
                        allowBlank: false,
                        vtype: 'csvFile',
                        emptyText: 'Enter a filename ending with .csv extension',
                        listeners: {
                            change: 'updateBtnState'
                        }
                    }
                ]
            },
            {
                xtype: 'fieldset',
                title: 'Schedule',
                padding: 10,
                items: [
                    {
                        xtype: 'box',
                        padding: '0 0 10 0',
                        html: 'These settings define the schedule at which  the export will be performed'
                    },
                    {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        items: [
                            {
                                xtype: 'datefield',
                                fieldLabel: 'Next run',
                                name: 'xfirstrundate',
                                itemId: 'xfirstrundate',
                                allowBlank: false,
                                format: 'Y-m-d',
                                margin: '0 10 0 0',
                                value: Ext.Date.add(new Date(), Ext.Date.DAY),
                                invalidText: 'Valid date format is YYYY-MM-DD.',
                                editable: false
                            },
                            {
                                xtype: 'timefield',
                                format: 'H:i',
                                name: 'xfirstruntime',
                                itemId:  'xfirstruntime',
                                enableKeyEvents: true,
                                listeners:{
                                    change: 'updateBtnState',
                                    afterrender: function () {
                                        firstRun = new Date();
                                        firstRun.setMinutes(
                                            firstRun.getMinutes()
                                            + 30
                                            - parseInt(firstRun.getMinutes() % 15, 10)
                                        );
                                        Ext.ComponentQuery.query('#xfirstruntime')[0].setValue(firstRun);
                                    }
                                }
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        items: [{
                            xtype: 'combobox',
                            fieldLabel: 'Frequency',
                            valueField: 'id',
                            displayField: 'text',
                            editable: false,
                            triggerAction: 'all',
                            name: 'frequency_unit',
                            itemId: 'frequency_unit',
                            store: [
                                { id: '-1', text: '--' },
                                { id: '1', text: 'Hourly' },
                                { id: '2', text: 'Daily' },
                                { id: '3', text: 'Weekly' },
                                { id: '4', text: 'Monthly' }
                            ],
                            value: -1,
                            validator: function () {
                                if (
                                    Ext.ComponentQuery.query('#frequency_unit')[0].getValue() == -1 && !Ext.ComponentQuery.query('#run_once')[0].checked
                                ) {
                                    return false;
                                }
                                return true;
                            },
                            listeners: {
                                change: 'updateBtnState'
                            }
                        }, {
                            xtype: 'box',
                            padding: '4 10',
                            html: 'or'
                        }, {
                            xtype: 'checkboxfield',
                            itemId: 'run_once',
                            boxLabel: 'One-Time Export',
                            name: 'run_once',
                            inputValue: 'once',
                            listeners: {
                                change: 'updateBtnState'
                            }
                        },{
                            xtype: 'hidden',
                            name: 'id',
                            itemId: 'id'
                        },{
                            xtype: 'hidden',
                            name: 'creation_time',
                            itemId: 'creation_time'
                        }]
                    }
                ]
            }
        ]
    }],

    buttons: [
        {
            text: 'Save',
            //ui:'primary',
            formBind: true,
            itemId: 'scheduleExportSaveButton',
            tabIndex: 9,
            disabled: true,
            tooltip: {
                title: 'Save Scheduled Export',
                text: 'Save the scheduled export.'
            },
            handler: sfw.Default.saveScheduleReport
        }, {
            text: 'Close',
            tabIndex: 8,
            tooltip: {
                title: 'Close window',
                text: 'Cancel all changes and close the window.'
            },
            handler: function () {
                this.up('window').destroy();
            }
        }
    ],
    controller: {
        updateBtnState: function () {
            var buttonDisabledFlag;
            const me = this,
                view = me.getView(),
                checkFreq = view.down('#frequency_unit'),
                checkRunOnce = view.down('#run_once'),
                checkOutputFileName = view.down('#output_file_name'),
                checkExportName = view.down('#scheduled_exports_name'),
                saveBtn = view.down('#scheduleExportSaveButton');
            if (checkRunOnce.getValue()) {
                var cmp = Ext.ComponentQuery.query('#frequency_unit')[0];
                cmp.setValue(-1);
                cmp.clearInvalid();
                cmp.disable();
            } else {
                Ext.ComponentQuery.query('#frequency_unit')[0].enable();
            }
            if (Ext.form.VTypes.csvFile(checkOutputFileName.getValue()) &&
                checkExportName.getValue() !== '' &&
                (checkFreq.getValue() != '-1' || checkRunOnce.getValue() != false)) {
                buttonDisabledFlag = false;
            } else {
                buttonDisabledFlag = true;
            }
            saveBtn.setDisabled(buttonDisabledFlag);
        }
    }
});

Ext.onReady(function(){
    Ext.apply(Ext.form.VTypes, {
        csvFile: function(val,field) {
            var csvFileTest = /^(\w+)([\-][\w]+)*[\.][Cc][Ss][Vv]$/;
            return csvFileTest.test(val);
        },
        csvFileText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.csv\'.'
    });
});
