Ext.define('sfw.view.commonpopupwindows.ViewInstallations', {
    extend: "Ext.window.Window",
    alias: [
        'widget.viewinstallations'
    ],

    id: "installationGrid",

    width: 1200,
    height: 700,
    bodyPadding: 5,

    itemId: "installationGrid",
    maximizable: true,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    layout: "fit",
    controller: 'vendorpatchmodule',

    config: {
        smartGroupId: null
    },

    viewModel: {
        data: {
            'installationdata': null,
            'kbtable': '',
            sgType: 'product'
        },
        stores: {
            overviewproductpiestore: {
                type: 'overviewpie'
            },
            productSGStore: {
                fields: ['id','name'],
                proxy: {
                    type: 'ajax',
                    url: 'action=smart_groups&which=menuSummary&smartGroupTextType=product&',
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        successProperty: 'success'
                    }
                },
                autoLoad: false
            }
        },
        formulas: {
            totalCount: function (get) {
                return parseInt(get('installationdata.countEndOfLife')) + parseInt(get('installationdata.countInsecure')) + parseInt(get('installationdata.countPatched'));
            },
            kbtableData: function (get) {
                if (get('kbtable')) {
                    Ext.getCmp('kbtable').show();
                } else {
                    Ext.getCmp('kbtable').hide();
                }
            }
        }
    },

    tbar: [{
        xtype: 'label',
        text: ' View from the context of Smart Group: ',
        padding: '0 0 0 5'
    }, {
        xtype: 'combo',
        text: ' View from the context of Smart Group: ',
        name: "searchType_vpm",
        itemId: 'productSgCombo',
        queryMode: 'local',
        editable: false,
        matchFieldWidth: false,
        bind: {
            store: '{productSGStore}'
        },
        valueField: "id",
        displayField: "name",
        autoLoad: true,
        forceSelection: true,
        triggerAction: "all",
        selectOnFocus: false,
        listeners: {
            afterrender: 'onProductSGRender',
            select: sfw.Default.refreshInstallation
        }
    }],

    items: [{
        xtype: 'tabpanel',
        tabBar: {
            layout: {
                pack: 'center'
            }
        },
        plain: true,
        reference: 'tabpanel',
        border: false,
        defaults: {
            bodyPadding: 10,
            scrollable: true,
            //closable: true,
            border: false
        },
        bind: {},
        items: [{
            title: 'Overview',
            layout: {
                type: 'hbox',
                align: 'stretch'
            },

            items: [{
                layout: {
                    type: 'hbox',
                    align: 'stretch'
                },
                flex: 3,
                border: 1,
                bodyPadding: 5,
                defaults: {
                    //bodyStyle: 'padding:20px'
                    margin: 5
                },
                items: [{
                    id: 'kbtable',
                    autoWidth: true,
                    //border: true,
                    autoScroll: true,
                    bodyPadding: 10,
                    height: 350,
                    bind: {
                        html: '{kbtable}'
                    }
                }, {

                    flex: 2,
                    ui: 'light',
                    layout: {
                        type: 'table',
                        columns: 2
                    },
                    items: [{
                        html: '<b>State of Detected Installations</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Insecure:',
                        width: 300
                    }, {
                        bind: {
                            html: '{installationdata.countInsecure}'
                        }, bodyStyle: 'text-align: right'
                    }, {
                        html: '&nbsp;&nbsp;End-Of-Life:',
                        width: 300
                    }, {
                        bind: {
                            html: '{installationdata.countEndOfLife}'
                        }, bodyStyle: 'text-align: right'
                    }, {
                        html: '&nbsp;&nbsp;Secure:',
                        width: 300
                    }, {
                        bind: {
                            html: '{installationdata.countPatched}'
                        }, bodyStyle: 'text-align: right'
                    }, {
                        html: '<b>Total</b>',
                        width: 300
                    }, {
                        bind: {
                            html: '{totalCount}'
                        }, bodyStyle: 'text-align: right'
                    }, {
                        html: '<b>Other Info</b>',
                        colspan: 2
                    }, {
                        html: '&nbsp;&nbsp;Research Created:',
                        width: 300
                    }, {
                        bind: {
                            html: '{installationdata.createdAt}'
                        }, bodyStyle: 'text-align: right'
                    }
                    ]
                }
                ]
            }, {
                xtype: 'polar',
                flex: 3,
                border: 1,
                reference: 'chart',
                theme: 'default-gradients',
                //width: '100%',
                //height: 450,
                //insetPadding: 40,
                innerPadding: 20,
                bind: {
                    store: '{overviewproductpiestore}'
                },
                legend: {
                    docked: 'bottom'
                },
                interactions: ['rotate'],
                series: [{
                    type: 'pie',
                    angleField: 'data',
                    colors: [sfw.Default.returnStatusColor(3), sfw.Default.returnStatusColor(2), sfw.Default.returnStatusColor(1)],
                    label: {
                        field: 'label',
                        renderer: function (text, sprite, config, rendererData, index) {
                            var rec = rendererData.store.findRecord('label', text);
                            return rec.get('data') + '%';
                        }
                    },
                    highlight: true,
                    tooltip: {
                        trackMouse: true,
                        renderer: function (tooltip, record, item) {
                            tooltip.setHtml('Installations: ' + record.get('count'));
                        }
                    }
                }]
            }]
        }, {
            title: 'Installations',
            xtype: 'grid',
            layout: 'fit',
            bodyPadding: 0,
            id: 'installationsGrid',

            store: {
                type: 'viewinstallations'
            },

            controller: 'vendorpatchmodule',
            viewConfig: {
                deferEmptyText: false,
                emptyText: 'No installations found'
            },
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: 'solid',
                items: [
                    {
                        xtype: 'checkboxfield',
                        itemId: 'secureCheckBox',
                        boxLabel: 'Secure',
                        name: 'secure',
                        checked: true,
                        width: 100,
                        handler: sfw.Default.reloadViewInstallations
                    },
                    {
                        xtype: 'checkboxfield',
                        itemId: 'eolCheckBox',
                        boxLabel: 'End-Of-life',
                        name: 'endoflife',
                        checked: true,
                        width: 100,
                        handler: sfw.Default.reloadViewInstallations
                    },
                    {
                        xtype: 'checkboxfield',
                        itemId: 'insecureCheckBox',
                        boxLabel: 'Insecure',
                        name: 'insecure',
                        checked: true,
                        width: 100,
                        handler: sfw.Default.reloadViewInstallations
                    },{
                        xtype: 'tbspacer', width: 2
                    },{
                        xtype: 'textfield',
                        emptyText: 'Search Host',
                        itemId:'searchinstalations',
                        listeners: {
                            specialkey: sfw.Default.handleSpecialKeysInstallations
                        }
                    },{
                        xtype: 'button',
                        text: 'Search',
                        ui: 'primary',
                        handler: sfw.Default.reloadViewInstallations
                    },{
                        xtype: 'tbfill',
                        flex: 1
                    }, {
                        xtype: 'exportButton'
                    }]
            }],

            columns: [
                {text: 'Host', dataIndex: 'host', flex: 1},
                {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                {
                    xtype: 'widgetcolumn',
                    text: 'Criticality',
                    dataIndex: 'vuln_criticality',
                    align: 'center',
                    sortable: true,
                    width: 90,
                    widget: {
                        xtype: 'sectorprogress',
                        height: 8
                    }
                },
                {
                    text: 'Threat Score',
                    dataIndex: 'vuln_threat_score',
                    align: 'right',
                    renderer: sfw.Default.threatScoreDefault
                },
                {text: 'State', dataIndex: 'state', flex: 1, renderer: 'renderState'},
                {text: 'Version', dataIndex: 'version', flex: 1},
                {text: 'Missing MS KB', dataIndex: 'missing_ms_kb', flex: 1},
                {text: 'Last Scan', dataIndex: 'updated', flex: 1, renderer: 'renderLastScanDate'},
                {text: 'Path', dataIndex: 'path', flex: 3}
            ],

            listeners: {
                itemcontextmenu: function (grid, record, item, index, e) {
                    e.stopEvent();
                    if (record.data.path) {
                        var contextMenu = Ext.create('Ext.menu.Menu', {
                            plain: true,
                            hidden: 1,
                            items: [{
                                text: 'Copy path to clipboard',
                                listeners: {
                                    click: {fn: sfw.Default.copyPath, extra: record}
                                }
                            },{
                                text: 'Add to Block List',
                                listeners: {
                                    click: {fn: sfw.Default.addToBlockList, extra: record}
                                }
                            }]
                        });

                        contextMenu.showAt(e.getXY());
                    }
                },
                beforerender : sfw.Default.checkThreatEnabled,
                afterrender: sfw.Default.reloadViewInstallations
            },

            bbar: {
                xtype: 'pagingtoolbar',
                bind: {
                    store: "{viewinstallations}"
                },
                region: 'south',
                displayInfo: true,
                displayMsg: 'Displaying hosts {0} - {1} of {2}',
                emptyMsg: "No installations found"
            }
        }, {
            title: ' All Advisories ',
            tabConfig: {
                listeners: {
                    click: sfw.Default.reloadAllAdvisories
                }
            },
            xtype: 'grid',
            layout: 'fit',
            flex: 1,
            bodyPadding: 0,

            store: {
                type: 'alladvisories'
            },

            controller: 'vendorpatchmodule',
            viewConfig: {
                deferEmptyText: false,
                emptyText: 'No advisories found'
            },

            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: 'solid',
                items: [
                    {
                        xtype: 'tbfill',
                        flex: 1
                    }, {
                        xtype: 'exportButton'
                    }
                ]
            }],

            columns: [
                {text: 'SAID', dataIndex: 'vuln_id', flex: 1, renderer: sfw.Default.renderSaid},
                {text: 'Advisory Description', dataIndex: 'vuln_title', flex: 1},
                {
                    xtype: 'widgetcolumn',
                    text: 'Criticality',
                    dataIndex: 'vuln_criticality',
                    align: 'center',
                    sortable: true,
                    width: 90,
                    widget: {
                        xtype: 'sectorprogress',
                        height: 8
                    }
                },
                {
                    text: 'Threat Score',
                    dataIndex: 'vuln_threat_score',
                    align: 'right',
                    renderer: sfw.Default.threatScoreDefault
                },
                {text: 'Advisory Published', dataIndex: 'release_date', flex: 1},
                {text: 'Solution Status', dataIndex: 'sol_status', flex: 1},
                {text: 'Attack Vector', dataIndex: 'attack_vector', flex: 1},
                {text: 'Zero Day', dataIndex: 'zero_day', flex: 1},
                {
                    text: 'CVSS Base Score',
                    dataIndex: 'cvss_score_all',
                    flex: 1,
                    renderer: function (val, cell, record) {
                        var cvss3 = '';
                        cvss3 = typeof record.data.vuln_cvss3_score == 'undefined' ? '' : record.data.vuln_cvss3_score;
                        var cvss4 = '';
                        cvss4 = typeof record.data.vuln_cvss4_score == 'undefined' ? '' : record.data.vuln_cvss4_score;
                        return sfw.Default.cvssSgRenderer(record.data.cvss_score, cvss3, cvss4);
                    }
                },
                {text: 'CVSS2 Base Score', dataIndex: 'cvss_score', flex: 1, hidden: true},
                {text: 'CVSS3 Base Score', dataIndex: 'vuln_cvss3_score', flex: 1, hidden: true},
                {text: 'CVSS4 Base Score', dataIndex: 'vuln_cvss4_score', flex: 1, hidden: true},
                {text: 'Vulnerabilities', dataIndex: 'vulnerabilities', flex: 1, align: "right",
                    renderer: function (value, metadata) {
                        metadata.tdCls = 'padding-right: 10px;';
                        return value;
                    }
                }
            ],
            listeners: {
                beforerender : sfw.Default.checkThreatEnabled,
                afterrender: sfw.Default.reloadAllAdvisories
            },

            bbar: {
                xtype: 'pagingtoolbar',
                bind: {
                    store: "{alladvisories}"
                },
                region: 'south',
                displayInfo: true,
                displayMsg: 'Displaying advisories {0} - {1} of {2}',
                emptyMsg: "No advisories found"
            }
        }]
    }],

    buttons: [
        {
            text: 'Close',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]
});
