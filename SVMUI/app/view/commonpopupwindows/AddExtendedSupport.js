Ext.define('sfw.view.commonpopupwindows.AddExtendedSupport', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.escuSupport',
    width: 1000,
    height: 700,
    bodyPadding: 0,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    maximizable: true,
    layout: "fit",
    title: '',
    itemId: 'esuSupport',

    items: [{
        xtype: 'form',
        bodyPadding: 10,
        items: [
            {
                xtype: 'textfield',
                itemId:'productId',
                name:'productId',
                value:'',
                hidden:true,
            },{
                xtype: 'textfield',
                itemId:'productName',
                name:'productName',
                value:'',
                hidden:true,
            },{
                xtype: 'textfield',
                itemId:'addEdit',
                name:'addEdit',
                value:'',
                hidden:true,
            },{
                xtype: 'datefield',
                fieldLabel: 'Extended Security Updates (ESU) Date:',
                name: 'esuDate',
                labelWidth: 235,
                format:sfw.Globals.dateShortInput,
                value: '',
                itemId: 'esuDate',
                allowBlank:false,
                editable: false,
                minValue: new Date()
            },{
                xtype:'tbspacer',
                height:10
            },{
                xtype: 'panel',
                itemId:'siteList',
                listeners: {
                    beforerender: function () {
                        const siteLocalItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        const siteSelector = siteLocalItemSelector.createSiteSelector();
                        this.add(siteSelector);
                    }
                }
            },{
                xtype:'tbspacer',
                height:30
            },{
                xtype: 'panel',
                itemId:'hostList',
                listeners: {
                    beforerender: function () {
                        const hostLocalItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                        const hostSelector = hostLocalItemSelector.createHostSelector();
                        this.add(hostSelector);
                    }
                }
            }],

        buttons: [
            {
                text: 'Save',
                itemId: 'saveEsuData',
                formBind: true,
                ui: 'primary',
                tooltip: {
                    title: 'Submit details',
                    text: 'Submit ESU details.'
                },
                handler: sfw.Default.addEsuData
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]

})