Ext.define('sfw.view.commonpopupwindows.IntuneGroupsMasterList', {
    extend: "Ext.window.Window",
    xtype: 'IntuneGroups',
    width: 1000,
    height: 700,
    bodyPadding: 0,
    scrollable: "vertical",
    modal: true,
    resizable: true,
    constrainHeader: true,
    maximizable: true,
    layout: "fit",
    title:"Intune Assignments Groups",

    config : {
        connectionId : '',
        intent: 1
    },

    items:[{
        xtype: 'grid',
        itemId : 'intunegroupsgrid',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: 'solid',
            items: [{
                xtype: 'textfield',
                emptyText: 'Enter text to search',
                itemId:'searchGroups',
                listeners:{
                    specialkey: function(field, e) {
                        if (e.getKey() == e.ENTER) {
                            sfw.Default.groupSearch();
                        }
                    }
                }
            },{
                xtype: 'button',
                text: 'Find',
                ui:'primary',
                handler: sfw.Default.groupSearch
            }]
        }],
        layout: 'fit',
        selType: 'checkboxmodel',
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No Groups Found'
        },
        store: {type: 'intunegroupsmasterlist'},
        columns: [
            {text: 'Name', dataIndex: 'name', flex: 1},
            {text: 'Object ID', dataIndex: 'guid', flex: 1}
        ],
        listeners: {
            afterrender: function (grid) {
                const view = grid.getView();
                const scroller = view.getScrollable();

                if (scroller) {
                    scroller.on('scroll', function (scrollable, x, y) {
                        //console.log('Scrolled to:', y);

                        // Fetch data when scrolled to the bottom
                        const maxScroll = scrollable.getMaxPosition().y;
                        if (y >= maxScroll - 10) { // 10px buffer before the bottom
                            //console.log('Fetching more data...');
                            sfw.Default.fetchMoreData(grid.getStore());
                        }
                    });
                }
            },selectionchange: function(grid, selected){
                sfw.Default.saveSelectedRecords();
                //sfw.Default.updateSelectedRecords(selected);
                sfw.Default.restoreSelectedRecords();
            }
        }
    }],

    buttons: [
        '->',
        {
            text: 'Save',
            tabIndex: 9,
            itemId:'saveGroups',
            handler: sfw.Default.addGroups
        },{
            text: 'Cancel',
            tabIndex: 9,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]

});
