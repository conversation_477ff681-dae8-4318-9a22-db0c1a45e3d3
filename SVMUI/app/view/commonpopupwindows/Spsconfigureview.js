Ext.define('sfw.view.commonpopupwindows.Spsconfigureview',{
    extend: 'Ext.window.Window',
    xtype: 'sfw.spsconfigurewindow',

    width: 600,
    height: 370,
    bodyPadding: 10,

    title: 'Configure View',
    reference: 'spswindow',
    modal: true,
    layout: 'fit',

    controller: 'spspatching',

    viewModel: {

        data:{
            configureViewData:null
        },

        formulas:{
            viewsolutions:function(get){
                return (typeof sfw.util.Default.get('sps.viewSolutions') !== 'undefined'?(sfw.util.Default.get('sps.viewSolutions') === 'true'):get('configureViewData.viewSolutions'))
            },
            eolorinsecure:function(get){
                return (typeof sfw.util.Default.get('sps.showOnlyEolOrInsecure') !== 'undefined'?(sfw.util.Default.get('sps.showOnlyEolOrInsecure') === 'true'):get('configureViewData.eolOfLifeInsecure'));
            },
            onlyupdateable:function(get){
                return (typeof sfw.util.Default.get('sps.onlyUpdatable') !== 'undefined'?(sfw.util.Default.get('sps.onlyUpdatable') === 'true'):get('configureViewData.onlyUpdateTable'));
            },
            microsoftproducts:function(get){
                return (typeof sfw.util.Default.get('sps.hideMicrosoftProducts') !== 'undefined'?(sfw.util.Default.get('sps.hideMicrosoftProducts') === 'true'):get('configureViewData.microSoftProducts'))
            },
            highlightcreated:function(get){
                return (typeof sfw.util.Default.get('sps.highlightNonExisting') !== 'undefined'?(sfw.util.Default.get('sps.highlightNonExisting') === 'true'):get('configureViewData.highLightCreated'))
            },
            spslite:function(get){
                return (typeof sfw.util.Default.get('sps.litePage') !== 'undefined'?(sfw.util.Default.get('sps.litePage') === 'true'):get('configureViewData.viewSpsLitePage'));
            },
            spsliteHidden:function(get){
                return (typeof sfw.util.Default.get('sps.litePage') === 'undefined');
            }
        }
    },

    items:[{
        xtype: 'form',
        items: [{
            html: 'You can customize the view of the products displayed using the following configurable options:<br>',
            height: 30,
            border: false
        },{
            xtype: 'checkboxfield',
            boxLabel: 'Group products where patched version and architecture are identical',
            bind: '{viewsolutions}',
            name:'viewsolutions'
        },{
            xtype: 'checkboxfield',
            boxLabel: 'Display only End-of-Life or Insecure products',
            bind: '{eolorinsecure}',
            name:'eolorinsecure'
        },{
            xtype: 'checkboxfield',
            boxLabel: 'Display only products for which silent update packages can be created automatically',
            bind: '{onlyupdateable}',
            name:'onlyupdateable'
        },{
            xtype: 'checkboxfield',
            boxLabel: 'Hide Microsoft products',
            bind: '{microsoftproducts}',
            name:'microsoftproducts'
        },{
            xtype:'checkboxfield',
            boxLabel:'Highlight products for which update packages have been created',
            bind: '{highlightcreated}',
            name:'highlightcreated'
        },{
            xtype:'checkboxfield',
            boxLabel:'Display SPS Lite page',
            hidden: true,
            bind:{
                value:'{spslite}',
                hidden:'{spsliteHidden}',
            },
            name:'spsLitePage'
        },{
            xtype: 'box',
            html: '<br><b>Note:</b> The Software Vulnerability Manager should not be used for creating patches for Microsoft products. The updates should come from Microsoft.',
            border: false
        }]
    }],

    buttons: [
        {
            text: 'Apply',
            //ui:'primary',
            formBind: true,
            tooltip: 'Apply Settings to View',
            handler: 'applyConfigureView'

        }, {
            text: 'Cancel',
            handler: function () {
                this.up("window").destroy();
            }
        }
    ]

})