Ext.define("sfw.view.commonpopupwindows.Advisory", {
    extend: "Ext.window.Window",
    alias: "advisoryWindow",

    width: 800,
    height: 700,
    bodyPadding: 10,
    scrollable: "vertical",
    modal: true,

    viewModel: {
        data: {
            advisorydata: null
        }
    },

    bind:{
        title: '{advisorydata.data.details.vuln_title}'
    },

    defaults: {
        style: {
            border: '1px solid #d0d0d0',
            padding: '10px',
            marginBottom: '10px'
        },
        autoScroll: false,
        border: true,
        defaultBindProperty: 'data'
    },

    defaultType: 'container',

    items: [{
        bind: '{advisorydata}',
        tpl: [
            '<tpl>',
            '<table style="width:100%">',
            '<tr>',
                '<td style="width:20%"><b>Secunia Advisory ID:</b></td>',
                '<td style="width:70%">SA{data.details.vuln_id}</td>',
                '<td><nobr>{data.languageHtml}</nobr></td>',
            '</tr>',
            '<tr>',
                '<td style="width:20%"><b>Creation Date:</b></td>',
                '<td colspan="2">{data.details.vuln_create_date}</td>',
            '</tr>',
            '<tr>',
                '<td style="width:20%;vertical-align:top;"><b>Criticality</b></td>',
                '<td>{data.details.criticalityImage} - {data.advisoryCriticalityText}</td>',
            '</tr>',
            '<tpl if="false !== sfw.util.Auth.LoginDetails.isThreatModuleEnabled">',
            '<tr>',
                '<td style="width:20%;vertical-align:top;"><b>Threat Score:</b></td>',
                '<td>{data.threatScore}</td>',
            '</tr>',
            '</tpl>',
            '<tr>',
                '<td style="width:20%;vertical-align:top;"><b>Impact</b></td>',
                '<td>{data.advisoryImpactFormatted}</td>',
            '</tr>',
            '<tr>',
                '<td style="width:20%"><b>Where</b></td>',
                '<td>{data.advisoryWhereFormatted}</td>',
            '</tr>',
            '<tr>',
                '<td style="width:20%"><b>Solution Status</b></td>',
                '<td>{data.advisorySolutionStatusText}</td>',
            '</tr>',
            '<tpl if="data.cvss4ScoreData != null">',
            '<tr>',
            '<td style="width:20%;vertical-align:top;"><b>Secunia CVSS4 Scores</b></td>',
            '<td>{data.cvss4ScoreData}</td>',
            '</tr>',
            '<tpl elseif="data.cvss3ScoreData != null">',
            '<tr>',
            '<td style="width:20%;vertical-align:top;"><b>Secunia CVSS3 Scores</b></td>',
            '<td>{data.cvss3ScoreData}</td>',
            '</tr>',
            '<tpl elseif="data.cvssScoreData != null">',
            '<tr>',
                '<td style="width:20%;vertical-align:top;"><b>Secunia CVSS2 Scores</b></td>',
                '<td>{data.cvssScoreData}</td>',
            '</tr>',
            '</tpl>',

            '<tpl if="data.cve != null">',
            '<tr>',
                '<td style="width:20%;vertical-align:top;" ><b>CVE Reference(s):</b></td>',
                '<td>{data.cve}</td>',
            '</tr>',
            '</tpl>',
            '</table>',
            '</tpl>'
        ],

    }, {
        bind: '{advisorydata}',
        tpl: [
            '<table style="width:100%">',
            '<tpl if="data.affectedOsLength != 0">',
                '<tr><td><b>{data.affectedtitle}</b></td></tr>',
            '</tpl>',
            '<tpl for="data.affectedOs">',
                '<tpl for=".">',
                    '<tpl if="xindex === 2">',
                        '<tr><td>{.}</td></tr>',
                    '</tpl>',
                '</tpl>',
            '</tpl>',
            '<tpl if="data.affectedSoftwarelength != 0">',
                '<tr><td><b>{data.affectedtitle}</b></td></tr>',
            '</tpl>',
            '<tpl for="data.affectedSoftware">',
                '<tpl for=".">',
                    '<tpl if="xindex === 2">',
                        '<tr><td>{.}</td></tr>',
                    '</tpl>',
                '</tpl>',
            '</tpl>',
            '</table>'
        ]
    }, {
        bind: '{advisorydata}',
        tpl: [
            '<tpl for="data.content">',
                '<tpl if="text_text.trim()">',
                    '<tpl if="xindex === 1 && text_type_name == \'Description\'">',
                        '<b>Secunia Advisory Details</b>',
                    '<tpl else>',
                        '<b>{text_type_name}<br></b>',
                    '</tpl>',
                    '<p>{text_text}</p><br/>',
                '</tpl>',
            '</tpl>'
        ]
    }],

    buttons: [
        '->',
        {
            text: 'Close',
            ui : 'primary',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]

});