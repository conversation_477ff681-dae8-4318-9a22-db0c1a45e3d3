Ext.define('sfw.view.commonpopupwindows.NotificationWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.notificationwindow',
    width: 1100,
    height: 700,
    layout: 'auto',
    bodyPadding: 10,
    autoScroll: true,
    modal: true,
    maximizable: true,

    items:[{
        xtype: 'form',
        ui: 'light',
        title:'Notification Details',
        padding: '10px 10px 10px',
        frame: true,
        autoWidth: true,
        autoScroll: true,
        layout:"auto",

        items:[{
            xtype:'fieldset',
            layout: 'auto',
            height: 'auto',
            title: 'Name & Applicability',
            items: [{
                xtype: 'box',
                html: 'You must give this notification a name (or short description) to be used when receiving alerts.  Here you will also select the Events for which the notification will apply.'
            }, {
                xtype: 'label',
                itemId: 'formType',
                text: '',
                hidden: true,
            }, {
                layout: 'hbox',
                padding: '15 10 0 10',
                items: [{
                    layout: 'form',
                    flex: 1,
                    width: 450,
                    items:[{
                        xtype:'textfield',
                        allowBlank: false,
                        anchor: '97%',
                        fieldLabel: 'Name',
                        enableKeyEvents: true,
                        emptyText: 'Enter a name (or short description) for this notification...',
                        itemId: 'notificationfiledname',
                        allowBlank: false,
                        name: 'name'
                    }]
                }]
            }]
        },{
            xtype: 'fieldset',
            layout: 'form',
            height: 'auto',
            title: 'Event Selection',
            items: [
                {
                    xtype: 'box',
                    html: 'Select events:'
                },
                {
                    xtype: 'common.selectevents',
                    padding: 0
                }
            ]
        },{
            xtype: 'fieldset',
            height: 'auto',
            labelWidth: 10,
            title: 'Alert Conditions',
            layout: 'vbox',
            items:[{
                xtype: 'box',
                html: 'How often should this notification rule run? Scheduled is based on when the rule is saved/modified:'
            },{
                layout: 'hbox',
                padding: '15 15 15 15',
                items:[{
                    xtype: "combo",
                    store: sfw.Default.frequencyStore,
                    valueField: 'id',
                    displayField: 'text',
                    mode: 'local',
                    itemId: 'frequency_type',
                    allowBlank: false,
                    editable: false,
                    name: 'frequency_type',
                    hiddenName: 'frequency_type',
                    triggerAction: 'all',
                    emptyText: 'Choose Frequency',
                    name:'frequency',
                    listeners:{
                        select:sfw.Default.validateNotification
                    }
                }, {
                    xtype: "combo",
                    store: {
                        fields: ['index', 'dayName'],
                        data: [
                            [1,'Monday'],
                            [2,'Tuesday'],
                            [3,'Wednesday'],
                            [4,'Thursday'],
                            [5,'Friday'],
                            [6,'Saturday'],
                            [7,'Sunday']
                        ]
                    },
                    valueField: 'index',
                    displayField: 'dayName',
                    mode: 'local',
                    itemId: 'frequency_day',
                    //allowBlank: false,
                    editable: false,
                    hiddenName: 'frequency_day',
                    triggerAction: 'all',
                    emptyText: 'Choose Day',
                    margin:"0 0 0 10",
                    name:'frequency_day',
                    listeners:{
                        afterrender:function(){
                            Ext.ComponentQuery.query('#frequency_day')[0].hide();
                        },
                        select:sfw.Default.validateNotification
                    }
                },{
                    xtype: 'timefield',
                    name: 'frequency_time',
                    itemId:  'frequency_time',
                    labelAlign: 'left',
                    format: 'H:i',
                    increment: 30,
                    //editable: false,
                    enableKeyEvents: true,
                    autoSelect: true,
                    name:'frequency_time',
                    emptyText : 'Choose time',
                    margin:"0 0 0 10",
                    listeners:{
                        select:sfw.Default.validateNotification
                    }
                }]
            },{
                xtype: 'checkboxfield',
                itemId: 'notify',
                boxLabel: 'NOTIFY me when the conditions are NOT met. I.e., leave unchecked for a \'no news is good news\' policy.',
                name: 'notify',
                checked: false,
            }]
        },{
            xtype: 'fieldset',
            layout: 'form',
            height: 'auto',
            name: 'emailrecipients',
            title: 'Recipients Selection',
            items: [
                {
                    xtype: 'box',
                    html: 'Select email recipients:'
                },
                {
                    xtype: 'reporting.emailRecipients',
                    padding: 0,
                    header: false
                }
            ]
        }],

        buttons: [
            {
                text: 'Save',
                formBind: true,
                tabIndex: 9,
                itemId: 'save',
                handler: sfw.Default.saveLogNotification
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]

});