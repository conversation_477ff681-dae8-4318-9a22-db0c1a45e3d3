Ext.apply(Ext.form.field.VTypes, {
            IPAddress:  function(v) {
                return /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(v);
            },
            IPAddressText: 'Must be a valid IP address (0-255.0-255.0-255.0-255)',
            IPAddressMask: /[\d\.]/i
});

Ext.define('sfw.view.administration.IPAccessManagementController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.administration-ipaccessmanagement',

    modifiedIpRange: function(item, newVal, oldVal) {
        switch (newVal.ipType){
            case 'SINGLE': Ext.ComponentQuery.query('#ipField')[0].setHidden(false) && Ext.ComponentQuery.query('#startIpField')[0].setHidden(true) && Ext.ComponentQuery.query('#endIpField')[0].setHidden(true) ;
            break;
            case 'RANGE': Ext.ComponentQuery.query('#startIpField')[0].setHidden(false) && Ext.ComponentQuery.query('#endIpField')[0].setHidden(false) && Ext.ComponentQuery.query('#ipField')[0].setHidden(true) ;
            break;
        }

    },

    validateValues: function() {
		if ( Ext.ComponentQuery.query('#singleIpRadio' )[0].getValue() === true ) {
			if ( Ext.ComponentQuery.query('#labelfield' )[0].getValue() !== '' && Ext.form.field.VTypes.IPAddress( Ext.ComponentQuery.query('#ipField')[0].getValue()) ) {
				Ext.ComponentQuery.query('#saveIpRuleButton')[0].enable();
			} else {
				Ext.ComponentQuery.query('#saveIpRuleButton')[0].disable();
			}
		} else {
			if ( Ext.ComponentQuery.query('#labelfield' )[0].getValue() !== '' && Ext.form.field.VTypes.IPAddress( Ext.ComponentQuery.query('#startIpField')[0].getValue() ) && Ext.form.field.VTypes.IPAddress( Ext.ComponentQuery.query('#endIpField')[0].getValue() ) ) {
				Ext.ComponentQuery.query('#saveIpRuleButton')[0].enable();
			} else {
				Ext.ComponentQuery.query('#saveIpRuleButton')[0].disable();
			}
		}
	},


    processReturnCode: function( code ) {
        switch ( code ) {
        case 0:
            break;
        case 1:
            Ext.Msg.alert( 'Validation error', 'The IP(s) do not have a valid format.' );
            break;
        case 2:
            Ext.Msg.alert( 'Validation error', 'This IP Rule configuration would block your access to the Software Vulnerability Manager.First add your current IP to Allow List' );
            break;
        case 3:
            Ext.Msg.alert( 'Validation error', 'The first IP must be lower than the second one.' );
            break;
        case 4:
            Ext.Msg.alert( 'Validation error', 'Please name the IP Access Rule for easier identification.' );
            break;
        case -1:
        default:
            Ext.Msg.alert( 'Unexpected error', 'An unexpected error has occurred.' );
            break;
        }
    },

    deleteRule: function (event, target,options) {
        var delParams = {};
        delParams.id = options.extra.data.id;
        delParams.rule = options.extra.data.label;

        var me = this;

        Ext.Ajax.request({
              url: 'action=ajaxapi_csi_ip_access_rules&which=delete',
              method: 'POST',
              dataType: 'json',
              params: delParams,
              success: function( data ) {
                   data = Ext.decode( data.responseText );
			        me.processReturnCode( data.status );
			        if ( parseInt( data.status, 10 ) === 0 ) {
				        Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().reload();
			        }
			},
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Deleting record failed");
			}
        });
    },

    disableRule: function (event, target,options) {
        var delParams = {};
        delParams.id = options.extra.data.id;
        delParams.rule = options.extra.data.label;

        var me = this;


    Ext.Ajax.request({
         url: 'action=ajaxapi_csi_ip_access_rules&which=disable',
         method: 'POST',
         dataType: 'json',
         params: delParams,
         success: function( data ) {
               data = Ext.decode( data.responseText );
			   me.processReturnCode( data.status );
			   if ( parseInt( data.status, 10 ) === 0 ) {
				   Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().reload();
			   }
		 },
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Disabling record failed");
			}
        });
    },

    enableRule: function (event, target,options) {
        var delParams = {};
        delParams.id = options.extra.data.id;
        delParams.rule = options.extra.data.label;

        var me = this;


    Ext.Ajax.request({
         url: 'action=ajaxapi_csi_ip_access_rules&which=enable',
         method: 'POST',
         dataType: 'json',
         params: delParams,
         success: function( data ) {
               data = Ext.decode( data.responseText );
			   me.processReturnCode( data.status );
			   if ( parseInt( data.status, 10 ) === 0 ) {
				   Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().reload();
			   }
		 },
			failure: function () {
				Ext.Msg.alert("Unexpected Error", "Enabling record failed");
			}
        });
    },

    //Returns the type of the rule (SINGLE or RANGE)
    isRange: function( rule ) {
        if ( typeof rule === 'string' && rule.indexOf( '|' ) >= 0 ) {
            return true;
        }
        return false;
    },

    //Get the two IPs v4 of a ranged rule
    getBoundries: function( rule ) {
        var ips = rule.split( '|', 2 );
        if ( ips.length !== 2 ) {
            throw { number:1, message: "Could not process '" + rule + "' as a ranged IP." };
        } else {
            return ips;
        }
    },

    //Visually renders the IP rule in the grid
    gridRenderIp : function( rule ) {
    var me = this;
        if ( me.isRange( rule ) ) {
            try {
                var ips = me.getBoundries( rule );
                return ips[0] + ' &mdash; ' + ips[1];
            } catch ( ex ) {
                sfw.util.Debug.trigger( ex, "gridRenderIp" );
                return '';
            }
        } else {
            return rule;
        }
    },

    //Validates if a string is a valid IP v4 address
    isValidIpFormat: function( ip ) {
        if ( typeof ip === 'string' ) {
            var classes = ip.split( '.' );
            if ( classes.length !== 4 ) {
                return false;
            }
            for ( var i = 0; i < classes.length; i++ ) {
                if (
                    !classes[i].match( /^\d{1,3}$/ )
                    || parseInt( classes[i], 10) > 255
                ) {
                    return false;
                }
            }
            return true;
        }
        return false;
    },

    //Save the selected users to the rule
    saveUserSelection: function() {
        var me =this,
        view= me.getView();
        //Ext.ComponentQuery.query('#usersField')[0].setValue( '' );
        var selections = view.down('#userSelection').down('#selectedGrid');
        var selectedusersData = selections.getSelection();
        //console.log(Ext.ComponentQuery.query('#usersField'));
        if ( selectedusersData.length > 0 ) {
            Ext.ComponentQuery.query('#usersField')[0].setValue( me.constructSelectionString() );
        }
        view.destroy();
    },

    // When you want to convert the selection store to a selection string to keep it current
    constructSelectionString: function() {
            var me =this,
        view= me.getView();
        var selections = view.down('#userSelection').down('#selectedGrid');
        var selectedusersData = selections.getSelection();
        //console.log(selectedusersData);
        var selectionString = '';
        for ( var i = 0; i < selectedusersData.length; ++i ) {
            if ( selectionString !== '' ) {
                selectionString += ',';
            }
            selectionString += selectedusersData[i].data.recipient_account_id;
        }
        return selectionString;

    },

    saveIpRule: function (btn ) {
        var me = this;
        var id = 'new';
        var action = 'add';
        var view = btn.up('window');
        //var user_list = '';
        if(view.getFlag()==1){
             id = editId;
             action = 'update';
        }
        if ( ! ( Ext.ComponentQuery.query('#labelfield')[0].getValue().length > 0 ) ) {
		    Ext.Msg.alert( 'Validation error', 'Please name the IP Access Rule for easier identification.' );
		    return;
	    }
	    if ( Ext.ComponentQuery.query('#singleIpRadio')[0].getValue() === true ) {
		    if ( ! ( Ext.ComponentQuery.query('#ipField')[0].getValue().length > 0 ) ) {
			    Ext.Msg.alert( 'Validation error', 'Please specify an IP.' );
			    return;
		    }
		    if ( !me.isValidIpFormat(Ext.ComponentQuery.query('#ipField')[0].getValue() ) ) {
			    Ext.Msg.alert( 'Validation error', 'Please specify a valid IP.' );
			    return;
		    }
	    } else {
		    if ( !( Ext.ComponentQuery.query('#startIpField')[0].getValue().length > 0 ) || !( Ext.ComponentQuery.query('#endIpField')[0].getValue().length > 0 ) ) {
			    Ext.Msg.alert( 'Validation error', 'Both IP boundries need to be specified for a range IP Access Rule.' );
			    return;
		    }
		    if ( !me.isValidIpFormat( Ext.ComponentQuery.query('#startIpField')[0].getValue() ) ) {
			    Ext.Msg.alert( 'Validation error', 'Please specify a valid lower boundry IP.' );
			    return;
		    }
		    if ( !me.isValidIpFormat( Ext.ComponentQuery.query('#endIpField')[0].getValue() ) ) {
			    Ext.Msg.alert( 'Validation error', 'Please specify a valid higher boundry IP.' );
			    return;
		    }
	    }
	    if ( Ext.ComponentQuery.query('#usersCustomRadio')[0].getValue() === true && !( Ext.ComponentQuery.query('#usersField')[0].getValue().length > 0 ) ) {
			Ext.Msg.alert( 'Validation error', 'Please specify an account to apply the rule to.' );
			return;
	    }


        Ext.Ajax.request({

              url: 'action=ajaxapi_csi_ip_access_rules&which='+action,
              dataType: 'json',
              params: {
                id: id
			    ,users: Ext.ComponentQuery.query('#usersField' )[0].getValue()
			    ,label: Ext.ComponentQuery.query('#labelfield' )[0].getValue()
			    ,ipType: Ext.ComponentQuery.query('#ipRadiogroup')[0].getValue()
			    ,ip: Ext.ComponentQuery.query('#ipField')[0].getValue()
			    ,startIp: Ext.ComponentQuery.query('#startIpField')[0].getValue()
			    ,endIp: Ext.ComponentQuery.query('#endIpField')[0].getValue()
			    ,listType: Ext.ComponentQuery.query('#listRadiogroup')[0].getValue()
			    ,usersCustomization: Ext.ComponentQuery.query('#usersRadiogroup')[0].getValue()
              },
              success: function(data) {
                data = Ext.decode( data.responseText );
			    me.processReturnCode( data.status );
			    if ( parseInt( data.status, 10 ) === 0 ) {
                    Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().reload();
                    Ext.ComponentQuery.query('#ipaccessaddrule')[0].destroy();
			    }
		      }
        });
    },

    doubleClickHandler: function(grid, record){
                var me = this;
                editId = record.data.id;
                var editIpRule = Ext.create("sfw.view.administration.IpAccessAddWindow",
                {
                    flag: 1,
                });
                editIpRule.show();
                editIpRule.down('#labelfield').setValue(record.data.label);
                //editIpRule.down('#ipField').setValue(record.data.ip);
                editIpRule.down('#listRadiogroup').setValue(record.data.type);
                editIpRule.down('#usersField').setValue(record.data.users);
                editIpRule.setTitle('Edit IP Access Rule');
                if ( me.isRange( record.data.ip ) ) {
                    editIpRule.down('#rangeIpRadio').setValue( true );
                    //editIpRule.down('#ipField').setVisible( false );
                    //this.rangeIpEdit.setVisible( true );
                    try {
                        var ips = me.getBoundries( record.data.ip );
                        editIpRule.down('#startIpField').setValue( ips[0] );
                        editIpRule.down('#endIpField').setValue( ips[1] );
                    } catch ( ex ) {
                        editIpRule.down('#endIpField').setValue( '' );
                        editIpRule.down('#endIpField').setValue( '' );
                        //sfw.debug.trigger( ex, "CSIIpManagement.openIpRuleWindow" );
                    }
                } else {
                    editIpRule.down('#singleIpRadio').setValue( true );
                    editIpRule.down('#ipField').setValue( record.data.ip );
                    //this.singleIpEdit.setVisible( true );
                    //this.rangeIpEdit.setVisible( false );
		        }
                editIpRule.down('#blockListRadio').setDisabled(true);
                editIpRule.down('#helpTooltip').show();
                if(record.data.enabled === '1'){
                    for (var i = 0; i<Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().getTotalCount(); i++){
                        if(Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().data.items[i].data.enabled==='1' && Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().data.items[i].data.id !==record.data.id){
                            editIpRule.down('#blockListRadio').setDisabled(false);
                            editIpRule.down('#helpTooltip').hide();
                        }
                    }
                }else{
                            editIpRule.down('#blockListRadio').setDisabled(false);
                            editIpRule.down('#helpTooltip').hide();
                }
                if ( Ext.ComponentQuery.query('#usersField' )[0].getValue() !== '' ) {
			        editIpRule.down('#usersCustomRadio').setValue( true );
		        } else {
			        editIpRule.down('#usersAllRadio').setValue( true );
		        }

    },

    //Handler for checking if an IP is allowed to connect to the CSI
    checkIpAgainstRules: function() {
        var me = this;
        Ext.ComponentQuery.query('#checkResult')[0].setVisible( false );
        Ext.ComponentQuery.query('#checkResult')[0].setHtml( '' );
        if ( !( Ext.ComponentQuery.query('#ipCheckField')[0].getValue().length > 0 ) || !me.isValidIpFormat( Ext.ComponentQuery.query('#ipCheckField')[0].getValue() ) ) {
            Ext.Msg.alert( 'Check IP', 'Please fill in a valid IP v4 address.' );
            return;
        }
        Ext.Ajax.request({
            waitMsg:'Checking...'
            ,url: 'action=ajaxapi_csi_ip_access_rules&which=check',
            dataType: 'json',
            params: {
                ipCheckField: Ext.ComponentQuery.query('#ipCheckField')[0].getValue()
			    ,user: Ext.ComponentQuery.query('#checkIpCombo')[0].getValue()
            }
            ,success: function( data) {
                Ext.ComponentQuery.query('#checkResult')[0].setVisible( true );
                var response = Ext.decode( data.responseText );
                var resultStatus = parseInt( response.status, 10 );
                if ( resultStatus === 0 ) {
                    Ext.ComponentQuery.query('#checkResult')[0].setHtml( 'The provided IP-User combination <span class="StringError">cannot</span> connect to the Software Vulnerability Manager.' );
                } else if ( resultStatus === 1 ) {
                    Ext.ComponentQuery.query('#checkResult')[0].setHtml( 'The provided IP-User combination <span class="StringSuccess">can</span> connect to the Software Vulnerability Manager.' );
                }
            }
        });
    },



    

});
