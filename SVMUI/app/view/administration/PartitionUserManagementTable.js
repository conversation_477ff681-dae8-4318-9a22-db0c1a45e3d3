Ext.define('sfw.view.administration.PartitionUserManagementTable', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.partitionUserManagementTable',
    //id:'partitionUserTable',

    viewModel: {
        data: {
            'mode': '',
            'partitionuserdata': null
        },

        formulas:{
            isPartition: function(get) {
                return get('mode') != 'partition';
            },
            hostLicensesUsed:function(get){
                var hostLicensesUsed = parseInt(get('partitionuserdata.host_granted'),10) - parseInt(get('partitionuserdata.host_available'),10);
                return hostLicensesUsed.toString();
            },

            userLicensesUsed:function(get){
                var userLicensesUsed = parseInt(get('partitionuserdata.user_granted'),10) - parseInt(get('partitionuserdata.user_available'),10);
                return userLicensesUsed.toString();
            },

            partitionLicensesUsed:function(get){
                var partitionLicensesUsed = parseInt(get('partitionuserdata.partition_granted'),10) - parseInt(get('partitionuserdata.partition_available'),10);
                return partitionLicensesUsed.toString();
            }
        }
    },

    defaults: {
        bodyStyle: 'padding:20px'
    },

    items: [{
        flex: 3,
        layout: {
            type: 'table',
            columns: 4
        },
        items: [{
            html: '',
            width: 200
        }, {
            html: '<b>Host Licenses</b>',
            width: 200
        }, {
            html:'<b>User Licenses</b>',
            width: 200
        },{
            html:'<b>Partition Licenses</b>',
            bind: {
                hidden: '{isPartition}'
            },
            width: 200
        },{
            html: '<b>Licenses Granted:</b>',
            width: 200
        }, {
            bind:{
                html:'{partitionuserdata.host_granted}'
            },
            width: 200
        }, {
            bind:{
                html:'{partitionuserdata.user_granted}'
            },
            width: 200
        },{
            bind:{
                html:'{partitionuserdata.partition_granted}',
                hidden: '{isPartition}'
            },
            width: 200
        },{
            html: '<b>Licenses Used:</b>',
            width: 200
        }, {
            bind:{
                html:'{hostLicensesUsed}'
            },
            width: 200
        }, {
            bind:{
                html:'{userLicensesUsed}'
            },
            width: 200
        },{
            bind:{
                html:'{partitionLicensesUsed}',
                hidden: '{isPartition}'
            },
            width: 200
        },{
            html: '<b>Licenses Available:</b>',
            width: 200
        }, {
            bind:{
                html:'<b>{partitionuserdata.host_available}</b>'
            },
            width: 200
        }, {
            bind:{
                html:'<b>{partitionuserdata.user_available}</b>'
            },
            width: 200
        },{
            bind:{
                html:'<b>{partitionuserdata.partition_available}</b>',
                hidden: '{isPartition}'
            },
            width: 200
        }]
    }]
});