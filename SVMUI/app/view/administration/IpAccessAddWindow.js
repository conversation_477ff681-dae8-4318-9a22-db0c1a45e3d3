
Ext.define('sfw.view.administration.IpAccessAddWindow',{
    extend:'Ext.window.Window',
    xtype:'sfw.newipwindow',
    controller:'administration-ipaccessmanagement',
    config: {
        flag:0
    },
    itemId: 'ipaccessaddrule',
    width: 600,
    height: 270,
    title:'New IP Rule',
    modal: true,
    layout: 'fit',
    items:[{
        xtype:'form',
        bodyPadding: 10,
        items: [{
            allowBlank: false,
            xtype: 'textfield',
            itemId: 'labelfield',
            name: 'name',
            fieldLabel: 'Name'
        },{
            xtype: 'textfield',
            itemId: 'usersField',
            hidden: true
        },{
            xtype: 'radiogroup',
            fieldLabel: 'Type',
            itemId: 'ipRadiogroup',
            items: [{
                boxLabel: 'Single IP',
                inputValue: 'SINGLE',
                itemId: 'singleIpRadio',
                name: 'ipType',
                checked: true,
            },{
                boxLabel: 'IP Range',
                name: 'ipType',
                inputValue: 'RANGE',
                itemId: 'rangeIpRadio',
            }],
            listeners: {
                change: 'modifiedIpRange'
            },
        },{
            xtype:'container',
            layout:'table',
            items:[{
                xtype: 'textfield',
                itemId: 'ipField',
                fieldLabel:'IP',
                vtype: 'IPAddress',
                margin: "3 0 0 0",
                listeners: {
                    valid: 'validateValues',
                    invalid: 'validateValues'
                }
            },{
                xtype: 'textfield',
                itemId: 'startIpField',
                fieldLabel:'From',
                vtype: 'IPAddress',
                margin: "3 0 0 0",
                hidden:true,
                listeners: {
                    valid: 'validateValues',
                    invalid: 'validateValues'
                }
                //allowBlank: false,
            },{
                xtype: 'textfield',
                itemId: 'endIpField',
                fieldLabel: 'To',
                vtype: 'IPAddress',
                margin: "3 0 0 20",
                hidden:true,
                listeners: {
                    valid: 'validateValues',
                    invalid: 'validateValues'
                }
                //allowBlank: false,
            }]
        },{
            xtype: 'radiogroup',
            itemId: 'listRadiogroup',
            margin: "10 0 0 0",
            fieldLabel: 'Added to',
            items: [{
                boxLabel: 'Allow List',
                inputValue: 'Allow List',
                name: 'listType',
                checked: true,
                listeners: {
                    change: function () {
                        Ext.ComponentQuery.query('#usersCustomRadio')[0].disable();
                        Ext.ComponentQuery.query('#usersAllRadio')[0].disable();
                    }
                }
            },{
                boxLabel: 'Block List',
                inputValue: 'Block List',
                itemId: 'blockListRadio',
                name: 'listType',
                listeners: {
                    change: function () {
                        Ext.ComponentQuery.query('#usersCustomRadio')[0].enable();
                        Ext.ComponentQuery.query('#usersAllRadio')[0].enable();
                        Ext.ComponentQuery.query('#customConfigure')[0].disable();
                    }
                }
            },{
                xtype: 'label',
                html: '<p>(?)</P>',
                itemId: 'helpTooltip',
                listeners : {
                    render: function(c) {
                    Ext.create('Ext.tip.ToolTip', {
                        target: c.getEl(),
                        html: 'Break list rules can only be added when there is at least one allow list rule enabled.'
                    });
                   }
                }
            }]
        },{
            xtype: 'radiogroup',
            fieldLabel: 'Users',
            margin: "10 0 0 0",
            itemId: 'usersRadiogroup',
            name: 'users',
            items: [{
                boxLabel: 'All',
                inputValue: 'all',
                itemId: 'usersAllRadio',
                name: 'usersCustomization',
                checked: true,
                listeners: {
                    change: function () {
                        Ext.ComponentQuery.query('#customConfigure')[0].enable();
                    }
                }
            },{
                boxLabel: 'Custom',
                itemId: 'usersCustomRadio',
                name: 'usersCustomization',
                inputValue: 'custom',
                listeners: {
                    change: function () {
                        Ext.ComponentQuery.query('#customConfigure')[0].disable();
                    }
                }
            },{
                itemId: 'customConfigure',
                xtype:'button',
                text: 'Configure',
                handler:function(btn){
                var view = btn.up('window');
                var userSelectionPopup = Ext.create("sfw.view.administration.IPAccessUserWindow",
                {
                    listeners: {
                        afterrender: function (userSelectionPopup) {
                                const localItemSelector = sfw.app.getController("sfw.controller.LocalItemSelector");
                                const usersSelection = userSelectionPopup.down('#userSelection');
                                const userSelectionSelector = localItemSelector.createUserSelector('101');
                                usersSelection.add(userSelectionSelector);
                                    sfw.Default.getSelectorValues({
                                        action: 'recipients',
                                        which: 'get_recipients',
                                        module_id: 101,
                                        method_id: 1,
                                    }, userSelectionSelector,Ext.ComponentQuery.query('#usersField')[0].getValue());
                            }
                    }
                });
                userSelectionPopup.show();
                },
                //ui: 'primary',
                disabled: true,
                tooltip: {
                    text: 'Select specific users / sub-users for which this rule applies.'
                }
            }]
        }],
        buttons: [
            {
                text: 'Save',
                itemId: 'saveIpRuleButton',
                formBind: true,
                //ui: 'primary',
                handler: "saveIpRule"
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]
});