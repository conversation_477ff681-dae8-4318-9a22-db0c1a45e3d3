Ext.define('sfw.view.administration.PasswordPolicy', {
    extend: 'Ext.form.Panel',

    xtype: 'sfw.csiPasswordPolicyConfiguration',
    controller: 'administration-passwordpolicycontroller',
    viewModel: {},

    requires: [
        'Flexera.widgets.CustomNumberField',
    ],

    title: 'Password Policy Configuration',
    ui: 'light',

    frame: false,
    bodyPadding: 10,
    scrollable: true,

    items: [{
        xtype: 'panel',
        ui: 'light',
        layout : 'vbox',
        title: 'Policy Rules',
        bodyPadding: 10,
        frame : true,
        items: [{
            xtype: 'box',
            html: 'Configure the Software Vulnerability Manager password policy for your users.'
        }, {
            xtype: 'fieldcontainer',
            layout: 'hbox',
            padding: '10 0 0 60',
            items: [{
                xtype: 'checkbox',
                name: 'must_contain_at_least',
                itemId: 'must_contain_at_least',
                inputValue: 1,
                boxLabel: 'Password must be at least',
                handler: function (checkbox, checked) {
                    var min_length = Ext.ComponentQuery.query('#min_length')[0];
                    if (checked) {
                        min_length.setDisabled(false);
                        min_length.reset();
                    } else {
                        min_length.setDisabled(true);
                        min_length.setValue('');
                        min_length.clearInvalid();
                    }

                }
            }, {
                xtype: 'customnumberfield',
                itemId: 'min_length',
                name: 'min_length',
                minNumber: '5',
                maxNumber: '25',
                valueNumber: '8',
                enableKeyEvents: true,
                listeners:{
                    keypress:function(form,e){
                        if(this.getValue() != null){
                            if(this.getValue().toString().length >= 2){
                                e.preventDefault();
                            }
                        }
                    }
                }
            }, {
                xtype: 'displayfield',
                value: 'characters long.'
            }]
        }, {
            xtype: 'fieldcontainer',
            layout: 'hbox',
            padding: '0 0 0 60',
            items: [{
                xtype: 'checkbox',
                name: 'not_reusable_for',
                itemId: 'not_reusable_for',
                inputValue: 1,
                boxLabel: 'Users must be prevented from reusing the password for at least',
                handler: function (checkbox, checked) {
                    var min_changes = Ext.ComponentQuery.query('#min_changes')[0];
                    if (checked) {
                        min_changes.setDisabled(false);
                        min_changes.reset();
                    } else {
                        min_changes.setDisabled(true);
                        min_changes.setValue('');
                        min_changes.clearInvalid();
                    }

                }
            }, {
                xtype: 'customnumberfield',
                name: 'min_changes',
                itemId: 'min_changes',
                minNumber: '1',
                maxNumber: '99',
                valueNumber: '5',
                enableKeyEvents: true,
                listeners:{
                    keypress:function(form,e){
                        if(this.getValue() != null){
                            if(this.getValue().toString().length >= 2){
                                e.preventDefault();
                            }
                        }
                    }
                }
            }, {
                xtype: 'displayfield',
                value: 'changes.'
            }]
        }, {
            xtype: 'fieldcontainer',
            layout: 'hbox',
            padding: '0 0 0 60',
            items: [{
                xtype: 'checkbox',
                name: 'must_have_numerics',
                itemId: 'must_have_numerics',
                inputValue: 1,
                boxLabel: 'Password must contain at least',
                handler: function (checkbox, checked) {
                    var min_numerics = Ext.ComponentQuery.query('#min_numerics')[0];
                    if (checked) {
                        min_numerics.setDisabled(false);
                        min_numerics.reset();
                    } else {
                        min_numerics.setDisabled(true);
                        min_numerics.setValue('');
                        min_numerics.clearInvalid();
                    }

                }

            }, {
                xtype: 'customnumberfield',
                name: 'min_numerics',
                itemId: 'min_numerics',
                minNumber: '1',
                maxNumber: '9',
                valueNumber: '1',
                enableKeyEvents: true,
                listeners:{
                    keypress:function(form,e){
                        if(this.getValue() != null){
                            if(this.getValue().toString().length >= 1){
                                e.preventDefault();
                            }
                        }
                    }
                }
            }, {
                xtype: 'displayfield',
                value: 'digits.'
            }]
        }, {
            xtype: 'checkbox',
            padding: '0 0 0 60',
            name: 'must_have_upper_and_lower',
            itemId: 'must_have_upper_and_lower',
            inputValue: 1,
            boxLabel: 'Password must contain at least one lower case, one upper case and at least one special character.'
        }, {
            xtype: 'fieldcontainer',
            layout: 'hbox',
            padding: '0 0 0 60',
            items: [{
                xtype: 'checkbox',
                name: 'must_be_changed_after',
                itemId: 'must_be_changed_after',
                inputValue: 1,
                boxLabel: 'Password must be changed at least every',
                handler: function (checkbox, checked) {
                    var max_days = Ext.ComponentQuery.query('#max_days')[0];
                    if (checked) {
                        max_days.setDisabled(false);
                        max_days.reset();
                    } else {
                        max_days.setDisabled(true);
                        max_days.setValue('');
                        max_days.clearInvalid();
                    }

                }
            }, {
                xtype: 'customnumberfield',
                name: 'max_days',
                itemId: 'max_days',
                minNumber: '2',
                maxNumber: '999',
                valueNumber: '180',
                enableKeyEvents: true,
                listeners:{
                    keypress:function(form,e){
                        if(this.getValue() != null){
                            if(this.getValue().toString().length >= 3){
                                e.preventDefault();
                            }
                        }
                    }
                }

            }, {
                xtype: 'displayfield',
                value: 'days.'
            }]
        }, {
            xtype: 'hidden',
            name: 'checkCloud_flag',
            itemId: 'checkCloud_flag'
        }, {
            xtype: 'button',
            ui: 'primary',
            text: 'Save',
            handler: 'onFormSubmit'
        }]
    }],
    listeners: {
        beforerender: 'getPasswordRules'
    }
});
