Ext.define('sfw.view.administration.IPAccessUserWindow', {
    extend: 'Ext.window.Window',
    xtype: 'sfw.ipaccessuserwindow',
    width: 600,
    height: 430,
    layout: 'fit',
    bodyPadding: 5,
    autoScroll: true,
    modal: true,
    title: 'User Selection',
    controller: 'administration-ipaccessmanagement',
    config: {
        flag:0,
    },

    items:[{
            xtype: 'panel',
            layout: 'form',
            height: 'auto',
            name: 'userselection',
            items: [
                {
                    xtype: 'common.userselection',
                    padding: 0,
                    header: false
                }
            ],

        buttons: [
            {
                text: 'Save',
                //ui: 'primary',
                formBind: true,
                tabIndex: 9,
                itemId: 'save',
                handler: 'saveUserSelection'
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }]

});