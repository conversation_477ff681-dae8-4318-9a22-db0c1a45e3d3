Ext.define('sfw.view.administration.PasswordPolicyController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.administration-passwordpolicycontroller',

    getForm: function () {
        return this.getView().getForm();
    },

    getPasswordRules: function () {
        var view = this.getView();
        Ext.Ajax.request({
            url: 'action=password_policy_configuration&which=get_options',
            method: 'POST',
            dataType: 'json',
            params: {},
            success: function (response) {
                var response = Ext.decode(response.responseText);

                var max_days = response.data.max_days.option_value;
                var must_be_changed_after = response.data.max_days.option_is_enabled;
                var min_length = response.data.min_length.option_value;
                var must_contain_at_least = response.data.min_length.option_is_enabled;
                var min_changes = response.data.min_changes.option_value;
                var not_reusable_for = response.data.min_changes.option_is_enabled;
                var min_numerics = response.data.min_numerics.option_value;
                var must_have_numerics = response.data.min_numerics.option_is_enabled;
                var isCloudCheck = response.data.isCloudCheck.option_value;
                var must_have_mixed_case = response.data.must_have_mixed_case.option_is_enabled;

                if (min_length && must_contain_at_least) {

                    if (isCloudCheck == 1) {
                        view.down('#must_contain_at_least').setValue(1);
                        view.down('#must_contain_at_least').setDisabled(true);
                        if (min_length != 0) {
                            view.down('#min_length').setValue(min_length);
                        } else {
                            view.down('#min_length').setValue(8);
                        }
                    } else {
                        view.down('#must_contain_at_least').setValue(must_contain_at_least);
                        view.down('#min_length').setValue(min_length);
                    }
                }

                if (min_numerics && must_have_numerics) {

                    if (isCloudCheck == 1) {
                        view.down('#must_have_numerics').setValue(1);
                        view.down('#must_have_numerics').setDisabled(true);
                        if (min_numerics != 0) {
                            view.down('#min_numerics').setValue(min_numerics);
                        } else {
                            view.down('#min_numerics').setValue(1);
                        }
                    } else {
                        view.down('#must_have_numerics').setValue(must_have_numerics);
                        view.down('#min_numerics').setValue(min_numerics);
                    }
                }

                if ( min_changes && not_reusable_for) {
                    if (isCloudCheck == 1) {
                        view.down('#not_reusable_for' ).setValue(1);
                        view.down('#not_reusable_for' ).setDisabled(true);
                        if ( min_changes != 0) {
                            view.down('#min_changes' ).setValue( min_changes ) ;
                        } else {
                            view.down('#min_changes').setValue(5);
                        }
                    } else {
                        view.down('#not_reusable_for').setValue(not_reusable_for);
                        view.down('#min_changes' ).setValue( min_changes );
                    }
                }

                if (max_days && must_be_changed_after) {

                    if (isCloudCheck == 1) {
                        view.down('#must_be_changed_after').setValue(1);
                        view.down('#must_be_changed_after').setDisabled(true);
                        if (max_days != 0) {
                            view.down('#max_days').setValue(max_days);
                        } else {
                            view.down('#max_days').setValue(180);
                        }
                    } else {
                        view.down('#must_be_changed_after').setValue(must_be_changed_after);
                        view.down('#max_days').setValue(max_days);
                    }
                }

                if (must_have_mixed_case) {
                    if (isCloudCheck == 1) {
                        view.down('#must_have_upper_and_lower').setValue(1);
                        view.down('#must_have_upper_and_lower').setDisabled(true);
                    } else {
                        view.down('#must_have_upper_and_lower').setValue(must_have_mixed_case);
                    }

                }
                view.down('#checkCloud_flag').setValue(isCloudCheck);



            },
            failure: function (response, opts) {

            }
        });

    },



    onFormSubmit: function () {
        var form = this.getForm(),
            pretty, values, i;

        if (form.isValid()) {
            values = form.getFieldValues();

            var mustContain = values['must_contain_at_least'];
            var minLength = values['min_length'];

            var isNotReusable = values['not_reusable_for'];
            var minChanges = values['min_changes'];

            var mustHaveNumerics = values['must_have_numerics'];
            var minNumerics = values['min_numerics'];

            var mustHaveMixedCase = values['must_have_upper_and_lower'];

            var mustBeChangedAfter = values['must_be_changed_after'];
            var maxDays = values['max_days'];

            var checkCloudFlag = values['checkCloud_flag'];

            // Checking following since in cloud this values are disabled and will not be passed on form submit
            if (checkCloudFlag && typeof mustContain == 'undefined') {
                mustContain = true;
            }
            if (checkCloudFlag && typeof isNotReusable == 'undefined') {
                isNotReusable = true;
            }
            if (checkCloudFlag && typeof mustHaveNumerics == 'undefined') {
                mustHaveNumerics = true;
            }
            if (checkCloudFlag && typeof mustBeChangedAfter == 'undefined') {
                mustBeChangedAfter = true;
            }
            if (checkCloudFlag && typeof mustHaveMixedCase == 'undefined') {
                mustHaveMixedCase = true;
            }

        }


        if ( mustBeChangedAfter && maxDays <= 0 ) {
            Ext.Msg.show({
                title: "Error"
                ,msg: "Password has to be valid for a number of days greater than zero."
                ,buttons: Ext.Msg.OK
            });
            return;
        }

        // Combined conditions only need to be checked when there is minimum required
        // length for the password.
        if ( mustContain ) {
            var valid = true;
            var message = "";
            if ( mustHaveNumerics && minNumerics > minLength ) {
                message = "The minimum password length is shorter than the minimum number of digits required.";
                valid = false;
            }
            if ( valid && mustHaveMixedCase && minLength < 2 ) {
                message = "The minimum password length is too short to satisfy the mixed case requirement.";
                valid = false;
            }
            if ( valid && mustHaveMixedCase && mustHaveNumerics && ( minNumerics + 2 ) > minLength ) {
                message = "The minimum password length is too short to satisfy the mixed case requirement and the minimum number of digits requirement.";
                valid = false;
            }
            /* CSIL-8060 added JS validation */
            if (valid && checkCloudFlag == 1) {
                if (valid && minLength < 8) {
                    message = "The minimum password length is 8 characters .";
                    valid = false;
                }
                if (valid && minNumerics < 1) {
                    message = "The minimum numeric in password is 1 character.";
                    valid = false;
                }
                if (valid && minChanges < 5) {
                    message = "The minimum number to prevent reuse last password is 5.";
                    valid = false;
                }
                if (valid && maxDays > 180) {
                    message = "The password must be expired in 180 days.";
                    valid = false;
                }
            }
            if ( !valid ) {
                Ext.Msg.show( {
                    title: "Error"
                    ,msg: message
                    ,buttons: Ext.Msg.OK
                } );
                return;
            }
        }

        Ext.Ajax.request({

            url:  'action=password_policy_configuration&which=save_options'
            ,params: {
                'minLengthEnabled': mustContain,
                'minLength': minLength,

                'minChangesEnabled': isNotReusable,
                'minChanges': minChanges,

                'minNumericsEnabled': mustHaveNumerics,
                'minNumerics': minNumerics,

                'mustHaveMixedCaseEnabled': mustHaveMixedCase,

                'maxDaysEnabled': mustBeChangedAfter,
                'maxDays': maxDays,
            }
            ,success: function( data ) {
                data = Ext.decode( data.responseText );
                if ( !data.success ) {
                   // sfw.util.Debug.log( 'Error while trying to change the Password Policy Configuration: ' + data.msg );
                    Ext.Msg.show({
                        title: "Error"
                        ,msg: "Error while trying to save password policy."
                        ,buttons: Ext.Msg.OK
                    });
                    return;
                } else {
                    if(typeof Ext.ComponentQuery.query('#changePassowrdWindow')[0] !== 'undefined'){
                        var changePasswordWindow = Ext.ComponentQuery.query('#changePassowrdWindow')[0];
                        changePasswordWindow.getController().refresh();
                    }
                    Ext.Msg.show({
                        title: "Success"
                        ,msg: "Password policy saved. "
                        ,buttons: Ext.Msg.OK
                    });
                }
            }
            ,failure: function() {
                //sfw.util.Debug.log( 'Error while trying to change the Password Policy Configuration.' );
                Ext.Msg.show({
                    title: "Error"
                    ,msg: "Server error while trying to save password policy."
                    ,buttons: Ext.Msg.OK
                });
                return; }
        });
    }
});
