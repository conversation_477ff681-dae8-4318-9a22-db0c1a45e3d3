
Ext.define('sfw.view.administration.IPAccessManagement',{
    extend: 'Flexera.widgets.grid.Abstract',
    xtype: 'sfw.csiIpManagement',
    store : { type : 'IpAccessmanagementgrid' },
    itemId: 'ipAccessManagementGrid',


    requires: [
        'sfw.view.administration.IPAccessManagementController',
        'sfw.view.administration.IPAccessManagementModel',
        'sfw.Default',
        'sfw.util.Util'
    ],
    stateful: true,
    stateId: 'sfw_csiIpManagement_grid',
    title: 'IP Access Management',
    //ui: 'workbench',
    border: 1,
    cls: 'shadow',
    controller: 'administration-ipaccessmanagement',

	viewModel: {
		formulas: {
		    currentIP: function(){
		        return sfw.util.Auth.LoginDetails.currentlogin_ip;
		    }
		}
	},
	viewConfig : {
            deferEmptyText: false,
            emptyText: 'No IP access rule to display'
    },

    dockedItems: [{
        xtype: 'toolbar',
        dock: 'top',
        items: [{
            xtype: 'button',
            text: 'New IP Rule',
            ui: 'primary',
            handler:function(){
                var newiprule = Ext.create("sfw.view.administration.IpAccessAddWindow",
                {

                });
                newiprule.show();
                newiprule.down('#blockListRadio').setDisabled(true);
                newiprule.down('#helpTooltip').show();
                if(Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().getTotalCount()>0){
                    for (var i = 0; i<Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().getTotalCount(); i++){
                        if(Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().data.items[i].data.enabled==='1'){
                            newiprule.down('#blockListRadio').setDisabled(false);
                            newiprule.down('#helpTooltip').hide();
                        }
                    }
                }
                if(Ext.ComponentQuery.query('#ipAccessManagementGrid')[0].getStore().getTotalCount()<=0){
                    newiprule.down('#ipField').setValue(sfw.util.Auth.LoginDetails.currentlogin_ip);
                }
            }
        },{
            xtype:'tbseparator'
        },{
            xtype: 'button',
            text: 'Check IP',
            ui: 'primary',
            handler: function(){
                Ext.create("sfw.view.administration.IpAccessCheckWindow").show();
            }
        },{
            xtype:'tbseparator'
        },{
            xtype:'label',
            bind: {
                html: 'Your IP: {currentIP}'
            }
        },{
            xtype: 'tbfill',
            flex: 1
        }, {
            xtype: 'exportButton',
        }
    ]
    }],


    columns: [
        { text: 'IP Access Rule', dataIndex: 'label', flex: 4 },
        { text: 'IP Configuration', dataIndex: 'ip', flex: 1, renderer: 'gridRenderIp'},
        { text: 'Type', dataIndex: 'type', flex: 1, renderer: function(type){
            if (type === "Allow List") {
                return '<span class="StringSuccess">Allow List</span>';
            } else if (type === "Block List") {
                return '<span class="StringError">Block List</span>';
            }
        } },
        { text: 'Active', dataIndex: 'enabled', flex: 1, renderer: function(value){
            if (parseInt(value,10) === 1) {
                return '<span class="StringSuccess">Yes</span>';
            } else if (parseInt(value,10) === 0) {
                return '<span class="StringError">No</span>';
            }
        } },
        { text: 'Last Edit', dataIndex: 'last_update', flex: 1, renderer: function(value){
            try {
                var date = sfw.util.Util.dateCreate( value, true );
		        return Ext.util.Format.date(date, sfw.Globals.dateLongOutput );
	        } catch ( ex ) {
		        return 'Never';
	    }
        }}
    ],
    listeners: {
        itemcontextmenu: function (grid, record, item, index, e) {

            var enabled = grid.getStore().getAt(index).get( 'enabled' ) == 1 ? true : false;
            if(enabled){
                gridText = 'Disable',
                functionName = 'disableRule'
            } else {
                gridText = 'Enable',
                functionName = 'enableRule'
            }
            var contextMenu = Ext.create('Ext.menu.Menu', {
                controller: 'administration-ipaccessmanagement',
                plain: true,
                items: [{
                    text: gridText,
                    listeners: {
                            click: {fn: functionName, extra: record}
                    }
               },{
                    text: 'Delete',
                    listeners: {
                        click: {fn: 'deleteRule', extra: record}
                    }
                }


                ]
            });
            e.stopEvent();
            contextMenu.showAt(e.getXY());
        },
        itemdblclick: 'doubleClickHandler'
    },

    bbar: {
        xtype: 'pagingtoolbar',
        region: 'south',
        displayInfo: true,
        displayMsg: 'Displaying IP access rules {0} - {1} of {2}',
        emptyMsg: "No IP access rules configured",
        bind:{
            store:"{IpAccessmanagementgrid}"
        },
    },

});
