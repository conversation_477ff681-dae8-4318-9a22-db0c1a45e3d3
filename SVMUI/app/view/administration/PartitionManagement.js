Ext.define('sfw.view.administration.PartitionManagement', {
    extend: 'Ext.panel.Panel',
    xtype: 'sfw.csiPartitionManagement',
    title:'Partition Management',//: Overview of Administrators and Licenses In Your Network',
    border: 1,
    cls: 'shadow',
    requires: [
        'sfw.store.administration.PartitionManagement'
    ],

    layout:{
        type:'vbox',
        align: 'stretch'
    },

    viewModel : {
        stores :{
            partitionMgmtStore: {
                type:  'partitionmanagement',
                listeners: {
                    load: 'onPartionStoreLoad'
                }
            }
        }
    },
    controller: 'usermanagement',

    items: [{
        xtype: 'sfw.partitionUserManagementTable',
        flex: 1,
        margin: '0 0 10 0',
        itemId:'partitionTableItemId'
    }, {
        xtype: 'gridpanel',
        title:'Partition Management: Overview Of Administrators And Licenses In Your Network',
        preventHeader: true,
        viewConfig : {
            deferEmptyText: false,
            emptyText: 'No accounts'
        },
        flex: 5,
        bind: '{partitionMgmtStore}',
        controller : 'usermanagement',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [
                {
                    xtype: 'button',
                    ui: 'primary',
                    text: 'Create New Partition Administrator',
                    id : 'new_partition_admin_button',
                    handler: 'createPartitionAdmin'
                },{
                    xtype:'tbfill',
                    flex:'1'
                },{
                    xtype:'exportButton'
                }]
        }],

        columns:[
            { text: 'Name',dataIndex:'account_name', flex:3, renderer: function( value, metaData, record ) {

                    var retVal = '<b>' + value + '</b>';
                    if ( sfw.util.Auth.LoginDetails.loginAccountId === parseInt( record.get( 'account_id' ), 10 ) ) {
                        retVal = '<font color="#999999">' + retVal + '</font>';
                    }
                    return retVal;
                } },
            { text: 'Username', dataIndex:'account_username', flex: 1 },
            { text: 'Account Email',  dataIndex:'account_email', flex: 1,hidden:true },
            { text: 'Recipient Email', dataIndex:'recipient_email',  flex: 1 },
            { text: 'Recipient Mobile', dataIndex:'recipient_mobile',  flex: 1,hidden:true },
            { text: 'Host Licenses Granted', dataIndex:'host_granted',  flex: 1, align: 'right' },
            { text: 'Host Licenses Available',  dataIndex:'host_available', flex: 1, align: 'right' },
            { text: 'User Licenses Granted',  dataIndex:'user_granted', flex: 1, align: 'right' },
            { text: 'User Licenses Available',  dataIndex:'user_available', flex: 1, align: 'right' }
        ],

        listeners: {
            itemcontextmenu: function (grid, record, item, index, e) {
                var contextMenu = Ext.create('Ext.menu.Menu', {
                    controller:'usermanagement',
                    width: 250,
                    plain: true,
                    items: [{
                        text: 'View/Edit Account',
                        listeners: {
                            click: {fn: 'editUser', extra: record}
                        }
                    },{
                        text: 'Delete Account',
                        listeners: {
                            afterrender:function(){
                                if(!(sfw.Default.evaluateCanDelete(record,'partition')))
                                {
                                    this.setDisabled(true);
                                }
                            },
                            click: {fn: 'deletePartionAccount', extra: record}
                        }
                    }]
                });
                e.stopEvent();
                contextMenu.showAt(e.getXY());
            },
            itemdblclick: 'onUserRecordDblClicked'
        },
        layout: 'fit',
        margin: '2 2 0 0',
        bbar: {
            xtype: 'pagingtoolbar',
            region: 'south',
            bind: {
                store: '{partitionMgmtStore}'
            },
            displayInfo: true,
            displayMsg: 'Displaying Accounts {0} - {1} of {2}',
            emptyMsg: "No accounts to display"
        }
    }],
});
