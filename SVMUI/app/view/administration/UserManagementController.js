var licenseData = {
    // Init these - they get populated later when the store loads.
    hostsGrantedToPool: 0
    , hostsAvailableInPool: 0
    , usersGrantedToPool: 0
    , usersAvailableInPool: 0
    , partitionsGranted : 0
    , partitionsAvailable : 0
};


Ext.define('sfw.view.administration.UserManagementController', {
    extend: 'Ext.app.ViewController',

    alias: 'controller.usermanagement',

    createUser: function (event, target, options) {

        var me = this;
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var createUser = Ext.create("sfw.view.administration.UserManagementCreateNew",
            {
                mode: 'user',
                accountId: 0,
                listeners: {
                    afterrender: function () {

                        me.ssoConfiguration('user');
                        Ext.getCmp('user_license_field').hide();
                        Ext.getCmp('host_assign').hide();
                        Ext.getCmp('host_license_assign').hide();
                        Ext.getCmp('saveUserButton').setDisabled(true);
                        if(!LoginDetails.isPartitionAdmin){
                            Ext.getCmp('shared_sgs').hide();
                        }else{
                            Ext.getCmp('share_sg_chkbx_col').getStore().load();
                        }

                    }

                }
            });
        if ( LoginDetails.isSMSEnabled ) {
            createUser.down('#mobile_recipients').show();
        }
        if ( LoginDetails.account.settings.ad_integration == '1') {
            createUser.down('#ad_restrict').show();
        }
        createUser.down('#host_license_display').setValue( '( ' +licenseData.hostsAvailableInPool + ' Host Licenses Available )');
        createUser.down('#host_limit').setMaxValue( licenseData.hostsAvailableInPool);
        createUser.show();
        createUser.setTitle('Create New User');


    },

    createAdmin: function (event, target, options) {

        var me = this;
        var createAdmin = Ext.create("sfw.view.administration.UserManagementCreateNew",
            {
                mode: 'admin',
                accountId: 0,
                listeners: {
                    afterrender: function () {
                        me.ssoConfiguration('user');
                        Ext.getCmp('saveUserButton').setDisabled(true);
                        Ext.getCmp('restrict_network').hide();
                        Ext.getCmp('roles_permissions').hide();
                        Ext.getCmp('user_assign').hide();
                        Ext.getCmp('user_license_assign').hide();
                        Ext.getCmp('host_assign').hide();
                        Ext.getCmp('host_license_assign').hide();
                        Ext.getCmp('share_sg_chkbx_col').getStore().load();

                    }
                }
            });
        if ( LoginDetails.isSMSEnabled ) {
            createAdmin.down('#mobile_recipients').show();
        }
        createAdmin.down('#host_license_display').setValue( '( ' +licenseData.hostsAvailableInPool + ' Host Licenses Available )');
        createAdmin.down('#user_license_display').setValue( '( ' +licenseData.usersAvailableInPool + ' User Licenses Available )');
        createAdmin.down('#host_limit').setMaxValue( licenseData.hostsAvailableInPool);
        createAdmin.down('#user_limit').setMaxValue( licenseData.usersAvailableInPool);
        createAdmin.show();
        createAdmin.setTitle('Create New Administrator');


    },

    createPartitionAdmin: function (event, target, options) {
        var me = this;

        var createPartitionAdmin = Ext.create("sfw.view.administration.UserManagementCreateNew",
            {
                mode: 'partition',
                accountId: 0,
                listeners: {
                    afterrender: function () {

                        me.ssoConfiguration('partition');
                        Ext.getCmp('saveUserButton').setDisabled(true);
                        Ext.getCmp('shared_sgs').hide();
                        Ext.getCmp('restrict_network').hide();
                        Ext.getCmp('roles_permissions').hide();
                        Ext.getCmp('user_limit').hide();
                        Ext.getCmp('unlimited_user_checkbox').hide();
                        Ext.getCmp('user_license_display').hide();
                        Ext.getCmp('host_limit').hide();
                        Ext.getCmp('view_all_completed_scans_and_single_host_agents').hide();
                        Ext.getCmp('unlimited_host_checkbox').hide();
                        Ext.getCmp('host_license_display').hide();
                        Ext.getCmp('user_assign').show();

                    }
                }
            });
        //createPartitionAdmin.getMode() === 'partition';
        if ( LoginDetails.isSMSEnabled ) {
            createPartitionAdmin.down('#mobile_recipients').show();
        }
        createPartitionAdmin.down('#host_license_assign').setValue( '( ' +licenseData.hostsAvailableInPool + ' Host Licenses Available )');
        createPartitionAdmin.down('#user_license_assign').setValue( '( ' +licenseData.usersAvailableInPool + ' User Licenses Available )');
        createPartitionAdmin.down('#host_assign_number').setMaxValue( licenseData.hostsAvailableInPool);
        createPartitionAdmin.down('#user_assign_number').setMaxValue( licenseData.usersAvailableInPool);
        createPartitionAdmin.show();
        createPartitionAdmin.setTitle('Create New  Partition Administrator');
        createPartitionAdmin.setHeight(500);
    },

    editUser: function (event, target, options) {

        var type = options.extra.store.config.type;

        var type_sso = 'user';



        var me = this;
        var LoginDetails = sfw.util.Auth.LoginDetails;

        var account_id = options.extra.data.account_id;
        var is_admin = options.extra.data.is_admin;
        var account_name = options.extra.data.account_name;
        var account_email = options.extra.data.account_email;
        var account_username = options.extra.data.account_username;
        var recipient_email = options.extra.data.recipient_email;
        var mode = 'user';
        var source = 'edit';

        var index = options.extra.store.find('account_id', account_id);

        if (-1 === index) {
            return false;
        }


        if(type == 'partitionmanagement'){
            type_sso = 'partition';
            mode = 'partition';

        }

        if(is_admin == 1){
            mode = 'admin';
        }

        var record = options.extra.store.getAt(index).data;


        var params = {
            account_id: account_id
        };

        var additionalData = {};

        Ext.Ajax.request({
            url: 'action=account_management&which=get_details',
            method: 'POST',
            dataType: 'json',
            params: params,
            success: function (response, opts) {

                var response = Ext.decode(response.responseText);

                if (response.success === false) {
                    Ext.Msg.alert("Unexpected Error", "Unable to load account details.");
                    // TODO: temp fix for order of function execution.
                    // Ext.getCmp( self.tmpId ).hide();
                } else {
                    //var additionalData = {};
                    additionalData.usedUserLicenses = response.used_user_licenses;
                    additionalData.adRestrictedPath = response.ad_restricted_path;
                    additionalData.accountRestriction = response.account_restriction;

                    // Set up modules
                    var roles = {
                        scanning: 0
                        , filter_scan_results: 0
                        , results: 0
                        , reporting: 0
                        , db_access: 0
                        , patching: 0
                        , vim_integration: 0
                    };
                    for (roleId in response.roles) {
                        switch (parseInt(roleId, 10)) {
                            case 1:
                                roles.scanning = 1;
                                break;
                            case 2:
                                roles.results = 1;
                                break;
                            case 3:
                                roles.reporting = 1;
                                break;
                            case 4:
                                roles.patching = 1;
                                break;
                            case 7:
                                roles.vim_integration = 1;
                                break;
                            case 8:
                                roles.filter_scan_results = 1;
                                break;
                            case 10:
                                roles.db_access = 1;
                                break;
                            default:
                                break;
                        }
                    }

                    additionalData.roles = roles;


                    // self.loadAccountConfiguration( additionalData );
                    var createPartitionAdmin = Ext.create("sfw.view.administration.UserManagementCreateNew",
                        {
                            mode: mode,
                            source : source,
                            accountId: account_id,
                            listeners: {
                                afterrender: function (comp) {
                                   if( LoginDetails.account.ssoSettings.is_sso != 1) {
                                       Ext.getCmp('generate_password').setVisible(true);
                                       Ext.getCmp('generate_password').setValue(false);
                                   }
                                    me.ssoConfiguration(type_sso,'edit');
                                    Ext.getCmp('user_assign').show();
                                    Ext.getCmp('account_name').setValue(record.account_name);
                                    Ext.getCmp('account_username').setValue(record.account_username);
                                    Ext.getCmp('account_email').setValue(record.account_email);
                                    Ext.getCmp('recipient_email').setValue(record.recipient_email);
                                    Ext.getCmp('view_all_completed_scans_and_single_host_agents').setValue(record.view_all_completed_scans_and_single_host_agents);
                                    // Break the mobile number, if exists, into the country
                                    // code and the actual number. They are '-' separated.
                                    var mobileArray, countryCode, mobileNumber;
                                    if (record.recipient_mobile.length) {
                                        mobileArray = record.recipient_mobile.split('-');
                                        if (2 === mobileArray.length) {
                                            countryCode = parseInt(mobileArray[0], 10);
                                            mobileNumber = parseInt(mobileArray[1], 10);
                                        }
                                        Ext.getCmp('recipient_country_code').setValue(countryCode);
                                        Ext.getCmp('recipient_mobile_number').setValue(mobileNumber);
                                    }
                                    //TODO waiting for edition constant


                                    /*if ( sfw.sharedConstants.EDITION == sfw.sharedConstants.SERVER_EDITION ) { /!* EDITION *!/
                                        if ( !( LoginDetails.account.ssoSettings.is_sso == 1 && this.isUserManagement )) {
                                            thisForm.ldapCheckbox.setValue(parseInt( record.auth_type,10) == sfw.sharedConstants.AUTH_TYPE_LDAP );
                                        }
                                    }/!* EDITION *!/*/
                                    //TODO

                                    /*if ( LoginDetails.account.ssoSettings.is_sso == 1 && this.isUserManagement ) {
                                        thisForm.ssoCheckbox.setValue(parseInt( record.auth_type,10) == sfw.sharedConstants.AUTH_TYPE_SSO );
                                    }*/
                                    if (LoginDetails.account.ssoSettings.is_sso == 1) {
                                        Ext.getCmp('sso').setValue(parseInt(record.auth_type, 10) == sfw.util.SharedConstants.AUTH_TYPE_SSO);
                                        Ext.getCmp('generate_password_view').setVisible(false);
                                        if (type == 'partitionmanagement') {
                                            Ext.getCmp('generate_password').setValue(false);
                                            Ext.getCmp('generate_password').hide();

                                        }
                                    } else {
                                        if (type == 'partitionmanagement') {
                                            Ext.getCmp('generate_password_view').setVisible(false);
                                        }
                                    }

                                    var usersAssigned, hostsAssigned, userLimit, hostLimit, actualLimit, newText,
                                        usedHostLicenses, usedUserLicenses = 0;
                                    var actualLimitUser = 0;

                                    if (type == 'partitionmanagement') {

                                        Ext.getCmp('user_limit').hide();
                                        Ext.getCmp('unlimited_user_checkbox').hide();
                                        Ext.getCmp('user_license_display').hide();
                                        Ext.getCmp('host_limit').hide();
                                        Ext.getCmp('unlimited_host_checkbox').hide();
                                        Ext.getCmp('host_license_display').hide();
                                        Ext.getCmp('shared_sgs').hide();
                                        Ext.getCmp('restrict_network').hide();
                                        Ext.getCmp('roles_permissions').hide();

                                        // Hide the View All Scans and Agents checkbox for Partition Admins
                                        Ext.getCmp('view_all_completed_scans_and_single_host_agents').setVisible(false);

                                        /!* Configure the Assign Host Licenses field *!/

                                        hostsAssigned = parseInt(record.host_granted, 10);
                                        if (hostsAssigned) {
                                            Ext.getCmp('host_assign_number').setValue(hostsAssigned);
                                        }
                                        // The limit is the number already granted + the number available to be granted
                                        actualLimit =  parseInt(hostsAssigned) + parseInt(licenseData.hostsAvailableInPool);
                                        Ext.getCmp('host_assign_number').setMaxValue(actualLimit);
                                        // The assigned host licenses can't be below the amount actually in use
                                        usedHostLicenses = parseInt(record.hosts_used, 10);
                                        Ext.getCmp('host_assign_number').setMinValue(usedHostLicenses);

                                        // Re-validate the form using the new limits
                                        Ext.getCmp('host_assign_number').validate();

                                        // Update the text label to include the new limits
                                        if (usedHostLicenses > 0) {
                                            // If some hosts are used then show them as the minimum accepted value
                                            newText = '(Choose a value between ' + usedHostLicenses + ' and ' + actualLimit + ')';
                                        } else {
                                            newText = '(' + actualLimit + ' Host Licenses Available)';
                                        }
                                        Ext.getCmp('host_license_assign').setValue(newText);

                                        /!* Configure the Assign User Licenses field *!/

                                        usersAssigned = parseInt(record.user_granted, 10);
                                        if (usersAssigned) {
                                            Ext.getCmp('user_assign_number').setValue(usersAssigned);
                                        }
                                        // The limit is the number already granted + the number available to be granted
                                        actualLimitUser = parseInt(usersAssigned) + parseInt(licenseData.usersAvailableInPool);
                                        Ext.getCmp('user_assign_number').setMaxValue(actualLimitUser);
                                        // The assigned user licenses can't be below the amount actually in use
                                        if ('undefined' !== additionalData.usedUserLicenses) {
                                            usedUserLicenses = parseInt(additionalData.usedUserLicenses, 10);
                                        }
                                        Ext.getCmp('user_assign_number').setMinValue(usedUserLicenses);

                                        // Re-validate the form using the new limits
                                        Ext.getCmp('user_assign_number').validate();

                                        // Update the text label to include the new limits
                                        if (0 < usedUserLicenses) {
                                            // Modify display text since they already have some assigned.
                                            newText = '(Choose a value between ' + usedUserLicenses + ' and ' + actualLimitUser + ')';
                                        } else {
                                            newText = '(' + actualLimitUser + ' User Licenses Available)';
                                        }
                                        Ext.getCmp('user_license_assign').setValue(newText);

                                    } else if (record.is_admin == 1) {
                                        Ext.getCmp('user_assign').hide();
                                        Ext.getCmp('user_license_assign').hide();
                                        Ext.getCmp('host_assign').hide();
                                        Ext.getCmp('host_license_assign').hide();
                                       // Ext.getCmp('host_limit').hide();
                                        Ext.getCmp('roles_permissions').hide();
                                        Ext.getCmp('restrict_network').hide();
                                        if(!LoginDetails.isPartitionAdmin){
                                            Ext.getCmp('shared_sgs').hide();
                                        }

                                        hostLimit = parseInt(record.host_limit, 10);
                                        // if Negative, leave unlimited checked which is the default
                                        if (0 <= hostLimit) {
                                            Ext.getCmp('unlimited_host_checkbox').setValue(false);
                                            Ext.getCmp('host_limit').setValue(hostLimit);
                                        }

                                        userLimit = parseInt(record.user_limit, 10);
                                        if (0 <= userLimit) {
                                            Ext.getCmp('unlimited_user_checkbox').setValue(false);
                                            Ext.getCmp('user_limit').setValue(userLimit);
                                        }

                                        usedHostLicenses = parseInt(record.hosts_used, 10);

                                        if ('undefined' !== additionalData.usedUserLicenses) {
                                            usedUserLicenses = parseInt(additionalData.usedUserLicenses, 10);
                                        }

                                        if ( 0 < licenseData.hostsAvailableInPool ) {
                                            newText = '(' + licenseData.hostsAvailableInPool + ' Host Licenses Available)';
                                            Ext.getCmp('host_license_display').setValue(newText);
                                            Ext.getCmp('host_limit').setMaxValue(licenseData.hostsAvailableInPool);

                                        }

                                        if ( 0 < licenseData.usersAvailableInPool ) {
                                            newText = '(' + licenseData.usersAvailableInPool + ' User Licenses Available)';

                                            Ext.getCmp('user_license_display').setValue( newText );
                                            Ext.getCmp('user_limit').setMaxValue( licenseData.usersAvailableInPool );

                                        }

                                        if (0 < usedHostLicenses) {
                                            actualLimit = parseInt(usedHostLicenses) + parseInt(licenseData.hostsAvailableInPool);
                                            newText = '(Choose a value between ' + usedHostLicenses + ' and ' + actualLimit + ')';
                                            Ext.getCmp('host_license_display').setValue(newText);
                                            Ext.getCmp('host_limit').setMinValue(usedHostLicenses);
                                            Ext.getCmp('host_limit').setMaxValue(actualLimit);
                                        }
                                        if (0 < usedUserLicenses) {
                                            actualLimit = parseInt(usedUserLicenses) + parseInt(licenseData.usersAvailableInPool);
                                            newText = '(Choose a value between ' + usedUserLicenses + ' and ' + actualLimit + ')';
                                            Ext.getCmp('user_license_display').setValue(newText);
                                            Ext.getCmp('user_limit').setMinValue(usedUserLicenses);
                                            Ext.getCmp('user_limit').setMaxValue(actualLimit);
                                        }

                                    } else if (record.is_admin == 0) {

                                        Ext.getCmp('user_license_field').hide();
                                        Ext.getCmp('host_assign').hide();
                                        Ext.getCmp('host_license_assign').hide();
                                        if(!LoginDetails.isPartitionAdmin){
                                            Ext.getCmp('shared_sgs').hide();
                                        }
                                        if ( 0 < licenseData.hostsAvailableInPool ) {
                                            newText = '(' + licenseData.hostsAvailableInPool + ' Host Licenses Available)';
                                            Ext.getCmp('host_license_display').setValue(newText);
                                            Ext.getCmp('host_limit').setMaxValue(licenseData.hostsAvailableInPool);

                                        }

                                        if ( 0 < licenseData.usersAvailableInPool ) {
                                            newText = '(' + licenseData.usersAvailableInPool + ' User Licenses Available)';

                                            Ext.getCmp('user_license_display').setValue( newText );
                                            Ext.getCmp('user_limit').setMaxValue( licenseData.usersAvailableInPool );

                                        }
                                        hostLimit = parseInt(record.host_limit, 10);
                                        // if Negative, leave unlimited checked which is the default
                                        if (0 <= hostLimit) {
                                            Ext.getCmp('unlimited_host_checkbox').setValue(false);
                                            Ext.getCmp('host_limit').setValue(hostLimit);
                                        }

                                        usedHostLicenses = parseInt(record.hosts_used, 10);
                                        // Deal with display
                                        if (0 < usedHostLicenses) {
                                            actualLimit = parseInt(usedHostLicenses) + parseInt(licenseData.hostsAvailableInPool);
                                            newText = '(Choose a value between ' + usedHostLicenses + ' and ' + actualLimit + ')';
                                            Ext.getCmp('host_license_display').setValue(newText);
                                           Ext.getCmp('host_limit').setMinValue(usedHostLicenses);
                                            Ext.getCmp('host_limit').setMaxValue(actualLimit);
                                        }

                                    }

                                    // The rest of the fiels only affect normal users
                                    // (i.e. roles and restrictions)
                                    if (record.is_admin == 0) {

                                        var roles = {
                                            scanning: 0
                                            , filter_scan_results: 0
                                            , results: 0
                                            , reporting: 0
                                            , db_access: 0
                                            , patching: 0
                                            , vim_integration: 0
                                        };
                                        if (additionalData.roles) {

                                            roles = additionalData.roles;
                                        }


                                        if (Ext.getCmp('view_all_completed_scans_and_single_host_agents')) {
                                            var allowAllCompletedScans = Ext.getCmp('view_all_completed_scans_and_single_host_agents');
                                            allowAllCompletedScans.setValue(record.view_all_completed_scans_and_single_host_agents ? true : false);
                                        }

                                        if (record.is_restricted) {
                                            // @msp
                                            // Pre-select the Restricted radio button if the loaded Account is a restricted account
                                            var restrictedPermRadioButton = Ext.getCmp('restricted');
                                            if (restrictedPermRadioButton) {
                                                restrictedPermRadioButton.setValue(true);
                                            }
                                        } else if (record.is_read_only == 1) {
                                            Ext.getCmp('read_only').setValue(true);
                                        } else {
                                            Ext.getCmp('read_write').setValue(true);

                                            // While we know we're not read only, set the
                                            // write dependent roles
                                            if (roles.scanning) {
                                                Ext.getCmp('scanning').setValue(true);
                                                Ext.getCmp('filter_scan_result').setValue(roles.filter_scan_results);
                                            }

                                            Ext.getCmp('patching').setValue(roles.patching);
                                        }

                                        // @msp
                                        // Configure the Edit User form if the loaded Account is a restricted account
                                        // Restricted MSP Users have everything disabled except Results and Reporting
                                        if (record.is_restricted) {
                                            // Uncheck all of the Roles & Sub-Roles checkboxes
                                            Ext.getCmp('scanning').reset();
                                            Ext.getCmp('filter_scan_result').reset();
                                            Ext.getCmp('database_access').reset();
                                            Ext.getCmp('patching').reset();
                                            //thisForm.vimIntegrationRoleCheckbox.reset();
                                            Ext.getCmp('account_restriction').reset();
                                            Ext.getCmp('reporting').setValue(roles.reporting);
                                            Ext.getCmp('results').setValue(roles.results);

                                            // Disable all checkboxes except Results and Reports
                                            Ext.getCmp('scanning').setDisabled(true);
                                            Ext.getCmp('filter_scan_result').setDisabled(true);
                                            Ext.getCmp('patching').setDisabled(true);
                                            //thisForm.vimIntegrationRoleCheckbox.setDisabled( true );
                                            Ext.getCmp('database_access').setDisabled(true);
                                            Ext.getCmp('account_restriction').setDisabled(true);
                                            /*if (thisForm.isAdminRoleCheckbox) {
                                                thisForm.isAdminRoleCheckbox.reset();
                                                thisForm.isAdminRoleCheckbox.setDisabled( true );
                                            }*/
                                        } else {

                                            // The rest are not write-access dependent.
                                            Ext.getCmp('results').setValue(roles.results);
                                            if (roles.reporting) {
                                                Ext.getCmp('reporting').setValue(true);
                                                Ext.getCmp('database_access').setValue(roles.db_access);
                                            }
                                            //thisForm.vimIntegrationRoleCheckbox.setValue( roles.vim_integration );

                                            // RESTRICTIONS SECTION
                                            //TODO  can be done after saving AD data
                                            /*var adRestrictedPath = '';
                                            if ('undefined' !== additionalData.adRestrictedPath) {
                                                adRestrictedPath = additionalData.adRestrictedPath;
                                            }


                                            if (adRestrictedPath) {
                                                Ext.getCmp('ad_restriction').setValue(true);
                                                // thisForm.adRestrictionPathDisplay.setValue( adRestrictedPath );
                                            }*/

                                            if (!Ext.isEmpty(additionalData.adRestrictedPath)) {
                                                comp.down('#ad_restrict').setHidden(false);
                                                comp.down('#ad_restriction').setValue(true);
                                                var selectedNode = comp.down('#ad_selected_node');
                                                selectedNode.setValue(additionalData.adRestrictedPath);
                                            }

                                            var accountRestriction = 0;
                                            if ('undefined' !== additionalData.accountRestriction) {
                                                accountRestriction = additionalData.accountRestriction;
                                            }

                                            Ext.getCmp('account_restriction').setValue(accountRestriction);
                                        }

                                        var params = {
                                            account_id: account_id
                                        };
                                        //thisForm.ipGridPanel.getStore().load({ params: params });*/
                                    }

                                    if (record.is_admin == 1 || record.is_admin == 0) {
                                        // Reload the Share Smart Groups form when loading a Use
                                        //TODO

                                        var sgstore = Ext.getStore('sharesmartgroups');
                                        var toSelect = []

                                        sgstore.getProxy().setExtraParams({
                                            'account_id': account_id });
                                        sgstore.load({

                                            callback: function (records, operation, success) {

                                                for (var i = 0; i < records.length; i++) {
                                                    if (records[i].data.is_shared == 1) {
                                                        toSelect.push(records[i]);
                                                    }

                                                }
                                                Ext.ComponentQuery.query("#share_sg_chkbx_col")[0].getSelectionModel().select(toSelect);
                                            }
                                        });

                                    }

                                    // Run the validation to ensure we enable the save button
                                    me.validateAccountForm();
                                    // Now double check who we are actually trying to edit and
                                    // what kind of user we are and disable parts of the form
                                    // if necessary. Do last in case we disable save button.

                                    me.enforceEditRights(record);


                                }
                            }
                        });
                    createPartitionAdmin.show();
                    if ( LoginDetails.isSMSEnabled ) {
                        createPartitionAdmin.down('#mobile_recipients').show();
                    }

                    if (LoginDetails.loginAccountId === parseInt(record.account_id, 10)) {
                        createPartitionAdmin.setTitle('View Your Account - Note: you may only edit your recipient info.');
                    } else if (!LoginDetails.isPartitionAdmin && (record.is_admin == 1)) {
                        createPartitionAdmin.setTitle('View Administrator Account - Note: only the Partition Administrator can edit other Admins.');
                    } else if (type == 'partitionmanagement') {
                        createPartitionAdmin.setTitle('View/Edit Partition Administrator');
                    } else if (record.is_admin == 1) {
                        createPartitionAdmin.setTitle('View/Edit Administrator');
                    } else {
                        createPartitionAdmin.setTitle('View/Edit User');
                    }

                }
            },
            failure: function (response, opts) {

            }
        });


    },

    ssoConfiguration(type,source) {


        var LoginDetails = sfw.util.Auth.LoginDetails;
        //by default the LDAP checkbox should be hidden in cloud
        if ( sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION ) {
            Ext.getCmp('ldap').setVisible(false);
            Ext.getCmp('ldap').setValue( false );
        }

        if (LoginDetails.account.ssoSettings.is_sso == 1 && type == 'user') {

            Ext.getCmp('sso').setVisible(true);
            if (LoginDetails.account.ssoSettings.disable_standard_login == 1) {
                Ext.getCmp('generate_password_view').setVisible(false);
                Ext.getCmp('generate_password_view').setValue(false);
                Ext.getCmp('generate_password').setValue(false);
            } else {
                if(source == 'edit'){
                    Ext.getCmp('generate_password').setVisible(true);
                    Ext.getCmp('generate_password').setValue(false);

                }else{
                    Ext.getCmp('generate_password_view').setVisible(true);
                }

            }
            if ( sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION ) {
                Ext.getCmp('ldap').setVisible(false);
                Ext.getCmp('ldap').setValue( false );
            }
        } else if (LoginDetails.account.ssoSettings.is_sso != 1 && type == 'user') {
            if(source == 'edit'){
                Ext.getCmp('generate_password_view').setVisible(false);
            }else{
                Ext.getCmp('generate_password_view').setVisible(true);
            }

            Ext.getCmp('sso').setVisible(false);
            Ext.getCmp('sso').setValue(false);
            if ( sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION ) {
                Ext.getCmp('ldap').setVisible(true);
            }
        } else if (type == 'partition') {
            Ext.getCmp('sso').setVisible(false);


        }
        //Since use SSO checkbox will be checked by default, we will hide generate password checkbox
        if (LoginDetails.account.ssoSettings.is_sso == 1 && Ext.getCmp('sso').getValue()) {
            Ext.getCmp('generate_password_view').hide();
            Ext.getCmp('generate_password').hide();
        }

    },

    noLimitHost: function (checkbox) {

        var checked = checkbox.value;
        Ext.getCmp('host_limit').setDisabled(checked);
        this.validateAccountForm();
    },

    noLimitUser: function (checkbox) {

        var checked = checkbox.value;
        Ext.getCmp('user_limit').setDisabled(checked);
        this.validateAccountForm();
    },

    checkSSO: function (checkbox) {

        var source = checkbox.up('window').getSource();
        var checked = checkbox.value;
        var LoginDetails = sfw.util.Auth.LoginDetails;

        if (LoginDetails.account.ssoSettings.is_sso == 1 && LoginDetails.account.ssoSettings.disable_standard_login == 0) {
            if (checked) {
                Ext.getCmp('generate_password_view').hide();
                Ext.getCmp('generate_password').hide();
            } else {
                if(source == 'edit'){
                    Ext.getCmp('generate_password').show();
                }else {
                    Ext.getCmp('generate_password_view').show();
                }

            }
        }
    },

    checkSubRoleScanning: function (checkbox) {

        var checked = checkbox.value;
        Ext.getCmp('filter_scan_result').setDisabled(!checked);
    },

    checkSubRoleReporting: function (checkbox) {
        var checked = checkbox.value;
        if (Ext.getCmp('restricted').getValue()) {
            Ext.getCmp('database_access').setDisabled(true);
        } else {
            Ext.getCmp('database_access').setDisabled(!checked);
        }
    },

    dbAccessWarning: function (checkbox) {
        var checked = checkbox.value;
        var warningText = "Access to Database Console will allow a user to have access to raw data. It's recommended to limit access to this module to very few users, should this user have this access?";
        if (checked) {
            Ext.getCmp('restrict_network').setTitle("Restrict User\'s Network Access - Unavailable when Database Console Role Granted ");
            Ext.getCmp('restrict_network').collapse();
            Ext.getCmp('restrict_network').setDisabled(true);
            Ext.MessageBox.confirm('Warning ', warningText, function (button) {
                if (button === 'no') {
                    Ext.getCmp('database_access').setValue(false);
                }
            });
        } else {
            Ext.getCmp('restrict_network').setTitle("Restrict User\'s Network Access");
            Ext.getCmp('restrict_network').expand();
            Ext.getCmp('restrict_network').setDisabled(false);

        }
    },

    readWriteSelection: function (radio) {
        if (radio.value) {
            Ext.getCmp('scanning').setDisabled(false);
            Ext.getCmp('patching').setDisabled(false);
            Ext.getCmp('account_restriction').setDisabled(false);

        }

    },

    readOnlySelection: function (radio) {
        if (radio.value) {
            Ext.getCmp('scanning').setDisabled(true);
            Ext.getCmp('patching').setDisabled(true);
            Ext.getCmp('account_restriction').setDisabled(true);

        }

    },

    restrictedSelection: function (radio) {
        if (radio.value) {
            Ext.getCmp('scanning').setDisabled(true);
            Ext.getCmp('patching').setDisabled(true);
            Ext.getCmp('database_access').setValue(false);
            Ext.getCmp('database_access').setDisabled(true);
            Ext.getCmp('account_restriction').setDisabled(true);

        }
    },

    enableAdButton: function (checkbox) {
        var me = this,
            adBrowserBtn = me.getView().down('#browse_ad_button');

        adBrowserBtn.setDisabled(!checkbox.value);

        if (checkbox.value) {
            //Ext.getCmp('browse_ad_button').setDisabled(false);
            //Ext.getCmp( 'patching' ).setDisabled( true);
        }
    },

    openADDialog: function (btn) {
        var me = this,
            view = me.getView(),
            selectedNode = view.down('#ad_selected_node');

        var adDialog = Ext.create('sfw.view.common.ActiveDirectoryDialog', {
            animateTarget: btn,
            listeners: {
                close: function (dialog) {
                    var node = dialog.getSelectedDirectory();
                    if (node) {
                        selectedNode.setValue(node.fqdn);
                    }
                }
            }
        });

        adDialog.show();
    },

    saveAdmin: function (btn) {
        var me = this;

        var LoginDetails = sfw.util.Auth.LoginDetails;
        var allParams = {};

        var mode = btn.up('window').getMode();
        var accountId = btn.up('window').getAccountId();


        // The following fields are used by ALL types:
        allParams['account_name'] = Ext.getCmp('account_name').getValue();
        allParams['account_username'] = Ext.getCmp('account_username').getValue();
        allParams['account_email'] = Ext.getCmp('account_email').getValue();

        var setoption = sfw.Default.setForAccount("view_all_completed_scans_and_single_host_agents", Ext.getCmp('view_all_completed_scans_and_single_host_agents').getValue() ? 1 : 0, accountId)

        allParams['view_all_completed_scans_and_single_host_agents'] = Ext.getCmp('view_all_completed_scans_and_single_host_agents').getValue() ? 1 : 0;

        if (LoginDetails.account.ssoSettings.is_sso == 0 || (mode == 'partition')) {
            allParams['generate_password'] = Ext.getCmp('generate_password').getValue() ? 1 : 0;
        } else if (LoginDetails.account.ssoSettings.is_sso == 1 && LoginDetails.account.ssoSettings.disable_standard_login == 0) {
            // checking for the 'use sso for authentication' checkbox. If not selected then only we'll pass value for 'generate password' checkbox.
            var ssoAuth = Ext.getCmp('sso').getValue() ? 1 : 0;
            if (ssoAuth == 0) {
                allParams['generate_password'] = Ext.getCmp('generate_password').getValue() ? 1 : 0;
            }
        }

        if ( sfw.SharedConstants.EDITION == sfw.SharedConstants.SERVER_EDITION ) {
             if ( !( LoginDetails.account.ssoSettings.is_sso == 1 && (mode != 'partition') )) {
                 allParams['ldap_auth'] = Ext.getCmp('ldap').getValue() ? 1 : 0;
             }
         }

        if ( LoginDetails.account.ssoSettings.is_sso == 1 && (mode != 'partition')) {
            allParams['sso_auth'] = Ext.getCmp('sso').getValue() ? 1 : 0;
        }

        allParams['recipient_email'] = Ext.getCmp('recipient_email').getValue();

        if (Ext.getCmp('recipient_country_code').getValue() && Ext.getCmp('recipient_mobile_number').getValue()) {
            allParams['recipient_country_code'] = Ext.getCmp('recipient_country_code').getValue();
            allParams['recipient_mobile_number'] = Ext.getCmp('recipient_mobile_number').getValue();
        }

        allParams['is_partition_admin'] = 0;
        allParams['is_admin'] = 0;


        if (mode == 'partition') {

            allParams['is_partition_admin'] = 1;
            allParams['is_admin'] = 1;
            allParams['host_license_limit'] = -1; // unlimited by default
            allParams['user_license_limit'] = -1; // unlimited by default

            allParams['host_assign'] = 0;
            if (Ext.getCmp('host_assign_number').getValue()) {
                allParams['host_assign'] = parseInt(Ext.getCmp('host_assign_number').getValue(), 10);
            }

            allParams['user_assign'] = 1;
            // cannot create a partition without at least a license for himself
            if (1 < parseInt(Ext.getCmp('user_assign_number').getValue(), 10)) {
                allParams['user_assign'] = parseInt(Ext.getCmp('user_assign_number').getValue(), 10);
            }

        } else if (mode == 'admin') {
            allParams['is_admin'] = 1;
            //TODO
            // Admin: get both host and user license limits
            if (Ext.getCmp('unlimited_host_checkbox').getValue()) {
                allParams['host_license_limit'] = -1; // unlimited
            } else {
                allParams['host_license_limit'] = parseInt(Ext.getCmp('host_limit').getValue(), 10);
            }

            if (Ext.getCmp('unlimited_host_checkbox').getValue()) {
                allParams['user_license_limit'] = -1; // unlimited
            } else {
                allParams['user_license_limit'] = parseInt(Ext.getCmp('user_limit').getValue(), 10);
            }

        } else {
            // Only need host licenses (cut and paste of
            // above for admins)
            //TODO
            if (Ext.getCmp('unlimited_host_checkbox').getValue()) {
                allParams['host_license_limit'] = -1; // unlimited
            } else {
                allParams['host_license_limit'] = parseInt(Ext.getCmp('host_limit').getValue(), 10);
            }
            allParams['user_license_limit'] = 0;

            // Get role/permissions data
            allParams['read_only'] = 0;
            if (Ext.getCmp('read_only').getValue()) {
                allParams['read_only'] = 1;
            }

            // Add restricted flag if chosen
            var restrictedPermRadioButton = Ext.getCmp('restricted').getValue();
            if (restrictedPermRadioButton) {
                allParams['is_restricted'] = 1;
            }
            // Init them all so the values always exist
            allParams['role_scanning'] = 0;
            allParams['role_filter_scan_results'] = 0;
            allParams['role_patching'] = 0;
            allParams['role_results'] = 0;
            allParams['role_reporting'] = 0;
            allParams['role_db_access'] = 0;
            allParams['role_vim_integration'] = 0;

            // Now adjust based on selections
            if (!allParams['read_only']) {
                allParams['role_scanning'] = Ext.getCmp('scanning').getValue() ? 1 : 0;
                if (allParams['role_scanning']) {
                    allParams['role_filter_scan_results'] = Ext.getCmp('filter_scan_result').getValue() ? 1 : 0;
                }

                allParams['role_patching'] = Ext.getCmp('patching').getValue() ? 1 : 0;
            }

            // The rest don't depend on having write access
            allParams['role_results'] = Ext.getCmp('results').getValue() ? 1 : 0;
            allParams['role_reporting'] = Ext.getCmp('reporting').getValue() ? 1 : 0;
            if (allParams['role_reporting']) {
                allParams['role_db_access'] = Ext.getCmp('database_access').getValue() ? 1 : 0;
            }


            /*allParams['role_vim_integration'] = this.vimIntegrationRoleCheckbox.getValue() ? 1 : 0;*/

            // -- Restrictions --
            // We can only set restrictions if we didnt' enable DB_ACCESS
            if (!allParams['role_db_access']) {

                // AD:
                //TODO ad path

                /* if ( Ext.getCmp( 'ad_restriction' ).getValue()) {
                     allParams['ad_restricted_path'] = this.adRestrictionPathDisplay.getValue();
                 }*/

                // Account - "limit to me":
                allParams['account_restriction'] = 0;
                if (Ext.getCmp('account_restriction').getValue()) {
                    allParams['account_restriction'] = 1;
                }
                var IPs = [];
                allParams['IPs'] = Ext.encode( IPs );
            }
        }

        var shareList = {selected: [], unselected: []};

        var allRecords = (Ext.getStore('sharesmartgroups').getData().getSource() || Ext.getStore('sharesmartgroups').getData()).getRange();

        if (mode != 'partition') {
            Ext.getStore('sharesmartgroups').each(function (record) {

                shareList.unselected.push(record.get("id"));
            });
        }

        var selected = Ext.getCmp('share_sg_chkbx_col').getSelectionModel().getSelected();


        if (selected.length) {

            for (var i = 0; i < selected.length; i++) {
                var position = shareList.unselected.indexOf(selected.items[i].data.id);
                if (-1 !== position) {
                    shareList.unselected.splice(position, 1);
                }
                shareList.selected.push(selected.items[i].data.id);
            }
        }

        if (shareList.selected && shareList.unselected && LoginDetails.isPartitionAdmin) {
            allParams["smartgroup_share_lists[selected]"] = shareList.selected.join(",");
            allParams["smartgroup_share_lists[unselected]"] = shareList.unselected.join(",");
        }

        //If an accountId is set we are editing, else
        // creating. We make the same API call either way, we
        // just need to set the accountId in params.
        allParams['account_id'] = accountId;

        var which = "editCreate_User";

        if(mode == 'partition'){
            which = "editCreate_Partition";
        }

        //AD integration
        const hasADIntegration = sfw.ActiveDirectorySettings.hasADIntegration(),
            selectedNode = me.getView().down('#ad_selected_node');
            var nodeValue = selectedNode.getValue();

        if (hasADIntegration && !Ext.isEmpty(selectedNode) && nodeValue != 'No AD Branch Selected') {
            allParams['ad_restricted_path'] = selectedNode.getValue();
        }

        Ext.Ajax.request({
            url: 'action=account_management&which='+which
            , method: "POST"
            , params: allParams
            , dataType: 'json'
            , success: function (data) {
                var status = Ext.util.JSON.decode(data.responseText);
                var title, text;
                switch (status.error) {
                    case 0:
                        // self.mainScope.refresh(); // refresh main grid
                        // self.accountDetailsWindow.hide();
                        //
                        btn.up('window').destroy();
                        if(mode == 'partition') {
                            var partitionstore = Ext.getStore('partitionmanagement');
                            partitionstore.load();
                        }else{
                            var userstore = Ext.getStore('usermanagement');
                            userstore.load();
                        }

                        title = 'Success';
                        text = 'Account saved.';
                        break;

                    case 3:
                        title = 'Error';
                        text = 'The selected username is already taken. Please try another.';
                        break;

                    case 6:
                        title = 'Error';
                        text = 'Form submitted with invalid data.';
                        break;

                    case 31:
                        title = 'Error';
                        text = 'License Issue: Cannot remove licenses that have already been used.';
                        break;

                    case 32:
                        title = 'Error';
                        text = 'License Issue: Not enough licenses available.';
                        break;

                    case 30:
                        title = 'Error';
                        text = 'License Issue: The prescribed license limit has been reached.';
                        break;

                    case 15:
                    case 20:
                        title = 'Partial failure (Account still saved)';
                        text = 'Some restrictions were not saved.';
                        if (15 == status.error) {
                            text += ' The AD path was invalid.';
                        } else {
                            test += ' A hostname or IP value/range/network failed to validate.';
                        }
                        break;

                    case 76:
                        title = 'Error';
                        text = 'The selected username was not found in the LDAP directory.';
                        break;

                    default:
                        // Note, there are more potential error
                        // return codes - we only show messages
                        // for the expected ones. The rest are
                        // redundant safety mechanisms, and are
                        // never actually expected. Cath those all
                        // here.
                        title = 'Error';
                        text = 'Unexpected error.';
                        break;
                }
                // self.closeAccountButton.setDisabled( false );
                Ext.Msg.alert(title, text);
            }
            , failure: function () {
                sfw.util.Debug.log( "Error saving Agent/Site configuration (2)." );
                Ext.Msg.show({
                    title: "Error"
                    , msg: "Saving configuration failed."
                    , buttons: Ext.Msg.OK
                    , icon: Ext.MessageBox.ERROR
                });
            }
        });
    },

    onUserRecordDblClicked: function(view, record) {
        var me = this,
            options = {};

        options.extra = record;

        me.editUser(null, null, options);
    },

    validateAccountForm: function (event, target, options) {

        Ext.getCmp('saveUserButton').setDisabled(true);
        if (!Ext.getCmp('account_name').isValid()
            || !Ext.getCmp('account_username').isValid()
            || !Ext.getCmp('account_email').isValid()
            || !Ext.getCmp('recipient_email').isValid()
            || !Ext.getCmp('recipient_mobile_number').isValid()
        ) {
            return false;
        }

        if (Ext.getCmp('unlimited_host_checkbox').getValue() !== true && !Ext.getCmp('host_limit').isValid()) {
            return false;
        }
        if (Ext.getCmp('unlimited_user_checkbox').getValue() !== true && !Ext.getCmp('user_limit').isValid()) {
            return false;
        }
        if (!Ext.getCmp('user_assign_number').isValid() || !Ext.getCmp('host_assign_number').isValid()) {
            return false;
        }


        var mobileNumber = Ext.getCmp('recipient_mobile_number').getValue();
        var prefix = Ext.getCmp('recipient_country_code').getValue();

        if (prefix && !mobileNumber) {
            Ext.getCmp('recipient_mobile_number').markInvalid(true);
            return false;

        } else if (!prefix && mobileNumber) {
            Ext.getCmp('recipient_country_code').markInvalid(true);
            return false;

        } else if (mobileNumber
            && (mobileNumber.toString().length > 15
                || mobileNumber.toString().length < 6)
        ) {
            Ext.getCmp('recipient_mobile_number').markInvalid(true);
            return false;
        }

        Ext.getCmp('saveUserButton').setDisabled(false);
        return true;

    },

    enforceEditRights : function ( dataRecord ) {

        var windowTitle = '';


        var LoginDetails = sfw.util.Auth.LoginDetails;

        // If it is your own account, you can edit only the
        // recipient details. Note - if you are in here at
        // all, you are an admin, so much of the form is
        // already hidden - just disable the account details
        // section.
        if ( LoginDetails.loginAccountId === parseInt( dataRecord.account_id, 10 ) ) {
            windowTitle = "View Your Account - Note: you may only edit your recipient info.";
            Ext.getCmp('account_details').setDisabled(true);
            Ext.getCmp('shared_sgs').setDisabled(true);

        } else if (!LoginDetails.isPartitionAdmin
            && (dataRecord.is_admin!= 0 )) {
            // If it is another admin account and we are not the
            // partition admin, we can't edit at all
            windowTitle = "View Administrator Account - Note: only the Partition Administrator can edit other Admins.";
            Ext.getCmp('account_details').setDisabled(true);
            Ext.getCmp('recipients_details').setDisabled(true);
            Ext.getCmp('saveUserButton').setDisabled(true);
        }

    },

    deletePartionAccount:function(event, target,options){
        Ext.Msg.show({
            title:'Delete Allow List Rule',
            message: 'Are you sure you want to delete the user '+ options.extra.data.account_name +' and all associated data',
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    sfw.Default.deletePartionUserAccounts(options.extra.data.account_id,1,'partition');
                } else if (btn === 'no') {
                    //console.log('NO');
                }
            }
        });
    },

    deleteUserManagementAccount: function(event, target, options){
        var selectionArray = [];
        var deletables = 0;
        for ( var i=0; i < options.extra.length; ++i ) {
            if (sfw.Default.evaluateCanDelete(options.extra[i], 'user')){
                deletables++;
                selectionArray.push( options.extra[i].data.account_id );
            }
        }
        deleteIdList = selectionArray.join(",");
        attemptedDeletes = selectionArray.length;

        var message = '';

        if(options.extra.length > 1) {
            message = 'Are you sure you want to delete the '+ deletables +' deletable user and all associated data?';
        } else {
            message = 'Are you sure you want to delete the user '+ options.extra[0].data.account_name +' and all associated data'
        }

        Ext.Msg.show({
            title: 'Confirm Deletion',
            message: message,
            buttons: Ext.Msg.YESNO,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'yes') {
                    sfw.Default.deletePartionUserAccounts(deleteIdList,attemptedDeletes,'user');
                } else if (btn === 'no') {
                    //console.log('NO');
                }
            }
        });

    },

    onBeforeRenderUser : function(type){
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var me= this;
            view = me.getView();
        if (type == 'user') {
            if (parseInt(licenseData.usersAvailableInPool, 10) <= 0) {
                Ext.getCmp('new_user_button').setDisabled(true);

                // If the create admin button exists
                // (i.e. we are partition admin) disable
                // that too
                if (LoginDetails.isPartitionAdmin) {
                    Ext.getCmp('new_administrator_button').setDisabled(true);
                }
            }else{
                Ext.getCmp('new_user_button').setDisabled(false);
                if (LoginDetails.isPartitionAdmin) {
                    Ext.getCmp('new_administrator_button').setDisabled(false);
                }
            }

        } else {
            if (parseInt(licenseData.partitionsAvailable) <= 0
                || parseInt(licenseData.usersAvailableInPool, 10) <= 0) {
                Ext.getCmp('new_partition_admin_button').setDisabled(true);
            }else{
                Ext.getCmp('new_partition_admin_button').setDisabled(false);
            }

        }

    },
    onBeforeRender: function () {
        var LoginDetails = sfw.util.Auth.LoginDetails;
        var me = this;
        view = me.getView();
        if (!LoginDetails.isPartitionAdmin) {
            view.down('#new_admin_button').hide();

        }
    },

    onUserStoreLoad:function(store,records){

        store.removeAll();
        store.add(records[0].data.data.rows);

        licenseData.hostsAvailableInPool = records[0].data.poolLicenses.host_available;
        licenseData.hostsGrantedToPool = records[0].data.poolLicenses.host_granted;
        licenseData.usersGrantedToPool = records[0].data.poolLicenses.user_granted;
        licenseData.usersAvailableInPool = records[0].data.poolLicenses.user_available;
        var me= this,
            view = me.getView(),
            table = view.down('#userTableItemId');
        if(view.xtype === 'sfw.userManagement') {
            table.getViewModel().set('mode', 'user');
        }
        table.getViewModel().set('partitionuserdata',records[0].data.poolLicenses);
        me.onBeforeRenderUser('user');
    },

    onPartionStoreLoad:function(store,records){
        store.removeAll();
        store.add(records[0].data.data.rows);
        licenseData.hostsAvailableInPool = records[0].data.poolLicenses.host_available;
        licenseData.hostsGrantedToPool = records[0].data.poolLicenses.host_granted;
        licenseData.usersGrantedToPool = records[0].data.poolLicenses.user_granted;
        licenseData.usersAvailableInPool = records[0].data.poolLicenses.user_available;
        licenseData.partitionsGranted = records[0].data.poolLicenses.partition_granted;
        licenseData.partitionsAvailable = records[0].data.poolLicenses.partition_available;


        var me= this,
            view = me.getView(),
            table = view.down('#partitionTableItemId');
        if(view.xtype === 'sfw.csiPartitionManagement') {
            table.getViewModel().set('mode', 'partition');
        }
        table.getViewModel().set('partitionuserdata',records[0].data.poolLicenses);
        me.onBeforeRenderUser('partition');
    }
});
