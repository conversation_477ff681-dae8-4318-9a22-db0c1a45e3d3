Ext.define('sfw.view.administration.UserManagement', {
    extend: 'Ext.panel.Panel',

    xtype: 'sfw.csiUserManagement',
    title: 'User Management',

    border: 1,
    cls: 'shadow',

    requires: [
        'sfw.store.administration.UserManagementGrid'
    ],

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    controller: 'usermanagement',

    viewModel : {
        stores :{
            userMgmtStore: {
                type:  'usermanagement',
                listeners: {
                    load: 'onUserStoreLoad'
                }
            }
        }
    },

    items: [
        {
            xtype: 'sfw.partitionUserManagementTable',
            flex: 1,
            margin: '0 0 10 0',
            itemId:'userTableItemId'
        },
        {
            xtype: 'gridpanel',
            title:'User Management: Overview Of Users And Licenses In Your Partition',
            preventHeader: true,
            viewConfig : {
                deferEmptyText: false,
                emptyText: 'No accounts'
            },
            controller: 'usermanagement',
            flex: 5,
            bind: {
                store: '{userMgmtStore}'
            },
            selModel: {
                mode: 'MULTI'
            },
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                items: [
                    {
                        xtype: 'button',
                        text: 'Create New User',
                        ui : 'primary',
                        id : 'new_user_button',
                        handler: 'createUser'
                    },
                    {
                        xtype: 'button',
                        text: 'Create New Administrator',
                        ui : 'primary',
                        id : 'new_administrator_button',
                        itemId : 'new_admin_button',
                        handler: 'createAdmin'
                    },{
                        xtype:'tbfill',
                        flex:1
                    },{
                        xtype:'exportButton'
                    }
                ]
            }],

            columns: [
                { text: 'Name',dataIndex:'account_name', flex:2,
                    renderer: function( value, metaData, record ) {
                        // If it is admin, it is bold. If it is your account, it is grey.
                        var retVal = value;
                        if ( parseInt( record.get( 'is_admin' ), 10 ) ) {
                            retVal = '<b>' + retVal + '</b>';
                        }
                        if ( sfw.util.Auth.LoginDetails.loginAccountId === parseInt( record.get( 'account_id' ), 10 ) ) {
                            retVal = '<font color="#999999">' + retVal + '</font>';
                        }
                        return retVal;
                    }
                },
                { text: 'Username', dataIndex:'account_username', flex: 1 },
                { text: 'Account Email',  dataIndex:'account_email', flex: 1,hidden:true },
                { text: 'Recipient Email', dataIndex:'recipient_email',  flex: 1 },
                { text: 'Recipient Mobile', dataIndex:'recipient_mobile',  flex: 1, hidden:true },
                { text: 'Host License Limit', dataIndex:'host_limit',  flex: 1, align: 'right',
                    renderer: function( val ) {
                        var retVal = parseInt( val );
                        if ( 0 > retVal ) {
                            retVal = 'No Limit';
                        }
                        return retVal;
                    }
                },
                { text: 'Used Host Licenses',  dataIndex:'hosts_used', flex: 1, align: 'right' },
                { text: 'User Type',  dataIndex:'is_admin', flex: 1,
                    renderer: function( val, cell, record ) {
                        var retVal = 'User';

                        if ( parseInt( record.get('is_restricted'), 10 ) ) {
                            retVal = 'Restricted ' + retVal;
                        }else if ( parseInt( record.get('is_read_only'), 10 ) ) {
                            retVal = 'Read Only ' + retVal;
                        }

                        if ( parseInt( record.get('is_admin'), 10 ) ) {
                            retVal = 'Admin';
                            if ( parseInt( record.get('is_partition_admin'), 10 ) ) {
                                retVal = 'Root ' + retVal;
                            }
                        }
                        return retVal;
                    }
                },
                { text: 'Authentication Type',  dataIndex:'auth_type', flex: 1,
                    renderer: function( val, cell, record ) {
                        var value = parseInt(val, 10 );
                        var retVal = '';

                        if(value == 3)
                            retVal = 'LDAP Login';
                        else if(value == 7)
                            retVal = 'SSO Login';
                        else
                            retVal = 'Standard Login';
                        return retVal;
                    }
                },
                { text: 'User Licenses',  dataIndex:'user_limit', flex: 1,
                    renderer: function( val ) {
                        var retVal = parseInt( val );
                        if ( 0 > retVal ) {
                            retVal = 'No limit';
                        }
                        return retVal;
                    },
                    hidden:true
                },
                { text: 'Created By',  dataIndex:'parent_account_name', flex: 1,
                    renderer: function( val, cell, record ) {
                        // We normally return a 'parent_username' value here from
                        // the backend, but for ONLY the P0-Admin, this value will
                        // be meaningless and contain a random username (due to
                        // the join/groupBy). We handle this case specially here.
                        var retVal = val;
                        if ( !parseInt( record.get('account_esm'), 10 ) ) {
                            retVal = '<b>Flexera</b>';
                        }
                        return retVal;
                    }
                },
                { text: 'Last Logged In',  dataIndex:'last_login', flex: 1,
                    renderer: sfw.Default.gridRenderUTCDateInLocaltime
                },
            ],

            listeners: {
                beforerender : 'onBeforeRender',
                itemdblclick: 'onUserRecordDblClicked',
                itemcontextmenu: function (grid, record, item, index, e) {
                    var selected = grid.getSelectionModel().getSelected();
                    if(selected.items.length > 1)
                    {
                        additionalText = 'Delete All Selected (deletable) Accounts';
                    }else{
                        additionalText = 'Delete Account';
                    }

                    var contextMenu = Ext.create('Ext.menu.Menu', {
                        controller: 'usermanagement',
                        width: 260,
                        plain: true,
                        items: [{
                            text: 'View/Edit Account',
                            listeners: {
                                afterRender: function() {
                                    if (selected.items.length > 1)
                                    {
                                        this.hide();
                                    }
                                },
                                click: {fn: 'editUser', extra: record}
                            }
                        },{
                            text: additionalText,
                            listeners: {
                                afterrender:function(){
                                    if(additionalText == 'Delete Account') {
                                        if (!(sfw.Default.evaluateCanDelete(record, 'user'))){
                                            this.setDisabled(true);
                                        }
                                    }
                                },
                                click: {fn:'deleteUserManagementAccount' , extra: selected.items}
                            }
                        }]
                    });
                    e.stopEvent();
                    contextMenu.showAt(e.getXY());
                },
            },

            layout: 'fit',
            margin: '2 2 0 0',
            bbar: {
                xtype: 'pagingtoolbar',
                bind: {
                    store: '{userMgmtStore}'
                },
                region: 'south',
                displayInfo: true,
                displayMsg: 'Displaying Accounts {0} - {1} of {2}',
                emptyMsg: "No accounts to display"
            }
        }
    ]
});
