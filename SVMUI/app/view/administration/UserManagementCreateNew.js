Ext.define('sfw.view.administration.UserManagementCreateNew', {
    extend: 'Ext.window.Window',

    //  xtype: 'sfw.notificationwindow',
    width: 900,
    height: 850,
    layout: 'auto',
    scrollable: 'vertical',
    bodyPadding: 10,

    controller: 'usermanagement',
    modal: true,
    maximizable: true,

    config: {
        mode: '',
        accountId: '',
        source: ''
    },

    items: [{
        xtype: 'fieldset',
        collapsible: 'true',
        // layout: 'auto',
        height: 'auto',
        title: 'Account Details',
        id : 'account_details',
        items: [{
            layout: 'vbox',
            padding: '15 10 0 10',
            items: [{
                // layout: 'form',
                flex: 1,
                width: 950,
                //,items: self.notificationNameTextfield
                defaults: {
                    labelWidth: 150,
                    labelAlign: 'right'
                },
                items: [{
                    xtype: 'textfield',
                    allowBlank: false,
                    anchor: '97%',
                    width: 700,
                    fieldLabel: 'Name',
                    enableKeyEvents: true,
                    emptyText: 'Please enter a name for this account',
                    blankText: 'Account Name is required',
                    id: 'account_name',
                    listeners: {
                        change: "validateAccountForm",
                    }
                }, {
                    xtype: 'textfield',
                    allowBlank: false,
                    anchor: '97%',
                    width: 700,
                    fieldLabel: 'Username',
                    enableKeyEvents: true,
                    emptyText: 'Please enter a username for this account',
                    blankText: 'Username is required',
                    id: 'account_username',
                    listeners: {
                        change: "validateAccountForm",
                    }
                }, {
                    xtype: 'textfield',
                    allowBlank: false,
                    anchor: '97%',
                    width: 700,
                    fieldLabel: 'Email',
                    vtype: 'email',
                    enableKeyEvents: true,
                    emptyText: 'Please enter an account email address',
                    blankText: 'Email is required',
                    id: 'account_email',
                    listeners: {
                        change: "validateAccountForm",
                    }
                }, {
                    xtype: 'checkboxfield',
                    id: 'ldap',
                    padding: '0 0 0 155',
                    boxLabel: 'Use LDAP for authentication',
                    checked: false,
                },{
                    xtype: 'checkboxfield',
                    id: 'sso',
                    padding: '0 0 0 155',
                    boxLabel: 'Use SSO for authentication',
                    checked: true,
                    disabled: false,
                    handler: "checkSSO"
                },  {
                    xtype: 'checkboxfield',
                    id: 'generate_password_view',
                    padding: '0 0 0 155',
                    boxLabel: 'Generate a new one-time password and email it to the address specified above',
                    checked: true,
                    disabled : true
                }, {
                    xtype: 'checkboxfield',
                    id: 'generate_password',
                    padding: '0 0 0 155',
                    boxLabel: 'Generate a new one-time password and email it to the address specified above',
                    checked: true,
                    hidden : true
                }, {
                    xtype: 'checkboxfield',
                    id: 'view_all_completed_scans_and_single_host_agents',
                    padding: '0 0 0 155',
                    boxLabel: 'Allow User to see the Completed Scans and Single Host Agents from all other Users',
                    name: 'notify',
                    checked: false
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    id: 'host_license_field',
                    items: [{
                        xtype: 'checkboxfield',
                        labelWidth: 150,
                        fieldLabel: 'Host License Limit',
                        id: 'unlimited_host_checkbox',
                        boxLabel: 'No Limit',
                        checked: true,
                        handler: 'noLimitHost'
                    }, {
                        xtype: 'tbspacer',
                        width: 10
                    }, {
                        xtype: 'numberfield',
                        allowBlank: false,
                        width: 100,
                        allowDecimals: false,
                        allowNegative: false,
                        id: 'host_limit',
                        value: 0,
                        minValue: 0,
                        disabled: true,
                        listeners: {
                            change: "validateAccountForm",
                        }
                    }, {
                        xtype: 'tbspacer',
                        width: 15
                    }, {
                        xtype: 'displayfield',
                        value: '(No Licenses Available)',
                        id: 'host_license_display',
                        itemId : 'host_license_display'
                    }]
                }, {
                    layout: 'hbox',
                    //fieldLabel: 'User License Limit',
                    id: 'user_license_field',
                    items: [{
                        xtype: 'checkboxfield',
                        fieldLabel: 'User License Limit',
                        id: 'unlimited_user_checkbox',
                        labelWidth: 150,
                        boxLabel: 'No Limit',
                        checked: true,
                        handler: 'noLimitUser'
                    }, {
                        xtype: 'tbspacer',
                        width: 10
                    }, {
                        xtype: 'numberfield',
                        allowBlank: false,
                        width: 100,
                        // id: 'recipient_email',
                        allowDecimals: false,
                        allowNegative: false,
                        id: 'user_limit',
                        value: 0,
                        disabled : true,
                        listeners: {
                            change: "validateAccountForm",
                        }
                    }, {
                        xtype: 'tbspacer',
                        width: 15
                    }, {
                        xtype: 'displayfield',
                        value: '(No Licenses Available)',
                        id: 'user_license_display',
                        itemId: 'user_license_display'
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    fieldLabel: 'Assign Host Licenses',
                    labelWidth: 150,
                    layout: 'hbox',
                    id: 'host_assign',
                    items: [
                        {
                            xtype: 'numberfield',
                            allowBlank: false,
                            width: 120,
                            allowDecimals: false,
                            allowNegative: false,
                            hideLabel: true,
                            id: 'host_assign_number',
                            value: 0,
                            minValue: 0,
                            listeners: {
                                change: "validateAccountForm",
                            }
                        }, {
                            xtype: 'tbspacer',
                            width: 15
                        }, {
                            xtype: 'displayfield',
                            value: '(No Licenses Available)',
                            id: 'host_license_assign',
                            itemId: 'host_license_assign'
                        }
                    ]
                }, {
                    xtype: 'fieldcontainer',
                    fieldLabel: 'Assign User Licenses',
                    labelWidth: 150,
                    layout: 'hbox',
                    hidden: true,
                    id: 'user_assign',
                    items: [
                        {
                            xtype: 'numberfield',
                            allowBlank: false,
                            id: 'user_assign_number',
                            width: 120,
                            allowDecimals: false,
                            allowNegative: false,
                            hideLabel: true,
                            value: 1,
                            minValue: 0,
                            listeners: {
                                change: "validateAccountForm",
                            }
                        }, {
                            xtype: 'tbspacer',
                            width: 15
                        }, {
                            xtype: 'displayfield',
                            value: '(No Licenses Available)',
                            id: 'user_license_assign',
                            itemId : 'user_license_assign'
                        }
                    ]
                }]

            }]
        }]
    }, {
        xtype: 'fieldset',
        collapsible: 'true',
        layout: 'auto',
        height: 'auto',
        title: 'Recipients Details',
        id : 'recipients_details',
        items: [{
            layout: 'form',
            padding: '15 10 0 10',
            items: [{
                xtype: 'box',
                padding: '0 0 10 0',
                html: 'Enter an email address ' + (sfw.util.Auth.LoginDetails.isSMSEnabled ? 'and (optionally) a mobile number' : '') + 'to be used for emails, notifications and alerts the Flexera Software Vulnerability Manager can be configured to send.'
            }, {
                layout: 'hbox',
                items: [{
                    xtype: 'textfield',
                    allowBlank: false,
                    width: 600,
                    labelWidth: 150,
                    labelAlign: 'right',
                    id: 'recipient_email',
                    fieldLabel: 'Email',
                    vtype: 'email',
                    padding: '0 0 5 5',
                    blankText: 'Recipient Email is required',
                    emptyText: 'Please enter a recipient email address',
                    listeners: {
                        change: "validateAccountForm",
                    }
                }, {
                    xtype: 'tbspacer',
                    width: 5
                }, {
                    xtype: 'button',
                    text: 'Use Above Email',
                    id: 'use_above_email_button',
                    ui: 'primary',
                    allowBlank: false,
                    handler: function () {
                        var email = Ext.getCmp('account_email').getValue();
                        Ext.getCmp('recipient_email').setValue(email);
                    }
                }]
            }, {
                xtype: 'container',
                layout: 'hbox',
                padding: '0 0 0 5',
                itemId : 'mobile_recipients',
                hidden : true,
                items: [{
                    xtype: "combo",
                    fieldLabel: 'Mobile Number',
                    store: sfw.Default.countryCode,
                    valueField: 'countryCode',
                    displayField: 'countryName',
                    mode: 'local',
                    labelWidth: 150,
                    width: 250,
                    labelAlign: 'right',
                    id: 'recipient_country_code',
                    allowBlank: false,
                    editable: false,
                    forceSelection : true,
                    triggerAction: 'all',
                    emptyText: '...',
                    tpl: Ext.create('Ext.XTemplate',
                        '<ul class="x-list-plain"><tpl for=".">',
                        '<li role="option" class="x-boundlist-item">{countryName} <span style="float:right;color:#878787;">(+{countryCode})</span></li>',
                        '</tpl></ul>'
                    ),
                    displayTpl: Ext.create('Ext.XTemplate',
                        '<tpl for=".">{countryCode}</tpl>'
                    ),
                    listConfig: {
                        width: 300,
                        minWidth: 300
                    },
                    listeners: {
                        change: "validateAccountForm",
                    }
                }, {
                    xtype: 'tbspacer',
                    width: 5
                }, {
                    xtype: 'numberfield',
                    width: 345,
                    hideTrigger: true,
                    keyNavEnabled: false,
                    mouseWheelEnabled: false,
                    id: 'recipient_mobile_number',
                    listeners: {
                        change: "validateAccountForm",
                    }
                }]
            }]
        }]
    }, {
        xtype: 'fieldset',
        collapsible: 'true',
        layout: 'auto',
        height: 'auto',
        title: 'User Roles & Permissions',
        id: 'roles_permissions',
        items: [{
            layout: 'hbox',
            padding: '15 10 0 10',
            items: [{
                // layout: 'form',
                flex: 1,
                width: 800,
                //,items: self.notificationNameTextfield
                items: [{
                    xtype: 'box',
                    padding: '0 0 10 0',
                    html: 'Configure the specific roles and permissions for this user. Note that Read-Only and Restricted users have restrictions on what roles they may be granted, and even within certain roles, there are actions and views they will not have access to. Restricted users are limited to logging in, changing their password and only viewing their Smart Groups and Reports.'
                }, {
                    xtype: 'radiogroup',
                    id: 'addedto',
                    fieldLabel: 'Write Permissions',
                    labelWidth: 150,
                    layout: 'table',
                    name: 'added',
                    items: [{
                        boxLabel: 'Read/Write',
                        id: 'read_write',
                        inputValue: '1',
                        padding: '0 10 0 0',
                        checked: true,
                        width: 100,
                        handler: 'readWriteSelection'
                    }, {
                        boxLabel: 'Read Only',
                        id: 'read_only',
                        inputValue: '2',
                        width: 100,
                        padding: '0 10 0 0',
                        handler: 'readOnlySelection'
                    }, {
                        boxLabel: 'Restricted',
                        id: 'restricted',
                        inputValue: '3',
                        handler: 'restrictedSelection'
                    }]

                }, {
                    xtype: 'fieldset',
                    //collapsible: 'true',
                    // layout: 'auto',
                    height: 'auto',
                    width: 800,
                    title: 'Roles & Sub-Roles',
                    items: [{
                        xtype: 'checkboxfield',
                        id: 'scanning',
                        boxLabel: 'Scanning',
                        //name: 'notify',
                        //checked: false,
                        handler: 'checkSubRoleScanning'

                    }, {
                        xtype: 'checkboxfield',
                        id: 'filter_scan_result',
                        boxLabel: 'Filter Scan Results',
                        style: {
                            marginLeft: '20px',
                        },
                        name: 'notify',
                        checked: false,
                        disabled: true

                    }, {
                        xtype: 'checkboxfield',
                        id: 'reporting',
                        boxLabel: 'Reporting',
                        name: 'notify',
                        checked: false,
                        handler: 'checkSubRoleReporting'

                    }, {
                        xtype: 'checkboxfield',
                        id: 'database_access',
                        boxLabel: 'Database Console',
                        style: {
                            marginLeft: '20px',
                        },
                        name: 'notify',
                        checked: false,
                        handler: 'dbAccessWarning',
                        disabled: 'true'

                    }, {
                        xtype: 'checkboxfield',
                        id: 'patching',
                        boxLabel: 'Patching',
                        name: 'notify',
                        checked: false,

                    }, {
                        xtype: 'checkboxfield',
                        id: 'results',
                        boxLabel: 'Results',
                        name: 'notify',
                        checked: false,

                    }]
                }]

            }]
        }]
    }, {
        xtype: 'fieldset',
        collapsible: 'true',
        // layout: 'auto',
        height: 'auto',
        id: 'restrict_network',
        title: 'Restrict User\'s Network Access',
        items: [{
            layout: 'vbox',
            padding: '15 10 0 10',
            items: [{
                //  layout: 'form',
                flex: 1,
                width: 820,
                items: [{
                    xtype: 'box',
                    padding: '0 0 10 0',
                    html: 'You can limit this user\'s access and view of the network by restricting their scope using the following filters:'
                }, {
                    xtype: 'fieldset',
                    collapsible: 'true',
                    collapsed : 'true',
                    layout: 'vbox',
                    height: 'auto',
                    title: 'Restrict On AD',
                    padding: 10,
                    itemId : 'ad_restrict',
                    hidden: true,
                    items: [{
                        xtype: 'box',
                        html: 'Using the Active Directory, restrict a user to a specific branch and its descendents.'
                    }, {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        padding: '10px 0 5px 0px',
                        labelWidth: 150,
                        labelAlign: 'right',
                        fieldLabel: 'Impose AD Restriction',
                        items: [
                            {
                                xtype: 'checkboxfield',
                                itemId: 'ad_restriction',
                                name: 'notify',
                                widht: 100,
                                checked: false,
                                hideLabel: true,
                                handler: 'enableAdButton'
                            }, {
                                xtype: 'tbspacer',
                                width: 10
                            }, {
                                xtype: 'button',
                                text: 'Browse AD',
                                itemId: 'browse_ad_button',
                                allowBlank: false,
                                disabled: true,
                                handler: 'openADDialog'
                            }
                        ]
                    }, {
                        xtype: 'fieldcontainer',
                        layout: 'hbox',
                        padding: '5px 0 10px 0px',
                        labelWidth: 150,
                        labelAlign: 'right',
                        fieldLabel: 'Selected',
                        items: [
                            {
                                xtype: 'displayfield',
                                itemId: 'ad_selected_node',
                                value: 'No AD Branch Selected'
                            }
                        ]
                    }]
                }, {
                    xtype: 'fieldset',
                    collapsible: 'true',
                    collapsed : 'true',
                    // layout: 'auto',
                    height: 'auto',
                    padding: 10,
                    title: 'Restrict To This Account',
                    items: [{
                        xtype: 'box',
                        padding: '0 0 10 0',
                        html: 'If selected, the user will only see hosts they themselves have scanned. Note: User must have the \'Scanning\' Role configured for this to make sense.'
                    },{
                        xtype: 'checkboxfield',
                        id: 'account_restriction',
                        boxLabel: 'Impose Account Restriction',
                        name: 'notify',
                        checked: false,
                    }]
                }, {
                    xtype: 'fieldset',
                    collapsible: 'true',
                    padding: 10,
                    collapsed : 'true',
                    // layout: 'auto',
                    height: 'auto',
                    title: 'Restrict To Individual Host',
                    items: [{
                        html: 'Feature unavailable. You must use the Software Vulnerability Manager in Internet Explorer with the Software Vulnerability Manager IE Plugin installed to enable this feature.',
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    },{
                        html:sfw.CommonConstants.ACTIVEX_MESSAGE,
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    }]
                }, {
                    xtype: 'fieldset',
                    collapsible: 'true',
                    padding: 10,
                    collapsed : 'true',
                    // layout: 'auto',
                    height: 'auto',
                    title: 'Restrict On IP Range',
                    items: [{
                        html: 'Feature unavailable. You must use the Software Vulnerability Manager in Internet Explorer with the Software Vulnerability Manager IE Plugin installed to enable this feature.',
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    },{
                        html:sfw.CommonConstants.ACTIVEX_MESSAGE,
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    }]
                }, {
                    xtype: 'fieldset',
                    collapsible: 'true',
                    padding: 10,
                    collapsed : 'true',
                    // layout: 'auto',
                    height: 'auto',
                    title: 'Restrict On IP Network',
                    items: [{
                        html: 'Feature unavailable. You must use the Software Vulnerability Manager in Internet Explorer with the Software Vulnerability Manager IE Plugin installed to enable this feature.',
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION == sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    },{
                        html:sfw.CommonConstants.ACTIVEX_MESSAGE,
                        listeners:{
                            beforerender:function(){
                                if(sfw.SharedConstants.EDITION != sfw.SharedConstants.HOSTED_EDITION){
                                    this.hide();
                                }
                            }
                        }
                    }]

                }]
            }]
        }]
    }, {
        xtype: 'fieldset',
        collapsible: 'true',
        layout: 'fit',
        height: 400,
        title: 'Share Smartgroups',
        id: 'shared_sgs',
        items: [{
            padding: '15 10 0 10',
            items: [{
                xtype: 'box',
                padding: '0 0 10 0',
                html: 'Choose which Smart Groups you would like to copy to this User'

            },{
                xtype: 'grid',
                flex: 0.5,
                autoscroll : true,
                height : 300,
                title: 'Smartgroups to copy to this user',
                ui: 'gray',
                viewConfig : {
                    deferEmptyText: false,
                    emptyText: 'No existing Smart Groups can be shared.'
                },
                id: 'share_sg_chkbx_col',
                itemId : 'share_sg_chkbx_col',
                store: {type: 'sharesmartgroups'},
                columnLines: true,
                selType: 'checkboxmodel',
                autoScroll: true,
                columns: [{
                    text: "Name",
                    dataIndex: 'name',
                    flex: 1
                }, {
                    text: "Description",
                    dataIndex: 'description',
                    width: 100,
                }, {
                    text: "Status",
                    width: 100,
                    dataIndex: 'status'
                }]
            }]
        }]
    }],

    buttons: [
        {
            text: 'Save',
            id: 'saveUserButton',
            formBind: true,
            //ui: 'primary',
            handler: "saveAdmin"
        }, {
            text: 'Close',
            tabIndex: 8,
            handler: function () {
                this.up('window').destroy();
            }
        }
    ]
});
