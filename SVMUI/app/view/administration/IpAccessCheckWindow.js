
Ext.define('sfw.view.administration.IpAccessCheckWindow',{
    extend:'Ext.window.Window',
    xtype:'sfw.checkipwindow',
    controller:'administration-ipaccessmanagement',
    title:'Check IP',
    width: 550,
    layout: 'fit',
    modal: true,

    items: [{
        xtype: 'form',
        bodyPadding: 10,
        items:[{
            xtype: 'box',
            padding: '0 0 10 0',
            html: '<p>Use this form to test if an IP has access to the Software Vulnerability Manager based on the current rules.<p>',
        },{
            xtype: 'textfield',
            fieldLabel: 'IP',
            vtype: 'IPAddress',
            labelWidth: 100,
            width: 400,
            maxWidth: 400,
            itemId: 'ipCheckField',
            allowBlank: false,
        },{
            xtype: 'combo',
            fieldLabel: 'User',
            labelWidth: 100,
            emptyText: 'Any User',
            itemId: 'checkIpCombo',
            width: 400,
            maxWidth: 400,
            store: {type: 'IpAccessCheckWindowCombo',
            },
            valueField: "recipient_account_id",
            displayField: "account_name",
            anchor: '-15',
            typeAhead: true,
            allowBlank: false,
        },{
            xtype: 'label',
            html: '',
            hidden: true,
            itemId: 'checkResult'
        }],
        buttons: [
            {
                text: 'Check IP',
                itemId: 'checkIpRuleButton',
                formBind: true,
                //ui: 'primary',
                handler: "checkIpAgainstRules"
            }, {
                text: 'Cancel',
                tabIndex: 8,
                handler: function () {
                    this.up('window').destroy();
                }
            }
        ]
    }],
});