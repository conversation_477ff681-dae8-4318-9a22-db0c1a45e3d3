Ext.define("sfw.view.administration.ActiveDirectory", {
    extend: "Ext.form.Panel",
    xtype: "sfw.csiActiveDirectory",
    ui: 'light',

    frame: false,
    bodyPadding: 10,
    scrollable: true,
    requires: [
        "sfw.view.administration.ActiveDirectoryController",
        "sfw.view.administration.ActiveDirectoryModel",
    ],

    controller: "administration-activedirectory",
    viewModel: {
        type: "administration-activedirectory",
    },
    title: 'Active Directory',
    ui: 'light',
    items: [
        {
            xtype: "panel",
            ui: 'light',
            title: "Active Directory",
            ui: 'light',
            frame: true,
            bodyPadding: 5,
            width: 1000,
            height: 800,
            labelAlign: 'right',
            id: "ad_fieldset",
            items: [
                {
                    xtype: "label",
                    html: "<p>This setting allows your group policies to be automatically updated in the Software Vulnerability Manager when changes are made to the Active Directory. Switching to Active Directory will remove your current Sites structure. Your existing sites will be backed up.<p>",
                },
                {
                    xtype: "checkbox",
                    itemId: "ad_integration",
                    boxLabel: "Enable Active Directory integration",
                    handler: function (checkbox, checked) {
                        if (sfw.ActiveDirectorySettings.isADSet() && ((sfw.ActiveDirectorySettings.hasADIntegration() && checked) || (!sfw.ActiveDirectorySettings.hasADIntegration() && !checked))) {
                            return;
                        }

                        Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#ad_integration')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(true);
                        Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled(true);

                        var callback = {
                            fn: sfw.ActiveDirectorySettings.setADIntegrationCallback
                            , context: this
                        };

                        sfw.ActiveDirectorySettings.setADIntegration(checked, callback);

                    },
                    listeners: {
                        beforerender: function () {
                            if (sfw.util.Auth.LoginDetails.isReadOnly) {
                                this.setDisabled(true);
                            }
                            this.setValue(sfw.ActiveDirectorySettings.hasADIntegration());
                        }
                    }

                },
                { xtype: 'box', autoEl : { tag : 'hr' } },
                {xtype: 'tbspacer', height: 10},
                {
                    xtype: "label",
                    html: "<p>To connect with LDAPS, enter the domain's fully qualified domain name (FQDN) with port number eg: DSCCM.SCCM.TEST:636.<p>",
                },
                {
                    xtype: "checkbox",
                    id: "ad_laps",
                    boxLabel: "Use LDAPS",
                    handler: function (checkbox, checked) {
                        if(!ldapTemp) {
                            sfw.ActiveDirectorySettings.setLDAPS(checked);
                        }
                        ldapTemp = false;
                        if (checked) {
                            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled(false);
                        } else {
                            Ext.ComponentQuery.query('#LDAPText')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#LDAPText')[0].setValue('');
                        }
                    },
                    listeners: {
                        beforerender: function () {
                            ldapTemp = sfw.ActiveDirectorySettings.isLDAPS();
                            this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || sfw.util.Auth.LoginDetails.isReadOnly);
                            this.setValue(sfw.ActiveDirectorySettings.isLDAPS());
                        }
                    }
                },
                {
                    xtype: "textfield",
                    width: 300,
                    itemId: 'LDAPText',
                    allowBlank: false,
                    listeners: {
                        beforerender: function () {
                            this.setValue(sfw.ActiveDirectorySettings.getLdapsURL()),
                            this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || !sfw.ActiveDirectorySettings.scanSpecificPath() || !sfw.ActiveDirectorySettings.isLDAPS() || sfw.util.Auth.LoginDetails.isReadOnly);
                        }
                    }
                },
                { xtype: 'box', autoEl : { tag : 'hr' } },
                {xtype: 'tbspacer', height: 10},
                {
                    xtype: "label",
                    html: "<p>Using the options below you can control which Active Directory paths are going to be scanned. The Active Directory scanner will try to fetch the widest structure possible starting from the provided root location. The scanner only analyses Domain Controllers and Organizational Units.</p>",
                },
                {
                    xtype: "fieldcontainer",
                    flex: 1,
                    defaultType: "radio",
                    border: false,
                    items: [
                        {
                            name: "ad_scan_options",
                            itemId: 'ad_scan_options',
                            boxLabel:
                                "All accessible branches - By looking at the Active Directory Partitions, the scanner determines the accessible Domain Controllers that it can scan",
                            handler: function (checkbox, checked) {
                                if (checked) {
                                    Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
                                    Ext.ComponentQuery.query('#adScanPath')[0].setValue('');
                                    Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(true);
                                    Ext.ComponentQuery.query('#netBiosCheck')[0].setValue('');
                                    Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
                                    Ext.ComponentQuery.query('#netBiosNameText')[0].setValue('');
                                }
                            },
                            listeners: {
                                beforerender: function () {    
                                    this.setDisabled(sfw.util.Auth.LoginDetails.isReadOnly || !sfw.ActiveDirectorySettings.hasADIntegration());
                                    this.setValue(sfw.ActiveDirectorySettings.scanAllBranches());
                                }
                            }
                        },
                        {
                            name: "ad_scan_options",
                            itemId: 'adScanSpecificPath',
                            boxLabel:
                                "Specific Domain Controller - You can specify a certain Domain Controller to be scanned. It must be accessible from the host running the Software Vulnerability Manager",
                            handler: function (checkbox, checked) {
                                if (checked) {
                                    Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#adScanPath')[0].setValue(sfw.ActiveDirectorySettings.getADPath().length > 0
                                        ? sfw.ActiveDirectorySettings.getADPath()
                                        : sfw.ActiveDirectorySettings.getRootDomain());
                                }
                            },
                            listeners: {
                                beforerender: function () {       
                                    this.setDisabled(sfw.util.Auth.LoginDetails.isReadOnly || !sfw.ActiveDirectorySettings.hasADIntegration());
                                    this.setValue(sfw.ActiveDirectorySettings.scanSpecificPath());
                                }
                            }
                        },
                        {
                            xtype: "fieldcontainer",
                            items: [
                                {
                                    xtype: "textfield",
                                    width:500,
                                    fieldLabel: "DC",
                                    itemId: 'adScanPath',
                                    vtype:'activeDirectoryContainer',
                                    listeners: {
                                        beforerender: function () {
                                            this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || sfw.util.Auth.LoginDetails.isReadOnly || !sfw.ActiveDirectorySettings.scanSpecificPath());
                                            this.setValue(sfw.ActiveDirectorySettings.hasADIntegration()
                                                ? (
                                                    sfw.ActiveDirectorySettings.scanSpecificPath()
                                                        ? (
                                                            sfw.ActiveDirectorySettings.getADPath().length > 0
                                                                ? sfw.ActiveDirectorySettings.getADPath()
                                                                : sfw.ActiveDirectorySettings.getRootDomain()
                                                        )
                                                        : ''
                                                )
                                                : '');
                                        },
                                        invalid:function(){
                                            Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(true);
                                        } ,
                                        valid:function(){
                                            if(sfw.ActiveDirectorySettings.hasADIntegration()){
                                                  Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(sfw.util.Auth.LoginDetails.isReadOnly);
                                            }
                                        }
                                    }
                                }, {
                                    xtype:'container',
                                    layout:{
                                         type:'hbox',
                                        aling:'stretch'
                                    },
                                    items:[{
                                           xtype: "checkbox",
                                           itemId: 'netBiosCheck',
                                           boxLabel: "Set nETBIOSName manually:&nbsp;&nbsp;",
                                           handler: function (checbox, checked) {
                                               if (checked) {
                                                   Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(false)
                                               } else {
                                                   Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
                                                   Ext.ComponentQuery.query('#netBiosNameText')[0].setValue('');
                                               }
                                           },
                                           listeners: {                                                                                                                                                                
                                               beforerender: function () {                                                                                                                                             
                                                   this.setValue(sfw.ActiveDirectorySettings.isManualNetbios()),                                                                                                       
                                                       this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || !sfw.ActiveDirectorySettings.scanSpecificPath() || sfw.util.Auth.LoginDetails.isReadOnly);  
                                               }                                                                                                                                                                       
                                           }
                                    },{
                                          xtype: 'textfield',
                                          itemId: 'netBiosNameText',
                                          allowBlank: false,
                                          listeners: {
                                              beforerender: function () {
                                                  this.setValue(sfw.ActiveDirectorySettings.getManualNetbios());
                                                  this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || !sfw.ActiveDirectorySettings.scanSpecificPath() || !sfw.ActiveDirectorySettings.isManualNetbios() || sfw.util.Auth.LoginDetails.isReadOnly)
                                              }
                                          }

                                    }]

                                },
                                {
                                    xtype: "label",
                                    html: '<p>If you don\'t select the "Set nETBIOSName manually" checkbox, the nETBIOSName will be automatically detected from AD.<p>',
                                },
                                { xtype: 'box', autoEl : { tag : 'hr' } },
                            ],
                        },
                    ],
                },
                {
                    xtype: "label",
                    html: "<p>The view options help you control how the elements of the Active Directory are being displayed in the Software Vulnerability Manager.<p>",
                },
                {
                    xtype: "checkbox",
                    itemId: "ad_hide_empty_ous",
                    boxLabel: "Hide empty Organizational Units in the tree views",
                    /**
                     * This needs to be controlled from the database setting
                     * @type {Boolean}
                     */
                    checked: false,
                    listeners: {
                        beforerender: function () {
                            this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || sfw.util.Auth.LoginDetails.isReadOnly)
                            this.setValue(sfw.ActiveDirectorySettings.isHideEmptyOusEnabled());
                        }
                    }
                },
                {
                    xtype: "checkbox",
                    itemId: "ad_site_show_distinguishedname",
                    boxLabel:
                        "Show Distinguished Names for sites instead of single Organizational Units",
                    /**
                     * This needs to be controlled from the database setting
                     * @type {Boolean}
                     */
                    checked: false,
                    listeners: {
                        beforerender: function () {
                            this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration());
                            this.setValue( parseInt( sfw.util.Auth.LoginDetails.account.configuration.get( 'AD_SITE_SHOW_DISTINGUISHED_NAME' ), 10 ) );

                        }
                    }
                },
                { xtype: 'box', autoEl : { tag : 'hr' } },
                {xtype: 'tbspacer', height: 10},
                {
                    xtype: "label",
                    html: "<p>Using the schedule options below you can set Active Directory scans at regular intervals.<p>",
                },
                {
                    xtype: "panel",
                    layout: "table",
                    cls: "ContentPadding",
                    layoutConfig: {
                        columns: 8,
                    },
                    items: [
                        {
                            xtype: "combobox",
                            itemId: "ad_frequency_unit",
                            fieldLabel: "Frequency",
                            width: 223,
                            layout: "vbox",
                            emptyText: "Choose how often the scan will run.",
                            store: [
                                {id: -1, text: "-"},
                                {id: 1, text: "Hourly"},
                                {id: 2, text: "Daily"},
                                {id: 3, text: "Weekly"},
                                {id: 4, text: "Monthly"},
                            ],
                            selectOnFocus: false,
                            mode: "local",
                            editable: false,
                            allowBlank: false,
                            forceSelection: true,
                            valueField: "id",
                            displayField: "text",
                            triggerAction: "all",
                            listeners: {
                                beforerender: function () {
                                    this.setValue(sfw.ActiveDirectorySettings.getADScheduleInterval());
                                    this.setDisabled(!sfw.ActiveDirectorySettings.hasADIntegration() || sfw.util.Auth.LoginDetails.isReadOnly)
                                }
                            },
                            validator: function () {
                                if (
                                    this.getValue() == -1
                                ) {
                                    return false;
                                }
                                return true;
                            },
                        }
                    ],
                },
            ],
            buttons: [
                {
                    text: "Save Active Directory Settings",
                    handler: function () {
                        if (sfw.ActiveDirectorySettings.hasADIntegration()) {
                            if (
                                !Ext.ComponentQuery.query('#ad_frequency_unit')[0].validate()
                                || !Ext.ComponentQuery.query('#netBiosNameText')[0].validate()
                                || !Ext.ComponentQuery.query('#LDAPText')[0].validate()
                            ) {
                                return;
                            }

                            if (sfw.ActiveDirectorySettings.getContainerName(Ext.ComponentQuery.query('#adScanPath')[0].getValue()) === "OU" && !Ext.ComponentQuery.query('#netBiosCheck')[0].getValue()) {
                                Ext.Msg.alert('Active Directory settings', "When scanning an OU you must set the nETBIOSName manually.");
                                return;
                            }

                            var options = {
                                hideEmptyOus: Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].getValue()
                                ,
                                siteShowDistinguishedName: Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].getValue() //
                                ,
                                adScanPath:
                                    Ext.ComponentQuery.query('#ad_scan_options')[0].getValue()
                                        ? ''
                                        : Ext.ComponentQuery.query('#adScanPath')[0].getValue()
                                ,
                                adScanInterval: Ext.ComponentQuery.query('#ad_frequency_unit')[0].getValue()
                                ,
                                adIsManualNetbios: Ext.ComponentQuery.query('#netBiosCheck')[0].getValue()
                                ,
                                adManualNetbios: Ext.ComponentQuery.query('#netBiosNameText')[0].getValue()
                                ,
                                adLdapsURL: Ext.ComponentQuery.query('#LDAPText')[0].getValue()
                            };

                            Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#ad_integration')[0].setDisabled(true);
                            //Ext.ComponentQuery.query('#ad_frequency_unit')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(true);
                            Ext.ComponentQuery.query('#ad_laps')[0].setDisabled(true);

                            var callback = {
                                fn: function () {
                                    Ext.ComponentQuery.query('#adSaveButton')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#adScanSpecificPath')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#ad_scan_options')[0].setDisabled(false);
                                    if (sfw.ActiveDirectorySettings.scanSpecificPath()) {
                                        Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(false);
                                        Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(false);
                                        if (sfw.ActiveDirectorySettings.isManualNetbios()) {
                                            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(false);
                                        } else {
                                            Ext.ComponentQuery.query('#netBiosNameText')[0].setDisabled(true);
                                        }
                                    } else {
                                        Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
                                        Ext.ComponentQuery.query('#adScanPath')[0].setDisabled(true);
                                        Ext.ComponentQuery.query('#netBiosCheck')[0].setDisabled(true);
                                    }
                                    Ext.ComponentQuery.query('#ad_hide_empty_ous')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#ad_site_show_distinguishedname')[0].setDisabled(false);
                                    Ext.ComponentQuery.query('#ad_integration')[0].setDisabled(false);

                                    Ext.ComponentQuery.query('#ad_laps')[0].setDisabled(false);
                                    if (sfw.ActiveDirectorySettings.isLDAPS()) {
                                        Ext.ComponentQuery.query('#LDAPText')[0].setDisabled(false);
                                        Ext.ComponentQuery.query('#LDAPText')[0].setValue(sfw.ActiveDirectorySettings.getLdapsURL());
                                    } else {
                                        Ext.ComponentQuery.query('#LDAPText')[0].setDisabled(true);
                                        Ext.ComponentQuery.query('#LDAPText')[0].setValue('');
                                    }

                                },
                                context: this
                            };

                            sfw.ActiveDirectorySettings.saveSettings(options, callback);

                        }
                    },
                    ui: 'primary',
                    itemId: 'adSaveButton'
                },
            ],
        },
    ],
});


Ext.onReady(function(){
    Ext.apply(Ext.form.VTypes, {
        activeDirectoryContainer: function(val,field) {  
                if ( typeof sfw.ActiveDirectorySettings === 'undefined' || sfw.ActiveDirectorySettings === null ) {
                return false;
            }
            var res = '';
            try {
                res = sfw.ActiveDirectorySettings.getContainerName( val );
            } catch ( ex ) {
                sfw.debug.trigger( ex, 'sfw.csiSettings::context', 'Could not parse container name.' );
            }
            if ( res.length > 0 ) {
                return true;
            }
            return false;
        },
        activeDirectoryContainerText: 'Please provide an Active Directory distinguished name'
    });
});