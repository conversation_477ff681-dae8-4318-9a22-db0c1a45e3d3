Ext.define('sfw.overriders.panel.Panel', {
   override: 'Ext.panel.Panel',

   config: {
       stateful: true
   },

    initComponent: function () {
       const me = this;
        if (me.xtype == 'flexera-abstractgrid' && ("undefined" != typeof me.smartgroupStateId)) {
            me.stateId = 'sfw-state-' + me.smartgroupStateId;
        } else {
            me.stateId = 'sfw-state-' + me.xtype;
        }
       me.callParent();
    }
});