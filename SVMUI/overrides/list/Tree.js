Ext.define('Flexera.overrides.list.Tree', {
    override: 'Ext.list.Tree',


    dismissOnMouseOut: true,

    //https://fiddle.sencha.com/#view/editor&fiddle/38st
    constructor: function(config) {
        if (config.plugins) {
            config.plugins.forEach(function(plugin) {
                if (plugin.ptype) {
                    plugin.type = plugin.ptype;
                }
            })
        }
        this.callParent([config])
    },

    privates: {
        onToolStripMouseOver: function (e) {
            var me = this;
            var item = e.getTarget('[data-recordId]'),
                id;

            if (item) {
                id = item.getAttribute('data-recordId');
                item = this.itemMap[id];

                if (item) {
                    this.floatItem(item, me.dismissOnMouseOut);
                }
            }
        }
    }
});