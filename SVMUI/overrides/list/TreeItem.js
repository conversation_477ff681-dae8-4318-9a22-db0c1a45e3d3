Ext.define('Flexera.overrides.list.TreeItem', {
    extend: 'Ext.list.TreeItem',
    xtype: 'customtreelistitem',
    config: {
        floaterConfig: {}
    },
    privates: {
        createFloater: function () {
            var me = this,
                owner = me.getOwner(),
                ownerTree = me.up('treelist'),
                ui = owner.getUi(),
                cls = Ext.baseCSSPrefix + 'treelist',
                floater,
                toolElement = me.getToolElement(),
                expandedWidth = ownerTree.expandedWidth,
                defaultListWidth = ownerTree.defaultListWidth;

            // if (expandedWidth === null) {
            //     expandedWidth = defaultListWidth;
            // }

            if (ui) {
                cls += ' ' + cls + '-' + ui;
            }

            me.floater = floater = new Ext.container.Container(Ext.applyIf({
                // cls: ownerTree.self.prototype.element.cls + ' ' +
                //     ownerTree.uiPrefix + ownerTree.getUi() + ' ' +
                //     Ext.baseCSSPrefix + 'treelist-floater',
                cls: cls + ' ' + Ext.baseCSSPrefix + 'treelist-floater',
                floating: true,
                //width: Ext.isIE8 ? defaultListWidth : (expandedWidth - toolElement.getWidth()),
                // maxHeight: 200,
                scrollable: true,
                shadow: false,
                hidden: true,
                renderTo: Ext.getBody(),
                listeners: {
                    element: 'el',
                    click: function (e) {
                        return owner.onClick(e);
                    }
                }
            }, me.getFloaterConfig()));

            floater.add(me);
            floater.show();
            floater.el.alignTo(toolElement, 'tr?');

            return floater;
        }
    }
})