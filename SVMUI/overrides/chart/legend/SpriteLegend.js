/**
 * Created by <PERSON><PERSON><PERSON> on 7/12/2018.
 * Ext 6.6.0 break application when uses legend with type sprite, BUG, we do not have methods isXtype and getItemId
 */
Ext.define('Flexera.overrides.chart.legend.SpriteLegend', {
    override: 'Ext.chart.legend.SpriteLegend',

    isXType: function (xtype) {
        return xtype === 'sprite';
    },

    getItemId: function () {
        return this.getId();
    }
});