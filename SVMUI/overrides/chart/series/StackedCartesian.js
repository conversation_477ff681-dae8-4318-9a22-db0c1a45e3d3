Ext.define('Flexera.overrides.chart.series.StackedCartesian', {
    override: 'Ext.chart.series.StackedCartesian',

    provideLegendInfo: function (target) {
        var me = this,
            sprites = me.getSprites(),
            title = me.getTitle(),
            field = me.getYField(),
            hidden = me.getHidden(),
            single = sprites.length === 1,
            style, fill,
            i, name;

        for (i = 0; i < sprites.length; i++) {
            style = me.getStyleByIndex(i);
            fill = style.fillStyle;

            if (title) {
                if (Ext.isArray(title)) {
                    name = title[i];
                } else if (single) {
                    name = title;
                }
            }

            if (!title || !name) {
                if (Ext.isArray(field)) {
                    name = field[i];
                } else {
                    name = me.getId();
                }
            }
            if (Ext.isArray(me.hideInLegendFields) && Ext.Array.contains(me.hideInLegendFields, field[i])) {
                return;
            }

            target.push({
                name: name,
                mark: (Ext.isObject(fill) ? fill.stops && fill.stops[0].color : fill) ||
                    style.strokeStyle || 'black',
                disabled: hidden[i],
                series: me.getId(),
                index: i
            });
        }
    }
});