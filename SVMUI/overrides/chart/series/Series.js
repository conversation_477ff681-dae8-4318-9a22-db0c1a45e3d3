Ext.define('Flexera.overrides.chart.series.Series', {
    override: 'Ext.chart.series.Series',
    /**
     * Provide legend information to target array.
     *
     * @param {Array} target
     *
     * The information consists:
     * @param {String} target.name
     * @param {String} target.mark
     * @param {Boolean} target.disabled
     * @param {String} target.series
     * @param {Number} target.index
     */
    // provideLegendInfo: function (target) {
    //     var me = this,
    //         style = me.getSubStyleWithTheme(),
    //         fill = style.fillStyle;
    //
    //     if (Ext.isArray(fill)) {
    //         fill = fill[0];
    //     }
    //
    //     target.push({
    //         name: me.getTitle() || me.getYField() || me.getId(),
    //         mark: (Ext.isObject(fill)
    //             ? fill.stops && fill.stops[0].color
    //             : fill) || style.strokeStyle || 'black',
    //         disabled: me.getHidden(),
    //         series: me.getId(),
    //         index: 0
    //     });
    // }
});