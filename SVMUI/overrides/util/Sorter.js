Ext.define('Flexera.overrides.util.Sorter', {
    override: 'Ext.util.Sorter',
    /**
     * Returns this sorter's serialized state. This is used when transmitting this sorter
     * to a server.
     * @return {Object}
     */
    serialize: function() {
        return {
            //We are changing the property param name to field, to support server API - by <PERSON><PERSON><PERSON>
            field: this.getProperty(),
            direction: this.getDirection()
        };
    }
});

