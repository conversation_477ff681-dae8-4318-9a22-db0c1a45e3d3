Ext.define('Flexera.overrides.window.Window', {
    override: 'Ext.window.Window',
    //closeAction: 'hide',
    //animateCls: 'fadeInDownBig',
    shadow: 'frame',
    animateCls: false,
    constrainHeader: true,
    config: {
        /**
         * @cfg {Ext.Component} maskCmp Ext.ComponentQuery selecto of
         * the component to mask on show - typically it is set to '[isMainPanel]'.
         * It is also used as alignTarget if set. Having the maskCmp
         * allows to have this window added to items of a lower level
         * component and still mask the main panel
         */
        maskCmp: null
    },
    initComponent: function () {
        var me = this
            , maskCmp = me.getMaskCmp()
        ;

        // get the component to mask if necessary
        maskCmp = maskCmp && me.up(maskCmp);

        // set align target and install listeners
        // if we got a valid containing component
        if (maskCmp && maskCmp.isComponent) {
            me.alignTarget = maskCmp;
            me.on({
                show: function () {
                    maskCmp.mask();
                }
                , hide: function () {
                    maskCmp.unmask();
                }
                , close: function () {
                    maskCmp.unmask();
                }
            });
        }
        me.callParent(arguments);
        if (me.animateCls) {
            me.addCls(['animated', me.animateCls])
        }

    }
});