//https://fiddle.sencha.com/#fiddle/3eht&view/editor
Ext.define('EXTJS_27534.list.Tree', {
    override: 'Ext.list.Tree',

    privates: {
        onNodeExpand: function (node) {
            var me = this,
                item = me.itemMap[node.internalId],
                childNodes, len, i, parentNode, child;

            if (item) {
                if (!item.isRootItem && me.getSingleExpand()) {
                    me.collapsingForExpand = true;
                    parentNode = (item.getParentItem() || me.rootItem).getNode();
                    childNodes = parentNode.childNodes;
                    for (i = 0, len = childNodes.length; i < len; ++i) {
                        child = childNodes[i];
                        if (child !== node) {
                            //collapsing the expanded node makes item.getOwner and item.parent null
                            child.collapse();
                        }
                    }
                    me.collapsing = false;
                }
                item = me.itemMap[node.internalId]; //workaround reset item
                item.nodeExpand(node);
            }
        }
    }
});