//https://fiddle.sencha.com/#fiddle/3eht&view/editor

Ext.define('EXTJS_27534.list.TreeItem', {
    override: 'Ext.list.TreeItem',

    nodeExpandBegin: function(animation) {
        var me = this,
            itemContainer = me.itemContainer,
            height;

        if (me.collapsing) {
            me.stopAnimation(me.collapsing);
        }
        me.callSuper([
            animation
        ]);

        if (animation && !itemContainer.isDestroyed) {
            // The expanded state is in effect, so itemContainer is visible again.
            height = itemContainer.getHeight();
            itemContainer.setHeight(0);
            me.expanding = me.runAnimation(Ext.merge({
                to: {
                    height: height
                },
                callback: me.nodeExpandDone,
                scope: me
            }, animation));
        }
    }

})