//Override - Bug, dashboard panel does not suport use listeners in viewTemplate
Ext.define('EXTJS_26431_dashboard_Dashboard', {
    override: 'Ext.dashboard.Dashboard',
    createView: function(config) {
        var me = this,
            type = config.type,
            part = me.getPart(type),
            view = part.createView(config);

        if (!view.id) {
            view.id = me.id + '_' + type + (me.idSeed++);
        }

        view.bubbleEvents = Ext.Array.from(view.bubbleEvents).concat(['expand', 'collapse']);
        view.stateful = me.stateful;

        // FIX HERE
        Ext.apply(view.listeners, config.listeners, {
            removed: this.onItemRemoved,
            scope: this
        });
        // END FIX

        return view;
    }
});