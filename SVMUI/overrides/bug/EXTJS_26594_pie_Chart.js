//Undo EXTJS-26594 -Pie chart renders incorrectly if there is a zero data point
//Restore 7.3.0 code.
//https://fiddle.sencha.com/#fiddle/3bug&view/editor

Ext.define(null, {
    override: 'Ext.chart.series.Pie',
    compatibility: ['7.4.x', '7.5.x'],
    getSprites: function () {
        var me = this,
            chart = me.getChart(),
            xField = me.getXField(),
            yField = me.getYField(),
            store = me.getStore();

        if (!chart || !store) {
            return Ext.emptyArray;
        }

        me.getColors();
        me.getSubStyle();

        // eslint-disable-next-line vars-on-top, one-var
        var items = store.getData().items,
            length = items.length,
            animation = me.getAnimation() || (chart && chart.getAnimation()),
            sprites = me.sprites,
            sprite,
            spriteCreated = false,
            spriteIndex = 0,
            label = me.getLabel(),
            labelTpl = label && label.getTemplate(),
            i,
            rendererData,
            createSprite = false;

        rendererData = {
            store: store,
            field: xField, // for backward compatibility only (deprecated in 5.5)
            angleField: xField,
            radiusField: yField,
            series: me
        };

        for (i = 0; i < length; i++) {
            if (items[i].get(xField)) createSprite = true;
        }

        for (i = 0; i < length; i++) {
            sprite = sprites[i];

            if (!sprite && createSprite) {
                sprite = me.createSprite();

                if (me.getHighlight()) {
                    sprite.config.highlight = me.getHighlight();
                    sprite.addModifier('highlight', true);
                }

                if (labelTpl && labelTpl.getField()) {
                    labelTpl.setAttributes({
                        labelOverflowPadding: me.getLabelOverflowPadding()
                    });
                    labelTpl.getAnimation().setCustomDurations({callout: 200});
                }

                sprite.setAttributes(me.getStyleByIndex(i));
                sprite.setRendererData(rendererData);
                spriteCreated = true;
            }

            if (sprite) {
                sprite.setRendererIndex(spriteIndex++);
                sprite.setAnimation(animation);
            }
        }

        if (spriteCreated) {
            me.doUpdateStyles();
        }

        return me.sprites;
    }

});