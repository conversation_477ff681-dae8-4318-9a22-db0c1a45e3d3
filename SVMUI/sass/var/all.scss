$base-color: dynamic(#277BC7);

$panel-navigation-background-color: dynamic(#0a1024);
//$panel-navigation-item-line-height: dynamic(64px);
$panel-navigation-item-line-height: dynamic(44px);
$panel-navigation-item-text-color: dynamic(#f1f1f1);


//---- Login Button style
.btn-reg-login {
  background-image: linear-gradient(#fed969 0, #e7ad01 100%) !important;
  box-shadow: 0 2px 2px rgba(0, 0, 0, .2);
  .x-btn-text {
    border-radius: 6px;
    height: auto;
    font-family: Lato, Arial, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    height: 18px !important;
    padding: 2px;
  }
  .x-btn-inner {
    color: #000000;
  }
}

//--- SSO FormPanel bgColor
.sso-form-panel {
  .x-panel-body-default {
    background-color: #206BB6 !important;
  }
}

.plus-minus-btn {
  .x-btn-inner-default-small {
    padding: 0 !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }
}

//-- Smart Group grid selection row bgColor
.x-grid-item-selected {
  background-color: #89b9d5 !important;
}

.adtree-all-computers {
  background-image:url(../resources/images/tree/all-computers.png) !important;
}
.adtree-domain {
  background-image:url(../resources/images/tree/domain.png) !important;
}
.adtree-group {
  background-image:url(../resources/images/tree/group.png) !important;
}
.adtree-non-ad-groups {
  background-image:url(../resources/images/tree/non-ad-groups.png) !important;
}
//------ dashboard overview table
.dashboard_portlet {
  width: 100%;
  border-collapse: collapse;
}
.RightAlign {
  text-align: right;
}
.splitter-cls {
  z-index: 999;
  width: 5px;
  background-clip: content-box;
  border: 2px solid #cacaca;
}
.splitter-cls:hover {
  border: 3px #50a7d1;
}
//----

.x-treelist-nav .x-treelist-row {
  padding-left: 5px !important;
}
.x-treelist-row {
  .x-treelist-item-wrap {
    line-height: 30px !important;
    .x-treelist-item-icon {
      width: 20px !important;
    }
    .x-treelist-item-text {
      font-size: 14px !important;
      margin-left: 30px !important;
    }
  }
}
.x-treelist-item-floated {
  .x-treelist-item-text {
    margin-left: 20px !important;
  }
}
.x-treelist-container {
  .x-treelist-item-expandable {
    .x-treelist-container {
      padding-left: 20px !important;
      .x-treelist-item-selected > .x-treelist-row {
        background-color: #12283f !important;
      }
    }
    .x-treelist-item-wrap {
      height: 30px !important;
      line-height: 30px !important;
      margin-left: 0px !important;
      .x-treelist-item-text {
        font-size: 12px !important;
        line-height: 30px !important;
      }
      .x-treelist-item-expander {
        line-height: 30px !important;
      }
    }
  }
  .x-treelist-item-leaf {
    height: 30px !important;
    line-height: 30px !important;
    .x-treelist-item-wrap {
      height: 30px !important;
      margin-left: 0px !important;
      .x-treelist-item-text {
        height: 30px !important;
        font-size: 12px !important;
        line-height: 30px !important;
        margin-left: 30px !important;
      }
    }
  }
}