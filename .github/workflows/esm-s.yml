name: SVM application
on:
  push:
    branches:
      - prod
      - ci
      - qa
  workflow_dispatch:

env:
  AWS_REGION: eu-west-1
  AWS_DEFAULT_REGION: eu-west-1
  S3_LOC: s3://svm-build-artifacts-prod/
  TMP_DIR: /var/tmp
defaults:
  run:
    shell: bash

jobs:
  scale-app-runner:
    name: Scale up the runner
    # Use share workflow from flexera/workflows for scale-runner job
    uses: flexera/workflows/.github/workflows/scale-up-runner.yml@v2
    permissions:
      id-token: write
    # Required parameters for scale-runner job
    # Typically provide the output values from flexera/terraform-aws-github-runner module
    with:
      aws-region: eu-west-1
      role-to-assume: "arn:aws:iam::429434580563:role/esms-mgmt-gh-actions-autoscaling"
      auto-scaling-group-name: "esms-mgmt-github-runner-bptn0"
  prep:
    name: "Prepare"
    runs-on: [self-hosted, linux, 'infra_id:esms']
    outputs:
      branch: ${{ steps.branch.outputs.branch }}
      workdir: ${{ steps.pwd.outputs.pwd }}
    steps:
      - name: Branch
        id: branch
        run: echo "branch=${GITHUB_REF##*/}" >> $GITHUB_OUTPUT
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: esm-s
      - name: Working directory
        id: pwd
        run: echo "pwd=$TMP_DIR/$(date -u +%F_%H-%M-%S_%N)" >> $GITHUB_OUTPUT
      - name: Make working directory
        id: workdir
        run: mkdir -vp ${{ steps.pwd.outputs.pwd }}
      - name: Copy to working directory
        id: copy
        run: cp -Tar esm-s ${{ steps.pwd.outputs.pwd }}
      - name: Extra directories
        id: extra
        working-directory: ${{ steps.pwd.outputs.pwd }}
        run: cd ${{ steps.pwd.outputs.pwd }} && mkdir -v artifacts build
  mutilate:
    name: "Mangle configs"
    needs: prep
    runs-on: [self-hosted, linux, 'infra_id:esms']
    strategy:
      matrix:
        file:
          - "./csi/build.xml"
          - "./sccm_plugin/build.conf.php"
          - "./csi/build.properties"
    defaults:
      run:
        working-directory: ${{ needs.prep.outputs.workdir }}
    steps:
      - name: Shameful hack
        if: needs.prep.outputs.branch != 'prod'
        id: shame
        run: sed -i "s/https:\/\/csi7.secunia.com/https:\/\/csi7.${{ needs.prep.outputs.branch }}.secunia.com/g" ${{ matrix.file }}
  csi:
    name: "CSI app"
    needs: [prep, mutilate]
    runs-on: [self-hosted, linux, 'infra_id:esms']
    env:
      OPENSSL_CONF: /etc/ssl
    defaults:
      run:
        working-directory: ${{ needs.prep.outputs.workdir }}
    steps:
      - name: pwd
        run: pwd
      - name: openssl conf
        run: echo $OPENSSL_CONF
      - name: Build
        id: build
        run: cd csi && ./staging-build.sh $(pwd)/../build
      - name: Archive
        id: archive
        run: cd build && tar cvzf ../artifacts/csi.tar.gz .
  simple-apps:
    name: Simple apps
    runs-on: [self-hosted, linux, 'infra_id:esms']
    needs: prep
    strategy:
      matrix:
        app:
          - ca
          - crm
          - csi7dl
          - graph
    defaults:
      run:
        working-directory: ${{ needs.prep.outputs.workdir }}
    steps:
      - name: pwd
        run: pwd
      - name: Archive
        id: archive
        run: cd ${{ matrix.app }} && tar cvzf ../artifacts/${{ matrix.app }}.tar.gz .
  save-artifacts:
    name: Save artifacts
    runs-on: [self-hosted, linux, 'infra_id:esms']
    needs: [prep, csi, simple-apps]
    strategy:
      matrix:
        app:
          - csi
          - ca
          - crm
          - csi7dl
          - graph
    steps:
      - name: Save artifact
        id: save
        uses: actions/upload-artifact@v4.4.3
        with:
          path: ${{ needs.prep.outputs.workdir }}/artifacts/${{ matrix.app }}.tar.gz
          name: ${{ matrix.app }}
          retention-days: 10
  promote:
    name: Promote
    runs-on: [self-hosted, linux, 'infra_id:esms']
    environment: ${{ needs.prep.outputs.branch }}
    needs: [prep, save-artifacts]
    strategy:
      matrix:
        app:
          - csi
          - ca
          - crm
          - csi7dl
          - graph
    steps:
      - name: pwd
        run: pwd
      - name: Download saved artifact
        id: download
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.app }}
      - name: Hash
        id: hash
        run: sha1sum ${{ matrix.app }}.tar.gz
      - name: S3 copy
        if: needs.prep.outputs.branch == 'prod' || needs.prep.outputs.branch == 'qa' || needs.prep.outputs.branch == 'ci'
        id: s3
        run: aws s3 cp --no-progress ${{ matrix.app }}.tar.gz ${S3_LOC}${{ needs.prep.outputs.branch }}/
#  deploy:
#    name: Deploy with Terraform
#    runs-on: [self-hosted, linux, 'infra_id:esms']
#    needs: [prep, promote]
#    steps:
#      - name: Repository Dispatch
#        uses: peter-evans/repository-dispatch@v2
#        with:
#          token: ${{ secrets.GLOBAL_FLEXERA_CI_TOKEN }}
#          repository: flexera/hsk-terraform
#          event-type: hsk-terraform-${{ needs.prep.outputs.branch }}
#          client-payload: '{ "branch": "${{ needs.prep.outputs.branch }}" }'
  cleanup:
    name: Clean up working directory
    runs-on: [self-hosted, linux, 'infra_id:esms']
    needs: [prep, save-artifacts]
    if: always()
    steps:
      - name: cleanup
        id: rm
        run: rm -rf ${{ needs.prep.outputs.workdir }}
      - name: Confirm cleanup
        id: check
        run: if [ -d ${{ needs.prep.outputs.workdir }} ]; then echo "Workdir '${{ needs.prep.outputs.workdir }}' not cleaned up" ; exit 1 ; fi
