name: 'Build SVM RHEL8 PHP7 rpm'
on:
    workflow_dispatch:

env:
    AWS_DEFAULT_REGION: eu-west-1

jobs:
    ecr-setup:
        uses: ./.github/workflows/ecr-setup.yml

    main:
        name: "rhel8-php7"
        needs: [ecr-setup]
        runs-on: [self-hosted, linux, 'infra_id:vt']
        timeout-minutes: 2880
        
        steps:
        - name: Run
          run: |
                secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')
                docker run --rm \
                -v "$(pwd)/log:/job/log" \
                -e DB_USER_VT="$(echo $secret_json | jq -r .username)" \
                -e DB_PASS_VT="$(echo $secret_json | jq -r .password)" \
                -e DB_HOST_VT="$(echo $secret_json | jq -r .host)" \
                -v "$(pwd)/persist:/job/persist" \
                --log-driver none -a stderr -a stdout \
                ${{ needs.ecr-setup.outputs.ecr_endpoint }}/rhel8-php7 \
                > log/stdout 2> log/stderr