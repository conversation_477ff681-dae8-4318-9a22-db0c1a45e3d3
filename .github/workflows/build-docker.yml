name: Build Docker images
on:
  push:
    branches: 
      - ci
  workflow_dispatch:
env:
  AWS_DEFAULT_REGION: eu-west-1

jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  build:
    # Very short name because the GUI interface cuts off the names of the images
    name: Do
    runs-on: [self-hosted, linux, 'infra_id:esms']
    env:
      IMAGE_TAG: ${{ github.run_id }}
    needs:
      - ecr-setup
    strategy:
      fail-fast: false
      matrix:
        jobname:
          - rhel8-php7
          - rhel9-php8

    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Create ECR repository
        run: |
          aws ecr describe-repositories --repository-names ${{ matrix.jobname }} || \
            aws ecr create-repository --repository-name ${{ matrix.jobname }} \
            --image-scanning-configuration scanOnPush=true --encryption-configuration encryptionType=KMS
            
      - name: Build and push
        id: build
        working-directory: csi/build/build_rpm
        run: |
          ls -la 
          image="${{ needs.ecr-setup.outputs.ecr_endpoint }}/${{ matrix.jobname }}"
          docker build -t $image -f ${{ matrix.jobname }}/Dockerfile .
          docker tag $image $image:$IMAGE_TAG
          docker push $image
          docker push $image:$IMAGE_TAG
