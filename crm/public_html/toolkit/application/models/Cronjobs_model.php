<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Cronjobs_Model extends CI_Model {

	private $table;
	public $primary_key;
	public $data;

	var $column_order = array(null, 'role_name'); //set column field database for datatable orderable
	var $column_search = array('role_name'); //set column field database for datatable searchable 
	var $order = array('role_id' => 'asc'); // default order 
	var $limit = ''; // default order 
	var $select_column = array();

	
	function __construct() {
		parent::__construct();
		$this->table = substr(strtolower(get_class($this)), 0, -6);
		$this->primary_key = array();
		$this->date = array();
	}

	private function reset() {
		$this->primary_key = array();
		$this->data = array();
	}

	private function reset_pk() {
		$this->primary_key = array();
	}

	private function reset_data() {
		$this->data = array();
	}

	public function view_roles() {
		$this->caDB->where('status_ind','1');
		$query = $this->caDB->get($this->table);
		return $query->result();
	}

	public function insert() {
		$this->caDB->insert($this->table, $this->data);
		$this->reset_data();
		return true;
	}

	public function update() {
		$this->caDB->update($this->table, $this->data, $this->primary_key);
		$this->reset();
		return true;
	}

	public function delete() {
		$this->caDB->delete($this->table, $this->primary_key);
		$this->reset_pk();
		return true;
	}
	
	public function get_max_value($field) {
		$this->caDB->select_max($field);
		$query = $this->caDB->get($this->table);
		$row = $query->row();
		return $row->$field;
	}
	public function get_row() {
		$this->caDB->where($this->primary_key);
		$this->caDB->select('*');
		$this->caDB->from($this->table);
		$query = $this->caDB->get();
		$row = $query->row();
		return $row;
	}
	public function basicView() {
		$query = $this->caDB->get($this->table);
		return $query->result();
	}

	private function _get_threat_score()
	{
		
		$i = 0;
		$order_by = 'ORDER BY end_time desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									FROM csi_sync_status css
									INNER JOIN accounts ac USING (cst_id)
									WHERE job_type='TI_CRON' AND account_expires > UTC_TIMESTAMP() AND end_time >= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -24 HOUR) 
									AND ".$where_condition.' GROUP BY css.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getThreatScore()
	{
		$this->select_column = "'Every 6 hours' AS SCHEDULE, css.cst_id, COUNT(DISTINCT(css.partition_id)) AS Processed_partitions, MAX(end_time) Latest_processed_time";
		$this->column_order = array('css.cst_id','Processed_partitions','end_time');
		$this->column_where = array('css.cst_id','css.partition_id','end_time');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_threat_score();
		return $resultData->result();
	}

	function threatscore_Filtered()
	{
		$resultData = $this->_get_threat_score();
		return $resultData->num_rows();
	}

	private function _get_vpm_jobs()
	{
		
		$i = 0;
		$order_by = 'ORDER BY end_time desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									FROM csi_sync_status css
									INNER JOIN accounts ac USING (cst_id)
									WHERE job_type='vpm' AND account_expires > UTC_TIMESTAMP() AND end_time >= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -24 HOUR)
									AND ".$where_condition.' GROUP BY ac.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getVpmJobs()
	{
		$this->select_column = "'Daily at 3 AM' AS SCHEDULE, css.cst_id, COUNT(DISTINCT(css.partition_id)) AS Processed_partitions, MAX(end_time) Latest_processed_time";
		$this->column_order = array('css.cst_id','Processed_partitions','end_time');
		$this->column_where = array('css.cst_id','css.partition_id','end_time');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_vpm_jobs();
		return $resultData->result();
	}

	function vpmjobs_Filtered()
	{
		$resultData = $this->_get_vpm_jobs();
		return $resultData->num_rows();
	}

	
	private function _get_live_update()
	{
		
		$i = 0;
		$order_by = 'ORDER BY end_time desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									FROM csi_sync_status css
									INNER JOIN accounts ac USING (cst_id)
									WHERE job_type='LIVE_UPDATE' AND account_expires > UTC_TIMESTAMP() AND end_time >= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -.5 HOUR)
									AND ".$where_condition.' GROUP BY ac.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getLiveUpdates()
	{
		$this->select_column = "'Every 1 hour' AS SCHEDULE, css.cst_id,COUNT(DISTINCT(css.partition_id)) AS Processed_partitions, MAX(end_time) Latest_processed_time";
		$this->column_order = array('css.cst_id','Processed_partitions','end_time');
		$this->column_where = array('css.cst_id','css.partition_id','end_time');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_live_update();
		return $resultData->result();
	}

	function liveupdate_Filtered()
	{
		$resultData = $this->_get_live_update();
		return $resultData->num_rows();
	}
	
	
	private function _get_patch_template()
	{
		
		$i = 0;
		$order_by = 'ORDER BY date desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									FROM csi_template_log ctl
									INNER JOIN accounts ac USING (cst_id)
									where  account_expires > UTC_TIMESTAMP() AND date >= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -3 HOUR)
									AND ".$where_condition.' GROUP BY ac.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getPatchTemplatesJobs()
	{
		$this->select_column = "'Every 3 hours' AS SCHEDULE,ctl.cst_id,COUNT(DISTINCT(ctl.partition_id)) AS Processed_partitions, MAX(date) Latest_processed_time";
		$this->column_order = array('ctl.cst_id','Processed_partitions','date');
		$this->column_where = array('ctl.cst_id','ctl.partition_id','date');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_patch_template();
		return $resultData->result();
	}

	function patchtemplate_Filtered()
	{
		$resultData = $this->_get_patch_template();
		return $resultData->num_rows();
	}
	
	
	private function _get_smartgoup_compiled()
	{
		
		$i = 0;
		$order_by = 'ORDER BY last_compile_time desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from csi_smartgroup_generation csg inner join accounts ac on ac.cst_id=csg.cst_id 
									where in_progress=0 and last_compile_time >=date_add(UTC_TIMESTAMP(), interval -6 hour)  AND account_expires > UTC_TIMESTAMP()
									AND ".$where_condition.' GROUP BY csg.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getSmartgroupComplied()
	{
		$this->select_column = "'Compiled in last 6 hours' AS SCHEDULE, csg.cst_id as cst_id, count(distinct(csg.partition_id)) as Processed_partitions,max(last_compile_time) as Latest_compile_time";
		$this->column_order = array('csg.cst_id','csg.partition_id','Latest_compile_time');
		$this->column_where = array('csg.cst_id','csg.partition_id','last_compile_time');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_smartgoup_compiled();
		return $resultData->result();
	}

	function smartgroupcomplied_Filtered()
	{
		$resultData = $this->_get_smartgoup_compiled();
		return $resultData->num_rows();
	}
	
	
	private function _get_smartgoup_progress()
	{
		
		$i = 0;
		$order_by = 'ORDER BY last_started_at desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_order as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from csi_smartgroup_generation csg inner join accounts ac on ac.cst_id=csg.cst_id 
									where in_progress=1  AND account_expires > UTC_TIMESTAMP()
									AND ".$where_condition.' GROUP BY csg.cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getSmartgroupProgress()
	{
		$this->select_column = "'Compiling in-progress' AS SCHEDULE,csg.cst_id as cst_id, count(distinct(csg.partition_id)) as Currently_Compiling_partitions,max(last_started_at) as last_started_at ";
		$this->column_order = array('csg.cst_id','csg.partition_id','last_started_at');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_smartgoup_progress();
		return $resultData->result();
	}

	function smartgroupProgress_Filtered()
	{
		$resultData = $this->_get_smartgoup_progress();
		return $resultData->num_rows();
	}
	
	
	private function _get_smartgoup_notification()
	{
		
		$i = 0;
		$order_by = 'ORDER BY sync_updated desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from csi_smartgroup_notifications sgn inner join accounts ac on ac.account_id=sgn.account_id 
									where account_expires > UTC_TIMESTAMP() and sync_updated >=date_add(UTC_TIMESTAMP(), interval -6 hour)
									AND ".$where_condition.' GROUP BY cst_id '.$order_by.' '.$this->limit);
												
		return $query;
	}

	function getSmartgroupNotification()
	{
		$this->select_column = "'Every Minute' AS Schedule,cst_id, count(distinct(id)) as triggered_notifications_In_Last6Hours, max(sync_updated) AS last_notification";
		$this->column_order = array('cst_id','id','last_notification');
		$this->column_where = array('cst_id','id','sync_updated');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_smartgoup_notification();
		return $resultData->result();
	}

	function smartgroupnotificaiton_Filtered()
	{
		$resultData = $this->_get_smartgoup_notification();
		return $resultData->num_rows();
	}
	
	
	private function _get_report_generated()
	{
		
		$i = 0;
		$order_by = 'ORDER BY last_gen_date desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where ers.report_format in (3,4) and process_status in (2,3) AND account_expires > UTC_TIMESTAMP() and last_gen_date >=date_add(UTC_TIMESTAMP(), interval -24 hour)
									AND (".$where_condition.' ) '.$order_by.' '.$this->limit);

		$query_count=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where ers.report_format in (3,4) and process_status in (2,3) AND account_expires > UTC_TIMESTAMP() and last_gen_date >=date_add(UTC_TIMESTAMP(), interval -24 hour)
									AND (".$where_condition.' ) ');
												
		return array($query,$query_count);
	}

	function getReportGenerated()
	{
		$this->select_column = "ers.id,cst_id,partition_id, ers.account_id, one_time_gen, report_title, case report_format when 3 then 'PDF' when 4 then 'CSV' end as report_type, last_gen_date,end_date, sync_updated report_submitted_time,configuration_options_id";
		$this->column_order = array('ers.id','report_title','ers.account_id','cst_id','report_format','last_gen_date','report_submitted_time');
		$this->column_where = array('ers.id','report_title','ers.account_id','cst_id','report_format','last_gen_date','sync_updated');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_report_generated();
		return $resultData[0]->result();
	}

	function reportgenerated_Filtered()
	{
		$resultData = $this->_get_report_generated();
		return $resultData[1]->num_rows();
	}

	private function _get_report_progress()
	{
		
		$i = 0;
		$order_by = 'ORDER BY sync_updated desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 1 AND account_expires > UTC_TIMESTAMP()
									AND one_time_gen=1 AND ( ".$where_condition.' ) '.$order_by.' '.$this->limit);

		$query_count=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 1 AND account_expires > UTC_TIMESTAMP()
									AND one_time_gen=1 AND ( ".$where_condition." )");

		return array($query,$query_count);
	}

	function getReportProgress()
	{
		$this->select_column = "ers.id,cst_id,partition_id, ers.account_id, one_time_gen, report_title, case report_format when 3 then 'PDF' when 4 then 'CSV' end as report_type, last_gen_date,end_date, sync_updated report_submitted_time,configuration_options_id";
		$this->column_order = array('ers.id','report_title','ers.account_id','cst_id','report_format','last_gen_date','report_submitted_time');
		$this->column_where = array('ers.id','report_title','ers.account_id','cst_id','report_format','sync_updated');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_report_progress();
		return $resultData[0]->result();
	}

	function reportprogress_Filtered()
	{
		$resultData = $this->_get_report_progress();
		return $resultData[1]->num_rows();
	}

	private function _get_recurring_report_progress()
	{

		$i = 0;
		$order_by = 'ORDER BY sync_updated desc';

		if (isset($_POST['order']['0']['dir'])) {

			$order_by = 'ORDER BY ' . $this->column_order[$_POST['order']['0']['column'] - 1] . ' ' . $_POST['order']['0']['dir'];
		}


		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if ($_POST['search']['value']) {
				$where[] = $item . " LIKE '%" . $_POST['search']['value'] . "%'";
			}
		}

		$where_condition = ' 1=1 ';
		if (!empty($where)) {
			$where_condition = implode(' OR ', $where);
		}

		$query = $this->caDB->query("SELECT " . $this->select_column . "
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 1 AND account_expires > UTC_TIMESTAMP()
									AND one_time_gen=0 AND ( " . $where_condition . ' ) ' . $order_by . ' ' . $this->limit);

		$query_count = $this->caDB->query("SELECT " . $this->select_column . "
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 1 AND account_expires > UTC_TIMESTAMP()
									AND one_time_gen=0 AND ( " . $where_condition . " )");

		return array($query, $query_count);
	}

	function getRecurringReportProgress()
	{
		$this->select_column = "ers.id,cst_id,partition_id, ers.account_id, one_time_gen, report_title, case report_format when 3 then 'PDF' when 4 then 'CSV' end as report_type, last_gen_date,end_date, sync_updated report_submitted_time,recurrence_schedule,configuration_options_id";
		$this->column_order = array('ers.id', 'report_title', 'ers.account_id', 'cst_id', 'recurrence_schedule', 'one_time_gen', 'report_format', 'last_gen_date', 'report_submitted_time');
		$this->column_where = array('ers.id', 'report_title', 'ers.account_id', 'cst_id', 'one_time_gen', 'report_format', 'sync_updated');
		$limit = '';
		if ($_POST['length'] != -1)
			$limit = 'LIMIT ' . $_POST['start'] . ', ' . $_POST['length'];

		$this->limit = $limit;
		$resultData = $this->_get_recurring_report_progress();
		return $resultData[0]->result();
	}

	function recurringReportprogress_Filtered()
	{
		$resultData = $this->_get_recurring_report_progress();
		return $resultData[1]->num_rows();
	}

	private function _get_report_failed()
	{
		
		$i = 0;
		$order_by = 'ORDER BY sync_updated desc';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_where as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 4 AND account_expires > UTC_TIMESTAMP()
									AND ".$where_condition.' '.$order_by.' '.$this->limit);

		$query_count=$this->caDB->query("SELECT ".$this->select_column."
									from enhanced_reporting_schedule ers inner join accounts ac on ac.account_id=ers.account_id 
									where report_format in (3,4) and process_status  = 4 AND account_expires > UTC_TIMESTAMP()
									AND  (".$where_condition." )" );
												
		return array($query,$query_count);
	}

	function getReportFailed()
	{
		$this->select_column = "ers.id,ers.account_id,cst_id ,partition_id,report_title, case report_format when 3 then 'PDF' when 4 then 'CSV' end as report_type, last_gen_date Latest_processed_time,configuration_options_id";
		$this->column_order =  array('ers.id','report_title','ers.account_id','cst_id','report_format');
		$this->column_where =  array('cst_id','id','report_title','ers.account_id');
		$limit = '';
		if($_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_report_failed();
		return $resultData[0]->result();
	}

	function reportfailed_Filtered()
	{
		$resultData = $this->_get_report_failed();
		return $resultData[1]->num_rows();
	}
}