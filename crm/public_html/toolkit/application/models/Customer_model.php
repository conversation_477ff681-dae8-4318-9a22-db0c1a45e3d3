<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Customer_Model extends CI_Model {

	private $table;
	public $primary_key;
	public $data;

	var $column_order = array(null, 'role_name'); //set column field database for datatable orderable
	var $column_search = array('role_name'); //set column field database for datatable searchable 
	var $order = array('role_id' => 'asc'); // default order 
	var $limit = ''; // default order 
	var $select_column = array();

	
	function __construct() {
		parent::__construct();
		$this->table = substr(strtolower(get_class($this)), 0, -6);
		$this->primary_key = array();
		$this->date = array();
	}

	
	
	public function getOnpremCustomer()
	{
		$query=$this->caDB->query("SELECT   a.cst_id,a.account_id,
										   a.account_name,
										   a.account_email,
										   a.account_username,
										   max(a.account_expires) as Expires,
										   max(a.last_login) as last_login
									FROM accounts as a
									WHERE  account_esm is null
									");
		
		return $query->row();
	}
	
	public function cust_check($cust_id){
		
		$this->caDB->where('cst_id',$cust_id);
		$query = $this->caDB->get('accounts');
		if ($query->num_rows() > 0){
			return true;
		}
		else{
			return false;
		}
		
	}
	
	private function _get_partitions($cust_id)
	{
		
		$i = 0;
		$order_by = 'ORDER BY account_name ASC';
		
		if(isset($_POST['order']['0']['dir'])){
			
			$order_by = 'ORDER BY '.$this->column_order[$_POST['order']['0']['column']-1].' '.$_POST['order']['0']['dir'];
		}
		
		
		$i = 0;
		$where = array();
		foreach ($this->column_order as $item) {
			if($_POST['search']['value']) {
				$where[] = $item." LIKE '%".$_POST['search']['value']."%'";
			}
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$query=$this->caDB->query("SELECT ".$this->select_column."
									FROM accounts ac
									LEFT JOIN partition_license_usage pl USING (cst_id,partition_id)
									WHERE cst_id=".(int)$cust_id."
									AND ".$where_condition.' '.$order_by.' '.$this->limit);
		$str = $this->caDB->last_query();
	
		return $query;
	}

	public function getPartitions($cust_id)
	{
		$this->select_column = "account_id,account_name,account_username,uid,agent_uid,partition_id,max_licensed_devices,used_device_licenses,account_email,is_admin,is_partition_admin,recipient_email,recipient_mobile,host_granted,user_granted,host_available,user_available,auth_type";
		$this->column_order = array();
		$limit = '';
		if(isset($_POST['length']) && $_POST['length'] != -1)
			$limit = 'LIMIT '.$_POST['start'].', '.$_POST['length'];
		
		$this->limit = $limit;
		$resultData = $this->_get_partitions($cust_id);
		return $resultData;
	}
	
	public function getOtherDetails($cust_id){
		
		$common_where = array( 'cst_id='.(int)$cust_id
						,'partition_id = 0');
		
		
		$where = array( 'license_type = 1' );
		$where = array_merge($common_where,$where);
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' AND ',$where);
		}

		$query=$this->caDB->query("SELECT num_granted,num_available
									FROM partition_license_pool 
									WHERE ".$where_condition);

		$numbers = $query->row();
		$num_granted = (isset($numbers->num_granted))? $numbers->num_granted : '-';
		$num_available = (isset($numbers->num_available))? $numbers->num_available : '-';
		$hostLicenseNumbers = array( $num_granted, $num_available );
		
		unset($where);
		$where = array( 'license_type = 2' );
		$where = array_merge($common_where,$where);
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' AND ',$where);
		}

		$query=$this->caDB->query("SELECT num_granted,num_available
									FROM partition_license_pool 
									WHERE ".$where_condition);
		$numbers = $query->row();
		$num_granted = (isset($numbers->num_granted))? $numbers->num_granted : '-';
		$num_available = (isset($numbers->num_available))? $numbers->num_available : '-';
		$userLicenseNumbers = array( $num_granted ,$num_available);
		
		unset($where);
		$where = array( 'license_type = 3' );
		$where = array_merge($common_where,$where);
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' AND ',$where);
		}

		$query=$this->caDB->query("SELECT num_granted,num_available
									FROM partition_license_pool 
									WHERE ".$where_condition);
									
		$numbers = $query->row();
		$num_granted = (isset($numbers->num_granted))? $numbers->num_granted : '-';
		$num_available = (isset($numbers->num_available))? $numbers->num_available : '-';
		$partitionLicenseNumbers = array( $num_granted,$num_available );
		
		return array('hostLicenseNumbers'=>$hostLicenseNumbers,'userLicenseNumbers'=>$userLicenseNumbers,'partitionLicenseNumbers'=>$partitionLicenseNumbers);

	}
	
	public function getTotalhost($cust_id)
	{
		
		$query=$this->pdb->query("SELECT count(*) as total_host
									FROM nsi_devices ac
									JOIN nsi_groups_v3 USING (group_id)
									WHERE software_inspector_id NOT IN (0,100)");
		
		return $query->row()->total_host;
	}
	
	
	private function _get_host_sg()
	{
		$this->column_order = $this->column_order;
		$this->order = array('compiled_time' => 'desc'); // default order
		
		
		$this->pdb->where($this->primary_key);
		$this->pdb->select($this->select_column);
		$this->pdb->from('csi_smartgroups');
		$this->pdb->join('csi_smartgroup_compiled_hosts', 'csi_smartgroups.id = csi_smartgroup_compiled_hosts.smartgroup_id AND csi_smartgroups.account_id=csi_smartgroup_compiled_hosts.account_id ', 'left');

		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->pdb->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->pdb->like($item, $_POST['search']['value']);
				}
				else{
					$this->pdb->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->pdb->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->pdb->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->pdb->order_by(key($order), $order[key($order)]);
		}
	}
	

	function getHostSmartgroup($filter){
	
		$this->select_column = $filter['columns'];
		$this->column_search = $filter['searchable'];
		
		$this->column_order = $filter['orderable'];
		
		$this->_get_host_sg();
		
		if(isset($_POST['length']) && $_POST['length'] != -1)
			$this->pdb->limit($_POST['length'], $_POST['start']);
		
		$query = $this->pdb->get();
		$str = $this->pdb->last_query();
		
		return $query->result();
	}

	function hostSmartgroup_Filtered(){
		$this->_get_host_sg();
		$query = $this->pdb->get();
		return $query->num_rows();
	}

	
	
	
	private function _get_product_sg()
	{
		$this->column_order = $this->select_column; 
		$this->order = array('compiled_time' => 'desc'); // default order
		
		
		$this->pdb->where($this->primary_key);
		$this->pdb->select($this->select_column);
		$this->pdb->from('csi_smartgroups');
		$this->pdb->join('csi_smartgroup_compiled_software_totals', 'csi_smartgroups.id = csi_smartgroup_compiled_software_totals.smartgroup_id', 'left');

		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->pdb->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->pdb->like($item, $_POST['search']['value']);
				}
				else{
					$this->pdb->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->pdb->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->pdb->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->pdb->order_by(key($order), $order[key($order)]);
		}
	}
	

	function getProductSmartgroup($filter){
		
		$this->select_column = $filter['columns'];
		$this->column_search = $filter['searchable'];
		
		$this->column_order = array();
		
		$this->_get_product_sg();
		
		if(isset($_POST['length']) && $_POST['length'] != -1)
			$this->pdb->limit($_POST['length'], $_POST['start']);
		
		$query = $this->pdb->get();
		return $query->result();
	}

	function productSmartgroup_Filtered(){
		$this->_get_product_sg();
		$query = $this->pdb->get();
		return $query->num_rows();
	}

    function getreportconfiguration($filter){

        $this->select_column = $filter['columns'];
        $this->column_search = $filter['searchable'];
        $this->_get_report_search();
        if(isset($_POST['length']) && $_POST['length'] != -1)
            $this->caDB->limit($_POST['length'], $_POST['start']);
        $query = $this->caDB->get();
        return $query->result();
    }

    function getreportconfiguration_filtered(){

        $this->_get_report_search();
        $query = $this->caDB->get();
        return $query->num_rows();
    }


    private function _get_report_search()
    {
        $this->column_order = $this->select_column;
        $this->order = array('last_gen_date' => 'desc'); // default order
        $this->caDB->where($this->primary_key);
        $this->caDB->select($this->select_column);
        $this->caDB->from('enhanced_reporting_schedule');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->caDB->like($item, $_POST['search']['value']);
                }
                else{
                    $this->caDB->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->caDB->group_end(); //close bracket
            }
            $i++;
        }

        if(isset($_POST['order'])) // here order processing
        {
            $this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->caDB->order_by(key($order), $order[key($order)]);
        }
    }

    function get_csi_configuration($filter){

        $this->select_column = $filter['columns'];
        $this->column_search = $filter['searchable'];
        $this->_get_csi_config_search();
        if(isset($_POST['length']) && $_POST['length'] != -1)
            $this->pdb->limit($_POST['length'], $_POST['start']);
        $query = $this->pdb->get();

        return $query->result();
    }

    function get_csi_configuration_filtered(){

        $this->_get_csi_config_search();
        $query = $this->pdb->get();
        return $query->num_rows();
    }


    private function _get_csi_config_search()
    {
        $this->column_order = $this->select_column;
        $this->order = array('option_name' => 'asc'); // default order
        $this->pdb->where($this->primary_key);
        $this->pdb->select($this->select_column);
        $this->pdb->from('csi_configuration');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->pdb->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->pdb->like($item, $_POST['search']['value']);
                }
                else{
                    $this->pdb->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->pdb->group_end(); //close bracket
            }
            $i++;
        }

        if(isset($_POST['order'])) // here order processing
        {
            $this->pdb->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->pdb->order_by(key($order), $order[key($order)]);
        }


    }
    function update_configuration($account_id,$option_value,$option_name)
    {
        $date= date("Y-m-d");
        $desc = '';
        if($option_name=='REPORT_MAX_ROW_COUNT_PER_REQUEST')
        {
            $desc = 'Not to overwhelm php internal memory, as we parse sql query in batches';

        }elseif($option_name=='DECREASE_AGENT_CHECKIN')
        {
            $desc = 'Increase Agent check-in frequency time';
        }
        elseif($option_name=='PRODUCT_SG_DEVICES_FETCH_PER_LOOP')
        {
	        $account_id = 0;
	        $desc = 'Number of hosts to be fetched per loop at a time for Product Smart Groups generation';
        }
        $description = 'Toolkit '.$date.' '.$desc;
        $query=$this->pdb->query( "REPLACE INTO csi_configuration (account_id, option_name, option_value, option_is_enabled, option_internal_description) 
 VALUES (".$account_id.", '".$option_name."', '".$option_value."', '1', '".$description."')");
        return $this->pdb->last_query();
    }
}