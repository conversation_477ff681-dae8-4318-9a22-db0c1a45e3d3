<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Admin_Users_Model extends CI_Model {

	private $table;
	public $primary_key;
	public $data;
	
	var $column_order = array(null, 'first_name','last_name','email','role_name','username','a.status_ind'); //set column field database for datatable orderable
	var $column_search = array('first_name','last_name','email','role_name','username','a.status_ind'); //set column field database for datatable searchable 
	var $order = array('first_name' => 'asc'); // default order

	function __construct() {
		parent::__construct();
		$this->table = substr(strtolower(get_class($this)), 0, -6);
		$this->primary_key = array();
		$this->date = array();
	}

	private function reset() {
		$this->primary_key = array();
		$this->data = array();
	}

	private function reset_pk() {
		$this->primary_key = array();
	}

	private function reset_data() {
		$this->data = array();
	}

	public function login($data) {
		$this->db->where('a.username', $data['username']);
		$this->db->where('a.status_ind', '1');
		$this->db->from($this->table . ' as a');
		$this->db->join('admin_roles as ar', 'a.role_id=ar.role_id', 'left');
		$query = $this->db->get();
		if($query->num_rows() > 0)
		{
		    $hash = $query->row()->password;
		    if (password_verify($data['password'], $hash)) {
		    $row = $query->row();
		    return $row;
		} else {
		    return null;
		    }
		}else{
		    return null;
		}
	}

	public function get_row() {
		$this->db->where($this->primary_key);
		$this->db->select('u.*,r.role_name');
		$this->db->from($this->table . ' as u');
		$this->db->join('admin_roles as r', 'u.role_id=r.role_id', 'left');
		$query = $this->db->get();
		$row = $query->row();
		return $row;
	}

	public function session_id() {
		$this->db->where($this->primary_key);
		$this->db->where('status_ind', '1');
		$query = $this->db->get($this->table);
		$row = $query->row();
		if (!empty($row->user_session_id)) {
			return $row->user_session_id;
		} else {
			return false;
		}
	}

	public function update() {
		$this->db->update($this->table, $this->data, $this->primary_key);
		$this->reset();
		return true;
	}

	public function insert() {
		$this->db->insert($this->table, $this->data);
		$this->reset();
		return true;
	}

	public function delete() {
		$this->db->delete($this->table, $this->primary_key);
		$this->reset();
		return true;
	}

	public function basicView() {
		$this->db->select('a.*,b.role_id,b.role_name');
		$this->db->from($this->table . ' as a');
		$this->db->join('admin_roles as b', 'a.role_id = b.role_id', 'left');
		$query = $this->db->get();
		return $query->result();
	}
	
	private function _get_users_query()
	{
		
		$this->db->from($this->table . ' as a');
		$this->db->join('admin_roles as b', 'a.role_id = b.role_id', 'left');
		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->db->order_by(key($order), $order[key($order)]);
		}
	}

	function view()
	{
		$this->_get_users_query();
		if($_POST['length'] != -1)
		$this->db->limit($_POST['length'], $_POST['start']);
		$query = $this->db->get();
		
		//$str = $this->db->last_query();
   
		return $query->result();
	}

	function count_filtered()
	{
		$this->_get_users_query();
		$query = $this->db->get();
		return $query->num_rows();
	}

	public function count_all()
	{
		$this->db->from($this->table);
		return $this->db->count_all_results();
	}

	public function change_user_password(){
        $current_password   =   $this->input->post('current_password');
        $new_password       =   $this->input->post('new_password');
        $confirm_password   =   $this->input->post('confirm_password');
        $user_id             =  $this->session->userdata['user_id'];
        $response = array();
        if ( $current_password == "" ) {
            $response[0] = 2;
            $response[1]= 'Please enter your current password.';
            return $response;
        } else if ( $new_password != $confirm_password ) {
            $response[0] = 2;
            $response[1]= 'New password does not match the confirmation field.';
            return $response;
        } else if ( $new_password == "" ) {
            $response[0] = 2;
            $response[1]= 'New password cannot be empty.';
            return $response;
        } else if ( strlen($new_password) < 8 ) {
            $response[0] = 2;
            $response[1]= 'New password too short - use at least an 8 character password.';
            return $response;
        } else if ( $current_password == $new_password ) {
            $response[0] = 2;
            $response[1] = 'New password identical to old password - please choose another password.';
            return $response;
        }

        $this->db->where('user_id', $user_id);
        $query = $this->db->get($this->table);
        $hash = $query->row()->password;
        if (password_verify($current_password,$hash))
        {
            $data = array('password'=>password_hash($new_password,PASSWORD_BCRYPT));
            $this->db->set($data);
            $where = array('user_id'=>$user_id);
            $this->db->where($where);
            $this->db->update($this->table);
            if($this->db->affected_rows() > 0){
               $response[0] = 1;
               $response[1]= 'Password updated successfully';
               return $response;
            }
        } else {
            $response[0] = 2;
            $response[1]= 'Current Password is not matching';
            return $response;
        }
    }

    public function check_username_exist($username){
        $this->db->where('username', $username);
        $query = $this->db->get($this->table);
        if ($query->num_rows() > 0){
            return true;
        }else{
            return false;
        }
    }

}