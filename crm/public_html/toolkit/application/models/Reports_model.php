<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Reports_Model extends CI_Model {

	private $table;
	public $primary_key;
	public $data;

	var $column_order = array(null, 'role_name'); //set column field database for datatable orderable
	var $column_search = array('role_name'); //set column field database for datatable searchable 
	var $order = array('role_id' => 'asc'); // default order 
	var $limit = ''; // default order 
	var $select_column = array();

	
	function __construct() {
		parent::__construct();
		$this->table = substr(strtolower(get_class($this)), 0, -6);
		$this->primary_key = array();
		$this->date = array();
	}

	private function reset() {
		$this->primary_key = array();
		$this->data = array();
	}

	private function reset_pk() {
		$this->primary_key = array();
	}

	private function reset_data() {
		$this->data = array();
	}

	public function view_roles() {
		$this->caDB->where('status_ind','1');
		$query = $this->caDB->get($this->table);
		return $query->result();
	}

	public function insert() {
		$this->caDB->insert($this->table, $this->data);
		$this->reset_data();
		return true;
	}

	public function update() {
		$this->caDB->update($this->table, $this->data, $this->primary_key);
		$this->reset();
		return true;
	}

	public function delete() {
		$this->caDB->delete($this->table, $this->primary_key);
		$this->reset_pk();
		return true;
	}
	
	public function get_max_value($field) {
		$this->caDB->select_max($field);
		$query = $this->caDB->get($this->table);
		$row = $query->row();
		return $row->$field;
	}
	public function get_row() {
		$this->caDB->where($this->primary_key);
		$this->caDB->select('*');
		$this->caDB->from($this->table);
		$query = $this->caDB->get();
		$row = $query->row();
		return $row;
	}
	public function basicView() {
		$query = $this->caDB->get($this->table);
		return $query->result();
	}

	
	public function getCustomerDetails($cust_id)
	{
		$query=$this->caDB->query('SELECT * from ca.accounts where cst_id ='.$cust_id );
													
		return $query->row();
	}
	
	private function _get_all_customers()
	{
		$this->column_search = array('a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','cpi.csi_version','cpi.patch_version');
		
		$this->column_order = array(null,'a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','a.account_expires','last_login','cpi.csi_version','cpi.patch_version');
		
		$this->order = array('a.last_login' => 'desc'); // default order
		
		$str=" cpi.csi_version = '7'";
		$this->caDB->where($str, NULL, FALSE);
		
		$this->caDB->select($this->select_column);
		
		$this->caDB->from('csi_pdb_info as cpi');
		$this->caDB->join('accounts as a', 'cpi.cst_id = a.cst_id', 'LEFT');

		
		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->caDB->like($item, $_POST['search']['value']);
				}
				else{
					$this->caDB->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->caDB->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->caDB->order_by(key($order), $order[key($order)]);
		}
		 $this->caDB->group_by('a.cst_id,db_client_host'); 
	}

	function getCloudCustomer()
	{
		$this->select_column = 'a.cst_id,a.account_id,
					   a.account_name,
					   a.account_email,
					   a.account_username,
					   cpi.db_client_host,
					   max(a.account_expires) as Expires,
					   max(a.last_login) as last_login,
					   cpi.csi_version,
					   cpi.patch_version';
		$this->_get_all_customers();
		if(isset($_POST['length']) && $_POST['length'] != -1)
			$this->caDB->limit($_POST['length'], $_POST['start']);
		
		$query = $this->caDB->get();
		
		$str = $this->caDB->last_query();
   
		return $query->result();
	}

	function allCustomer_Filtered()
	{
		$this->_get_all_customers();
		
		$query = $this->caDB->get();
		
		$str = $this->caDB->last_query();
		
		return $query->num_rows();
	}
	
	private function _get_expired_customers()
	{
		$this->column_search = array('a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','cpi.csi_version','cpi.patch_version');
		
		$this->column_order = array(null,'a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','a.account_expires','a.last_login','cpi.csi_version','cpi.patch_version');
		
		$this->order = array('a.account_expires' => 'desc'); // default order
		
		$str=" cpi.csi_version = '7' and a.account_expires < CURDATE()";
		$this->caDB->where($str, NULL, FALSE);

		$this->caDB->select($this->select_column);
		
		$this->caDB->from('csi_pdb_info as cpi');
		$this->caDB->join('accounts as a', 'cpi.cst_id = a.cst_id', 'LEFT');
		
		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->caDB->like($item, $_POST['search']['value']);
				}
				else{
					$this->caDB->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->caDB->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->caDB->order_by(key($order), $order[key($order)]);
		}
		 $this->caDB->group_by('a.cst_id,db_client_host'); 
	}

	function getExpiredCustomer()
	{
		$this->select_column = 'a.cst_id,
						   a.account_name,
						   a.account_email,
						   a.account_username,
						   cpi.db_client_host,
						   max(a.account_expires) as Expires,
						   max(a.last_login) as last_login,
						   cpi.csi_version,
						   cpi.patch_version';
		$this->_get_expired_customers();
		if(isset($_POST['length']) && $_POST['length'] != -1)
		{
		    $this->caDB->limit($_POST['length'], $_POST['start']);
		}
		$query = $this->caDB->get();
		
		return $query->result();
	}

	function expiredCustomer_Filtered()
	{
		$this->_get_expired_customers();
		
		$query = $this->caDB->get();
		
		$str = $this->caDB->last_query();
		
		return $query->num_rows();
	}

	public function expiredCustomer_count()
	{
		$this->select_column = 'count(*)';
		$this->_get_expired_customers();
		return $this->caDB->count_all_results();
	}
	
	
	private function _get_detectable_products()
	{
		$this->order = array('platform asc,os_soft_name asc'); // default order
		$i = 0;
		foreach ($this->column_search as $item) {
		    if($_POST['search']['value']) {
		        if($i===0){
		            $this->vuln_track->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
		            $this->vuln_track->like($item, $_POST['search']['value']);
		        }
		        else{
		            $this->vuln_track->or_like($item, $_POST['search']['value']);
		        }

		        if(count($this->column_search) - 1 == $i) //last loop
		            $this->vuln_track->group_end(); //close bracket
		    }
		    $i++;
		}
		
		$where_condition = ' 1=1 ';
		if(!empty($where)){
			$where_condition = implode(' OR ',$where);
		}

		$this->vuln_track->select($this->select_column);
		$this->vuln_track->from('os_soft');
 		$this->vuln_track->join('(SELECT DISTINCT(CAST(product_id AS UNSIGNED)) as pid, CASE platform WHEN 1 THEN "Windows" WHEN 2 THEN "Mac OSX" WHEN 3 THEN "RHEL" WHEN 4 THEN "Android" END as platform
								    FROM `sr_match_files`
								    WHERE platform in (1,2,3,4) AND product_id is not null AND product_id > 0) AS hat', 'os_soft.os_soft_id = hat.pid');
 		$this->vuln_track->join('vendor', 'os_soft.vendor_id = vendor.vendor_id', 'LEFT');

        if(isset($_POST['order'])) // here order processing
        {
            $this->vuln_track->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->vuln_track->order_by('platform asc,os_soft_name asc');
        }
	}

	function getDetectableProducts()
    {
        $this->select_column = 'os_soft_id, platform, os_soft_name, vendor_name ';
        $this->column_order = array('os_soft_id', 'platform', 'os_soft_name', 'vendor_name');
        $this->column_search = array('os_soft_id', 'platform', 'os_soft_name', 'vendor_name');
        $resultData = $this->_get_detectable_products();
        if(isset($_POST['length']) && $_POST['length'] != -1)
        {
            $this->vuln_track->limit($_POST['length'], $_POST['start']);
        }
        $query = $this->vuln_track->get();
        return $query->result();
    }


	function detectableProducts_Filtered()
	{
	    $resultData = $this->_get_detectable_products();
	    $query = $this->vuln_track->get();
	    $str = $this->vuln_track->last_query();
	    return $query->num_rows();
	}

	
	private function _get_download_credentials()
	{
        $this->order = array('user_cstid' => 'asc'); // default order
        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->crm->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->crm->like($item, $_POST['search']['value']);
                }
                else{
                    $this->crm->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->crm->group_end(); //close bracket
            }
            $i++;
        }

        $str = "(u.user_products & p.product_bw) > 0 AND  1=1";
        $this->crm->select($this->select_column);
        $this->crm->from('download_products as p');
        $this->crm->from('download_users as u');
        $this->crm->where($str, NULL, FALSE);
        if(isset($_POST['order'])) // here order processing
        {
            $this->crm->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->crm->order_by(key($order), $order[key($order)]);
        }
	}

	function getDownloadCredentials()
	{
		$this->select_column = ' u.user_cstid, u.user_email, p.product_code';
		$this->column_order = array('u.user_cstid', 'u.user_email','p.product_code');
		$this->column_search = array('u.user_cstid', 'u.user_email','p.product_code');
		$resultData = $this->_get_download_credentials();
		if(isset($_POST['length']) && $_POST['length'] != -1)
		{
		    $this->crm->limit($_POST['length'], $_POST['start']);
		}
		$result = $this->crm->get();
		//echo $this->crm->last_query();
		return $result->result();
	}

	function downloadcredentials_Filtered()
	{
		$resultData = $this->_get_download_credentials();
		$query = $this->crm->get();
		return $query->num_rows();
	}

	private function _get_hosted_active_csi6_customer()
	{
		$this->column_search = array('a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','cpi.csi_version','cpi.patch_version');
		
		$this->column_order = array(null,'a.cst_id','a.account_name','a.account_email','a.account_username','cpi.db_client_host','max(a.account_expires) as Expires','max(a.last_login) as last_login','cpi.csi_version','cpi.patch_version'); 
		
		$this->order = array('a.account_expires' => 'desc'); // default order 
		
		$str=" cpi.csi_version = '6.0' and a.account_expires > CURRENT_TIMESTAMP";
		
		$this->caDB->where($str, NULL, FALSE);
		
		$this->caDB->select($this->select_column);
		
		$this->caDB->from('csi_pdb_info as cpi');
		$this->caDB->join('accounts as a', 'cpi.cst_id = a.cst_id', 'LEFT');
		
		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->caDB->like($item, $_POST['search']['value']);
				}
				else{
					$this->caDB->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->caDB->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->caDB->order_by(key($order), $order[key($order)]);
		}
		 $this->caDB->group_by('a.cst_id,db_client_host'); 
	}

	function getActiveHostedCsi6Customer()
	{
		$this->select_column = 'a.cst_id,
					   a.account_name,
					   a.account_email,
					   a.account_username,
					   cpi.db_client_host,
					   max(a.account_expires) as Expires,
					   max(a.last_login) as last_login,
					   cpi.csi_version,
					   cpi.patch_version';
		$this->_get_hosted_active_csi6_customer();
		if(isset($_POST['length']) && $_POST['length'] != -1)
		{
		    $this->caDB->limit($_POST['length'], $_POST['start']);
		}
		$query = $this->caDB->get();
		
		$str = $this->caDB->last_query();
   
		return $query->result();
	}

	function activeHostedCsi6Customer_Filtered()
	{
		$this->_get_hosted_active_csi6_customer();
		
		$query = $this->caDB->get();
		
		$str = $this->caDB->last_query();
		
		return $query->num_rows();
	}

    function getpdbInfoDetails($cust_id)
    {
        $this->select_column = 'a.cst_id,
					   a.account_name,
					   a.account_email,
					   a.account_username,
					   cpi.partition_id,
					   cpi.csi_version,
					   cpi.patch_version,
					   cpi.date_applied,
					   cpi.locked,
					   cpi.lock_date,
					   cpi.db_client_host,
					   sso_con.is_sso,
					   sso_con.guid,
					   sso_con.disable_standard_login,
					   sso_con.template_account_id';
        $this->_get_pdb_details($cust_id);
        if(isset($_POST['length']) && $_POST['length'] != -1)
        {
            $this->caDB->limit($_POST['length'], $_POST['start']);
        }
        $query = $this->caDB->get();
        return  $query->result();
    }

    private function _get_pdb_details($cust_id)
    {
        $this->column_search = array('cpi.cst_id','cpi.partition_id','csi_version','patch_version','date_applied','locked','lock_date','db_client_host','sso_con.guid','sso_con.template_account_id');
        $this->column_order = array('','','','','cpi.partition_id','csi_version','patch_version','date_applied','locked','lock_date','db_client_host');
        $this->order = array('a.account_name' => 'asc'); // default order
        //$this->caDB->where($this->primary_key);
        $this->caDB->select('*');
        $this->caDB->from('csi_pdb_info as cpi');
        $this->caDB->join('accounts as a', 'cpi.cst_id = a.cst_id and cpi.partition_id = a.partition_id', 'INNER');
        $this->caDB->join('sso_configuration as sso_con', 'cpi.cst_id = sso_con.cst_id and cpi.partition_id = sso_con.partition_id', 'LEFT');
        $this->caDB->where('cpi.cst_id='.$cust_id );
        $this->caDB->where('is_partition_admin=1');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->caDB->like($item, $_POST['search']['value']);
                }
                else{
                    $this->caDB->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->caDB->group_end(); //close bracket
            }
            $i++;
        }

        if(isset($_POST['order'])) // here order processing
        {
            $this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->caDB->order_by(key($order), $order[key($order)]);
        }

        $this->caDB->group_by(array("cpi.cst_id", "cpi.partition_id"));
    }

    function getpdbInfoDetails_Filtered($cust_id)
    {
        $this->_get_pdb_details($cust_id);

        $query = $this->caDB->get();

        $str = $this->caDB->last_query();

        return $query->num_rows();
    }

    function generate_report_asap($report_id,$account_id){

        $data = array('generate_asap'=>1,'process_status'=>0);
        $this->caDB->set($data);
        $where = array('account_id'=>$account_id,'id'=>$report_id);
        $this->caDB->where($where);
        $this->caDB->update('enhanced_reporting_schedule');

    }

    function recompile_sg_asap($smartgroup_id,$customer_id,$partition_id,$type,$account_id)
    {
        $smartgroupIds = array($smartgroup_id);
        // if any of SG are of type HOST, also add all products to generation list
        if ($type == 'host') {
            $name = "All Products";
            $where = array('name' => $name, 'account_id' => $account_id, 'type' => 2, 'editable' => 0);
            $this->pdb->select('id');
            $this->pdb->from('csi_smartgroups');
            $this->pdb->where($where);
            if ($row = $this->pdb->get()->row()) {
                $id = $row->id;
                array_push($smartgroupIds, $id);
            }
        }
        $data = array('generate_asap'=>1,'in_progress'=>0);
        $this->pdb->set($data);
        $this->pdb->where_in('id', $smartgroupIds);
       // $this->pdb->where('in_progress', 0);
        $this->pdb->update('csi_smartgroups');
        $datacadb = array('generate_asap'=>1,'in_progress'=>0);
        $this->caDB->set($datacadb);
        $where = array('cst_id'=>$customer_id,'partition_id'=>$partition_id);
        $this->caDB->where($where);
        $this->caDB->update('csi_smartgroup_generation');
    }

    function pdb_info_unlock($customer_id,$partition_id)
    {
        $data = array('locked'=>0);
        $this->caDB->set($data);
        $where = array('cst_id'=>$customer_id,'partition_id'=>$partition_id);
        $this->caDB->where($where);
        $this->caDB->update('csi_pdb_info');

    }

    function unlock_account($account_id)
    {
        $this->caDB->select('COUNT(account_id) AS count');
        $this->caDB->from('login_attempts');
        $this->caDB->where('account_id', $account_id);
        $count = $this->caDB->get()->row()->count;

        if ($count >= ATTEMPTS_FAILED_TEMP_LOCKED_COUNT) {

            $this->caDB->where('account_id', $account_id);
            $this->caDB->delete('login_attempts');
            return $afftectedRows = $this->caDB->affected_rows();
        }
        return 0;

    }

    function get_host_from_customerId($customer_id,$partition_id)
    {
        $partition_id = ($partition_id > 0) ? $partition_id : 0;
        $where = array('cst_id'=>$customer_id,'partition_id'=>$partition_id);
        $this->caDB->select('db_client_host');
        $this->caDB->from('csi_pdb_info');
        $this->caDB->where($where);
        return $this->caDB->get()->row()->db_client_host;

    }

    function getDownloadPackage($searchBySource)
    {
        $this->select_column = 'cst_id,
                               vpm_id,
                               package_name,
                               source,
                               INET_NTOA(ip_v4) as ip_v4,
                               created_at';
        $this->whereSource = $searchBySource;
        $this->_get_download_package_details();
        if(isset($_POST['length']) && $_POST['length'] != -1)
        {
            $this->crm->limit($_POST['length'], $_POST['start']);
        }
        $query = $this->crm->get();
        return  $query->result();
    }

    private function _get_download_package_details()
    {
        $this->column_search = array('cst_id','vpm_id','package_name','source','INET_NTOA(ip_v4)','created_at');
        $this->column_order = array('','cst_id','vpm_id','package_name','source','ip_v4','created_at');
        $this->order = array('created_at' => 'desc'); // default order
        //$this->caDB->where($this->primary_key);
        if($this->whereSource !== NULL && $this->whereSource!== '') {
            $this->crm->where('source='.$this->whereSource );
        }
        $this->crm->select($this->select_column);
        $this->crm->from('packages_usage');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->crm->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->crm->like($item, $_POST['search']['value']);
                }
                else{
                    $this->crm->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->crm->group_end(); //close bracket
            }
            $i++;
        }

        if(isset($_POST['order'])) // here order processing
        {
            $this->crm->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->crm->order_by(key($order), $order[key($order)]);
        }
     }

    function getDownloadPackage_Filtered()
    {
        $this->_get_download_package_details();
        $query = $this->crm->get();
        $str = $this->crm->last_query();
        return $query->num_rows();
    }

    function report_debug_status($report_id,$account_id,$debug_status)
    {
        $debug = ($debug_status == 0) ? 1:0;
        $data = array('debug_enable'=>$debug);
        $this->caDB->set($data);
        $where = array('account_id'=>$account_id,'id'=>$report_id);
        $this->caDB->where($where);
        $this->caDB->update('enhanced_reporting_schedule');
        echo $this->caDB->last_query();
    }

    function get_large_customers_details($cust_id)
    {
        $this->select_column = 'cst_id,
					   type,
					   created_at';

        $this->_get_lg_cust_details($cust_id);
        if(isset($_POST['length']) && $_POST['length'] != -1)
        {
            $this->caDB->limit($_POST['length'], $_POST['start']);
        }
        $query = $this->caDB->get();
        return  $query->result();
    }

    private function _get_lg_cust_details($cust_id)
    {
        $this->column_search = array('cst_id','type','created_at');
        $this->column_order = array('cst_id','type','created_at');
        $this->order = array('created_at' => 'desc'); // default order
        $this->caDB->where('cst_id ='.$cust_id );
        $this->caDB->select('*');
        $this->caDB->from('csi_large_customers');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->caDB->like($item, $_POST['search']['value']);
                }
                else{
                    $this->caDB->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->caDB->group_end(); //close bracket
            }
            $i++;
        }

        if(isset($_POST['order'])) // here order processing
        {
            $this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->caDB->order_by(key($order), $order[key($order)]);
        }
    }

    function get_large_customer_data_filtered($cust_id)
    {
        $this->_get_lg_cust_details($cust_id);

        $query = $this->caDB->get();
        $str = $this->caDB->last_query();
        return $query->num_rows();
    }

    public function delete_lg_cst() {
        $this->caDB->delete('csi_large_customers', $this->primary_key);
        $this->reset_pk();
        return true;
    }

    public function insert_customer() {
        $this->caDB->replace('csi_large_customers', $this->data);
        $this->reset();
        return true;
    }

	function report_modules($account_id,$configuration_id)
	{
		$data = array('id'=>$configuration_id,'account_id' => $account_id);
		$this->pdb->where($data);
		$this->pdb->select('configuration');
		$this->pdb->from('reporting_configuration_options');
		$result = $this->pdb->get();
		$row = $result->row();
		if($row) {
			return $row->configuration;
		}else{
			return NULL;
		}
	}

    private function _get_detailed_customers()
    {
        $this->column_search = array('a.cst_id','a.partition_id','a.account_id','a.account_username','a.account_email','cpi.db_client_host','last_login','cpi.csi_version','cpi.patch_version','csg.last_compile_time','csg.generate_asap','csg.time_elapsed','csg.in_progress','csg.last_started_at','csg.time_elapsed','csg.time_elapsed','locked');

	    $this->column_order = array('','a.cst_id','a.partition_id','a.account_id','a.account_username','a.account_email','cpi.db_client_host','last_login','cpi.csi_version','cpi.patch_version','csg.last_compile_time','csg.generate_asap','csg.time_elapsed','csg.in_progress','csg.last_started_at',null,'csg.time_elapsed','csg.time_elapsed','total_devices','locked');

        $this->order = array('csg.last_started_at' => 'desc'); // default order

        $str="a.account_expires > UTC_TIMESTAMP() and a.account_id = a.root_account_id and a.is_partition_admin=1 and cpi.locked = 0";
        $this->caDB->where($str, NULL, FALSE);

        $this->caDB->select($this->select_column);

        $this->caDB->from('csi_pdb_info as cpi');
		$this->caDB->join('csi_smartgroup_generation as csg', 'cpi.cst_id = csg.cst_id and cpi.partition_id = csg.partition_id', 'inner');
        $this->caDB->join('accounts as a', 'cpi.cst_id = a.cst_id and cpi.partition_id = a.partition_id', 'inner');

        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0){
                    $this->caDB->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
                    $this->caDB->like($item, $_POST['search']['value']);
                }
                else{
                    $this->caDB->or_like($item, $_POST['search']['value']);
                }

                if(count($this->column_search) - 1 == $i) //last loop
                    $this->caDB->group_end(); //close bracket
            }
            $i++;
        }
	    $this->caDB->having('total_devices > 0');
        if(isset($_POST['order'])) // here order processing
        {
            $this->caDB->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        }
        else if(isset($this->order))
        {
            $order = $this->order;
            $this->caDB->order_by(key($order), $order[key($order)]);
        }

    }

    function getCloudCustomerDetails()
    {
        $this->select_column = 'a.cst_id,
                                a.partition_id,
                                a.account_id,
                                a.account_email,
                                a.account_username,
                                cpi.db_client_host,
                                a.last_login,
                                cpi.csi_version,
                                cpi.patch_version,
                                csg.last_compile_time,
                                csg.generate_asap,
                                csg.time_elapsed,
                                csg.in_progress,
                                csg.last_started_at,
                                UTC_Timestamp() as Currenttime,
                                (csg.time_elapsed/60000) as compile_min,
                                (csg.time_elapsed/3600000) as compile_hr,
                                (select sum(Z.used_device_licenses) from accounts Z where Z.cst_id = a.cst_id and Z.partition_id =a.partition_id) as total_devices,
                                cpi.locked';
        $this->_get_detailed_customers();
        if (isset($_POST['length']) && $_POST['length'] != -1)
            $this->caDB->limit($_POST['length'], $_POST['start']);

        $query = $this->caDB->get();

        $str = $this->caDB->last_query();

        return $query->result();
    }

    function customerDetails_Filtered()
    {
        $this->_get_detailed_customers();

        $query = $this->caDB->get();

        $str = $this->caDB->last_query();

        return $query->num_rows();
    }

}
