<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Admin_Roles_Model extends CI_Model {

	private $table;
	public $primary_key;
	public $data;

	var $column_order = array(null, 'role_name'); //set column field database for datatable orderable
	var $column_search = array('role_name'); //set column field database for datatable searchable 
	var $order = array('role_name' => 'asc'); // default order

	
	function __construct() {
		parent::__construct();
		$this->table = substr(strtolower(get_class($this)), 0, -6);
		$this->primary_key = array();
		$this->date = array();
	}

	private function reset() {
		$this->primary_key = array();
		$this->data = array();
	}

	private function reset_pk() {
		$this->primary_key = array();
	}

	private function reset_data() {
		$this->data = array();
	}

	public function view_roles() {
		$this->db->where('status_ind','1');
		$query = $this->db->get($this->table);
		return $query->result();
	}

	public function insert() {
		$this->db->insert($this->table, $this->data);
		$this->reset_data();
		return true;
	}

	public function update() {
		$this->db->update($this->table, $this->data, $this->primary_key);
		$this->reset();
		return true;
	}

	public function delete() {
		$this->db->delete($this->table, $this->primary_key);
		$this->reset_pk();
		return true;
	}
	
	public function get_max_value($field) {
		$this->db->select_max($field);
		$query = $this->db->get($this->table);
		$row = $query->row();
		return $row->$field;
	}
	public function get_row() {
		$this->db->where($this->primary_key);
		$this->db->select('*');
		$this->db->from($this->table);
		$query = $this->db->get();
		$row = $query->row();
		return $row;
	}
	public function basicView() {
		$query = $this->db->get($this->table);
		return $query->result();
	}

	private function _get_users_query()
	{
		
		$this->db->from($this->table);
		$i = 0;
		foreach ($this->column_search as $item) {
			if($_POST['search']['value']) {
				if($i===0){
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->db->order_by(key($order), $order[key($order)]);
		}
	}

	function view()
	{
		$this->_get_users_query();
		if($_POST['length'] != -1)
		$this->db->limit($_POST['length'], $_POST['start']);
		$query = $this->db->get();
		
		//$str = $this->db->last_query();
   
		return $query->result();
	}

	function count_filtered()
	{
		$this->_get_users_query();
		$query = $this->db->get();
		return $query->num_rows();
	}

	public function count_all()
	{
		$this->db->from($this->table);
		return $this->db->count_all_results();
	}
}