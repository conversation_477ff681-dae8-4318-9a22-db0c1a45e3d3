<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

//  application/core/MY_Controller.php
class MY_Controller extends CI_Controller {

	public $preview = "";
	
	public function __construct() {
        parent::__construct();
		$this->load->helper('url');
		$this->load->model('admin_users_model');
        $this->load->model('admin_users_accesses_model');
		
         $user_id = $this->session->userdata('user_id');
        if (empty($user_id) || $this->session->userdata('user_id') != 1) {
            redirect('');
        } else {
            $_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
        }
	}
}