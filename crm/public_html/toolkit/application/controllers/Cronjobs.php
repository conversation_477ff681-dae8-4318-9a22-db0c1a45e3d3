<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>ronjobs extends CI_Controller {

	public $class_name;
	public $caDB;
	public $pdb;
	
	public function __construct() {
		parent::__construct();
		$this->class_name = strtolower(get_class());
		$this->load->model('admin_roles_accesses_model');
		$this->load->model('admin_users_accesses_model');
		$this->load->model('cronjobs_model');
		$this->load->model('reports_model');

		$this->caDB = $this->load->database('ca', TRUE);
		
		$user_id = $this->session->userdata('user_id');
		if (empty($user_id)) {
			redirect('');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
	}

	public function index() {

	}

	public function jobs() {
		$msg = array();
		$data['view'] = $this->class_name . '/all_jobs_list';
		$data['title'] = 'Jobs - ' . SITE_TITLE;
		$data['page_heading'] = 'Jobs';
		$data['breadcrumb'] = "Jobs";
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function getThreatScoreData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$threatScore = $this->cronjobs_model->getThreatScore();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($threatScore as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Processed_partitions;
			$row[] = $qdata->Latest_processed_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->threatscore_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getVpmJobsData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
				
		$vpmJobs = $this->cronjobs_model->getVpmJobs();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($vpmJobs as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Processed_partitions;
			$row[] = $qdata->Latest_processed_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->vpmjobs_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getLiveUpdatesData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$liveUpdate = $this->cronjobs_model->getLiveUpdates();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($liveUpdate as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Processed_partitions;
			$row[] = $qdata->Latest_processed_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->liveupdate_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getPatchTemplateData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$patchTemplate = $this->cronjobs_model->getPatchTemplatesJobs();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($patchTemplate as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Processed_partitions;
			$row[] = $qdata->Latest_processed_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->patchtemplate_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	public function smartgroupjobs(){
		
		$msg = array();
		$data['view'] = $this->class_name . '/all_smartgroup_list';
		$data['title'] = 'Smartgroup - ' . SITE_TITLE;
		$data['page_heading'] = 'Smartgroup';
		$data['breadcrumb'] = "Smartgroup";
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	
	public function getSmartgroupCompliedData(){
		
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$sgCompiled = $this->cronjobs_model->getSmartgroupComplied();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($sgCompiled as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Processed_partitions;
			$row[] = $qdata->Latest_compile_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->smartgroupcomplied_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	public function getSmartgroupProgressData(){
				
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
			
		$sgProgress = $this->cronjobs_model->getSmartgroupProgress();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($sgProgress as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->SCHEDULE;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->Currently_Compiling_partitions;
			$row[] = $qdata->last_started_at;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->smartgroupProgress_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getSmartgroupNotificationData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$sgNotificaiton = $this->cronjobs_model->getSmartgroupNotification();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($sgNotificaiton as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $qdata->Schedule;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->triggered_notifications_In_Last6Hours;
			$row[] = $qdata->last_notification;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->smartgroupnotificaiton_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	
	public function reports(){
		
		$msg = array();
		$data['view'] = $this->class_name . '/all_report_list';
		$data['title'] = 'Report Generation - ' . SITE_TITLE;
		$data['page_heading'] = 'Report Generation';
		$data['breadcrumb'] = "Report Generation";
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function getReportGeneratedData(){
		
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
				
		$reportGenerated = $this->cronjobs_model->getReportGenerated();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($reportGenerated as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = '<td><a title="Report Config" href="javascript:void(0);" onclick="getReportModules(\'' . $qdata->account_id . '\',\'' . $qdata->configuration_options_id . '\',\''.$qdata->cst_id.'\',\''.$qdata->partition_id.'\')">'.$qdata->id.'</a></td>';
			$row[] = $qdata->report_title;
			$row[] = $qdata->account_id;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->report_type;
			$row[] = $qdata->last_gen_date;
			$row[] = $qdata->report_submitted_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->reportgenerated_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getReportProgressData(){
				
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
				
		$reportProgress = $this->cronjobs_model->getReportProgress();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($reportProgress as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = '<td><a title="Report Config" href="javascript:void(0);" onclick="getReportModules(\'' . $qdata->account_id . '\',\'' . $qdata->configuration_options_id . '\',\''.$qdata->cst_id.'\',\''.$qdata->partition_id.'\')">'.$qdata->id.'</a></td>';
			$row[] = $qdata->report_title;
			$row[] = $qdata->account_id;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->report_type;
			$row[] = $qdata->last_gen_date;
			$row[] = $qdata->report_submitted_time;
			$row[] = '<td><a title="Regenerate" href="javascript:void(0);" onclick="reportRegenerate(\''.$qdata->account_id.'\',\''. $qdata->id.'\',\''.$qdata->cst_id.'\',\''.$qdata->partition_id.'\')"><button class="btn btn-success btn-xs"><i class="fa fa-recycle"></i></button></a></td>';
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->reportprogress_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);

		header('Content-Type: application/json');
		echo json_encode($output);
	}

	public function getRecurringReportProgressData()
	{
		if (isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;

		$reportProgress = $this->cronjobs_model->getRecurringReportProgress();

		$data = array();
		$no = $_POST['start'];
		foreach ($reportProgress as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = '<td><a title="Report Config" href="javascript:void(0);" onclick="getReportModules(\'' . $qdata->account_id . '\',\'' . $qdata->configuration_options_id . '\',\'' . $qdata->cst_id . '\',\'' . $qdata->partition_id . '\')">'.$qdata->id.'</a></td>';
			$row[] = $qdata->report_title;
			$row[] = $qdata->account_id;
			$row[] = $qdata->cst_id;
			$row[] = $this->functions->getReprecurrenceScheduleRenderer($qdata->recurrence_schedule, $qdata->one_time_gen);
			$row[] = $qdata->end_date;
			$row[] = $qdata->report_type;
			$row[] = $qdata->last_gen_date;
			$row[] = $qdata->report_submitted_time;
			$row[] = '<td><a title="Regenerate" href="javascript:void(0);" onclick="reportRegenerate(\'' . $qdata->account_id . '\',\'' . $qdata->id . '\',\'' . $qdata->cst_id . '\',\'' . $qdata->partition_id . '\')"><button class="btn btn-success btn-xs"><i class="fa fa-recycle"></i></button></a></td>';
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->recurringReportprogress_Filtered();
		$output = array(
			"draw" => $_POST['draw'],
			"recordsTotal" => $row_count,
			"recordsFiltered" => $row_count,
			"data" => $data,
		);

		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function getReportFailedData(){
		
		if(isset($_POST['custom_custid']) && !empty($_POST['custom_custid']))
			$_POST['search']['value'] = (int)$_POST['custom_custid'];//$cust_id;
		
		$reportFailed = $this->cronjobs_model->getReportFailed();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($reportFailed as $qdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = '<td><a title="Report Config" href="javascript:void(0);" onclick="getReportModules(\'' . $qdata->account_id . '\',\'' . $qdata->configuration_options_id . '\',\'' . $qdata->cst_id . '\',\'' . $qdata->partition_id . '\')">' . $qdata->id . '</a></td>';
			$row[] = $qdata->report_title;
			$row[] = $qdata->account_id;
			$row[] = $qdata->cst_id;
			$row[] = $qdata->report_type;
			$row[] = $qdata->Latest_processed_time;
			$data[] = $row;
		}
		$row_count = $this->cronjobs_model->reportfailed_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}

	public function get_report_modules()
	{
		$account_id = $_POST['account_id'];
		$configuration_id = $_POST['configuration_id'];
		$cst_id = $_POST['cst_id'];
		$partition_id = $_POST['partition_id'];
		$this->pdb      = $this->functions->pdbConnection($cst_id,$partition_id);
		$customerReport = $this->reports_model->report_modules( $account_id,$configuration_id);
		$this->pdb->close();
		$customerReport = explode("|", $customerReport);
		$readable_values = array('CSI_MISC:SHOW;MISC_NONE' => 'Publish Report Parameters'
		, 'CSI_EXEC:YES' => 'Executive Summary Report'
		, 'CSI_DASH' => 'Dashboard Profile Id'
		, 'CSI_SITES' => 'Site Level Statistics'
		, 'SITES' => 'Selected Sites'
		, 'ALL' => 'All sites for all selected users'
		, 'OVR' => 'Overall Summary Statistics'
		, 'CRIT' => 'Overall Criticality Statistics'
		, 'IMP' => 'Overall Impact Statistics'
		, 'AV' => 'Overall Attack Vendor Statistics'
		, 'BSP' => 'By-Site Statistics on Secure Products'
		, 'BSI' => 'By-Site Statistics on Insecure Products'
		, 'BSE' => 'By-Site Statistics on EOL Products'
		, 'DET' => 'Detail Site specific Data'
		, 'CSI_HOSTS' => 'Host Level Statistics'
		, 'CSI_PRODUCTS' => 'Product Level Statistics'
		, 'HOSSG' => 'Host Smartgroup Id'
		, 'PRSG' => 'Product Smartgroup Id'
		, 'INS:0' => 'Insecure Installations : Show All'
		, 'INS:1' => 'Insecure Installations : Not Critical'
		, 'INS:2' => 'Insecure Installations : Less Critical'
		, 'INS:3' => 'Insecure Installations : Moderately Critical'
		, 'INS:4' => 'Insecure Installations : Highly Critical'
		, 'INS:5' => 'Insecure Installations : Extremely Critical'
		, 'EOL' => 'End of Life Installations'
		, 'PAT' => 'Secure Installations'
		, ';' => ', '
		);
		foreach ($customerReport as $key_report => $report) {

			foreach ($readable_values as $key => $val) {
				if (strpos($report, $key) !== FALSE) {
					$report = str_replace($key, $val, $report);
				}
			}
			$customerReport[$key_report] = $report;

		}

		echo json_encode($customerReport);
	}

}