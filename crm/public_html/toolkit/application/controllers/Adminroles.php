<?php

if (!defined('BASEPATH'))
	exit('No direct script access allowed');

class Adminroles extends CI_Controller {

	public $class_name;
	
	public function __construct() {
		parent::__construct();
		$this->class_name = strtolower(get_class());
		$this->load->model('admin_menuitems_model');
		$this->load->model('admin_roles_model');
		$this->load->model('admin_roles_accesses_model');
		$this->load->model('admin_users_accesses_model');
		$user_id = $this->session->userdata('user_id');
		if (empty($user_id)) {
			redirect('');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
	}

	public function access($role_id = 1) {
		$accesses = array();
		$acc_query = $this->admin_menuitems_model->view();
		$data['admin_roles'] = $this->admin_roles_model->basicView();
		$acc_data = array();
		foreach ($acc_query as $acc) {
			if(!empty($acc->parent_menuitem_id)){
				$acc_data[$acc->parent_menuitem_id]['child'][] = $acc;
			} else {
				$acc_data[$acc->menuitem_id]['parent'] = $acc;
			}
		}
		
		$data['query'] = $acc_data;
		$roles_accesses = $this->admin_roles_accesses_model->view($role_id);
		foreach ($roles_accesses as $row) {
			$accesses[] = $row->menuitem_id;
		}
		$data['role_id'] = $role_id;
		$data['admin_roles_accesses'] = $accesses;
		$data['view'] = 'adminroles/access-form';
		$data['title'] = 'Administrator Dashboard -'.SITE_TITLE;
		$data['page_heading'] = 'Admin Roles';
		$data['scripts'] = array('assets/javascript/adminusers.js');
		$this->load->view('templates/dashboard', $data);
	}
  
	public function index() {
		$data['view'] = 'adminroles/list';
		$data['title'] = 'Administrator Dashboard - '.SITE_TITLE;
		$data['breadcrumb'] = $data['page_heading'] = 'Admin Roles List';
		$data['scripts'] = array('assets/js/common.js', 'assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function add() {
		$data['view'] = 'adminroles/form';
		$data['title'] = 'Administrator Dashboard - '.SITE_TITLE;
		$data['page_heading'] = 'Add Admin Roles';
		$data['scripts'] = array('assets/javascript/adminroles.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function edit($role_id) {
		$this->admin_roles_model->primary_key = array('role_id' => $role_id);
		$data['query'] = $this->admin_roles_model->get_row();
		$data['view'] = 'adminroles/form';
		$data['title'] = 'Administrator Dashboard - '.SITE_TITLE;
		$data['page_heading'] = 'Edit Admin Roles';
		$data['scripts'] = array('assets/javascript/adminroles.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function save() {
		

		$admin_role_id = $this->input->post('role_id'); 
		$this->admin_roles_model->data = $this->input->post();
		if (!empty($admin_role_id)) {
			$this->admin_roles_model->data['modified_date'] = date('Y-m-d : H:i:s');
			$this->admin_roles_model->data['modified_by'] = $this->session->userdata('user_id');
			$this->admin_roles_model->primary_key = array('role_id' => $admin_role_id);
			if ($this->admin_roles_model->update()) {
				$msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Updated Successfully');
			} else {
				$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Update Record.');
			}
		} else {
			$this->admin_roles_model->data['created_date'] = date('Y-m-d : H:i:s');
			$this->admin_roles_model->data['created_by'] = $this->session->userdata('user_id');
			$this->admin_roles_model->data['modified_by'] = $this->session->userdata('user_id');
			
			if ($this->admin_roles_model->insert()) {
				$msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Added Successfully');
			} else {
				$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Add Record.');
			}
		}

		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	public function delete($role_id) {
		$msg = array();
		$this->admin_roles_model->primary_key = array('role_id' => $role_id);
		if ($this->admin_roles_model->delete()) {
			$msg = array('type' => 'success', 'icon' => 'icon-ok green', 'txt' => 'Record deleted successfully');
		} else {
			$msg = array('type' => 'error', 'icon' => 'icon-remove red', 'txt' => 'Sorry! Unable to Delete.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	public function update() {
		$status = true;
		$role_id = $this->input->post('role_id');
		$this->admin_roles_accesses_model->primary_key = array('role_id' => $role_id);
		if ($this->admin_roles_accesses_model->delete()) {
			foreach ($this->input->post('menuitem_id') as $menuitem_id) {
				$this->admin_roles_accesses_model->data = array('menuitem_id' => $menuitem_id, 'role_id' => $role_id);
				if ($this->admin_roles_accesses_model->insert()) {
					$status = TRUE;
				}
			}
		}

		if ($status) {
			$msg = array('type' => 'success', 'icon' => 'icon-ok green', 'txt' => 'Save Changes Updated Successfully');
		} else {
			$msg = array('type' => 'error', 'icon' => 'icon-remove red', 'txt' => 'Sorry! Unable to Delete.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	
	public function getApiData(){
		$userData = $this->admin_roles_model->view();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($userData as $adminroles) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $adminroles->role_name;
			$row[] = $this->functions->getStatus($adminroles->status_ind);
			$row[] = $this->functions->getLinks($adminroles->role_id,$this->class_name,true,true,true);
			$data[] = $row;
		}

		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $this->admin_roles_model->count_all(),
						"recordsFiltered" => $this->admin_roles_model->count_filtered(),
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
}