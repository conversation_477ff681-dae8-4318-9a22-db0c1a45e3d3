<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Adminusers extends CI_Controller {

	public $class_name;

	public function __construct() {
		parent::__construct();
		$this->class_name = strtolower(get_class());
		/* these are the default modules to load in all controller */
		$this->load->model('admin_roles_model');
		$this->load->model('admin_users_model');
		$this->load->model('admin_menuitems_model');
		$this->load->model('admin_users_accesses_model');
		$this->load->model('admin_roles_accesses_model');

		$user_id = $this->session->userdata('user_id');
		$this->admin_users_model->primary_key = array('user_id' => $user_id);
		$user_session_id = $this->admin_users_model->session_id();
		if (empty($user_id) && $this->session->userdata['logged_session_id'] != md5($user_session_id) || !$this->admin_users_accesses_model->is_allowed($user_id, 1)) {
			redirect('logout');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
		$permissions = $this->admin_users_accesses_model->get_permisions($user_id, 1);
		$this->permission = array($permissions->add_permission, $permissions->edit_permission, $permissions->delete_permission);

	}

	public function index() {
		$msg = array();
		$data['view'] = $this->class_name . '/users_list';
		//$data['query'] = $this->admin_users_model->view();
		$data['title'] = 'Admin User Page - ' . SITE_TITLE;
		$data['page_heading'] = 'Admin Users List';
		$data['breadcrumb'] = "Users List";
		$data['scripts'] = array('assets/js/common.js', 'assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function add() {
		if ($this->permission[0] > 0) {
			$msg = array();
			$data['roles'] = $this->admin_roles_model->view_roles();
			$data['view'] = $this->class_name . '/form';
			$data['title'] = 'Add New User - ' . SITE_TITLE;
			$data['breadcrumb'] = "<a href=$this->class_name>Users List</a> &nbsp;&nbsp; > &nbsp;&nbsp; Add New User";
			$data['page_heading'] = 'Add New User';
			$this->load->view('templates/dashboard', $data);
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
			$this->session->set_flashdata('msg', $msg);
			redirect("/$this->class_name/");
		}
	}

	public function edit($admin_user_id) {
		if ($this->permission[1] > 0) {
			if (!empty($admin_user_id)) {
				$this->admin_users_model->primary_key = array('user_id' => $admin_user_id);
				$data['query'] = $this->admin_users_model->get_row();
				$data['roles'] = $this->admin_roles_model->view_roles();
				$data['view'] = $this->class_name . '/form';
				$data['title'] = 'Edit User Profile - ' . SITE_TITLE;
				$data['breadcrumb'] = "<a href=$this->class_name>Users List</a> &nbsp;&nbsp; > &nbsp;&nbsp; Edit User";
				$data['page_heading'] = 'Edit User';
				$this->load->view('templates/dashboard', $data);
			}
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
			$this->session->set_flashdata('msg', $msg);
			redirect("/$this->class_name/");
		}
	}

	public function save() {

		if (!empty($_POST) && ($this->permission[0] > 0 || $this->permission[1] > 0)) {
		    $username = $this->input->post('username');
		    $check_username = $this->admin_users_model->check_username_exist($username);
		    if(empty($check_username)) {
		        $admin_user_id = $this->input->post('user_id');
		        $this->admin_users_model->data = $this->input->post();
		        if (!empty($admin_user_id)) {
		            $pass = $this->input->post('password');
		            $hash = password_hash($pass, PASSWORD_BCRYPT);
		            $this->admin_users_model->data['password'] = $hash;
		            $this->admin_users_model->data['modified_date'] = date('Y-m-d : H:i:s');
		            $this->admin_users_model->data['modified_by'] = $this->session->userdata('user_id');
		            $this->admin_users_model->primary_key = array('user_id' => $admin_user_id);
		            if ($this->admin_users_model->update()) {
		                $msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Updated Successfully');
		            } else {
		                $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Update Record.');
		            }
		        } else {
		            $pass = $this->input->post('password');
		            $hash = password_hash($pass, PASSWORD_BCRYPT);
		            $this->admin_users_model->data['password'] = $hash;
		            $this->admin_users_model->data['created_date'] = date('Y-m-d : H:i:s');
		            $this->admin_users_model->data['created_by'] = $this->session->userdata('user_id');

		            if ($this->admin_users_model->insert()) {
		                $msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Added Successfully');
		            } else {
		                $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Add Record.');
		            }
		        }
		    }else{
		        $msg = array();
		        $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! The entered Username is already taken.Please try with other Username.');
		    }
		} else {
		    $msg = array();
		    $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	public function delete($user_id) {
		if (!empty($user_id) && $this->permission[2] > 0) {
			$this->admin_users_model->primary_key = array('user_id' => $user_id);
			if ($this->admin_users_model->delete()) {
				$msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Deleted Successfully');
			} else {
				$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Add Record.');
			}
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	public function access($user_id) {
		if (!empty($user_id) && ($this->session->userdata('role_id') == 1)) {
			$accesses = array();
			$data['query'] = $this->admin_menuitems_model->view();
			$roles_accesses = $this->admin_users_accesses_model->view($user_id);
			foreach ($roles_accesses as $row) {
				$accesses[] = $row->menuitem_id;
			}
			$data['user_id'] = $user_id;
			$data['admin_users_accesses'] = $accesses;
			$data['view'] = $this->class_name . '/accessform';
			$data['title'] = 'User Access - ' . SITE_TITLE;
			$data['page_heading'] = 'User Access';
			$data['breadcrumb'] = "User Access";
			$data['scripts'] = array('javascripts/' . $this->class_name . '.js', 'javascripts/dashboard.js');
			$this->load->view('templates/dashboard', $data);
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
			$this->session->set_flashdata('msg', $msg);
			redirect("/$this->class_name/");
		}
	}

	public function permission($user_id) {
		if (!empty($user_id) && ($this->session->userdata('role_id') == 1)) {
			$accesses = array();
			$roles_accesses = $this->admin_users_accesses_model->view_access($user_id);
			foreach ($roles_accesses as $row) {
				$accesses[] = $row->menuitem_id;
			}
			$data['user_id'] = $user_id;
			$data['query'] = $roles_accesses; //$_SESSION['sidebar_menuitems'];
			$data['view'] = $this->class_name . '/permissionform';
			$data['title'] = 'User Access - ' . SITE_TITLE;
			$data['page_heading'] = 'User Access';
			$data['breadcrumb'] = "User Access";
			$data['scripts'] = array('javascripts/' . $this->class_name . '.js', 'javascripts/dashboard.js');
			$this->load->view('templates/dashboard', $data);
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
			$this->session->set_flashdata('msg', $msg);
			redirect("/$this->class_name/");
		}
	}

	public function saveaccess() {
		if ($this->session->userdata('role_id') == 1 ) {
			$status = true;
			$user_id = $this->input->post('user_id');
			$this->admin_users_accesses_model->primary_key = array('user_id' => $user_id);
			if ($this->admin_users_accesses_model->delete()) {
				$menuitem_ids = $this->input->post('menuitem_id');
				foreach ($menuitem_ids as $menuitem_id) {
					$this->admin_users_accesses_model->data = array('menuitem_id' => $menuitem_id, 'user_id' => $user_id);
					if ($this->admin_users_accesses_model->insert()) {
						$status = true;
					}
				}
			}

			if ($status) {
				$msg = array('type' => 'success', 'icon' => 'icon-ok green', 'txt' => 'Save Changes Updated Successfully');
			} else {
				$msg = array('type' => 'error', 'icon' => 'icon-remove red', 'txt' => 'Sorry! Unable to Delete.');
			}
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	public function savepermission() {
		
		if ($this->session->userdata('role_id') == 1) {
			$status = true;
			$user_id = $this->input->post('user_id');
			$menuitem_ids = $this->input->post('menuitem_id');
			$i = 0;
			foreach ($menuitem_ids as $menuitem_id) {
				$add_permission = $this->input->post('add_permission');
				if (!empty($add_permission[$i])) {
					$add_permission = $add_permission[$i];
				} else {
					$add_permission = 0;
				}
				if (!empty($this->input->post('edit_permission')[$i])) {
					$edit_permission = $this->input->post('edit_permission')[$i];
				} else {
					$edit_permission = 0;
				}
				if (!empty($this->input->post('delete_permission')[$i])) {
					$delete_permission = $this->input->post('delete_permission')[$i];
				} else {
					$delete_permission = 0;
				}
				$this->admin_users_accesses_model->data = array('add_permission' => $add_permission, 'edit_permission' => $edit_permission, 'delete_permission' => $delete_permission);
				$this->admin_users_accesses_model->primary_key = array('menuitem_id' => $menuitem_id);
				$this->admin_users_accesses_model->primary_key = array('user_id' => $user_id);
				if ($this->admin_users_accesses_model->update()) {
					$status = true;
				}
				$i++;
			}
			if ($status) {
				$msg = array('type' => 'success', 'icon' => 'icon-ok green', 'txt' => 'Save Changes Updated Successfully');
			} else {
				$msg = array('type' => 'error', 'icon' => 'icon-remove red', 'txt' => 'Sorry! Unable to Delete.');
			}
		} else {
			$msg = array();
			$msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! You do not have the permission.');
		}
		$this->session->set_flashdata('msg', $msg);
		redirect("/$this->class_name/");
	}

	
	public function getApiData(){
		$userData = $this->admin_users_model->view();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($userData as $adminuser) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $adminuser->first_name;
			$row[] = $adminuser->last_name;
			$row[] = $adminuser->email;
			$row[] = $adminuser->role_name;
			$row[] = $adminuser->username;
			if ($adminuser->modified_date == "") {
				$row[] = $adminuser -> created_date;
			} else { 
				$row[] = $adminuser->modified_date;
			}
			
			$row[] = $this->functions->getStatus($adminuser->status_ind);
			$row[] = $this->functions->getLinks($adminuser->user_id,$this->class_name,true,true);
			$data[] = $row;
		}

		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $this->admin_users_model->count_all(),
						"recordsFiltered" => $this->admin_users_model->count_filtered(),
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
}