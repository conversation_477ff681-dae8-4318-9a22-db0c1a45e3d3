<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Index extends CI_Controller {

	public $caDB;
	
	public function __construct() {
		parent::__construct();

		$this->load->model('admin_roles_accesses_model');
		$this->load->model('admin_users_accesses_model');
		$this->load->model('admin_users_model');
		$this->caDB = $this->load->database('ca', TRUE);
		
	}

	public function index() {
		$msg = array();
		$user_id = $this->session->userdata('user_id');
		if (!empty($user_id)) {
			redirect('dashboard');
		}
		
		$this->form_validation->set_rules('username', 'Username', 'trim|required');
		$this->form_validation->set_rules('password', 'Password', 'trim|required');
		
		if ($this->form_validation->run() == true) {
			$login_detail = (array) $this->admin_users_model->login($this->input->post());
			if (!empty($login_detail) && $login_detail > 0) {
				unset($login_detail['user_session_id']);
				$user_session_id = rand('2659748135965', '088986555510245579');
				$this->admin_users_model->data['user_session_id'] = $user_session_id;
				$login_detail['logged_session_id'] = md5($user_session_id);
				$this->session->set_userdata($login_detail);
				$this->admin_users_model->primary_key = array('user_id' => $this->session->userdata('user_id'));
				$this->admin_users_model->update();
				redirect('dashboard');
			} else {
				$msg = array('txt' => 'Invalid Username or Password');
				$this->session->set_flashdata('msg', $msg);
				redirect('');
			}
		}
		$data['view'] = 'index/index';
		$data['title'] = 'Login Page - ' . SITE_TITLE;
		$this->load->view('templates/default', $data);
	}

	public function dashboard() {
		
		$user_id = $this->session->userdata('user_id');
		$this->admin_users_model->primary_key = array('user_id' => $user_id);
		$user_session_id = $this->admin_users_model->session_id();
		if (empty($user_id) && $this->session->userdata['logged_session_id'] != md5($user_session_id)) {
			redirect('');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
		
		$data['view'] = 'dashboard/index';
		$data['title'] = 'Dashboard';
		$data['page_heading'] = 'Dashboard';
		$data['sub_heading'] = '';
		$data['breadcrumb'] = "Dashboard";
		//$data['scripts'] = array('javascripts/dashboard.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function onPremdashboard() {
		$this->load->model('customer_model');
		$this->load->model('reports_model');
		
		$this->caDB = $this->load->database('ca', TRUE); 
		
		$customerReport = $this->customer_model->getOnpremCustomer();
		$this->session->set_userdata($customerReport);
		
		$user_id = $this->session->userdata('user_id');
		$this->admin_users_model->primary_key = array('user_id' => $user_id);
		$user_session_id = $this->admin_users_model->session_id();
		if (empty($user_id) && $this->session->userdata['logged_session_id'] != md5($user_session_id)) {
			redirect('');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
		
		
		$data['cust_id'] = $cust_id = $this->session->userdata('cst_id');
		//$this->pdb = $this->functions->pdbConnection($cust_id);
		
		if(!$this->customer_model->cust_check($cust_id)){
			redirect('');
		}
		
		$partition_details = $this->customer_model->getPartitions($cust_id);
		$data['response']['total_partition'] = $partition_details->num_rows();
		$data['response']['partition'] = $all_partition = $partition_details->result();
		
		$data['response']['other_details'] = $this->customer_model->getOtherDetails($cust_id);
		
		$data['view'] = 'dashboard/onprem';
		$data['title'] = 'Dashboard';
		$data['page_heading'] = 'Dashboard';
		$data['sub_heading'] = '';
		$data['breadcrumb'] = "Dashboard";
		$data['scripts'] = array('assets/javascript/dashboard.js');
		$this->load->view('templates/dashboard', $data);
	}
	public function logout() {
		$this->session->sess_destroy();
	   // session_destroy();
		redirect('');
	}

	public function check_db_exist(){
        $customer_id  =   $this->input->post('cst_id');
        $partition_id    =   $this->input->post('partition_id');
        $this->pdb = $this->functions->pdbConnection($customer_id,$partition_id);
        echo json_encode($this->pdb);
	}

	public function change_password(){
        $check_password = $this->admin_users_model->change_user_password();
        echo json_encode($check_password);
    }
}
