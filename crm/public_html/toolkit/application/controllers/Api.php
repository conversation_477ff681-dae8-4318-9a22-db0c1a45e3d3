<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class api extends CI_Controller {

	public $class_name;
	public $caDB;
	public $pdb;
	
	public $whitelist = array(
		SMARTGROUP_TYPE_HOST => array(
			'allowed' => array(
				'id'
				,'name'
				,'description'
				,'IF(generate_asap=1,2,in_progress) as in_progress'
				,'compiled_time'
				,'hosts'
				,'(num_eol+num_insecure+num_patched) as num_installations'
				,'generate_asap'
				,'average_score'
				,'compile_started'
			)
			,'searchable' => array(
				'name'
				,'description'
				,'generate_asap'
				,'compiled_time'
				,'average_score'
				,'hosts'
			)
			,'orderable' => array(
				'name'
				,'name'
				,'description'
				,'generate_asap'
				,'compiled_time'
				,'hosts'
				,'num_installations'
				,'hosts'
				,'average_score'
				,'num_installations'
			)
		)
		,SMARTGROUP_TYPE_PRODUCT => array(
			// The base listing is for the overview page
			'allowed' => array(
				'id'
				,'name'
				,'description'
				,'generate_asap'
				,'compiled_time'
				,'num_products'
				,'num_installations'
				,'num_hosts'
				,'business_impact'
				,'custom_columns'
				,'all_custom_columns'
				,'date_modified'
				,'logic_type'
				,'IF(generate_asap=1,2,in_progress) as in_progress'
				,'editable'
				,'compile_started'
			)
			,'searchable' => array(
				'id'
				,'name'
				,'editable'
				,'description'
				,'logic_type'
				,'business_impact'
				,'custom_columns'
				,'all_custom_columns'
				,'num_products'
				,'num_installations'
				,'num_hosts'
				,'date_modified'
				,'compiled_time'
				,'generate_asap'
			)
			,'orderable' => array(
				'name'
				,'business_impact'
				,'num_products'
				,'num_installations'
				,'num_hosts'
				,'date_modified'
				,'compiled_time'
				,'in_progress'
			)
		),REPORT_CONFIGURATION=> array(
            // The base listing is for the overview page
            'allowed' => array(
                'id'
            ,'account_id'
            ,'report_title'
            ,'end_date'
            ,'recurrence_schedule'
            ,'last_gen_date'
            ,'modified_date'
            ,'process_status'
            ,'one_time_gen'
            ,'filename'
            ,'recipients'
            ,'time_frame'
            ,'no_email'
            ,'source'
            ,'report_format'
            ,'configuration_options_id'
            ,'debug_enable'
             )
        ,'searchable' => array(
                'id'
            ,'one_time_gen'
            ,'report_title'
            ,'filename'
            ,'end_date'
            ,'recurrence_schedule'
            ,'recipients'
            ,'time_frame'
            ,'last_gen_date'
            ,'modified_date'
            ,'process_status'
            ,'no_email'
            ,'source'
            ,'report_format'
            ,'configuration_options_id'
            )
        ,'orderable' => array(
                'report_title'
            ,'end_date'
            ,'recurrence_schedule'
            ,'last_gen_date'
            ,'modified_date'
            ,'date_modified'
            ,'process_status'
           )
        ),CSI_CONFIGURATION=> array(
            // The base listing is for the csi_configuration page
            'allowed' => array(
                'account_id'
            ,'option_name'
            ,'option_value'
            )
        ,'searchable' => array(
                'account_id'
            ,'option_name'
            ,'option_value'
            )
        ,'orderable' => array(
                'account_id'
            ,'option_name'
            ,'option_value'
           )
        )

	 );
	 
	public function __construct() {
		parent::__construct();
		$this->load->model('customer_model');
		$this->caDB = $this->load->database('ca', TRUE);
	}

	public function index() {

	}

	public function overview($smartGroupType,$cust_id,$accountId,$partition_id) {
		
		if(!$this->customer_model->cust_check($cust_id)){
			$response['data'] = 'No Data';
			$response['status'] = false;
			header('Content-Type: application/json');
			echo json_encode($response);
		}
		
		$this->pdb = $this->functions->pdbConnection($cust_id,$partition_id);
		
		if ( 'hostsmartgroup' == $smartGroupType ) {
			$smartGroupType = SMARTGROUP_TYPE_HOST;
		} else if ( 'productsmartgroup' == $smartGroupType ) {
			$smartGroupType = SMARTGROUP_TYPE_PRODUCT;
		}else if ( 'reportconfiguration' == $smartGroupType ) {
            $smartGroupType = REPORT_CONFIGURATION;
        }
		
		$columns = $this->whitelist[$smartGroupType]['allowed'];
		$searchable = $this->whitelist[$smartGroupType]['searchable'];
		$orderable = $this->whitelist[$smartGroupType]['orderable'];

		$where = array();
		$order = array();
		$limit = array();

		$filter = array(
				'columns'=>$columns
				,'searchable'=>$searchable
				,'orderable'=>$orderable

				);
		// Add to the existing where array in case we're searching/filtering
		$where['csi_smartgroups.account_id'] = $accountId;
		$where['csi_smartgroups.type'] = $smartGroupType;
		
		
		$this->customer_model->primary_key = $where;

		switch($smartGroupType){
			case SMARTGROUP_TYPE_HOST:
				$sgresult = $this->customer_model->getHostSmartgroup($filter);
				$type = 'host';
				$formatData = array();
				$no = $_POST['start'];
				foreach ($sgresult as $sgdata) {
					$no++;
					$row = array();
					$row[] = $no;
					$row[] = $sgdata->name;
					$row[] = $sgdata->description;
					$compilation = 'Complete';
					if($sgdata->generate_asap){
						$compilation = 'Queued for compilation';
					} else if($sgdata->in_progress){
						$compilation = 'Recompiling....';
					}
					$row[] = $compilation;
					$row[] = $sgdata->compiled_time;
					$row[] = $sgdata->hosts;
					$row[] = $sgdata->num_installations;
					$compile_started = strtotime($sgdata->compile_started);
					$date_utc = new \DateTime("now", new \DateTimeZone("UTC"));
					$date =  $date_utc->format('Y-m-d H:i:s');
					$current_time = strtotime($date);
					$interval = abs($compile_started - $current_time);
					    $recomp = '<td><a title="Recompile" href="javascript:void(0);" onclick="smartGroupRecompile(\''.$cust_id.'\',\''.$sgdata->id.'\',\''.$accountId.'\',\''.$partition_id.'\',\''.$type.'\')"><button class="btn btn-success btn-xs"><i class="fa fa-recycle"></i></button></a></td>';
					$row[] = $recomp;
					$formatData[] = $row;
				}
				$row_count = $this->customer_model->hostSmartgroup_Filtered();
				
			break;
			case SMARTGROUP_TYPE_PRODUCT:
				$sgresult = $this->customer_model->getProductSmartgroup($filter);
				$type = 'product';
				$formatData = array();
				$no = $_POST['start'];
				foreach ($sgresult as $sgdata) {
					$no++;
					$row = array();
					$row[] = $no;
					$row[] = $sgdata->name;
					$row[] = $sgdata->description;
					$compilation = 'Complete';
					if($sgdata->generate_asap){
						$compilation = 'Queued for compilation';
					} else if($sgdata->in_progress){
						$compilation = 'Recompiling....';
					}
					$row[] = $compilation;
					$row[] = $sgdata->compiled_time;
					$row[] = $sgdata->num_products;
					$row[] = $sgdata->num_installations;
					$row[] = $sgdata->num_hosts;
					$compile_started_product = strtotime($sgdata->compile_started);
					$date_utc = new \DateTime("now", new \DateTimeZone("UTC"));
					$date =  $date_utc->format('Y-m-d H:i:s');
					$current_time = strtotime($date);
					$interval_p = abs($compile_started_product - $current_time);
					    $recomp = '<td><a title="Recompile" href="javascript:void(0);" onclick="smartGroupRecompile(\''.$cust_id.'\',\''.$sgdata->id.'\',\''.$accountId.'\',\''.$partition_id.'\',\''.$type.'\')"><button class="btn btn-success btn-xs"><i class="fa fa-recycle"></i></button></a></td>';
					$row[] = $recomp;
					$formatData[] = $row;
				}
				$row_count = $this->customer_model->productSmartgroup_Filtered();
				
			break;
            }
		
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $formatData,
				);
				
		header('Content-Type: application/json');
		echo json_encode($output);
		
	}


    public function reportconfiguration($customer_id,$accountId,$partition_id) {


        $columns = $this->whitelist[4]['allowed'];
        $searchable = $this->whitelist[4]['searchable'];
        $where = array();
        $where['enhanced_reporting_schedule.account_id'] = $accountId;
        $where['enhanced_reporting_schedule.source'] = 'csi';
        $this->customer_model->primary_key = $where;
        $order = array();
        $limit = array();

        $filter = array(
            'columns'=>$columns
        ,'searchable'=>$searchable
        );

        $sgresult = $this->customer_model->getreportconfiguration($filter);
        $formatData = array();
        $no = $_POST['start'];
        foreach ($sgresult as $sgdata) {
            $no++;
            $row = array();
            $row[] = $id = $sgdata->id;
            $row[] = $sgdata->report_title;
            $row[] = $sgdata->account_id;
            $row[] = $sgdata->end_date;
            $row[] = $this->functions->getReprecurrenceScheduleRenderer($sgdata->recurrence_schedule,$sgdata->one_time_gen);
            $row[] = $sgdata->last_gen_date;
            $row[] = $sgdata->modified_date;
            $process_stat = $sgdata->process_status;
            $process_status = $this->functions->getReportStatus($process_stat);
            $row[] = $process_status;
            $row[] = '<td><a title="Regenerate" href="javascript:void(0);" onclick="reportRegenerate(\''.$accountId.'\',\''.$sgdata->id.'\',\''.$customer_id.'\',\''.$partition_id.'\')"><button class="btn btn-success btn-xs"><i class="fa fa-recycle"></i></button></a></td>';
            $debug_enable =  $sgdata->debug_enable;
            $debug_enable_onoff = ($debug_enable == 1) ? 'On':'Off';
            $row[] = $debug_enable_onoff;
            $row[] = '<td><a title="Change Debug Status" href="javascript:void(0);" onclick="reportDebugEnable(\''.$accountId.'\',\''.$sgdata->id.'\',\''.$customer_id.'\',\''.$partition_id.'\',\''.$debug_enable.'\')"><button class="btn btn-default btn-xs"><i class="fa fa-toggle-on"></i></button></a></td>';
            $row[] = '<td ><a title="Configuration Values" href="javascript:void(0);" onclick="getreportconfig(\''.$accountId.'\',\''.$customer_id.'\',\''.$partition_id.'\',\''.$sgdata->configuration_options_id.'\')"><button class="btn btn-default btn-xs"><i class="fa fa-info-circle"></i></button></a></td>';
            $formatData[] = $row;
        }
        $row_count = $this->customer_model->getreportconfiguration_filtered();

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $row_count,
            "recordsFiltered" => $row_count,
            "data" => $formatData,
        );

        header('Content-Type: application/json');
        echo json_encode($output);

    }

    public function csi_configuration($customer_id,$accountId,$partition_id) {

        $this->pdb = $this->functions->pdbConnection($customer_id,$partition_id);
        $this->csipdb = $this->functions->pdbConnection($customer_id,$partition_id);
        $columns = $this->whitelist[5]['allowed'];
        $searchable = $this->whitelist[5]['searchable'];
        $where = array();
       // $where['enhanced_reporting_schedule.account_id'] = $accountId;

        $this->customer_model->primary_key = $where;
        $order = array();
        $limit = array();

        $filter = array(
            'columns'=>$columns
        ,'searchable'=>$searchable
        );

        $sgresult = $this->customer_model->get_csi_configuration($filter);
        $formatData = array();
        $no = $_POST['start'];
        foreach ($sgresult as $sgdata) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $sgdata->account_id;
            $row[] = $sgdata->option_name;
            $row[] = $sgdata->option_value;
            $formatData[] = $row;
        }
        $row_count = $this->customer_model->get_csi_configuration_filtered();

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $row_count,
            "recordsFiltered" => $row_count,
            "data" => $formatData,
        );

        header('Content-Type: application/json');
        echo json_encode($output);

    }

    public function update_csi_configuration(){

        $option_value =   $_POST['option_value'];
        $option_name =   $_POST['option_name'];
        $account_id =   $_POST['account_id'];
        $customer_id =   $_POST['customer_id'];
        $partition_id =   $_POST['partition_id'];

        $this->pdb = $this->functions->pdbConnection($customer_id,$partition_id);
        $customerReport = $this->customer_model->update_configuration($account_id,$option_value,$option_name);
        echo json_encode($option_value);
    }

}