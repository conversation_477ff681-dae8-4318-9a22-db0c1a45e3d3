<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Reports extends CI_Controller {

	public $class_name;
	public $caDB;
	public $pdb;
	public $crm;
	
	public function __construct() {
		parent::__construct();
		$this->class_name = strtolower(get_class());
		$this->load->model('admin_roles_accesses_model');
		$this->load->model('admin_roles_model');
		$this->load->model('admin_users_accesses_model');
		$this->load->model('reports_model');		
		$this->load->model('customer_model');
		
		$this->caDB = $this->load->database('ca', TRUE); 
		$this->crm = $this->load->database('crm', TRUE);
		$this->vuln_track = $this->load->database('vuln_track', TRUE);


        $user_id = $this->session->userdata('user_id');
		if (empty($user_id)) {
			redirect('');
		} else {
			$side_menu_users = $this->admin_users_accesses_model->get_user_access($this->session->userdata('user_id'));
			$side_menu_roles = $this->admin_roles_accesses_model->get_role_access($this->session->userdata('role_id'));
			
			$_SESSION['sidebar_menuitems'] = (!empty($_SESSION['sidebar_menuitems'])) ? $_SESSION['sidebar_menuitems'] : $side_menu_roles;
		}
	}

	public function index() {
		echo "in";exit;
	}
	
	
	
	public function access($cust_data) {
		
		$cust_details = explode('-',$cust_data);
		if(!is_array($cust_details) || empty($cust_details)){
			redirect('');
		}
		
		$action_function = $cust_details[0];
		$cust_id = $cust_details[1];
		
		$response = $this->$action_function($cust_id);
		
		$data['cust_id'] = $cust_id;
		$data['response'] = $response;
		$data['view'] = $this->class_name . '/'.$action_function."_details";
		$data['title'] = 'Customer Details - ' . SITE_TITLE;
		$data['page_heading'] = 'Customer Details - ('.$cust_id.')';
		$data['breadcrumb'] = "Customer Details";
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}

	public function csi7_cloud_active_and_reactivatable_customers() {
		
		$msg = array();
		$data['view'] = $this->class_name . '/cloud_customer_list';
		$data['title'] = 'Cloud Customer - ' . SITE_TITLE;
		$data['page_heading'] = 'Cloud Customer';
		$data['breadcrumb'] = "Cloud Customer";
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function getcsi7_cloud_active_and_reactivatable_customersData(){
		
		
		$customerReport = $this->reports_model->getCloudCustomer();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key=>$rdata) {
			
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->cst_id;
			$row[] = $rdata->account_name;
			$row[] = $rdata->account_email;
			$row[] = $rdata->account_username;
			$row[] = $rdata->db_client_host;
			$row[] = $rdata->Expires;
			$row[] = $rdata->last_login;
			$row[] = $rdata->csi_version;
			$row[] = $rdata->patch_version;
			$row[] = $this->functions->getLinks('all_customer-'.$rdata->cst_id,$this->class_name,false,false,true);
			
			$data[] = $row;
		}
		$row_count = $this->reports_model->allCustomer_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}

	public function csi7_cloud_expired_customers() {
		
		$msg = array();
		$data['view'] = $this->class_name . '/cloud_expired_customer_list';
		$data['title'] = 'Cloud Expired Customer - ' . SITE_TITLE;
		$data['breadcrumb'] = $data['page_heading'] = 'Cloud Expired Customer';
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function getcsi7_cloud_expired_customersData(){
		
		
		$customerReport = $this->reports_model->getExpiredCustomer();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key=>$rdata) {
			
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->cst_id;
			$row[] = $rdata->account_name;
			$row[] = $rdata->account_email;
			$row[] = $rdata->account_username;
			$row[] = $rdata->db_client_host;
			$row[] = $rdata->Expires;
			$row[] = $rdata->last_login;
			$row[] = $rdata->csi_version;
			$row[] = $rdata->patch_version;
			
			$data[] = $row;
		}
		$row_count = $this->reports_model->expiredCustomer_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	public function detectable_products() {
		
		$msg = array();
		$data['view'] = $this->class_name . '/detectable_products_list';
		$data['title'] = 'Cloud Customer - ' . SITE_TITLE;
		$data['breadcrumb'] = $data['page_heading'] = 'Detectable Products';
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function get_detectable_products_Data(){
		
		
		$customerReport = $this->reports_model->getDetectableProducts();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key=>$rdata) {
			
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->os_soft_id;
			$row[] = $rdata->platform;
			$row[] = $rdata->os_soft_name;
			$row[] = $rdata->vendor_name;
			
			$data[] = $row;
		}
		$row_count = $this->reports_model->detectableProducts_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	public function download_credentials() {
		
		$msg = array();
		$data['view'] = $this->class_name . '/download_credentials_list';
		$data['title'] = 'Download Credentials - ' . SITE_TITLE;
		$data['breadcrumb'] =  $data['page_heading'] = 'Download Credentials';
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function get_download_credentials_Data(){
		
		
		$customerReport = $this->reports_model->getDownloadCredentials();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key=>$rdata) {
			
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->user_cstid;
			$row[] = $rdata->user_email;
			$row[] = $rdata->product_code;			
			$data[] = $row;
		}
		$row_count = $this->reports_model->downloadcredentials_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	
	public function hosted_csi6_active_customers() {
		
		$msg = array();
		$data['view'] = $this->class_name . '/hosted_csi6_active_customers_list';
		$data['title'] = 'CSI6 Active Customer - ' . SITE_TITLE;
		$data['breadcrumb'] = $data['page_heading'] = 'CSI6 Active Customer';
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}
	
	public function gethosted_csi6_active_customersData(){
		
		
		$customerReport = $this->reports_model->getActiveHostedCsi6Customer();
		
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key=>$rdata) {
			
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->cst_id;
			$row[] = $rdata->account_name;
			$row[] = $rdata->account_email;
			$row[] = $rdata->account_username;
			$row[] = $rdata->db_client_host;
			$row[] = $rdata->Expires;
			$row[] = $rdata->last_login;
			$row[] = $rdata->csi_version;
			$row[] = $rdata->patch_version;
			
			$data[] = $row;
		}
		$row_count = $this->reports_model->activeHostedCsi6Customer_Filtered();
		$output = array(
						"draw" => $_POST['draw'],
						"recordsTotal" => $row_count,
						"recordsFiltered" => $row_count,
						"data" => $data,
				);
		
		header('Content-Type: application/json');
		echo json_encode($output);
	}
	
	
	private function all_customer($cust_id){


		
		if(!$this->customer_model->cust_check($cust_id)){
			redirect('');
		}
		
		$partition_details = $this->customer_model->getPartitions($cust_id);
		$data['total_partition'] = $partition_details->num_rows();
		$data['partition'] = $all_partition = $partition_details->result();
		
		$data['other_details'] = $this->customer_model->getOtherDetails($cust_id);
		
		$data['aModules']  = array(
			"Vulnerability Intelligence Feed (VIF) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_VTSE,
			"Enterprise Vulnerability Manager (EVM) / Management and Administration in VIM 4.x &amp; VIM 3.x" => MOD_ESM,
			"User Management (UM)" => MOD_UM,
			"Vulnerability Manager (VM) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_SM,
			"Vulnerability Tracking (VTS) / Vulnerability Manager in VIM 4.x &amp; VIM 3.x" => MOD_VTS,
			"Surveillance Scanner (SS)" => MOD_VSS,
			"Corporate Software Inspector (CSI)" => MOD_NSI,
			"Binary Analysis (BA) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_BA,
			"Vulnerability Database</b> (default module)" => MOD_VDB,
			/* "Technical Support</b> (default module)" => MOD_SUPPORT, */
			"Account Information</b> (default module) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_ACCOUNT
		);
	
		return $data;
		
	}

    public function get_pdb_details($cust_id) {

        $msg = array();
        $data['cust_id'] = $cust_id;
        $data['view'] = $this->class_name . '/pdb_info_list';
        $data['title'] = 'PDB Info Details - ' . SITE_TITLE;
        $data['breadcrumb'] = $data['page_heading'] = 'PDB Info Details';
        $data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
        $this->load->view('templates/dashboard', $data);
    }

    public function get_pdb_info_data($cust_id){

        $this->pdb = $this->functions->pdbConnection($cust_id);

        if(!$this->customer_model->cust_check($cust_id)){
            redirect('');
        }
        $customerReport = $this->reports_model->getpdbInfoDetails($cust_id);
        $data = array();
        $no = $_POST['start'];
        foreach ($customerReport as $key=>$rdata) {
             $no++;
            $row = array();
            $row[] = $no;
            $row[] = $rdata->account_name;
            $row[] = $rdata->account_username;
            $row[] = $rdata->account_email;
            $row[] = $rdata->partition_id;
            $row[] = $rdata->csi_version;
            $row[] = $rdata->patch_version;
            $row[] = $rdata->date_applied;
            $row[] = $rdata->locked;
            $row[] = $rdata->lock_date;
            $row[] = $rdata->db_client_host;
            if($rdata->locked == 1) {
                $row[] = '<td><a title="Unlock" href="javascript:void(0);" onclick="unlockPdbInfo(\'' . $cust_id . '\',\'' . $rdata->partition_id . '\')"><button class="btn btn-success btn-xs"><i class="fa fa-unlock"></i></button></a></td>';
            }
            else{
                $row[] = '-';
            }
            $row[] = ($rdata->is_sso == 1) ? "Yes":"No";
            $row[] = $rdata->guid;
            $row[] = ($rdata->disable_standard_login == 1) ? "Yes" : "No";
            $row[] = $rdata->template_account_id;
            $data[] = $row;
        }
        $row_count = $this->reports_model->getpdbInfoDetails_Filtered($cust_id);
        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $row_count,
            "recordsFiltered" => $row_count,
            "data" => $data,
        );

        header('Content-Type: application/json');
        $this->pdb->close();
        echo json_encode($output);
    }

    public function asap_report_generation(){
        $report_id  =   $_POST['report_id'];
        $account_id  =   $_POST['account_id'];
        $customerReport = $this->reports_model->generate_report_asap($report_id,$account_id);
        echo json_encode($report_id);
    }

    public function recompile_sg_asap(){
        $smartgroup_id  =   $_POST['smartgroup_id'];
        $customer_id    =   $_POST['customer_id'];
        $partition_id   =   $_POST['partition_id'];
        $type           =   $_POST['type'];
        $account_id     =   $_POST['account_id'];
        $this->pdb      = $this->functions->pdbConnection($customer_id,$partition_id);
        $sgcompilation = $this->reports_model->recompile_sg_asap($smartgroup_id,$customer_id,$partition_id,$type,$account_id);
        $this->pdb->close();
        echo json_encode($smartgroup_id);
    }

    public function unlock_pdb_info(){
        $customer_id  =   $_POST['customer_id'];
        $partition_id    =   $_POST['partition_id'];
        $this->pdb      = $this->functions->pdbConnection($customer_id);
        $sgcompilation = $this->reports_model->pdb_info_unlock($customer_id,$partition_id);
        $this->pdb->close();
        echo json_encode($customer_id);
    }

    public function unlock_account(){
        $account_id  =   $_POST['account_id'];
        $rows = $this->reports_model->unlock_account($account_id);
        echo json_encode($rows);
    }

    public function package_downloads() {
        $controller = $this->router->fetch_class();
        $method = $this->router->fetch_method();
        $role_id = $this->session->userdata('role_id');
        $menuitem_link = $controller . '/' . $method;
        $get_access = $this->functions->get_menu_access($role_id, $menuitem_link);

        if ($get_access != 0) {
            $msg = array();
            $data['view'] = $this->class_name . '/download_package_list';
            $data['title'] = 'DPackages Download  - ' . SITE_TITLE;
            $data['breadcrumb'] = $data['page_heading'] = 'Packages Download';
            $data['scripts'] = array('assets/javascript/' . $this->class_name . '.js');
            $this->load->view('templates/dashboard', $data);
        }
        else {
            redirect('');
        }
    }

    public function get_download_package() {
        $searchBySource = $_POST['searchBySource'];
        $customerReport = $this->reports_model->getDownloadPackage($searchBySource);
        $data = array();
        $no = $_POST['start'];
        foreach ($customerReport as $key=>$rdata) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $rdata->cst_id;
            $row[] = $rdata->vpm_id;
            $row[] = $rdata->package_name;
            $row[] = $this->functions->getSource($rdata->source);
            $row[] = $rdata->ip_v4;
            $row[] = $rdata->created_at;
            $data[] = $row;
        }
        $row_count = $this->reports_model->getDownloadPackage_Filtered();
        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $row_count,
            "recordsFiltered" => $row_count,
            "data" => $data,
        );

        header('Content-Type: application/json');

        echo json_encode($output);
    }

    public function change_report_debug_status(){
        $report_id  =   $_POST['report_id'];
        $account_id  =   $_POST['account_id'];
        $debug_status  =   $_POST['debug_status'];
        $customerReport = $this->reports_model->report_debug_status($report_id,$account_id,$debug_status);
        echo json_encode($report_id);
    }

    public function get_large_customers($cust_id) {

        $msg = array();
        $data['cust_id'] = $cust_id;
        $data['view'] = $this->class_name . '/large_customers_list';
        $data['title'] = 'Large Customers Details - ' . SITE_TITLE;
        $data['breadcrumb'] = $data['page_heading'] = 'Large Customers Details';
        $data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
        $this->load->view('templates/dashboard', $data);
    }

    public function get_large_customer_data($cust_id){
        if(!$this->customer_model->cust_check($cust_id)){
            redirect('');
        }
         $customerReport = $this->reports_model->get_large_customers_details($cust_id);
        $data = array();
        $no = $_POST['start'];
        foreach ($customerReport as $key=>$rdata) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $rdata->cst_id;
            if($rdata->type== 0){$type = 'Reporting';} else if ($rdata->type == 1) { $type = 'License Sync';} else if ($rdata->type == 2)  {$type = 'Dashboard';} else if ($rdata->type == 4) { $type = 'Collect Windows Software Suggestions data' ;} else if ($rdata->type == 5) { $type = 'Collect Linux Software Suggestions data' ;} else if ($rdata->type == 6) { $type = 'Collect Mac Software Suggestions data' ;}else {$type = 'Product Smart Groups Compilation';};
            $row[] = $type;
            $row[] = $rdata->created_at;
           $row[] = '<td><a title="Delete" href="reports/delete_lg_cstid/'.$rdata->cst_id.'/'.$rdata->type.'" ><button class="btn btn-warning btn-xs"><i class="fa fa-trash"></i></button></a></td>';

            $data[] = $row;
        }
        $row_count = $this->reports_model->get_large_customer_data_filtered($cust_id);
        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $row_count,
            "recordsFiltered" => $row_count,
            "data" => $data,
        );

        header('Content-Type: application/json');
        //$this->pdb->close();csi_pdb
        echo json_encode($output);
    }

    public function delete_lg_cstid($cust_id,$type) {
        if (!empty($cust_id)) {
            $this->reports_model->primary_key = array('cst_id' => $cust_id,'type' => $type);
            if ($this->reports_model->delete_lg_cst()) {
                $msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Deleted Successfully');
            } else {
                $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Delete Record.');
            }
        } else {
            $msg = array();
            $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Some error occurred.');
        }
        $this->session->set_flashdata('msg', $msg);
        redirect("reports/get_large_customers/".$cust_id);
    }

    public function add_new_customer($cust_id) {
	        $msg = array();
	        $data['cst_id'] = $cust_id;
            $data['view'] = $this->class_name . '/add_customer_form';
            $data['title'] = 'Add New Type - ' . SITE_TITLE;
            $data['breadcrumb'] = "<a href=$this->class_name>Large Customers List</a> &nbsp;&nbsp; > &nbsp;&nbsp; Add New Type";
            $data['page_heading'] = 'Add New Type';
            $this->load->view('templates/dashboard', $data);
	}

    public function save_customer()
    {
        if (!empty($_POST)) {
            $customer_id = $this->input->post('customer_id');
            $type = $this->input->post('type');
            $this->reports_model->data['cst_id'] = $customer_id;
            $this->reports_model->data['type'] = $type;
            $this->reports_model->data['created_at'] = date('Y-m-d : H:i:s');
            if ($this->reports_model->insert_customer()) {
                $msg = array('type' => 'success', 'icon' => 'fa fa-check', 'txt' => 'Record Updated Successfully');
            } else {
                $msg = array('type' => 'error', 'icon' => 'fa fa-thumbs-down', 'txt' => 'Sorry! Unable to Add Record.');
            }
            $this->session->set_flashdata('msg', $msg);
            redirect("reports/get_large_customers/".$customer_id);
        }
    }
	public function customer_details()
	{
		$msg = array();
		$data['roles'] = $this->admin_roles_model->view_roles();
		$data['view'] = $this->class_name . '/cloud_customer_details';
		$data['title'] = 'Customer Details - ' . SITE_TITLE;
		$data['breadcrumb'] = "<a href=$this->class_name>Cloud Customer List</a> &nbsp;&nbsp; > &nbsp;&nbsp; Customer Details";
		$data['page_heading'] = 'Customer Details';
		$data['scripts'] = array('assets/javascript/'.$this->class_name.'.js');
		$this->load->view('templates/dashboard', $data);
	}


	public function detailed_customers()
	{
		$customerReport = $this->reports_model->getCloudCustomerDetails();
		$data = array();
		$no = $_POST['start'];
		foreach ($customerReport as $key => $rdata) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $rdata->cst_id;
			$row[] = $rdata->partition_id;
			$row[] = $rdata->account_id;
			$row[] = $rdata->account_username;
			$row[] = $rdata->account_email;
			$row[] = $rdata->db_client_host;
			$row[] = $rdata->last_login;
			$row[] = $rdata->csi_version;
			$row[] = $rdata->patch_version;
			$row[] = $rdata->last_compile_time;
			$row[] = $rdata->generate_asap;
			$row[] = $rdata->time_elapsed;
			$row[] = $rdata->in_progress;
			$row[] = $rdata->last_started_at;
			$row[] = $rdata->Currenttime;
			$row[] = $rdata->compile_min;
			$row[] = $rdata->compile_hr;
			$row[] = $rdata->total_devices;
			$row[] = $rdata->locked;
			$data[] = $row;
		}
		$row_count = $this->reports_model->customerDetails_Filtered();
		$output = array(
			"draw" => $_POST['draw'],
			"recordsTotal" => $row_count,
			"recordsFiltered" => $row_count,
			"data" => $data,
		);

		header('Content-Type: application/json');
		//$this->pdb->close();csi_pdb
		echo json_encode($output);
	}

}