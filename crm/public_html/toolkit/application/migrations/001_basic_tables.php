<?php

class Migration_Basic_tables extends CI_Migration
{
    public function up()
    {
       // $this->dbforge->custom_query('DROP TABLE IF EXISTS admin_menuitems,admin_menus,admin_roles,admin_roles_accesses,admin_users,admin_users_accesses;');
        $this->dbforge->custom_query('CREATE TABLE IF NOT EXISTS  admin_menuitems  (
							   menuitem_id  int(11) NOT NULL AUTO_INCREMENT,
							   menu_id  int(11) NOT NULL,
							   parent_menuitem_id  int(11) DEFAULT NULL,
							   menuitem_target  varchar(100) NOT NULL,
							   menuitem_link  varchar(255) NOT NULL,
							   menuitem_text  varchar(255) NOT NULL,
							   display_order  int(11) NOT NULL,
							   status_ind  tinyint(4) NOT NULL,
							   created_date  datetime NOT NULL,
							   last_modified_date  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
							   created_by  int(11) NOT NULL,
							   last_modified_by  int(11) DEFAULT NULL,
							  PRIMARY KEY ( menuitem_id )
							) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;');

        $this->dbforge->custom_query("INSERT INTO  admin_menuitems(menuitem_id,menu_id,parent_menuitem_id,menuitem_target,menuitem_link,menuitem_text,display_order,status_ind,created_date,last_modified_date,created_by,last_modified_by) VALUES 
									(1,1,NULL,'','#','Admin Users',2,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(2,1,1,'','adminusers/index','Admin Users List',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(3,1,1,'','adminusers/add','Add Admin Users',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(4,4,1,'','adminroles','Admin Roles',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(5,1,NULL,'','#','Reports',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(6,1,5,'','reports/csi7_cloud_active_and_reactivatable_customers','All Customer',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(7,1,5,'','reports/csi7_cloud_expired_customers','Expired Customer',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(8,1,5,'','reports/detectable_products','Detectable Products',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(9,1,5,'','reports/download_credentials','Download Credentials',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(10,1,5,'','reports/hosted_csi6_active_customers','Hosted Active Customer(CSI6)',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(11,1,5,'','reports/hosted_csi7_active_and_recently_expired','Hosted Active/expired Customer(CSI7)',1,0,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(12,1,NULL,'','#','Cronjobs',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(13,1,12,'','cronjobs/jobs','Jobs',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(14,1,12,'','cronjobs/smartgroupjobs','Smart Group Jobs',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(15,1,12,'','cronjobs/reports','Reports Generation',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									,(16,1,5,'','reports/package_downloads','Packages Download',1,1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,0)
									;");


        $this->dbforge->custom_query("CREATE TABLE IF NOT EXISTS `admin_menus` (
									  `menu_id` int(11) NOT NULL AUTO_INCREMENT,
									  `menu_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
									  `status_ind` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1=Active, 0=Inactive',
									  PRIMARY KEY (`menu_id`)
									) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;");

        $this->dbforge->custom_query("INSERT INTO admin_menus (menu_id,menu_name,status_ind) VALUES (1,'Super Admin',1);");

        $this->dbforge->custom_query("CREATE TABLE IF NOT EXISTS `admin_roles` (
									  `role_id` int(11) NOT NULL AUTO_INCREMENT,
									  `role_name` varchar(50) DEFAULT NULL,
									  `status_ind` smallint(6) DEFAULT NULL,
									  `created_date` datetime DEFAULT NULL,
									  `modified_date` datetime DEFAULT NULL,
									  `created_by` smallint(6) DEFAULT NULL,
									  `modified_by` smallint(6) DEFAULT NULL,
									  PRIMARY KEY (`role_id`)
									) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;");

        $this->dbforge->custom_query("INSERT INTO admin_roles(role_id,role_name,status_ind,created_date,modified_date,created_by,modified_by) VALUES 
									(1,'Admin',1,'".$this->dbforge->currentDateTime()."','".$this->dbforge->currentDateTime()."',1,1);");

        $this->dbforge->custom_query("CREATE TABLE IF NOT EXISTS `admin_roles_accesses` (
									  `role_id` int(11) NOT NULL,
									  `menuitem_id` int(11) NOT NULL,
									  `add_permission` int(11) NOT NULL,
									  `edit_permission` int(11) NOT NULL,
									  `delete_permission` int(11) NOT NULL,
									  KEY `admin_roles_accesses_ibfk_1` (`role_id`),
									  KEY `admin_roles_accesses_ibfk_2` (`menuitem_id`)
									) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;");


        $this->dbforge->custom_query("INSERT INTO admin_roles_accesses (role_id,menuitem_id,add_permission,edit_permission,delete_permission) 
										VALUES 
										(1,1,0,0,0)
										,(1,2,0,0,0)
										,(1,3,0,0,0)
										,(1,4,0,0,0)
										,(1,5,0,0,0)
										,(1,6,0,0,0)
										,(1,7,0,0,0)
										,(1,8,0,0,0)
										,(1,9,0,0,0)
										,(1,10,0,0,0)
										,(1,11,0,0,0)
										,(1,12,0,0,0)
										,(1,13,0,0,0)
										,(1,14,0,0,0)
										,(1,15,0,0,0);
									");


        $this->dbforge->custom_query("CREATE TABLE IF NOT EXISTS `admin_users` (
									  `user_id` int(11) NOT NULL AUTO_INCREMENT,
									  `role_id` int(11) DEFAULT NULL,
									  `first_name` varchar(100) NOT NULL,
									  `last_name` varchar(100) NOT NULL,
									  `username` varchar(50) DEFAULT NULL,
									  `email` varchar(50) DEFAULT NULL,
									  `password` varchar(255) DEFAULT NULL,
									  `user_session_id` varchar(60) DEFAULT NULL,
									  `status_ind` smallint(6) DEFAULT '0' COMMENT '1=active,0=inactive',
									  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
									  `created_by` smallint(6) DEFAULT '0',
									  `modified_date` datetime DEFAULT NULL,
									  `modified_by` smallint(6) DEFAULT '0',
									  PRIMARY KEY (`user_id`)
									) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;");
        $hashnprabhu     =  password_hash ( 'NPrabhu', PASSWORD_BCRYPT );
        $hashMmarino     =  password_hash ( 'Mmarino', PASSWORD_BCRYPT );
        $hashWMahmood     =  password_hash ( 'WMahmood', PASSWORD_BCRYPT );
        $hashdevtest      =  password_hash ( 'devtest', PASSWORD_BCRYPT );
        $this->dbforge->custom_query("INSERT INTO admin_users(user_id,role_id,first_name,last_name,username,email,password,user_session_id,status_ind,created_date,created_by,modified_date,modified_by) VALUES 
									(1,1,'Navin','Prabhu','NPrabhu','<EMAIL>','".$hashnprabhu."',null,1,'".$this->dbforge->currentDateTime()."',0,'".$this->dbforge->currentDateTime()."',1),
									(2,1,'Michael','Marino','Mmarino','<EMAIL>','".$hashMmarino."',null,1,'".$this->dbforge->currentDateTime()."',0,'".$this->dbforge->currentDateTime()."',1),
									(3,1,'Waqas','Mahmood','WMahmood ','<EMAIL>','".$hashWMahmood."',null,1,'".$this->dbforge->currentDateTime()."',0,'".$this->dbforge->currentDateTime()."',1),
									(4,1,'devtest','Test','devtest ','<EMAIL>','".$hashdevtest."',null,1,'".$this->dbforge->currentDateTime()."',0,'".$this->dbforge->currentDateTime()."',1)
									;");


        $this->dbforge->custom_query("CREATE TABLE `admin_users_accesses` (
									  `user_id` int(11) NOT NULL,
									  `menuitem_id` int(11) NOT NULL,
									  `add_permission` int(11) NOT NULL,
									  `edit_permission` int(11) NOT NULL,
									  `delete_permission` int(11) NOT NULL,
									  KEY `user_id` (`user_id`),
									  KEY `menuitem_id` (`menuitem_id`)
									) ENGINE=InnoDB DEFAULT CHARSET=latin1;");

        $this->dbforge->custom_query("INSERT INTO `admin_users_accesses`(user_id,menuitem_id,add_permission,edit_permission,delete_permission) 
                                                                                VALUES (1,1,1,1,1),(1,2,1,1,1),(1,3,1,1,1),(1,4,1,1,1),
                                                                                (2,1,1,1,1),(2,2,1,1,1),(2,3,1,1,1),(2,4,1,1,1),
                                                                                (3,1,1,1,1),(3,2,1,1,1),(3,3,1,1,1),(3,4,1,1,1),
                                                                                (4,1,1,1,1),(4,2,1,1,1),(4,3,1,1,1),(4,4,1,1,1);");
    }

    public function down()
    {
        $this->dbforge->drop_table('admin_menuitems');
        $this->dbforge->drop_table('admin_menus');
        $this->dbforge->drop_table('admin_roles_accesses');
        $this->dbforge->drop_table('admin_users');
        $this->dbforge->drop_table('admin_users_accesses');

    }
}