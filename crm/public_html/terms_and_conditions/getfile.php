<?php

require_once( '../sales/configuration.php' );
require_once( INCLUDE_PATH . 'TermsAndConditions.php' );
require_once( INCLUDE_PATH . 'sales_functions.php' );
require_once( INCLUDE_PATH . 'global_functions.php' );

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();
$file = DBQueryGetRows( 'SELECT path FROM ca.terms WHERE id = ' . (int) $_GET['id'] );

if ( file_exists( $file[0]['path'] ) ) {
	header("Content-type:application/pdf");
	header("Content-Disposition:attachment;filename=secunia_csi_terms_and_conditions.pdf");
	readfile( $file[0]['path'] );
} else {
	echo 'File not found.';
}
