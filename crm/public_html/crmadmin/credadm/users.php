<?php
$baseURL = "/crmadmin/credadm/";
function genPassword(){
	$return = "";
	for($i=0;$i<8;$i++){
		$return .= chr(rand(0, 25) + ord('a'));
	}
	return $return;
}
$action = isset($_GET['action']) ? $_GET['action'] : '';
if ($action == "delete" && is_numeric($_GET['user'])){
	//Delete user
	mysql_query("DELETE FROM download_users WHERE user_id=".$_GET['user']);
}
$status = '';
if (isset($_POST['email']) && isset($_POST['cst_id']) && isset($_POST['products']) && isset($_POST['update_user'])){
	$userproducts = 0;
	foreach($_POST['products'] as $bw){
		$userproducts = $userproducts+$bw;
	}
	$query = "UPDATE download_users SET user_username='".$_POST['email']."', user_products=".$userproducts.", user_cstid='".$_POST['cst_id']."', user_email='".$_POST['email']."' WHERE user_id='".$_POST['update_user']."'";
	mysql_query($query);
	$status = "<span style=\"color: #00DD00;\"><b>User Updated</b></span><br><br>";
} elseif (isset($_POST['email']) && isset($_POST['cst_id']) && isset($_POST['products'])){
	//Add user
	$password = genPassword();
	$userproducts = 0;
	foreach($_POST['products'] as $bw){
		$userproducts = $userproducts+$bw;
	}
	$query = "INSERT INTO download_users SET user_username='".$_POST['email']."', user_password=OLD_PASSWORD('".$password."'), user_products=".$userproducts.", user_cstid='".$_POST['cst_id']."', user_email='".$_POST['email']."'";
	mysql_query($query);
	$status = "<span style=\"color: #00DD00;\"><b>User created</b></span><br><br>";
}

if ($action == "credentials"){
	//Send credentials
	$query = mysql_query("SELECT * FROM download_users WHERE user_id=".$_GET['user']);
	$data = mysql_fetch_assoc($query);
	$password = genPassword();
	$msg = "Dear customer,\n\nplease find enclosed username and password to download the software needed to install your Flexera product.\n\nUsername: ".$data['user_username']."\nPassword: ".$password."\n\nYou can use the credentials at https://ca.secunia.com/download\n\nIf you have any questions, please do not hesitate to contact <NAME_EMAIL>.\n\n\nStay secure,\n\nFlexera";
	$headers = "From: Flexera <<EMAIL>>\r\n";
	mysql_query("UPDATE download_users SET user_password=OLD_PASSWORD('".$password."') WHERE user_id=".$_GET['user']);
	mail("<EMAIL>", "Flexera software download", $msg, $headers);
	mail($data['user_email'], "Flexera software download", $msg, $headers);
	$status = "<span style=\"color: #00DD00;\"><b>New credentials sent</b></span><br><br>";
}

?>
<h1>Users</h1>
<?php
echo $status;
if ($action == "new"){
	?>
	<form method="POST" action="<?php echo $baseURL; ?>">
		<b>Username / E-mail</b><br>
		<input type="text" name="email"><br><br><b>CST_ID</b><br><input type="text" name="cst_id"><br><br><b>Products</b><br>
		<?php
		# Removing non-obfuscated versions as those are only used for a very speciel select few....
		//$query = mysql_query("SELECT * FROM download_products");
		$query = mysql_query("SELECT * FROM download_products WHERE product_code NOT LIKE '%non-obfuscated%'");
		while($row = mysql_fetch_assoc($query)){
			echo "<input type=\"checkbox\" name=\"products[]\" value=\"".$row['product_bw']."\"> ".$row['product_code']."<br>";
		}
		?><br>
		<input type="submit" value="Create">
	</form>
	<?php
}elseif ($action == "edit"){
    $query = mysql_query("SELECT * FROM download_users WHERE user_id = ".$_GET['user']);
	$crm_user = mysql_fetch_assoc($query);
	$displayfiles = array();
	$downloadble_files_query = mysql_query("SELECT file_filename, product_bw FROM download_files LEFT JOIN download_products ON product_id = file_product");
	while($downloadble_file = mysql_fetch_assoc($downloadble_files_query)){
		$displayfiles[] = (int) $downloadble_file['product_bw'];
	}
	?>
    <form method="POST" action="<?php echo $baseURL; ?>">
        <b>Username / E-mail</b><br>
        <input type="text" name="email" value="<?php echo $crm_user['user_username']; ?>"><br><br><b>CST_ID</b><br><input type="text" name="cst_id" value="<?php echo $crm_user['user_cstid']; ?>"><br><br><b>Products</b><br> <input type="hidden" name="update_user" value="<?php echo $crm_user['user_id']; ?>">
		<?php
		# Removing non-obfuscated versions as those are only used for a very speciel select few....
		//$query = mysql_query("SELECT * FROM download_products");
		$products_query = mysql_query("SELECT * FROM download_products WHERE product_code NOT LIKE '%non-obfuscated%'");
        while($row = mysql_fetch_assoc($products_query)){
            if (in_array($row['product_bw'], $displayfiles) &&  ($row['product_bw'] & ((int) $crm_user['user_products']))){
                echo "<input type=\"checkbox\" name=\"products[]\" value=\"".$row['product_bw']."\" checked> ".$row['product_code']."<br>";
            }else{
                echo "<input type=\"checkbox\" name=\"products[]\" value=\"".$row['product_bw']."\"> ".$row['product_code']."<br>";
            }
        }

		?><br>
        <input type="submit" value="Update">
    </form>
	<?php
} else {
	?>
	<a href="<?php echo $baseURL; ?>?action=new">New user</a>
	<table width="100%" cellpadding="0" cellspacing="0" style="border: 1px solid #000000;">
	<tr>
		<td style="padding: 2px; width: 300px; background-color: #BBBBBB;"><b>Username</b></td>
		<td style="padding: 2px; width: 300px; background-color: #BBBBBB;"><b>E-mail</b></td>
		<td style="padding: 2px; width: 75px; background-color: #BBBBBB;"><b>CST_ID</b></td>
		<td style="padding: 2px; width: 250px; background-color: #BBBBBB;">&nbsp;</td>
		<td style="padding: 2px; background-color: #BBBBBB;">&nbsp;</td>
	</tr>
	<?php
	$query = mysql_query("SELECT * FROM download_users ORDER BY user_username ASC");
	$color = true;
	while($row = mysql_fetch_assoc($query)){
		$bgcolor = (($color = !$color)?'#DDDDDD':'#FFFFFF');
		echo "<tr><td style=\"padding: 2px; border-top: 1px solid #000000; background-color: ".$bgcolor.";\"><a href=\"".$baseURL."?action=edit&user=".$row['user_id']."\">".$row['user_username']."</a></td><td style=\"padding: 2px; border-top: 1px solid #000000; background-color: ".$bgcolor.";\"><a href=\"mailto:".$row['user_email']."\">".$row['user_email']."</a></td><td style=\"padding: 2px; border-top: 1px solid #000000; background-color: ".$bgcolor.";\">".$row['user_cstid']."</td><td style=\"padding: 2px; border-top: 1px solid #000000; background-color: ".$bgcolor.";\"><a href=\"#\" onclick=\"if (confirm('Do you really want to delete \'".$row['user_username']."\' from the system?')) location.href='".$baseURL."?action=delete&user=".$row['user_id']."';\">Delete user</a>&nbsp;&nbsp;<a href=\"#\" onclick=\"if (confirm('Do you really want to send new credentials to \'".$row['user_username']."\'?')) location.href='".$baseURL."?action=credentials&user=".$row['user_id']."';\">Send new credentials</a></td><td style=\"padding: 2px; border-top: 1px solid #000000; background-color: ".$bgcolor.";\">&nbsp;</td></tr>";
	}
	?>
	</table>
	<?php
}
?>
