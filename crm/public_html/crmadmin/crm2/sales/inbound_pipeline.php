<?php
error_reporting(0);

// Load configuration
include 'configuration.php';

// Open DB Connection
fOpenDatabase();

if ( !fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ){
	// Grant access to Lead Management or above
	echo "Permission denied.";
	die();
}

// Create a new customer card, similar to a new CSI customer account
function createNewCustomerCard( $iFormId ){
	$iFormId = (int) $iFormId;
	if ( $iFormId == 0 ){
		return -1;
	}

	$iCRMSegmentID = 1641;
	$iLeadSource = 7;

	// Fetch form data
	$rResult = DBQuery("SELECT * FROM crm.form_data WHERE id = '".$iFormId."'");
	$iNumRows = mysql_num_rows( $rResult );
	if ( $iNumRows != 1 ){
		return -1;
	}

	$aRow = mysql_fetch_array( $rResult );
	$sCompanyName = mysql_real_escape_string( $aRow['company_name'] );
	$sPersonName = mysql_real_escape_string( $aRow['person_name'] );
	$sPersonPhone = mysql_real_escape_string( $aRow['person_phone'] );
	$sPersonEmail = mysql_real_escape_string( $aRow['person_email'] );
	$sPersonTitle = mysql_real_escape_string( $aRow['person_job_title'] );
	$sSubscriptionType = mysql_real_escape_string( $aRow['subscription_type'] );

	// Determine company country, based on the contact person country
	$sCountry = $aRow['person_country'];
	$iCountryId = (int) DBGetRowValue("crm.countries", "id", "country = '". mysql_real_escape_string( $sCountry )."'");

	// Validate form data
	if ( ( $sCompanyName == "" ) || ( $sPersonName == "" ) ){
		return -1;
	}

	// Company Card
	DBQuery("INSERT INTO crm.cst (name, case_name, segment_id, lead_source, invoice_country) VALUES('" . $sCompanyName . "', 'Company Card', '" . $iCRMSegmentID . "', '".$iLeadSource."', ".$iCountryId.");");
	$iMasterID = mysql_insert_id();

	// CASE_CSI
	DBQuery("INSERT INTO crm.cst (name, case_name, segment_id, master_id, invoice_country) VALUES('" . $sCompanyName . "', 'card_nsi', '" . $iCRMSegmentID . "', '" . $iMasterID . "', ".$iCountryId.");");
	$iCustomerID = mysql_insert_id();

	// CASE_VI
	DBQuery("INSERT INTO crm.cst (name, case_name, segment_id, master_id, invoice_country) VALUES('" . $sCompanyName . "', 'card_vi', '" . $iCRMSegmentID . "', '" . $iMasterID . "', ".$iCountryId.");");
	$iVICSTID = mysql_insert_id();

	// Comment
	$sComment = mysql_real_escape_string( print_r($aRow, true) );
	$sComment = strip_tags( $sComment );

	// Insert comment
	$sQuery = "INSERT INTO crm.comments (comment, added, cst_id, person_id, type) values('" . $sComment . "', now(), '" . $iCustomerID . "', '0', 1)";
	DBQuery($sQuery);

	$sQuery = "INSERT INTO crm.comments (comment, added, cst_id, person_id, type) values('" . $sComment . "', now(), '" . $iVICSTID  . "', '0', 1)";
	DBQuery($sQuery);

	// Contact
	$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact, invoice, subscription_type) VALUES('" . $iCustomerID . "', '1', '" . $sPersonName . ', ' . $sPersonTitle . "', '" . $sPersonPhone . "', '" . $sPersonEmail . "', 1, 1, '".$sSubscriptionType."')";
	DBQuery($sQuery);
	$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact, invoice) VALUES('" . $iVICSTID  . "', '1', '" . $sPersonName . ', ' . $sPersonTitle . "', '" . $sPersonPhone . "', '" . $sPersonEmail . "', 1, 1)";
	DBQuery($sQuery);

	// Update form cst_id
	DBQuery("UPDATE crm.form_data SET cst_id = '".$iMasterID."', last_action='Import into CRM' WHERE id = '".$iFormId."'");

	return $iMasterID;
}

// Import into CRM
if ( ( $_GET['action'] == 4 ) && ( is_numeric( $_GET['formid'] ) && $_GET['formid'] > 0 ) ){
	createNewCustomerCard( $_GET['formid'] );
	exit();
}


// Handle special actions
// 1) Make duplicate
if ( ( $_GET['action'] == 1 ) && ( is_numeric( $_GET['cstid'] ) && ( $_GET['cstid'] > 0 ) ) && ( is_numeric( $_GET['masterid'] ) && ( $_GET['masterid'] > 0 ) ) ){
	DBQuery("UPDATE crm.form_data SET last_action = 'Make shadow', form_handled = 1 WHERE id = '".$_GET['form_id']."'");
	// Begin by moving comments:
	$rResult = DBQuery("SELECT cst_id, case_name FROM crm.cst WHERE master_id = '".$_GET['cstid']."' AND case_name IN ('card_vi', 'card_nsi') ");
	$iNumRows = mysql_num_rows( $rResult );
	for ( $i = 0; $i < $iNumRows; $i++ ){
		$aRow = mysql_fetch_array( $rResult );
		if ( $aRow['case_name'] == "card_nsi" ) {
			$iCSTIDNSI = DBGetRowValue("crm.cst", "cst_id", "master_id = '".$_GET['masterid']."' AND case_name = 'card_nsi'");
			DBQuery("UPDATE crm.comments SET cst_id = '".(int)$iCSTIDNSI."' WHERE cst_id = '".(int)$aRow['cst_id']."' ");
		} elseif ( $aRow['case_name'] == "card_vi" ) {
			$iCSTIDVI = DBGetRowValue("crm.cst", "cst_id", "master_id = '".$_GET['masterid']."' AND case_name = 'card_vi'");
			DBQuery("UPDATE crm.comments SET cst_id = '".(int)$iCSTIDVI."' WHERE cst_id = '".(int)$aRow['cst_id']."'");
		}
	}
	DBQuery("UPDATE crm.cst SET case_name = 'card_shadow', master_id = '".$_GET['masterid']."' WHERE cst_id = '".$_GET['cstid']."'");
	DBQuery("UPDATE crm.cst SET master_id = '".$_GET['masterid']."', case_name = 'card_shadow' WHERE master_id = '".$_GET['cstid']."'");
	// Special actions need not go any further
	exit();
}

// Store the fact that info was requested
if ( ( $_GET['action'] == 2 ) && ( is_numeric( $_GET['form_id'] ) && $_GET['form_id'] > 0 ) ){
	DBQuery("UPDATE crm.form_data SET last_action = 'Request Info', info_requested = 1 WHERE id = '".$_GET['form_id']."'");
	exit();
}

// Mark form as handled AND send the email to the appropriate department
//	_objAjax.open( "GET", "?page=inbound_pipeline&action=3&formid=" + iFormId + "&cstid=" + icst_id + "&where=" + iWhere + "&company_name=" + sCompanyName );
if ( ( $_GET['action'] == 3 ) && ( is_numeric( $_GET['formid'] ) && $_GET['formid'] > 0 ) && ( is_numeric( $_GET['where'] ) && ( $_GET['where'] > 0 )  ) && ( $_GET['company_name'] != "" ) ){
	
	$slMailSubject = "INBOUND - ".htmlspecialchars( $_GET['company_name'] );
	$slMailBody = PUBLIC_URL."?page=customer&cst_id=".$_GET['cstid'];
	$sTo = "";
	$bTrash = false;
	$sSentTo = "";
	$iSegmentId = 0;
	switch ( $_GET['where'] ){
		case "4":
			$sTo = "<EMAIL>";
			$iSegmentId = 1413;
			$sSentTo = 'CSI';
			break;
		case "5":
			$sTo = "<EMAIL>";
			$iSegmentId = 1404;
			$sSentTo = 'TLA';
			break;
		case "6":
			$sTo = "<EMAIL>";
			$iSegmentId = 1427;
			$sSentTo = 'Partners';
			break;
		case "7":
			$bTrash = true;
			break;
		case "8":
			$sTo = "<EMAIL>";
			$iSegmentId = 1427;
			$sSentTo = 'Minas';
			break;
		case "9":
			$sTo = "<EMAIL>";
			$iSegmentId = 1427;
			$sSentTo = 'Ashley';
			break;
	}
	if ( ( $sTo == "" ) && ( $bTrash == false ) ){
		// Invalid action -> abort
		exit();
	}

	// Mark the form as handled
	DBQuery("UPDATE crm.form_data SET form_handled = 1 WHERE id = '".$_GET['formid']."'");
	if ( $bTrash == false ){
		// Send the mail
		DBQuery("UPDATE crm.form_data SET last_action = 'Company sent to ".$sSentTo."' WHERE id = '".$_GET['formid']."'");
		// Update segment
		DBQuery("UPDATE crm.cst SET cst.segment_id = '".$iSegmentId."' WHERE cst_id = '".(int)$_GET['cstid']."'");
		DBQuery("UPDATE crm.cst SET cst.segment_id = '".$iSegmentId."' WHERE master_id = '".(int)$_GET['cstid']."'");
		mail( $sTo, $slMailSubject, $slMailBody );
	} else {
		DBQuery("UPDATE crm.form_data SET last_action = 'Company sent to TRASH', trash = 1 WHERE id = '".$_GET['formid']."'");
		// Trash customer
		DBQuery("UPDATE crm.cst SET cst.segment_id = 1481 WHERE cst_id = '".(int)$_GET['cstid']."'");
		DBQuery("UPDATE crm.cst SET cst.segment_id = 1481 WHERE master_id = '".(int)$_GET['cstid']."'");
	}
	exit();
} elseif ( ( $_GET['action'] == 3 ) && ( is_numeric( $_GET['formid'] ) ) && ( $_GET['formid'] > 0 ) && ( $_GET['where'] == '7' ) ) { // Move invalid forms to 'trash' -> hide and mark as handled
	// Mark form as hidden
	DBQuery("UPDATE crm.form_data SET last_action = 'Form marked as hidden', form_handled = 1, trash = 1 WHERE id = '".$_GET['formid']."'");
	exit();
}

// Define the generic mail template
$sMailTo = "#EMAIL#";
$sMailSubject = "Information Request";
$sMailBody = "
Dear #NAME#,";

// Prepare mail template for JavaScript
function formatJS( $sValue ){
	$sValue = str_replace( "\n", "%0A", $sValue);
	$sValue = str_replace( "\r", "", $sValue);
	$sValue = str_replace( "'", "\\'", $sValue);

	return $sValue;
}

$sMailTo = formatJS( $sMailTo );
$sMailBody = formatJS( $sMailBody );
$sMailSubject = formatJS( $sMailSubject );

// Check duplicate entry
function isDuplicate( $aValue ){
	// Secure data
	$aValue['id'] = (int) $aValue[0]; // -> skip current form

	$aValue['person_email'] = mysql_real_escape_string( $aValue[1] );
	$aValue['company_name'] = mysql_real_escape_string( $aValue[2] );
	$aValue['person_phone'] = mysql_real_escape_string( $aValue[3] );
	$aValue['person_web'] = mysql_real_escape_string( $aValue[4] );

	$iNumRows = DBNumRows( "crm.form_data", "id <> '".$aValue['id']."' AND trash = 0 AND ( person_email LIKE '%".$aValue['person_email']."%' OR company_name LIKE '%".$aValue['company_name']."%' OR person_phone LIKE '%".$aValue['person_phone']."%' OR person_web LIKE '%".$aValue['person_web']."%' )" );
	if ( $iNumRows > 0 ){
		return true;
	}
	return false;
}

// Fetch forms list

// Construct search logic
if ( $_GET['action'] == "search" ){
	$bHasSearch = false;
	// Time search
	$sTime = "";
	if ( ( $_GET['from'] != "" ) && ( $_GET['to'] != "" ) ){
		$sTime = " ( submit_date >= '".$_GET['from']."' OR submit_date <= '".$_GET['to']."' )";
		$bHasSearch = true;
	}
	if ( $_GET['all'] != 'on' ){
		$bHandled = '';
	} else {
		$bHandled = " AND form_handled = 0 ";
	}

	// Text search - overkill
	if ( $_GET['search'] != "" ){
		// Search in all possible fields
		$sLogicalOperator = "OR";
		$sSearchType = 0; // 0 - LIKE, 1 - =
		$aFields = array( "form_name", "company_name", "person_name", "person_email", "company_hosts_network", "text_referral"); // To register a new search field, add the table column name to this array
		// Construct search query
		$sText = "(";
		for ( $i = 0; $i < count( $aFields ); $i++ ){
			$sText .= "`".$aFields[$i]."` ".( ( $sSearchType == 0 ) ? " LIKE " : ' = ' )." '".( ( $sSearchType == 0 ) ? "%" : '' ).$_GET['search'].( ( $sSearchType == 0 ) ? "%" : '' )."'";
			if ( $i < count( $aFields ) - 1 ){
				$sText .= " ".$sLogicalOperator." ";
			}
		}
		$sText .= ")";
		$bHasSearch = true;
	}
	// Construct the SQL statement
	if ( $bHasSearch == true ){
		$sSQL = " WHERE ".$sTime.( ( ( $sTime != "" ) && ( $sText != "" ) )? " AND ": "" ).$sText." ";
	}
	$rResource = DBQuery("SELECT * FROM crm.form_data ".$sSQL.( ( $sSQL != "" ) ? ' AND ' : ' WHERE ' )." trash = 0 ".$bHandled." ORDER BY id desc");
} else {
	$_GET['all'] = 'on';
	$rResource = DBQuery("SELECT * FROM crm.form_data WHERE trash = 0 AND form_handled = 0 ORDER BY id desc");
}
$iNumRows = mysql_num_rows( $rResource );

?>
<script>
// special ajax object
var _objAjaxHandled = null;
// Format mail template
var _mailTo;
var _mailTemplate;
_mailTo = '<?= $sMailTo ?>';
_mailSubject = '<?= $sMailSubject ?>';
_mailTemplate = '<?= $sMailBody ?>';
var _lMailTo;
var _lMailSubject;
var _lMailTemplate;
function formatTemplate( sMailTo, sName, sCompanyName ){
	_lMailTo = _mailTo.replace( "#EMAIL#", sMailTo );
	_lMailTemplate = _mailTemplate.replace( "#NAME#", sName );
	_lMailTemplate = _lMailTemplate.replace( "#COMPANY_NAME#", sCompanyName );
	_lMailSubject = _mailSubject.replace( "#NAME#", sName );
	_lMailSubject = _lMailSubject.replace( "#COMPANY_NAME#", sCompanyName );
}
function ajaxObject(){
	var _objAjax;
	if ( window.XMLHttpRequest ){
		_objAjax = new XMLHttpRequest();
	} else if (window.ActiveXObject) {
		_objAjax = new ActiveXObject("Microsoft.XMLHTTP");
	} else {
		_objAjax = null;
	}
	if ( _objAjax == null ) {
		return null; // Client does not support XMLHTTP
	}
	return _objAjax;
}
// Store CST ID Value using a 'dumb' ajax request ( no response handling )
function storeCSTID( icst_id, imasterid, iFormId ){
	// Similar to the voting mechanism
	var _objAjax = ajaxObject();

	_objAjax.onreadystatechange = function(){
		if ( _objAjax.readyState == 4 ){
			document.location.reload();
		}
	};
	_objAjax.open( "GET", "?page=inbound_pipeline&action=1&cstid=" + icst_id + "&masterid="+ imasterid + "&form_id=" + iFormId);
	_objAjax.send( null );
}
// Store the action request
function storeActionRequest( iFormId ){
	var _objAjax = ajaxObject();

	_objAjax.open( "GET", "?page=inbound_pipeline&action=2&form_id=" + iFormId );
	_objAjax.send( null );
}

function _reloadPage(){
	if ( _objAjaxHandled.readyState == 4 ){
		document.location.reload();
	}
}

function sendTo( iWhere, icst_id, sCompanyName, iFormId ){ // Send to -> see below
	// Mark current form as 'handled' AND send the email
	_objAjaxHandled = ajaxObject();

	_objAjaxHandled.onreadystatechange = _reloadPage; // Reload this page, once the form has been marked as handled
	_objAjaxHandled.open( "GET", "?page=inbound_pipeline&action=3&formid=" + iFormId + "&cstid=" + icst_id + "&where=" + iWhere + "&company_name=" + sCompanyName );
	_objAjaxHandled.send( null );
}

function importCRM( iFormId ){
	_objAjaxHandled = ajaxObject();

	_objAjaxHandled.onreadystatechange = _reloadPage; // Reload this page, once the form has been marked as handled
	_objAjaxHandled.open( "GET", "?page=inbound_pipeline&action=4&formid=" + iFormId );
	_objAjaxHandled.send( null );
}

function handleLMAction( oParent, icst_id, sMailTo, sName, sCompanyName, iFormId ){
	switch ( oParent.value ){
		case "1":
			formatTemplate( sMailTo, sName, sCompanyName );
			document.location = 'mailto:' + _lMailTo + '?body=' + _lMailTemplate + '&subject=' + _lMailSubject;
			// Make a "dumb" ajax request, showing that a request has been made
			storeActionRequest( iFormId );
			break;
		case "2":
			var imasterid = prompt("Existing Company Card Id:");
			storeCSTID( icst_id, imasterid, iFormId );
			break;
		case "3":
			// Import into CRM
			importCRM( iFormId );
			break;
		// Send to: 
		case "4": // CSI - tp@
		case "5": // TLA(VI) - hz@
		case "6": // PARTNER - ao@
		case "7": // TRASH ( Store in segment 'TRASH' )
		case "8": // PARTNER - <EMAIL> -> MINAS
		case "9": // PARTNER - awright@ -> Ashely Wright
			// Display some friendly error messages
			if ( oParent.value != "7" ){
				if ( sCompanyName == "" ){
					alert("The form does not contain a valid company name.");
					return;
				}
				if ( ( icst_id == "0" ) || ( icst_id == "" ) ){
					alert("The form is not attached to any Company Card ID. Try importing in the CRM.");
					return;
				}
			}
			sendTo( oParent.value, icst_id, sCompanyName, iFormId );
			break;
	}
}
</script>
<table width="100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr>
		<td colspan="<?php if ( $_GET['all'] != 'on' ) { echo "10"; } else { echo "9"; } ?>">
			<form method="GET" action="">
				<input type="hidden" name="page" value="inbound_pipeline">
				<input type="hidden" name="action" value="search">
				<table width="100%">
					<tr>
						<td>
							<b>Display Options:</b>
						</td>
					</tr>
					<tr>
						<td style="padding-left: 5px; width: 20%;">
							<input type="checkbox" name="all" <?= ( ( $_GET['all'] == 'on' ) ? 'checked' : '' ) ?>> Show all unhandled
						</td>
					<tr>
						<td  style="padding-left: 5px; width: 20%;">
							<input type="text" style="width: 50px;" size="50" name="from" value="<?= htmlspecialchars( $_GET['from'] ) ?>"> - <input type="text" style="width: 50px;" size="50" name="to" value="<?= htmlspecialchars( $_GET['to'] ) ?>"> Period
						</td>
					</tr>
					<tr>
						<td  style="padding-left: 5px; width: 20%;">
							<input type="text" name="search" value="<?= htmlspecialchars( $_GET['search'] ) ?>">
						</td>
					</tr>
					<tr>
						<td  style="padding-left: 5px; width: 20%;">
							<input type="submit" value="Search">
						</td>
					</tr>
				</table>
			</form>
		</td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td><b>Form</b></td>
		<td><b>Company</b></td>
		<td><b>Name</b></td>
		<td><b>Email</b></td>
		<td><b>Size (hosts in network)</b></td>
		<td><b>Source</b></td>
		<td><b>Duplicate</b></td>
		<td><b>Last action</b></td>
		<td><b>LM Action</b></td>
		<?php
		if ( $_GET['all'] != 'on' ){
			echo '<td><b>State</b></td>';
		}
		?>
	</tr>
	<?php
		// Function to check if there is a valid customer id for the current form
		function isValidCST( $iCSTId ){
			if ( ( $iCSTId != 0 ) && ( $iCSTId != "" ) ){
				return true;
			}
			return false;
		}
		// Output forms
		for ( $i = 0; $i < $iNumRows; $i++ ){
			$aRow = mysql_fetch_array( $rResource );
			$sState = DBGetRowValue("crm.cst", "cst.forecast_expectancy", "cst_id = '".(int) $aRow['cst_id']."'" );
			echo "<tr>";
			echo "<td><a ".( ( !isValidCST( $aRow['cst_id'] ) ) ? "" : "href='?page=customer&cst_id=".htmlspecialchars( $aRow['cst_id'] )."'" )." style='cursor: pointer;' onmousemove=\"ieMouseEvent( event )\" onmouseover=\"createHover( 'h".(int) $aRow['id']."', ".(int) $aRow['id']." );\" onmouseout=\"hideHover('h".(int) $aRow['id']."');\">".htmlspecialchars( $aRow['form_name'] )."</a></td>";
			echo "<td>".htmlspecialchars( $aRow['company_name'] )."</td>";
			echo "<td>".htmlspecialchars( $aRow['person_name'] )."</td>";
			echo "<td>".htmlspecialchars( $aRow['person_email'] )."</td>";
			echo "<td>".htmlspecialchars( ( ( $aRow['company_hosts_network'] == "- Please Select -" ) ? "" : $aRow['company_hosts_network'] ) )."</td>";
			echo "<td>".htmlspecialchars( ( ( $aRow['text_referral'] == "- Please Select -" ) ? "" : $aRow['text_referral'] ) )."</td>";
			echo "<td>".( ( isDuplicate( array( $aRow['id'], $aRow['person_email'], $aRow['company_name'], $aRow['person_phone'], $aRow['person_web'] ) ) == true ) ? "dup" :  '-' )."</td>";
			echo "<td>".htmlspecialchars( $aRow['last_action'] )."</td>";
			if ( $aRow['form_handled'] == 0 ){
				echo "<td><select autocomplete='off' onChange=\"handleLMAction( this, ".(int)$aRow['cst_id'].", '".formatJS( htmlspecialchars( $aRow['person_email'] ) )."', '".formatJS( htmlspecialchars( $aRow['person_name'] ) )."', '".formatJS( htmlspecialchars( $aRow['company_name'] ) )."', ".(int) $aRow['id']." )\">";
				echo "<option value='0' selected>- Select action -</option>";
				echo "<option value='1'>Request info</option>";
				if ( isValidCST( $aRow['cst_id'] ) ) {
					// Make a duplicate
					echo "<option value='2'>Make shadow</option>";
				} else {
					// Or import a new customer
					echo "<option value='3'>Import in CRM</option>";
				}
				echo "<option value='4'>Send to CSI</option>";
				echo "<option value='5'>Send to TLA</option>";
				echo "<option value='6'>Send to Partner</option>
					<option value='8'>Send to Minas</option>
					<option value='9'>Send to Ashley</option>";
				echo "<option value='7'>".( isValidCST( $aRow['cst_id'] ) ? "Send Company to TRASH" : "Send Form to TRASH" )."</option>";
				echo "</select></td>";
			} else {
				echo "<td>handled</td>";
			}
			if ( $_GET['all'] != 'on' ){
				echo "<td>".( ( $sState == "" ) ? "" : $sState."%" )."</td>";
			}
			echo "</tr>";
		}
	?>
	<tr>
		<td colspan="9"><br></td>
	</tr>


</tbody></table>
</form>

</body>
</html>
