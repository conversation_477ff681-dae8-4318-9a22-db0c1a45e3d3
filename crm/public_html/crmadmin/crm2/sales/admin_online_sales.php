<?php
// Access Level: 1
// Admin
// SM
// Lead Management
fVerifyAccess( $aRepData['person_id'], 1 );

// Save
$bSaved = false;
if ( $_GET['sale_id'] && $_GET['sale_status'] ) {
	// Set status
	DBQuery("UPDATE ca.shop SET shop.sale_status = '" . (int) $_GET['sale_status'] . "' WHERE shop.id = '" . (int) $_GET['sale_id'] . "' LIMIT 1");
	$bSaved = true;

	// If status == approved add insert 'Online Sale' entry
	if ( $_GET['sale_status'] == 1 && $_GET['cst_id'] > 0 ) {
		// Lock sale to customer id
		DBQuery("UPDATE ca.shop SET shop.cst_id = '" . (int) $_GET['cst_id'] . "' WHERE shop.id = '" . (int) $_GET['sale_id'] . "' LIMIT 1");

		// Update segment for customer
		$aCustomer = DBGetRow('crm.cst', "cst.cst_id = '" . $_GET['cst_id'] . "'");
		DBQuery("UPDATE crm.cst SET cst.segment_id = '1451' WHERE cst.cst_id = '" . $aCustomer['master_id'] . "' || cst.master_id = '" . $aCustomer['master_id'] . "'");
	}
}

// Select all inbound sales
$aSales = DBGetRows('ca.shop', '', 'sale_status, wp_transTime DESC');
?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Manage Online Sales';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td class="TablePadding">
				Manage the sales conducted through the online shop. After verifying through the WorldPay interface that a payment was successful, the status should be updated here accordingly. Please note. When a sale goes from "<?= fOnlineSaleStatus( 0 ) ?>" -> "<?= fOnlineSaleStatus( 1 ) ?>" then a sales entry is created along with it.<br>
<?= ( $bSaved ? '<br><b>Sale status updated.</b><br>' : '' ) ?>

<table width="100%" cellspacing="1" cellpadding="0">
<?php
while ( list($iKey, $aSale) = each($aSales) ) {
	// Output headline
	if ( $iLastStatus != (int) $aSale['sale_status'] || !isset($iLastStatus) ) {
		echo '<tr><td colspan="5"><br><b>' . fOnlineSaleStatus( $aSale['sale_status'] ) . '</b></td></tr>';
	}
	$iLastStatus = (int) $aSale['sale_status'];

	// Output date-headline
	if ( substr($aSale['wp_transTime'], 0, 10) != $sLastDate ) {
		echo '<tr><td></td><td colspan="4"><br><b>' . substr($aSale['wp_transTime'], 0, 10) . '</b></td></tr>';
	}
	$sLastDate = substr($aSale['wp_transTime'], 0, 10);

	// Get company details (if License Key has been tied to an account)
	$aCustomer = DBQueryGetRows("SELECT cst.name, accounts.account_id, accounts.cst_id, accounts.account_username, quantity from ca.license_keys, ca.accounts, crm.cst where license_keys.id = '" . $aSale['license_id'] . "' AND accounts.account_id = license_keys.account_id AND cst.cst_id = accounts.cst_id");

	// Output
	echo '<tr><td width="175" class="TablePadding">';
	if ( !$aSale['sale_status'] ) {
		echo '<select onChange="location = \'?page=admin_online_sales&sale_id=' . $aSale['id'] . '&cst_id=' . $aCustomer[0]['cst_id'] . '&sale_status=\' + this.value + \'\';"><option> - Change Status - </option><option value="1">' . fOnlineSaleStatus( 1 ) . '</option><option value="3">' . fOnlineSaleStatus( 3 ) . '</option></select>';
	} else { 
		echo '&nbsp;';
	}

	echo '</td><td width="200" style="padding-left: 5px;" valign="top">' . htmlspecialchars( ( !$aSale['company'] ? '- no company name -' : $aSale['company'] ) ) . '</b></td><td width="300" valign="top">' . ( $aCustomer ? '<a href="?page=customer&amp;cst_id=' . $aCustomer[0]['cst_id'] . '">' . htmlspecialchars($aCustomer[0]['name']) . '</a>' : '<font color="red">License Key not used</font>' ) . '</td><td valign="top" width="200">' . htmlspecialchars( $aSale['wp_name'] ) . '</td><td width="100" valign="top">' . htmlspecialchars( str_replace('CSI License Key: ', '', $aSale['wp_desc']) ) . '</td><td width="100" align="right" valign="top">' . htmlspecialchars( $aSale['wp_authAmountString'] ) . '&nbsp;&nbsp;</td><td valign="top">' . htmlspecialchars( $aSale['wp_email'] ) . '</td></tr>';
}
?>
</table>
				
			</td>
		</tr>
</table>
