<?php
// Approve selected
if ( isset( $_GET['cst_id'] ) ) {
	foreach ( $_GET['cst_id'] as $master_id => $value ) { // We only approve master cards
		DBQuery("UPDATE crm.cst SET approved = 1 WHERE cst_id = '".(int)$master_id."' LIMIT 1");
	}
}
// Partner ID in this case is an account_id, so we can track which customer was created by wich partner portal user.
$account_id = $_GET['account_id'];
$partner_id = DBGetRowValue( "ca.partner_profile", "partner_id", "account_id = '".(int)$account_id."'" );
$sort = "name";
$dir = "ASC";
if ( isset( $_GET['sort'] ) && isset( $_GET['dir'] ) ) {
	switch ( $_GET['sort'] ) {
		case "cst_id":
		case "name":
		case "approved":
			$sort = $_GET['sort'];
			break;
	}
	switch ( $_GET['dir'] ) {
		case "ASC":
		case "DESC":
			$dir = $_GET['dir'];
			break;
	}
}
$query = "SELECT master_id, cst_id, (SELECT approved FROM crm.cst WHERE cst.cst_id = A.master_id) AS approved,(SELECT name FROM crm.cst WHERE cst.cst_id = A.master_id) AS name FROM crm.cst AS A WHERE partner_id = '".(int)$partner_id."' AND case_name = 'card_partner' ORDER BY ".$sort." ".$dir;
$results = DBQuery( $query );

$output = "";
$j = 0;

for ( $i = 0; $i < mysql_num_rows( $results ); $i++ ) {
	$row = mysql_fetch_assoc( $results );

	$output .= "<tr style='background-color:".( $j % 2 == 0 ? 'lightgray' : 'white' ).";'>
		<td style='padding: 5px;'>
			<input type='checkbox' style=\"display: inline; width: 10px;\" name='cst_id[".(int)$row['master_id']."]'>
			<a href='?page=customer&cst_id=".(int)$row['cst_id']."' target='_blank'>".(int)$row['cst_id']."</a>
		</td>
		<td style='padding: 5px;'>
			".( $row['approved'] == 1 ? "Yes" : "No" )."
		</td>
		<td style='padding: 5px;'>
			".htmlspecialchars( $row['name'] )."
		</td>
	</tr>
	";
	$j++;
}
?>
<table>
<form action="index.php" method="GET">
<input type='hidden' name="page" value="customers">
<input type='hidden' name="account_id" value="<?= (int)$_GET['account_id'] ?>">
<input type='submit' value="Approve">
<tr>
	<td style='padding: 5px;'>
		<a href="?page=customers&account_id=<?= (int)$_GET['account_id'] ?>&sort=cst_id&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>">Customer ID</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?page=customers&account_id=<?= (int)$_GET['account_id'] ?>&sort=approved&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>">Approved</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?page=customers&account_id=<?= (int)$_GET['account_id'] ?>&sort=name&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>">Customer Name</a>
	</td>
</tr>
<?php echo $output; ?>
</table>
</form>