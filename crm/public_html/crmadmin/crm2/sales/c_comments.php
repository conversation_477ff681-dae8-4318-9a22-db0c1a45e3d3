<?php
// Counter
$iLimit = 0;

// Select comments
$aComments = DBGetRows('crm.comments', "comments.cst_id = '" . $_GET['cst_id'] . "'", 'comments.added DESC');

?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Loop through comments
	while ( list( $iKey, $aComment ) = each($aComments) ) {
		// Only display comment - if it has content
		if ( !$aComment['comment'] ) {
			continue;
		}
	?>
	<tr>
		<td>
			<?= fCommentBox( $aComment['type'], $aComment['person_id'], $aComment['added'], $aComment['comment']) ?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?php
		// Limit
		if ( !$_GET['show_all'] && ++$iLimit > 10 ) {
			$bLimited = true;
			break;
		}
	}

	// Display option to show all comments?
	if ( $bLimited == true ) {
		echo '
	<tr>
		<td><a href="?page=customer&amp;cst_id=' . intval($_GET['cst_id']) . '&amp;show_all=1">Show all comments</a></td>
	</tr>';
	}

	// Admin; Delete comment history?
	if ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ) {
		echo '
	<tr>
		<td>[ <a href="?page=customer&amp;cst_id=' . (int) $_GET['cst_id'] . '&amp;action=delete_comments" onClick="return confirm(\'Are you sure you wish to delete the comment(s) on this customer card?\');">Delete Comment(s)</a> ]</td>
	</tr>';
	}
	?>
</table>
