<?php
// Access Level: 1
// Admin
// SM
// Lead Management
fVerifyAccess( $aRepData['person_id'], 1 );

// Save
$bSaved = false;
if ( $_GET['save'] == 1 )
{
	DBQuery("UPDATE crm.cst SET cst.cluster_id = '" . $_GET['mother_id'] . "' WHERE cst.cst_id = '" . $_GET['child_id'] . "'");
	$bSaved = true;
}

?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Create Company Cluster';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td class="TablePadding">
				<?= $bSaved ? 'Data saved<br><br>' : '' ?>
				<form method="GET" action="?">
				<input type="hidden" name="page" value="admin_cluster">
				<input type="hidden" name="save" value="1">
				<b>Child Company ID:</b><br>
				<input type="text" name="child_id" style="width: 200px;"><br>
				<br>
				<b>Mother Company ID:</b><br>
				<input type="text" name="mother_id" style="width: 200px;"><br>
				<br>
				<input type="submit" value="Save">
				</form>
			</td>
		</tr>
</table>
<br><br>
