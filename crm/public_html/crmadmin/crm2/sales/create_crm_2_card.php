<?php
require_once "/home/<USER>/secrets.php";

// Open Database Connection
mysql_connect('192.168.100.100', 'crm', DB_PASS);
mysql_select_db('crm');

$sName = $_GET['name'];

{
	// Add to CRM
	$sCCQuery = "INSERT INTO crm.cst (name, customer_marked_dead, case_name, case_opened) VALUES('" . mysql_escape_string($sName) . "', 0, 'Company Card', now())\n";

	// Create company card
	mysql_query($sCCQuery);
	$iCSTID = mysql_insert_id();
	echo "<a href='index.php?page=customer&cst_id=" . $iCSTID . "'>Company Card: " . $iCSTID . "</a>";

	// Card's
	$sNSIQuery = "INSERT INTO crm.cst (name, customer_marked_dead, case_name, case_opened, master_id) VALUES('" . mysql_escape_string($sName) . "', 0, 'card_nsi', now(), '" . $iCSTID . "')\n";
	$sVIQuery = "INSERT INTO crm.cst (name, customer_marked_dead, case_name, case_opened, master_id) VALUES('" . mysql_escape_string($sName) . "', 0, 'card_vi', now(), '" . $iCSTID . "')\n";
	$sCRCQuery = "INSERT INTO crm.cst (name, customer_marked_dead, case_name, case_opened, master_id) VALUES('" . mysql_escape_string($sName) . "', 0, 'card_crc', now(), '" . $iCSTID . "')\n";

	// Create card's
	mysql_query($sNSIQuery);
	$iNSICSTID = mysql_insert_id();
	mysql_query($sVIQuery);
	$iVICSTID = mysql_insert_id();
	mysql_query($sCRCQuery);
	$iCRCCSTID = mysql_insert_id();

/*	// Add Comment: $aResult['name'] is responsible for $aResult['clients_responsible'] clients out of $aResult['clients_total'].
	$sQuery = "INSERT INTO crm.comments (comment, added, cst_id, person_id, type) values('" . mysql_escape_string($aResult['name']) . " is responsible for " . mysql_escape_string($aResult['clients_responsible']) . " clients out of " . mysql_escape_string($aResult['clients_total']) . ".', now(), '" . $iNSICSTID . "', '0', 1)";
	DBQuery($sQuery);
	$sQuery = "INSERT INTO crm.comments (comment, added, cst_id, person_id, type) values('" . mysql_escape_string($aResult['name']) . " is responsible for " . mysql_escape_string($aResult['clients_responsible']) . " clients out of " . mysql_escape_string($aResult['clients_total']) . ".', now(), '" . $iVICSTID  . "', '0', 1)";
	DBQuery($sQuery);

	// Contact
	$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact, invoice) VALUES('" . $iNSICSTID . "', '1', '" . mysql_escape_string($aResult['name']) . "', '" . mysql_escape_string($aResult['phone']) . "', '" . mysql_escape_string($aResult['email']) . "', 1, 1)";
	DBQuery($sQuery);
	$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact, invoice) VALUES('" . $iVICSTID  . "', '1', '" . mysql_escape_string($aResult['name']) . "', '" . mysql_escape_string($aResult['phone']) . "', '" . mysql_escape_string($aResult['email']) . "', 1, 1)";
	DBQuery($sQuery);

	// Add link back to NSI Account Request Table and mark as Imported
	$sQuery = "UPDATE website.nsi_account_requests SET cst_id = '" . $iCSTID . "', status = 1 WHERE id = '" . $aResult['id'] . "' LIMIT 1";
	DBQuery($sQuery);
*/
}



?>
