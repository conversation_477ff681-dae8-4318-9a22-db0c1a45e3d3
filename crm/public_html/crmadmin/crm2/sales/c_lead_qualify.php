<?php
$rQuery = mysql_query("SELECT * FROM lead_qualifications WHERE lead_cstid='".$GLOBALS['aCustomer']['Case']['cst_id']."'") or die(mysql_error());
$aLeadData = mysql_fetch_assoc($rQuery);
if ($GLOBALS['aRepData']['person_level'] == 2){
	echo "<a href=\"?page=lead_approval&approve=".$aLeadData['lead_id']."\" target=\"_self\">Approve</a>&nbsp;<a href=\"?page=lead_approval&decline=".$aLeadData['lead_id']."\" target=\"_self\">Decline</a>";
}
?>
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%" valign="bottom">
			Status
		</td>
		<td width="80%">
			<?php
			switch($aLeadData['lead_status']){
				case "new":
					echo "New";
					break;
					
				case "waiting":
					echo "Waiting for approval";
					break;
					
				case "approved":
					echo "Approved";
					break;
					
				case "rejected":
					echo "Rejected";
					break;
					
				default:
					echo "N/A";
					break;
			}
			?> 
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="bottom">
			Decision maker identified
		</td>
		<td width="80%">
			<input type="checkbox" value="identified" id="fdecimaker_identified" name="fdecimaker_identified" <?php echo ($aLeadData['lead_id_deci_finance'] == 1) ? "checked " : ""; ?><?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? "readonly " : ""; ?>/>&nbsp;Financial
			<input type="checkbox" value="identified" id="tdecimaker_identified" name="tdecimaker_identified" <?php echo ($aLeadData['lead_id_deci_tech'] == 1) ? "checked " : ""; ?><?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? "readonly " : ""; ?>/>&nbsp;Technical 
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="top">
			Create want
		</td>
		<td width="80%">
			<textarea id="createwant" name="createwant" style="color: #000000; width: 50%"<?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? " readonly" : ""; ?>><?php echo htmlspecialchars($aLeadData['lead_createwant']); ?></textarea>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="top">
			Competitor
		</td>
		<td width="80%">
			<select name="competitor" style="color: #000000; width: 40%;" id="competitor"<?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? " readonly" : ""; ?>>
				<option value="0" <?php echo ($aLeadData['lead_competitor'] == "0" || !isset($aLeadData['lead_competitor'])) ? "selected " : ""; ?>>N/A</option>
				<?php
				$rQuery = mysql_query("SELECT * FROM competitors ORDER BY competitor_name ASC");
				while($aRow = mysql_fetch_assoc($rQuery)){
					$aOptions[] = array('value' => $aRow['competitor_id'], 'label' => htmlspecialchars($aRow['competitor_name']));
				}
				foreach($aOptions as $aOption){
					echo "<option value=\"".$aOption['value']."\" ".(($aLeadData['lead_competitor'] == $aOption['value']) ? "selected " : "")."/>".$aOption['label'];
				}
				?>
			</select>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="bottom">
			Snapshot done
		</td>
		<td width="80%">
			<input type="checkbox" value="snapshotdone" id="snapshot_done" name="snapshot_done" <?php echo ($aLeadData['lead_snapshot_done'] == 1) ? "checked " : ""; ?><?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? "readonly " : ""; ?>/>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="top">
			Result Walkthrough
		</td>
		<td width="80%">
			<textarea id="resultwalkthrough" name="resultwalkthrough" style="color: #000000; width: 50%" id="resultwalkthrough"<?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? " readonly" : ""; ?>><?php echo htmlspecialchars($aLeadData['lead_walkthrough_result']); ?></textarea>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="20%" valign="bottom">
			Callback time
		</td>
		<td width="80%">
			<input type="text" value="<?= $GLOBALS['aCustomer']['Case']['appointment'] ?>" id="appointmentlead" name="appointmentlead" style="color: #000000; width: 50%" readonly<?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? " readonly" : ""; ?>>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?php
	if ($GLOBALS['aRepData']['person_level'] == 3){ 
		?>
		<tr>
			<td width="20%" valign="bottom">
				Save for approval
			</td>
			<td width="80%">
				<input type="checkbox" name="saveapproval" id="saveapproval" value="true" <?php echo ($aLeadData['lead_status'] == "waiting") ? "checked " : ""; ?><?php echo ($aLeadData['lead_status'] == "waiting" || $GLOBALS['aRepData']['person_level'] == 1) ? "readonly " : ""; ?>></input>
			</td>
		</tr>
		<?php
	}
	?>
</table>

<div id="calendar" style="display: none; position: absolute; width: 350px; height: 550px; background: #FFFFFF; border: 1px solid #000000;">
<iframe src="?page=calendar" width="100%" height="100%" frameborder="0"></iframe>
</div>
