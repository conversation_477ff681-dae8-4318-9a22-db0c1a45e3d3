<?php
// No injection
$iNumberSales = 0;
$iTotalRevenue = 0;
$iTotalPaidRevenue = 0;
$iPaidNumberSales = 0;
$iCanvasRevenue = 0;
$iRecurrenceRevenue = 0;
$iCancelledTotal = 0;
$iNumberCancelledSales = 0;
$iNumberTrials = 0;
$sSalesOutput = '';
$sTrialsOutput = '';
$sCancelledOutput = '';

// Period, input or today
$sFrom = ( $_POST['from'] ? $_POST['from'] : date('Y-m-d') );
$sTo = ( $_POST['to'] ? $_POST['to'] : date('Y-m-d') );

// Select all from saleslog
$res = mysql_query("select * from lead_qualifications, cst where (lead_submit_date >= '" . $sFrom . "' && lead_submit_date <= '" . $sTo . "') && lead_rep = '" . $aRepData['person_id'] . "' AND cst.cst_id = lead_qualifications.lead_cstid order by lead_submit_date");
$iTotalCustomers = mysql_num_rows( $res );

?>

<form method="POST" action="?page=isa_report">
<table width="100%" cellpadding="0" cellspacing="0">
	<?php
	$sTitle = 'ISA Snapshot Report';
	$iColSpan = 6;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td colspan="2" class="TablePadding">
			<b>Select period to display</b><br>
			<input type="text" value="<?= htmlspecialchars($sFrom) ?>" name="from" style="width: 150px;"> - <input type="text" value="<?= htmlspecialchars($sTo) ?>" name="to" style="width: 150px;"> <input type="submit" value="Display">
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Short Report';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td colspan="1" class="TablePadding">
			No. of Customers:<br>
		</td>
		<td colspan="5" valign="top">
			<?=$iTotalCustomers?><br>
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'List of customers';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td colspan="6">
			<table width="100%" cellpadding="0" cellspacing="0">
				<tr class="TableSubHeadline TableGreyBackground TablePadding">
					<td>Company</td>
					<td>Next appointment</td>
					<td>Status</td>
					<td>Submit date</td>
					<td>Decision identified</td>
					<td>Decision maker</td>
				</tr>
				<?php
					for ( $i = 0; $i < $iTotalCustomers; $i++ ) {
						$row = mysql_fetch_assoc( $res );
						echo "<tr class=\"".( $i % 2 != 0 ? "TableGreyBackground" : "" )."\">
							<td>
								<a href='?page=customer&cst_id=".$row['lead_cstid']."'>".htmlspecialchars( $row['name'] )."</a>
							</td>
							<td>
								".$row['lead_next_appointment']."
							</td>
							<td>
								".$row['lead_status']."
							</td>
							<td>
								".$row['lead_submit_date']."
							</td>
							<td>
								".( $row['lead_id_deci_finance'] == 1 ? "Yes" : "No" )."
							</td>
							<td>
								".( $row['lead_id_deci_tech'] == 1 ? "Technical" : "Financial" )."
							</td>
						</tr>";
					}
				?>
			</table>
		</td>
	</tr>
</table>

</form>
