<?php
// No injection
$iNumberSales = 0;
$iTotalRevenue = 0;
$iTotalPaidRevenue = 0;
$iPaidNumberSales = 0;
$iCanvasRevenue = 0;
$iRecurrenceRevenue = 0;
$iCancelledTotal = 0;
$iNumberCancelledSales = 0;
$iNumberTrials = 0;
$sSalesOutput = '';
$sTrialsOutput = '';
$sCancelledOutput = '';

// Period, input or today
$sFrom = ( $_POST['from'] ? $_POST['from'] : date('Y-m-d') );
$sTo = ( $_POST['to'] ? $_POST['to'] : date('Y-m-d') );

// Select all from saleslog
$res = mysql_query("select * from saleslog where ((sold_date >= '" . $sFrom . "' && sold_date <= '" . $sTo . "') || ((status = 3 || status = 6 || status = 7) && (status_date >= '" . $sFrom . "' && status_date <= '" . $sTo . "'))) " . " && person_id = '" . $aRepData['person_id'] . "'" . ( $_GET['this_lang_id'] ? " && lang_id = '" . $_GET['this_lang_id'] . "'" : '' ) . " order by sold_date");

// Loop over result
while ( $row = mysql_fetch_array($res) ) {
	// Calculate product price
	$iProductPrice = $row['product_price'] - $row['discount'];
	$iProductPriceEuro = round( ( $iProductPrice * $row['currency_exchange_rate'] ) / $row['currency_exchange_rate_euro'], 2 );

	// Select other data about customer
	$x_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
	$x_row = mysql_fetch_array($x_res);

	// Build list of all customer id's
	if ( $x_row['master_id'] ) {
		$iMasterID = $x_row['master_id'];
	} else {
		$iMasterID = $x_row['cst_id'];
	}

	$sCustomerIDs = $iMasterID . ',';
	$rAll = mysql_query("SELECT * from crm.cst WHERE master_id = '" . $iMasterID . "'");
	while ( $aAll = mysql_fetch_array($rAll) ) {
		$sCustomerIDs .= $aAll['cst_id'] . ',';
	}

echo "<!-- All: $sCustomerIDs -->\n";
	
	// Canvas or Recurrence
	{
		$sSaleType = '';
		$t_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $row['product_category'] . "'");
		if ( mysql_num_rows($t_res) == 0 && $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 ) {
			if ( $iProductPrice > 0 ) {
				$iCanvasRevenue += $iProductPriceEuro;
				$sSaleType = 'Canv.';
			}
		} elseif ( mysql_num_rows($t_res) > 0 && $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 ) {
			if ( $iProductPrice > 0 ) {
				$iRecurrenceRevenue += $iProductPriceEuro;
				$sSaleType = 'Recu.';
			}
		}
	}

	// Check if been cancelled or is good
	if ( $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 ) {
		// Output
		if ( $iProductPrice ) {
			// Determine Class
			$sClassSalesOutput = ( $sClassSalesOutput === '' ? ' class="TableGreyBackground"' : '' );

			// HTML Content
			$sSalesOutput .= '<tr' . $sClassSalesOutput . fRowLink('?page=customer&cst_id=' . $row['cst_id'] . '&right=overview') . '><td class="TablePadding">' . htmlspecialchars($x_row['name']) . '</td>';
			$sSalesOutput .= '<td>' . $row['product_name'] . ' (' . number_format( $iProductPrice, 2) . ')</td>';
			$sSalesOutput .= '<td>' . number_format( $iProductPriceEuro, 2) . ' (' . $sSaleType . ')</td>';
			$sSalesOutput .= '<td>' . fDifferenceMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
			$sSalesOutput .= '<td>' . fSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . '</td>';
			$sSalesOutput .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';
		} else {
			// Determine Class
			$sClassTrialsOutput = ( $sClassTrialsOutput === '' ? ' class="TableGreyBackground"' : '' );

			// HTML Content
			$sTrialsOutput .= '<tr' . $sClassTrialsOutput . fRowLink('?page=customer&cst_id=' . $row['cst_id'] . '&right=overview') . '><td class="TablePadding">' . htmlspecialchars($x_row['name']) . '</td>';
			$sTrialsOutput .= '<td>' . $row['product_name'] . '</td>';
			$sTrialsOutput .= '<td>' . number_format( $iProductPriceEuro, 2) . '</td>';
			$sTrialsOutput .= '<td>' . fDifferenceMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
			$sTrialsOutput .= '<td>' . ( time() > strtotime($row['expires_date']) ? 'Expired' : 'Active' ) . '</td>';
			$sTrialsOutput .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';
		}

		// Sold total
		$iTotalRevenue += $iProductPriceEuro;

		// Totalt payed revenue
		if ( $row['status'] == 2 ) {
			$iTotalPaidRevenue += $iProductPriceEuro;
			$iPaidNumberSales++;
		}

		// Unit counter
		if ( $iProductPrice ) {
			$iNumberSales++;
		} else {
			$iNumberTrials++;
		}
	} else {
		// Determine Class
		$sClassCancelledOutput = ( $sClassCancelledOutput === '' ? ' class="TableGreyBackground"' : '' );

		// HTML Content
		$sCancelledOutput .= '<tr' . $sClassCancelledOutput . fRowLink('?page=customer&cst_id=' . $row['cst_id'] . '&right=overview') . '><td class="TablePadding">' . htmlspecialchars($x_row['name']) . '</td>';
		$sCancelledOutput .= '<td>' . $row['product_name'] . ' (' . number_format( $iProductPrice, 2) . ')</td>';
		$sCancelledOutput .= '<td>' . number_format( $iProductPriceEuro, 2) . '</td>';
		$sCancelledOutput .= '<td>' . fDifferenceMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
		$sCancelledOutput .= '<td>' . fSaleStatus($row['status']) . '</td>';
		$sCancelledOutput .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';

		// Cancelled total
		$iCancelledTotal += $iProductPriceEuro;

		// Unit counter
		$iNumberCancelledSales++;
	}
}

// Totals
$sSalesOutput .= '<tr><td><br></td></tr>';
$sSalesOutput .= '<tr><td class="TablePadding"><b>Total revenue</b></td><td><b>' . $iNumberSales . '</b></td><td colspan="3"><b>' . number_format( $iTotalRevenue, 2) . '</b></td></tr>';
$sSalesOutput .= '<tr><td><br></td></tr>';
$sSalesOutput .= '<tr><td class="TablePadding"><b>Total payed revenue</b></td><td><b>' . $iPaidNumberSales . '</b></td><td colspan="3"><b>' . number_format( $iTotalPaidRevenue, 2) . '</b></td></tr>';

$sTrialsOutput .= '<tr><td><br></td></tr>';
$sTrialsOutput .= '<tr><td class="TablePadding"><b>Total</b></td><td><b>' . $iNumberTrials . '</b></td><td colspan="3"></td></tr>';

$sCancelledOutput .= '<tr><td><br></td></tr>';
$sCancelledOutput .= '<tr><td class="TablePadding"><b>Total revenue</b></td><td><b>' . $iNumberCancelledSales . '</b></td><td colspan="3"><b>' . number_format( $iCancelledTotal, 2) . '</b></td></tr>';
?>

<form method="POST" action="?page=sale_report">
<table width="100%" cellpadding="0" cellspacing="0">
	<?php
	$sTitle = 'Sale Report';
	$iColSpan = 6;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td colspan="2" class="TablePadding">
			<b>Select period to display</b><br>
			<input type="text" value="<?= htmlspecialchars($sFrom) ?>" name="from" style="width: 150px;"> - <input type="text" value="<?= htmlspecialchars($sTo) ?>" name="to" style="width: 150px;"> <input type="submit" value="Display">
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Short Report';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td colspan="1" class="TablePadding">
			No. of Customers:<br>
			<br>
			Total Revenue:<br>
			Total Revenue Paid:<br>
			<br>
			Total Revenue Canvas:<br>
			Total Revenue Recurrence:<br>
			<br>
			No. of Free Trials:<br>
			<br>
			No. of Cancellations:<br>
			Total Revenue Cancelled:<br>
		</td>
		<td colspan="5" valign="top">
			<?=$iNumberSales?><br>
			<br>
			<?=number_format( ($iTotalRevenue ? $iTotalRevenue : 0), 2)?><br>
			<?=number_format( ($iTotalPaidRevenue ? $iTotalPaidRevenue : 0), 2)?><br>
			<br>
			<?=number_format( ($iCanvasRevenue ? $iCanvasRevenue : 0), 2)?><br>
			<?=number_format( ($iRecurrenceRevenue ? $iRecurrenceRevenue : 0), 2)?><br>
			<br>
			<?=$iNumberTrials?><br>
			<br>
			<?=($iNumberCancelledSales ? $iNumberCancelledSales : 0)?><br>
			<?=number_format( ($iCancelledTotal ? $iCancelledTotal : 0), 2)?><br>
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Sales';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td width="22%" class="TableSubHeadline TableGreyBackground TablePadding"><b>Company</b></td>
		<td width="32%" class="TableSubHeadline TableGreyBackground"><b>Product</b></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground"><b>Amount</b></td>
		<td width="20%" class="TableSubHeadline TableGreyBackground"><b>Period</b></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground"><b>Status</b></td>
		<td width="6%" class="TableSubHeadline TableGreyBackground"><b>Commi.</b></td>
	</tr>

	<?= $sSalesOutput ?>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Free Trials';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td class="TableSubHeadline TableGreyBackground TablePadding"><b>Company</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Product</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Amount</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Period</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Status</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Commi.</b></td>
	</tr>

	<?=$sTrialsOutput?>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Cancelled Sales';
	$iColSpan = 6;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td class="TableSubHeadline TableGreyBackground TablePadding"><b>Company</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Product</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Amount</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Period</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Status</b></td>
		<td class="TableSubHeadline TableGreyBackground"><b>Commi.</b></td>
	</tr>

	<?=$sCancelledOutput?>

	<tr>
		<td><br><br></td>
	</tr>
</table>

</form>
