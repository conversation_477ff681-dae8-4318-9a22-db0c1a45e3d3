<?php
if ( $_GET['team'] ) {
	switch ( $_GET['team'] ) {
		case 2:
			$sTeam = 'CSI Team';
			$iDepartment = 2;
			break;
	}

	$rep_res = mysql_query("SELECT * FROM crm.salespeople " . ( $iDepartment > 0 ? " WHERE department = '" . $iDepartment . "'" : '' ) . " AND person_level=3 ORDER BY name");
	while ( $rep_row = mysql_fetch_array($rep_res) ) {
		$sSalesReps .= $rep_row['person_id'] . ',';
		$sSalesRepsNames .= '<font color="' . ( $rep_row['display'] == 1 ? 'green' : 'red' ) . '">' . htmlspecialchars($rep_row['name']) . '</font>, ';
	}
	$sSalesReps = trim($sSalesReps, ',');
	$sSalesRepsNames = trim($sSalesRepsNames, ', ');
	$sTeam .= ' (' . $sSalesRepsNames . ')';
	$_GET['export'] = 1;
} else {
        // Sanitize $_GET['person_id']
       	$sSalesReps = (int) $_GET['person_id'];
}

$row['forecast_start_month'] = '2007-01';
$row['forecast_months_start'] = 1;

// Overwrite 'forecast_months'
{
        // Start
        list($iStartYear, $iStartMonth) = split('-', $row['forecast_start_month']);
        $iStartMonths = (($iStartYear*12) + $iStartMonth) - 1;

        // Months left of "start" year
        $iMonthsLeft = $iStartMonths % 12;

        // How long should it go forth
        $iYears = (int) date('Y') - $iStartYear + 2;

        // Set it up
        $row['forecast_months'] = ($iYears * 12) - $iMonthsLeft;
}

if ( $_GET['from'] ) {
	list($iInputStartYear, $iInputStartMonth) = split('-', $_GET['from']);
	$iInputStartMonths = (($iInputStartYear*12) + $iInputStartMonth) - 1;
	$row['forecast_months_start'] = ($iInputStartMonths - $iStartMonths) + 1;
	$row['forecast_months'] = $row['forecast_months_start'] + $_GET['to'] - 1;
}

if ( $_GET['save'] && $bUpdate ) {

	// Delete all month values
	mysql_query("DELETE FROM crm.forecast WHERE person_id = '" . $_GET['person_id'] . "'");
	
	// Loop over each month value and insert it
	for ( $i = 1; $i<=$row['forecast_months'] ; $i++ ) {
		mysql_query("INSERT INTO crm.forecast (month, revenue, person_id) VALUES('" . $i . "', '" . preg_replace('[^0-9]*', '', $_POST['month_' . $i]) . "', '" . $_GET['person_id'] . "')");
	}

	// Redirect to page - avoid link and refresh updates
	header("Location: lead_report.php?person_id=" . htmlspecialchars($_GET['person_id']) . "&from=".htmlspecialchars($_GET['from'])."&to=".htmlspecialchars($_GET['to'])."&status=1");
	exit();
}

// Content starts here
?>
<form method="GET" action="">
	<input type="hidden" name="page" value="isatargets">
	<input type="hidden" name="right" value="overview"
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td>
				<b>Select sales person</b><br>
				<select name="person_id"><option value=""> - select - </option>
				<?php
				// Select all salespeople
				//echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople WHERE person_level=3 ORDER BY name', 'person_id', 'name');
				$aPersons = DBGetRows("crm.salespeople", "person_level=3 AND display=1 AND department=2", "name");
				foreach($aPersons as $aPerson){
					echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\">".htmlspecialchars($aPerson['name'])."</option>";
				}
				?>				
				</select>
				<br><br>
				<b>Or, view data for a whole team:</b><br>
				<label><b><input type="checkbox" name="team" <?= ($_GET[team] == 2 ? 'checked' : '') ?> value="2" style="width: 15px;"> CSI Team</b></label><br>
				<br>
				<b>Specify date range: YYYY-MM - X Months</b><br>
				<input type="text" name="from" value="<?= htmlspecialchars( $_GET['from'] ? $_GET['from'] : $row['forecast_start_month'] ) ?>" maxlength="7" style="width: 100px;"> - <input type="text" maxlength="7" name="to" value="<?= htmlspecialchars( $_GET['to'] ? $_GET['to'] : $row['forecast_months'] ) ?>" style="width: 100px;"><br>
			</td>
		</tr>
		<tr>
			<td>&nbsp;</td>
		</tr>
		<tr>
			<td><input type="submit" value="Display" class="submit" style="width: 10%;"></td>
		</tr>
	</table>
</form>
<table width="100%" border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td style="width: 200px;"><b>Period</b></td>
		<td style="width: 200px; text-align: right;"><b>Budget</b></td>
		<td style="width: 200px; text-align: right;"><b>Realised</b></td>
		<td style="width: 200px; text-align: right;"><b>Performance</b></td>
		<td>&nbsp;</td>
	</tr>
	<?php
	for ( $i = $row['forecast_months_start'] ; $i<=$row['forecast_months'] ; $i++ ) {
		$s_res = mysql_query("SELECT SUM(revenue) as revenue FROM crm.forecast WHERE person_id IN(" . $sSalesReps . ") AND month = '" . $i . "' LIMIT 1");
		$s_row = mysql_fetch_array($s_res);
		list($year, $month) = split('-', $row['forecast_start_month']);
		$date = date('Y-m', mktime(0, 0, 0, ($month+$i-1), 1, $year));
		list($year, $month) = split('-', $date);
		
		$query = mysql_query("SELECT COUNT(*) AS count FROM leadlog WHERE lead_person_id IN(".$sSalesReps.") AND SUBSTRING(lead_approve_date, 1, 7)='".$year."-".$month."'");
		$data = mysql_fetch_assoc($query);
		$iRealised = $data['count'];
		
		$iPerformance = ($iRealised/$s_row['revenue'])*100;
		
		if ( $year == date('Y') && $month == date('m') ) {
			$sColor = '#C2EABD';
			$sDescription = 'Current';
		} elseif ( $year < date('Y') || ($year == date('Y') && $month < date('m') ) ) {
			$sColor = '#DEDEDE';
			$sDescription = 'Historic';
		} else {
			$sColor = '#FFFFFF';
			$sDescription = 'Future';
		}
		
		$aTotals[$year]['budget'] = $aTotals[$year]['budget'] + $s_row['revenue'];
		$aTotals[$year]['realised'] = $aTotals[$year]['realised'] + $iRealised;
					
		if ($year != $lastyear){
			if ($lastyear){
				$iTotalPerformance = ($aTotals[$lastyear]['realised']/$aTotals[$lastyear]['budget'])*100;
				echo "<tr style=\"background: black;\"><td style=\"padding-left: 10px; color: #FFFFFF;\">Total for ".$lastyear."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".$aTotals[$lastyear]['budget']."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".$aTotals[$lastyear]['realised']."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".number_format($iTotalPerformance, 2)."%</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr>";
			}
			echo "<tr><td><b>".$year."</b></td></tr>";
		}
		echo "<tr style=\"background: ".$sColor.";\">";
		echo "<td style=\"padding-left: 10px; border-bottom: 1px solid #ffffff;\">".$date." (".$sDescription.")</td>";
		if ($bUpdate and !strstr($sSalesReps, ",")){
			echo "<td><input type=\"text\" style=\"width: 100%; text-align: right;\" tabindex=\"".(2+$i)."\" name=\"month_".$i."\" value=\"".number_format($s_row['revenue'])."\">";
		} else {
			echo "<td style=\"border-bottom: 1px solid #ffffff; text-align: right;\">".number_format($s_row['revenue'])."</td>";
		}
		//echo "<td style=\"border-bottom: 1px solid #ffffff; text-align: right;\">".number_format($s_row['revenue'])."</td>";
		echo "<td style=\"border-bottom: 1px solid #ffffff; text-align: right;\">".number_format($iRealised)."</td>";
		echo "<td style=\"border-bottom: 1px solid #ffffff; text-align: right;\">".number_format($iPerformance, 2)." %</td>";
		echo "<td style=\"border-bottom: 1px solid #ffffff; text-align: right;\">&nbsp;</td>";
		echo "</tr>";
		
		$lastyear = $year;
	}

	$iTotalPerformance = ($aTotals[$lastyear]['realised']/$aTotals[$lastyear]['budget'])*100;
	echo "<tr style=\"background: black;\"><td style=\"padding-left: 10px; color: #FFFFFF;\">Total for ".$lastyear."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".$aTotals[$lastyear]['budget']."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".$aTotals[$lastyear]['realised']."</td><td style=\"padding-left: 10px; color: #FFFFFF; text-align: right;\">".number_format($iTotalPerformance, 2)."%</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr>";
	
	if ($bUpdate){
		echo "<tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td><input type=\"submit\" value=\"Update Targets\" class=\"submit\"></td></tr>";
	}
	?>
</table>
