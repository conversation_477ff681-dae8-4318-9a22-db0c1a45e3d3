<script>
// Define current stage
var iCurrentStage = <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] < 26 ? 1 : 2 ?>;
</script>
<div id="stage_1" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] < 26 ? '' : 'style="display: none;"' ?>>
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="3"><b>Progress</b></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="0" name="forecast_expectancy" <?= !$GLOBALS['aCustomer']['Case']['forecast_expectancy'] ? 'checked' : '' ?>><b>0%</b> - No Contact</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="10" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 10 ? 'checked' : '' ?>><b>10%</b> - Contact Identified</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="25" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 25 ? 'checked' : '' ?>><b>25%</b> - Qualified &amp; Solution Match</label></td>
	</tr>
</table>
</div>

<div id="stage_2" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] > 25 ? '' : 'style="display: none;"' ?>>
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="3"><b>Price Revealed</b></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="40" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 40 ? 'checked' : '' ?>><b>40%</b> - Negative reaction</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="50" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 50 ? 'checked' : '' ?>><b>50%</b> - Neutral reaction</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="60" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 60 ? 'checked' : '' ?>><b>60%</b> - Positive reaction</label></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="3"><b>Negotiating</b></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="70" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 70 ? 'checked' : '' ?>><b>70%</b> - Negotiating price</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="80" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 80 ? 'checked' : '' ?>><b>80%</b> - Awaiting final decision</label></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td>Amount &euro;</td>
		<td><input type="text" name="forecast_amount" style="width: 150px;"></td>
	</tr>
	<tr>
		<td>Expected closed (YYYY-MM-DD)</td>
		<td><input type="text" name="forecast_date" style="width: 150px;"></td>
	</tr>
</table>
</div>

<br>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="25%"><a href="">&lt;&lt; Won</a></td>
		<td width="40%" align="center"><?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] < 26 ? '<a href="javascript:void(0);" onClick="if ( confirm(\'Go to next stage?\') ) { fDisplayNextStage( this ); }">Next Stage: Forecast</a>' : '' ?></td>
		<td width="35%" align="right">
			<a href="javascript:void(0);" onClick="fToggleLost( this );">Lost &gt;&gt;</a>
			<div align="left" id="lost" style="display: none; position: absolute; padding: 2px; width: 210px; background: #FFFFFF; border: 1px solid #000000;">
			<b>Lost Reason:</b><br>
			<div id="lost_stage_1" style="display: none;">
			<label><input type="radio" name="lost_reason" value="1" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason(1) ?></label><br>
			<label><input type="radio" name="lost_reason" value="2" onClick="fChangeLostNextContact( false );"> To small</label><br>
			<label><input type="radio" name="lost_reason" value="3" onClick="fChangeLostNextContact( false );"> Not qualified for VI</label><br>
			<label><input type="radio" name="lost_reason" value="4" onClick="fChangeLostNextContact( false );"> Not qualified for NSI</label><br>
			<label><input type="radio" name="lost_reason" id="lost_reason_5" value="5" onClick="fChangeLostNextContact( true );"> Qualified; no budget</label><br>
			<label><input type="radio" name="lost_reason" id="lost_reason_6" value="6" onClick="fChangeLostNextContact( true );"> Qualified; competing product</label><br>
			<label><input type="radio" name="lost_reason" value="7" onClick="fChangeLostNextContact( false );"> Qualified; no decision power</label><br>
			<label><input type="radio" name="lost_reason" value="8" onClick="fChangeLostNextContact( false );"> Qualified; other reasons</label><br>
			</div>
			<div id="lost_stage_2" style="display: none;">
			<label><input type="radio" name="lost_reason" value="9" onClick="fChangeLostNextContact( false );"> No access to decision maker</label><br>
			<label><input type="radio" name="lost_reason" value="10" onClick="fChangeLostNextContact( false );"> On price</label><br>
			<label><input type="radio" name="lost_reason" value="11" onClick="fChangeLostNextContact( false );"> To competitor</label><br>
			<label><input type="radio" name="lost_reason" value="12" onClick="fChangeLostNextContact( false );"> On no-bid</label><br>
			<label><input type="radio" name="lost_reason" value="13" onClick="fChangeLostNextContact( false );"> Other reasons</label><br>
			</div>
			<br>
			<b>Keep Customer?</b><br>
			<input type="checkbox" name="lost_keep" value="1" onchange=" if ( this.checked ) { fChangeLostNextContact( false ); fChangeLostNextAppointment( true ); } else { fChangeLostNextAppointment( false ); } "> Yes<br>
			<br>
			<div id="lost_next_contact" style="display: none;">
			<b>Next Contact (YYYY-MM-DD)</b><br>
			<input type="text" name="lost_next_contact"><br>
			<br>
			</div>
			<div id="lost_keep_appointment" style="display: none;">
			<b>Next Appointment</b><br>
			<input type="text" name="lost_keep_appointment" value="<?= $GLOBALS['aCustomer']['Case']['appointment'] ?>" onKeyUp="document.getElementById('appointment').value = this.value;"><br>
			<br>
			</div>
			<b>Comment:</b><br>
			<textarea name="lost_comment"></textarea><br>
			<br>
			<input type="submit" value="Save - Lost - Today &gt;&gt;">
			</div>
		</td>
	</tr>
</table>



