<?php
// Get Last VI comment
if ( $GLOBALS['aCustomer']['VIID'] )
{
	$aVI = DBQueryGetRows("SELECT * FROM comments WHERE comments.cst_id = '" . $GLOBALS['aCustomer']['VIID'] . "' AND LENGTH(comments.comment) > 0 ORDER BY comments.added DESC LIMIT 1");
}

// Get Last NSI comment
if ( $GLOBALS['aCustomer']['NSIID'] )
{
	$aNSI = DBQueryGetRows("SELECT * FROM comments WHERE comments.cst_id = '" . $GLOBALS['aCustomer']['NSIID'] . "' AND LENGTH(comments.comment) > 0 ORDER BY comments.added DESC LIMIT 1");
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><b>VI @ <?= $aVI[0]['added'] ? $aVI[0]['added'] . ' by ' . fReturnRepDetailFromID($aVI[0]['person_id']) : 'N/A' ?></b></td>
	</tr>
	<tr>
		<td class="TablePadding"><?= $aVI[0]['comment'] ?></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><b>CSI @ <?= $aNSI[0]['added'] ? $aNSI[0]['added'] . ' by ' . fReturnRepDetailFromID($aNSI[0]['person_id']) : 'N/A' ?></b></td>
	</tr>
	<tr>
		<td class="TablePadding"><?= $aNSI[0]['comment'] ?></td>
	</tr>
</table>
