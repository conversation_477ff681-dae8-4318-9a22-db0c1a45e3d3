<?php
// Check if customer is hidden
if ( in_array($_GET['cst_id'], $GLOBALS['aHiddenCST']) || in_array(fGetMasterID($_GET['cst_id']), $GLOBALS['aHiddenCST'])){
	if (!in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aHiddenAllow'])){
		die();
	}
}

// ACTION: Export Online Sale
if ( $_GET['export_online_sale'] ) {
	DBQuery("UPDATE crm.saleslog SET saleslog.online_payment_id = '" . GenerateRandomID(15) . "' WHERE saleslog.sale_id = '" . $_GET['export_online_sale'] . "' LIMIT 1");
}

// ACTION: Delete Comment Log
if ( $_GET['action'] == 'delete_comments' && fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ) {
	DBQuery("DELETE FROM crm.comments WHERE cst_id = '" . $_GET['cst_id'] . "'");
}

// Update case ownership
if ( $_GET['action'] == 'new_owner' ) {
	// Select customer details (Company->Case)
        DBQuery("UPDATE crm.cst SET person_id = '" . $_GET['new_owner'] . "' WHERE cst_id = '" . $_GET['case_id'] . "' LIMIT 1");
}

// Create a new case ( !!!ONLY FOR CSC CARDS!!! )
if ( $_GET['action'] == 'new_case' ) {
	DBQuery("INSERT INTO crm.cst (master_id, case_name) VALUES('" . $_GET['cst_id'] . "', 'card_csc')");
}

// Update segment
if ( $_GET['action'] == 'new_segment' ) {
	if ( $_GET['new_segment'] != "" ){
		DBQuery("UPDATE crm.cst SET segment_id = '" . $_GET['new_segment'] . "', canvas_timestamp = NOW() WHERE cst_id = '" . $_GET['cst_id'] . "' LIMIT 1");
	}
}

// Update active partner
if ( $_GET['action'] == 'cactive_partner' ){
	if ( $_GET['cactive_partner'] == 'true' ){
		$sSet = '1';
	} elseif ( $_GET['cactive_partner'] == 'false' ) {
		$sSet = '0';
	}
	if ( $sSet != "" ) {
		DBQuery("UPDATE crm.cst SET active_partner = '" . $sSet . "' WHERE cst_id = '" . (int)$_GET['cst_id'] . "' AND case_name = 'Company Card' LIMIT 1");
	}
	$sSet = ""; // Reset variable.
}

// Assign company to partner
if ( $_GET['action'] == 'bind_partner' ){
	if ( $_GET['pcst_id'] != "" ) {
		// Get partner_card id
		$cPartner = DBQuery("SELECT cst_id FROM crm.cst WHERE master_id = '".(int)$_GET['pcst_id']."' AND case_name = 'card_partner' LIMIT 1");
		$partner = mysql_fetch_assoc( $cPartner );
		$_GET['pcst_id'] = $partner['cst_id'];
		$rPartner = DBQuery("SELECT partner_id FROM ca.partner_profile WHERE account_id = (SELECT account_id FROM ca.accounts WHERE cst_id = '".(int)$partner['cst_id']."' AND special_limits LIKE 'partner%' LIMIT 1) LIMIT 1");
		$partner = mysql_fetch_assoc( $rPartner );
		$partner_id = $partner['partner_id'];
	} else {
		$partner_id = 0;
	}
	DBQuery("UPDATE crm.cst SET partner_id = '".(int)$partner_id."', approved = 1 WHERE master_id = '".(int)$_GET['cst_id']."'");
	DBQuery("UPDATE crm.cst SET partner_id = '".(int)$partner_id."', approved = 1 WHERE cst_id = '".(int)$_GET['cst_id']."' LIMIT 1");
}

// Update reference ( only admin may do that )
if ( fVerifyAccess( $aRepData['person_id'], 2) ){ // Only admin could change this
	if ( $_GET['action'] == 'cref' ){
		if ( $_GET['ref'] == 'true' ){
			$sSet = '1';
		} elseif ( $_GET['ref'] == 'false' ) {
			$sSet = '0';
		}
	}
	if ( $sSet != "" ) {
		DBQuery("UPDATE crm.cst SET reference = '" . $sSet . "' WHERE cst_id = '" . (int)$_GET['cst_id'] . "' AND case_name = 'Company Card'");
	}
}

// Select customer details (Company->Case)
$aCustomer = fGetCustomerDetails( $_GET['cst_id'] );

//Open CRM to allow agents to see only their customers
if (substr($_SERVER['REMOTE_ADDR'], 0, 7) != "192.168" && $_SERVER['REMOTE_ADDR'] != "89.233.1.75" && $_SERVER['REMOTE_ADDR'] != "172.25.0.10"){
	$bAllowAccess = false;
	$aCustomerIDs = explode(",", $aCustomer['CaseIDs']);
	$aCustomerIDs[] = $aCustomer['Case']['master_id'];
	foreach($aCustomerIDs as $iCustomerID){
		$iOwnerID = fGetCaseOwner($iCustomerID);
		if ($GLOBALS['aRepData']['person_id'] == $iOwnerID){
			$bAllowAccess = true;
		}
	}

	//To die, or not to die, that's the question
	if (!$bAllowAccess){
		die("You do not have access to this customer");
	}
}

// Special handler for a customer page, where another user has just been assigned
/*if ( isISA( $aRepData['person_id'] ) ) {
	$iCSICasePersonId = DBGetRowValue("crm.cst", "person_id", "case_name = 'card_nsi' AND master_id = '".$_GET['cst_id']."'");
	if ( ( $aRepData['person_id'] != $iCSICasePersonId ) && ( is_numeric( $iCSICasePersonId ) ) && ( $iCSICasePersonId != 0 ) ){
		echo "Card has already been assigned to: ".htmlspecialchars( DBGetRowValue("crm.salespeople", "name", "person_id = '".$iCSICasePersonId."'") )."<br>
			<input type='button' value='Back' onclick='document.location.href = \"index.php?page=today&right=performance\";'>
		";
		die();
	}

	if ( $aCustomer['Case']['person_id'] == "" && isISA( $aRepData['person_id'] ) ){
		if ( !checkMoreLeads( $aRepData['person_id'] ) ){
			echo "You have reached the maxium number of appointments and callbacks.
			<br>
			Please clean up your callback list, so that you can access more canvas leads.
			<br>
			<input type='button' value='Back' onclick='document.location.href = \"index.php?page=today&right=performance\";'>
		";
			die();
		}
	}
}*/

// Choose customer template
switch ( $aCustomer['Case']['case_name'] ) {
	case 'card_vi':
		include INCLUDE_PATH . 'customer_vi.php';
		break;
	case 'card_nsi':
		include INCLUDE_PATH . 'customer_nsi.php';
		break;
	case 'card_partner':
		include INCLUDE_PATH . 'customer_partner.php';
		break;
	case 'card_crc':
		include INCLUDE_PATH . 'customer_crc.php';
		break;
	case 'card_csc':
		include INCLUDE_PATH . 'customer_csc.php';
		break;
	default:
		include INCLUDE_PATH . 'customer_company.php';
		break;
}
?>
