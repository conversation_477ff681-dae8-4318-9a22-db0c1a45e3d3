<?php
// Select customer details (Company->Case)
$aCustomer = fGetCustomerDetails( ( $_GET['cst_id'] ? $_GET['cst_id'] : $_POST['cst_id'] ) );

// Save sale(s)
if ( $_POST['save'] == 1 )
{
	// Fetch currency exchange rate
	$iCurrencyRate = DBGetRowValue("crm.country_currency", "currency", "currency_name = '".strtoupper( $_POST['currency'] )."'");
	$fEuroRate = (float)DBGetRowValue("crm.country_currency", "currency", "currency_name = 'EURO' OR currency_name = 'EUR'");

	// Store PO, Reset forecast (pct + value + date) on Case
	DBQuery("UPDATE crm.cst SET cst.po_number = '" . $_POST['po_number'] . "', cst.forecast_expectancy = '30', cst.forecast_amount = 0, cst.forecast_date = '0000-00-00' WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1");

	// Department
	$department = mysql_real_escape_string(DBGetRowValue("crm.salespeople, crm.salesteams", "salesteams.name", "salespeople.person_id = '".(int) $aRepData['person_id']."' && salespeople.department = salesteams.team_id"));

	// VI Sale?
	if ( $_POST['vi_price'] )
	{
		// Construct product name
		$sProductName =
			$aProductTypes[$_POST['vi_product']]['name'] .
			( $_POST['evm_accounts'] ? ', ' . intval($_POST['evm_accounts']) . ' Accounts' : '' ) .
			( $_POST['vif_assets'] ? ', ' . intval($_POST['vif_assets']) . ' Asset Lists' : '' ) .
			( $_POST['vi_poc'] ? ', PoC' : '' ) .
			", " . $_POST['currency'];

		if ($_POST['evm_accounts']){
			$iLicenseAmount = intval($_POST['evm_accounts']);
		} elseif ($_POST['vif_assets']){
			$iLicenseAmount = intval($_POST['vif_assets']);
		} else {
			$iLicenseAmount = 0;
		}
		DBQuery("INSERT INTO crm.saleslog ( currency_exchange_rate, currency_exchange_rate_euro, sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date, product_category, verification_text, license_amount, reference, department, sale_type) VALUES( '".$iCurrencyRate."', '".$fEuroRate."', now(), '" . $_POST['vi_to'] ."', 0, '" . $sProductName . "', '0', '" . $_POST['vi_price'] ."', '" . $_POST['vi_product'] . "', 0, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '" . $_POST['vi_product'] . "', '" . $_POST['payment_days'] . "', '" . ( $_POST['include_vat'] ? 2 : 1 ) . "', '" . $_POST['currency'] . "', '" . $_POST['vi_from'] ."', '" . fReturnProductCategory( $_POST['vi_product'] ) . "', '" . $_POST['verification_text'] ."', '".$iLicenseAmount."', '" . ( $_POST['reference_customer'] ? 1 : 0 ) . "', '" . $department . "', '" . $_POST['vi_sale_type'] . "')");
	}

	// SS Sale?
	if ( $_POST['ss_price'] )
	{
		$iLicenseAmount = intval($_POST['ss_ips']);
		DBQuery("INSERT INTO crm.saleslog ( currency_exchange_rate, currency_exchange_rate_euro, sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date, product_category, verification_text, license_amount, reference, department, sale_type) VALUES( '".$iCurrencyRate."', '".$fEuroRate."',now(), '" . $_POST['ss_to'] ."', 0, 'Surveillance Scanner, " . $_POST['ss_ips'] . " IP Addresses, " . $_POST['ss_frequency'] . " Scanning, " . $_POST['currency'] . "', '0', '" . $_POST['ss_price'] ."', '5', 0, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '5', '" . $_POST['payment_days'] . "', '" . ( $_POST['include_vat'] ? 2 : 1 ) . "', '" . $_POST['currency'] . "', '" . $_POST['ss_from'] ."', '" . fReturnProductCategory( 5 ) . "', '" . $_POST['verification_text'] ."', '".$iLicenseAmount."', '" . ( $_POST['reference_customer'] ? 1 : 0 ) . "', '" . $department . "', '" . $_POST['ss_sale_type'] . "')");
	}

	// CSI Sale?
	if ( $_POST['csi_price'] )
	{
		DBQuery("INSERT INTO crm.saleslog ( currency_exchange_rate, currency_exchange_rate_euro, sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date, product_category, verification_text, reference, department, sale_type) VALUES( '".$iCurrencyRate."', '".$fEuroRate."',now(), '" . $_POST['csi_to'] ."', 0, '" . $aProductTypes[$_POST['csi_product']]['name'] . ( $_POST['csi_poc'] ? ', PoC' : '' ) . ", " . $_POST['currency'] . "', '0', '" . $_POST['csi_price'] ."', '" . $_POST['csi_product'] . "', 0, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '" . $_POST['csi_product'] . "', '" . $_POST['payment_days'] . "', '" . ( $_POST['include_vat'] ? 2 : 1 ) . "', '" . $_POST['currency'] . "', '" . $_POST['csi_from'] ."', '" . fReturnProductCategory( $_POST['csi_product'] ) . "', '" . $_POST['verification_text'] ."', '" . ( $_POST['reference_customer'] ? 1 : 0 ) . "', '" . $department . "', '" . $_POST['csi_sale_type'] . "')");
	}

        // SVP Sale
        if ( $_POST['svp_price'] )
        {
                DBQuery("INSERT INTO crm.saleslog ( currency_exchange_rate, currency_exchange_rate_euro, sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date, product_category, verification_text, reference, department, sale_type) VALUES( '".$iCurrencyRate."', '".$fEuroRate."',now(), '" . $_POST['svp_to'] ."', 0, '" . $aProductTypes[$_POST['svp_product']]['name'] . ", " . $_POST['currency'] . "', '0', '" . $_POST['svp_price'] ."', '" . $_POST['svp_product'] . "', 0, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '" . $_POST['svp_product'] . "', '" . $_POST['payment_days'] . "', '" . ( $_POST['include_vat'] ? 2 : 1 ) . "', '" . $_POST['currency'] . "', '" . $_POST['svp_from'] ."', '" . fReturnProductCategory( $_POST['svp_product'] ) . "', '" . $_POST['verification_text'] ."', '" . ( $_POST['reference_customer'] ? 1 : 0 ) . "', '" . $department . "', '" . $_POST['svp_sale_type'] . "')");
        }

	// Server sale
	if ( $_POST['server_price'] ){
                DBQuery("INSERT INTO crm.saleslog ( currency_exchange_rate, currency_exchange_rate_euro, sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date, product_category, verification_text, reference, department, sale_type) VALUES( '".$iCurrencyRate."', '".$fEuroRate."',now(), '" . $_POST['server_to'] ."', 0, '" . $aProductTypes[203]['name'] . ", " . $_POST['currency'] . "', '0', '" . $_POST['server_price'] ."', '203', 0, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '203', '" . $_POST['payment_days'] . "', '" . ( $_POST['include_vat'] ? 2 : 1 ) . "', '" . $_POST['currency'] . "', '" . $_POST['server_from'] ."', '" . fReturnProductCategory( 203 ) . "', '" . $_POST['verification_text'] ."', '" . ( $_POST['reference_customer'] ? 1 : 0 ) . "', '" . $department . "', '" . $_POST['server_sale_type'] . "')");
	}

	// Update reference customer
	if ( $_POST['reference_customer'] ) {
		// Get parent card:
		$master_id = DBGetRowValue("crm.cst", "master_id", "cst_id = '".(int)$_POST['cst_id']."'");
		DBQuery("UPDATE crm.cst SET reference = 1 WHERE cst_id = '".(int)$master_id."' LIMIT 1");
	}


	header('location: ?page=customer&cst_id=' . intval($_POST['cst_id']));
	exit();
}

// Get primary contact person
$aContact = DBGetRow('crm.contacts', "contacts.cst_id = '" . $_GET['cst_id'] . "' AND invoice = 1");

// Get Country
$aCountry = DBGetRow('crm.countries', "countries.id = '" . $GLOBALS['aCustomer']['Company']['invoice_country'] . "'");

// VI & SVP Product List
$sVIProductOptions = '';
$sSVPProductOptions = '';
while ( list($iProductID, $aProductType) = each($aProductTypes) )
{
	if ( $aProductType['available'] && $aProductType['container'] == 'VI' ) {
		$sVIProductOptions .= '<option value="' . $iProductID . '">' . $aProductType['name'] . '</option>';
	}elseif ( $aProductType['available'] && $aProductType['container'] == 'SVP' ) {
                $sSVPProductOptions .= '<option value="' . $iProductID . '">' . $aProductType['name'] . '</option>';
        }elseif ( $aProductType['available'] && $aProductType['container'] == 'NSI' ) {
		$sCSIProductOptions .= '<option value="' . $iProductID . '">' . $aProductType['name'] . '</option>';
	}
}

$sSaleTypes = '<option value="">- Select Sale Type -</option>';
$sSaleTypes .= '<option value="POC">POC</option>';
$sSaleTypes .= '<option value="New">New</option>';
$sSaleTypes .= '<option value="Recurrence">Recurrence</option>';
$sSaleTypes .= '<option value="Upgrade">Upgrade</option>';
?>

<script language="JavaScript">
function fValidateForm(){
	if (document.getElementById('verification_text').value != "" && confirm('Have you verified ALL details? (Currency, price, dates, product, etc.)')){
		return true;
	} else {
		return false;
	}
}
</script>

<form method="POST" id="invoiceform" action="?page=invoice" onSubmit="return fValidateForm();">
<input type="hidden" name="save" value="1">
<input type="hidden" name="cst_id" value="<?= $_GET['cst_id'] ?>">
<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'Invoice: ' . htmlspecialchars($aCustomer['Company']['name']);
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="100%" valign="top" class="TablePadding">
			Please review address details, references, and recipients. If any of the before said details are incorrect then <a href="?page=customer&amp;cst_id=<?= intval($_GET['cst_id']) ?>">go back to the customer</a> and correct them.
		</td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td align="center">
			<table width="750" cellspacing="0" cellpadding="2" style="border: 1px solid #000000;">
				<tr>
					<td>
						<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['name']) ?><br>
						<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_1']) . ( $GLOBALS['aCustomer']['Company']['field_1'] ? '<br>' : '' ) ?>
						<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_2']) . ( $GLOBALS['aCustomer']['Company']['field_2'] ? '<br>' : '' ) ?>
						<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_5']) ?> <?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_4']) ?><br>
						<?= $aCountry['country'] ?><br>
						<br>
						FAO: <?= htmlspecialchars($aContact['name']) ?>
					</td>
				</tr>
				<tr>
					<td style="border-bottom: 1px solid #DEDEDE;" colspan="3"><br></td>
				</tr>
				<tr>
					<td style="border-bottom: 1px solid #DEDEDE;" colspan="2" width="66%" valign="top">
						<table width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td width="20%">
									<b>Your Ref.:</b>
								</td>
								<td width="80%">
									<?= htmlspecialchars($aContact['name']) ?>
								</td>
							</tr>
							<tr>
								<td>
									<b>PO Number:</b>
								</td>
								<td>
									<input type="text" name="po_number" style="width: 50%">
								</td>
							</tr>
							<tr>
								<td>
									<b>Our Ref.:</b>
								</td>
								<td>
									<?= fReturnRepDetailFromID($aRepData['person_id']) ?>
								</td>
							</tr>
						</table>
					</td>
					<td style="border-bottom: 1px solid #DEDEDE; border-left: 1px solid #DEDEDE;" width="34%" valign="top">
						<b>Invoice Number:</b><br>
						DKXXXX
					</td>
				</tr>
				<tr>
					<td width="33%" valign="top">
						<b>Period:</b> (YYYY-MM-DD)
					</td>
					<td width="33%" valign="top" style="border-left: 1px solid #DEDEDE;">
						<b>Product:</b>
					</td>
					<td width="33%" valign="top" style="border-left: 1px solid #DEDEDE;">
						<b>Price:</b>
					</td>
				</tr>
				<tr>
					<td><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
				</tr>
				<tr>
					<td valign="top">
						<b>VI Sale:</b><br>
						<input type="text" name="vi_from" value="<?= date('Y-m-d') ?>" style="width: 45%"> - <input type="text" name="vi_to" style="width: 45%">
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<select name="vi_product" onChange="fVIExtraOptions(this);">
							<option> - Product - </option>
							<?= $sVIProductOptions ?>
						</select><br>
						<br>
						<input type="checkbox" name="vi_poc" value="1"> Proof of Concept (PoC)
						
						<div id="vif" style="display: none;"><br><br><input type="text" style="width: 10%;" name="vif_assets"> Asset Lists</div>
						<div id="evm" style="display: none;"><br><br><input type="text" style="width: 10%;" name="evm_accounts"> Accounts</div>
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<input type="text" name="vi_price" id="sale_0_price" onKeyUp="fUpdatePrice();" style="width: 30%">
						<select name="vi_sale_type">
							<?= $sSaleTypes ?>
						</select>
					</td>
				</tr>
				<tr>
					<td><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
				</tr>
				<tr>
					<td valign="top">
						<b>SS Sale:</b><br>
						<input type="text" name="ss_from" value="<?= date('Y-m-d') ?>" style="width: 45%"> - <input type="text" name="ss_to" style="width: 45%">
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						Surveillance Scanner,
						<select name="ss_frequency">
								<option>Frequency</option>
								<option value="Daily">Daily</option>
								<option value="Weekly">Weekly</option>
								<option value="Monthly">Monthly</option>
						</select>, 
						<input type="text" style="width: 10%;" name="ss_ips"> slots
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<input type="text" name="ss_price" id="sale_1_price" onKeyUp="fUpdatePrice();" style="width: 30%">
						<select name="ss_sale_type">
							<?= $sSaleTypes ?>
						</select>
					</td>
				</tr>
				<tr>
					<td><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
				</tr>
				<tr>
					<td valign="top">
						<b>CSI Sale:</b><br>
						<input type="text" name="csi_from" value="<?= date('Y-m-d') ?>" style="width: 45%"> - <input type="text" name="csi_to" style="width: 45%">
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<select name="csi_product">
							<option> - Product - </option>
							<?= $sCSIProductOptions ?>
						</select><br><br>
						<input type="checkbox" name="csi_poc" value="1"> Proof of Concept (PoC)
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<input type="text" name="csi_price" id="sale_4_price" onKeyUp="fUpdatePrice();" style="width: 30%">
						<select name="csi_sale_type">
							<?= $sSaleTypes ?>
						</select>
					</td>
				</tr>
                                <tr>
                                        <td><br></td>
                                        <td style="border-left: 1px solid #DEDEDE;"><br></td>
                                        <td style="border-left: 1px solid #DEDEDE;"><br></td>
                                </tr>
                                <tr>
                                        <td valign="top">
                                                <b>SVP Sale:</b><br>
                                                <input type="text" name="svp_from" value="<?= date('Y-m-d') ?>" style="width: 45%"> - <input type="text" name="svp_to" style="width: 45%">
                                        </td>
                                        <td valign="top" style="border-left: 1px solid #DEDEDE;">
                                                <br>
                                                <select name="svp_product">
                                                        <option> - Product - </option>
                                                        <?= $sSVPProductOptions ?>
                                                </select>
                                        </td>
                                        <td valign="top" style="border-left: 1px solid #DEDEDE;">
                                                <br>
                                                <input type="text" name="svp_price" id="sale_3_price" onKeyUp="fUpdatePrice();" style="width: 30%">
						<select name="svp_sale_type">
							<?= $sSaleTypes ?>
						</select>
                                        </td>
                                </tr>

				<tr>
					<td><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
					<td style="border-left: 1px solid #DEDEDE;"><br></td>
				</tr>
				<tr>
					<td valign="top">
						<b>Server Sale:</b><br>
							<input type="text" name="server_from" value="<?= date('Y-m-d') ?>" style="width: 45%"> - <input type="text" name="server_to" style="width: 45%">
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
							Server license
					</td>
					<td valign="top" style="border-left: 1px solid #DEDEDE;">
						<br>
						<input type="text" name="server_price" id="server_price" onKeyUp="fUpdatePrice();" style="width: 30%">
						<select name="server_sale_type">
							<?= $sSaleTypes ?>
						</select>
					</td>
				</tr>

				<tr>
					<td style="border-bottom: 1px solid #DEDEDE;"><br><br><br></td>
					<td style="border-bottom: 1px solid #DEDEDE; border-left: 1px solid #DEDEDE;"><br><br><br></td>
					<td style="border-bottom: 1px solid #DEDEDE; border-left: 1px solid #DEDEDE;"><br><br><br></td>
				</tr>
				<tr>
					<td colspan="3" style="border-bottom: 1px solid #DEDEDE;">
						<b>Customer verification:</b><br>
						<textarea name="verification_text" id="verification_text"></textarea><br>
						<i>e.g. e-mail from the customer stating that they want to buy. The mail must contain information on product, period and price. You also must keep the sender, recipient and date/time for future reference.</i>
					</td>
				</tr>
				<tr>
					<td colspan="2" style="border-bottom: 1px solid #DEDEDE;">
						<b>VAT (25% / Companies in DK only):</b> <input type="checkbox" name="include_vat" id="include_vat" onChange="fUpdatePrice();"> Yes
					</td>
					<td style="border-left: 1px solid #DEDEDE; border-bottom: 1px solid #DEDEDE;">
						<span id="vat">0.00</span>
					</td>
				</tr>
				<tr>
					<td colspan="2" style="border-bottom: 1px solid #DEDEDE;">
						<b>Total</b>
					</td>
					<td style="border-bottom: 1px solid #DEDEDE; border-left: 1px solid #DEDEDE;">
						<span id="total">0.00</span>
						<select name="currency">
							<option> - Currency - </option>
							<?php
							// Fetch List of currencies
							$rResult = DBQuery("SELECT * FROM crm.country_currency ORDER by currency_name ASC");
							$iNumRows = mysql_num_rows( $rResult );
							for ( $i = 0; $i < $iNumRows; $i++ ){
								$aRow = mysql_fetch_array( $rResult );
								echo "<option value=\"".htmlspecialchars( $aRow['currency_name'] )."\">".htmlspecialchars( $aRow['currency_name'] )."</option>";
							}
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td colspan="3">Terms of payment: Nett <input type="text" name="payment_days" value="8" style="width: 5%"> days</td>
				</tr>
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td colspan="3">Bank/Payment details are automatically inserted here.</td>
				</tr>
			</table>
			<br>
			<label><input type="checkbox" name="reference_customer" value="1"> Customer accepted to become a Reference</label><br>
			<br>
			<input type="submit" value="Issue invoice">
		</td>
	</tr>
		</td>
	</tr>
</table>
</form>
