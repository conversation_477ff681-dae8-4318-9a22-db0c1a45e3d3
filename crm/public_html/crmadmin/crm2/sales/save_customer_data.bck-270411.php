<?php
$cardType = DBQuery("SELECT case_name FROM crm.cst WHERE cst_id = '".$_POST['cst_id']."'");
$cardType = mysql_fetch_assoc( $cardType );
$cardType = $cardType['case_name'];
if ( $cardType == "card_partner" ) {
	$iMasterID = fGetMasterID($_POST['cst_id']);
	$parentCCName = DBQuery("SELECT name FROM crm.cst WHERE cst_id = '".(int)$iMasterID."' LIMIT 1");
	$parentCCName = mysql_fetch_assoc( $parentCCName );
	$parentCCName = $parentCCName['name'];
	// Set name
	DBQuery("UPDATE crm.cst SET name = '".mysql_real_escape_string( $parentCCName )."' WHERE cst_id = '".(int)$_POST['cst_id']."' LIMIT 1");
} 
// Save for callback?
if ($_POST['save_callback'] == "true"){
	$iMasterID = fGetMasterID($_POST['cst_id']);
	DBQuery("UPDATE cst SET callback_timestamp=NOW() WHERE cst_id='".$_POST['cst_id']."'");
	DBQuery("UPDATE cst SET callback_timestamp=NOW() WHERE cst_id='".$iMasterID."'");
} else {
	$iMasterID = fGetMasterID($_POST['cst_id']);
	DBQuery("UPDATE cst SET callback_timestamp='0000-00-00 00:00:00' WHERE cst_id='".$_POST['cst_id']."'");
	DBQuery("UPDATE cst SET callback_timestamp='0000-00-00 00:00:00' WHERE cst_id='".$iMasterID."'");
}

// Lead qualification
if (($aRepData['person_level'] == 2 || $aRepData['person_level'] == 3) && $aRepData['department'] == 2){
	$rQuery = mysql_query("SELECT lead_id FROM lead_qualifications WHERE lead_cstid='".$_POST['cst_id']."'");
	if (mysql_num_rows($rQuery) == 0){
		if ($aRepData['person_level'] == 2){
			mysql_query("INSERT INTO lead_qualifications SET lead_cstid='".$_POST['cst_id']."', lead_id_deci_finance='".(($_POST['fdecimaker_identified'] == "identified") ? "1" : "0")."', lead_id_deci_tech='".(($_POST['tdecimaker_identified'] == "identified") ? "1" : "0")."', lead_createwant='".$_POST['createwant']."', lead_competitor='".$_POST['competitor']."', lead_snapshot_done='".(($_POST['snapshot_done'] == "snapshotdone") ? "1" : "0")."', lead_walkthrough_result='".$_POST['resultwalkthrough']."', lead_next_appointment='".$_POST['appointmentlead']."', lead_status='new', lead_iae='".$GLOBALS['aRepData']['iae_id']."'");
		} else {
			mysql_query("INSERT INTO lead_qualifications SET lead_cstid='".$_POST['cst_id']."', lead_id_deci_finance='".(($_POST['fdecimaker_identified'] == "identified") ? "1" : "0")."', lead_id_deci_tech='".(($_POST['tdecimaker_identified'] == "identified") ? "1" : "0")."', lead_createwant='".$_POST['createwant']."', lead_competitor='".$_POST['competitor']."', lead_snapshot_done='".(($_POST['snapshot_done'] == "snapshotdone") ? "1" : "0")."', lead_walkthrough_result='".$_POST['resultwalkthrough']."', lead_next_appointment='".$_POST['appointmentlead']."', lead_status='new', lead_rep='".$GLOBALS['aRepData']['person_id']."', lead_iae='".$GLOBALS['aRepData']['iae_id']."'");
		}
		$iLeadID = mysql_insert_id();
	} else {
		$aLeadData = mysql_fetch_assoc($rQuery);
		if ($aRepData['person_level'] == 2){
			mysql_query("UPDATE lead_qualifications SET lead_id_deci_finance='".(($_POST['fdecimaker_identified'] == "identified") ? "1" : "0")."', lead_id_deci_tech='".(($_POST['tdecimaker_identified'] == "identified") ? "1" : "0")."', lead_createwant='".$_POST['createwant']."', lead_competitor='".$_POST['competitor']."', lead_snapshot_done='".(($_POST['snapshot_done'] == "snapshotdone") ? "1" : "0")."', lead_walkthrough_result='".$_POST['resultwalkthrough']."', lead_next_appointment='".$_POST['appointmentlead']."', lead_status='new', lead_iae='".$GLOBALS['aRepData']['iae_id']."' WHERE lead_id='".$aLeadData['lead_id']."'");
		} else {
			mysql_query("UPDATE lead_qualifications SET lead_id_deci_finance='".(($_POST['fdecimaker_identified'] == "identified") ? "1" : "0")."', lead_id_deci_tech='".(($_POST['tdecimaker_identified'] == "identified") ? "1" : "0")."', lead_createwant='".$_POST['createwant']."', lead_competitor='".$_POST['competitor']."', lead_snapshot_done='".(($_POST['snapshot_done'] == "snapshotdone") ? "1" : "0")."', lead_walkthrough_result='".$_POST['resultwalkthrough']."', lead_next_appointment='".$_POST['appointmentlead']."', lead_status='new', lead_rep='".$GLOBALS['aRepData']['person_id']."', lead_iae='".$GLOBALS['aRepData']['iae_id']."' WHERE lead_id='".$aLeadData['lead_id']."'");
		}
		$iLeadID = $aLeadData['lead_id'];
	}
}
	
if ($_POST['saveapproval'] == "true"){
	mysql_query("UPDATE lead_qualifications SET lead_status='waiting' WHERE lead_id='".$iLeadID."'");
}

// Customer Details
$aCustomerDetails = fGetCustomerDetails( $_POST['cst_id'] );

// Mail flow control
// Stop -> Remove
if ( $_POST['stop_mail'] == 1 ) {
	DBQuery("DELETE FROM crm.trial_mail_pipeline WHERE cst_id = '".(int) $iMasterID."'");
}

// Change mail type
if ( $_POST['switch_mail'] == 1 ){
	DBQuery("UPDATE crm.trial_mail_pipeline SET type = 1, stage = 1, date = NOW() WHERE cst_id = '".$iMasterID."'");
}

// Name
$sName = '';
if ( isset($_POST['name']) && fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	$sName = ", name = '" . $_POST['name'] . "'";
}

// CRM: MASTER
{
	$sQuery = "UPDATE crm.cst SET  company_type = '".$_POST['company_type']."', cst.deployment_tools = '".(int)$_POST['deployment_tools']."', cst.timezone = '" . $_POST['timezone'] . "', phone = '" . $_POST['phone'] . "', company_industry = '" . $_POST['company_industry'] . "', account_type = '" . $_POST['account_type'] . "', lead_source = '" . $_POST['lead_source'] . "', field_12 = '" . $_POST['tag'] . "', cst.linkedin_id = '" . $_POST['linkedin_id'] . "', cst.company_employees = '" . ereg_replace('[^0-9]*', '', $_POST['company_employees']) . "', cst.company_employees_location = '" . ereg_replace('[^0-9]*', '', $_POST['company_employees_location']) . "', cst.company_resp_location = '" . ereg_replace('[^0-9]*', '', $_POST['company_resp_location']) . "', cst.category_clients = '" . ereg_replace('[^0-9]*', '', $_POST['category_clients']) . "', cst.category_servers = '" . ereg_replace('[^0-9]*', '', $_POST['category_servers']) . "', cst.category_itpeople = '" . ereg_replace('[^0-9]*', '', $_POST['category_itpeople']) . "', cst.category_externalips = '" . ereg_replace('[^0-9]*', '', $_POST['category_externalips']) . "', cst.web = '" . $_POST['web'] . "', cst.field_1 = '" . $_POST['address_1'] . "', cst.field_2 = '" . $_POST['address_2'] . "', cst.field_4 = '" . $_POST['town'] . "', cst.field_5 = '" . $_POST['zipcode'] . "', cst.field_6 = '".$_POST['vatnum']."', cst.field_7 = '".$_POST['eannum']."', cst.invoice_country = '" . $_POST['country'] . "', cst.financial_year_start = '" . date('Y-') . $_POST['financial_year_start'] . "', cst.budget_start = '" . date('Y-') . $_POST['budget_start'] . "', cst.alias = '" . $_POST['alias'] . "', cst.duns_number = '" . $_POST['duns_number'] . "'" . $sName . " WHERE cst.cst_id = '" . $aCustomerDetails['Company']['cst_id'] . "' LIMIT 1";
	DBQuery($sQuery);
}

// CRM: CASE
{
	// New Appointment?
	if ( ( !$_POST['appointment'] || $_POST['appointment'] == '0000-00-00 00:00:00' ) && !$_POST['lost_reason'] ) {
		$sAppointment = '';
	} else {
		$sAppointment = "appointment = '" . $_POST['appointment'] . "', ";
	}

//	if ( isISA( $aRepData['person_id'] ) ){
//		if ( !checkISADate( $_POST['appointment'] ) && isset( $_POST['appointment'] ) ){
//			$sAppointment = "";
//		}
//	}

	// Set person id
	$sPersonID = '';
	if ( !$aCustomerDetails['Case']['person_id'] && !fVerifyAccess( $aRepData['person_id'], 2 ) ) {
		$sPersonID = ", cst.person_id = '" . $aRepData['person_id'] . "'";
	}
	// Same as above, but for a CSC case
	if ( !$aCustomerDetails['Case']['person_id'] && $aCustomerDetails['Case']['case_name'] == 'card_csc' ) {
		$sPersonID = ", cst.person_id = '" . $aRepData['person_id'] . "'";
	}

	//$sQuery = "UPDATE crm.cst SET " . $sAppointment . " cst.forecast_expectancy = '" . $_POST['forecast_expectancy'] . "', cst.forecast_date = '" . $_POST['forecast_date'] . "', cst.forecast_amount = '" . ereg_replace('[^0-9]*', '', $_POST['forecast_amount']) . "'" . $sPersonID . ", cst.financial_year_start = '" . date('Y-') . $_POST['financial_year_start'] . "', cst.budget_start = '" . date('Y-') . $_POST['budget_start'] . "', cst.alias = '" . $_POST['alias'] . "'" . $sName . " WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
	//$sQuery = "UPDATE crm.cst SET " . $sAppointment . " cst.forecast_expectancy = '" . $_POST['forecast_expectancy'] . "', cst.forecast_date = '" . $_POST['forecast_date'] . "', cst.forecast_amount = '" . ereg_replace('[^0-9]*', '', $_POST['forecast_amount']) . "'" . $sPersonID . ", cst.financial_year_start = '" . date('Y-') . $_POST['financial_year_start'] . "', cst.budget_start = '" . date('Y-') . $_POST['budget_start'] . "', cst.alias = '" . $_POST['alias'] . "'" . $sName . " WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
	$sQuery = "UPDATE crm.cst SET " . $sAppointment . " flag ='".$_POST['flag']."', cst.financial_year_start = '" . date('Y-') . $_POST['financial_year_start'] . "'" . $sPersonID . ", cst.budget_start = '" . date('Y-') . $_POST['budget_start'] . "', po_number = '".$_POST['po_number']."', cst.alias = '" . $_POST['alias'] . "'" . $sName . " WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
	DBQuery($sQuery);

	if ($aCustomerDetails['Case']['person_id'] == $aRepData['person_id']){
		//Also update forecast
		$sQuery = "UPDATE crm.cst SET cst.forecast_expectancy = '" . $_POST['forecast_expectancy'] . "', cst.forecast_date = '" . $_POST['forecast_date'] . "', cst.forecast_amount = '" . ereg_replace('[^0-9]*', '', $_POST['forecast_amount']) . "'" . $sPersonID . " WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
		DBQuery($sQuery);
	}
}

// CRM: CONTACTS
{
	// Check if primary contact is present
	if ( !$_POST['aContacts']['primary_contact'] ) {
		// It wasn't - this is the first contact, it will always default to the primary one
		$_POST['aContacts']['primary_contact'] = 1;
	}

	// Remove all existing contacts
	DbQuery("DELETE FROM crm.contacts WHERE contacts.cst_id = '" . $_POST['cst_id'] . "'");
	// Remove all subscriptions
	DbQuery("DELETE FROM crm.subscriptions WHERE subscriptions.cst_id = '" . $_POST['cst_id'] . "'");	

	// Loop through contacts
	$iPos = 0;
	for ( $iCount = 1 ; $iCount <= (count($_POST['aContacts'])-2) ; $iCount++ ) {
		// Insert contacts
		if ( is_array($_POST['aContacts'][$iCount]) && $_POST['aContacts'][$iCount]['delete'] != 1 ) {
			$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, phone, email, competence, primary_contact, decision_maker, invoice, title, mobile, nurturing, aware ) VALUES('" . $_POST['cst_id'] . "', '" . ++$iPos . "', '" . $_POST['aContacts'][$iCount]['name'] . "', '" . $_POST['aContacts'][$iCount]['phone'] . "', '" . $_POST['aContacts'][$iCount]['email'] . "', '" . $_POST['aContacts'][$iCount]['competence'] . "', '" . ( $_POST['aContacts']['primary_contact'] == $iCount ? 1 : 0 ) . "', '" . ( $_POST['aContacts'][$iCount]['decision_maker'] ? 1 : 0 ) . "', '" . ( $_POST['aContacts']['invoice'] == $iCount ? 1 : 0 ) . "', '" . $_POST['aContacts'][$iCount]['title'] . "', '" . $_POST['aContacts'][$iCount]['mobile'] . "', '".($_POST['nurturing'][$iCount] == "on" ? "1" : "0" )."', '".($_POST['aware'][$iCount] == "on" ? "1" : "0" )."' )";
			DBQuery($sQuery);
			$_id = mysql_insert_id();
			// Store subscription types
			$subscriptions = $_POST['subscription_type'][$iCount];
			foreach ( $subscriptions as $campaign_name => $value ) {
				if ( $value == "on" ) {
					DBQuery("INSERT INTO crm.subscriptions SET cst_id = '".(int)$_POST['cst_id']."', name = '".mysql_real_escape_string( $campaign_name )."', contact_id = '".(int)$_id."'");
				}
			}			
		}
	}
	$sQuery = "";
}

// CRM: CRC STATUS CHANGE FOR SALE
if ( $_POST['crc_change_sale_status'] && in_array( $GLOBALS['aRepData']['person_id'], $GLOBALS['aCRC'] ) ) {
	while ( list($iSaleID, $iStatus) = each($_POST['crc_change_sale_status']) ) {
		if ( DBGetRowValue('crm.saleslog', 'status', "sale_id = '" . (int) $iSaleID . "'") != $iStatus ) {
// JB (2009-10-27): Make the 'status_date' update conditional, only update if in the past, don't update future dates (future invoices)
//			DBQuery("UPDATE crm.saleslog SET status = '" . (int) $iStatus . "', status_date = NOW() WHERE sale_id = '" . (int) $iSaleID . "' LIMIT 1");
// (CASE WHEN status_date IS NULL THEN NOW() ELSE status_date END)

			DBQuery("UPDATE crm.saleslog SET status = '" . (int) $iStatus . "', status_date = (CASE WHEN NOW() > status_date THEN NOW() ELSE (CASE WHEN status_date IS NULL THEN NOW() ELSE status_date END) END) WHERE sale_id = '" . (int) $iSaleID . "' LIMIT 1");
		}
	}
}

// CRM: FORECAST LOG
if ( $aCustomerDetails['Case']['forecast_amount'] != ereg_replace('[^0-9]*', '', $_POST['forecast_amount']) || $aCustomerDetails['Case']['forecast_expectancy'] != $_POST['forecast_expectancy'] ) {
	$sQuery = "INSERT INTO crm.forecast_log (cst_id, person_id, logged, expectancy, amount) VALUES('" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', NOW(), '" . $_POST['forecast_expectancy'] . "', '" . ereg_replace('[^0-9]*', '', $_POST['forecast_amount']) . "')";
	DBQuery($sQuery);
}

// CRM: LOST LOG
if ( $_POST['lost_reason'] ) {
	// Save lost reason
	$sQuery = "INSERT INTO crm.lost_log (person_id, cst_id, reason, comment, lost_time) VALUES('" . $aRepData['person_id'] . "', '" . $_POST['cst_id'] . "', '" . $_POST['lost_reason'] . "', '" . $_POST['lost_comment'] . "', NOW())";
	DBQuery($sQuery);
	
	DBQuery("DELETE FROM lead_qualifications WHERE lead_cstid='" . $_POST['cst_id'] . "'");
	
	$iLostReason = (int) $_POST['lost_reason'];
	switch ($iLostReason) {
		case 1:
			$iNewSegment = 1545;
			break;
		case 2:
			$iNewSegment = 1546;
			break;
		case 3:
			$iNewSegment = 1576;
			break;
		case 4:
			$iNewSegment = 1575;
			break;
		case 5:
			$iNewSegment = 1549;
			break;
		case 6:
			$iNewSegment = 1550;
			break;
		case 7:
			$iNewSegment = 1551;
			break;
		case 8:
			$iNewSegment = 1552;
			break;
		case 9:
			$iNewSegment = 1553;
			break;
		case 10:
			$iNewSegment = 1554;
			break;
		case 11:
			$iNewSegment = 1555;
			break;
		case 12:
			$iNewSegment = 1556;
			break;
		case 13:
			$iNewSegment = 1557;
			break;
		case 14:
			$iNewSegment = 1559;
			break;
		case 15:
			$iNewSegment = 1560;
			break;
		case 16:
			$iNewSegment = 1561;
			break;
		case 17:
			$iNewSegment = 1577;
			break;
		case 18:
			$iNewSegment = 1599;
			break;
		case 19:
			$iNewSegment = 1594;
			break;
		case 20:
			$iNewSegment = 1595;
			break;
		case 21:
			$iNewSegment = 1596;
			break;
		case 22:
			$iNewSegment = 1618;
			break;
		case 23:
			$iNewSegment = 1600;
			break;
		case 24:
			$iNewSegment = 1611;
			break;
		case 25:
			$iNewSegment = 1546;
			break;
		case 27:
			$iNewSegment = 1670;
			break;
		case 28:
			$iNewSegment = 1736;
			break;
		case 29:
			$iNewSegment = 1737;
			break;
		case 30:
			$iNewSegment = 1738;
			break;
		default:
			$iNewSegment = 1535;
			break;
	}

	$iMasterID = fGetMasterID($_POST['cst_id']);

	$blISA = false;
	$sChangeSegment = "";
	if ( ( $iLostReason == 25 ) && (  isISA( $aRepData['person_id'] ) ) ) {
		$sChangeSegment = ", segment_id='".$iNewSegment."'";
	} elseif ( ( $iLostReason == 26 ) && (  isISA( $aRepData['person_id'] ) ) ) {
		$blISA = true;
	} else {
		$sChangeSegment = ", segment_id='".$iNewSegment."'";
	}

	$sQuery = "UPDATE crm.cst SET person_id = NULL, appointment = NULL".$sChangeSegment.", canvas_timestamp = NOW() WHERE cst.cst_id='".$_POST['cst_id']."' LIMIT 1";
	DBQuery($sQuery);
	$sQuery = "UPDATE crm.cst SET person_id = NULL, appointment = NULL".$sChangeSegment.", canvas_timestamp = NOW() WHERE cst.cst_id='".$iMasterID."' LIMIT 1";
	DBQuery($sQuery);

	if ( $blISA == false ) { // If ISA has lost a lead, then rotate in lod ( meaning that call_back will be added AND the log entry )
		// Clear "Stage"
		$sQuery = "UPDATE crm.cst SET cst.forecast_expectancy = '0', cst.forecast_date = '', cst.forecast_amount = '0' WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
		DBQuery($sQuery);

		// Reset ownership / next appointment ?
		// Disabled temporarily / ST / VBAKKER / MKAKANI
		/*if ( !$_POST['lost_keep'] ) {
			// Reset ownership and place in 'Lost Segment'
			$sQuery = "UPDATE crm.cst SET person_id = NULL, appointment = '" . $_POST['lost_next_contact'] . "', segment_id = '" . $aRepData['lost_segment_id'] . "' WHERE cst.cst_id = '" . $_POST['cst_id'] . "' LIMIT 1";
			DBQuery($sQuery);
		}*/

		// Special lost reason, that should TAG the customer
		if ( $_POST['lost_reason'] == 17 ) { // VI -> Qualified for CSI
			// Set tag
			$sQuery = "INSERT INTO crm.customer_tags SET cst_id = '" . $aCustomerDetails['Company']['cst_id'] . "', tag = 'VI_CST->CSI'";
			DBQuery($sQuery);

			// Update segment for master_id
			$sQuery = "UPDATE crm.cst SET cst.segment_id = '1459' WHERE cst_id = '" . $aCustomerDetails['Company']['cst_id'] . "' LIMIT 1";
			DBQuery($sQuery);
		}
	}

}

// CRM: COMMENTS
{
	// Loop through comments
	for ( $iCount = 1 ; $iCount <= 4 ; $iCount++ ) {
		$sQuery = "INSERT INTO crm.comments (comments.comment, comments.added, comments.cst_id, comments.person_id, comments.type) VALUES('" . preg_replace("/\n/", '<br>', $_POST['comment'][$iCount]) . "', now(), '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '" . $iCount . "')";
		DBQuery($sQuery);
	}
}

// CA: CSI LICENSE KEY
if ( $_POST['lk_account_id'] && $_POST['lk_starts'] && $_POST['lk_expires'] && $_POST['lk_host_licenses'] && $_POST['lk_type'] ) {
	// Generate License Key
	$sLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

	// Store License Key
	DBQuery("INSERT INTO ca.license_keys SET " .
	"license = '" . $sLicKey . "', " .
	"account_id = '" . $_POST['lk_account_id'] . "', " .
	"created = '" . $_POST['lk_starts'] . " 00:00:00', " .
	"valid_from = created, " .
	"valid_to = '" . $_POST['lk_expires'] . " 23:59:59', " .
	"activated = created, " .
	"quantity = '" . intval($_POST['lk_host_licenses']) . "', " .
	"type = '" . intval($_POST['lk_type']) . "'");
}

// CA: ACCOUNTS
if ( $_POST['ca_username'] ) {
	// Verify that ca_username is available
	if ( DBNumRows('ca.accounts', "account_username = '" . $_POST['ca_username'] ."'") ) {
		header('Location: ?page=customer&cst_id=' . $_POST['cst_id'] . '&right=overview&error=username_taken');
		exit();
	}

	// Check if CST_ID has account already, if it is, then create a new Shadow CST_ID
	$aAccount = DBGetRow('ca.accounts', "accounts.cst_id = '" . $_POST['cst_id'] . "'");
	if ( $aAccount ) {
		// Create Shadow CST ID
		$sQuery = "INSERT INTO crm.cst (master_id, person_id) VALUES('" . $aCustomerDetails['Company']['cst_id'] . "', '" . $aRepData['person_id'] . "')";
		DBQuery($sQuery);

		// Get Shadow CST_ID
		$iAccountCSTID = mysql_insert_id();
	} else {
		$iAccountCSTID = $_POST['cst_id'];
	}

	// Password / Pincode
	$iPinCode = rand(1000,9999);

	// If Trial - Register trial on customer
	if ( $_POST['ca_trial'] ) {
		$sQuery = "INSERT INTO crm.saleslog (sold_date, expires_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, product_category) VALUES(NOW(), '" . $_POST['ca_expires'] . "', '0', '" . $aProductTypes[$_POST['ca_product_type']]['short_name'] . " - Trial Account', 0, '" . $_POST['ca_product_type'] . "', 1, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '9999', 0, 1, '" . fReturnProductCategory( $_POST['ca_product_type'] ) . "')";
		DBQuery($sQuery);
	}

	// Create Account
	$sQuery = "INSERT INTO ca.accounts (account_username, account_password, account_login_type, account_product_type, modules, show_modules, account_recv_all, account_expires, cst_id, account_gen_pwd, lang_id, ca_lang_id) VALUES('" . $_POST['ca_username'] . "', PASSWORD('" . $iPinCode . "'), '1', '" . $_POST['ca_product_type'] . "', '" . $aDefaultModuleMappings[$_POST['ca_product_type']]['modules'] . "', '" . $aDefaultModuleMappings[$_POST['ca_product_type']]['show_modules'] . "', '1', '" . $_POST['ca_expires'] . "', '" . $iAccountCSTID . "', 1, 1, 1)";
	DBQuery($sQuery);

	$iAccountID = mysql_insert_id();

	// If CSI, create with 1 trial licenses
	if ( $_POST['ca_product_type'] == 17 && $iAccountID ) {
		// Store 'CSI Base Setting' for a trial
		mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $iAccountID . "', '" . $iAccountCSTID . "', 0, 3, 1)");

		$sLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

		DBQuery("INSERT INTO ca.license_keys SET " .
			"license = '" . $sLicKey . "', " .
			"account_id = '" . $iAccountID . "', " .
			"created = NOW(), " .
			"valid_from = created, " .
			"valid_to = '" . $_POST['ca_expires'] . " 23:59:59', " .
			"activated = created, " . 
			"quantity = '1', " .
			"type = 32");
	}

	if ( ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) && $iAccountID ) {
		// Set the VIM trial special limits
		if ( $_POST['ca_product_type'] == 208 ) {
			$xmlAccess = 0;
		} else {
			$xmlAccess = 1;
		}

		DBQuery("UPDATE ca.accounts SET special_limits = 'vim_30', account_version = 'vim_30', account_product_type = '".(int)$_POST['ca_product_type']."', account_options = '".(int)$aDefaultOptionMapping[$_POST['ca_product_type']]."', xml_access = '".(int)$xmlAccess."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
	}

	// If BA, automatically add user to receive emails about new BA's
	if ( ( $_POST['ca_product_type'] == 8 || $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) && $iAccountID ) {
		// IF VIM 3.0 Add customer details on the new account
		$contactValue = $_POST['ca_username'];
		if ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) {
			// Fetch primary contact:
			$result = DBGetRow("crm.contacts", "cst_id = '".(int)$_POST['cst_id']."' AND primary_contact = 1");
			$name = $result['name']; // There should be one primary contact, for a valid value
			$contactValue = mysql_real_escape_string( $result['email'] );
			DBQuery("UPDATE ca.accounts SET account_name = '".mysql_real_escape_string( $name )."', account_email = '".mysql_real_escape_string( $result['email'] )."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
		}

		DBQuery("INSERT INTO ca.contact_method SET " . 
			"contact_method_value = '" . $contactValue . "', " .
			"contact_method_type = 1, " . 
			"account_id = '" . $iAccountID . "', " . 
			"name = '".( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ? mysql_real_escape_string( $name ) : "Contact" )."', " .
			"pos = '1', " . 
			"lang_eng = '1'");
	}

	// Partner account
	if ( $_POST['ca_product_type'] == 9 && $iAccountID ) {
		// Update account with special limit
		DBQuery("UPDATE ca.accounts SET special_limits = 'partner_portal' WHERE account_id = '" . $iAccountID . "' LIMIT 1");

		// Create entry in 'partner_profile'
		DBQuery("INSERT INTO ca.partner_profile SET account_id = '" . $iAccountID . "', invoice_country = '" . $_POST['country'] . "'");
	}

	// Email Pincode
	if ( $iAccountID ) {
		mail($aRepData['email'], 'Account Pincode', 'Username: ' . $_POST['ca_username'] . '
Pincode: ' . $iPinCode, 'From: <EMAIL>');
	}
	
	// Redirect user to CA Account Admin
	if ( $iAccountID ) {
		header('location: ?page=ca_account_management&account_id=' . $iAccountID . '&cst_id=' . $iAccountCSTID);
	} else {
		header('location: ?page=customer&cst_id=' . $_POST['cst_id'] . '&right=overview');
	}
	exit();
}

// Redirect user according to save button used
{
	if ( $_POST['save_button'] == 'Save - View Today' ) {
		header('location: ?page=today');
	} elseif ( $_POST['save_button'] == 'Save - Lost - Today' ) {
		header('location: ?page=today&right=performance');
	} else {
		header('location: ?page=customer&cst_id=' . $_POST['cst_id'] . '&right=overview');
	}
	exit();
}
?>
