<?php

/**
 * @file LicenseRestrictions.class.php
 * This file contains the LicenseRestrictions class
 */


/**
 * Class LicenseRestrictions
 * Handles checking for restrictions on License Key creation.
 *
 * @package CRM
 * @subpackage Licenses
 * @date 10.24.2012
 * <AUTHOR>
 *
 * Use the canCreateLicense(..) method to check whether the license can be created or not.
 *
 * The license restrictions functionality requires the `salespeople_profile_type` table
 * to be created and populated with and the `profile_type_id` column have been added to the `salespeople` table.
 *
 * Below is the SQL that would accomplish this task. By default all Sales People would
 * be set to default Users which means they can't add licenses.
 *
 * -- 1) Create the table
 *	 CREATE TABLE `crm`.`salespeople_profile_type` (
 *		`id` tinyint(3) unsigned NOT NULL auto_increment
 *		,`system_name` varchar(16) NOT NULL COMMENT 'internal code use'
 *		,`name` varchar(32) NOT NULL
 *		,PRIMARY KEY (`id`)
 *	 ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COMMENT 'Profile types of sales people';
 *
 * -- 2) Populate the table
 *	 INSERT INTO `crm`.`salespeople_profile_type` (`id`, `system_name`, `name`) VALUES (10, 'default', 'User'), (20, 'sales_enterprise', 'Sales Enterprise'), (30, 'sales_smb', 'Sales SMB'), (40, 'cec', 'CEC');
 *
 * -- 3) Updates the `salespeople` table
 *	 ALTER TABLE crm.salespeople ADD COLUMN profile_type_id tinyint(3) unsigned NOT NULL DEFAULT 10 COMMENT 'Relates to salespeople_profile_type.id';
 *
 */
abstract class LicenseRestrictions
{

	/**
	 * @var string
	 * Contains the last error message
	 */
	private static $_error = '';

	/**
	 * Returns an error message about why the last method call failed.
	 *
	 * @return string
	 *	If the return value is an empty string then no error occurred.
	 */
	public static function getLastError()
	{
		return self::$_error;
	}

	/**
	 * Gets the license restrictions for a Sales Person.
	 * ENHANCEMENT Put the license limit info in a table so they can be viewed and managed via the admin panel
	 *
	 * @param array $repAccount Sales Person account
	 * @return mixed
	 *	Returns an array with the below elements if the license restrictions are found for the account:
	 *		maxLicenseDays  : int - Maximum number of days for the license
	 *		maxLicenseHosts : int - Maximum number of hosts for the license
	 *	Returns false if there is no license restriction for the Sales Persons
	 */
	public static function getForAccount( $repAccount )
	{
		// These case IDs coincides with `crm`.`salespeople_profile_type`.`id`
		switch ( $repAccount['profile_type_id'] ) {
			case 20: // Sales Enterprise
				// These are the restrictions for the user
				$maxLicenseDays = 30;
				$maxLicenseHosts = 100;
				break;

			case 30: // Sales SMB
				$maxLicenseDays = 7;
				$maxLicenseHosts = 5;
				break;

			case 40: // CEC
				$maxLicenseDays = *********; // effectively unlimited
				$maxLicenseHosts = *********;
				break;

			case 10: // Regular User
				$maxLicenseDays = 0; // can not create any
				$maxLicenseHosts = 0;
				break;
			default:
				self::$_error = 'No License Restrictions found for the Sales Person';
				return false;
		}

		return array(
			'maxLicenseDays' => $maxLicenseDays
			,'maxLicenseHosts' => $maxLicenseHosts
		);
	}

	/**
	 * Checks if a license can be created or not based on the Sales Person trying
	 * to create it and the particulars of the license.
	 *
	 * @param array $repAccount
	 * @param array $licenseData
	 *	hosts      : int Number of hosts
	 *	start_date : string YYYY-MM-DD  License start date
	 *	end_date   : string YYYY-MM-DD  License is no longer valid on this date
	 *	type       : int
	 *	target_account_id : int  Refers to ca.accounts.id
	 * @return bool
	 */
	public static function canCreateLicense( $repAccount, $licenseData )
	{
		self::$_error = ''; // reset error

		// Ensure it's not 0 or negative
		if ( $licenseData['hosts'] < 1 ) {
			self::$_error = 'Invalid number of hosts:' . $licenseData['hosts'];
			return false;
		}

		// Ensure the start date is before the end date
		if ( strtotime($licenseData['start_date']) >= strtotime($licenseData['end_date']) ) {
			self::$_error = 'End date must come after the Start date: ' . htmlspecialchars( $licenseData['start_date'] . ' >= ' . $licenseData['end_date'] );
			return false;
		}
		// Ensure the Sales Person's Profile Type can create this license
		if ( !self::canProfileTypeCreateLicense( $repAccount, $licenseData ) ) {
			return false;
		}
		return true;
	}

	/**
	 * Checks if the Sales Person's Profile Type can create the License
	 *
	 * @param array $repAccount
	 * @param array $licenseData
	 * @return bool
	 */
	private static function canProfileTypeCreateLicense( $repAccount, $licenseData )
	{
		// Extract $maxLicenseDays and $maxLicenseHosts
		extract( self::getForAccount( $repAccount ) );

		// assert days
		$startTimestamp = strtotime( $licenseData['start_date'] . ' 00:00:00' );
		$endTimestamp = strtotime( $licenseData['end_date'] . ' 00:00:00' );
		$days = floor( ($endTimestamp - $startTimestamp) / 86400 );
		if ( $days > $maxLicenseDays ) {
			self::$_error = 'The number of Days exceeds the maximum: ' . $days;
			return false;
		}
		// assert hosts
		if ( $licenseData['hosts'] > $maxLicenseHosts ) {
			self::$_error = 'The number of Hosts exceeds the maximum: ' . $licenseData['hosts'];
			return false;
		}
		return true;
	}
}