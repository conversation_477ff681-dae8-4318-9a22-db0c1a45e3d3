<?php
error_reporting(0);

// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

// Open DB Connection
fOpenDatabase();

$sString = 'Number of employees: ';

$aComments = DBGetRows('crm.comments', "comment like '%" . $sString . "%'");

while ( list($iKey, $aComment) = each($aComments) ) {
	$iEmp = (int) trim(substr($aComment['comment'], ( strpos($aComment['comment'], $sString) + strlen($sString) ) , 10));

	// Get master id
	$iMasterID = DBGetRowValue('crm.cst', "master_id", "cst_id = '" . $aComment['cst_id'] . "'");

	if ( $iMasterID && !$aDone[$iMasterID] ) {
		$sQuery = "UPDATE crm.cst SET company_employees = '" . $iEmp . "' WHERE cst_id = '" . $iMasterID . "' LIMIT 1";
		DBQuery( $sQuery );
		echo $iMasterID . ' // ' . $iEmp . "\n";
		$aDone[$iMasterID] = true;
	}
}
?>
