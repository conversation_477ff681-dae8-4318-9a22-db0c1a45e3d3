<?php
// Data containers
$aResult = array();
$aOutput = array();

// Select all sales 
$aSales = DBGetRows('crm.saleslog', "cst_id IN(" . $GLOBALS['aCustomer']['AllIDs'] . ") AND !(product_price-discount)", 'saleslog.sold_date DESC');

// Generate output
while ( list($iKey, $aSale) = each($aSales) )
{
	// Determine container
	$sContainer = $GLOBALS['aProductTypes'][$aSale['product_type']]['container'];

	// Choose background class 
	if ( $sClass[$sContainer] === '' )
	{
		$sClass[$sContainer] = ' class="TableGreyBackground"';
	}
	else
	{
		$sClass[$sContainer] = '';
	}

	// Output
	$aOutput[$sContainer] .= '
	<tr' . $sClass[$sContainer] . '>
		<td>' . $GLOBALS['aProductTypes'][$aSale['product_type']]['short_name'] . '</td>
		<td>' . $aSale['sold_date'] . '</td>
		<td>' . fDifferenceMonths($aSale['sold_date'], $aSale['expires_date']) . ' months</td>
		<td>' . strtoupper(fReturnRepDetailFromID($aSale['person_id'], 'init')) . '</td>
	</tr>';
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Display if content
	if ( $aOutput['VI'] )
	{
	?>
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline">VI Trials</td>
		<td width="30%" class="TableSubHeadline">Registered</td>
		<td width="25%" class="TableSubHeadline">Period</td>
		<td width="25%" class="TableSubHeadline">Issued by</td>
	</tr>
	<?php
	echo $aOutput['VI'];
	}

	// Display if content
	if ( $aOutput['NSI'] )
	{
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline">CSI Trials</td>
		<td width="30%" class="TableSubHeadline">Registered</td>
		<td width="25%" class="TableSubHeadline">Period</td>
		<td width="25%" class="TableSubHeadline">Issued by</td>
	</tr>
	<?php
	echo $aOutput['NSI'];
	}
	?>
</table>
