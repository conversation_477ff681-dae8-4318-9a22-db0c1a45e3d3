<?php
//Make sure only level 2 can access this
if ($aRepData['person_level'] != 2){
	die("No access");
}

if ($_GET['approve']){
	$rQuery = mysql_query("SELECT * FROM lead_qualifications WHERE lead_id='".$_GET['approve']."'");
	$aLeadData = mysql_fetch_assoc($rQuery);
	
	$rQuery = mysql_query("SELECT * FROM salespeople WHERE person_id='".$aLeadData['lead_iae']."'");
	$aIAEData = mysql_fetch_assoc($rQuery);
	
	mysql_query("UPDATE lead_qualifications SET lead_status='approved' WHERE lead_id='".$_GET['approve']."'");
	mysql_query("UPDATE cst SET person_id='".$aLeadData['lead_iae']."', segment_id='".$aIAEData['canvas_segment_id']."' WHERE cst_id='".$aLeadData['lead_cstid']."'");
	mysql_query("UPDATE cst SET segment_id='".$aIAEData['canvas_segment_id']."' WHERE cst_id='".fGetMasterID($aLeadData['lead_cstid'])."'");
	mysql_query("INSERT INTO leadlog SET lead_approve_date=NOW(), lead_person_id='".mysql_escape_string($aLeadData['lead_rep'])."', lead_approved_by='".mysql_escape_string($aRepData['person_id'])."', lead_org_id='".mysql_escape_string($aLeadData['lead_id'])."', lead_org_cstid='".mysql_escape_string($aLeadData['lead_cstid'])."', lead_org_id_deci_finance='".mysql_escape_string($aLeadData['lead_id_deci_finance'])."', lead_org_id_deci_tech='".mysql_escape_string($aLeadData['lead_id_deci_tech'])."', lead_org_createwant='".mysql_escape_string($aLeadData['lead_createwant'])."', lead_org_competitor='".mysql_escape_string($aLeadData['lead_competitor'])."', lead_org_snapshot_done='".mysql_escape_string($aLeadData['lead_snapshot_done'])."', lead_org_walkthrough_result='".mysql_escape_string($aLeadData['lead_walkthrough_result'])."', lead_org_next_appointment='".mysql_escape_string($aLeadData['lead_next_appointment'])."', lead_org_status='".mysql_escape_string($aLeadData['lead_status'])."'");
}

if ($_GET['decline']){
	mysql_query("UPDATE lead_qualifications SET lead_status='rejected' WHERE lead_id='".$_GET['decline']."'");
}

$rQuery = mysql_query("SELECT * FROM lead_qualifications WHERE lead_status='waiting' ORDER BY lead_submit_date DESC");
if (mysql_num_rows($rQuery) > 0){
	?>
	<table width="100%" cellpadding="0" cellspacing="2">
		<tr>
			<td colspan="1" class="MenuHeadline">Customer name</td>
			<td colspan="1" class="MenuHeadline">IAE name</td>
			<td colspan="1" class="MenuHeadline">ISA name</td>
			<td colspan="1" class="MenuHeadline">Submitted for approval</td>
		</tr>
		<?php
		
		while($aRow = mysql_fetch_assoc($rQuery)){
			$aCustomerData = fGetCustomerDetails($aRow['lead_cstid']);
			$sIAEname = htmlspecialchars(fReturnRepDetailFromID($aRow['lead_iae']));
			$sISAname = htmlspecialchars(fReturnRepDetailFromID($aRow['lead_rep']));
			echo "<tr bgcolor=\"#D9D9D9\" class=\"data\"><td colspan=\"1\" height=\"10\" valign=\"top\" style=\"padding-left: 3px;\"><a href=\"?page=customer&cst_id=".$aCustomerData['Case']['cst_id']."&right=overview\">".htmlspecialchars($aCustomerData['Company']['name'])."</a></td><td height=\"10\" valign=\"top\" style=\"padding-left: 3px;\">".$sIAEname."</td><td height=\"10\" valign=\"top\" style=\"padding-left: 3px;\">".$sISAname."</td><td>".$aRow['lead_submit_date']."</td></tr>"; 
		}
		?>
	</table>
	<?php
} else {
	echo "No leads to approve.";
}
?>
