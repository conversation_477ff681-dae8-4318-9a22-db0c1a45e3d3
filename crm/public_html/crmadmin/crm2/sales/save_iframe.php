<?php
$aCustomerDetails = fGetCustomerDetails( $_POST['cst_id'] );

// CA: ACCOUNTS
if ( !empty($_POST['ca_username']) ) {
        // Verify that ca_username is available
        if ( DBNumRows('ca.accounts', "account_username = '" . $_POST['ca_username'] ."'") ) {
		echo '<font color="RED"><b>ERROR: Username is already used.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
                exit();
        }

	error_log("st-debug 1");

        // Check if CST_ID has account already, if it is, then create a new Shadow CST_ID
        $aAccount = DBGetRow('ca.accounts', "accounts.cst_id = '" . $_POST['cst_id'] . "'");
        if ( $aAccount ) {
                // Create Shadow CST ID
                $sQuery = "INSERT INTO crm.cst (master_id, person_id) VALUES('" . $aCustomerDetails['Company']['cst_id'] . "', '" . $aRepData['person_id'] . "')";
                DBQuery($sQuery);

                // Get Shadow CST_ID
                $iAccountCSTID = mysql_insert_id();
        } else {
                $iAccountCSTID = $_POST['cst_id'];
        }


	error_log("st-debug 1.5");

		// Insert primary contacts row. This fixes bug #5499
		if ( !empty( $_POST['ca_email'] ) ) {
			// Only insert this row if it doesn't exist yet. We only want it to ensure we can fetch the email address later
			$contact = DBGetRow( 'crm.contacts', "cst_id = '" . $iAccountCSTID . "' AND primary_contact = 1" );
			if ( false === $contact ) {
				// The name and email inserted here are fetched later when inserting the contact_method row.
				$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, email, primary_contact) VALUES('" . $iAccountCSTID . "', '1', '" . $_POST['ca_username'] . "', '" . $_POST['ca_email'] . "', '1' )";
				DBQuery($sQuery);
			}
		}

	error_log("st-debug 2");

        // Password / Pincode
        $iPinCode = rand(1000,9999);

        // If Trial - Register trial on customer
        if ( !empty($_POST['ca_trial']) ) {
                $sQuery = "INSERT INTO crm.saleslog (sold_date, expires_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, product_category) VALUES(NOW(), '" . $_POST['ca_expires'] . "', '0', '" . $aProductTypes[$_POST['ca_product_type']]['short_name'] . " - Trial Account', 0, '" . $_POST['ca_product_type'] . "', 1, '" . $_POST['cst_id'] . "', '" . $aRepData['person_id'] . "', '9999', 0, 1, '" . fReturnProductCategory( $_POST['ca_product_type'] ) . "')";
                DBQuery($sQuery);
        }

        // Create Account
        $sQuery = "INSERT INTO ca.accounts (account_email, account_username, account_password, account_login_type, account_product_type, modules, show_modules, account_recv_all, account_expires, cst_id, account_gen_pwd, lang_id, ca_lang_id) VALUES('" . $_POST['ca_email'] . "','" . $_POST['ca_username'] . "', PASSWORD('" . $iPinCode . "'), '1', '" . $_POST['ca_product_type'] . "', '" . $aDefaultModuleMappings[$_POST['ca_product_type']]['modules'] . "', '" . $aDefaultModuleMappings[$_POST['ca_product_type']]['show_modules'] . "', '1', '" . $_POST['ca_expires'] . "', '" . $iAccountCSTID . "', 1, 1, 1)";
        DBQuery($sQuery);

        $iAccountID = mysql_insert_id();

	error_log("st-debug 3");

        // If CSI, create with 1 trial licenses
        if ( $_POST['ca_product_type'] == 17 && $iAccountID ) {
                // Store 'CSI Base Setting' for a trial
                mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $iAccountID . "', '" . $iAccountCSTID . "', 0, 3, 1)");

                $sLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

                DBQuery("INSERT INTO ca.license_keys SET " .
                        "license = '" . $sLicKey . "', " .
                        "account_id = '" . $iAccountID . "', " .
                        "created = UTC_TIMESTAMP(), " .
                        "valid_from = created, " .
                        "valid_to = '" . $_POST['ca_expires'] . " 23:59:59', " .
                        "activated = created, " .
                        "quantity = '1', " .
                        "type = 32");
        }

	error_log("st-debug 4");

        if ( ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) && $iAccountID ) {
                // Set the VIM trial special limits
                if ( $_POST['ca_product_type'] == 208 ) {
                        $xmlAccess = 0;
                } else {
                        $xmlAccess = 1;
                }

                DBQuery("UPDATE ca.accounts SET special_limits = 'vim_30', account_version = 'vim_30', account_product_type = '".(int)$_POST['ca_product_type']."', account_options = '".(int)$aDefaultOptionMapping[$_POST['ca_product_type']]."', xml_access = '".(int)$xmlAccess."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
        }

	error_log("st-debug 5");

        // If BA, automatically add user to receive emails about new BA's
        if ( ( $_POST['ca_product_type'] == 8 || $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) && $iAccountID ) {
                // IF VIM 3.0 Add customer details on the new account
                $contactValue = $_POST['ca_username'];
                if ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) {
                        // Fetch primary contact:
                        $result = DBGetRow("crm.contacts", "cst_id = '".(int)$_POST['cst_id']."' AND primary_contact = 1");
                        $name = $result['name']; // There should be one primary contact, for a valid value
                        $contactValue = mysql_real_escape_string( $result['email'] );
                        DBQuery("UPDATE ca.accounts SET account_name = '".mysql_real_escape_string( $name )."', account_email = '".mysql_real_escape_string( $result['email'] )."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
                }

                DBQuery("INSERT INTO ca.contact_method SET " .
                        "contact_method_value = '" . $contactValue . "', " .
                        "contact_method_type = 1, " .
                        "account_id = '" . $iAccountID . "', " .
                        "name = '".( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ? mysql_real_escape_string( $name ) : "Contact" )."', " .
                        "pos = '1', " .
                        "lang_eng = '1'");
        }

	error_log("st-debug 6");

		// VIM 4.0 - Set the VIM trial special limits
		if ( $_POST['ca_product_type'] == 210 && $iAccountID ) {
			DBQuery("UPDATE ca.accounts SET special_limits = 'vim_40', account_version = 'vim_40', account_product_type = '210', account_options = '".(int)$aDefaultOptionMapping[$_POST['ca_product_type']]."', xml_access = '1' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");

	error_log("st-debug 7");

			$name = '';

			// Try to get an existing name if one exists
			$result = DBGetRow( 'crm.contacts', "cst_id = '" . $iAccountCSTID . "' AND primary_contact = 1" );
			if ( false !== $result ) {
				$name = $result['name'];
				if ( $name ) {
					DBQuery( "UPDATE ca.accounts SET account_name = '" . mysql_real_escape_string( $name ) . "' WHERE account_id = '" . (int)$iAccountID . "' LIMIT 1");
				}
			}

			// VIM 4.0 - Update contact info
			DBQuery("INSERT INTO ca.contact_method SET " .
				"contact_method_value = '" . mysql_real_escape_string( $_POST['ca_email'] ) . "', " .
				"contact_method_type = 1, " .
				"account_id = '" . (int)$iAccountID . "', " .
				"name = '" . mysql_real_escape_string( $name ) . "', " .
				"pos = '1', " .
				"lang_eng = '1'"
			);
        }

	error_log("st-debug 8");

		// Partner account
        if ( $_POST['ca_product_type'] == 9 && $iAccountID ) {
                // Update account with special limit, DEFAUL TO TRIAL!
                if ( $_POST['ca_trial'] ) {
                        $pSpecialLimits = "partner_portal";
                } else {
                        $pSpecialLimits = "partner_portal_trial";
                }
                DBQuery("UPDATE ca.accounts SET special_limits = '".$pSpecialLimits."' WHERE account_id = '" . $iAccountID . "' LIMIT 1");

                // Create entry in 'partner_profile'
                DBQuery("INSERT INTO ca.partner_profile SET account_id = '" . $iAccountID . "', invoice_country = '" . $_POST['country'] . "'");
                $_partnerId = mysql_insert_id();

                // Assign current partner_id to the current customer id
                DBQuery("UPDATE crm.cst SET partner_id = '".(int)$_partnerId."' WHERE cst_id = ( SELECT master_id FROM crm.cst WHERE cst_id = '".(int)$iAccountCSTID."' LIMIT 1 ) LIMIT 1");
        }

	error_log("st-debug 9");

        // Email Pincode
        if ( $iAccountID ) {
                mail($aRepData['email'], 'Account Pincode', 'Username: ' . $_POST['ca_username'] . '
Pincode: ' . $iPinCode, 'From: <EMAIL>');
        }

        // Redirect user to CA Account Admin
        if ( $iAccountID ) {
                header('location: ?page=ca_account_management&iframe=1&account_id=' . $iAccountID . '&cst_id=' . $iAccountCSTID);
        } else {
		echo '<font color="RED"><b>ERROR: An unknown error occurred, no account was created.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
        }
        exit();

	error_log("st-debug 10");
}

// CA: CSI LICENSE KEY
if ( $_POST['lk_account_id'] && $_POST['lk_starts'] && $_POST['lk_expires'] && $_POST['lk_host_licenses'] && $_POST['lk_type'] ) {

	// Check if it's possible to create the license.
	require_once INCLUDE_PATH . 'LicenseRestrictions.class.php';
	$rawLicenseData = array(
		'hosts' => $_POST['lk_host_licenses']
		,'start_date' => $_POST['lk_starts']
		,'end_date' => $_POST['lk_expires']
		,'type' => $_POST['lk_type']
		,'target_account_id' => $_POST['lk_account_id']
	);
	$canCreateLicense = LicenseRestrictions::canCreateLicense( $aRepData, $rawLicenseData );

	if ( $canCreateLicense ) {
        // Generate License Key
        $sLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

        // Store License Key
        DBQuery("INSERT INTO ca.license_keys SET " .
        "license = '" . $sLicKey . "', " .
        "account_id = '" . $_POST['lk_account_id'] . "', " .
        "created = '" . $_POST['lk_starts'] . " 00:00:00', " .
        "valid_from = created, " .
        "valid_to = '" . $_POST['lk_expires'] . " 23:59:59', " .
        "activated = created, " .
        "quantity = '" . intval($_POST['lk_host_licenses']) . "', " .
        "type = '" . intval($_POST['lk_type']) . "'");

        echo '<script>window.opener.location = window.opener.location;</script><font color="RED"><b>SUCCESS: License key created and assigned to account.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
        exit();
	}

	// TODO log this failure

	// Not possible to create the license key. To check the error related to it
	// call LicenseRestrictions::getLastError();
	// Return the License Restrictions for $repAccount
	$licenseRestrictions = LicenseRestrictions::getForAccount( $aRepData );
	?><script>
		window.opener.location = window.opener.location;
	</script>
	<span style="font-weight: bold; color: red;">
		FAILURE: License Key not created
	</span>
	<br/>
	<b>Error:</b> <?php echo LicenseRestrictions::getLastError(); ?>
	<p>
	Please review the fields in the License Key form and try again.
	<br>
	<ul>
		<li>Maximum allowed Days per License key: <b><?php echo $licenseRestrictions['maxLicenseDays']; ?></b></li>
		<li>Maximum allowed Hosts per License key: <b><?php echo $licenseRestrictions['maxLicenseHosts']; ?></b></li>
	</ul>
	</p>
	<a href="javascript:window.close();">Close this window</a>
	<?php
}
