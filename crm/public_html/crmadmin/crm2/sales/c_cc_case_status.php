<?php
$aCSCPeople = $GLOBALS['aCRCAdmins'];
// VI
$aVI = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_vi'");

// Store case id
$GLOBALS['aCustomer']['VIID'] = $aVI['cst_id'];

// NSI
$aNSI = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_nsi'");

// Store case id
$GLOBALS['aCustomer']['NSIID'] = $aNSI['cst_id'];

// Partner
$aPartner = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_partner'");

// CRC
$aCRC = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_crc'");

// CSC
$aCSC = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_csc'");

// Code for creating a new CSC case
$sCreateCase = "
	title='Create a new CSC Case'
	style='cursor: pointer;'
	onClick='document.location=\"?page=customer&cst_id=".(int)$_GET['cst_id']."&right=overview&action=new_case\"'
";
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline"><b>Case</b></td>
		<td width="35%" class="TableSubHeadline"><b>State</b></td>
		<td width="23%" class="TableSubHeadline"><b>Next Appointment</b></td>
		<td width="22%" class="TableSubHeadline"><b>Secunia Rep.</b></td>
	</tr>
	<tr <?= $aVI['cst_id'] ? fRowLink('?page=customer&cst_id=' . $aVI['cst_id']) : '' ?>>
		<td>VI</td>
		<td><?= fReturnCaseStatusName($aVI['case_status'], $aVI['forecast_expectancy'], $aVI['forecast_amount'], $aVI['forecast_date'], $aVI['appointment']) ?></td>
		<td><?= $aVI['appointment'] ?></td>
		<td onClick="event.cancelBubble = true;"><?= ( ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && ( ( !$aVI['appointment'] || $aVI['appointment'] == '0000-00-00 00:00:00' ) || in_array($aVI['person_id'], $GLOBALS['aLMAllowedMove'][$GLOBALS['aRepData']['person_id']]) ) ) || fVerifyAccess( $GLOBALS['aRepData']['person_id'], 3 ) ? fSelectChangeOwner($aVI['person_id'], $aVI['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fReturnRepDetailFromID($aVI['person_id']) ) ?></td>
	</tr>
	<tr class="TableGreyBackground" <?= $aNSI['cst_id'] ? fRowLink('?page=customer&cst_id=' . $aNSI['cst_id']) : '' ?>>
		<td>CSI</td>
		<td><?= fReturnCaseStatusName($aNSI['case_status'], $aNSI['forecast_expectancy'], $aNSI['forecast_amount'], $aNSI['forecast_date'], $aNSI['appointment']) ?></td>
		<td><?= $aNSI['appointment'] ?></td>
		<td onClick="event.cancelBubble = true;"><?= ( ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && ( ( !$aNSI['appointment'] || $aNSI['appointment'] == '0000-00-00 00:00:00' ) || in_array($aNSI['person_id'], $GLOBALS['aLMAllowedMove'][$GLOBALS['aRepData']['person_id']]) ) ) || fVerifyAccess( $GLOBALS['aRepData']['person_id'], 3 )  ? fSelectChangeOwner($aNSI['person_id'], $aNSI['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fReturnRepDetailFromID($aNSI['person_id']) ) ?></td>
	</tr>
	<tr <?= $aCRC['cst_id'] ? fRowLink('?page=customer&cst_id=' . $aCRC['cst_id']) : '' ?>>
		<td>CRC</td>
		<td><?= fReturnCaseStatusName($aCRC['case_status'], $aCRC['forecast_expectancy'], $aCRC['forecast_amount'], $aCRC['forecast_date'], $aCRC['appointment']) ?></td>
		<td><?= $aCRC['appointment'] ?></td>
		<td onClick="event.cancelBubble = true;"><?= ( ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && ( ( !$aCRC['appointment'] || $aCRC['appointment'] == '0000-00-00 00:00:00' ) || in_array($aCRC['person_id'], $GLOBALS['aLMAllowedMove'][$GLOBALS['aRepData']['person_id']]) ) ) || fVerifyAccess( $GLOBALS['aRepData']['person_id'], 3 ) ? fSelectChangeOwner($aCRC['person_id'], $aCRC['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fReturnRepDetailFromID($aCRC['person_id']) ) ?></td>
	</tr>
	<tr class="TableGreyBackground" <?= $aCSC['cst_id'] ? fRowLink('?page=customer&cst_id=' . $aCSC['cst_id']) : $sCreateCase ?>>
		<td>CSC</td>
		<td><?= fReturnCaseStatusName($aCSC['case_status'], $aCSC['forecast_expectancy'], $aCSC['forecast_amount'], $aCSC['forecast_date'], $aCSC['appointment']) ?></td>
		<td><?= $aCSC['appointment'] ?></td>
		<td onClick="event.cancelBubble = true;"><?= ( in_array( $GLOBALS['aRepData']['person_id'], $aCSCPeople ) || ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && ( ( !$aCSC['appointment'] || $aCSC['appointment'] == '0000-00-00 00:00:00' ) || in_array($aCSC['person_id'], $GLOBALS['aLMAllowedMove'][$GLOBALS['aRepData']['person_id']]) ) ) || fVerifyAccess( $GLOBALS['aRepData']['person_id'], 3 ) ? fSelectChangeOwner($aCSC['person_id'], $aCSC['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fReturnRepDetailFromID($aCSC['person_id']) ) ?></td>
	</tr>
	<?php
	// Only display partner if available
	if ( $aPartner['cst_id'] ) {
	?>
	<tr class="TableGreyBackground" <?= $aPartner['cst_id'] ? fRowLink('?page=customer&cst_id=' . $aPartner['cst_id']) : '' ?>>
		<td>Partner</td>
		<td><?= fReturnCaseStatusName($aPartner['case_status'], $aPartner['forecast_expectancy'], $aPartner['forecast_amount'], $aPartner['forecast_date'], $aPartner['appointment']) ?></td>
		<td><?= $aPartner['appointment'] ?></td>
		<td onClick="event.cancelBubble = true;"><?= ( ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && ( ( !$aPartner['appointment'] || $aPartner['appointment'] == '0000-00-00 00:00:00' ) || in_array($aCRC['person_id'], $GLOBALS['aLMAllowedMove'][$GLOBALS['aRepData']['person_id']]) ) ) || fVerifyAccess( $GLOBALS['aRepData']['person_id'], 3 ) ? fSelectChangeOwner($aPartner['person_id'], $aPartner['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fReturnRepDetailFromID($aPartner['person_id']) ) ?></td>
	</tr>
	<?php
	}

	// Check if Lead Management, then display all shadow cases as well
	if ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ) {
		?>
		<tr><td><br></td></tr>
			<tr class="TableGreyBackground">
				<td width="20%" class="TableSubHeadline"><b>Shadows</b></td>
				<td width="35%" class="TableSubHeadline"><b>State</b></td>
				<td width="23%" class="TableSubHeadline"><b>Next Appointment</b></td>
				<td width="22%" class="TableSubHeadline"><b>Secunia Rep.</b></td>
			</tr>
		<?php
		// Loop through all shadow cases
		$aShadows = DBGetRows('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND (cst.case_name NOT IN('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc' ) OR case_name IS NULL)");
		while ( list($iKey, $aShadow) = each($aShadows) ) {
			?>
			<tr>
				<td>SHADOW</td>
				<td><?= fReturnCaseStatusName($aShadow['case_status'], $aShadow['forecast_expectancy'], $aShadow['forecast_amount'], $aShadow['forecast_date'], $aVI['appointment']) ?></td>
				<td><?= $aShadow['appointment'] ?></td>
				<td><?= fSelectChangeOwner($aShadow['person_id'], $aShadow['cst_id'], $GLOBALS['aCustomer']['Company']['cst_id']) ?></td>
			</tr>
			<?php
		}
	}
	?>
</table>
