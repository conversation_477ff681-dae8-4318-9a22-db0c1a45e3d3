<?php
// Data containers
$aResult = array();
$aOutput = array();

// Select all sales 
$aSales = DBGetRows('ca.shop', "shop.cst_id IN(" . $GLOBALS['aCustomer']['AllIDs'] . ")", 'shop.wp_transTime DESC');

// Generate output
while ( list($iKey, $aSale) = each($aSales) ) {
	// Determine container
	$sContainer = 'NSI';

	// Choose background class 
	if ( $sClass[$sContainer] === '' ) {
		$sClass[$sContainer] = ' class="TableGreyBackground"';
	} else {
		$sClass[$sContainer] = '';
	}

	// Output
	$aOutput[$sContainer] .= '
	<tr' . $sClass[$sContainer] . '>
		<td>' . htmlspecialchars($aSale['wp_desc']) . '</td>
		<td>' . substr($aSale['wp_transTime'], 0, 10) . '</td>
		<td>' . htmlspecialchars($aSale['wp_currency']) . '</td>
		<td class="NumberCell">' . number_format($aSale['wp_amount'], 2) . '</td>
		<td>' . fOnlineSaleStatus($aSale['sale_status']) . '</td>
	</tr>';

	// Generate data for Financial History
	if ( $aSale['sale_status'] != 3 )
	{
		// Paid
		if ( $aSale['sale_status'] == 1 )
		{
			$sStatus = 'paid';
		}
		else // Awaiting
		{
			$sStatus = 'awaiting';
		}

		// Store
		$GLOBALS['aFinancial'][substr($aSale['wp_transTime'], 0, 4)][$sStatus] += (int) $aSale['wp_amount'];
		$GLOBALS['aFinancial']['total'][$sStatus] += (int) $aSale['wp_amount'];
	}
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Display if content
	if ( $aOutput['VI'] )
	{
	?>
	<tr class="TableGreyBackground">
		<td width="30%" class="TableSubHeadline">VI Sales</td>
		<td width="15%" class="TableSubHeadline">Sold</td>
		<td width="15%" class="TableSubHeadline">Currency</td>
		<td width="15%" class="TableSubHeadline">Amount</td>
		<td width="25%" class="TableSubHeadline">Status</td>
	</tr>
	<?php
	echo $aOutput['VI'];
	}

	// Display if content
	if ( $aOutput['NSI'] )
	{
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr class="TableGreyBackground">
		<td width="30%" class="TableSubHeadline">CSI Sales</td>
		<td width="15%" class="TableSubHeadline">Purchase</td>
		<td width="15%" class="TableSubHeadline">Currency</td>
		<td width="15%" class="TableSubHeadline">Amount</td>
		<td width="25%" class="TableSubHeadline">Status</td>
	</tr>
	<?php
	echo $aOutput['NSI'];
	}
	?>
</table>
