// Special case for IE
var _ieX = 0;
var _ieY = 0;

function ieMouseEvent( ev ){
	var iScrollTop = document.body.scrollTop;
	var iScrollLeft = document.body.scrollLeft;
	_ieX = ev.clientX + iScrollLeft;
	_ieY = ev.clientY + iScrollTop;
}

// Remember this object 
var _stackHover = null;
// Hover div
function showHover( sContainerName, iHeight, iWidth, iId ){
	_objHover = document.createElement('DIV');
	_objHover.id = 'objHoverLayer_' + sContainerName;
	_objHover.name = 'objHoverLayer_' + sContainerName;
	_objHover.style.visibility = 'hidden';
	_objHover.style.position = 'absolute';
	_objHover.style.zIndex = '14';
	_objHover.style.top = 0;
	_objHover.style.left = 0;
	_objHover.style.height = iHeight + "px";
	_objHover.style.width = iWidth + "px";
	_objHover.style.backgroundColor = 'white';
	_objHover.style.border = '1px solid black';
	// Prepare the HTML data
	var sHtmlContent = "<iframe width='" + iWidth + "' height='" + iHeight + "' frameborder='0' SCROLLING='NO' SCROLLBARS='NO' src='index.php?page=hovertable&id=" + iId + "'>";
	sHtmlContent += "</iframe>";
	
	_objHover.innerHTML = sHtmlContent;
	_objHover.className = 'modal_layer';
	document.body.appendChild( _objHover );
	_stackHover = document.getElementById("objHoverLayer_" + sContainerName);
}

// UFO, let it fly ;-)
function moveHover( ev ){
	if ( _stackHover != null ){
		var iX = 0; // Mouse coordinates
		var iY = 0;
		if ( typeof ev.pageX == "undefined" ) {
			// IE
			iX = _ieX;
			iY = _ieY;
		} else {
			// FF
			iX = ev.pageX;
			iY = ev.pageY;
		}
		// If it is hidden, display it
		if ( _stackHover.style.visibility == "hidden" ){
			_stackHover.style.visibility = 'visible';
		}
		_stackHover.style.left = iX + 20; // Move the object
		_stackHover.style.top = iY + 20;
	}
}

function hideHover( sContainerName){
	// Remove object from DOM
	document.getElementById( "objHoverLayer_" + sContainerName ).parentNode.removeChild( _stackHover );
	// Remove object from stack
	_stackHover = null;
}

// Capture mouse move
try {
	// IE
	document.attachEvent( "onmousemove", moveHover);
} catch (ex) {
	// FF etc.
	document.addEventListener( "mousemove", moveHover, true);
}

// Create Hover object
function createHover( sName, iId){
	showHover( sName, 480, 900, iId);
}
