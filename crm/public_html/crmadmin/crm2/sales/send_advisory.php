<?php
// Send advisory?
if ( $_GET['vuln_id'] && $_GET['email'] ) {
	// Load advisory formatting settings
	define('LANG_ID', 1);
	include '/home/<USER>/public_html/crmadmin/vuln_track/sender/email_functions.php';

	// Generate email content
	list($sSubject, $sContent) = GenerateAdvisoryEmail($_GET['vuln_id'], '', 'Secunia recommends that you verify all advisories you receive by
clicking the link.
Secunia NEVER sends attached files with advisories.
Secunia does not advise people to install third party patches, only
use those supplied by the vendor.

Definitions: (Criticality, Where etc.)
http://secunia.com/about_secunia_advisories/

Contact details:
Web     : http://secunia.com/
E-mail  : <EMAIL>
Tel     : +45 7020 5144
Fax     : +45 7020 5145');

	// Send email
	mail($_GET['email'], '[SA' . intval($_GET['vuln_id']) . '] ' . $sSubject, $sContent, 'From: Secunia <<EMAIL>>');

?>

<table width="100%" cellpadding="0" cellspacing="0">

	<?php
	$sTitle = 'Sent Advisory';
	$iColSpan = 2;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td colspan="2" class="TablePadding">
			Secunia advisory "<b><?= '[SA' . intval($_GET['vuln_id']) . '] ' . htmlspecialchars($sSubject) ?></b>" was successfully sent to "<b><?= htmlspecialchars($_GET['email']) ?></b>". <a href="javascript:history.back();">Return to previous page</a>.
		</td>
	</tr>
	</form>

	<tr>
		<td><br><br></td>
	</tr>

</table>

<?php
	
	exit();
}

?>

<table width="100%" cellpadding="0" cellspacing="0">
	<?php
	$sTitle = 'Send Advisory';
	$iColSpan = 2;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<form method="GET" action="?">
	<input type="hidden" name="page" value="send_advisory">
	<tr>
		<td colspan="2" class="TablePadding">
			Search for:<br>
			<input type="text" name="search" style="width: 200px;" value="<?=htmlspecialchars($_GET['search'])?>"> <input type="submit" name="submit" value="Search">
		</td>
	</tr>
	</form>

	<tr>
		<td><br><br></td>
	</tr>

	<tr>
		<td colspan="2">
			<?php
			// Search
			if ( $_GET['search'] ) {
				// Delimeter
				$sTitle = 'Advisories matching: "' . htmlspecialchars($_GET['search']) . '"';
				$iColSpan = 2;
				$bPrint = false;
				$bWindowTitle = false;
				include INCLUDE_PATH . 'page_header.php';

				// Make search
				$aAdvisories = DBGetRows('vuln_track.os_soft, vuln_track.os_soft_rel, vuln_track.vuln', "os_soft.os_soft_name LIKE '%" . $_GET['search'] . "%' AND (os_soft_rel.os_id = os_soft_id OR os_soft_rel.soft_id = os_soft_id) AND vuln.vuln_id = os_soft_rel.vuln_id", 'vuln.vuln_create_date DESC');

				// New table
				echo '<table width="100%" cellpadding="0" cellspacing="0">';

				// Output
				echo '<tr><td width="10%" class="TableSubHeadline TableGreyBackground TablePadding"><b>Date</b></td>';
				echo '<td width="5%" class="TableSubHeadline TableGreyBackground"><b>SAID</b></td>';
				echo '<td width="45%" class="TableSubHeadline TableGreyBackground"><b>Title</b></td>';
				echo '<td width="20%" class="TableSubHeadline TableGreyBackground"><b>E-mail</b></td>';
				echo '<td width="20%" class="TableSubHeadline TableGreyBackground"><b>Send</b></td></tr>';

				// Register of advisories shown
				$aShown = array();

				// Loop over result
				while ( list($iKey, $aAdvisory) = each($aAdvisories) ) {
					if ( $aShown[$aAdvisory['vuln_id']] ) {
						continue;
					}
					$aShown[$aAdvisory['vuln_id']] = true;

					// Determine Class
					$sClass = ( $sClass === '' ? ' class="TableGreyBackground"' : '' );

					echo '<form method="GET" action="?"><input type="hidden" name="vuln_id" value="' . $aAdvisory['vuln_id'] . '"><input type="hidden" name="page" value="send_advisory">';
					echo '<tr'. $sClass . '><td class="TablePadding">' . $aAdvisory['vuln_create_date'] . '</td>';
					echo '<td>SA' . $aAdvisory['vuln_id'] . '</td>';
					echo '<td><a href="http://secunia.com/advisories/' . $aAdvisory['vuln_id'] . '/" target="_blank">' . $aAdvisory['vuln_title'] . '</a></td>';
					echo '<td><input type="text" name="email"></a></td>';
					echo '<td><input type="submit" value="Send"></td></tr></form>';
				}

				// End table
				echo '</table>';
			}
			?>
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>
</table>
</form>
