<?php
error_reporting(0);

// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

// Open DB Connection
fOpenDatabase();

$aContacts = DBGetRows('crm.salespeople', "salespeople.display = 1", 'salespeople.name');

header("Content-type: text/directory");
header("Content-Disposition: attachment; filename=secunia_contacts_".strftime("%Y%m%d%H%M%S").".vcf");
header("Pragma: public");
foreach($aContacts as $aContact){
	echo "BEGIN:VCARD\nVERSION:3.0\nTEL;TYPE=WORK,VOICE;X-EVOLUTION-UI-SLOT=1:".$aContact['local_number']."\nEMAIL;TYPE=WORK;X-EVOLUTION-UI-SLOT=1:".$aContact['email']."\nURL:\nTITLE:\nROLE:\nX-EVOLUTION-MANAGER:\nX-EVOLUTION-ASSISTANT:\nNICKNAME:\nX-EVOLUTION-SPOUSE:\nNOTE:\nFN:".$aContact['name']."\nX-EVOLUTION-FILE-AS:".$aContact['name']."\nX-EVOLUTION-BLOG-URL:\nCALURI:\nFBURL:\nX-EVOLUTION-VIDEO-URL:\nX-MOZILLA-HTML:FALSE\nUID:secunia-id-".$aContact['person_id']."\nREV:".strftime("%Y-%m-%dT%H:%M:%SZ")."\nEND:VCARD\n\n";
}
?>
