<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="3"><b>Progress</b></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="0" onchange="fToggleForecast( false ); iForecastExpectancy = 0;" name="forecast_expectancy" <?= !$GLOBALS['aCustomer']['Case']['forecast_expectancy'] ? 'checked' : '' ?>><b>0%</b> - No recent contact</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="20" onchange="fToggleForecast( false ); iForecastExpectancy = 20;" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 20 ? 'checked' : '' ?>><b>20%</b> - CRC: Order Verification: Done</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="40" onchange="fToggleForecast( false ); iForecastExpectancy = 40;" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 40 ? 'checked' : '' ?>><b>40%</b> - CRC: Solution Setup time scheduled with CSC</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="60" onchange="fToggleForecast( false ); iForecastExpectancy = 60;" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 60 ? 'checked' : '' ?>><b>60%</b> - CSC: Solution setup: Done</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="80" onchange="fToggleForecast( false ); iForecastExpectancy = 80;" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 80 ? 'checked' : '' ?>><b>80%</b> - CRC: Setup verification: Done</label></td>
	</tr>
	<tr>
		<td colspan="3"><label><input type="radio" value="90" onchange="fToggleForecast( true ); iForecastExpectancy = 90; iForecastExpectancy = 0;" name="forecast_expectancy" <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] == 90 ? 'checked' : '' ?>><b>90%</b> - CRC: 6 month Service call: Done</label></td>
	</tr>
</table>
<!--
<br>

<div id="forecast" style="display: <?= $GLOBALS['aCustomer']['Case']['forecast_expectancy'] > 50 ? 'block' : 'none' ?>;">
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="2"><b>Amount &amp; Expected Closed</b></td>
	</tr>
	<tr>
		<td width="35%">Amount &euro;</td>
		<td width="65%"><input type="text" name="forecast_amount" id="forecast_amount" value="<?= number_format($GLOBALS['aCustomer']['Case']['forecast_amount']) ?>" style="width: 150px;"></td>
	</tr>
	<tr>
		<td>Expected closed (YYYY-MM-DD)</td>
		<td><input type="text" name="forecast_date" id="forecast_date" value="<?= $GLOBALS['aCustomer']['Case']['forecast_date'] ?>" style="width: 150px;"></td>
	</tr>
</table>
</div>
-->
<br>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="25%"><a href="?page=invoice&amp;cst_id=<?= intval($_GET['cst_id']) ?>">&lt;&lt; Won</a></td>
		<td width="40%" align="center"></td>
		<td width="35%" align="right">
			<a href="javascript:void(0);" onClick="fToggleLost( this );">Lost &gt;&gt;</a>
			<div align="left" id="lost" style="display: none; position: absolute; padding: 2px; width: 210px; background: #FFFFFF; border: 1px solid #000000;">
			<b>Lost Reason:</b><br>
			<label><input type="radio" name="lost_reason" value="1" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 1 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="2" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 2 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="3" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 3 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="4" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 4 ) ?></label><br>
			<label><input type="radio" name="lost_reason" id="lost_reason_5" value="5" onClick="fChangeLostNextContact( true );"> <?= fReturnLostReason( 5 ) ?></label><br>
			<label><input type="radio" name="lost_reason" id="lost_reason_6" value="6" onClick="fChangeLostNextContact( true );"> <?= fReturnLostReason( 6 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="7" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 7 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="8" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 8 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="9" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 9 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="10" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 10 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="11" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 11 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="12" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 12 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="13" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 13 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="17" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 17 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="28" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 28 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="29" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 29 ) ?></label><br>
			<label><input type="radio" name="lost_reason" value="30" onClick="fChangeLostNextContact( false );"> <?= fReturnLostReason( 30 ) ?></label><br>
			<!--<br>
			<b>Keep Customer?</b><br>
			<input type="checkbox" name="lost_keep" value="1" onchange=" if ( this.checked ) { fChangeLostNextContact( false ); fChangeLostNextAppointment( true ); } else { fChangeLostNextAppointment( false ); } "> Yes<br>-->
			<br>
			<div id="lost_next_contact" style="display: none;">
			<b>Next Contact (YYYY-MM-DD)</b><br>
			<input type="text" name="lost_next_contact"><br>
			<br>
			</div>
			<div id="lost_keep_appointment" style="display: none;">
			<b>Next Appointment</b><br>
			<input type="text" name="lost_keep_appointment" value="<?= $GLOBALS['aCustomer']['Case']['appointment'] ?>" onKeyUp="document.getElementById('appointment').value = this.value;"><br>
			<br>
			</div>
			<b>Comment:</b><br>
			<textarea name="lost_comment"></textarea><br>
			<br>
			<input type="submit" name="save_button" value="Save - Lost - Today">
			</div>
		</td>
	</tr>
</table>

