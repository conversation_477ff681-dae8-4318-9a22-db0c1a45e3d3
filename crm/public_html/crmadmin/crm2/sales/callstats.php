<?php
if ($_GET['from']){
	$iFrom = strtotime($_GET['from']." 00:00:00");
} else {
	$iFrom = mktime(0, 0, 0)-2592000;
}

if ($_GET['to']){
	$iTo = strtotime($_GET['to']." 23:59:59");
} else {
	$iTo = mktime(23, 59, 59);
}

if ($_GET['person1_id']){
	$iPerson1 = (int) $_GET['person1_id'];
	$aPersons = DBGetRows("salespeople", "person_id='".$iPerson1."' AND display=1 AND department=2 AND person_level=3", "name ASC");
	if (sizeof($aPersons) == 0) die();
}
if ($_GET['person2_id']){
	$iPerson2 = (int) $_GET['person2_id'];
	$aPersons = DBGetRows("salespeople", "person_id='".$iPerson2."' AND display=1 AND department=2 AND person_level=3", "name ASC");
	if (sizeof($aPersons) == 0) die();
}
if ($_GET['person3_id']){
	$iPerson3 = (int) $_GET['person3_id'];
	$aPersons = DBGetRows("salespeople", "person_id='".$iPerson3."' AND display=1 AND department=2 AND person_level=3", "name ASC");
	if (sizeof($aPersons) == 0) die();
}

function fGetPersonName($iPersonID){
	$rQuery = mysql_query("SELECT name FROM salespeople WHERE person_id='".$iPersonID."'");
	if (mysql_num_rows($rQuery) == 0){
		return "N/A";
	} else {
		$aPersonData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
		return htmlspecialchars($aPersonData['name']);
	}
}

//2 is outgoing
//1 is incoming

function fGetCallStats($iPersonID, $iTypeID){
	$iSuccess = 0;
	$iFailed = 0;
	$iCallDuration = 0;
	$rQuery = mysql_query("SELECT local_number FROM salespeople WHERE person_id='".$iPersonID."'");
	$aPersonData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	if ($iTypeID == 1){
		//Incoming
		$rQuery = mysql_query("SELECT * FROM it_phone_cdr WHERE LENGTH(call_src) > 3 AND call_dst='".$aPersonData['local_number']."' AND call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	} elseif ($iTypeID == 2) {
		//Outgoing
		$rQuery = mysql_query("SELECT * FROM it_phone_cdr WHERE call_src='".$aPersonData['local_number']."' AND LENGTH(call_dst) > 3 AND call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	}
	while($aCallData = mysql_fetch_array($rQuery, MYSQL_ASSOC)){
		if ($aCallData['call_billsec'] == 0){
			//Unsuccessful
			$iFailed++;
		} else {
			//Successful
			$iSuccess++;
			$iCallDuration = $iCallDuration + $aCallData['call_billsec'];
		}
	}
	$aReturn['iFailed'] = $iFailed;
	$aReturn['iSuccessful'] = $iSuccess;
	$aReturn['iCallDuration'] = $iCallDuration;
	return $aReturn;
}

function fSecondsToHuman($iSeconds){
	$iHours = floor($iSeconds/3600);
	$iBalance = $iSeconds-($iHours*3600);
	$iMinutes = floor($iBalance/60);
	$iSeconds = floor($iBalance-($iMinutes*60));
	return str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
}
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="">
		<input type="hidden" name="page" value="callstats">
		<input type="hidden" name="right" value="overview">
		<tr>
			<td colspan="2">
				<b>Select period to display</b><br>
				<input type="text" value="<?=strftime("%Y-%m-%d", $iFrom);?>" name="from" style="width: 150px;"> - <input type="text" value="<?=strftime("%Y-%m-%d", $iTo)?>" name="to" style="width: 150px;"><br>
			</td>
			<td colspan="3">
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td width="33%">
							<b>Select sales person 1</b><br>
							<select name="person1_id"><option value="">- Select -</option><?php
							$aPersons = DBGetRows("salespeople", "display=1 AND department=2 AND person_level=3", "name ASC");
							foreach($aPersons as $aPerson){
								if ($_GET['person1_id'] == $aPerson['person_id']){
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\" selected>".htmlspecialchars($aPerson['name'])."</option>";
								} else {
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\">".htmlspecialchars($aPerson['name'])."</option>";
								}
							}
							?></select>
						</td>
						<td width="33%">
							<b>Select sales person 2</b><br>
							<select name="person2_id"><option value="">- Select -</option><?php
							foreach($aPersons as $aPerson){
								if ($_GET['person2_id'] == $aPerson['person_id']){
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\" selected>".htmlspecialchars($aPerson['name'])."</option>";
								} else {
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\">".htmlspecialchars($aPerson['name'])."</option>";
								}
							}
							?></select>
						</td>
						<td width="34%">
							<b>Select sales person 3</b><br>
							<select name="person3_id"><option value="">- Select -</option><?php
							foreach($aPersons as $aPerson){
								if ($_GET['person3_id'] == $aPerson['person_id']){
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\" selected>".htmlspecialchars($aPerson['name'])."</option>";
								} else {
									echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\">".htmlspecialchars($aPerson['name'])."</option>";
								}
							}
							?></select>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	
		<tr>
			<td colspan="7" width="100%"><br></td>
		</tr>
		
		<tr>
			<td colspan="7" width="100%"><input type="submit" value="Display"></td>
		</tr>
	</form>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>

	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Incoming Summary Report</h2></b>
			<?php
			$aDataPerson1 = fGetCallStats($iPerson1, 1);
			$aDataPerson2 = fGetCallStats($iPerson2, 1);
			$aDataPerson3 = fGetCallStats($iPerson3, 1);
			?>
		</td>
	</tr>
	<tr>
		<td colspan="7" valign="top">
			<table width="850" cellspacing="0" cellpadding="0">
				<tr>
					<td></td>
					<td align="right" width="150"><b><?php echo ($iPerson1 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson1."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson1);?><?php echo($iPerson1 > 0 ? "</a>" : ""); ?></b></td>
					<td align="right" width="150"><b><?php echo ($iPerson2 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson2."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson2);?><?php echo($iPerson2 > 0 ? "</a>" : ""); ?></b></td>
					<td align="right" width="150"><b><?php echo ($iPerson3 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson3."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson3);?><?php echo($iPerson3 > 0 ? "</a>" : ""); ?></b></td>
				</tr>
				<tr>
					<td><b>No. of successful calls</b></td>
					<td align="right"><?=number_format($aDataPerson1['iSuccessful'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson2['iSuccessful'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson3['iSuccessful'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>No. of failed calls</b></td>
					<td align="right"><?=number_format($aDataPerson1['iFailed'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson2['iFailed'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson3['iFailed'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>Total no. of calls</b></td>
					<td align="right"><?=number_format(($aDataPerson1['iSuccessful']+$aDataPerson1['iFailed']), 0, ".", ","); ?></td>
					<td align="right"><?=number_format(($aDataPerson2['iSuccessful']+$aDataPerson2['iFailed']), 0, ".", ","); ?></td>
					<td align="right"><?=number_format(($aDataPerson3['iSuccessful']+$aDataPerson3['iFailed']), 0, ".", ","); ?></td>
				</tr>
				<tr><td><br></td></tr>
				<tr>
					<td><b>Total call duration:</b></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson1['iCallDuration']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson2['iCallDuration']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson3['iCallDuration']); ?></td>
				</tr>
				<tr>
					<td><b>Average call duration (successfull calls):</b></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson1['iCallDuration']/$aDataPerson1['iSuccessful']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson2['iCallDuration']/$aDataPerson2['iSuccessful']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson3['iCallDuration']/$aDataPerson3['iSuccessful']); ?></td>
				</tr>
			</table>
		</td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Outgoing Summary Report</h2></b>
			<?php
			$aDataPerson1 = fGetCallStats($iPerson1, 2);
			$aDataPerson2 = fGetCallStats($iPerson2, 2);
			$aDataPerson3 = fGetCallStats($iPerson3, 2);
			?>
		</td>
	</tr>
	<tr>
		<td colspan="7" valign="top">
			<table width="850" cellspacing="0" cellpadding="0">
				<tr>
					<td></td>
					<td align="right" width="150"><b><?php echo ($iPerson1 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson1."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson1);?><?php echo($iPerson1 > 0 ? "</a>" : ""); ?></b></td>
					<td align="right" width="150"><b><?php echo ($iPerson2 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson2."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson2);?><?php echo($iPerson2 > 0 ? "</a>" : ""); ?></b></td>
					<td align="right" width="150"><b><?php echo ($iPerson3 > 0 ? "<a href=\"?page=callcdr&right=overview&person_id=".$iPerson3."&from=".strftime("%Y-%m-%d", $iFrom)."&to=".strftime("%Y-%m-%d", $iTo)."\">" : "");?><?=fGetPersonName($iPerson3);?><?php echo($iPerson3 > 0 ? "</a>" : ""); ?></b></td>
				</tr>
				<tr>
					<td><b>No. of successful calls</b></td>
					<td align="right"><?=number_format($aDataPerson1['iSuccessful'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson2['iSuccessful'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson3['iSuccessful'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>No. of failed calls</b></td>
					<td align="right"><?=number_format($aDataPerson1['iFailed'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson2['iFailed'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aDataPerson3['iFailed'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>Total no. of calls</b></td>
					<td align="right"><?=number_format(($aDataPerson1['iSuccessful']+$aDataPerson1['iFailed']), 0, ".", ","); ?></td>
					<td align="right"><?=number_format(($aDataPerson2['iSuccessful']+$aDataPerson2['iFailed']), 0, ".", ","); ?></td>
					<td align="right"><?=number_format(($aDataPerson3['iSuccessful']+$aDataPerson3['iFailed']), 0, ".", ","); ?></td>
				</tr>
				<tr><td><br></td></tr>
				<tr>
					<td><b>Total call duration:</b></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson1['iCallDuration']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson2['iCallDuration']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson3['iCallDuration']); ?></td>
				</tr>
				<tr>
					<td><b>Average call duration (successfull calls):</b></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson1['iCallDuration']/$aDataPerson1['iSuccessful']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson2['iCallDuration']/$aDataPerson2['iSuccessful']); ?></td>
					<td align="right"><?=fSecondsToHuman($aDataPerson3['iCallDuration']/$aDataPerson3['iSuccessful']); ?></td>
				</tr>
			</table>
		</td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
</table>
