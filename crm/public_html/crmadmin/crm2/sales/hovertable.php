<?php
	if ( !fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ){
		// Grant access to Lead Management or above
		echo "Permission denied.";
		die();
	}

	$iFormId = (int) $_GET['id'];

	// Fetch forms list
	$rResource = DBQuery("SELECT * FROM crm.form_data where id = " . $iFormId);
	$iNumRows = mysql_num_rows( $rResource );

	if ( $iNumRows == 1 ){
		$aRow = mysql_fetch_array( $rResource );

		// Form data
		$sFormName = htmlspecialchars( $aRow['form_name'] );
		$sSubmitDate = htmlspecialchars( $aRow['submit_date'] );
		$sRecipient = htmlspecialchars( $aRow['secunia_recipient'] );
		$sFormSubmitButton = htmlspecialchars( $aRow['form_submit_button'] );
		$sSubject = htmlspecialchars( $aRow['text_subject'] );

		// Company data
		$sCSTId = htmlspecialchars( $aRow['cst_id'] );

		$sCompanyName = htmlspecialchars( $aRow['company_name'] );
		$sCompanyType = htmlspecialchars( $aRow['company_type'] );
		$sCompanyNeed = htmlspecialchars( $aRow['company_need'] );
		$sCompanySize = htmlspecialchars( $aRow['company_hosts_network'] );
		$sCompanyAddress = htmlspecialchars( $aRow['company_address'] );
		$sCompanyCity = htmlspecialchars( $aRow['company_city'] );
		$sCompanyState = htmlspecialchars( $aRow['company_state'] );
		$sCompanyZip = htmlspecialchars( $aRow['company_zip'] );
		$sCompanyPoBox = htmlspecialchars( $aRow['company_po_box'] );

		// Financial data
		$sFinancialName = htmlspecialchars( $aRow['financial_name'] );
		$sFinancialEmail = htmlspecialchars( $aRow['financial_email'] );
		$sFinancialPhone = htmlspecialchars( $aRow['financial_phone'] );

		// Technical data
		$sTechnicalName = htmlspecialchars( $aRow['technical_name'] );
		$sTechnicalEmail = htmlspecialchars( $aRow['technical_email'] );
		$sTechnicalPhone = htmlspecialchars( $aRow['technical_phone'] );

		// Contact person data
		$sPersonName = htmlspecialchars( $aRow['person_name'] );
		$sPersonJobTitle = htmlspecialchars( $aRow['person_job_title'] );
		$sPersonDepartment = htmlspecialchars( $aRow['person_department'] );
		$sPersonPhone = htmlspecialchars( $aRow['person_phone'] );
		$sPersonEmail = htmlspecialchars( $aRow['person_email'] );
		$sPersonWeb = htmlspecialchars( $aRow['person_web'] );
		$sPersonHosts = htmlspecialchars( $aRow['person_hosts_responsible'] );
		$sPersonCountry = htmlspecialchars( $aRow['person_country'] );

		// Forecast data
		$sForecastPotential = htmlspecialchars( $aRow['forecast_potential'] );
		$sForecastPurchase = htmlspecialchars( $aRow['forecast_purchase_timeframe'] );

		// Text data
		$sTextReferral = htmlspecialchars( $aRow['text_referral'] );
		$sTextReason = htmlspecialchars( $aRow['text_reason'] );
		$sTextComment = htmlspecialchars( $aRow['text_comment'] );

		// Marketing data
		$sPartnershipProgram = htmlspecialchars( $aRow['partnership_program'] );
		$sRefCode = htmlspecialchars( $aRow['ref_code'] );

		// Direct marketing subscription type
		$sSubscriptionType = htmlspecialchars( $aRow['subscription_type'] );

		// Misc data
		$sEventDate = htmlspecialchars( $aRow['event_date'] );
	}

	// Fetch navigation history
	$rResult = DBQuery("SELECT * FROM crm.form_stats WHERE form_id = '".$iFormId."' order by id desc");
	$iNumStats = mysql_num_rows( $rResult );

?>
<style>
       	body {
              	margin: 0px;
                padding: 0px;
        }
	.main_table{
                font-family: verdana;
                font-size: 10px;
        }
	.content_table{
		font-family: verdana;
		font-size: 10px;
	}
</style>
<table width="100%" height="100%" cellspacing="0" cellpadding="0" border="0">
<tr valign="top">
<td width="300">
<table border="0" cellspacing="0" cellpadding="0" width="300" class="main_table">
        <tr>
            	<td>
			<b>Form details</b>
			<table cellspacing="0" cellpadding="0" border="0" class="content_table" width="100%">
				<tr valign="top">
					<td>
						<nobr>Customer id (CSI Trial):</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCSTId; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Form name:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sFormName; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Submit date:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sSubmitDate; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Recipient:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sRecipient; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person email:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonEmail; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person name:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonName; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Job Title:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonJobTitle; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Department:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonDepartment; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Phone:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonPhone; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Web:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonWeb; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Hosts:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonHosts; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Person Country:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPersonCountry; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company name:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyName; ?>
					</td>

				</tr>
				<tr valign="top">
					<td>
						<nobr>Company type:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyType; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company need:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyNeed; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company hosts:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanySize; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Forecast potential:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sForecastPotential; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Forecast timeframe:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sForecastPurchase; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Text referral:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTextReferral; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Text reason:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTextReason; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Text comment:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTextComment; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Text subject:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sSubject; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Partnership program:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sPartnershipProgram; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Reference code:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sRefCode; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company P.O. Box:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyPoBox; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company address:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyAddress; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company city:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyCity; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company state:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyState; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Company zip:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sCompanyZip; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Financial contact name:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sFinancialName; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Financial contact email:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sFinancialEmail; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Financial contact phone:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sFinancialPhone; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Technical contact name:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTechnicalName; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Technical contact email:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTechnicalEmail; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Technical contact phone:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sTechnicalPhone; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Subscription type:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sSubscriptionType; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Event date:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sEventDate; ?>
					</td>
				</tr>
				<tr valign="top">
					<td>
						<nobr>Form submit button:</nobr>
					</td>
					<td width="100%" style="padding-left: 10px;">
						<?= $sFormSubmitButton; ?>
					</td>
				</tr>
			</table>
                </td>
        </tr>
</table>
</td>
<td style="border-left: solid black 1px;">
<table border="0" cellspacing="0" cellpadding="0" width="596" class="main_table">
	<tr>
		<td>
			<b>Navigation History</b>
		</td>
	</tr>
	<tr>
		<td>
			<table cellspacing="0" cellpadding="0" border="0" class="content_table" width="100%">
				<tr>
					<td>
						<b>Link</b>
					</td>
					<td>
						<b>Duration</b>
					</td>
				</tr>
				<?php
					$iStripe = 0;
					for ( $i = 0; $i < $iNumStats; $i++ ){
						$iStripe = ( ( $iStripe == 1 ) ? 0 : 1 );
						$aRow = mysql_fetch_array( $rResult );
						echo "<tr valign='top' style='background-color: ".( ( $iStripe == 1 ) ? 'whitesmoke' : 'white' ).";'>";
						echo "<td>".htmlspecialchars( $aRow['page'] )."</td>";
						echo "<td><nobr>".htmlspecialchars( $aRow['time'] )."</nobr></td>";
						echo "</tr>";
					}
				?>
			</table>
		</td>
	</tr>
</table>
</td>
</tr>
</table>
