<?php

/**
 * @file _common_module.php
 * This file outputs a single module row and is included by _common_module_list.php
 */

$toggleDivId ++;
// The checked state of the visibility checkbox
$visibleChecked = (int) $aAccountCustomer['show_modules'] & $module->getVisibilityBitmask();
// Whether Current User is allowed to work with the checkbox or not.
$enableCheckboxes = empty( $aModuleRestrictions[MOD_NSI] )
					|| ( is_array( $aModuleRestrictions[MOD_NSI] ) && in_array( $aRepData['person_id'], $aModuleRestrictions[MOD_NSI] ) );
// Whether to render this value as a hidden element or as a checkbox so the user can toggle it
$isHidden = !( empty( $aModuleRestrictionsEnable[MOD_NSI] )
			|| ( $aModuleRestrictionsEnable[MOD_NSI] && in_array( $aRepData['person_id'], $aModuleRestrictionsEnable[MOD_NSI] ) ) );
?>

<tr style="background-color: <?php echo ( $odd ^= 1 ) ? '#DFDFDF' : '#FFFFFF'; ?>">
	<td style="vertical-align: middle;">
		<div style="line-height: 22px;" onclick="var el = document.getElementById('moremod<?php echo $toggleDivId; ?>'); el.style.display = el.style.display == 'none' ? 'block' : 'none';">
			<?php echo $module->getName(), ( '' == $module->getVersionSupport() ) ? '' : " ({$module->getVersionSupport()})", '<br>'; ?>

		</div>
		<div style="display: none; color: gray" id="moremod<?php echo $toggleDivId; ?>">
			<!-- Place extra config options in this node -->
			<?php echo $module->getDescription(); ?>
		</div>
	</td>
	<td>
	<?php
	// if hidden then we use a hidden INPUT and set it's real value.
	if ( false === $isHidden ) : ?>

		<input type="checkbox" name="common_modules_enabled[<?php echo $cstId; ?>][]" value="<?php echo $module->getId(); ?>" style="width: 15px;" <?php echo ( $module->getEnabled() ? 'checked="checked"' : '' ), ( $enableCheckboxes ? '' : ' disabled' ); ?> />
	<?php else : // false == $isHidden ?>
		<?php if ( $module->getEnabled() ) : ?>

		<input type="hidden" name="common_modules_enabled[<?php echo $cstId; ?>][]" value="<?php echo $module->getId(); ?>" />
		<?php endif; // #ENDIF getEnabled ?>
		<?php echo $module->getEnabled() ? 'Enabled' : 'Disabled'; ?>
	<?php endif; // ENDIF $isHidden ?>

	</td>
	<td>
		<input type="checkbox" name="modules_visible[<?php echo $module->getVisibilityBitmask(); ?>]" value="1" style="width: 15px;" <?php echo ( $visibleChecked ? 'checked="checked"' : '' ), ( $enableCheckboxes ? '' : ' disabled' ); ?> />
	</td>
	<td></td>
</tr>