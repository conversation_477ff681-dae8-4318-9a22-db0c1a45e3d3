<tr>
	<td colspan="12">
		<form method="GET" action="">
			<input type="hidden" name="person_id" value="<?= (int) $_GET['person_id'] ?>">
			<input type="hidden" name="task" value="<?= htmlspecialchars($_GET['task']) ?>">
			<input type="hidden" name="type" value="<?= htmlspecialchars($_GET['segment_id']) ?>">
			<input type="hidden" name="page" value="admin_segment_overview_assign">
			<table width="100%">
				<tr>
					<td>
						<b>Search:</b><br>
						<input type="text" name="search" style="width: 250px" value="<?= htmlspecialchars($_GET['search']) ?>">
					</td>
					<td>
						<b>Segment:</b><br>
						<?= makeSegmentCombo( (int)$_GET['segment_id'],"","segment_id") ?>
					</td>
					<td>
						<b>Account type:</b><br>
						<select name="account_type">
							<option value="all"<?=($_GET['account_type'] == "all" ? " selected" : "");?>> - All - </option>
							<option value="CSI"<?=($_GET['account_type'] == "CSI" ? " selected" : "");?>>CSI</option>
							<option value="TLA"<?=($_GET['account_type'] == "TLA" ? " selected" : "");?>>TLA</option>
							<option value="Partner"<?=($_GET['account_type'] == "Partner" || $_GET['account_type'] == "SA" ? " selected" : "");?>>Partner</option>
							<option value="CSC"<?=($_GET['account_type'] == "CSC" ? " selected" : "");?>>CSC</option>
						</select>
					</td>
					<td>
						<nobr><b>Creation date ( YYYY-MM-DD ):</b><br></nobr>
						<nobr><input type="text" name="create_date_1" style="width:80px;" value="<?= htmlspecialchars( $_GET['create_date_1'] ) ?>"> - <input type="text" name="create_date_2" style="width:80px;" value="<?= htmlspecialchars( $_GET['create_date_2'] ) ?>"></nobr>
					</td>
					<td>
						<b>Lost:</b><br>
						<nobr>
							<select name="lost_interval" id="lost_interval">
								<option value="0">- Interval -</option>
								<option <?= ( ( $_GET['lost_interval'] == "0-30" ) ? "selected" : "" ) ?> value="0-30">0-30</option>
								<option <?= ( ( $_GET['lost_interval'] == "30-60" ) ? "selected" : "" ) ?> value="30-60">30-60</option>
								<option <?= ( ( $_GET['lost_interval'] == "60-90" ) ? "selected" : "" ) ?> value="60-90">60-90</option>
								<option <?= ( ( $_GET['lost_interval'] == "90-120" ) ? "selected" : "" ) ?> value="90-120">90-120</option>
								<option <?= ( ( $_GET['lost_interval'] == "120-150" ) ? "selected" : "" ) ?> value="120-150">120-150</option>
								<option <?= ( ( $_GET['lost_interval'] == "150-180" ) ? "selected" : "" ) ?> value="150-180">150-180</option>
								<option <?= ( ( $_GET['lost_interval'] == ">180" ) ? "selected" : "" ) ?> value=">180">>180</option>
							</select> days ago
						</nobr>
					</td>
					<td>
						<b>Industry:</b><br>
						<select name="company_industry">
							<option value="all"<?=($_GET['company_industry'] == "all" ? " selected" : "");?>> - All - </option>
							<?php
							$aIndustries = fSortArrayOther($GLOBALS['aIndustries']);
							while ( list($iIndustryID, $sIndustry) = each($aIndustries) ) {
								echo "<option value=\"".$iIndustryID."\"".($_GET['company_industry'] == $iIndustryID ? " selected" : "").">".htmlspecialchars($sIndustry)."</option>";
							}
							?>
						</select>
					</td>
					<td>
						<b>Country:</b><br>
						<select name="invoice_country">
							<option value="all"<?=($_GET['invoice_country'] == "all" ? " selected" : "");?>> - All - </option>
							<?php
							$sQuery = "SELECT id, country FROM crm.countries ORDER BY country ASC";
							$aCountries = DBQueryGetRows($sQuery);
							foreach($aCountries as $aCountry){
								$iCountryID = $aCountry['id'];
								$sCountry = $aCountry['country'];
								echo "<option value=\"".$iCountryID."\"".($_GET['invoice_country'] == $iCountryID ? " selected" : "").">".htmlspecialchars($sCountry)."</option>";
							}
							?>
						</select>
					</td>
					<td>
						<b>Lead Source:</b><br>
						<select name="lead_source">
							<option value="all"<?=($_GET['lead_source'] == "all" ? " selected" : "");?>> - All - </option>
							<?php
							while ( list($iLeadSourceID, $sLeadSource) = each($GLOBALS['aLeadSources']) ) {
								echo "<option value=\"".$iLeadSourceID."\"".($_GET['lead_source'] == $iLeadSourceID ? " selected" : "").">".htmlspecialchars($sLeadSource)."</option>";
							}
							?>
						</select>
					</td>
					<td><input type="submit" value="Search"></td>
				</tr>
				<tr>
					<td colspan="7">
						<input onclick="if ( this.checked ) { document.location = document.location + '&trash=on' } else { var dl = new String(document.location); document.location = dl.replace(/&trash=on/g, ''); };" type="checkbox" <?php echo ( ( $_GET['trash'] == "on" ) ? "checked" : "" ); ?>>Show Trash content
					</td>
				</tr>
			</table>
		</form>
	</td>
</tr>

<tr>
	<td><br></td>
</tr>
