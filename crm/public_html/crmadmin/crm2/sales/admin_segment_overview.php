<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	exit();
}

// Cache?
if ( !file_exists(CACHE_DIR . 'admin_segment_overview.cache') || $_GET['refresh_cache'] || ( $_GET['trash'] == "on" ) ) {
	if ( $_GET['trash'] == "on" ){
		$iTrashSegmentId = -1;
		$sHideTrashSegment = "";
	} else {
		$iTrashSegmentId = 1481;
		$sHideTrashSegment = " AND segment_id <> ".$iTrashSegmentId;
	}
	// Build array of special CRM2 segments, that don't fit in the "Country->Size" segmentation
	$aSpecialSegments = array();
	$aSegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' ".$sHideTrashSegment." AND segments.segment_name NOT LIKE '% - Canvas' AND segments.segment_name NOT LIKE '% - Lost Leads' AND segments.segment_status = 0", 'segments.segment_name');
	while ( list($iKey, $aSegment) = each($aSegments) ) {
		$aSpecialSegments[$aSegment['segment_id']]['name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);
		$aSpecialSegments[$aSegment['segment_id']]['count'] = 0;
		$aSpecialSegments[$aSegment['segment_id']]['total'] = 0;
	}

	// Select data
	$sQuery = "SELECT cst_id, invoice_country, region, large_account, master_id, segment_id, region FROM crm.cst LEFT JOIN crm.countries ON ( cst.invoice_country = countries.id ) WHERE cst.case_name = 'Company Card' AND (segment_id != ".$iTrashSegmentId." || segment_id IS NULL)";
	$rCustomers = DBQuery($sQuery);
	$iCustomers = mysql_num_rows($rCustomers);
 
	// Loop through results and sort according to segments
	while ( $aCustomer = mysql_fetch_array($rCustomers) ) {
		$aCustomer['invoice_country'] = intval($aCustomer['invoice_country']);

		// Detailed count
		// Total
		$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['total']++;

		// "Free" / available
		$iActive = DBNumRows('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name IN ('card_vi', 'card_crc', 'card_nsi', 'card_partner') AND cst.person_id > 0");
		if ( $aSpecialSegments[$aCustomer['segment_id']] && $iActive == 0 ) {
			$aSpecialSegments[$aCustomer['segment_id']]['count']++;
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		} elseif ( $aSpecialSegments[$aCustomer['segment_id']] ) {
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		} elseif ( $iActive == 0 && fSegmentNameFromID( $aCustomer['segment_id'] ) != 'n/a' ) {
			// Free - but assigned
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free_assigned']++;
			$aRegionCountFreeAssigned[$aCustomer['region']]++;
			$aCountryCountFreeAssigned[$aCustomer['invoice_country']]++;
		} elseif ( $iActive == 0 ) {
			// Free
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free']++;
			$aRegionCountFree[$aCustomer['region']]++;
			$aCountryCountFree[$aCustomer['invoice_country']]++;
		}
	
		// Country count
		$aCountryCount[$aCustomer['invoice_country']]++;

		// Region count
		$aRegionCount[$aCustomer['region']]++;
	}

	// Only cache results without the trash items
	if ( $_GET['trash'] != "on" ){
	// Cache results
	$sCache = '
<?php
$iCustomers = "' . $iCustomers . '";
$aRegions = unserialize(\'' . serialize($aRegions) . '\');
$aRegionCount = unserialize(\'' . serialize($aRegionCount) . '\');
$aRegionCountFreeAssigned = unserialize(\'' . serialize($aRegionCountFreeAssigned) . '\');
$aRegionCountFree = unserialize(\'' . serialize($aRegionCountFree) . '\');
$aCountryCount = unserialize(\'' . serialize($aCountryCount) . '\');
$aCountryCountFree = unserialize(\'' . serialize($aCountryCountFree) . '\');
$aCountryCountFreeAssigned = unserialize(\'' . serialize($aCountryCountFreeAssigned) . '\');
$aSpecialSegments = unserialize(\'' . serialize($aSpecialSegments) . '\');
?>
';

		// Save cache
		file_put_contents(CACHE_DIR . 'admin_segment_overview.cache', $sCache);
	}
} else {
	// Load file cache
	include CACHE_DIR . 'admin_segment_overview.cache';
	$iCache = filemtime( CACHE_DIR . 'admin_segment_overview.cache' );
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Segment Overview (' . number_format($iCustomers) . ' Cards) (' . ( $iCache ? 'CACHE: ' . date('Y-m-d G:i:s', $iCache) . ' - <a href="?page=admin_segment_overview&refresh_cache=1">refresh</a>' : 'Not cached' ) . ')';
	$iColSpan = 5;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<?php
	include 'admin_include_search.php';
	?>

	<tr>
		<td style="padding-left: 25px; border-top: 1px solid #000000;"><b>Segment</b></td>
		<td style="border-top: 1px solid #000000;"><b>Total</b></td>
		<td style="border-top: 1px solid #000000;"><b>Free</b></td>
		<td style="border-top: 1px solid #000000;"><b>Assigned</b></td>
	</tr>

	<tr>
		<td><br></td>
	</tr>
	<tr style="background: #DEDEDE;">
		<td style="padding-left: 5px; font-size: 13px; border-bottom: 1px solid #000000;"><b>Special Segments</b>&nbsp;</td>
		<td style="font-size: 13px; border-bottom: 1px solid #000000;">&nbsp;</b></td>
		<td style="font-size: 13px; border-bottom: 1px solid #000000;">&nbsp;</td>
		<td style="font-size: 13px; border-bottom: 1px solid #000000;">&nbsp;</td>
	</tr>

	<?php
	// Special Result
	while ( list($iSegmentID, $aSegment) = each($aSpecialSegments) ) {
        ?>

                <tr>
                        <td style="padding-top: 5px; padding-left: 25px; "><a href="?page=admin_segment_overview_assign&amp;segment_id=<?= $iSegmentID ?>"><?= $aSegment['name'] ?></a>&nbsp;</td>
                        <td style="padding-top: 5px;"><?= number_format($aSegment['total']) ?></td>
                        <td style="padding-top: 5px;"><?= number_format($aSegment['count']) ?></td>
                        <td style="padding-top: 5px;"><?= number_format($aSegment['total']-$aSegment['count']) ?></td>
                </tr>

	<?php
	}

	// Result
	while ( list($iRegion, $aCountries) = each($aRegions) )
	{
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr style="background: #DEDEDE;">
			<td style="padding-left: 5px; font-size: 13px;"><b><?= fReturnRegionName( $iRegion ) ?></b>&nbsp;</td>
			<td style="font-size: 13px;"><b><?= number_format($aRegionCount[$iRegion]) ?></b></td>
			<td style="font-size: 13px;"><b><?= number_format($aRegionCountFree[$iRegion]) ?></b></td>
			<td style="font-size: 13px;"><b>&nbsp;</b></td>
		</tr>
		<?php
		while ( list($iCountry, $aSegments) = each($aCountries) )
		{
		?>
			<tr>
				<td style="padding-top: 5px; padding-left: 25px; border-top: 1px solid #000000;"><b><?= fCountryNameFromID( $iCountry ) ?></b></td>
				<td style="padding-top: 5px; border-top: 1px solid #000000;"><b><?= number_format($aCountryCount[$iCountry]) ?></b></td>
				<td style="padding-top: 5px; border-top: 1px solid #000000;"><b><?= number_format($aCountryCountFree[$iCountry]) ?></b></td>
				<td style="padding-top: 5px; border-top: 1px solid #000000;"><b>&nbsp;</b></td>
			</tr>
			<?php
			while ( list($iSegment, $aCounts) = each($aSegments) )
			{
			?>
				<tr>
					<td width="20%" style="padding-left: 45px;"><?= $iSegment ? 'LA' : 'SMB' ?></td>
					<td width="20%" ><a href="?page=admin_segment_overview_assign&amp;segment=<?= $iSegment ?>&amp;country_id=<?= $iCountry ?>&amp;type=taken"><?= number_format($aCounts['total']) ?></a></td>
					<td width="15%" ><a href="?page=admin_segment_overview_assign&amp;segment=<?= $iSegment ?>&amp;country_id=<?= $iCountry ?>&amp;type=free"><?= number_format($aCounts['free']) ?></a></td>
					<td width="45%" ><a href="?page=admin_segment_overview_assign&amp;segment=<?= $iSegment ?>&amp;country_id=<?= $iCountry ?>&amp;type=free"><?= number_format($aCounts['free_assigned']) ?></a></td>
				</tr>
			<?php
			}
			?>
		<?php
		}
		?>
	<?php
	}
	?>
</table>
