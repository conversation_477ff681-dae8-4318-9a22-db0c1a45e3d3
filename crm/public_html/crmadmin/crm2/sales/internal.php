<?php
$aContacts = DBGetRows('crm.salespeople', "salespeople.display = 1", 'salespeople.name');
$rLDAP = ldap_connect("ad-hq-1.secunia.local", 389);
ldap_set_option($LDAP, LDAP_OPT_PROTOCOL_VERSION, 3);
ldap_bind($rLDAP, "<EMAIL>", "ijp8gws4");
$aFields = array('cn','mail','telephonenumber','samaccountname');
$rResult = @ldap_search($rLDAP, "OU=Accounts,dc=secunia,dc=local", "(&(objectClass=organizationalPerson)(|(userAccountControl=512)(userAccountControl=66048)))", $aFields);
$aEntries = ldap_get_entries($rLDAP, $rResult);
$aHide = array('sta', 'hja', 'tka', 'mha', 'jba', 'il<PERSON><PERSON><PERSON>', 'jlore<PERSON><PERSON>', 'hen<PERSON><PERSON>', 'larst<PERSON><PERSON>b<PERSON>g', 'c<PERSON><PERSON><PERSON>', 'l<PERSON><PERSON><PERSON>', 'frank<PERSON>nsen', 'sussie<PERSON><PERSON><PERSON>', 'j<PERSON>ven<PERSON>', 'jesperjarmann', 'mirzabaig', 'michaelhartmann', 'dortheasser', 'justinmlynarski', 'jjohansen', 'eja', 'bla', 'maha');
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sAllEmails = '';
	$sTitle = 'Internal Contact List';
	$iColSpan = 4;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr class="TableGreyBackground">
		<td width="10%" class="TableSubHeadline TablePadding">Extension</td>
		<td width="20%" class="TableSubHeadline">Name</td>
		<td width="20%" class="TableSubHeadline">Direct</td>
		<td width="70%" class="TableSubHeadline">Email</td>
	</tr>
<!--	<tr>
		<td class="TablePadding">999</td>
		<td>IT Support</td>
		<td>+45 3338 7600</td>
		<td><a href="mailto:<EMAIL>"><EMAIL></a></td>
	</tr>
	<tr class="TableGreyBackground">
		<td class="TablePadding">888</td>
		<td>Customer Support Center</td>
		<td>+45 3338 7600</td>
		<td><a href="mailto:<EMAIL>"><EMAIL></a></td>
	</tr>-->
	<?	
	function cmp($a, $b){
		return strnatcmp($a['name'], $b['name']);
	}

	foreach($aEntries as $aEntry) {
		if (in_array($aEntry['samaccountname'][0], $aHide)) continue;
		if (trim($aEntry['samaccountname'][0]) == "") continue;
		
		switch(substr($aEntry['telephonenumber'][0], -3, 1)){
			case "":
				$ext = "N/A";
				$direct = "N/A";
				break;
			case "3":
				$ext = substr($aEntry['telephonenumber'][0], -3);
				$direct = $aEntry['telephonenumber'][0];
				break;
			case "4":
			case "6":
			case "7":
				$ext = substr($aEntry['telephonenumber'][0], -3);
				$direct = $aEntry['telephonenumber'][0];
				break;
		}

		if (substr($direct, 0, 3) == "+45"){
			$direct = "+45 ".substr($direct, 3, 4)." ".substr($direct, 7, 4);
		}

		if (substr($direct, 0, 2) == "+1"){
			$direct = "+1 (".substr($direct, 2, 3).") ".substr($direct, 5, 3)." ".substr($direct, 8, 4);
		}

		$aUsers[] = array(
			'ext' => $ext,
			'name' => $aEntry['cn'][0],
			'direct' => $direct,
			'mail' => $aEntry['mail'][0]
		);
	}

	usort($aUsers, "cmp");

	for($i=0;$i<sizeof($aUsers);$i++){
		// Choose background class 
		if ( $sClass === '' ) {
			$sClass= ' class="TableGreyBackground"';
		} else {
			$sClass = '';
		}
		?>
		<tr <?= $sClass ?>>
			<td class="TablePadding"><?= $aUsers[$i]['ext'] ?></td>
			<td><?= utf8_decode($aUsers[$i]['name']) ?></td>
			<td><?= $aUsers[$i]['direct'] ?></td>
			<td><a href="mailto:<?= $aUsers[$i]['mail'] ?>"><?= $aUsers[$i]['mail'] ?></a></td>
		</tr>
		<?php
	}
	?>
</table>
