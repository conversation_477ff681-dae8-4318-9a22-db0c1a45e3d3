<?php
require_once "/home/<USER>/secrets.php";

require_once INCLUDE_PATH . 'LicenseRestrictions.class.php';
require_once INCLUDE_PATH . 'CommonModules.class.php';
require_once INCLUDE_PATH . 'CommonModule.class.php';
CommonModules::setConn( $GLOBALS[ DB_HOST . DB_USER ] ); // set MySQL conn to use

// Any account creation errors
if ( $_GET['error'] == 'username_taken' ) {
	echo "<script>alert('ERROR: Username already used!\\n\\nPlease select a new username and try creating the account again.');</script>";
}

// Container
$sOutput = '';
$iLastCST = 0;

// Get all accounts
$aAccounts = DBGetRows('ca.accounts', "accounts.cst_id IN(" . $GLOBALS['aCustomer']['AllIDs'] . ")", 'accounts.cst_id, accounts.account_id DESC, accounts.account_expires');

// Loop through accounts
while ( list($iKey, $aAccount) = each($aAccounts) ) {
	// Build list of AccountIDs (if EVM)
	{
		// Reset container
		if ( $iLastCST != $aAccount['cst_id'] ) {
			$sAccountIDs = '';
		}
		$iLastCST = $aAccount['cst_id'];

		// Append AccountID
		$sAccountIDs .= $aAccount['account_id'] . ',';

		// Proceed if this is an EVM sub-account
		if ( $aAccount['account_esm'] ) {
			continue;
		}
	}

	// Modules
	{
		// Initialise
		$iModules = $aAccount['show_modules'];
		$iModMask = 0x1;
		$sModules = '';

		// Loop through modules
		while( $iModules ) {
			$sModuleName = fModuleName( $iModules & $iModMask );

			// Show status of ca.modules which have been flagged as displayed
			$visibilityBit = $iModules & $iModMask & 0x1FFFFFFFC000;
			if ( $visibilityBit ) {
				// Get the module id that matches `ca`.`modules` table. Module visibility is stored in the bitmask
				// so we get our module id by counting how many bits have been shifted.
				$moduleId = -13; // coincides with the reserved bits (15 thru 45)
				while ( $visibilityBit >>= 1 ) {
					$moduleId ++;
				}
				// check if the module is enabled for the account it's listing under.
				if ( $module = CommonModules::getById( $moduleId ) ) {
					if ( $module->isEnabledForAccount($aAccount) ) {
						$sModules .= $module->getName() . ' (' . $module->getVersionSupport() . ')<br>';
					} else {
						// not enabled so show it greyed out
						$sModules .= '<font color="grey">' . $module->getName() . ' (' . $module->getVersionSupport() . ') (inactive)</font><br>';
					}
				}
			}
			if( $sModuleName ) {
				if ( ($iModules & $iModMask) & $aAccount['modules'] ) {
					$sModules .= $sModuleName . '<br>';
				} else {
					$sModules .= '<font color="grey">' . $sModuleName . ' (inactive)</font><br>';
				}
			}
			$iModules = $iModules & ~$iModMask;
			$iModMask = $iModMask << 1;
		}
	}

	// Prepare AccountIDs
	$sAccountIDs = substr($sAccountIDs, 0, -1);

	$sOutput .= '
	<tr class="TableGreyBackground">
		<td colspan="2"><b>' . htmlspecialchars($aAccount['account_username']) . ' - <a href="index.php?page=ca_account_management&account_id=' . $aAccount['account_id'] . '&cst_id=' . $aAccount['cst_id'] . '&iframe=' . ( $_GET['iframe'] ? 1 : 0 ) . '"' . ( $_GET['iframe'] ? ' target="_blank"' : '' ) . '>Edit Account</a></b> - ' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'Expired' : $aAccount['account_expires'] ) . ' - <a href="javascript:void;" onClick="document.getElementById(\'' . urlencode($aAccount['account_username']) . '\').style.display = ( document.getElementById(\'' . urlencode($aAccount['account_username']) . '\').style.display != \'none\' ? \'none\' : \'block\' );">Toggle Details</a></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2">
			<div id="' . urlencode($aAccount['account_username']) . '"' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'style="display: none;"' : '' ) . '>
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td width="20%" valign="top" class="TablePadding">CA Modules</td>
					<td width="80%" valign="top">' . $sModules . '</td>
				</tr>
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Last Login</td>
					<td valign="top">' . ( $aAccount['last_login'] ? ( $aAccount['special_limits'] == 'csi_50' ? date('Y-m-d G:i:s', strtotime($aAccount['last_login'] . ' UTC')) : $aAccount['last_login'] ) : 'Never logged in' ) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Expires</td>
					<td valign="top">' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'Expired' : $aAccount['account_expires'] ) . '</td>
				</tr>';

				// VI?
				if ( $aAccount['modules'] & ( MOD_VTS | MOD_SM | MOD_ESM ) )
				{
					// Number of devices
					$iDevices = DBGetRowValue('ca.devices', 'count(*)', "devices.account_id IN(" . $sAccountIDs . ")");

					// Number of apps.
					$iDeviceSoftware = DBGetRowValue('ca.devices, ca.device_software', 'count(*)', "devices.account_id IN(" . $sAccountIDs . ") AND devices.device_id = device_software.device_id");

					// Number of advisories received (email)
					$iEmails = DBGetRowValue('ca.usage_alerts', 'count(*)', "usage_alerts.account_id IN(" . $sAccountIDs . ") AND usage_alerts.type = 1");

					// Number of advisories received (sms)
					$iSMS = DBGetRowValue('ca.usage_alerts', 'count(*)', "usage_alerts.account_id IN(" . $sAccountIDs . ") AND usage_alerts.type = 2");

					$sOutput .= '
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td class="TablePadding"><b>VI - Details</b></td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Network Devices</td>
					<td valign="top">' . number_format($iDevices) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Applications</td>
					<td valign="top">' . number_format($iDeviceSoftware) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Emails Received</td>
					<td valign="top">' . number_format($iEmails) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">SMS Received</td>
					<td valign="top">' . number_format($iSMS) . '</td>
				</tr>';
				}

				// VIF / VIM
				if ( $aAccount['modules'] & MOD_VTSE || $aAccount['special_limits'] == 'vim_beta' || in_array( $aAccount['special_limits'], array('vim_30', 'vim_40') ) ) {
					// VIF
					$iAssets = DBGetRowValue('ca.vtse_assets', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ")");
					$iAssetsAll = DBGetRowValue('ca.vtse_assets', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && xml_receive_all = 1");
					$iProducts = DBGetRowValue('ca.vtse_assets, ca.vtse_asset_products', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && vtse_asset_products.asset_id = vtse_assets.asset_id");
					$iVendors = DBGetRowValue('ca.vtse_assets, ca.vtse_asset_vendors', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && vtse_asset_vendors.asset_id = vtse_assets.asset_id");
					// VIM
					$iAssets += DBGetRowValue('ca.vi_assets', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ")");
					$iAssetsAll += DBGetRowValue('ca.vi_assets', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && xml_receive_all = 1");
					$iProducts += DBGetRowValue('ca.vi_assets, ca.vi_asset_products', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && vi_asset_products.asset_id = vi_assets.asset_id");
					$iVendors += DBGetRowValue('ca.vi_assets, ca.vi_asset_vendors', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && vi_asset_vendors.asset_id = vi_assets.asset_id");

					$sRecipients = "";
					if ( in_array($aAccount['account_version'], array('vim_30', 'vim_40') ) ) {
						// For the new vim, list configured recipients:
						// Build the VIM stats.
						$children = getChildren( (int)$aAccount['account_id'] );
						$sqlIn = implode( ",", $children );
						$sqlIn .= ( $sqlIn == "" ? "" : ", " ).(int)$aAccount['account_id'];
						$recipients = DBGetRows( "ca.contact_method", "account_id IN ( ".$sqlIn." ) AND contact_method_type = 1" );
						if ( count( $recipients ) != 0 ) {
							$sRecipients = "<table><tr><td><b>Recipient Name</b></td><td><b>Email Address</b><td></tr></tr>";
							for ( $i = 0; $i < count( $recipients ); $i++ ) {
								$sRecipients .= "<tr>";
								$sRecipients .= "<td>".htmlspecialchars($recipients[$i]['name'])."</td>";
								$sRecipients .= "<td>".htmlspecialchars($recipients[$i]['contact_method_value'])."</td>";
								$sRecipients .= "</tr>";
							}
							$sRecipients .= "</table>";
						}
					}
					$vimVersionNumber = '4.0';
					if ( 'vim_30' === $aAccount['special_limits'] ) {
						$vimVersionNumber = '3.0';
					}
					$sOutput .= '
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td class="TablePadding"><b>VIF/VIM - Details</b></td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Asset Lists</td>
					<td valign="top">' . number_format($iAssets) . ' (' . number_format($iAssetsAll) . ' receive all)</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Products</td>
					<td valign="top">' . number_format($iProducts) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Vendors</td>
					<td valign="top">' . number_format($iVendors) . '</td>
				</tr>';
					if ( $aAccount['account_version'] != "" ) {
						$sOutput .= '<tr>
						<td valign="top" class="TablePadding">Recipients (VIM ' . $vimVersionNumber . ')</td>
						<td valign="top">' . $sRecipients . '</td>
					</tr>';
					}
				}

				// CSI?
				if ( $aAccount['modules'] & MOD_NSI ) {
					// Base settings
                                        $aBaseSettings = DBGetRow('ca.nsi_base_settings', "nsi_base_settings.account_id = '" . $aAccount['account_id'] . "'");

					// Open connection to MariaDB
					if ( $aAccount['special_limits'] == 'csi_60' ) {
	                                        $dbhost = DBGetRowValue('ca.csi_pdb_info', 'db_client_host', "cst_id = '" . $aAccount['cst_id'] . "'");
                                                if ($dbhost == ""){
                                                        $dbhost = "csi6db-hsk-001.intnet";
                                                }
                                                mysql_close();
                                                mysql_connect($dbhost, 'crm', DB_PASS);
                                                echo mysql_error();
                                                $db = 'ca_' . $aBaseSettings['cst_id'] . '.';

					} else if ( $aAccount['special_limits'] == 'csi_50' ) {
						$dbhost = DBGetRowValue('ca.csi_db_client_hosts', 'db_client_host', "cst_id = '" . $aAccount['cst_id'] . "'");
						if ($dbhost == ""){
							$dbhost = "csi5db-hsk-002.intnet";
						}
						mysql_close();
						mysql_connect($dbhost, 'crm', DB_PASS);
						echo mysql_error();
						$db = 'ca_' . $aBaseSettings['cst_id'] . '.';
					} else {
						$db = 'ca.';
						// Make sure reads are performed on DBBCK to ensure performance
						mysql_close();
						mysql_connect("db-ix-1.intnet", "crm", DB_PASS);
						echo mysql_error();
					}

					// Hosts imported
					$iHosts = DBGetRowValue($db . 'nsi_devices', 'count(*)', "nsi_devices.account_id IN(" . $sAccountIDs . ")");

					// Hosts imported using agent
					$iHostsAgent = DBGetRowValue($db . 'nsi_device_agent_conf', 'count(*)', "nsi_device_agent_conf.account_id IN(" . $sAccountIDs . ")");

					// Total Appllications
					$iApplications = DBGetRowValue($db . 'nsi_devices', 'sum(no_total)', "account_id IN(" . $sAccountIDs . ")");

					// Total Appllications Secure
					$iApplicationsSecure = DBGetRowValue($db . 'nsi_devices', 'sum(no_patched)', "account_id IN(" . $sAccountIDs . ")");

					// Total Appllications Insecure
					$iApplicationsInsecure = DBGetRowValue($db . 'nsi_devices', 'sum(no_insecure)', "account_id IN(" . $sAccountIDs . ")");

					// Open connection to master db
					mysql_close();
					fOpenDatabase();


					$sOutput .= '
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td class="TablePadding"><b>CSI - Details</b></td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Trial Account</td>
					<td valign="top">' . ( $aBaseSettings['s_trial'] ? 'Yes' : 'No' ) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Hosts Imported</td>
					<td valign="top">' . number_format($iHosts) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Hosts w/ Agent</td>
					<td valign="top">' . number_format($iHostsAgent) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Applications</td>
					<td valign="top">' . number_format($iApplications) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Insecure</td>
					<td valign="top">' . number_format($iApplicationsInsecure) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Secure</td>
					<td valign="top">' . number_format($iApplicationsSecure) . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">End-of-Life</td>
					<td valign="top">' . number_format($iApplications - $iApplicationsInsecure - $iApplicationsSecure) . '</td>
				</tr>';
				}



				if ( $aAccount['modules'] & MOD_NSI || $aAccount['modules'] & MOD_BA || $aAccount['modules'] & MOD_VTSE ) {
					$sLicenseKeyOptions = '';
//					if ( $aAccount['modules'] & MOD_NSI ) {
						$sLicenseKeyOptions .= '<option value="32">CSI</option>';
//					}
//					if ( $aAccount['modules'] & MOD_VTSE ) {
						$sLicenseKeyOptions .= '<option value="8">VIF</option>';
//					}
					if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable'][MOD_BA]) ) {
						$sLicenseKeyOptions .= '<option value="64">BA</option>';
					}

	  				$sOutput .= '
				<tr>
                                        <td><br></td>
                                </tr>
                                <tr>
                                        <td class="TablePadding" colspan="2">
						<b>License Keys - <a href="javascript:void(0);" onClick="fToggleLicenseKey( document.getElementById(\'ca_new_license_key\'), this, \'' . $aAccount['account_id'] . '\');">Add License Key</a></b>
					</td>
				</tr>';

					// License Keys
					$aLicenseKeys = DBGetRows('ca.license_keys', "account_id = '" . $aAccount['account_id'] . "'");
					while ( list($iKey, $aLicense) = each($aLicenseKeys) ) {
						if ( $aLicense['type'] == 64 ) {
							$sType = 'BA';
							$sTypeValue = 'Credits';
							$iUsed = DBGetRowValue('ca.license_exploits', 'sum(credits)', "license_id = '" . $aLicense['id'] . "'");
						} elseif ( $aLicense['type'] & MOD_VTSE ) {
							$sType = 'VIF';
							$sTypeValue = 'Asset Lists';
							$iUsed = DBGetRowValue('ca.license_vtse_assets', 'count(*)', "license_id = '" . $aLicense['id'] . "'");
						} else {
							$sType = 'CSI';
							$sTypeValue = 'Host Licenses';
							$iUsed = DBGetRowValue('ca.license_hosts', 'count(*)', "license_id = '" . $aLicense['id'] . "'");
						}

						$sOutput .= '<tr>
						<td valign="top" class="TablePadding" colspan="2">&nbsp;' . $aLicense['license'] . ', ' . $sType . ', ' . $aLicense['quantity'] . ' ' . $sTypeValue . ', ' . $iUsed . ' used, expires ' . substr($aLicense['valid_to'], 0, 10) . 'a</td>
                                </tr>';
					}
				}

				// SS?
				if ( $aAccount['modules'] & MOD_VSS )
				{
					// Containers
					$sAvailableSlots = '';
					$sSlotsUsed = '';

					// SS limits
					$aScanLimits = DBGetRows('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $aAccount['account_id'] . "'", 'vss_scan_limits.type');

					// Trial or Live?
					if ( !$aScanLimits )
					{
						// Trial
						$sAvailableSlots = 'Trial account (1 scan, no scheduling)';

						// Check if the scan has been executed
						$aScan = DBGetRow('ca.vss_nessus_reports_raw', "vss_nessus_reports_raw.account_id = '" . $aAccount['account_id'] . "'");

						if ( $sScan['scan_ended'] )
						{
							$sSlotsUsed = 'Trial scan finished at ' . $sScan['scan_ended'];
						}
						else
						{
							$sSlotsUsed = 'Trial scan not used/finished.';
						}
					}
					else
					{
						// Live
						while ( list($iKey, $aScanLimit) = each($aScanLimits) )
						{
							$sAvailableSlots .= $aScanLimit['number'] . ' / ' . fSSFrequencyName($aScanLimit['type']) . '<br>';
						}

						// Select scan profiles
						$aScanProfiles = DBGetRows('ca.vss_scan_profiles', "vss_scan_profiles.account_id = '" . $aAccount['account_id'] . "'");
						while ( list($iKey, $aScanProfile) = each($aScanProfiles) )
						{
							$aSSResult[$aScanProfile['frequency']] += count( fGenerateSSTargetsArray($aScanProfile['targets']) );
						}

						// Generate output
						$sSlotsUsed = number_format($aSSResult[1]) . ' / Daily<br>
						' . number_format($aSSResult[2]) . ' / Weekly<br>
						' . number_format($aSSResult[3]) . ' / Monthly<br>';
					}

					$sOutput .= '
				<tr>
					<td><br></td>
				</tr>
				<tr>
					<td class="TablePadding"><b>SS - Details</b></td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Available slots</td>
					<td valign="top">' . $sAvailableSlots . '</td>
				</tr>
				<tr>
					<td valign="top" class="TablePadding">Slots used</td>
					<td valign="top">' . $sSlotsUsed . '</td>
				</tr>';
				}

				$sOutput .= '<tr>
					<td><br><br></td>
				</tr>
			</table>
			</div>
		</td>
	</tr>';
}

# Get license restrictions for the current Sales Person so we can show them on the Add License Key form
$licenseRestrictions = LicenseRestrictions::getForAccount( $GLOBALS['aRepData'] );
if ( false === $licenseRestrictions ) { // If no restrictions found then show 0, 0
	$licenseRestrictions = array(
		'maxLicenseDays' => 0
		,'maxLicenseHosts' => 0
	);
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	echo $sOutput;
	?>
</table>

<div align="left" id="ca_new_license_key" style="display: none; position: absolute; padding: 2px; width: 350px; background: #FFFFFF; border: 1px solid #000000;">
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			Type
		</td>
		<td>
			<select name="lk_type" id="lk_type">
				<?= $sLicenseKeyOptions ?>
			</select>
		</td>
	</tr>
	<tr>
		<td>
			Starts
		</td>
		<td>
			<input type="text" name="lk_starts" value="<?= date('Y-m-d') ?>">
		</td>
	</tr>
	<tr>
		<td>
			Expires
			<br><span style="font-size: 0.8em; color: gray;">Max: <?php echo $licenseRestrictions['maxLicenseDays']; ?> days</span>
		</td>
		<td>
			<input type="text" name="lk_expires">
		</td>
	</tr>
	<tr>
		<td>
			Quantity
			<br><span style="font-size: 0.8em; color: gray;">Max: <?php echo $licenseRestrictions['maxLicenseHosts']; ?> hosts</span>
		</td>
		<td>
			<input type="text" name="lk_host_licenses">
		</td>
	</tr>
	<tr>
		<td>
			&nbsp;
		</td>
		<td>
			<br>
			<input type="submit" value="Save - Create and Insert License Key">
		</td>
	</tr>
</table>
</div>

<input type="hidden" name="lk_account_id" id="lk_account_id" value="">

<br>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%"><br></td>
		<td width="80%">
			<a href="javascript:void(0);" onClick="fToggleNewAccount( document.getElementById('ca_new_account'), this );">New account</a><br>
			<div align="left" id="ca_new_account" style="display: none; position: absolute; padding: 2px; width: 350px; background: #FFFFFF; border: 1px solid #000000;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td>
						Username
					</td>
					<td>
						<input type="text" id="ca_username" name="ca_username">
					</td>
				</tr>
				<tr>
					<td>
						Email
					</td>
					<td>
						<input type="text" id="ca_email" name="ca_email">
					</td>
				</tr>
				<tr>
					<td>
						Product
					</td>
					<td>
						<select id="ca_product_type" name="ca_product_type">
							<option> - Select Product - </option>
<!--							<option value="1"> VTS </option>
							<option value="2"> VM </option>
							<option value="3"> EVM </option>
							<option value="4"> VIF </option>
							<option value="5"> SS </option>
							<option value="208"> VIM 3.0 Small Bussiness Edition</option>
							<option value="209"> VIM </option>-->
							<option value="210"> VIM 4.0</option>
							<?php
							if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable'][MOD_NSI]) ) {
								?>
								<option value="17" selected> CSI </option>
								<?php
							}
/*                                                        if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable'][MOD_BA]) ) {
                                                                ?>
                                                                <option value="8" selected> BA </option>
                                                                <?php
                                                        }
                                                        if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable']['Partner']) ) {
                                                                ?>
                                                                <option value="9"> Partner </option>
                                                                <?php
                                                        }*/
                                                        ?>
						</select>
					</td>
				</tr>
				<tr>
					<td>
						Trial
					</td>
					<td>
						<input type="checkbox" value="1" name="ca_trial"> Yes
					</td>
				</tr>
				<tr>
					<td>
						Expires
					</td>
					<td>
						<input type="text" name="ca_expires" value="<?= date('Y-m-d', time() + (86400*7)) ?>">
					</td>
				</tr>
				<tr>
					<td>
						&nbsp;
					</td>
					<td>
						<br>
						<input type="submit" value="Save - Create Account - Edit Details &gt;&gt;" id="btnSubmitAccount">
					</td>
				</tr>
			</table>
			</div>
		</td>
	</tr>
</table>
