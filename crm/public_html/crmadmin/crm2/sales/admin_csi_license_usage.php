<?php
// Running through browser or as logging cronjob
$isCron = false;
if ( $argv[0] ) {
	$isCron = true;

	// Load configuration
	include '/home/<USER>/public_html/crmadmin/crm2/sales/configuration.php';

	// Load sales functions
	include INCLUDE_PATH . 'sales_functions.php';

	// Load global functions
	include INCLUDE_PATH . 'global_functions.php';

	// Open DB Connection
	fOpenDatabase();
} elseif ( $aRepData['person_id'] == 95 || $aRepData['person_id'] == 169 )  {
	// Special exception
	// Rickard
	// Oana
} else {
	// Access Level: 1
	// Admin
	// SM
	// Lead Management
	fVerifyAccess( $aRepData['person_id'], 1 );
}

// Save comments
if ( $_POST['comment'] ) {
	while ( list($accountID, $comment) = each($_POST['comment']) ) {
		DBQuery("REPLACE INTO crm.nsi_usage_account_notes SET comment = '" . $comment . "', account_id = '" . (int) $accountID . "', updated = NOW()");
	}
}

// Input requests
$compare = NULL;
if ( $_GET['compare'] ) {
	$data = DBGetRows('ca.nsi_customer_usage_data', "logged LIKE '" . $_GET['compare'] . "%'");
	while ( list($key, $values) = each($data) ) {
		$compare[$values['account_id']] = $values;
	}
}

$output = '';
$GLOBALS['redLogin'] = 0;
$GLOBALS['yellowLogin'] = 0;
$GLOBALS['greenLogin'] = 0;
$GLOBALS['redLicense'] = 0;
$GLOBALS['yellowLicense'] = 0;
$GLOBALS['greenLisence'] = 0;
$accounts = DBGetRows('ca.accounts', "modules & 32 && " . ( $_GET['expires_to'] && $_GET['expires_from'] ? "( account_expires >= '" . $_GET['expires_from'] . "' && account_expires <= '" . $_GET['expires_to'] . "' )" : "account_expires >= '" . date('Y-m-d') . "'" ) . " && account_esm IS NULL");
while ( list($key, $values) = each($accounts) ) {
	// CRM Details
	$customer = fGetCustomerDetails( $values['cst_id'] );

/*	// Account / customer status
	$accountStatus = fCalculateAccountStatus( $values['cst_id'], $customer, true );

	// Skip anyone that is not 'a customer'
	if ( $accountStatus != 'Customer' && !$_GET['only_inbound'] ) {
		continue;
	} else if ( $_GET['only_inbound'] && $accountStatus != 'Prospect' ) {
		continue;
	}*/

	// Longest duration between start and end of license
	$maxDuration = DBGetRowValue('ca.license_keys', 'max(datediff(valid_to, valid_from))', "account_id = '" . $values['account_id'] . "'");
	if ( $maxDuration < 60 ) {
		continue;
	}

	// License Details
	$licensesAvailable = DBGetRowValue('ca.license_keys', 'sum(quantity)', "account_id = '" . $values['account_id'] . "' && valid_from < now() && valid_to > now()");
	$licensesUsed = DBGetRowValue('ca.license_hosts, ca.license_keys', 'count(*)', "license_keys.account_id = '" . $values['account_id'] . "' && valid_from < now() && valid_to > now() && license_keys.id = license_hosts.license_id");
	$allLicensesAvailable += $licensesAvailable;
	$allLicensesUsed += $licensesUsed;
	$licensePercentage = round(($licensesUsed / $licensesAvailable) * 100);
	if ( $licensePercentage > 75 ) {
		$licensePercentageColor = 'darkgreen';
		$GLOBALS['redLicense']++;
	} else if ( $licensePercentage > 25 ) {
		$licensePercentageColor = '#EBAB00';
		$GLOBALS['yellowLicense']++;
	} else {
		$licensePercentageColor = 'red';
		$GLOBALS['greenLicense']++;
	}

	$totalAccounts++;
	$csi4Accounts += ( strstr($values['special_limits'], 'csi_40') ? 1 : 0 );

	$csiVersion = '3.0';
	if ( strstr($values['special_limits'], 'csi_40') ) {
		$csiVersion = '4.0';
	} else if ( strstr($values['special_limits'], 'csi_50') ) {
		$csiVersion = '5.0';
	} else if ( strstr($values['special_limits'], 'csi_60') ) {
		$csiVersion = '6.0';
	}

	// Patching?
	$wsus = DBGetRowValue('ca.nsi_usage_log', 'sum(value)', "account_id = '" . $values['account_id'] . "' && type = 'WSUS-SRV-HOSTS'");
	$wsusPatches = DBGetRowValue('ca.nsi_usage_log', 'count(*)', "account_id = '" . $values['account_id'] . "' && type = 'WSUS-SRV-PACKAGE'");

	// Logging data?
	if ( $isCron ) {
		DBQuery("INSERT INTO ca.nsi_customer_usage_data SET logged = NOW(), account_id = '" . $values['account_id'] ."', cst_id = '" . $values['cst_id'] . "', csi_version = '" . mysql_real_escape_string( $csiVersion ) . "', licenses_available = '" . $licensesAvailable . "', licenses_used = '" . $licensesUsed . "', last_login = '" . $values['last_login'] . "', wsus_hosts = '" . (int) $wsus . "', wsus_packages = '" . (int) $wsusPatches . "'");
		echo ++$xx . "INSERT INTO ca.nsi_customer_usage_data SET logged = NOW(), account_id = '" . $values['account_id'] ."', cst_id = '" . $values['cst_id'] . "', csi_version = '" . mysql_real_escape_string($csiVersion) . "', licenses_available = '" . $licensesAvailable . "', licenses_used = '" . $licensesUsed . "', last_login = '" . $values['last_login'] . "', wsus_hosts = '" . (int) $wsus . "', wsus_packages = '" . (int) $wsusPatches . "'";
		continue;
	}

	// Sort key
	switch ( $_GET['sort'] ) {
		case 'lastLogin':
			$sortKey = $values['last_login'];
			$sortFlag = SORT_REGULAR;
			break;
		case 'licensePercentage':
			$sortKey = $licensePercentage;
			$sortFlag = SORT_NUMERIC;
			break;
		case 'licensesAvailable':
			$sortKey = $licensesAvailable;
			$sortFlag = SORT_NUMERIC;
			break;
		case 'licensesUsed':
			$sortKey = $licensesUsed;
			$sortFlag = SORT_NUMERIC;
			break;
		case 'wsusHosts':
			$sortKey = $wsus;
			$sortFlag = SORT_NUMERIC;
			break;
		case 'wsusPatches':
			$sortKey = $wsusPatches;
			$sortFlag = SORT_NUMERIC;
			break;
		default:
			$sortKey = $values['account_expires'];
			$sortFlag = SORT_REGULAR;
	}
	$sortKey .= '___' . $values['account_id'];

	$output[$sortKey] = '
<tr>
	<td>' . $values['account_id'] . '</td>
	<td><a href="?page=ca_account_management&account_id=' . $values['account_id'] . '">' . htmlspecialchars($values['account_username']) . '</a></td>
	<td>' . $values['account_expires'] . '</td>
	<td>' . $csiVersion . '</td>
	<td width="50" align="right" style="padding-right: 5px;">' . fCompare( $licensesUsed, $compare[$values['account_id']]['licenses_used'] ) . $licensesUsed . '</td>
	<td align="right" width="50" style="padding-right: 5px;">' . fCompare( $licensesAvailable, $compare[$values['account_id']]['licenses_available'] ) . $licensesAvailable . '</td>
	<td width="50" align="right" style="padding-right: 5px; color: ' . $licensePercentageColor . ';">' . fCompare( $licensePercentage, round($compare[$values['account_id']]['licenses_used'] / $compare[$values['account_id']]['licenses_available'] * 100) ) . $licensePercentage . '%</td>
	<td>' . formatLicenseUsage( $licensesAvailable, $licensesUsed ) . '</td>
	<td align="right">' . fCompare( round((time() - strtotime($values['last_login'])) / 86400 ), round(( strtotime( $_GET['compare'] . ' ' . date('G:i:s') ) - strtotime($compare[$values['account_id']]['last_login'])) / 86400 ), true ) . formatLastLogin( $values['last_login'] ) . '</td>
	<td align="right" style="padding-right: 5px;">' . fCompare( $wsus, $compare[$values['account_id']]['wsus_hosts'] ) . ( $wsus > 0 ? number_format($wsus) : '-' ) . '</td>
	<td align="right" style="padding-right: 5px;">' . fCompare( $wsusPatches, $compare[$values['account_id']]['wsus_packages'] ) . ( $wsusPatches > 0 ? number_format($wsusPatches) : '-' ) . '</td>
	<td><a href="?page=customer&cst_id=' . $values['cst_id'] . '">' . $customer['Company']['name'] . '</a></td>
	<td><input type="text" name="comment[' . $values['account_id'] . ']" value="' . htmlspecialchars( DBGetRowValue('crm.nsi_usage_account_notes', 'comment', "account_id = '" . $values['account_id'] . "'") ) . '"></td>
	<td><a href="mailto:<EMAIL>?subject=' . htmlspecialchars('LL: ' . $customer['Company']['name'] ) . '&body=' . rawurlencode('https://ca.secunia.com/crmadmin/crm2/sales/?page=customer&cst_id=' . $values['cst_id']) . '">Create SF Case</a></td>
</tr>';
}

ksort($output, $sortFlag);

if ( $isCron ) {
	exit();
}

function fCompare( $current, $old, $reverse = false ) {
	if ( !$_GET['compare'] ) {
		return;
	}

	$diff = ($current - $old);

	if ( ($diff > 0 && !$reverse) || ($diff < 0 && $reverse) ) {
		return '(<font color="DARKGREEN">' . ($diff) . '</font>)&nbsp;';
	}
	if ( ($diff < 0 && !$reverse) || ($diff > 0 && $reverse) ) {
		return '(<font color="RED">' . $diff . '</font>)&nbsp;';
	}
}

function formatLastLogin( $lastLogin ) {
	if ( !$lastLogin ) {
		$ret = 'Never logged in';
		$color = 'red';
		$GLOBALS['redLogin']++;
	} else {
		$days = round((time() - strtotime($lastLogin)) / 86400 );
		$ret = $days . ' days ago';

		if ( $days > 30 ) {
			$color = 'red';
			$GLOBALS['redLogin']++;
		} else if ( $days > 14 ) {
			$color = '#EBAB00';
			$GLOBALS['yellowLogin']++;
		} else {
			$color = '#000000';
			$GLOBALS['greenLogin']++;
		}
	}
	return '<font style="color: ' . $color . '; padding-right: 3px;">' . $ret . "</font>";
}

function formatLicenseUsage( $total, $used ) {
	if ( !$total ) {
		return '-';
	}

	// 1% = 2.25 pixels
	$unit = 2.25;
	$percentage = round(($used / $total) * 100);

	// Determine base color
	if ( $percentage > 75 ) {
		$color = '9aFFB9';
	} else if ( $percentage > 25 ) {
		$color = '#FFFF9A';
	} else {
		$color = '#FF9A9A';
	}

	// "progress bar"
	$ret = '<div style="border: 1px solid #000000; width: 225px; background: #FFFFFF; height: 10px;"><div style="background: lightgreen; height: 10px; width: ' . ($unit * $percentage) . 'px "></div></div>';

	return $ret;
}

$options = DBQueryGetRows("SELECT DISTINCT LEFT(logged, 10) AS date FROM ca.nsi_customer_usage_data ORDER BY date");
$compareOptions = '';
while ( list($key, $values) = each($options) ) {
	$compareOptions .= '<option value="' . $values['date'] . '"' . ( $values['date'] == $_GET['compare'] ? ' selected' : '' ) . '>' . $values['date'] . '</option>';
}
?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Secunia CSI Account Overview and License Usage';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
		<tr>
			<td style="padding-left: 5px;">
				<form method="GET" action="?">
				<input type="hidden" name="page" value="admin_csi_license_usage">
				<table width="100%" Cellspacing="0" cellpadding="0">
					<tr>
						<td width="150">
							<b>Account Expires - From:</b><br>
							<input type="text" name="expires_from" value="<?= htmlspecialchars($_GET['expires_from']) ?>" style="width: 95%">
						</td>
						<td width="150">
							<b>Account Expires - To:</b><br>
							<input type="text" name="expires_to" value="<?= htmlspecialchars($_GET['expires_to']) ?>" style="width: 95%">
						</td>
						<td width="150">
							<b>Compare Against:</b><br>
							<select name="compare">
								<option>- Select Logged Data -</option>
								<?= $compareOptions ?>
							</select>
						</td>
						<td width="150">
							<b>Sorted By:</b><br>
							<select name="sort">
								<option>Account Expires</option>
								<option value="lastLogin"<?= ( $_GET['sort'] == 'lastLogin' ? ' selected' : '' ) ?>>Last Login</option>
								<option value="licensePercentage"<?= ( $_GET['sort'] == 'licensePercentage' ? ' selected' : '' ) ?>>License Usage Percent</option>
								<option value="licensesAvailable"<?= ( $_GET['sort'] == 'licensesAvailable' ? ' selected' : '' ) ?>>Licenses Available</option>
								<option value="licensesUsed"<?= ( $_GET['sort'] == 'licensesUsed' ? ' selected' : '' ) ?>>Licenses Used</option>
								<option value="wsusHosts"<?= ( $_GET['sort'] == 'wsusHosts' ? ' selected' : '' ) ?>>WSUS Hosts</option>
								<option value="wsusPatches"<?= ( $_GET['sort'] == 'wsusPatches' ? ' selected' : '' ) ?>>WSUS Patches Created</option>
							</select>
						</td>
						<td>
							<input type="submit" value="Update">
						</td>
					</tr>
				</table>
				</form>
			</td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td class="TablePadding">
				<form method="POST" action="?<?= htmlspecialchars($_SERVER['QUERY_STRING']) ?>">
				<table width="100%" cellspacing="0" cellpadding="3">
					<tr>
						<td width="75"><b>Account ID</b></td>
						<td width="200"><b>Username</b></td>
						<td width="75"><b>Expires</b></td>
						<td width="75"><b>CSI Version</b></td>
						<td width="400" colspan="4"><b>License: Used, Available, Percentage</b></td>
						<td width="150"><b>Last Login</b></td>
						<td width="100"><b>WSUS/SCCM</b></td>
						<td width="100"><b>Patches Dep.</b></td>
						<td width="*"><b>Customer Name</b></td>
					</tr>
<?= implode($output) ?>
					<tr><td colspan="14" style="border-top: 2px solid #000000;">&nbsp;</td></tr>
                                        <tr>
                                                <td valign="top" colspan="2"><b>Total / Averages:</b></td>
                                                <td></td>
                                                <td valign="top"><?= number_format($csi4Accounts) ?> of. <?= number_format($totalAccounts) ?></td>
                                                <td valign="top" align="right"><?= number_format( $allLicensesUsed ) ?></td>
						<td valign="top" align="right"><?= number_format( $allLicensesAvailable ) ?></td>
						<td valign="top" align="right"><?= round($allLicensesUsed / $allLicensesAvailable * 100, 2) ?>%<br><?= '<font color="red">' . $GLOBALS['redLicense'] . '</font> / <font color="#EBAB00">' . $GLOBALS['yellowLicense'] . '</font> / ' . $GLOBALS['greenLicense'] ?></td>
						<td valign="top"></td>
                                                <td valign="top" align="right"><?= '<font color="red">' . $GLOBALS['redLogin'] . '</font> / <font color="#EBAB00">' . $GLOBALS['yellowLogin'] . '</font> / ' . $GLOBALS['greenLogin'] ?></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
						<td><input type="submit" value="Save"></td>
                                        </tr>
				</table>
				</form>
			</td>
		</tr>
</table>
<br><br>
