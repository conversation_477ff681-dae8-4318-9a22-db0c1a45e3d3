<?php
// What to display: Performance or overview of appointments
if ( !$_GET['display'] )
{
/*	// Result Array
	$aResult = array();

	// Select budgets
	$sQuery = "
SELECT
salespeople.person_id,
salespeople.name,
forecast.month,
forecast.revenue
FROM
crm.salespeople,
crm.forecast
WHERE
salespeople.display = 1 AND
salespeople.forecast_start_month AND
salespeople.person_id NOT IN(1,60,73) AND
salespeople.person_id = forecast.person_id AND
forecast.month = ((year(now())-year(STR_TO_DATE(forecast_start_month,'%Y-%m')))+(month(now())-month(STR_TO_DATE(forecast_start_month,'%Y-%m')))+1)";
	$aTargets = DBQueryGetRows($sQuery);

	// Loop through each rep
	while ( list($iKey, $aData) = each($aTargets) )
	{
		// Get sales revenue to date (cur month)
		$sQuery = "
SELECT
SUM( ROUND( (saleslog.product_price * country_currency.currency) / " . CURRENCY_EURO . " ) ) as revenue
FROM
crm.saleslog,
crm.country_currency
WHERE
saleslog.person_id = '" . $aData['person_id'] . "' AND
saleslog.sold_date >= STR_TO_DATE(now(), '%Y-%m') AND
(saleslog.status IS NULL OR saleslog.status != 3) AND
saleslog.product_price > 0  AND
saleslog.currency = country_currency.currency_name
GROUP BY
saleslog.person_id";
		$aRevenue = DBQueryGetRows($sQuery);

		// Append to result
		$aResult[$aData['person_id']] = round(($aRevenue[0]['revenue']/$aData['revenue'])*100);
	}

	arsort($aResult);
	?>

	<table width="100%" cellspacing="0" cellpadding="0">
		<?php
		$sTitle = 'Top Performers (Target / Revenue)';
		$iColSpan = 3;
		include INCLUDE_PATH . 'page_header.php';
		?>
		<tr>
			<td width="10%" class="TableSubHeadline TableGreyBackground TablePadding">&nbsp;</td>
			<td width="50%" class="TableSubHeadline TableGreyBackground">Person</td>
			<td width="40%" class="TableSubHeadline TableGreyBackground">Percentage</td>
		</tr>
		<?php
		$iCount = 0;
		while ( list($iPersonID, $iPercentage) = each($aResult) )
		{
			if ( $sClass === '' )
			{
				$sClass = ' class="TableGreyBackground TablePadding"';
			}
			else
			{
				$sClass = '';
			}
		?>
	
		<tr <?= $sClass ?>>
			<td class="TablePadding"><?= ++$iCount ?>.</td>
			<td><?= fReturnRepDetailFromID( $iPersonID ) ?></td>
			<td><?= number_format($iPercentage) ?>%</td>
		</tr>

		<?php
		}
		?>
	</table>
<?php
*/
}
else
{
	// Select Appointments
	$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 cst.contact,
 cst.phone,
 cst.cst_id
FROM
 crm.cst
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment > '" . date('Y-m-d') . " 00:00:00' AND
 cst.appointment <= '" . date('Y-m-d') . " 23:59:59'
ORDER BY
 cst.appointment";
	$aAppointments = DBQueryGetRows($sQuery);

	// Select Expired Appointments
$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 cst.contact,
 cst.phone,
 cst.cst_id
FROM
 crm.cst
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment < '" . date('Y-m-d') . "' AND cst.appointment > '0000-00-00'
ORDER BY
 cst.appointment";
$aExpiredAppointments = DBQueryGetRows($sQuery);

	// Select Canvas Leads
	$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 cst.contact,
 cst.phone,
 cst.cst_id
FROM
 crm.cst
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment = '" . date('Y-m-d') . " 00:00:00'
ORDER BY
 cst.forecast_expectancy";
	$aCallbacks = DBQueryGetRows($sQuery);
?>

	<table width="100%" cellspacing="0" cellpadding="0">
		<?php
		$sTitle = 'Appointments';
		$iColSpan = 3;
		include INCLUDE_PATH . 'page_header.php';
		?>
		<tr class="TableGreyBackground">
			<td width="20%" class="TableSubHeadline TablePadding">Time</td>
			<td width="80%" class="TableSubHeadline">Company</td>
		</tr>
		<?php
		$bBorderDisplayed = false;
		while ( list($iKey, $aData) = each($aAppointments) )
		{
			if ( $sClass === '' )
			{
				$sClass = ' class="TableGreyBackground"';
			}
			else
			{
				$sClass = '';
			}

			// Add border if just after "now"
			$sStyle = '';
			if ( strtotime($aData['appointment']) > time() && $bBorderDisplayed == false )
			{
				$bBorderDisplayed = true;
				$sStyle = ' style="border-bottom: 1px solid #CECECE;"';
			}
		?>
		<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aData['cst_id']) ?>>
			<td<?= $sStyle ?> class="TablePadding"><?= substr($aData['appointment'], 11, 5) ?></td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['name']) ?> </td>
		</tr>
		<?php
		}
		?>
	</table>

	<br>
	<a href="?page=today&amp;right=performance" target="main" class="TablePadding">Return to overview</a>
	<br><br><br>

	<table width="100%" cellspacing="0" cellpadding="0">
		<?php
		// Only display "Expired block" if there is actually some expired appointments
		if ( count($aExpiredAppointments) )
		{
			$sTitle = '<blink><font color="red">' . count($aExpiredAppointments) . ' Expired Appointments</font></blink>';
			$iColSpan = 5;
			$bPrint = true;
			$bWindowTitle = false;
			include INCLUDE_PATH . 'page_header.php';
			?>
			<tr class="TableGreyBackground">
				<td class="TableSubHeadline TablePadding">Company</td>
			</tr>
			<?php
			$bBorderDisplayed = false;
			while ( list($iKey, $aData) = each($aExpiredAppointments) )
			{
				if ( $sClass === '' )
				{
					$sClass = ' class="TableGreyBackground"';
				}
				else
				{
					$sClass = '';
				}
			?>
			<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aData['cst_id']) ?>>
				<td class="TablePadding"><?= htmlspecialchars($aData['name']) ?></td>
			</tr>
			<?php
			}
		}
		else
		{
			$sTitle = 'Callbacks';
			$iColSpan = 4;
			include INCLUDE_PATH . 'page_header.php';
			?>
			<tr class="TableGreyBackground">
				<td class="TableSubHeadline TablePadding">Company</td>
			</tr>
			<?php
			$bBorderDisplayed = false;
			while ( list($iKey, $aData) = each($aCallbacks) )
			{
				if ( $sClass === '' )
				{
					$sClass = ' class="TableGreyBackground"';
				}
				else
				{
					$sClass = '';
				}
			?>
			<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aData['cst_id']) ?>>
				<td class="TablePadding"><?= htmlspecialchars($aData['name']) ?></td>
			</tr>
			<?php
			}
		}
		?>
	</table>
<?php
}
?>