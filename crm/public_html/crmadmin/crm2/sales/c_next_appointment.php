<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%" valign="bottom">
			Next Appointment
		</td>
		<td width="80%">
			<input type="text" value="<?= $GLOBALS['aCustomer']['Case']['appointment'] ?>" id="appointment" name="appointment" style="width: 50%">
		</td>
	</tr>
	<tr>
		<td width="20%" valign="bottom">
			Save for callback
		</td>
		<td>
			<input name="save_callback" type="checkbox" value="true"<?=($GLOBALS['aCustomer']['Case']['callback_timestamp'] != '0000-00-00 00:00:00') ? " checked" : "";?>>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td></td>
		<td><a href="javascript:void(0);" id="calendar_link" onClick="fToggleCalendar( document.getElementById('calendar'), this );">View calendar</a> / <a href="javascript:void(0);" id="calendar_link" onClick="document.getElementById('appointment').value = '<?= date('Y-m-d G:i:s', ( $GLOBALS['aCustomer']['Case']['appointment'] && $GLOBALS['aCustomer']['Case']['appointment'] != '0000-00-00 00:00:00' ? strtotime($GLOBALS['aCustomer']['Case']['appointment']) : time() ) + 86400) ?>'; document.forms['myForm'].submit();">Push to tomorrow</a></td>
	</tr>
</table>

<div id="calendar" style="display: none; position: absolute; width: 350px; height: 550px; background: #FFFFFF; border: 1px solid #000000;">
<iframe src="?page=calendar" width="100%" height="100%" frameborder="0"></iframe>
</div>
