<?php
// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

// Open DB Connection
fOpenDatabase();

// Select all customers (CRM-2)
$aCustomers = DBGetRows('crm.cst', "( (segment_id != 1367 && segment_id != 1481 ) || segment_id IS NULL) && case_name = 'company card' && length(name) > 0");

echo "CSTID	Name	Address 1	Address 2	Zipcode	Town	Country	DUNS\n";

while ( list($iKey, $aCustomer) = each($aCustomers) ) {
	// Test if it got a contact
	if ( DBNumRows('cst, contacts', "cst.master_id = '" . $aCustomer['cst_id'] . "' && cst.cst_id = contacts.cst_id && length(contacts.name) > 0") > 0 ) {
		continue;
	}

	echo 
	o($aCustomer['cst_id']) . "\t" . 
	o($aCustomer['name']) . "\t" .

	o($aCustomer['field_1']) . "\t" .
	o($aCustomer['field_2']) . "\t" .
	o($aCustomer['field_5']) . "\t" .
	o($aCustomer['field_4']) . "\t" .
	o(fCountryNameFromID($aCustomer['invoice_country'])) . "\t" .

	o($aCustomer['duns_number']) . "\n";
}

// clean for tabs
function o($s) { return str_replace("\n", '', str_replace("\r", '', str_replace("\t", '', $s))); }
?>
