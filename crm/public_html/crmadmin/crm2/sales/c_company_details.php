<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td class="TablePadding">
			Flag Company
		</td>
		<td>
			<select name="flag">
				<option></option>
				<option <?= $GLOBALS['aCustomer']['Case']['flag'] == "A" ? "selected " : "" ?>>A</option>
				<option <?= $GLOBALS['aCustomer']['Case']['flag'] == "B" ? "selected " : "" ?>>B</option>
				<option <?= $GLOBALS['aCustomer']['Case']['flag'] == "C" ? "selected " : "" ?>>C</option>
			</select>
		</td>
	</tr>
	<tr>
		<td colspan="2"><b>Segmentation</b></td>
	</tr>
	<tr>
		<td class="TablePadding">Status</td>
		<td><?= fCalculateAccountStatus($GLOBALS['aCustomer']['Company']['cst_id']); ?></td>
	</tr>
	<tr>
		<td class="TablePadding" width="20%">Region</td>
		<td width="80%"><?= fReturnRegionNameFromCountryID( $GLOBALS['aCustomer']['Company']['invoice_country'] ) ?></td>
	</tr>
	<tr>
		<td class="TablePadding">Country</td>
		<td><?= fCountryNameFromID( $GLOBALS['aCustomer']['Company']['invoice_country'] ) ?></td>
	</tr>
        <tr>
                <td class="TablePadding">Segment</td>
                <td><?= fSegmentNameFromID( $GLOBALS['aCustomer']['Company']['segment_id'] ) ?></td>
        </tr>
	<tr>
		<td class="TablePadding">Secunia Department</td>
		<td>
			<input type="radio" name="account_type" value="TLA" <?= $GLOBALS['aCustomer']['Company']['account_type'] == 'TLA' ? 'checked' : '' ?>> TLA
			<input type="radio" name="account_type" value="CSI" <?= $GLOBALS['aCustomer']['Company']['account_type'] == 'CSI' ? 'checked' : '' ?>> CSI
			<input type="radio" name="account_type" value="Partner" <?= $GLOBALS['aCustomer']['Company']['account_type'] == 'Partner' || $GLOBALS['aCustomer']['Company']['account_type'] == 'SA' ? 'checked' : '' ?>> Partner
			<input type="radio" name="account_type" value="CRC" <?= $GLOBALS['aCustomer']['Company']['account_type'] == 'CRC' ? 'checked' : '' ?>> CRC
		</td>
	</tr>
	<tr>
		<td class="TablePadding">Company Type</td>
		<td>
			<select name="company_type" id="company_type">
			<option value="">- Select -</option>
			<option value="Enterprise" <?= $GLOBALS['aCustomer']['Company']['company_type'] == 'Enterprise' ? 'selected' : '' ?>> Enterprise </option>
			<option value="SMB" <?= $GLOBALS['aCustomer']['Company']['company_type'] == 'SMB' ? 'selected' : '' ?>> SMB </option>
			<option value="Partner" <?= $GLOBALS['aCustomer']['Company']['company_type'] == 'Partner' || $GLOBALS['aCustomer']['Company']['account_type'] == 'SA' ? 'selected' : '' ?>> Partner </option>
			</select>
		</td>
	</tr>
	<tr>
		<td class="TablePadding">Lead Source</td>
		<td>
			<select name="lead_source">
				<option> - Select Lead Source - </option>
				<?php
				while ( list($iLeadSourceID, $sLeadSource) = each($GLOBALS['aLeadSources']) ) {
					echo '<option value="' . $iLeadSourceID . '"' . ( $GLOBALS['aCustomer']['Company']['lead_source'] == $iLeadSourceID ? ' selected' : '' ) . '>' . htmlspecialchars($sLeadSource) . '</option>';
				}
				?>
			</slect>
		</td>
	</tr>
	<tr>
		<td class="TablePadding">Industry</td>
		<td>
			<select name="company_industry">
				<option> - Select Industry - </option>
			<?php
			$aIndustries = fSortArrayOther($GLOBALS['aIndustries']);
			while ( list($iIndustryID, $sIndustry) = each($aIndustries) ) {
				echo '<option value="' . $iIndustryID . '"' . ( $GLOBALS['aCustomer']['Company']['company_industry'] == $iIndustryID ? ' selected' : '' ) . '>' . htmlspecialchars($sIndustry) . '</option>';
			}
			?>
			</select>
		</td>
	</tr>
        <tr>
                <td class="TablePadding">Timezone</td>
                <td>
                        <select name="timezone">
                                <?php
                                for ( $iTZ = 0 ; $iTZ < count($GLOBALS['aTimezones']) ; $iTZ++ ) {
                                        echo '<option value="' . htmlspecialchars($GLOBALS['aTimezones'][$iTZ]) . '"' . ( $GLOBALS['aTimezones'][$iTZ] == $GLOBALS['aCustomer']['Company']['timezone'] || ( !$GLOBALS['aTimezones'][$iTZ] && !$GLOBALS['aCustomer']['Company']['timezone'] ) ? ' selected' : '' ) . '>' . htmlspecialchars($GLOBALS['aTimezones'][$iTZ]) . '</option>';
                                }
                                ?>
                        </select>
                </td>
        </tr>
        <tr>
                <td class="TablePadding">Tag</td>
                <td><input type="text" name="tag" class="" style="width: 100px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_12']) ?>"></td>
        </tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><b>Company Card</b></td>
		<td><a href="?page=customer&amp;cst_id=<?= $GLOBALS['aCustomer']['Company']['cst_id'] ?>">View here</a></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Company Facts</b></td>
	</tr>
	<tr>
		<td class="TablePadding">Total Employees</td>
		<td><input type="text" name="company_employees" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['company_employees']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Employees on location:</td>
		<td><input type="text" name="company_employees_location" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['company_employees_location']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Resp. locations</td>
		<td><input type="text" name="company_resp_location" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['company_resp_location']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Clients</td>
		<td><input type="text" name="category_clients" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['category_clients']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Servers</td>
		<td><input type="text" name="category_servers" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['category_servers']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">IT People</td>
		<td><input type="text" name="category_itpeople" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['category_itpeople']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Deployment tools:</td>
		<td>
			<select name="deployment_tools" id="deployment_tools">
				<option value="0">- Select -</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 1 ) ? "selected" : "" )?> value="1">Altiris</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 2 ) ? "selected" : "" )?> value="2">Capa</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 3 ) ? "selected" : "" )?> value="3">ZENworks</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 4 ) ? "selected" : "" )?> value="4">Shavlik</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 5 ) ? "selected" : "" )?> value="5">Gfi</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 6 ) ? "selected" : "" )?> value="6">Lumension</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 7 ) ? "selected" : "" )?> value="7">Foundstone</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 8 ) ? "selected" : "" )?> value="8">McAfee</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 9 ) ? "selected" : "" )?> value="9">LANDesk</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 10 ) ? "selected" : "" )?> value="10">WSUS</option>
				<option <?=( ( $GLOBALS['aCustomer']['Company']['deployment_tools'] == 11 ) ? "selected" : "" )?> value="11">SCCM</option>
			</select>
		</td>
	</tr>
	<tr>
		<td class="TablePadding">External IPs</td>
		<td><input type="text" name="category_externalips" class="NumberCell" style="width: 100px;" value="<?= number_format($GLOBALS['aCustomer']['Company']['category_externalips']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Financial Year Start</td>
		<td><input type="text" name="financial_year_start" style="width: 100px;" value="<?= htmlspecialchars(substr($GLOBALS['aCustomer']['Company']['financial_year_start'], 5, 5)) ?>"> MM-DD</td>
	</tr>
	<tr>
		<td class="TablePadding">Budget Process Start</td>
		<td><input type="text" name="budget_start" style="width: 100px;" value="<?= htmlspecialchars(substr($GLOBALS['aCustomer']['Company']['budget_start'], 5, 5)) ?>">  MM-DD</td>
	</tr>
	<tr>
		<td class="TablePadding">Website</td>
		<td><input type="text" name="web" id="web" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['web']) ?>"> <a href="javascript:void(0);" onClick="this.href= ( !this.href.indexOf('http://') ? 'http://' : '' ) + document.getElementById('web').value;" target="_blank">Open website</a></td>
	</tr>
	<tr>
		<td class="TablePadding">Company Aliases</td>
		<td><input type="text" name="alias" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['alias']) ?>"> Comma seperated list</td>
	</tr>
        <tr>
                <td class="TablePadding">D.U.N.S Number</td>
                <td><input type="text" name="duns_number" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['duns_number']) ?>"></td>
        </tr>
	<tr>
		<td class="TablePadding">LinkedIn Company ID</td>
		<td><input type="text" id="linkedin_id" name="linkedin_id" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['linkedin_id']) ?>"> <a href="javascript:void(0);" onClick="this.href = 'http://www.linkedin.com/companies/' + document.getElementById('linkedin_id').value + '?trk=co_search_results';" target="_blank">Open LinkedIn</a></td>
	</tr>
	<tr>
		<td class="TablePadding">Company Phone</td>
		<td><input type="text" name="phone" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['phone']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Last PO Number</td>
		<td><input type="text" name="po_number" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Case']['po_number']) ?>"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Company Address</b></td>
	</tr>
	<tr>
		<td class="TablePadding">Company Name</td>
		<td>
<?php
if ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ) {
	echo '<input type="text" name="name" style="width: 275px;" value="' . htmlspecialchars($GLOBALS['aCustomer']['Company']['name']) . '">';
} else {
	echo htmlspecialchars($GLOBALS['aCustomer']['Company']['name']);
}
?></td>
	</tr>
	<tr>
		<td class="TablePadding">Address 1</td>
		<td><input type="text" name="address_1" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_1']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Address 2</td>
		<td><input type="text" name="address_2" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_2']) ?>"></td>
	</tr>
	<tr>
		<td class="TablePadding">Zip code / Town</td>
		<td>
			<input type="text" name="zipcode" style="width: 100px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_5']) ?>"><img src="/" style="visibility: hidden;" width="5" border="0"><input type="text" name="town" style="width: 170px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_4']) ?>">
		</td>
	</tr>
	<tr>
		<td class="TablePadding">Country</td>
		<td>
			<select name="country">
				<option value="0"> - Select Country - </option>
				<?php
				// Select all countries
				$aCountries = DBGetRows('crm.countries', '', 'country');
				while ( list($iKey, $aCountry) = each($aCountries) )
				{
					echo '<option value="' . $aCountry['id'] . '"' . ( $GLOBALS['aCustomer']['Company']['invoice_country'] == $aCountry['id'] ? ' selected' : '' ) . '>' . htmlspecialchars($aCountry['country']) . '</option>';
				}
				?>
			</select>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Financial Details</b></td>
	</tr>
	<tr>
		<td class="TablePadding">VAT no.</td>
		<td>
			<input type="text" name="vatnum" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_6']) ?>">&nbsp;e.g. DK26833345
		</td>
	</tr>
	<tr>
		<td class="TablePadding">EAN no.</td>
		<td>
			<input type="text" name="eannum" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['field_7']) ?>">&nbsp;e.g. 5712345123456
		</td>
	</tr>

        <tr>
                <td><br></td>
        </tr>
        <tr>
                <td colspan="2"><b>Customer / Agreement Status</b></td>
        </tr>
        <tr>
                <td class="TablePadding">Agreement Terminated?</td>
                <td>
                        <input type="checkbox" name="agreement_terminated" <?= ($GLOBALS['aCustomer']['Company']['agreement_terminated'] ? 'checked' : '' ) ?> value="1">&nbsp;Yes
                </td>
        </tr>
        <tr>
                <td class="TablePadding">Agreement End-Date</td>
                <td>
                        <input type="text" name="agreement_terminated_from" style="width: 275px;" value="<?= htmlspecialchars($GLOBALS['aCustomer']['Company']['agreement_terminated_from']) ?>">&nbsp;e.g. 2017-12-31
                </td>
        </tr>

	<?php
		if ( hasCSITrial(  $GLOBALS['aCustomer']['AllIDs'] ) && ( DBNumRows("crm.trial_mail_pipeline", "cst_id = '".(int)$GLOBALS['aCustomer']['Company']['cst_id']."'") == 1 ) ){
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Automated CSI Trial Mail Flow</b></td>
	</tr>
	<tr>
		<td>Stop trial mail flow</td>
		<td><input type="checkbox" name="stop_mail" value="1"></td>
	</tr>
		<?php
			$ilType = DBGetRowValue("crm.trial_mail_pipeline", "type", "cst_id = '".(int)$GLOBALS['aCustomer']['Company']['cst_id']."'");
		?>
	<tr>
		<td>30 days unlimited trial mail flow</td>
		<td><input type="checkbox" name="switch_mail" value="1" <?=( ( $ilType == 1 ) ? "disabled checked" : "" )?>></td>
	</tr>
	<?php
		}
	?>
</table>
