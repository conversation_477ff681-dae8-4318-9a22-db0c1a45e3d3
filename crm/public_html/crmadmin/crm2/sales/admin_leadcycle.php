<?php
// Access Level: 1
// Admin
// SM
// Lead Management
fVerifyAccess( $aRepData['person_id'], 1 );

// Update data
if ( $_POST && $_POST['segment_id'] ) {
	while( list($iCustomerID, $iLostLogID) = each($_POST['aMove']) ) {
		// Select "Lost Log"
		$aLostLog = DBGetRow('crm.lost_log', "id = '" . $iLostLogID . "'");

		// Build comment and insert "Lost Log" as a comment
		$sComment = 'Customer has previously been marked as lost:<br>
Person: ' . fReturnRepDetailFromID( $aLostLog['person_id'] ) . '<br>
Reason: ' . fReturnLostReason( $aLostLog['reason'] ) . '<br>
Comment: ' . htmlspecialchars( $aLostLog['comment'] );

		DBQuery("INSERT INTO crm.comments SET comment = '" . mysql_escape_string($sComment) . "', added = NOW(), cst_id = '" . $iCustomerID . "'");

		// Delete "Lost Log"
		DBQuery("DELETE FROM crm.lost_log WHERE cst_id = '" . $iCustomerID . "'");

		// Move "Master CST ID" to Reps. Canvas segment 
		$aCustomer = DBGetRow('crm.cst', "cst_id = '" . $iCustomerID . "'");

		DBQuery("UPDATE crm.cst SET segment_id = '" . $_POST['segment_id'] . "' WHERE cst_id = '" . $aCustomer['master_id'] . "' LIMIT 1");
	}
}

// SQL Statements
$aSQL['0-6'] = 'AND (date_sub(NOW(), INTERVAL 6 MONTH) < lost_time)';
$aSQL['6-12'] = 'AND (date_sub(NOW(), INTERVAL 6 MONTH) >= lost_time AND date_sub(NOW(), INTERVAL 12 MONTH) < lost_time)';
$aSQL['12-18'] = 'AND (date_sub(NOW(), INTERVAL 12 MONTH) >= lost_time AND date_sub(NOW(), INTERVAL 18 MONTH) < lost_time)';
$aSQL['18-24'] = 'AND (date_sub(NOW(), INTERVAL 18 MONTH) >= lost_time AND date_sub(NOW(), INTERVAL 24 MONTH) < lost_time)';
$aSQL['24+'] = 'AND (date_sub(NOW(), INTERVAL 24 MONTH) >= lost_time)';

?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Lead Cycle' . ( $_GET['region'] ? ' (' . htmlspecialchars($_GET['region']) . ' / ' . htmlspecialchars($_GET['range']) . ' Months)' : '' );
	$iColSpan = 2 + ( $_GET['region'] ? 3 : 0 );
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

// Overview or customer list?
if ( !$_GET['region'] ) {

	// Loop through each country region
	while ( list($sRegion, $aCountries) = each($aCountryRegions) ) {
		// Special rule for "Other" (reserved lookup)
		if ( $sRegion == 'Other' ) {
			$sLookup = 'NOT ';
		} else {
			$sLookup = '';
		}

		// Select Lost Leads 0-6 months
		$aResult['0-6'] = DBNumRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountries) . ") " . $aSQL['0-6']);
		$aResult['6-12'] = DBNumRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountries) . ") " . $aSQL['6-12']);
		$aResult['12-18'] = DBNumRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountries) . ") " . $aSQL['12-18']);
		$aResult['18-24'] = DBNumRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountries) . ") " . $aSQL['18-24']);
		$aResult['24+'] = DBNumRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountries) . ") " . $aSQL['24+']);
	?>

	<tr>
		<td width="150" class="TablePadding TableSubHeadline TableGreyBackground"><?= $sRegion ?></td>
		<td class="TableSubHeadline TableGreyBackground">Leads in category</td>
	</tr>

		<?php
		// Output result
		reset($aResult);
		while ( list($sRange, $iValue) = each($aResult) ) {
		?>

	<tr>
		<td clasS="TablePadding"><?= $sRange ?> Months</td>
		<td><a href="?page=admin_leadcycle&amp;region=<?= $sRegion ?>&amp;range=<?= $sRange ?>"><?= number_format($iValue) ?></a></td>
	</tr>
	
		<?php
		}
		?>

	<tr>
		<td><br></td>
	</tr>

	<?php
	}

} else { // Show details

	// Select result
	$aResult = DBGetRows('crm.cst, crm.lost_log', "cst.cst_id = lost_log.cst_id AND invoice_country " . $sLookup . " in (" . implode(',', $aCountryRegions[$_GET['region']]) . ") " . $aSQL[$_GET['range']], 'lost_time');

	?>

	<form method="POST" action="">

	<tr>
		<td class="TablePadding TableSubHeadline TableGreyBackground">Lost Time</td>
		<td class="TableSubHeadline TableGreyBackground">Company</td>
		<td class="TableSubHeadline TableGreyBackground">Person</td>
		<td class="TableSubHeadline TableGreyBackground">Reason</td>
		<td class="TableSubHeadline TableGreyBackground">Comment</td>
	</tr>

	<?php
	// Output result
	while ( list($iKey, $aCustomer) = each($aResult) ) {

		// Switch BG
		$sBG = ( $sBG != '#FFFFFF' ? '#FFFFFF' : '#EEEEEE' );
	?>

	<tr style="background-color: <?= $sBG ?>;">
		<td class="TablePadding"><input type="checkbox" name="aMove[<?= $aCustomer['cst_id'] ?>]" value="<?= $aCustomer['id'] ?>">&nbsp;<?= $aCustomer['lost_time'] ?></td>
		<td><?= trim(htmlspecialchars(strtoupper($aCustomer['case_name'])), 'CARD_') ?>: <a href="?page=customer&amp;cst_id=<?= $aCustomer['cst_id'] ?>"><?= htmlspecialchars($aCustomer['name']) ?></a></td>
		<td><?= htmlspecialchars(fReturnRepDetailFromID($aCustomer['person_id'])) ?></td>
		<td><?= htmlspecialchars(fReturnLostReason($aCustomer['reason'])) ?></td>
		<td><?= htmlspecialchars($aCustomer['comment']) ?></td>
	</tr>

	<?php
	}
}
	?>

	<tr><td><br></td></tr>

	<?php
	// Select segments
	$sSegments = '';
	$aSegments = DBGetRows('crm.segments', "segments.segment_status = 0 AND segments.segment_name LIKE 'CRM2%'", 'segment_name');
	while ( list($iKey, $aSegment) = each($aSegments) ) {
		$aSegment['segment_name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);
	
		$sSegments .= '<option value="' . $aSegment['segment_id'] . '">' . htmlspecialchars($aSegment['segment_name']) . '</option>';
	}
	?>
	
	<tr>
		<td>
			&nbsp;&nbsp;<b>Select Segment:</b><br>
			&nbsp;&nbsp;<select name="segment_id"><?= $sSegments ?></select>
		</td>
	</tr>


	<tr>
		<td><br><input type="submit" value="Update"></td>
	</tr>

</form>

</table>
<br>
<br>

