<?php
if ( $aRepData['product'] == 1 || $aRepData['person_level'] == 1) {
	$iStartMonth = date('Y-') . str_pad( ( ( ( ceil(date('m') / 3 ) - 1 ) *3  ) + 1 ), 2, STR_PAD_LEFT, 0);
	$iEndMonth = date('Y-') . str_pad( ( ( ( ceil(date('m') / 3 ) - 1 ) *3  ) + 3 ), 2, STR_PAD_LEFT, 0);
} else {
	$iStartMonth = date('Y-m');
}

//echo "<!-- " . $iStartMonth . ' / ' . $iEndMonth . " -->";

// Get rigth offset for Quarterly target output
$iOffset = date('m') % 3;
if ( $iOffset == 0 ) {
	$iOffset = 2;
} elseif ( $iOffset == 2 ) {
	$iOffset = 1;
} else {
	$iOffset = 0;
}

// Select Top Monhly
$sQuery = "
SELECT
 saleslog.person_id,
 SUM( ROUND((saleslog.product_price*saleslog.currency_exchange_rate)/saleslog.currency_exchange_rate_euro) ) AS amount_euro
FROM
 crm.saleslog,
 crm.salespeople
WHERE
 saleslog.sold_date >= '" . $iStartMonth . "' AND
 (saleslog.status is null || (saleslog.status != 3 && saleslog.status != 6 && saleslog.status != 7) ) AND
 saleslog.person_id NOT IN(73) AND
 saleslog.product_price > 0 AND
 saleslog.person_id = salespeople.person_id AND
 (salespeople.product = '" . $aRepData['product'] . "' )
GROUP BY
 saleslog.person_id
ORDER BY
 amount_euro DESC";
$aTopMonthly = DBQueryGetRows($sQuery);
// (salespeople.product = '" . $aRepData['product'] . "' || saleslog.person_id = 82 )

// Select Top Weekly
$sQuery = "
SELECT
 saleslog.person_id,
 SUM( ROUND((saleslog.product_price*saleslog.currency_exchange_rate)/saleslog.currency_exchange_rate_euro) ) AS amount_euro
FROM
 crm.saleslog,
 crm.salespeople
WHERE
 saleslog.sold_date >= '" . date('Y-m-d', mktime(0, 0, 0, date('m'), (date('d') - date('w') + 1), date('Y'))) . "' AND
 (saleslog.status is null || (saleslog.status != 3 && saleslog.status != 6 && saleslog.status != 7) ) AND
 saleslog.person_id NOT IN(73) AND
 saleslog.product_price > 0 AND
 saleslog.person_id = salespeople.person_id AND
 (salespeople.product = '" . $aRepData['product'] . "' )
GROUP BY
 saleslog.person_id
ORDER BY
 amount_euro DESC
LIMIT 5";
$aTopWeekly = DBQueryGetRows($sQuery);

// Select CRM message
$aMessage = DBGetRow('crm.crm_message');
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%" valign="top" style="padding-top: 5px;">
			<fieldset>
			<legend>Secunia CRM 2.0 - Menu</legend>
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td width="50%" valign="top"><a href="?page=today&amp;right=performance" target="main" accesskey="t">Today</a></td>
					<td width="50%" valign="top"><a href="?page=internal_conference&amp;right=overview" target="main">Howto: Tele Conference</a></td>
				</tr>
				<tr>
					<td width="50%" valign="top"><a href="?page=forecast_report&amp;right=overview" target="main">Forecast Report</a></td>
					<td width="50%" valign="top"><a href="?page=internal&amp;right=overview" target="main">Contact List</a></td>
				</tr>
				<tr>
					<td width="50%" valign="top">
						<a href="?page=sale_report&amp;right=overview" target="main">Sale Report</a><br />
						<a href="?page=isa_report&amp;right=overview" target="main">ISA Report</a>
					</td>
					<td width="50%" valign="top"><?php echo ($aRepData['person_level'] == 2) ? "<a href=\"?page=lead_approval&amp;right=overview\" target=\"main\">Lead Approval</a>" : "<br>"; ?></td>
				</tr>
				<tr>
					<td width="50%" valign="top"><a href="?page=customer_report&amp;right=overview" target="main">Customer Report</a></td>
					<td width="50%" valign="top">
<?php
// Show lead management menu options?
if ( fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	?>
	<select onChange="parent.frames['main'].location = this.value;">
		<option> - Lead Management Options -</option>
		<option value="?page=admin_segment_overview&right=overview">Segment Overview</option>
		<option value="?page=admin_rep_customers&right=overview">Representative Overview</option>
		<option value="?page=admin_cluster&right=overview">Clustering</option>
		<option value="?page=admin_online_sales">Online Sales</option>
		<option value="?page=admin_leadflow">Lead Flow</option>
		<option value="?page=admin_leadcycle">Lead Cycle</option>
		<option value="?page=admin_dm_campaign">DM (In progress)</option>
		<option value="?page=admin_csi_beta">Attach CST Beta to Card</option>
		<option value="?page=inbound_pipeline">Inbound Pipeline</option>
	</select>
	<?php
}
?>
					</td>
				</tr>
				<tr>
					<td valign="top"><a href="?page=send_advisory&amp;right=overview" target="main">Send Advisory</a></td>
					<td valign="top">Demo Acc. Pincode: <?= (int) file_get_contents('/home/<USER>/capwd/pincode') ?>
<?php if ( $aRepData['product'] != 1 ) { ?>
					<br><?= in_array($aRepData['person_id'], array(1,2,71,74,65,99,102,103)) ? '<a href="?page=assigned_leads" target="main">Customer Leads</a>' : '' ?>
<?php } ?>
					</td>
				</tr>
			</table>
			</fieldset>
		</td>
		<td width="3">&nbsp;</td>
		<td width="16%" valign="top" style="padding-top: 5px;">
			<?php
			$iExtension = (int) $aRepData['local_number'];

			$iStart = mktime(0, 0, 0);
			$iEnd = mktime(23, 59, 59);
			
			$sQuery = "SELECT * FROM crm.it_phone_cdr WHERE (call_src='".$iExtension."' OR call_dst='".$iExtension."') AND call_date > ".$iStart." AND call_date <= ".$iEnd;
			$aCalls = DBQueryGetRows($sQuery);

			// Total Calls
			$iDuration = 0;
			foreach($aCalls as $aCall){
				$iDuration = $aCall['call_billsec']+$iDuration; 
			}
			$iCalls = sizeof($aCalls);
			$iAverage = $iDuration / $iCalls;
			$iHours = floor($iAverage/3600);
			$iMinutes = floor(($iAverage-($iHours*3600))/60);
			$iSeconds = floor($iAverage-($iHours*3600)-($iMinutes*60));
			$sAverage = str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
			
			$iHours = floor($iDuration/3600);
			$iMinutes = floor(($iDuration-($iHours*3600))/60);
			$iSeconds = floor($iDuration-($iHours*3600)-($iMinutes*60));
			$sDuration = str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
			
			// Successful Calls
			$iDuration = 0;
			foreach($aCalls as $aCall){
				if ($aCall['call_billsec'] > 30){
					$iDuration = $aCall['call_billsec']+$iDuration;
					$aSuccessfulCalls[] = $aCall;
				} 
			}
			$iCalls = sizeof($aCalls);
			$iCallsSuc = sizeof($aSuccessfulCalls);
			$iAverage = $iDuration / $iCallsSuc;
			$iHours = floor($iAverage/3600);
			$iMinutes = floor(($iAverage-($iHours*3600))/60);
			$iSeconds = floor($iAverage-($iHours*3600)-($iMinutes*60));
			$sAverageSuc = str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);

			$iHours = floor($iDuration/3600);
			$iMinutes = floor(($iDuration-($iHours*3600))/60);
			$iSeconds = floor($iDuration-($iHours*3600)-($iMinutes*60));
			$sDurationSuc = str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
			?>
			<fieldset>
				<legend>Calls today</legend>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td style="width: 120px;">&nbsp;</td>
						<td>Successful</td>
						<td>Total</td>
					</tr>
					<tr>
						<td>No. of calls:</td>
						<td><?= $iCallsSuc ?> (<?= round(($iCallsSuc/$iCalls)*100); ?>%)</td>
						<td><?= $iCalls ?></td>
					</tr>
					<tr>
						<td>Average duration:</td>
						<td><?= $sAverageSuc ?></td>
						<td><?= $sAverage ?></td>
					</tr>
					<tr>
						<td>Total duration:</td>
						<td><?= $sDurationSuc ?></td>
						<td><?= $sDuration ?></td>
					</tr>
				</table>
				<?php echo ($aRepData['person_level'] == 2) ? "<a href=\"?page=callstats&amp;right=overview\" target=\"main\">ISA call statistics</a>" : ""; ?>
			</fieldset>
		</td>
		<td width="3">&nbsp;</td>
		<td width="15%" valign="top" style="padding-top: 5px;">
			<fieldset>
				<?php
				if ( $aRepData['person_level'] == 1) {
					echo "<legend>Revenue (Q".ceil(date('m') / 3).")</legend>";
					$sQuery = "
SELECT
 saleslog.cst_id,
 saleslog.product_category,
 saleslog.sold_date,
 ROUND((saleslog.product_price*saleslog.currency_exchange_rate)/saleslog.currency_exchange_rate_euro) AS amount_euro
FROM
 crm.saleslog,
 crm.salespeople
WHERE
 saleslog.sold_date >= '" . $iStartMonth . "' AND
 (saleslog.status is null || (saleslog.status != 3 && saleslog.status != 6 && saleslog.status != 7) ) AND
 saleslog.person_id NOT IN(73) AND
 saleslog.product_price > 0 AND
 saleslog.person_id = salespeople.person_id AND
 saleslog.person_id = '" . $aRepData['person_id']."' AND
 (salespeople.product = '" . $aRepData['product'] . "' )
ORDER BY
 amount_euro DESC";
					$aSales = DBQueryGetRows($sQuery);
					foreach($aSales as $aSale){
						$aCustomer = fGetCustomerDetails( $aSale['cst_id'] );
						$sCustomerIDs = $aCustomer['AllIDs'];
						if (fIsCanvas( $aSale['sold_date'], $sCustomerIDs, $aSale['product_category'] )){
							$iNewBusiness += $aSale['amount_euro'];
						} else {
							$iRecurrence += $aSale['amount_euro'];
						}
						$iTotal += $aSale['amount_euro'];
					}
					$iTarget = DBGetRowValue('crm.forecast', 'sum(revenue)', "person_id = '" . $aRepData['person_id'] . "' AND month >= '" . (fReturnPersonForecastMonth($aRepData['person_id'])-$iOffset) . "' && month <= '" . (fReturnPersonForecastMonth($aRepData['person_id'])+2-$iOffset) . "'");
					?>
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td>New business:</td>
							<td>&euro; <?=number_format($iNewBusiness);?></td>
						</tr>
						<tr>
							<td>Target:</td>
							<td>&euro; <?=number_format($iTarget);?> (<?=(round(($iNewBusiness/$iTarget)*100))?>%)</td>
						</tr>
						<tr>
							<td>Recurrence:</td>
							<td>&euro; <?=number_format($iRecurrence);?></td>
						</tr>
						<tr>
							<td>Total revenue:</td>
							<td>&euro; <?=number_format($iRecurrence+$iNewBusiness);?></td>
						</tr>
					</table>
					<?php
				} elseif ($aRepData['person_level'] == 3 && $aRepData['department'] == 2){
					echo "<legend>Snapshots - ".date('F')."</legend>";
					$aRows = DBQueryGetRows("SELECT count(*) AS count FROM leadlog WHERE lead_approve_date >= '" . $iStartMonth . "' AND lead_person_id='".$aRepData['person_id']."'");
					$iApproved = $aRows[0]['count'];
					
					//$iTarget has been hard coded for March until administration punches in the right numbers
					$iTarget = DBGetRowValue('crm.forecast', 'sum(revenue)', "person_id = '" . $aRepData['person_id'] . "' AND month = '" . (fReturnPersonForecastMonth($aRepData['person_id'])-$iOffset) . "'");
					//$iTarget = 69;


					$sQuery = "
SELECT
 saleslog.cst_id,
 saleslog.product_category,
 saleslog.sold_date,
 ROUND((saleslog.product_price*saleslog.currency_exchange_rate)/saleslog.currency_exchange_rate_euro) AS amount_euro
FROM
 crm.saleslog,
 crm.salespeople
WHERE
 saleslog.sold_date >= '" . $iStartMonth . "' AND
 (saleslog.status is null || (saleslog.status != 3 && saleslog.status != 6 && saleslog.status != 7) ) AND
 saleslog.person_id NOT IN(73) AND
 saleslog.product_price > 0 AND
 saleslog.person_id = salespeople.person_id AND
 saleslog.person_id = '" . $aRepData['iae_id']."' AND
 (salespeople.product = '" . $aRepData['product'] . "' )
ORDER BY
 amount_euro DESC";
					$aSales = DBQueryGetRows($sQuery);
					foreach($aSales as $aSale){
						$aCustomer = fGetCustomerDetails( $aSale['cst_id'] );
						$sCustomerIDs = $aCustomer['AllIDs'];
						if (fIsCanvas( $aSale['sold_date'], $sCustomerIDs, $aSale['product_category'] )){
							$aLeadSales = DBQueryGetRows("SELECT lead_id FROM leadlog WHERE lead_org_cstid='".$aSale['cst_id']."'");
							if (sizeof($aLeadSales) > 0){
								$iTeamRevenue += $aSale['amount_euro'];
							}
						}
					}
					$iTeamTarget = DBGetRowValue('crm.forecast', 'sum(revenue)', "person_id = '" . $aRepData['iae_id'] . "' AND month = '" . (fReturnPersonForecastMonth($aRepData['iae_id'])-$iOffset) . "'");
					?>
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td>Approved:</td>
							<td># <?=number_format($iApproved);?></td>
						</tr>
						<tr>
							<td>Target:</td>
							<td># <?=number_format($iTarget);?> (<?=(round(($iApproved/$iTarget)*100))?>%)</td>
						</tr>
						<tr>
							<td>Team revenue:</td>
							<td>&euro; <?=number_format($iTeamRevenue);?></td>
						</tr>
						<tr>
							<td>Team target:</td>
							<td>&euro; <?=number_format($iTeamTarget);?> (<?=(round(($iTeamRevenue/$iTeamTarget)*100))?>%)</td>
						</tr>
					</table>
					<?php
				} else {
					echo "<legend>Target</legend>";
					if ( $aRepData['product'] == 1 ) {
						$iRevenueTarget = DBGetRowValue('crm.forecast', 'sum(revenue)', "person_id = '" . $aRepData['person_id'] . "' AND month >= '" . (fReturnPersonForecastMonth($aRepData['person_id'])-$iOffset) . "' && month <= '" . (fReturnPersonForecastMonth($aRepData['person_id'])+2-$iOffset) . "'");
					} else {
						$iRevenueTarget = DBGetRowValue('crm.forecast', 'revenue', "person_id = '" . $aRepData['person_id'] . "' AND month = '" . fReturnPersonForecastMonth($aRepData['person_id']) . "'");
					}
					for ( $iCount = 0 ; $iCount < count($aTopMonthly) ; $iCount++ ) {
						if ( $aTopMonthly[$iCount]['person_id'] == $aRepData['person_id'] ) {
							$sYou = '&euro; ' . number_format($aTopMonthly[$iCount]['amount_euro']) . " (" . round( $aTopMonthly[$iCount]['amount_euro'] / $iRevenueTarget * 100 ) . "%)";
						}
					}
					if (trim($sYou) == "") $sYou = "&euro; 0 (0%)";
					?>
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td>Target:</td>
							<td>&euro; <?= number_format($iRevenueTarget) ?></td>
						</tr>
						<tr>
							<td>You:</td>
							<td><?= $sYou ?></td>
						</tr>
					</table>
					<?php
				}
				if ($aRepData['person_level'] == 2){
					echo "<a href=\"?page=isatargets&right=overview\" target=\"main\">ISA Targets</a>";
				}
				?>
			</fieldset>
		</td>
		<td width="3">&nbsp;</td>
		<td width="14%" valign="top" style="padding-top: 5px;">
			<fieldset>
			<legend>Top <?= ( $aRepData['product'] == 1 ? 5 : 5 ) ?> - Week <?= date('W') ?></legend>
			<?php
			for ( $iCount = 0 ; $iCount < ( $aRepData['product'] == 1 ? 5 : 5 ) ; $iCount++ ) {
				echo ($iCount+1) . '. ' . fReturnRepDetailFromID($aTopWeekly[$iCount]['person_id'], 'name') . ' (&euro; ' . number_format($aTopWeekly[$iCount]['amount_euro'],2) . ')<br>';
			}
			?>
			</fieldset>
		</td>
		<td width="3">&nbsp;</td>
		<td width="18%" valign="top" style="padding-top: 5px;">
			<fieldset>
			<legend>Top 5 - <?= ( ($aRepData['product'] == 1 || $aRepData['person_level'] == 1) ? 'Q' . ceil(date('m') / 3) : date('F') ) ?></legend>
			<?php
			for ( $iCount = 0 ; $iCount < 5 ; $iCount++ ) {
				echo ($iCount+1) . '. ' . fReturnRepDetailFromID($aTopMonthly[$iCount]['person_id'], 'name') . ' (&euro; ' . number_format($aTopMonthly[$iCount]['amount_euro'],2) . ')<br>';
			}
			?>
			</fieldset>
		</td>
		<td width="3">&nbsp;</td>
		<td width="17%" valign="top" style="padding-top: 5px;">
			<fieldset>
				<legend><u>S</u>earch (ALT+S)</legend>
				<form method="POST" target="right" action="?page=<?php echo getenv('REMOTE_USER') == 'jb' ? "search2" : "search"; ?>">
					<input type="text" name="search" id="search_field" accesskey="s"><br>
					<?php
					if (fVerifyAccess($aRepData['person_id'], 1)){
						echo "<input type=\"checkbox\" name=\"widesearch\" value=\"true\"> Wide Search<br>
							<input type=\"checkbox\" name=\"itrash\"> Include Trash can<br>";
					} else {
						echo "<input type=\"checkbox\" name=\"extendedsearch\" value=\"true\"> Extended Search<br>";
					}
					?>
				</form>
			</fieldset>
		</td>
	</tr>
</table>
