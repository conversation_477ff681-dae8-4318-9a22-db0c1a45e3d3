<?php
// Trim input
$_POST['search'] = trim($_POST['search']);
// Search limits (privileges)
$wide = false;
if ( !fVerifyAccess( $aRepData['person_id'], 1 ) ) {
	// Sales reps have the extra search option
	if ( $_POST['extendedsearch'] == 'true' ) {
		$wide = true;
	}
}
if ( fVerifyAccess( $aRepData['person_id'], 2 ) && $_POST['widesearch'] == 'true' ) { // Over full-search (LM/Admin)
	$sSearchLimit = '';
} elseif ( fVerifyAccess( $aRepData['person_id'], 1 ) && $_POST['widesearch'] == 'true' ) { // full-search
	$sSearchLimit = "( cst.customer_marked_dead != 6 OR cst.customer_marked_dead IS NULL ) AND ( cst.segment_id != 1378 || cst.segment_id IS NULL ) AND ";
} elseif ( $_POST['extendedsearch'] != 'true' ) {
	$sSearchLimit = "cst.person_id = '" . $aRepData['person_id'] . "' AND ";
	$aIDs = DBQueryGetRows("select cst_id from cst where person_id = '" . $aRepData['person_id'] . "' AND case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc') union select master_id from cst where person_id = '" . $aRepData['person_id'] . "' AND case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc')");
	$sIDs = '';
	while ( list($iKey, $aValue) = each($aIDs) ) {
		if ( $aValue['cst_id'] ) {
			$sIDs .= $aValue['cst_id'] . ',';
		}
	}
	
	$sSearchLimit = 'cst.cst_id IN (' . substr($sIDs, 0, -1) . ') AND ';
}

function makeSCard( $data ) {
	$sCard = "";
	// Card
	switch ( $data['case_name'] ) {
		case 'card_vi':
			$sCard = 'VI: ';
			break;
		case 'card_nsi':
			$sCard = 'CSI: ';
			break;
		case 'card_partner':
			$sCard = 'PARTNER: ';
			break;
		case 'card_crc':
			$sCard = 'CRC: ';
			break;
		case 'card_csc':
			$sCard = 'CSC: ';
			break;
		case 'Company Card':
			$sCard = '<b>CC: ';
			break;
		default:
			continue;
	}
	return $sCard;
}

// Search for customers
$aQuery = "
SELECT
 cst.cst_id,
 cst.master_id,
 cst.segment_id,
 cst.name AS company,
 cst.active_partner,
 cst.person_id,
 cst.case_name
FROM
 crm.cst
LEFT JOIN
 (crm.contacts)
ON
 (contacts.cst_id = cst.cst_id)
WHERE
 " . $sSearchLimit . "
 (
	cst.name like '%" . $_POST['search'] . "%' OR
	contacts.name like '%" . $_POST['search'] . "%' OR
	contacts.phone like '%" . $_POST['search'] . "%' OR
	contacts.email like '%" . $_POST['search'] . "%' OR
	cst.alias like '%" . $_POST['search'] . "%' OR
	(field_12 like '%" . $_POST['search'] . "%' || master_id in (select cst_id from cst where field_12 like '%" . $_POST['search'] . "%') )
 ) AND
 case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc', 'card_csc')
ORDER BY
 cst.name";
$aCustomers = DBQueryGetRows($aQuery);

	$sContent = "";
	$iCount = 0;
	$aListed = array();
	while ( list($iKey, $aCustomer) = @each($aCustomers) ) {
		// Test if "master_id" has a segment id of '1367' (trash)

		if ( $_POST['itrash'] != "on" ){
			if ( ( !$aCustomer['master_id'] && $aCustomer['segment_id'] == 1481 ) || DBGetRowValue('crm.cst', 'segment_id', "cst_id = '" . $aCustomer['master_id'] . "'") == 1481 ) {
				continue;
			}
		}

		$sClass = ( $sClass === '' ? ' class="TableGreyBackground TablePadding"' : '' );

		$u_1 = "";
		$u_2 = "";

		// No dupes
		if ( $aListed[$aCustomer['cst_id']] ) {
			continue;
		}
		$aListed[$aCustomer['cst_id']] = true;

		$sCard = makeSCard( $aCustomer );

		$click = "";
		/* if ( $wide == false && ( $aCustomer['person_id'] == $aRepData['person_id'] || $aCustomer['person_id'] == "" || $aCustomer['person_id'] == "0"  ) ) {
			$u_1 ="<u>";
			$u_2 ="</u>";
			if ( $aCustomer['person_id'] == $aRepData['person_id'] ) {
				$click = fRowLink('?page=customer&cst_id=' . $aCustomer['cst_id']);
			}
		} else */
		if ( $wide == true && ( $aCustomer['person_id'] == $aRepData['person_id'] || $aCustomer['person_id'] == "" || $aCustomer['person_id'] == "0" ) ) {
			$u_1 ="<u>";
			$u_2 ="</u>";
		}

		if ( $aCustomer['person_id'] == $aRepData['person_id'] || $wide == false ) {
			$click = fRowLink('?page=customer&cst_id=' . $aCustomer['cst_id']);
		}

		if ( $aCustomer['case_name'] == 'Company Card' ) {
			$u_1 = "";
			$u_2 = "";
		}

		$sContent .= "
		<tr ".$style." ".$sClass . $click .">
			<td class=\"TablePadding\">".++$iCount . ". " . $sCard .  $u_1 . htmlspecialchars($aCustomer['company']) .  $u_2 ."<!-- (".$aCustomer['cst_id'] . " - " . $aCustomer['master_id'] .") --></td>
		</tr>";

		if ( $aCustomer['active_partner'] == 1 ) { // Include all related customers
			$jCount = 0;
			// Partner case id
			$partnerCSTId = DBGetRowValue("crm.cst", "cst_id", "case_name = 'card_partner' AND master_id = '".(int)$aCustomer['cst_id']."'");
			$aQuery = "
			SELECT
			cst.cst_id,
			A.person_id,
			A.master_id,
			A.segment_id,
			cst.name AS company,
			A.case_name
			FROM
			crm.cst AS A
			LEFT JOIN
			(crm.contacts)
			ON
			(contacts.cst_id = A.cst_id)
			LEFT JOIN
			crm.cst
			ON
			cst.cst_id = A.master_id
			WHERE
			" . $sSearchLimit . "
			A.case_name IN ( 'card_partner' )
			AND A.partner_id = ( SELECT partner_profile.partner_id FROM ca.partner_profile WHERE partner_profile.account_id = ( SELECT account_id FROM ca.accounts WHERE accounts.cst_id =  ".(int)$partnerCSTId." AND accounts.special_limits LIKE 'partner%' ) LIMIT 1 )
			ORDER BY
			cst.name";
			$subCustomers = DBQueryGetRows($aQuery);
			while ( list($iKey, $subCustomer) = @each($subCustomers) ) {
				// Test if "master_id" has a segment id of '1367' (trash)
				if ( $_POST['itrash'] != "on" ){
					if ( ( !$subCustomer['master_id'] && $subCustomer['segment_id'] == 1481 ) || DBGetRowValue('crm.cst', 'segment_id', "cst_id = '" . $subCustomer['master_id'] . "'") == 1481 ) {
						continue;
					}
				}

				$sClass = ( $sClass === '' ? ' class="TableGreyBackground TablePadding"' : '' );

				// No dupes
				if ( $aListed[$subCustomer['cst_id']] ) {
					continue;
				}
				$aListed[$subCustomer['cst_id']] = true;

				$sCard = makeSCard( $subCustomer );

				$u_1 = "";
				$u_2 = "";

				$click = "";
				/* if ( $wide == false && ( $aRepData['person_id'] == $subCustomer['person_id'] || $subCustomer['person_id'] == "" || $aCustomer['person_id'] == "0" ) ) {
					if ( $subCustomer['person_id'] == $aRepData['person_id'] ) {
						$click = fRowLink('?page=customer&cst_id=' . $subCustomer['cst_id']);
					}
					$u_1 ="<u>";
					$u_2 ="</u>";
				} else */
				if ( $wide == true && ( $subCustomer['person_id'] == $aRepData['person_id'] || $subCustomer['person_id'] == "" || $subCustomer['person_id'] == "0" ) ) {
					$u_1 ="<u>";
					$u_2 ="</u>";
				}

				if ( $subCustomer['person_id'] == $aRepData['person_id'] || $wide == false ) {
					$click = fRowLink('?page=customer&cst_id=' . $subCustomer['cst_id']);
				}

				if ( $subCustomer['case_name'] == 'Company Card' ) {
					$u_1 = "";
					$u_2 = "";
				}

				$sContent .= "
				<tr ".$style." ".$sClass . $click .">
					<td class=\"TablePadding\">&nbsp;&nbsp;&nbsp;".$iCount.".". ++$jCount . ". " . $sCard . $u_1 . htmlspecialchars($subCustomer['company']) .  $u_2 ."<!-- (".$subCustomer['cst_id'] . " - " . $subCustomer['master_id'] .") --></td>
				</tr>";
			}
		}

	}

	?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'Search - ' . $iCount . ' result' . ( count($aCustomers) != 1 ? 's' : '' );
	$iColSpan = 3;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="100%" class="TableSubHeadline TableGreyBackground TablePadding">Company</td>
	</tr>
	<?php
	echo $sContent == "" ? '<td>Not in CRM.</td>' : $sContent;
	?>
</table>
