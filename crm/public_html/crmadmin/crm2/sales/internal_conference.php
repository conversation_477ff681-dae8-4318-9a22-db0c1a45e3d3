<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'Howto: Teleconference';
	$iColSpan = 1;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td class="TablePadding">

<b>Example</b><br>
4 attendees. Test Customer and MM, TP, and MH<br>
<br>
MM will have a Teleconference with my Test customer, and want to have MH ( 621 ) and TP  ( 620 ) with me.<br>
<br>
<b>Step 1.</b><br>
<br>
MM start to call TP ( 620 ) When there is a connection, I ask him to wait a moment, as I have to bring in MH.<br>
<br>
<b>Step 2. </b><br>
<br>
Press the right point at the right butten. <br>
<br>
Display: Start conference. <br>
<br>
<b>Step 3.</b><br>
<br>
Press OK<br>
<br>
Display: Call up. <br>
<br>
I now call up <PERSON>H at 621<br>
<br>
<b>Step 4.</b><br>
<br>
Press the right point at the right butten.<br>
<br>
Display: conference.<br>
<br>
<b>Step 5.</b><br>
<br>
Press OK. <br>
<br>
Display: 3 attendee. <br>
<br>
<b>Step 6.</b><br>
<br>
Bring in the test customer: <br>
<br>
Press the right point at the right butten. <br>
<br>
Display: expand conference.<br>
<br>
Press Ok<br>
<br>
Call up customer<br>
<br>
Click right point at the right butten. <br>
<br>
Click right point at the left butten: Display done and return.<br>
<br>
Click OK at the right point at the right butten. <br>
<br>
Display: 4 attendee.<br>
<br>
If you need additional attendees, then just repeat from Step 6. <br>
<br>

		</td>
	</tr>
</table>