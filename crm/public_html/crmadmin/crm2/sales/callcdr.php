<?php
$aPersons = DBGetRows("salespeople", "person_id='".$_GET['person_id']."' AND display=1 AND department=2 AND person_level=3", "name ASC");
if (sizeof($aPersons) == 0) die();

if ($_GET['from']){
	$iFrom = strtotime($_GET['from']." 00:00:00");
} else {
	$iFrom = mktime(0, 0, 0)-2592000;
}

if ($_GET['to']){
	$iTo = strtotime($_GET['to']." 23:59:59");
} else {
	$iTo = mktime(23, 59, 59);
}

if ($_GET['person_id']){
	$iPerson = (int) $_GET['person_id'];
}

$rQuery = mysql_query("SELECT * FROM cdr_country ORDER BY country_probability DESC");
while($aCountry = mysql_fetch_array($rQuery)){
	$aCountries[] = array('code' => $aCountry['country_code'], 'country' => $aCountry['country_name']);
}

function fGetPersonName($iPersonID){
	$rQuery = mysql_query("SELECT name FROM salespeople WHERE person_id='".$iPersonID."'");
	if (mysql_num_rows($rQuery) == 0){
		return "N/A";
	} else {
		$aPersonData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
		return htmlspecialchars($aPersonData['name']);
	}
}

//2 is outgoing
//1 is incoming

function fGetCallStats($iPersonID, $iTypeID){
	$iSuccess = 0;
	$iFailed = 0;
	$iCallDuration = 0;
	$rQuery = mysql_query("SELECT local_number FROM salespeople WHERE person_id='".$iPersonID."'");
	$aPersonData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	if ($iTypeID == 1){
		//Incoming
		$rQuery = mysql_query("SELECT * FROM it_phone_cdr WHERE LENGTH(call_src) > 3 AND call_dst='".$aPersonData['local_number']."' AND call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	} elseif ($iTypeID == 2) {
		//Outgoing
		$rQuery = mysql_query("SELECT * FROM it_phone_cdr WHERE call_src='".$aPersonData['local_number']."' AND LENGTH(call_dst) > 3 AND call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	}
	while($aCallData = mysql_fetch_array($rQuery, MYSQL_ASSOC)){
		if ($aCallData['call_billsec'] == 0){
			//Unsuccessful
			$iFailed++;
		} else {
			//Successful
			$iSuccess++;
			$iCallDuration = $iCallDuration + $aCallData['call_billsec'];
		}
	}
	$aReturn['iFailed'] = $iFailed;
	$aReturn['iSuccessful'] = $iSuccess;
	$aReturn['iCallDuration'] = $iCallDuration;
	return $aReturn;
}

function fGetCalls($iPersonID){
	$rQuery = mysql_query("SELECT local_number FROM salespeople WHERE person_id='".$iPersonID."'");
	$aPersonData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	//Get outgoing
	$rQuery = mysql_query("SELECT call_id, call_date, call_src, call_dst, call_billsec FROM it_phone_cdr WHERE ((call_src='".$aPersonData['local_number']."' AND LENGTH(call_dst) > 3) OR (call_dst='".$aPersonData['local_number']."' AND LENGTH(call_src) > 3)) AND call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	while($aCallData = mysql_fetch_array($rQuery, MYSQL_ASSOC)){
		$aCalls[] = $aCallData;
	}
	return $aCalls;
}

function fSecondsToHuman($iSeconds){
	$iHours = floor($iSeconds/3600);
	$iBalance = $iSeconds-($iHours*3600);
	$iMinutes = floor($iBalance/60);
	$iSeconds = floor($iBalance-($iMinutes*60));
	return str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
}

function fGetCountryName($sPhone){
	if (substr($sPhone, 0, 1) == "+"){
		$iPhone = substr($sPhone, 1);
	} else {
		$iPhone = $sPhone;
	}
	
	foreach($GLOBALS['aCountries'] as $aCountry){
		if (substr($iPhone, 0, strlen($aCountry['code'])) == $aCountry['code']) return $aCountry['country'];
	}
}

function fGetCountryCode($sPhone){
	if (substr($sPhone, 0, 1) == "+"){
		$iPhone = substr($sPhone, 1);
	} else {
		$iPhone = $sPhone;
	}
	
	foreach($GLOBALS['aCountries'] as $aCountry){
		if (substr($iPhone, 0, strlen($aCountry['code'])) == $aCountry['code']) return $aCountry['code'];
	}
}

function fGetCountryNameFromCode($iCode){
	foreach($GLOBALS['aCountries'] as $aCountry){
		if ($aCountry['code'] == $iCode) return $aCountry['country'];
	}
}
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="7" width="100%">
		Phone Statistics
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="">
		<input type="hidden" name="page" value="callcdr">
		<input type="hidden" name="right" value="overview">
		<tr>
			<td colspan="2">
				<b>Select period to display</b><br>
				<input type="text" value="<?=strftime("%Y-%m-%d", $iFrom)?>" name="from" style="width: 150px;"> - <input type="text" value="<?=strftime("%Y-%m-%d", $iTo)?>" name="to" style="width: 150px;"><br>
			</td>
			<td colspan="3">
				<b>Select sales person</b><br>
				<select name="person_id"><?php
					$aPersons = DBGetRows("salespeople", "display=1 AND department=2 AND person_level=3", "name ASC");
					foreach($aPersons as $aPerson){
						if ($_GET['person_id'] == $aPerson['person_id']){
							echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\" selected>".htmlspecialchars($aPerson['name'])."</option>";
						} else {
							echo "<option value=\"".htmlspecialchars($aPerson['person_id'])."\">".htmlspecialchars($aPerson['name'])."</option>";
						}
					}
				?></select>
			</td>
		</tr>
	
		<tr>
			<td colspan="7" width="100%"><br></td>
		</tr>
		
		<tr>
			<td colspan="7" width="100%"><input type="submit" value="Display"></td>
		</tr>
	</form>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>

	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Summary Report</h2></b>
			<?php
			$aCallDataIn = fGetCallStats($iPerson, 1);
			$aCallDataOut = fGetCallStats($iPerson, 2);
			?>
		</td>
	</tr>
	<tr>
		<td colspan="7" valign="top">
			<table width="650" cellspacing="0" cellpadding="0">
				<tr>
					<td></td>
					<td align="right" width="150"><b>Incoming</b></td>
					<td align="right" width="150"><b>Outgoing</b></td>
				</tr>
				<tr>
					<td><b>No. of successful calls</b></td>
					<td align="right"><?=number_format($aCallDataIn['iSuccessful'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aCallDataOut['iSuccessful'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>No. of failed calls</b></td>
					<td align="right"><?=number_format($aCallDataIn['iFailed'], 0, ".", ","); ?></td>
					<td align="right"><?=number_format($aCallDataOut['iFailed'], 0, ".", ","); ?></td>
				</tr>
				<tr>
					<td><b>Total no. of calls</b></td>
					<td align="right"><?=number_format(($aCallDataIn['iSuccessful']+$aCallDataIn['iFailed']), 0, ".", ","); ?></td>
					<td align="right"><?=number_format(($aCallDataOut['iSuccessful']+$aCallDataOut['iFailed']), 0, ".", ","); ?></td>
				</tr>
				<tr><td><br></td></tr>
				<tr>
					<td><b>Total call duration:</b></td>
					<td align="right"><?=fSecondsToHuman($aCallDataIn['iCallDuration']); ?></td>
					<td align="right"><?=fSecondsToHuman($aCallDataOut['iCallDuration']); ?></td>
				</tr>
				<tr>
					<td><b>Average call duration (successfull calls):</b></td>
					<td align="right"><?=fSecondsToHuman($aCallDataIn['iCallDuration']/$aCallDataIn1['iSuccessful']); ?></td>
					<td align="right"><?=fSecondsToHuman($aCallDataOut['iCallDuration']/$aCallDataOut['iSuccessful']); ?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Country allocation</h2></b>
			<?php
			$aCalls = fGetCalls($iPerson);
			foreach($aCalls as $aCall){
				if ($aCall['call_src'] == "anonymous"){
					$sExternal = "N/A";
					$aCall['call_src'] = "N/A";
					$iCountryCode = 0;
				} else {
					if (strlen($aCall['call_src']) > 3){
						if (substr($aCall['call_src'], 0, 2) == "00"){
							$sExternal = "+".substr($aCall['call_src'], 2);
						} else {
							$sExternal = "+45".$aCall['call_src'];
						}
						$aCall['call_src'] = $sExternal;
					} else {
						if (substr($aCall['call_dst'], 0, 2) == "00"){
							$sExternal = "+".substr($aCall['call_dst'], 2);
						} else {
							$sExternal = "+45".$aCall['call_dst'];
						}
						$aCall['call_dst'] = $sExternal;
					}
					$iCountryCode = fGetCountryCode($sExternal);
				}
				$aCallNumbers[$iCountryCode] = $aCallNumbers[$iCountryCode]+1;
			}
			arsort($aCallNumbers);
			$aKeys = array_keys($aCallNumbers);
			foreach($aKeys as $sKey){
				if ($sKey == 0){
					$aCountriesList[] = "N/A";
				} else {
					$aCountriesList[] = fGetCountryNameFromCode($sKey);
				}
				$aValues[] = $aCallNumbers[$sKey];
			}
			$sData = "labels:".implode(",",$aCountriesList).";values:".implode(",",$aValues);
			echo "<img src=\"http://gfx.hstint/phone/?app=countrypie&data=".base64_encode($sData)."\">";
			?>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Call log</h2></b>
			<?php
			$aCalls = fGetCalls($iPerson);
			if (sizeof($aCalls) == 0){
				echo "There are no calls registered in this period for this extension.";
			} else {
				?>
				<table width="850" cellspacing="0" cellpadding="0">
					<tr>
						<td><b>Time</b></td>
						<td align="left"><b>Source</b></td>
						<td align="left"><b>Destination</b></td>
						<td align="left"><b>Country</b></td>
						<td align="right"><b>Duration</b></td>
						<td align="right"><b>ID</b></td>
					</tr>
					<?php
					foreach($aCalls as $aCall){
						if ($aCall['call_src'] == "anonymous"){
							$sExternal = "N/A";
							$aCall['call_src'] = "N/A";
							$sCountry = "N/A";
						} else {
							if (strlen($aCall['call_src']) > 3){
								if (substr($aCall['call_src'], 0, 2) == "00"){
									$sExternal = "+".substr($aCall['call_src'], 2);
								} else {
									$sExternal = "+45".$aCall['call_src'];
								}
								$aCall['call_src'] = $sExternal;
							} else {
								if (substr($aCall['call_dst'], 0, 2) == "00"){
									$sExternal = "+".substr($aCall['call_dst'], 2);
								} else {
									$sExternal = "+45".$aCall['call_dst'];
								}
								$aCall['call_dst'] = $sExternal;
							}
							$sCountry = fGetCountryName($sExternal);
						}
						?>
						<tr>
							<td><?=strftime("%Y-%m-%d %H:%M:%S", $aCall['call_date']);?></td>
							<td align="left"><?=htmlspecialchars($aCall['call_src']);?></td>
							<td align="left"><?=htmlspecialchars($aCall['call_dst']);?></td>
							<td align="left"><?=htmlspecialchars($sCountry);?></td>
							<td align="right"><?=fSecondsToHuman($aCall['call_billsec']);?></td>
							<td align="right"><?=htmlspecialchars($aCall['call_id']);?></td>
						</tr>
						<?php
					}
					?>
				</table>
				<?php
			}
			?>
		</td>
	</tr>
</table>
