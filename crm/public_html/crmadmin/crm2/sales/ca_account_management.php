<?php
// Set vars
$iVirus = 0;
$iAdvisory = 0;
$sUpdate = '';
$sPassword = '';
$iSuccess = 0;
$sComment = '';
$bAccountsUpdate = FALSE;
$bUsernameFailed = FALSE;
$bAdvisoryFailed = FALSE;
$bVirusFailed = FALSE;
$bESMFailed = FALSE;
$iModulesVisible = 0;
$iModulesEnabled = 0;
$iOptionsEnabled = 0;

// Set account_id
if ( !empty( $_GET['account_id'] ) ) {
	$iAccountID = $_GET['account_id'];
} elseif ( !empty( $_POST['account_id'] ) ) {
	$iAccountID = $_POST['account_id'];
} else {
	exit( 'Halting. Account ID required.' );
}

// Select 'account' and 'customer' details
$aAccountCustomer = DBGetRow('ca.accounts, crm.cst', "accounts.account_id = '" . $iAccountID . "' AND cst.cst_id = accounts.cst_id");

// Determine if CSI-5 is an option
// JB: Edit here, remove if and iDevices and make isCSI5Available default to true
$isCSI5Available = true;
//$iDevices = DBNumRows('ca.nsi_devices', "account_id = '" . (int) $iAccountID . "'");
//if ( !$iDevices && ( !$aAccountCustomer['special_limits'] || $aAccountCustomer['special_limits'] == 'csi_50' ) ) {
//	$isCSI5Available = true;
//}

function subAccountsSqlIn( $iAccountID ) {
	// Build the VIM stats.

	$children = getChildren( $iAccountID );

	if ( false === $children ) {
		return (int)$iAccountID;;
	}
	$sqlIn = implode( ",", $children );
	$sqlIn .= ( $sqlIn == "" ? "" : ", " ).(int)$iAccountID;

	return $sqlIn;
}

if ( isset($_GET['type']) && $_GET['type'] == 'vim_stats' ) {
	$sqlIn = subAccountsSqlIn( $iAccountID );
	$assetLists = DBNumRows("ca.vi_assets", "account_id IN ( ".$sqlIn." )");
	$recipients = DBNumRows("ca.contact_method", "account_id IN ( ".$sqlIn." ) AND contact_method_type = 1");
	echo "<br>Sub accounts: " . ( count( $children ) - 1 );
	echo "<br>Configured Asset List(s) - including sub accounts: " . $assetLists;
	echo "<br>Configured Recipient(s): " . $recipients;

	die();
}

// Modular Product Structure Setup
{
	// Modules
	$aModules = array(
		"Vulnerability Intelligence Feed (VIF) / <b>N/A</b> in VIM 4.0 &amp; VIM 3.0" => MOD_VTSE,
		"Enterprise Vulnerability Manager (EVM) / Management and Administration in VIM 4.0 &amp; VIM 3.0" => MOD_ESM,
		"User Management (UM)" => MOD_UM,
		"Vulnerability Manager (VM) / <b>N/A</b> in VIM 4.0 &amp; VIM 3.0" => MOD_SM,
		"Vulnerability Tracking (VTS) / Vulnerability Manager in VIM 4.0 &amp; VIM 3.0" => MOD_VTS,
		"Surveillance Scanner (SS)" => MOD_VSS,
		"Corporate Software Inspector (CSI)" => MOD_NSI,
		"Binary Analysis (BA) / <b>N/A</b> in VIM 4.0 &amp; VIM 3.0" => MOD_BA,
		"Vulnerability Database</b> (default module)" => MOD_VDB,
		"Technical Support</b> (default module)" => MOD_SUPPORT,
		"Account Information</b> (default module) / <b>N/A</b> in VIM 4.0 &amp; VIM 3.0" => MOD_ACCOUNT
	);
}

if ( !empty($_POST['modules_enabled']) || !empty($_POST['modules_visible']) )
{
	// Loop through modules Enabled
	while ( list($sModuleName, $iModuleValue) = each($aModules) )
	{
		// Enabled Modules
		if ( isset($_POST['modules_enabled'][$iModuleValue]) && $_POST['modules_enabled'][$iModuleValue] == 1 )
		{
			$iModulesEnabled |= $iModuleValue;
		}

		// Visible Modules
		if ( isset($_POST['modules_visible'][$iModuleValue]) && $_POST['modules_visible'][$iModuleValue] == 1 )
		{
			$iModulesVisible |= $iModuleValue;
		}
	}
}

// Select 'account' and 'customer' details
$aAccountCustomer = DBGetRow('ca.accounts, crm.cst', "accounts.account_id = '" . $iAccountID . "' AND cst.cst_id = accounts.cst_id");

// Verify owner - only the owner can edit
// TODO: Allow any rep to edit if is attached to a case/company card
//if ( $aAccountCustomer['person_id'] != $aRepData['person_id'] || !$aAccountCustomer['person_id'] || $aAccountCustomer['account_esm'] )
//{
//	echo 'You do not appear to be the creator of this account. Access is therefore restricted. <a href="javascript:history.back();">Return to customer</a>';
//	exit();
//}

// Select add-on details
{
	// EVM
	$aESM = DBGetRow('ca.esm', "esm.master_account_id = '" . $aAccountCustomer['account_id'] . "'");

	// Virus
	$aViruses = DBGetRows('ca.extra_virus', "extra_virus.cst_id = '" . $aAccountCustomer['cst_id'] . "'");
	while ( list($iKey, $aVirus) = each($aViruses) )
	{
		$iVirus += $aVirus['number'];
	}

	// Advisory contacts
	$aAdvisories = DBGetRows('ca.extra_contact', "extra_contact.account_id = '" . $aAccountCustomer['account_id'] . "'");
	while ( list($iKey, $aAdvisory) = each($aAdvisories) )
	{
		$iAdvisory += $aAdvisory['number'];
	}
}

// Save data
if ( isset( $_GET['s'] ) && $_GET['s'] == 1 )
{
	$account_options = 0;
	$account_options |= !empty( $_POST['OPT_ADVISORY_DEEP_LINKS'] ) ? OPT_ADVISORY_DEEP_LINKS : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_EXTENDED_DATA'] ) ? OPT_ADVISORY_EXTENDED_DATA : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_EXTENDED_DESCRIPTION'] ) ? OPT_ADVISORY_EXTENDED_DESCRIPTION : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_POC_DATA'] ) ? OPT_ADVISORY_POC_DATA : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ASSET_RECEIVE_ALL'] ) ? OPT_ASSET_RECEIVE_ALL : OPT_NONE;

	// Update 'accounts' table
	// If vim_40 then include saving with account options unchecked
	if ( !empty($_POST['new_username']) || !empty($_POST['new_name']) || !empty($_POST['new_expire'])
	   || !empty($_POST['new_password']) || !empty($_POST['new_product']) || $iModulesEnabled
	   || $iModulesVisible || !empty($_POST['account_company']) || 'vim_40' === $aAccountCustomer['special_limits']
	) {
		if ( !empty($_POST['new_username']) )
		{
			// Check username availaility
			if ( !count(DBGetRows('ca.accounts', "accounts.account_username = '" . $_POST['new_username'] . "'")) )
			{
				$sUpdate = "accounts.account_username = '" . trim($_POST['new_username']) . "'";
				$sComment = 'Changed username (' . htmlspecialchars($_POST['new_username']) . ')<br>';
			}
			else
			{
				$bUsernameFailed = true;
			}
		}

		if ( !empty($_POST['new_name']) ) {
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.account_name = '" . trim($_POST['new_name']) . "'";
			$sComment = 'Changed name (' . htmlspecialchars($_POST['new_name']) . ')<br>';
		}

		// Expires date - must be run on 'cst_id' not account_id, in order to affect sub-esm accounts too.
		if ( isset($_POST['new_expire']) && preg_match('^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$', $_POST['new_expire']) )
		{
			// Execute query here based on "cst_id" as it needs to affect any and all sub-esm accounts also
			DBQuery("UPDATE ca.accounts SET accounts.account_expires = '" . $_POST['new_expire'] . "' WHERE accounts.cst_id = '" . $aAccountCustomer['cst_id'] . "'");
			$sComment .= 'Expiry date changed (' . htmlspecialchars($_POST['new_expire']) . ')<br>';
		}

		// New Password
		if ( !empty($_POST['new_password']) )
		{
			// Generate random password
			$sPassword = rand(********,********);

			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.account_password = password('" . $sPassword . "'), accounts.account_gen_pwd = 1";
			$sComment .= 'Password reset<br>';
		}

		// New Module Product
		if ( $iModulesEnabled || $iModulesVisible )
		{
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.modules = '" . $iModulesEnabled . "', accounts.show_modules = '" . $iModulesVisible . "'";
			// now that show_modules is saved, update the current account/customer $var
			// so it's up to date when querying it for which visibility checkboxes are checked
			$aAccountCustomer['show_modules'] = $iModulesVisible;

			$sComment .= 'Product Modules changed<br>';

			// If new product module == MOD_ESM, then insert 5 esm accounts if not already there
			if ( $iModulesEnabled & MOD_ESM )
			{
				// Check if there already an entry in 'ca.esm'
				if ( !mysql_num_rows(DBQuery("SELECT * FROM ca.esm WHERE esm.master_account_id = '" . intval($aAccountCustomer['account_id']) . "' LIMIT 1")) )
				{
					DBQuery("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES(5, '" . intval($aAccountCustomer['account_id']) . "', '" . intval($aAccountCustomer['cst_id']) . "')");
				}
			}
		}

		if ( in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) ) {
			if ( !empty($_POST['account_company']) ) {
				$sUpdate .= ( $sUpdate ? ', ' : '' ) . " account_company = '".$_POST['account_company']."' ";
				$sComment .= "Account company name changed: ".$_POST['account_company']." <br/>";
			}

			if ( isset($_POST['xml_access']) && $_POST['xml_access'] == '' ) {
				$_POST['xml_access'] = 0;
			}
			// Update sub accounts Options
			DBQuery("UPDATE ca.accounts SET account_options = '".(int)$account_options."' WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )");

			// Update sub accounts XML access
			$xmlAccess = isset($_POST['xml_access']) ? (int)$_POST['xml_access'] : 0;
			DBQuery("UPDATE ca.accounts SET xml_access = '".$xmlAccess."' WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )");
		}

		// Execute Changes
		DBQuery("UPDATE ca.accounts SET " . $sUpdate . " WHERE accounts.account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
	}

	#####################################################
	# SAVE CHANGES TO `ca`.`modules` RELATED SELECTIONS #
	#####################################################
	if ( is_pint($aAccountCustomer['cst_id']) && is_pint($aAccountCustomer['account_id'] ) ) {
		//ob_start(); // make sure nothing comes before header
		$cstId = $aAccountCustomer['cst_id'];
		$accountId = $aAccountCustomer['account_id'];
		require_once INCLUDE_PATH . 'CommonModules.class.php';
		require_once INCLUDE_PATH . 'CommonModule.class.php';
		CommonModules::setConn( $GLOBALS[ DB_HOST . DB_USER ] ); // set MySQL conn to use

		$moduleIds = array();
		if ( isset($_POST['common_modules_enabled'][$cstId]) ) {
			//$moduleIds = array_filter( $_POST['common_modules_enabled'][$cstId], create_function( '$val', 'return is_pint($val);' ) ); // remove non-integers
			$moduleIds = array_filter( $_POST['common_modules_enabled'][$cstId], function( $val) { return is_pint($val); } );
		}
		// Save any changes to ca.module selection
		CommonModules::updateAccount( $aAccountCustomer, $moduleIds );
		// Update the module visibility for the Account
		//$bitmask = array_reduce( array_keys($_POST['modules_visible']), create_function( '$v, $k', 'return $v |= $k;'), 0 );
		$bitmask = array_reduce( array_keys($_POST['modules_visible']), function( $v, $k) { return $v |= $k; }, 0 );
		$bitmask &= 0x1FFFFFFFC000; // mask to ca.modules only
		if ( $bitmask > 0 ) {
			CommonModules::updateVisibility( $aAccountCustomer, $bitmask );
		}
	}
	// Update number of extra virus
	if ( isset($_POST['new_virus']) && is_numeric($_POST['new_virus']) )
	{
		// Check if customer is using more advisory contacts, than what is being set
		$iCount = (mysql_num_rows(DBQuery('ca.virus_contact', "virus_contact.account_id = '" . $aAccountCustomer['account_id'] . "'")) / 2) - 1;

		if ( $iCount <= $_POST['new_virus'] )
		{
			DBQuery("DELETE FROM ca.extra_virus WHERE extra_virus.cst_id = '" . $aAccountCustomer['cst_id'] . "'");
			DBQuery("INSERT INTO ca.extra_virus (cst_id, number) VALUES('" . $aAccountCustomer['cst_id'] . "', '" . intval($_POST['new_virus']) . "')");

			$sComment .= 'VI: Extra Virus Contact Changed (' . intval($_POST['new_virus']) . ')<br>';
		}
		else
		{
			$bVirusFailed = TRUE;
		}
	}

	// Update number of extra advisory
	if ( isset($_POST['new_advisory']) && is_numeric($_POST['new_advisory']) )
	{
		// Check if customer is using more virus contacts, than what is being set
		$iCount = (mysql_num_rows(DBQuery('ca.contact_method', "contact_method.account_id = '" . $aAccountCustomer['account_id'] . "'")) / 2) - 1;

		if ( $iCount <= $_POST['new_advisory'] )
		{
			DBQuery("DELETE FROM ca.extra_contact WHERE extra_contact.account_id = '" . $aAccountCustomer['account_id'] . "'");
			DBQuery("INSERT INTO ca.extra_contact (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . intval($_POST['new_advisory']) . "', 'ADV')");

			$sComment .= 'VI: Extra Advisory Contact Changed (' . intval($_POST['new_advisory']) . ')<br>';
		}
		else
		{
			$bAdvisoryFailed = TRUE;
		}
	}

	// Update ESM
	if ( isset($_POST['new_esm']) && is_numeric($_POST['new_esm']) )
	{
		// Check if customer is using more accounts, than what is being set
		$iCount = mysql_num_rows(DBQuery('ca.accounts', "accounts.cst_id = '" . $aAccountCustomer['cst_id'] . "'")) - 1;

		if ( $iCount <= $_POST['new_esm'] )
		{
			// Update or Insert
			if ( mysql_num_rows(DBQuery("SELECT * FROM ca.esm WHERE esm.master_account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1")) )
			{
				DBQuery("UPDATE ca.esm SET esm.no_users = '" . intval($_POST['new_esm']) . "' WHERE esm.master_account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
			}
			else
			{
				DBQuery("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES('" . intval($_POST['new_esm']) . "', '" . $aAccountCustomer['account_id'] . "', '" . $aAccountCustomer['cst_id'] . "')");
			}

			$sComment .= 'EVM/UM: Account Number Changed (' . intval($_POST['new_esm']) . ')<br>';
		}
		else
		{
			$bESMFailed = TRUE;
		}
	}

	// Update SS
	if ( !empty($_POST['ss_update']) )
	{
		// Delete all entries
		DBQuery("DELETE FROM ca.vss_scan_limits WHERE vss_scan_limits.account_id = '" . $aAccountCustomer['account_id'] . "'");

		// New entries (Daily, Weekly, Monthly)
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_daily_number'] . "', '1')");
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_weekly_number'] . "', '2')");
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_monthly_number'] . "', '3')");

		$sComment .= 'SS: Available Scan Slots (' . intval($_POST['new_ss_daily_number']) . ' ' . fSSFrequencyName(1) . ' / ' . intval($_POST['new_ss_weekly_number']) . ' ' . fSSFrequencyName(2) . ' / ' . intval($_POST['new_ss_monthly_number']) . ' ' . fSSFrequencyName(3) . ')<br>';
	}

	$ignore = false;
	// Only account type can be chosen at a time
	if ( !empty($_POST['csi_40']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_40' WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 4.0.<br>';
	} elseif ( !empty($_POST['csi_50']) && $isCSI5Available ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_50'  WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )" );
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 5.0.<br>';
	} elseif ( !empty($_POST['csi_60']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_60'  WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )" );
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 6.0.<br>';
	} elseif ( !empty($_POST['vim_40']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'vim_40' WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$sComment .= 'Account enabled for the Secunia VIM 4.0.<br>';
	} elseif ( !empty($_POST['vim_30']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'vim_30' WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$sComment .= 'Account enabled for the Secunia VIM 3.0.<br>';
	} elseif ( in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = NULL WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$sComment .= 'Account disabled for the Secunia VIM 3.0 and VIM 4.0.<br>';
	}

	// Update CSI Base Settings
/*	if ( $_POST['s_trial'] || $_POST['s_unique_hosts'] )
	{
		// Verify that we have an entry in this table - insert vs. update
		if ( mysql_num_rows(DBQuery("SELECT * FROM ca.nsi_base_settings WHERE nsi_base_settings.account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1")) )
		{
			// Update Base Settings
			DBQuery("UPDATE ca.nsi_base_settings SET nsi_base_settings.s_trial = '" . $_POST['s_trial'] . "', nsi_base_settings.s_unique_hosts = '" . $_POST['s_unique_hosts'] . "' WHERE nsi_base_settings.account_id = '" . $aAccountCustomer['account_id'] . "'");
		}
		else
		{
			DBQuery("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $aAccountCustomer['cst_id'] . "', '0', '" . $_POST['s_unique_hosts'] . "', '" . $_POST['s_trial'] . "')");
		}

		$sComment .= 'CSI: Trial / Host Licenses Changed (' . ( $_POST['s_trial'] ? 'Trial Account' : 'Customer Account' ). ' / ' . intval($_POST['s_unique_hosts']) . ')<br>';
	}*/

	// Save comment - if available
	if ( $sComment )
	{
		DBQuery("INSERT INTO crm.comments (comment, added, cst_id, person_id, type) VALUES('" . mysql_escape_string($sComment) . "', NOW(), '" . $aAccountCustomer['cst_id'] .  "', '" . $aRepData['person_id'] . "', '3')");
	}
}

// Redirect to customer
if ( isset( $_GET['s'] ) && $_GET['s'] == 1 )
{
	// If no errors or data to display
	if ( !$bUsernameFailed && !$bAdvisoryFailed && !$bVirusFailed && !$bESMFailed && !$sPassword ) {
		if ( $_GET['iframe'] == 1 ) {
			echo '<script>window.opener.location = window.opener.location; window.close();</script>';
//			header("Location: iframe_ca_accounts.php?cst_id=" . intval($_GET['cst_id']));
		} else {
			header("Location: ?page=customer&cst_id=" . intval($_GET['cst_id']));
		}
		exit();
	}
}

?>

<form method="POST" action="?page=ca_account_management&amp;s=1&amp;account_id=<?= intval($_GET['account_id']) ?>&amp;cst_id=<?= intval($_GET['cst_id']) ?>&amp;iframe=<?= ( $_GET['iframe'] ? 1 : 0 ) ?>">
<table width="100%" cellpadding="1" cellspacing="0">
	<?php
	$sTitle = 'CA - Account Administration';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

	if ( isset( $_GET['s'] ) && $_GET['s'] == 1 )
	{
		?>
		<tr>
			<td><?=( $bUsernameFailed ? 'Failed. The chosen username is not available. <a href="?page=ca_account_management&amp;account_id=' . intval($_GET['account_id']) . '&amp;cst_id=' . intval($_GET['cst_id']) . '">Try again</a> or' : 'Data Updated.' ) . ' <a href="?page=customer&amp;cst_id=' . intval($_GET['cst_id']) . '">Return to customer</a>.' ?></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<?php
		if ( $bAdvisoryFailed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>Advisory Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $bVirusFailed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>Virus Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $bESMFailed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>Sub-Accounts change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some accounts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $sPassword )
		{
			?>
			<tr>
				<td><b>New Password: <?= htmlspecialchars($sPassword) ?></b></td>
			</tr>
			<tr>
				<td><br></td>
			</tr>
			<?php
		}
	}
	else
	{
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><b>Customer</b></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Company Name</td>
		<td><a href="?page=customer&amp;cst_id=<?= $aAccountCustomer['cst_id'] ?>"><?= htmlspecialchars($aAccountCustomer['name']) ?></a></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Next Appointment</td>
		<td><?= htmlspecialchars($aAccountCustomer['appointment']) ?></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="35%"><b>Customer Area Details</b></td>
		<td width="30%"><b>Current Value</b></td>
		<td width="35%"><b>New Value</b></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Username</td>
		<td><?= htmlspecialchars($aAccountCustomer['account_username']) ?></td>
		<td><input type="text" name="new_username"></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Name</td>
		<td><?= htmlspecialchars($aAccountCustomer['account_name']) ?></td>
		<td><input type="text" name="new_name"></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Password</td>
		<td>*************</td>
		<td><input type="checkbox" name="new_password" style="width: 15px;"> Generate New Password</td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Expires</td>
		<td><?=htmlspecialchars($aAccountCustomer['account_expires'])?></td>
		<td><input type="text" name="new_expire"></td>
	</tr>
	<?php

	// If not SS, BA, and CSI
	if ( !($aAccountCustomer['modules'] & MOD_VSS) && !($aAccountCustomer['modules'] & MOD_BA) && !($aAccountCustomer['modules'] & MOD_NSI) && 1 == 2 )
	{
		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Extra Contacts</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Extra Advisory Contact</td>
			<td><?=intval($iAdvisory)?></td>
			<td><input type="text" name="new_advisory"></td>
		</tr>
		<?php
	}

	// Enable access to CSI 4.0
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><b>Access to:</b></td>
		<td><b>Current Value</b></td>
		<td><b>New Value</b> <span style="color: gray; font-size: 0.9em;">(Choose One Only)<span></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">CSI 4.0</td>
		<td><?= ( $aAccountCustomer['special_limits'] == 'csi_40' ? 'Yes' : 'No' ) ?></td>
		<td><label><input class="accounttypecb" type="checkbox" value="1" name="csi_40"<?= ( $aAccountCustomer['special_limits'] == 'csi_40' ? ' checked' : '' ) ?> <?= ( $aAccountCustomer['special_limits'] == 'csi_50' || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the CSI 4.0</label></td>
	</tr>
        <tr>
                <td style="padding-left: 3px;">CSI 5.0 (NOTE: Downgrade from CSI 5.0 is NOT possible)</td>
                <td><?= ( $aAccountCustomer['special_limits'] == 'csi_50' ? 'Yes' : 'No' ) ?></td>
                <td><label><input class="accounttypecb" type="checkbox" value="1" name="csi_50"<?= ( $aAccountCustomer['special_limits'] == 'csi_50' ? ' checked' : '' ) ?> <?= ( !$isCSI5Available || $aAccountCustomer['special_limits'] == 'csi_50' || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the CSI 5.0</label></td>
        </tr>
        <tr>
                <td style="padding-left: 3px;">CSI 6.0 Beta (NOTE: Downgrade from CSI 6.0 is NOT possible)</td>
                <td><?= ( $aAccountCustomer['special_limits'] == 'csi_60' ? 'Yes' : 'No' ) ?></td>
                <td><label><input class="accounttypecb" type="checkbox" value="1" name="csi_60"<?= ( $aAccountCustomer['special_limits'] == 'csi_60' ? ' checked' : '' ) ?> <?= ( !$isCSI5Available || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the <b>CSI 6.0</b></label></td>
        </tr>
        <tr>
                <td style="padding-left: 3px;">VIM 3.0</td>
                <td><?= ( $aAccountCustomer['special_limits'] == 'vim_30' ? 'Yes' : 'No' ) ?></td>
                <td><label><input class="accounttypecb" type="checkbox" value="1" name="vim_30"<?= ( $aAccountCustomer['special_limits'] == 'vim_30' ? ' checked' : '' ) ?>> Check this box, to enable access to VIM 3.0</label></td>
        </tr>
		<tr>
                <td style="padding-left: 3px;">VIM 4.0</td>
                <td><?= ( $aAccountCustomer['special_limits'] == 'vim_40' ? 'Yes' : 'No' ) ?></td>
                <td><label><input class="accounttypecb" type="checkbox" value="1" name="vim_40"<?= ( $aAccountCustomer['special_limits'] == 'vim_40' ? ' checked' : '' ) ?>> Check this box, to enable access to <b>VIM 4.0</b></label></td>
        </tr>
	<tr>
		<td><br></td>
	</tr>

	<?php if ( $aAccountCustomer['special_limits'] == 'vim_30' ) { ?>
	<tr>
		<td colspan=3>
			<b>VIM 3.0 Options</b>
		</td>
	</tr>
		<tr>
			<td><b>Option:</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Access to XML Feeds</td>
			<td><?= ( $aAccountCustomer['xml_access'] == 1 ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="xml_access" <?= ( $aAccountCustomer['xml_access'] == 1 ? "checked" : "" ) ?>> Check this box, to enable XML Feeds in the VIM 3.0 account</td>
		</tr>
		<tr>
			<td>Company Name</td>
			<td><?= htmlspecialchars( $aAccountCustomer['account_company'] ) ?></td>
 			<td><input type="text" name="account_company"></td>
		</tr>
		<tr>
			<td>View Deep Links</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_DEEP_LINKS" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View PoC Data</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_POC_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Solution</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Description</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DESCRIPTION" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Asset List Receive All</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ASSET_RECEIVE_ALL" <?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Statistics</td>
			<td><input type="button" onClick="window.open('?page=ca_account_management&type=vim_stats&account_id=<?= (int)$_GET['account_id']; ?>', 'VIM Account Statistics', 'height=500, width=500');" value="Click to View"></td>
			<td>&nbsp;</td>
		</tr>
	</tr>
	<?php } // END vim_30 ?>
	<?php if ( $aAccountCustomer['special_limits'] == 'vim_40' ) { ?>
	<tr>
		<td colspan=3>
			<b>VIM 4.0 Options</b>
		</td>
	</tr>
		<tr>
			<td><b>Option:</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Access to XML Feeds</td>
			<td><?= ( $aAccountCustomer['xml_access'] == 1 ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="xml_access" <?= ( $aAccountCustomer['xml_access'] == 1 ? "checked" : "" ) ?>> Check this box, to enable XML Feeds in the VIM 4.0 account</td>
		</tr>
		<tr>
			<td>Company Name</td>
			<td><?= htmlspecialchars( $aAccountCustomer['account_company'] ) ?></td>
 			<td><input type="text" name="account_company"></td>
		</tr>
		<tr>
			<td>View Deep Links</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_DEEP_LINKS" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View PoC Data</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_POC_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Solution</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Description</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DESCRIPTION" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Asset List Receive All</td>
			<td><?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "Yes" : "No" ) ?></td>
			<td><input type="checkbox" value="1" name="OPT_ASSET_RECEIVE_ALL" <?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Statistics</td>
			<td><input type="button" onClick="window.open('?page=ca_account_management&type=vim_stats&account_id=<?= (int)$_GET['account_id']; ?>', 'VIM Account Statistics', 'height=500, width=500');" value="Click to View"></td>
			<td>&nbsp;</td>
		</tr>
	</tr>
	<?php } // END VIM 4.0 ?>
	<?php
	// SS Options
	if ( $aAccountCustomer['modules'] & MOD_VSS )
	{
		// Select SS options
		$aSSDailyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 1");
		$aSSWeeklyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 2");
		$aSSMonthlyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 3");

		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Available Slots Settings</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Change Values</td>
			<td>-</td>
			<td><input type="checkbox" value="1" name="ss_update"> Check this box, to change values below</td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Daily Scan Slots</td>
			<td><?= number_format($aSSDailyDetails['number']) ?></td>
			<td><input type="text" value="<?= number_format($aSSDailyDetails['number']) ?>" name="new_ss_daily_number"></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Weekly Scan Slots</td>
			<td><?= number_format($aSSWeeklyDetails['number']) ?></td>
			<td><input type="text" value="<?= number_format($aSSWeeklyDetails['number']) ?>" name="new_ss_weekly_number"></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Monthly Scan Slots</td>
			<td><?= number_format($aSSMonthlyDetails['number']) ?></td>
			<td><input type="text" value="<?= number_format($aSSMonthlyDetails['number']) ?>" name="new_ss_monthly_number"></td>
		</tr>
		<?php
	}

	// ESM settings
	if ( $aAccountCustomer['modules'] & MOD_ESM || $aAccountCustomer['modules'] & MOD_UM || in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) )
	{
		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>EVM/UM Details</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Sub-Accounts</td>
			<td><?=intval($aESM['no_users'])?></td>
			<td><input type="text" name="new_esm"></td>
		</tr>
		<?php
	} ?>

	<?php
	// Include the modules from `ca`.`modules`
	include_once '_common_module_list.php'; ?>

		<!-- VIF/EVM Modules -->
		<tr>
			<td colspan="3"><br><!-- spacer --></td>
		</tr>
		<tr><td colspan="3" style="padding-top: 6px; line-height: 20px"></td></tr>
		<tr>
			<td><b>VIF/EVM Account Product Modules</b></td>
			<td><b>Enabled Modules</b></td>
			<td><b>Displayed Modules</b></td>
		</tr>
		<tr>
			<td colspan="3" style="height: 8px"></td>
		</tr>
		<?php
		$sBGColor = '#DFDFDF';
		// Loop Through Modules
		foreach( $aModules as $sModuleName => $iModuleValue )
		{
			// Change BG Color
			$sBGColor = ( $sBGColor != '#DFDFDF' ? '#DFDFDF' : '#FFFFFF' );



			// If there are no module restrictions or there are module restricts and the rep is in the restriction
			echo '
		<tr bgcolor="' . $sBGColor . '">
			<td><label for="cbEnable' . $iModuleValue . '">' . $sModuleName . '</label></td>
			<td>'
			. (
				( empty($aModuleRestrictionsEnable[$iModuleValue]) || ( $aModuleRestrictionsEnable[$iModuleValue] && in_array($aRepData['person_id'], $aModuleRestrictionsEnable[$iModuleValue]) ) )
				? ('<input type="checkbox" id="cbEnable' . $iModuleValue . '" name="modules_enabled[' . $iModuleValue . ']" value="1" style="width: 15px;"' . ( $aAccountCustomer['modules'] & $iModuleValue ? ' checked' : '' )
					. (
						// If there are no module restrictions or there are module restricts and the rep is in the restriction
						( empty( $aModuleRestrictions[$iModuleValue] )  || ( $aModuleRestrictions[$iModuleValue] && @in_array($aRepData['person_id'], $aModuleRestrictions[$iModuleValue]) )
							? ''
							: ' disabled'
						) . '>'
					  )
				  )
				: '<input type="hidden" name="modules_enabled[' . $iModuleValue . ']" value="' . ( $aAccountCustomer['modules'] & $iModuleValue ? '1"> Enabled' : '0"> Disabled' )
			) .
			'</td>
			<td><input type="checkbox" name="modules_visible[' . $iModuleValue . ']" value="1" style="width: 15px;"' . ( $aAccountCustomer['show_modules'] & $iModuleValue ? ' checked' : '' ) . ( empty($aModuleRestrictions[$iModuleValue]) || ( $aModuleRestrictions[$iModuleValue] && in_array($aRepData['person_id'], $aModuleRestrictions[$iModuleValue]) ) ? '' : ' disabled' ) . '></td>
		</tr>';
		}
		?>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td colspan="2"></td>
		<td><input type="submit" value="Submit Changes" class="submit"></td>
	</tr>
	<?php
	}
	?>
</table>
</form>
<script type="text/javascript">

	(function () { // Link the EVM & UM checkboxes together when on VIM 4.0
		// Create vars that reference the enable EVM and UM checkboxes
		var evmCheckbox = document.getElementsByName( "modules_enabled[2]" )[0];
		var umCheckbox = document.getElementsByName( "modules_enabled[2048]" )[0];
		/**
		 * Handles the onclick event for the VIF/EVM's enable EVM and UM checkboxes.
		 * This makes both checkboxes function as a single item if VIM 40 is selected.
		 * @param {Event} e Not required by IE
		 */
		function evmUmClicked( e ) {
			if ( !document.getElementsByName( "vim_40" )[0].checked ) return;
			evmCheckbox.checked = umCheckbox.checked = (e.target || event.srcElement).checked;
		}
		// Bind onclick handlers to the enable EVM and UM checkboxes
		if ( undefined !== evmCheckbox && undefined !== umCheckbox ) {
			if ( document.addEventListener ) {
				// Webkit + FF
				evmCheckbox.addEventListener( "click", evmUmClicked, false ); // EVM
				umCheckbox.addEventListener( "click", evmUmClicked, false ); // UM
			} else {
				// IE
				evmCheckbox.attachEvent( "onclick", evmUmClicked ); // EVM
				umCheckbox.attachEvent( "onclick", evmUmClicked ); // UM
			}
		}
	})();

	(function () { // Make the Account Type checkboxes act like Radio Buttons
		/**
		 * Handles the onclick event for the account type checkboxes
		 * This unchecks all the account type checkboxes except for the
		 * box that was the source of the event.
		 * This is to limit the account type selection to a single type without
		 * resorting to radio buttons which requires rewriting part of the code.
		 * @param {Event} e Not required by IE
		 */
		function accounttypeSelected( e ) {
			var checkbox = e.target || event.srcElement;
			var a = document.getElementsByTagName( "input" );
			for ( var i = 0, len = a.length; i <  len; i++ ) {
				if ( "checkbox" === a[i].type && "accounttypecb" === a[i].className ) {
					if ( checkbox !== a[i] ) {
						a[i].checked = false;
					}
				}
			}
			// Toggle display of old modules when account type is changed
			if ( undefined !== toggleOldModulesDisplay ) {
				toggleOldModulesDisplay( checkbox.name );
			}
		}
		// Bind onclick handlers to the account type checkboxes
		var a = document.getElementsByTagName( "input" );
		for ( var i = 0, len = a.length; i <  len; i++ ) {
			if ( "checkbox" === a[i].type && "accounttypecb" === a[i].className ) {
				if ( a[i].addEventListener) {
					// Webkit + FF
					a[i].addEventListener( "click", accounttypeSelected, false );
				} else {
					// IE
					a[i].attachEvent( "onclick", accounttypeSelected );
				}
			}
		}
	})();

</script>