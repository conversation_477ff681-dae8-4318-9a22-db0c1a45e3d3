<?php
// Registers
$aShown = array();
$aOutputBuffer = array();

// Clean 'limit' input
$sLimit = '';
$sLimitURL = '';
if ( $_GET['limit'] ) {
	while ( list($iKey, $sCard) = each($_GET['limit']) ) {
		$sLimit .= "'" . preg_replace('[^a-z_]*', '', $sCard) . "',";
		$sLimitURL .= '&amp;limit[]=' . preg_replace('[^a-z_]*', '', $sCard);
	}
	$sLimit = trim($sLimit, ',');
}

// Get a list of customers
$aCustomers = DBGetRows('crm.cst', "cst.person_id = '" . ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) && $_GET['person_id'] ? $_GET['person_id'] : $aRepData['person_id'] ) . "'" . ( $sLimit ? " AND case_name IN(" . $sLimit . ")" : '' ), 'cst.name');


// Loop through customers
while ( list($iKey, $aCustomer) = each($aCustomers) ) {
	// No Dupes
	if ( $aShown[ ( $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'] ) ] ) {
		continue;
	}
	$aShown[ ( $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'] ) ] = true;

	// Containers
	$iTotalEuro = 0;
	$iTotalPaidEuro = 0;
	$iLogins = 0;
	$sVIExpires = '';
	$sNSIExpires = '';
	$sLastLogin = '';
	$sActiveAccounts = '';
	$iActiveAccounts = 0;

	// Customer Details
	$aCustomerDetails = fGetCustomerDetails( $aCustomer['cst_id'] );

	// Select all sales
	$aSales = DBGetRows('crm.saleslog', "saleslog.cst_id in (" . $aCustomerDetails['AllIDs'] . ") AND (saleslog.product_price-saleslog.discount) > 0 AND ( ( saleslog.status != 6 AND saleslog.status != 7 AND saleslog.status != 3 ) OR saleslog.status IS NULL )", 'saleslog.sale_id');
	while ( list($iKey, $aSale) = each($aSales) ) {
		// Convert to EURO
		$iAmountEuro = round( ( ($aSale['product_price']-$aSale['discount']) * $aSale['currency_exchange_rate'] ) / $aSale['currency_exchange_rate_euro'] );

		// Total
		$iTotalEuro += $iAmountEuro;

		// Paid
		if ( $aSale['status'] == 2 )
		{
			$iTotalPaidEuro += $iAmountEuro;
		}

		// Register expires date
		if ( $aSale['product_category'] == 1 && $aSale['expires_date'] ) {
			$sVIExpires = $aSale['expires_date'];
		} elseif ( $aSale['product_category'] == 3 && $aSale['expires_date'] ) {
			$sNSIExpires = $aSale['expires_date'];
		}
	}

	// Check if any EURO were sold to customer
	if ( $iTotalEuro < 1 ) {
		// No euro
		continue;
	}

	// Select primary account for this CST
	$aAccounts = DBGetRows('ca.accounts', "accounts.cst_id in (" . $aCustomerDetails['AllIDs'] . ")", 'accounts.last_login DESC');
	while ( list($iKey, $aAccount) = each($aAccounts) )
	{
		if ( $sLastLogin === '' ) {
			$sLastLogin = $aAccount['last_login'];
		}

		// Aggregate list of active accounts
		if ( !$aAccount['account_esm'] && strtotime($aAccount['account_expires']) >= time() ) {
			$sActiveAccounts .= $aAccount['account_expires'] . " (" . ++$iActiveAccounts . ")<br>";
		}

		// Select logins for account
		$iLogins += DBNumRows('ca.usage_logins', "usage_logins.account_id = '" . $aAccount['account_id'] . "'");
	}

	// Determine sort operator
	switch ( $_GET['sort']  )
	{
		case 'number_sales':
			$mSort = str_pad(count($aSales), 3, 0, STR_PAD_RIGHT) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'total_amount':
			$mSort = $iTotalEuro . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'paid':
			$mSort = round(($iTotalPaidEuro/$iTotalEuro) * 100) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'appointment':
			$mSort = strtotime($aCustomer['appointment']) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'vi_expires':
			$mSort = strtotime($sVIExpires) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'nsi_expires':
			$mSort = strtotime($sNSIExpires) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'last_login':
			$mSort = strtotime($sLastLogin) . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		case 'logins':
			$mSort = $iLogins . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
			break;
		default:
			$mSort = $aCustomer['name'] . str_pad($aCustomer['cst_id'], 6, 0, STR_PAD_RIGHT );
	}

	// Add content to output buffer
	$aOutputBuffer[$mSort] = array
	(
		'name' => $aCustomerDetails['Company']['name'],
		'number_sales' => count($aSales),
		'total_euro' => $iTotalEuro,
		'total_paid_euro' => $iTotalPaidEuro,
		'appointment' => $aCustomer['appointment'],
		'cst_id' => $aCustomer['cst_id'],
		'last_login' => $sLastLogin,
		'logins' => $iLogins,
		'vi_expires' => ( strtotime($sVIExpires) < time() ? '<font color="red">' . $sVIExpires . '</font>' : ( (strtotime($sVIExpires)-time()) < 7776000 ? '<font color="orange">' . $sVIExpires . '</font>' : $sVIExpires ) ),
		'nsi_expires' => ( strtotime($sNSIExpires) < time() ? '<font color="red">' . $sNSIExpires . '</font>' : ( (strtotime($sNSIExpires)-time()) < 7776000 ? '<font color="orange">' . $sNSIExpires . '</font>' : $sNSIExpires ) ),
		'active_accounts' => $sActiveAccounts
	);
}

// Sort output buffer
ksort($aOutputBuffer);

$sBaseURL = '?page=customer_report' . $sLimitURL . '&amp;';
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'Customer Report';
	$iColSpan = 10;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td class="TablePadding" colspan="10">
<form METHOD="GET" action="?">
<input type="hidden" name="page" value="customer_report">
Limit display, show only customers where you are on the:
<input type="checkbox" name="limit[]" value="card_vi"<?= ( in_array('card_vi', $_GET['limit']) ? ' checked' : '' ) ?>> VI
<input type="checkbox" name="limit[]" value="card_nsi"<?= ( in_array('card_nsi', $_GET['limit']) ? ' checked' : '' ) ?>> CSI
<input type="checkbox" name="limit[]" value="card_crc"<?= ( in_array('card_crc', $_GET['limit']) ? ' checked' : '' ) ?>> CRC
<input type="checkbox" name="limit[]" value="card_partner"<?= ( in_array('card_partner', $_GET['limit']) ? ' checked' : '' ) ?>> Partner
<input type="submit" value="Update Report"><br><br>
</form>
		</td>
	</tr>

	<tr>
		<td width="17%" class="TableSubHeadline TableGreyBackground TablePadding"><a href="<?= $sBaseURL ?>">Company</a></td>
		<td width="8%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=number_sales">Number of Sales</a></td>
		<td width="8%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=total_amount">Total Amount</a></td>
		<td width="7%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=paid">Paid %</td>
		<td width="7%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=vi_expires">VI Expires</a></td>
		<td width="8%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=nsi_expires">CSI Expires</a></td>
		<td width="8%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;">Accounts Expires</td>
		<td width="11%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=last_login">Last Login</a></td>
		<td width="6%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=logins"># Logins</a></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 5px;"><a href="<?= $sBaseURL ?>sort=appointment">Next Appointment</a></td>
	</tr>

	<?php
	// Loop through customers
	while ( list($iKey, $aCustomer) = each($aOutputBuffer) )
	{
		// Determine background
		$sClass = ( $sClass === '' ? ' class="TableGreyBackground"' : '' );
		?>
		<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aCustomer['cst_id'] . '&right=overview') ?>>
			<td valign="top" class="TablePadding"><?= htmlspecialchars($aCustomer['name']) ?></td>
			<td valign="top" class="NumberCell"><?= $aCustomer['number_sales'] ?></td>
			<td valign="top" class="NumberCell"><?= number_format($aCustomer['total_euro'], 2) ?></td>
			<td valign="top" class="NumberCell"><?= round(($aCustomer['total_paid_euro']/$aCustomer['total_euro']) * 100) ?>%</td>
			<td valign="top" class="NumberCell"><?= ( $aCustomer['vi_expires'] ? $aCustomer['vi_expires'] : '-' ) ?></td>
			<td valign="top" class="NumberCell"><?= ( $aCustomer['nsi_expires'] ? $aCustomer['nsi_expires'] : '-' ) ?></td>
			<td valign="top" class="NumberCell"><?= ( $aCustomer['active_accounts'] ? $aCustomer['active_accounts'] : '<font color="grey">- no act. accounts -</font>' ) ?></td>
			<td valign="top" class="NumberCell"><?= htmlspecialchars($aCustomer['last_login']) ?></td>
			<td valign="top" class="NumberCell"><?= number_format($aCustomer['logins']) ?></td>
			<td valign="top" class="NumberCell"><?= htmlspecialchars($aCustomer['appointment']) ?></td>
		</tr>
		<?php
	}
	?>
</table>

<br>
<br>
<br>
<br>
