<?php
// Access Level: 1
// Admin
// SM
// Lead Management
fVerifyAccess( $aRepData['person_id'], 1 );


?>

<form method="post" action="">
<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Lead Flow';
	$iColSpan = 5;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td class="TablePadding">
			<b>Select Sales Person</b><br>
			<select name="person_id">
				<option>- Select Sales Person -</option>
				<?php
				$aReps = DBGetRows('crm.salespeople', 'display = 1 && canvas_segment_id > 0', 'name');
				while ( list($iKey, $aRep) = each($aReps) ) {
					echo '<option value="' . $aRep['person_id'] . '">' . $aRep['name'] . '</option>';
				}
				?>
			</select><br>
			<br>
			<b>Or, Select Team</b><br>
			<label><input type="radio" name="team" value="all" <?= ( $_POST['team'] == 'all' ? 'checked' : '' ) ?>> All teams</label><br>
			<label><input type="radio" name="team" value="1" <?= ( $_POST['team'] == '1' ? 'checked' : '' ) ?>> VI</label><br>
			<label><input type="radio" name="team" value="2" <?= ( $_POST['team'] == '2' ? 'checked' : '' ) ?>> CSI</label><br>
			<label><input type="radio" name="team" value="3" <?= ( $_POST['team'] == '3' ? 'checked' : '' ) ?>> Partner</label><br>
			<br>
			<input type="submit" value="Generate Report">
		</td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>

	<?php
	if ( $_POST ) {

		// Get rep. details (if needed)
		$sSelectString = '';
		if ( $_POST['person_id'] > 0 ) {
			$aRep = DBGetRow('crm.salespeople', "person_id = '" . $_POST['person_id'] . "'");
			$sSelectString = 'WHERE person_id = ' . $aRep['person_id'];
		} elseif ( $_POST['team'] ) {
			// Check product id
			if ( is_numeric($_POST['team']) ) {
				$aReps = DBGetRows('crm.salespeople', "product = '" . $_POST['team'] . "'");
				while ( list($iKey, $aRep) = each($aReps) ) {
					$sSelectString .= $aRep['person_id'] . ',';
				}
				$sSelectString = 'WHERE person_id IN(' . trim($sSelectString, ',') . ')';
			}
		}

		// Start value
		$iStartValue = 'n/a';
		$iTotalLost = 'n/a';

		// Head line
		$sTitle = 'Report for ' . ( $aRep['name'] ? $aRep['name'] : 'the selected team' );
		$iColSpan = 5;
		$bPrint = false;
		$bWindowTitle = false;
		include INCLUDE_PATH . 'page_header.php';
		?>

	<tr>
		<td class="TablePadding TableSubHeadline TableGreyBackground">Year-Month</td>
		<td class="TableSubHeadline TableGreyBackground">Leads Start</td>

		<td class="TableSubHeadline TableGreyBackground">Leads End</td>
		<td class="TableSubHeadline TableGreyBackground">Lost</td>
		<td class="TableSubHeadline TableGreyBackground">Assigned</td>
	</tr>

		<?php
	
		// Select rep. leads usage data
		$sQuery = 'SELECT logged, sum(total_leads) AS total_leads, sum(lost_leads) AS lost_leads FROM crm.sales_rep_lead_usage ' . $sSelectString . ' GROUP BY logged ORDER BY logged';
		$aUsageRows = DBQueryGetRows($sQuery);
		while ( list($iKey, $aUsage) = each($aUsageRows) ) {
			list($iYear, $iMonth) = split('-', $aUsage['logged']);
			?>
	<tr>
		<td class="TablePadding"><?= date('Y F', mktime(0,0,0,$iMonth,-15,$iYear)) ?></td>
		<td class=""><?= $iStartValue ?></td>
		<td class=""><?= $aUsage['total_leads'] ?></td>
		<td class=""><?= ( is_numeric($iTotalLost) ? $aUsage['lost_leads'] - $iTotalLost : 'n/a' ) ?></td>
		<td class=""><?= ( is_numeric($iTotalLost) ? (($iStartValue - $aUsage['total_leads']) - ($aUsage['lost_leads'] - $iTotalLost)) * -1 : 'n/a' ) ?></td>
	</tr>
			<?php
			// Add to total
			$iTotalLostLeads += ( is_numeric($iTotalLost) ? $aUsage['lost_leads'] - $iTotalLost : '-' );
			$iTotalAssiLeads += ( is_numeric($iTotalLost) ? (($iStartValue - $aUsage['total_leads']) - ($aUsage['lost_leads'] - $iTotalLost)) * -1 : '-' );

			// Values for "next" month
			$iStartValue = $aUsage['total_leads'];
			$iTotalLost = $aUsage['lost_leads'];
		}
		?>

	<tr>
		<td class="TablePadding"><b>Total</b></td>
		<td><b>-</b></td>
		<td><b>-</b></td>
		<td><b><?= number_format($iTotalLostLeads) ?></b></td>
		<td><b><?= number_format($iTotalAssiLeads) ?></b></td>
	</tr>

		<?php
	}
	?>
</table>
</form>
<br><br>
