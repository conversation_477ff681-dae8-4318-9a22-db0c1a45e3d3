<?php

// Function for returning comment box
function fCommentBox( $iType, $iPersonID, $sAdded, $sComment )
{
	// Get name and color of comment box
	$aNameColor = fReturnCommentBoxNameColor( $iType );

	return '
			<table width="100%" cellspacing="0" cellpadding="2" style="border: 1px solid #000000; background: ' . $aNameColor[1] . ';">
				<tr>
					<td style="border-bottom: 1px solid #000000;">' . $sAdded . ' - ' . $aNameColor[0] . ' - ' . fReturnRepDetailFromID( $iPersonID ) . '</td>
				</tr>
				<tr>
					<td>' . nl2br(wordwrap(strip_tags($sComment), 70, '<wbr> ', true)) . '</td>
				</tr>
			</table>';
}

function fReturnCommentBoxNameColor ( $iType )
{
	// Comment Name and Color
	switch ( $iType )
	{
		case 1:
			$sName = 'Research re. IT &amp; IT Organisation';
			$sColor = '#EAFFD0';
			break;
		case 2:
			$sName = 'Customer Relation';
			$sColor = '#FFE7E7';
			break;
		case 3:
			$sName = 'Set Up &amp; Solution Feedback';
			$sColor = '#FFF8E4';
			break;
		case 4:
			$sName = 'Sales; Buying Signals, Gains &amp; Avoids';
			$sColor = '#DBF0FF';
			break;
		default:
			$sName = 'N/A';
			$sColor = '#FFFFFF';
	}

	return array($sName, $sColor);
}

// Function for returning name of CA module
function fModuleName( $iModule )
{
	switch ( $iModule )
	{
		case MOD_NONE:
			return '';
		case MOD_SM:
			return 'Vulnerability Manager';
		case MOD_ESM:
			return 'Enterprise Vulnerability Manager';
		case MOD_VTS:
			return 'Vulnerability Tracking Service';
		case MOD_VTSE:
			return 'Vulnerability Intelligence Feed';
		case MOD_VSS:
			return 'Surveillance Scanner';
		case MOD_NSI:
			return 'Corporate Software Inspector';
		case MOD_VDB:
			return 'Vulnerability Database';
		case MOD_VWA:
			return 'Virus and Worm Alerting';
		case MOD_SUPPORT:
			return 'Technical Support';
		case MOD_ACCOUNT:
			return 'Account information';
		case MOD_BA:
			return 'Binary Analysis';
		case MOD_UM:
			return 'User Management';
	}
}

// Function for returning name of CA module
function fReturnProductTypeName( $iProductType )
{
	switch ( $iProductType )
	{
		case MOD_NONE:
			return '';
		case MOD_SM:
			return 'Security Manager';
		case MOD_ESM:
			return 'Enterprise Security Manager';
		case MOD_VTS:
			return 'Vulnerability Tracking Service';
		case MOD_VTSE:
			return 'Vulnerability Tracking Service - Enterprise Edition';
		case MOD_VSS:
			return 'Surveillance Scanner';
		case MOD_NSI:
			return 'Network Software Inspector';
	}
}

// Function for returning SS frequency name
function fSSFrequencyName( $iType )
{
	// Frequency Name
	switch ( $iType )
	{
		case 1:
			return 'Daily';
		case 2:
			return 'Weekly';
		case 3:
			return 'Monthly';
	}
}

// Return customer state
function fCustomerBasicDetails( $iCSTID, $aDetails )
{
	// Result array
	$aResult = array();

	// Check if customer
	$aSales = DBGetRow('crm.saleslog', "saleslog.cst_id in (" . $aDetails['AllIDs'] . ") AND (saleslog.status != 3 || saleslog.status is null) AND (product_price-discount) > 0 AND saleslog.expires_date >= now()");
	if ( $aSales )
	{
		$aResult['customer'] = true;
	}

	// Master country
	$aCountry = DBGetRow('crm.countries', "countries.id = '" . $aDetails['Company']['invoice_country'] . "'");
	$aResult['country'] = $aCountry['country'];
	$aResult['region'] = $aCountry['region'];

	// Return result
	return $aResult;
}

// Generate array of SS targets
function fGenerateSSTargetsArray( $sTargets )
{
	// Explode by ','
	$aTargets = explode(',', $sTargets);

	// Loop over result
	while ( list($iKey, $sTarget) = each($aTargets) )
	{
		$sTarget = trim($sTarget);
		if ( preg_match('^([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)([0-9]{1,3})-([0-9]{1,3})$', $sTarget, $aMatches) )
		{
			// Check values
			if ( $aMatches[3] > 254 )
			{
				$aMatches[3] = 254;
			}
			if ( $aMatches[2] > 254 )
			{
				$aMatches[2] = 254;
			}
			if ( $aMatches[3] < 1 )
			{
				$aMatches[3] = 1;
			}
			if ( $aMatches[2] < 1 )
			{
				$aMatches[2] = 1;
			}

			// Parse range
			for ( $iCount = $aMatches[2] ; $iCount <= $aMatches[3] ; $iCount++ )
			{
				$aResult[$aMatches[1] . $iCount] = $aMatches[1] . $iCount;
			}
		}
		else
		{
			if ( $sTarget )
			{
				$aResult[$sTarget] = $sTarget;
			}
		}
	}

	return $aResult;
}

function fGenerateLicenseKey($sChars)
{
	$key = '';
	$ncRows = 0;

	// Loop until a unique key is generated
	do {
		for ( $i=1; $i<=25; $i++ ) {
			$key .= $sChars[rand(0, strlen($sChars) - 1)];
			if ( $i % 5 == 0 && $i != 25 ) {
				$key .= "-";
			}
		}
		$ncRows = DBNumRows("license_keys", "license = '" . $key . "'");
	} while ($ncRows);
	return $key;
}

// Function for generating random IDs
function GenerateRandomID($len)
{
	// Numbers (48-57)
	for ( $i=48 ; $i<=57 ; $i++ )
	{
		$chars[] = chr($i);
	}

	// UC Letters (65-90)
	for ( $i=65; $i<=90 ; $i++ )
	{
		$chars[] = chr($i);
	}

	// LC Letters (97-122)
	for ( $i=97 ; $i<=122 ; $i++ )
	{
		$chars[] = chr($i);
	}

	// Make id
	$id = '';
	for ( $i=0 ; $i<$len ; $i++ )
	{
		srand ((double) microtime() * 948625);
		$id .= $chars[rand(0, count($chars)-1)];
	}

	// Return ID
	return $id;
}






?>
