<?php
$sJavascript = "";
if ( isISA( $aRepData['person_id'] ) ){
	// IF the user IS ISA and CARD HAS NO OWNER and has CLICKED on the CSI case card THEN assign him as owner
	// Addition to the above: SET AN APPOINTMENT CALLBACK TIME STAMP ( CUR_DATE 00:00:00 )
	if ( ( $GLOBALS['aCustomer']['Case']['person_id'] == "" ) || ( $GLOBALS['aCustomer']['Case']['person_id'] == 0 ) ){
		DBQuery("UPDATE crm.cst SET person_id = '".(int) $aRepData['person_id']."', callback_timestamp=NOW(), appointment = date(NOW()) WHERE cst_id = '".(int) $_GET['cst_id']."'");
		DBQuery("UPDATE crm.cst SET person_id = '".(int) $aRepData['person_id']."', callback_timestamp=NOW(), appointment = date(NOW()) WHERE master_id = '".(int) $_GET['cst_id']."'");
	}
	$sJavascript = "if ( doCheckDate() == true ) { return ".(($aRepData['department'] == 2) ? "fTestNSIFormData" : "fTestFormDataTLA")."( true ); } else { return false; }";
	} else {
	$sJavascript = "return ".($aRepData['department'] == 2) ? "fTestNSIFormData" : "fTestFormDataTLA"."( true );";
}
?>
<form method="POST" action="?page=save_customer_data" onSubmit="<?= $sJavascript ?>">
<input type="hidden" name="cst_id" value="<?= intval($_GET['cst_id']) ?>">
<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'CSI: ' . htmlspecialchars($aCustomer['Company']['name']);
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="49%" valign="top">
			<?php
			echo fIncludeCardBox( 'Company Details', 'c_company_details.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Contacts', 'c_contacts.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Sold Products', 'c_sold_products.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Online Sales', 'c_online_sales.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Trials', 'c_trials.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Customer Area Accounts', 'c_ca_accounts.php' );

			echo '<br>';
			?>
		</td>
		<td width="2%" background="yellow"></td>
		<td width="49%" valign="top">
			<div align="right">
				<input type="submit" value="Save" name="save_button"> <input type="submit" value="Save - View Today" name="save_button">&nbsp;&nbsp;
			</div>
			<?php
			if (($aRepData['person_level'] == 3 || $aRepData['person_level'] == 2) && $aRepData['department'] == 2){
				echo fIncludeCardBox( 'Lead Qualification', 'c_lead_qualify.php' );
				//Temporarily show the stage box until training days are over!
				echo '<br>';
				echo fIncludeCardBox( 'Current Stage', 'c_nsi_stage.php' );
			} else {
				echo fIncludeCardBox( 'Current Stage', 'c_nsi_stage.php' );
				
				if ($aRepData['department'] == 2){
					echo '<br>';
					echo fIncludeCardBox( 'Lead Qualification', 'c_lead_qualify.php' );
				}
			}

			echo '<br>';

			echo fIncludeCardBox( 'Next Appointment', 'c_next_appointment.php' );

			echo '<br>';
			echo fIncludeCardBox( 'Add Comment', 'c_add_comment.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Comments', 'c_comments.php' );

			echo '<br>';
			?>
			<div align="right">
				<input type="submit" value="Save" name="save_button"> <input type="submit" value="Save - View Today" name="save_button">&nbsp;&nbsp;
			</div>
		</td>
	</tr>
</table>
</form>
