<?php

/**
 * @file _common_module.php
 * @requires CommonModule.class.php
 * @requires CommonModules.class.php
 * This file outputs a list of ca.module row and is included by ca_account_management.php
 */

require_once INCLUDE_PATH . 'CommonModule.class.php';
require_once INCLUDE_PATH . 'CommonModules.class.php';
// Assign a db connection to work with
CommonModules::setConn( $GLOBALS[ DB_HOST . DB_USER ] );
?>

	</table>
	<!-- Common DB Modules -->
	<table width="100%" cellpadding="1" cellspacing="0">
		<tr>
			<td colspan="3"><br><br><!-- spacer --></td>
		</tr>
		<tr>
			<td colspan="3" class="TableHeadline">
				Modules<br>
			</td>
		</tr>
		<tr>
			<td colspan="3" style="height: 8px"></td>
		</tr>
		<?php
	$toggleDivId = 0; // id used for setting toggleable DIV's id
	$cstId = $aAccountCustomer['cst_id'];
	$modules = CommonModules::getAllIncludingStatus( $cstId, $iAccountID );

	#########################
	# LIST CUSTOMER MODULES #
	#########################
	$odd = 1; // used for odd row colouring
	//$customerModules = array_filter( $modules, create_function( '$v', 'return $v->getAppliesTo() & CommonModules::APPLIES_TO_CUSTOMER;' ) );
	$customerModules = array_filter( $modules, function($v) {return $v->getAppliesTo() & CommonModules::APPLIES_TO_CUSTOMER;});
	if ( count($customerModules) ) :
		?>

		<tr>
			<td><b>Customer Modules</b> <span style="color: gray; font-size: 0.9em;">(These apply to all of the Customer's Accounts)<span></td>
			<td><b>Enabled</b></td>
			<td><b>Displayed</b></td>
		</tr>
		<tr>
			<td colspan="3" style="height: 8px"></td>
		</tr>
		<?php
		foreach ( $customerModules as $module ) {
			include '_common_module.php';
		}
		?>

		<tr>
			<td colspan="3"><br><!-- spacer --></td>
		</tr>
	<?php endif; // END $customerModules

	#########################
	# LIST ACCOUNT MODULES  #
	#########################
	$odd = 1; // used for odd row colouring
	//$accountModules = array_filter( $modules, create_function( '$v', 'return $v->getAppliesTo() & CommonModules::APPLIES_TO_ACCOUNT;' ) );
	$accountModules  = array_filter( $modules, function($v) {return $v->getAppliesTo() & CommonModules::APPLIES_TO_ACCOUNT;});
	if ( count($accountModules) ) :
		?>

		<tr>
			<td><b>Account Modules</b> <span style="color: gray; font-size: 0.9em;">(These apply to a single Account)<span></td>
			<td><b>Enabled</b></td>
			<td><b>Displayed</b></td>
		</tr>
		<tr>
			<td colspan="3" style="height: 8px"></td>
		</tr>
		<?php
		foreach ( $accountModules as $module ) {
			include '_common_module.php';
		}
		?>

		<tr>
			<td colspan="3"><br><!-- spacer --></td>
		</tr>
	<?php endif; // END $accountModules

