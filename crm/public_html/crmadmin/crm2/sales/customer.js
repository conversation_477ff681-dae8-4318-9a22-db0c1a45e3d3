// Test if submitted data is compliant
var iForecastExpectancy = 0;
function fTestVIFormData( bDontTestAppointment ) {
	if ( iForecastExpectancy >= 60 && parseInt(document.getElementById('forecast_amount').value) == 0 ) {
		alert('ERROR: You need to enter an amount.');
		return false;
	}

	if ( !bDontTestAppointment && !fTestAppointment() ) {
		alert('The entered appointment time cannot be used. Please correct appointment time before saving.');
		return false;
	}

	return true;
}

function fLeftTrim( value ){
	var re = /\s*((\S+\s*)*)/;
	return value.replace(re, "$1");
}

function fRightTrim( value ){
	var re = /((\s*\S+)*)\s*/;
	return value.replace(re, "$1");
}

function fTrim( value ){
	return fLeftTrim(fRightTrim(value));
}

function fTestNSIFormData( bDontTestAppointment ) {
	if ( iForecastExpectancy >= 75 && parseInt(document.getElementById('forecast_amount').value) == 0 ) {
		alert('ERROR: You need to enter an amount.');
		return false;
	}

	if ( !bDontTestAppointment && !fTestAppointment() ) {
		alert('The entered appointment time cannot be used. Please correct appointment time before saving.');
		return false;
	}
	
	if ( document.getElementById('saveapproval').checked ){
		//Marked as save for approval, check content
		if ( !document.getElementById('fdecimaker_identified').checked ){
			alert('Please identify the financial decision maker!');
			return false;
		}
		
		if ( !document.getElementById('tdecimaker_identified').checked ){
			alert('Please identify the technical decision maker!');
			return false;
		}
		
		if ( fTrim(document.getElementById('createwant').value) == "" ){
			alert('Please fill out "Create want"');
			return false;
		}
		
		if ( !document.getElementById('snapshot_done').checked ){
			alert('Please make sure snapshot is completed');
			return false;
		}
		
		if ( fTrim(document.getElementById('resultwalkthrough').value) == "" ){
			alert('Please fill out the result of the walkthrough');
			return false;
		}
	}

	return true;
}

function fTestFormDataTLA( bDontTestAppointment ) {
	element = document.forms[0].forecast_expectancy;
	for(i=0; i < element.length; i++){
		if (element[i].checked){
			iForecastExpectancy = element[i].value;
		}
	}

	if ( iForecastExpectancy >= 60 && document.forms[0].category_clients.value == '0' && document.forms[0].category_servers.value == '0') {
		alert('ERROR: You need to set either clients or servers to a value higher than 0');
		return false;
	}
	
	if ( iForecastExpectancy >= 60 && document.forms[0].company_industry.value == '- Select Industry -' ) {
		alert('ERROR: You need to select industry');
		return false;
	}
	
	if ( iForecastExpectancy >= 60 && document.forms[0].financial_year_start.value == '00-00' ) {
		alert('ERROR: You need to fill out financial year');
		return false;
	}
	
	if ( iForecastExpectancy >= 60 && document.forms[0].budget_start.value == '00-00' ) {
		alert('ERROR: You need to fill out budget cycle');
		return false;
	}
	
	if ( iForecastExpectancy >= 60 && parseInt(document.getElementById('forecast_amount').value) == 0 ) {
		alert('ERROR: You need to enter an amount.');
		return false;
	}

	if ( !bDontTestAppointment && !fTestAppointment() ) {
		alert('The entered appointment time cannot be used. Please correct appointment time before saving.');
		return false;
	}
	
	return true;
}

// Test if an in valid appointment was entered AND no lost reason was selected
function fTestAppointment() {
	var sAppointment = document.getElementById('appointment').value;
	var bLost = false;
	for ( var iC = 0 ; iC < document.forms[document.forms.length-1].lost_reason.length ; iC++ ) {
		if ( document.forms[document.forms.length-1].lost_reason[iC].checked ) {
			bLost = true;
		}
	}

	if ( ( sAppointment.length == 0 || sAppointment == '0000-00-00 00:00:00' ) && !bLost ) {
		return false;
	}
	return true;
}

// Toggle calendar layer
function fToggleCalendar( oElement, oLink ) {
	if ( oElement.style.display == 'none' ) {
		oElement.style.display = 'block';
		oLink.innerHTML = 'Hide calendar';
	} else {
		oElement.style.display = 'none';
		oLink.innerHTML = 'View calendar';
	}
}

// Toggle New License Key
function fToggleLicenseKey( oElement, oLink, iAccountID ) {
	if ( oElement.style.display == 'none' ) {
		document.getElementById('lk_account_id').value = iAccountID;
		oElement.style.display = 'block';
		oElement.style.top = (tempY+15);
		oElement.style.left = tempX;
		oLink.innerHTML = 'Cancel add License Key';
	} else {
		document.getElementById('lk_account_id').value = 0;
		oElement.style.display = 'none';
		oLink.innerHTML = 'Add License Key';
	}
}

// Toggle CA New Account
function fToggleNewAccount( oElement, oLink )
{
	if ( oElement.style.display == 'none' )
	{
		oElement.style.display = 'block';
		oLink.innerHTML = 'Cancel new account';
	}
	else
	{
		oElement.style.display = 'none';
		oLink.innerHTML = 'New account';
	}
}

// Display comment box
function fDisplayCommentField( iType )
{
	document.getElementById('comment_' + iType).style.display = 'block';
	document.getElementById('comment_field_' + iType).focus();
}

// Add new contact
function fNewContact()
{
	// Get HTML content
	var sContent = document.getElementById('contact_template').innerHTML;

	// Increase counter
	++iContactCount;

	// Replace ##COUNTER## in sContent
	sContent = sContent.replace(/##COUNTER##/g, iContactCount);

	// Append content
	document.getElementById('new_contacts').innerHTML += sContent;
	
}

// Display next stage
function fDisplayNextStage( eLink )
{
	// Remove "stage 1"
	document.getElementById('stage_1').style.display = 'none';

	// Display "stage 2"
	document.getElementById('stage_2').style.display = 'block';

	// Update JS variable to "stage 2"
	iCurrentStage = 2;

	// Remove link to next stage
	eLink.innerHTML = '';
}

// Toggle lost layer
function fToggleLost( eLink )
{
	// Get object
	var oElement = document.getElementById('lost');

	if ( oElement.style.display == 'none' )
	{
		oElement.style.display = 'block';
		eLink.innerHTML = 'Cancel Lost Reason';
	}
	else
	{
		oElement.style.display = 'none';
		eLink.innerHTML = 'Lost &gt;&gt;';
	}
}

// Display or hide 'next contact' for a lost reason
function fChangeLostNextContact ( bDisplay )
{
	if ( bDisplay == true && document.getElementById('lost_keep_appointment').style.display == 'none' )
	{
		document.getElementById('lost_next_contact').style.display = 'block';
	}
	else
	{
		document.getElementById('lost_next_contact').style.display = 'none';
	}
}

// Display or hide 'next contact' for a lost reason
function fChangeLostNextAppointment ( bDisplay )
{
	if ( bDisplay == true )
	{
		document.getElementById('lost_keep_appointment').style.display = 'block';
	}
	else
	{
		document.getElementById('lost_keep_appointment').style.display = 'none';

		// Check if "Qualified; no budget" or "Qualified; competing product" is selected
		if ( document.getElementById('lost_reason_5').checked == true || document.getElementById('lost_reason_6').checked == true )
		{
			fChangeLostNextContact( true );
		}
	}
}

function fToggleForecast ( bDisplay )
{
	if ( bDisplay )
	{
		document.getElementById('forecast').style.display = 'block';
	}
	else
	{
		document.getElementById('forecast').style.display = 'none';
		document.getElementById('forecast_amount').value = '0';
		document.getElementById('forecast_date').value = '';
	}
}
function validDate( sDate ){ 
	var aTemp = sDate.split(' ');
	if ( aTemp.length != 2 ) {
		return false;
	}
	aTemp = aTemp[0].split('-');
	if ( aTemp.length != 3 ){
		return false;
	}
	if ( ( aTemp[0].length != 4 ) || ( aTemp[1].length != 2 ) || ( aTemp[2].length != 2 ) ){
		return false; 
	} 
	return true;
}

function doCheckDate(){
	var sAppointmentFiled = document.getElementById('appointment').value;
	if ( sAppointmentFiled == "" ){
		return true;
	}
	if ( validDate( sAppointmentFiled ) == false ) {
		alert('Invalid date');
		return false;
	}
//	var sFutureDate = new Date();
//	sFutureDate.setDate( sFutureDate.getDate() + 30 );
//	var aTemp = sAppointmentFiled.split(' ');
//	var aTemp = aTemp[0].split('-');
//	var aTempDate = new Date();
//	aTempDate.setFullYear( aTemp[0], aTemp[1] - 1, aTemp[2] );
//	if ( aTempDate > sFutureDate ){
//		alert('You can only set and appointment date which is within the next 30 days.');
//		return false;
//	} else {
//		return true;
//	}
	return true;
}
