<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	exit();
}

// Cache?
if ( !file_exists(CACHE_DIR . 'admin_segment_overview_2.cache') || $_GET['refresh_cache'] || ( $_GET['trash'] == "on" ) ) {
	if ( $_GET['trash'] == "on" ){
		$iTrashSegmentId = -1;
		$sHideTrashSegment = "";
	} else {
		$iTrashSegmentId = 1481;
		$sHideTrashSegment = " AND segment_id <> ".$iTrashSegmentId;
	}
	// Build array of special CRM2 segments, that don't fit in the "Country->Size" segmentation
	$aSpecialSegments = array();
	$aSegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' ".$sHideTrashSegment." AND segments.segment_name NOT LIKE '% - Canvas' AND segments.segment_name NOT LIKE '% - Lost Leads' AND segments.segment_status = 0", 'segments.segment_name');
	while ( list($iKey, $aSegment) = each($aSegments) ) {
		$aSpecialSegments[$aSegment['segment_id']]['name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);
		$aSpecialSegments[$aSegment['segment_id']]['count'] = 0;
		$aSpecialSegments[$aSegment['segment_id']]['total'] = 0;
		if ( $aSegment['parent_segment_id'] > 0 ) {
			$aSpecialSegments[$aSegment['parent_segment_id']]['child'][$aSegment['segment_id']] = true;
		}
	}

	// Select data
	$sQuery = "SELECT cst_id, invoice_country, region, large_account, master_id, segment_id, region FROM crm.cst LEFT JOIN crm.countries ON ( cst.invoice_country = countries.id ) WHERE cst.case_name = 'Company Card' AND (segment_id != ".$iTrashSegmentId." || segment_id IS NULL)";
	$rCustomers = DBQuery($sQuery);
	$iCustomers = mysql_num_rows($rCustomers);

	// Loop through results and sort according to segments
	while ( $aCustomer = mysql_fetch_array($rCustomers) ) {
		$aCustomer['invoice_country'] = intval($aCustomer['invoice_country']);

		// Detailed count
		// Total
		$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['total']++;

		// "Free" / available
		$iActive = DBNumRows('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name IN ('card_vi', 'card_crc', 'card_nsi', 'card_partner') AND cst.person_id > 0");
		if ( $aSpecialSegments[$aCustomer['segment_id']] && $iActive == 0 ) {
			$aSpecialSegments[$aCustomer['segment_id']]['count']++;
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		} elseif ( $aSpecialSegments[$aCustomer['segment_id']] ) {
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		} elseif ( $iActive == 0 && fSegmentNameFromID( $aCustomer['segment_id'] ) != 'n/a' ) {
			// Free - but assigned
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free_assigned']++;
			$aRegionCountFreeAssigned[$aCustomer['region']]++;
			$aCountryCountFreeAssigned[$aCustomer['invoice_country']]++;
		} elseif ( $iActive == 0 ) {
			// Free
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free']++;
			$aRegionCountFree[$aCustomer['region']]++;
			$aCountryCountFree[$aCustomer['invoice_country']]++;
		}

		// Country count
		$aCountryCount[$aCustomer['invoice_country']]++;

		// Region count
		$aRegionCount[$aCustomer['region']]++;
	}

	// Only cache results without the trash items
	if ( $_GET['trash'] != "on" ){
	// Cache results
	$sCache = '
<?php
$iCustomers = "' . $iCustomers . '";
$aRegions = unserialize(\'' . serialize($aRegions) . '\');
$aRegionCount = unserialize(\'' . serialize($aRegionCount) . '\');
$aRegionCountFreeAssigned = unserialize(\'' . serialize($aRegionCountFreeAssigned) . '\');
$aRegionCountFree = unserialize(\'' . serialize($aRegionCountFree) . '\');
$aCountryCount = unserialize(\'' . serialize($aCountryCount) . '\');
$aCountryCountFree = unserialize(\'' . serialize($aCountryCountFree) . '\');
$aCountryCountFreeAssigned = unserialize(\'' . serialize($aCountryCountFreeAssigned) . '\');
$aSpecialSegments = unserialize(\'' . serialize($aSpecialSegments) . '\');
?>
';

		// Save cache
		file_put_contents(CACHE_DIR . 'admin_segment_overview_2.cache', $sCache);
	}
} else {
	// Load file cache
	include CACHE_DIR . 'admin_segment_overview_2.cache';
	$iCache = filemtime( CACHE_DIR . 'admin_segment_overview_2.cache' );
}
?>

<?php
// Get all segments and their children
$result = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' ".$sHideTrashSegment." AND segments.segment_name NOT LIKE '% - Canvas' AND segments.segment_name NOT LIKE '% - Lost Leads' AND segments.segment_status = 0 AND ( segments.parent_segment_id = 0 OR segments.parent_segment_id IS NULL )", 'segments.segment_name');

$total = 0;
function buildSegments( $result, $segments = array(), &$free = null, &$assigned = null ) {
	global $total;
	for ( $i = 0; $i < count( $result ); $i++ ) {
		$segments[$result[$i]['segment_id']] = $result[$i];

		// 'Free' customers
		$sQuery = "SELECT cst_id, invoice_country, region, large_account, master_id, segment_id, region FROM crm.cst LEFT JOIN crm.countries ON ( cst.invoice_country = countries.id ) WHERE cst.case_name = 'Company Card' AND segment_id = ".(int)$result[$i]['segment_id']." AND ( person_id IS NULL OR person_id = 0 )";
		$rCustomers = DBQuery( $sQuery );
		$iFreeCustomers = mysql_num_rows( $rCustomers );

		// 'Assigned' customers
		$sQuery = "SELECT cst_id, invoice_country, region, large_account, master_id, segment_id, region FROM crm.cst LEFT JOIN crm.countries ON ( cst.invoice_country = countries.id ) WHERE cst.case_name = 'Company Card' AND segment_id = ".(int)$result[$i]['segment_id']." AND person_id > 0";
		$rCustomers = DBQuery( $sQuery );
		$iAssignedCustomers = mysql_num_rows( $rCustomers );

		$total += $iFreeCustomers + $iAssignedCustomers;

		$segments[$result[$i]['segment_id']]['free'] = $iFreeCustomers;
		$segments[$result[$i]['segment_id']]['assigned'] = $iAssignedCustomers;
		if ( $free != null && $assigned != null ) { // Increment parent segment counts
			$free += $segments[$result[$i]['segment_id']]['free'];
			$assigned += $segments[$result[$i]['segment_id']]['assigned'];
		}

		$subsegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' ".$sHideTrashSegment." AND segments.segment_name NOT LIKE '% - Canvas' AND segments.segment_name NOT LIKE '% - Lost Leads' AND segments.segment_status = 0 AND segments.parent_segment_id = ".(int)$result[$i]['segment_id'], 'segments.segment_name');
		if ( count( $subsegments ) > 0 ) {
			$segments[$result[$i]['segment_id']]['children'] = buildSegments( $subsegments, array(), $segments[$result[$i]['segment_id']]['free'], $segments[$result[$i]['segment_id']]['assigned'] );
		}
	}

	return $segments;
}

function formatOutput( $segments, $child = false, $k = 0 ) {
	$content = "";

	// Begin table
	$content .= "<table width=\"100%\" style=\"padding: 0px;\">";
	if ( $child == false ) {
		$content .= "<tr style=\"background: rgb(222, 222, 222) none repeat scroll 0% 0%; -moz-background-clip: border; -moz-background-origin: padding; -moz-background-inline-policy: continuous;\">
			<td width=\"100%\">
				<div>
					<table width=\"730\">
						<tr>
							<td style='width: 300px;'>
								<b>Segment
							</td>
							<td style='width: 100px; text-align: right;'>
								<b>Total</b>
							</td>
							<td style='220px; text-align: right;'>
								<b>Free</b>
							</td>
							<td style='100px; text-align: right;'>
								<b>Assigned</b>
							</td>
						</tr>
					</table>
				</div>
			</td>
		</tr>";
	}

	foreach ( $segments as $segment_id => $segment ) {
		$subContent = "";
		$subSegments = "";
		if ( is_array( $segment['children'] ) ) {
			$subContent .= formatOutput( $segment['children'], true, $k + 10 );
			foreach ( $segment['children'] as $_segment_id => $temp ) {
				$subSegments .= ( $subSegments != "" ? "," : "" ) . "\"segment_".(int)$_segment_id."\"";
			}
			$subSegments = "[ ".$subSegments."] ";
		} else {
			if ( $child == false ) {
				continue;
			}
		}

		// Add content
		$content .= "<tr>
			<td width=\"100%\" ".( $child == true ? "style='cursor: pointer; display: block;' name='segment_".(int)$segment['segment_id']."' id='segment_".(int)$segment['segment_id']."' ".( $subContent != "" ? " style='cursor: pointer;' " : "" ) : ( $subContent != "" ? " style='cursor: pointer;' " : "" ) ).">
				<div " . ( $subContent != "" ? "onClick='display(".$subSegments.");' " : "" ).">
					<table width=\"730\">
						<tr>
							<td style='width:400px;'>
								".( $subContent == "" ? "<a href=\"?page=admin_segment_overview_assign&segment_id=".(int)$segment_id."\">" : "<b>" ).htmlspecialchars( str_replace('CRM2 - ', '', $segment['segment_name'] ) ).( $subContent == "" ? "</a>" : "</b>" )."
							</td>
							<td style='width: 100px; text-align: right;'>
								".( $subContent == "" ? (int)( $segment['free'] + $segment['assigned'] ) : "" )."
							</td>
							<td style='width: 130px; text-align: right;'>
								".( $subContent == "" ? (int)$segment['free'] : "" )."
							</td>
							<td style='width: 260px; text-align: right;'>
								".( $subContent == "" ? (int)$segment['assigned'] : "" )."
							</td>
						</tr>
					</table>
				</div>
		".$subContent."</td></tr>";
	}

	// End table
	$content .= "</table>";

	return $content;
}

?>
<script>
function display(obj) {
	for ( var i = 0; i < obj.length; i++ ) {
		if ( typeof document.getElementById( obj[i] ) !== "undefined" ) {
			if ( document.getElementById( obj[i] ).style.display == 'block' ) {
				document.getElementById( obj[i] ).style.display = 'none';
			} else {
				document.getElementById( obj[i] ).style.display = 'block';
			}
		}
	}
}
</script>
<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$content = "<table width=\"800\"><tr><td width=\"800\">".formatOutput( buildSegments( $result ) )."</td></tr></table>";
	// Head line
	$sTitle = 'Segment Overview (' . number_format($total) . ' Cards)';
	$iColSpan = 5;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<?php
	include 'admin_include_search.php';
	?>

	<tr>
		<td style="padding-left: 25px; border-top: 1px solid #000000;">
			<?php
				echo $content;
			?>
		</td>
	</tr>
</table>
