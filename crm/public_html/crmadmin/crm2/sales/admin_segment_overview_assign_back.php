<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) )
{
	exit();
}

// Save - Change: Segment, Trash-it, Country
if ( $_GET['save'] == 1 ) {
	while ( list($iCustomerID, $aValues) = each($_POST['company']) ) {
		// Update segment
		if ( $aValues['segment'] == 1 ) {
			DBQuery("UPDATE crm.cst SET cst.segment_id = '" . $_POST['segment_id'] . "', cst.segment_assigned = NOW() WHERE cst.cst_id = '" . $iCustomerID . "'");
		}

		// Trash lead?
		if ( $aValues['trash'] == 1 ) {
			DBQuery("UPDATE crm.cst SET cst.segment_id = 1367, cst.segment_assigned = NOW() WHERE ( cst.cst_id = '" . $iCustomerID . "' || master_id = '" . $iCustomerID . "' )");
		}

		// Update country?
		if ( $aValues['country'] > 0 ) {
			DBQuery("UPDATE crm.cst SET cst.invoice_country = '" . intval($aValues['country']) . "' WHERE ( cst.cst_id = '" . $iCustomerID . "' || master_id = '" . $iCustomerID . "' )");
		}
	}

	echo "Data updated successfully.";
	exit();
} elseif ( $_GET['save'] == 2 ) { // Save - Change: Appointment + Ownership
	DBQuery("UPDATE crm.cst SET cst.appointment = '" . $_POST['appointment'] . "', person_id = '" . $_POST['new_rep'] . "' WHERE cst_id = '" . $_POST['case_id'] . "' LIMIT 1");
} elseif ( $_GET['save'] == 3 ) { // Save - Change: Create Partner Case
	echo "Case created.";
	DBQuery("INSERT INTO crm.cst (master_id, case_name) VALUES('" . $_GET['master_id'] . "', '" . $_GET['case'] . "')");
	exit();
}

// Build country select options
$sCountryOptions = '';
$aCountries = DBGetRows('crm.countries', '', 'countries.country');
while ( list($iKey, $aCountry) = each($aCountries) ) {
	$sCountryOptions .= '<option value="' . $aCountry['id'] . '">' . $aCountry['country'] . '</option>';
}

// Select data
if ( $_GET['segment_id'] > 0 && !$_GET['special_segment'] ) {
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' && segment_id = '" . $_GET['segment_id'] . "' ORDER BY cst.name";
} elseif ( $_GET['segment_id'] > 0 ) {
	// Select all lost cases
	$sQuery = "SELECT * FROM crm.cst WHERE segment_id = '" . $_GET['segment_id'] . "' ORDER BY cst.name";

	// Retrieve cases
	$aCases = DBQueryGetRows($sQuery);

	// Loop through cases and build string of CST_ID's
	$sCSTIDs = '';
	while ( list($iKey, $aCase) = each($aCases) ) {
		$sCSTIDs .= ( $aCase['master_id'] ? $aCase['master_id'] : $aCase['cst_id'] ) . ',';
	}

	// Get customer cards
	$sQuery = "SELECT * FROM crm.cst WHERE cst_id in (" . trim($sCSTIDs, ',') . ") ORDER BY cst.name";

} elseif ( $_GET['task'] == 'appointments' ) {
	// Select appointment type
	if ( $_GET['type'] == 'active' ) {
		$aCustomers = DBGetRows('crm.cst', "appointment > '" . date('Y-m-d') . "' AND appointment NOT LIKE '% 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	} elseif ( $_GET['type'] == 'expired' ) {
		$aCustomers = DBGetRows('crm.cst', "appointment < '" . date('Y-m-d') . "' AND appointment != '0000-00-00 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	} else {
		$aCustomers = DBGetRows('crm.cst', "appointment >= '" . date('Y-m-d') . "' AND appointment like '% 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	}

	// Loop through cases and build string of CST_ID's
	$sCSTIDs = '';
	while ( list($iKey, $aCustomer) = each($aCustomers) ) {
		$sCSTIDs .= ( $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'] ) . ',';
	}
	
	// Get customer cards
	$sQuery = "SELECT * FROM crm.cst WHERE cst_id in (" . trim($sCSTIDs, ',') . ") ORDER BY cst.name";


} elseif ( !$_GET['search'] ) {
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' " . ( $_GET['country_id'] > 0 ? "AND cst.invoice_country = '" . $_GET['country_id'] . "'" : ' AND ( !cst.invoice_country OR cst.invoice_country IS NULL )' ) . " AND (cst.segment_id != 1367 OR cst.segment_id IS NULL) ORDER BY cst.name";
} else {
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' && ( cst.name like '%" . $_GET['search'] . "%' || cst.alias like '%" . $_GET['search'] . "%' || cst.cst_id = '" . $_GET['search'] . "') ORDER BY cst.name";
}

$aCustomers = DBQueryGetRows($sQuery);

// Loop through results and sort according to segments
while ( list($iKey, $aCustomer) = each($aCustomers) ) {
	// Case details
	$aVI = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_vi'");
	$aNSI = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_nsi'");
	$aCRC = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_crc'");
	$aPartner = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_partner'");

	// Limited ?
	if ( $_GET['type'] == 'free' ) {
		// Include if both VI and NSI are "free"
		if ( !$aVI['person_id'] && !$aNSI['person_id'] && !$aPartner['person_id'] ) {
			$aResults[$aCustomer['cst_id']] = $aCustomer;
		} else {
			// Skip
			continue;
		}
	} else {
		// All
		$aResults[$aCustomer['cst_id']] = $aCustomer;
	}

	// Case details
	$aResults[$aCustomer['cst_id']]['case_vi_person'] = fReturnRepDetailFromID($aVI['person_id']);
	$aResults[$aCustomer['cst_id']]['case_vi_appointment'] = $aVI['appointment'];
	$aResults[$aCustomer['cst_id']]['case_vi_id'] = $aVI['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_nsi_person'] = fReturnRepDetailFromID($aNSI['person_id']);
	$aResults[$aCustomer['cst_id']]['case_nsi_appointment'] = $aNSI['appointment'];
	$aResults[$aCustomer['cst_id']]['case_nsi_id'] = $aNSI['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_partner_person'] = fReturnRepDetailFromID($aPartner['person_id']);
	$aResults[$aCustomer['cst_id']]['case_partner_appointment'] = $aPartner['appointment'];
	$aResults[$aCustomer['cst_id']]['case_partner_id'] = $aPartner['cst_id'];

	// Latest "Lost reason"
	{
		// VI
		$aVILostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aVI['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_id'] = $aVILostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_text'] = $aVILostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_time'] = $aVILostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_vi_lost_person_id'] = $aVILostReason[0]['person_id'];

		// NSI
		$aNSILostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aNSI['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_id'] = $aNSILostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_text'] = $aNSILostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_time'] = $aNSILostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_person_id'] = $aNSILostReason[0]['person_id'];

		// CRC
		$aCRCLostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aCRC['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_id'] = $aCRCLostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_text'] = $aCRCLostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_time'] = $aCRCLostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_crc_lost_person_id'] = $aCRCLostReason[0]['person_id'];

		// Partner
		$aPartnerLostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aPartner['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_id'] = $aPartnerLostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_text'] = $aPartnerLostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_time'] = $aPartnerLostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_partner_lost_person_id'] = $aPartnerLostReason[0]['person_id'];
	}
}

?>

<form method="POST" action="?page=admin_segment_overview_assign&save=1">
<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Segment Overview / Assign Company Cards';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td>&nbsp;&nbsp;<a href="javascript:history.back();">Back</a></td>
	</tr>

	<?php
	// Result
	$aRepData['person_id'] = (int) $_GET['person_id'];
	while ( list($iCustomerID, $aData) = each($aResults) ) {
		if ( fSkipCanvasLead( $aData, $aRepData ) && $_GET['canvas_leads'] ) {
			continue;
		}

		// Segment Name
		$sSegmentName = fSegmentNameFromID( $aData['segment_id'] );
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 5px;"><?= ++$iCount ?>. <label><input type="checkbox" name="company[<?= $iCustomerID ?>][segment]" value="1"><b><a href="?page=customer&amp;cst_id=<?= $iCustomerID ?>"><?= htmlspecialchars($aData['name']) ?></a></b></label> <font color="<?= $sSegmentName != 'n/a' ? 'red' : 'blue' ?>"><?= $sSegmentName ?></font></td>
			<td><b>Trash Customer: <input type="checkbox" name="company[<?= $iCustomerID ?>][trash]" value="1"> | Country: <select name="company[<?= $iCustomerID ?>][country]"><option value="0"> - Select new country - </option><?= $sCountryOptions ?></select></td>
		</tr>
		<tr>
			<td width="25%" style="padding-left: 25px; border-top: 1px solid #000000;" bgcolor="#DEDEDE"><a href="?page=customer&amp;cst_id=<?= $aData['case_vi_id'] ?>">VI: <?= htmlspecialchars($aData['case_vi_person']) ?></a> <?= $aData['case_vi_id'] ? '[<a href="javascript:void(0);" onClick="fDisplayOwnershipBox(' . $aData['case_vi_id'] . ', \'' . htmlspecialchars($aData['case_vi_appointment']) . '\');">Ownership</a>]' : '[<a href="?page=admin_segment_overview_assign&amp;save=3&amp;master_id=' . $iCustomerID . '&amp;case=card_vi" onclick="return confirm(\'Please confirm that you wish to create a VI case on this company\');">Create Case</a>]' ?></td>
			<td width="25%" bgcolor="#DEDEDE" style="border-top: 1px solid #000000;"><?= htmlspecialchars($aData['case_vi_appointment']) ?>&nbsp;</td>
			<td width="50%" bgcolor="#DEDEDE" style="border-top: 1px solid #000000;"><?= fReturnRepDetailFromID($aData['case_vi_lost_person_id']) ?> // <?= fReturnLostReason($aData['case_vi_lost_reason_id']) ?> // <?= htmlspecialchars($aData['case_vi_lost_reason_text']) ?> // <?= $aData['case_vi_lost_reason_time'] ?></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><a href="?page=customer&amp;cst_id=<?= $aData['case_nsi_id'] ?>">CSI: <?= htmlspecialchars($aData['case_nsi_person']) ?></a> <?= $aData['case_nsi_id'] ? '[<a href="javascript:void(0);" onClick="fDisplayOwnershipBox(' . $aData['case_nsi_id'] . ', \'' . htmlspecialchars($aData['case_nsi_appointment']) . '\');">Ownership</a>]' : '[<a href="?page=admin_segment_overview_assign&amp;save=3&amp;master_id=' . $iCustomerID . '&amp;case=card_nsi" onclick="return confirm(\'Please confirm that you wish to create a CSI case on this company\');">Create Case</a>]' ?></td>
			<td bgcolor="#DEDEDE"><?= htmlspecialchars($aData['case_nsi_appointment']) ?>&nbsp;</td>
			<td bgcolor="#DEDEDE"><?= fReturnRepDetailFromID($aData['case_nsi_lost_person_id']) ?> // <?= fReturnLostReason($aData['case_nsi_lost_reason_id']) ?> // <?= htmlspecialchars($aData['case_nsi_lost_reason_text']) ?> // <?= $aData['case_nsi_lost_reason_time'] ?></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><a href="?page=customer&amp;cst_id=<?= $aData['case_crc_id'] ?>">CSI: <?= htmlspecialchars($aData['case_crc_person']) ?></a> <?= $aData['case_crc_id'] ? '[<a href="javascript:void(0);" onClick="fDisplayOwnershipBox(' . $aData['case_crc_id'] . ', \'' . htmlspecialchars($aData['case_crc_appointment']) . '\');">Ownership</a>]' : '[<a href="?page=admin_segment_overview_assign&amp;save=3&amp;master_id=' . $iCustomerID . '&amp;case=card_crc" onclick="return confirm(\'Please confirm that you wish to create a CRC case on this company\');">Create Case</a>]' ?></td>
			<td bgcolor="#DEDEDE"><?= htmlspecialchars($aData['case_crc_appointment']) ?>&nbsp;</td>
			<td bgcolor="#DEDEDE"><?= fReturnRepDetailFromID($aData['case_crc_lost_person_id']) ?> // <?= fReturnLostReason($aData['case_crc_lost_reason_id']) ?> // <?= htmlspecialchars($aData['case_crc_lost_reason_text']) ?> // <?= $aData['case_crc_lost_reason_time'] ?></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><a href="?page=customer&amp;cst_id=<?= $aData['case_partner_id'] ?>">Partner:<?= htmlspecialchars($aData['case_partner_person']) ?></a> <?= $aData['case_partner_id'] ? '[<a href="javascript:void(0);" onClick="fDisplayOwnershipBox(' . $aData['case_partner_id'] . ', \'' . htmlspecialchars($aData['case_partner_appointment']) . '\');">Ownership</a>]' : '[<a href="?page=admin_segment_overview_assign&amp;save=3&amp;master_id=' . $iCustomerID . '&amp;case=card_partner" onclick="return confirm(\'Please confirm that you wish to create a Partner case on this company\');">Create Case</a>]' ?></td>
			<td bgcolor="#DEDEDE"><?= htmlspecialchars($aData['case_partner_appointment']) ?>&nbsp;</td>
			<td bgcolor="#DEDEDE"><?= fReturnRepDetailFromID($aData['case_partner_lost_person_id']) ?> // <?= fReturnLostReason($aData['case_partner_lost_reason_id']) ?> // <?= htmlspecialchars($aData['case_partner_lost_reason_text']) ?> // <?= $aData['case_partner_lost_reason_time'] ?></td>
		</tr>
	<?php
	}
	?>
	
	<tr>
		<td><br><br></td>
	</tr>

	<?php
	// Select segments
	$sSegments = '';
	$aSegments = DBGetRows('crm.segments', "segments.segment_status = 0 AND segments.segment_name LIKE 'CRM2%'", 'segment_name');
	while ( list($iKey, $aSegment) = each($aSegments) ) {
		$aSegment['segment_name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);

		$sSegments .= '<option value="' . $aSegment['segment_id'] . '">' . htmlspecialchars($aSegment['segment_name']) . '</option>';
	}
	?>

	<tr>
		<td>
			&nbsp;&nbsp;<b>Select Segment:</b><br>
			&nbsp;&nbsp;<select name="segment_id"><?= $sSegments ?></select>
		</td>
	</tr>

	<tr>
		<td>
			<br>&nbsp;&nbsp;<input type="submit" value="Update">
		</td>
	</tr>

</table>

</form>

<div id="change_ownership_form" style="display: none; background: #ffffff; border: 2px solid #000000; width: 300px; position: fixed; top: 100px; left: 300px; padding: 3px;">
<form method="POST" action="?<?= htmlspecialchars($_SERVER['QUERY_STRING']) ?>&save=2">
<input type="hidden" name="case_id" id="case_id" value="">
<b>Change Ownership:</b><br>
<br>
<b>New Appointment:</b><br>
<input type="text" name="appointment" id="appointment"><br>
<br>
<b>Sales People:</b><br>
<label><input type="radio" name="new_rep" value="0"> - Remove Sales Rep. -</label><br>
<?php
$aSalesReps = DBGetRows('crm.salespeople', "salespeople.lost_segment_id > 0 AND salespeople.display = 1", 'name');

// Loop through results and sort according to segments
while ( list($iKey, $aSaleRep) = each($aSalesReps) )
{
	echo '<label><input type="radio" name="new_rep" value="' . $aSaleRep['person_id'] . '">' . htmlspecialchars( $aSaleRep['name'] ) .'</label><br>';
}
?>
<br>
<input type="submit" value="Save"> <input type="submit" onClick="document.getElementById('change_ownership_form').style.display = 'none'; return false;" value="Cancel">
</form>
</div>
