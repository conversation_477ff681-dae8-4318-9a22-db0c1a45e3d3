<?php
// Data containers
$aResult = array();
$aOutput = array();

// Select all sales 
$aSales = DBGetRows('crm.saleslog', "cst_id IN(" . $GLOBALS['aCustomer']['AllIDs'] . ") AND (product_price-discount) > 0", 'saleslog.sold_date DESC');

// Generate output
while ( list($iKey, $aSale) = each($aSales) ) {
	// Determine container
	$sContainer = $GLOBALS['aProductTypes'][$aSale['product_type']]['container'];

	// Choose background class 
	if ( $sClass[$sContainer] === '' ) {
		$sClass[$sContainer] = ' class="TableGreyBackground"';
	} else {
		$sClass[$sContainer] = '';
	}

	// NSI: Extract Host Licenses
	$sExtra = '';
	preg_match_all('/([0-9]*) Host License/i', $aSale['product_name'], $aFound);
	if ( $aFound[1][0] ) {
		$sExtra = ', ' . $aFound[1][0] . ' HL';
	}

	// Output
	$aOutput[$sContainer] .= '
	<tr' . $sClass[$sContainer] . ' title="Sale ID: ' . $aSale['sale_id'] . '">
		<td>' . $GLOBALS['aProductTypes'][$aSale['product_type']]['short_name'] . $sExtra . '</td>
		<td>' . $aSale['sold_date'] . '</td>
		<td>' . $aSale['expires_date'] . '</td>
		<td>' . $aSale['currency'] . '</td>
		<td class="NumberCell">' . number_format($aSale['product_price']-$aSale['discount'], 2) . '</td>
		<td>' . strtoupper(fReturnRepDetailFromID($aSale['person_id'], 'init')) . '</td>';

	// Select box for CRC or normal display to everybody else
	if ( in_array( $GLOBALS['aRepData']['person_id'], $GLOBALS['aCRC'] ) && $aSale['status'] != 2 && $aSale['status'] != 3 && $aSale['status'] != 6 ) {
		$aOutput[$sContainer] .= '<td>
			<select name="crc_change_sale_status[' . $aSale['sale_id'] . ']">
				<option value="0"' . ( !$aSale['status'] ? ' selected' : '' ) . '>' . fSaleStatus(0) . '</option>
				<option value="4"' . ( $aSale['status'] == 4 ? ' selected' : '' ) . '>' . fSaleStatus(4) . '</option>
				<option value="5"' . ( $aSale['status'] == 5 ? ' selected' : '' ) . '>' . fSaleStatus(5) . '</option>
			</select>
		</td>';
	} else {
		$aOutput[$sContainer] .= '<td>' . fSaleStatus($aSale['status'], $aSale['online_payment_id'], $aSale['product_category'], $aSale['sale_id'] ) . '</td>';
	}
	$aOutput[$sContainer] .= '</tr>';

	// Generate data for Financial History
	if ( $aSale['status'] != 3 && $aSale['status'] != 6 && $aSale['status'] != 7 ) {
		// Paid
		if ( $aSale['status'] == 2 ) {
			$sStatus = 'paid';
		} else { // Awaiting
			$sStatus = 'awaiting';
		}

		// Store
		$iEURO = round( ( ($aSale['product_price']-$aSale['discount']) * $aSale['currency_exchange_rate'] ) / $aSale['currency_exchange_rate_euro'] );
		$GLOBALS['aFinancial'][substr($aSale['sold_date'], 0, 4)][$sStatus] += $iEURO;
		$GLOBALS['aFinancial']['total'][$sStatus] += $iEURO;
	}
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// VI
	echo fGenerateSalesDataTable( $aOutput['VI'], 'VI' );

	// CSI
	echo fGenerateSalesDataTable( $aOutput['NSI'], 'CSI' );

	// SS
	echo fGenerateSalesDataTable( $aOutput['SS'], 'SS' );

        // SVP
	echo fGenerateSalesDataTable( $aOutput['SVP'], 'SVP' );
        ?>
</table>

<?php

// function for generating output table
function fGenerateSalesDataTable( $sData, $sTitle ) {
	if ( $sData ) {
		return '
	<tr>
		<td><br></td>
	</tr>
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline">' . $sTitle . ' Sales</td>
		<td width="15%" class="TableSubHeadline">Sold</td>
		<td width="15%" class="TableSubHeadline">Period</td>
		<td width="13%" class="TableSubHeadline">Currency</td>
		<td width="12%" class="TableSubHeadline">Amount</td>
		<td width="10%" class="TableSubHeadline">Sold by</td>
		<td width="15%" class="TableSubHeadline">Status</td>
	</tr>' . $sData;
	}
}

?>
