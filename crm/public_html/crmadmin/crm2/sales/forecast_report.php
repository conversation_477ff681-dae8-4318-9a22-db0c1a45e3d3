<?php
// Date stamps
$sMonthFrom = date('Y-m-1');
$sMonthTo = date('Y-m-31');
$sQuarterFrom = ( ( date('m') / 3 ) >= 1 ? date('Y-' . ( (floor( (date('m')-1) / 3) * 3) + 1 ) . '-1') : date('Y-1-1') );
$sQuarterTo = date('Y-' . (ceil(date('m') / 3) * 3) . '-31');

// Variables
$sExpires = '';
$sResult = '';
$iTotalForecasted = 0;
$iTotalCalculated = 0;
$iTotalCustomers = 0;

// Build result
if ( $_GET['submitted'] ) {
	// Period?
	switch ( $_GET['period'] ) {
		// This Month
		case 1:
			$sExpires = "AND ( ( cst.forecast_date >= '" . $sMonthFrom . "' AND cst.forecast_date <= '" . $sMonthTo . "' )";
			break;

		// This Q
		case 2:
			$sExpires = "AND ( ( cst.forecast_date >= '" . $sQuarterFrom . "' AND cst.forecast_date <= '" . $sQuarterTo . "' )";
			break;

		// User input
		case 3:
			$sExpires = "AND ( ( cst.forecast_date >= '" . $_GET['from'] . "' AND cst.forecast_date <= '" . $_GET['to'] . "' )";
			break;
	}

	// Generate HTML output
	$aCustomers = DBQueryGetRows("
	SELECT
	 cst.cst_id,
	 cst.name,
	 cst.person_id,
	 cst.forecast_amount,
	 cst.forecast_expectancy,
	 cst.forecast_date,
	 max(comments.added) as added
	FROM
	 crm.cst,
	 crm.comments
	WHERE
	 cst.forecast_amount > 0 &&
	 cst.cst_id = comments.cst_id &&
	 cst.person_id = '" . $aRepData['person_id'] . "'
	 " . ( $sExpires ? $sExpires . ( $_GET['display_without_date'] == 1 ? ' || cst.forecast_date is null ) ' : ')' ) : '' ) . "
	 AND ( cst.forecast_expectancy >= '" . $_GET['expectancy_from'] . "' AND cst.forecast_expectancy <= '" . $_GET['expectancy_to'] . "' )
	GROUP BY
	 cst.cst_id
	ORDER BY
	 cst.person_id
	");
	while ( list($iKey, $aCustomer) = each($aCustomers) ) {
		// Select from forecast_log, to determine if this is a new or an up/down-graded forecast
		$aLogRow = DBQueryGetRows("SELECT * FROM crm.forecast_log WHERE forecast_log.cst_id = '" . $aCustomer['cst_id'] . "' ORDER BY logged DESC LIMIT 1,1");
		$aLogRow = $aLogRow[0];

		// Was Amount changed
		if ( $aLogRow['amount'] ) {
			if ( $aLogRow['amount'] > $aCustomer['forecast_amount'] ) {
				$sForecastAmountChange = '<img src="gfx/sort_down.gif" alt="" sortatt="' . $aCustomer['forecast_amount'] . '"> ';
			} elseif ( $aLogRow['amount'] < $aCustomer['forecast_amount'] ) {
				$sForecastAmountChange = '<img src="gfx/sort_up.gif" alt="" sortatt="' . $aCustomer['forecast_amount'] . '"> ';
			} else {
				$sForecastAmountChange = '';
			}
			
		} else {
			$sForecastAmountChange = '';
		}

		// Was Expectancy changed
		if ( $aLogRow['expectancy'] ) {
			if ( $aLogRow['expectancy'] > $aCustomer['forecast_expectancy'] ) {
				$sForecastExpectancyChange = '<img src="gfx/sort_down.gif" alt="" sortatt="' . $aCustomer['forecast_expectancy'] . '"> ';
			} elseif ( $aLogRow['expectancy'] < $aCustomer['forecast_expectancy'] ) {
				$sForecastExpectancyChange = '<img src="gfx/sort_up.gif" alt="" sortatt="' . $aCustomer['forecast_expectancy'] . '"> ';
			} else {
				$sForecastExpectancyChange = '';
			}
			
		} else {
			$sForecastExpectancyChange = '';
		}

		// Determine Class
		$sClass = ( $sClass === '' ? ' class="TableGreyBackground"' : '' );

		// HTML Output
		$sResult .= '
		<tr' . $sClass . fRowLink('?page=customer&cst_id=' . $aCustomer['cst_id'] . '&right=overview') . '>
			<td class="TablePadding" valign="top">' . htmlspecialchars($aCustomer['name']) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . $sForecastAmountChange . number_format(htmlspecialchars($aCustomer['forecast_amount'])) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . $sForecastExpectancyChange . ( $aCustomer['forecast_expectancy'] < 101 ? htmlspecialchars($aCustomer['forecast_expectancy']) : 'LOST' ) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . ( $aCustomer['forecast_expectancy'] < 101 ? number_format(htmlspecialchars($aCustomer['forecast_amount'] * ($aCustomer['forecast_expectancy'] / 100))) : 0 ) . '</td>
			<td valign="top">' . htmlspecialchars($aCustomer['forecast_date']) . '</td>
			<td valign="top">' . htmlspecialchars($aCustomer['added']) . '</td>
		</tr>';

		// Totals
		$iTotalCustomers++;
		$iTotalForecasted += $aCustomer['forecast_amount'];
		$iTotalCalculated += ( $aCustomer['forecast_expectancy'] < 101 ? $aCustomer['forecast_amount'] * ($aCustomer['forecast_expectancy'] / 100) : 0 );
	}
}
?>

<form method="GET" action="?">
<input type="hidden" name="submitted" value="1">
<input type="hidden" name="page" value="forecast_report">
<table width="100%" cellpadding="0" cellspacing="0">
	<?php
	$sTitle = 'Forecast Report';
	$iColSpan = 7;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td colspan="2" class="TablePadding" valign="top">
			<b>Select Period</b><br>
			<label><input type="radio" name="period" value="1" style="width: 15px;" <?=( $_GET['period'] == 1 ? 'checked' : '' )?>> This Month (<?= $sMonthFrom ?> - <?= $sMonthTo ?>)</label><br>
			<label><input type="radio" name="period" value="2" style="width: 15px;" <?=( $_GET['period'] == 2 ? 'checked' : '' )?>> This Quarter (<?= $sQuarterFrom ?> - <?= $sQuarterTo ?>)</label><br>
			<label><input type="radio" name="period" value="3" style="width: 15px;" <?=( $_GET['period'] == 3 ? 'checked' : '' )?>> Other Period: </label><input type="text" name="from" style="width: 100px;" value="<?= ( $_GET['from'] ? htmlspecialchars($_GET['from']) : date('Y-m-d') ) ?>"> - <input type="text" name="to" style="width: 100px;" value="<?= ( $_GET['to'] ? htmlspecialchars($_GET['to']) : date('Y-m-d') ) ?>">
			<br>
			<br>
			<label><input type="checkbox" name="display_without_date" style="width: 15px;" value="1" <?=( $_GET['display_without_date'] == 1 ? 'checked' : '' )?>> Display entries without an expected close date</label><br>
			<br>
			<input type="submit" value="Display Report" style="width: 25%" class="submit">
		</td>
		<td colspan="5" valign="top">
			<b>Insert Expectancy Values</b><br>
			<br>
			From:<br>
			<input type="text" style="width: 75px" name="expectancy_from" value="<?= $_GET['expectancy_from'] ? $_GET['expectancy_from'] : 0 ?>" class="NumberCell">%<br>
			<br>
			To:<br>
			<input type="text" style="width: 75px" name="expectancy_to" value="<?= $_GET['expectancy_to'] ? $_GET['expectancy_to'] : 100 ?>" class="NumberCell">%
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Result';
	$iColSpan = 7;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td width="38%" class="TableSubHeadline TableGreyBackground TablePadding"><b>Company</b></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 15px;"><b>Forecasted (A)</b></td>
		<td width="14%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 15px;"><b>Expectancy % (B)</b></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground" align="right" style="padding-right: 15px;"><b>Deal Value (A*B)</b></td>
		<td width="10%" class="TableSubHeadline TableGreyBackground"><b>Expected Closed</b></td>
		<td width="18%" class="TableSubHeadline TableGreyBackground"><b>Last Updated</b></td>
	</tr>

	<?= $sResult ?>

	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td class="TablePadding"><b>Total <?= $iTotalCustomers ?> Customers</b></td>
		<td align="right" style="padding-right: 15px;"><b><?= number_format($iTotalForecasted) ?></b></td>
		<td></td>
		<td align="right" style="padding-right: 15px;"><b><?= number_format($iTotalCalculated) ?></b></td>
		<td></td>
		<td></td>
	</tr>

</table>
</form>
