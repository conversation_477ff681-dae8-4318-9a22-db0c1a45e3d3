<?php
// If requested, download the Business Plan file
if ( $_GET['action'] == "dbplan" && is_numeric( $_GET['account_id'] ) ) {
	// Load configuration
	include 'configuration.php';

	// Load sales functions
	include INCLUDE_PATH . 'sales_functions.php';

	// Load global functions
	include INCLUDE_PATH . 'global_functions.php';

	// Open DB Connection
	fOpenDatabase();

	$content = DBGetRow("ca.partner_business_plans", "partner_id = '".$_GET['account_id']."'");

	if ( $content['file'] == "" ) {
		header("HTTP/1.0 404 Not Found");
		echo "<h1>The requested file could not be found.</h1>";
		die();
	}
	ob_clean();
	header("Content-type: application/zip"); // Will _always_ be application/zip
	header("Content-disposition: attachment; filename=\"".urlencode( $content['file_name'] )."\"");
	print base64_decode( $content['file'] );
	die();
}

// Get master id
$mid = DBGetRowValue("crm.cst", "master_id", "cst_id = ".(int)$_GET['cst_id'] );

if ( $_GET['action'] == "approve" && is_numeric( $_GET['account_id'] ) ) {
	// Approve and upgrade selected account
	DBQuery("UPDATE ca.accounts SET special_limits = 'partner_portal' WHERE account_id = '".(int)$_GET['account_id']."' LIMIT 1");

	// Get partner id
	$pid = DBGetRowValue("ca.partner_profile", "partner_id", "account_id = ".(int)$_GET['account_id'] );

	// Update status
	DBQuery("UPDATE ca.partner_application SET status = 2 WHERE partner_id = '".(int)$pid."' LIMIT 1");

	// Set as active partner
	DBQuery("UPDATE crm.cst SET active_partner = 1 WHERE cst_id = ".(int)$mid." LIMIT 1");

}
// Partner type config
$partnerList = array(
	1 => "Consultant"
	,2 => "MSSP"
	,3 => "System Integrator"
	,4 => "Reseller"
);

// Get Partner Portal Accounts owned by this customer case
$result = DBGetRows("ca.accounts", "cst_id IN ( SELECT cst_id FROM crm.cst AS C WHERE master_id = ( SELECT master_id FROM crm.cst AS B WHERE B.cst_id = '".(int)$_GET['cst_id']."' LIMIT 1 ) ) AND special_limits LIKE 'partner%'");

$accounts = "";
for ( $i = 0; $i < count( $result ); $i++ ) {
	// Check account completion status:
	// - Business Plan
	$total = DBNumRows("ca.partner_business_plans", "partner_id = '".(int)$result[$i]['account_id']."'");
	// - Profile Details

	// Get partner id
	$pid = DBGetRowValue("ca.partner_profile", "partner_id", "account_id = ".(int)$result[$i]['account_id'] );

	$detailsRow = DBGetRow("ca.partner_application", "partner_id = '".(int)$pid."'
		AND program is not null
		AND name is not null
		AND email is not null
		AND phone is not null
		AND company_name is not null
		AND address is not null
		AND state is not null
		AND zipcode is not null
		AND country is not null
		AND number_of_employees is not null
		AND number_of_sales_staff is not null
		AND number_of_technical_staff is not null
		AND security_vendors is not null
		AND sales_revenue_2010 is not null
		AND management_sponsors is not null
		AND wsus_responsible is not null
		AND sales_director is not null
		AND product_manager is not null
		AND presales is not null
		AND account_manager is not null
		AND sale_expected_2011 is not null
		AND customers_expected_2011 is not null
		AND right_now is not null
		AND support_now is not null
		AND city is not null
	");

	$details = 0;
	if ( $detailsRow['name'] != "" ) {
		$details = 1;
	}

	$status = "Not Ready.";
	if ( $detailsRow['status'] == 2 ) {
		$status = "Approved.";
	} elseif ( $detailsRow['status'] == 1 ) {
		$status = "Pending.";
	}

	$accounts .= "
		<table>
			<tr valign=\"top\">
				<td>
				<b>".htmlspecialchars( $result[$i]['account_username'] )." - ".$status."</b>
				</td>
				<td>
					".( $total == 1 ? "<a target=\"_blank\" href=\"c_ca_partner.php?action=dbplan&account_id=".(int)$result[$i]['account_id']."\">Business Plan </a><br/>" : "Business Plan Not Uploaded<br/>" )."
					".( $details == 1 ? "<u>Profile Details:</u>
					<br/>Program: ".htmlspecialchars( $detailsRow['program'] )."
					<br/>Name: ".htmlspecialchars( $detailsRow['name'] )."
					<br/>Email: ".htmlspecialchars( $detailsRow['email'] )."
					<br/>Phone: ".htmlspecialchars( $detailsRow['phone'] )."
					<br/>Company Name: ".htmlspecialchars( $detailsRow['company_name'] )."
					<br/>Address: ".htmlspecialchars( $detailsRow['address'] )."
					<br/>State: ".htmlspecialchars( $detailsRow['state'] )."
					<br/>Zipcode: ".htmlspecialchars( $detailsRow['zipcode'] )."
					<br/>Country: ".htmlspecialchars( $detailsRow['country'] )."
					<br/>Number of Employees: ".htmlspecialchars( $detailsRow['number_of_employees'] )."
					<br/>Number of Sales Staff: ".htmlspecialchars( $detailsRow['number_of_sales_staff'] )."
					<br/>Number of Technical Staff: ".htmlspecialchars( $detailsRow['number_of_technical_staff'] )."
					<br/>Security Vendors: ".htmlspecialchars( $detailsRow['security_vendors'] )."
					<br/>Sales Revenue 2010: ".htmlspecialchars( $detailsRow['sales_revenue_2010'] )."
					<br/>Management Sponsors: ".htmlspecialchars( $detailsRow['management_sponsors'] )."
					<br/>WSUS Resp.: ".htmlspecialchars( $detailsRow['wsus_responsible'] )."
					<br/>Sales Director: ".htmlspecialchars( $detailsRow['sales_director'] )."
					<br/>Product Manager: ".htmlspecialchars( $detailsRow['product_manager'] )."
					<br/>Presales: ".htmlspecialchars( $detailsRow['presales'] )."
					<br/>Account Manager: ".htmlspecialchars( $detailsRow['account_manager'] )."
					<br/>Sales Expected 2011: ".htmlspecialchars( $detailsRow['sale_expected_2011'] )."
					<br/>Customers Expected 2011: ".htmlspecialchars( $detailsRow['customers_expected_2011'] )."
					<br/>Sale right now: ".htmlspecialchars( $detailsRow['right_now'] )."
					<br/>Support right now: ".htmlspecialchars( $detailsRow['support_now'] )."
					<br/>Newsletter: ".htmlspecialchars( $detailsRow['newsletter'] )."
					<br/>City: ".htmlspecialchars( $detailsRow['city'] )."
					<br/>Vatno: ".htmlspecialchars( $detailsRow['vatno'] )."
					<br/>Eanno: ".htmlspecialchars( $detailsRow['eanno'] )."
					<br/>Dunsno: ".htmlspecialchars( $detailsRow['dunsno'] )."
					<br/>" : "Application Form not Finished" )."
					".( $total == 1 && $details == 1 && $detailsRow['status'] == 1 ? "<a href=\"?page=customer&cst_id=".(int)$_GET['cst_id']."&action=approve&account_id=".(int)$result[$i]['account_id']."\">Approve</a><br/>" : "" )."
					<br/><a href=\"?page=customers&account_id=".(int)$result[$i]['account_id']."\">View Customers</a>
				</td>
			</tr>
		</table>
";
}
?>
<table>
	<tr valign="top">
		<td>
			Review Partner:
		</td>
		<td>
			<?= $accounts ?>
		</td>
	</td>
<?php


?>
</table>
