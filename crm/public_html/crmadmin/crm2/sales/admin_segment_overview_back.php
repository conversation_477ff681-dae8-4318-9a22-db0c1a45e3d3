<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) )
{
	exit();
}

// Build array of special CRM2 segments, that don't fit in the "Country->Size" segmentation
$aSpecialSegments = array();
$aSegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' AND segments.segment_name NOT LIKE '% - Canvas' AND segments.segment_name NOT LIKE '% - Lost Leads'", 'segments.segment_name');
while ( list($iKey, $aSegment) = each($aSegments) )
{
	$aSpecialSegments[$aSegment['segment_id']]['name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);
	$aSpecialSegments[$aSegment['segment_id']]['count'] = 0;
	$aSpecialSegments[$aSegment['segment_id']]['total'] = 0;
}

// Select data
$sQuery = "SELECT * FROM crm.cst LEFT JOIN crm.countries ON ( cst.invoice_country = countries.id ) WHERE cst.case_name = 'Company Card' ORDER BY countries.region, countries.country";
$aCustomers = DBQueryGetRows($sQuery);
 
// Loop through results and sort according to segments
while ( list($iKey, $aCustomer) = each($aCustomers) )
{
	$aCustomer['invoice_country'] = intval($aCustomer['invoice_country']);

	// Detailed count
	{
		// Total
		$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['total']++;

		// "Free" / available
		$aActive = DBGetRows('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name IN ('card_vi', 'card_crc', 'card_nsi', 'card_partner') AND cst.person_id > 0");
		if ( $aSpecialSegments[$aCustomer['segment_id']] && count($aActive) == 0 )
		{
			$aSpecialSegments[$aCustomer['segment_id']]['count']++;
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		}
		elseif ( $aSpecialSegments[$aCustomer['segment_id']] )	
		{
			$aSpecialSegments[$aCustomer['segment_id']]['total']++;
		}
		elseif ( count($aActive) == 0 && fSegmentNameFromID( $aCustomer['segment_id'] ) != 'n/a' )
		{
			// Free - but assigned
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free_assigned']++;
			$aRegionCountFreeAssigned[$aCustomer['region']]++;
			$aCountryCountFreeAssigned[$aCustomer['invoice_country']]++;
		}
		elseif ( count($aActive) == 0 )
		{
			// Free
			$aRegions[$aCustomer['region']][$aCustomer['invoice_country']][intval($aCustomer['large_account'])]['free']++;
			$aRegionCountFree[$aCustomer['region']]++;
			$aCountryCountFree[$aCustomer['invoice_country']]++;
		}
	}
	
	// Country count
	$aCountryCount[$aCustomer['invoice_country']]++;

	// Region count
	$aRegionCount[$aCustomer['region']]++;
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Segment Overview (' . count($aCustomers) . ' Customers)';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td>
			<form method="GET" action="">
			<input type="hidden" name="page" value="admin_segment_overview_assign">
			<b>Search:</b><br>
			<input type="text" name="search">
			</form>
		</td>
	</tr>

	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 25px; border-top: 1px solid #000000;"><b>Segment</b></td>
		<td style="border-top: 1px solid #000000;"><b>Total</b></td>
		<td style="border-top: 1px solid #000000;"><b>Free (Assigned)</b></td>
	</tr>

	<tr>
		<td><br></td>
	</tr>
	<tr style="background: #DEDEDE;">
		<td style="padding-left: 5px; font-size: 13px; border-bottom: 1px solid #000000;"><b>Special Segments</b>&nbsp;</td>
		<td style="font-size: 13px; font-size: 13px; border-bottom: 1px solid #000000;">&nbsp;</b></td>
		<td style="font-size: 13px; font-size: 13px; border-bottom: 1px solid #000000;">&nbsp;</td>
	</tr>

	<?php
	// Special Result
	while ( list($iSegmentID, $aSegment) = each($aSpecialSegments) )
	{
        ?>

                <tr>
                        <td style="padding-top: 5px; padding-left: 25px; "><a href="?page=admin_segment_overview_assign&amp;segment_id=<?= $iSegmentID ?>"><?= $aSegment['name'] ?></a>&nbsp;</td>
                        <td style="padding-top: 5px;"><?= number_format($aSegment['total']) ?></td>
                        <td style="padding-top: 5px;"><?= number_format($aSegment['count']) ?></td>
                </tr>

	<?php
	}

	// Result
	while ( list($iRegion, $aCountries) = each($aRegions) )
	{
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr style="background: #DEDEDE;">
			<td style="padding-left: 5px; font-size: 13px;"><b><?= fReturnRegionName( $iRegion ) ?></b>&nbsp;</td>
			<td style="font-size: 13px;"><b><?= number_format($aRegionCount[$iRegion]) ?></b></td>
			<td style="font-size: 13px;"><b><?= number_format($aRegionCountFree[$iRegion]) ?></b></td>
		</tr>
		<?php
		while ( list($iCountry, $aSegments) = each($aCountries) )
		{
		?>
			<tr>
				<td style="padding-top: 5px; padding-left: 25px; border-top: 1px solid #000000;"><b><?= fCountryNameFromID( $iCountry ) ?></b></td>
				<td style="padding-top: 5px; border-top: 1px solid #000000;"><b><?= number_format($aCountryCount[$iCountry]) ?></b></td>
				<td style="padding-top: 5px; border-top: 1px solid #000000;"><b><?= number_format($aCountryCountFree[$iCountry]) ?></b></td>
			</tr>
			<?php
			while ( list($iSegment, $aCounts) = each($aSegments) )
			{
			?>
				<tr>
					<td width="20%" style="padding-left: 45px;"><?= $iSegment ? 'LA' : 'SMB' ?></td>
					<td width="20%" ><a href="?page=admin_segment_overview_assign&amp;segment=<?= $iSegment ?>&amp;country_id=<?= $iCountry ?>&amp;type=taken"><?= number_format($aCounts['total']) ?></a></td>
					<td width="60%" ><a href="?page=admin_segment_overview_assign&amp;segment=<?= $iSegment ?>&amp;country_id=<?= $iCountry ?>&amp;type=free"><?= number_format($aCounts['free']) ?> (<?= number_format($aCounts['free_assigned']) ?>)</a></td>
				</tr>
			<?php
			}
			?>
		<?php
		}
		?>
	<?php
	}
	?>
</table>
