<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	exit();
}

//Sort URL
$sURL = '?page=admin_segment_overview_assign&special_segment=' . urlencode($_GET['special_segment']) . '&task=' . urlencode($_GET['task']) . '&type=' . urlencode($_GET['type']) . '&person_id=' . (int) $_GET['person_id'] . '&segment_id=' . (int) $_GET['segment_id'] . '&search=' . urlencode($_GET['search']) . '&canvas_leads=' . urlencode($_GET['canvas_leads']) . '&order=';

if ( $_GET['trash'] == "on" ){
	$iTrashSegmentId = -1;
} else {
	$iTrashSegmentId = 1481;
}

$bFilterLost = false;
if ( isset( $_GET['lost_interval'] ) && $_GET['lost_interval'] != "0" ){
	$aLost = explode( "-", $_GET['lost_interval'] );
	if ( $_GET['lost_interval'] == '>180' ){
		$aLost[0] = "180";
		$aLost[1] = "6000"; // A random large value
	}

	if ( is_numeric( $aLost[0] ) && ( is_numeric( $aLost[1] ) ) ){
		$iLostDay_1 = $aLost[0];
		$iLostDay_2 = $aLost[1];

		// Build the date based search
		$sStartDate = strtotime( date("Y-m-d", strtotime( "- ".$iLostDay_1."days" ) ) );
		$sEndDate = strtotime( date("Y-m-d", strtotime( "- ".$iLostDay_2."days" ) ) );
		$bFilterLost = true;
	}
}

// Store preference in cookie
if ( $_GET['order'] ) {
	setcookie('order_pref', $_GET['order']);
} else {
	$_GET['order'] = $_COOKIE['order_pref'];
}

function fGetCountryName($iCountryID){
	$aCountryData = DBQueryGetRows("SELECT country FROM crm.countries WHERE countries.id = '".$iCountryID."'");
	return $aCountryData[0]['country'];
}

function fGenerateSegmentSelectBox($iCstID, $iSegmentID){
/*	$aSegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' AND segments.segment_name NOT LIKE '% - Lost Leads'", 'segments.segment_name');
	$sReturn = '<select onchange=>
			<option value="-1">- Select Segment -</option>
	';
	foreach($aSegments as $aSegment){
		if ($aSegment['segment_id'] == $iSegmentID){
			$sReturn .= '<option value="'.htmlspecialchars($aSegment['segment_id']).'" selected>'.htmlspecialchars(str_replace('CRM2 - ', '', $aSegment['segment_name'])).'</option>';
		} else {
			$sReturn .= '<option value="'.htmlspecialchars($aSegment['segment_id']).'">'.htmlspecialchars(str_replace('CRM2 - ', '', $aSegment['segment_name'])).'</option>';
		}
	}
	$sReturn .= '</select>';*/
	$sReturn = makeSegmentCombo( $iSegmentID, 'onchange="if ( this.value != -1 ){ location = \'' . $GLOBALS['sURL'] . '&cst_id='.$iCstID.'&save=4&new_segment=\' + this.value; }"' );
	return $sReturn;
}

// Save - Change: Segment, Trash-it, Country
if ( $_GET['save'] == 1 ) {
	while ( list($iCustomerID, $aValues) = each($_POST['company']) ) {
		// Update segment
		if ( ( $aValues['segment'] == 1 ) && ( $_POST['segment_id'] != -1 ) ) {
			DBQuery("UPDATE crm.cst SET cst.segment_id = '" . $_POST['segment_id'] . "', cst.segment_assigned = NOW(), canvas_timestamp = NULL WHERE cst.cst_id = '" . $iCustomerID . "'");
		}

		// Trash lead?
		if ( $aValues['trash'] == 1 ) {
			DBQuery("UPDATE crm.cst SET cst.segment_id = 1481, cst.segment_assigned = NOW(), canvas_timestamp = NULL WHERE ( cst.cst_id = '" . $iCustomerID . "' || master_id = '" . $iCustomerID . "' )");
		}

		// Update country?
		if ( $aValues['country'] > 0 ) {
			DBQuery("UPDATE crm.cst SET cst.invoice_country = '" . intval($aValues['country']) . "' WHERE ( cst.cst_id = '" . $iCustomerID . "' || master_id = '" . $iCustomerID . "' )");
		}
	}
} elseif ( $_GET['save'] == 2 && fCalculateAccountStatus($_POST['case_id']) != 'Customer' ) { // Save - Change: Appointment + Ownership
	DBQuery("UPDATE crm.cst SET cst.appointment = '" . $_POST['appointment'] . "', person_id = '" . $_POST['new_rep'] . "' WHERE cst_id = '" . $_POST['case_id'] . "' LIMIT 1");
} elseif ( $_GET['save'] == 3 ) { // Save - Change: Create Partner Case
	DBQuery("INSERT INTO crm.cst (master_id, case_name) VALUES('" . $_GET['master_id'] . "', '" . $_GET['case'] . "')");
} elseif ( $_GET['save'] == 4 ) { // Save - Change segment
	if ( $_GET['new_segment'] != -1 ){
		DBQuery("UPDATE crm.cst SET segment_id='".$_GET['new_segment']."', canvas_timestamp = NULL WHERE cst_id='".$_GET['cst_id']."' LIMIT 1");
	}
}

// Redirect?
if ( $_GET['save'] == 1 ) {
	header('Location: ?page=admin_segment_overview_assign&segment_id=' . urlencode($_POST['i_segment_id']) . '&special_segment=' . urlencode($_POST['i_special_segment']) . '&task=' . urlencode($_POST['i_task']) . '&type=' . urlencode($_POST['i_type']) . '&person_id=' . urlencode($_POST['i_person_id']) . '&search=' . urlencode($_POST['i_search']) . '&country_id=' . urlencode($_POST['i_country_id']));
	exit();
}

// Build country select options
$sCountryOptions = '';
$aCountries = DBGetRows('crm.countries', '', 'countries.country');
while ( list($iKey, $aCountry) = each($aCountries) ) {
	$sCountryOptions .= '<option value="' . $aCountry['id'] . '">' . $aCountry['country'] . '</option>';
}

// Select data
if ( $_GET['segment_id'] && !$_GET['special_segment'] ) {
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' && segment_id IN (".arrayToSqlIn( explode( ",", $_GET['segment_id'] ) ).")";

	$sQuery .= fSearch();

} elseif ( $_GET['segment_id'] > 0 ) {
	// Select all lost cases
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' && segment_id IN (".arrayToSqlIn( explode( ",", $_GET['segment_id'] ) ).")";

	$sQuery .= fSearch();

	// Retrieve cases
	$aCases = DBQueryGetRows($sQuery);

	// Loop through cases and build string of CST_ID's
	$sCSTIDs = '';
	while ( list($iKey, $aCase) = each($aCases) ) {
		$sCSTIDs .= ( $aCase['master_id'] ? $aCase['master_id'] : $aCase['cst_id'] ) . ',';
	}

	// Get customer cards
	$sQuery = "SELECT * FROM crm.cst WHERE cst_id in (" . trim($sCSTIDs, ',') . ")";

} elseif ( $_GET['task'] == 'appointments' ) {
	// Select appointment type
	if ( $_GET['type'] == 'active' ) {
		$aCustomers = DBGetRows('crm.cst', "appointment > '" . date('Y-m-d') . "' AND appointment NOT LIKE '% 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	} elseif ( $_GET['type'] == 'expired' ) {
		$aCustomers = DBGetRows('crm.cst', "appointment < '" . date('Y-m-d') . "' AND appointment != '0000-00-00 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	} elseif ( $_GET['type'] == 'all' ) {
		$aCustomers = DBGetRows('crm.cst', "person_id = '" . $_GET['person_id'] . "' && appointment > '0000-00-00 00:00:00'");
	} else {
		$aCustomers = DBGetRows('crm.cst', "appointment >= '" . date('Y-m-d') . "' AND appointment like '% 00:00:00' AND person_id = '" . $_GET['person_id'] . "'");
	}

	// Loop through cases and build string of CST_ID's
	$sCSTIDs = '';
	while ( list($iKey, $aCustomer) = each($aCustomers) ) {
		$sCSTIDs .= ( $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'] ) . ',';
	}
	
	// Get customer cards
	$sQuery = "SELECT * FROM crm.cst WHERE cst_id in (" . trim($sCSTIDs, ',') . ")";


} elseif ( $_GET['search'] || $_GET['company_industry'] || $_GET['invoice_country'] || $_GET['lead_source']) {
	$sCreated = "";
	$bFilterDate = false;
	if ( ( isset( $_GET['create_date_1'] ) ) && ( isset( $_GET['create_date_2'] ) ) ){
		if ( ( $_GET['create_date_1'] != "" ) && ( $_GET['create_date_2'] != "" ) ){
			$bFilterDate = true;
			$sCreated = ", (SELECT added FROM comments WHERE cst_id = cst.cst_id ORDER BY added desc limit 1) as created ";
		}
	}

	$sQuery = "SELECT *".$sCreated." FROM crm.cst WHERE  ( cst.segment_id != ".$iTrashSegmentId." || cst.segment_id IS NULL ) AND cst.case_name = 'Company Card'";

	$sQuery .= fSearch();

	if ( $bFilterDate == true ){
		$sQuery = "SELECT * FROM (".$sQuery.") as A WHERE ( created <= '".mysql_real_escape_string( $_GET['create_date_2'] )."' && created >= '".mysql_real_escape_string( $_GET['create_date_1'] )."' )";
	}

} elseif ( !$_GET['search'] ) {
	$sQuery = "SELECT * FROM crm.cst WHERE cst.case_name = 'Company Card' " . ( $_GET['country_id'] > 0 ? "AND cst.invoice_country = '" . $_GET['country_id'] . "'" : ' AND ( !cst.invoice_country OR cst.invoice_country IS NULL )' ) . " AND (cst.segment_id != ".$iTrashSegmentId." OR cst.segment_id IS NULL)";
}


function fSearch() {
	$sQuery = " && ( cst.name like '%" . $_GET['search'] . "%' || cst.alias like '%" . $_GET['search'] . "%' || cst.cst_id = '" . $_GET['search'] . "')";

	if (trim($_GET['account_type']) != "" && trim($_GET['account_type']) != "all"){
		if ( $_GET['account_type'] == "Partner" || $_GET['account_type'] == "SA" ) {
			$sQuery .= " && ( cst.account_type='Partner' || cst.account_type='SA' )";
		} else {
			$sQuery .= " && cst.account_type='".$_GET['account_type']."'";
		}
	}
	if (trim($_GET['company_industry']) != "" && trim($_GET['company_industry']) != "all"){
		$sQuery .= " && cst.company_industry='".$_GET['company_industry']."'";
	}
	if (trim($_GET['invoice_country']) != "" && trim($_GET['invoice_country']) != "all"){
		$sQuery .= " && cst.invoice_country='".$_GET['invoice_country']."'";
	}
	if (trim($_GET['lead_source']) != "" && trim($_GET['lead_source']) != "all"){
		$sQuery .= " && cst.lead_source='".$_GET['lead_source']."'";
	}

	return $sQuery;
}

// 23,17,21,3,7,6,1,4,2,16
$aCustomers = DBQueryGetRows($sQuery);

// Loop through results and sort according to segments
while ( list($iKey, $aCustomer) = each($aCustomers) ) {
	// Case details
	$aVI = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_vi'");
	$aNSI = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_nsi'");
	$aCRC = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_crc'");
	$aCSC = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_csc'");
	$aPartner = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND cst.case_name = 'card_partner'");

        $cstStatus = fCalculateAccountStatus($aCustomer['cst_id']);
        $csiLastContact = fReturnTimeSince(DBGetRowValue('crm.comments', 'MAX(added)', "cst_id = '" . $aNSI['cst_id'] . "' && cst_id > 0 && person_id > 0  && person_id = '" . $aNSI['person_id'] . "' && length(comment) > 0"));

        if ( $_GET['type'] == 'all' && $_GET['task'] == 'appointments' ) {
                if ( $csiLastContact < 180 ) {
                        continue;
                }

		if ( !in_array($aCustomer['invoice_country'], array(23,17,21,3,7,6,1,4,2,16)) ) {
			continue;
		}

		if ( $cstStatus == 'Customer' ) {
			continue;
		}
//		if (  ) {
//		}
        }

	// Limited ?
	if ( $_GET['type'] == 'free' ) {
		// Include if both VI and NSI are "free"
		if ( !$aVI['person_id'] && !$aNSI['person_id'] && !$aPartner['person_id'] && !$aCRC['person_id'] ) {
			$aResults[$aCustomer['cst_id']] = $aCustomer;
		} else {
			// Skip
			continue;
		}
	} else {
		// All
		$aResults[$aCustomer['cst_id']] = $aCustomer;
	}

	// Account Status
	$aResults[$aCustomer['cst_id']]['account_status'] = $cstStatus;

	// Case details
	$aResults[$aCustomer['cst_id']]['case_vi_person'] = fReturnRepDetailFromID($aVI['person_id']);
	$aResults[$aCustomer['cst_id']]['case_vi_appointment'] = $aVI['appointment'];
	$aResults[$aCustomer['cst_id']]['case_vi_id'] = $aVI['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_nsi_person'] = fReturnRepDetailFromID($aNSI['person_id']);
	$aResults[$aCustomer['cst_id']]['case_nsi_appointment'] = $aNSI['appointment'];
	$aResults[$aCustomer['cst_id']]['case_nsi_id'] = $aNSI['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_crc_person'] = fReturnRepDetailFromID($aCRC['person_id']);
	$aResults[$aCustomer['cst_id']]['case_crc_appointment'] = $aCRC['appointment'];
	$aResults[$aCustomer['cst_id']]['case_crc_id'] = $aCRC['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_partner_person'] = fReturnRepDetailFromID($aPartner['person_id']);
	$aResults[$aCustomer['cst_id']]['case_partner_appointment'] = $aPartner['appointment'];
	$aResults[$aCustomer['cst_id']]['case_partner_id'] = $aPartner['cst_id'];
	$aResults[$aCustomer['cst_id']]['case_csc_person'] = fReturnRepDetailFromID($aCSC['person_id']);
	$aResults[$aCustomer['cst_id']]['case_csc_appointment'] = $aCSC['appointment'];
	$aResults[$aCustomer['cst_id']]['case_csc_id'] = $aCSC['cst_id'];

	// Latest "Lost reason"
	{
		// VI
		$aVILostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aVI['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_id'] = $aVILostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_text'] = $aVILostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_vi_lost_reason_time'] = $aVILostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_vi_comment_time'] = DBGetRowValue('crm.comments', 'MAX(added)', "cst_id = '" . $aVI['cst_id'] . "' && cst_id > 0 && person_id > 0 && person_id = '" . $aVI['person_id'] . "' && length(comment) > 0");
		$aResults[$aCustomer['cst_id']]['case_vi_lost_person_id'] = $aVILostReason[0]['person_id'];

		// NSI
		$aNSILostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aNSI['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_id'] = $aNSILostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_text'] = $aNSILostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_reason_time'] = $aNSILostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_nsi_comment_time'] = $csiLastContact; 
		$aResults[$aCustomer['cst_id']]['case_nsi_lost_person_id'] = $aNSILostReason[0]['person_id'];

		// CRC
		$aCRCLostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aCRC['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_id'] = $aCRCLostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_text'] = $aCRCLostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_crc_lost_reason_time'] = $aCRCLostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_crc_comment_time'] = DBGetRowValue('crm.comments', 'MAX(added)', "cst_id = '" . $aCRC['cst_id'] . "' && cst_id > 0 && person_id > 0 && person_id = '" . $aCRC['person_id'] . "' && length(comment) > 0");
		$aResults[$aCustomer['cst_id']]['case_crc_lost_person_id'] = $aCRCLostReason[0]['person_id'];

		// Partner
		$aPartnerLostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aPartner['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_id'] = $aPartnerLostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_text'] = $aPartnerLostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_partner_lost_reason_time'] = $aPartnerLostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_partner_comment_time'] = DBGetRowValue('crm.comments', 'MAX(added)', "cst_id = '" . $aPartner['cst_id'] . "' && cst_id > 0 && person_id > 0 && person_id = '" . $aPartner['person_id'] . "' && length(comment) > 0");
		$aResults[$aCustomer['cst_id']]['case_partner_lost_person_id'] = $aPartnerLostReason[0]['person_id'];

		// CSC
		$aCSCLostReason = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aCSC['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");
		$aResults[$aCustomer['cst_id']]['case_csc_lost_reason_id'] = $aCSCLostReason[0]['reason'];
		$aResults[$aCustomer['cst_id']]['case_csc_lost_reason_text'] = $aCSCLostReason[0]['comment'];
		$aResults[$aCustomer['cst_id']]['case_csc_lost_reason_time'] = $aCSCLostReason[0]['lost_time'];
		$aResults[$aCustomer['cst_id']]['case_csc_comment_time'] = DBGetRowValue('crm.comments', 'MAX(added)', "cst_id = '" . $aCSC['cst_id'] . "' && cst_id > 0 && person_id > 0 && person_id = '" . $aCSC['person_id'] . "' && length(comment) > 0");
		$aResults[$aCustomer['cst_id']]['case_csc_lost_person_id'] = $aCSCLostReason[0]['person_id'];
	}
}

if (sizeof($aCustomers) == 0){
	// Head line
	$sTitle = 'Segment Overview / Assign Company Cards';
//	$iColSpan = 12;
	$iColSpan = 16;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

	// Sort URL
	// segment_id=1427&person_id=74&canvas_leads=1&special_segment=1
	$sURL = '?page=admin_segment_overview_assign&special_segment=' . urlencode($_GET['special_segment']) . '&task=' . urlencode($_GET['task']) . '&type=' . urlencode($_GET['type']) . '&person_id=' . (int) $_GET['person_id'] . '&segment_id=' . (int) $_GET['segment_id'] . '&search=' . urlencode($_GET['search']) . '&canvas_leads=' . urlencode($_GET['canvas_leads']) . '&order=';
	include 'admin_include_search.php';
	echo "No matches.";
} else {
	?>
	<table width="100%" cellspacing="1" cellpadding="0">
		<?php
		// Head line
		$sTitle = 'Segment Overview / Assign Company Cards';
		$iColSpan = 12;
		$bPrint = true;
		$bWindowTitle = true;
		include INCLUDE_PATH . 'page_header.php';
	
		// Sort URL
		// segment_id=1427&person_id=74&canvas_leads=1&special_segment=1
		?>
	
		<?php
		include 'admin_include_search.php';
		?>
	<form method="POST" action="?page=admin_segment_overview_assign&save=1">
	<input type="hidden" name="i_segment_id" value="<?= htmlspecialchars($_GET['segment_id']) ?>">
	<input type="hidden" name="i_special_segment" value="<?= htmlspecialchars($_GET['special_segment']) ?>">
	<input type="hidden" name="i_task" value="<?= htmlspecialchars($_GET['task']) ?>">
	<input type="hidden" name="i_type" value="<?= htmlspecialchars($_GET['type']) ?>">
	<input type="hidden" name="i_person_id" value="<?= htmlspecialchars($_GET['person_id']) ?>">
	<input type="hidden" name="i_search" value="<?= htmlspecialchars($_GET['search']) ?>">
	<input type="hidden" name="i_country_id" value="<?= htmlspecialchars($_GET['country_id']) ?>">
		<tr>
			<td>&nbsp;&nbsp;<a href="javascript:history.back();">Back</a><br><br></td>
		</tr>
		
		<tr>
			<td>&nbsp;&nbsp;<a href="<?= $sURL ?>name"><b>Name</b></a></td>
			<td><a href="<?= $sURL ?>case_nsi_appointment"><b>Last Appointment</b></a></td>
			<td><a href="<?= $sURL ?>case_nsi_comment_time"><b>Comment</b></a></td>
			<td><a href="<?= $sURL ?>case_nsi_lost_reason_time"><b>Lost</b></a></td>
			<td><a href="<?= $sURL ?>salesrep"><b>Sales Rep.</b></a></td>
			<td><a href="<?= $sURL ?>case_nsi_lost_reason_id"><b>Lost Reason</b></a></td>
			<td><a href="<?= $sURL ?>case_nsi_lost_reason_text"><b>Lost Comment</b></a></td>
			<td><a href="<?= $sURL ?>industry"><b>Industry</b></a></td>
			<td><a href="<?= $sURL ?>clients"><b>Clients</b></a></td>
			<td><a href="<?= $sURL ?>servers"><b>Servers</b></a></td>
			<td><a href="<?= $sURL ?>country"><b>Country</b></a></td>
			<td><b>Web</b></td>
			<td><a href="<?= $sURL ?>account_status"><b>Status</b></a></td>
			<td><a href="<?= $sURL ?>account_type"><b>Type</b></a></td>
			<td><a href="<?= $sURL ?>employees"><b>Employees</b></a></td>
			<td><a href="<?= $sURL ?>lead_source"><b>Source</b></a></td>
		</tr>
	
		<?php
		if (isset($_GET['order'])){
			$sSort = $_GET['order'];
		} else {
			$sSort = "name";
		}
		
		switch($sSort){
			case 'case_nsi_appointment':
			case 'case_nsi_lost_reason_time':
			case 'case_nsi_comment_time':
				function fSort($A, $B){
					if (strtotime($A[$GLOBALS['sSort']]) == strtotime($B[$GLOBALS['sSort']])){
						return 0;
					}
					return (strtotime($A[$GLOBALS['sSort']]) < strtotime($B[$GLOBALS['sSort']])) ? -1 : 1;
				}
				break;
				
			case "salesrep":
				function fSort($A, $B){
					$A = strtolower(fReturnRepDetailFromID($A['case_nsi_lost_person_id']));
					$B = strtolower(fReturnRepDetailFromID($B['case_nsi_lost_person_id']));
					return strcmp($A, $B);
				}
				break;
				
			case "country":
				function fSort($A, $B){
					$A = strtolower(fCountryNameFromID($A['invoice_country']));
					$B = strtolower(fCountryNameFromID($B['invoice_country']));
					return strcmp($A, $B);
				}
				break;
			
			case 'name':
			case 'account_status':
			case 'account_type':
			case 'lead_source':
			case 'case_nsi_lost_reason_id':
			case 'case_nsi_lost_reason_text':
				function fSort($A, $B){
					$A = strtolower($A[$GLOBALS['sSort']]);
					$B = strtolower($B[$GLOBALS['sSort']]);
					return strcmp($A, $B);
				}
				break;
			case "clients":
				function fSort($A, $B){
					$A = (int) $A['category_clients'];
					$B = (int) $B['category_clients'];
					return $A > $B;
				}
				break;
			case "servers":
				function fSort($A, $B){
					$A = (int) $A['category_servers'];
					$B = (int) $B['category_servers'];
					return $A > $B;
				}
				break;
			case "employees":
				function fSort($A, $B){
					$A = (int) $A['company_employees'];
					$B = (int) $B['company_employees'];
					return $A > $B;
				}
				break;
		}
		
		uasort($aResults, "fSort");
		// Result
		$aRepData['person_id'] = (int) $_GET['person_id'];
		while ( list($iCustomerID, $aData) = each($aResults) ) {
			// Skip when filtering by lost date / time
			if ( $bFilterLost == true ){
				$sVIlost = strtotime( $aData['case_vi_lost_reason_time'] );
				$sNSIlost = strtotime( $aData['case_nsi_lost_reason_time'] );
				$sCRClost = strtotime( $aData['case_crc_lost_reason_time'] );
				$sPartnerlost = strtotime( $aData['case_partner_lost_reason_time'] );
				$sCSClost = strtotime( $aData['case_csc_lost_reason_time'] );

				if ( ( $sVIlost == "" ) && ( $sNSIlost == "" ) && ( $sCRClost == "" ) ){
					continue;
				}

				if ( $_GET['account_type'] == "TLA" ){
					if ( ( ( $sVIlost >= $sStartDate ) || ( $sVIlost <= $sEndDate ) ) ){
						continue;
					}
				}

				if ( ( $_GET['account_type'] == "CSI" ) || ( $_GET['account_type'] == "" ) || ( $_GET['account_type'] == "all" ) ){
					if ( ( ( $sNSIlost >= $sStartDate ) || ( $sNSIlost <= $sEndDate ) ) ){
						continue;
					}
				}

				if ( $_GET['account_type'] == "SA" ){
					if ( ( ( $sCRClost >= $sStartDate ) || ( $sCRClost <= $sEndDate ) ) ){
						continue;
					}
				}

				if ( $_GET['account_type'] == "Partner" ){
					if ( ( ( $sPartnerlost >= $sStartDate ) || ( $sPartnerlost <= $sEndDate ) ) ){
						continue;
					}
				}

				if ( $_GET['account_type'] == "CSC" ){
					if ( ( ( $sCSClost >= $sStartDate ) || ( $sCSClost <= $sEndDate ) ) ){
						continue;
					}
				}
			}

			if ( $_GET['canvas_leads'] && fSkipCanvasLead( $aData, $aRepData ) ) {
				continue;
			}
	
			// Segment Name
			$sSegmentName = fSegmentNameFromID( $aData['segment_id'] );
	
			// WWW Output
			$sWWW = str_replace('"', '', ( !preg_match('^http', $aData['web']) ? 'http://' . $aData['web'] : $aData['web'] ) );
		?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td colspan="5" style="padding-left: 5px;"><?= ++$iCount ?>. <label><input type="checkbox" name="company[<?= $iCustomerID ?>][segment]" value="1"><b><a href="?page=customer&amp;cst_id=<?= $iCustomerID ?>"><?= htmlspecialchars($aData['name']) ?></a></b></label>&nbsp;<?=fGenerateSegmentSelectBox($iCustomerID, $aData['segment_id']);?></td>
				<td><input type="checkbox" name="company[<?= $iCustomerID ?>][trash]" value="1"><b>Trash Customer</b></td>
				<td><select name="company[<?= $iCustomerID ?>][country]"><option value="0"> - Select new country - </option><?= $sCountryOptions ?></select></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<tr bgcolor="#DEDEDE">
				<td width="10%" style="padding-left: 25px;"><?= caseDetails($aData['case_vi_id'], $aData['case_vi_person'], $aData['case_vi_appointment'], $sURL, $iCustomerID, $aData['account_status'], 'vi'); ?></td>
				<td width="15%"><?= htmlspecialchars($aData['case_vi_appointment']) ?></td>
				<td width="5%"><?= fReturnTimeSince( $aData['case_vi_comment_time'] ) ?></td>
				<td width="5%"><?= fReturnTimeSince( $aData['case_vi_lost_reason_time'] ) ?></td>
				<td width="10%"><?= fReturnRepDetailFromID($aData['case_vi_lost_person_id']) ?></td>
				<td width="15%"><?= fReturnLostReason($aData['case_vi_lost_reason_id']) ?></td>
				<td width="15%"><?= htmlspecialchars($aData['case_vi_lost_reason_text']) ?></td>
				<td width="15%"><?= htmlspecialchars( $GLOBALS['aIndustries'][$aData['company_industry']] ) ?></td>
				<td width="15%"><?= htmlspecialchars( $aData['category_clients'] ) ?></td>
				<td width="15%"><?= htmlspecialchars( $aData['category_servers'] ) ?></td>

				<td width="15%"><?= htmlspecialchars(fGetCountryName($aData['invoice_country'])); ?></td>
				<td width="3%"><?= ( strlen($aData['web']) > 7 ? '<a href="' . $sWWW . '" title="' . $sWWW . '" target="_blank">WWW</a>' : '-' ) ?></td>
				<td width="3%"><?= htmlspecialchars($aData['account_status']) ?></td>
				<td width="3%"><?= htmlspecialchars($aData['account_type']) ?></td>
				<td width="3%"><?= number_format($aData['company_employees']) ?></td>
				<td width="3%"><?= fReturnLeadSource($aData['lead_source']) ?></td>
			</tr>
			<tr bgcolor="#DEDEDE">
				<td style="padding-left: 25px;"><?= caseDetails($aData['case_nsi_id'], $aData['case_nsi_person'], $aData['case_nsi_appointment'], $sURL, $iCustomerID, $aData['account_status'], 'nsi'); ?></td>
				<td><?= htmlspecialchars($aData['case_nsi_appointment']) ?></td>
				<td><?= $aData['case_nsi_comment_time'] ?></td>
				<td><?= fReturnTimeSince( $aData['case_nsi_lost_reason_time'] ) ?></td>
				<td><?= fReturnRepDetailFromID($aData['case_nsi_lost_person_id']) ?></td>
				<td><?= fReturnLostReason($aData['case_nsi_lost_reason_id']) ?></td>
				<td><?= htmlspecialchars($aData['case_nsi_lost_reason_text']) ?></td>
			</tr>
			<tr bgcolor="#DEDEDE">
				<td style="padding-left: 25px;"><?= caseDetails($aData['case_crc_id'], $aData['case_crc_person'], $aData['case_crc_appointment'], $sURL, $iCustomerID, $aData['account_status'], 'crc'); ?></td>
				<td><?= htmlspecialchars($aData['case_crc_appointment']) ?></td>
				<td><?= fReturnTimeSince( $aData['case_crc_comment_time'] ) ?></td>
				<td><?= fReturnTimeSince( $aData['case_crc_lost_reason_time'] ) ?></td>
				<td><?= fReturnRepDetailFromID($aData['case_crc_lost_person_id']) ?></td>
				<td><?= fReturnLostReason($aData['case_crc_lost_reason_id']) ?></td>
				<td><?= htmlspecialchars($aData['case_crc_lost_reason_text']) ?></td>
			</tr>
			<tr bgcolor="#DEDEDE">
				<td style="padding-left: 25px;"><?= caseDetails($aData['case_partner_id'], $aData['case_partner_person'], $aData['case_partner_appointment'], $sURL, $iCustomerID, $aData['account_status'], 'partner'); ?></td>
				<td><?= htmlspecialchars($aData['case_partner_appointment']) ?></td>
				<td><?= fReturnTimeSince( $aData['case_partner_comment_time'] ) ?></td>
				<td><?= fReturnTimeSince( $aData['case_partner_lost_reason_time'] ) ?></td>
				<td><?= fReturnRepDetailFromID($aData['case_partner_lost_person_id']) ?></td>
				<td><?= fReturnLostReason($aData['case_partner_lost_reason_id']) ?></td>
				<td><?= htmlspecialchars($aData['case_partner_lost_reason_text']) ?></td>
			</tr>
			<tr bgcolor="#DEDEDE">
				<td style="padding-left: 25px;"><?= caseDetails($aData['case_csc_id'], $aData['case_csc_person'], $aData['case_csc_appointment'], $sURL, $iCustomerID, $aData['account_status'], 'csc'); ?></td>
				<td><?= htmlspecialchars($aData['case_csc_appointment']) ?></td>
				<td><?= fReturnTimeSince( $aData['case_csc_comment_time'] ) ?></td>
				<td><?= fReturnTimeSince( $aData['case_csc_lost_reason_time'] ) ?></td>
				<td><?= fReturnRepDetailFromID($aData['case_csc_lost_person_id']) ?></td>
				<td><?= fReturnLostReason($aData['case_csc_lost_reason_id']) ?></td>
				<td><?= htmlspecialchars($aData['case_csc_lost_reason_text']) ?></td>
			</tr>
		<?php
		}
		?>
		
		<tr>
			<td><br><br></td>
		</tr>
	
		<?php
		// Select segments
		$sSegments = '';
		$aSegments = DBGetRows('crm.segments', "segments.segment_status = 0 AND segments.segment_name LIKE 'CRM2%' AND segments.segment_name NOT LIKE '% - Lost Leads'", 'segment_name');
		while ( list($iKey, $aSegment) = each($aSegments) ) {
			$aSegment['segment_name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);
	
			$sSegments .= '<option value="' . $aSegment['segment_id'] . '">' . htmlspecialchars($aSegment['segment_name']) . '</option>';
		}
		?>
	
		<tr>
			<td>
				&nbsp;&nbsp;<b>Select Segment:</b><br>
				&nbsp;&nbsp;<?= makeSegmentCombo( -1, '', "segment_id" ); ?>
			</td>
		</tr>
	
		<tr>
			<td>
				<br>&nbsp;&nbsp;<input type="submit" value="Update">
			</td>
		</tr>
	
	</table>
	
	</form>
	<?php
}
?>

<div id="change_ownership_form" style="display: none; background: #ffffff; border: 2px solid #000000; width: 500px; position: fixed; top: 20px; left: 300px; padding: 3px;">
<form method="POST" action="?<?= htmlspecialchars($_SERVER['QUERY_STRING']) ?>&save=2">
<input type="hidden" name="segment_id" value="<?= (int) $_GET['segment_id'] ?>">
<input type="hidden" name="case_id" id="case_id" value="">
<b>Change Ownership:</b><br>
<br>
<b>New Appointment:</b><br>
<input type="text" name="appointment" id="appointment"><br>
<br>
<b>Sales People:</b><br>
<label><input type="radio" name="new_rep" value="0" checked> - Remove Sales Rep. -</label><br>
<?php
$aSalesReps = DBGetRows('crm.salespeople', "salespeople.lost_segment_id > 0 AND salespeople.display = 1", 'name');

$iRows = ceil(sizeof($aSalesReps)/2);
$iPos = 0;

echo "<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">";
// Loop through results and sort according to segments
for($i=0;$i<$iRows;$i++){
	if ($i % 2 == 0){
		echo "<tr>";
	}
	echo '<td><label><input type="radio" name="new_rep" value="' . $aSalesReps[$iPos]['person_id'] . '">' . htmlspecialchars( $aSalesReps[$iPos]['name'] ) .'</label></td>';
	$iPos++;
	if (isset($aSalesReps[$iPos])){
		echo '<td><label><input type="radio" name="new_rep" value="' . $aSalesReps[$iPos]['person_id'] . '">' . htmlspecialchars( $aSalesReps[$iPos]['name'] ) .'</label></td>';
	} else {
		echo '<td>&nbsp;</td>';
	}
	$iPos++;
	if ($i % 2 == 0){
		echo "</tr>";
	}
}
?>
</table>
<br>
<input type="submit" value="Save"> <input type="submit" onClick="document.getElementById('change_ownership_form').style.display = 'none'; return false;" value="Cancel">
</form>
</div>

<?php
function caseDetails( $caseID, $casePerson, $appointment, $sURL, $iCustomerID, $accountStatus, $caseStr ) {
	echo '<a href="?page=customer&amp;cst_id=' . $caseID . '">' . strtoupper( ( $caseStr != 'nsi' ? $caseStr : 'CSI' ) ) . ': ' . htmlspecialchars($casePerson) . '</a> ';
	
	if ( $caseID && $accountStatus != 'Customer' ) {
		echo '[<a href="javascript:void(0);" onClick="fDisplayOwnershipBox(' . $caseID . ', \'' . htmlspecialchars($appointment) . '\');">Ownership</a>]';
	}
	
	if ( !$caseID ) {
		echo '[<a href="' . $sURL . '&amp;save=3&amp;master_id=' . $iCustomerID . '&amp;case=card_' . $caseStr . '" onclick="return confirm(\'Please confirm that you wish to create a VI case on this company\');">Create Case</a>]';
	}
}

function fReturnTimeSince( $sTime ) {
	if (trim( $sTime ) != ""){
		$iDays = round(((time()-strtotime( $sTime ))/86400)*1);
		if ($iDays == 0){
			return "Today";
		} elseif ($iDays == 1){
			return "Yesterday";
		} else {
			return number_format($iDays, 0, "", ",")." days";
		}
	}
}
?>
