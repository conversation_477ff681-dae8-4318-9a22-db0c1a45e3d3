<?php
// Command line request (direct request)
if ( $_SERVER['argv'][0] ){
	$bLogOnly = true;

	// Load configuration
	include '/home/<USER>/public_html/crmadmin/crm2/sales/configuration.php';

	// Load sales functions
	include INCLUDE_PATH . 'sales_functions.php';

	// Load global functions
	include INCLUDE_PATH . 'global_functions.php';

	// Open DB Connection
	fOpenDatabase();	
} else {
	// Access Level: 1
	// Admin
	// SM
	// Lead Management
	fVerifyAccess( $aRepData['person_id'], 1 );

	// Display content
	$bLogOnly = false;
}

// Select data
$aSalesReps = DBGetRows('crm.salespeople', "salespeople.lost_segment_id > 0", 'name');

// Loop through results and sort according to segments
$aData = array();
while ( list($iKey, $aSaleRep) = each($aSalesReps) ) {

	// Select customer data
//	$aCustomers = DBGetRows('crm.cst', "cst.person_id = '" . $aSaleRep['person_id'] . "'");
	$aCustomers = DBGetRows('crm.cst', "cst.person_id = '" . $aSaleRep['person_id'] . "' AND cst.case_name IN ('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc')");
	while ( list($iKey, $aCustomer) = each($aCustomers) ) {
		// Array structure
		if ( $aCustomer['appointment'] && $aCustomer['appointment'] != '0000-00-00 00:00:00' ) {
			// Expired?
			if ( strtotime($aCustomer['appointment']) < time() && $aCustomer['appointment'] != date('Y-m-d') . ' 00:00:00' ) {
				$aData[$aSaleRep['person_id']]['expired_appointments']++;
			} elseif ( substr($aCustomer['appointment'], 11, 8) == '00:00:00' ) {
				$aData[$aSaleRep['person_id']]['callbacks']++;
			} else {
				$aData[$aSaleRep['person_id']]['appointments']++;
			}

			// Total
			$aData[$aSaleRep['person_id']]['total']++;
		}
	}

	// Transfer data from 'salespeople'
	$aData[$aSaleRep['person_id']]['lost_segment'] = $aSaleRep['lost_segment_id'];
	$aData[$aSaleRep['person_id']]['canvas_segment'] = $aSaleRep['canvas_segment_id'];

	if ( !$bLogOnly ) {
		// Customers in lost segment (fluid number, changes when lead management takes actions)
		$aData[$aSaleRep['person_id']]['lost'] = count(DBQueryGetRows("SELECT cst_id FROM crm.cst WHERE segment_id = '" . $aSaleRep['lost_segment_id'] . "'"));
	} else {
		// Customers logged as lost (Fixed increasing number)
		$aData[$aSaleRep['person_id']]['lost'] = count(DBQueryGetRows("SELECT distinct cst_id FROM crm.lost_log WHERE person_id = '" . $aSaleRep['person_id'] . "'"));
	}

	// Customers (approx.)
	$aData[$aSaleRep['person_id']]['customers'] = count(DBQueryGetRows("SELECT * FROM crm.saleslog WHERE person_id = '" . $aSaleRep['person_id'] . "' && product_price - discount > 0 && expires_date > NOW() && (status != 3 || status IS NULL);"));

	// Total
	$aData[$aSaleRep['person_id']]['total'] += $aData[$aSaleRep['person_id']]['customers'];

	// Leads available
	$aLeadCustomers = DBGetRows('crm.cst', "cst.segment_id IN (".arrayToSqlIn( getSubsegments( $aRepData['canvas_segment_id'] ) ).") ");
	while ( list($iKey, $aLeadCustomer) = each($aLeadCustomers) ) {
		if ( fSkipCanvasLead( $aLeadCustomer, $aSaleRep ) ) {
			continue;
		}
		$aData[$aSaleRep['person_id']]['leads_available']++;
		$aData[$aSaleRep['person_id']]['leads_available_csts'][] = $aLeadCustomer['cst_id'];
	}
}

if ( $bLogOnly == true ) {
	echo "LOG NOW!";
	while ( list($iPersonID, $aPersonData) = each($aData) ) {
		echo 'Logging: ' . $iPersonID . "\n";
		DBQuery("INSERT INTO crm.sales_rep_lead_usage (person_id, logged, total_leads, lost_leads) VALUES('" . $iPersonID . "', NOW(), '" . $aPersonData['total'] . "', '" . $aPersonData['lost'] . "')");
	}
	exit();
}
?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Overview of Sale Representatives';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

	// Result
	while ( list($iPersonID, $aPersonData) = each($aData) ) {
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%" style="padding-left: 5px;"><b><?= fReturnRepDetailFromID( $iPersonID ) ?></b>&nbsp;</td>
			<td width="80%"><b><?= number_format($aPersonData['total']) ?></b></td>
		</tr>
		<tr>
			<td style="padding-left: 25px; border-top: 1px solid #000000;" bgcolor="#DEDEDE"><b>Non-expired Customers</b></td>
			<td bgcolor="#DEDEDE" style="border-top: 1px solid #000000;"><b><a href="?page=customer_report&amp;person_id=<?= $iPersonID ?>"><?= number_format($aPersonData['customers']) ?></a></b> (approx.)</td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><b>Appointments</b></td>
			<td bgcolor="#DEDEDE"><b><a href="?page=admin_segment_overview_assign&amp;person_id=<?= $iPersonID ?>&amp;task=appointments&amp;type=active"><?= number_format($aPersonData['appointments']) ?></a></b></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><b>Appointments - Call Backs</b></td>
			<td bgcolor="#DEDEDE"><b><a href="?page=admin_segment_overview_assign&amp;person_id=<?= $iPersonID ?>&amp;task=appointments&amp;type=callback"><?= number_format($aPersonData['callbacks']) ?></a></b></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><b>Appointments - Expired</b></td>
			<td bgcolor="#DEDEDE"><b><a href="?page=admin_segment_overview_assign&amp;person_id=<?= $iPersonID ?>&amp;task=appointments&amp;type=expired"><?= number_format($aPersonData['expired_appointments']) ?></a></b></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><b>Lost Leads</b></td>
			<td bgcolor="#DEDEDE"><b><a href="?page=admin_segment_overview_assign&amp;segment_id=<?= $aPersonData['lost_segment'] ?>&amp;special_segment=1"><?= number_format($aPersonData['lost']) ?></a></b></td>
		</tr>
		<tr>
			<td style="padding-left: 25px;" bgcolor="#DEDEDE"><b>Leads available</b></td>
			<td bgcolor="#DEDEDE" style="color: <?= $aPersonData['leads_available'] < 10 ? 'yellow' : $aPersonData['leads_available'] < 2 ? 'red' : 'black' ?>;"><b><a title="<?= implode(',', $aPersonData['leads_available_csts']) ?>" href="?page=admin_segment_overview_assign&amp;segment_id=<?= $aPersonData['canvas_segment'] ?>&amp;person_id=<?= $iPersonID ?>&amp;canvas_leads=1&amp;special_segment=1"><?= number_format($aPersonData['leads_available']) ?></a></b></td>
		</tr>
	<?php
	}
	?>
</table>
<br><br>
