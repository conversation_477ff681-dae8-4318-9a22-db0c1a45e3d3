<?php
	// User access configuration file
	// Required by: global_function.php -> fVerifyAccess()
	// NOTE: DO NOT add any other configuration options, this file works with util/add_sales_person.php

	$aUserAccess[1] = 4; // JB: Admin
	$aUserAccess[2] = 3; // TP: SM
	$aUserAccess[3] = 3; // HZ: SM
	$aUserAccess[4] = 3; // NHR: SM
	$aUserAccess[5] = 4; // TK: Admin
	$aUserAccess[6] = 1; // MM: Full search
	$aUserAccess[27] = 1; // KKM: Full Search
	$aUserAccess[47] = 2; // VBAKKER: Lead Management
	$aUserAccess[167] = 2; // MNIXON: Student Assistant
	$aUserAccess[166] = 2; // mtodorov: Lead Management
	$aUserAccess[134] = 2; // AOLSEN: Lead Management
	$aUserAccess[106] = 4; // STIMMERMAN: Admin
	$aUserAccess[74] = 4; // AO: Admin
	$aUserAccess[95] = 2; // KMIKKELSEN: Lead Management
	$aUserAccess[112] = 2; // RJOHANSSON: Lead Management
	$aUserAccess[82] = 4; // LDAMGAARD: Admin
	$aUserAccess[98] = 2; // caarslew: Lead Management
	$aUserAccess[207] = 2; // kmarek: Lead Management
	$aUserAccess[138] = 2; // RENKEULONSKA: Lead Management
	$aUserAccess[135] = 2; // burak beklenoglu: Lead Management (temp)
	$aUserAccess[85] = 4; // naarslew: Admin
	$aUserAccess[120] = 2; // MADHEV: Lead Management
//	$aUserAccess[121] = 2; // ASHLEY: Lead Management
	$aUserAccess[132] = 1; // lcorreia: Full Search
	$aUserAccess[221] = 1; // jmellberg: Full Search (stimmerman)
	$aUserAccess[136] = 1; // iivarsson: Full Search
	$aUserAccess[169] = 1; // omarin: Full Search
	$aUserAccess[186] = 1; // wmahmood: Full Search (stimmerman)
	$aUserAccess[306] = 1; // ajellas: Full Search (stimmerman)
	$aUserAccess[204] = 1; // rdanailov: Full Search (stimmerman)
	$aUserAccess[125] = 1; // polsen: Admin
	$aUserAccess[153] = 1; // aengel: full search
	$aUserAccess[178] = 1; // orochford: full search
	$aUserAccess[133] = 4; // mjensen: Admin
	$aUserAccess[131] = 4; // lpivetal: Admin (stimmerman, requested by mjensen)
	$aUserAccess[155] = 2; // sandersen: Lead Management
	$aUserAccess[165] = 4; // jbratting: Admin
	$aUserAccess[118] = 1; // pmaartensson: full search (jb)
	$aUserAccess[184] = 2; // nandersen: LM (stimmerman)
	$aUserAccess[189] = 2; // HVALIKANGAS: LM (stimmerman)
	$aUserAccess[190] = 2; // MMOLLER: LM (stimmerman)
	$aUserAccess[188] = 1; // bbirkvald: full search (jb)
	$aUserAccess[117] = 1; // bseiling: full search (jb)
	$aUserAccess[71] = 1; // radragna: full search (jb)
	$aUserAccess[144] = 1; // cfornas: full search (jb)
	$aUserAccess[205] = 4; // pgraux: admin (jb)
	$aUserAccess[194] = 1; // mstengaard: full search (stimmerman)
	$aUserAccess[209] = 2; // jribe: LM (jb)
	$aUserAccess[222] = 3; // tzeihlund: SM (stimmerman)
	$aUserAccess[159] = 1; // bflottmann: full search (stimmerman)
	$aUserAccess[344] = 1; // sojensen: full search (stimmerman)
	$aUserAccess[230] = 1; // ghillgren: full search (stimmerman)
	$aUserAccess[130] = 1; // nfriberg: full search (stimmerman)
	$aUserAccess[113] = 1; // mmckeown: full search (stimmerman)
	$aUserAccess[224] = 2; // randersen: Lead Management (jb)
	$aUserAccess[232] = 2; // cbank: Lead Management (stimmerman)
	$aUserAccess[277] = 2; // ccachovan: Lead Management (stimmerman)
	$aUserAccess[234] = 2; // jkoller: Lead Management (stimmerman)
	$aUserAccess[75] = 1;  // mawakian: full search (stimmerman, requested by TP)
	$aUserAccess[258] = 2; // mlow: Lead Management (stimmerman)
	$aUserAccess[264] = 2; // laarslew: Lead Management
	$aUserAccess[278] = 1; // jkhan: full search
?>
