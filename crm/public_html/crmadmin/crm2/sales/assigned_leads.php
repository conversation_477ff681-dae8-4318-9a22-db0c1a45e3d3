<?php
// Period, input or today
$sFrom = ( $_POST['from'] ? $_POST['from'] : date('Y-m-1') );
$sTo = ( $_POST['to'] ? $_POST['to'] : date('Y-m-31') );

?>

<form method="POST" action="?page=assigned_leads">
<table width="100%" cellpadding="0" cellspacing="0">
	<?php
	$sTitle = 'Customers Leads';
	$iColSpan = 5;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="30%" class="TablePadding">
			<b>Select period to display</b><br>
			<input type="text" value="<?= htmlspecialchars($sFrom) ?>" name="from" style="width: 150px;"> - <input type="text" value="<?= htmlspecialchars($sTo) ?>" name="to" style="width: 150px;"> <input type="submit" value="Display"><br>
			<label><input type="checkbox" name="include_no_assigned_date" value="1"<?= ( $_POST['include_no_assigned_date'] == 1 ? ' checked' : '' ) ?>> Include customers without "Assigned" date</label>
		</td>
		<td valign="top" colspan="4">
			<b>Limit to Lost Reason</b><br>
			<select name="lost_reason">
				<option> - Select Lost Reason - </option>
				<?php
				for ( $iCount = 1 ; $iCount <= 15 ; $iCount++ ) {
					echo '<option value="' . $iCount . '"' . ( $_POST['lost_reason'] == $iCount ? ' selected' : '' ) . '>' . fReturnLostReason( $iCount ) . '</option>';
				}
				?>
			</select>
		</td>
	</tr>

	<tr>
		<td><br><br></td>
	</tr>

	<?php
	$sTitle = 'Leads';
	$iColSpan = 5;
	$bPrint = false;
	$bWindowTitle = false;
	include INCLUDE_PATH . 'page_header.php';

	// Select leads that match selections
	if ( in_array($aRepData['person_id'], array(1,74,99,102,103)) ) {
		$aCustomers = DBGetRows('crm.cst', "cst.person_id = '" . $aRepData['person_id'] . "' AND case_name IN('card_vi','card_nsi','card_partner', 'card_crc','Company Card') AND cst.appointment >= '" . $sFrom . "' AND cst.appointment <= '" . $sTo . "'", 'appointment DESC');
	} else {
		$aCustomers = DBGetRows('crm.cst', "cst.segment_id IN (".arrayToSqlIn( getSubsegments( $aRepData['canvas_segment_id'] ) ).") AND ( (cst.segment_assigned >= '" . $sFrom . "' AND cst.segment_assigned <= '" . $sTo . "')" . ( !$_POST['include_no_assigned_date'] ? " AND segment_assigned" : "OR segment_assigned IS NULL" ) . ")", 'segment_assigned DESC, cst_id DESC');
	}
	?>

	<tr>
		<td class="TablePadding TableSubHeadline TableGreyBackground">Company</td>
		<td class="TableSubHeadline TableGreyBackground">Assigned</td>
		<td class="TableSubHeadline TableGreyBackground">Next Appointment</td>
		<td class="TableSubHeadline TableGreyBackground">Lost Reason</td>
		<td class="TableSubHeadline TableGreyBackground">Lost Time</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>

	<?php
	while ( list($iKey, $aCustomer) = each($aCustomers) ) {
		if ( !in_array($aRepData['person_id'], array(1,74,99,102,103)) ) {
			// Select case
			$aCase = DBGetRow('crm.cst', "cst.master_id = '" . $aCustomer['cst_id'] . "' AND case_name = 'card_nsi'");

			// Skip if no case id
			if ( !$aCase['cst_id'] ) {
				continue;
			}
		} else {
			$aCase = $aCustomer;
		}

		// Lost?
		$aLostLog = DBQueryGetRows("SELECT * FROM crm.lost_log WHERE lost_log.cst_id = '" . $aCase['cst_id'] . "' ORDER BY lost_log.lost_time DESC LIMIT 1");

		// Limited to a Lost Reason
		if ( $_POST['lost_reason'] > 0 && $_POST['lost_reason'] != $aLostLog[0]['reason'] ) {
			continue;
		}

		// Font color
		if ( $aLostLog[0]['reason'] ) {
			$sFontColor = 'red';
		} else {
			$sFontColor = 'black';
		}

		// Background color
		$sBackground = ( $sBackground != '#FFFFFF' ? '#FFFFFF' : '#DEDEDE' );

		echo '
	<tr style="background-color: ' . $sBackground . ';">
		<td class="TablePadding"><a href="?page=customer&amp;cst_id=' . $aCase['cst_id'] . '" style="color: ' . $sFontColor . '">' . htmlspecialchars($aCustomer['name']) . '</a></td>
		<td style="color: ' . $sFontColor . '">' . ( $aCustomer['segment_assigned'] ? $aCustomer['segment_assigned'] : '- n/a -' ) . '</td>
		<td style="color: ' . $sFontColor . '">' . ( $aCase['appointment'] ? $aCase['appointment'] : '- no appointment -' ) . '</td>
		<td style="color: ' . $sFontColor . '">' . fReturnLostReason($aLostLog[0]['reason']) . '</td>
		<td style="color: ' . $sFontColor . '">' . htmlspecialchars($aLostLog[0]['lost_time']) . '</td>
	</tr>';
	}
	?>


	<tr>
		<td><br><br></td>
	</tr>
</table>

</form>
