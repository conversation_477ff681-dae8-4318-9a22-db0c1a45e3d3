<?php
// Access Level: 1
// Admin
// SM
// Lead Management
fVerifyAccess( $aRepData['person_id'], 1 );

// Save
$bSaved = false;
if ( $_GET['save'] == 1 ) {
	$iAccountID = DBGetRowValue('ca.accounts', 'account_id', "account_id = '" . $_GET['account_id'] . "' && cst_id = *********");

	if ( $iAccountID > 0 && $_GET['cst_id'] > 0 ) {
		DBQuery("UPDATE ca.accounts SET cst_id = '" . $_GET['cst_id'] . "' WHERE account_id = '" . $iAccountID . "' LIMIT 1");
		DBQuery("UPDATE ca.nsi_customer_details SET cst_id = '" . $_GET['cst_id'] . "' WHERE account_id = '" . $iAccountID . "' LIMIT 1");
		DBQuery("UPDATE ca.nsi_base_settings SET cst_id = '" . $_GET['cst_id'] . "' WHERE account_id = '" . $iAccountID . "' LIMIT 1");
		$bSaved = true;
	}
}

?>

<table width="100%" cellspacing="1" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Attach CSI-BETA-Account to existing Customer Card';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td class="TablePadding">
				<?= $bSaved ? 'Data saved<br><br>' : '' ?>
				<form method="GET" action="?">
				<input type="hidden" name="page" value="admin_csi_beta">
				<input type="hidden" name="save" value="1">
				<b>Account ID (copy from beta email):</b><br>
				<input type="text" name="account_id" style="width: 200px;"><br>
				<br>
				<b>Existing Company ID:</b><br>
				<input type="text" name="cst_id" style="width: 200px;"><br>
				<br>
				<input type="submit" value="Save">
				</form>
			</td>
		</tr>
</table>
<br><br>
