<?php
// Select all contacts
$aContacts = DBGetRows('crm.contacts', "contacts.cst_id = '" . $GLOBALS['aCustomer']['Case']['cst_id'] . "'", 'contacts.position ASC');
?>
<script>
function hide_me( name ) {
	if ( document.getElementById( name ).style.display == 'none' ) {
		document.getElementById( name ).style.display = 'block';
	} else {
		document.getElementById( name ).style.display = 'none';
	}

	return false;
}
</script>
<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Loop through all contacts
	$iCount = 0;
	while ( list($iKey, $aContact) = each($aContacts) )
	{
		$subscription_type = array();
		// Get subscription types
		$_result = DBQueryGetRows("SELECT name FROM crm.subscriptions WHERE cst_id = '".(int)$GLOBALS['aCustomer']['Case']['cst_id']."' AND contact_id = '".(int)$aContact['id']."'");
		for ( $x = 0; $x < count( $_result ); $x++ ) {
			$subscription_type[$_result[$x]['name']] = $_result[$x]['name'];
		}
		$aContact['subscription_type'] = $subscription_type;
		$iCount = $iKey+1;
	?>
		<tr>
			<td><b><?= $iCount ?>. Contact person</b></td>
			<td><label><input type="checkbox" name="aContacts[<?= $iCount ?>][delete]" value="1" onClick="if ( this.checked == true ) { return confirm('Are you sure you wish to delete this contact?'); }"> Delete</label></td>
		</tr>
		<tr>
			<td width="20%" class="TablePadding">Name</td>
			<td width="80%"><input type="text" name="aContacts[<?= $iCount ?>][name]" value="<?= htmlspecialchars($aContact['name']) ?>" id="name_<?= $iCount ?>"></td>
		</tr>
		<tr>
			<td class="TablePadding">Title</td>
			<td>
				<select name="aContacts[<?= $iCount ?>][title]">
					<option> - Select Title - </option>
					<?php
					//reset($GLOBALS['aTitleOptions']);
					$aTitleOptions = fSortArrayOther($GLOBALS['aTitleOptions']);
					while ( list($iTitleID, $sTitle) = each($aTitleOptions) ) {
						echo '<option value="' . $iTitleID . '"' . ( $aContact['title'] == $iTitleID ? ' selected' : '' ) . '>' . htmlspecialchars($sTitle) . '</option>';
					}
					?>
				</select>
			</td>
		</tr>
		<tr>
			<td class="TablePadding">Phone</td>
			<td><input type="text" name="aContacts[<?= $iCount ?>][phone]" value="<?= htmlspecialchars($aContact['phone']) ?>"></td>
		</tr>
		<tr>
			<td class="TablePadding">Mobile</td>
			<td><input type="text" name="aContacts[<?= $iCount ?>][mobile]" value="<?= htmlspecialchars($aContact['mobile']) ?>"></td>
		</tr>
		<tr>
			<td class="TablePadding">Email</td>
			<td><input type="text" name="aContacts[<?= $iCount ?>][email]" value="<?= htmlspecialchars($aContact['email']) ?>" id="email_<?= $iCount ?>"></td>
		</tr>
		<tr>
			<td class="TablePadding">Classification</td>
			<td>
				<select name="aContacts[<?= $iCount ?>][competence]">
					<option>- Select Competence -</option>
					<option value="Active"<?= $aContact['competence'] == 'Active' ? ' selected' : '' ?>>Active</option>
					<option value="Expert"<?= $aContact['competence'] == 'Expert' ? ' selected' : '' ?>>Expert</option>
					<option value="Passive"<?= $aContact['competence'] == 'Passive' ? ' selected' : '' ?>>Passive</option>
				</select> |
				<label><input type="radio" name="aContacts[primary_contact]" value="<?= $iCount ?>"<?= $aContact['primary_contact'] ? ' checked' : '' ?>> Primary</label> |
				<label><input type="radio" name="aContacts[invoice]" value="<?= $iCount ?>" <?= $aContact['invoice'] ? ' checked' : '' ?>> Invoice</label> |
				<label><input type="checkbox" name="nurturing[<?= $iCount ?>]" <?= $aContact['nurturing'] ? ' checked' : '' ?>> Nurturing</label> |
				<label><input type="checkbox" name="aware[<?= $iCount ?>]" <?= $aContact['aware'] ? ' checked' : '' ?>> Knows Secunia</label> |
				<label><input type="checkbox" name="aContacts[<?= $iCount ?>][decision_maker]"<?= $aContact['decision_maker'] ? ' checked' : '' ?>> Decision</label> | <a href="javascript:;" onclick="location='mailto:' + document.getElementById('name_<?= $iCount ?>').value + ' <' + document.getElementById('email_<?= $iCount ?>').value + '>?subject=Secunia';">Send Email</a>
			</td>
		</tr>
		<tr valign="top">
			<td class="TablePadding">
				<a href="" onclick="return hide_me( '_dm<?= $iCount ?>' );">DM Subscriptions</a>
			</td>
			<td>
				<div id="_dm<?= $iCount ?>" name="_dm<?= $iCount ?>" style="display:none;">
					<table>
					<tr valign="top">
					<td>
					<nobr>
						<input type="checkbox" <?= $aContact['subscription_type']['CSI Trial Request'] == "CSI Trial Request" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][CSI Trial Request]">CSI Trial Request<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['VIM Trial Request'] == "VIM Trial Request" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][VIM Trial Request]">VIM Trial Request<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Report Request'] == "Report Request" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Report Request]">Report Request<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Contact us'] == "Contact us" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Contact us]">Contact us<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['<EMAIL>'] == "<EMAIL>" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][<EMAIL>]"><EMAIL><br/>
						<input type="checkbox" <?= $aContact['subscription_type']['PSI Partner'] == "PSI Partner" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][PSI Partner]">PSI Partner<br/>
					</nobr>
					</td>
					<td>
					<nobr>
						<input type="checkbox" <?= $aContact['subscription_type']['Partner Program'] == "Partner Program" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Partner Program]">Partner Program<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['PSI'] == "PSI" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][PSI]">PSI<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Conference'] == "Conference" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Conference]">Conference<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Conference (Import)'] == "Conference (Import)" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Conference (Import)]">Conference (Import)<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Database (Import - not approved contact)'] == "Database (Import - not approved contact)" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Database (Import - not approved contact)]">Database (Import - not approved contact)<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Database (Important - approved contact)'] == "Database (Important - approved contact)" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Database (Important - approved contact)]">Database (Important - approved contact)<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Online Campaigns'] == "Online Campaigns" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Online Campaigns]">Online Campaigns<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Press list (from <EMAIL>/ <EMAIL>)'] == "Press list (from <EMAIL>/ <EMAIL>)" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Press list (from <EMAIL>/ <EMAIL>)]">Press list (from <EMAIL>/ <EMAIL>)<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Corporate Newsletter (from <EMAIL>)'] == "Corporate Newsletter (from <EMAIL>)" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Corporate Newsletter (from <EMAIL>)]">Corporate Newsletter (from <EMAIL>)<br/>
						<input type="checkbox" <?= $aContact['subscription_type']['Nurturing program'] == "Nurturing program" ? " checked " : "" ?> name="subscription_type[<?= $iCount ?>][Nurturing program]">Nurturing program<br/>
					</nobr>
					<td>
					</tr>
					</table>
				</div>
			</td>
		</td>
		<tr>
			<td><br></td>
		</tr>
	<?php
	}
	?>
</table>

<div id="new_contacts"></div>

<script>
var iContactCount = <?= $iCount ?>;
</script>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%"><br></td>
		<td width="80%"><a href="javascript:void(0);" onClick="fNewContact(); this.innerHTML = '';">New contact</a></td>
	</tr>
</table>



<div id="contact_template" style="display: none;">

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="2"><b>##COUNTER##. Contact person (New)</b></td>
	</tr>
	<tr>
		<td width="20%" class="TablePadding">Name</td>
		<td width="80%"><input type="text" name="aContacts[##COUNTER##][name]" value=""></td>
	</tr>
	<tr>
		<td class="TablePadding">Title</td>
		<td>
			<select name="aContacts[##COUNTER##][title]">
				<option> - Select Title - </option>
				<?php
				reset($GLOBALS['aTitleOptions']);
				while ( list($iTitleID, $sTitle) = each($GLOBALS['aTitleOptions']) ) {
					echo '<option value="' . $iTitleID . '"' . ( $aContact['title'] == $iTitleID ? ' selected' : '' ) . '>' . htmlspecialchars($sTitle) . '</option>';
				}
				?>
			</select>
		</td>
	</tr>
	<tr>
		<td class="TablePadding">Phone</td>
		<td><input type="text" name="aContacts[##COUNTER##][phone]" value=""></td>
	</tr>
	<tr>
		<td class="TablePadding">Mobile</td>
		<td><input type="text" name="aContacts[##COUNTER##][mobile]" value=""></td>
	</tr>
	<tr>
		<td class="TablePadding">Email</td>
		<td><input type="text" name="aContacts[##COUNTER##][email]" value=""></td>
	</tr>
	<tr>
		<td class="TablePadding">Classification</td>
		<td>
			<select name="aContacts[##COUNTER##][competence]">
				<option>- Select Competence -</option>
				<option value="Active">Active</option>
				<option value="Expert">Expert</option>
				<option value="Passive">Passive</option>
			</select> |
			<label><input type="radio" name="aContacts[primary_contact]"> Primary<label> |
			<label><input type="radio" name="aContacts[invoice]"> Invoice<label> |
			<label><input type="checkbox" name="aContacts[##COUNTER##][decision_maker]"> Decision</label></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
</table>

</div>
