<?
// Store preference in cookie
if ( !isISA( $aRepData['person_id'] ) ){
	if ( $_GET['sort'] ) {
		setcookie('sort_pref', $_GET['sort']);
	} else {
		$_GET['sort'] = $_COOKIE['sort_pref'];
	}
	if ( isset($_GET['index']) ) {
		setcookie('index_pref', $_GET['index']);
	} else {
        	$_GET['index'] = $_COOKIE['index_pref'];
	}
}

// Move forward expired appointments
if ( $_GET['move_appointment'] ) {
	DBQuery("UPDATE crm.cst SET cst.appointment = '" . date('Y-m-d') . " 00:00:00' WHERE cst.person_id = '" . $aRepData['person_id'] . "' AND appointment <= '" . date('Y-m-d', time() - 86400) . " 23:59:59' AND cst.appointment != '0000-00-00 00:00:00' AND cst.appointment IS NOT NULL");
}

// Input date, only allow 5 days forward
if ( ereg('^[1-5]{1}$', $_GET['date']) || ( $aRepData['person_id'] == 118 && $_GET['date'] > 0 ) ) {
	$sDate = date('Y-m-d', time() + ( 86400 * $_GET['date'] ) );
} else {
	$sDate = date('Y-m-d');
}

// Limited display?
if ( strlen($_GET['limit']) > 0 || strlen($_COOKIE['limit']) > 0 ) {
	// Store selection?
	if ( strlen($_GET['limit']) == 0 ) {
		$_GET['limit'] = $_COOKIE['limit'];
	}

	// Enable limit?
	if ( $_GET['limit'] > 0 ) {
		$bLimit = true;
	}
}

// Select Appointments
$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 contacts.name AS contact,
 contacts.phone AS phone,
 cst.cst_id,
 cst.forecast_expectancy,
 cst.forecast_amount,
 cst.forecast_date,
 cst.case_status,
 cst.master_id,
 cst.timezone
FROM
 crm.cst
LEFT JOIN
 crm.contacts
ON
 contacts.cst_id = cst.cst_id AND
 contacts.primary_contact = 1
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment > '" . $sDate . " 00:00:00' AND
 cst.appointment <= '" . $sDate . " 23:59:59'
ORDER BY
 cst.appointment";
$aAppointments = DBQueryGetRows($sQuery);

// Select Callbacks
$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 contacts.name AS contact,
 contacts.phone AS phone,
 cst.cst_id,
 cst.flag,
 cst.forecast_expectancy,
 cst.forecast_amount,
 cst.forecast_date,
 cst.case_status,
 cst.master_id,
 cst.timezone
FROM
 crm.cst
LEFT JOIN
 crm.contacts
ON
 contacts.cst_id = cst.cst_id AND
 contacts.primary_contact = 1
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment = '" . $sDate . " 00:00:00'
ORDER BY
 cst.forecast_expectancy";
$aCallbacks = DBQueryGetRows($sQuery);

// Select Expired Appointments
$sQuery = "
SELECT
 cst.name,
 cst.appointment,
 contacts.name AS contact,
 contacts.phone AS phone,
 cst.cst_id,
 cst.forecast_expectancy,
 cst.forecast_amount,
 cst.forecast_date,
 cst.flag,
 cst.case_status,
 cst.master_id
FROM
 crm.cst
LEFT JOIN
 crm.contacts
ON
 contacts.cst_id = cst.cst_id AND
 contacts.primary_contact = 1
WHERE
 cst.person_id = '" . $aRepData['person_id'] . "' AND
 cst.appointment < '" . date('Y-m-d') . "' AND cst.appointment > '0000-00-00'
ORDER BY
 cst.appointment";
$aExpiredAppointments = DBQueryGetRows($sQuery);

// Select Canvas Leads
// Get the segment all subsegments addisgned to the current person
$sQuery = "
".( isISA( $aRepData['person_id'] ) ? "SELECT * FROM (" : "" )."
SELECT
 cst.name,
 cst.phone,
 cst.contact,
 cst.flag,
 cst.cst_id,
 cst.case_name,
 cst.master_id,
 cst.timezone,
 cst.callback_timestamp,
 cst.canvas_timestamp,
 cst.person_id
FROM
 crm.cst,
 crm.salespeople
WHERE
 salespeople.person_id = '" . $aRepData['person_id'] . "' AND
 cst.segment_id IN (".arrayToSqlIn( getSubsegments( $aRepData['canvas_segment_id'] ) ).") AND
 ( cst.appointment IS NULL OR !cst.appointment ) AND
 case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc') 
ORDER BY
 cst.canvas_timestamp ASC
 ".( isISA( $aRepData['person_id'] ) ? ") AS A WHERE person_id IS NULL OR person_id = 0 OR person_id = '".(int) $aRepData['person_id'] ."'" : "" );

$aCanvas = DBQueryGetRows($sQuery);
$iLimit = 25; // Number of canvas leads to show
$iMaxLimit = 300; // Maximum number of leads in canvas
$sClass = ' class="TableGreyBackground"';
?>
<table width="100%" cellspacing="0" cellpadding="0">
	<?
//	$sTitle = count($aAppointments) . ' Appointments (' . $sDate . ')' . ( $_GET['date'] < 5 || $aRepData['person_id'] == 118 ? ' <a href="?page=today&amp;date=' . ($_GET['date']+1) . '">Next day &gt;&gt;</a>' : '' ) . ( $_GET['date'] > 0 ? ' <a href="?page=today&amp;date=' . ($_GET['date']-1) . '">&lt;&lt; Previous day</a>' : '' ) . '&nbsp;&nbsp;<select onChange="location = \'?page=today&limit=\' + this.value;"><option value="0">Show all</option><option value="1"' . ( $_GET['limit'] == 1 ? 'selected' : '' ) . '>' . fReturnRegionName(1) . '</option><option value="2"' . ( $_GET['limit'] == 2 ? 'selected' : '' ) . '>' . fReturnRegionName(2) . '</option><option value="3"' . ( $_GET['limit'] == 3 ? 'selected' : '' ) . '>' . fReturnRegionName(3) . '</option></select>';
	$sTitle = count($aAppointments) . ' Appointments (' . $sDate . ')' . ( $_GET['date'] < 5 || $aRepData['person_id'] == 118 ? ' <a href="?page=today&amp;date=' . ($_GET['date']+1) . '">Next day &gt;&gt;</a>' : '' ) . ( $_GET['date'] > 0 ? ' <a href="?page=today&amp;date=' . ($_GET['date']-1) . '">&lt;&lt; Previous day</a>' : '' ) . '&nbsp;&nbsp;<select onChange="location = \'?page=today&limit=\' + this.value;"><option value="0">Show all</option><option value="1"' . ( $_GET['limit'] == 1 ? 'selected' : '' ) . '>' . fReturnRegionName(1) . '</option><option value="2"' . ( $_GET['limit'] == 2 ? 'selected' : '' ) . '>' . fReturnRegionName(2) . '</option><option value="3"' . ( $_GET['limit'] == 3 ? 'selected' : '' ) . '>' . fReturnRegionName(3) . '</option><option value="4"' . ( $_GET['limit'] == 4 ? 'selected' : '' ) . '>' . fReturnRegionName(4) . '</option><option value="5"' . ( $_GET['limit'] == 5 ? 'selected' : '' ) . '>' . fReturnRegionName(5) . '</option><option value="6"' . ( $_GET['limit'] == 6 ? 'selected' : '' ) . '>' . fReturnRegionName(6) . '</option></select>';
	$iColSpan = 11;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="5%" class="TableSubHeadline TableGreyBackground TablePadding">Time</td>
		<td width="2%" class="TableSubHeadline TableGreyBackground" title="Timezone">TZ</td>
		<td width="16%" class="TableSubHeadline TableGreyBackground">Company</td>
		<td width="4%" class="TableSubHeadline TableGreyBackground">Flag&nbsp;</td>
		<td width="8%" class="TableSubHeadline TableGreyBackground">Country</td>
		<td width="15%" class="TableSubHeadline TableGreyBackground">Contact</td>
		<td width="10%" class="TableSubHeadline TableGreyBackground">Phone</td>
		<td width="10%" class="TableSubHeadline TableGreyBackground">Lead source</td>
		<td width="10%" class="TableSubHeadline TableGreyBackground">Case Status</td>
		<td width="15%" class="TableSubHeadline TableGreyBackground">Tag</td>
		<td width="5%" class="TableSubHeadline TableGreyBackground">TAB</td>
	</tr>
	<?
	$bBorderDisplayed = false;
	while ( list($iKey, $aData) = each($aAppointments) ) {
		if ( $sClass === '' ) {
			$sClass = ' class="TableGreyBackground"';
		} else {
			$sClass = '';
		}

		// Get customer base details
		$aCustomerExtendedDetails = fGetCustomerDetails( $aData['cst_id'] );
		$aCustomerDetails = fCustomerBasicDetails( $aData['cst_id'], $aCustomerExtendedDetails );
		$sExtraStyle = '';
		if ( $aCustomerDetails['customer'] ) { 
			$sExtraStyle = ' style="color: green;"';
		}

		// Add border if just after "now"
		$sStyle = '';
		if ( strtotime($aData['appointment']) > time() && $bBorderDisplayed == false ) {
			$bBorderDisplayed = true;
			$sStyle = ' style="border-top: 1px solid #CECECE;"';
		}

		// Limit by region
		if ( $bLimit && $aCustomerDetails['region'] != $_GET['limit'] ) {
			continue;
		}
	?>
	<tr <?= $sClass . $sExtraStyle . fRowLink('?page=customer&cst_id=' . $aData['cst_id'] . '&right=overview') ?>>
		<td<?= $sStyle ?> class="TablePadding"><?= substr($aData['appointment'], 11, 5) ?></td>
		<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'timezone', "cst_id = '" . $aData['master_id'] . "'") ) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars( $aData['name'] == "" ? DBGetRowValue('crm.cst', 'name', "cst_id = '".(int)$aData['master_id']."'" ) : $aData['name'] ) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars($aData['flag']) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars(fCountryNameFromID($aCustomerExtendedDetails['Company']['invoice_country'])) ?></td>
		<td<?= $sStyle ?>><?= htmlspecialchars($aData['contact']) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars($aData['phone']) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars( $aData['lead_source'] ) ?></td>
		<td<?= $sStyle ?>><?= fReturnCaseStatusName( $aData['case_status'], $aData['forecast_expectancy'], $aData['forecast_amount'], $aData['forecast_date'], $aData['appointment'] ) ?>&nbsp;</td>
		<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'field_12', "cst_id = '" . $aData['master_id'] . "'") ) ?></td>
		<td<?= $sStyle ?>>[<a href="index.php?page=customer&amp;cst_id=<?= $aData['cst_id'] ?>&amp;tab=1" onclick="fAbortBubble(event);" style="text-decoration: none;" style="text-decoration: none;">TAB</a>]</td>
	</tr>
	<?
	}
	?>
</table>


<?
// Only display "Callback block" if there is actually some callbacks
if ( count($aCallbacks) ) {
	$sClass = ' class="TableGreyBackground"';
	?>
	
	<br><br>
	
	<table width="100%" cellspacing="0" cellpadding="0">
		<?
		$sTitle = count($aCallbacks) . ' Callbacks (' . $sDate . ')';
		$iColSpan = 11;
		$bPrint = false;
		$bWindowTitle = false;
		include INCLUDE_PATH . 'page_header.php';
		?>
		<tr>
			<td width="5%" class="TableSubHeadline TableGreyBackground TablePadding">&nbsp;</td>
			<td width="2%" class="TableSubHeadline TableGreyBackground" title="Timezone">TZ</td>
			<td width="16%" class="TableSubHeadline TableGreyBackground">Company</td>
			<td width="4%" class="TableSubHeadline TableGreyBackground">Flag&nbsp;</td>
			<td width="8%" class="TableSubHeadline TableGreyBackground">Country</td>
			<td width="15%" class="TableSubHeadline TableGreyBackground">Contact</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Phone</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Lead source</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Case Status</td>
			<td width="15%" class="TableSubHeadline TableGreyBackground">Tag</td>
			<td width="5%" class="TableSubHeadline TableGreyBackground">TAB</td>
		</tr>
		<?
		$bBorderDisplayed = false;
		while ( list($iKey, $aData) = each($aCallbacks) ) {
			if ( $sClass === '' ) {
				$sClass = ' class="TableGreyBackground"';
			} else {
				$sClass = '';
			}

			// Get customer base details
			$aCustomerExtendedDetails = fGetCustomerDetails( $aData['cst_id'] );
			$aCustomerDetails = fCustomerBasicDetails( $aData['cst_id'], $aCustomerExtendedDetails );
			$sExtraStyle = '';
			if ( $aCustomerDetails['customer'] ) {
				$sExtraStyle = ' style="color: green;"';
			}
	
			// Add border if just after "now"
			$sStyle = '';
			if ( strtotime($aData['appointment']) > time() && $bBorderDisplayed == false ) {
				$bBorderDisplayed = true;
				$sStyle = ' style="border-bottom: 1px solid #CECECE;"';
			}

			// Limit by region
			if ( $bLimit && $aCustomerDetails['region'] != $_GET['limit'] ) {
				continue;
			}
		?>
		<tr <?= $sClass . $sExtraStyle . fRowLink('?page=customer&cst_id=' . $aData['cst_id'] . '&right=overview') ?>>
			<td<?= $sStyle ?> class="TablePadding">Today</td>
			<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'timezone', "cst_id = '" . $aData['master_id'] . "'") ) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['name']) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['flag']) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars(fCountryNameFromID($aCustomerExtendedDetails['Company']['invoice_country'])) ?></td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['contact']) ?> </td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['phone']) ?> </td>
			<td<?= $sStyle ?>><?= htmlspecialchars( $aData['lead_source'] ) ?></td>
			<td<?= $sStyle ?>><?= fReturnCaseStatusName( $aData['case_status'], $aData['forecast_expectancy'], $aData['forecast_amount'], $aData['forecast_date'], $aData['appointment'] ) ?></td>
			<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'field_12', "cst_id = '" . $aData['master_id'] . "'") ) ?></td>
			<td<?= $sStyle ?>>[<a href="index.php?page=customer&amp;cst_id=<?= $aData['cst_id'] ?>&amp;tab=1" onclick="fAbortBubble(event);" style="text-decoration: none;" style="text-decoration: none;">TAB</a>]</td>
		</tr>
		<?
		}
		?>
	</table>

	<?
}

// Only display "Expired block" if there is actually some expired appointments
if ( count($aExpiredAppointments) ) {
	$sClass = ' class="TableGreyBackground"';
	?>
	
	<br><br>
	
	<table width="100%" cellspacing="0" cellpadding="0">
		<?
		$sTitle = '<blink><font color="red">' . count($aExpiredAppointments) . ' Expired Appointments</font></blink> <a hreF="?page=today&amp;move_appointment=1" onClick="return confirm(\'Are you sure?\');">Move ALL expired appointments up as callbacks for today</a>';
		$iColSpan = 11;
		$bPrint = false;
		$bWindowTitle = false;
		include INCLUDE_PATH . 'page_header.php';
		?>
		<tr>
			<td width="5%" class="TableSubHeadline TableGreyBackground TablePadding">&nbsp;</td>
			<td width="2%" class="TableSubHeadline TableGreyBackground" title="Timezone">TZ</td>
			<td width="16%" class="TableSubHeadline TableGreyBackground">Company</td>
			<td width="4%" class="TableSubHeadline TableGreyBackground">Flag&nbsp;</td>
			<td width="8%" class="TableSubHeadline TableGreyBackground">Country</td>
			<td width="15%" class="TableSubHeadline TableGreyBackground">Contact</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Phone</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Lead source</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Case Status</td>
			<td width="15%" class="TableSubHeadline TableGreyBackground">Tag</td>
			<td width="5%" class="TableSubHeadline TableGreyBackground">TAB</td>
		</tr>
		<?
		$bBorderDisplayed = false;
		while ( list($iKey, $aData) = each($aExpiredAppointments) ) {
			if ( $sClass === '' ) {
				$sClass = ' class="TableGreyBackground"';
			} else {
				$sClass = '';
			}
	
			// Add border if just after "now"
			$sStyle = '';
			if ( strtotime($aData['appointment']) > time() && $bBorderDisplayed == false ) {
				$bBorderDisplayed = true;
				$sStyle = ' style="border-bottom: 1px solid #CECECE;"';
			}
			$aCustomerExtendedDetails = fGetCustomerDetails( $aData['cst_id'] );
		?>
		<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aData['cst_id'] . '&right=overview') ?>>
			<td<?= $sStyle ?> class="TablePadding"><?= date('H:i', strtotime($aData['appointment']))."<br>".date('j. M \'y', strtotime($aData['appointment'])) ?></td>	
			<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'timezone', "cst_id = '" . $aData['master_id'] . "'") ) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['name']) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['flag']) ?>&nbsp;</td>
			<td<?= $sStyle ?>><?= htmlspecialchars(fCountryNameFromID($aCustomerExtendedDetails['Company']['invoice_country'])) ?></td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['contact']) ?> </td>
			<td<?= $sStyle ?>><?= htmlspecialchars($aData['phone']) ?> </td>
			<td<?= $sStyle ?>><?= htmlspecialchars( $aData['lead_source'] ) ?></td>
			<td<?= $sStyle ?>><?= fReturnCaseStatusName( $aData['case_status'], $aData['forecast_expectancy'], $aData['forecast_amount'], $aData['forecast_date'], $aData['appointment'] ) ?></td>
			<td<?= $sStyle ?>><?= htmlspecialchars( DBGetRowValue('crm.cst', 'field_12', "cst_id = '" . $aData['master_id'] . "'") ) ?></td>
			<td<?= $sStyle ?>>[<a href="index.php?page=customer&amp;cst_id=<?= $aData['cst_id'] ?>&amp;tab=1" onclick="fAbortBubble(event);" style="text-decoration: none;" style="text-decoration: none;">TAB</a>]</td>
		</tr>
		<?
		}
		?>
	</table>

	<br><br>

	<i class="TablePadding">Canvas leads are disabled until "Expired Appointments" have been cleaned up</i>

<?php } else {
	// Copy leads into a new array
	$aNewCanvas = array();
	foreach($aCanvas as $aData){
		if ( fSkipCanvasLead( $aData, $aRepData ) ) {
			continue;
		}

		$aCustomerExtendedDetails = fGetCustomerDetails( $aData['cst_id'] );
		$aEntry = array(
			'cst_id' => htmlspecialchars($aData['cst_id']),
			'timezone' => htmlspecialchars($aData['timezone']),
			'name' => htmlspecialchars($aData['name']),
			'country' => htmlspecialchars(fCountryNameFromID($aCustomerExtendedDetails['Company']['invoice_country'])),
			'contact' => htmlspecialchars($aData['contact']),
			'phone' => htmlspecialchars($aData['phone']),
			'lead_source' => htmlspecialchars( $aData['lead_source'] ),
			'tag' => htmlspecialchars( DBGetRowValue('crm.cst', 'field_12', "cst_id = '" . ( $aData['master_id'] ? $aData['master_id'] : $aData['cst_id'] ) . "'") ),
			'callback_timestamp' => $aData['callback_timestamp'],
			'canvas_timestamp' => $aData['canvas_timestamp']
		);
		$aNewCanvas[] = $aEntry;

		if ( isISA( $aRepData['person_id'] ) && count($aNewCanvas) == $iLimit ) {
			// Only collect data for '$iLimit' if ISA
			break;
		}
	}

	if ( !isISA( $aRepData['person_id'] ) ){
		switch($_GET['sort']){
			case "":
			case "name":
				$sSort = "name";
				function fSort($A, $B){
				if (strtotime($A['callback_timestamp']) < strtotime($B['callback_timestamp'])){
					return -1;
					} elseif(strtotime($A['callback_timestamp']) == strtotime($B['callback_timestamp'])) {
						return strcmp(strtolower($A['name']), strtolower($B['name']));
					} else {
						return 1;
					}
				}
				break;
		
			case "id":
				$sSort = "id";
				function fSort($A, $B){
					if ($A['cst_id'] == $B['cst_id']){
						return 0;
					}
					return ($A['cst_id'] < $B['cst_id']) ? -1 : 1;
				}
				break;

			case "country":
				$sSort = "country";
				function fSort($A, $B){
					return strcmp(strtolower($A['country']), strtolower($B['country']));
				}
				break;
			
			case "contact":
				$sSort = "contact";
				function fSort($A, $B){
					return strcmp(strtolower($A['contact']), strtolower($B['contact']));
				}
				break;
			
			case "tag":
				$sSort = "tag";
				function fSort($A, $B){
					return strcmp(strtolower($A['tag']), strtolower($B['tag']));
				}
				break;
			case "flag":
				$sSort = "flag";
				function fSort($A, $B){
					return strcmp(strtolower($A['flag']), strtolower($B['flag']));
				}
				break;
		}	
	}
	
	uasort($aNewCanvas, "fSort");

	$iTotal = count($aNewCanvas);
	$iPages = ceil($iTotal/$iLimit);
	if ( !isISA( $aRepData['person_id'] ) ){
		if (isset($_GET['index']) && $iPages > 1 ){
			$iIndex = (int) $_GET['index'];
		} else {
			$iIndex = 0;
		}
	}
	
	$sClass = ' class="TableGreyBackground"';	
	?>
	<br><br>
<?php
	// Get the person's team
	// Limits for CRC/CSI: appointments + callbacks + expired
	$count = count( $aAppointments ) + count( $aCallbacks ) + count( $aExpiredAppointments );
	// Limits for TLA: appointments + callbacks + expired - activecustomers
	// Active Customer: Has a sale and the contract still is running.
	$cactiveCustomers = count( DBQueryGetRows("SELECT
			COUNT(DISTINCT cst.cst_id) AS activecustomers
		FROM
			crm.cst
		LEFT JOIN
			crm.saleslog
		ON
			cst.cst_id = saleslog.cst_id
		WHERE
			( saleslog.product_price - saleslog.discount ) > 0 AND saleslog.expires_date < NOW()
			AND saleslog.person_id = '".(int)$aRepData['person_id']."'
	") ); // Count the number of active customers that belong to this user

	if ( $aRepData['department'] == 1 ) {
		$count = $count - $cactiveCustomers;
	}
	if ( $count >= $iMaxLimit ) {
		die("You already have ".$iMaxLimit." assigned leads.");
	}
?>
	<table width="100%" cellspacing="0" cellpadding="0">
		<?
		$sTitle = 'Canvas Leads';
		$iColSpan = 11;
		$bWindowTitle = false;
		$bPrint = false;
		include INCLUDE_PATH . 'page_header.php';
		?>
		<tr>
			<td colspan="3" style="text-align: right;">
				<?php
				if ( !isISA( $aRepData['person_id'] ) ){
					if ($iIndex == 0){
						echo "&lt;&lt; Previous";
					} else {
						echo "<a href=\"?page=today&right=performance&sort=".$sSort."&index=".($iIndex-1)."\">&lt;&lt; Previous</a>";
					}
				}
				?>
			</td>
			<td colspan="3" style="text-align: center;">
				<?php
				if ( !isISA( $aRepData['person_id'] ) ){
					for($i=0;$i<$iPages;$i++){
						if ($iIndex == $i){
							$aLink[] = $i+1;
						} else {
						$aLink[] = "<a href=\"?page=today&right=performance&sort=".$sSort."&index=".$i."\">".($i+1)."</a>";
						}
					}
					echo implode("&nbsp;", $aLink);
				}
				?>
			</td>
			<td colspan="3" style="text-align: left;">
				<?php
				if ( !isISA( $aRepData['person_id'] ) ){
					if ($iIndex == ($iPages-1)){
						echo "Next &gt;&gt;";
					} elseif ( $iPages > 1 )  {
						echo "<a href=\"?page=today&right=performance&sort=".$sSort."&index=".($iIndex+1)."\">Next &gt;&gt;</a>";
					} else {
						echo "Next &gt;&gt;";
					}
				}
				?>
			</td>
		</tr>
		<tr>
			<td colspan="9">&nbsp;</td>
		</tr>
		<tr>
			<td width="5%" class="TableSubHeadline TableGreyBackground TablePadding"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=id\">" : "" ?>ID<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="2%" class="TableSubHeadline TableGreyBackground" title="Timezone">TZ</td>
			<td width="16%" class="TableSubHeadline TableGreyBackground"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=name\">" : "" ?>Company<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="4%" class="TableSubHeadline TableGreyBackground"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=flag\">" : "" ?>Flag&nbsp;<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="8%" class="TableSubHeadline TableGreyBackground"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=country\">" : "" ?>Country<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="15%" class="TableSubHeadline TableGreyBackground"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=contact\">" : "" ?>Contact<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Phone</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Lead source</td>
			<td width="10%" class="TableSubHeadline TableGreyBackground">Case Status</td>
			<td width="15%" class="TableSubHeadline TableGreyBackground"><?= ( !isISA( $aRepData['person_id'] ) ) ? "<a href=\"?page=today&right=performance&index=".$iIndex."&sort=tag\">" : "" ?>Tag<?= ( !isISA( $aRepData['person_id'] ) ) ? "</a>" : "" ?></td>
			<td width="5%" class="TableSubHeadline TableGreyBackground">TAB</td>
		</tr>
		<?
		$bBorderDisplayed = false;
		$iStart = $iIndex*$iLimit;
		if (($iStart+$iLimit) > $iTotal){
			$iEnd = $iTotal;
		} else {
			$iEnd = $iStart + $iLimit;
		}
		for($i=$iStart;$i<$iEnd;$i++){
			$aData = $aNewCanvas[$i];

			if ( $sClass === '' ) {
				$sClass = ' class="TableGreyBackground"';
			} else {
				$sClass = '';
			}
		?>
		<tr <?= $sClass . fRowLink('?page=customer&cst_id=' . $aData['cst_id'] . '&right=overview') ?>>
			<td class="TablePadding"><?= $aData['cst_id'] ?></td>
			<td><?= $aData['timezone'] ?></td>
			<td><?= $aData['name'] ?>&nbsp;</td>
			<td><?= $aData['flag'] ?></td>
			<td><?= $aData['country'] ?></td>
			<td><?= $aData['contact'] ?></td>
			<td><?= $aData['phone'] ?></td>
			<td><?= $aData['lead_source'] ?></td>
			<td>Not approached</td>
			<td><?= $aData['tag'] ?></td>
			<td>[<a href="index.php?page=customer&amp;cst_id=<?= $aData['cst_id'] ?>&amp;tab=1" onclick="fAbortBubble(event);" style="text-decoration: none;" style="text-decoration: none;">TAB</a>]</td>
		</tr>
		<?
		}
		?>
	</table>
	<?
}
?>
