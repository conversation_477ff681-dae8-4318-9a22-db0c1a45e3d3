<?php
// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

// Open DB Connection
fOpenDatabase();

// Get base details for sales rep.
if ( getenv('REMOTE_USER') == 'jb' )
{
	$aRepData = DBGetRow('crm.salespeople', "init = 'jjepsen'");
}
else
{
	$aRepData = DBGetRow('crm.salespeople', "init = '" . getenv('REMOTE_USER') . "'");
}

// Save data
if ( $_POST['save'] == 1 )
{
	// Loop through result
	while ( list($iMasterID, $aSelection) = each($_POST['customer']) )
	{
		// Trashcan?
		if ( $aSelection['trashcan'] == 1 )
		{
			DBQuery("UPDATE crm.cst SET segment_id = 1481, person_id = NULL, appointment = NULL, customer_marked_dead = 7 WHERE cst.cst_id = '" . intval($iMasterID) . "' LIMIT 1");

			while ( list($mCase, $mSelection) = each($aSelection) )
	                {
        	                // Update existing or create new cards
                	        if ( preg_match('^[0-9]*$', $mCase) )
				{
					DBQuery("UPDATE crm.cst SET segment_id = 1481, person_id = NULL, appointment = NULL, customer_marked_dead = 7 WHERE cst.cst_id = '" . intval($sCase) . "' LIMIT 1");
				}
			}

			continue;
		}

		// Get details for use as Base Details on CC
		if ( preg_match('^[0-9]*$', $aSelection['base']) )
		{
			$aBaseDetails = DBGetRow('crm.cst', "cst.cst_id = '" . $aSelection['base'] . "'");
		}
		else
		{
			// There should ALWAYS be a master
			continue;
			$aBaseDetails = array();
		}
	
		// Create "Customer Card"

		// Revive
if ( getenv('REMOTE_USER') == 'vbakker' || getenv('REMOTE_USER') == 'caarslew' )
{
		DBQuery("INSERT INTO crm.cst (case_name, name, segment_id, person_id, field_1, field_2, field_5, field_4, invoice_country, category_clients, category_servers, category_itpeople, category_externalips, web) VALUES('Company Card', '" . mysql_escape_string($aBaseDetails['name']) . "', '" . mysql_escape_string($aBaseDetails['segment_id']) . "', NULL, '" . mysql_escape_string($aBaseDetails['field_1']) . "', '" . mysql_escape_string($aBaseDetails['field_2']) . "', '" . mysql_escape_string($aBaseDetails['field_5']) . "', '" . mysql_escape_string($aBaseDetails['field_4']) . "', '" . mysql_escape_string($aBaseDetails['invoice_country']) . "', '" . mysql_escape_string($aBaseDetails['category_clients']) . "', '" . mysql_escape_string($aBaseDetails['category_servers']) . "', '" . mysql_escape_string($aBaseDetails['category_itpeople']) . "', 0, '" . mysql_escape_string($aBaseDetails['web']) . "');");
}
else
{
		// Normal Convert
		DBQuery("INSERT INTO crm.cst (case_name, name, segment_id, person_id, field_1, field_2, field_5, field_4, invoice_country, category_clients, category_servers, category_itpeople, category_externalips, web) VALUES('Company Card', '" . mysql_escape_string($aBaseDetails['name']) . "', '" . mysql_escape_string($aBaseDetails['segment_id']) . "', '" . $aRepData['person_id'] . "', '" . mysql_escape_string($aBaseDetails['field_1']) . "', '" . mysql_escape_string($aBaseDetails['field_2']) . "', '" . mysql_escape_string($aBaseDetails['field_5']) . "', '" . mysql_escape_string($aBaseDetails['field_4']) . "', '" . mysql_escape_string($aBaseDetails['invoice_country']) . "', '" . mysql_escape_string($aBaseDetails['category_clients']) . "', '" . mysql_escape_string($aBaseDetails['category_servers']) . "', '" . mysql_escape_string($aBaseDetails['category_itpeople']) . "', 0, '" . mysql_escape_string($aBaseDetails['web']) . "');");
}
		echo mysql_error();

		// Get Master ID
		$iMasterID = mysql_insert_id();

		// Loop through each case
		while ( list($mCase, $mSelection) = each($aSelection) )
		{
			// Update existing or create new cards
			if ( preg_match('^[0-9]*$', $mCase) )
			{
				// Convert contacts if this wasn't a shadow
				if ( $mSelection != 'shadow' )
				{
					DBQuery("UPDATE crm.cst SET master_id = '" . $iMasterID . "', name = '" . mysql_escape_string($aBaseDetails['name']) . "', invoice_country = '" . $aBaseDetails['invoice_country'] . "', case_name = '" . 'card_' . $mSelection . "', forecast_expectancy = '', forecast_date = '', forecast_amount = ''" . ( getenv('REMOTE_USER') == 'vbakker' || getenv('REMOTE_USER') == 'caarslew' ? ", person_id = NULL, segment_id = '" . mysql_escape_string($aBaseDetails['segment_id']) . "', appointment = NULL" : '' ) . " WHERE cst.cst_id = '" . $mCase . "' LIMIT 1");
					echo mysql_error();

					// Select contacts
					$aContacts = DBGetRow('crm.cst', "cst.cst_id = '" . $mCase . "'");

					// #1
					if ( $aContacts['contact'] )
					{
						DBQuery("INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact) VALUES('" . $mCase . "', 1, '" . mysql_escape_string($aContacts['contact']) . "', '" . mysql_escape_string($aContacts['phone']) . "', '" . mysql_escape_string($aContacts['email']) . "', 1);");
						echo mysql_error();
					}

					// #2
					if ( $aContacts['contact_2'] )
					{
						DBQuery("INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact) VALUES('" . $mCase . "', 2, '" . mysql_escape_string($aContacts['contact_2']) . "', '" . mysql_escape_string($aContacts['phone_2']) . "', '" . mysql_escape_string($aContacts['email_2']) . "', 0);");
						echo mysql_error();
					}

					// #3
					if ( $aContacts['contact_3'] )
					{
						DBQuery("INSERT INTO crm.contacts (cst_id, position, name, phone, email, primary_contact) VALUES('" . $mCase . "', 3, '" . mysql_escape_string($aContacts['contact_3']) . "', '" . mysql_escape_string($aContacts['phone_3']) . "', '" . mysql_escape_string($aContacts['email_3']) . "', 0);");
						echo mysql_error();
					}
				}
				else
				{
					DBQuery("UPDATE crm.cst SET master_id = '" . $iMasterID . "', invoice_country = '" . $aBaseDetails['invoice_country'] . "', segment_id = '" . mysql_escape_string($aBaseDetails['segment_id']) . "'" . ( $mSelection == 'subsidiary' ? ", case_name = 'card_subsidiary'" : ", case_name = 'card_shadow'" ) . " WHERE cst.cst_id = '" . $mCase . "' LIMIT 1");
					echo mysql_error();
					DBQuery("UPDATE crm.cst SET appointment = NULL, forecast_expectancy = '', forecast_date = '', forecast_amount = '', invoice_country = '" . $aBaseDetails['invoice_country'] . "', segment_id = '" . mysql_escape_string($aBaseDetails['segment_id']) . "' WHERE cst.cst_id = '" . $mCase . "' LIMIT 1");
					echo mysql_error();
				}
			}
			elseif ( $mCase != 'base' )
			{
				DBQuery("INSERT INTO crm.cst (master_id, case_name, name, segment_id, invoice_country) VALUES('" . $iMasterID . "', 'card_" . $mCase . "', '" . mysql_escape_string($aBaseDetails['name']) . "', '" . mysql_escape_string($aBaseDetails['segment_id']) . "', '" . $aBaseDetails['invoice_country'] . "')");
				echo mysql_error();
			}
		}
	}
	
	header('location: convert.php');
	exit();
}

// Select all customers with an iactive appointment and a case_name different from (case_vi, case_nsi)
if ( ( getenv('REMOTE_USER') == 'vbakker' || getenv('REMOTE_USER') == 'caarslew' ) && $_GET['cst_id'] )
{
	$aCustomers = DBGetRows('crm.cst', "cst.cst_id = '" . $_GET['cst_id'] . "' && case_name NOT IN ('card_vi', 'card_nsi', 'card_subsidiary', 'Customer Card')");
}
elseif ( ( getenv('REMOTE_USER') == 'vbakker' || getenv('REMOTE_USER') == 'caarslew' ) && !$_GET['cst_id'] )
{
	$aCustomers = DBGetRows('crm.cst', "cst.segment_id = '1293' && ( appointment = '0000-00-00 00:00:00' || appointment IS NULL ) && case_name NOT IN ('card_vi', 'card_nsi', 'card_subsidiary', 'Customer Card')");
}
else
{
	$aCustomers = DBGetRows('crm.cst', "cst.person_id = '" . $aRepData['person_id'] . "' && (segment_id != 1481 || segment_id is null) && appointment > '0000-00-00 00:00:00' && ( case_name NOT IN ('card_vi', 'card_nsi', 'card_subsidiary', 'Customer Card') || case_name is null )");
}

?>

<html>
<head>
	<title>Secunia CRM 2.0 - Sales</title>
	<link rel="stylesheet" TYPE="text/css" HREF="/crmadmin/crm2/sales/default.css">
</head>
<body topmargin="0" leftmargin="0" rightmargin="0" bottommargin="0" class="TablePadding">

<b>Customer card convertion functionality for Secunia CRM 1.0 to 2.0</b><br>
<br>
Customers requiring conversion: <?= count($aCustomers) ?><br>

<form method="POST" action="convert.php">
<input type="hidden" name="save" value="1">

<?php
// Loop through list of customers
while ( list($iKey, $aCustomer) = each($aCustomers) )
{
	// Determine Master ID
	if ( $aCustomer['master_id'] )
	{
		$iMasterID = $aCustomer['master_id'];
	}
	else
	{
		$iMasterID = $aCustomer['cst_id'];
	}

	// Check if Master ID has been handled
	if ( $aHandled[$iMasterID] )
	{
		continue;
	}
	$aHandled[$iMasterID] = true;

	// REVIVAL-CONVERT: Verify that all customer are dead
//	if ( getenv('REMOTE_USER') == 'vbakker' )
	{
		$bSkip = false;
		$aCaseCustomers = DBGetRows('crm.cst', "cst.master_id = '" . $iMasterID . "' OR cst.cst_id = '" . $iMasterID . "'", 'appointment DESC');
		while ( list($iKey, $aCaseCustomer) = each($aCaseCustomers) )
		{	
	                if ( strstr($aCaseCustomer['case_name'], 'card_') || $aCaseCustomer['segment_id'] == 1481 )
        	        {
				$bSkip = true;
                	        break;
	                }
		}

		if ( $bSkip )
		{
			continue;
		}
	}

	// Customer Name
	echo '<br><b></b><br>
	<table width="100%">
		<tr>
			<td colspan="8" bgcolor="#DEDEDE"><input type="checkbox" name="customer[' . $iMasterID . '][trashcan]" onClick="return confirm(\'Do you really wish to Trashcan this customer? (If yes, then it will not be imported to CRM 2.\')" value="1"><b>' . $aCustomer['name'] . ' (' . $iMasterID . ')</b></td>
		</tr>
		<tr>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				&nbsp;&nbsp;<b>Case Name
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				&nbsp;&nbsp;<b>Base Details for CC
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				<b>VI
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				<b>NSI
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				<b>Make Shadow
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				<b>Subsidiary
			</td>
			<td width="10%" style="border-bottom: 1px solid #DEDEDE;">
				<b>Next Appointment
			</td>
			<td width="40%" style="border-bottom: 1px solid #DEDEDE;">
				<b>Owner
			</td>
		</tr>';

	// Get Cases
	$aCaseCustomers = DBGetRows('crm.cst', "cst.master_id = '" . $iMasterID . "' OR cst.cst_id = '" . $iMasterID . "'", 'appointment DESC');

	// Loop through all cases
	while ( list($iKey, $aCaseCustomer) = each($aCaseCustomers) )
	{
		echo '
		<tr>
			<td>
				&nbsp;&nbsp;<a href="/crmadmin/crm/sales/customer.php?cst_id=' . $aCaseCustomer['cst_id'] . '" target="_blank">' . $aCaseCustomer['case_name'] . '</a>
			</td>
			<td>
				<input type="radio" name="customer[' . $iMasterID . '][base]" value="' . $aCaseCustomer['cst_id'] . '">
			</td>
			<td>
				<input type="radio" name="customer[' . $iMasterID . '][' . $aCaseCustomer['cst_id'] . ']" value="vi">
			</td>
			<td>
				<input type="radio" name="customer[' . $iMasterID . '][' . $aCaseCustomer['cst_id'] . ']" value="nsi">
			</td>
			<td>
				<input type="radio" name="customer[' . $iMasterID . '][' . $aCaseCustomer['cst_id'] . ']" value="shadow">
			</td>
			<td>
				<input type="radio" name="customer[' . $iMasterID . '][' . $aCaseCustomer['cst_id'] . ']" value="subsidiary">
			</td>
			<td>
				' . $aCaseCustomer['appointment'] . '
			</td>
			<td>
				' . fReturnRepDetailFromID($aCaseCustomer['person_id']) . '
			</td>
		</tr>';
	}

	echo '
		<tr>
			<td style="border-top: 1px solid #DEDEDE;">
				&nbsp;&nbsp;Create Blank
			</td>
			<td style="border-top: 1px solid #DEDEDE;">&nbsp;
<!--				<input type="radio" name="customer[' . $iMasterID . '][base]" value="blank">-->
			</td>
			<td style="border-top: 1px solid #DEDEDE;">
				<input type="checkbox" name="customer[' . $iMasterID . '][vi]" value="blank">
			</td>
			<td style="border-top: 1px solid #DEDEDE;">
				<input type="checkbox" name="customer[' . $iMasterID . '][nsi]" value="blank">
			</td>
			<td style="border-top: 1px solid #DEDEDE;">
				-
			</td>
			<td style="border-top: 1px solid #DEDEDE;" colspan="3">
				-
			</td>
		</tr>
	</table>';

	// Limit to 10
	if ( ++$iCount > 4 )
	{
		break;
	}
}
?>
<br>
<br>
<input type="submit" value="Convert!">
</form>

</body>
</html>
