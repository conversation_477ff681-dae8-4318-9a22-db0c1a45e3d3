<?php
// Access Level: 2
// Admin
// SM
// Lead Management
if ( !fVerifyAccess( $aRepData['person_id'], 2 ) ) {
	exit();
}

// Configuration
$iDisplayLimit = 500;

// User Input
$sInputName = $_POST['name'] ? $_POST['name'] : $_GET['name'];
$aInputStatus = $_POST['status'] ? $_POST['status'] : $_GET['status'];
$aInputCountries = $_POST['countries'] ? $_POST['countries'] : $_GET['countries'];
$sInputType = $_POST['type'] ? $_POST['type'] : $_GET['type'];
$iInputPage = $_GET['pageno'] ? $_GET['pageno'] : 1;
$sInputCustomerCategory = $_POST['customer_category'] ? $_POST['customer_category'] : $_GET['customer_category'];
$sVTS = $_POST['vts'] ? $_POST['vts'] : $_GET['vts'];
$sVM = $_POST['vm'] ? $_POST['vm'] : $_GET['vm'];
$sPostVM = $sVM;
$sPostVTS = $sVTS;
$sVTS = $sVTS == "on" ? "1" : "";
$sVM = $sVM == "on" ? "2" : "";
$sReference = $_POST['cst_reference'] ? $_POST['cst_reference'] : $_GET['cst_reference'];
$sPostReference = $sReference;
$sReference = $sReference == "1" ? 1 : "";
//$sNurturing = $_POST['nurturing'] ? $_POST['nurturing'] : $_GET['nurturing'];
//$sPostNurturing = $sNurturing;
//$sNurturing = ( $sNurturing == "on" ) ? 1 : " null ";
// Base URL
$aTmp = $aInputStatus;
array_walk($aTmp, 'alt_urlencode');
$aTmpC = $aInputCountries;
array_walk($aTmpC, 'alt_urlencode');
$aTmpD = $_GET['lead_source'];
array_walk($aTmpD, 'alt_urlencode');
$aTmpE = $_GET['industry'];
array_walk($aTmpE, 'alt_urlencode');

$sBaseURL = '?page=admin_dm_campaign&amp;name=' . urlencode($sInputName) . '&amp;type=' . urlencode($sInputType) . ( $aTmp ? '&amp;status[]=' . implode('&amp;status[]=', $aTmp) : '' ) . '&amp;countries[]=' . implode('&amp;countries[]=', $aTmpC) . '&amp;lead_source[]=' . implode('&amp;lead_source[]=', $aTmpD) . '&amp;industry[]=' . implode('&amp;industry[]=', $aTmpE) . '&amp;customer_category=' . urlencode( $sInputCustomerCategory ) . '&amp;vm=' . urlencode( $sPostVM ) . '&amp;vts=' . urlencode( $sPostVTS ) . '&amp;cst_reference=' . urlencode( $sPostReference ) ;

function alt_urlencode(&$sItem, $iKey) { $sItem = urlencode($sItem); }

// Send email request?
$bSent = false;
if ( $_POST['sendemail'] == 1 && $_POST['body'] && $_POST['subject'] ) {
	// Loop through each recipient customer id
	while ( list($iKey, $iCustomerID) = each( $_POST['cb'] ) ) {
		echo '<pre>' . str_repeat('-', 100) . "\n";

		// Select contact
		$aContact = DBGetRow('crm.contacts', "primary_contact = 1 AND cst_id = '" . (int) $iCustomerID . "'" );
		$aContact['name'] = trim( $aContact['name'] );
		$aContact['name'] = substr($aContact['name'], 0, ( strpos($aContact['name'], ' ') > strpos($aContact['name'], ',') && strpos($aContact['name'], ',') > 0 ? strpos($aContact['name'], ',') : strpos($aContact['name'], ' ') ) );

		// Test if campaign has already been mailed to this customer
		if ( DBGetRow('crm.dm_campaigns', "campaign = '" . $sInputName . "' AND recipient = '" . mysql_escape_string($aContact['email']) . "'") || DBGetRow('crm.dm_unsubscribe', "email = '" . mysql_escape_string($aContact['email']) . "'") ) {
			continue;
		}

		// Construct email
		$sSubject = $_POST['subject'];
		$sContent = fInsertDetails( $_POST['body'], '##NAME##', $aContact['name'] );

		// Campaign name
		$sCampaignName = strtolower( $sInputName );
		$sCampaignName = str_replace( " ", "_", $sInputName );

                $sContent .= '

Unsubscribe:
http://secunia.com/corporate/directmarketing/unsubscribe/' . $aContact['email'] . '/'.$sCampaignName.'/';

		// Append unsubscribe link
		if ( !$bSent ) {
			echo 'From: ' . htmlspecialchars($_POST['from']) . "\n";
			echo 'Email: ' . $aContact['email'] . "\n";
			echo 'Subject: ' . htmlspecialchars($sSubject) . "\n";
			echo "Body:\n" . htmlspecialchars(wordwrap($sContent)) . '</pre>';
		}
		$bSent = true;

		// Send email
		mail(stripslashes($aContact['email']), stripslashes($sSubject), wordwrap(stripslashes($sContent)), 'From: ' . stripslashes($_POST['from']));
		// Log that email has been sent
		DBQuery("INSERT INTO crm.dm_campaigns SET campaign = '" . $sInputName. "', recipient = '" . mysql_escape_string($aContact['email']) . "', cst_id = '" . (int) $iCustomerID . "', contact_id = '".(int)$aContact['id']."'");
		$campaign_id = mysql_insert_id();
		// Log WHICH subscription types are included in this campaign, so when the user unsubscribes, they also go away.
		foreach ( $_POST['subscription_type'] as $subscriptionName => $value ) {
			if ( $value == "on" ) {
				DBQuery("INSERT INTO crm.dm_campaign_properties SET campaign_id = '".(int)$campaign_id."', cst_id = '".(int)$aContact['cst_id']."', contact_id = '".(int)$aContact['id']."', subscription_name = '".mysql_escape_string( $subscriptionName )."'");
			}
		}

		// Add a new comment on the customer's case card
		// Type = 2 <=> 'Customer Relation' comment type
		DBQuery("INSERT INTO crm.comments SET comment = 'Campaign name: ". $sInputName ."\nContent:". fInsertDetails( $_POST['body'], '##NAME##', $aContact['name'] ) ."', added = NOW(), person_id = 0, type = 2, cst_id = '".(int)$iCustomerID."'");
	}

	exit();
}

function fInsertDetails( $sContent, $sFind, $sReplace ) {
	return str_replace($sFind, $sReplace, $sContent);
}


// Check if the 'DM Subscribers' is a criteria, if so, select only contacts that match it
$dmSubscribers = $_REQUEST['subscription_type'];
$sqlSubscribers = "";
foreach ( $dmSubscribers as $subName => $sChecked ) {
	// Also build the SQL query
	if ( $sChecked == "on" ) {
		$sqlSubscribers .= ( $sqlSubscribers != "" ? " OR " : "" ) . " name='".mysql_real_escape_String( $subName )."' ";
	}
}

// Submit
if ( $sInputName ) {
	// Generate array of Company IDs that _CAN_ be included
	//
	// NOTE: It is important that we NEVER include just any
	// customer as this could be considered unsolicited email
	// (SPAM) if sent to a person that haven't accepted or
	// who haven't had an indepth dialogue with Secunia before.
	//

	// Generate new or use cache
	if ( !file_exists(CACHE_DIR . 'admin_dm_campaign.cache') || $_GET['refresh_cache'] ) {
		$aIncludable = array();

		// CRITERIA: Customers - currently or lost
		if ( in_array('Customer', $aInputStatus) || in_array('Lost Customer', $aInputStatus) ) {
			$aCustomers = DBGetRows('crm.saleslog, crm.cst', "(saleslog.product_price - saleslog.discount) > 0 AND saleslog.cst_id = cst.cst_id AND (status is null || (status != 3 && status != 6 && status != 7) )");
			while ( list($iKey, $aCustomer) = each($aCustomers) ) {
				$iCSTID = $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'];
				$aIncludable[ $iCSTID ] = fCalculateAccountStatus( $iCSTID );
			}
		}

		// CRITERIA: Customer of a certain product category
		if ( $sInputCustomerCategory ) {
			$aCustomers = DBGetRows('crm.saleslog, crm.cst', "(saleslog.product_price - saleslog.discount) > 0 AND saleslog.cst_id = cst.cst_id AND expires_date >= NOW() AND (status is null || (status != 3 && status != 6 && status != 7) ) AND product_category = '" . $sInputCustomerCategory . "'");
			while ( list($iKey, $aCustomer) = each($aCustomers) ) {
				$iCSTID = $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'];
				$aIncludable[ $iCSTID ] = fCalculateAccountStatus( $iCSTID );
			}
		}

		// CRITERIA: Propects with a Secunia CA Account (e.g. trials, inbounds, etc.)
		if ( $_GET['ca_account'] ) {
			$aCustomers = DBGetRows('ca.accounts, crm.cst', "accounts.cst_id = cst.cst_id");
			while ( list($iKey, $aCustomer) = each($aCustomers) ) {
				$iCSTID = $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'];
				$aIncludable[ $iCSTID ] = fCalculateAccountStatus( $iCSTID );
			}
		}

		// CRITERIA: Prospects from a specific lead source
		$sLeadSources = preg_replace('/[^0-9,]*/', '', implode(',', $_GET['lead_source']));
		if ( $sLeadSources ) {
			$res = DBQuery('SELECT cst_id, master_id from crm.cst WHERE cst.lead_source IN(' . $sLeadSources . ')');
			while ( $aCustomer = mysql_fetch_array($res) ) {
				$iCSTID = $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'];
				$aIncludable[ $iCSTID ] = fCalculateAccountStatus( $iCSTID );
			}
		}

		// CRITERIA: Prospects from specific industries
		$sIndustries = preg_replace('/[^0-9,]*/', '', implode(',', $_GET['industry']));
		if ( $sIndustries ) {
			$aCustomers = DBGetRows('crm.cst', "cst.company_industry IN(" . $sIndustries . ")");
			while ( list($iKey, $aCustomer) = each($aCustomers) ) {
				$iCSTID = $aCustomer['master_id'] ? $aCustomer['master_id'] : $aCustomer['cst_id'];
				$aIncludable[ $iCSTID ] = fCalculateAccountStatus( $iCSTID );
			}
		}

		// CRITERIA: Customers who have opt-in for these DM mails
		// 0|NULL = Unknown | 1 = OPT-IN | 2 = Unsubscribed
		$aExplicitCustomers = DBGetRows('crm.cst', 'dm_status in (1,2)');
		while ( list($iKey, $aCustomer) = each($aExplicitCustomers) ) {
			if ( $aCustomer['dm_status'] == 1 && $_GET['opt_in'] ) {
				// Include
				$aIncludable[ $aCustomer['cst_id'] ] = fCalculateAccountStatus( $aCustomer['cst_id'] );
			} else {
				// Exclude (customer requested not to receive any more DM)
				unset( $aCustomer['cst_id'] );
			}
		}

		// Cache results
		$sCache = '<?php
$aIncludable = unserialize(\'' . serialize($aIncludable) . '\');
?>
';

		// Save cache
		file_put_contents(CACHE_DIR . 'admin_dm_campaign.cache', $sCache);
	} else {
		// Load file cache
		include CACHE_DIR . 'admin_dm_campaign.cache';
		$iCache = filemtime( CACHE_DIR . 'admin_dm_campaign.cache' );
	}

	// Select customers
	$aResults = array();
	while ( list($iCSTID, $sStatus) = each($aIncludable) ) {
		// Status Limits
		if ( $aInputStatus && !in_array($sStatus, $aInputStatus) ) {
			continue;
		}

		// Find relevant details
		$aData = DBGetRow('crm.cst', "case_name = '" . $sInputType . "' AND cst_id = '" . $iCSTID . "'" );

		if ( !$aData ) {
			$aData = DBGetRow('crm.cst', "case_name = '" . $sInputType . "' AND master_id = '" . $iCSTID . "'");
		}
		$aData['status'] = $sStatus;
		$aData['type'] = $sInputType;

		// Get CC details
		$aMaster = DBGetRow('crm.cst', "cst_id = '" . $aData['master_id'] . "'");
		$aData['name'] = $aMaster['name'];

		// Test for trashcan segment
		if ( $aMaster['segment_id'] == 1367 || $aMaster['segment_id'] == 1481 ) {
			continue;
		}
		
		$aData['invoice_country'] = $aMaster['invoice_country'];
		$aData['company_employees'] = $aMaster['company_employees'];

		// Country limits
		if ( !in_array($aData['invoice_country'], $aInputCountries) && !(count($aInputCountries) == 1 && $aInputCountries[0] == '' ) ) {
			continue;
		}

		// Reference limits
		if ( $sReference != "" && $sReference == 1 ) {
			$reference = DBGetRowValue("crm.cst", "reference", "cst_id = '".(int) $iCSTID ."'");
			if ( $reference != 1 ) {
				continue;
			}
		}

		// Get primary contact
		$aContact = DBGetRow('crm.contacts', "primary_contact = 1 AND cst_id = '" . $aData['cst_id'] . "'");
		$aData['contact'] = $aContact['name'];
		$aData['email'] = $aContact['email'];
//		$aData['nurturing'] = $aContact['nurturing'];

//		if ( $sNurturing == 1 && $aData['nurturing'] != 1 ) {
//			continue;
//		}

		// Check if filtering based on DM Subscribers
		if ( $sqlSubscribers != "" ) {
			// Check if contact is within that list
			$iTest = DBNumRows("crm.subscriptions", "contact_id = '".(int)$aContact['id']."' AND (".$sqlSubscribers.")");
			if ( $iTest == 0 ) {
				continue; // Contact is not within that list
			}
		}

		// Check if it has a VIM or VTS product type, but not a different one:
		if ( $sVTS != "" || $sVM != "" ) {
			$whereStatement = "";
			if ( $sVTS != "" ) {
				$whereStatement = " product_type = ".(int)$sVTS." ";
			}
			if ( $sVM != "" ) {
				$whereStatement = $whereStatement . ( $sVTS != "" ? " OR " : "" )." product_type = ".(int)$sVM;
			}
			// Get all the CST_IDs of the current CST_ID (sub card ids)
			$_aRows = DBGetRows("crm.cst", "master_id = " . $iCSTID );
			$sqlIn = "";
			for ( $t = 0; $t < count( $_aRows ); $t++ ) {
				if ( $t != 0 ) {
					$sqlIn .= ", ";
				}
				$sqlIn .= (int)$_aRows[$t]['cst_id'];
			}
			$bVMVTScount = DBNumRows("crm.saleslog", "( ".$whereStatement." ) AND cst_id IN ( ".$sqlIn." ) && product_price > 0");
			$bTotalCount = DBNumRows("crm.saleslog", "cst_id IN ( ".$sqlIn." ) && product_price > 0");
			if ( $bTotalCount != $bVMVTScount || $bTotalCount == 0 ) {
				continue;
			}
		}

		// Sort
		switch ( $_GET['sort'] ) {
			case 'name':
			case 'contact':
			case 'type':
			case 'status':
			case 'email':
			case 'invoice_country':
			case 'company_employees':
				$sSort = $aData[$_GET['sort']];
				break;
			default:
				$sSort = $aData['name'];
		}

		// Data
		$aResults[ $sSort . '__' . $aData['cst_id'] ] = $aData;
	}

	// Sort result
	ksort($aResults);
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	// Head line
	$sTitle = 'Direct Marketing - Campaign Management';
	$iColSpan = 8;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr>
		<td colspan="<?= $iColSpan ?>" style="padding-left: 5px;">
			<form method="GET" action="" onSubmit="if ( !document.getElementById('name').value ) { alert('Please insert a campaign name otherwise the system cannot determine which recipients have already received this campaign.'); return false; }">
			<input type="hidden" name="page" value="admin_dm_campaign">
			<b>Cache:</b> <?= $iCache ? 'Yes, from: ' . date('Y-m-d G:i:s', $iCache) . ' (<input type="checkbox" name="refresh_cache" value="1"> refresh)' : 'No' ?><br>
			<br>
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td width="150" valign="top">
						<b>Include Cards That are/has:</b><br>
						<input type="checkbox" name="opt_in" value="1" <?= ( $_GET['opt_in'] ? ' checked' : '' ) ?>> Subscribers<br>
						<input type="checkbox" name="ca_account" value="1" <?= ( $_GET['ca_account'] ? ' checked' : '' ) ?>> CA Accounts<br>
						<input type="checkbox" name="cst_reference" value="1" <?= ( $_GET['cst_reference'] ? ' checked' : '' ) ?>> Reference Customers<br>
					</td>
					<td width="150" valign="top">
						<b>Lead Source:</b><br>
						<select name="lead_source[]" MULTIPLE>
							<?php while ( list($iId, $sLeadSource) = each($aLeadSources) ) { ?>
							<option value="<?= $iId ?>"<?= in_array($iId, $_GET['lead_source']) ? ' selected' : ''  ?>><?= htmlspecialchars($sLeadSource) ?></option>
							<?php } ?>
						</select>
					</td>
					<td width="150" valign="top">
						<b>Industry:</b><br>
						<select name="industry[]" MULTIPLE>
							<?php while ( list($iId, $sIndustry) = each($aIndustries) ) { ?>
							<option value="<?= $iId ?>"<?= in_array($iId, $_GET['industry']) ? ' selected' : ''  ?>><?= htmlspecialchars($sIndustry) ?></option>
							<?php } ?>
						</select>
					</td>
					<td width="150" valign="top">
						<b>Status:</b><br>
						<select name="status[]" MULTIPLE>
							<option value="Lead"<?= in_array('Lead', $aInputStatus) ? ' selected' : ''  ?>>Lead</option>
							<option value="Prospect"<?= in_array('Prospect', $aInputStatus) ? ' selected' : ''  ?>>Prospect</option>
							<option value="Customer"<?= in_array('Customer', $aInputStatus) ? ' selected' : ''  ?>>Customer</option>
							<option value="Lost Customer"<?= in_array('Lost Customer', $aInputStatus) ? ' selected' : ''  ?>>Lost Customer</option>
						</select>
						<br />
					</td>
					<td wdth="150" valign="top">
						<b>Customer Type:</b><br>
							<select name="customer_category" id="customer_category" onchange="return fOnChange();">
								<option value="0"> - Select - </option>
								<option value="1"<?= ( $sInputCustomerCategory == 1 ? ' selected' : '' ) ?>> VI </option>
								<option value="2"<?= ( $sInputCustomerCategory == 2 ? ' selected' : '' ) ?>> SS </option>
								<option value="3"<?= ( $sInputCustomerCategory == 3 ? ' selected' : '' ) ?>> CSI </option>
							</select>
						<br/>
						<div style="display: <?= $sVTS != "" || $sInputCustomerCategory == 1 ? 'block' : 'none' ?>;" id="_vts"><input type="checkbox" name="vts" id="vts" <?= $sVTS != "" ? 'checked' : '' ?>> VTS </div>
                                                <div style="display: <?= $sVM != "" || $sInputCustomerCategory == 1 ? 'block' : 'none' ?>;" id="_vm"><input type="checkbox" name="vm" id="vm" <?= $sVM != "" ? 'checked' : '' ?>> VM </div>
					</td>
					<td width="150" valign="top">
						<b>Case Card:</b> (required)<br>
						<select name="type">
							<option value="0"> - Select - </option>
							<option value="card_vi"<?= $sInputType == 'card_vi' ? ' selected' : '' ?>>VI</option>
							<option value="card_nsi"<?= $sInputType == 'card_nsi' ? ' selected' : '' ?>>CSI</option>
							<option value="card_crc"<?= $sInputType == 'card_crc' ? ' selected' : '' ?>>CRC</option>
							<option value="card_partner"<?= $sInputType == 'card_partner' ? ' selected' : '' ?>>Partner</option>
						</select>
						<br><br>Note: This indicates where the contact information is collected from.
					</td>
					<td width="150" valign="top">
						<b>Countries:</b> (required)<br>
						<select name="countries[]" MULTIPLE size="4">
							<?php
							$aCountries = DBGetRows('crm.countries', '', 'country');
							while ( list($iKey, $aCountry) = each($aCountries) ) {
								echo '<option value="' . $aCountry['id'] . '"' . ( in_array($aCountry['id'], $aInputCountries) ? ' selected' : '' ) . '>' . htmlspecialchars($aCountry['country']) . '</option>';
							}
							?>
						</select><br>
						<b>DM Subscribers:</b><br>
						<nobr>
						<input name="subscription_type[CSI Trial Request]" <?php echo $_REQUEST['subscription_type']['CSI Trial Request'] == "on" ? " checked " : "" ?> type="checkbox">CSI Trial Request<br>
						<input name="subscription_type[VIM Trial Request]" <?php echo $_REQUEST['subscription_type']['VIM Trial Request'] == "on" ? " checked " : "" ?> type="checkbox">VIM Trial Request<br>
						<input name="subscription_type[Report Request]" <?php echo $_REQUEST['subscription_type']['Report Request'] == "on" ? " checked " : "" ?> type="checkbox">Report Request<br>
						<input name="subscription_type[Contact us]" <?php echo $_REQUEST['subscription_type']['Contact us'] == "on" ? " checked " : "" ?> type="checkbox">Contact us<br>
						<input name="subscription_type[<EMAIL>]" <?php echo $_REQUEST['subscription_type']['<EMAIL>'] == "on" ? " checked " : "" ?> type="checkbox"><EMAIL><br>
						<input name="subscription_type[PSI Partner]" <?php echo $_REQUEST['subscription_type']['PSI Partner'] == "on" ? " checked " : "" ?> type="checkbox">PSI Partner<br>
						<input name="subscription_type[Partner Program]" <?php echo $_REQUEST['subscription_type']['Partner Program'] == "on" ? " checked " : "" ?> type="checkbox">Partner Program<br>
						<input name="subscription_type[PSI]" <?php echo $_REQUEST['subscription_type']['PSI'] == "on" ? " checked " : "" ?> type="checkbox">PSI<br>
						<input name="subscription_type[Conference]" <?php echo $_REQUEST['subscription_type']['Conference'] == "on" ? " checked " : "" ?> type="checkbox">Conference<br>
						<input name="subscription_type[Conference (Import)]" <?php echo $_REQUEST['subscription_type']['Conference (Import)'] == "on" ? " checked " : "" ?> type="checkbox">Conference (Import)<br>
						<input name="subscription_type[Database (Import - not approved contact)]" <?php echo $_REQUEST['subscription_type']['Database (Import - not approved contact)'] == "on" ? " checked " : "" ?> type="checkbox">Database (Import - not approved contact)<br>
						<input name="subscription_type[Database (Important - approved contact)]" <?php echo $_REQUEST['subscription_type']['Database (Important - approved contact)'] == "on" ? " checked " : "" ?> type="checkbox">Database (Important - approved contact)<br>
						<input name="subscription_type[Online Campaigns]" <?php echo $_REQUEST['subscription_type']['Online Campaigns'] == "on" ? " checked " : "" ?> type="checkbox">Online Campaigns<br>
						<input name="subscription_type[Press list (from <EMAIL>/ <EMAIL>)]" <?php echo $_REQUEST['subscription_type']['Press list (from <EMAIL>/ <EMAIL>)'] == "on" ? " checked " : "" ?> type="checkbox">Press list (from <EMAIL>/ <EMAIL>)<br>
						<input name="subscription_type[Corporate Newsletter (from <EMAIL>)]" <?php echo $_REQUEST['subscription_type']['Corporate Newsletter (from <EMAIL>)'] == "on" ? " checked " : "" ?> type="checkbox">Corporate Newsletter (from <EMAIL>)<br>
						<input name="subscription_type[Nurturing program]" <?php echo $_REQUEST['subscription_type']['Nurturing program'] == "on" ? " checked " : "" ?> type="checkbox">Nurturing program<br>
						</nobr>
					</td>
					<td width="150" valign="top">
						<b>Campaign Name:</b><br>
						<input type="text" name="name" id="name" value="<?= htmlspecialchars( $sInputName ) ?>">
					</td>
					<td width="150" valign="bottom">
						<input type="submit" value="Generate Report">
					</td>
					<td width="*" valign="top">
						<span style="height: 15px; background: #7ABCFF">&nbsp;&nbsp;&nbsp;&nbsp;</span> Already received campaign<br>
						<span style="height: 15px; background: #FF7A7A">&nbsp;&nbsp;&nbsp;&nbsp;</span> No email<br>
						<span style="height: 15px; background: #AD0000">&nbsp;&nbsp;&nbsp;&nbsp;</span> Email on Mailing List<br>
						<span style="height: 15px; background: #FEA9FF">&nbsp;&nbsp;&nbsp;&nbsp;</span> Unsubscribed<br>
					</td>
				</tr>
			</table>
			</form>
		</td>
	</tr>

	<tr>
		<td><br></td>
	</tr>
	<tr style="background: #DEDEDE;">
		<td colspan="<?= $iColSpan ?>" style="padding-left: 5px; font-size: 13px; border-bottom: 1px solid #000000;"><b>Campaign Result (<?= number_format( count( $aResults ) ) ?>)</b>&nbsp;</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;" width="80"><b>Include</b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=name">Company Name</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=contact">Primary Contact</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=email">Email</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=status">Status</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=type">Type</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=invoice_country">Country</a></b></td>
		<td><b><a href="<?=$sBaseURL ?>&sort=company_employees">Employees</a></b></td>
	</tr>

<form method="POST" action="">
<input type="hidden" name="page" value="admin_dm_campaign">
<?php
// Reload DM Subscriptions
foreach ( $_REQUEST['subscription_type'] as $name => $value ) {
	echo "<input type=\"hidden\" name=\"subscription_type[".htmlspecialchars( $name )."]\" value=\"".htmlspecialchars( $value )."\">\n";
}
?>
<input type="hidden" name="sendemail" value="1">
<input type="hidden" name="pageno" value="<?= htmlspecialchars($iInputPage) ?>">
<input type="hidden" name="type" value="<?= htmlspecialchars($sInputType) ?>">
<?php
while ( list($iKey, $sValue) = each($aInputCountries) ) {
	?>
	<input type="hidden" name="countries[]" value="<?= htmlspecialchars($sValue) ?>">
	<?php
}
while ( list($iKey, $sValue) = each($aInputStatus) ) {
	?>
	<input type="hidden" name="status[]" value="<?= htmlspecialchars($sValue) ?>">
	<?php
}
?>

<input type="hidden" name="name" value="<?= htmlspecialchars($sInputName) ?>">

	<?php
	// Special Result
	$iCheckbox = 0;
	$iOffsetCound = 0;
	while ( list($iKey, $aResult) = each($aResults) ) {
		// Start offset?
		$iOffsetCount++;
		if ( $iInputPage > 1 ) {
			if ( (($iInputPage * $iDisplayLimit) - $iDisplayLimit) >= $iOffsetCount ) {
				continue;
			}
		}

		// Test if campaign has already been issued to this recipient (email address)
		$aCampaignStatus = DBGetRow('crm.dm_campaigns', "campaign = '" . $sInputName . "' AND recipient = '" . mysql_escape_string($aResult['email']) . "' AND cst_id = '" . $aResult['cst_id'] . "'");

		// Test if email is registered on our 'advisory mailing list'
		$aMailingList = DBGetRow('website.sec_adv_email', "email = '" . mysql_escape_string($aResult['email']) . "'");

		// Important: Remove all who have explicitly unsubscribed by following the link at the bottom of each DM mail (!)
		$aUnsubscribed = null;
		if ( $aResult['email'] ) {
			$aUnsubscribed = DBGetRow('crm.dm_unsubscribe', "email = '" . mysql_escape_string($aResult['email']) . "'");
		}

		// Reset
		$sColor = '';

		// Email on mailing list
		if ( $aMailingList ) {
			$sColor = '#AD0000';
		}

		// No email
		if ( !$aResult['email'] ) {
			$sColor = '#FF7A7A';
		}

		// Already received
		if ( $aCampaignStatus ) {
			$sColor = '#7ABCFF';
		}

		// Unsubscribed
		if ( $aUnsubscribed ) {
			$sColor = '#FEA9FF';
		}
        ?>
                <tr style="background: <?= $sColor ?>;">
                        <td style="padding-top: 5px; padding-left: 25px; "><input type="checkbox" name="cb[]" id="cb_<?= $iCheckbox++ ?>" value="<?= $aResult['cst_id'] ?>"<?= $sColor ? ' disabled' : '' ?>></td>
                        <td style="padding-top: 5px;"><?= $iOffsetCount ?>. <a href="?page=customer&amp;cst_id=<?= $aResult['cst_id'] ?>" target="_blank"><?= htmlspecialchars($aResult['name']) ?></a></td>
			<td style="padding-top: 5px;"><label for="cb_<?= $iCheckbox ?>"><?= htmlspecialchars($aResult['contact']) ?></label></td>
			<td style="padding-top: 5px;"><?= htmlspecialchars($aResult['email']) ?></td>
			<td style="padding-top: 5px;"><?= htmlspecialchars($aResult['status']) ?></td>
			<td style="padding-top: 5px;"><?= htmlspecialchars(strtoupper(str_replace('card_', '', $aResult['type']))) ?></td>
			<td style="padding-top: 5px;"><?= fCountryNameFromID( $aResult['invoice_country'] ) ?></td>
			<td style="padding-top: 5px;"><?= htmlspecialchars( number_format($aResult['company_employees']) ) ?></td>
                </tr>
	<?php
		// Display limit
		if ( $iCheckbox == $iDisplayLimit ) {
			break;
		}
	}
	?>
	<tr>
		<td align="center"><a href="javascript:;" onClick="fInvertSelection();">Invert</a></td>
	</tr>
	<tr>
		<td></td>
		<td>
			<br><input type="submit" value="Open Template Form" onclick="fShowMailFormLayer(); return false;">

<div style="display: none; position: absolute; width: 500px; background: #FFFFFF; border: 1px solid #000000;" id="template_form">
<table width="100%">
        <tr>
                <td>
			From:<br>
			<input type="text" name="from"><br>
			<br>
                        Subject:<br>
                        <input type="text" name="subject"><br>
			<br>
			Body Text: (Insert ##NAME## where the first name should be)<br>
			<textarea name="body" style="height: 400px;"></textarea><br>
			<br>
			<div align="right"><button>Cancel</button>&nbsp;&nbsp;<input type="submit" value="Send email to the selected people >>"></div>
                </td>
        </tr>
</table>
</div>
		</td>
	</tr>
</form>

	<tr>
		<td colspan="<?= $iColSpan ?>" align="right" style="padding-right: 25px;">
			<br>
			Pages: 
			<?php
			for ( $iPage = 1 ; $iPage <= ceil( count( $aResults ) / $iDisplayLimit ) ; $iPage++ ) {
				if ( $iPage != $iInputPage ) {
					echo '<a href="' . $sBaseURL . ( $_GET['sort'] ? '&amp;sort=' . urlencode($_GET['sort']) : '' ) . '&amp;pageno=' . $iPage . '">' . $iPage . '</a> ';
				} else {
					echo '<b>' . $iPage . '</b> ';
				}
			}
			?>
			<br>
			<br>
			<br>
		</td>
	</tr>
</table>
<script>
function fShowMailFormLayer() {
	oElem = document.getElementById('template_form');

	// Show mail layer
	document.getElementById('template_form').style.display = 'block';
}

function fInvertSelection() {
	// Loop through each 'cb'
	for ( var iCount = 0; iCount < 500 ; iCount++ ) {
		if ( document.getElementById('cb_' + iCount).checked || document.getElementById('cb_' + iCount).disabled ) {
			document.getElementById('cb_' + iCount).checked = false;
		} else {
			document.getElementById('cb_' + iCount).checked = true;
		}
	}
}
</script>
