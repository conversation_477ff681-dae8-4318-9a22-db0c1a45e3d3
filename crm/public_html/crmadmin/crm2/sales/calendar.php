<?php
// Month by Month Overview
if ( !$_GET['date'] ) {
	// Get Month, last day, first day
	$iLastDayMonth = mktime(0, 0, 0, date('m')+1+($_GET['month']), 0, date('Y'));
	$iLastDayPreviousMonth = mktime(0, 0, 0, date('m')+($_GET['month']), 0, date('Y'));

	// Calculate the last day of the selected month
	$iLastDay = date('d', $iLastDayMonth);
	?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = '<center><a href="?page=calendar&amp;month=' . intval($_GET['month']-1) . '"><<</a> ' . date('F Y', $iLastDayMonth) . ' <a href="?page=calendar&amp;month=' . intval($_GET['month']+1) . '">>></a></center>';
	$iColSpan = 3;
	include INCLUDE_PATH . 'page_header.php';
	?>

	<tr class="TableGreyBackground">
		<td width="10%" class="TableSubHeadline TablePadding">Day</td>
		<td width="30%" class="TableSubHeadline">Weekday</td>
		<td width="60%" class="TableSubHeadline">Appointments</td>
	</tr>

	<?php
	// Loop through each day
	for ( $iDay = 1 ; $iDay <= $iLastDay ; $iDay++ ) {
		// Timestamp
		$iCurrentDay = ($iLastDayPreviousMonth + (86400*$iDay));

		// Test for daylight savings
		if ( !date('I', $iCurrentDay) ) {
			// Add extra hour
			$iCurrentDay += 3600;
		}

		// Weekday
		$sWeekday = date('l', $iCurrentDay);

		// Check if weekend
		if ( $sWeekday == 'Saturday' || $sWeekday == 'Sunday' )
		{
			$sClass = ' class="TableGreyBackground"';
		}
		else
		{
			$sClass = '';
		}

		// Select appointments
		$aAppointments = DBGetRows('crm.cst', "cst.person_id = '" . $aRepData['person_id'] . "' AND cst.appointment like '" . date('Y-m-d', $iCurrentDay) . "%'");

	?>
	<tr<?= $sClass . fRowLink('?page=calendar&month=' . intval($_GET['month']) . '&date=' . $iCurrentDay, false) ?>>
		<td class="TablePadding"><?= $iDay ?></td>
		<td class="TablePadding"><?= $sWeekday ?></td>
		<td><?= count($aAppointments) ?> appointments</td>
	</tr>
	<?php
	}
	?>
</table>
<br>
<a href="javascript:void(0);" class="TablePadding" onClick="parent.fToggleCalendar( parent.document.getElementById('calendar'), parent.document.getElementById('calendar_link') );">Close calendar</a>

	<?php
} else { // Detailed month overview
	// Select all appointments for the day
	$aAppointments = DBGetRows('crm.cst', "cst.person_id = '" . $aRepData['person_id'] . "' AND appointment like '" . date('Y-m-d', $_GET['date']) . "%'", 'cst.appointment ASC');

	?>

<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = '<center>' . date('jS F Y', $_GET['date']) . '</center>';
	$iColSpan = 3;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline TablePadding">Time</td>
		<td width="80%" class="TableSubHeadline">Company</td>
	</tr>
	<?php
	// Display appointments
	while ( list($iKey, $aAppointment) = each($aAppointments) ) {
		// Select class
		if ( $sClass === '' ) {
			$sClass = ' class="TableGreyBackground"';
		} else {
			$sClass = '';
		}

		echo '
		<tr' . $sClass . '>
			<td class="TablePadding">' . substr($aAppointment['appointment'], 11, 5) . '</td>
			<td>' . htmlspecialchars($aAppointment['name']) . '</td>
		</tr>
		';
	}
	?>

	<tr>
		<td colspan="2" class="TablePadding">
			<br>
			<b>Appointment Suggestion:</b><br>
			<input type="text" style="width: 50%;" value="<?= date('Y-m-d 11:00:00', $_GET['date']) ?>" id="suggestion"> <button class="submit" onClick="parent.document.getElementById('appointment').value = document.getElementById('suggestion').value; parent.document.getElementById('appointmentlead').value = document.getElementById('suggestion').value; parent.fToggleCalendar( parent.document.getElementById('calendar'), parent.document.getElementById('calendar_link') );">Insert Date >></button><br>
			<br>
			<a href="javascript:history.back();">Back</a> | <a href="javascript:void(0);" class="TablePadding" onClick="parent.fToggleCalendar( parent.document.getElementById('calendar'), parent.document.getElementById('calendar_link') );">Close calendar</a>
		</td>
	</tr>
</table>
	<?php
}
?>
