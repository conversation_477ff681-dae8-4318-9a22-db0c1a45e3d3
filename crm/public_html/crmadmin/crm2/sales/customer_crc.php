<form method="POST" name="myForm" action="?page=save_customer_data" onSubmit="return fTestCRCFormData();">
<input type="hidden" name="cst_id" value="<?= intval($_GET['cst_id']) ?>">
<table width="100%" cellspacing="0" cellpadding="0">
	<?php
	$sTitle = 'CRC: ' . htmlspecialchars($aCustomer['Company']['name']);
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';
	?>
	<tr>
		<td width="49%" valign="top">
			<?php
			echo fIncludeCardBox( 'Company Details', 'c_company_details.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Contacts', 'c_contacts.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Sold Products', 'c_sold_products.php' );

                        echo '<br>';

                        echo fIncludeCardBox( 'Online Sales', 'c_online_sales.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Trials', 'c_trials.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Customer Area Accounts', 'c_ca_accounts.php' );

			echo '<br>';
			?>
		</td>
		<td width="2%" background="yellow"></td>
		<td width="49%" valign="top">
			<div align="right">
				<input type="submit" value="Save" name="save_button"> <input type="submit" value="Save - View Today" name="save_button">&nbsp;&nbsp;
			</div>
			<?php
			echo fIncludeCardBox( 'Current Stage', 'c_crc_stage.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Next Appointment', 'c_next_appointment.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Add Comment', 'c_add_comment.php' );

			echo '<br>';

			echo fIncludeCardBox( 'Comments', 'c_comments.php' );

			echo '<br>';
			?>
			<div align="right">
				<input type="submit" value="Save" name="save_button"> <input type="submit" value="Save - View Today" name="save_button">&nbsp;&nbsp;
			</div>
		</td>
	</tr>
</table>
</form>
