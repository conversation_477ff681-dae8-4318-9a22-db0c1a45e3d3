<?php
// Uncluster, if that is the case
if ( is_numeric( $_GET['uncluster'] ) && ( $_GET['uncluster'] != 0 ) ){
	// Remove child company
	DBQuery("UPDATE crm.cst SET cluster_id = NULL WHERE cst_id = '".$_GET['uncluster']."'");
}
// Select any child/mother companies to this company
$aClusterChildren = DBGetRows('crm.cst', "cst.cluster_id = '" . $_GET['cst_id'] . "'");
$aClusterMother = DBGetRows('crm.cst', "cst.cst_id = '" . $GLOBALS['aCustomer']['Company']['cluster_id'] . "'");

// Get partner case data
$aPartner = DBGetRow('crm.cst', "cst.master_id = '" . $GLOBALS['aCustomer']['Company']['cst_id'] . "' AND cst.case_name = 'card_partner'");
$partnerName = "";
$partnerCSTID = 0;
$partnerID = DBGetRowValue("crm.cst", "partner_id", "master_id = '".(int)$GLOBALS['aCustomer']['Company']['cst_id']."' AND case_name = 'card_partner'"); // Check if there is a partner card and it has a partner_id
if ( $partnerID ) {
	$partnerAccountID = DBGetRowValue( "ca.partner_profile", "account_id", "partner_id = '".(int)$partnerID."'" ); // Fetch the account_id, because it stores the customer id
	if ( $partnerAccountID ) {
		$partnerCaseCSTID = DBGetRowValue( "ca.accounts", "cst_id", "account_id = '".(int)$partnerAccountID."'" ); // Fetch the customer id, which points to the card_partner case
		if ( $partnerCaseCSTID ) {
			$partnerCSTID = DBGetRowValue( "crm.cst", "master_id", "cst_id = '".(int)$partnerCaseCSTID."'" ); // Fetch the master of selected card
			if ( $partnerCSTID ) {
				$partnerName = DBGetRowValue( "crm.cst", "name", "cst_id = '".(int)$partnerCSTID."'" ); // Fetch the partner which owns this company
			}
		}
	}
}
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="2"><b>CRM Details</b></td>
	</tr>
	<tr>
		<td class="TablePadding">Company ID</td>
		<td><?= intval( $_GET['cst_id'] ) ?></td>
	</tr>
	<?php if ( $aPartner['cst_id'] && $partnerName == "" ) { ?>
	<tr>
		<td class="TablePadding">
			Active Partner
		</td>
		<td>
			<input type="checkbox" name="cactive_partner" <?= $GLOBALS['aCustomer']['Company']['active_partner'] == 1 ? "checked" : "" ?> onClick="document.location = '?page=customer&cst_id=<?= (int) $_GET['cst_id']  ?>&action=cactive_partner&cactive_partner='+this.checked">
		</td>
	</tr>
	<?php
		}
	?>
	<tr>
		<td class="TablePadding">Partner Name</td>
		<td>
			<?php
			$onChange = "
				onChange='document.location=\"?page=customer&amp;cst_id=".(int)$_GET['cst_id']."&amp;action=bind_partner&amp;pcst_id=\"+this.value';
			";
			echo returnSelectBox(array($partnerCSTID), 'cst_id', false, 'select master_id, (SELECT cst.name FROM crm.cst where cst_id = A.master_id ) as name from crm.cst as A where ( partner_id is null OR partner_id = 0 ) AND case_name = "card_partner" AND (SELECT active_partner FROM crm.cst WHERE cst.cst_id = A.master_id) = 1', 'master_id', 'name', $onChange);
			?>
			<?php if ( $partnerName != "" ) { ?>
				<a href="?page=customer&cst_id=<?= (int)$partnerCSTID ?>" target="_blank">Click to view Partner</a>
			<?php } ?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Segmentation</b></td>
	</tr>
	<tr>
		<td class="TablePadding" width="20%">Region</td>
		<td width="80%"><?= fReturnRegionNameFromCountryID( $GLOBALS['aCustomer']['Company']['invoice_country'] ) ?></td>
	</tr>
	<tr>
		<td width="20%" class="TablePadding">Country</td>
		<td width="80%"><?= fCountryNameFromID( $GLOBALS['aCustomer']['Company']['invoice_country'] ) ?></td>
	</tr>
	<tr>
		<td class="TablePadding">Large Account</td>
		<td><?= $GLOBALS['aCustomer']['Company']['large_account'] ? 'Yes' : 'No' ?></td>
	</tr>
	<tr>
		<td class="TablePadding">Segment</td>
		<td><?= ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2 ) ? fSelectChangeSegment($GLOBALS['aCustomer']['Company']['segment_id'], $GLOBALS['aCustomer']['Company']['cst_id']) : fSegmentNameFromID( $GLOBALS['aCustomer']['Company']['segment_id'] ) )?></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Cluster Details</b></td>
	</tr>
	<tr>
		<td valign="top" class="TablePadding">Mother company</td>
		<td>
			<?php
			if ( count($aClusterChildren) > 0 )
			{
				echo '<b>Yes, for ' . count($aClusterChildren) . ' companies:</b><br>';

				// List customers
				while ( list($iKey, $aClusterChild) = each($aClusterChildren) )
				{
					echo '<a href="?page=customer&cst_id='.(int)$_GET['cst_id'].'&uncluster='.(int) $aClusterChild['cst_id'].'">[Uncluster]</a>&nbsp<a href="?page=customer&amp;cst_id=' . $aClusterChild['cst_id'] . '">' . htmlspecialchars($aClusterChild['name']) . '</a><br>';
				}
			}
			else
			{
				echo 'No';
			}
			?>
		</td>
	</tr>
	<tr>
		<td valign="top" class="TablePadding">Child company</td>
		<td>
			<?php
			if ( count($aClusterMother) > 0 )
			{
				echo '<b>Yes, a child of:</b><br>';
				
				// List customers
//				while ( list($iKey, $aClusterMother) = each($aClusterMothers) )
//				{
					echo '<a href="?page=customer&amp;cst_id=' . $aClusterMother[0]['cst_id'] . '">' . htmlspecialchars($aClusterMother[0]['name']) . '</a><br>';
//				}
			}
			else
			{
				echo 'No';
			}
			?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Company Facts</b></td>
	</tr>
	<tr>
		<td class="TablePadding">Clients</td>
		<td><?= number_format($GLOBALS['aCustomer']['Company']['category_clients']) ?></td>
	</tr>
	<tr>
		<td class="TablePadding">Servers</td>
		<td><?= number_format($GLOBALS['aCustomer']['Company']['category_servers']) ?></td>
	</tr>
	<tr>
		<td class="TablePadding">IT People</td>
		<td><?= number_format($GLOBALS['aCustomer']['Company']['category_itpeople']) ?></td>
	</tr>
	<tr>
		<td class="TablePadding">External IPs</td>
		<td><?= number_format($GLOBALS['aCustomer']['Company']['category_externalips']) ?></td>
	</tr>
	<?php
		if ( fVerifyAccess( $GLOBALS['aRepData']['person_id'], 2) ){ // Only admin could change this
			echo "<tr>
				<td class=\"TablePadding\">Reference Customer</td>
				<td><input ".( ( $GLOBALS['aCustomer']['Company']['reference'] == 1 ) ? "checked" : "" )." type=\"checkbox\" name=\"reference\" onClick=\"document.location = '?page=customer&cst_id=".(int) $_GET['cst_id']."&action=cref&ref='+this.checked\"> </td>
			</tr>";
		}
	?>
</table>
