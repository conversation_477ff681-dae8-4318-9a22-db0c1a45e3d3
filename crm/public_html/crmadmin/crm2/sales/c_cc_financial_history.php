<table width="100%" cellspacing="0" cellpadding="0">
	<tr class="TableGreyBackground">
		<td width="20%" class="TableSubHeadline"><b>Period</b></td>
		<td width="30%" class="TableSubHeadline"><b>Amount</b></td>
		<td width="50%" class="TableSubHeadline"><b>% Paid</b></td>
	</tr>
	<?php
	for ( $iYear = date('Y') ; $iYear >= 2002 ; $iYear-- )
	{
		// Choose background class 
		if ( $sClass === '' )
		{
			$sClass = ' class="TableGreyBackground"';
		}
		else
		{
			$sClass = '';
		}

		$iTotal = $GLOBALS['aFinancial'][$iYear]['awaiting'] + $GLOBALS['aFinancial'][$iYear]['paid'];
		?>
		<tr<?= $sClass ?>>
			<td><?= $iYear ?></td>
			<td class="NumberCell"><?= number_format( $iTotal, 2) ?> &euro;</td>
			<td><?= $iTotal ? round( $GLOBALS['aFinancial'][$iYear]['paid'] / $iTotal * 100 ) . '% Paid' : '-' ?></td>
		</tr>
		<?php
	}
	?>
	<tr>
		<td><b>Total</b></td>
		<td class="NumberCell"><b><?= number_format( ($GLOBALS['aFinancial']['total']['awaiting'] + $GLOBALS['aFinancial']['total']['paid']), 2) ?> &euro;</b></td>
		<td><b><?= ($GLOBALS['aFinancial']['total']['awaiting'] + $GLOBALS['aFinancial']['total']['paid']) ? round( $GLOBALS['aFinancial']['total']['paid'] / ($GLOBALS['aFinancial']['total']['awaiting'] + $GLOBALS['aFinancial']['total']['paid']) * 100 ) . '% Paid' : '-' ?></b></td>
	</tr>
</table>