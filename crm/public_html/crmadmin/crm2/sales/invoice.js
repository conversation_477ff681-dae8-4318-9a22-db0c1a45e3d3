// Function for updating total and VAT
function fUpdatePrice()
{
	// Get objects
	var iProductOne =   parseInt( ( document.getElementById('sale_0_price').value ? document.getElementById('sale_0_price').value : 0 ) );
	var iProductTwo =   parseInt( ( document.getElementById('sale_1_price').value ? document.getElementById('sale_1_price').value : 0 ) );
	var iProductFour =  parseInt( ( document.getElementById('sale_3_price').value ? document.getElementById('sale_3_price').value : 0 ) );
	var iProductFive = parseInt( ( document.getElementById('sale_4_price').value ? document.getElementById('sale_4_price').value : 0 ) );
	var iProductServer = parseInt( ( document.getElementById('server_price').value ? document.getElementById('server_price').value : 0 ) );
	var bVAT = document.getElementById('include_vat').checked;

	// Total without VAT
	var iTotal = (iProductOne+iProductTwo+iProductFour+iProductFive+iProductServer);


	// Check if VAT applies
	if ( bVAT && iTotal > 0 )
	{
		// Update VAT
		iTmp = (iTotal * 0.25);
		document.getElementById('vat').innerHTML = iTmp.toFixed(2);

		// New Total
		iTotal = iTotal * 1.25;
	}
	else
	{
		// Update VAT
		document.getElementById('vat').innerHTML = '0.00';
	}

	// Update total
	document.getElementById('total').innerHTML = iTotal.toFixed(2);
}

function fVIExtraOptions(obj) {
	// Hide all
	document.getElementById('evm').style.display = 'none';
	document.getElementById('vif').style.display = 'none';

	// vif: vif_assets -> 4
	// evm: evm_accounts -> 3 / 11
	if ( obj.value == 4 ) {
		document.getElementById('vif').style.display = 'block';
	} else if ( obj.value == 3 || obj.value == 11 ) {
		document.getElementById('evm').style.display = 'block';
	}
}
