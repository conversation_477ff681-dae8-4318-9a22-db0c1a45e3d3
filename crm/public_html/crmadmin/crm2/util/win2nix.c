// Convert a windows saved file to a unix saved file
// Windows uses: CR(\r)+LF(\n)
// Unix uses: LF (\n)
#include <stdio.h>

FILE *fOutput; // Output file

void usage(char *argv[]){
	// Print tool usage
	printf("Usage: %s input_file output_file\n", argv[0]);
}

void copy(char cChar){
	// Copy if not \r
	if ( cChar != '\r' ){
		fputc( cChar, fOutput );
	}
}

int main(int argc, char *argv[]){
	char cChar;
	if ( argc != 3 ){
		usage( argv );
		return 0;
	}

	// File pointer
	FILE *fFile; // Input file
	fFile = fopen( argv[1], "r" );
	if ( fFile == NULL ){
		printf("Cannot read input file: %s\n", argv[1]);
		return 0;
	}
	fOutput = fopen( argv[2], "w" );
	if ( fOutput == NULL ){
		printf("Cannot write to output file: %s\n", argv[2]);
		return 0;
	}
	
	// Parse input file
	cChar = fgetc( fFile );
	copy( cChar );
	while ( cChar != EOF ){
		cChar = fgetc( fFile );
		if ( cChar != EOF ){
			copy( cChar );
		}
	}

	// Close files
	fclose( fOutput );
	fclose( fFile );
	return 1;
}
