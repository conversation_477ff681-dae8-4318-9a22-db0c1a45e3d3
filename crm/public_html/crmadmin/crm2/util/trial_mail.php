<?php

//	Function to process CSI Trial accounts emails
//	*/2 * * * * php /home/<USER>/trial_mail/trial_mail.php

// Configure time in days since entry for each stage
$aType7Days[1] = 1;
$aType7Days[2] = 2;
$aType7Days[3] = 3;
$aType7Days[4] = 4;
$aType7Days[5] = 5;

$aType30Days[1] = 1;
$aType30Days[2] = 2;
$aType30Days[3] = 3;
$aType30Days[4] = 4;
$aType30Days[5] = 5;
$aType30Days[6] = 6;

// Configure subjects
$aSubject7Days[1] = 'Secunia - CSI Full License';
$aSubject7Days[2] = 'Secunia CSI solution experts stang by to assist';
$aSubject7Days[3] = 'Secunia CSI - Invitation';
$aSubject7Days[4] = 'Secunia CSI - Support';
$aSubject7Days[5] = 'Secunia CSI trial has expired';

$aSubject30Days[1] = 'Secunia CSI 4.0 Support ';
$aSubject30Days[2] = 'Secunia CSI solution experts standing by to assist';
$aSubject30Days[3] = 'Secunia CSI invitation';
$aSubject30Days[4] = 'Secunia CSI license';
$aSubject30Days[5] = '';
$aSubject30Days[6] = 'Secunia CSI end of license';

// Sender
$sFrom = '<EMAIL>';

// Log error and exit
function fErrorHandler( $iError, $sErrorDescription, $sErrorFile, $iErrorLine ){
	syslog(LOG_ERR, "ERROR SENDING CSI TRIAL MAIL: ". $iError. $sErrorFile. "(". $iErrorLine .")" . ": - ". $sErrorDescription);
	die();
}

set_error_handler("fErrorHandler");

// Require configuration file
require("/home/<USER>/public_html/crm_new/configuration.php");
require(INCLUDE_PATH."global_functions.php");

// Change directory to script directory
chdir(TRIAL_MAIL_PATH);

fOpenDatabase();

// Read mail template file
// Filename structure: stage_[0|1][1-12].txt
function fFetchMailTemplate( $iType ){
	if ( is_numeric( $iType ) ){
		$sFileName = "stage_".$iType.".txt";
	}

	$sContent = file_get_contents( $sFileName );

	return $sContent;
}

// Render the mail content
// Variable options:
// {company} - company name
// {contact} - contact name
function fRenderMailTemplate( $sContent, $sCompany, $sContact, $sSalesPersonName, $sSalesPersonNumber, $sSalesPersonEmail ){
	// Customer
	$sContent = str_replace("{company}", $sCompany, $sContent);

	// Normalize contact name ( DB STRUCTURE: NAME, JOB TITLE)
	$aTemp = explode(", ", $sContact);
	$sContact = $aTemp[0];
	$sContent = str_replace("{contact}", $sContact, $sContent);

	// Sales person

	$sContent = str_replace("{sales_person_name}", $sSalesPersonName, $sContent );
	$sContent = str_replace("{sales_person_phone}", $sSalesPersonNumber, $sContent );
	$sContent = str_replace("{sales_person_email}", $sSalesPersonEmail, $sContent );

	return $sContent;
}

function fIncrementStage( $iId, $iInterval ){
	DBQuery("UPDATE trial_mail_pipeline SET stage = stage + 1, date = date  WHERE id = '".(int) $iId."'");
}

// Remove from pipeline
function fFlushMail( $iId ){
	DBQuery("DELETE FROM trial_mail_pipeline WHERE id = '".(int) $iId."'");
}

function fSendMail( $sTo, $sSubject, $sContent){
	global $sFrom;
	mail( $sTo, $sSubject, $sContent, "From: ".$sFrom );
}

// Fetch sales person data
function fSalesPersonData( $iPersonId ){
	$rPersonData = DBQuery( "SELECT name, local_number, email FROM crm.salespeople WHERE person_id = '".$iPersonId."'" );
	if ( mysql_num_rows( $rPersonData ) > 0 ){
		$aRow = mysql_fetch_array( $rPersonData );
		return $aRow;
	}
	return null;
}

function fCustomerData( $iCSTId ){
	$rCSICase = DBQuery("SELECT * FROM crm.cst WHERE master_id = '".(int)$iCSTId."' AND case_name = 'card_nsi'");
	if ( mysql_num_rows( $rCSICase ) > 0 ){
		$aRow = mysql_fetch_array( $rCSICase );
		$iCSTId = (int) $aRow['cst_id'];
		$iPersonId = $aRow['person_id'];
		$sCompanyName = $aRow['name'];
	}
	$rContact = DBQuery("SELECT name, email FROM crm.contacts WHERE cst_id = '".(int)$iCSTId."'");
	if ( mysql_num_rows( $rContact ) > 0 ){
		$aRow = mysql_fetch_array( $rContact );
		$sContactEmail = $aRow['email'];
		$sContactName = $aRow['name'];
	}
	unset($aRow);
	$aRow['name'] = $sCompanyName;
	$aRow['contact'] = $sContactName;
	$aRow['email'] = $sContactEmail;
	$aRow['person_id'] = $iPersonId;

	return $aRow;
}

$sMailTemplate = "";
$sRawMailTemplate = "";
// Process each entry in pipeline
for ( $iType = 0; $iType < 2; $iType++ ){
	if ( $iType == 1 ){
		$iMax = 6;
	} else {
		$iMax = 5;
	}
	for ( $iStage = $iMax; $iStage > 0; $iStage-- ){
		$sRawMailTemplate = fFetchMailTemplate( $iType.$iStage );
		$iInterval = ( $iType == 0 ) ? $aType7Days[$iStage] : $aType30Days[$iStage];
		$rResult = DBQuery( "SELECT id, cst_id, type, stage, date FROM trial_mail_pipeline WHERE type = '".$iType."' AND stage = '".$iStage."' AND date <= DATE_SUB( now(), INTERVAL ".$iInterval." day ) AND pause = 0" );
		$iNumRows = mysql_num_rows( $rResult );
		if ( $iNumRows > 0 ){
			// Handle items
			for ( $i = 0; $i < $iNumRows; $i++ ){
				$bSkipSendEmail = false;
				$aRow = mysql_fetch_array( $rResult );
				$aCustomerData = fCustomerData( $aRow['cst_id'] );
				$sCompany = $aCustomerData['name'];
				$sContact = $aCustomerData['contact'];
				$iId = $aRow['id'];
				$sTo = $aCustomerData['email'];
				$aSalesPerson = fSalesPersonData( (int)$aCustomerData['person_id'] );
				if ( ( $aSalesPerson == null ) && ( $iType == 1 ) ){
					$bSkipSendEmail = true;
				}
				$sMailTemplate = fRenderMailTemplate( $sRawMailTemplate, $sCompany, $sContact, $aSalesPerson['name'],  $aSalesPerson['local_number'],  $aSalesPerson['email'] );
				if ( $sMailTemplate == "" ){
					$bSkipSendEmail = true; // Skip if there is no email template for current stage
				}
				$sSubject = ( ( $iType == 0 ) ? $aSubject7Days[$iStage] : $aSubject30Days[$iStage] );
				$sSubject = fRenderMailTemplate( $sSubject, $sCompany, $sContact,  $aSalesPerson['name'],  $aSalesPerson['local_number'],  $aSalesPerson['email'] );
				if ( $bSkipSendEmail == false ){
					fSendMail( $sTo, $sSubject, $sMailTemplate );
				}
				$bIncrement = true;
				if ( ( $iStage == 5 ) && ( $iType == 0 ) ){
					$bIncrement = false;
					fFlushMail( $iId );
				} elseif ( ( $iStage == 6 ) && ( $iType == 1 ) ){
					$bIncrement = false;
					fFlushMail( $iId );
				}
				if ( $bIncrement == true ){
					fIncrementStage( $iId, ( ( $iType == 0 ) ? $aType7Days[$iStage + 1] : $aType30Days[$iStage + 1] ) );
				}
			}
		}
	}
}

?>
