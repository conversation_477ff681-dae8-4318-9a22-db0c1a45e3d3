<?php
	// Include local configuration file
	require "configuration.php";

	echo "Initials (login):"; $sUsername = $IO->readLine();
	echo "Password (plain text!):"; $sPassword = $IO->readLine();
	echo "Person Name:"; $sPersonName = $IO->readLine();
	echo "Email:"; $sEmail = $IO->readLine();
	echo "Local phone number:"; $sNumber = $IO->readLine();
	echo "Mobile phone number:"; $sMobileNumber = $IO->readLine();

	echo "Title:"; $sTitle = $IO->readLine();

	echo "Department:"; $sDepartment = $IO->readLine();

	echo "Is Manager:"; $sIsManager = $IO->readLine();

	echo "User rights level ( 4 - Admin, 3 - SM, 2 - LM, 1 Full Search, 0 - No special rights ):"; $sRights = $IO->readLine();

	if ( $sIsManager == "" ){
		echo "Manager:"; $sManager = $IO->readLine();
	}

	echo "New user id: ".$USERS->addUser( $sUsername, $sPassword, $sPersonName, $sEmail, $sNumber, $sMobileNumber, $sTitle, $sDepartment, $sIsManager, $sManager, $sRights )."\n";
?>
