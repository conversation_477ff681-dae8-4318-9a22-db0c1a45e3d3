<?php
	// Basic IO functions
	class _IO{
		var $rStdIn; // Input stream

		function readLine(){ // Read console input line
			if ( !$this->rStdIn ){
				$this->rStdIn = fopen("php://stdin","r");
			}
			$sInput = fgets( $this->rStdIn );

			$sInput = str_replace("\r", "", $sInput);
			$sInput = str_replace("\n", "", $sInput);

			return $sInput;
		}
	}
	// Customer handling functions
	class _CUSTOMERS{
		// Store _NEW_ customer in database
		// Internal function
		var $rFile;
		function _storeCustomer( $sLine ){
			// Ignore comment lines: begining with //
			if ( ( $sLine[0] == '/' ) && ( $sLine[1] == '/' ) ){
				echo "Comment: ".htmlspecialchars( $sLine )."\r\n";
				return -1;
			}
			// Get values
			// Order: name, phone, fax, contact, email, web, company_number, timezone, category, category_clients, category_servers, category_itpeople, company_employees, company_industry,  company_revenue_usd
			// CASE NAME for new customer: Company Card
			$sCaseName = "Company Card";
			// Default comment: Imported
			$sComment = "Imported";
			$aValues = explode( ",", $sLine );
			// Secure and normalize data
			for ( $i = 0; $i < count( $aValues ); $i++ ){
				if ( trim( $aValues[$i] ) == "" ){
					$aValues[$i] = null;
				}
				$aValues[$i] = mysql_real_escape_string( $aValues[$i] );
			}

			mysql_query("INSERT INTO crm.cst ( name, phone, fax, contact, email, web, company_number, timezone, category, category_clients, category_servers, category_itpeople, company_employees, company_industry, company_revenue_usd, invoice_country, category_externalips, case_name )
				VALUES
				( '".$aValues[0]."', '".$aValues[1]."','".$aValues[2]."','".$aValues[3]."','".$aValues[4]."','".$aValues[5]."','".$aValues[6]."','".$aValues[7]."','".$aValues[8]."','".$aValues[9]."','".$aValues[10]."','".$aValues[11]."','".$aValues[12]."','".$aValues[13]."','".$aValues[14]."','".$aValues[15]."','".$aValues[16]."'
				, '".$sCaseName."' )");
		}

		function _openFile( $sFileName ){
			$this->rFile = fopen($sFileName, "r");
			if ( $this->rFile == null ){
				return -1; // Cannot open file
			}
		}

		function _parseFile(){
			while (!feof( $this->rFile ) ){
				$sLine = fgets( $this->rFile );
				$this->_storeCustomer( $sLine );
			}
			fclose( $this->rFile );
		}

		function loadCustomers( $sFileName ){
			if ( $this->_openFile( $sFileName ) == -1 ){
				echo "Cannot read file: ".htmlspecialchars( $sFileName )."\r\n";
				return -1;
			}
			$this->_parseFile();
		}
	}

	class _SEGMENTS{
		function newSegment( $sName ){
			mysql_query("INSERT INTO segments ( segment_name, segment_status ) VALUES ( '".mysql_real_escape_string( $sName )."', 0 )");
			return mysql_insert_id();
		}
	}

	// User handling functions
	class _USERS{
		function _rewriteAccessFile(){ // Function to rewrite user_access.php
			// Load existing users, for merging / updating
			require(INCLUDE_PATH."user_access.php");

			$rFile = fopen( INCLUDE_PATH."user_access.php", "w" );
			if ( $rFile != null ){
				$rResult = mysql_query("SELECT person_id, rights FROM crm.salespeople WHERE rights > 0 ORDER by rights ASC");
				$iNumRows = mysql_num_rows( $rResult );
				fwrite( $rFile, "<?php
// User access configuration file
// Required by: global_function.php -> fVerifyAccess()
// NOTE: DO NOT add any other configuration options, this file works with util/add_sales_person.php\n" );
				if ( $iNumRows > 0 ){
					for ( $i = 0; $i < $iNumRows; $i++ ){
						$aRow = mysql_fetch_array( $rResult );
						$aUserAccess[$aRow['person_id']] = $aRow['rights']; // Update or add new user id
					}
				}
				// Store new data
				foreach ( $aUserAccess as $sId => $sRights ){
					fwrite( $rFile, "\$aUserAccess[".(int)$sId."] = ".(int) $sRights.";\n" );
				}
				fwrite( $rFile, "?>" );
				fclose( $rFile );
			} else {
				echo "Cannot write file: ". INCLUDE_PATH."user_access.php";
				return -1;
			}
		}

		function addUser( $sUsername, $sPassword, $sPersonName, $sEmail, $sNumber, $sMobileNumber, $sTitle, $sDepartment, $sIsManager, $sManager, $sRights ){ // Add a new sales person
			// Store user in database
			mysql_query("INSERT INTO crm.salespeople
					( init, name, email, local_number, mobile_number, title, department, manager, is_manager, display, rights )
					VALUES
					( '".mysql_real_escape_string( $sUsername )."', '".mysql_real_escape_string( $sPersonName )."', '".mysql_real_escape_string( $sEmail )."', '".mysql_real_escape_string( $sNumber )."',
					'".mysql_real_escape_string( $sMobileNumber )."', '".mysql_real_escape_string( $sTitle )."', '".mysql_real_escape_string( $sDepartment )."', '".mysql_real_escape_string( $sManager )."', '".mysql_real_escape_string( $sIsManager )."', 1, '".(int) $sRights."' )
			");
			// Add htpasswd user
			if ( mysql_error() == "" ){
				// UNSECURE
				exec("htpasswd -b ".HTPASSWD." ".$sUsername." ".$sPassword);
			}
			// Reqrite the hardcoded access file
			$this->_rewriteAccessFile();
			return mysql_insert_id();
		}
	}

	// Departments
	class _DEPARTMENTS{
		function addDepartment( $sTeamId, $sLongName, $sShortName ){
			// Store department
			mysql_query("INSERT INTO crm.salesteams ( team_id, name, short ) VALUE ( '".mysql_real_escape_string( $sTeamId )."', '".mysql_real_escape_string( $sLongName )."', '".mysql_real_escape_string( $sShortName )."' )");
		}
	}

	// Database functions
	class _DATABASE{
		var $rConnection;
		function fConnectDatabase(){
			if ( !$this->rConnection ){
				$this->rConnection = mysql_connect( DB_HOST, DB_USER, DB_PASS);
				mysql_select_db( DB_NAME );
			}
		}
	}

$IO = new _IO();
$USERS = new _USERS();
$DATABASE = new _DATABASE();
$CUSTOMERS = new _CUSTOMERS();
$SEGMENTS = new _SEGMENTS();
$DEPARTMENTS = new _DEPARTMENTS();
$DATABASE->fConnectDatabase();
?>
