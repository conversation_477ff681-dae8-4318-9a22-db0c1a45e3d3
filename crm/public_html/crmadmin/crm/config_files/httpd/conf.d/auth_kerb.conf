#
# The mod_auth_kerb module implements Kerberos authentication over
# HTTP, following the "Negotiate" protocol.
# 

LoadModule auth_kerb_module modules/mod_auth_kerb.so

#
# Sample configuration: Kerberos authentication must only be
# used over SSL to prevent replay attacks.  The keytab file
# configured must be readable only by the "apache" user, and
# must contain service keys for "HTTP/www.example.com", where
# "www.example.com" is the FQDN of this server.
#

#<Location /private>
#  SSLRequireSSL
#  AuthType Kerberos
#  AuthName "Kerberos Login"
#  KrbMethodNegotiate On
#  KrbMethodK5Passwd Off
#  KrbAuthRealms EXAMPLE.COM
#  Krb5KeyTab /etc/httpd/conf/keytab
#  require valid-user
#</Location>
