#
# Mod_perl incorporates a Perl interpreter into the Apache web server,
# so that the Apache web server can directly execute Perl code.
# Mod_perl links the Perl runtime library into the Apache web server
# and provides an object-oriented Perl interface for Apache's C
# language API.  The end result is a quicker CGI script turnaround
# process, since no external Perl interpreter has to be started.
#

LoadModule perl_module modules/mod_perl.so

# Uncomment this line to globally enable warnings, which will be
# written to the server's error log.  Warnings should be enabled
# during the development process, but should be disabled on a
# production server as they affect performance.
#
#PerlSwitches -w

# Uncomment this line to enable taint checking globally.  When Perl is
# running in taint mode various checks are performed to reduce the
# risk of insecure data being passed to a subshell or being used to
# modify the filesystem.  Unfortunately many Perl modules are not
# taint-safe, so you should exercise care before enabling it on a
# production server.
#
#PerlSwitches -T

# This will allow execution of mod_perl to compile your scripts to
# subroutines which it will execute directly, avoiding the costly
# compile process for most requests.
#
#Alias /perl /var/www/perl
#<Directory /var/www/perl>
#    SetHandler perl-script
#    PerlResponseHandler ModPerl::Registry
#    PerlOptions +ParseHeaders
#    Options +ExecCGI
#</Directory>

# This will allow remote server configuration reports, with the URL of
#  http://servername/perl-status
# Change the ".example.com" to match your domain to enable.
#
#<Location /perl-status>
#    SetHandler perl-script
#    PerlResponseHandler Apache2::Status
#    Order deny,allow
#    Deny from all
#    Allow from .example.com
#</Location>
