#
# mod_authz_ldap can be used to implement access control and 
# authenticate users against an LDAP database.
# 

LoadModule authz_ldap_module modules/mod_authz_ldap.so

<IfModule mod_authz_ldap.c>

#   <Location /private>
#      AuthzLDAPEngine on
#    
#      AuthzLDAPServer localhost
#      AuthzLDAPUserBase ou=People,dc=example,dc=com
#      AuthzLDAPUserKey uid
#      AuthzLDAPUserScope base
#
#      AuthType basic
#      AuthName "<EMAIL>"
#      require valid-user
#
#   </Location>

</IfModule>
