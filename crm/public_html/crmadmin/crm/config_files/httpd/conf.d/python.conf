#
# Mod_python is a module that embeds the Python language interpreter
# within the server, allowing Apache handlers to be written in Python.
#

LoadModule python_module modules/mod_python.so

# Override type-map handler for /var/www/manual
<Directory "/var/www/manual/mod/mod_python">
	<Files *.html>
		SetHandler default-handler
	</Files>
</Directory>

# This will cause files beneath /var/www/html with the extension .spam
# to be handled by the Python script /var/www/html/eggs.py
#
#<Directory /var/www/html>
#    AddHandler python-program .spam
#    PythonHandler eggs
#</Directory>

# This will cause all requests to the /python heirachy of your
# webserver to be handled by the python script /path/to/myhandler.py
#
#<Location /python>
#    SetHandler python-program
#    PythonPath "sys.path + ['/path/to']"
#    PythonHandler myhandler
#</Location>

# This will cause all requests to the /python hierachy of your
# webserver to be handled by mod_python's Publisher handler
# (see http://localhost/manual/mod/mod_python/hand-pub.html)
#
#<Location /python>
#    SetHandler python-program
#    PythonHandler mod_python.publisher
#</Location>

# This will cause the output of all requests to files beneath
# /var/www/html with the extension .flt to be filtered through
# the Python script /var/www/html/filter.py
#
#<Directory /var/www/html>
#    PythonOutputFilter filter MYFILTER
#    AddOutputFilter MYFILTER .flt
#</Directory>

# This will enable use of "Python Server Pages", HTML pages with
# inline Python code which is interpreted by the server:
#
#<Directory /var/www/html>
#    AddHandler mod_python .psp
#    PythonHandler mod_python.psp
#</Directory>

