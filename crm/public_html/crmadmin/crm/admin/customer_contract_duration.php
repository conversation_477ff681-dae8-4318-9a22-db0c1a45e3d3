<?php
error_reporting(0);

// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$base = 'status = 2 && (product_price-discount) > 0 && expires_date > NOW() ';

$done = array();

// Get all paid sales
$sales = DBGetRows('crm.saleslog', $base);

while ( list($i, $sale) = each($sales) ) {
	// Get customer ids
	$cst = fGetCustomerDetails( $sale['cst_id'] );

	echo $sale['sale_id'] . "\t" . $cst['Company']['cst_id'] . "\t" . $cst['Company']['name'] . "\t" . $sale['product_name'] . "\t" . $sale['sold_date'] . "\t" . $sale['expires_date'] . "\t" . ReturnMonthsNew($sale['sold_date'], $sale['expires_date'])  . "\t" . round( ReturnMonthsNew($sale['sold_date'], $sale['expires_date']) / 12 ) . "\n";

}

function ReturnMonthsNew($from, $to)
{
	$startDate = strtotime($from);
	$stopDate = strtotime($to);

	// logic borrowed from a comment on php.net
	$months = ((idate('Y', $stopDate) * 12) + idate('m', $stopDate)) - ((idate('Y', $startDate) * 12) + idate('m', $startDate));

        return $months;
}


?>
