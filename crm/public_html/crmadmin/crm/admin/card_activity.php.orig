<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Case statuses array
$caseStatus = array(
	"card_nsi" => array(
		0 => "Canvas"
		,20 => "Intro"
		,40 => "Commitments"
		,60 => "Offer"
		,80 => "Closing"
	)
	,"card_vi" => array(
		0 => "Canvas"
		,20 => "Intro"
		,40 => "Commitments"
		,60 => "Offer"
		,80 => "Closing"
	)
	,"card_crc" => array(
		0 => "No recent contact"
		,20 => "CRC: Order Verification: Done"
		,40 => "CRC: Solution Setup time scheduled with CSC"
		,60 => "CSC: Solution setup: Done"
		,80 => "CRC: Setup verification: Done"
		,90 => "CRC: 6 month Service call: Done"
	)
	,"card_csc" => array(
		0 => "No Contact"
		,20 => "CSC: Appointment Made"
		,40 => "CSC: Setup done"
		,60 => "CSC: Inactive user"
		,80 => "CSC: Active user"
		,100 => "CSC: Active and Happy User"
	)
);

if ( isset( $_GET['s_person_id'] ) && isset( $_GET['segment_id'] ) && isset( $_GET['person_id'] ) ) {
	if ( $_GET['s_person_id'] != "" ) { // Move to sales person
		if ( $_GET['all'] == "on" ) { // Everything
			// Commit 515 applied on 21-03-2011
			mysql_query("UPDATE crm.cst SET person_id = '".(int)$_GET['s_person_id']."' WHERE person_id = '".(int)$_GET['person_id']."'");
			
		} else { // Move selected customers
			$sqlIN = "";
			foreach ( $_GET['cst_id'] as $cst_id => $on ) {
				$sqlIN .= ( $sqlIN == "" ? "" : "," ). (int)$cst_id;
			}
			mysql_query("UPDATE crm.cst SET person_id = '".(int)$_GET['s_person_id']."' WHERE person_id = '".(int)$_GET['person_id']."' AND cst_id IN ( ".$sqlIN." )");
		}
	}

	// And or move to segment
	if ( $_GET['segment_id'] != "" ) {
		if ( $_GET['all'] == "on" ) { // Everything
			// Select master cards
			$result = mysql_query("SELECT master_id FROM crm.cst WHERE person_id = '".(int)$_GET['person_id']."'");
			$sqlIN2 = "";
			for ( $i = 0; $i < mysql_num_rows( $result ); $i++ ) {
				$row = mysql_fetch_assoc( $result );
				$sqlIN2 .= ( $sqlIN2 == "" ? "" : "," ) . $row['master_id'];
			}
		} else { // Move selected customers
			$sqlIN = "";
			foreach ( $_GET['cst_id'] as $cst_id => $on ) {
				$sqlIN .= ( $sqlIN == "" ? "" : "," ). (int)$cst_id;
			}
			// Select master cards
			$result = mysql_query("SELECT master_id FROM crm.cst WHERE cst_id IN ( ".$sqlIN." )");
			// Reset case owners if selected
			if ( $_GET['reset'] == "on" ) {
				mysql_query("UPDATE crm.cst SET person_id = NULL, appointment = NULL WHERE cst_id IN ( ".$sqlIN." )");
			}
			$sqlIN2 = "";
			for ( $i = 0; $i < mysql_num_rows( $result ); $i++ ) {
				$row = mysql_fetch_assoc( $result );
				$sqlIN2 .= ( $sqlIN2 == "" ? "" : "," ) . $row['master_id'];
			}
		}
		mysql_query("UPDATE crm.cst SET segment_id = '".(int)$_GET['segment_id']."' WHERE cst_id IN ( ".$sqlIN2." )");
	}

	header("Location: card_activity.php?person_id=".(int)$_GET['person_id']."&customer=".$_GET['customer']."&sort=".$_GET['sort']."&dir=".$_GET['dir'] );
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

if ( $_GET['team'] != "" ) {
	$_GET['person_id'] = "0";
}

$trash = "";
if ( $_GET['ignoretrash'] == "on" ) {
//	$trash = " AND segment_id != 1481 ";
	$trash = " AND parent_segment_id != 1481 AND segment_id != 1481 ";
} 

$parent_segment_id = $_GET['parent_segment_id'];
$segment = "";
if ( is_numeric( $parent_segment_id ) && $parent_segment_id != 0 ) {
	$trash = "";
	$_GET['ignoretrash'] = "";
	$segment = " AND parent_segment_id = '".$parent_segment_id."' ";
}

$extraSelect = "";
$extraQuery = "";
$extraEndSelect = "";
if ( $_GET['customer'] == "on" ) {
	$extraSelect = "SELECT * FROM (";
	$extraQuery = ",(SELECT count(*) FROM crm.saleslog WHERE cst_id = A.cst_id AND ( ( status is null OR status < 3 OR status = 5 ) && ( (product_price - discount ) > 0 ) ) LIMIT 1) AS sales";
	$extraEndSelect = ") AS B WHERE sales = 1";
}

$partnerSQL = "";
if ( $_GET['partner_id'] != "" ) {
	$_GET["team"] = "";
	$_GET["person_id"] = "0";
	$partnerSQL = " partner_id = '".(int)$_GET['partner_id']."'";
}

$sort = "last_activity";
$dir = "ASC";
if ( isset( $_GET['sort'] ) && isset( $_GET['dir'] ) ) {
	switch ( $_GET['sort'] ) {
		case "cst_id":
		case "last_activity":
		case "name":
		case "segment_name":
		case "case_status":
		case "person_name":
			$sort = $_GET['sort'];
			break;
	}
	switch ( $_GET['dir'] ) {
		case "ASC":
		case "DESC":
			$dir = $_GET['dir'];
			break;
	}
}

$activity = ""; // Activity filter
if ( is_numeric( $_GET['months'] ) &&  $_GET['months'] != 0 ) {
	$activity = " AND last_activity <= DATE_SUB(CURDATE(), INTERVAL ".(int)$_GET['months']." MONTH) OR last_activity IS NULL";
}

// Add team filter
$sqlIn = "";
if ( $_GET['team'] != "" ) {
	$result = DBGetRows("crm.salespeople", "department = '".(int)$_GET['team']."'");
	for ( $i = 0; $i < count( $result ); $i++ ) {
		$sqlIn .= ( $i != "" ? "," : "" ).$result[$i]['person_id'];
	}
	$sqlIn = "person_id IN (".$sqlIn.")";
}

$query = "SELECT * FROM ( ".$extraSelect."SELECT
 		*
 		".$extraQuery."
		,(SELECT segment_name FROM crm.segments WHERE segments.segment_id = (SELECT segment_id FROM crm.cst WHERE cst_id = A.master_id LIMIT 1) LIMIT 1) AS segment_name
		,(SELECT segment_status FROM crm.segments WHERE segments.segment_id = (SELECT segment_id FROM crm.cst WHERE cst_id = A.master_id LIMIT 1) LIMIT 1) AS segment_status
		,(SELECT name FROM crm.cst WHERE cst_id = A.master_id ) AS customer_name
 		,(SELECT name FROM crm.salespeople WHERE person_id = A.person_id LIMIT 1) AS person_name
 		,(SELECT added FROM crm.comments WHERE cst_id IN ( CASE WHEN A.case_name = 'Company Card' THEN ( SELECT group_concat( cst.cst_id SEPARATOR ',' ) FROM crm.cst WHERE cst.master_id = A.cst_id AND cst.case_name IN ('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc')  ) ELSE A.cst_id END ) ORDER BY added DESC LIMIT 1 ) AS last_activity
 		,(SELECT segment_id FROM crm.cst WHERE cst_id = A.master_id LIMIT 1) AS parent_segment_id
 	FROM
 		crm.cst AS A
 	WHERE
		".( $sqlIn != "" ? $sqlIn . " AND " : ( $partnerSQL != "" ? $partnerSQL . " AND " : ( $segment != "" ? "" : " person_id = '".(int)$_GET['person_id']."' AND " ) ) )."
		case_name LIKE 'card%'
 	ORDER BY
		".$sort." ".$dir." ".$extraEndSelect." ) AS C WHERE ( ( segment_name is null AND segment_status is null ) OR ( segment_status = 0 AND segment_name LIKE 'CRM2 - %' ) ) ".$trash." ".$segment." ".$activity;

$query = "SELECT * FROM ( ".$extraSelect."SELECT
		*
		".$extraQuery."
		,(SELECT segment_name FROM crm.segments WHERE segments.segment_id = (SELECT segment_id FROM crm.cst WHERE cst_id = CASE WHEN A.case_name = 'Company Card' THEN A.cst_id ELSE A.master_id END LIMIT 1) LIMIT 1) AS segment_name
		,(SELECT segment_status FROM crm.segments WHERE segments.segment_id = (SELECT segment_id FROM crm.cst WHERE cst_id = CASE WHEN A.case_name = 'Company Card' THEN A.cst_id ELSE A.master_id END LIMIT 1) LIMIT 1) AS segment_status
		,(SELECT name FROM crm.cst WHERE cst_id = CASE WHEN A.case_name = 'Company Card' THEN A.cst_id ELSE A.master_id END LIMIT 1) AS customer_name
		,(SELECT name FROM crm.salespeople WHERE person_id = A.person_id LIMIT 1) AS person_name
		,(SELECT added FROM crm.comments WHERE cst_id IN ( CASE WHEN A.case_name = 'Company Card' THEN ( SELECT group_concat( cst.cst_id SEPARATOR ',' ) FROM crm.cst WHERE cst.master_id = A.cst_id AND cst.case_name IN ('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc')  ) ELSE A.cst_id END ) ORDER BY added DESC LIMIT 1 ) AS last_activity
	FROM
		crm.cst AS A
	WHERE
		".( $sqlIn != "" ? $sqlIn : ( $partnerSQL != "" ? $partnerSQL : " person_id = '".(int)$_GET['person_id']."'" ) )."
		AND case_name LIKE 'card%'
		".$trash."
	ORDER BY
		".$sort." ".$dir." ".$extraEndSelect." ) AS C WHERE ( ( segment_name is null AND segment_status is null ) OR ( segment_status = 0 AND segment_name LIKE 'CRM2 - %' ) ) ".$activity;

// Fetch shadow cards
$aShadows = DBGetRows('crm.cst', "master_id IS NOT NULL AND cst.person_id = '" . (int)$_GET['person_id'] . "' AND (cst.case_name NOT IN('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc', 'Company Card' ) OR case_name IS NULL)");
$shadows = array();

for ( $i = 0; $i < count( $aShadows ); $i++ ) {
	$shadows[$aShadows[$i]['cst_id']] = true;
}

//if ( $_GET['person_id'] != "" ) {
if ( $_GET['person_id'] != "" || $_GET['partner_id'] != "" || $_GET['parent_segment_id'] != "" || $_GET['team'] != "" ) {
	$results = mysql_query( $query );

	$output = "";
	$j = 0;
	for ( $i = 0; $i < mysql_num_rows( $results ); $i++ ) {
		$row = mysql_fetch_assoc( $results );

		if ( $shadows[$row['cst_id']] ) {
			continue; // Ignore shadow cards
		}
		$output .= "<tr style='background-color:".( $j % 2 == 0 ? 'lightgray' : 'white' ).";'>
		<td style='padding: 5px;'>
			<input type='checkbox' style=\"display: inline; width: 10px;\" name='cst_id[".(int)$row['cst_id']."]'>
			<a href='".HTTP_PATH_CRM2."?page=customer&cst_id=".(int)$row['cst_id']."' target='_blank'>".(int)$row['cst_id']."</a>
		</td>
		<td style='padding: 5px;'>
			".htmlspecialchars( $row['last_activity'] )."
		</td>
		<td style='padding: 5px;'>
			".htmlspecialchars( $row['customer_name'] )."
		</td>
		".( $_GET['team'] != "" ?
		"<td style='padding: 5px;'>
			".htmlspecialchars( $row['person_name'] )."
		</td>" : ""
		)."
		<td style='padding: 5px;'>
			".htmlspecialchars( $row['case_name'] )."
		</td>
		<td style='padding: 5px;'>
			".htmlspecialchars( str_replace( "CRM2 - ", "", $row['segment_name'] ) )."
		</td>
		<td style='padding: 5px;'>
			".htmlspecialchars( $caseStatus[$row['case_name']][$row['case_status']] )."
		</td>
	</tr>
	";
	$j++;
	}
}
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td>
			<form method="GET" action="card_activity.php">
			<b>Select Sales Person's Cards: </b>
			<?php
			// Select all salespeople
			echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
			or <b>Sales Team</b>
			<?php
			echo returnSelectBox(array($team), 'team', false, 'select * from salesteams where team_id > 0 order by name', 'team_id', 'name');
			?>
			or <b>Partner</b>
			<?php
			echo returnSelectBox( array( ( int )$_GET['partner_id'] ), 'partner_id', false, 'SELECT partner_id, ( SELECT name FROM crm.cst WHERE cst_id = ( select master_id FROM crm.cst where cst_id = ( SELECT cst_id FROM ca.accounts WHERE account_id = ( SELECT account_id FROM ca.partner_profile WHERE partner_id = A.partner_id ) ) ) ) AS name FROM ca.partner_profile AS A WHERE account_id = (SELECT account_id FROM ca.accounts WHERE cst_id IN ( SELECT cst_id from crm.cst as A where ( partner_id is null OR partner_id = 0 ) AND case_name = "card_partner" AND (SELECT active_partner FROM crm.cst WHERE cst.cst_id = A.master_id) = 1 ) AND special_limits LIKE "partner%")', 'partner_id', 'name');
			?>
			or <b>Segment ID</b>
			<?php
				function fGenerateSegmentSelectBox( $iSegmentID ){
					$sReturn = makeSegmentCombo( $iSegmentID );
					return $sReturn;
				}
				echo fGenerateSegmentSelectBox( $_GET['parent_segment_id'] );
			?>
			<br/>
			Cards with no activity within the past <input type='text' style="display: inline; width: 20px;" name='months' value="<?php echo is_numeric( $_GET['months'] ) ? $_GET['months'] : "" ; ?>">months.
			<br/>
			<input type='checkbox' style="display: inline; width: 10px;" name='customer' <?php echo $_GET['customer'] == "on" ? "checked" : "" ; ?>>Show Customers only
			<br/>
			<input type='checkbox' style="display: inline; width: 10px;" name='ignoretrash' <?= $_GET['ignoretrash'] == "on" ? "checked " : "" ?>>Ignore Trash
                        <br/>
			<input type="submit" value="Display" style="display: inline; width: 50px;">
			</form>
		</td>
	</tr>
</table>
<?php
	if ( $_GET['person_id'] == "" && $_GET['team'] == "" && $_GET['partner_id'] == "" && $_GET['parent_segment_id'] == "" ) {
		exit();
	}
?>
<form action="card_activity.php" method="GET">
<input type='hidden' name="person_id" value="<?= (int)$_GET['person_id'] ?>">
<input type='hidden' name="team" value="<?= (int)$_GET['team'] ?>">
<input type='hidden' name="months" value="<?= (int)$_GET['months'] ?>">
<input type='hidden' name="partner_id" value="<?= (int)$_GET['partner_id'] ?>">
<input type='hidden' name="sort" value="<?= htmlspecialchars( $_GET['sort'] ) ?>">
<input type='hidden' name="dir" value="<?= htmlspecialchars( $_GET['dir'] ) ?>">
<input type='hidden' name='customer' value="<?php echo $_GET['customer'] == "on" ? "on" : "" ; ?>">
<input type='submit' value="Move" style="display: inline; width: 50px;"> to Sales Person:
<?php
	// Select all salespeople
	echo returnSelectBox(array(0), 's_person_id', false, 'SELECT * FROM salespeople ORDER BY name ASC', 'person_id', 'name');
?>
or Segment ID
<?php
	// Select all segments
	echo returnSelectBox( array(0), 'segment_id', false, 'SELECT segment_id, REPLACE(segment_name, "CRM2 - ", "") AS segment_name FROM segments WHERE segment_status = 0 AND segment_name LIKE "CRM2 - %" ORDER BY segment_name ASC', 'segment_id', 'segment_name');
?>
<table cellpadding="0" cellspacing="0" width="100%">
<tr>
	<td colspan="5">
		<input type='checkbox' style="display: inline; width: 10px;" name='all'>Apply to all<br/>
		<input type='checkbox' style="display: inline; width: 10px;" name='reset'>Reset Case Owner
	</td>
</tr>
<tr>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=cst_id&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Customer ID</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=last_activity&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Last Activity(comment)</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=name&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Customer Name</a>
	</td>
	<?php
	if ( $_GET['team'] != "" ) {
	?>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=person_name&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Sales Person Name</a>
	</td>
	<?php
	}
	?>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=case_name&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Case Name</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=segment_name&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Segment Name</a>
	</td>
	<td style='padding: 5px;'>
		<a href="?person_id=<?= (int)$_GET['person_id'] ?>&partner_id=<?= (int)$_GET['partner_id'] ?>&team=<?= (int)$_GET['team'] ?>&sort=case_status&dir=<?= isset( $_GET['dir'] ) && $_GET['dir'] == "ASC" ? "DESC" : "ASC" ?>&months=<?= (int)$_GET['months'] ?>">Case Status</a>
	</td>
</tr>
<?php echo $output; ?>
</table>
</form>
