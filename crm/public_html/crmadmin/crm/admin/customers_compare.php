<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

// Update
if ( $_POST['task'] == 'update' )
{
	while ( list($cst_id, $value) = @each($_POST['kill']) )
	{
		if ( $value )
		{
			mysql_query("update cst set appointment = '0000-00-00 00:00:00' where cst_id = '" . $cst_id . "' limit 1");
		}
	}

	while ( list($cst_id, $person_id) = @each($_POST['move_cst']) )
	{
		if ( $person_id > 0 )
		{
			mysql_query("update cst set person_id = '" . $person_id . "' where cst_id = '" . $cst_id . "' limit 1");
		}
	}
}
?>

<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="3" width="100%">
			Compare Customers Between Two Sales People
		</td>
	</tr>
	<tr>
		<td colspan="3" width="100%">
			<br>
		</td>
	</tr>
	<form method="POST" action="customer_compare.php">
	<input type="hidden" name="task" value="compare">
	<tr>
		<td width="30%">
			<select name="person_1">
			<option value="0"> - Select Sales Person 1 - </option>
			<?php
			// Select sales people
			$res = mysql_query("select * from salespeople where display = 1 order by name");
			while ( $row = mysql_fetch_array($res) )
			{
				echo '<option value="' . $row['person_id'] . '"' . ( $_POST['person_1'] == $row['person_id'] ? 'selected' : '') . '>' . $row['name'] . '</option>';
			}
			?>
			</select> vs.
		</td>
		<td width="30%">
			<select name="person_2">
			<option value="0"> - Select Sales Person 2 - </option>
			<?php
			// Select sales people
			$res = mysql_query("select * from salespeople where display = 1 order by name");
			while ( $row = mysql_fetch_array($res) )
			{
				echo '<option value="' . $row['person_id'] . '"' . ( $_POST['person_2'] == $row['person_id'] ? 'selected' : '') . '>' . $row['name'] . '</option>';
			}
			?>
			</select>
		</td>
		<td width="60%">
			<input type="submit" value="Compare Sales People">
		</td>
	</tr>
	</form>
	<tr>
		<td colspan="3" width="100%">
			<br>
			<br>
		</td>
	</tr>
	<?php
	if ( $_POST['task'] == 'compare' && $_POST['person_1'] && $_POST['person_2'] )
	{
		// Select sales people
		$sp_res = mysql_query("select * from salespeople where display = 1 order by name");
		while ( $sp_row = mysql_fetch_array($sp_res) )
		{
			$options .= '<option value="' . $sp_row['person_id'] . '">' . $sp_row['name'] . '</option>';
		}
		?>
		<form action="customers_compare.php" method="POST">
		<input type="hidden" name="task" value="update">
		<tr>
			<td>
				<b>Company name</b>
			</td>
			<td>
				<b>Appointment / Sales Person</b>
			</td>
			<td>
				<b>Kill / Move to new person</b>
			</td>
		</tr>
		<tr>
			<td colspan="3" width="100%">
				<br>
			</td>
		</tr>
		
		<?php
		// Select all active customers from SP1
		$res = mysql_query("select * from cst where appointment > '0000-00-00 00:00:00' && person_id = '" . $_POST['person_1'] . "' order by name");

		// Loop over result
		while ( $row = @mysql_fetch_array($res) )
		{
			// Compare with other salespersons customers
			$c_res = mysql_query("select * from cst where cst_id != " . $row['cst_id'] . " && person_id != '" . $_POST['person_1'] . "' && appointment > '0000-00-00 00:00:00' && " . ( $_POST['person_2'] ? " person_id = '" . $_POST['person_2'] . "' && " : '' ) . "name like '" . substr($row['name'],0, ( strpos($row['name'], ' ') ? strpos($row['name'], ' ') : strlen($row['name']) ) ) . "%'");
			if ( !mysql_num_rows($c_res) )
			{
				continue;
			}
			?>
			<tr>
				<td>
					<a href="<?= HTTP_PATH_CRM2; ?>customer.php?cst_id=<?=$row['cst_id']?>" target="_blank"><b><?=htmlspecialchars($row['name'])?></b></a>
				</td>
				<td>
					<?=$row['appointment']?>
				</td>
				<td>
					<input type="checkbox" style="width: 15px;" name="kill[<?=$row['cst_id']?>]"> / 
					<select name="move_cst[<?=$row['cst_id']?>]">
					<option value="0"> - Move to - </option>
					<?=$options?>
					</select>
				</td>
			</tr>
			<?php
			$bgcolor = "#FFFFFF";
			while ( $c_row = mysql_fetch_array($c_res) )
			{
				if ( $bgcolor == '#FFFFFF' )
				{
					$bgcolor = "#CECECE";
				}
				else
				{
					$bgcolor = "#FFFFFF";
				}
				?>
				<tr bgcolor="<?=$bgcolor?>">
					<td>
						&nbsp;->&nbsp;</a><a href="<?= HTTP_PATH_CRM2; ?>customer.php?cst_id=<?=$c_row['cst_id']?>" target="_blank"><?=htmlspecialchars($c_row['name'])?></a>
					</td>
					<td>
						<?=$c_row['appointment']?> - <?=ReturnPersonNameFromID($c_row['person_id'])?>
					</td>
					<td>
						<input type="checkbox" style="width: 15px;" name="kill[<?=$c_row['cst_id']?>]"> / 
						<select name="move_cst[<?=$c_row['cst_id']?>]">
						<option value="0"> - Move to - </option>
						<?=$options?>
						</select>
					</td>
				</tr>
				<?php
			}
			?>
			<tr>
				<td colspan="3" width="100%">
					<br>
				</td>
			</tr>
			<?php
		}
		?>
		<tr>
			<td colspan="3" width="100%">
				<br>
			</td>
		</tr>
		<tr>
			<td colspan="3" width="100%">
				<input type="submit" value="Update">
			</td>
		</tr>
		<tr>
			<td colspan="3" width="100%">
				<br>
			</td>
		</tr>
		</form>
		<?php
	}
	?>
	<tr>
		<td colspan="3" width="100%">
			<br>
		</td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
