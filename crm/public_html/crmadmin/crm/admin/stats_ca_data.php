<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Variables
$sp_options = '';
$headline = '';
$table = '';
$end_smg = '';

// Select all salespeople - for building the options box
$res = mysql_query("select *  from salespeople order by name");
while ( $row = mysql_fetch_array($res) )
{
	$sp_options .= '<option value="' . $row['person_id'] . '">' . htmlspecialchars($row['name']) . '</option>';

	if ( $_GET['person_id'] == $row['person_id'] )
	{
		$person = $row['name'];
	}
}

// Form or Data
if ( !$_GET['submitted'] )
{
	// Generate form

	// Headline
	$headline = 'Customer Area Data';

	// Form Table
	$table = '
	<form method="GET" action="">
	<input type="hidden" name="submitted" value="1">
	<tr>
		<td colspan="2"><b>Period</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="40%" style="padding-left: 15px;">From:<br>
		<input type="text" name="from" value="' . date('Y-m-d') . '"></td>
		<td width="60%">To:<br>
		<input type="text" name="to" value="' . date('Y-m-d') . '"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;">Data Grouping:<br>
		<select name="grouping">
			<option value="Days">Days</option>
			<option value="Weeks" selected>Weeks</option>
			<option value="Months">Months</option>
			<option value="Years">Years</option>
		</select></td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr><!--
	<tr>
		<td colspan="2"><b>Limit To Sales Person</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;" colspan="2">
		<select name="person_id">
			<option value="">- Select Sales Person -</option>
			' . $sp_options . '
		</select>
		</td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>-->
	<tr>
		<td colspan="2"><b>Generate</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;"><input type="submit" value="Generate Data"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	</form>';
}
else
{
	// Generate Report Data

	// Data Grouping
	switch ( $_GET['grouping'] )
	{
		case 'Weeks':
			$last = 'W';
			$period_selector = 'W - Y';
			break;

		case 'Days':
			$last = 'd';
			$period_selector = 'd/m - Y';
			break;

		case 'Months':
			$last = 'm';
			$period_selector = 'm - Y';
			break;

		case 'Years':
			$last = 'Y';
			$period_selector = 'Y';
			break;
	}

	// Generate periods
	{
		// Get seconds
		$from = strtotime($_GET['from']);
		$to = strtotime($_GET['to']);
	
		// How many Months are we spanning
		for ( $i = $from ; $i <= $to ; $i += 86400 )
		{
			// Move to next if already done
			if ( $l == date($last, $i) )
			{
				continue;
			}
			$l = date($last, $i);
	
			// Store period
			$period[date($period_selector, $i)] = ++$t;
			$t_period[$t] = date($period_selector, $i);
		}
	}

	// Select all logins
	$res = mysql_query("select * from ca.usage_logins where login >= '" . $_GET['from'] . "' && login <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		$logins_count[$period[date($period_selector, strtotime($row['login']))]]++;

		// Account Type
		$logins_type_count[$row['product_type']][$period[date($period_selector, strtotime($row['login']))]]++;
	}

	// Select page views
	$res = mysql_query("select * from ca.usage_page_views where viewed >= '" . $_GET['from'] . "' && viewed <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		$page_views_count[$period[date($period_selector, strtotime($row['viewed']))]]++;
	}

	// Select SD device submissions
	$res = mysql_query("select * from ca.im_devices where submitted >= '" . $_GET['from'] . "' && submitted <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		$sd_devices_count[$period[date($period_selector, strtotime($row['submitted']))]]++;
	}

	// Select alerts issued
	$res = mysql_query("select * from ca.usage_alerts where issued >= '" . $_GET['from'] . "' && issued <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		if ( $row['type'] == 1 && strlen($row['recipient']) > 3 )
		{
			$email_count[$period[date($period_selector, strtotime($row['issued']))]]++;
		}
		elseif ( $row['type'] == 2 && strlen($row['recipient']) > 3 )
		{
			$sms_count[$period[date($period_selector, strtotime($row['issued']))]]++;
		}
	}

	// Select all scan conducted using 'embedded scanner'
	$res = mysql_query("select * from testzone.ca_vulnerability_scan_usage_log where requested >= '" . $_GET['from'] . "' && requested <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		$embedded_scans_count[$period[date($period_selector, strtotime($row['requested']))]]++;
	}

	// Select all scan conducted using 'scheduled scanner'
	$res = mysql_query("select scan_started from ca.vss_nessus_reports_raw where scan_started >= '" . $_GET['from'] . "' && scan_started <= '" . $_GET['to'] . " 23:59:59'");
	while ( $row = mysql_fetch_array($res) )
	{
		$scheduled_scans_count[$period[date($period_selector, strtotime($row['scan_started']))]]++;
	}

	// Generate HTML output based on grouped values
	$width = 350;
	for ( $i = 1 ; $i <= $t ; $i++ )
	{
		// Field type? (borders)
		if ( $i < $t )
		{
			$field = 'field';
		}
		else
		{
			$field = 'field-end';
		}

		// Legend
		$html_legend .= '<td class="' . $field . '" width="100" align="center"><b>' . $t_period[$i] . '</b></td>';
		$width += 100;

		// Total Logins
		$html_logins_count .= '<td class="' . $field . '" align="right">' . ( $logins_count[$i] ? number_format($logins_count[$i]) : '0' ) . '</td>';

		// Account Type Logins
		for ( $l = 1 ; $l <= 5 ; $l++ )
		{
			$html_logins_type_count[$l] .= '<td class="' . $field . '" align="right">' . ( $logins_type_count[$l][$i] ? number_format($logins_type_count[$l][$i]) : '0' ) . '</td>';
		}

		// Total Page Views
		$html_page_views_count .= '<td class="' . $field . '" align="right">' . ( $page_views_count[$i] ? number_format($page_views_count[$i]) : '0' ) . '</td>';

		// Total Email Alerts
		$html_email_count .= '<td class="' . $field . '" align="right">' . ( $email_count[$i] ? number_format($email_count[$i]) : '0' ) . '</td>';

		// Total SD Device submissions
		$html_sd_devices_count .= '<td class="' . $field . '" align="right">' . ( $sd_devices_count[$i] ? number_format($sd_devices_count[$i]) : '0' ) . '</td>';

		// Total SMS Alerts
		$html_sms_count .= '<td class="' . $field . '" align="right">' . ( $sms_count[$i] ? number_format($sms_count[$i]) : '0' ) . '</td>';

		// Total Embedded Scan Done
		$html_embedded_scans_count .= '<td class="' . $field . '" align="right">' . ( $embedded_scans_count[$i] ? number_format($embedded_scans_count[$i]) : '0' ) . '</td>';

		// Total Scheduled Scan Done
		$html_scheduled_scans_count .= '<td class="' . $field . '" align="right">' . ( $scheduled_scans_count[$i] ? number_format($scheduled_scans_count[$i]) : '0' ) . '</td>';

		// Average Page Views pr. Login
		$html_avg_page_views_count .= '<td class="' . $field . '" align="right">' . ( $page_views_count[$i] ? number_format(round(($page_views_count[$i]/$logins_count[$i]))) : '0' ) . '</td>';
	}

	// Build output + leading legend
	$table = '
	<tr bgcolor="#E3E3E3">
		<td width="350" class="field"><b>Customer Usage / ' . htmlspecialchars($_GET['grouping']) . ' (' . htmlspecialchars($_GET['from']) . ' - ' . htmlspecialchars($_GET['to']) . ')<b></td>' . $html_legend . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Logins</b></td>
	</tr>
	<tr>
		<td class="field"> - Total Number of Logins</td>' . $html_logins_count . '
	</tr>
	<tr>
		<td class="field"> - Total Number of Page Views</td>' . $html_page_views_count . '
	</tr>
	<tr>
		<td class="field"> - Average Page Views / Login</td>' . $html_avg_page_views_count . '
	</tr>
	<tr>
		<td class="field"> - Number of Logins: VTS</td>' . $html_logins_type_count[1] . '
	</tr>
	<tr>
		<td class="field"> - Number of Logins: SM</td>' . $html_logins_type_count[2] . '
	</tr>
	<tr>
		<td class="field"> - Number of Logins: ESM</td>' . $html_logins_type_count[3] . '
	</tr>
	<tr>
		<td class="field"> - Number of Logins: VTS-E</td>' . $html_logins_type_count[4] . '
	</tr>
	<tr>
		<td class="field"> - Number of Logins: ESM</td>' . $html_logins_type_count[5] . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Alerts Sent to Customers</b></td>
	</tr>
	<tr>
		<td class="field"> - Email</td>' . $html_email_count . '
	</tr>
	<tr>
		<td class="field"> - SMS</td>' . $html_sms_count . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Scans Conducted</b></td>
	</tr>
	<tr>
		<td class="field"> - Embedded Scanner</td>' . $html_embedded_scans_count . '
	</tr>
	<tr>
		<td class="field"> - Surveillance Scanner</td>' . $html_scheduled_scans_count . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Software Detector</b></td>
	</tr>
	<tr>
		<td class="field"> - Devices Submitted</td>' . $html_sd_devices_count . '
	</tr>
';

	// Headline
	$headline = 'Customer Area Data: ' . ( $person ? $person : 'All Sales People' );

	// End messages
	$end_msg = '';
}

// Display data in template
require('stats_template.php');






?>
