<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Require field template
require(INCLUDE_PATH_LIB.'customer_temp_2.php');

echo "<table>";

// Select data
$s_res = mysql_query("select distinct cst_id from saleslog where ( (product_price - discount) > 0 ) && lang_id != 2 && person_id in (2,3) && (status is null || status < 3)");

// Loop over result
while ( $s_row = mysql_fetch_array($s_res) )
{
	// Select data
	$res = mysql_query("select * from cst where cst_id = '" . $s_row['cst_id'] . "' limit 1");
	$row = mysql_fetch_array($res);


switch ( $row['lang_id'] )
{
case 2:
$country = 'Denmark';
break;
case 4:
$country = 'Norway';
break;
case 3:
$country = 'Sweden';
break;
default:
$country = 'United Kingdom / Germany / Sweden';
break;

}
if ( $row['lang_id'] == 2 )
{
	continue;
}
	echo '<tr><td>' . $row['name'] . '</td><td>' . $country . '</td><td>' . $row['phone'] . '</td></tr>';
}

echo "</table>";

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
        $split = explode('-', $date);
        $date = $split[2] . '-' . $split[1] . '-' . $split[0];

        // Return formatted date
        return $date;
}

?>
