<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Update message
if ( $_GET['message'] )
{
	mysql_query("update crm_message set message = '" . $_GET['message'] . "'");
	$updated = 1;
}

// Select message
$res = mysql_query("select * from crm_message");
$row = mysql_fetch_array($res);

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration', '/crmadmin/crm/admin/index.php');
?>
<br>
<table width="600" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Update Message
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<?php
	if ( $updated )
	{
	?>
	<tr>
		<td colspan="2" width="600">
			<b>Message Updated.</b>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<?php
	}
	?>
	<form method="GET" action="update_message.php">
	<tr>
		<td colspan="2">
			</b>Update Message</b><br>
			<input type="text" name="message" value="<?=htmlspecialchars($row['message'])?>">
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="2">
			<input type="submit" value="Update">
		</td>
	</tr>
	</form>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
