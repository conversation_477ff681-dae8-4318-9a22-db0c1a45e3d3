AuthUserFile /home/<USER>/.htpasswd
AuthGroupFile /dev/null
AuthName "Secunia"
AuthType Basic
<Limit GET POST PUT DELETE CONNECT OPTIONS PATCH PROPFIND PROPPATCH MKCOL COPY MOVE LOCK UNLOCK>
Deny from all
#Secunia
#Allow from 80.161.200.182
#TS2
Allow from 192.168.50.71
Allow from 192.168.50.72
Allow from 192.168.50.74
Allow from 192.168.50.75
#PXY
Allow from 192.168.50.205

#Windows PRODUCTION
Allow from 192.168.53.71
Allow from 192.168.53.62
Allow from 192.168.53.63
Allow from 192.168.53.124
Allow from 192.168.53.121
Allow from 192.168.53.124
Allow from 192.168.53.21
Allow from 192.168.53.254
Allow from 192.168.50.52
Allow from 192.168.54
Allow from 192.168.53.17
Allow from 89.233.1.75
Allow from 172.25.0.10
Allow from 172.16

Require user nt
Require user jb
Require user tp
Require user hz
Require user vchand
Order deny,allow
</Limit>
