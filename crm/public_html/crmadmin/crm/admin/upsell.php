<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

$cols = 1;
$_cols = 1;
$output = "";
$customers = array();
$moneySold = 0;
$moneyExpired = 0;
$countSold = 0;
$countExpired = 0;
function customer( $id ) {
	return DBGetRowValue('crm.cst', 'name', 'cst_id = ( SELECT master_id FROM crm.cst WHERE cst_id = '.(int)$id.')' );
}

function render( $value, $key, $type = 'expired' ) {
	global $output;
	if ( $type == 'expired' ) {
		global $customers;
		global $moneyExpired;
		global $countExpired;
		$countExpired++;
		$moneyExpired += ( (float) ( $value['product_price'] - $value['discount'] ) ) * fFOREX( $value['currency_exchange_rate'], $value['currency_exchange_rate_euro'] );
		// Get all expired sales
		$customers[$value['cst_id']] = '-';
	} elseif ( $type == 'sold' ) {
		// Get the amount of sold, for the same customers, which could indicate a retention or upsale rate
		// This time the key is a customer id
		// Note: We only include VALID/APPROVED sales here!
		$result = DBQueryGetRows("SELECT * FROM crm.saleslog WHERE sold_date >= '".mysql_real_escape_string( $_GET['start_date'] )."' AND sold_date <= '".mysql_real_escape_string( $_GET['end_date'] )."' AND cst_id = '".(int)$key."' AND status = 2");
		array_walk( $result, 'render', 'sale' );
		return;
	} elseif ( $type == 'sale' ) {
		global $countSold;
		global $moneySold;
 		$moneySold += ( (float) ( $value['product_price'] - $value['discount'] ) ) * fFOREX( $value['currency_exchange_rate'], $value['currency_exchange_rate_euro'] );
		$countSold++;
	}
	$output .= '<tr>
		<td>'.customer( $value['cst_id'] ).'</td>
		<td>'.$value['expires_date'].'</td>
		<td>'.htmlspecialchars( $type ).'</td>
	</tr>';
}

if ( $_GET['submitted'] == "1" ) {
	// Fetch all sales
	$result = DBQueryGetRows("SELECT * FROM crm.saleslog WHERE expires_date >= '".mysql_real_escape_string( $_GET['start_date'] )."' AND expires_date <= '".mysql_real_escape_string( $_GET['end_date'] )."' AND status = 2 ORDER BY expires_date DESC");
	array_walk( $result, 'render' );
	array_walk( $customers, 'render', 'sold' );
}
?>
<br>
<form method="GET" action="upsell.php">
<input type="hidden" name="submitted" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="<?= $cols ?>"><b>Upsell / Resale Report</b></td>
	</tr>
	<tr>
		<td colspan="<?= $cols ?>">Select date:</td>
	</tr>
	<tr>
		<td colspan="<?= $cols ?>"><input name="start_date" type="text" style="display: inline; width: 100px;" value="<?= htmlspecialchars( $_GET['start_date'] ) ?>"> - <input name="end_date" type="text" style="display: inline; width: 100px;" value="<?= htmlspecialchars( $_GET['end_date'] )?>"> &nbsp;<input style="display: inline; width: 100px;" type="submit" value="Display"></td>
	</tr>
</table>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="<?= $_cols ?>"><b>Expired customers: <?= $countExpired ?></b></td>
		<td colspan="<?= $_cols ?>"><b>Sold customers: <?= $countSold ?></b></td>
		<td colspan="<?= $_cols ?>"><b>Retention:<?= round( ( $countSold - $countExpired ) / $countExpired * 100, 2 ) ?>%</b></td>
	</tr>
	<tr>
		<td colspan="<?= $_cols ?>"><b>Expired amount: <?= number_format( $moneyExpired, 2 ) ?></b></td>
		<td colspan="<?= $_cols ?>"><b>Sold amount: <?= number_format( $moneySold, 2 ) ?></b></td>
		<td colspan="<?= $_cols ?>"><b>Upsell: <?= round( ( $moneySold - $moneyExpired ) / $moneyExpired * 100, 2 ) ?>%</b></td>
	</tr>
	<tr>
		<td colspan="<?= $_cols ?>"><b>Customer</b></td>
		<td colspan="<?= $_cols ?>"><b>Expired/Expire date</b></td>
		<td colspan="<?= $_cols ?>"><b>Type</b></td>
	</tr>
	<?= $output; ?>
</table>