<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

// Update data
if ( $_POST && $_GET['edit'] ) {
	$sQuery = "crm.salespeople SET name = '" . $_POST['name'] . "', local_number = '" . $_POST['local_number'] . "', mobile_number = '" . $_POST['mobile_number'] . "', title = '" . $_POST['title'] . "', product = '" . $_POST['product'] . "', forecast_start_month = '" . $_POST['forecast_start_month'] . "', lost_segment_id = '" . $_POST['lost_segment_id'] . "', canvas_segment_id = '" . $_POST['canvas_segment_id'] . "', department = '" . $_POST['department'] . "', is_manager = '" . $_POST['is_manager'] . "', manager = '" . $_POST['manager'] . "', lang_id = 1, person_level = '".$_POST['person_level']."', iae_id='".$_POST['iae_id']."', vuln_lang_id = 1, display = 1" . ( $_POST['init'] ? ", init = '" . $_POST['init'] . "', email = '" . $_POST['init'] . "@secunia.com'" : '' );

	// Save the new Salesperson Profile Type if valid
	if ( isset($_POST['profile_type_id']) && is_pint($_POST['profile_type_id']) ) {
		$sQuery .= ', profile_type_id = ' . $_POST['profile_type_id'];
	}

	// New or update?
	if ( is_numeric($_GET['edit']) ) {
		DBQuery("UPDATE " . $sQuery . " WHERE person_id = '" . $_GET['edit'] . "'");
	} else {
		DBQuery("INSERT INTO " . $sQuery);
		$_GET['edit'] = mysql_insert_id();
	}

	// Redirect
	header("location: contact_list.php");
}

// "Delete" user
if ( is_numeric($_GET['delete']) ) {
	DBQuery("UPDATE crm.salespeople SET display = 0 WHERE person_id = '" . $_GET['delete'] . "' LIMIT 1");

	// Redirect
	header("location: contact_list.php");
}

?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2">
			Internal Contact List
		</td>
	</tr>
	<tr>
		<td><br>[ <a href="contact_list.php">Overview</a> ] [ <a href="contact_list.php?edit=new">New Person</a> ]<br><br></td>
	</tr>
<?php
// List People?
if ( !$_GET['edit'] ) {
	// Show list
	$aReps = DBGetRows('crm.salespeople', 'display = 1', 'name');

	echo '
	<tr>
		<td><b>Name</b></td>
		<td><b>ID</b></td>
		<td><b>Action</b></td>
	</tr>';

	// Loop through people
	while ( list($iKey, $aRep) = each($aReps) ) {
		echo '
	<tr>
		<td width="20%" style="border-top: 1px solid #DEDEDE;">' . htmlspecialchars($aRep['name']) . ' (' . htmlspecialchars($aRep['init']) . ')</td>
		<td width="10%" style="border-top: 1px solid #DEDEDE;">' . htmlspecialchars($aRep['person_id']) . '</td>
		<td width="70%" style="border-top: 1px solid #DEDEDE;">[ <a href="?edit=' . $aRep['person_id'] . '">Edit</a> ] [ <a href="?delete=' . $aRep['person_id'] . '" onClick="return confirm(\'ARE YOU SURE YOU WISH TO DELETE: ' . htmlspecialchars($aRep['name']) . '?\');">Delete</a> ]</td>
	</tr>';
	}

} else { // Update data / Create new
	// Show specific person
	$aRep = DBGetRow('crm.salespeople', "person_id = '" . $_GET['edit'] . "'");
	?>

	<form method="POST" action="?edit=<?= htmlspecialchars($_GET['edit']) ?>">

	<table width="100%" cellspacing="0" cellpadding="0">
		<tr>
			<td><b>Base Details</b><br><br></td>
		</tr>
		<tr>
			<td width="20%">Name</td>
			<td width="80%"><input type="text" name="name" style="width: 250px;" value="<?= htmlspecialchars($aRep['name']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Initials (only on creation)</td>
			<td width="80%"><?= ( $_GET['edit'] == 'new' ? '<input type="text" name="init" style="width: 250px;" value="">' : htmlspecialchars($aRep['init']) ) ?></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Email (not editable)</td>
			<td width="80%"><?= htmlspecialchars($aRep['email']) ?></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Local Number</td>
			<td width="80%"><input type="text" name="local_number" style="width: 250px;" value="<?= htmlspecialchars($aRep['local_number']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Mobile</td>
			<td width="80%"><input type="text" name="mobile_number" style="width: 250px;" value="<?= htmlspecialchars($aRep['mobile_number']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Title</td>
			<td width="80%"><input type="text" name="title" style="width: 250px;" value="<?= htmlspecialchars($aRep['title']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Is Manager?</td>
			<td width="80%"><input type="radio" name="is_manager" value="1" <?= ( $aRep['is_manager'] == 1 ? ' checked' : '' ) ?>> Yes <input type="radio" name="is_manager" value="0" <?= ( !$aRep['is_manager'] ? ' checked' : '' ) ?>> No</td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Manager</td>
			<td width="80%">
				<select name="manager">
					<option value="0"> - No Manager - </option>
					<?php
					$aManagers = DBGetRows('crm.salespeople', 'is_manager = 1', 'name');
					while ( list($iKey, $aManager) = each($aManagers) ) {
						echo '<option value="' . $aManager['person_id'] . '"' . ( $aManager['person_id'] == $aRep['manager'] ? ' selected' : '' ) . '>' . htmlspecialchars($aManager['name']) . '</option>';
					}
					?>
				</select>
			</td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">Department</td>
			<td width="80%"><select name="department" style="width: 250px;">
				<?php
				$aDepartments = DBGetRows('crm.salesteams', '', 'name');
				foreach($aDepartments as $aDepartment){
					echo "<option value=\"".$aDepartment['team_id']."\"".( $aRep['department'] == $aDepartment['team_id'] ? ' selected' : '' ).">".$aDepartment['name']."</option>";
				}
				?>
			</select></td>
		</tr>
		<tr>
			<td colspan="2"><br></td>
		</tr>
		<tr>
			<td>
				Profile Type
				<br><span style="color: grey; font-size: 0.7em;">Affects License Key creation restrictions</span>
			</td>
			<td>
				<select name="profile_type_id" style="width: 250px;">
					<?php
					// List all the Salespeople Profile Types.
					$profileTypes = DBGetRows('crm.salespeople_profile_type');
					foreach($profileTypes as $profileType){
						echo "<option value=\"".$profileType['id']."\"".( $aRep['profile_type_id'] == $profileType['id'] ? ' selected' : '' ).">".$profileType['name']."</option>";
					}
					?>
				</select>
			</td>
		</tr>
		<tr>
			<td><br><br><b>CRM Details - Sales People Only</b><br><br></td>
		</tr>
		<tr>
			<td width="20%">Product Category</td>
			<td width="80%">
				<select name="product" style="width: 250px;">
					<option value="0">N/A</option>
					<option value="1"<?= ( $aRep['product'] == 1 ? ' selected' : '' ) ?>>VI</option>
					<option value="2"<?= ( $aRep['product'] == 2 ? ' selected' : '' ) ?>>CSI</option>
					<option value="3"<?= ( $aRep['product'] == 3 ? ' selected' : '' ) ?>>Partner</option>
				</select>
			</td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">CRM - Start Month</td>
			<td width="80%"><input type="text" name="forecast_start_month" style="width: 250px;" value="<?= htmlspecialchars($aRep['forecast_start_month']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">CRM - Lost Segment ID</td>
			<td width="80%"><input type="text" name="lost_segment_id" style="width: 250px;" value="<?= htmlspecialchars($aRep['lost_segment_id']) ?>"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="20%">CRM - Canvas Segment ID</td>
			<td width="80%"><input type="text" name="canvas_segment_id" style="width: 250px;" value="<?= htmlspecialchars($aRep['canvas_segment_id']) ?>"></td>
		</tr>
		<tr>
			<td><br><br></td>
		</tr>
		<tr>
			<td width="20%">Internal Account Executive</td>
			<td width="80%">
				<select name="iae_id" style="width: 250px;">
					<option value="0"<?= ( $aRep['iae_id'] == 0 ? ' selected' : '' ) ?>>N/A</option>
					<?php
					$rQuery = mysql_query("SELECT person_id, name FROM salespeople WHERE person_level=1 ORDER BY name ASC");
					while($aIAE = mysql_fetch_assoc($rQuery)){
						echo "<option value=\"".htmlspecialchars($aIAE['person_id'])."\"".($aRep['iae_id'] == $aIAE['person_id'] ? ' selected' : '')." />".htmlspecialchars($aIAE['name']);
					}
					?>
				</select>
			</td>
		</tr>
		<tr>
			<td><br><br></td>
		</tr>
		<tr>
			<td width="20%">Is IAE</td>
			<td width="80%">
				<input type="radio" name="person_level" value="1"<?= ( $aRep['person_level'] == 1 ? ' checked' : '' ) ?>> Yes <input type="radio" name="person_level" value="3" <?= ( ($aRep['person_level'] != 1) ? ' checked' : '' ) ?>> No
			</td>
		</tr>
		<tr>
			<td><br><br></td>
		</tr>
		<tr>
			<td></td>
			<td><input type="submit" value="Save Data" style="width: 250px;"></td>
		</tr>
	</table>

	</form>

	<?php
}
?>


