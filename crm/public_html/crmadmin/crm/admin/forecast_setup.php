<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Only administration is allow to update the targets
$bUpdate = false;
if ( ($persondata[0] == 82 || $persondata[0] == 125 || $persondata[0] == 149 || $persondata[0] == 1 || $persondata[0] == 106 || $persondata[0] == 155 || $persondata[0] == 158 || $persondata[0] == 164) && $_GET['from'] == '2007-01' ) {
	$bUpdate = true;
}

// Input
{
	// Team selected
	if ( $_GET['team'] ) {
		switch ( $_GET['team'] ) {
			case 1:
				$sTeam = 'TLA Team';
				$iDepartment = 1;
				break;
			case 2:
				$sTeam = 'CSI Team';
				$iDepartment = 2;
				break;
			case 3:
				$sTeam = 'SA Team';
				$iDepartment = 5;
				break;
			case 4:
				$sTeam = 'CRC Team';
				$iDepartment = 7;
				break;
			case 5:
				$sTeam = 'Everybody';
				$iDepartment = null;
				break;
		}

		// Generate list of person_id's
		$rep_res = mysql_query("SELECT * FROM crm.salespeople " . ( $iDepartment > 0 ? " WHERE department = '" . $iDepartment . "'" : '' ) . " ORDER BY name");
		while ( $rep_row = mysql_fetch_array($rep_res) ) {
			$sSalesReps .= $rep_row['person_id'] . ',';
			$sSalesRepsNames .= '<font color="' . ( $rep_row['display'] == 1 ? 'green' : 'red' ) . '">' . htmlspecialchars($rep_row['name']) . '</font>, ';
		}
		$sSalesReps = trim($sSalesReps, ',');
		$sSalesRepsNames = trim($sSalesRepsNames, ', ');
		$sTeam .= ' (' . $sSalesRepsNames . ')';
		$_GET['export'] = 1;
	} else {
	        // Sanitize $_GET['person_id']
        	$sSalesReps = (int) $_GET['person_id'];
	}
}

// Get Data
if ( $_GET['person_id'] ) {
        // Select person data
        $res = mysql_query("SELECT * FROM crm.salespeople WHERE person_id = '" . intval($_GET['person_id']) . "' LIMIT 1");
        $row = mysql_fetch_array($res);
}
$row['forecast_start_month'] = '2007-01';
$row['forecast_months_start'] = 1;

// Overwrite 'forecast_months'
{
        // Start
        list($iStartYear, $iStartMonth) = split('-', $row['forecast_start_month']);
        $iStartMonths = (($iStartYear*12) + $iStartMonth) - 1;

        // Months left of "start" year
        $iMonthsLeft = $iStartMonths % 12;

        // How long should it go forth
        $iYears = (int) date('Y') - $iStartYear + 2;

        // Set it up
        $row['forecast_months'] = ($iYears * 12) - $iMonthsLeft;
}

// Got user input?
if ( $_GET['from'] ) {
	list($iInputStartYear, $iInputStartMonth) = split('-', $_GET['from']);
	$iInputStartMonths = (($iInputStartYear*12) + $iInputStartMonth) - 1;
	$row['forecast_months_start'] = ($iInputStartMonths - $iStartMonths) + 1;
	$row['forecast_months'] = $row['forecast_months_start'] + $_GET['to'] - 1;
}

// Store data?
if ( $_GET['forecast_setup'] ) {
	// Update base data
	mysql_query("UPDATE crm.salespeople set forecast_months = '" . $row['forecast_months'] . "' WHERE person_id = '" . $_GET['person_id'] . "' LIMIT 1");

	// Delete all month values
	mysql_query("DELETE FROM crm.forecast WHERE person_id = '" . $_GET['person_id'] . "'");

	// Loop over each month value and insert it
	for ( $i = 1; $i<=$row['forecast_months'] ; $i++ ) {
		mysql_query("INSERT INTO crm.forecast (month, revenue, person_id, revenue_primo) VALUES('" . $i . "', '" . preg_replace('[^0-9]*', '', $_GET['month_' . $i]) . "', '" . $_GET['person_id'] . "', '" . preg_replace('[^0-9]*', '', $_GET['pmonth_' . $i]) . "')");
	}

	// Redirect to page - avoid link and refresh updates
	header("Location: forecast_setup.php?person_id=" . $_GET['person_id'] . "&status=1");
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration', HTTP_PATH.'admin/index.php');

$iColspan = 14;
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="<?= $iColspan ?>" width="100%">
			Targets Setup - Administrative
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<form method="GET" action="forecast_setup.php">
	<tr>
		<td colspan="<?= $iColspan ?>">
			<b>Select sales person</b><br>
			<?php
			// Select all salespeople
			echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
			<br><br>
			<b>Or, view data for a whole team:</b><br>
			<label><b><input type="radio" name="team" <?= ($_GET[team] == 1 ? 'checked' : '') ?> value="1" style="width: 15px;"> TLA Team</b></label><br>
			<label><b><input type="radio" name="team" <?= ($_GET[team] == 2 ? 'checked' : '') ?> value="2" style="width: 15px;"> CSI Team</b></label><br>
			<label><b><input type="radio" name="team" <?= ($_GET[team] == 3 ? 'checked' : '') ?> value="3" style="width: 15px;"> SA Team</b></label><br>
			<label><b><input type="radio" name="team" <?= ($_GET[team] == 4 ? 'checked' : '') ?> value="4" style="width: 15px;"> CRC Team</b></label><br>
			<label><b><input type="radio" name="team" <?= ($_GET[team] == 5 ? 'checked' : '') ?> value="5" style="width: 15px;"> Everybody</b></label><br>
			<br>
			<b>Specify date range: YYYY-MM - X Months</b><br>
			<input type="text" name="from" value="<?= htmlspecialchars( $_GET['from'] ? $_GET['from'] : $row['forecast_start_month'] ) ?>" maxlength="7" style="width: 100px;"> - <input type="text" maxlength="7" name="to" value="<?= htmlspecialchars( $_GET['to'] ? $_GET['to'] : $row['forecast_months'] ) ?>" style="width: 100px;"><br>
			<br>
			<label><input type="checkbox" name="output" value="1"<?= $_GET['output'] ? ' checked' : '' ?>> <b>Output customer names and links for expires sales?</b></label>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="<?= $iColspan ?>"><input type="submit" value="Display" class="submit" style="width: 10%;"></td>
	</tr>
	</form>

	<?php
	// Output page delimeter
	echo returnPageDelimeter($iColspan);

	// Only include stuff below here if 'person_id' is available 
	if ( $_GET['person_id'] || $_GET['team'] ) {
	?>

	<form method="GET" action="forecast_setup.php">
	<input type="hidden" name="person_id" value="<?= intval($_GET['person_id']) ?>">
	<input type="hidden" name="forecast_setup" value="1">
	<tr>
		<td colspan="<?= $iColspan ?>">
			<b><?= ( !strstr($sSalesReps, ',') ? $row['name'] : $sTeam ) . ( $_GET['status'] ? ' - <font color="red">Data saved successfully!</font>' : '' ) ?></b>
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<tr>
		<td>
			<b>Base Settings:</b>
		</td>
	</tr>
	<tr>
		<td>
			Employee Start (YYYY-MM):
		</td>
		<td>
			<?= $row['forecast_start_month'] ?>
		</td>
	</tr>
	<tr>
		<td>
			Displaying
		</td>
		<td colspan="2">
			Historic data, current year, and one year ahead
		</td>
	</tr>
<?php if ( !$_GET['export'] ) { ?>
        <tr>
                <td>
                        Optimize output
                </td>
                <td colspan="2">
                        <a href="forecast_setup.php?person_id=<?= $sSalesReps ?>&export=1">OpenOffice</a>
                </td>
        </tr>
<?php } ?>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<tr>
		<td width="*">
			<b>Period</b>
		</td>
		<td width="*" style="text-align: right;">
			<b>Budget current: &euro;</b>
		</td>
		<td width="*" style="text-align: right;">
			<b>Budget primo: &euro;</b>
		</td>
		<td width="*" style="text-align: right;">
			<b>Realised: &euro;</b>
		</td>
                <td width="*" style="text-align: right;">
                        <b>Recurrence: &euro;</b>
                </td>
		<td width="*" style="text-align: right;">
                        <b>Recurrence: #</b>
                </td>
                <td width="*" style="text-align: right;">
                        <b>New Bizz: &euro;</b>
                </td>
                <td width="*" style="text-align: right;">
                        <b>New Bizz: #</b>
                </td>
		<td width="*" style="text-align: right;">
			<b>Forecast: &euro;</b>
		</td>
		<td width="*" style="text-align: right;">
			<b>Performance: %</b>
		</td>
                <td width="*" style="text-align: right;">
                        <b>Performance: &euro;</b>
                </td>
                <td width="*" style="text-align: right;">
                        <b>Lost/Expires: &euro;</b>
                </td>
                <td width="*" style="text-align: right;">
                        <b>Lost/Expires: #</b>
                </td>
		<td width="*" style="text-align: right;">
			<b>Potential: &euro;</b>
		</td>
		<td width="*" style="text-align: right;">
                        <b>Potential: #</b>
                </td>
	</tr>
	<?php
	// Loop over months as set in crm.salespeople
	$sLinksExpired = '';
	for ( $i = $row['forecast_months_start'] ; $i<=$row['forecast_months'] ; $i++ ) {
		// Retrieve values set
		$s_res = mysql_query("SELECT SUM(revenue) as revenue , SUM(revenue_primo) as revenue_primo FROM crm.forecast WHERE person_id IN(" . $sSalesReps . ") AND month = '" . $i . "' LIMIT 1");
		$s_row = mysql_fetch_array($s_res);

		if ( $s_row['revenue'] < 150 ) {
			// ISA budget, set to 0
			$s_row['revenue'] = 0;
			$s_row['revenue_primo'] = 0;
		}

		// Calculate "realised" if $row['forecast_start_month'] is set
		$iRealised = 0;
		$iRecurrence = 0;
		$iCanvas = 0;
		$aCanvasCustomers = null;
		$iCanvasCustomers = 0;
		$aRecurrenceCustomers = null;
		$iRecurrenceCustomers = 0;
		if ( $row['forecast_start_month'] ) {
			// Start year / month
			list($year, $month) = split('-', $row['forecast_start_month']);

			// Build date
			$date = date('Y-m', mktime(0, 0, 0, ($month+$i-1), 1, $year));

			// Select all sales and convert them to EURO
			$res = mysql_query("select * from crm.saleslog where person_id IN(" . $sSalesReps .") && sold_date like '" . $date . "%' && ((status != 3 && status != 6 && status != 7) || status is null) && (product_price-discount) > 0");
			while ( $sa_row = mysql_fetch_array($res) ) {
				$iEuro = ( (float) ( $sa_row['product_price'] - $sa_row['discount'] ) ) * fFOREX( $sa_row['currency_exchange_rate'], $sa_row['currency_exchange_rate_euro'] );
				$iRealised += $iEuro;

				// Canvas or recurrence
				$aCompanyDetails = fGetCustomerDetails( $sa_row['cst_id'] );

				// Test if canvas - if not it is recurrence
				if ( fIsCanvas( $sa_row['sold_date'], $aCompanyDetails['AllIDs'], $sa_row['product_category'] ) ) {
					$aCanvasCustomers[ $aCompanyDetails['Company']['cst_id'] ] = true;
					++$iCanvasCustomers;
					$iCanvas += $iEuro;
				} else {
					$aRecurrenceCustomers[ $aCompanyDetails['Company']['cst_id'] ] = true;
					++$iRecurrenceCustomers;
					$iRecurrence += $iEuro;
				}
			}
		}

		// Select all sales expiring this month and test for a newer sale of the same type on the customer that holds the sale
		$iExpires = 0;
		$aExpiresCustomers = null;
		$iExpiresCustomers = 0;
		$iPotential = 0;
		$aPotentialCustomers = null;
		$iPotentialCustomers = 0;
		$aSales = DBGetRows('crm.saleslog', "expires_date like '" . $date . "%' && person_id in (" . $sSalesReps .") && ((status != 3 && status != 6 && status != 7) || status is null) && (product_price-discount) > 0");

		$aExpiredIncluded = array();

		while ( list($iKey, $aSale) = each($aSales) ) {
			// Get details (primarily all CST IDs)
			$aCompanyDetails = fGetCustomerDetails( $aSale['cst_id'] );

			// Potential: Sales that expire this month (not taking into account if a newer sale has been made or not)
			$iPotential += ( (float) ( $aSale['product_price'] - $aSale['discount'] ) ) * fFOREX( $aSale['currency_exchange_rate'], $aSale['currency_exchange_rate_euro'] );
			$aPotentialCustomers[$aCompanyDetails['Company']['cst_id']] = true;
			++$iPotentialCustomers;

			// Test if there is another sale that expires AFTER the one we are looking at AND that it is of the SAME product category
			if ( DBNumRows('crm.saleslog', "cst_id in(" . $aCompanyDetails['AllIDs'] . ") && ((status != 3 && status != 6 && status != 7) || status is null) && (product_price-discount) > 0 && expires_date > '" . $aSale['expires_date'] . "'") == 0 && !$aExpiredIncluded[$aCompanyDetails['Company']['cst_id']] ) {
				// No sales - "lost" customer
				$iExpires += ( (float) ( $aSale['product_price'] - $aSale['discount'] ) ) * fFOREX( $aSale['currency_exchange_rate'], $aSale['currency_exchange_rate_euro'] );
				$aExpiresCustomers[$aCompanyDetails['Company']['cst_id']] = true;
				++$iExpiresCustomers;

				// Output details?
				if ( $_GET['output'] ) {
					$expiry = DBGetRowValue('crm.saleslog', 'max(expires_date)', "cst_id in(" . $aCompanyDetails['AllIDs'] . ") && ((status != 3 && status != 6 && status != 7) || status is null) && (product_price-discount) > 0");
					$start = DBGetRowValue('crm.saleslog', 'min(sold_date)', "cst_id in(" . $aCompanyDetails['AllIDs'] . ") && ((status != 3 && status != 6 && status != 7) || status is null) && (product_price-discount) > 0");
					$sLinksExpired .= '<tr><td>' . $aCompanyDetails['Company']['cst_id'] . '</td><td><a href="'.HTTP_PATH_CRM2.'?page=customer&amp;cst_id=' . $aCompanyDetails['Company']['cst_id'] . '">' . htmlspecialchars($aCompanyDetails['Company']['name']) . "</a></td><td>" . htmlspecialchars($aSale['product_name']) . "</td><td>" . substr($start, 0,4) . "</td><td>" . substr($expiry,0,4 ) . "</td><td>" . $start . "</td><td>" . $expiry . "</td><td>&euro; " . number_format(( (float) ( $aSale['product_price'] - $aSale['discount'] ) ) * fFOREX( $aSale['currency_exchange_rate'], $aSale['currency_exchange_rate_euro'] )) . "</td></tr>";
				}

				$aExpiredIncluded[$aCompanyDetails['Company']['cst_id']] = true;
			}
		}

		// Calculate forecast
		$fc_res = mysql_query("SELECT COUNT(*) AS customers, SUM(cst.forecast_amount * cst.forecast_expectancy / 100) AS forecast FROM crm.cst WHERE cst.person_id IN(" . $sSalesReps . ") AND cst.forecast_date LIKE '" . $date . "%';");
		$fc_row = mysql_fetch_array($fc_res);
		$iForecast = $fc_row['forecast'];
		$iForecastCustomers = $fc_row['customers'];

		// Get Current Month + Year
		list($year, $month) = split('-', $date);

		// Determine background color
		if ( $year == date('Y') && $month == date('m') ) {
			$sColor = '#C2EABD';
			$sDescription = 'Current';
		} elseif ( $year < date('Y') || ($year == date('Y') && $month < date('m') ) ) {
			$sColor = '#DEDEDE';
			$sDescription = 'Historic';
		} else {
			$sColor = '#FFFFFF';
			$sDescription = 'Future';
		}

		// Year totals
		$aYear[$year]['target'] += $s_row['revenue'];
		$aYear[$year]['primo'] += $s_row['revenue_primo'];
		$aYear[$year]['forecast'] += $iForecast;
		$aYear[$year]['forecast_customers'] += $iForecastCustomers;
		$aYear[$year]['realised'] += $iRealised;
		$aYear[$year]['canvas'] += $iCanvas;
		$aYear[$year]['recurrence'] += $iRecurrence;
		$aYear[$year]['recurrence_customers'] += $iRecurrenceCustomers;
		$aYear[$year]['canvas_customers_unique'] = array_merge((array)$aYear[$year]['canvas_customers_unique'], (array)$aCanvasCustomers);
		$aYear[$year]['canvas_customers'] += $iCanvasCustomers;
		$aYear[$year]['recurrence_customers_unique'] = array_merge((array)$aYear[$year]['recurrence_customers_unique'], (array)$aRecurrenceCustomers);
		$aYear[$year]['expires'] += $iExpires;
		$aYear[$year]['expires_customers_unique'] = array_merge((array)$aYear[$year]['expires_customers_unique'], (array)$aExpiresCustomers);
		$aYear[$year]['expires_customers'] += $iExpiresCustomers;
		$aYear[$year]['potential'] += $iPotential;
		$aYear[$year]['potential_customers_unique'] = array_merge((array)$aYear[$year]['potential_customers_unique'],(array)$aPotentialCustomers);
		$aYear[$year]['potential_customers'] += $iPotentialCustomers;

		// Output "Year" delimeter
		if ( $sLastYear != $year ) {
			// Total row
			if ( $sLastYear ) {
				fFooterOut( $sLastYear, $aYear );
			}

			// Year headline
			echo '<tr><td><br><b>' . $year . '</b></td></tr>';
		}
		$sLastYear = $year;

		// Difference
		$iDifference = $iRealised - $s_row['revenue'];
	
		// Output row
		if ($bUpdate){
			//Editable
			$sPrimo = '<td><input type="text" style="width: 100%; text-align: right;" tabindex="' . (3+$i) . '" id="pmonth_' . $i . '" name="pmonth_' . $i . '" value="' . number_format($s_row['revenue_primo']) . '"></td>';
		} else {
			$sPrimo = '<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($s_row['revenue_primo']) . '</td>';
		}

		echo '
	<tr style="background: ' . $sColor . ';">
		<td style="padding-left: 10px; border-bottom: 1px solid #ffffff;">' . $date . ' (' . $sDescription . ')</td>
		' . ( $bUpdate ? '<td><input type="text" style="width: 100%; text-align: right;" tabindex="' . (2+$i) . '" id="month_' . $i . '" name="month_' . $i . '" value="' . number_format($s_row['revenue']) . '" onchange="fTransferBudget(' . $i . ')">' : '<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($s_row['revenue']) ) . '</td>
		' . $sPrimo .'
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iRealised) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iRecurrence) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">(' . number_format(count($aRecurrenceCustomers)) . ') ' . number_format($iRecurrenceCustomers) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iCanvas) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">(' . number_format(count($aCanvasCustomers)) . ') ' . number_format($iCanvasCustomers) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iForecast) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . round($iRealised / $s_row['revenue'] * 100) . '%</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right; color: ' . ( $iDifference < 0 ? 'red' : 'black' ) . ';">' . number_format($iDifference) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iExpires) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">(' . number_format(count($aExpiresCustomers)) . ') ' . number_format($iExpiresCustomers) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">' . number_format($iPotential) . '</td>
		<td style="border-bottom: 1px solid #ffffff; text-align: right;">(' . number_format(count($aPotentialCustomers)) . ') ' . number_format($iPotentialCustomers) . '</td>
	</tr>';
	}

	fFooterOut( $sLastYear, $aYear );
	?>
	<tr>
		<td>
			<br>
		</td>
	</tr>
<?php
if ( $_GET['output'] ) {
	echo returnPageDelimeter($iColspan);
	echo '<tr><td colspan="' . $iColspan . '"><b>Expired Customers/Sales:</b><br><br><table width="100%">' . $sLinksExpired . '</table></td></tr>';
}
?>

<?php if ( $bUpdate ) { ?>
	<tr>
		<td></td>
		<td>
			<input type="submit" value="Update Targets" class="submit">
		</td>
	</tr>
<?php } ?>
	</form>

	<?php
	}
	?>

	<tr>
		<td colspan="6">
			<br>
		</td>
	</tr>
</table>

<?php
// Footer output function
function fFooterOut( $sLastYear, $aYear ) {
	$iDifference = $aYear[$sLastYear]['realised'] - $aYear[$sLastYear]['target'];
	
	echo '
	<tr style="background: black;">
		<td style="padding-left: 10px; color: #FFFFFF;"><b>Total for ' . $sLastYear . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['target']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['primo']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['realised']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['recurrence']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>(' . number_format(count($aYear[$sLastYear]['recurrence_customers_unique'])) . ') ' . number_format($aYear[$sLastYear]['recurrence_customers']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['canvas']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>(' . number_format(count($aYear[$sLastYear]['canvas_customers_unique'])) . ') ' . number_format($aYear[$sLastYear]['canvas_customers']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['forecast']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . round($aYear[$sLastYear]['realised'] / $aYear[$sLastYear]['target'] * 100) . '%</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right; color: ' . ( $iDifference < 0 ? 'red' : 'black' ) . ';"><b>' . number_format($iDifference) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['expires']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>(' . number_format(count($aYear[$sLastYear]['expires_customers_unique'])) . ') ' . number_format($aYear[$sLastYear]['expires_customers']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>' . number_format($aYear[$sLastYear]['potential']) . '</b></td>
		<td style="padding-left: 10px; color: #FFFFFF; text-align: right;"><b>(' . number_format(count($aYear[$sLastYear]['potential_customers_unique'])) . ') ' . number_format($aYear[$sLastYear]['potential_customers']) . '</b></td>
	</tr>';

}

// Output footer
echo HTMLFooter();
?>
