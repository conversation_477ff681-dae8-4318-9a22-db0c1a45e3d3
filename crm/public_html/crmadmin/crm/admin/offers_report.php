<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Date stamps
$month_from = date('Y-m-1');
$month_to = date('Y-m-31');
$q_from = ( ( date('m') / 3 ) >= 1 ? date('Y-' . ( (floor(date('m') / 3) * 3) + 1 ) . '-1') : date('Y-1-1') );
$q_to = date('Y-' . (ceil(date('m') / 3) * 3) . '-31');

// Variables
$people = '';
$expires = '';
$person_offers = '';
$result = '';

// Build result
if ( $_GET['submitted'] )
{
	// Period?
	switch ( $_GET['period'] )
	{
		// This Month
		case 1:
			$expires = " where expires >= '" . $month_from . "' && expires <= '" . $month_to . "'";
			$expires_real = " where sold_date >= '" . $month_from . "' && sold_date <= '" . $month_to . "'";
			break;

		// This Q
		case 2:
			$expires = " where expires >= '" . $q_from . "' && expires <= '" . $q_to . "'";
			$expires_real = " where sold_date >= '" . $q_from . "' && sold_date <= '" . $q_to . "'";
			break;

		// User input
		case 3:
			$expires = " where expires >= '" . $_GET['from'] . "' && expires <= '" . $_GET['to'] . "'";
			$expires_real = " where sold_date >= '" . $_GET['from'] . "' && sold_date <= '" . $_GET['to'] . "'";
			break;
	}

	// Sales People
	{
		if ( !$_GET['sp_all'] )
		{
			// Limit on salespeole
			while ( list($key, $person_id) = @each($_GET['salespeople']) )
			{
				$people .= " person_id = '" . $person_id . "' || ";
			}
		}
	}

	// Select all offers
	$res = mysql_query("select * from offers" . $expires . ( !$expires ? ' where ' : ( $people ? ' && (' . substr($people, 0, -4) . ')' : '' )) . $sSQLAccessLimit . ' order by expires');
	while ( $row = mysql_fetch_array($res) )
	{
		// Select company details
		$c_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
		$c_row = mysql_fetch_array($c_res);

		$person_offers[$row['person_id']][$row['cst_id']]['name'] = $c_row['name'];
		$person_offers[$row['person_id']][$row['cst_id']]['product'] = $row['product'];
		$person_offers[$row['person_id']][$row['cst_id']]['expires'] = $row['expires'];
		$person_offers[$row['person_id']][$row['cst_id']]['person_id'] = $row['person_id'];
		$person_offers[$row['person_id']][$row['cst_id']]['count']++;
		$person_offers[$row['person_id']][$row['cst_id']]['amount'] += $row['amount'] * fFOREX( DBGetRowValue("crm.country_currency", "currency", "currency_name = '".mysql_real_escape_string( $row['currency'] )."'"), DBGetRowValue("crm.country_currency", "currency", "currency_name = 'EURO' OR currency_name = 'EUR'") );
	}

	// Loop through each person
	while ( list($person_id, $offers) = @each($person_offers) )
	{
		// Output Person name
		$result .= '
			<tr>
				<td><b>' . ReturnPersonNameFromID($person_id) . '</b></td>
			</tr>';

		// Reset value
		$sp_total = 0;
		$customer_count = 0;

		// Generate output list of offers
		while ( list($cst_id, $data) = @each($offers) )
		{
			// Reset vars
			$realised = NULL;
			$already_paid = NULL;

			// Select highest paid sale on this customer (if any)
			$s_res = mysql_query("select max((product_price-discount)) as max_amount, product_name from saleslog where cst_id = '" . $cst_id . "' && status = 2 group by cst_id");
			$s_row = mysql_fetch_array($s_res);
			$already_paid = $s_row['max_amount'] * fFOREX( DBGetRowValue("crm.country_currency", "currency", "currency_name = '".mysql_real_escape_string( $s_row['currency'] )."'"), DBGetRowValue("crm.country_currency", "currency", "currency_name = 'EURO' OR currency_name = 'EUR'") );

			// Check for realised sales in the period
			$r_res = mysql_query("select * from saleslog " . $expires_real . " && (status in(0,2) || status is NULL) && cst_id = '" . $cst_id . "'");
			while ( $r_row = mysql_fetch_array($r_res) )
			{
				$realised += ( (float) ( $r_row['product_price'] - $r_row['discount'] ) ) * fFOREX( $r_row['currency_exchange_rate'], $r_row['currency_exchange_rate_euro'] ) ;
				$realised_total += ( (float) ( $r_row['product_price'] - $r_row['discount'] ) ) * fFOREX( $r_row['currency_exchange_rate'], $r_row['currency_exchange_rate_euro'] ) ;
			}

			// HTML Output
			$result .= '
			<tr>
				<td style="padding-left: 10px;"><a href="../sales/customer.php?cst_id=' . $cst_id . '" target="_blank">' . htmlspecialchars($data['name']) . '</a></td>
				<td>' . 'N/A' . '</td>
				<td>' . $data['product'] . ( $data['count'] > 1 ? ' (' . $data['count'] . ' Offers)' : '' ) . '</td>
				<td align="right" style="padding-right: 20px;">' . number_format(round($data['amount']/$data['count'])) . ',-</td>
				<td>' . $data['expires'] . '</td>
				<td align="right" style="padding-right: 70px;">' . number_format($realised) . ',-</td>
			</tr>';

			$sp_total += round( $data['amount'] / $data['count'] );
			$customer_count++;
		}

		// Output Total
		if ( $sp_total > 0 || $customer_count > 0 )
		{
			$result .= '
			<tr>
				<td style="padding-left: 10px;"><b>Number of customers: ' . $customer_count . '</b></td>
				<td colspan="3" align="right" style="padding-right: 20px;"><b>' . number_format($sp_total) . ',-</b></td>
			</tr>' . returnPageDelimeter(6, '100%', 1);
		}
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');
?>

<br>
<form method="GET" action="offers_report.php">
<input type="hidden" name="submitted" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="6"><b>Offers Report</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="1" valign="top">
			<b>Select Sales Person(s):</b><br>
			<?php
			echo returnSelectBox($_GET['salespeople'], 'salespeople[]', true, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
		</td>
		<td colspan="5" valign="top">
			<b>Select Period</b><br>
			<label><input type="radio" name="period" value="1" style="width: 15px;" <?=( $_GET['period'] == 1 ? 'checked' : '' )?>> This Month (<?= $month_from ?> - <?= $month_to ?>)</label><br>
			<label><input type="radio" name="period" value="2" style="width: 15px;" <?=( $_GET['period'] == 2 ? 'checked' : '' )?>> This Quarter (<?= $q_from ?> - <?= $q_to ?>)</label><br>
			<label><input type="radio" name="period" value="3" style="width: 15px;" <?=( $_GET['period'] == 3 ? 'checked' : '' )?>> Other Period: <input type="text" name="from" style="width: 100px;" value="<?= ( $_GET['from'] ? htmlspecialchars($_GET['from']) : date('Y-m-d') ) ?>"> - <input type="text" name="to" style="width: 100px;" value="<?= ( $_GET['to'] ? htmlspecialchars($_GET['to']) : date('Y-m-d') ) ?>"></label>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="6">
			<input type="submit" value="Display Report" style="width: 25%" class="submit">
		</td>
	</tr>

	<?= returnPageDelimeter(6); ?>

	<tr>
		<td width="35%"><b>Company Name</b></td>
		<td width="15%"><b>Marked</b></td>
		<td width="15%"><b>Solution</b></td>
		<td width="10%"><b>Amount</b></td>
		<td width="10%"><b>Expires</b></td>
		<td width="15%"><b>Realised</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>

	<?=$result?>

</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
