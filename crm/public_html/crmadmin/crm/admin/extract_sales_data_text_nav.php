<?php
// Language
$iLangID = intval($_GET['lang_id']);
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Require field template
require(INCLUDE_PATH_LIB."customer_temp_" . $iLangID . ".php");

// Set header to display plain text
header("Content-type: text/plain");

// Input data
$sFrom = $_GET['from'];
$sTo = $_GET['to'];

// Define fields (Headers)
//echo "filename,company_name,contact,address_1,address_2,state,town,zipcode,today_date,starts_date,expires_date,days_to_pay,product_price,product_vat,product_total,sales_person,sales_person_title,contact_email,one_time_password,product,po_number,country,currency,\n";

// Select data
$rSales = mysql_query("select * from saleslog where (status is null || status_date like '2009-01%') && (product_price - discount) > 0 " . ( $_GET['person_id'] ? " && person_id = '" . $_GET['person_id'] . "'" : '' ) . "");

function fReverseDate($sDate){
	list($iYear, $iMonth, $iDay) = explode("-", $sDate);
	return $iDay."-".$iMonth."-".$iYear;
	
}

// Loop through result
while ( $aSales = mysql_fetch_array($rSales) )
{
	// Select customer details
	$c_res = mysql_query("select * from cst where cst_id = '" . $aSales['cst_id'] . "' limit 1");
	$c_row = mysql_fetch_array($c_res);
//	print_r($c_row);
//	continue;

	if ( $c_row['master_id'] )
	{
		$iCaseID = $c_row['cst_id'];
		$iPO = $c_row['po_number'];
		$c_res = mysql_query("select * from cst where cst_id = '" . $c_row['master_id'] . "' limit 1");
		$c_row = mysql_fetch_array($c_res);
	}

	// Select invoice contact
	$ic_res = mysql_query("SELECT * FROM crm.contacts WHERE contacts.cst_id = '" . $iCaseID . "' AND contacts.invoice = 1 LIMIT 1");
	$ic_row = mysql_fetch_array($ic_res);

	// No invoice contact ?
	if ( !$ic_row['name'] )
	{
		$ic_res = mysql_query("SELECT * FROM crm.contacts WHERE contacts.cst_id = '" . $iCaseID . "' AND contacts.primary_contact = 1 LIMIT 1");
		$ic_row = mysql_fetch_array($ic_res);
	}

	// Select data on sales person
	$s_res = mysql_query("select * from salespeople where person_id = '" . $aSales['person_id'] . "' limit 1");
	$s_row = mysql_fetch_array($s_res);

	// Select one time password
	$o_res = mysql_query("select * from one_time_passwords where cst_id = '" . $aSales['cst_id'] . "' limit 1");
	$o_row = mysql_fetch_array($o_res);

	// Select country name
	$country_res = mysql_query("select * from countries where id = '" . $c_row['invoice_country'] . "' limit 1");
	$country_row = mysql_fetch_array($country_res);

	// Product
	switch ( $aSales['product_type'] )
	{
		case 1:
			$sProduct = 'Vulnerability Tracking Service';
			break;
		case 2:
			$sProduct = 'Security Manager';
			break;
		case 3:
			$sProduct = 'Enterprise Security Manager';
			break;
		case 4:
			$sProduct = 'Vulnerability Tracking Service - Enterprise Edition';
			break;
		case 5:
			$sProduct = 'Surveillance Scanner';
			break;
		case 6:
			$sProduct = 'Security Manager with Network Software Detector';
			break;
		case 7:
			$sProduct = 'Enterprise Security Manager with Network Software Detector';
			break;
		case 9:
			$sProduct = $aSales['product_name'];
			break;
		default:
			$sProduct = '';
	}

	// Calculate VAT
	$iVat = 0;
	if ( $aSales['lang_id'] == 2 ) 
	{
		$iVat = ($aSales['product_price'] - $aSales['discount']) * 0.25;
	}

	// output
	if ( ($aSales['product_price'] - $aSales['discount']) > 0 )
	{
		//echo '"' . $aSales['sold_date'] . "_" . $aSales['sale_id'] . "_" . $c_row['cst_id'] . "\",\"" . $c_row['name'] . "\",\"" . $ic_row['name'] . "\",\"" . $c_row[$customer_data['Address 1']] . "\",\"" . $c_row[$customer_data['Address 2']] . "\",\"" . $c_row[$customer_data['State']] . "\",\"" . $c_row[$customer_data['Town']] . "\",\"" . $c_row[$customer_data['Zipcode']] . "\",\"" . ConvertGetDate('', 'MySQL-Date') . "\",\"" . ( $aSales['invoice_start_date'] ? $aSales['invoice_start_date'] : $aSales['sold_date'] ) . "\",\"" . $aSales['expires_date'] . "\",\"" . $aSales['payment_time'] . "\",\"" . number_format($aSales['product_price'] - $aSales['discount']) . "\",\"" . $iVat . "\",\"" . number_format(($aSales['product_price'] - $aSales['discount']) + $iVat) . "\",\"" . $s_row['name'] . '","' . $s_row['title'] . '","' . $c_row['email'] . '","' . $o_row['password']  . "\",\"" . $sProduct . "\",\"" . $iPO . "\",\"" . $country_row['country'] . "\",\"" . $aSales['currency'] . "\",\n";
		echo '"' . $aSales['sale_id'] . '","' . $c_row['cst_id'] . '","' . $c_row['name'] . '","' . $ic_row['name'] . '","' . $c_row[$customer_data['Address 1']] . '","' . $c_row[$customer_data['Address 2']] . '","' . $c_row[$customer_data['State']] . '","' . $c_row[$customer_data['Town']] . '","' . $c_row[$customer_data['Zipcode']] . '","' . $aSales['currency']  . '","' . $aSales['product_type'] . '","1","' . ($aSales['product_price'] - $aSales['discount']) . '","' . fReverseDate(($aSales['invoice_start_date'] ? $aSales['invoice_start_date'] : $aSales['sold_date'])) . '","' . fReverseDate($aSales['expires_date']) . '","' . $s_row['name'] . '"' . "\n";
	}
}

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
	$split = explode('-', $date);
	$date = $split[2] . '-' . $split[1] . '-' . $split[0];

	// Return formatted date
	return $date;
}

?>
