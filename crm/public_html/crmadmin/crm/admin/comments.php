<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Set dates
$from = ( $_GET['from'] ? $_GET['from'] : date('Y-m-d 00:00:00') );
$to = ( $_GET['to'] ? $_GET['to'] : date('Y-m-d 23:59:59') );
$output = '';

// Select comments
if ( $_GET['person_id'] )
{
	// Generate type's to include
	$type = '';
	$type .= ( $_GET['comment'][1] ? ( $type ? ',' : '' ) . $_GET['comment'][1] : '' );
	$type .= ( $_GET['comment'][2] ? ( $type ? ',' : '' ) . $_GET['comment'][2] : '' );
	$type .= ( $_GET['comment'][3] ? ( $type ? ',' : '' ) . $_GET['comment'][3] : '' );
	$type .= ( $_GET['comment'][4] ? ( $type ? ',' : '' ) . $_GET['comment'][4] : '' );

	// Generate list of comments
	$res = mysql_query("select comments.cst_id, comments.added, comments.comment, cst.name, comments.type from comments, cst where comments.person_id = '" . $_GET['person_id'] . "' && type in (" . $type . ") && added >= '" . $from . "' && added <= '" . $to . "' && comments.cst_id = cst.cst_id order by added desc");
	while ( $row = mysql_fetch_array($res) )
	{
		$output .= '
<tr>
	<td valign="top"><a href="'.HTTP_PATH_CRM2.'customer.php?cst_id=' . $row['cst_id'] . '" target="new">' . htmlspecialchars($row['name']) . '</a>
	</td>
	<td valign="top" colspan="2">' . OutputComment($row['added'], $_GET['person_id'], $row['comment'], $row['type']) . '
	</td>
</tr>
<tr><td><br></td></tr>
';
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="3">
			View Comments
		</td>
	</tr>
	<tr><td><br></td></tr>
	<form method="GET" action="comments.php">
	<tr>
		<td colspan="3">
			<b>Select sales person</b><br>
			<select name="person_id">
			<?php
			// Select all salespeople
			$s_res = mysql_query("select * from salespeople order by name");
			while ( $s_row = mysql_fetch_array($s_res) )
			{
				echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
			}
			?>
			</select>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td valign="top">
			<b>From:</b><br>
			<input type="text" value="<?=htmlspecialchars($from)?>" name="from" style="width: 150px;">
		</td>
		<td valign="top">
			<b>To:</b><br>
			<input type="text" value="<?=htmlspecialchars($to)?>" name="to" style="width: 150px;">
		</td>
		<td>
			<b>Comment Types:</b><br>
			<input type="checkbox" name="comment[1]" <?=($_GET['comment'][1] ? 'checked' : '' )?> value="1" style="width: 15px;"> Research re. IT &amp; IT Organisation<br>
			<input type="checkbox" name="comment[2]" <?=($_GET['comment'][2] ? 'checked' : '' )?> value="2" style="width: 15px;"> Customer Relation<br>
			<input type="checkbox" name="comment[3]" <?=($_GET['comment'][3] ? 'checked' : '' )?> value="3" style="width: 15px;"> Set Up &amp; Solution Feedback<br>
			<input type="checkbox" name="comment[4]" <?=($_GET['comment'][4] ? 'checked' : '' )?> value="4" style="width: 15px;"> Sales; Buying Signals, Gains & Avoids<br>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2"><br></td>
		<td colspan="1" align="right"><input type="submit" value="Display"></td>
	</tr>
	</form>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="3">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="2">
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?=$output?>
	<tr>
		<td colspan="3">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="2">
		</td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();

function OutputComment($added, $person_id, $comment, $type)
{
	switch ( $type )
	{
		case 1:
			$name = 'Research re. IT & IT Organisation';
			$color = '#EAFFD0';
			break;
		case 2:
			$name = 'Customer Relation';
			$color = '#FFE7E7';
			break;
		case 3:
			$name = 'Set Up & Solution Feedback';
			$color = '#FFF8E4';
			break;
		case 4:
			$name = 'Sales; Buying Signals, Gains & Avoids';
			$color = '#DBF0FF';
			break;
		default:
			$color = '#FFFFFF';
			$name = 'N/A';
	}

	// Prepare comment for output
	$comment = htmlspecialchars($comment);
	$comment = str_replace('&lt;br&gt;', '<br>', $comment);

	$o = '<table width="100%" cellspacing="0" cellpadding="0"><tr>
			<td colspan="2" style="border: 1px solid; padding: 2px;" bgcolor="' . $color . '"><b>' . $added . ' - ' . $name . ' - ' . ReturnPersonNameFromID($person_id) . '</b></td>
		</tr>
		<tr>
			<td colspan="2" style="border-left: 1px solid; border-right: 1px solid; border-bottom: 1px solid; padding-left: 3px; padding-bottom: 3px; padding-right: 3px; padding-top: 3px; font-size: 12px;" bgcolor="' . $color . '">' . $comment . '</td>
		</tr>
		<tr>
			<td colspan="2"><br></td>
		</tr></table>';

	// Return output
	return $o;
}

?>
