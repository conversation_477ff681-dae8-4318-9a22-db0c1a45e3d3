<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// New (Updated to 1) use historic/logged currency 2) exclude 'Rejected' sales)
// Used for reports 2002-2006 for Pia, on 18th May 2010
/*
SELECT
 saleslog.cst_id,
 product_name,
 ROUND( product_price * currency_exchange_rate / currency_exchange_rate_euro ) AS amount,
 sold_date,
 expires_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''),
 REPLACE(LEFT(sold_date, 7), '-', '')) AS months
  FROM
   saleslog
  WHERE
   (status is null || (status != 3 && status != 4)) &&
   (product_price-discount) > 0 &&
   sold_date >= '2006-01-01' &&
   sold_date <= '2006-12-31'
  ORDER BY sold_date;
*/

// New (Used for all sales 2009, used on the 4th March 2010)
/*
SELECT
 saleslog.cst_id,
 product_name,
 ROUND( (product_price-discount) * country_currency.currency ) AS amount,
 sold_date,
 expires_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''),
 REPLACE(LEFT(sold_date, 7), '-', '')) AS months
  FROM
   saleslog,
   country_currency
  WHERE
   saleslog.currency = country_currency.currency_name &&
   (status is null || status != 3) &&
   product_price > 0 &&
   sold_date >= '2009-01-01' &&
   sold_date <= '2009-10-31'
  ORDER BY sold_date;
*/


// Alternativ (Successfully Used for 1. half of 2007 numbers, on the 9th of Juli 2007):
// And again 19th Feb. 2008
// And again 4th November 2008
// And again 7th January 2009
// And again 2nd November 2009 
// And again 5th January 2010
// See also: /home/<USER>/work/jb_txt/deferred_income_1_half_2007.sxc
/*
SELECT
 saleslog.cst_id,
 product_name,
 ROUND( (product_price-discount) * country_currency.currency ) AS amount,
 sold_date,
 expires_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''),
 REPLACE(LEFT(sold_date, 7), '-', '')) AS months
  FROM
   saleslog,
   country_currency
  WHERE
   saleslog.currency = country_currency.currency_name &&
   (status is null || status != 3) &&
   product_price > 0 &&
   sold_date >= '2009-01-01' &&
   sold_date <= '2009-10-31' &&
   (
     PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''), REPLACE(LEFT(sold_date, 7), '-', '')) > 15 &&
     ROUND( (product_price-discount) * country_currency.currency ) > 150000
   )
  ORDER BY sold_date;
*/

// Alternativ (Successfully used for 1st 9 months of 2007 numbers, on the 29th of October 2007)
/*
SELECT
 saleslog.cst_id,
 product_name,
 (product_price-discount) as amount_local,
 ROUND( (product_price-discount) * country_currency.currency ) AS amount_dkk,
 status,
 sold_date,
 expires_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''),
 REPLACE(LEFT(sold_date, 7), '-', '')) AS months
  FROM
   saleslog,
   country_currency
  WHERE
   saleslog.currency = country_currency.currency_name &&
   (status is null || status != 3) &&
   product_price > 0 &&
   sold_date >= '2007-01-01' &&
   sold_date < '2007-10-01' &&
   (
     PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''), REPLACE(LEFT(sold_date, 7), '-', '')) > 15 &&
     ROUND( (product_price-discount) * country_currency.currency ) > 150000
   )
  ORDER BY sold_date;
*/


echo "Months\tDKK\tStart\tEnd\tCST_ID\tProduct\tCustomer Name\n";

// Select all sales data
$res = mysql_query("select (product_price-discount) as total, currency_exchange_rate, lang_id, product_name, sold_date, expires_date, status, cst_id from saleslog where (product_price-discount) > 0 && (status != 3 || status is NULL) && (sold_date <= '2006-11-31' && sold_date >= '2006-1-01' )");
while ( $row = mysql_fetch_array($res) )
{
	// Amount in DKK
	$total = ( (float) $row['total'] * fFOREX( $row['currency_exchange_rate'], 1 );

	// Period
	{
		$s_day = 0;
		$s_month = 0;
		$s_year = 0;
		$e_day = 0;
		$e_month = 0;
		$e_year = 0;
		$s_month_plus = 0;
		$e_month_plus = 0;
		$year_months = 0;
		$months = 0;
		$month_total = 0;
		$raised = 0;
		$e_raised = 0;
		
		// Split sold date
		{
			// Day
			$s_day = substr($row['sold_date'], 8, 2);
			if ( $s_day < 15 )
			{
				$s_day = 1;
			}
			else
			{
				$s_day = 1;
				$s_month_plus = 1;
				$raised = 1;
			}

			// Month
			$s_month = substr($row['sold_date'], 5, 2) + $s_month_plus;

			// Year
			$s_year = substr($row['sold_date'], 0, 4);
		}

		// Split expires date
		{
			// Day
			$e_day = substr($row['expires_date'], 8, 2);
			if ( $e_day < 15 )
			{
				$e_day = 1;
			}
			else
			{
				$e_day = 1;
				$e_month_plus = 1;
				$e_raised = 1;
			}

			// Month
			$e_month = substr($row['expires_date'], 5, 2) + $e_month_plus;

			// Year
			$e_year = substr($row['expires_date'], 0, 4);
		}

		// Check year
		if ( $s_year != $e_year )
		{
			$year_months = ($e_year - $s_year) * 12;
			
		}

		// Check month
		if ( $s_month != $e_month )
		{
			$months = ($e_month - $s_month);
		}
		else
		{
			$months = 0;
		}

		// Total
		$month_total = $year_months + $months;
	}

	// alle salg paa over 50.000 og over 1 aar
	if ( ( ($month_total < 13 && ($month_total == 12 && $raised && !$e_raised) ) || $total < 50000 ) )
	{
		continue;
	}
	elseif ( $month_total < 12 )
	{
		continue;
	}
	
	// Total sum
	$sum += $total;

$c_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
$c_row = mysql_fetch_array($c_res);

	// Output
	echo $month_total . "\t" . $total . "\t" . $s_year . '-' . $s_month . '-' . $s_day . "\t" . $e_year . '-' . $e_month . '-' . $e_day . "\t" . $row['cst_id'] . "\t" . $row['product_name'] . "\t" . $c_row['name'] . "\n";
}

echo "\n\nTOTAL: " . number_format($sum) . "\n";

echo "\n\nREGLER:\n - Alle salg p&aring; over 50.000 og over 1 &aring;r.\n\n"

?>
