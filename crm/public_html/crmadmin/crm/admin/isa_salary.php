<?php
/**
Since this file is somewhat special it will include logic description where applicable, even if the code makes it obvious.
All events occur in: crm.lead_qualifications, crm.saleslog, crm.cst, crm.leadlog and are being logged into crm.lead_payments
*/
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

/**
 * Log payments
*/
if ( isset( $_GET['pay'] ) ) {
	$temp = explode( ",", $_GET['pay'] );
	// Based on these logs, the administration is supposed to be able to reproduce any payment at any time ( when the lead was created, why it was paid, to whom, when the sale occured, which lead created the sale )
	for ( $i = 0; $i < count( $temp ); $i++ ) {
		// Log payment ----> Leads being covered
		mysql_query( "INSERT INTO crm.lead_payments SET lead_id = '".(int)$temp[$i]."', payment_time = now(), isa_id = '".(int)$_GET['person_id']."'" );
	}
	// Log payment ----> Sales being paid
	foreach ( $_GET['sale'] as $sale => $lead ) {
		mysql_query( "INSERT INTO crm.lead_payments SET lead_id = '".(int)$lead."', payment_time = now(), isa_id = '".(int)$_GET['person_id']."', sale_id = '".(int)$sale."'" );
	}

	header( "Location: isa_salary.php?person_id=".$_GET['person_id'] );
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

// Comission percentage
$percentage = 2;

// Minimum number of approved snapshots
$minSnapshots = 35;

// Year template
$yearTemplate = "
<table width='100%'>
	<tr>
		<td style='background-color: black; color: white; font-weight: bold;' colspan=\"10\">
			Year: {YEAR}
		</td>
	</tr>
	<tr style=\"background-color: gray; color: white;\">
		<td>
			Month
		</td>
		<td>
			Customer
		</td>
		<td>
			Type
		</td>
		<td>
			Value
		</td>
		<td>
			Currency
		</td>
		<td>
			EUR
		</td>
		<td>
			Exchange Rate
		</td>
		<td>
			Period
		</td>
		<td>
			Lead Approved
		</td>
		<td>
			Sale Date
		</td>
	</tr>
	{MONTH}
</table>
";

$monthTemplate = "
<tr>
	<td {MONTH_STYLE} style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		<b>{MONTH}</b>
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{CUSTOMER}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{TYPE}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{VALUE}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{CURRENCY}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{EURO}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{EXCHANGE_RATE}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{PERIOD}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{LEAD_APPROVE}
	</td>
	<td style=\"background-color: {BG_COLOR}; color: {FG_COLOR};\">
		{SALE_DATE}
	</td>
</tr>
";

$payTemplate = "
<tr>
	<td style='background-color: white; color: black; font-weight: bold;' colspan=\"10\">
		To pay this month: {PAY} out of {TOTAL}
	</td>
</tr>
	<td style='background-color: white; color: black;' colspan=\"10\">
		<form method=\"GET\" action=\"isa_salary.php\">
			<input name=\"pay\" value=\"{LEAD_IDS}\" type=\"hidden\">
			{SALE_IDS}
			<input name=\"person_id\" value=\"".(int)$_GET['person_id']."\" type=\"hidden\">
			<input type=\"submit\" value=\"Pay comission\" {DISABLED} style=\"display: inline; width: 100px;\">
		</form>
	</td>
</tr>";

$comissionTemplate = "
	<tr>
		<td style='background-color: black; color: white; font-weight: bold;text-align: right;' colspan=\"10\">
			Approved Leads: {LEAD_COUNT} Sales: {SALE_COUNT} Paid: {PAID_COUNT} Comission: {COMISSION}
		</td>
	</tr>
";

// Function for generating HTML content
function render( $type, $values ) {
	global $monthTemplate;
	global $yearTemplate;
	global $comissionTemplate;
	global $payTemplate;

	$content = "";
	switch ( $type ) {
		case 3:
			$content = $payTemplate;
			$content = str_replace( "{PAY}", $values['pay'], $content );
			$content = str_replace( "{TOTAL}", $values['total'], $content );
			$content = str_replace( "{LEAD_IDS}", $values['lead_ids'], $content );
			$content = str_replace( "{DISABLED}", $values['disabled'], $content );
			$content = str_replace( "{SALE_IDS}", $values['sale_ids'], $content );

			break;
		case 2:
			$content = $comissionTemplate;
			$content = str_replace( "{COMISSION}", $values['_comission'], $content );
			$content = str_replace( "{LEAD_COUNT}", $values['_lead_count'], $content );
			$content = str_replace( "{SALE_COUNT}", $values['_sale_count'], $content );
			$content = str_replace( "{PAID_COUNT}", $values['_paid_count'], $content );
			break;
		case 1:
			$content = $monthTemplate;
			// #669966 - green
			// #0066CC - blue
			// #700000 - red

			$content = str_replace( "{BG_COLOR}", ( $values['approved'] == 0 ? '' : ( $values['_type'] == "lead" ? '#700000' : ( $values['_type'] == "paid/lead" || $values['_type'] == "paid/sale" ? '#0066CC' : '#669966' ) ) ), $content );
			$content = str_replace( "{FG_COLOR}", ( $values['approved'] == 0 ? '' : ( $values['_type'] == "lead" ? 'white' : 'white' ) ), $content );

			$content = str_replace( "{MONTH}", $values['_month'], $content );
			if ( $values['_month'] == "" ) {
				$content = str_replace( "{MONTH_STYLE}", "style='background-color: white;'", $content );
			} else {
				$content = str_replace( "{MONTH_STYLE}", "", $content );
			}
			$content = str_replace( "{CUSTOMER}", htmlspecialchars( $values['name'] ), $content );

			$content = str_replace( "{TYPE}", $values['_type'], $content );
			$content = str_replace( "{VALUE}", ( $values['product_price'] - $values['discount'] ), $content );
			$content = str_replace( "{PERIOD}", $values['product_period'], $content );

			$content = str_replace( "{CURRENCY}", $values['currency'], $content );
			$content = str_replace( "{EURO}", round( ( $values['product_price'] - $values['discount'] ) * fFOREX( $values['currency_exchange_rate'], $values['currency_exchange_rate_euro'] ), 2 ), $content );
			$content = str_replace( "{EXCHANGE_RATE}", $values['currency_exchange_rate'], $content );
			$content = str_replace( "{LEAD_APPROVE}", $values['lead_approve_date'], $content );
			$content = str_replace( "{SALE_DATE}", $values['status_date'], $content );

			$content = str_replace( "{COMISSION}", $values['htmlComission'], $content );
			break;
		case 0:
			$content = $yearTemplate;
			$content = str_replace( "{YEAR}", $values['_year'], $content );
			$content = str_replace( "{MONTH}", $values['htmlMonth'], $content );
			break;
	}

	return $content;
}
?>
<br>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td class="MenuHeadline" width="100%">
				ISA Salary Calculator
			</td>
		</tr>
	<tr>
		<td>
			<form method="GET" action="isa_salary.php">
			<b>Select ISA sales person</b><br>
			<?php
			// Select all salespeople
			echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople WHERE person_level=3 ORDER BY name', 'person_id', 'name');
			?>
			<br>
			<input type="submit" value="Display" style="display: inline; width: 50px;">
			</form>
		</td>
	</tr>
	<tr>
		<td>&nbsp;</td>
	</tr>
	<tr>
		<td>
			<?php
				// Load data for selected person
				if ( isset( $_GET['person_id'] ) && $_GET['person_id'] != "" && is_numeric( $_GET['person_id'] ) ) {
					// Prepare SQL statements
					$statements = new stdClass();

					// Select ALL leads CREATED[AND APPROVED] in each MONTH AND those that created a SALE[CASH IN] in each MONTH

					// Created
					// This data is stored in crm.leadlog, having these relevant properties: lead_org_status = [APPROVED->VALID].
					// A sales person is bound by the lead_person_id field.
					// The lead's date used is the snapshot creation date: leadlog.lead_approve_date
					$statements->created = "SELECT * FROM crm.leadlog, crm.cst WHERE leadlog.lead_org_cstid = cst.cst_id AND lead_person_id = '".$_GET['person_id']."' ORDER BY lead_approve_date DESC";

					// Cashed in
					// This data is stored in crm.saleslog, having these relevant properties: status = 2 (CashedIN)
					// A sale is bound to the lead_qualifications through the customer id (lead_cstid).
					// The sales's date is: sold_date
					// A valid sale type is: product_trial = 0
					$statements->cashed = "SELECT * FROM crm.saleslog, crm.cst, crm.leadlog WHERE saleslog.status = 2 AND saleslog.cst_id = leadlog.lead_org_cstid AND saleslog.cst_id = cst.cst_id AND leadlog.lead_person_id = '".$_GET['person_id']."' AND ( product_price - discount ) > 0 AND sold_date >= lead_approve_date && product_name not like '%Server License%' ORDER BY sold_date DESC";

					// Prepare resources
					$resources = new stdClass();

					// Fetch data from both of them, display using color highlighting
					$resources->created = mysql_query( $statements->created );

					$resources->cashed = mysql_query( $statements->cashed );

					// Build output data, and group by month
					$output = new stdClass();

					// Prepare year 'array'
					$output->year = array();

					for ( $i = 0; $i < mysql_num_rows( $resources->created ); $i++ ) {
						$row = mysql_fetch_assoc( $resources->created );
						$aux = explode(" ", $row['lead_approve_date'] );
						$year = substr( $aux[0], 0, 4 ); // Push item to the right year
						$month = substr( $aux[0], 5, 2 ); // Push item to the right month

						if ( !isset( $output->year[$year]->month ) ) {
							$output->year[$year]->month = array();
						}
						if ( !isset( $output->year[$year]->month[$month] ) ) {
							$output->year[$year]->month[$month] = array();
						}

						$row['_type'] = "lead";
						$row['_year'] = $year;
						$row['_month'] = date( "F", mktime( 0, 0, 0, $month + 1, 0, 0 ) );
						// Check if the lead was already paid:
						if ( mysql_num_rows( mysql_query( "SELECT * FROM crm.lead_payments WHERE lead_id = '".(int)$row['lead_id']."' LIMIT 1" ) ) == 1 ) {
							$row['_type'] = "paid/lead";
						}
						$output->year[$year]->month[$month][] = $row; // Store data for display
					}

					for ( $i = 0; $i < mysql_num_rows( $resources->cashed ); $i++ ) {
						$row = mysql_fetch_assoc( $resources->cashed );

						$aux = explode(" ", $row['lead_approve_date'] );
						$year = substr( $aux[0], 0, 4 );
						$month = substr( $aux[0], 5, 2 );

						if ( !isset( $output->year[$year]->month ) ) {
							$output->year[$year]->month = array();
						}
						if ( !isset( $output->year[$year]->month[$month] ) ) {
							$output->year[$year]->month[$month] = array();
						}

						$row['_type'] = "sale";
						$row['_year'] = $year;
						$row['_month'] = date( "F", mktime( 0, 0, 0, $month + 1, 0, 0 ) );

						// Check if the sale was caused by a lead that was already paid:
						if ( mysql_num_rows( mysql_query( "SELECT * FROM crm.lead_payments WHERE lead_id = '".(int)$row['lead_id']."' LIMIT 1" ) ) == 1 ) {
							$row['_type'] = "paid/sale";
						}
						$output->year[$year]->month[$month][] = $row;
					}

					// Format output
					// For every year
					$content = "";
					$saleTotal = 0; // Total value sold
					$payment = true; // Payments can only be done IN the last available month
					$toPay = "";
					foreach ( $output->year as $year => $entry ) {
						$temp = array();
						$sold = 0; // Count the number of sold products
						$paid = 0; // Count the number of leads already paid
						$leads = ""; // Track each LEAD that has to be paid!
						$sales = ""; // Track each SALE that has to be paid, including sale id and lead id.
						// For every month within that year
						foreach ( $entry->month as $month => $values ) {
							$color = 0;
							$approved = 0; // Count the number of approved leads
							// For every entry within that month
							$month = ""; // Temp variable for storing displaied month
							foreach ( $values as $cst_id => $row ) {
								if ( $month == $row['_month'] ) {
									$row['_month'] = "";
								} else {
									$month = $row['_month'];
								}
								if ( !is_array( $temp[$month]['entries'] ) ) {
									$temp[$month]['entries'] = array();
								}
								$temp[$month]['entries'][] = $row;
								if ( $row['_type'] == "lead" || $row['_type'] == "paid/lead" ) {
									$color++;
								}
								if ( $row['_type'] == "lead" ) {
									$approved++;
									$leads .= ( $leads == "" ? "" : "," ) . $row["lead_id"];
								} else if ( $row['_type'] == "sale" ) {
									$sold++;
									$saleTotal += ( $row['product_price'] - $row['discount'] ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
									$sales .= "<input name=\"sale[".$row["sale_id"]."]\" value=\"".$row["lead_id"]."\" type=\"hidden\">";
								}
							}

							// ( For the latest month ) If the ISA has met her minimum number of approved snapshots, count value to be paid based on the amount of sales
							$pay = 0;
							if ( $approved >= $minSnapshots && $payment == true ) {
								$pay = $saleTotal * $percentage / 100;

								// Add salary info
								//$toPay = render( 3, array( "pay" => $pay, "total" => $saleTotal, "lead_ids" => $leads, "disabled" => ( $pay != 0 && $payment ? "" : "disabled" ), "sale_ids" => $sales ) );
								$temp[$month]['topay'] = array( "pay" => $pay, "total" => $saleTotal, "lead_ids" => $leads, "disabled" => ( $pay != 0 && $payment ? "" : "disabled" ), "sale_ids" => $sales );
							}

							$temp[$month]['color'] = $color >= $minSnapshots ? true : false;

							// Add statistics
							$temp[$month]['statistics'] = array( "_comission" => $percentage, "_lead_count" => $approved, "_sale_count" => $sold, "_paid_count" => $paid );
							//render( 2, array( "_comission" => $percentage, "_lead_count" => $approved, "_sale_count" => $sold, "_paid_count" => $paid ) );

							$payment = false;
						}

						// Paint stuff
						$tempContent = "";
						foreach ( $temp as $month => $val ) {
							for ( $k = 0; $k < count( $val['entries'] ); $k++ ) {
								$val['entries'][$k]['approved'] = $val['color'];
								if ( $temp[$month]['statistics']['_lead_count'] < $minSnapshots && ( $val['entries'][$k]['_type'] == "sale" || $val['entries'][$k]['_type'] == "paid/sale" ) ) {
									$val['topay']['total'] = 0;
									$val['statistics']['_sale_count'] = 0;
									continue;
								}
								$tempContent .= render( 1, $val['entries'][$k] );
							}
							if ( array_key_exists( 'topay', $val ) && $val['topay']['total'] != 0 ) {
								$tempContent .= render( 3, $val['topay'] );
							}
							$tempContent .= render( 2, $val['statistics'] );
						}

						$content .= render( 0, array(
								"_year" => $year
								,"htmlMonth" => $tempContent
							)
						);
					}

			echo $toPay.$content;
		}
	?>
		</td>
	</tr>
