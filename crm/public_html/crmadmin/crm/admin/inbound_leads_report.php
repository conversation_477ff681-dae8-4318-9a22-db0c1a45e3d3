<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Variables
$sLeads = '';

// All leads
{
	// Select leads and their original details
	$rRes = mysql_query("SELECT * FROM website.website_leads WHERE submitted >= '2007-01-06' " . ( $_GET['salespeople'] ? "&& person_id = '" . intval($_GET['salespeople']) . "'" : '' ) . " ORDER BY submitted DESC");

	// Generate Output List
	while ( $aRow = @mysql_fetch_array($rRes) )
	{
		// Select 'cst' table details
		$rCstRes = mysql_query("SELECT * FROM crm.cst WHERE cst_id = '" . $aRow['cst_id'] . "' LIMIT 1");
		$aCstRow = mysql_fetch_array($rCstRes);

		// Select last action
		$rLARes = mysql_query("SELECT MAX(added) as last_action FROM comments WHERE cst_id = '" . $aRow['cst_id'] . "'");
		$rLARow = mysql_fetch_array($rLARes);

		$sLeads .= '
	<tr onMouseOver="this.setAttribute(\'class\', \'hoverTRPointer\');" onMouseOut="this.setAttribute(\'class\', \'noHoverTRPointer\');" onClick="window.open(\'/crmadmin/crm/sales/customer.php?cst_id=' . $aRow['cst_id'] . '\', \'_blank\')">
		<td style="padding-left: 10px;" valign="top"><a href="customer.php?cst_id=' . $aRow['cst_id'] . '">' . $aRow['company'] . '</a></td>
		<td valign="top">' . $aCstRow['name'] . '</td>
		<td valign="top">' . ReturnPersonNameFromID($aRow['person_id']) . '</td>
		<td valign="top">' . $rLARow['last_action'] . '</td>
		<td valign="top">' . $aRow['country'] . '</td>
		<td valign="top">' . $aRow['reference'] . '</td>
		<td valign="top">' . $aRow['business_need'] . '</td>
		<td valign="top">' . $aRow['type'] . '</td>
		<td valign="top">' . $aRow['submitted'] . '</td>
	</tr>';
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php', 'inbound_leads_report.js');

?>
<br>

<form method="GET" action="inbound_leads_report.php">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td><b>Inbound Customer Leads Report:</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2" valign="top">
			<b>Select Sales Person(s):</b><br>
			<?php
			echo returnSelectBox($_GET['salespeople'], 'salespeople', false, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
		</td>
		<td colspan="5" valign="bottom">
			<input type="submit" value="Display Report" style="width: 25%" class="submit">
		</td>
	</tr>

	<?= returnPageDelimeter(9); ?>

	<tr>
		<td style="padding-left: 10px;" width="15%" onClick="oSortLeads.mSort(this, 0, 'string');" class="imitateLink"><b>Inbound Name</b></td>
		<td width="9%" onClick="oSortLeads.mSort(this, 1, 'string');" class="imitateLink"><b>CRM Name</b></td>
		<td width="10%" onClick="oSortLeads.mSort(this, 2, 'string');" class="imitateLink"><b>Sales Person</b></td>
		<td width="9%" onClick="oSortLeads.mSort(this, 3, 'string');" class="imitateLink"><b>Last Action</b></td>
		<td width="9%" onClick="oSortLeads.mSort(this, 4, 'string');" class="imitateLink"><b>Country</b></td>
		<td width="9%" onClick="oSortLeads.mSort(this, 5, 'string');" class="imitateLink"><b>Reference</b></td>
		<td width="15%" onClick="oSortLeads.mSort(this, 6, 'string');" class="imitateLink"><b>Business Need</b></td>
		<td width="15%" onClick="oSortLeads.mSort(this, 7, 'string');" class="imitateLink"><b>Request Type</b></td>
		<td width="9%" onClick="oSortLeads.mSort(this, 8, 'string');" class="imitateLink"><b>Request Received</b></td>
	</tr>
	<tbody id="leads">
	<?= $sLeads ?>
	</tbody>
	<tr>
		<td><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
