<?php
$URL = "../../crm2/sales/";
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Configuration from CRM-2
require(INCLUDE_PATH_CRM2.'configuration.php');

// Access Limit?
require('access_limits.php');

$from = date( "Y-m-d", strtotime("-3 months") );

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

$output = "";

function rows2html( $rows ) {
	global $URL;
	$content = "<table width='900'>";

	$description = "		<tr>
			<td>
				<nobr><b>Sale date</b></nobr>
			</td>
			<td>
				<nobr><b>Company Name</b></nobr>
			</td>
			<td>
				<nobr><b>Case ID (Customer ID)</b></nobr>
			</td>
			<td>
				<nobr><b>Sales Person</b></nobr>
			</td>
			<td>
				<nobr><b>Product Name</b></nobr>
			</td>
			<td>
				<nobr><b>Expires Date</b></nobr>
			</td>
			<td>
				<nobr><b>Price</b></nobr>
			</td>
			<td>
				<nobr><b>CRC Person</b></nobr>
			</td>
		</tr>";

	$year = "";
	$month = "";
	for ( $i = 0; $i < count( $rows ); $i++ ) {
		$_temp = explode( "-", $rows[$i]['status_date'] );
		$_month = $_temp[1];
		$_year = $_temp[0];
		if ( $_year != $year || $_month != $month ) {
			if ( $_year != $year ) {
				$content .= "<tr><td colspan='7'><h2>Year: ".(int)$_year."</h2></td></tr>";
			}
			if ( $_month != $month ) {
				$content .= "<tr><td colspan='7'><h3>Month: ".date ("F", mktime( 0, 0, 0, $_month, 1, $_year) )."</h3></td>".$description."</tr>";
			}
			$year = $_year;
			$month = $_month;
		}
		$content .= "<tr>
			<td>
				<nobr>".htmlspecialchars( $rows[$i]['status_date'] )."</nobr>
			</td>
			<td>
				<nobr>".htmlspecialchars( $rows[$i]['name'] )."</nobr>
			</td>
			<td>
				<nobr><a href='".$URL."?page=customer&cst_id=".(int)$rows[$i]['cst_id']."' target='_blank'>".htmlspecialchars( $rows[$i]['cst_id'] )."</a></nobr>
			</td>
			<td>
				<nobr>".htmlspecialchars( DBGetRowValue( "crm.salespeople", "name", "person_id = " .(int)$rows[$i]['person_id'] ) )."</nobr>
			</td>
			<td>
				<nobr>".htmlspecialchars( $rows[$i]['product_name'] )."</nobr>
			</td>
			<td>
				<nobr>".htmlspecialchars( $rows[$i]['expires_date'] )."</nobr>
			</td>
			<td>
				<nobr>".number_format( ( (float) ( $rows[$i]['product_price'] - $rows[$i]['discount'] ) ) * fFOREX( $rows[$i]['currency_exchange_rate'], $rows[$i]['currency_exchange_rate_euro'] ) , 2)." EUR</nobr>
			</td>
			<td>
				<nobr>".htmlspecialchars( DBGetRowValue( "crm.salespeople", "name", "person_id = ( SELECT person_id FROM crm.cst WHERE case_name = 'card_crc' AND master_id = ( SELECT master_id FROM crm.cst WHERE cst_id = '".(int)$rows[$i]['cst_id']."' ) ) " ) )."</nobr>
			</td>
		</tr>";
	} 

	$content .= "</table>";	
	return $content;
}

$rows = DBQueryGetRows("SELECT 
		status_date
		,( SELECT name FROM crm.cst WHERE cst_id = A.cst_id ) AS name
		,cst_id
		,person_id
		,product_category 
		,product_price
		,product_name
		,expires_date
		,discount
		,currency_exchange_rate
		,currency_exchange_rate_euro
	FROM 
		crm.saleslog AS A 
	WHERE 
		status_date >= '".$from."' 
		AND status = 2 
		AND sale_type = 'New' 
		AND ( product_price - discount > 0 )
		AND product_category = 3
	ORDER by status_date DESC
");

echo rows2html( $rows );
// Output footer
echo HTMLFooter();
?>
