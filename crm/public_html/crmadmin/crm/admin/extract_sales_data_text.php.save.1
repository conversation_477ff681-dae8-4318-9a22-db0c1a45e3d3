<?php
// Language
$iLangID = intval($_GET['lang_id']);
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Require field template
require(INCLUDE_PATH_LIB."customer_temp_" . $iLangID . ".php");

// Set header to display plain text
header("Content-type: text/plain");

// Input data
$sFrom = $_GET['from'];
$sTo = $_GET['to'];

// Define fields (Headers)
//echo "filename,company_name,contact,address_1,address_2,state,town,zipcode,today_date,starts_date,expires_date,days_to_pay,product_price,product_vat,product_total,sales_person,sales_person_title,contact_email,one_time_password,product,po_number,country,currency,\n";

// Select data
$rSales = mysql_query("select * from saleslog where status_date >= '" . $sFrom . "' && status_date <= '" . $sTo . "' && status = 5");

function fReverseDate($sDate){
	list($iYear, $iMonth, $iDay) = explode("-", $sDate);
	return $iDay."-".$iMonth."-".$iYear;
	
}

// Loop through result
while ( $aSales = mysql_fetch_array($rSales) )
{
	// Select customer details
	$c_res = mysql_query("select * from cst where cst_id = '" . $aSales['cst_id'] . "' limit 1");
	$c_row = mysql_fetch_array($c_res);
//	print_r($c_row);
//	continue;

	if ( $c_row['master_id'] )
	{
		$iCaseID = $c_row['cst_id'];
		$iPO = $c_row['po_number'];
		$c_res = mysql_query("select * from cst where cst_id = '" . $c_row['master_id'] . "' limit 1");
		$c_row = mysql_fetch_array($c_res);
	}

	// Select country info
	$cn_res = mysql_query("select * from countries where id = '" . $c_row['invoice_country'] . "'");
	$cn_row = mysql_fetch_array($cn_res);
	$sCountryCode = $cn_row['code'];

	// Select invoice contact
	$ic_res = mysql_query("SELECT * FROM crm.contacts WHERE contacts.cst_id = '" . $iCaseID . "' AND contacts.invoice = 1 LIMIT 1");
	$ic_row = mysql_fetch_array($ic_res);

	// No invoice contact ?
	if ( !$ic_row['name'] )
	{
		$ic_res = mysql_query("SELECT * FROM crm.contacts WHERE contacts.cst_id = '" . $iCaseID . "' AND contacts.primary_contact = 1 LIMIT 1");
		$ic_row = mysql_fetch_array($ic_res);
	}

	// Select data on sales person
	$s_res = mysql_query("select * from salespeople where person_id = '" . $aSales['person_id'] . "' limit 1");
	$s_row = mysql_fetch_array($s_res);

	// Select one time password
	$o_res = mysql_query("select * from one_time_passwords where cst_id = '" . $aSales['cst_id'] . "' limit 1");
	$o_row = mysql_fetch_array($o_res);

	// Select country name
	$country_res = mysql_query("select * from countries where id = '" . $c_row['invoice_country'] . "' limit 1");
	$country_row = mysql_fetch_array($country_res);

	// Product
	switch ( $aSales['product_type'] )
	{
		case 6:
			$sProduct = 'Security Manager with Network Software Detector';
			break;
		case 7:
			$sProduct = 'Enterprise Security Manager with Network Software Detector';
			break;
		case 9:
			$sProduct = $aSales['product_name'];
			break;

		//"New" products
		case 1:
			$sProduct = "Secunia VTS\n-Vulnerability Intelligence\n-Vulnerability Database\n-Text + messaging alerting\n-Advisories via email\n-Support 8:30 - 16:30 CET";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 2:
			$sProduct = "Secunia VM\n-Vulnerability Intelligence\n-Vulnerability Database\n-Text + messaging alerting\n-Advisories via email\n-Advisory management, documentation, reports and statistics capabilities\n-Ticketing system\n-Support 8:30 - 16:30 CET";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 3:
			//$sProduct = "Secunia EVM\n-Vulnerability Intelligence\n-Vulnerability Database\n-Multi user access, user management, multiple account capability\n-Text + messaging alerting\n-Advisories via email\n-Advisory management, documentation, reports, and statistics capabilities\n-Ticketing system\n-Support 8:30 - 16:30 CET";
			$sProduct = "Secunia EVM\nSecunia Enterprise Vulnerability Manager\n\nPer seat/user license covering ".$aSales['license_amount']." user account".($aSales['license_amount'] > 1 ? 's' : '')."\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed solution:\n- Vulnerability Management\n- Ticketing\n- Remediation Tracking\n- Hierarchical multi user management\n- Reporting\nThe solution includes Vulnerability Intelligence data";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 4:
			//$sProduct = "Secunia VIF\n-Vulnerability Intelligence\n-Advanced filtering capability\n-Vulnerability Database\n-Text + messaging alerting\n-Advisories via email\n-Database delivery via XML feed, multiple xml feed support\n-Multi user / feed support\n-Support 8:30 - 16:30 CET";
			$sProduct = "Secunia VIF\nSecunia Vulnerability Intelligence Feed\n\nSite license covering ".$aSales['license_amount']." asset list".($aSales['license_amount'] > 1 ? 's' : '')."\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed solution:\n- SMS alerting\n- Email alerting\n- XML synchronisation\n- Software filtering\n- Vendor filtering\nThe solution includes Vulnerability Intelligence data";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 5:
			$sProduct = "Secunia SS\n-IP, Port, and vulnerability scanning\n-Periodic differential scanning\n-Text + messaging alerting\n-Management via Customer Area\n-Support 8:30 - 16:30 CET";
			break;

		case 11:
			//$sProduct = "Secunia EVM-S\n-Vulnerability Intelligence\n-Vulnerability Database\n-Multi user access, user management, multiple account capability\n-Text + messaging alerting\n-Advisories via Email\n-Advisory management, documentation, reports, and statistics capabilities\n-Ticketing system\n-Support 8:30 - 16:30 CET\n-Includes 1 license for running software on 1 local server";
			$sProduct = "Secunia EVM-S\nSecunia Enterprise Vulnerability Manager Server edition\n\nPer seat/user license covering ".$aSales['license_amount']." user account".($aSales['license_amount'] > 1 ? 's' : '')."\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of license solution:\n- Vulnerability Management\n- Ticketing\n- Remediation Tracking\n- Hierarchical multi user management\n- Reporting\nThe Solution includes Vulnerability Intelligence data and Server software license.";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 16:
			$sProduct = "Secunia BA\n-Binary Analyses during subscription period\n-Analysis of critical remote vulnerabilities in certain software\n-PoC, exploit code, and other support files provided as applicable\n-Access to download site\n-Access to voting system";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/";
			break;

		case 17:
			//$sProduct = "Secunia CSI\n-Unlimited inspections of licensed hosts (limited to 400 hosts)\n-CSI GUI application\n-CSI Agent application\n-Email alerting\n-Basic set-up support\n-Online support";
			$sProduct = "Secunia CSI\nSecunia Corporate Software Inspector 3.0\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 18:
			//$sProduct = "Secunia CSI Professional\n-Unlimited inspections of licensed hosts (limited to 1000 hosts)\n-CSI GUI application\n-CSI Agent application\n-Email alerting\n-Standard set-up support\n-Online support\n-Mail support - response time 2 business days";
			$sProduct = "Secunia CSI-P\nSecunia Corporate Software Inspector 3.0 Professional\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia rTerms and Conditions set forth 
at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 19:
			//$sProduct = "Secunia CSI Enterprise\n-Unlimited inspections of licensed hosts (limited to 2.500 hosts)\n-Multiple accounts capabilities\n-CSI GUI application\n-CSI Agent application\n-Email alerting\n-Advanced set-up support\n-Online support\n-Mail support - response time 1 business day\n-Phone support 8:30 - 16:30 CET";
			$sProduct = "Secunia CSI-E\nSecunia Corporate Software Inspector 3.0 Enterprise edition\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 20:
			//$sProduct = "Secunia CSI Enterprise - Server Edition\n-Includes 1 license for running software on 1 local server\n-Unlimited inspections of licensed hosts (limited to 2.500 hosts)\n-Multiple accounts capabilities\n-CSI GUI application\n-CSI Agent application\n-Email alerting\n-Advanced set-up support\n-Online support\n-Mail support - repsonse time 1 business days\nPhone support 8:30 - 16:30 CET";
			$sProduct = "Secunia CSI-S\nSecunia Corporate Software Inspector 3.0 Server edition\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping\nIncludes Server software license";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;
		
		case 21:
			$sProduct = $aSales['product_name'];
			break;

		case 200:
			//$sProduct = "Corporate Software Inspector 4.x - Professional";
			$sProduct = "Secunia CSI-P 4.x\nSecunia Corporate Software Inspector 4.x (CSI) Professional\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- Integrated with Microsoft WSUS and Microsoft SCCM for 3rd Party Patch Management\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping\n- Patch Management/Deployment (requires Microsoft WSUS or Microsoft SCCM) The license includes signature updates";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 201:
			//$sProduct = "Corporate Software Inspector 4.x - Enterprise";
			$sProduct = "Secunia CSI-E 4.x\nSecunia Corporate software Inspector 4.x Enterprise edition\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- Integrated with Microsoft WSUS and Microsoft SCCM for 3rd Party Patch Management\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping\n- Patch Management/Deployment (requires Microsoft WSUS or Microsoft SCCM) the license includes signature updates";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 202:
			$sProduct = "Secunia CSI 4.x\nSecunia Corporate software Inspector 4.x\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- integrated with Microsoft WSUS and Microsoft SCCM for 3rd Party Patch Management\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping\n- Patch Management/Deployment (requires Microsoft WSUS or microsoft SCCM) The license includes signature updates";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 203:
			$sProduct = "Server License";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 204:
			$sProduct = "Secunia CSI-SB 4.x\nSecunia Corporate software Inspector 4.x\n\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable\n\nDescription of licensed software:\n- integrated with Microsoft WSUS for 3rd Party Patch Management\n- Software Inspection/Authenticated Vulnerability Scanning\n- Software Inventory/Asset Mapping\n- Patch Management/Deployment (requires Microsoft WSUS) The license includes signature updates";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 205:
			$sProduct = "Secunia CSI - Standard Support"; 
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 206:
			$sProduct = "Secunia CSI - Premium Support";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 207:
			$sProduct = "Secunia CSI - Enterprise Support";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 208:
			$sProduct = "Secunia VIM SMB";
			$sProduct .= "\n\nLicense/user recipients allowed\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable - unless agreed otherwise";
			$sProduct .= "\n\nDescription of licensed solution:\n- Asset list configuration\n- Vulnerability management\n- Ticketing\n- Remediation tracking\n- Support";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		case 209:
			$sProduct = "Secunia VIM";
			$sProduct .= "\n\nLicense/user recipients allowed\n- the license is valid in the period shown\n- the license is restricted to the purchasing entity\n- the license is non-transferable - unless agreed otherwise";
			$sProduct .= "\n\nDescription of licensed solution:\n- Asset list configuration\n- Real time vulnerability alerting e-mail/SMS\n- Vulnerability management\n- Vulnerability database\n- Extended advisories\n- Ticketing\n- Remediation tracking\n- Hierachical multi user management\n- Automatic report generation\n- Management and administration\n- Support";
			$sProduct .= "\n\nAll Secunia Products, Services and Solutions are sold accordingly to Secunia Terms and Conditions set forth at:\nhttps://ca.secunia.com/terms_and_conditions/csi/";
			break;

		default:
			$sProduct = '';
	}

	// Calculate VAT
	$iVat = 0;
	if ( $aSales['lang_id'] == 2 ) 
	{
		$iVat = ($aSales['product_price'] - $aSales['discount']) * 0.25;
	}

	// output
	if ( ($aSales['product_price'] - $aSales['discount']) > 0 )
	{
		//echo '"' . $aSales['sold_date'] . "_" . $aSales['sale_id'] . "_" . $c_row['cst_id'] . "\",\"" . $c_row['name'] . "\",\"" . $ic_row['name'] . "\",\"" . $c_row[$customer_data['Address 1']] . "\",\"" . $c_row[$customer_data['Address 2']] . "\",\"" . $c_row[$customer_data['State']] . "\",\"" . $c_row[$customer_data['Town']] . "\",\"" . $c_row[$customer_data['Zipcode']] . "\",\"" . ConvertGetDate('', 'MySQL-Date') . "\",\"" . ( $aSales['invoice_start_date'] ? $aSales['invoice_start_date'] : $aSales['sold_date'] ) . "\",\"" . $aSales['expires_date'] . "\",\"" . $aSales['payment_time'] . "\",\"" . number_format($aSales['product_price'] - $aSales['discount']) . "\",\"" . $iVat . "\",\"" . number_format(($aSales['product_price'] - $aSales['discount']) + $iVat) . "\",\"" . $s_row['name'] . '","' . $s_row['title'] . '","' . $c_row['email'] . '","' . $o_row['password']  . "\",\"" . $sProduct . "\",\"" . $iPO . "\",\"" . $country_row['country'] . "\",\"" . $aSales['currency'] . "\",\n";
		$aLines = explode("\n", $sProduct);
		for($i=0;$i<sizeof($aLines);$i++){
			$sProductName = $aLines[$i];
			if ($i < 1){
				echo '"' . $aSales['sale_id'] . '";"' . $c_row['cst_id'] . '";"' . fNavisionEncode($c_row['name']) . '";"' . fNavisionEncode($ic_row['name']) . '";"' . fNavisionEncode($c_row[$customer_data['Address 1']]) . '";"' . fNavisionEncode($c_row[$customer_data['Address 2']]) . '";"' . fNavisionEncode($c_row[$customer_data['State']]) . '";"' . fNavisionEncode($c_row[$customer_data['Town']]) . '";"' . $c_row[$customer_data['Zipcode']] . '";"' . ( $aSales['currency'] == "EURO" ? "EUR" : $aSales['currency'])  . '";"' . fNavisionEncode($aSales['product_type']) . '";"1";"' . ($aSales['product_price'] - $aSales['discount']) . '";"' . fReverseDate(($aSales['invoice_start_date'] ? $aSales['invoice_start_date'] : $aSales['sold_date'])) . '";"' . fReverseDate($aSales['expires_date']) . '";"' . fNavisionEncode($s_row['name']) . '";"' . $sCountryCode . '";"' .$s_row['person_id'] . '";"' . fNavisionEncode($s_row['name']) . '";"' . $s_row['department'] . '";"' . fNavisionEncode($sProductName) . '";"' . fNavisionEncode($ic_row['name']) . '";"' . $iPO . '";"' .$c_row['field_6']. '";"' .$c_row['field_7']. '"' . "\n";
			} else {
				echo '"' . $aSales['sale_id'] . '";"' . $c_row['cst_id'] . '";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"' . fNavisionEncode($sProductName) . '";"";"";"' .$c_row['field_6']. '";"' .$c_row['field_7']. '"' . "\n";
			}
		}
		//print_r($aSales);
	}
}

// Function to make MS Navision understand our jibberish codepage
function fNavisionEncode($sString, $sCodepage='Windows-1252'){
	//$sString = htmlentities($sString);
	//$sString = html_entity_decode($sString, ENT_QUOTES, $sCodepage);
	return $sString;
}

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
	$split = explode('-', $date);
	$date = $split[2] . '-' . $split[1] . '-' . $split[0];

	// Return formatted date
	return $date;
}

?>
