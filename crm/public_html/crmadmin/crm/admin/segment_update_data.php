<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Save data
if ( $submitted == 'Update' )
{
        // Write to segments table
        mysql_query("update segments set segment_name = '" . $segment_name . "', parent_segment_id = '".(int)$_POST['parent_segment_id']."' where segment_id = '" . $segment_id . "'");
}

// Select data on this segment_id
$res = mysql_query("select * from segments where segment_id = '" . $segment_id . "'");
$row = mysql_fetch_array($res);

$parent_segment_id = $row['parent_segment_id'];

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Update Segment
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <form method="POST" action="segment_update_data.php">
        <input type="hidden" name="segment_id" value="<?=htmlspecialchars($segment_id)?>">
	<tr>
		<td class="TablePadding">Parent Segment</td>
		<td>
			<?php
			//echo returnSelectBox(array((int)$parent_segment_id), 'parent_segment_id', false, 'select segment_name, segment_id from crm.segments WHERE segment_name like "crm2 - %" AND segment_status = 0', 'segment_id', 'segment_name');
			echo makeSegmentCombo( $row['parent_segment_id'] );
			?>
		</td>
	</tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">
                        Name:
                </td>
                <td colspan="1" width="85%">
                        <input type="text" name="segment_name" size="50" value="<?=htmlspecialchars($row['segment_name'])?>">
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">

                </td>
                <td colspan="1" width="85%">
                        <input type="submit" name="submitted" value="Update">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
