<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Reset vars
$criteria = '';

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration', HTTP_PATH.'admin/index.php');

// Revive customers
if ( $task == 'revive' )
{
	// Loop over erresult
	while ( list($cst_id, $value) = each($revive) )
	{
		if ( $value )
		{
			mysql_query("update cst set " . ( !$new_person_id ? 'person_id = null' : "person_id = '" . $new_person_id . "'" ) . ", appointment = now() where cst_id = '" . $cst_id . "'");
		}
	}
}

// Select Dead Customers
{
	// Make criteria
	if ( $segment_id )
	{
		$criteria = " && cst.segment_id = '" . $segment_id . "'";
	}

	if ( $person_id )
	{
		$criteria .= " && cst.person_id = '" . $person_id . "'";
	}

	if ( $lang_id )
	{
		$criteria .= " && cst.lang_id = '" . $lang_id . "'";
	}

	if ( $search )
	{
		$criteria .= " && (cst.name like '%" . $search . "%' || cst.phone like '" . $search . "%')";
	}

	// Select dead customers
	if ( $criteria )
	{
		$res = mysql_query("select cst.cst_id, cst.name, cst.person_id, max(added) as last_contact from (cst) LEFT JOIN comments ON cst.cst_id = comments.cst_id where !appointment " . $criteria . $sSQLAccessLimit . " group by cst.cst_id order by " . ( $sort ? $sort : 'name' ));
	}
}
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="4" width="100%">
                        Revive Customers
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <form method="get" action="customer_revive.php">
        <tr>
                <td colspan="4" width="100%">
                        Choose Sales Person<br>
                        <select name="person_id">
                        <option value="">Optional - Not Selected</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from salespeople order by name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
                        }
                        ?>
                        </select><br><br>
                        Choose Segment<br>
                        <select name="segment_id">
                        <option value="">Optional - Not Selected</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from segments order by segment_name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['segment_id'] . '"' . ( $s_row['segment_id'] == $segment_id  ? 'selected' : '' ) . '>' . $s_row['segment_name'] .  '</option>';
                        }
                        ?>
                        </select><br><br>
                        Choose Lang<br>
                        <select name="lang_id">
                        <option value="">Optional - Not Selected</option>
                        <option value="1">United Kingdom</option>
                        <option value="2">Denmark</option>
                        <option value="4">Norway</option>
			</select><br><br>			
                        Company Name / Phone<br>
                        <input type="text" name="search" value="<?=htmlspecialchars($search)?>"> - <input type="submit" value="Show Customers">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="5" width="100%">
                        <img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
                </td>
        </tr>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
        	<td width="40%">
                	<a href="customer_revive.php?segment_id=<?= intval($segment_id) ?>&amp;person_id=<?= intval($person_id) ?>&amp;sort=name"><b>Company Name</b></a>
                </td>
                <td width="25%">
                	<a href="customer_revive.php?segment_id=<?= intval($segment_id) ?>&amp;person_id=<?= intval($person_id) ?>&amp;sort=person_id"><b>Sales Person</b></a>
                </td>
                <td width="25%">
                	<a href="customer_revive.php?segment_id=<?= intval($segment_id) ?>&amp;person_id=<?= intval($person_id) ?>&amp;sort=last_contact"><b>Last contact</b></a>
                </td>
		<td width="10%">
                	<b>Revive</b>
	        </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <form method="post" action="customer_revive.php">
        <input type="hidden" name="person_id" value="<?=htmlspecialchars($person_id)?>">
        <input type="hidden" name="segment_id" value="<?=htmlspecialchars($segment_id)?>">
        <input type="hidden" name="lang_id" value="<?=htmlspecialchars($lang_id)?>">
        <input type="hidden" name="task" value="revive">
                <?php
                // Loop over result
                while ( $row = @mysql_fetch_array($res) )
                {
			$c_res = mysql_query("select max(added) as last_contact from comments where cst_id = '" . $row['cst_id'] . "'");
			$c_row = mysql_fetch_array($c_res);
                ?>
        	<tr>
                	<td width="40%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=htmlentities($row['name'])?></a>
                        </td>
                	<td width="25%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=ReturnPersonNameFromID($row['person_id'])?></a>
                        </td>
                	<td width="25%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=$c_row['last_contact']?></a>
                        </td>
                	<td width="10%">
                        	<input type="checkbox" name="revive[<?=$row['cst_id']?>]" value="1">
                        </td>
                <tr>
                <?php
                }
                ?>
        <tr>
                <td colspan="4" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
		<td colspan="3" width="90%">
                </td>
                <td colspan="1" width="100%">                        
                        Move To Sales Person<br>
                        <select name="new_person_id">
                        <option value="">Not Selected - Make free to everybody</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from salespeople order by name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                                echo '<option value="' . $s_row['person_id'] . '">' . $s_row['name'] .  '</option>';
                        }
                        ?>
                        </select>
                </td>
        </tr>
        <tr>
                <td colspan="3" width="90%">
                </td>
                <td colspan="1" width="10%">
                        <input type="submit" value="Revive">
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br><br>
                </td>
        </tr>
        </form>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
