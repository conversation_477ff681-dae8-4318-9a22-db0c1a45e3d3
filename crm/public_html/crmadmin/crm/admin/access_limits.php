<?php
// Configuration
$sSQLAccessLimit = '';
$aLimits = array();

// <PERSON>
{
	$aLimits[73]['people'] = '48,73,81';
	$aLimits[73]['sale_report.php']['sql_limit'] = " AND saleslog.person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['forecast_report.php']['sql_limit'] = " AND cst.person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['offers_report.php']['sql_limit'] = " AND person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['delete_offer.php']['sql_limit'] = "";
	$aLimits[73]['sp_appointment_list.php']['sql_limit'] = " AND person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['customer_list.php']['sql_limit'] = " AND person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['customer_move.php']['sql_limit'] = " AND person_id IN(" . $aLimits[73]['people'] . ")";
	$aLimits[73]['customer_revive.php']['sql_limit'] = " AND cst.person_id IN(" . $aLimits[73]['people'] . ")";
}

// Andreas
/*{
	$aLimits[74]['people'] = '';
	$aLimits[74]['sale_report.php']['sql_limit'] = "";
	$aLimits[74]['forecast_report.php']['sql_limit'] = "";
	$aLimits[74]['offers_report.php']['sql_limit'] = "";
	$aLimits[74]['delete_offer.php']['sql_limit'] = "";
	$aLimits[74]['sp_appointment_list.php']['sql_limit'] = "";
	$aLimits[74]['customer_list.php']['sql_limit'] = "";
	$aLimits[74]['customer_move.php']['sql_limit'] = "";
	$aLimits[74]['customer_revive.php']['sql_limit'] = "";
}*/

// Access Limitations
if ( is_array($aLimits[$persondata[0]]) )
{
	// Get Persons Limits
	$aLimits = $aLimits[$persondata[0]];

	// File name
	$sFile = substr($_SERVER['SCRIPT_NAME'], strrpos($_SERVER['SCRIPT_NAME'], '/')+1, 200);

	// Is Script included as OK?
	if ( $aLimits[$sFile] )
	{
		$sSQLAccessLimit = $aLimits[$sFile]['sql_limit'];
	}
	else
	{
		echo "Access Denied.";
		exit();
	}
}
?>
