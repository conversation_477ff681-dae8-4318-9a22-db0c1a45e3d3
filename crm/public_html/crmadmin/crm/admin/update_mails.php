<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Delete vendor
if ( $task == 'delete' )
{
        mysql_query("delete from mail_templates where id = '" . $id. "'");
}

// Select all vendor from vendor table
$res = mysql_query("select * from mail_templates where prod_limit = 5 order by lang_id desc");

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Update mail templates
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <?php
        // Loop over vendor result
        while ( $row = mysql_fetch_array($res) )
        {
        ?>
        <tr>
                <td colspan="1" width="40%">
                        "<a href="update_mail_data.php?id=<?=$row['id']?>"><?=$row['title']?></a>"
                </td>
                <td colspan="1" width="20%">
                	<?=ReturnCountryName($row['lang_id'])?>
                </td>
                <td colspan="1" width="40%">
                        <a href="update_mails.php?id=<?=$row['id']?>&amp;task=delete" onClick="return confirm('Do you wish to delete: <?=htmlspecialchars($row['title'])?>');">[Delete]</a>
                </td>
        </tr>
        <?php
        }
        ?>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
