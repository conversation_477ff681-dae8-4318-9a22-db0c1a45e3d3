<?php
require_once "/home/<USER>/secrets.php";
require_once "../../../../sales/mysql.php";
// Open DB connection
mysql_connect('192.168.100.100', 'crm', DB_PASS);
mysql_select_db('crm');

// Cross sale definition
$cross['1'] = array(5,6);
$cross['2'] = array(5,6);
$cross['3'] = array(5,6);
$cross['4'] = array(5,6);
$cross['5'] = array(1,2,3,4,6);
$cross['6'] = array(1,2,3,4,5);


// Select all "active" sales
$res = mysql_query("select product_type, cst.cst_id, cst.master_id, ((product_price-discount)*country_currency.currency)/7.44 as price from saleslog, cst, country_currency where (product_price-discount) > 0 && (status is null || status != 3) && saleslog.cst_id = cst.cst_id && product_type in (1,2,3,4) && saleslog.currency = country_currency.currency_name");
while ( $row = mysql_fetch_array($res) )
{
	// Select all CST_ID's
	$cst_ids = '';
	$cst_res = mysql_query("select cst_id, master_id from cst where cst_id = '" . $row['cst_id'] . "' UNION select cst_id, master_id from cst where cst_id = '" . $row['master_id'] . "' UNION select cst_id, master_id from cst where master_id = '" . $row['master_id'] . "'");
	while ( $cst_row = mysql_fetch_array($cst_res) )
	{
		$cst_ids .= $cst_row['cst_id'] . ',';
	}
//	$cst_ids = $row['cst_id'] . ',' . ( $row['master_id'] ? $row['master_id'] . ',' : '' );

	// Check for cross sale for this CST_ID
	if ( !$done[$row['cst_id']] )
	{
		// Check for cross sales on either this cst_id, the master cst_id, or another child cst_id
		$c_res = @mysql_query("select sum(((product_price-discount)*country_currency.currency)/7.44) as cross_revenue from saleslog, country_currency where (product_price-discount) > 0 && (status is null || status != 3) && cst_id in(" . substr($cst_ids, 0, -1). ") && product_type in (" . @implode(',', $cross[$row['product_type']]) . ") && saleslog.currency = country_currency.currency_name");
		$c_row = @mysql_fetch_array($c_res);
		if ( $c_row['cross_revenue'] > 0 )
		{
			echo $row['product_type'] . "\t" . $row['cst_id'] . "\t" . number_format($row['price']) . "\t" . number_format($c_row['cross_revenue']) . "\n";
			$result[$row['product_type']]['count']++;
			$result[$row['product_type']]['revenue'] += $c_row['cross_revenue'];
		}
	}
	$done[$row['cst_id']] = true;
}

var_dump($result);
?>
