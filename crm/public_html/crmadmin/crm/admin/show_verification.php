<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Configuration from CRM-2
require(INCLUDE_PATH_CRM2.'configuration.php');

// Access Limit?
require('access_limits.php');

$rQuery = mysql_query("SELECT * FROM saleslog WHERE sale_id='".$_GET['saleid']."'");
$aSaleData = mysql_fetch_assoc($rQuery);
if (strlen(trim($aSaleData['verification_text'])) > 0){
	echo nl2br(htmlspecialchars($aSaleData['verification_text']));
} else {
	echo "No verification entered";
}
?>
