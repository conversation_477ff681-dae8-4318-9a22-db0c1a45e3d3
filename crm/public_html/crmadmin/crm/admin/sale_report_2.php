<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Configuration from CRM-2
require(INCLUDE_PATH_CRM2.'configuration.php');

// Access Limit?
require('access_limits.php');

$iColspan = 10;

// Register globals...
$from = $_GET['from'];
$to = $_GET['to'];
$person_id = $_GET['person_id'];
$team_id = $_GET['team_id'];

// Function to print deparment
function fGetDepartment($iDepartmentID, $bWrappers=true, $bShort=true){
	if ($bShort){
		$sDepartmentName = DBGetRowValue("salesteams", "short", "team_id='".$iDepartmentID."'");
	} else {
		$sDepartmentName = DBGetRowValue("salesteams", "name", "team_id='".$iDepartmentID."'");
	}
	if ($bWrappers){
		return " (".$sDepartmentName.")";
	} else {
		return $sDepartmentName;
	}
}

// Function to output for JS
function fJSOUT($sInput) {
	return htmlspecialchars(str_replace("'", "\\'", $sInput));
}

// Select sales from period
if ( $from && $to )
{
	// Select all from saleslog
	$sQuery = "select * from saleslog" . ( $_GET['invoice_country'] ? ', cst' : '' ) . " left join salespeople on saleslog.person_id = salespeople.person_id where ((sold_date >= '" . $from . "' && sold_date <= '" . $to . "') || (status = 3 && (status_date >= '" . $from . "' && status_date <= '" . $to . "'))) " . ( $person_id ? " && saleslog.person_id = '" . $person_id . "'" : '' ) . ( $team_id ? " && salespeople.department = '" . $team_id . "'" : '' ) . ( $_GET['this_lang_id'] ? " && lang_id = '" . $_GET['this_lang_id'] . "'" : '' ) . ( $_GET['invoice_country'] ? ' && saleslog.cst_id = cst.cst_id && invoice_country ' . ( $_GET['invoice_country_rev'] ? 'not' : '' ) . ' in(' . preg_replace('[^ , 0-9]*', '', $_GET['invoice_country']) . ')' : '' ) . $sSQLAccessLimit . ( $_GET['product_type'] ? " && product_type = '" . $_GET['product_type'] . "'" : '' ) . " order by sold_date";
	$res = mysql_query($sQuery);

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		// Determine Sale Type
		if ( !$_GET['by_product'] )
		{
			if ( $row['product_category'] == 4 ) { // Partner
				$tmp_type = 'SA';
			} elseif ( $row['product_category'] == 3 ) { // CSI
				$tmp_type = 'CSI';
			} elseif ( $row['product_category'] == 2 ) { // SS
				$tmp_type = 'SS';
			} else { // VI
				$tmp_type = 'VI';
			}
		}
		else
		{
			$tmp_type = $row['product_type'];
		}

		// Select other data about customer
		$x_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
		$x_row = mysql_fetch_array($x_res);

		// Select salesperson data
		$sp_res = mysql_query("select * from salespeople where person_id = '" . $row['person_id'] . "' limit 1");
		$sp_row = mysql_fetch_array($sp_res);

		// Build list of all customer id's
		if ( $x_row['master_id'] ) {
			$iMasterID = $x_row['master_id'];
		} else {
			$iMasterID = $x_row['cst_id'];
		}

		$sCustomerIDs = $iMasterID . ',';
		$rAll = mysql_query("SELECT * from crm.cst WHERE master_id = '" . $iMasterID . "'");
		while ( $aAll = mysql_fetch_array($rAll) ) {
			$sCustomerIDs .= $aAll['cst_id'] . ',';
		}

		// Canvas or Recurrence
		{
			$sale_type = '';
			$t_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $row['product_category'] . "'");
			if ( mysql_num_rows($t_res) == 0 && $row['status'] != 3 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$canvas_revenue[$tmp_type] +=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
					$sale_type = $sSaleTypeNew;
				}
			}
			elseif ( mysql_num_rows($t_res) > 0 && $row['status'] != 3 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$resale_revenue[$tmp_type] +=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
					$sale_type = $sSaleTypeRecur;
				}
			}
		}

		// Check if been cancelled or is good
		if ( $row['status'] != 3 ) {
			// Output
			if ( ($row['product_price'] - $row['discount']) ) {
				$sJSOut .= "\t{company: '" . fJSOUT($x_row['name']) . "', sold_by: '" . fJSOUT($sp_row['init']) . "', team: '" . fJSOUT(fGetDepartment($sp_row['department'], false)) . "', product: '". fJSOUT($row['product_name']) . "', product_category: '" . fJSOUT($tmp_type) . "', type: '" . fJSOUT($sale_type) . "', amount: '" . fJSOUT( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ) . "', period: '" . fJSOUT(ReturnMonths($row['sold_date'], $row['expires_date'])) . "', status: '" . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . "', commission: '" . fJSOUT( $row['commision_amount'] ? 'Yes' : 'No' ) . "', lead_source: '" . fJSOUT($aLeadSources[DBGetRowValue('crm.cst', 'lead_source', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")]) . "', industry: '" . fJSOUT($aIndustries[DBGetRowValue('crm.cst', 'company_industry', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")]) . "'},\n";
			}
		}
	}
}
else
{
	$from = ConvertGetDate('', 'MySQL-Date');
	$to = ConvertGetDate('', 'MySQL-Date');
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php', false, true);
?>
<table cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="<?= $iColspan ?>">
		Sale Report 2.0
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<form method="GET" action="sale_report_2.php">
	<tr>
		<td width="350">
			<b>Select period to display</b><br>
			<input type="text" value="<?=htmlspecialchars($from)?>" name="from" style="width: 150px;"> - <input type="text" value="<?=htmlspecialchars($to)?>" name="to" style="width: 150px;"><br>
		</td>
		<td width="450">
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td width="200">
						<b>Select sales person</b><br>
						<?= returnSelectBox(array($person_id), 'person_id', false, 'select * from salespeople order by name', 'person_id', 'name'); ?>
					</td>
					<td width="200">
						<b>Select sales team</b><br>
						<?= returnSelectBox(array($team_id), 'team_id', false, 'select * from salesteams where team_id > 0 order by name', 'team_id', 'name'); ?>
					</td>
				</tr>
			</table>
		</td>
		<td valign="bottom"><input type="submit" value="Generate" style="width: 150px;"></td>
	</tr>

	<tr>
		<td><br></td>
	</tr>

	</form>
</table>

<div id="sales_data"></div>

<br>

<script>
var aSalesData = [
<?= $sJSOut ?>
];
</script>

<?php
// Output footer
echo HTMLFooter();
?>
