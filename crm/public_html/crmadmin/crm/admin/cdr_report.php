<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');

if ($_GET['from']){
	$iFrom = strtotime($_GET['from']." 00:00:00");
} else {
	$iFrom = mktime(0, 0, 0)-86400;
}

if ($_GET['to']){
	$iTo = strtotime($_GET['to']." 23:59:59");
} else {
	$iTo = mktime(23, 59, 59);
}

if ($_GET['person_id']){
	$iPerson = (int) $_GET['person_id'];
}

function fGetCalls(){
	//Get outgoing
	$rQuery = mysql_query("SELECT call_id, call_date, call_src, call_dst, call_billsec FROM it_phone_cdr WHERE call_date >= ".$GLOBALS['iFrom']." AND call_date <= ".$GLOBALS['iTo']) or die(mysql_error());
	while($aCallData = mysql_fetch_array($rQuery, MYSQL_ASSOC)){
		$aCalls[] = $aCallData;
	}
	return $aCalls;
}

function fSecondsToHuman($iSeconds){
	$iHours = floor($iSeconds/3600);
	$iBalance = $iSeconds-($iHours*3600);
	$iMinutes = floor($iBalance/60);
	$iSeconds = floor($iBalance-($iMinutes*60));
	return str_pad($iHours, 2, "0", STR_PAD_LEFT).":".str_pad($iMinutes, 2, "0", STR_PAD_LEFT).":".str_pad($iSeconds, 2, "0", STR_PAD_LEFT);
}

function fGetCountryName($sPhone){
        if (substr($sPhone, 0, 1) == "+"){
                $iPhone = substr($sPhone, 1);
        } else {
                $iPhone = $sPhone;
        }

        foreach($GLOBALS['aCountries'] as $aCountry){
                if (substr($iPhone, 0, strlen($aCountry['code'])) == $aCountry['code']) return $aCountry['country'];
        }
}

function fGetCountryCode($sPhone){
        if (substr($sPhone, 0, 1) == "+"){
                $iPhone = substr($sPhone, 1);
        } else {
                $iPhone = $sPhone;
        }

        foreach($GLOBALS['aCountries'] as $aCountry){
                if (substr($iPhone, 0, strlen($aCountry['code'])) == $aCountry['code']) return $aCountry['code'];
        }
}

function fGetCountryNameFromCode($iCode){
        foreach($GLOBALS['aCountries'] as $aCountry){
                if ($aCountry['code'] == $iCode) return $aCountry['country'];
        }
}
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="7" width="100%">
		Phone Statistics
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="cdr_report.php">
		<tr>
			<td colspan="7">
				<b>Select period to display</b><br>
				<input type="text" value="<?=strftime("%Y-%m-%d", $iFrom)?>" name="from" style="width: 150px;"> - <input type="text" value="<?=strftime("%Y-%m-%d", $iTo)?>" name="to" style="width: 150px;"><br>
			</td>
		</tr>
	
		<tr>
			<td colspan="7" width="100%"><br></td>
		</tr>
		
		<tr>
			<td colspan="7" width="100%"><input type="submit" value="Display"></td>
		</tr>
	</form>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>

	<tr>
		<td colspan="7" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	
	<tr>
		<td colspan="7" width="100%">
			<b><h2>Call log</h2></b>
			<?php
			$aCalls = fGetCalls();
			if (sizeof($aCalls) == 0){
				echo "There are no calls registered in this period for this extension.";
			} else {
				?>
				<table width="850" cellspacing="0" cellpadding="0">
					<tr>
						<td><b>Time</b></td>
						<td align="left"><b>Source</b></td>
						<td align="left"><b>Destination</b></td>
						<td align="left"><b>Country</b></td>
						<td align="right"><b>Duration</b></td>
						<td align="right"><b>ID</b></td>
					</tr>
					<?php
					foreach($aCalls as $aCall){
						if ($aCall['call_src'] == "anonymous"){
							$sExternal = "N/A";
							$aCall['call_src'] = "N/A";
							$sCountry = "N/A";
						} else {
							if (strlen($aCall['call_src']) > 3){
								if (substr($aCall['call_src'], 0, 2) == "00"){
									$sExternal = "+".substr($aCall['call_src'], 2);
								} elseif (strlen($aCall['call_src']) == 3) {
									$sExternal = $aCall['call_src'];
								} else {
									$sExternal = "+45".$aCall['call_src'];
								}
								$aCall['call_src'] = $sExternal;
							} else {
								if (substr($aCall['call_dst'], 0, 2) == "00"){
									$sExternal = "+".substr($aCall['call_dst'], 2);
								} elseif (strlen($aCall['call_dst']) == 3) {
									$sExternal = $aCall['call_dst'];
								} else {
									$sExternal = "+45".$aCall['call_dst'];
								}
								$aCall['call_dst'] = $sExternal;
							}
							$sCountry = fGetCountryName($sExternal);
						}
						?>
						<tr>
							<td><?=strftime("%Y-%m-%d %H:%M:%S", $aCall['call_date']);?></td>
							<td align="left"><?=htmlspecialchars($aCall['call_src']);?></td>
							<td align="left"><?=htmlspecialchars($aCall['call_dst']);?></td>
							<td align="left"><?=htmlspecialchars($sCountry);?></td>
							<td align="right"><?=fSecondsToHuman($aCall['call_billsec']);?></td>
							<td align="right"><?=htmlspecialchars($aCall['call_id']);?></td>
						</tr>
						<?php
					}
					?>
				</table>
				<?php
			}
			?>
		</td>
	</tr>
</table>
<?php
echo HTMLFooter();
?>
