<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Select all accounts from customer area
$res = mysql_query("select distinct cst.cst_id, accounts.account_id, accounts.account_esm, cst.name, account_expires, person_id, account_product_type from ca.accounts, crm.cst where accounts.cst_id = cst.cst_id && cst.cst_id != 2 && account_product_type != 6 order by cst.cst_id");
while ( $row = mysql_fetch_array($res) )
{
	// Count number of scans requested
	$c_res = mysql_query("select device_id from ca.im_devices where account_id = '" . $row['account_id'] . "' && nsd_profile is NULL");

	// Dead or active?
	if ( strtotime($row['account_expires']) > time() )
	{
		$t = 'Active';
	}
	else
	{
		$t = 'Dead';
	}
    
	// Product
	switch ( $row['account_product_type'] )
	{
		case 1:
			$product = 'VTS';
			break;
		case 2:
			$product = 'SM';
			if ( $row['account_esm'] )
			{
				$product .= ' (ESM)';
			}
			break;
		case 3:
			$product = 'ESM';
			break;
		case 4:
			$product = 'VTS-E';
			break;
		case 5:
			$product = 'SS';
			break;
		case 6:
			$product = 'SM with NSD';
			break;
		default:
			$product = 'Unknown';
			break;
	}

	// Group by number of scans
	if ( mysql_num_rows($c_res) > 50 )
	{
		$s = '50+';
	}
	elseif ( mysql_num_rows($c_res) > 25 )
	{
		$s = '25-50';
	}
	elseif ( mysql_num_rows($c_res) > 10 )
	{
		$s = '10-25';
	}
	elseif ( mysql_num_rows($c_res) > 5 )
	{
		$s = '5-10';
	}
	elseif ( mysql_num_rows($c_res) > 1 )
	{
		$s = '1-5';
	}
	else
	{
		$s = 'N/A';
	}

	// Output
	$output[$t][$s] .= '
		<tr>
			<td style="padding-left: 10px;"><a href="'.HTTP_PATH_CRM2.'customer.php?cst_id=' . $row['cst_id'] . '">' . htmlspecialchars($row['name']) . '</a></td>
			<td><a href="'.HTTP_PATH_CRM2.'customer.php?cst_id=' . $row['cst_id'] . '">' . ReturnPersonNameFromID($row['person_id']) . '</a></td>
			<td>' . $product . '</td>
			<td>' . $row['account_expires'] . '</td>
			<td>' . mysql_num_rows($c_res) . '</td>
		</tr>
	';
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');
?>

<br>
<form method="GET" action="forecast_report_new.php">
<input type="hidden" name="submitted" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="6"><b>Accounts Software Detections</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="35%"><b>Company Name</b></td>
		<td width="15%"><b>Sales Person</b></td>
		<td width="25%"><b>Solution</b></td>
		<td width="10%"><b>Expires</b></td>
		<td width="15%"><b>Total Detections</b></td>
	</tr>
	<tr>
		<td colspan="5"><br><hr><br></td>
	</tr>
	<tr>
		<td><b>Active Accounts</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>50+ Detections</b></td>
	</tr>
	<?=$output['Active']['50+']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>25-50 Detections</b></td>
	</tr>
	<?=$output['Active']['25-50']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>10-25 Detections</b></td>
	</tr>
	<?=$output['Active']['10-25']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>5-10 Detections</b></td>
	</tr>
	<?=$output['Active']['5-10']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>1-5 Detections</b></td>
	</tr>
	<?=$output['Active']['1-5']?>
	<tr>
		<td colspan="5"><br><hr><br></td>
	</tr>
	<tr>
		<td><b>Expired Accounts</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>50+ Detections</b></td>
	</tr>
	<?=$output['Dead']['50+']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>25-50 Detections</b></td>
	</tr>
	<?=$output['Dead']['25-50']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>10-25 Detections</b></td>
	</tr>
	<?=$output['Dead']['10-25']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>5-10 Detections</b></td>
	</tr>
	<?=$output['Dead']['5-10']?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 5px;"><b>1-5 Detections</b></td>
	</tr>
	<?=$output['Dead']['1-5']?>
	<tr>
		<td><br><br><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
