<?php
if ( $iBigReportType != 1 ){
	$iBigReportType = 0;
}
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Configuration from CRM-2
require(INCLUDE_PATH_CRM2.'configuration.php');

// Access Limit?
require('access_limits.php');

// Colspan
$iColspan = 13;

// No injection
$good['CORE'] = 0;
$good['SS'] = 0;
$good['CSI'] = 0;
$good_total = '';
$payed_total = '';
$canvas_revenue = '';
$resale_revenue = '';
$trials = '';
$bad = '';
$bad_total = '';
$good_output = '';
$t_good_output = '';
$bad_output = '';
$sSaleTypeNew = 'New.';
$sSaleTypeRecur = 'Recu.';

// Register globals...
$from = $_GET['from'];
$to = $_GET['to'];
$person_id = $_GET['person_id'];
$team = $_GET['team'];

// Function to print deparment
function fGetDepartment($iDepartmentID, $bWrappers=true, $bShort=true){
/*	if ($bShort){
		$sDepartmentName = DBGetRowValue("salesteams", "short", "team_id='".$iDepartmentID."'");
	} else {
		$sDepartmentName = DBGetRowValue("salesteams", "name", "team_id='".$iDepartmentID."'");
	}
	if ($bWrappers){
		return " (".$sDepartmentName.")";
	} else {
		return $sDepartmentName;
	}*/
}

// Select sales from period
if ( $from && $to )
{
	// Select all from saleslog
	$sQuery = "select * from saleslog" . ( $_GET['invoice_country'] ? ', cst' : '' ) . " left join salespeople on saleslog.person_id = salespeople.person_id where ((sold_date >= '" . $from . "' && sold_date <= '" . $to . "') || ((status = 3 || status = 6 || status = 7) && (status_date >= '" . $from . "' && status_date <= '" . $to . "'))) " . ( $person_id ? " && saleslog.person_id = '" . $person_id . "'" : '' ) . ( $team ? " && saleslog.department = '" . $team . "'" : '' ) . ( $_GET['this_lang_id'] ? " && lang_id = '" . $_GET['this_lang_id'] . "'" : '' ) . ( $_GET['invoice_country'] ? ' && saleslog.cst_id = cst.cst_id && invoice_country ' . ( $_GET['invoice_country_rev'] ? 'not' : '' ) . ' in(' . preg_replace('[^ , 0-9]*', '', $_GET['invoice_country']) . ')' : '' ) . $sSQLAccessLimit . ( $_GET['product_type'] ? " && product_type = '" . $_GET['product_type'] . "'" : '' ) . " order by sold_date";
	$res = mysql_query($sQuery);

	$bShowOnlyOne = false; // Determine if the cancelled product should only be shown once

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		// Determine Sale Type
		if ( !$_GET['by_product'] )
		{
			if ( $row['product_category'] == 4 ) { // Partner
				$tmp_type = 'SA';
			} elseif ( $row['product_category'] == 3 ) { // CSI
				$tmp_type = 'CSI';
			} elseif ( $row['product_category'] == 2 ) { // SS
				$tmp_type = 'SS';
			} else { // CORE
				$tmp_type = 'CORE';
			}
		}
		else
		{
			$tmp_type = $row['product_type'];
		}
		$fValue =  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
		if ( $fValue < 10000 && $_GET['10000'] ) {
			continue;
		}

		// Select other data about customer
		$x_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
		$x_row = mysql_fetch_array($x_res);

		// Select salesperson data
		$sp_res = mysql_query("select * from salespeople where person_id = '" . $row['person_id'] . "' limit 1");
		$sp_row = mysql_fetch_array($sp_res);

		// Build list of all customer id's
		if ( $x_row['master_id'] ) {
			$iMasterID = $x_row['master_id'];
		} else {
			$iMasterID = $x_row['cst_id'];
		}

		$sCustomerIDs = $iMasterID . ',';
		$rAll = mysql_query("SELECT * from crm.cst WHERE master_id = '" . $iMasterID . "'");
		while ( $aAll = mysql_fetch_array($rAll) ) {
			$sCustomerIDs .= $aAll['cst_id'] . ',';
		}

		// Canvas or Recurrence
		{
			$sale_type = '';
			$sale_type_new = '';
			$t_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $row['product_category'] . "'");
			if ( mysql_num_rows($t_res) == 0 && $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$canvas_revenue[$tmp_type] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
					$sale_type = $sSaleTypeNew;
				}
			}
			elseif ( mysql_num_rows($t_res) > 0 && $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$resale_revenue[$tmp_type] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
					$sale_type = $sSaleTypeRecur;
				}
			}
			$sale_type_new = $row['sale_type'];
		}

		// Get Company Card details
		$aMaster = DBGetRow('crm.cst', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'");

		// Check if been cancelled or is good
		if ( $row['status'] != 3 && $row['status'] != 6 && $row['status'] != 7 ) {
			// Output
			if ( ($row['product_price'] - $row['discount']) ) {
				$good_output[$tmp_type] .= '<tr><td><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
				$good_output[$tmp_type] .= '<td>' . $sp_row['init'] . fGetDepartment($sp_row['department']) . '</td>';
				$good_output[$tmp_type] .= '<td>' . $row['product_name'] . ' (' . number_format(($row['product_price'] - $row['discount']), 2) . ')</td>';
				$good_output[$tmp_type] .= '<td>' . $sale_type . '</td>';
				$good_output[$tmp_type] .= '<td>' . $sale_type_new . '</td>';
				$good_output[$tmp_type] .= '<td align="right" style="padding-right: 20px;">' . number_format( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ), 2) . '</td>';
				$good_output[$tmp_type] .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
				$good_output[$tmp_type] .= '<td>' . ( $row['invoice_start'] ? $row['invoice_start'] : $row['sold_date'] ) . '</td>';
				$good_output[$tmp_type] .= '<td>' . $row['expires_date'] . '</td>';
				$good_output[$tmp_type] .= '<td><a href="show_verification.php?saleid='.htmlspecialchars($row['sale_id']).'" target="_blank">' . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . '</a></td>';
				$good_output[$tmp_type] .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td>';
				$good_output[$tmp_type] .= '<td>' . $aLeadSources[ $aMaster['lead_source'] ] . '</td>';
				$good_output[$tmp_type] .= '<td>' . $aIndustries[ $aMaster['company_industry'] ] . '</td>';
				$good_output[$tmp_type] .= '<td>' . DBGetRowValue('crm.countries', 'country', "id = '" . $aMaster['invoice_country'] . "'") . '</td></tr>';

				$sJSOut .= "{company: '" . htmlspecialchars($x_row['name']) . "', sold_by: '" . htmlspecialchars($sp_row['init']) . "', team: '" . htmlspecialchars(trim(fGetDepartment($sp_row['department']), ' )(')) . "', product: '". htmlspecialchars($row['product_name']) . "', product_category: '" . htmlspecialchars($tmp_type) . "', type: '" . htmlspecialchars($sale_type) . "', amount: '" .   ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) . "', period: '" . ReturnMonths($row['sold_date'], $row['expires_date']) . "', status: '" . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . "', commission: '" . ( $row['commision_amount'] ? 'Yes' : 'No' ) . "', lead_source: '" . $aLeadSources[DBGetRowValue('crm.cst', 'lead_source', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . "', industry: '" . $aIndustries[DBGetRowValue('crm.cst', 'company_industry', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . "'},\n";
			} else {
				$t_good_output[$tmp_type] .= '<tr><td><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
				$t_good_output[$tmp_type] .= '<td>' . $sp_row['init'] . fGetDepartment($sp_row['department']) . '</td>';
				$t_good_output[$tmp_type] .= '<td><a href="'.HTTP_PATH.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $row['product_name'] . '</a></td><td></td>';
				$t_good_output[$tmp_type] .= '<td align="right" style="padding-right: 20px;">' . number_format( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) , 2) . '</td>';
				$t_good_output[$tmp_type] .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
				$t_good_output[$tmp_type] .= '<td>' . ( time() > strtotime($row['expires_date']) ? 'Expired' : 'Active' ) . '</td>';
				$t_good_output[$tmp_type] .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td>';
				$t_good_output[$tmp_type] .= '<td>' . $aLeadSources[DBGetRowValue('crm.cst', 'lead_source', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . '</td>';
				$t_good_output[$tmp_type] .= '<td>' . $aIndustries[DBGetRowValue('crm.cst', 'company_industry', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . '</td></tr>';
			}

			// Sold total
			$good_total[$tmp_type] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

			// Totalt payed revenue
			if ( $row['status'] == 2 )
			{
				$payed_total[$tmp_type] +=   ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				$payed_total[$tmp_type . '_' . $sale_type] +=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				$payed[$tmp_type]++;
			} elseif ( $row['status'] == 3 || $row['status'] == 6 || $row['status'] == 7 ){
				$payed_total[$tmp_type] -=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				$payed_total[$tmp_type . '_' . $sale_type] -=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				$payed[$tmp_type]++;
			}

			// Unit counter
			if ( ($row['product_price'] - $row['discount']) )
			{
				$good[$tmp_type]++;
			}
			else
			{
				if ( $iBigReportType != 1 ){
					$trials[$tmp_type]++;
				}
			}
		} elseif ( ( $row['status'] == 3 || $row['status'] == 6 || $row['status'] == 7 ) && ( $iBigReportType == 1 ) )  {
			if ( strtotime( $from ) < strtotime( $row['sold_date'] ) ) {
				$bShowOnlyOne = true;
				for ( $k = 0; $k < 2; $k++ ){
					$good_output[$tmp_type] .= '<tr><td><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
					$good_output[$tmp_type] .= '<td>' . $sp_row['init'] . fGetDepartment($sp_row['department']) . '</td>';
					$good_output[$tmp_type] .= '<td>' . $row['product_name'] . ' (' . number_format(($row['product_price'] - $row['discount']), 2) . ')</td>';
					$good_output[$tmp_type] .= '<td>' . $sale_type . '</td>';
					$good_output[$tmp_type] .= '<td>' . $sale_type_new . '</td>';
					$good_output[$tmp_type] .= '<td align="right" style="padding-right: 20px;'.( ( $k == 1 ) ? 'color:red;' : '' ).'">' . ( ( $k == 1 ) ? "-" : "" ) . number_format( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) , 2) . '</td>';
					$good_output[$tmp_type] .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
					$good_output[$tmp_type] .= '<td>' .( ( $k == 1 ) ? $row['status_date']." (canceled) " : ( $row['invoice_start'] ? $row['invoice_start'] : $row['sold_date'] ) ). '</td>';
					$good_output[$tmp_type] .= '<td>' . ( ( $k == 1 ) ? "-" : $row['expires_date'] ) . '</td>';
					$good_output[$tmp_type] .= '<td><a href="show_verification.php?saleid='.htmlspecialchars($row['sale_id']).'" target="_blank">' . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . '</a></td>';
					$good_output[$tmp_type] .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td>';
					$good_output[$tmp_type] .= '<td>' . $aLeadSources[ $aMaster['lead_source'] ] . '</td>';
					$good_output[$tmp_type] .= '<td>' . $aIndustries[ $aMaster['company_industry'] ] . '</td>';
					$good_output[$tmp_type] .= '<td>' . DBGetRowValue('crm.countries', 'country', "id = '" . $aMaster['invoice_country'] . "'") . '</td></tr>';

					if ( $k == 0 ){
						$bad_total[$tmp_type] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
						$bad_total_secondary[$tmp_type] = $bad_total[$tmp_type];
					} else {
						$bad_total[$tmp_type] -= ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
					}

				}
			} else {
				$k = 1;
				$good_output[$tmp_type] .= '<tr><td><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
				$good_output[$tmp_type] .= '<td>' . $sp_row['init'] . fGetDepartment($sp_row['department']) . '</td>';
				$good_output[$tmp_type] .= '<td>' . $row['product_name'] . ' (' . number_format(($row['product_price'] - $row['discount']), 2) . ')</td>';
				$good_output[$tmp_type] .= '<td>' . $sale_type . '</td>';
				$good_output[$tmp_type] .= '<td>' . $sale_type_new . '</td>';
				$good_output[$tmp_type] .= '<td align="right" style="padding-right: 20px;color: red;">' . ( ( $k == 1 ) ? "-" : "" ) . number_format( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) , 2) . '</td>';
				$good_output[$tmp_type] .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
				$good_output[$tmp_type] .= '<td>' .( ( $k == 1 ) ? $row['status_date']." (canceled) " : ( $row['invoice_start'] ? $row['invoice_start'] : $row['sold_date'] ) ). '</td>';
				$good_output[$tmp_type] .= '<td>' . ( ( $k == 1 ) ? "-" : $row['expires_date'] ) . '</td>';
				$good_output[$tmp_type] .= '<td><a href="show_verification.php?saleid='.htmlspecialchars($row['sale_id']).'" target="_blank">' . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . '</a></td>';
				$good_output[$tmp_type] .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td>';
				$good_output[$tmp_type] .= '<td>' . $aLeadSources[ $aMaster['lead_source'] ] . '</td>';
				$good_output[$tmp_type] .= '<td>' . $aIndustries[ $aMaster['company_industry'] ] . '</td>';
				$good_output[$tmp_type] .= '<td>' . DBGetRowValue('crm.countries', 'country', "id = '" . $aMaster['invoice_country'] . "'") . '</td></tr>';

				$bad_total[$tmp_type] +=  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

			}

			// Unit counter
			$bad[$tmp_type]++;

			// Unit counter
			if ( ($row['product_price'] - $row['discount']) )
			{
				$good[$tmp_type]++;
			}
		}
		else
		{
			// Output
			$bad_output[$tmp_type] .= '<tr><td><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
			$bad_output[$tmp_type] .= '<td>' . $sp_row['init'] . '</td>';
			$bad_output[$tmp_type] .= '<td>' . $row['product_name'] . ' (' . number_format(($row['product_price'] - $row['discount']),2) . ')</td>';
			$bad_output[$tmp_type] .= '<td></td>';
			$bad_output[$tmp_type] .= '<td align="right" style="padding-right: 20px;">' . number_format( ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ),2) . '</td>';
			$bad_output[$tmp_type] .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
			$bad_output[$tmp_type] .= '<td>' . ReturnSaleStatus($row['status']) . '</td>';
			$bad_output[$tmp_type] .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td>';
			$bad_output[$tmp_type] .= '<td>' . $aLeadSources[DBGetRowValue('crm.cst', 'lead_source', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . '</td>';
			$bad_output[$tmp_type] .= '<td>' . $aIndustries[DBGetRowValue('crm.cst', 'company_industry', "cst_id = '" . ( $x_row['master_id'] ? $x_row['master_id'] : $x_row['cst_id'] ) . "'")] . '</td></tr>';

			// Sold total
			$bad_total[$tmp_type] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

			// Unit counter
			$bad[$tmp_type]++;
		}
	}

	// Good Totals
	if ( $iBigReportType == 1 ){
		// Totals - CORE
		$good_output['CORE'] .= '<tr><td colspan="4"><b>Total revenue - VI</b></td><td><b>' . $good['CORE'] . '</b></td><td align="right" style="padding-right: 20px;'.( ( ( $good_total['CORE'] - $bad_total['CORE'] ) < 0 ) ? 'color: red;' : '' ).'"><b>' .( ( $iBigReportType == 1 ) ? ( number_format( $good_total['CORE'] - $bad_total['CORE'] , 2) ) : number_format($good_total['CORE'], 2) ). '</b></td></tr>';
		$good_output['CORE'] .= '<tr><td colspan="4"><b>Total paid revenue - VI</b></td><td><b>' . $payed['CORE'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['CORE'], 2) . '</b></td></tr>';

		// Totals - SS
		$good_output['SS'] .= '<tr><td colspan="4"><b>Total revenue - SS</b></td><td><b>' . $good['SS'] . '</b></td><td align="right" style="padding-right: 20px;'.( ( ( $good_total['SS'] - $bad_total['SS'] ) < 0 ) ? 'color: red;' : '' ).'"><b>' .( ( $iBigReportType == 1 ) ? ( number_format($good_total['SS'] - $bad_total['SS'], 2) ) : number_format($good_total['SS'], 2) ). '</b></td></tr>';
		$good_output['SS'] .= '<tr><td colspan="4"><b>Total paid revenue - SS</b></td><td><b>' . $payed['SS'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['SS'], 2) . '</b></td></tr>';

		// Totals - CSI
		$good_output['CSI'] .= '<tr><td colspan="4"><b>Total revenue - CSI</b></td><td><b>' . $good['CSI'] . '</b></td><td align="right" style="padding-right: 20px;'.( ( ( $good_total['CSI'] - $bad_total['CSI'] ) < 0 ) ? 'color: red;' : '' ).'"><b>' .( ( $iBigReportType == 1 ) ? ( number_format($good_total['CSI'] - $bad_total['CSI'], 2) ) : number_format($good_total['CSI'], 2) ). '</b></td></tr>';
		$good_output['CSI'] .= '<tr><td colspan="4"><b>Total paid revenue - CSI</b></td><td><b>' . $payed['CSI'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['CSI'], 2) . '</b></td></tr>';

                // Totals - SA
                $good_output['SA'] .= '<tr><td colspan="4"><b>Total revenue - SA</b></td><td><b>' . $good['SA'] . '</b></td><td align="right" style="padding-right: 20px;'.( ( ( $good_total['SA'] - $bad_total['SA'] ) < 0 ) ? 'color: red;' : '' ).'"><b>' .( ( $iBigReportType == 1 ) ? ( number_format($good_total['SA'] - $bad_total['SA'], 2) ) : number_format($good_total['SA'], 2) ). '</b></td></tr>';
                $good_output['SA'] .= '<tr><td colspan="4"><b>Total paid revenue - SA</b></td><td><b>' . $payed['SA'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['SA'], 2) . '</b></td></tr>';
	} else {
		// Totals - CORE
		$good_output['CORE'] .= '<tr><td colspan="4"><b>Total revenue - VI</b></td><td><b>' . $good['CORE'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($good_total['CORE'], 2) . '</b></td></tr>';
		$good_output['CORE'] .= '<tr><td colspan="4"><b>Total paid revenue - VI</b></td><td><b>' . $payed['CORE'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['CORE'], 2) . '</b></td></tr>';

		// Totals - SS
		$good_output['SS'] .= '<tr><td colspan="4"><b>Total revenue - SS</b></td><td><b>' . $good['SS'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($good_total['SS'], 2) . '</b></td></tr>';
		$good_output['SS'] .= '<tr><td colspan="4"><b>Total paid revenue - SS</b></td><td><b>' . $payed['SS'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['SS'], 2) . '</b></td></tr>';

		// Totals - CSI
		$good_output['CSI'] .= '<tr><td colspan="4"><b>Total revenue - CSI</b></td><td><b>' . $good['CSI'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($good_total['CSI'], 2) . '</b></td></tr>';
		$good_output['CSI'] .= '<tr><td colspan="4"><b>Total paid revenue - CSI</b></td><td><b>' . $payed['CSI'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['CSI'], 2) . '</b></td></tr>';

                // Totals - SA
                $good_output['SA'] .= '<tr><td colspan="4"><b>Total revenue - SA</b></td><td><b>' . $good['SA'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($good_total['SA'], 2) . '</b></td></tr>';
                $good_output['SA'] .= '<tr><td colspan="4"><b>Total paid revenue - SA</b></td><td><b>' . $payed['SA'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($payed_total['SA'], 2) . '</b></td></tr>';
	}

	// Trials
	if ( $iBigReportType != 1 ){
			// Trials - CORE
			$t_good_output['CORE'] .= '<tr><td colspan="3"><b>Total - VI</b></td><td><b>' . $trials['CORE'] . '</b></td><td colspan="3"></td></tr>';

			// Trials - SS
			$t_good_output['SS'] .= '<tr><td colspan="3"><b>Total - SS</b></td><td><b>' . $trials['SS'] . '</b></td><td colspan="3"></td></tr>';

			// Trials - CSI
			$t_good_output['CSI'] .= '<tr><td colspan="3"><b>Total - CSI</b></td><td><b>' . $trials['CSI'] . '</b></td><td colspan="3"></td></tr>';

                	// Trials - SA
                	$t_good_output['SA'] .= '<tr><td colspan="3"><b>Total - SA</b></td><td><b>' . $trials['SA'] . '</b></td><td colspan="3"></td></tr>';
	}
	// Cancelled Revenue
	{
		// Cancelled - CORE
		$bad_output['CORE'] .= '<tr><td colspan="3"><b>Total revenue - VI</b></td><td><b>' . $bad['CORE'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($bad_total['CORE'], 2) . '</b></td></tr>';

		// Cancelled - SS
		$bad_output['SS'] .= '<tr><td colspan="3"><b>Total revenue - SS</b></td><td><b>' . $bad['SS'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($bad_total['SS'], 2) . '</b></td></tr>';

		// Cancelled - CSI
		$bad_output['CSI'] .= '<tr><td colspan="3"><b>Total revenue - CSI</b></td><td><b>' . $bad['CSI'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($bad_total['CSI'], 2) . '</b></td></tr>';

                // Cancelled - SA
                $bad_output['SA'] .= '<tr><td colspan="3"><b>Total revenue - SA</b></td><td><b>' . $bad['SA'] . '</b></td><td align="right" style="padding-right: 20px;"><b>' . number_format($bad_total['SA'], 2) . '</b></td></tr>';
	}
}
else
{
	$from = ConvertGetDate('', 'MySQL-Date');
	$to = ConvertGetDate('', 'MySQL-Date');
}

// To output data based on 'by_product' for data extraction and report generation
if ( $_GET['by_product'] )
{
	// Plain text output
	echo '<pre>';

	// New biz Revenue
	echo '<b>New Biz:</b><br>';
	print_r($canvas_revenue) . '<br><br>


';

	// Recurrence Revenue
	echo '<b>Recurrence:</b><br>';
	print_r($resale_revenue) . '<br><br><br>';

	exit();
}

// Table header
$sHeader = '
<tr>
	<td width="20%"><b>Company</b></td>
	<td width="5%"><b>SP</b></td>
	<td width="25%"><b>Product</b></td>
	<td width="5%"><b>Type (auto)</b></td>
	<td width="5%"><b>Type (man)</b></td>
	<td width="5%"><b>Amount</b></td>
	<td width="5%"><b>Period</b></td>
	<td width="5%"><b>Start</b></td>
	<td width="5%"><b>End</b></td>
	<td width="5%"><b>Status</b></td>
	<td width="5%"><b>Commi.</b></td>
	<td width="5%"><b>Lead&nbsp;Source</b></td>
	<td width="5%"><b>Industry</b></td>
	<td width="5%"><b>Country</b></td>
</tr>';

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="<?= $iColspan ?>" width="100%">
		Sale Report
		</td>
	</tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="">
	<tr>
		<td colspan="2">
			<b>Select period to display</b><br>
			<input type="text" value="<?=htmlspecialchars($from)?>" name="from" style="width: 150px;"> - <input type="text" value="<?=htmlspecialchars($to)?>" name="to" style="width: 150px;"><br>
		</td>
		<td colspan="3">
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td width="50%">
						<b>Select sales person</b><br>
						<?php
						// Select all salespeople
						echo returnSelectBox(array($person_id), 'person_id', false, 'select * from salespeople order by name', 'person_id', 'name');
						?>
					</td>
					<td width="50%">
						<b>Select sales team</b><br>
						<?php
						echo returnSelectBox(array($team), 'team', false, 'select * from salesteams where team_id > 0 order by name', 'name', 'name');
						?>
					</td>
				</tr>
			</table>
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%"><br></td>
	</tr>
	
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%"><input type="submit" value="Display"></td>
	</tr>

	</form>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<br>
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<b><h2>Summary Report</h2></b>
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%"><br></td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" valign="top">
			<table width="600" cellspacing="0" cellpadding="0">
				<tr>
					<td width="200"></td>
					<td width="100" align="right"><b>VI</b></td>
					<td width="100" align="right"><b>SS</b></td>
					<td width="100" align="right"><b>CSI</b></td>
					<td width="100" align="right"><b>SA</b></td>
					<td width="100" align="right"><b>TOTAL</b></td>
				</tr>
				<tr><td><br></td></tr>
				<?php
					if ( $iBigReportType == 1 ){
						echo '<tr><td><b><u>Before cancellation</u></b></td></tr>';
					}
				?>
				<tr>
					<td><b>No. of Customers:</b></td>
					<td align="right"><?= $good['CORE'] ?></td>
					<td align="right"><?= $good['SS'] ?></td>
					<td align="right"><?= $good['CSI'] ?></td>
					<td align="right"><?= (int) $good['SA'] ?></td>
					<td align="right"><?= ($good['CORE']+$good['SS']+$good['CSI']+$good['SA']) ?></td>
				</tr>
				<tr><td><br></td></tr>
				<tr>
					<td><b>Revenue:</b></td>
					<td align="right"><?= number_format($good_total['CORE'], 2) ?></td>
					<td align="right"><?= number_format($good_total['SS'], 2) ?></td>
					<td align="right"><?= number_format($good_total['CSI'], 2) ?></td>
					<td align="right"><?= number_format($good_total['SA'], 2) ?></td>
					<td align="right"><?= number_format($good_total['CORE']+$good_total['SS']+$good_total['CSI']+$good_total['SA'], 2) ?></td>
				</tr>
				<tr>
					<td><b>Revenue Paid:</b></td>
					<td align="right"><?= number_format($payed_total['CORE'], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SS'], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CSI'], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SA'], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CORE']+$payed_total['SS']+$payed_total['CSI']+$payed_total['SA'], 2) ?></td>
				</tr>
				<tr><td><br></td></tr>
				<tr>
					<td><b>Revenue New Sale:</b></td>
					<td align="right"><?= number_format($canvas_revenue['CORE'], 2) ?></td>
					<td align="right"><?= number_format($canvas_revenue['SS'], 2) ?></td>
					<td align="right"><?= number_format($canvas_revenue['CSI'], 2) ?></td>
					<td align="right"><?= number_format($canvas_revenue['SA'], 2) ?></td>
					<td align="right"><?= number_format($canvas_revenue['CORE']+$canvas_revenue['SS']+$canvas_revenue['CSI']+$canvas_revenue['SA'], 2) ?></td>
				</tr>
				<tr>
					<td><b>Revenue New Sale Paid:</b></td>
					<td align="right"><?= number_format($payed_total['CORE_' . $sSaleTypeNew], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SS_' . $sSaleTypeNew], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CSI_' . $sSaleTypeNew], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SA_' . $sSaleTypeNew], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CORE_' . $sSaleTypeNew]+$payed_total['SS_' . $sSaleTypeNew]+$payed_total['CSI_' . $sSaleTypeNew]+$payed_total['SA_' . $sSaleTypeNew], 2) ?></td>
				</tr>
				<tr><td><br></td></tr>

				<tr>
					<td><b>Revenue Recurrence:</b></td>
					<td align="right"><?= number_format($resale_revenue['CORE'], 2) ?></td>
					<td align="right"><?= number_format($resale_revenue['SS'], 2) ?></td>
					<td align="right"><?= number_format($resale_revenue['CSI'], 2) ?></td>
					<td align="right"><?= number_format($resale_revenue['SA'], 2) ?></td>
					<td align="right"><?= number_format($resale_revenue['CORE']+$resale_revenue['SS']+$resale_revenue['CSI']+$resale_revenue['SA'], 2) ?></td>
				</tr>
				<tr>
					<td><b>Revenue Recurrence Paid:</b></td>
					<td align="right"><?= number_format($payed_total['CORE_' . $sSaleTypeRecur], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SS_' . $sSaleTypeRecur], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CSI_' . $sSaleTypeRecur], 2) ?></td>
					<td align="right"><?= number_format($payed_total['SA_' . $sSaleTypeRecur], 2) ?></td>
					<td align="right"><?= number_format($payed_total['CORE_' . $sSaleTypeRecur]+$payed_total['SS_' . $sSaleTypeRecur]+$payed_total['CSI_' . $sSaleTypeRecur]+$payed_total['SA_' . $sSaleTypeRecur], 2) ?></td>
				</tr>
				<tr><td><br></td></tr>
				<?php if ( $iBigReportType != 1 ){ ?>
				<tr>
					<td><b>Trials:</b></td>
					<td align="right"><?= ( $trials['CORE'] ? $trials['CORE'] : 0 ) ?></td>
					<td align="right"><?= ( $trials['SS'] ? $trials['SS'] : 0 ) ?></td>
					<td align="right"><?= ( $trials['CSI'] ? $trials['CSI'] : 0 ) ?></td>
					<td align="right"><?= ( $trials['SA'] ? $trials['SA'] : 0 ) ?></td>
					<td align="right"><?= ($trials['CORE']+$trials['SS']+$trials['CSI']+$trials['SA']) ?></td>
				</tr>
				<tr><td><br></td></tr>
				<?php } ?>
				<?php
					if ( $iBigReportType == 1 ){
						echo '<tr><td><b><u>Cancellation</u></b></td></tr>';
					}
				?>
				<tr>
					<td><b>Cancellations:</b></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= ( $bad['CORE'] ? $bad['CORE'] : 0 ) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= ( $bad['SS'] ? $bad['SS'] : 0 ) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= ( $bad['CSI'] ? $bad['CSI'] : 0 ) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= ( $bad['SA'] ? $bad['SA'] : 0 ) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= ($bad['CORE']+$bad['SS']+$bad['CSI']+$bad['SA']) ?></td>
				</tr>
				<tr>
					<td><b>Revenue Cancelled:</b></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= number_format( ( $bShowOnlyOne == false ) ? $bad_total['CORE'] : $bad_total_secondary['CORE'], 2) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= number_format(( $bShowOnlyOne == false ) ? $bad_total['SS'] : $bad_total_secondary['SS'], 2) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= number_format(( $bShowOnlyOne == false ) ? $bad_total['CSI'] : $bad_total_secondary['CSI'], 2) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= number_format(( $bShowOnlyOne == false ) ? $bad_total['SA'] : $bad_total_secondary['SA'], 2) ?></td>
					<td align="right" <?= ( ( $iBigReportType == 1 ) ? 'style="color: red">-' : ">" ) ?><?= number_format($bad_total['CORE']+$bad_total['SS']+$bad_total['CSI']+$bad_total['SA'], 2) ?></td>
				</tr>
				<?php
					if ( $iBigReportType == 1 ){
						echo '<tr><td><b><u>Sums</u></b></td></tr>';
						echo '<tr>
								<td><b>Revenue</b></td>
								<td align="right">' .( number_format($good_total['CORE'] - $bad_total['CORE'], 2) ).'</td>
								<td align="right">' .( number_format($good_total['SS'] - $bad_total['SS'], 2) ).'</td>
								<td align="right">' .( number_format($good_total['CSI'] - $bad_total['CSI'], 2) ).'</td>
								<td align="right">' .( number_format($good_total['SA'] - $bad_total['SA'], 2) ).'</td>
								<td align="right">' .( number_format(($good_total['CORE']+$good_total['SS']+$good_total['CSI']+$good_total['SA']) - ($bad_total['CORE']+$bad_total['SS']+$bad_total['CSI']+$bad_total['SA']), 2 ) ).'</td>
							</tr>';
					}
				?>
			</table>
		</td>
	</tr>
	<tr><td><br><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>


	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<b><h2>Sales - VI</h2></b>
		</td>
	</tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $good_output['CORE']?>
			</table>
		</td>
	</tr>


	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<b><h2>Sales - SS</h2></b>
		</td>
	</tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $good_output['SS']?>
			</table>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<b><h2>Sales - CSI</h2></b>
		</td>
	</tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $good_output['CSI'] ?>
			</table>
		</td>
	</tr>

        <tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                        <img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
                </td>
        </tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                        <b><h2>Sales - SA</h2></b>
                </td>
        </tr>
        <tr>
                <td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
                        <table width="100%" cellspacing="0" cellpadding="0">
                                <?=$sHeader . $good_output['SA']?>
                        </table>
                </td>
        </tr>
	<?php if ( $iBigReportType != 1 ){ ?>
	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Free Trials - VI</h2></b>
		</td>
	</tr>
	<tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $t_good_output['CORE'] ?>
			</table>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Free Trials - SS</h2></b>
		</td>
	</tr>
	<tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $t_good_output['SS'] ?>
			</table>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Free Trials - CSI</h2></b>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $t_good_output['CSI'] ?>
			</table>
		</td>
	</tr>

        <tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                        <img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
                </td>
        </tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                <b><h2>Free Trials - SA</h2></b>
                </td>
        </tr>

        <tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
                        <table width="100%" cellspacing="0" cellpadding="0">
                                <?= $sHeader . $t_good_output['SA'] ?>
                        </table>
                </td>
        </tr>


	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<br><br>
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Cancelled Sales - VI</h2></b>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $bad_output['CORE'] ?>
			</table>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>
	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Cancelled Sales - SS</h2></b>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $bad_output['SS'] ?>
			</table>
		</td>
	</tr>

	<tr><td colspan="<?= $iColspan ?>" width="100%"><br><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
		</td>
	</tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%">
		<b><h2>Cancelled Sales - CSI</h2></b>
		</td>
	</tr>

	<tr><td><br></td></tr>

	<tr>
		<td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<?= $sHeader . $bad_output['CSI'] ?>
			</table>
		</td>
	</tr>

        <tr><td><br><br></td></tr>
        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                        <img src="../sales/gfx/orangebottom.gif" width="100%" height="1">
                </td>
        </tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%">
                <b><h2>Cancelled Sales - SA</h2></b>
                </td>
        </tr>

        <tr><td><br></td></tr>

        <tr>
                <td colspan="<?= $iColspan ?>" width="100%" style="padding-left: 10px;">
                        <table width="100%" cellspacing="0" cellpadding="0">
                                <?= $sHeader . $bad_output['SA'] ?>
                        </table>
                </td>
        </tr>
	<?php } ?>
	<tr>
		<td>
		<br>
		</td>
	</tr>
</table>

<script>
<?= $sJSOut ?>
</script>

<?php
// Output footer
echo HTMLFooter();
?>
