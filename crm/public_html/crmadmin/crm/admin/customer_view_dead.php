<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Revice customers
if ( $task == 'revive' )
{
	// Loop over erresult
        while ( list($cst_id, $value) = each($revive) )
        {
        	if ( $value )
                {
                	mysql_query("update cst set person_id = null, appointment = null where cst_id = '" . $cst_id . "'");
                }
        }
}

// Make criteria
if ( $segment_id )
{
	$criteria = " && segment_id = '" . $segment_id . "'";
}
elseif ( $person_id )
{
	$criteria = " && person_id = '" . $person_id . "'";
}

// Select dead customers
$res = mysql_query("select name, cst.person_id, cst.cst_id, max(added) as last_contact from cst, comments where cst.person_id && !appointment && cst.cst_id = comments.cst_id " . $criteria . " group by cst.cst_id order by " . ( $sort ? $sort : 'name' ));

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="4" width="100%">
                        Revive Customers
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
        	<td width="40%">
                	<a href="customer_view_dead.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=name"><b>Company Name</b></a>
                </td>
                <td width="25%">
                	<a href="customer_view_dead.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=person_id"><b>Sales Person</b></a>
                </td>
                <td width="25%">
                	<a href="customer_view_dead.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=last_contact"><b>Last contact</b></a>
                </td>
		<td width="10%">
                	<b>Revive</b>
	        </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <form method="post" action="customer_view_dead.php">
        <input type="hidden" name="person_id" value="<?=htmlspecialchars($person_id)?>">
        <input type="hidden" name="segment_id" value="<?=htmlspecialchars($segment_id)?>">
        <input type="hidden" name="task" value="revive">
                <?php
                // Loop over result
                while ( $row = mysql_fetch_array($res) )
                {
                ?>
        	<tr>
                	<td width="40%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=htmlentities($row['name'])?></a>
                        </td>
                	<td width="25%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=ReturnPersonNameFromID($row['person_id'])?></a>
                        </td>
                	<td width="25%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=$row['last_contact']?></a>
                        </td>
                	<td width="10%">
                        	<input type="checkbox" name="revive[<?=$row['cst_id']?>]" value="1">
                        </td>
                <tr>
                <?php
                }
                ?>
        <tr>
                <td colspan="4" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="3" width="90%">
                </td>
                <td colspan="1" width="10%">
                        <input type="submit" value="Revive">
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br><br>
                </td>
        </tr>
        </form>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
