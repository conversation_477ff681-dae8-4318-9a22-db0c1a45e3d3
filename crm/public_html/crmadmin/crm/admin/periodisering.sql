select
 saleslog.cst_id,
 product_name,
 round( (product_price-discount) * country_currency.currency ) as amount,
 sold_date,
 expires_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''),
 REPLACE(LEFT(sold_date, 7), '-', '')) as months
  from
   saleslog,
   country_currency
  where
   saleslog.currency = country_currency.currency_name &&
   (status is null || status != 3) &&
   product_price > 0 &&
   sold_date >= '2006-01-01' &&
   sold_date < '2007-01-01' &&
   (
     PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''), REPLACE(LEFT(sold_date, 7), '-', '')) > 12 &&
     round( (product_price-discount) * country_currency.currency ) > 50000
   )
  order by sold_date;

