<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Select sales from period
if ( !$_GET['from'] && !$_GET['to'] )
{
	$from = ConvertGetDate('', 'MySQL-Date');
	$to = ConvertGetDate('', 'MySQL-Date');
}
else
{
	$from = $_GET['from'];
	$to = $_GET['to'];
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration', HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="5" width="100%">
                        Extract Sales Data
                </td>
        </tr>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
        <form action="extract_sales_data_text.php" method="GET">
        <tr>
                <td colspan="5" width="100%">
                        Select country to display<br>
<select name="lang_id">
<option value="0" <?=( !$_GET['this_lang_id'] ? "selected" : "" )?>>Show All</option>
<?php
$l_res = mysql_query("select * from vuln_track.language order by lang_id");
while ( $l_row = mysql_fetch_array($l_res) )
{
?>
	<option value="<?=$l_row['lang_id']?>" <?=( $_GET['this_lang_id'] == $l_row['lang_id'] ? "selected" : "" )?>><?=$l_row['lang_name']?></option>
<?php
}
?>
</select>
                        <br>
                        Select period to display<br>
			<input type="text" value="<?=htmlspecialchars($from)?>" name="from"> - <input type="text" value="<?=htmlspecialchars($to)?>" name="to"><br>
                        <br>
                        Select sales person<br>
                        <select name="person_id">
                        <option value="0">Optional - Not Selected</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from salespeople order by name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
                        }
                        ?>
                        </select> - <input type="submit" value="Display">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
