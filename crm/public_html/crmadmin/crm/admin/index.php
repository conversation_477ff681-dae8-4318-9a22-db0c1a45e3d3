<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<br>
<table width="600" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Segments
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="segment_new.php">New Segments</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="segment_update_list.php">Update/Delete Segments</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Sales
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sale_report.php">Sale Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="new_sale_report.php">New Sale Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="report_cst_sales_overview_csv.php">Sale Report - Generate CSV File</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
                <td width="575">
			<a href="upsell.php">Upsell / Resale Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="card_activity.php">Card Activity</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sale_report_type_update.php">Sale Report Type Update</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sale_report_accounting.php">Sale Report ( Accounting )</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="forecast_report.php">Forecast Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="delete_offer.php">Delete Offer</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="offers_report.php">Offers Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="forecast_setup.php">Targets Setup / Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="lead_report.php">ISA Targets Setup / Report</a>
		</td>
	</tr>
        <tr>
               	<td width="25">
                       	&nbsp;
            	</td>
                <td width="575">
                       	<a href="isa_salary.php">ISA Salary</a>
                </td>
        </tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sale_yearly_report.php">Yearly Sales Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sales_loss.php">Sales Loss Report</a>
		</td>
	</tr>
        <tr>
                <td colspan="2" width="600">
                        <br>
                </td>
        </tr>
        <tr>
                <td class="MenuHeadline" colspan="2" width="600">
                        General
                </td>
        </tr>
        <tr>
                <td>
                        &nbsp;
                </td>
                <td>
                        <a href="contact_list.php">Internal Contact List</a>
                </td>
        </tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Sales People
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sp_appointment_list.php">Appointment Lists</a>
		</td>
	</tr>
        <tr>
                <td width="25">
                       	&nbsp;
                </td>
                <td width="575">
                       	<a href="sp_customers.php">Current Salespersons Customers</a>
               	</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="customer_list.php">Customer Lists</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sp_stats.php">Month by Month Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sp_segment_add.php">Add to Segment</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="comments.php">View Comments</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="inbound_leads_report.php">View Inbound Leads Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="lead_subscription.php">Subscribe Sales Person to Inbound Leads</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sss_account_overview.php">Surveillance Scanner Account Overview</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="cdr_stats.php">Phone Statistics</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="cdr_report.php">Phone Report</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Accounting
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts/sale_set_status_new.php">Set Status For A Sale</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts/sp_commision.php">Commision</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts/commission_overview.php">Commision Level Overview</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts/currency_exchange_rates.php">Currency exchange rates</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Customers
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="customer_move.php">Move Customers</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="customer_revive.php">Revive Customers</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="customers_compare.php">Compare Customers</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Miscellaneous
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="new_mail.php">New Mail Templates</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="update_mails.php">Update Mail Templates</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="crmmessage/update_message.php">Update CRM Message</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Extract
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="extract_sales_data.php">Extract Sales Data</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			TP:
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="tmp_register_country.php">Set country</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts_scan_count.php">Customer Vulnerability Scan Counts</a>
		</td>
	</tr>
        <tr>
                <td width="25">
                        &nbsp;
                </td>
                <td width="575">
                        <a href="nsi_account_overview.php">CSI Scans and License Usage</a>
                </td>
        </tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			HZ:
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="accounts_sd_count.php">Accounts Software Detections</a>
		</td>
	</tr>
</table>
<br>
<br>
<?php
// Output footer
echo HTMLFooter();
?>
