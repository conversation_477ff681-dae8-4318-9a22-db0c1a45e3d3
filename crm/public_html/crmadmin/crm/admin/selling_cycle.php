<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$choises = array(
//	array(strtotime('2002-10-01'), strtotime('2002-12-31'), '2002 Q4'),
//	array(strtotime('2003-01-01'), strtotime('2003-03-31'), '2003 Q1'),
//	array(strtotime('2003-04-01'), strtotime('2003-06-30'), '2003 Q2'),
//	array(strtotime('2003-07-01'), strtotime('2003-09-30'), '2003 Q3'),
//	array(strtotime('2003-10-01'), strtotime('2003-12-31'), '2003 Q4'),
//	array(strtotime('2004-01-01'), strtotime('2004-03-31'), '2004 Q1'),
//	array(strtotime('2004-04-01'), strtotime('2004-06-30'), '2004 Q2'),
//	array(strtotime('2004-07-01'), strtotime('2004-09-30'), '2004 Q3'),
//	array(strtotime('2004-10-01'), strtotime('2004-12-31'), '2004 Q4'),
//	array(strtotime('2005-01-01'), strtotime('2005-03-31'), '2005 Q1'),
//	array(strtotime('2005-04-01'), strtotime('2005-06-30'), '2005 Q2'),
//	array(strtotime('2005-07-01'), strtotime('2005-09-30'), '2005 Q3'),
//	array(strtotime('2005-10-01'), strtotime('2005-12-31'), '2005 Q4'),
//	array(strtotime('2006-01-01'), strtotime('2006-03-31'), '2006 Q1'),
//	array(strtotime('2006-04-01'), strtotime('2006-06-30'), '2006 Q2'),
//	array(strtotime('2006-07-01'), strtotime('2006-09-30'), '2006 Q3'),
//	array(strtotime('2006-10-01'), strtotime('2006-12-31'), '2006 Q4')
	array(strtotime('2002-01-01'), strtotime('2002-12-31'), '2002'),
	array(strtotime('2003-01-01'), strtotime('2003-12-31'), '2003'),
	array(strtotime('2004-01-01'), strtotime('2004-12-31'), '2004'),
	array(strtotime('2005-01-01'), strtotime('2005-12-31'), '2005'),
	array(strtotime('2006-01-01'), strtotime('2006-12-31'), '2006')
);

// Loop over each Quarter
while ( list($key, $data) = each($choises) )
{
	$query = "select * from saleslog" . ( $_GET['invoice_country'] ? ', cst' : '' ) . " where ((product_price - discount) > 0)
&& (status != 3 || status is NULL)
&& ( sold_date >= '" . date('Y-m-d', $data[0]) . "' && sold_date <= '" . date('Y-m-d', $data[1]) . "' )
" . ( $_GET['invoice_country'] ? ' && saleslog.cst_id = cst.cst_id && invoice_country ' . ( $_GET['invoice_country_rev'] ? 'not' : '' ) . ' in(' . preg_replace('[^ , 0-9]*', '', $_GET['invoice_country']) . ')' : '' ) . "
order by saleslog.lang_id";
	$res = mysql_query($query);
	while ( $row = mysql_fetch_array($res) )
	{
		// Must be paid or newer than 60 days!!
		if ( $row['status'] != 2 ) // Not paid?
		{
			// If not yet paid, then the sale must be sold less than 63 days ago!
			$diff = ceil((time() - strtotime($row['sold_date'])) / 86400);
			if ( $diff > 63 )
			{
//				echo '<a href="/crmadmin/crm/sales/customer.php?cst_id=' . $row['cst_id'] . '">' . $row['sale_id'] . '</a>' . " - Not Paid - and sold " . $diff . " days ago!<br>";
//				continue;
			}
		}

		if ( $done[$row['cst_id']] )
		{
			continue;
		}
		$done[$row['cst_id']] = true;

		if ( $row['lang_id'] == 8 )
		{
			$row['lang_id'] = 1;
		}

		// Include 
		if ( $_GET['esms'] && !stristr($row['product_name'], 'Server') )
		{
			continue;
		}
		elseif ( !$_GET['esms'] && stristr($row['product_name'], 'Server') )
		{
			echo "Skipped: ESMS";
			continue;
		}

		// First comment
		$a_res = mysql_query("select min(added) as start from comments where cst_id = '" . $row['cst_id'] . "' && person_id > 0");
		$a_row = mysql_fetch_array($a_res);
		$cycle = ceil((strtotime($row['sold_date']) - strtotime(substr($a_row['start'], 0, 10))) / 86400);
		$cycle = ( $cycle > 0 ? $cycle : 0 );
	
		// Data
		if ( $_GET['by_product'] )
		{
			$totaldays[$row['product_type']] += $cycle;
			$totalcount[$row['product_type']]++;
		}
		else
		{
			$totaldays += $cycle;
			$totalcount++;
		}

//if ( $data[2] == '2006 Q4' )
//	echo '<a href="/crmadmin/crm/sales/customer.php?cst_id=' . $row['cst_id'] . '">' . $row['cst_id'] . '</a>' . ' -> ' . $cycle . "<br>";
	}

	if ( $_GET['by_product'] )
	{
		// Loop through each product type
		while ( list($product_type, $value) = each($totaldays) )
		{
			$result .= '<tr><td>' . $data[2] . ' - "' . $product_type . '"</td><td>' . round($value / $totalcount[$product_type]) . '</td><td>' . $value . ' / ' . $totalcount[$product_type] . '</td></tr>';
		}
		$totaldays = array();
		$totalcount = array();
	}
	else
	{
		$result .= '<tr><td>' . $data[2] . '</td><td>' . round($totaldays / $totalcount) . '</td><td>' . $totalcount . ' / ' . $totaldays . '</td></tr>';

		$totalcount = 0;
		$totaldays = 0;
	}
}

?>
<br>
<br>
<br>
<table width="100%" cellpadding="0" cellspacing="0">

<tr>
	<td width="25%"><b>Period</b></td>
	<td width="25%"><b>Average</b></td>
	<td width="50%"><b>Ratio</b></td>
</tr>

<?=$result?>

</table>
