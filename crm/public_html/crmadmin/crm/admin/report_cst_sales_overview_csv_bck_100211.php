<?php
// Initialise (function libraries and openening DB connection)
/*require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

require("../../crm2/sales/configuration.php");
require("../../crm2/sales/global_functions.php");*/


// Load configuration
include '../../crm2/sales/configuration.php';

// Load sales functions
include '../../crm2/sales/sales_functions.php';

// Load global functions
include '../../crm2/sales/global_functions.php';

// Open DB Connection
fOpenDatabase();

$_GET['from'] = '2011-01-01';
$_GET['to'] = '2011-12-31';

$allSales = DBQueryGetRows("SELECT
 cst.name,
 saleslog.cst_id,
 saleslog.sale_type,
 product_name,
 product_category,
 product_type,
 saleslog.person_id,
 ROUND( (product_price-discount) * currency_exchange_rate / currency_exchange_rate_euro ) AS amount_euro,
 (product_price-discount) as amount_currency,
 currency,
 sold_date,
 expires_date,
 invoice_start_date,
 PERIOD_DIFF(REPLACE(LEFT(expires_date, 7), '-', ''), REPLACE(LEFT(sold_date, 7), '-', '')) AS months
  FROM
   saleslog,
   cst
  WHERE
   ( (status != 3 && status != 4 && status != 6 && status != 7) || status is null ) &&
   (product_price-discount) > 0
   " . ( $_GET['from'] && $_GET['to'] ? " && sold_date >= '" . $_GET['from'] . "' && sold_date <= '" . $_GET['to'] . "'" : '' ) . "
   && cst.cst_id = saleslog.cst_id
  ORDER BY sold_date;");

// Loop through array and generate data array in accordance with specs.
$dates = array();
while ( list($iKey, $sale) = each($allSales) ) {
	// Get customer details
        $x_res = mysql_query("select * from cst where cst_id = '" . $sale['cst_id'] . "' limit 1");
        $x_row = mysql_fetch_array($x_res);

        // Build list of all customer id's
        if ( $x_row['master_id'] ) {
                $iMasterID = $x_row['master_id'];
        } else {
                $iMasterID = $x_row['cst_id'];
        }

        $sCustomerIDs = $iMasterID . ',';
        $rAll = mysql_query("SELECT * from crm.cst WHERE master_id = '" . $iMasterID . "'");
        while ( $aAll = mysql_fetch_array($rAll) ) {
                $sCustomerIDs .= $aAll['cst_id'] . ',';
        }

	// Recurrence 
	$res = mysql_query("select * from saleslog where sold_date < '" . $sale['sold_date'] . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $sale['product_category'] . "'");
	if ( mysql_num_rows($res) > 0 ) {
		$sale['type'] = 'Recurrence';
	} else {
		$sale['type'] = 'New';
	}

	// Lowest 'entry date'
	if ( !$dates[$iMasterID . '-' . $sale['product_category']]['entry_date'] || $dates[$iMasterID . '-' . $sale['product_category']]['entry_date'] > $sale['sold_date'] ) {
		$dates[$iMasterID . '-' . $sale['product_category']]['entry_date'] = $sale['sold_date'];
	} 

	// Highest 'exit date'
	if ( $dates[$iMasterID . '-' . $sale['product_category']]['exit_date'] < $sale['expires_date'] ) {
		$dates[$iMasterID . '-' . $sale['product_category']]['exit_date'] = $sale['expires_date'];
	}

	// Active or lost
	$res = mysql_query("select * from saleslog where expires_date > NOW() && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $sale['product_category'] . "'");
	if ( mysql_num_rows($res) > 0 ) {
		$sale['state'] = 'Active';
	} else {
		$res = mysql_query("select * from saleslog where expires_date > NOW() && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && (status is null || (status != 3 && status != 4)) && product_category = '" . $sale['product_category'] . "'");
		if ( mysql_num_rows($res) > 0) {
			$sale['state'] = 'Not paid';
		} else {
			$sale['state'] = 'Lost';
		}
	}

	$sale['short_name'] = $GLOBALS['aProductTypes'][$sale['product_type']]['short_name'];
	$sale['product_group'] = $GLOBALS['aProductTypes'][$sale['product_type']]['container'];
	$sale['product_segment'] = $GLOBALS['aProductTypes'][$sale['product_type']]['segment'];

	// Add to array
	$result[$iMasterID][] = $sale;
}

// Output
/*echo '<table cellspacing="0" cellpadding="0">';
echo "<tr>
<td><b>CSTID</b></td>
<td><b>Name</b></td>
<td><b>Industry</b></td>
<td><b>Segment</b></td>
<td><b>Region</b></td>
<td><b>Country</b></td>
<td><b>Product Name</b></td>
<td><b>Product Short</b></td>
<td><b>Product Group</b></td>
<td><b>Sold Date</b></td>
<td><b>Expires Date</b></td>
<td><b>Sold Year</b></td>
<td><b>Quarter</b></td>
<td><b>Duration in Months</b></td>
<td><b>Amount EURO</b></td>
<td><b>Amount Local</b></td>
<td><b>Local Currency</b></td>
<td><b>Sales Rep.</b></td>
<td><b>Unit (Floating)</b></td>
<td><b>New / Recurrence</b></td>
<td><b>Status</b></td>
</tr>";*/
while ( list( $cstID, $sales ) = each( $result ) ) {
	while ( list($key, $sale) = each( $sales ) ) {
		echo $cstID . "\t" . $sale['cst_id'] . "\t" . $sale['product_name'] . "\t" . $sale['product_category'] . "\t" . $sale['sold_date'] . "\t" . $sale['expires_date'] . "\t" . $sale['invoice_start_date'] . "\t" . $sale['months'] . "\t" . $sale['amount_euro'] . "\t" . $sale['amount_currency'] . "\t" . $sale['currency'] . "\t" . $sale['type'] . "\t" . $sale['state'] . "\t" . fCountryNameFromID( DBGetRowValue('crm.cst', 'invoice_country', "cst_id = '" . $cstID . "'") ) . "\t" . DBGetRowValue('crm.salespeople, crm.salesteams', 'salesteams.short', "person_id = '" . $sale['person_id'] . "' && salespeople.department = salesteams.team_id") . "\t" . fReturnRepDetailFromID( $sale['person_id'] ) . "\t" . ( $dates[$cstID . '-' . $sale['product_category']]['entry_date'] ? $dates[$cstID . '-' . $sale['product_category']]['entry_date'] : '' ) . "\t" . ( $dates[$cstID . '-' . $sale['product_category']]['exit_date'] ? $dates[$cstID . '-' . $sale['product_category']]['exit_date'] : '' ) . "\t" . ( $sale['expires_date'] < $dates[$cstID . '-' . $sale['product_category']]['exit_date'] ? 1 : 0 ) . "\t" . $sale['sale_type'] . "\n";
//		$dates[$cstID]['entry_date'] = null;
//		$dates[$cstID]['exit_date'] = null;
/*		echo "<tr><td>" . $cstID . "</td><td>" .
DBGetRowValue('crm.cst', 'name', "cst_id = '" . $cstID . "'") . "</td><td>" .
$GLOBALS['aIndustries'][DBGetRowValue('crm.cst', 'company_industry', "cst_id = '" . $cstID . "'")] . "</td><td>" .
$sale['product_segment'] . "</td><td>" .
fReturnRegionDetailed( $cstID ) . "</td><td>" .
fCountryNameFromID( DBGetRowValue('crm.cst', 'invoice_country', "cst_id = '" . $cstID . "'") ) . "</td><td>" .
$sale['product_name'] . "</td><td>" .
$sale['short_name'] . "</td><td>" .
$sale['product_group'] . "</td><td>" .
$sale['sold_date'] . "</td><td>" .
$sale['expires_date'] . "</td><td>" .
substr($sale['sold_date'], 0, 4) . "</td><td>" .
'Q' . (floor(date('m', strtotime($sale['sold_date'])) / 3.1) + 1) . "</td><td>" .
$sale['months'] . "</td><td>" .
$sale['amount_euro'] . "</td><td>" .
$sale['amount_currency'] . "</td><td>" .
$sale['currency'] . "</td><td>" .
fReturnRepDetailFromID( $sale['person_id'] ) . "</td><td>" .
DBGetRowValue('crm.salespeople, crm.salesteams', 'salesteams.short', "person_id = '" . $sale['person_id'] . "' && salespeople.department = salesteams.team_id")  . "</td><td>" .
$sale['type'] . "</td><td>" .
$sale['state'] . "</td><tr>";*/
	}
}
//echo "</table>";

?>
