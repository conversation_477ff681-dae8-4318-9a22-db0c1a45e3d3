<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Save data
if ( $submitted == 'Save' )
{
        // Write to vendor table
        mysql_query("insert into mail_templates (title, body, lang_id, prod_limit) values('" . $title . "', '" . $body . "', '" . $lang_id . "', 5)");
}

// Output HTML header
echo HTM<PERSON>Header('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        New mail template
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <form method="POST" action="new_mail.php">
        <tr>
                <td colspan="1" width="15%" valign="top">
                        Language:
                </td>
                <td colspan="1" width="85%">
                        <?=DisplayLanguageRadioChoices();?>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">
                        Title:
                </td>
                <td colspan="1" width="85%">
                        <input type="text" name="title" size="50">
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%" valign="top">
                        Content:
                </td>
                <td colspan="1" width="85%">
                        <textarea name="body" cols="100" rows="30"></textarea>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">
                </td>
                <td colspan="1" width="85%">
                        ##company_name## = Company Name<br>
                        ##company_contact## = Company Contact<br>
                        ##company_contact_email## = Company Contact E-mail<br>
                        <br>
                        ##product_name## = Product Name<br>
                        ##product_price## = Product Price<br>
                        ##product_period## = Product Period<br>
                        <br>
                        ##salesperson_name## = Salesperson Name<br>
                        ##salesperson_title## = Salesperson Title<br>
                        ##salesperson_email## = Salesperson E-mail<br>

                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">

                </td>
                <td colspan="1" width="85%">
                        <input type="submit" name="submitted" value="Save">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
