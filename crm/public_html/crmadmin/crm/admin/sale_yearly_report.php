<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');
?>
<b><h2>Yearly Sales Report</h2></b>
<?php
function fnGetMasterID($iCstID){
	$rQuery = mysql_query("SELECT master_id FROM crm.cst WHERE cst_id=".$iCstID);
	$aData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	if (is_numeric($aData['master_id'])){
		//If this is not final master ID
		$iReturnID = fnGetMasterID($aData['master_id']);
	} else {
		$iReturnID = $iCstID;
	}
	return $iReturnID;
}

function fnGetCstName($iCstID){
	$rQuery = mysql_query("SELECT name FROM cst WHERE cst_id=".$iCstID);
	$aData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	return $aData['name'];
}

function fnGetCstStatus($iCstID){
	$iToday = mktime(0, 0, 0);
	if ($GLOBALS['aExpire'][$iCstID] >= $iToday){
		return "<span style=\"color: #00DD00;\">Active</span>";
	} else {
		return "<span style=\"color: #DD0000;\">Inactive</span>";
	}
}

function fnGetExpiredCustomers($iYear){
	$aExpireKeys = array_keys($GLOBALS['aExpire']);
	$iReturnExpire = 0;
	for($i=0;$i<sizeof($aExpireKeys);$i++){
		$sExpire = $GLOBALS['aExpire'][$aExpireKeys[$i]];
		if (substr($sExpire, 0, 4) == $iYear) $iReturnExpire++;
	}
	return $iReturnExpire;
}

$iStartYear = 2002;
$iEndYear = strftime("%Y");
$rQuery = mysql_query("SELECT * FROM saleslog WHERE (product_price-discount)>0 AND (status = 2 OR status = 5 OR status IS NULL) ORDER BY sold_date ASC");
$aCstIDs = array();
while($aData = mysql_fetch_array($rQuery, MYSQL_ASSOC)){
	$iCstID = fnGetMasterID($aData['cst_id']);
	if (!in_array($iCstID, $aCstIDs)){
		$iCurrentYear = substr($aData['sold_date'], 0, 4);
		$aCstIDs[] = $iCstID;
		$aYears[$iCstID] = $iCurrentYear;
	} else {
		$iCurrentYear = $aYears[$iCstID];
	}
	
	$aSales[$iCurrentYear][$iCstID][] = array(
		'product_name' => $aData['product_name'],
		'product_price' => $aData['product_price']-$aData['discount'],
		'period_start' => $aData['sold_date'],
		'period_end' => $aData['expires_date'],
		'currency_exchange_rate' => $aData['currency_exchange_rate'],
		'currency_exchange_rate_euro' => $aData['currency_exchange_rate_euro']
	);
	
	list($iYear, $iMonth, $iDay) = explode("-", $aData['expires_date']);
	$iExpire = mktime(0, 0, 0, $iMonth, $iDay, $iYear);
	if ($iExpire > $GLOBALS['aExpire'][$iCstID]){
		$GLOBALS['aExpire'][$iCstID] = $iExpire;
	}
	
	$fPrice = $aData['product_price']-$aData['discount'];
	$aTotals[$iCstID] = $aTotals[$iCstID] + ( (float) ( $aData['product_price'] - $aData['discount'] ) ) * fFOREX( $aData['currency_exchange_rate'], $aData['currency_exchange_rate_euro'] );
	
	$iCurrentYear = substr($aData['sold_date'], 0, 4);
	$aYearTotals[$iCurrentYear] = $aYearTotals[$iCurrentYear] + ( (float) ( $aData['product_price'] - $aData['discount'] ) ) * fFOREX( $aData['currency_exchange_rate'], $aData['currency_exchange_rate_euro'] );
}

for($iCurrentYear=$iStartYear;$iCurrentYear<($iEndYear+1);$iCurrentYear++){
	?>
	<h3><?=$iCurrentYear;?></h3>
	<table width="400">
		<tr>
			<td colspan="2"><b>Summary</b></td>
		</tr>
		<tr>
			<td>&nbsp;&nbsp;New customers</td>
			<td><?=number_format(sizeof($aSales[$iCurrentYear]), 0, ".", ",");?></td>
		</tr>
		<tr>
			<td>&nbsp;&nbsp;Expiring customers</td>
			<td><?=number_format(fnGetExpiredCustomers($iCurrentYear), 0, ".", ",");?></td>
		</tr>
		<tr>
			<td>&nbsp;&nbsp;Total revenue</td>
			<td>&euro; <?=number_format($aYearTotals[$iCurrentYear], 2, ".", ",");?></td>
		</tr>
	</table><br>
	<br>
	<?php
	$aCstIDS = array_keys($aSales[$iCurrentYear]);
	
	for($i=0;$i<sizeof($aCstIDS);$i++){
		$iCstID = $aCstIDS[$i];
		echo "<table width=\"800\">
				<tr>
					<td width=\"500\" colspan=\"4\"><b>".fnGetCstName($iCstID)."</b></td>
				</tr>";
		foreach($aSales[$iCurrentYear][$iCstID] as $aSale){
			echo "<tr>
					<td width=\"500\">&nbsp;&nbsp;".$aSale['product_name']."</td>
					<td width=\"100\" style=\"text-align: right;\">&euro; ".number_format(( (float) ( $aSale['product_price'] - $aSale['discount'] ) ) * fFOREX( $aSale['currency_exchange_rate'], $aSale['currency_exchange_rate_euro'] ), 2, ".", ",")."</td>
					<td width=\"100\" style=\"text-align: right;\">".$aSale['period_start']."</td>
					<td width=\"100\" style=\"text-align: right;\">".$aSale['period_end']."</td>
				</tr>";
		}
		echo "<td>&nbsp;</td><td width=\"100\" style=\"text-align: right;\"><b>&euro; ".number_format($aTotals[$iCstID], 2, ".", ",")."</b></td>
					<td colspan=\"2\" style=\"text-align: right;\"><b>".fnGetCstStatus($iCstID)."</b></td>";
		echo "</table><br>";
	}
}
?>
