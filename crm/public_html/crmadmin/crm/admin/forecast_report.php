<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Date stamps
$month_from = date('Y-m-1');
$month_to = date('Y-m-31');
$q_from = ( ( date('m') / 3 ) >= 1 ? date('Y-' . ( (floor( (date('m')-1) / 3) * 3) + 1 ) . '-1') : date('Y-1-1') );
$q_to = date('Y-' . (ceil(date('m') / 3) * 3) . '-31');

// Variables
$people = '';
$expires = '';
$person_offers = '';
$result = '';
$iTotalForecasted = 0;
$iTotalCalculated = 0;
$iTotalCustomers = 0;

// Build result
if ( $_GET['submitted'] )
{
	// Period?
	switch ( $_GET['period'] )
	{
		// This Month
		case 1:
			$expires = "&& ( ( forecast_date >= '" . $month_from . "' && forecast_date <= '" . $month_to . "' )";
			break;

		// This Q
		case 2:
			$expires = "&& ( ( forecast_date >= '" . $q_from . "' && forecast_date <= '" . $q_to . "' )";
			break;

		// User input
		case 3:
			$expires = "&& ( ( forecast_date >= '" . $_GET['from'] . "' && forecast_date <= '" . $_GET['to'] . "' )";
			break;
	}

	// Sales People
	{
		if ( !$_GET['sp_all'] )
		{
			// Limit on salespeole
			while ( list($key, $person_id) = @each($_GET['salespeople']) )
			{
				$people .= " cst.person_id = '" . $person_id . "' || ";
			}
		}
	}

	// Generate HTML output
	$rRes = mysql_query("
	SELECT
	 cst.cst_id,
	 cst.name,
	 cst.person_id,
	 cst.forecast_amount,
	 cst.forecast_expectancy,
	 cst.forecast_date,
	 max(comments.added) as added
	FROM
	 crm.cst,
	 crm.comments
	WHERE
	 cst.forecast_amount > 0 &&
	 (
	  cst.customer_marked_dead != 3 ||
	  cst.customer_marked_dead is null
	 ) &&
	 cst.cst_id = comments.cst_id &&
	 (" . substr($people, 0, -4) . ")
	 " . ( $expires ? $expires . ( $_GET['display_without_date'] == 1 ? ' || forecast_date is null || !forecast_date ) ' : ')' ) : '' ) . "
	 " . ( count($_GET['expectancy']) > 0 ? " && forecast_expectancy IN(" . implode(',', $_GET['expectancy']) . ")" : '' ) . "
	 " . $sSQLAccessLimit . "
	GROUP BY
	 cst.cst_id
	ORDER BY
	 cst.person_id
	");
echo mysql_error();
	while ( $aRow = mysql_fetch_array($rRes) )
	{
		// Select from forecast_log, to determine if this is a new or an up/down-graded forecast
		$rLogRes = mysql_query("SELECT * FROM forecast_log WHERE cst_id = '" . $aRow['cst_id'] . "' ORDER BY logged DESC LIMIT 1,1");
		$aLogRow = mysql_fetch_array($rLogRes);

		// Was Amount changed
		if ( $aLogRow['amount'] )
		{
			if ( $aLogRow['amount'] > $aRow['forecast_amount'] )
			{
				$sForecastAmountChange = '<img src="/crmadmin/crm/sales/gfx/sort_down.gif" alt="" sortatt="' . $aRow['forecast_amount'] . '"> ';
			}
			elseif ( $aLogRow['amount'] < $aRow['forecast_amount'] )
			{
				$sForecastAmountChange = '<img src="/crmadmin/crm/sales/gfx/sort_up.gif" alt="" sortatt="' . $aRow['forecast_amount'] . '"> ';
			}
			else
			{
				$sForecastAmountChange = '';
			}
			
		}
		else
		{
			$sForecastAmountChange = '';
		}

		// Was Expectancy changed
		if ( $aLogRow['expectancy'] )
		{
			if ( $aLogRow['expectancy'] > $aRow['forecast_expectancy'] )
			{
				$sForecastExpectancyChange = '<img src="/crmadmin/crm/sales/gfx/sort_down.gif" alt="" sortatt="' . $aRow['forecast_expectancy'] . '"> ';
			}
			elseif ( $aLogRow['expectancy'] < $aRow['forecast_expectancy'] )
			{
				$sForecastExpectancyChange = '<img src="/crmadmin/crm/sales/gfx/sort_up.gif" alt="" sortatt="' . $aRow['forecast_expectancy'] . '"> ';
			}
			else
			{
				$sForecastExpectancyChange = '';
			}
			
		}
		else
		{
			$sForecastExpectancyChange = '';
		}

		// HTML Output
		$result .= '
		<tr onMouseOver="this.setAttribute(\'class\', \'hoverTRPointer\');" onMouseOut="this.setAttribute(\'class\', \'noHoverTRPointer\');" onClick="window.open(\'/crmadmin/crm/sales/customer.php?cst_id=' . $aRow['cst_id'] . '\', \'_blank\')">
			<td valign="top">' . ReturnPersonNameFromID($aRow['person_id']) . '</td>
			<td valign="top">' . htmlspecialchars($aRow['name']) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . $sForecastAmountChange . number_format(htmlspecialchars($aRow['forecast_amount'])) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . $sForecastExpectancyChange . ( $aRow['forecast_expectancy'] < 101 ? htmlspecialchars($aRow['forecast_expectancy']) : 'LOST' ) . '</td>
			<td valign="top" align="right" style="padding-right: 15px;">' . ( $aRow['forecast_expectancy'] < 101 ? number_format(htmlspecialchars($aRow['forecast_amount'] * ($aRow['forecast_expectancy'] / 100))) : 0 ) . '</td>
			<td valign="top">' . htmlspecialchars($aRow['forecast_date']) . '</td>
			<td valign="top">' . htmlspecialchars($aRow['added']) . '</td>
		</tr>';

		// Totals
		$iTotalCustomers++;
		$iTotalForecasted += $aRow['forecast_amount'];
		$iTotalCalculated += ( $aRow['forecast_expectancy'] < 101 ? $aRow['forecast_amount'] * ($aRow['forecast_expectancy'] / 100) : 0 );
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php', 'forecast_report.js');
?>

<br>
<form method="GET" action="forecast_report.php">
<input type="hidden" name="submitted" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="7"><b>Forecast Report</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2" valign="top">
			<b>Select Sales Person(s):</b><br>
			<?= returnSelectBox($_GET['salespeople'], 'salespeople[]', true, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name'); ?>
		</td>
		<td colspan="3" valign="top">
			<b>Select Period</b><br>
			<label><input type="radio" name="period" value="1" style="width: 15px;" <?=( $_GET['period'] == 1 ? 'checked' : '' )?>> This Month (<?= $month_from ?> - <?= $month_to ?>)</label><br>
			<label><input type="radio" name="period" value="2" style="width: 15px;" <?=( $_GET['period'] == 2 ? 'checked' : '' )?>> This Quarter (<?= $q_from ?> - <?= $q_to ?>)</label><br>
			<label><input type="radio" name="period" value="3" style="width: 15px;" <?=( $_GET['period'] == 3 ? 'checked' : '' )?>> Other Period: </label><input type="text" name="from" style="width: 100px;" value="<?= ( $_GET['from'] ? htmlspecialchars($_GET['from']) : date('Y-m-d') ) ?>"> - <input type="text" name="to" style="width: 100px;" value="<?= ( $_GET['to'] ? htmlspecialchars($_GET['to']) : date('Y-m-d') ) ?>">
			<br>
			<br>
			<label><input type="checkbox" name="display_without_date" style="width: 15px;" value="1" <?=( $_GET['display_without_date'] == 1 ? 'checked' : '' )?>> Display entries without an expected close date</label>
		</td>
		<td colspan="2" valign="top">
			<b>Select Specific Expectancy</b><br>
			<select name="expectancy[]" MULTIPLE>
				<option value=""> - Display All Expectancy Values - </option>
				<option value="30" <?= ( @in_array(30, $_GET['expectancy'])  ? ' selected' : '' ) ?>>30%</option>
				<option value="40" <?= ( @in_array(40, $_GET['expectancy'])  ? ' selected' : '' ) ?>>40%</option>
				<option value="50" <?= ( @in_array(50, $_GET['expectancy'])  ? ' selected' : '' ) ?>>50%</option>
				<option value="75" <?= ( @in_array(75, $_GET['expectancy'])  ? ' selected' : '' ) ?>>75%</option>
				<option value="90" <?= ( @in_array(90, $_GET['expectancy'])  ? ' selected' : '' ) ?>>90%</option>
				<option value="100"<?= ( @in_array(100, $_GET['expectancy']) ? ' selected' : '' ) ?>>100% (WON)</option>
				<option value="101"<?= ( @in_array(101, $_GET['expectancy']) ? ' selected' : '' ) ?>>LOST</option>
			</select>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="7">
			<input type="submit" value="Display Report" style="width: 25%" class="submit">
		</td>
	</tr>

	<?= returnPageDelimeter(7); ?>

	<tr>
		<td width="13%" onClick="oSortCustomers.mSort(this, 0, 'string');" class="imitateLink"><b>Sales Person</b></td>
		<td width="35%" onClick="oSortCustomers.mSort(this, 1, 'string');" class="imitateLink"><b>Company</b></td>
		<td width="10%" onClick="oSortCustomers.mSort(this, 2, 'int');" class="imitateLink"><b>Forecasted (A)</b></td>
		<td width="10%" onClick="oSortCustomers.mSort(this, 3, 'int');" class="imitateLink"><b>Expectancy % (B)</b></td>
		<td width="10%" onClick="oSortCustomers.mSort(this, 4, 'int');" class="imitateLink"><b>Deal Value (A*B)</b></td>
		<td width="10%" onClick="oSortCustomers.mSort(this, 5, 'date');" class="imitateLink"><b>Expected Closed</b></td>
		<td width="12%" onClick="oSortCustomers.mSort(this, 6, 'date');" class="imitateLink"><b>Last Updated</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>

	<tbody id="customers">
	<?=$result?>
	</tbody>

	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td></td>
		<td><b>Total <?= $iTotalCustomers ?> Customers</b></td>
		<td align="right" style="padding-right: 15px;"><b><?= number_format($iTotalForecasted) ?></b></td>
		<td></td>
		<td align="right" style="padding-right: 15px;"><b><?= number_format($iTotalCalculated) ?></b></td>
		<td></td>
		<td></td>
	</tr>

</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
