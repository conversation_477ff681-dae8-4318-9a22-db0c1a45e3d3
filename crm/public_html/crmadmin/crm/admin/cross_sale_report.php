<?php
error_reporting(0);

// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$base = "status = 2 && (product_price-discount) > 0 && product_category != 2 ";

$done = array();

// Get all paid sales
$sales = DBGetRows('crm.saleslog', $base);

while ( list($i, $sale) = each($sales) ) {
	// Get customer ids
	$cst = fGetCustomerDetails( $sale['cst_id'] );

	if ( $done[$cst['Company']['cst_id']] ) {
		continue;
	}

	// Test for a salie in a different product_category within the same timeperiod as this sale
	$cross = DBGetRow('crm.saleslog', $base . " AND cst_id IN(" . $cst['AllIDs'] . ") AND product_category != '" . $sale['product_category'] . "' AND sold_date >= '" . $sale['sold_date'] . "' && sold_date < '" . $sale['expires_date'] . "'");

	if ( $cross ) {
		echo $sale['sale_id'] . "\t" . $cst['Company']['cst_id'] . "\t" . $cst['Company']['name'] . "\t" . $sale['product_name'] . "\t" . $sale['sold_date'] . "\t" . $sale['expires_date'] . "\t" . round( (($sale['product_price'] - $sale['discount'] ) * $sale['currency_exchange_rate']) / $sale['currency_exchange_rate_euro']) . "\t" . $cross['product_name']  . "\t" . $cross['sold_date'] . "\t" . $cross['expires_date'] . "\t" . round( (($cross['product_price'] - $cross['discount'] ) * $cross['currency_exchange_rate']) / $cross['currency_exchange_rate_euro']) . "\n";
		$done[$cst['Company']['cst_id']] = $sale['sale_id'];
	}
}

?>
