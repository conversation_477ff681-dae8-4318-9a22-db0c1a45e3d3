<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$out = '';

$choises = array(
	array(strtotime('2002-10-01'), strtotime('2002-12-31'), '2002 Q4'),
	array(strtotime('2003-01-01'), strtotime('2003-03-31'), '2003 Q1'),
	array(strtotime('2003-04-01'), strtotime('2003-06-30'), '2003 Q2'),
	array(strtotime('2003-07-01'), strtotime('2003-09-30'), '2003 Q3'),
	array(strtotime('2003-10-01'), strtotime('2003-12-31'), '2003 Q4'),
	array(strtotime('2004-01-01'), strtotime('2004-03-31'), '2004 Q1'),
	array(strtotime('2004-04-01'), strtotime('2004-06-30'), '2004 Q2'),
	array(strtotime('2004-07-01'), strtotime('2004-09-30'), '2004 Q3'),
	array(strtotime('2004-10-01'), strtotime('2004-12-31'), '2004 Q4')
);

// Loop over each Quarter
while ( list($key, $data) = each($choises) )
{
	$result['0 - 3 months'][$data[2]] = 0;
	$result['3 - 6 months'][$data[2]] = 0;
	$result['6 - 12 months'][$data[2]] = 0;
	$result['12 - 24 months'][$data[2]] = 0;
	$result['Longer than 24 months'][$data[2]] = 0;

	$res = mysql_query("select * from saleslog where ((product_price - discount) > 0)
&& (status != 3 || status is NULL)
&& ( sold_date >= '" . date('Y-m-d', $data[0]) . "' && sold_date <= '" . date('Y-m-d', $data[1]) . "' )
order by lang_id");
	while ( $row = mysql_fetch_array($res) )
	{
		// Must be paid or newer than 63 days!!
		if ( $row['status'] != 2 ) // Not paid?
		{
			// If not yet paid, then the sale must be sold less than 63 days ago!
			$diff = ceil((time() - strtotime($row['sold_date'])) / 86400);
			if ( $diff > 63 )
			{
				//echo '<a href="/crmadmin/crm/sales/customer.php?cst_id=' . $row['cst_id'] . '">' . $row['sale_id'] . '</a>' . " - Not Paid - and sold " . $diff . " days ago!<br>";
				continue;
			}
		}

		// Calc value in DKK
		$value = round((strtotime($row['expires_date']) - strtotime($row['sold_date'])) / 86400);

		// Split rules
		if ( $value < 90 )
		{
			$result['0 - 3 months'][$data[2]]++;
		}
		elseif ( $value < 180 )
		{
			$result['3 - 6 months'][$data[2]]++;
		}
		elseif ( $value < 365 )
		{
			$result['6 - 12 months'][$data[2]]++;
		}
		elseif ( $value < 730 )
		{
			$result['12 - 24 months'][$data[2]]++;
		}
		elseif ( $value >= 730 )
		{
			$result['Longer than 24 months'][$data[2]]++;
		}

	}
}

while ( list($q, $data) = each($result) )
{
	// Headline
	$out .= '<tr><td colspan="3"><br></td></tr>';
	$out .= '<tr><td colspan="3"><b>' . $q . '</b></td></tr>';

	// Loop over result
	while ( list($key, $value) = each($data) )
	{
		$out .= '<tr><td> </td><td style="padding-left: 5px;">' . $key . '</td><td>' . $value . '</td></tr>';
	}
}

?>

<table width="100%" cellpadding="0" cellspacing="0">

<?=$out?>

</table>
