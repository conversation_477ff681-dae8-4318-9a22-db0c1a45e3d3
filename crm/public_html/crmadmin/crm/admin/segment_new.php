<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Save data
if ( $submitted == 'Save' )
{
	// Write to vendor table
	mysql_query("insert into segments (segment_name, segment_status, parent_segment_id) values('" . $segment_name . "', 0, '".(int)$_POST['parent_segment_id']."')");
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        New Segment
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <form method="POST" action="segment_new.php">
	<tr>
		<td class="TablePadding">Parent Segment</td>
		<td>
			<?php
			//echo returnSelectBox(array(0), 'parent_segment_id', false, 'select segment_name, segment_id from crm.segments WHERE segment_name like "crm2 - %" AND segment_status = 0', 'segment_id', 'segment_name');
			echo makeSegmentCombo();
			?>
		</td>
	</tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">
                        Name:
                </td>
                <td colspan="1" width="85%">
                        <input type="text" name="segment_name" size="50">
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">

                </td>
                <td colspan="1" width="85%">
                        <input type="submit" name="submitted" value="Save">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
