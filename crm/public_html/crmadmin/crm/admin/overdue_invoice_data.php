<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Set header to display plain text
echo "<table>";

// Select data
$res = mysql_query("select * from saleslog, cst where 

( (product_price - discount) > 0 ) && 
(status is null || status < 2) && 
date_add(sold_date, INTERVAL payment_time DAY) < now() &&
cst.cst_id = saleslog.cst_id 

order by name");

// Loop over result
while ( $row = mysql_fetch_array($res) )
{
	if ( $found[$row['cst_id']] )
	{
		continue;
	}
	$found[$row['cst_id']] = 1;

	// Days overdue
	$overdue = round( ( time() - ( strtotime($row['sold_date']) + ($row['payment_time'] * 86400) ) ) / 86400 );

	// The four types
	if ( $_GET['type'] == 1 && ( $overdue >= 0 && $overdue <= 30 ) )
	{
		// 0-30 days
	}
	elseif ( $_GET['type'] == 2 && ( $overdue > 30 && $overdue <= 60 ) )
	{
		// 31-60 days
	}
	elseif ( $_GET['type'] == 3 && ( $overdue > 60 && $overdue <= 90 ) )
	{
		// 61-90 days
	}
	elseif ( $_GET['type'] == 4 && ( $overdue > 90 ) )
	{
		// 91+ days
	}
	else
	{
		continue;
	}

	// Load template
	if ( $row['lang_id'] == 2 )
	{
		// Require field template
		require("../includes/customer_temp_2.php");

		$address = ( $row[$customer_data['Address 1']] ? $row[$customer_data['Address 1']] . "<br>" : '' ) . ( $row[$customer_data['Address 2']] ? $row[$customer_data['Address 2']] . "<br>" :'' ) . ( $row[$customer_data['Zipcode']] ? $row[$customer_data['Zipcode']] : '' ) . ' ' . ( $row[$customer_data['City']] ? $row[$customer_data['City']] : '' );
	}
	else
	{
		// Require field template
		require("../includes/customer_temp_1.php");

		$address = ( $row[$customer_data['Address 1']] ? $row[$customer_data['Address 1']] . "<br>" : '' ) . ( $row[$customer_data['Address 2']] ? $row[$customer_data['Address 2']] . "<br>" :'' ) . ( $row[$customer_data['State']] ? $row[$customer_data['State']] . "<br>" : '' ) . ( $row[$customer_data['Town']] ? $row[$customer_data['Town']] : '' ) . ' ' . ( $row[$customer_data['Zipcode']] ? $row[$customer_data['Zipcode']] : '' );
	}

	// Select country name
	$l_res = mysql_query("select * from countries where id = '" . $row['invoice_country'] . "' limit 1");
	$l_row = mysql_fetch_array($l_res);

	echo '<tr><td valign="top">' . $row['name'] . '</td><td valign="top">' . $address . '</td><td valign="top">' . $row['contact'] . '</td><td valign="top">' . $row['cst_id'] . '</td><td valign="top">' . $l_row['country'] . "</td></tr>";

}
echo "</table>";

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
        $split = explode('-', $date);
        $date = $split[2] . '-' . $split[1] . '-' . $split[0];

        // Return formatted date
        return $date;
}

?>
