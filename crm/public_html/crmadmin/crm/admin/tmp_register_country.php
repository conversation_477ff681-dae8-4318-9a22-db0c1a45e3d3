<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

// Submited data?
if ( $_POST['s'] )
{
	while ( list($cst_id, $country) = each($_POST['country']) )
	{
		if ( (int) $country > 0 )
		{
			mysql_query("update cst set invoice_country = '" . intval($country) . "' where cst_id = '" . intval($cst_id) . "' limit 1");
		}
	}
}

// select country options
$options = '';
$res = mysql_query("select * from countries order by country");
while ( $row = mysql_fetch_array($res) )
{
	$options .= '<option value="' . $row['id'] . '">' . htmlspecialchars($row['country']) . '</option>';
}

// Select customers and generate output
$output = '';
$javascript = '';
$res = mysql_query("select distinct cst.cst_id, name, field_1, field_2, field_3, field_4, field_5 from cst, saleslog where ((product_price-discount) > 0) && (status in(0,2) || status is null) && cst.cst_id = saleslog.cst_id && (invoice_country is NULL || invoice_country = 0) order by crm_lang_id, name limit 30");
while ( $row = mysql_fetch_array($res) )
{
	// Bgcolor color changer
	if ( $bgcolor == 'e3e3e3' )
	{
		$bgcolor = "ffffff";
	}
	else
	{
		$bgcolor = "e3e3e3";
	}

	$output .= '
	<tr bgcolor="#' . $bgcolor . '">
		<td><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . htmlspecialchars($row['name']) . '</a></td>
		<td>' . htmlspecialchars($row['field_1']) . ', ' . htmlspecialchars($row['field_2']) . ', ' . htmlspecialchars($row['field_4']) . ', ' . htmlspecialchars($row['field_5']) . ', ' . htmlspecialchars($row['field_3']) . '</td>
		<td><select id="' . $row['cst_id'] . '" name="country[' . $row['cst_id'] . ']"><option value="0"> - Select Country -</option>' . $options . '</select></td>
	</tr>';

	$javascript .= 'changeall[mytest++] = ' . $row['cst_id'] . ';' . "\n";
}
?>
<br>
<form method="POST" action="tmp_register_country.php">
<input type="hidden" name="s" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="5">
			Register Country Details to Customers
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="40%"><b>Company Name</b></td>
		<td width="40%"><b>Address Details</b></td>
		<td width="20%"><b>Country</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?=$output?>
<script>
var mytest = 0;
var changeall = Array();

<?=$javascript?>

function ChangeAll(lang_id)
{
	// Loop over all select boxes
	for ( i = 0 ; i < changeall.length ; i++ )
	{
		document.getElementById(changeall[i]).value = lang_id;
	}
}

</script>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="6"><input type="submit" value="Save"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td>Change All:<br>
		<select onChange="ChangeAll(this.value);"><option> - Change All - </option><?=$options?></select></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
