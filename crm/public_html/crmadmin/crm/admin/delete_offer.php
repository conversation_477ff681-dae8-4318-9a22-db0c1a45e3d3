<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Delete
if ( $_GET['delete'] )
{
	mysql_query("delete from offers where id = '" . $_GET['delete'] .  "' limit 1");
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales',  HTTP_PATH.'admin/index.php');
?>
<br>
<form method="GET" action="delete_offer.php">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="100%">Delete Offer</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="40%">Search for:<br><input type="text" name="search" value="<?=htmlspecialchars($search)?>"></td>
		<td width="60%" valign="bottom"><input type="submit" name="submit" class="submit" value="Search"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="2">
		<?php
		// Search
		if ( $_GET['search'] )
		{
			// Output
			echo '<hr><br><b>Result:</b><br><br>';

			// Make search
			$res = mysql_query("select * from cst where name like '%" . $_GET['search'] . "%' order by name");

			// New table
			echo '<table width="100%" cellpadding="0" cellspacing="0">';

			// Loop over result
			while ( $row = mysql_fetch_array($res) )
			{
				// Customer name
				echo '<tr><td style="padding-left: 10px;"><a href="'.HTTP_PATH_CRM2.'customer.php?cst_id=' . $row['cst_id'] . '">' . htmlspecialchars($row['name']) . '</a></td></tr>';

				// Show offers
				$o_res = mysql_query("select * from offers where cst_id = '" . $row['cst_id'] . "' order by expires");
				while ( $o_row = mysql_fetch_array($o_res) )
				{
					echo '<tr><td style="padding-left: 20px;">' . htmlspecialchars($o_row['product']) . ', ' . $o_row['currency'] . $o_row['amount'] . ' [<a href="?delete=' . $o_row['id'] . '&amp;search=' . urlencode($_GET['search']) . '" onClick="return confirm(\'Delete: '. htmlspecialchars($o_row['product']) . '?\');">Delete</a>]</td></tr>';
				}

				// Space
				echo '<tr><td><br></td></tr>';
			}

			// End table
			echo '</table>';
		}
		?>
		</td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
