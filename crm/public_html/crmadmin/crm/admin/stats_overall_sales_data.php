<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Variables
$sp_options = '';
$headline = '';
$table = '';
$end_smg = '';

if ( $_GET['submitted'] )
{
	setcookie('from', $_GET['from'], time()+(86400*365));
	setcookie('to', $_GET['to'], time()+(86400*365));
	setcookie('person_id', $_GET['person_id'], time()+(86400*365));
	setcookie('grouping', $_GET['grouping'], time()+(86400*365));
	setcookie('show_active_period', $_GET['show_active_period'], time()+(86400*365));
	setcookie('show_all_sp', $_GET['show_all_sp'], time()+(86400*365));
}

// Show all SP's ?
if ( $_GET['show_all_sp'] )
{
	// Remove any "person_id"
	$_GET['person_id'] = false;
}

// Select all salespeople - for building the options box
$res = mysql_query("select *  from salespeople order by name");
while ( $row = mysql_fetch_array($res) )
{
	$sp_options .= '<option value="' . $row['person_id'] . '"' . ( $_COOKIE['person_id'] == $row['person_id'] ? ' selected' : '' ) . '>' . htmlspecialchars($row['name']) . '</option>';

	if ( $_GET['person_id'] == $row['person_id'] )
	{
		$person = $row['name'];
	}
}

// Form or Data
if ( !$_GET['submitted'] )
{
	// Generate form

	// Headline
	$headline = 'Overall Sales Data';

	// Form Table
	$table = '
	<form method="GET" action="">
	<input type="hidden" name="submitted" value="1">
	<tr>
		<td colspan="2"><b>Period</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="40%" style="padding-left: 15px;">From:<br>
		<input type="text" name="from" value="' . $_COOKIE['from'] . '"></td>
		<td width="60%">To:<br>
		<input type="text" name="to" value="' . $_COOKIE['to'] . '"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;" colspan="2">
		Ignore period above and show full active period:<br>
		<input type="checkbox" name="show_active_period" value="1"' . ( $_COOKIE['show_active_period'] == 1 ? ' checked' : '' ) . '>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;">Data Grouping:<br>
		<select name="grouping">
			<option value="Days"' . ( $_COOKIE['grouping'] == 'Days' ? ' selected' : '' ) . '>Days</option>
			<option value="Weeks"' . ( $_COOKIE['grouping'] == 'Weeks' ? ' selected' : '' ) . '>Weeks</option>
			<option value="Months"' . ( $_COOKIE['grouping'] == 'Months' ? ' selected' : '' ) . '>Months</option>
			<option value="Years"' . ( $_COOKIE['grouping'] == 'Years' ? ' selected' : '' ) . '>Years</option>
		</select></td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Limit To Sales Person</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;" colspan="2">
		Select Sales Person:<br>
		<select name="person_id">
			<option value="">- Select Sales Person -</option>
			' . $sp_options . '
		</select>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;" colspan="2">
		Display All Sales People:<br>
		<input type="checkbox" name="show_all_sp" value="1"' . ( $_COOKIE['show_all_sp'] == 1 ? ' checked' : '' ) . '>
		</td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td colspan="2"><b>Generate</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 15px;"><input type="submit" value="Generate Data"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	</form>';

	// Display data in template
	require('stats_template.php');
}
else
{
	// Generate Report Data
	list($table, $headline, $end_msg, $width) = GenerateReport($person);

	// Display data in template
	require('stats_template.php');

	// Show all SP's ?
	if ( $_GET['show_all_sp'] )
	{
		// Generate report based on each active SP
		$res = mysql_query("select * from salespeople order by name");
		while ( $row = mysql_fetch_array($res) )
		{
			// Check if this user have ever been active in 'saleslog'
			$c_res = mysql_query("select sale_id from saleslog where person_id = '" . $row['person_id'] . "' limit 1");
			if ( mysql_num_rows($c_res) )
			{
				$_GET['person_id'] = $row['person_id'];

				// Generate Report Data
				list($table, $headline, $end_msg, $width) = GenerateReport($row['name']);

				// Display data in template
				require('stats_template.php');
			}
		}
	}
}

// Function for generating report
function GenerateReport($person)
{
	// Data Grouping
	switch ( $_GET['grouping'] )
	{
		case 'Weeks':
			$last = 'W';
			$period_selector = 'W - Y';
			break;

		case 'Days':
			$last = 'd';
			$period_selector = 'd/m - Y';
			break;

		case 'Months':
			$last = 'm';
			$period_selector = 'm - Y';
			break;

		case 'Years':
			$last = 'Y';
			$period_selector = 'Y';
			break;
	}

	// Months Splits
	$month_split[6] = true;
	$month_split[12] = true;
	$month_split[36] = true;
	$month_split[60] = true;
	$month_split[61] = true;

	// Only show a SP's active period?
	if ( $_GET['show_active_period'] )
	{
		// Select first "sale" 
		$res = mysql_query("select min(sold_date) as start from saleslog " . ( $_GET['person_id'] ? " where person_id = '" . $_GET['person_id'] . "'" : '' ));
		$row = mysql_fetch_array($res);
		$from = strtotime($row['start']);
		$_GET['from'] = $row['start'];

		// Select last "sale" 
		$res = mysql_query("select max(sold_date) as end from saleslog " . ( $_GET['person_id'] ? " where person_id = '" . $_GET['person_id'] . "'" : '' ));
		$row = mysql_fetch_array($res);
		$to = strtotime($row['end']);
		$_GET['to'] = $row['end'];
	}
	else
	{
		// Get seconds
		$from = strtotime($_GET['from']);
		$to = strtotime($_GET['to']);
	}

	// Generate periods
	{
		// How many Months are we spanning
		for ( $i = $from ; $i <= $to ; $i += 86400 )
		{
			// Move to next if already done
			if ( $l == date($last, $i) )
			{
				continue;
			}
			$l = date($last, $i);
	
			// Store period
			$period[date($period_selector, $i)] = ++$t;
			$t_period[$t] = date($period_selector, $i);
		}
	}

	// Group Data Fields
	$res = mysql_query("select * from saleslog where sold_date >= '" . $_GET['from'] . "' && sold_date <= '" . $_GET['to'] . "'" . ( $_GET['person_id'] ? " && person_id = '" . $_GET['person_id'] . "'" : '' ) . " order by sold_date");
	while ( $row = mysql_fetch_array($res) )
	{
		// No trials
		if ( ($row['product_price'] - $row['discount']) > 0 && $row['status'] != 3 )
		{
			// New Sales Count
			$total_sale_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;
	
			// New Sales Revenue
			$total_sale_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

			// Select Customer Country Details
			$l_res = mysql_query("select master_id from cst LEFT JOIN countries ON (invoice_country = id) where cst_id = '" . $row['cst_id'] . "' limit 1");
			$l_row = mysql_fetch_array($l_res);

			// Got master id?
			if ( $l_row['master_id'] ) {
				$l_res = mysql_query("select country, master_id from cst, countries where invoice_country = id && cst_id = '" . $l_row['master_id'] . "' limit 1");
				$tl_row = mysql_fetch_array($l_res);
				if ( $tl_row['country'] ) {
					$l_row = $tl_row;
				}
			}

			// New Sale - or recurrence
			$c_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id = '" . $row['cst_id'] . "' && (product_price-discount) > 0 && status = 2");
			if ( mysql_num_rows($c_res) )
			{
				// Recurrence Sale Count
				$recurrence_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

				// Recurrence Sale Revenue
				$recurrence_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

				// Paid sales
				if ( $row['status'] == 2 )
				{
					// PAID: Recurrence Count
					$paid_recurrence_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

					// PAID: Recurrence Revenue
					$paid_recurrence_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				}

				// Place in country container also
				{
					// Country: Recurrency Sale Count
					$recurrence_country_count[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]]++;

					// Country: Recurrency Sale Revenue
					$recurrence_country_revenue[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				}

				// Set type
				$type = 'recur';
			}
			else
			{
				// New Sale Count
				$new_sale_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

				// New Sale Revenue
				$new_sale_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

				// Paid sales
				if ( $row['status'] == 2 )
				{
					// PAID: New Sale Count
					$paid_new_sale_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

					// PAID: New Sale Revenue
					$paid_new_sale_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				}

				// Place in country container also
				{
					// Country: Recurrency Sale Count
					$new_sale_country_count[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]]++;

					// Country: Recurrency Sale Revenue
					$new_sale_country_revenue[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
				}
				
				// Set type
				$type = 'new';
			}

			// Paid sales
			if ( $row['status'] == 2 )
			{
				// PAID: Total Count
				$paid_total_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

				// PAID: Total Revenue
				$paid_total_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
			}

			// Country: Total Sale Count
			$total_country_count[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]]++;

			// Country: Total Sale Revenue
			$total_country_revenue[$l_row['country']][$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

			// Register countries
			$countries[$l_row['country']] = true;

			// Data based on period
			$months = ReturnMonths($row['sold_date'], $row['expires_date']);
			if ( $months < 6 )
			{
				$months = 6;
			}
			elseif ( $months < 12 )
			{
				$months = 12;
			}
			elseif ( $months < 36 )
			{
				$months = 36;
			}
			elseif ( $months < 60 )
			{
				$months = 60;
			}
			else
			{
				$months = 61;
			}

			// Count
			$split_count[$type][$months][$period[date($period_selector, strtotime($row['sold_date']))]]++;

			// Revenue
			$split_revenue[$type][$months][$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );

		}
		elseif ( $row['status'] != 3 )
		{
			// Trial Count
			$customer_trials[$period[date($period_selector, strtotime($row['sold_date']))]]++;
		}
		else
		{
			// Cancelled sales

			// Country: Total Sale Count
			$total_cancelled_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;

			// Country: Total Sale Revenue
			$total_cancelled_revenue[$period[date($period_selector, strtotime($row['sold_date']))]] += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] );
		}
	}

	// Select 'Lost' Customers
	$res = mysql_query("select * from saleslog where (expires_date >= '" . $_GET['from'] . "' && expires_date <= '" . $_GET['to'] . "') && expires_date <= now() &&  (product_price-discount) > 0 && status = 2" . ( $_GET['person_id'] ? " && person_id = '" . $_GET['person_id'] . "'" : '' ) . " order by sold_date");
	while ( $row = mysql_fetch_array($res) )
	{
		// Check if a sale was made after this sale, with an expire date set further out in the future: expires_date > 
		$c_res = mysql_query("select * from saleslog where cst_id = '" . $row['cst_id'] . "' && expires_date > '" . $row['expires_date'] . "' && (status != 3 || status is null)");
		if ( !mysql_num_rows($c_res) )
		{
			$total_lost_recurrence_count[$period[date($period_selector, strtotime($row['sold_date']))]]++;
		}
	}

	// Generate HTML output based on grouped values
	$width = 350;
	for ( $i = 1 ; $i <= $t ; $i++ )
	{
		// Field type? (borders)
		if ( $i < $t )
		{
			$field = 'field';
		}
		else
		{
			$field = 'field-end';
		}

		// Splitted
		{
			// NEW
			{
				@reset($month_split);
				while ( list($months, $data) = @each($month_split) )
				{
					$html_split_count['new'][$months] .= '<td class="' . $field . '" align="right">' . ( $split_count['new'][$months][$i] ? $split_count['new'][$months][$i] : '0' ) . '</td>';
				}
				@reset($month_split);
				while ( list($months, $data) = @each($month_split) )
				{
					$html_split_revenue['new'][$months] .= '<td class="' . $field . '" align="right">' . ( $split_revenue['new'][$months][$i] ? number_format($split_revenue['new'][$months][$i]) : '0' ) . '</td>';
				}
			}
			// Recur
			{
				@reset($month_split);
				while ( list($months, $data) = @each($month_split) )
				{
					$html_split_count['recur'][$months] .= '<td class="' . $field . '" align="right">' . ( $split_count['recur'][$months][$i] ? $split_count['recur'][$months][$i] : '0' ) . '</td>';
				}
				@reset($month_split);
				while ( list($months, $data) = @each($month_split) )
				{
					$html_split_revenue['recur'][$months] .= '<td class="' . $field . '" align="right">' . ( $split_revenue['recur'][$months][$i] ? number_format($split_revenue['recur'][$months][$i]) : '0' ) . '</td>';
				}
			}
		}

		// Language
		{
			// New Sale
			@reset($total_country_count);
			while ( list($country, $data) = @each($total_country_count) )
			{
				$html_new_sale_country_count[$country] .= '<td class="' . $field . '" align="right">' . ( $new_sale_country_count[$country][$i] ? $new_sale_country_count[$country][$i] : '0' ) . '</td>';
			}
			@reset($total_country_count);
			while ( list($country, $data) = @each($total_country_count) )
			{
				$html_new_sale_country_revenue[$country] .= '<td class="' . $field . '" align="right">' . ( $new_sale_country_revenue[$country][$i] ? number_format($new_sale_country_revenue[$country][$i]) : '0' ) . '</td>';
			}

			// Recurrence
			@reset($total_country_count);
			while ( list($country, $data) = @each($total_country_count) )
			{
				$html_recurrence_country_count[$country] .= '<td class="' . $field . '" align="right">' . ( $recurrence_country_count[$country][$i] ? $recurrence_country_count[$country][$i] : '0' ) . '</td>';
			}
			@reset($total_country_count);
			while ( list($country, $data) = @each($total_country_count) )
			{
				$html_recurrence_country_revenue[$country] .= '<td class="' . $field . '" align="right">' . ( $recurrence_country_revenue[$country][$i] ? number_format($recurrence_country_revenue[$country][$i]) : '0' ) . '</td>';
			}

			// Total
			@reset($total_country_count);
			while ( list($country, $data) = @each($total_country_count) )
			{
				$html_total_country_count[$country] .= '<td class="' . $field . '" align="right">' . ( $data[$i] ? $data[$i] : '0' ) . '</td>';
			}
			@reset($total_country_revenue);
			while ( list($country, $data) = @each($total_country_revenue) )
			{
				$html_total_country_revenue[$country] .= '<td class="' . $field . '" align="right">' . ( $data[$i] ? number_format($data[$i]) : '0' ) . '</td>';
			}
		}

		// Legend
		$html_legend .= '<td class="' . $field . '" width="100" align="center"><b>' . $t_period[$i] . '</b></td>';
		$width += 100;

		// Trial Count
		$html_trial_count .= '<td class="' . $field . '" align="right">' . ( $customer_trials[$i] ? number_format($customer_trials[$i]) : '0' ) . '</td>';

		// Lost Recurrence Count
		$html_lost_recurrence_count .= '<td class="' . $field . '" align="right">' . ( $total_lost_recurrence_count[$i] ? number_format($total_lost_recurrence_count[$i]) : '0' ) . '</td>';

		// Total Sale Count
		$html_total_sale_count .= '<td class="' . $field . '" align="right">' . ( $total_sale_count[$i] ? number_format($total_sale_count[$i]) : '0' ) . '</td>';

		// Total Sale Revenue
		$html_total_sale_revenue .= '<td class="' . $field . '" align="right">' . ( $total_sale_revenue[$i] ? number_format($total_sale_revenue[$i]) : '0' ) . '</td>';

		// PAID: New Sale Count
		$html_paid_new_sale_count .= '<td class="' . $field . '" align="right">' . ( $paid_new_sale_count[$i] ? number_format($paid_new_sale_count[$i]) : '0' ) . '</td>';

		// PAID: New Revenue
		$html_paid_new_sale_revenue .= '<td class="' . $field . '" align="right">' . ( $paid_new_sale_revenue[$i] ? number_format($paid_new_sale_revenue[$i]) : '0' ) . '</td>';

		// PAID: Recurrence Count
		$html_paid_recurrence_count .= '<td class="' . $field . '" align="right">' . ( $paid_recurrence_count[$i] ? number_format($paid_recurrence_count[$i]) : '0' ) . '</td>';

		// PAID: Recurrence Revenue
		$html_paid_recurrence_revenue .= '<td class="' . $field . '" align="right">' . ( $paid_recurrence_revenue[$i] ? number_format($paid_recurrence_revenue[$i]) : '0' ) . '</td>';

		// PAID: Total Count
		$html_paid_total_count .= '<td class="' . $field . '" align="right">' . ( $paid_total_count[$i] ? number_format($paid_total_count[$i]) : '0' ) . '</td>';

		// PAID: Total Revenue
		$html_paid_total_revenue .= '<td class="' . $field . '" align="right">' . ( $paid_total_revenue[$i] ? number_format($paid_total_revenue[$i]) : '0' ) . '</td>';

		// Total Cancelled Count
		$html_total_cancelled_count .= '<td class="' . $field . '" align="right">' . ( $total_cancelled_count[$i] ? number_format($total_cancelled_count[$i]) : '0' ) . '</td>';

		// Total Cancelled Revenue
		$html_total_cancelled_revenue .= '<td class="' . $field . '" align="right">' . ( $total_cancelled_revenue[$i] ? number_format($total_cancelled_revenue[$i]) : '0' ) . '</td>';

		// New Sale Count
		$html_new_sale_count .= '<td class="' . $field . '" align="right">' . ( $new_sale_count[$i] ? number_format($new_sale_count[$i]) : '0' ) . '</td>';

		// New Sale Revenue
		$html_new_sale_revenue .= '<td class="' . $field . '" align="right">' . ( $new_sale_revenue[$i] ? number_format($new_sale_revenue[$i]) : '0' ) . '</td>';

		// New Sale Count
		$html_recurrence_count .= '<td class="' . $field . '" align="right">' . ( $recurrence_count[$i] ? number_format($recurrence_count[$i]) : '0' ) . '</td>';

		// New Sale Revenue
		$html_recurrence_revenue .= '<td class="' . $field . '" align="right">' . ( $recurrence_revenue[$i] ? number_format($recurrence_revenue[$i]) : '0' ) . '</td>';
	}


	// Build output + leading legend
	$table = '
	<tr bgcolor="#E3E3E3">
		<td width="350" class="field"><b>Sales Data / ' . htmlspecialchars($_GET['grouping']) . ' (' . htmlspecialchars($_GET['from']) . ' - ' . htmlspecialchars($_GET['to']) . ')<b></td>' . $html_legend . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Overall</b></td>
	</tr>
	<tr>
		<td class="field"> - Total Sales</td>' . $html_total_sale_count . '
	</tr>
	<tr>
		<td class="field"> - Total Revenue</td>' . $html_total_sale_revenue . '
	</tr>
	<tr>
		<td class="field"> - Trials</td>' . $html_trial_count . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Lost Customers / Cancellations</b></td>
	</tr>
	<tr>
		<td class="field"> - Lost Recurrence Customers *</td>' . $html_lost_recurrence_count . '
	</tr>
	<tr>
		<td class="field"> - Cancelled Sales **</td>' . $html_total_cancelled_count . '
	</tr>
	<tr>
		<td class="field"> - Cancelled Revenue **</td>' . $html_total_cancelled_revenue . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Paid Revenues</b></td>
	</tr>
	<tr>
		<td class="field"> - Number: Total</td>' . $html_paid_total_count . '
	</tr>
	<tr>
		<td class="field"> - Number: New Sales</td>' . $html_paid_new_sale_count . '
	</tr>
	<tr>
		<td class="field"> - Number: Recurrence</td>' . $html_paid_recurrence_count . '
	</tr>
	<tr>
		<td class="field"> - Revenue: Total</td>' . $html_paid_total_revenue . '
	</tr>
	<tr>
		<td class="field"> - Revenue: New Sales</td>' . $html_paid_new_sale_revenue . '
	</tr>
	<tr>
		<td class="field"> - Revenue: Recurrence</td>' . $html_paid_recurrence_revenue . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>New Sales (Number)</b></td>
	</tr>
	<tr>
		<td class="field"> - Number: Total</td>' . $html_new_sale_count . '
	</tr>
	<tr>
		<td class="field"> - Number: 0 - 5 Months - PoC</td>' . $html_split_count['new'][6] . '
	</tr>
	<tr>
		<td class="field"> - Number: 6 - 11 Months</td>' . $html_split_count['new'][12] . '
	</tr>
	<tr>
		<td class="field"> - Number: 12 - 35 Months</td>' . $html_split_count['new'][36] . '
	</tr>
	<tr>
		<td class="field"> - Number: 36 - 59 Months</td>' . $html_split_count['new'][60] . '
	</tr>
	<tr>
		<td class="field"> - Number: 60+ Months</td>' . $html_split_count['new'][61] . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>New Sales (Revenue)</b></td>
	</tr>
	<tr>
		<td class="field"> - Revenue: Total</td>' . $html_new_sale_revenue . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 0 - 5 Months - PoC</td>' . $html_split_revenue['new'][6] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 6 - 11 Months</td>' . $html_split_revenue['new'][12] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 12 - 35 Months</td>' . $html_split_revenue['new'][36] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 36 - 59 Months</td>' . $html_split_revenue['new'][60] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 60+ Months</td>' . $html_split_revenue['new'][61] . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Recurrence (Number)</b></td>
	</tr>
	<tr>
		<td class="field"> - Number: Total</td>' . $html_recurrence_count . '
	</tr>
	<tr>
		<td class="field"> - Number: 0 - 5 Months - PoC</td>' . $html_split_count['recur'][6] . '
	</tr>
	<tr>
		<td class="field"> - Number: 6 - 11 Months</td>' . $html_split_count['recur'][12] . '
	</tr>
	<tr>
		<td class="field"> - Number: 12 - 35 Months</td>' . $html_split_count['recur'][36] . '
	</tr>
	<tr>
		<td class="field"> - Number: 36 - 59 Months</td>' . $html_split_count['recur'][60] . '
	</tr>
	<tr>
		<td class="field"> - Number: 60+ Months</td>' . $html_split_count['recur'][61] . '
	</tr>
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>Recurrence (Revenue)</b></td>
	</tr>
	<tr>
		<td class="field"> - Revenue: Total</td>' . $html_recurrence_revenue . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 0 - 5 Months - PoC</td>' . $html_split_revenue['recur'][6] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 6 - 11 Months</td>' . $html_split_revenue['recur'][12] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 12 - 35 Months</td>' . $html_split_revenue['recur'][36] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 36 - 59 Months</td>' . $html_split_revenue['recur'][60] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: 60+ Months</td>' . $html_split_revenue['recur'][61] . '
	</tr>
</table>
<br>
<br>
<table width="' . $width . '" cellspacing="0" cellpadding="1" style="border: 1px solid;">
	<tr bgcolor="#E3E3E3">
		<td width="350" class="field"><b>Country Sales / ' . htmlspecialchars($_GET['grouping']) . ' (' . htmlspecialchars($_GET['from']) . ' - ' . htmlspecialchars($_GET['to']) . ')<b></td>' . $html_legend . '
	</tr>';

	@ksort($countries);
	while ( list($country, $v) = @each($countries) )
	{
		$table .= '
	<tr>
		<td colspan="' . ($t+1) . '" class="field-end"><br><b>' . $country . '</b></td>
	</tr>
	<tr>
		<td class="field"> - Number: Total</td>' . $html_total_country_count[$country] . '
	</tr>
	<tr>
		<td class="field"> - Number: New Sales</td>' . $html_new_sale_country_count[$country] . '
	</tr>
	<tr>
		<td class="field"> - Number: Recurrence</td>' . $html_recurrence_country_count[$country] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: Total</td>' . $html_total_country_revenue[$country] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: New Sales</td>' . $html_new_sale_country_revenue[$country] . '
	</tr>
	<tr>
		<td class="field"> - Revenue: Recurrence</td>' . $html_recurrence_country_revenue[$country] . '
	</tr>';
	}

	// Headline
	$headline = 'Overall Sales Data: ' . ( $person ? $person : 'All Sales People' );

	// End messages
	$end_msg = '*) This number may not be 100% accurate as accounts can be continued on other customer ID\'s. This means that the number may be lower but not larger.<br>
	<br>
	**) Cancelled sales have been deducted from all values in the tables. This means that no numbers displayed include revenues from cancelled sales.';

	return array($table, $headline, $end_msg, $width);
}

?>
