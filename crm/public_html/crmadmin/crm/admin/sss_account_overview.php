<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');

// Generate output
if ( $_GET['person_id'] )
{
$res = mysql_query("select * from cst, ca.accounts where person_id = '" . $_GET['person_id'] . "' && cst.cst_id = accounts.cst_id && accounts.account_product_type = 5");
while ( $row = mysql_fetch_array($res) )
{
	// Determine how many hosts have been set-up
	$counted = 0;
	$c_res = mysql_query("select * from ca.vss_scan_profiles where account_id = '" . $row['account_id'] . "'");
	while ( $c_row = mysql_fetch_array($c_res) )
	{
		$counted += count(returnVSSTargetsArray($c_row['targets']));
	}

	// Credits used
	$c_res = mysql_query("select id from ca.vss_nessus_reports_raw where account_id = '" . $row['account_id'] . "'");
	$credits = @mysql_num_rows($c_res);

	// Which var to dump data to
	if ( $counted < 3 )
	{
		$var = 'output_0_2';
	}
	elseif ( $counted < 6 )
	{
		$var = 'output_3_5';
	}
	elseif ( $counted < 11 )
	{
		$var = 'output_6_10';
	}
	else
	{
		$var = 'output_max';
	}

	// Build data output
	$$var .= '<tr>
		<td style="padding-left: 15px;"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '">' . htmlspecialchars($row['name']) . '</a>
		</td>
		<td>' . htmlspecialchars($row['account_username']) . '
		</td>
		<td>' . htmlspecialchars($row['last_login']) . '
		</td>
		<td>' . number_format($counted, 0) . '
		</td>
		<td>' . number_format($credits, 0) . '
		</td>
	</tr>';
}

}
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline">
			Customer Account Overview - Secunia Surveillance Scanner
		</td>
	</tr>
        <tr>
                <td colspan="5"><br></td>
        </tr>

        <form method="GET" action="">
        <tr>
                <td colspan="5" valign="top"><b>Person:</b><br>
                <select name="person_id">
                <?php
                $sp_res = mysql_query("select * from salespeople where display = 1 order by name");
                while ( $sp_row = mysql_fetch_array($sp_res) )
                {
                        echo '<option value="' . $sp_row['person_id'] . '" ' . ( $sp_row['person_id'] == $_GET['person_id'] ? 'selected' : '' ) . '>' . $sp_row['name'] . '</option>';
                }
                ?>
                </select></td>
        </tr>
        <tr>
                <td colspan="5"><br></td>
        </tr>
	<tr>
		<td colspan="5"><input type="submit" value="Display"></td>
	</tr>
        <tr>
                <td colspan="5"><br><br></td>
        </tr>
	</form>

	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline" style="padding-left: 15px;">Company
		</td>
		<td class="MenuHeadline">Username
		</td>
		<td class="MenuHeadline">Last Login
		</td>
		<td class="MenuHeadline">Hosts Setup
		</td>
		<td class="MenuHeadline">Credits Used
		</td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			0 - 2 Hosts
		</td>
	</tr>
	<?=$output_0_2?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			3 - 5 Hosts
		</td>
	</tr>
	<?=$output_3_5?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			6 - 10 Hosts
		</td>
	</tr>
	<?=$output_6_10?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			> 10 Hosts
		</td>
	</tr>
	<?=$output_max?>
	<tr><td><br><br><br></td></tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
