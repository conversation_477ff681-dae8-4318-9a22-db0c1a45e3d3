<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Find the numbers
if ( $_GET['person_id'] )
{
	// Appointments
	$res = mysql_query("select cst_id from cst where person_id = '" . $_GET['person_id'] . "' && appointment");
	$appointment = mysql_num_rows($res);

	// Dead customers
	$res = mysql_query("select cst_id from cst where person_id = '" . $_GET['person_id'] . "' && !appointment");
	$dead = mysql_num_rows($res);
}

// Reset vars
$csv_data = '';


// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="2">
	<tr>
		<td class="MenuHeadline" colspan="4" width="100%">
			Stats
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="sp_stats.php">
	<tr>
		<td colspan="4" width="100%">
			Choose Sales Person<br>
			<select name="person_id">
			<?php
			// Select all salespeople
			$s_res = mysql_query("select * from salespeople order by name");
			while ( $s_row = mysql_fetch_array($s_res) )
			{
				echo '<option value="' . $s_row['person_id'] . '">' . $s_row['name'] .  '</option>';
			}
			?>
			</select> - <input type="submit" value="Display Stats" style="width: 150px;">
		</td>
	</tr>
	</form>
	<tr>
		<td colspan="4" width="100%">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<b>Report for <?=ReturnPersonNameFromID($_GET['person_id'])?></b>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<br><br>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<b>Overall (Not Date Specific):</b>
		</td>
	</tr>
	<tr>
		<td colspan="1" width="25%">Appointments (Active Customers)
		</td>
		<td colspan="3" width="75%"><?=htmlspecialchars($appointment)?>
		</td>
	</tr>
	<tr>
		<td colspan="1" width="25%">Dead Customers
		</td>
		<td colspan="3" width="75%"><?=htmlspecialchars($dead)?>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<br><br>
		</td>
	</tr>
	<?php
	// Select first entry in saleslog table
	$res = mysql_query("select min(sold_date) as sold_date from saleslog where person_id = '" . intval($_GET['person_id']) . "'");
	$row = mysql_fetch_array($res);

	$init = strtotime($row['sold_date']);

	while ( 1 )
	{
		// Generate dates
		$date = date('Y-m', mktime(0, 0, 0, date('m', $init) + $i, 1, date('Y', $init)));
		$date_plus_1m = date('Y-m', mktime(0, 0, 0, date('m', $init) + $i + 1, 1, date('Y', $init)));
		$month = date('F - Y', mktime(0, 0, 0, date('m', $init) + $i++, 1, date('Y', $init)));

		// For the graphs
		$canvas_revenue[$month] = array();
		$resale_revenue[$month] = array();

		// Generate numbers
		{
			// Select saleslog data
			$res = mysql_query("select * from saleslog where person_id = '" . $_GET['person_id'] . "' && (sold_date >= '" . $date . "-01' && sold_date < '" . $date_plus_1m . "-01')");

			// Build data vars
			$trial = 0;
			$no_customers = 0;
			$total_revenue = 0;
			$total_paid_revenue = 0;
			$total_unpaid_revenue = 0;
			$no_cancelled = 0;
			$total_cancelled_revenue = 0;
			while ( $row = mysql_fetch_array($res) )
			{
				// Trial
				if ( !($row['product_price'] - $row['discount']) )
				{
					++$trial;
				}

				// Total Revenue and Total no. of customers
				if ( ($row['product_price'] - $row['discount']) > 0 && $row['status'] != 3 )
				{
					$no_customers++;
					$total_revenue += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
					if ( $row['status'] == 2 )
					{
						$total_paid_revenue += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
					}
					elseif ( !$row['status'] )
					{
						$total_unpaid_revenue += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
					}
				}

				// Cancelled sales
				if ( ($row['product_price'] - $row['discount']) > 0 && $row['status'] == 3 )
				{
					$no_cancelled++;
					$total_cancelled_revenue += ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
				}

				// Canvas or resale?
				{
					$t_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id = '" . $row['cst_id'] . "' && (product_price - discount) > 0 && status = 2");
					if ( mysql_num_rows($t_res) == 0 && $row['status'] != 3 )
					{
						if ( ($row['product_price'] - $row['discount']) > 0 )
						{
							$canvas_revenue[$month][] = ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
						}
					}
					elseif ( mysql_num_rows($t_res) > 0 && $row['status'] != 3 )
					{
						if ( ($row['product_price'] - $row['discount']) > 0 )
						{
							$resale_revenue[$month][] = ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;
						}
					}
				}
			}
		}
		?>
		<tr>
			<td colspan="4" width="100%" bgcolor="#F2F2F2">
				<b><?=$month?></b>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="1" width="25%" valign="top">Customers
			</td>
			<td colspan="1" width="10%" valign="top"><?=$no_customers?>
			</td>
			<td colspan="2" width="65%" valign="top" rowspan="10">
				<?php
				$sales_res = mysql_query("select * from saleslog, cst where saleslog.person_id = '" . $_GET['person_id'] . "'&& (sold_date >= '" . $date . "-01' && sold_date < '" . $date_plus_1m . "-01') && (status is null || status != 3) && ((product_price - discount) > 0 ) && saleslog.cst_id = cst.cst_id");
				while ( $sales_row = mysql_fetch_array($sales_res) )
				{
					echo '- <a href="../sales/customer.php?cst_id=' . $sales_row['cst_id'] . '">' . $sales_row['product_name'] . ". Amount: " . ( (float) ( $sales_row['product_price'] - $sales_row['discount'] ) ) * fFOREX( $sales_row['currency_exchange_rate'], $sales_row['currency_exchange_rate_euro'] ) . '. Time: ' . ReturnMonths($sales_row['sold_date'], $sales_row['expires_date']) . ' months.</a><br>';
				}
				?>
			</td>
		</tr>
		<tr valign="top">
			<td colspan="1" width="25%" valign="top">Total Revenue
			</td>
			<td colspan="1" width="10%" valign="top"><?=number_format($total_revenue, 2)?>,-
			</td>
		</tr>
		<tr valign="top">
			<td colspan="1" width="25%" valign="top">Total Paid Revenue
			</td>
			<td colspan="1" width="10%" valign="top"><?=number_format($total_paid_revenue, 2)?>,-
			</td>
		</tr>
		<tr valign="top">
			<td colspan="1" width="25%" valign="top">Total Unpaid Revenue
			</td>
			<td colspan="1" width="10%" valign="top"><?=number_format($total_unpaid_revenue, 2)?>,-
			</td>
		</tr>
		<tr>
			<td colspan="4" width="100%" valign="top">
				<br>
			</td>
		</tr>
		<tr>
			<td colspan="1" width="25%" valign="top">Trials
			</td>
			<td colspan="1" width="10%" valign="top"><?=$trial?>
			</td>
		</tr>
		<tr>
			<td colspan="4" width="100%" valign="top">
				<br>
			</td>
		</tr>
		<tr>
			<td colspan="1" width="25%">No. of Cancelled Sales
			</td>
			<td colspan="1" width="10%"><?=$no_cancelled?>
			</td>
		</tr>
		<tr>
			<td colspan="1" width="25%">Total Revenue Cancelled
			</td>
			<td colspan="1" width="10%"><?=number_format($total_cancelled_revenue, 2)?>,-
			</td>
		</tr>
		<tr>
			<td colspan="4" width="100%">
				<br><br>
			</td>
		</tr>
		<?php
		if ( $date == date('Y-m') )
		{
			break;
		}

		// Generate CSV Output
		$csv_data .= $month . ';' . $no_customers . ';' . $total_revenue . ';' . $total_paid_revenue . ';' . $total_unpaid_revenue . ';' . $trial . ';' . $no_cancelled . ';' . $total_cancelled_revenue . "\n";
	}
	?>
	<tr>
		<td colspan="4" width="100%">
			<b>Report</b>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<table width="950" cellpadding="0" cellspacing="0">
			<tr>
				<td colspan="4" width="100%">
					<img src="/crmadmin/crm/sales/gfx/lightblue.gif" width="10" height="10"> New Sale<br>
					<img src="/crmadmin/crm/sales/gfx/orangebottom.gif" width="10" height="10"> Recurrence
				</td>
			</tr>
			<tr>
				<td colspan="4" width="100%">
					<br><br>
				</td>
			</tr>
				<?php
				$str = '';
				while ( list($month, $data) = each($canvas_revenue) )
				{
					while ( list($sale_no, $value) = each($data) )
					{
						$str .= '<img src="'.HTTP_PATH_CRM2.'gfx/lightblue.gif" width="' . (round((650/75000) * $value) - 1) . '" height="10" style="border-right: 1px solid;">';
						$total_canvas += $value;
					}
					while ( list($sale_no, $value) = each($resale_revenue[$month]) )
					{					
						$str .= '<img src="'.HTTP_PATH_CRM2.'gfx/orangebottom.gif" width="' . (round((650/75000) * $value) - 1) . '" height="10" style="border-right: 1px solid;">';
						$total_resale += $value;
					}
					?>
					<tr>
						<td width="150" valign="top">
							<b><?=$month?></b><br>
							Total: <?=number_format( ($total_canvas + $total_resale), 2)?>,-
						</td>
						<td width="800">
							<?=$str?><br>
							<?=number_format($total_canvas, 2)?>,- / <?=number_format($total_resale, 2)?>,-
						</td>
					</tr>
					<tr>
						<td colspan="4" width="100%">
							<br>
						</td>
					</tr>
					<?php
					$str = '';
					$total_canvas = 0;
					$total_resale = 0;
				}
				?>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<br><br>
		</td>
	</tr>
	<tr>
		<td colspan="4" width="100%">
			<b>CSV Output:</b><br>
			<pre>
<?php
echo "Month;Customers;Total Revenue;Total Paid Revenue;Total Unpaid Revenue;Trials;Cancelled Sales;Total Cancelled Revenue\n";
echo $csv_data;
?>
			</pre>
		</td>
	</tr>
</table>

<?php

// Output footer
echo HTMLFooter();
?>
