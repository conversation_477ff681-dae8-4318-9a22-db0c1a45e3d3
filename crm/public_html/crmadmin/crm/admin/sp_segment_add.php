<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Save segment_references
if ( $task == 'update' )
{
	mysql_query("delete from sp_segment_ref where person_id = '" . $person_id . "'");
	while ( list($key, $segment_id) = each($segments) )
	{
		mysql_query("insert into sp_segment_ref (segment_id, person_id) values('" . $segment_id . "', '" . $person_id . "')");
	}
}

// Select all segments
$segment_res = mysql_query("select * from segments where segment_status < 1 && segment_name like 'CRM2%' && segment_name not like '% - Canvas' && segment_name not like '% - Lost Leads' order by segment_name");

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="7" width="100%">
			Select Segments for Sales People
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<form method="POST" action="sp_segment_add.php">
	<tr>
		<td colspan="7" width="100%">
			Select sales person<br>
			<select name="person_id">
			<option value="">Optional - Not Selected</option>
			<?php
			// Select all salespeople
			$s_res = mysql_query("select * from salespeople order by name");
			while ( $s_row = mysql_fetch_array($s_res) )
			{
				echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
			}
			?>
			</select>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<input type="submit" value="Display">
		</td>
	</tr>
	</form>
	<?php
	if ( $person_id )
	{
	?>
	<tr>
		<td colspan="7" width="100%">
			<br><br>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<b>Displaying: <?=ReturnPersonNameFromID($person_id)?></b>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="1" width="40%">
			<b>Name</b>
		</td>
		<td colspan="1" width="10%">
			<b>Free</b>
		</td>
		<td colspan="1" width="10%">
			<b>Taken</b>
		</td>
		<td colspan="1" width="10%">
			<b>Dead</b>
		</td>
		<td colspan="1" width="10%">
			<b>Total</b>
		</td>
		<td colspan="1" width="15%">
			<b>People in segment</b>
		</td>
		<td colspan="1" width="5%">
			<b>Subscribe</b>
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%">
			<br>
		</td>
	</tr>
	<form action="sp_segment_add.php" method="POST">
	<input type="hidden" name="person_id" value="<?=intval($person_id)?>">
	<input type="hidden" name="task" value="update">
	<?php
	// Loop over vendor result
	while ( $segment_row = mysql_fetch_array($segment_res) )
	{
		// Get dead customers
		$res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && !appointment && person_id");
		$dead = mysql_num_rows($res);

		// Get available customers
		$res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && person_id is null");
		$free = mysql_num_rows($res);

		// Get taken customers
		$res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && appointment");
		$taken = mysql_num_rows($res);

		// Make list of sales people in this segment
		$sales_people = '';
		$res = mysql_query("select * from sp_segment_ref where segment_id = '" . $segment_row['segment_id'] . "'");
		while ( $row = mysql_fetch_array($res) )
		{
			$sales_people .= ReturnPersonNameFromID($row['person_id']) . '<br>';
		}

		// Check if checked should be true
		$checked = '';
		$res = mysql_query("select * from sp_segment_ref where segment_id = '" . $segment_row['segment_id'] . "' && person_id = '" . $person_id . "'");
		if ( mysql_num_rows($res) )
		{
			 $checked = 'checked';
		}

		// Choose bgcolor
		if ( $bgcolor == '#FFFFFF' )
		{
			$bgcolor = "#C0C0C0";
		}
		else
		{
			$bgcolor = "#FFFFFF";
		}
		?>
		<tr bgcolor="<?=$bgcolor?>">
			<td valign="top">
				<a href="segment_update_data.php?segment_id=<?=$segment_row['segment_id']?>"><?=$segment_row['segment_name']?></a>
			</td>
			<td valign="top">
				<?=$free?>
			</td>
			<td valign="top">
				<?=$taken?>
			</td>
			<td valign="top">
				<?=$dead?>
			</td>
			<td valign="top">
				<?=($free+$taken+$dead)?>
			</td>
			<td valign="top">
				<?=$sales_people?>
			</td>
			<td valign="top">
				<input type="checkbox" name="segments[]" value="<?=$segment_row['segment_id']?>" <?=$checked?>>
			</td>
		</tr>
	<?php
	}
	?>
		<tr>
			<td colspan="7" width="100%">
				<br>
			</td>
		</tr>
		<tr>
			<td colspan="6">
		</td>
			<td colspan="1">
				<input type="submit" value="Update">
			</td>
		</tr>
	</form>
	<?php
	}
	?>
		<tr>
			<td colspan="7" width="100%">
				<br><br>
			</td>
		</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
