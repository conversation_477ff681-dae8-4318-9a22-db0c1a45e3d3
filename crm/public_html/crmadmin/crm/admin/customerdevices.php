<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

if ( !$_GET['id'] )
{
	echo "NO ID!";
	exit();
}

// Select all salespeople
$s_res = mysql_query("select * from salespeople where person_id = '" . $_GET['id'] . "'");

// Output
echo '<pre>';

// Loop over all sales people
while ( $s_row = mysql_fetch_array($s_res) )
{
	// Select all customer in CA
	$ca_res = mysql_query("select distinct accounts.cst_id, cst.name, cst.contact, cst.phone, accounts.account_expires from ca.accounts, cst where cst.person_id = '" . $s_row['person_id'] . "' && cst.cst_id = accounts.cst_id order by cst.name");

	// Output
	echo "\n\n<b>" . $s_row['name'] . ': ' . mysql_num_rows($ca_res) . ' Customers registered in Customer Area</b>' . "\n\n";

	// Loop over every result
	while ( $row = mysql_fetch_array($ca_res) )
	{
		// Select number of devices this customer has
		$d_res = mysql_query("select * from ca.devices where cst_id = '" . $row['cst_id'] . "'");
		
		// Select Change
		$c_res = mysql_query("select * from device_reg where cst_id = '" . $row['cst_id'] . "' order by registered desc");
		$c_row = mysql_fetch_array($c_res);
		$change = mysql_num_rows($d_res) - $c_row['devices'];

		if ( strtotime($row['account_expires']) > strtotime('now') )
		{
			$good[] = out($row['name']) . "   " . out($row['cst_id'], 6) . '    ' . out($row['contact']) . "   " . out($row['phone'], 18) . "   " . out(mysql_num_rows($d_res), 8) . '    ' . out(( $change ? $change : '-' ), 10) . out($row['account_expires'], 12) . "\n";
		}
		else
		{
			$bad[] = out($row['name']) . "   " . out($row['cst_id'], 6) . '    ' . out($row['contact']) . "   " . out($row['phone'], 18) . "   " . out(mysql_num_rows($d_res), 8) . '    ' . out(( $change ? $change : '-' ), 10) . out($row['account_expires'], 12) . "\n";
		}

		// Should number of devices be registered for this run?
		if ( $_GET['register'] )
		{
			mysql_query("insert into device_reg (cst_id, devices, registered) values('" . $row['cst_id'] . "', '" . mysql_num_rows($d_res) . "', now())");
		}
	}

	// Output
	echo "<b>Active Accounts: " . count($good) . "\n\nCompany Name:          CST ID:   Contact:               Phone:               Devices:    Change:   Act. Exp.:</b>\n";

	echo @implode('', $good);

	// Output
	echo "\n\n\n<b>Expired Accounts: " . count($bad) . "\n\nCompany Name:          CST ID:   Contact:               Phone:               Devices:    Change:   Act. Exp.:</b>\n";

	echo @implode('', $bad);
}

// Output
echo '</pre>';



function out($str, $len = 20)
{
	return str_pad(substr($str, 0, $len), $len);
}
?>
