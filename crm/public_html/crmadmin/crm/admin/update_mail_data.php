<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Save data
if ( $submitted == 'Save' )
{
        // Write to vendor table
        mysql_query('update mail_templates set title = \'' . $title . '\', body = \'' . $body . '\', lang_id = \'' . $lang_id . '\' where id = \'' . $id . '\'');
}

// Select page data
$res = mysql_query("select * from mail_templates where id = '" . $id . "'");
$row = mysql_fetch_array($res);

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Update mail template
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <form method="POST" action="update_mail_data.php">
        <input type="hidden" name="id" value="<?=htmlspecialchars($id)?>">
        <tr>
                <td colspan="1" width="15%" valign="top">
                        Language:
                </td>
                <td colspan="1" width="85%">
                	<?=DisplayLanguageRadioChoices($row['lang_id'])?>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">
                        Title:
                </td>
                <td colspan="1" width="85%">
                        <input type="text" name="title" size="50" value="<?=htmlspecialchars($row['title'])?>">
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%" valign="top">
                        Content:
                </td>
                <td colspan="1" width="85%">
                        <textarea name="body" cols="100" rows="30"><?=htmlspecialchars($row['body'])?></textarea>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="15%">

                </td>
                <td colspan="1" width="85%">
                        <input type="submit" name="submitted" value="Save">
                </td>
        </tr>
        </form>
        <tr>
		<td> </td>
                <td>
                      <br>

                        ##company_name## = Company Name<br>
                        ##company_contact## = Company Contact<br>
                        ##company_contact_email## = Company Contact E-mail<br>
                        <br>
                        ##product_name## = Product Name<br>
                        ##product_price## = Product Price<br>
                        ##product_period## = Product Period<br>
                        <br>
                        ##salesperson_name## = Salesperson Name<br>
                        ##salesperson_title## = Salesperson Title<br>
                        ##salesperson_email## = Salesperson E-mail<br>


<br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
