<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Require field template
require(INCLUDE_PATH_LIB."customer_temp_1.php");

// Set header to display plain text
header("Content-type: text/plain");

echo "company_name;contact;address_1;address_2;state;town;zipcode;salesperson;salesperson_title\n";

// Select data
$s_res = mysql_query("select distinct cst_id from saleslog where saleslog.lang_id = 1 && ( (product_price - discount) > 0 ) && (status is null || status < 3)");

// Loop over result
while ( $s_row = mysql_fetch_array($s_res) )
{
	// Select data
	$res = mysql_query("select * from cst where cst_id = '" . $s_row['cst_id'] . "' limit 1");
	$row = mysql_fetch_array($res);

	// Salesperson data
	$sp_res = mysql_query("select * from salespeople where person_id = '" . $row['person_id'] . "' limit 1");
	$sp_row = mysql_fetch_array($sp_res);

	echo '"' . $row['name'] . '";"' . $row['contact'] . '";"' . $row[$customer_data['Address 1']] . '";"' . $row[$customer_data['Address 2']] . '";"' . $row[$customer_data['State']]  . '";"' . 
$row[$customer_data['Town']] . '";"' . $row[$customer_data['Zipcode']] . '";"' . $sp_row['name'] . '";"' . $sp_row['title'] . '"' . "\n";
}

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
        $split = explode('-', $date);
        $date = $split[2] . '-' . $split[1] . '-' . $split[0];

        // Return formatted date
        return $date;
}

?>
