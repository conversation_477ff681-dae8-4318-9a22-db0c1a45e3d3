<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Delete vendor
if ( $task == 'delete' )
{
        mysql_query("update segments set segment_status = 1 where segment_id = '" . $segment_id . "'");
}

// Select all segments
$segment_res = mysql_query("select * from segments where segment_status = 0 order by segment_name");

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="7" width="100%">
                        Update Segment
                </td>
        </tr>
        <tr>
                <td colspan="7" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="1" width="40%">
			<b>Name</b>
                </td>
                <td colspan="1" width="10%">
			<b>Free</b>
                </td>
                <td colspan="1" width="10%">
			<b>Taken</b>
                </td>
                <td colspan="1" width="10%">
			<b>Dead</b>
                </td>
                <td colspan="1" width="10%">
			<b>Total</b>
                </td>
                <td colspan="1" width="10%">
			<b>View Dead</b>
                </td>
                <td colspan="1" width="10%">
			<b>Delete</b>
                </td>
        </tr>
        <tr>
                <td colspan="7" width="100%">
                        <br>
                </td>
        </tr>
        <?php
        // Loop over vendor result
        while ( $segment_row = mysql_fetch_array($segment_res) )
        {
        	// Get dead customers
                $res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && !appointment && person_id");
                $dead = mysql_num_rows($res);

        	// Get available customers
                $res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && person_id is null");
                $free = mysql_num_rows($res);

        	// Get taken customers
                $res = mysql_query("select cst_id from cst where segment_id = '" . $segment_row['segment_id'] . "' && appointment");
                $taken = mysql_num_rows($res);

		// Check if new segment - output headline
		$t = trim(substr($segment_row['segment_name'], 0, strpos($segment_row['segment_name'], '-')));
		if ( $last_segment != trim(substr($segment_row['segment_name'], 0, strpos($segment_row['segment_name'], '-'))) )
		{
			?>
        <tr>
                <td colspan="7">
			<br>
			<b><?=$t?></b>
                </td>
	</tr>
			<?php
		}
		$last_segment = $t;
        ?>
        <tr>
                <td colspan="1" width="40%" style="padding-left: 5px;">
                        <a href="segment_update_data.php?segment_id=<?=$segment_row['segment_id']?>"><?=$segment_row['segment_name']?></a>
                </td>
                <td colspan="1" width="10%">
                	<?=$free?>
                </td>
                <td colspan="1" width="10%">
                	<?=$taken?>
                </td>
                <td colspan="1" width="10%">
                	<?=$dead?>
                </td>
                <td colspan="1" width="10%">
                	<?=($free+$taken+$dead)?>
                </td>
                <td colspan="1" width="10%">
                	<a href="customer_revive.php?segment_id=<?=$segment_row['segment_id']?>">[View Dead]</a>
                </td>
                <td colspan="1" width="10%">
                        <a href="segment_update_list.php?segment_id=<?=$segment_row['segment_id']?>&task=delete" onClick="return confirm('Do you wish to delete: <?=$segment_row['segment_name']?>');">[Delete]</a>
                </td>
        </tr>
        <?php
        }
        ?>
        <tr>
                <td colspan="7" width="100%">
                        <br><br>
                </td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
