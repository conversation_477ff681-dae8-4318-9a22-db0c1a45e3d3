<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$rRes = mysql_query("select distinct cst.cst_id, name, contact, field_1, field_2, field_3, field_4, field_5, invoice_country from saleslog, cst where expires_date >= now() && (status is null || status != 3) && product_price-discount > 0 && saleslog.cst_id = cst.cst_id order by cst.name");

$sOutput = '';
while ( $aRow = mysql_fetch_array($rRes) )
{
	// Select country
	$rCountryRes = mysql_query("select * from crm.countries where id = '" . $aRow['invoice_country'] . "'");
	$aCountryRow = mysql_fetch_array($rCountryRes);

	$sOutput .= '<tr>
	<td>' . htmlspecialchars($aRow['name']) . '</td>
	<td>' . 
		( $aRow['field_1'] ? htmlspecialchars($aRow['field_1']) . '<br>' : '' ) .
		( $aRow['field_2'] ? htmlspecialchars($aRow['field_2']) . '<br>' : '' ) . 
		( $aRow['field_3'] ? htmlspecialchars($aRow['field_3']) . '<br>' : '' ) .
		( $aRow['field_4'] ? htmlspecialchars($aRow['field_4']) . ' ' : '' ) .
		( $aRow['field_5'] ? htmlspecialchars($aRow['field_5']) : '' ) . '<br>' .
		$aCountryRow['country'] . 
	'</td>
	<td>' . htmlspecialchars($aRow['contact']) . '</td>
</tr>';
}

?>

<table>
<?= $sOutput ?>
</table>
