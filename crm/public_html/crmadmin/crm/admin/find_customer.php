<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales',  HTTP_PATH.'admin/index.php');
?>
<br>
<form method="GET" action="find_customer.php">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="100%">Find customer</td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%">Search for:<br><input type="text" name="search" value="<?=htmlspecialchars($search)?>"></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><input type="submit" name="submit" value="Search"></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%">
		<?php
		// Search
		if ( $search )
		{
			// Output
			echo '<img src="gfx/longline.gif" width="100%" height="1"><br><b>Result:</b><br><br>';

			// Make search
			$res = mysql_query("select * from cst where (name like '%" . $search . "%' || phone like '%" . $search . "%' || contact like '%" . $search . "%') order by name");

			// New table
			echo '<table width="100%" cellpadding="0" cellspacing="0">';

			// Output
			echo '<tr><td width="10%"><b>Icons</b></td>';
			echo '<td width="20%"><b>Name</b></td>';
			echo '<td width="30%"><b>Main / Direct / Mobile</b></td>';
			echo '<td width="20%"><b>Contact, Title</b></td>';
			echo '<td width="20%"><b>Owner</b></td></tr>';
			echo '<tr><td colspan="5"><br></td></tr>';

			// Loop over result
			while ( $row = mysql_fetch_array($res) )
			{
				// Select icons
				$icons = '';
				$i_res = mysql_query("select icon from icon_types, icon_ref where icon_types.icon_id = icon_ref.icon_id && icon_ref.cst_id = '" . $row['cst_id'] . "'");

				while ( $i_row = mysql_fetch_array($i_res) )
				{
					$icons .= '<img src="gfx/' . $i_row['icon'] . '.gif">  ';
				}

				// Select all AIDA icons
				$i_res = mysql_query("select icon_display from icons_aida, icons_aida_ref where icons_aida.icon_id = icons_aida_ref.icon_id && icons_aida_ref.cst_id = '" . $row['cst_id'] . "' order by icons_aida.icon_id");
				while ( $i_row = mysql_fetch_array($i_res) )
				{
					$icons .= $i_row['icon_display'] . ', ';
				}

				echo '<tr><td width="10%">' . substr($icons, 0, -2) . '</td>';
				echo '<td width="20%"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $row['name'] . '</a></td>';
				echo '<td width="30%"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $row['phone'] . '</a></td>';
				echo '<td width="20%"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $row['contact'] . '</a></td>';
				echo '<td width="20%"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . ReturnPersonNameFromID($row['person_id']) . '</a></td></tr>';
			}

			// End table
			echo '</table>';
		}
		?>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
