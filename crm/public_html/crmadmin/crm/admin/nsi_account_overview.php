<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Admin',  HTTP_PATH.'admin/index.php');

// Number of active hosts
$iActiveHosts = DBGetRowValue('ca.nsi_devices, ca.accounts', 'COUNT(*)', 'nsi_devices.account_id = accounts.account_id AND accounts.account_expires >= NOW()');
$iLicensedHosts = DBGetRowValue('ca.license_keys', 'SUM(quantity)', 'type = 32 AND valid_to >= NOW() AND quantity > 10');
$iUniqueAccounts = DBGetRowValue('ca.license_keys', 'COUNT(DISTINCT account_id)', 'type = 32 AND valid_to >= NOW() AND quantity > 10');

?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline">Customer Account Overview - Secunia CSI</td>
	</tr>
        <tr>
                <td colspan="5"><br></td>
        </tr>

	<tr>
		<td class="MenuHeadline">Overall Statistics</td>
	</tr>

        <tr>
                <td colspan="5"><br></td>
        </tr>

        <tr>
                <td colspan="5">
			<table bordeR="1">
				<tr>
					<td width="150">Active Hosts:</td>
					<td width="100" align="right"><?= number_format($iActiveHosts) ?></td>
				</tr>
				<tr>
					<td>Active Accounts:</td>
					<td align="right"><?= number_format($iUniqueAccounts) ?></td>
				</tr>
				<tr>
					<td>Licensed Hosts:</td>
					<td align="right"><?= number_format($iLicensedHosts) ?></td>
				</tr>
				<tr>
					<td>Licensed Usage:</td>
					<td align="right"><?= round( ( $iActiveHosts / $iLicensedHosts ) * 100 ) ?>%</td>
				</tr>
			</table>
			<br>
<?php
for ($iYearCount = 0 ; $iYearCount <= (date('Y') - 2007) ; $iYearCount++ ) {
$iYear = date('Y') - $iYearCount;
?>
<table border="1">
	<tr>
		<td class="MenuHeadline"><?= $iYear ?></td>
		<td class="MenuHeadline">Hosts - All</td>
		<td class="MenuHeadline">Hosts - Active</td>
		<td class="MenuHeadline">Hosts /w Agent - All</td>
		<td class="MenuHeadline">Hosts /w Agent - Active</td>
		<td class="MenuHeadline">Scan Attempts</td>
	</tr>
	<?php
	for ( $iMonth = 0 ; $iMonth < 12 ; $iMonth++ ) {
		echo '
	<tr>
		<td class="MenuHeadline">' . date('M', mktime(0,0,0,(12-$iMonth))) . '</td>
		<td align="right">' . number_format(DBGetRowValue('ca.nsi_devices', 'count(*)', "imported >= '" . $iYear . '-' . (12-$iMonth) . '-1' . "' AND imported <= '" . $iYear . '-' . (12-$iMonth) . '-31' . "'")) . '</td>
		<td align="right">' . number_format(DBGetRowValue('ca.nsi_devices, ca.accounts', 'count(*)', "nsi_devices.account_id = accounts.account_id AND accounts.account_expires >= NOW() AND imported >= '" . $iYear . '-' . (12-$iMonth) . '-1' . "' AND imported <= '" . $iYear . '-' . (12-$iMonth) . '-31' . "'")) . '</td>
		<td align="right">' . number_format(DBGetRowValue('ca.nsi_devices, ca.nsi_device_agent_conf', 'count(*)', "imported >= '" . $iYear . '-' . (12-$iMonth) . '-1' . "' AND imported <= '" . $iYear . '-' . (12-$iMonth) . '-31' . "' AND nsi_devices.nsi_device_id = nsi_device_agent_conf.nsi_device_id")) . '</td>
		<td align="right">' . number_format(DBGetRowValue('ca.nsi_devices, ca.nsi_device_agent_conf, ca.accounts', 'count(*)', "imported >= '" . $iYear . '-' . (12-$iMonth) . '-1'  . "' AND imported <= '" . $iYear . '-' . (12-$iMonth) . '-31' . "' AND nsi_devices.nsi_device_id = nsi_device_agent_conf.nsi_device_id AND nsi_devices.account_id = accounts.account_id AND accounts.account_expires >= NOW() ")) . '</td>
		<td align="right">' . number_format(DBGetRowValue('vuln_track.nsi_queue', 'count(*)', "status_date >= '" . $iYear . '-' . (12-$iMonth) . '-1' . "' AND status_date <= '" . $iYear . '-' . (12-$iMonth) . '-31' . "'")) . '</td>
	</tr>';
	}
	?>
</table>
<br>
<?php
}
?>

		</td>
        </tr>

        <tr>
                <td colspan="5" style="padding-left: 18px;">
			<br><br>
			<b>License Usage - Color Codes:</b><br>
			<span style="width: 10px; background: RED; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> Less than 1% usage<br>
			<span style="width: 10px; background: ORANGE; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> Less than 30% usage<br>
			<span style="width: 10px; background: YELLOW; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> Less than 60% usage<br>
			<span style="width: 10px; background: GREEN; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> Less than 90% usage<br>
			<span style="width: 10px; background: #2399CD; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> More than 90% usage<br>
			<span style="width: 10px; background: #DEDEDE; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span> Expired license<br>
			<br>
		</td>
        </tr>
	<tr>
		<td class="MenuHeadline">Account by Account Details</td>
		<td class="MenuHeadline">Quantity</td>
		<td class="MenuHeadline">Used Hosts</td>
		<td class="MenuHeadline">Used Percentage</td>
		<td class="MenuHeadline">Expires</td>
	</tr>

<?php
$rResult = mysql_query("SELECT * FROM ca.license_keys WHERE valid_to > NOW() AND quantity > 10 ORDER BY valid_to");
while ( $aResult = mysql_fetch_array($rResult) ) {
	// Output company name
	if ( $aShownAccountID[$aResult['account_id']] ) {
		continue;
	}
	$aShownAccountID[$aResult['account_id']] = true;

	// Select company name
	$rCST = mysql_query("SELECT cst.name, cst.cst_id, cst.master_id FROM ca.accounts, crm.cst WHERE accounts.account_id = '" . $aResult['account_id'] . "' AND accounts.cst_id = cst.cst_id LIMIT 1");
	$aCST = mysql_fetch_array($rCST);

	// Master?
	if ( $aCST['master_id'] ) {
		$rCST = mysql_query("SELECT cst.name, cst.cst_id FROM crm.cst WHERE cst_id = '" . $aCST['master_id'] . "' LIMIT 1");
		$aCST = mysql_fetch_array($rCST);
	}

?>
	<tr><td><br></td></tr>
	<tr>
		<td height="20" style="padding-left: 3px; background: #DEDEDE;" colspan="5"><a href="<?= HTTP_PATH_CRM2; ?>?page=customer&cst_id=<?= $aCST['cst_id'] ?>"><b><?= htmlspecialchars($aCST['name']) ?></b></a></td>
	</tr>

	<?php
	// Reset totals
	$iQuantity = 0;
	$iHosts = 0;

	$rLicense = mysql_query("SELECT license_keys.license, license_keys.valid_to, license_keys.quantity, count(license_hosts.host) as 'hosts' FROM ca.license_keys LEFT JOIN ca.license_hosts ON license_keys.id = license_hosts.license_id WHERE license_keys.quantity > 10 AND license_keys.account_id = '" . $aResult['account_id'] . "' GROUP BY license_keys.id ORDER BY license_keys.valid_to");
	while ( $aLicense = mysql_fetch_array($rLicense) ) {
		// Percentage
		$iPercentage = round( ($aLicense['hosts'] / $aLicense['quantity']) * 100 );

		// Find color
		if ( strtotime($aLicense['valid_to']) < time() ) {
			$sColor = '#DEDEDE';
			$sFontColor = "#DEDEDE";
		} elseif ( $iPercentage < 1 ) {
			$sColor = 'RED';
			$sFontColor = '#000000';
		} elseif ( $iPercentage < 30 ) {
			$sColor = 'ORANGE';
			$sFontColor = '#000000';
		} elseif ( $iPercentage < 60 ) {
			$sColor = 'YELLOW';
			$sFontColor = '#000000';
		} elseif ($iPercentage >= 90 ) {
			$sColor = '#2699CD';
			$sFontColor = '#000000';
		} else {
			$sColor = 'GREEN';
			$sFontColor = '#000000';
		}

		// Totals (active licenses)
		if ( strtotime($aLicense['valid_to']) > time() ) {
			$iQuantity += $aLicense['quantity'];
			$iHosts += $aLicense['hosts'];
		}
	?>
		<tr>
			<td style="padding-left: 18px; color: <?=$sFontColor?>;" height="20"><span style="width: 10px; background: <?= $sColor ?>; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;<?= $aLicense['license'] ?></td>
			<td style="color: <?=$sFontColor?>;"><?= number_format($aLicense['quantity']) ?></td>
			<td style="color: <?=$sFontColor?>;"><?= number_format($aLicense['hosts']) ?></td>
			<td style="color: <?=$sFontColor?>;"><?= $iPercentage ?>%</td>
			<td style="color: <?=$sFontColor?>;"><?= substr($aLicense['valid_to'], 0, 10) ?></td>
		</tr>
	<?php
	}

	$iPercentage = ($iHosts / $iQuantity) * 100;
	if ( $iPercentage < 1 ) {
		$sColor = 'RED';
	} elseif ( $iPercentage < 30 ) {
                $sColor = 'ORANGE';
        } elseif ( $iPercentage < 60 ) {
                $sColor = 'YELLOW';
        } elseif ($iPercentage >= 90 ) {
                $sColor = '#2699CD';
        } else {
                $sColor = 'GREEN';
        }
	?>
	<tr>
		<td style="padding-left: 18px; border-top: 1px solid #000000;" height="20"><span style="width: 10px; background: <?= $sColor ?>; border: 1px solid #000000;">&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;<b>Account Overall Usage</b></td>
		<td style="border-top: 1px solid #000000;"><b><?= number_format($iQuantity) ?></b></td>
		<td style="border-top: 1px solid #000000;"><b><?= number_format($iHosts) ?></b></td>
		<td style="border-top: 1px solid #000000;"><b><?= round($iPercentage) ?>%</b></td>
		<td style="border-top: 1px solid #000000;"><b>&nbsp;</b></td>
	</tr>
	<?php
}
?>

</table>

<?php
// Output footer
echo HTMLFooter();
?>
