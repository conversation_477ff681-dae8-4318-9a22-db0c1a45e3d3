<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Store data?
if ( $_POST['subscription_setup'] )
{
	// Generate contry string
	$countries = "'" . @implode("', '", $_POST['countries']) . "'";

	// Update data
	mysql_query("update crm.salespeople set lead_countries = '" . mysql_escape_string($countries) . "' where person_id = '" . $_POST['person_id'] . "' limit 1");

	// Redirect to page - avoid link and refresh updates
	header("Location: lead_subscription.php?person_id=" . $_POST['person_id'] . "&status=1");
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="PageHeadline" colspan="3" width="100%">
			Inbound Lead Country Subscription
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<form method="GET" action="lead_subscription.php">
	<tr>
		<td colspan="3">
			<b>Select sales person</b><br>
			<?php
			// Select all salespeople
			echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="3"><input type="submit" value="Display" class="submit" style="width: 10%;"></td>
	</tr>
	</form>

	<?php
	// Output page delimeter
	echo returnPageDelimeter(3, '100%', 1);

	// Only include stuff below here if 'person_id' is available 
	if ( $_GET['person_id'] )
	{
		// Select person data
		$res = mysql_query("SELECT * FROM crm.salespeople WHERE person_id = '" . intval($_GET['person_id']) . "' LIMIT 1");
		$row = mysql_fetch_array($res);
	?>

	<form method="POST" action="lead_subscription.php">
	<input type="hidden" name="person_id" value="<?= intval($_GET['person_id']) ?>">
	<input type="hidden" name="subscription_setup" value="1">
	<tr>
		<td colspan="3">
			<b><?= $row['name'] . ( $_GET['status'] ? ' - <font color="red">Data saved successfully!</font>' : '' ) ?></b>
		</td>
	</tr>
	<tr>
		<td colspan="3">
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="3">
			<b>Select Countries:</b>
		</td>
	</tr>
	<tr>
		<td>

		<?= returnSelectBox(explode(', ', str_replace("'", '', $row['lead_countries'])), 'countries[]', true, 'SELECT * FROM website.form_countries ORDER BY name', 'init', 'name'); ?>

		</td>
	</tr>
	<tr>
		<td colspan="3">
			<br>
		</td>
	</tr>
	<tr>
		<td><input type="submit" value="Update" class="submit" style="width: 10%;"></td>
	</tr>
	</form>

	<?php
	}
	?>

	<tr>
		<td colspan="3">
			<br>
		</td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
