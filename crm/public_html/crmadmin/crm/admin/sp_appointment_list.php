<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Set cookie
setcookie("this_lang_id", ( isset($input_this_lang_id) ? $input_this_lang_id : $this_lang_id ), time() + 10000000);
$this_lang_id = ( isset($input_this_lang_id) ? $input_this_lang_id : $this_lang_id );

// From and to
$from = ( $_GET['from'] ? $_GET['from'] : date('Y-m-d') );
$to = ( $_GET['to'] ? $_GET['to'] : date('Y-m-d') );

// Select whole appointment list
$res = mysql_query("select * from cst where person_id = '" . $_GET['person_id'] . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& crm_lang_id = '" . $this_lang_id . "'" : "" ) . $sSQLAccessLimit . " order by appointment");

// Flush var
$font = '';

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales',  HTTP_PATH.'admin/index.php');
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<form method="GET" action="sp_appointment_list.php">
	<tr>
		<td colspan="6" width="100%"><b>Admin:</b></td>
	</tr>
	<tr>
		<td colspan="6" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%" valign="top"><b>Period:</b><br>
		<input type="text" name="from" value="<?=htmlspecialchars($from)?>" style="width: 100px;"> - 
		<input type="text" name="to" value="<?=( $_GET['to'] ? htmlspecialchars($_GET['to']) : date('Y-m-d') )?>" style="width: 100px;"></td>
		<td colspan="2" width="100%" valign="top"><b>Person:</b><br>
		<select name="person_id">
		<?php
		$sp_res = mysql_query("select * from salespeople where display = 1 order by name");
		while ( $sp_row = mysql_fetch_array($sp_res) )
		{
			echo '<option value="' . $sp_row['person_id'] . '" ' . ( $sp_row['person_id'] == $_GET['person_id'] ? 'selected' : '' ) . '>' . $sp_row['name'] . '</option>';
		}
		?>
		</select></td>
		<td colspan="2" width="100%" valign="top"></td>
	</tr>
	<tr>
		<td colspan="6" width="100%"><br><br></td>
	</tr>
	<tr>
		<td colspan="6" width="100%">
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td width="20%" valign="top">
						<b>Language</b><br>
						<br>
<select name="input_this_lang_id">
<option value="0" <?=( !$this_lang_id ? "selected" : "" )?>>Show All</option>
<?php
$l_res = mysql_query("select * from vuln_track.language order by lang_id");
while ( $l_row = mysql_fetch_array($l_res) )
{
?>
	<option value="<?=$l_row['lang_id']?>" <?=( $this_lang_id == $l_row['lang_id'] ? "selected" : "" )?>><?=$l_row['lang_name']?></option>
<?php
}
?>
</select>
					</td>
					<td width="20%" valign="top">
						<b>Awareness</b><br>
						<br><select name="icon_c">
							<option value="0">- C - Ignore</option>
							<?php
							// Select all icons type: c
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'c'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_c == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_c, ',') ? 'selected' : '' )?>>- C - All</option>
						</select>
						<br><br><select name="icon_o">
							<option value="0">- O - Ignore</option>
							<?php
							// Select all icons type: o
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'o'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_o == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_o, ',') ? 'selected' : '' )?>>- O - All</option>
						</select>
					</td>
					<td width="20%" valign="top">
						<b>Interest</b><br>
						<br><select name="icon_s">
							<option value="0">- S - Ignore</option>
							<?php
							// Select all icons type: s
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 's'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_s == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_s, ',') ? 'selected' : '' )?>>- S - All</option>
						</select><br>
						<br><select name="icon_v">
							<option value="0">- V - Ignore</option>
							<?php
							// Select all icons type: v
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'v'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_v == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_v, ',') ? 'selected' : '' )?>>- V - All</option>
						</select><br>
						<br><select name="icon_e">
							<option value="0">- E - Ignore</option>
							<?php
							// Select all icons type: e
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'e'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_e == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_e, ',') ? 'selected' : '' )?>>- E - All</option>
						</select>
					</td>
					<td width="20%" valign="top">
						<b>Desire</b><br>
						<br><select name="icon_d">
							<option value="0">- D - Ignore</option>
							<?php
							// Select all icons type: D
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'd'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_d == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_d, ',') ? 'selected' : '' )?>>- D - All</option>
						</select>
					</td>
					<td width="20%" valign="top">
						<b>Action</b><br>
						<br><select name="icon_S">
							<option value="0">- $ - Ignore</option>
							<?php
							// Select all icons type: $
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = '$'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_S == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_S, ',') ? 'selected' : '' )?>>- $ - All</option>
						</select><br>
						<br>
						<?php
						// Select all icons type: $
						$ires = mysql_query("select * from icons_aida where icon_type = '$$'");
						$irow = mysql_fetch_array($ires);
						?>						
						<input type="checkbox" name="icon_SS" style="width: 5%;" value="<?=$irow['icon_id']?>" <?=( $icon_SS == $irow['icon_id'] ? 'checked' : '' )?>> <?=$irow['icon_display']?><br>
						<br><select name="icon_u">
							<option value="0">- U - Ignore</option>
							<?php
							// Select all icons type: U
							$ids = '';
							$ires = mysql_query("select * from icons_aida where icon_type = 'u'");
							while ( $irow = mysql_fetch_array($ires) )
							{
								echo '<option value="' . $irow['icon_id'] . '"' . ( $icon_u == $irow['icon_id'] ? 'selected' : '' ) . '>' . $irow['icon_display'] . '</option>';
								$ids .= $irow['icon_id'] . ",";
							}
							?>
							<option value="<?=substr($ids, 0, -1)?>" <?=( stristr($icon_u, ',') ? 'selected' : '' )?>>- U - All</option>
						</select><br>
						<br>
						<?php
						// Select all icons type: $
						$ires = mysql_query("select * from icons_aida where icon_type = 'star'");
						$irow = mysql_fetch_array($ires);
						?>						
						<input type="checkbox" name="icon_star" style="width: 5%;" value="<?=$irow['icon_id']?>" <?=( $icon_star == $irow['icon_id'] ? 'checked' : '' )?>> <?=$irow['icon_display']?>
					</td>
				</tr>
				<tr>
					<td colspan="5"><br></td>
				</tr>
				<tr>
					<td colspan="5"><input type="submit" value="Set Focus"></td>
				</tr>
				<tr>
					<td colspan="5"><br></td>
				</tr>
				<tr>
					<td colspan="5"><b>Summarised Icons:</b> Numbers below are only based upon period, language, and salesperson.<br>
					<br>
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">
								<br>
							</td>
							<td width="20%" valign="top">
								<b>C</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'c'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>O</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'o'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
							</td>
							<td width="20%" valign="top">
								<b>S</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 's'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>V</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'v'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>E</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'e'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
							</td>
							<td width="20%" valign="top">
								<b>D</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'd'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
							</td>
							<td width="20%" valign="top">
								<b>$</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = '\$'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>$$</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = '\$\$'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>U</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'u'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
								<b>STAR</b><br>
								<?php
								$ires = mysql_query("select * from icons_aida where icon_type = 'star'");
								while ( $irow = mysql_fetch_array($ires) )
								{
									// count em
									$cres = mysql_query("select cst.cst_id from cst, icons_aida_ref where icons_aida_ref.cst_id = cst.cst_id && icons_aida_ref.icon_id = '" . $irow['icon_id'] . "' && person_id = '" . ( $_GET['person_id'] ? $_GET['person_id'] : $persondata[0] ) . "' && ( appointment >= '" . $from . "%' && appointment <= '" . $to . " 23:59:59' ) " . ( $this_lang_id ? "&& lang_id = '" . $this_lang_id . "'" : "" ) . "");
								
									echo $irow['icon_display'] . ': ' . mysql_num_rows($cres) . '<br>';
								}
								?>
								<br>
							</td>
						</tr>
					</table>
					</td>
				</tr>
				<tr>
					<td colspan="5"><br></td>
				</tr>
			</table>
		</td>
	</tr>
	</form>
		<tr bgcolor="#DADADA">
			<td width="15%" style="padding-left: 3px;">&nbsp;</td>
			<td width="15%">Company</td>
			<td width="25%">Contact, title</td>
			<td width="29%">Main / Direct / Mobile</td>
			<td width="8%">Appointment</td>
			<td width="8%">Initiated</td>
		</tr>
		<?php
		// Loop over result
		while ( $row = mysql_fetch_array($res) )
        {
			// Check for AIDA icons selection
			if ( $icon_c )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_c . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_o )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_o . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_s )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_s . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_v )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_v . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_e )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_e . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_d )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_d . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_S )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_S . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_SS )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id = '" . $icon_SS . "' limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_u )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id in (" . $icon_u . ") limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}
			if ( $icon_star )
			{
				// Check if this customer has this reference
				$ires = mysql_query("select * from icons_aida_ref where cst_id = '" . $row['cst_id'] . "' && icon_id = '" . $icon_star . "' limit 1");
				if ( !mysql_num_rows($ires) )
				{
					continue;
				}
			}

			// Split
			$date = explode(' ', $row['appointment']);

			// Set last date
			if ( !$last_date )
			{
				$bgcolor = 'FFFFFF';
				$last_date = $date[0];
				echo '<tr><td><br></td></tr>';
				echo '<tr bgcolor="#DADADA"><td colspan="6"><a name="' . $last_date . '"></a><b>' . $last_date . '</b></a><br></td></tr>';
			}

			// If different date new tr
			if ( $date[0] != $last_date )
			{
				$bgcolor = 'FFFFFF';
				echo '<tr><td><br></td></tr>';
				echo '<tr bgcolor="#DADADA"><td colspan="6"><a name="' . $date[0] . '"><b>' . $date[0] . '</b></a><br></td></tr>';
			}

			// Set last date
			$last_date = $date[0];

			// Reset font
			$comment = '';
			$font = '';

			// Get product details
			$p_res = mysql_query("select max(sale_id) as sale_id from saleslog where cst_id = '" . $row['cst_id'] . "'");
			$p_row = mysql_fetch_array($p_res);
			
			if ( $p_row['sale_id'] )
			{
				// Select sale data
				$p_res = mysql_query("select * from saleslog where sale_id = '" . $p_row['sale_id'] . "' && (status is null || status != 3) limit 1");
				$p_row = mysql_fetch_array($p_res);

				// Set color
				if ( $p_row['product_trial'] )
				{
					$comment = 'On Trial';
					$font = '<font color="darkgreen">';
				}
				elseif ( $p_row['product_type'] )
				{
					$comment = 'Paying Customer';
					$font = '<font color="darkblue">';
				}

				// Check if sold product has expired
				$p_res = mysql_query("select * from saleslog where cst_id = '" . $row['cst_id'] . "' && expires_date > now() limit 1");

				// If this gives no results then it has expired
				if ( !mysql_num_rows($p_res) )
				{
					$comment = 'Product Expired';
					$font = '<font color="red">';
				}
			}
		
			// Select icons
			$icons = '';
			$i_res = mysql_query("select icon from icon_types, icon_ref where icon_types.icon_id = icon_ref.icon_id && icon_ref.cst_id = '" . $row['cst_id'] . "'");
			
			while ( $i_row = mysql_fetch_array($i_res) )
			{
				$icons .= '<img src="/crmadmin/crm/sales/gfx/' . $i_row['icon'] . '.gif">  ';
			}
			
			// Select all AIDA icons
			$i_res = mysql_query("select icon_display from icons_aida, icons_aida_ref where icons_aida.icon_id = icons_aida_ref.icon_id && icons_aida_ref.cst_id = '" . $row['cst_id'] . "' order by icons_aida.icon_id");
			while ( $i_row = mysql_fetch_array($i_res) )
			{
				$icons .= $i_row['icon_display'] . ', ';
			}

			// Find first comment stamp
			$i_res = mysql_query("select min(comment_id) as comment_id from comments where cst_id = '" . $row['cst_id'] . "' && person_id = '" . $_GET['person_id'] . "'");
			$i_row = mysql_fetch_array($i_res);
			$i_res = mysql_query("select * from comments where comment_id = '" . $i_row['comment_id'] . "' limit 1");
			$i_row = mysql_fetch_array($i_res);
?>
<tr>
<td><?=substr($icons, 0, -2)?></td>
<td><a href="<?= HTTP_PATH_CRM2; ?>customer.php?cst_id=<?=$row['cst_id']?>" title="<?=$row['name']?>" alt="<?=$row['name']?>" target="_blank"><?=$font . ( strlen($row['name']) > 15 ? trim(substr($row['name'], 0, 15)) . '...' : $row['name'] )?></td>
<td><a href="<?= HTTP_PATH_CRM2; ?>customer.php?cst_id=<?=$row['cst_id']?>" title="<?=$row['contact']?>" alt="<?=$row['contact']?>" target="_blank"><?=$font . $row['contact']?></td>
<td><?=$font . $row['phone']?></td>
<td><?=$font . trim(substr($row['appointment'] , strpos($row['appointment'], ' '), 9))?></td>
<td><?=$font . substr($i_row['added'], 0, 10)?></td>
</tr>
<?php
			$last_app = $row['appointment'];
		}
?>

<tr>
<td><br><br></td>
</tr>

</table>

<?php
// Output footer
echo HTMLFooter();
?>
