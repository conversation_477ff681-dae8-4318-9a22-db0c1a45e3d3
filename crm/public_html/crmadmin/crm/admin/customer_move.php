<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Move customers
if ( $task == 'move' )
{
	// Loop over erresult
	while ( list($cst_id, $value) = @each($move) )
	{
		if ( $value )
		{
			// Select customer data
			$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
			$row = mysql_fetch_array($res);
	
			// Update person id on customer
			mysql_query("update cst set person_id = '" . $move_to_person_id . "' where cst_id = '" . $cst_id . "'");
	
			// Move offers issued by current owner
			mysql_query("update offers set person_id = '" . $move_to_person_id . "' where cst_id = '" . $cst_id . "' && person_id = '" . $row['person_id'] . "'");
		}
	}
}

// Make criteria
if ( $segment_id )
{
	$criteria = "segment_id = '" . $segment_id . "'";
}

if ( $person_id )
{
	$criteria .= ( $criteria ? ' && ' : '' ) . "person_id = '" . $person_id . "'";
}

if ( $search )
{
	$criteria .= ( $criteria ? ' && ' : '' ) . "(name like '%" . $search . "%' || phone like '" . $search . "%')";
}

// Select dead customers
$res = mysql_query("select name, person_id, cst_id, appointment from cst where " . $criteria . $sSQLAccessLimit . " order by " . ( $sort ? $sort : 'name' ));

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>

<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="4" width="100%">
                        Move Customers
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <form method="post" action="customer_move.php">
        <tr>
                <td colspan="4" width="100%">
                        Choose Sales Person<br>
                        <select name="person_id">
                        <option value="">Optional - Not Selected</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from salespeople order by name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
                        }
                        ?>
                        </select><br><br>
                        Choose Segment<br>
                        <select name="segment_id">
                        <option value="">Optional - Not Selected</option>
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from segments order by segment_name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['segment_id'] . '"' . ( $s_row['segment_id'] == $segment_id  ? 'selected' : '' ) . '>' . $s_row['segment_name'] .  '</option>';
                        }
                        ?>
                        </select><br><br>
                        Company Name / Phone<br>
                        <input type="text" name="search" value="<?=htmlspecialchars($search)?>"> - <input type="submit" value="Show Customers">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="5" width="100%">
                        <img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
                </td>
        </tr>
        <tr>
                <td colspan="5" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
        	<td width="40%">
                	<a href="customer_move.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=name"><b>Company Name</b></a>
                </td>
                <td width="25%">
                	<a href="customer_move.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=person_id"><b>Sales Person</b></a>
                </td>
                <td width="15%">
                	<a href="customer_move.php?segment_id=<?=htmlspecialchars($segment_id)?>&amp;person_id=<?=htmlspecialchars($person_id)?>&amp;sort=appointment"><b>Appointment</b></a>
                </td>
                <td width="10%">
                	<b>Status</b>
                </td>
		<td width="10%">
                	<b>Move</b>
	        </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <form method="post" action="customer_move.php">
        <input type="hidden" name="person_id" value="<?=htmlspecialchars($person_id)?>">
        <input type="hidden" name="segment_id" value="<?=htmlspecialchars($segment_id)?>">
        <input type="hidden" name="search" value="<?=htmlspecialchars($search)?>">
        <input type="hidden" name="task" value="move">
                <?php
                // Loop over result
                while ( $row = @mysql_fetch_array($res) )
                {
                ?>
        	<tr>
                	<td width="40%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=htmlentities($row['name'])?></a>
                        </td>
                	<td width="25%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=ReturnPersonNameFromID($row['person_id'])?></a>
                        </td>
                	<td width="15%">
                        	<a href="../sales/customer.php?cst_id=<?=$row['cst_id']?>"><?=$row['appointment']?></a>
                        </td>
                	<td width="10%">
                        	<?=( $row['appointment'] == '0000-00-00 00:00:00' ? 'Dead' : '' )?>
                        </td>
                	<td width="10%">
                        	<input type="checkbox" name="move[<?=$row['cst_id']?>]" value="1">
                        </td>
                <tr>
                <?php
                }
                ?>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="3" width="90%">
                </td>
                <td colspan="1" width="10%">
                        Move To Sales Person<br>
                        <select name="move_to_person_id">
                        <?php
                        // Select all salespeople
                        $s_res = mysql_query("select * from salespeople order by name");
                        while ( $s_row = mysql_fetch_array($s_res) )
                        {
                        	echo '<option value="' . $s_row['person_id'] . '">' . $s_row['name'] .  '</option>';
                        }
                        ?>
                        </select>
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="3" width="90%">
                </td>
                <td colspan="1" width="10%">
                        <input type="submit" value="Move">
                </td>
        </tr>
        <tr>
                <td colspan="4" width="100%">
                        <br><br>
                </td>
        </tr>
        </form>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
