<?php
// Initialise (function libraries and openening DB connection)
require("../configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Save submitted
if ( $task == 'update' )
{
	// Loop over result
        while ( list($sale_id, $value) = each($status) )
        {
        	// Check if new status differs from allready selected status
                $res = mysql_query("select * from saleslog where sale_id = '" . $sale_id . "' && status = '" . $value . "'");
                if ( !mysql_num_rows($res) )
                {
                	mysql_query("update saleslog set status = '" . $value . "', status_date = now(), online_payment_id = null where sale_id = '" . $sale_id . "'");
                }
        }
}

// Search
if ( $search )
{
	// Select result
        $res = mysql_query("select cst.cst_id, cst.name as cst_name, product_name, product_price, discount, sold_date, sale_id, saleslog.status, salespeople.name as sales_name from cst, saleslog, salespeople where cst.name like '%" . $search . "%' && cst.cst_id = saleslog.cst_id && saleslog.person_id = salespeople.person_id");

        // Loop over result
        while ( $row = mysql_fetch_array($res) )
        {
        	$result .= '<tr><td valing="top"><a href="../../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_BLANK">' . $row['cst_name'] . '</a></td>';
        	$result .= '<td valing="top">' . $row['sales_name'] . '</td>';
        	$result .= '<td valing="top">' . $row['product_name'] . '</td>';
        	$result .= '<td valing="top">' . ($row['product_price'] - $row['discount']) . ',-</td>';
        	$result .= '<td valing="top">' . $row['sold_date'] . '</td>';
                $result .= '<td valing="top"><input type="radio" value="0" name="status[' . $row['sale_id'] . ']" ' . ( !$row['status'] ? 'checked' : '' ) . '> Awaiting<br><input type="radio" value="2" name="status[' . $row['sale_id'] . ']" ' . ( $row['status'] == 2 ? 'checked' : '' ) . '> Paid<br><input type="radio" value="3" name="status[' . $row['sale_id'] . ']" ' . ( $row['status'] == 3 ? 'checked' : '' ) . '> Cancelled<br></td></tr>';

                // Spacer
        	$result .= '<tr><td colspan="6" width="100%"><br></td></tr>';
        }
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="6" width="100%">
                        Set Status For A Sale
                </td>
        </tr>
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
        <form method="post" action="sale_set_status.php">
        <tr>
                <td colspan="6" width="100%"><input type="text" name="search" value="<?=htmlspecialchars($search)?>"> - <input type="submit" value="Find - Customer"></td>
        </tr>
        </form>
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
        <tr>
                <td colspan="6" width="100%"><img src="../sales/gfx/orangebottom.gif" width="100%" height="4"></td>
        </tr>
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
        <tr>
                <td><b>Copany name</b></td>
                <td><b>Sales person</b></td>
                <td><b>Product</b></td>
                <td><b>Price</b></td>
                <td><b>Sold date</b></td>
                <td><b>Status</b></td>
        </tr>
        <form method="post" action="sale_set_status.php">
	<input type="hidden" name="task" value="update">
	<input type="hidden" name="search" value="<?=htmlspecialchars($search)?>">
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
        <?=$result?>
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
        <tr>
                <td colspan="5"><br></td>
                <td colspan="1"><input type="submit" value="Update"></td>
        </tr>
        </form>
        <tr>
                <td colspan="6" width="100%"><br></td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
