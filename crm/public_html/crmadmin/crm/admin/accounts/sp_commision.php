<?php
// Initialise (function libraries and openening DB connection)
require("../configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Update commision
if ( $task == 'commision' )
{
	// Loop over result
        while ( list($sale_id, $commision) = @each($mark) )
        {
        	mysql_query("update saleslog set commision_amount = '" . $commision . "', commision_date = now() where sale_id = '" . $sale_id . "'");
        }
}

// Initvars
$sSaleTypeNew = 'New.';
$sSaleTypeRecur = 'Recur.';
$sSaleTypeRecurUpsale = 'Recur. Upsale';

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="7">
			Calculate Commision
		</td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<form method="post" action="sp_commision.php">
	<tr>
		<td colspan="1" valign="top"><b>Choose Sales Person</b><br>
			<select name="person_id">
				<option value="">Not Selected</option>
				<?php
				// Select all salespeople
				$s_res = mysql_query("select * from salespeople order by name");
				while ( $s_row = mysql_fetch_array($s_res) )
				{
					echo '<option value="' . $s_row['person_id'] . '"' . ( $s_row['person_id'] == $person_id  ? 'selected' : '' ) . '>' . $s_row['name'] .  '</option>';
				}
				?>
				</select><br><br>
				<b>Period - (From -> To)</b><br>
				<input type="text" name="from" value="<?=( $_POST['from'] ? htmlspecialchars($_POST['from']) : ConvertGetDate('', 'MySQL-Date') )?>">&nbsp;-&nbsp;<input type="text" name="to" value="<?=( $_POST['to'] ? htmlspecialchars($_POST['to']) : ConvertGetDate('', 'MySQL-Date') )?>"><br>
				<br>
				<b>Search: Name / Phone</b>
				<input type="text" value="<?= htmlspecialchars($search) ?>" name="search"><br>
				<br>
				<input type="submit" value="Display">
			</select>
		</td>
		<td colspan="6" valign="top">
			<b>Colors:</b><br><br>
			Blue = Salesperson HAS NOT been paid xx percent in commison of the revenue<br>
			Green = Salesperson HAS been paid xx percent in commison of the revenue
		</td>
	</tr>
	</form>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><img src="../sales/gfx/orangebottom.gif" width="100%" height="4"></td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<tr>
		<td><b>Company Name</b></td>
		<td><b>Product</b></td>
		<td><b>Sold Date</b></td>
		<td><b>Paid Date</b></td>
		<td><b>Prod. Type</b></td>
                <td><b>Comm. Paid</b></td>
		<td><b>Price</b></td>
	</tr>
	<form method="post" action="sp_commision.php">
		<input type="hidden" name="task" value="commision">
		<input type="hidden" name="person_id" value="<?=htmlspecialchars($person_id)?>">
		<input type="hidden" name="from" value="<?=htmlspecialchars($_POST['from'])?>">
		<input type="hidden" name="to" value="<?=htmlspecialchars($_POST['to'])?>">
		<input type="hidden" name="search" value="<?=htmlspecialchars($search)?>">
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<?php
	// Select sales for salesperson
	$res = mysql_query("select currency_exchange_rate, currency_exchange_rate_euro, sale_id, commision_date, commision_amount, cst.cst_id, cst.master_id, name, product_name, product_price, discount, sold_date, status, status_date, currency, product_category, product_type from saleslog, cst where
	saleslog.cst_id = cst.cst_id &&
	(status_date >= '" . $_POST['from'] . "' && status_date <= '" . $_POST['to'] . "' ) &&
	saleslog.person_id = '" . $person_id . "' &&
	(name like '%" . $search . "%' || phone like '%" . $search . "%') &&
	saleslog.status = 2
	order by name");
	while ( $row = mysql_fetch_array($res) ) {
		// Skip if no amount
		if ( ($row['product_price'] - $row['discount']) < 1 ) {
			continue;
		}

		// Amount in EURO
		$iAmountEURO =  ( (float) ( $row['product_price'] - $row['discount'] ) ) * fFOREX( $row['currency_exchange_rate'], $row['currency_exchange_rate_euro'] ) ;

		// Determine product type
		if ( $row['product_category'] == 3 ) {
			$sProductType = 'NSI';
		} elseif ( $row['product_category'] == 2 ) {
			$sProductType = 'SS';
		} else {
			$sProductType = 'VI';
		}

		// Build list of all customer id's
		if ( $row['master_id'] ) {
			$iMasterID = $row['master_id'];
		} else {
			$iMasterID = $row['cst_id'];
		}
	
		$sCustomerIDs = $iMasterID . ',';
		$rAll = mysql_query("SELECT * from crm.cst WHERE master_id = '" . $iMasterID . "'");
		while ( $aAll = mysql_fetch_array($rAll) ) {
			$sCustomerIDs .= $aAll['cst_id'] . ',';
		}

		// Canvas or Recurrence
		{
			$sSaleType = '';
			$t_res = mysql_query("SELECT MAX(saleslog.sale_id) AS last_sale_id FROM crm.saleslog WHERE saleslog.sold_date < '" . $row['sold_date'] . "' AND saleslog.cst_id IN (" . trim($sCustomerIDs, ',') . ") AND (saleslog.product_price - saleslog.discount) > 0 AND saleslog.status = 2 AND saleslog.product_category = '" . $row['product_category'] . "'");
			$aLastSale = mysql_fetch_array($t_res);
			if ( !$aLastSale['last_sale_id'] ) {
				// New Sale
				$sSaleType = $sSaleTypeNew;
			} else {
				// Check if amount for this sale is higher than the previous sale
				$rLastSale = mysql_query("SELECT * FROM crm.saleslog WHERE saleslog.sale_id = '" . $aLastSale['last_sale_id'] . "' LIMIT 1");
				$aLastSale = mysql_fetch_array($rLastSale);
				$iLastSaleAmountEURO =  ( (float) ( $aLastSale['product_price'] - $aLastSale['discount'] ) ) * fFOREX( $aLastSale['currency_exchange_rate'], $aLastSale['currency_exchange_rate_euro'] ) ;

				// Check if amount is higher
				if ( $iAmountEURO > $iLastSaleAmountEURO ) {
					// Upsale recurrence
					$sSaleType = $sSaleTypeRecurUpsale;
				} else {
					// Normal recurrence
					$sSaleType = $sSaleTypeRecur;
				}
			}
		}

		// Total amount
		$total += $iAmountEURO;

		// Total - Product Type
		$aTotals[$sProductType]['total'] += $iAmountEURO;

		// Total - Product Type - New / Recur.
		$aTotals[$sProductType]['total_' . $aSaleType] += $iAmountEURO;

		// Total - New / Recur.
		$aTotals['Total']['total_' . $sSaleType] += $iAmountEURO;

		// Font color
		if ( $row['commision_date'] ) {
			$font_color = 'green';
			$aTotals['Total']['commision_paid' . $sSaleType] += $iAmountEURO;

			// Add to total
			$aTotals[$sProductType]['commision_paid' . $sSaleType] += $iAmountEURO;
		} else {
			$font_color = 'blue';
			$aTotals['Total']['commision_not_paid' . $sSaleType] += $iAmountEURO;

			// Add to total
			$aTotals[$sProductType]['commision_not_paid' . $sSaleType] += $iAmountEURO;
		}

		// Color
		if ( $bgcolor != '#DEDEDE' ) {
			$bgcolor = '#DEDEDE';
		} else {
			$bgcolor = '#FFFFFF';
		}
		?>
		<tr bgcolor="<?= $bgcolor ?>">
			<input type="hidden" name="mark[<?=$row['sale_id']?>]" value="<?= $iAmountEURO ?>">
			<td width="20%"><a href="../../sales/customer.php?cst_id=<?=$row['cst_id']?>" target="_blank"><font color="<?=$font_color?>"><?= htmlspecialchars($row['name']) ?></a></td>
			<td width="33%"><a href="../../sales/customer.php?cst_id=<?=$row['cst_id']?>" target="_blank"><font color="<?=$font_color?>"><?= htmlspecialchars($row['product_name']) ?></a></td>
			<td width="10%"><a href="../../sales/customer.php?cst_id=<?=$row['cst_id']?>" target="_blank"><font color="<?=$font_color?>"><?=$row['sold_date']?></a></td>
			<td width="10%"><a href="../../sales/customer.php?cst_id=<?=$row['cst_id']?>" target="_blank"><font color="<?=$font_color?>"><?=$row['status_date']?></a></td>
			<td width="7%"><font color="<?=$font_color?>"><?= $sProductType . " (" . $sSaleType . ")" ?></td>
                        <td width="10%"><font color="<?=$font_color?>"><?= ( $font_color == 'green' ? 'Paid' : 'Not Paid' ) ?></td>
			<td width="10%" align="right"><font color="<?=$font_color?>">EURO <?= number_format($iAmountEURO) ?></td>
		</tr>
		<?php
	}
	?>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
	<tr bgcolor="white">
		<td colspan="2" valign="top">
			<br>
			<b>Totals</b><br>
			&nbsp;&nbsp;Customer Revenue Paid: Total<br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecurUpsale ?><br>
			<br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecurUpsale ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecurUpsale ?><br>
		</td>
		<td valign="top" align="right">
			<br>
			<br>
			<?= number_format($total) ?>,-<br>
			<?= number_format($aTotals['Total']['total_' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['Total']['total_' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['Total']['total_' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<br>
			<?= number_format($aTotals['Total']['commision_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['Total']['commision_not_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['Total']['commision_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['Total']['commision_not_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['Total']['commision_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<?= number_format($aTotals['Total']['commision_not_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
		</td>
		<td colspan="3" valign="top"><input type="submit" value="Mark As Paid Commision"></td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br><br></td>
	</tr>
	<tr bgcolor="white">
		<td colspan="2" valign="top">
			<br>
			<b>VI - Totals</b><br>
			&nbsp;&nbsp;Customer Revenue Paid: Total<br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecurUpsale ?><br>
			<br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecurUpsale ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecurUpsale ?><br>
		</td>
		<td valign="top" align="right">
			<br>
			<br>
			<?= number_format($aTotals['VI']['total']) ?>,-<br>
			<?= number_format($aTotals['VI']['total_' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['VI']['total_' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['VI']['total_' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<br>
			<?= number_format($aTotals['VI']['commision_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['VI']['commision_not_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['VI']['commision_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['VI']['commision_not_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['VI']['commision_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<?= number_format($aTotals['VI']['commision_not_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
		</td>
		<td colspan="1" valign="top"></td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br><br></td>
	</tr>
	<tr bgcolor="white">
		<td colspan="2" valign="top">
			<br>
			<b>SS - Totals</b><br>
			&nbsp;&nbsp;Customer Revenue Paid: Total<br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecurUpsale ?><br>
			<br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecurUpsale ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecurUpsale ?><br>
		</td>
		<td valign="top" align="right">
			<br>
			<br>
			<?= number_format($aTotals['SS']['total']) ?>,-<br>
			<?= number_format($aTotals['SS']['total_' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['SS']['total_' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['SS']['total_' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<br>
			<?= number_format($aTotals['SS']['commision_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['SS']['commision_not_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['SS']['commision_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['SS']['commision_not_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['SS']['commision_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<?= number_format($aTotals['SS']['commision_not_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
		</td>
		<td colspan="1" valign="top"></td>
	</tr>
	<tr>
		<td colspan="7" width="100%"><br><br></td>
	</tr>
	<tr bgcolor="white">
		<td colspan="2" valign="top">
			<br>
			<b>NSI - Totals</b><br>
			&nbsp;&nbsp;Customer Revenue Paid: Total<br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Customer Revenue Paid: <?= $sSaleTypeRecurUpsale ?><br>
			<br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeNew ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecur ?><br>
			&nbsp;&nbsp;Sales Person Commission Paid: <?= $sSaleTypeRecurUpsale ?><br>
			&nbsp;&nbsp;Sales Person Commission Not Paid: <?= $sSaleTypeRecurUpsale ?><br>
		</td>
		<td valign="top" align="right">
			<br>
			<br>
			<?= number_format($aTotals['NSI']['total']) ?>,-<br>
			<?= number_format($aTotals['NSI']['total_' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['NSI']['total_' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['NSI']['total_' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<br>
			<?= number_format($aTotals['NSI']['commision_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['NSI']['commision_not_paid' . $sSaleTypeNew]) ?>,-<br>
			<?= number_format($aTotals['NSI']['commision_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['NSI']['commision_not_paid' . $sSaleTypeRecur]) ?>,-<br>
			<?= number_format($aTotals['NSI']['commision_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
			<?= number_format($aTotals['NSI']['commision_not_paid' . $sSaleTypeRecurUpsale]) ?>,-<br>
		</td>
		<td colspan="1" valign="top"></td>
	</tr>
	</form>
	<tr>
		<td colspan="7" width="100%"><br></td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
