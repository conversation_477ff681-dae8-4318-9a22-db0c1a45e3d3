<?php
// Initialise (function libraries and openening DB connection)
require("../configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Commission Levels
$aCommissionLevels = array
(
	0 => 100,
	1 => 120,
	2 => 145,
	3 => 175,
	4 => 200,
	5 => 220
);

// Save Data
if ( $_POST['action'] == 'save' )
{
	// Loop through each value
	while ( list($iSaleID, $iCommissionLevel) = each($_POST['commission_level']) )
	{
		if ( strlen($iCommissionLevel) == 1 )
		{
			$sQuery = "UPDATE crm.saleslog SET commission_level = '" . $iCommissionLevel . "' WHERE sale_id = '" . intval($iSaleID) . "' LIMIT 1";
			mysql_query($sQuery);
		}
	}

	// Redirect
	header("Location: commission_overview.php?person_id=" . $_POST['person_id']);
	exit();
}

// Generate Page Content
$sContent = '';
$sSalesPerson = '';
if ( $_GET['person_id'] )
{
	// Select all sales made since the Sales Rep. started ( according to crm.forecast_start_month )
	$sQuery = "SELECT * FROM crm.salespeople WHERE person_id = '" . $_GET['person_id'] . "' LIMIT 1";
	$rRes = mysql_query($sQuery);
	$aRow = mysql_fetch_array($rRes);

	// Name
	$sSalesPerson = $aRow['name'];

	// Month Counter
	$iMonthCounter = 0;

	// Commission Level Register
	$aCommisionLevelRegister = array();

	// Select all sales made since this date
	$sQuery = "SELECT * FROM crm.saleslog, crm.cst WHERE (product_price-discount) > 0 && saleslog.cst_id = cst.cst_id && saleslog.person_id = '" . $_GET['person_id'] . "' && sold_date >= '" . $aRow['forecast_start_month'] . "' order by sold_date";
	$rSalesRes = mysql_query($sQuery);
	while ( $aSalesRow = mysql_fetch_array($rSalesRes) )
	{
		// USA Sales should be weighted 5% higher (!)
		$sBonusMark = '';
		if ( $aSalesRow['invoice_country'] == 10 )
		{
			// Add 5% to product price
			$aSalesRow['product_price'] *= 1.05;

			// Clearly mark the customer name as having received this bonus!
			$aSalesRow['name'] .= ' (ADDED %5 US BONUS)';
			$sBonusMark = '(+5%) ';
		}

		// Month output
		if ( $sLastMonth != substr($aSalesRow['sold_date'], 0, 7) )
		{
			// Output "Month" Total and Commission Level
			if ( $sLastMonth )
			{
				// Commission Percentage
				$iCommissionPercentage = round(($iMonthlyRevenue / $aTargetRow['revenue']) * 100);

echo $iMonthlyRevenue . '->' . $aTargetRow['revenue'] . '->' . $iMonthCounter . "\n";

				// Calculate Commission Level
				{
					if ( $iCommissionPercentage > $aCommissionLevels[5] )
					{
						$iCommissionLevel = 5;
					}
					elseif ( $iCommissionPercentage > $aCommissionLevels[4] )
					{
						$iCommissionLevel = 4;
					}
					elseif ( $iCommissionPercentage > $aCommissionLevels[3] )
					{
						$iCommissionLevel = 3;
					}
					elseif ( $iCommissionPercentage > $aCommissionLevels[2] )
					{
						$iCommissionLevel = 2;
					}
					elseif ( $iCommissionPercentage > $aCommissionLevels[1] )
					{
						$iCommissionLevel = 1;
					}
					else
					{
						$iCommissionLevel = 0;
					}
				}

				// Check if there are ANY mismatches in the commission calculation
				// 1) $aCommisionLevelRegister must ONLY contain 1 entry type 
				// 2) The Calculated Commission Level MUST be present in $aCommisionLevelRegister, unless $aCommisionLevelRegister is empty
				$sCommissionMismatch = 'No';
				if ( count($aCommisionLevelRegister) > 1 || ( !$aCommisionLevelRegister[$iCommissionLevel] && count($aCommisionLevelRegister) > 0 ) )
				{
					$sCommissionMismatch = '<font color="RED">Yes! There are currently sales registered with a different "Commission Level" then the one calculated by the system. <a href="commission_overview.php?task=adjust_selected&amp;person_id=' . $_GET['person_id'] . '" onClick="return confirm(\'Are you sure that you wish to edit the selected Commission Levels?\');">Please adjust</a>.</font>';
				}

				$sContent .= '
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="6">
			<b>
			Total:<br>
			' . number_format($iMonthlyRevenue) . ',-<br>
			<br>
			Compared to Target:<br>
			' . $iCommissionPercentage . '%<br>
			<br>
			Commission Level:<br>
			' . $iCommissionLevel . '<br>
			<br>
			Commision Mismatch:<br>
			' . $sCommissionMismatch . '
			</b>
		</td>
	</tr>';
			}

			// Calculate Month Diff
			{
				// Current Month
				$aCurrentDate = split('-', $aSalesRow['sold_date']);
				$iMonths = ($aCurrentDate[0] * 12) + $aCurrentDate[1];

				// Start Data
				$aCurrentDate = split('-', $aRow['forecast_start_month']);
				$iMonthsDiff = $iMonths - ( ($aCurrentDate[0] * 12) + $aCurrentDate[1] ) + 1;
			}

			// Select target for this month
			$sQuery = "SELECT * FROM crm.forecast WHERE person_id = '" . $_GET['person_id'] . "' && month = '" . $iMonthsDiff . "' LIMIT 1";
			$rTargetRes = mysql_query($sQuery);
			$aTargetRow = mysql_fetch_array($rTargetRes);

			// Output "Month" Header
			$sContent .= '
	<tr>
		<td>' . returnPageDelimeter(6, '100%', 1) . '</td>
	</tr>
	<tr>
		<td colspan="6"><h2>' . substr($aSalesRow['sold_date'], 0, 7) . ' ( Target: &euro; ' . number_format($aTargetRow['revenue']) . ',- )</h2></td>
	</tr>
	<tr>
		<td width="20%" style="padding-left: 5px;"><b>Customer Name</b></td>
		<td width="40%"><b>Product</b></td>
		<td width="10%"><b>State</b></td>
		<td width="10%"><b>Commission Level</b></td>
		<td width="10%"><b>Amount Local</b></td>
		<td width="10%"><b>Amount EURO</b></td>
	</tr>';

			// Customer Counter
			$iCustomerCountMonth = 0;

			// Revenue Container
			$iMonthlyRevenue = 0;

			// Commission Level Register
			$aCommisionLevelRegister = array();
		}
		$sLastMonth = substr($aSalesRow['sold_date'], 0, 7);

		// Sale Status
		switch ( $aSalesRow['status'] )
		{
			case 2:
				$sFormatting = '<font color="Blue">';
				$sSaleStatus = 'Paid';
				break;
			case 3:
				$sFormatting = '<i><font color="Red">';
				$sSaleStatus = 'Cancelled';
				break;
			default:
				$sFormatting = '';
				$sSaleStatus = 'Awaiting';
		}

		// Commission State
		if ( strlen($aSalesRow['commission_level']) == 1 && $_GET['task'] != 'adjust_selected' )
		{
			$sCommissionState = 'Level ' . $aSalesRow['commission_level'];

			// Register that there is an entry at this Commission Level
			$aCommisionLevelRegister[$aSalesRow['commission_level']] = true;
		}
		elseif ( $aSalesRow['status'] == 2 )
		{
			$sCommissionState = '
			<select name="commission_level[' . $aSalesRow['sale_id'] . ']" onClick="return false;">
				<option value="">Select</option>
				<option value="0"' . ( $aSalesRow['commission_level'] == 0 && strlen($aSalesRow['commission_level']) == 1 ? 'selected' : '' ) . '>0</option>
				<option value="1"' . ( $aSalesRow['commission_level'] == 1 ? 'selected' : '' ) . '>1</option>
				<option value="2"' . ( $aSalesRow['commission_level'] == 2 ? 'selected' : '' ) . '>2</option>
				<option value="3"' . ( $aSalesRow['commission_level'] == 3 ? 'selected' : '' ) . '>3</option>
				<option value="4"' . ( $aSalesRow['commission_level'] == 4 ? 'selected' : '' ) . '>4</option>
				<option value="5"' . ( $aSalesRow['commission_level'] == 5 ? 'selected' : '' ) . '>5</option>
			</select>';
			
			// Register that there is an entry at this Commission Level
			if ( $_GET['task'] == 'adjust_selected' )
			{
				$aCommisionLevelRegister[$aSalesRow['commission_level']] = true;
			}
		}
		else
		{
			$sCommissionState = 'N/A';
		}

		// Actual content
		$sContent .= '
		<tr onMouseOver="this.setAttribute(\'class\', \'hoverTRPointer\');" onMouseOut="this.setAttribute(\'class\', \'noHoverTRPointer\');">
			<td valign="top" style="padding-left: 5px;" onClick="window.open(\'/crmadmin/crm/sales/customer.php?cst_id=' . $aSalesRow['cst_id'] . '\', \'_blank\')">' . ++$iCustomerCountMonth . '. ' . $sFormatting . htmlspecialchars($aSalesRow['name']) . '</td>
			<td valign="top" onClick="window.open(\''.HTTP_PATH.'customer.php?cst_id=' . $aSalesRow['cst_id'] . '\', \'_blank\')">' . $sFormatting . htmlspecialchars($aSalesRow['product_name']) . '</td>
			<td valign="top" onClick="window.open(\''.HTTP_PATH.'customer.php?cst_id=' . $aSalesRow['cst_id'] . '\', \'_blank\')">' . $sFormatting . $sSaleStatus . '</td>
			<td valign="top">' . $sFormatting . $sCommissionState . '</td>
			<td valign="top" align="right" onClick="window.open(\''.HTTP_PATH.'customer.php?cst_id=' . $aSalesRow['cst_id'] . '\', \'_blank\')">' . $sFormatting . $sBonusMark . number_format($aSalesRow['product_price'] - $aSalesRow['discount']) . ',-&nbsp;</td>
			<td valign="top" align="right" onClick="window.open(\''.HTTP_PATH.'customer.php?cst_id=' . $aSalesRow['cst_id'] . '\', \'_blank\')">' . $sFormatting . $sBonusMark . number_format(( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] )) . ',-&nbsp;</td>
		</tr>';

		// Totals ( 2 = PAID | 3 = CANCELLED | AWAITING )
		if ( $aSalesRow['status'] == 2 )
		{
			$iTotalPaid++;
			$iTotalPaidAmount += ( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] );
			$iMonthlyRevenue += ( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] );
		}
		elseif ( $aSalesRow['status'] == 3 )
		{
			$iTotalCancelled++;
			$iTotalCancelledAmount += ( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] );
		}
		else
		{
			$iTotalAwaiting++;
			$iTotalAwaitingAmount += ( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] );
			$iMonthlyRevenue += ( (float) ( $aSalesRow['product_price'] - $aSalesRow['discount'] ) ) * fFOREX( $aSalesRow['currency_exchange_rate'], $aSalesRow['currency_exchange_rate_euro'] );
		}
	}

	// Commission Percentage
	$iCommissionPercentage = round(($iMonthlyRevenue / $aTargetRow['revenue']) * 100);

	// Calculate Commission Level
	{
		if ( $iCommissionPercentage > $aCommissionLevels[5] )
		{
			$iCommissionLevel = 5;
		}
		elseif ( $iCommissionPercentage > $aCommissionLevels[4] )
		{
			$iCommissionLevel = 4;
		}
		elseif ( $iCommissionPercentage > $aCommissionLevels[3] )
		{
			$iCommissionLevel = 3;
		}
		elseif ( $iCommissionPercentage > $aCommissionLevels[2] )
		{
			$iCommissionLevel = 2;
		}
		elseif ( $iCommissionPercentage > $aCommissionLevels[1] )
		{
			$iCommissionLevel = 1;
		}
		else
		{
			$iCommissionLevel = 0;
		}
	}

	// Check if there are ANY mismatches in the commission calculation
	// 1) $aCommisionLevelRegister must ONLY contain 1 entry type 
	// 2) The Calculated Commission Level MUST be present in $aCommisionLevelRegister, unless $aCommisionLevelRegister is empty
	$sCommissionMismatch = 'No';
	if ( count($aCommisionLevelRegister) > 1 || ( !$aCommisionLevelRegister[$iCommissionLevel] && count($aCommisionLevelRegister) > 0 ) )
	{
		$sCommissionMismatch = '<font color="RED">Yes! There are currently sales registered with a different "Commission Level" then the one calculated by the system. <a href="commission_overview.php?task=adjust_selected&amp;person_id=' . $_GET['person_id'] . '" onClick="return confirm(\'Are you sure that you wish to edit the selected Commission Levels?\');">Please adjust</a>.</font>';
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="6" width="100%">
			Commission Overview
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<form method="GET" action="commission_overview.php">
	<tr>
		<td colspan="6">
			<b>Select sales person</b><br>
			<?php
			// Select all salespeople
			echo returnSelectBox(array($_GET['person_id']), 'person_id', false, 'SELECT * FROM salespeople ORDER BY name', 'person_id', 'name');
			?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="6"><input type="submit" value="Display" class="submit" style="width: 10%;"></td>
	</tr>
	</form>

	<?= returnPageDelimeter(6) ?>

	<tr>
		<td colspan="6">
			<h2><?= $sSalesPerson . ' ( Start Month: ' . $aRow['forecast_start_month'] . ' )'?></h2>
		</td>
	</tr>

	<tr>
		<td colspan="6">
			<b>Commission Levels:</b><br>
			1: <?=$aCommissionLevels[1]?>%<br>
			2: <?=$aCommissionLevels[2]?>%<br>
			3: <?=$aCommissionLevels[3]?>%<br>
			4: <?=$aCommissionLevels[4]?>%<br>
			5: <?=$aCommissionLevels[5]?>%<br>
		</td>
	</tr>

	<tr>
		<td colspan="6">
			<br>
		</td>
	</tr>

	<form method="POST" action="commission_overview.php">
	<input type="hidden" name="action" value="save">
	<input type="hidden" name="person_id" value="<?= intval($_GET['person_id']) ?>">

	<?= $sContent ?>

	<tr>
		<td colspan="6">
			<br>
		</td>
	</tr>

	<tr>
		<td colspan="6">
			<b>
			Total:<br>
			<?= number_format($iMonthlyRevenue) ?>,-<br>
			<br>
			Compared to Target:<br>
			<?= $iCommissionPercentage ?>%<br>
			<br>
			Commission Level:<br>
			<?= $iCommissionLevel ?><br>
			<br>
			Commision Mismatch:<br>
			<?= $sCommissionMismatch ?>
			</b>
		</td>
	</tr>

	<tr>
		<td colspan="6">
			<br>
		</td>
	</tr>

	<?= returnPageDelimeter(6, '100%', 1) ?>

	<tr>
		<td colspan="3">
			<br>
		</td>
		<td colspan="3">
			<input type="submit" value="Save Data">
		</td>
	</tr>

	</form>

	<tr>
		<td colspan="6">
			<br>
		</td>
	</tr>

</table>

<?php
// Output footer
echo HTMLFooter();
?>
