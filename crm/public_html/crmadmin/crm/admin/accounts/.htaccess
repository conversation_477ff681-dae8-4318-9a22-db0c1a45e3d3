AuthUserFile /home/<USER>/.htpasswd
AuthGroupFile /dev/null
AuthName "Secunia"
AuthType Basic
<Limit GET POST PUT DELETE CONNECT OPTIONS PATCH PROPFIND PROPPATCH MKCOL COPY MOVE LOCK UNLOCK>
Deny from all
#Secunia
#Allow from 80.161.200.182
#TS2
Allow from 192.168.50.71
Allow from 192.168.50.72
Allow from 192.168.50.74
Allow from 192.168.50.75
Allow from 172.16

#Windows PRODUCTION
Allow from 192.168.53.71
Allow from 192.168.53.62
Allow from 192.168.53.63
Allow from 192.168.53.124
Allow from 192.168.53.121
Allow from 192.168.53.124
Allow from 192.168.53.21
Allow from 192.168.53.254
Allow from 192.168.50.52
Allow from 192.168.54
Allow from 192.168.53.17
Allow from 89.233.1.75
Allow from 172.25.0.10
Allow from 172.16

Require user nt
Require user dj
Require user jb
Require user mmansson
Require user ldamgaard
Require user misraelsen
Require user ao
Require user sagerskov
Require user polsen
Require user sandersen
Require user pgrau
Require user mkakani
Require user stimmerman
Require user bhusted
Require user rlyck
Require user jchristensen
Require user nnielsen
Require user jbratting
Require user rjensen
Require user cbender
Require user bflottmann
Require user sojensen

Order deny,allow
</Limit>
