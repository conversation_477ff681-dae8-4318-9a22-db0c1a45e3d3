<?php
// Initialise (function libraries and openening DB connection)
require("../configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Save submitted
if ( $_POST['task'] == 'update' ) {
	// Loop over result (sales)
	while ( list($iSaleID, $iValue) = each($_POST['status']) ) {
		// Check if new status differs from allready selected status
		$res = mysql_query("select * from saleslog where sale_id = '" . (int) $iSaleID . "' && status = '" . (int) $iValue . "'");
		if ( !mysql_num_rows($res) && $iValue ) {
			mysql_query("update saleslog set status = '" . (int) $iValue . "', status_date = now(), online_payment_id = null where sale_id = '" . (int) $iSaleID . "'");
		}
	}

	// Loop over result (accounts)
	while ( list($iAccountID, $sValue) = each($_POST['account']) ) {
		if ( $sValue ) {
			mysql_query("UPDATE ca.accounts set accounts.account_expires = NULL WHERE accounts.account_id = '" . $iAccountID . "' LIMIT 1");
		}
	}

        // Loop over result (licenses)
        while ( list($iLicenseID, $sValue) = each($_POST['licenses']) ) {
                if ( $sValue ) {
                        mysql_query("UPDATE ca.license_keys set valid_to = NULL WHERE id = '" . $iLicenseID . "' LIMIT 1");
                }
        }
}

// Search
$sResult = '';
if ( $_POST['search'] ) {
	// Select result
	$rRes = mysql_query("select * from crm.cst where cst.name like '%" . $_POST['search'] . "%'");

	// Loop over result
	while ( $aCustomer = mysql_fetch_array($rRes) ) {
		// Sales details
		$rSales = mysql_query("SELECT * FROM crm.saleslog WHERE saleslog.cst_id = '" . $aCustomer['cst_id'] . "' AND (saleslog.product_price - saleslog.discount) > 0 ORDER BY saleslog.sold_date DESC");

		// Skip if no sales
		if ( !mysql_num_rows($rSales) ) {
			continue;
		}

		// Company Details
		$sResult .= '
		<tr>
			<td><b><a href="'.HTTP_PATH_CRM2.'?page=customer&cst_id=' . $aCustomer['cst_id'] . '"><h2>' . htmlspecialchars($aCustomer['name']) . '</h2></a></b></td>
			<td>' . ( mysql_num_rows($rSales) == 1 ? '1 sale' : mysql_num_rows($rSales) . ' sales' ) . '</td>
		</tr>
		<tr>
			<td><b>Sales:</b></td>
		<tr>';

		// Sales
		while ( $aSale = mysql_fetch_array($rSales) ) {
			$sResult .= '
		<tr>
			<td>
				<select name="status[' . $aSale['sale_id'] . ']">
					<option value="0"' . ( !$aSale['status'] ? ' selected' : '' ) . '>Awaiting</option>
					<option value="2"' . ( $aSale['status'] == 2 ? ' selected' : '' ) . '>Paid</option>
					<option value="3"' . ( $aSale['status'] == 3 ? ' selected' : '' ) . '>Cancelled - Loss on debtor</option>
					<option value="6"' . ( $aSale['status'] == 6 ? ' selected' : '' ) . '>Cancelled - Wrong registration</option>
					<option value="7"' . ( $aSale['status'] == 7 ? ' selected' : '' ) . '>Cancelled - Invoice sent</option>
					<option value="4"' . ( $aSale['status'] == 4 ? ' selected' : '' ) . '>Rejected</option>
					<option value="5"' . ( $aSale['status'] == 5 ? ' selected' : '' ) . '>Approved</option>
				</select>
				&nbsp;&nbsp;' . htmlspecialchars($aSale['product_name']) . ' (ID: ' . $aSale['sale_id'] . ')</td>
			<td>' . htmlspecialchars($aSale['currency']) . ' ' . number_format($aSale['product_price']-$aSale['discount']) . ',-</td>
			<td>' . ReturnPersonNameFromID($aSale['person_id']) . '</td>
			<td>' . $aSale['sold_date'] . ' (' . $aSale['invoice_start_date'] . ') - ' . $aSale['expires_date'] . '</td>
		</tr>';
		}

		// Headline
		$sResult .= '
		<tr>
			<td><br><b>Customer Area Accounts:</b> - <font color="red">If there are no active sales, there should _not_ be any active accounts!</font></td>
		</tr>';

		// Accounts
		$sAccountIDs = '';
		$aCustomerDetails = fGetCustomerDetails( $aCustomer['cst_id'] );
		$rAccounts = mysql_query("SELECT * FROM ca.accounts WHERE accounts.cst_id IN(" . $aCustomerDetails['AllIDs'] . ") ORDER BY accounts.account_id DESC");
		while ( $aAccount = mysql_fetch_array($rAccounts) ) {
			$sResult .= '
			<tr>
				<td>
					' . ( !$aAccountIDs[$aAccount['account_id']] ? '
					<select name="account[' . $aAccount['account_id'] . ']">
						<option value="0">' . ( strtotime($aAccount['account_expires']) < time() ? 'Expired' : $aAccount['account_expires'] ) . '</option>
						<option value="expire">Expire Account</option>
					</select>' : 'Skipped, displayed above' ) . '
					&nbsp;&nbsp;' . htmlspecialchars($aAccount['account_username']) . ' (' . ( !$aAccount['account_esm'] ? 'Master' : 'Child' ) . ')</td>
			</tr>';

			$sAccountIDs .= $aAccount['account_id'] . ',';
			$aAccountIDs[$aAccount['account_id']] = true;
		}

		// Headline
		$sResult .= '
		<tr>
			<td><br><b>License Keys</b> - <font color="red">If there are no active sales, there should _not_ be any active licenses!</font></td>
		</tr>';

		// License Keys
		$rLicenses = mysql_query("SELECT * FROM ca.license_keys WHERE license_keys.account_id IN(" . trim($sAccountIDs, ',') . ")");
		while ( $aLicense = mysql_fetch_array($rLicenses) ) {
			$sResult .= '
			<tr>
				<td>
					' . ( !$aLicenseKeys[$aLicense['id']] ? '
					<select name="licenses[' . $aLicense['id'] . ']">
						<option value="0">' . ( strtotime($aLicense['valid_to']) < time() ? 'Expired' : $aLicense['valid_to'] ) . '</opt
ion>
						<option value="expire">Expire License</option>
					</select>' : 'Skipped, displayed above' ) . '
					&nbsp;&nbsp;' . htmlspecialchars($aLicense['license']) . '</td>
			</tr>';

			$aLicenseKeys[$aLicense['id']] = true;
		}

		// Spacer
		$sResult .= '
		<tr>
			<td colspan="6"><br><hr></td>
		</tr>';

	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="6">
                        Set Status For A Sale
                </td>
        </tr>
        <tr>
                <td><br></td>
        </tr>
        <form method="post" action="sale_set_status_new.php">
        <tr>
                <td colspan="6">
			<input type="text" name="search" value="<?=htmlspecialchars($search)?>" style="width: 15%;"> - <input type="submit" value="Find - Customer" style="width: 15%;">
		</td>
        </tr>
        </form>
        <tr>
                <td><br></td>
        </tr>
        <tr>
                <td colspan="6"><img src="<?= HTTP_PATH; ?>gfx/orangebottom.gif" width="100%" height="4"></td>
        </tr>
        <tr>
                <td><br></td>
        </tr>
        <form method="post" action="sale_set_status_new.php">
	<input type="hidden" name="task" value="update">
	<input type="hidden" name="search" value="<?=htmlspecialchars($search)?>">
        <tr>
                <td colspan="6"><br></td>
        </tr>
        <?= $sResult ?>
        <tr>
                <td colspan="6"><br></td>
        </tr>
        <tr>
                <td><input type="submit" value="Update" style="width: 10%;"></td>
        </tr>
        </form>
        <tr>
                <td colspan="6"><br></td>
        </tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
