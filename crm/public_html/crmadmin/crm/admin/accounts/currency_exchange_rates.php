<?php
// Initialise (function libraries and openening DB connection)
require("../configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Handle currency
	switch ( $_GET['action'] ){
		case "update":
			if ( is_numeric( $_GET['which'] ) && ( is_numeric( $_GET['value'] ) ) ){
				DBQuery("UPDATE crm.country_currency SET currency = '".$_GET['value']."' WHERE id = '".$_GET['which']."'");
				header("Location: currency_exchange_rates.php");
				die();
			}
			break;
		case "delete":
			if ( is_numeric( $_GET['which'] ) ){
				DBQuery("DELETE FROM crm.country_currency WHERE id = '".$_GET['which']."'");
				header("Location: currency_exchange_rates.php");
				die();
			}
			break;
		case "new":
			if ( is_numeric( $_POST['value'] ) && ( $_POST['name'] != "" ) ) {
				DBQuery("INSERT INTO crm.country_currency ( `currency_name`, `currency` ) VALUES ( '". strtoupper( $_POST['name'] ) ."', '".$_POST['value']."' )");
				header("Location: currency_exchange_rates.php");
				echo mysql_error();
				die();
			}
			break;
	}

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

if ( $_GET['show'] == 1 ){
	// Fetch EURO value
	$fEURO = DBGetRowValue( "crm.country_currency", "currency", "currency_name = 'EURO' OR currency_name = 'EUR'" );
}

$rResult = DBQuery("SELECT * FROM crm.country_currency ORDER BY currency_name ASC");
$iNumRows = mysql_num_rows( $rResult );
$fEuro = 0; // Store the value for one euro
for ( $i = 0; $i < $iNumRows; $i++ ){
	$aRow = mysql_fetch_array( $rResult );
	if ( ( strtolower( $aRow['currency_name'] ) == 'euro' ) || ( strtolower( $aRow['currency_name'] ) == 'eur' ) ){
		$fEuro = $aRow['currency'];
	}
	if ( ( strtolower( $aRow['currency_name'] ) == 'euro' ) || ( strtolower( $aRow['currency_name'] ) == 'eur' ) ){
		if ( $_GET['show'] == 1 ){
			continue;
		}
	}
	$sContent .= "
	<tr>
		<td>
			".htmlspecialchars( $aRow['currency_name'] )."
		</td>
		<td>
			".( ( $_GET['show'] == 1 ) ? number_format( fFOREX( $aRow['currency'], $fEuro ), 4 ) : (float) $aRow['currency'] )."
		</td>
		<td>
			<input type=\"button\" value=\"Update\" onclick=\"updateCurrency( ".(int) $aRow['id'] ." )\" ><input type=\"button\" onclick=\"deleteCurrency(".(int) $aRow['id'].", '".htmlspecialchars( str_replace( "'", "\\'", str_replace( "\\", "\\\\", $aRow['currency_name'] ) ) )."')\" value=\"Delete\">
		</td>
	</tr>";
}


?>
Currency exchange rates ( against <?= ( ( $_GET['show'] == 1 ) ? 'EURO ) [<a href="?show=0">Display against DKK</a>]' : 'DKK ) [<a href="?show=1">Display against EURO</a>]' ) ?>

<script language="javascript">
	function updateCurrency( iCId ){
		var iNewValue = prompt( "New exchange rate (to DKK):" );
		document.location = 'currency_exchange_rates.php?action=update&which=' + iCId + '&value=' + iNewValue;
	}
	function deleteCurrency( iCId, sName ){
		var bQuestion = confirm("Delete: " + sName + "?" );
		if ( bQuestion == true ){
			document.location = 'currency_exchange_rates.php?action=delete&which=' + iCId;
		}
	}
</script>

<table cellspacing="10" cellpadding="0" border="0">
	<tr>
		<td colspan="3">
			<form action="?action=new" method="post">
				<input type="text" value="" name="name" size="10">
				<input type="text" value="" name="value" size="10">
				<input type="Submit" value="New Currency (against DKK)">
			</form>
		</td>
	</tr>
	<tr>
		<td>
			Currency
		</td>
		<td>
			Value
		</td>
		<td>
			Action
		</td>
	</tr>
	<?php echo $sContent; ?>
	<tr>
		<td colspan="3">
			<?php
				if ( $_GET['show'] == 1 ){
					echo "1EURO = ". $fEuro ." DKK";
				} else {
					echo "1DKK = ". number_format( ( 1.0/(float)$fEuro ), 4 )." EURO";
				}
			?>
		</td>
	</tr>
</table>
