<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Access Limit?
require('access_limits.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

?>
<table>
<?php
$result = mysql_query("SELECT *, (SELECT count( DISTINCT master_id ) FROM crm.cst WHERE master_id is not null and cst.person_id = salespeople.person_id AND segment_id != 1481 ) as customers FROM crm.salespeople");
for ( $i = 0; $i < mysql_num_rows( $result ); $i++ ) {
	$row = mysql_fetch_assoc( $result );
	// Count customers
	$tmp = mysql_query();
	echo "<tr><td><a href='card_activity.php?person_id=".(int)$row['person_id']."&team=&partner_id=&months='>" . htmlspecialchars($row['name']) . "</a></td><td>" . (int)$row['customers'] . "</tr>";
}
?>
</table>

