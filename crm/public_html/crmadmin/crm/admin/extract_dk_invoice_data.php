<?php
// Initialise (function libraries and openening DB connection)
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

// Require field template
require(INCLUDE_PATH_LIB."customer_temp_2.php");

// Set header to display plain text
//header("Content-type: text/plain");

//echo "company_name;contact;address_1;address_2;town;zipcode\n";

// Select data
$res = mysql_query("select * from saleslog, cst where saleslog.lang_id = 2 && ( (product_price - discount) > 0 ) && (status is null || status < 2) && cst.cst_id = saleslog.cst_id && 

date_add(sold_date, INTERVAL payment_time DAY) < now() &&

sold_date < '2006-07-01'

order by name");

echo "<table>";

// Loop over result
while ( $row = mysql_fetch_array($res) )
{
        if ( $found[$row['cst_id']] )
        {
                continue;
        }
        $found[$row['cst_id']] = 1;

        echo '<tr><td valign="top">' . $row['name'] . '</td><td valign="top">' . ( $row[$customer_data['Address 1']] ? $row[$customer_data['Address 1']] . "<br>" : '' ) . ( $row[$customer_data['City']] ? $row[$customer_data['City']] : '' ) . ' ' . ( $row[$customer_data['Zipcode']] ? $row[$customer_data['Zipcode']] : '' ) . '</td><td valign="top">' . $row['contact'] . '</td><td valign="top">' . $row['cst_id'] . '' . "</td></tr>";

}

echo "</table>";

// Function for outputting dates in the right format depending on country
function OutputDateFormatted($date)
{
	// Danish date, year and days should be reversed
        $split = explode('-', $date);
        $date = $split[2] . '-' . $split[1] . '-' . $split[0];

        // Return formatted date
        return $date;
}

?>
