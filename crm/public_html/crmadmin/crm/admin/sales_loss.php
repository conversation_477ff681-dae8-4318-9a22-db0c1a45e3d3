<?php
// Load the Admin area configuration and specific functions
require("configuration.php");
require(INCLUDE_PATH_LIB.'initialise.php');

$from = $_GET['from'];
$to = $_GET['to'];

echo HTMLHeader('Secunia CRM - Administration',  HTTP_PATH.'admin/index.php');

function row2tr( $fieldMap, $row, $header = false ) {
	$output = "";
	if ( $header == true ) {
		$output = "<tr>";
		foreach ( $fieldMap as $key => $value ) {
			$style = "";
			if ( array_key_exists( 'header', $fieldMap[$key] ) ) {
				if ( array_key_exists( 'lookNfeel', $fieldMap[$key]['header'] ) ) {
					if ( array_key_exists( 'style', $fieldMap[$key]['header']['lookNfeel'] ) ) {
						$style = "style=\"".$fieldMap[$key]['header']['lookNfeel']['style']."\"";
					}
				}
			}
			$value = "?from=".(int)$_GET['from']."&to=".(int)$_GET['to']."&person_id=".(int)$_GET['person_id']."&team=".$_GET['team']."&sort=".$key."&dir=".( $_GET['dir'] == "ASC" ? "DESC" : "ASC" );
			$value = "<a href='".$value."'><b>".htmlspecialchars( $fieldMap[$key]['text'] )."</b></a>";
			if ( array_key_exists( 'sort', $fieldMap[$key] ) ) {
				if (  $fieldMap[$key]['sort'] == false ) {
					$value = "<b>" . htmlspecialchars( $fieldMap[$key]['text'] ) . "</b>";
				}
			}
			$output .= "<td ".$style.">".$value."</td>";
		}
		$output .= "<tr>";
	}
	$output .= "<tr>";
	foreach ( $fieldMap as $key => $value ) {
		$style = "";
		if ( array_key_exists( 'regular', $fieldMap[$key] ) ) {
			if ( array_key_exists( 'lookNfeel', $fieldMap[$key]['regular'] ) ) {
				if ( array_key_exists( 'style', $fieldMap[$key]['regular']['lookNfeel'] ) ) {
					$style = "style=\"".$fieldMap[$key]['regular']['lookNfeel']['style']."\"";
				}
			}
		}
		if ( array_key_exists( 'template', $fieldMap[$key] ) ) {
 			$row[$key] = str_replace( "{VALUE}", htmlspecialchars( $row[$key] ), $fieldMap[$key]['template'] );
		} else {
			$row[$key] = htmlspecialchars( $row[$key] );
		}
		$output .= "<td ".$style.">". $row[$key] ."</td>";
	}
	$output .= "</tr>";

	return $output;
}

?>

<table width="100%" cellpadding="0" cellspacing="0">
	<tbody>
	<tr>
		<td colspan="2" class="MenuHeadline" width="100%">
		Sales Loss Report
		</td>
	</tr>
	<tr>
		<td colspan="2" width="100%">
			<br>
		</td>
	</tr>
	<form method="GET" action="sales_loss.php">
	<tr>
		<td>
			<b>Select cancelation period of time</b><br>
			<input value="<?= htmlspecialchars( $_GET['from'] ) ?>" name="from" style="width: 150px;" type="text"> - <input value="<?= htmlspecialchars( $_GET['to'] ) ?>" name="to" style="width: 150px;" type="text"> <input style="width: 100px;" type="submit" value="Generate">
		</td>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td width="50%">
						<b>Select sales person</b><br>
						<?php
						// Select all salespeople
						echo returnSelectBox(array($person_id), 'person_id', false, 'select * from salespeople order by name', 'person_id', 'name');
						?>
					</td>
					<td width="50%">
						<b>Select sales team</b><br>
						<?php
						echo returnSelectBox(array($team), 'team', false, 'select * from salesteams where team_id > 0 order by name', 'name', 'name');
						?>
					</td>
					<td width="50%">
						<b>Cancelation reason</b><br>
						<input name="cancellation" value="<?= htmlspecialchars( $_GET['cancellation'] ) ?>" style="display:inline; width: 200px;">
					</td>
				</tr>
			</table>
		</td>
	</tr>
	</form>

	<!-- Begin Table Header -->
	<tr>
		<td colspan="2" width="100%">
			<table wdith="100%">

	<?php
	if ( $from != "" && $to != "" ) {
		// Begin dump sequence.
		// Canceled sales are:
		// 3 - Cancelled
		// 4 - Rejected (CRC)
		$sort = "status_date";
		$dir = "DESC";
		switch ( $_GET['sort'] ) {
			case "status_date":
			case "company_name":
			case "person_name":
			case "sold_date":
			case "status_date":
			case "sale_status":
			case "discount":
			case "product_name":
			case "product_price":
			case "currency":
			case "currency_exchange_rate":
			case "currency_exchange_rate_euro":
			case "sale_type":
			case "department":
			case "cancel_reason":
				$sort = $_GET['sort'];
				$dir = $_GET['dir'] == "ASC" ? "ASC" : "DESC";
				break;
		}
		$results = DBQueryGetRows("
			SELECT
				*
				,(SELECT name FROM crm.cst WHERE cst_id = CUSTOMER.master_id LIMIT 1) AS company_name
				,(SELECT CASE WHEN STATUS = 5 THEN 'Cancelled' ELSE 'Rejected (CRC)' END) AS sale_status
				,(SELECT name FROM crm.salespeople WHERE salespeople.person_id = saleslog.person_id ) AS person_name
			FROM
				crm.saleslog
			LEFT JOIN
				crm.cst AS CUSTOMER
			ON
				CUSTOMER.cst_id = saleslog.cst_id
			WHERE
				status IN ( 3, 4 )
				AND ( product_price - discount ) > 0
				AND status_date >= '".$from."' AND status_date <= '".$to."'
				".( $_GET['person_id'] != "" ? " AND saleslog.person_id = '".(int)$_GET['person_id']."'" : "" )."
				".( $_GET['team'] != "" ? " AND saleslog.department = '".$_GET['team']."'" : "" )."
				".( $_GET['cancellation'] != "" ? " AND saleslog.cancel_reason LIKE '%".$_GET['cancellation']."%'" : "" )."
		ORDER BY
			".$sort." ".$dir."
		");

		$style = "white-space:nowrap; padding-left: 5px; padding-right: 5px; padding-bottom: 5px;";
		$rowStyle = "white-space:nowrap; padding-left: 5px; padding-right: 5px;";
		for ( $j = 0; $j < count( $results );$j++ ) {
			echo row2tr( array(
				"cst_id" => array(
					"text" => "Cst ID"
					,"template" => "<a href='../crm2/sales/?page=customer&cst_id={VALUE}' target='_blank'>{VALUE}</a>"
					,"sort" => false
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"master_id" => array(
					"text" => "Company ID"
					,"template" => "<a href='../crm2/sales/?page=customer&cst_id={VALUE}' target='_blank'>{VALUE}</a>"
					,"sort" => false
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"company_name" => array(
					"text" => "Company Name"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"person_name" => array(
					"text" => "Person Name"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"sold_date" => array(
					"text" => "Sale Date"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"status_date" => array(
					"text" => "Status Date"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"sale_status" => array(
					"text" => "Status"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"discount" => array(
					"text" => "Discount"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"product_name" => array(
					"text" => "Product Name"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"product_price" => array(
					"text" => "Product Price"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"currency" => array(
					"text" => "Currency"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"currency_exchange_rate" => array(
					"text" => "Exchange Rate"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"currency_exchange_rate_euro" => array(
					"text" => "Exchange Rate EURO"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"sale_type" => array(
					"text" => "Sale Type"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"department" => array(
					"text" => "Department"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
				,"cancel_reason" => array(
					"text" => "Cancel Reason"
					,"header" => array(
						"lookNfeel" => array(
							"style" => $style
						)
					)
					,"regular" => array(
						"lookNfeel" => array(
							"style" => $rowStyle
						)
					)
				)
			), $results[$j], $j == 0 ? true : false );
		}
	}
	?>

		</td>
	</tr>

	</tbody>
</table>