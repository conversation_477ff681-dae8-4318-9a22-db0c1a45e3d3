<?php
require('configuration.php');
// Initialise (function libraries and openening DB connection)
require(INCLUDE_PATH_LIB.'initialise.php');

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Get Search value
$sSearch = trim($_GET['search']);

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<form method="GET" action="find_customer.php">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="100%">Find customer</td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%">Search for:<br><input type="text" name="search" value="<?=htmlspecialchars($search)?>"></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><input type="submit" name="submit" value="Search"></td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br></td>
	</tr>
	<tr>
		<td colspan="2" width="100%">
		<?php
		// Search
		if ( $sSearch )
		{
			// Output
			echo '<img src="gfx/longline.gif" width="100%" height="1"><br><b>Result:</b><br><br>';

			// New table
			echo '<table width="100%" cellpadding="0" cellspacing="0">';

			// Output
			echo '
			<tr>
				<td width="10%"><b>Icons</b></td>
				<td width="20%"><b>Name</b></td>
				<td width="20%"><b>Main / Direct / Mobile</b></td>
				<td width="20%"><b>Contact, Title</b></td>
				<td width="20%"><b>Owner</b></td>
				<td width="10%"><b>Last Activity</b></td>
			</tr>
			<tr><td colspan="5"><br></td></tr>';

			// Make Search and Loop through search result
			$sQuery = "SELECT * FROM crm.cst WHERE " . ( $persondata[0] != 72 && $persondata[0] != 47 && $persondata[0] != 98 ? "customer_marked_dead NOT IN(3,6,7) && " : '' ) . "(name LIKE '%" . $sSearch . "%' || phone LIKE '%" . $sSearch . "%' || contact LIKE '%" . $sSearch . "%') " . " ORDER BY name";
			$rRes = mysql_query($sQuery);
			while ( $aRow = mysql_fetch_array($rRes) )
			{
				// Select Standard Icons
				$sIcons = '';
				$rIconRes = mysql_query("SELECT icon FROM icon_types, icon_ref WHERE icon_types.icon_id = icon_ref.icon_id && icon_ref.cst_id = '" . $aRow['cst_id'] . "'");
				while ( $aIconRow = mysql_fetch_array($rIconRes) )
				{
					$sIcons .= '<img src="gfx/' . $aIconRow['icon'] . '.gif">  ';
				}

				// Select all AIDA icons
				$rIconRes = mysql_query("SELECT icon_display FROM icons_aida, icons_aida_ref WHERE icons_aida.icon_id = icons_aida_ref.icon_id && icons_aida_ref.cst_id = '" . $aRow['cst_id'] . "' ORDER BY icons_aida.icon_id");
				while ( $aIconRow = mysql_fetch_array($rIconRes) )
				{
					$sIcons .= $aIconRow['icon_display'] . ', ';
				}

				// Select Last Activity
				$rActivityRes = mysql_query("SELECT MAX(added) AS last_activity FROM crm.comments WHERE cst_id = '" . $aRow['cst_id'] . "'");
				$aActivityRow = mysql_fetch_array($rActivityRes);

				// Verify if paying sale
				$rSaleRes = mysql_query("SELECT * FROM crm.saleslog WHERE cst_id = '" . $aRow['cst_id'] . "' && (product_price-discount) > 0 && (status != 3 || status IS NULL) LIMIT 1");

				// Select BG Color
				if ( !mysql_num_rows($rSaleRes) )
				{
					$sBGColor = ( $sBGColor == '#FFFFFF' ? '#EBEBEB' : '#FFFFFF' );
				}
				else
				{
					$sBGColor = 'lightblue';
				}

				// Result
				echo '
				<tr bgcolor="' . $sBGColor . '">
					<td>' . substr($sIcons, 0, -2) . '</td>
					<td><a href="customer.php?cst_id=' . $aRow['cst_id'] . '">' . $aRow['name'] . '</a></td>
					<td>' . $aRow['phone'] . '</td>
					<td>' . $aRow['contact'] . '</td>
					<td>' . ReturnPersonNameFromID($aRow['person_id']) . '</td>
					<td>' . $aActivityRow['last_activity'] . '</td>
				</tr>';
			}

			// End table
			echo '</table>';
		}
		?>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="100%"><br><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
