<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Form submitted?
if ( $_POST['subject'] && $_POST['content'] )
{
	// Send email
	mail($_POST['email'], stripslashes($_POST['subject']), stripslashes($_POST['content']), "From: " . $_POST['sp_name'] . " <" . $_POST['sp_email'] . ">\r\nReply-To: " . $_POST['sp_email'] ."\r\nContent-Type: text/plain; charset=\"iso-8859-1\"\r\nContent-Transfer-Encoding: 8bit\r\n");

	// Log that template has been sent
	mysql_query("insert into mail_template_send (template_id, email, cst_id) values('" . $template_id . "', '" . $email . "', '" . $cst_id . "')");

	// Add to comments
	mysql_query("insert into comments (comment, added, cst_id, person_id, type) values('Sent mail template: " . $_POST['subject'] . "', now(), '" . intval($cst_id) . "', '" . $persondata[0] .  "', 2)");
}
else
{
	list($person_name, $person_email, $title, $content) = GenerateTemplateContent($_GET['id'], $_GET['cst_id']);
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<?php
if ( $_POST['subject'] && $_POST['content'] )
{
?>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td width="100%" class="MenuHeadline">
			Mail Template Sent To Customer: <?=htmlspecialchars($_POST['email']);?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><a href="customer.php?cst_id=<?=intval($_POST['cst_id'])?>">Return to Customer</a></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
</table>
<?php
}
else
{
?>
<form method="POST" action="send_mail_template.php">
<input type="hidden" name="cst_id" value="<?=intval($_GET['cst_id'])?>">
<input type="hidden" name="email" value="<?=htmlspecialchars($email);?>">
<input type="hidden" name="sp_name" value="<?=htmlspecialchars($person_name);?>">
<input type="hidden" name="sp_email" value="<?=htmlspecialchars($person_email);?>">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td width="100%" class="MenuHeadline">
			Send Mail Template To Customer: <?=htmlspecialchars($email);?>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td>
			<b>Subject:</b><br>
			<input type="text" name="subject" value="<?=htmlspecialchars($title)?>">
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td>
			<b>Content:</b><br>
			<textarea name="content" style="height: 300px;"><?=htmlspecialchars($content)?></textarea>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td>
			<input type="submit" class="submit" value="Send Email">
		</td>
	</tr>
</table>
</form>
<?php
}
?>

<?php
// Output footer
echo HTMLFooter();

// Function for generating template content
function GenerateTemplateContent($template_id, $cst_id)
{
	// Globalize email
	global $email, $persondata;

	// Select tamplate data
	$res = mysql_query("select * from mail_templates where id = '" . $template_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Read template
	$content = $row['body'];
	$title = $row['title'];

	// Select customer data
	$c_res = mysql_query("select * from cst where cst_id = '" . $cst_id . "'");
	$c_row = mysql_fetch_array($c_res);

	// Select latest product data
	$p_res = mysql_query("select max(sale_id) as sale_id from saleslog where cst_id = '" . $cst_id . "'");
	$p_row = mysql_fetch_array($p_res);
	$p_res = mysql_query("select * from saleslog where sale_id = '" . $p_row['sale_id'] . "' limit 1");
	$p_row = mysql_fetch_array($p_res);

	// Select one_time_password, if any
	$o_res = mysql_query("select * from one_time_passwords where cst_id = '" . $cst_id . "' limit 1");
	$o_row = mysql_fetch_array($o_res);

	// Select salesperson data
	$s_res = mysql_query("select * from salespeople where person_id = '" . $persondata[0] . "' limit 1");
	$s_row = mysql_fetch_array($s_res);

	// What email
	if ( !$email )
	{
		$email = $c_row['email'];
	}

	// Replace all dynamic input fields in template
	{
		// Company name
		$content = str_replace("##company_name##", $c_row['name'], $content);

		// Company contect
		$content = str_replace("##company_contact##", $c_row['contact'], $content);

		// Company contect
		$content = str_replace("##company_contact_email##", $c_row['email'], $content);

		// Product name
		$content = str_replace("##product_name##", $p_row['product_name'], $content);

		// Product price
		$content = str_replace("##product_price##", $p_row['product_price'], $content);

		// Product period
		$content = str_replace("##product_period##", $p_row['product_period'], $content);

		// One time password
		$content = str_replace("##other_one_time_password##", $o_row['password'], $content);

		// Name of salesperson
		$content = str_replace("##salesperson_name##", $s_row['name'], $content);

		// Title of salesperson
		$content = str_replace("##salesperson_title##", $s_row['title'], $content);

		// Email of salesperson
		$content = str_replace("##salesperson_email##", $s_row['email'], $content);
	}

	// Return content
	return array($s_row['name'], $s_row['email'], $title, wordwrap($content));
}
?>
