<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Load history 25
$fp = fopen("customer_select_hist", "r");
while ( !feof($fp) )
{
	$content .= fgets($fp, 1024);
}
fclose($fp);

// Split content by newlines
$ids = explode("\n", $content);

// Loop until we get a valid result
while ( !$cst_id )
{
        // Get cst id
        $cst_id = GetCstid($ids, $segment_id);

	// Check
        if ( $cst_id == 'no_customers' )
        {
        	// Reset cst_id
                $cst_id = '';

                // Break loop
                break;
        }
}

// Redirect to customer page
if ( $cst_id )
{
	// Remove first element of the ids array
        array_shift($ids);

        // Add this cst_id to ids array
        $ids[] = $cst_id;

        // Implode ids array
        $content = implode("\n", $ids);

        // Write to file
        $fp = fopen("customer_select_hist", "w");
        fputs($fp, $content);
        fclose($fp);

	// Redir
        header("Location: customer.php?cst_id=" . $cst_id . "&from=random&segment_id=" . $segment_id);
}
else
{
	echo "No customers left in segment.<br>";
	
	// Delete from segment
	mysql_query("delete from sp_segment_ref where person_id = '" . $persondata[0] . "' && segment_id = '" . $segment_id . "'");
}

// CST_ID
function GetCstid($ids, $segment_id)
{

        // Select all available customers in this segment
        $res = mysql_query("select cst_id from cst where segment_id = '" . $segment_id . "' && person_id is null");

        // Check number of results
        if ( !mysql_num_rows($res) )
        {
        	return 'no_customers';
        }

        // Loop over result
        while ( $row = mysql_fetch_array($res) )
        {
                $available[] = $row['cst_id'];
        }

        // Select index key from available array
        $cst_id = $available[rand(0, (count($available) - 1))];

        // Check cst id
        if ( in_array($cst_id, $ids) )
        {
        	return;
        }

        // Return cst_id
        return $cst_id;

}
?>
