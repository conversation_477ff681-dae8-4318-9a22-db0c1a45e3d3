AuthUserFile /home/<USER>/.htpasswd
AuthGroupFile /dev/null
AuthName "Secunia"
AuthType Basic
<Limit GET POST PUT DELETE CONNECT OPTIONS PATCH PROPFIND PROPPATCH MKCOL COPY MOVE LOCK UNLOCK>
Deny from all
Allow from 192.168.50.71
Allow from 192.168.50.72
Allow from 192.168.50.74
Allow from 192.168.50.75

#ST WS
Allow from 192.168.50.34
#HWKS-ST
Allow from 192.168.52.56

#Various HWKS
#RB
Allow from 192.168.52.6
#AO
Allow from 192.168.52.14
#MWINTHER
Allow from 192.168.52.18

Require valid-user
Order deny,allow
</Limit>
