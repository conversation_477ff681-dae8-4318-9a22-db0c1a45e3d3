// Object for sorting a table
function oSortTable(sTBody)
{
	// Sort Type
	var sType = '';

	// Property: TBody
	this.sTBody = sTBody;

	// Property: Container
	this.aContainer = new Array();

	// Property: Boolean
	this.bSortOrder = true;

	// Method: Get Table Body
	this.mTableBody = function () { return document.getElementById(this.sTBody); }

	// Method: Comparison method
	this.mComparisonMethod = function (xFirst, xSecond)
	{
		// String
		if ( sType == 'string' )
		{
			return ( xSecond.toLowerCase() < xFirst.toLowerCase() ? -1 : 1 );
		}
		else // Handles dates as well as normal integers
		{
			xSecond = xSecond.split('___');
			xFirst = xFirst.split('___');
			xSecond = parseInt(xSecond[0].replace(/[^0-9]*/g, ''));
			xFirst = parseInt(xFirst[0].replace(/[^0-9]*/g, ''));

			return ( xSecond < xFirst ? -1 : 1 );
		}
	}

	// Method: Display Sort Image
	this.mDisplaySortImage = function (oClicked)
	{
		// First, remove existing sort images
		var oCurrentSortImage = document.getElementById('sort_img_' + this.sTBody);
		if ( oCurrentSortImage )
		{
			oCurrentSortImage.parentNode.removeChild(oCurrentSortImage);
		}

		// Create new sort image
		var oSortImage = document.createElement('img');
		oSortImage.setAttribute('height', 7);
		oSortImage.setAttribute('width', 7);
		oSortImage.setAttribute('src', '/crmadmin/crm/sales/gfx/sort_' + ( oClicked.getAttribute('sortOrder') == 'DESC' ? 'down' : 'up' ) + '.gif');
		oSortImage.setAttribute('id', 'sort_img_' + this.sTBody);

		// Place sort image
		oClicked.appendChild(oSortImage);
	}

	// Method: Sort table
	this.mSort = function (oClicked, iRow, sSortType)
	{
		// Register Sort Type
		sType = sSortType;

		// Get Table Body
		var oTableBody = this.mTableBody();
		
		// Get Table TR Elements
		var oTableBodyTRs = oTableBody.getElementsByTagName('TR');

		// Loop counter
		var iCount = 0;

		// Enumerate table rows and store references in Container
		for ( i=0 ; i<oTableBodyTRs.length ; i++ )
		{
			// Get TR TD Elements
			var oTableBodyTRTDs = oTableBodyTRs[i].getElementsByTagName('TD');

			// Handle the different column data types
			if ( oTableBodyTRTDs.length >= iRow && oTableBodyTRTDs[iRow].childNodes.length > 0 )
			{
				switch ( oTableBodyTRTDs[iRow].childNodes[0].nodeName )
				{
					case 'IMG':
						if ( oTableBodyTRTDs[iRow].childNodes[0].getAttribute('sortatt') )
						{
							this.aContainer[i] = oTableBodyTRTDs[iRow].childNodes[0].getAttribute('sortatt') + '___' + i;
						}
						else
						{
							this.aContainer[i] = oTableBodyTRTDs[iRow].childNodes[0].getAttribute('src') + '___' + i;
						}
						break;

					case '#text':
					case 'A':
					case 'DIV':
						// Get everything from TAG, including HTML
						var sTmp = oTableBodyTRTDs[iRow].innerHTML;

						// Strip out all HTML
						sTmp = sTmp.stripTags();

						// Trim value
						sTmp = sTmp.trim();

						// Store clean value in container
						this.aContainer[i] = sTmp + '___' + i;
						break;

					default:
						alert('Unhandled TAG: ' + oTableBodyTRTDs[iRow].childNodes[0].firstChild.nodeName + ' / ' + oTableBodyTRTDs[iRow].childNodes[0].firstChild.nodeValue);
						break;
				}
			}
			else
			{
				// If no value in cell then "fill" with the following:
				this.aContainer[i] = '0000000000' + '___' + i;
			}
		}

		// Sort
		this.aContainer.sort(this.mComparisonMethod);

		// DESC or ASC sorting
		if ( oClicked.getAttribute('sortOrder') == 'ASC' || oClicked.getAttribute('sortOrder') == null )
		{
			// Store sort order
			oClicked.setAttribute('sortOrder', 'DESC');
		}
		else
		{
			// Reverse result
			this.aContainer.reverse();

			// Store sort order
			oClicked.setAttribute('sortOrder', 'ASC');
		}

		// Display sort image
		this.mDisplaySortImage(oClicked);

		// Generate new tbody for holding the sorted result
		var oNewTableBody = document.createElement('tbody');
		oNewTableBody.setAttribute('id', this.sTBody);

		// Output
		for ( i=(this.aContainer.length - 1) ; i>=0 ; i-- )
		{
			// Split string by '___' to get our array key
			var aData = this.aContainer[i].split('___');

			// Copy element node
			var oCopyNode = oTableBodyTRs[aData[1]].cloneNode(true)

			// Added copy of node to new TBODY
			oNewTableBody.appendChild(oCopyNode);
		}

		// Replace Current TBODY with New
		oTableBody.parentNode.replaceChild(oNewTableBody, oTableBody);
	}
}

