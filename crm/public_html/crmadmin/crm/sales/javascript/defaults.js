// Add 'trim' functionality to String object
// Usage: String = String.trim();
String.prototype.trim = function ()
{
	return this.replace(/^\s+|\s+$/g, '');
}

// Add 'stripTags' functionality to String object
// Usage: String = String.stripTags();
String.prototype.stripTags = function ()
{
	return this.replace(/<[^>]*>/g, '');
}

function fTransferBudget(iMonth){
	var iBudget = document.getElementById('month_' + iMonth).value;
	var iPrimo = document.getElementById('pmonth_' + iMonth).value;
	if (iPrimo == 0){
		document.getElementById('pmonth_' + iMonth).value = iBudget;
	}
}
