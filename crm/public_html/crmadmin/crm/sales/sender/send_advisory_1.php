<?php
// Require functions
require("../../includes/functions.php");

// Require email functions
require("../../../vuln_track/sender/email_functions.php");

error_reporting(0);

// Define lang id
define(LANG_ID, 1);

// Text
$footer = "Secunia recommends that you verify all advisories you receive, by clicking the link.
Secunia NEVER sends attached files with advisories.
Secunia does not advise people to install third party patches, only use those supplied by the vendor.

Contact details:
Web	: http://www.secunia.com/
E-mail	: <EMAIL>
Tel	: +44 (0) 20 7016 2693
Fax	: +44 (0) 20 7637 0419";

// Open database connection
OpenDatabase('crm');

// Change database - Use vuln_track
ChooseDatabase('vuln_track');

// Construct the advisory that is to be sent
if ( $vuln_id )
{
	list($title, $content) = GenerateAdvisoryEmail($vuln_id, 'com', $footer);
}

// Check for input vuln_id and email
if ( $vuln_id && $email && !$status )
{
        // Send this baby!
        mail($email, $title, $content, "From: Secunia <<EMAIL>>\r\nContent-Type: text/plain; charset=\"US-ASCII\"\r\nContent-Transfer-Encoding: 7bit\r\n");

        // Redir - no dual send!!
	header("Location: send_advisory_1.php?vuln_id=" . $vuln_id . "&email=" . $email . "&status=ok");
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Sent advisory
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                	The advisory "<?=$title?>" was <?=( $status ? '' : '<b>NOT</b>' )?> sent to <?=$email?>.
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>
<?php
// Output footer
echo HTMLFooter();
?>
