<?php
// Require functions
require("../../includes/functions.php");

// Require email functions
require("../../../vuln_track/sender/email_functions.php");

error_reporting(0);

// Define lang id
define(LANG_ID, 2);

// Text
$footer = "Secunia anbefaler, at du altid verificerer alle advisories du modtager ved at klikke på linket.
Secunia sender ALDRIG vedhæftede filer med advisories.
Secunia anbefaler ikke at installere patches lavet af en tredie person, installér kun dem lavet af producenten.

Kontakt detaljer:
Web	: http://www.secunia.dk/
E-mail	: <EMAIL>
Tlf.	: +45 7020 5144
Fax	: +45 7020 5145";

// Open database connection
OpenDatabase('crm');

// Change database - Use vuln_track
ChooseDatabase('vuln_track');

// Construct the advisory that is to be sent
if ( $vuln_id )
{
	list($title, $content) = GenerateAdvisoryEmail($vuln_id, 'dk', $footer);
}

// Check
if ( !preg_match("BESKRIVELSE:", $content) )
{
	echo "Not translated...";
	exit();
}

// Check for input vuln_id and email
if ( $vuln_id && $email && !$status )
{
        // Send this baby!
        mail($email, $title, $content, "From: Secunia <<EMAIL>>\r\nContent-Type: text/plain; charset=\"US-ASCII\"\r\nContent-Transfer-Encoding: 7bit\r\n");

        // Redir - no dual send!!
	header("Location: send_advisory_2.php?vuln_id=" . $vuln_id . "&email=" . $email . "&status=ok");
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Send advisory
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                	The advisory "<?=$title?>" was <?=( $status ? '' : '<b>NOT</b>' )?> sent to <?=$email?>.
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>
<?php
// Output footer
echo HTMLFooter();
?>
