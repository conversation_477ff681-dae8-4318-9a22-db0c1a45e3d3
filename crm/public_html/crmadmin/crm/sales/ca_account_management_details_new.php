<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Set vars	
$virus = 0;
$advisory = 0;
$update = '';
$password = '';
$error = '';
$succes = 0;
$comment = '';
$accounts_update = FALSE;
$iModulesVisible = 0;
$iModulesEnabled = 0;

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Set account_id
$account_id = ( $_GET['account_id'] ? $_GET['account_id'] : $_POST['account_id'] );

// Modular Product Structure Setup
{
	// Modules
	$aModules = array(
		"Enterprise Vulnerability Tracking (VTS-E)" => MOD_VTSE,
		"Enterprise Security Manager (ESM)" => MOD_ESM,
		"Security Manager (SM)" => MOD_SM,
		"Vulnerability Tracking (VTS)" => MOD_VTS,
		"Surveillance Scanner (SS)" => MOD_VSS,
		"Network Software Inspector (NSI)" => MOD_NSI,
		"Binary Analysis (BA)" => MOD_BA,
		"Vulnerability Database</b> (default module)" => MOD_VDB,
		"Virus &amp; Work Alerting</b> (default module expect for: BA, VTS-E)" => MOD_VWA,
		"Technical Support</b> (default module)" => MOD_SUPPORT,
		"Account Information</b> (default module)" => MOD_ACCOUNT
	);
}

if ( $_POST['modules_enabled'] || $_POST['modules_visible'] )
{
	// Loop through modules Enabled
	while ( list($sModuleName, $iModuleValue) = each($aModules) )
	{
		// Enabled Modules
		if ( $_POST['modules_enabled'][$iModuleValue] == 1 )
		{
			$iModulesEnabled |= $iModuleValue;
		}

		// Visible Modules
		if ( $_POST['modules_visible'][$iModuleValue] == 1 )
		{
			$iModulesVisible |= $iModuleValue;
		}
	}
}

// Select data
$res = mysql_query("select * from ca.accounts, cst where account_id = '" . $account_id . "' && cst.cst_id = accounts.cst_id limit 1");
$row = mysql_fetch_array($res);

// Select add-on details
{
	// Virus
	$v_res = mysql_query("select * from ca.extra_virus where cst_id = '" . $row['cst_id'] . "'");
	while ( $v_row = mysql_fetch_array($v_res) )
	{
		$virus += $v_row['number'];
	}

	// Advisory contacts
	$a_res = mysql_query("select * from ca.extra_contact where account_id = '" . $row['account_id'] . "'");
	while ( $a_row = mysql_fetch_array($a_res) )
	{
		$advisory += $a_row['number'];
	}
}

// Verify owner
if ( (($row['person_id'] != $persondata[0] || !$row['person_id']) && $persondata[0] != 60) || $row['account_esm']  )
{
	header("Location: /crmadmin/crm/sales/");
	exit();
}

// Save data
if ( $_GET['s'] == 1 )
{
	// Update 'accounts' table
	if ( $_POST['new_username'] || $_POST['new_expire'] || $_POST['new_password'] || $_POST['new_product'] || $iModulesEnabled || $iModulesVisible )
	{
		if ( $_POST['new_username'] )
		{
			$update = "account_username = '" . trim($_POST['new_username']) . "'";
			$comment = 'Changed username (' . htmlspecialchars($_POST['new_username']) . ')<br>';
		}

		// Expires date - must be run on 'cst_id' not account_id, in order to affect sub-esm accounts too.
		if ( preg_match('^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$', $_POST['new_expire']) )
		{
			mysql_query("UPDATE ca.accounts SET account_expires = '" . $_POST['new_expire'] . "' WHERE cst_id = '" . $row['cst_id'] . "'");
			$comment .= 'Expiry date changed (' . htmlspecialchars($_POST['new_expire']) . ')<br>';
		}

		// New Password
		if ( $_POST['new_password'] )
		{
			// Generate random password
			$password = GenerateRandomPassword(8);

			$update .= ( $update ? ', ' : '' ) . "account_password = password('" . $password . "'), account_gen_pwd = 1";
			$comment .= 'Password reset<br>';
		}

		// New Module Product
		if ( $iModulesEnabled || $iModulesVisible )
		{
			$update .= ( $update ? ', ' : '' ) . "modules = '" . $iModulesEnabled . "', show_modules = '" . $iModulesVisible . "'";
			$comment .= 'Product Modules changed<br>';

			// If new product module == MOD_ESM, then insert 5 esm accounts if not already there
			if ( $iModulesEnabled & MOD_ESM )
			{
				// Check if there already an entry in 'ca.esm'
				if ( !mysql_num_rows(mysql_query("SELECT * FROM ca.esm WHERE master_account_id = '" . intval($row['account_id']) . "' LIMIT 1")) )
				{
					mysql_query("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES(5, '" . intval($row['account_id']) . "', '" . intval($row['cst_id']) . "')");
				}
			}
		}

		// Execute Changes
		if ( $update )
		{
			mysql_query("UPDATE ca.accounts SET " . $update . " WHERE account_id = '" . $row['account_id'] . "' LIMIT 1");
			$accounts_update = TRUE;
			$succes = mysql_affected_rows();
			echo mysql_error();
		}
	}

	// Update number of extra virus
	if ( is_numeric($_POST['new_virus']) )
	{
		// Check if customer is using more advisory contacts, than what is being set
		$t_res = mysql_query("select id from ca.virus_contact where account_id = '" . $row['account_id'] . "'");
		$count = (mysql_num_rows($t_res) / 2) - 1;

		if ( $count <= $_POST['new_virus'] )
		{
			mysql_query("delete from ca.extra_virus where cst_id = '" . $row['cst_id'] . "'");
			mysql_query("insert into ca.extra_virus (cst_id, number) values('" . $row['cst_id'] . "', '" . intval($_POST['new_virus']) . "')");

			$comment .= 'Extra Virus Contact Changed (' . intval($_POST['new_virus']) . ')<br>';
		}
		else
		{
			$virus_failed = TRUE;
		}
	}

	// Update number of extra advisory
	if ( is_numeric($_POST['new_advisory']) )
	{
		// Check if customer is using more virus contacts, than what is being set
		$t_res = mysql_query("select contact_method_id from ca.contact_method where account_id = '" . $row['account_id'] . "'");
		$count = (mysql_num_rows($t_res) / 2) - 1;

		if ( $count <= $_POST['new_advisory'] )
		{
			mysql_query("delete from ca.extra_contact where account_id = '" . $row['account_id'] . "'");
			mysql_query("insert into ca.extra_contact (account_id, number, type) values('" . $row['account_id'] . "', '" . intval($_POST['new_advisory']) . "', 'ADV')");
			
			$comment .= 'Extra Advisory Contact Changed (' . intval($_POST['new_advisory']) . ')<br>';
		}
		else
		{
			$advisory_failed = TRUE;
		}
	}

	// Update ESM
	if ( is_numeric($_POST['new_esm']) )
	{
		// Check if customer is using more accounts, than what is being set
		$t_res = mysql_query("select account_id from ca.accounts where cst_id = '" . $row['cst_id'] . "'");
		$count = mysql_num_rows($t_res) - 1;

		if ( $count <= $_POST['new_esm'] )
		{
			// Update or Insert
			if ( mysql_num_rows(mysql_query("SELECT * FROM ca.esm WHERE master_account_id = '" . $row['account_id'] . "' LIMIT 1")) )
			{
				mysql_query("UPDATE ca.esm SET no_users = '" . intval($_POST['new_esm']) . "' WHERE master_account_id = '" . $row['account_id'] . "' LIMIT 1");
			}
			else
			{
				mysql_query("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES('" . intval($_POST['new_esm']) . "', '" . $row['account_id'] . "', '" . $row['cst_id'] . "')");
			}

			$comment .= 'ESM Account Number Changed (' . intval($_POST['new_esm']) . ')<br>';
		}
		else
		{
			$esm_failed = TRUE;
		}
	}

	// Update SSS
	if ( $_POST['new_frequency'] && $_POST['new_sss_number'] )
	{
		// Delete all entries
		mysql_query("delete from ca.vss_scan_limits where account_id = '" . $row['account_id'] . "'");

		// New entries
		mysql_query("insert into ca.vss_scan_limits (account_id, number, type) values('" . $row['account_id'] . "', '" . $_POST['new_sss_number'] . "', '" . $_POST['new_frequency'] . "')");

		// Update all currenct entries
		mysql_query("update ca.vss_scan_profiles set frequency = '" . $_POST['new_frequency'] . "' where account_id = '" . $row['account_id'] . "'");

		$comment .= 'Frequency Changed / Scan Slots (' . FrequencyName($_POST['new_frequency']). ' / ' . intval($_POST['new_sss_number']) . ')<br>';
	}
	
	// Update NSI Base Settings
	if ( $_POST['s_trial'] || $_POST['s_unique_hosts'] )
	{
		// Verify that we have an entry in this table - insert vs. update
		if ( mysql_num_rows(mysql_query("SELECT * FROM ca.nsi_base_settings WHERE account_id = '" . $row['account_id'] . "' LIMIT 1")) )
		{
			// Update Base Settings
			mysql_query("UPDATE ca.nsi_base_settings SET s_trial = '" . $_POST['s_trial'] . "', s_unique_hosts = '" . $_POST['s_unique_hosts'] . "' where account_id = '" . $row['account_id'] . "'");
		}
		else
		{
			mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $row['account_id'] . "', '" . $row['cst_id'] . "', '0', '" . $_POST['s_unique_hosts'] . "', '" . $_POST['s_trial'] . "')");
		}

		$comment .= 'Trial / Host Licenses Changed (' . ( $_POST['s_trial'] ? 'Trial Account' : 'Customer Account' ). ' / ' . intval($_POST['s_unique_hosts']) . ')<br>';
	}

	// Save comment - if available
	if ( $comment )
	{
		mysql_query("insert into comments (comment, added, cst_id, person_id, type) values('" . mysql_escape_string($comment) . "', now(), '" . $row['cst_id'] .  "', '" . $persondata[0] . "', '3')");
	}
}


// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>

<form method="POST" action="ca_account_management_details_new.php?s=1&amp;account_id=<?=intval($_GET['account_id'])?>&amp;cst_id=<?=intval($_GET['cst_id'])?>">
<table width="100%" cellpadding="1" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="3">
			Advanced Customer Area Administration - Account Details
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?php
	if ( $_GET['s'] == 1 )
	{
		?>
		<tr>
			<td><?=( !$succes && $accounts_update ? 'Failed - selected username probably not available. <a href="ca_account_management_details.php?account_id=' . intval($_GET['account_id']) . '&amp;cst_id=' . intval($_GET['cst_id']) . '">Click here</a> to go back.' : 'Data Updated. <a href="ca_account_management_details.php?account_id=' . intval($_GET['account_id']) . '&amp;cst_id=' . intval($_GET['cst_id']) . '">Click here</a> to see changes.' )?></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<?php
		if ( $advisory_failed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>Advisory Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $virus_failed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>Virus Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $esm_failed )
		{
			?>
			<tr>
				<td><br></td>
			</tr>
			<tr>
				<td>ESM Sub-Accounts change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some accounts first.</td>
			</tr>
			<?php
		}
		?>
		<?php
		if ( $password )
		{
			?>
			<tr>
				<td><b>New Password: <?=htmlspecialchars($password)?></b></td>
			</tr>
			<tr>
				<td><br></td>
			</tr>
			<?php
		}
	}
	else
	{
	?>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td><b>Customer</b></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Company Name</td>
		<td><a href="customer.php?cst_id=<?=$row['cst_id']?>"><?=htmlspecialchars($row['name'])?></a></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Next Appointment</td>
		<td><?=htmlspecialchars($row['appointment'])?></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="35%"><b>Customer Area Details</b></td>
		<td width="30%"><b>Current Value</b></td>
		<td width="35%"><b>New Value</b></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Username</td>
		<td><?=htmlspecialchars($row['account_username'])?></td>
		<td><input type="text" name="new_username"></td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Password</td>
		<td>*************</td>
		<td><input type="checkbox" name="new_password" style="width: 15px;"> Generate New Password</td>
	</tr>
	<tr>
		<td style="padding-left: 3px;">Expires</td>
		<td><?=htmlspecialchars($row['account_expires'])?></td>
		<td><input type="text" name="new_expire"></td>
	</tr>
	<?php

	// If not SS, BA, and NSI
	if ( !($row['modules'] & MOD_VSS) && !($row['modules'] & MOD_BA) && !($row['modules'] & MOD_NSI) )
	{
		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Extra Contacts</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Extra Virus Contact</td>
			<td><?=intval($virus)?></td>
			<td><input type="text" name="new_virus"></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Extra Advisory Contact</td>
			<td><?=intval($advisory)?></td>
			<td><input type="text" name="new_advisory"></td>
		</tr>
		<?php
	}

	// SS Options
	if ( $row['modules'] & MOD_VSS )
	{
		// Select SSS options
		$fr_res = mysql_query("select * from ca.vss_scan_limits where account_id = '" . $_GET['account_id'] . "' limit 1");
		$fr_row = mysql_fetch_array($fr_res);

		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Account Scan Frequency / Available Slots Settings</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Scan Frequency</td>
			<td><?=FrequencyName($fr_row['type'])?></td>
			<td>
				<select name="new_frequency">
					<option value="1"<?= ( $fr_row['type'] == 1 ? ' selected' : '' )?>><?=FrequencyName(1)?></option>
					<option value="2"<?= ( $fr_row['type'] == 2 ? ' selected' : '' )?>><?=FrequencyName(2)?></option>
					<option value="3"<?= ( $fr_row['type'] == 3 ? ' selected' : '' )?>><?=FrequencyName(3)?></option>
				</select>
			</td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Scan Slots</td>
			<td><?=$fr_row['number']?></td>
			<td><input type="text" value="<?=$fr_row['number']?>" name="new_sss_number"></td>
		</tr>
		<?php
	}

	// NSI Options
	if ( $row['modules'] & MOD_NSI )
	{
		// Select NSI options
		$rBaseSettings = mysql_query("SELECT * FROM ca.nsi_base_settings WHERE account_id = '" . $_GET['account_id'] . "' LIMIT 1");
		$aBaseSettings = mysql_fetch_array($rBaseSettings);

		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Host Licenses / Trial Account</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;" valign="top">Trial Account</td>
			<td valign="top"><?= ( $aBaseSettings['s_trial'] ? 'Yes' : 'No' ) ?></td>
			<td>
				<input type="radio" name="s_trial" style="width: 15px;" value="1"> Yes<br>
				<input type="radio" name="s_trial" style="width: 15px;" value="0"> No<br>
			</td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">Host Licenses</td>
			<td><?= number_format($aBaseSettings['s_unique_hosts']) ?></td>
			<td><input type="text" name="s_unique_hosts"></td>
		</tr>
		<?php
	}

	// ESM settings
	if ( $row['modules'] & MOD_ESM )
	{
		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>ESM Details</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td style="padding-left: 3px;">ESM Sub-Accounts</td>
			<td><?=intval($esm_total)?></td>
			<td><input type="text" name="new_esm"></td>
		</tr>
		<?php
	}
	?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Account Product Modules <?=$persondata[0]?></b></td>
			<td><b>Enabled Modules</b></td>
			<td><b>Displayed Modules</b></td>
		</tr>
		<?php
		// Loop Through Modules
		foreach( $aModules as $sModuleName => $iModuleValue )
		{
			// Output
			if ( !$aModuleRestrictions[$iModuleValue] || ( $aModuleRestrictions[$iModuleValue] && @in_array($persondata[0], $aModuleRestrictions[$iModuleValue]) ) )
			{
				// Change BG Color
				$sBGColor = ( $sBGColor != '#DFDFDF' ? '#DFDFDF' : '#FFFFFF' );

				echo '
		<tr bgcolor="' . $sBGColor . '">
			<td><b>' . $sModuleName . '</b></td>
			<td>' . ( !$aModuleRestrictionsEnable[$iModuleValue] || ( $aModuleRestrictionsEnable[$iModuleValue] && @in_array($persondata[0], $aModuleRestrictionsEnable[$iModuleValue]) ) ? '<input type="checkbox" name="modules_enabled[' . $iModuleValue . ']" value="1" style="width: 15px;"' . ( $row['modules'] & $iModuleValue ? ' checked' : '' ) . '>' : '' ) . '</td>
			<td><input type="checkbox" name="modules_visible[' . $iModuleValue . ']" value="1" style="width: 15px;"' . ( $row['show_modules'] & $iModuleValue ? ' checked' : '' ) . '></td>
		</tr>';
			}
		}
		?>
	<?php
	}
	?>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td colspan="2"></td>
		<td><input type="submit" value="Submit Changes" class="submit"></td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();

?>
