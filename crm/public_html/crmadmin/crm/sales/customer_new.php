<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise_new.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Settings, Expection Rules for Sales Management
$aAllowAccessToLockedCustomers = array(2,3,5,6,52,59,74);
$aAllowAccessToSecurityVendorCustomers = array(2,3,5,11,52,59,74);
$aAllowCreationOfNewCustomers = array(2,3,48,74);
$aAllowAccessToPayingCustomers = array(2,3,4,5,52,59,11,49,58,64,74);
$aAllowAccessToMarkedCustomers = array(6,7);

// Get lang id
$cst_lang_id = ReturnCustomerLangID($cst_id);
$cst_vuln_lang_id = ReturnCustomerVulnLangID($cst_id);
$crm_lang_id = ReturnCRMLangID($cst_id);

// Create New Customer
if ( strtolower($_GET['cst_id']) == 'new' && !in_array($persondata[0], $aAllowCreationOfNewCustomers) )
{
	header('Location: index.php');
	exit();
}

// Request to add Customer ID to Master ID
if ( $_GET['master_id'] && $_GET['add_id'] )
{
	// Update references
	mysql_query("UPDATE crm.cst SET master_id = '" . intval($_GET['master_id']) . "', case_name = 'Duplicate Customer' WHERE cst_id = '" . intval($_GET['add_id']) . "' LIMIT 1");

	// Send notification email
	mail('<EMAIL>', 'Duplicate Customer Added (' . getenv('REMOTE_USER') . ')', '
Customer added to Master:
https://ca.secunia.com/crmadmin/crm/sales/customer.php?cst_id=' . intval($_GET['add_id']) . '

Master:
https://ca.secunia.com/crmadmin/crm/sales/customer.php?cst_id=' . intval($_GET['master_id']));
}

// Select Customer Base Data
$res = mysql_query("SELECT * FROM crm.cst WHERE cst_id = '" . $_GET['cst_id'] . "' LIMIT 1");
$row = mysql_fetch_array($res);

// Check If Paying Sale Has Been Made
$rSaleRes = mysql_query("SELECT * FROM crm.saleslog WHERE cst_id = '" . $row['cst_id'] . "' && (product_price-discount) > 0 && (status != 3 || status IS NULL) LIMIT 1");

// Verify Access and/or Display Warnings
{
	// Check if Customer is marked as locked, with exception of Sales Managers / Sales Director
	if ( $row['customer_marked_dead'] == 3 && in_array($persondata[0], $aAllowAccessToLockedCustomers) )
	{
		$sWarning = '<font color="RED">Customer Locked!</font> - ';
	}
	elseif ( $row['customer_marked_dead'] == 3 ) // Check if customer is marked as "dead" / "3" - No access
	{
		echo "This Customer ID has been locked. If you feel this is incorrect please contact your sales manager.";
		exit();
	}
	elseif ( $row['customer_marked_dead'] == 6 && $persondata[5] != 6 && !in_array($persondata[0], $aAllowAccessToSecurityVendorCustomers) ) // Check if customer is marked as "SV" / "6"  - No access, is a "Security Vendor" customer.
	{
		echo "This Customer ID has been locked as it is marked as a \"Security Vendor\". If you feel this is incorrect please contact your sales manager.";
		exit();
	}
	elseif ( mysql_num_rows($rSaleRes) == 1 && $row['person_id'] != $persondata[0] && !in_array($persondata[0], $aAllowAccessToPayingCustomers) && $row['person_id'] > 0 ) // Check if a paying sale has been made, if so, owner of the customer must be the sales person or a Sales Manager / Sales Director
	{
		echo "This Customer ID has been locked as it is marked as a paying customer and you are not the \"owner\" of this Customer ID. If you feel this is incorrect please contact your sales manager.";
		exit();
	}
	elseif ( $row['customer_marked_dead'] > 3 && $row['customer_marked_dead'] != 6 && $row['customer_marked_dead'] != 7 )
	{
		$sWarning = '<font color="RED">Read Only!</font> - ';
	}
	else
	{
		$sWarning = '';
	}
}

// CASES: Requested Closed or Reopened
{
	if ( $_GET['close_case'] )
	{
		mysql_query("UPDATE cst SET case_status = 0 WHERE cst_id = '" . $_GET['cst_id'] . "' && person_id = '" . $persondata[0] .  "' LIMIT 1");
	}
	elseif ( $_GET['open_case'] )
	{
		mysql_query("UPDATE cst SET case_status = 1 WHERE cst_id = '" . $_GET['cst_id'] . "' && person_id = '" . $persondata[0] .  "' LIMIT 1");
	}
}

// DUPES: Check To See If We Can Automatically Locate Customer Dupes
{
	// Use the First 10 Chars of the Company Name
	$sCompanyString = substr($row['name'], 0, 10);
	$sDupeCustomers = '';

	// Select All Other Customers in the Database Starting with This
	$rDupeCustomers = mysql_query("SELECT * FROM crm.cst WHERE name LIKE '" . mysql_escape_string($sCompanyString) . "%' && customer_marked_dead NOT IN(3,6,7) && cst_id != '" . $row['cst_id'] . "' ORDER BY name");
	while ( $aDupeCustomers = mysql_fetch_array($rDupeCustomers) )
	{
		// Select Last Activity
		$rActivityRes = mysql_query("SELECT MAX(added) AS last_activity FROM crm.comments WHERE cst_id = '" . $aDupeCustomers['cst_id'] . "'");
		$aActivityRow = mysql_fetch_array($rActivityRes);

		// Select BG Color
		$sBGColor = ( $sBGColor == '#FFFFFF' ? '#EBEBEB' : '#FFFFFF' );

		// Generate HTML
		$sDupeCustomers .= '
		<tr bgcolor="' . $sBGColor . '">
			<td><a href="customer.php?cst_id=' . $aDupeCustomers['cst_id'] . '">' . htmlspecialchars($aDupeCustomers['name']) . '</a></td>
			<td>' . htmlspecialchars(ReturnPersonNameFromID($aDupeCustomers['person_id'])) . '</td>
			<td>' . $aActivityRow['last_activity'] . '</td>
		</tr>';
	}
}

// Overwrite Language Settings: Check if cst_lang_id isset else set to be input or salespersons
{
	if ( !$cst_lang_id )
	{
		if ( $lang_id )
		{
			$cst_lang_id = $lang_id;
		}
		else
		{
			$cst_lang_id = $persondata[1];
		}
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>

<script language="JavaScript">
var myData = new Array(50);

// Function for displaying/hiding a div
function ToggleDiv(div)
{
	if ( document.getElementById(div).innerHTML )
	{
		myData[div] = document.getElementById(div).innerHTML;
		document.getElementById(div).innerHTML = '';
	}
	else
	{
		document.getElementById(div).innerHTML = myData[div];
		myData[div] = '';
	}
}

// Function for expanding all DIV tags (required for the form field values inside them to be submitted...really nice feature...!)
function ExpandAll()
{
	for ( key in myData ) 
	{
		if ( myData[key] )
		{
			ToggleDiv(key);
		}
	}
}

function fDisplayPotentialDupes(iClientX,iClientY)
{
	// Layer
	var oLayer = document.getElementById('potential_dupes');

	// Move to Mouse Position
	oLayer.style.top = iClientX;
	oLayer.style.left = iClientY;

	// Display Layer
	oLayer.style.display = 'block';
}
</script>

<br>
<?= ( $row['customer_marked_dead'] < 3 || in_array($row['customer_marked_dead'], $aAllowAccessToMarkedCustomers) ? '<form method="GET" action="action/save_customer_new.php" name="data" onSubmit="ExpandAll();">' : '' ) ?>
<input type="hidden" name="cst_id" value="<?=htmlspecialchars($cst_id)?>">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="2">
			<h1>
			<?php
				// Customer Locked Warning, Customer Name, Case, and Potential Dupe Warning
				echo $sWarning . htmlspecialchars($row['name']) . ' (' . htmlspecialchars($row['case_name']) . ( $sDupeCustomers ? ' / <a href="#" onClick="fDisplayPotentialDupes(event.clientX, event.clientY);" style="font-size: 18px; font-weight: bold;">Potential Dupes</a>' : '' ) . ')';
			?>
			</h1>
		</td>
		<td align="right">
			<?php
			// Random Customer from Segment
			if ( $_GET['from'] == 'random' && $persondata[5] == 5 )
			{
				echo '<input type="submit" name="submit_newrand" class="submit" style="width: 50%;" value="SAVE &amp; DISPLAY NEW CUSTOMER"><input type="hidden" name="segment_id" value="' . intval($_GET['segment_id']) . '">';
			}

			// If customer isn't dead and display submit button
			if ( $row['customer_marked_dead'] < 3 || in_array($row['customer_marked_dead'], $aAllowAccessToMarkedCustomers) )
			{
				echo '<input type="submit" name="submit" class="submit" style="width: 35%;" value="SAVE DATA">';
			}
			?>
		</td>
	</tr>
	<tr>
		<td width="49%" valign="top">&nbsp;</td>
		<td width="2%" valign="top">&nbsp;</td>
		<td width="49%" valign="top">&nbsp;</td>
	</tr>
	<tr>
		<td width="49%" valign="top">
			<?php
			// Display Customer Cases
			echo ShowCustomerCases($cst_id, $persondata);

			// Display customer data
			echo ShowCustomerData($cst_id, $cst_lang_id, $persondata);

			// Display sold products
			if ( $cst_id != 'new' )
			{
				echo ShowSoldProducts($cst_id);
			}

			// Display products avalible
			echo '<br>';

			// Offers Sent
			echo ShowOffersSent($crm_lang_id);

			// Product Actions
			echo ShowProductActions($crm_lang_id);

			// Display Mail-Templates if user is selling product 5
			echo DisplayMailTemplates();

			// Customer Facts
			echo ShowCustomerFacts($cst_id);
			?>
		</td>
		<td width="2%">&nbsp;</td>
		<td width="49%" valign="top">
			<?php
			// Check if lead
			$l_res = mysql_query("SELECT * FROM website.website_leads WHERE cst_id = '" . $cst_id . "' LIMIT 1");

			// Display Category Numbers
			echo ShowCategoryNumbers(
				$row['category_clients'],
				$row['category_servers'],
				$row['category_itpeople'],
				( mysql_num_rows($l_res) == 1 ? true : false ),
				$row['segment_id'],
				$row['forecast_expectancy'],
				$row['forecast_amount'],
				$row['forecast_date']
			);

			// Display Appointment Field
			echo ShowAppointmentField($cst_id, $persondata);

			// Display comment field
			echo ShowCommentField($cst_id, $persondata);

			// Display icons
			echo '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Customer Icons</font></b></legend>
			<table width="100%" cellspacing="0" cellpadding="0">
			<tr><td style="padding-left: 10px;">' . ShowIcon($cst_id, $cst_lang_id);

			// Awareness
			echo ShowAwarenessIcons();

			// Interest
			echo ShowInterestIcons();

			// Desire
			echo ShowDesireIcons();

			// Action
			echo ShowActionIcons() . '</td></tr></table></fieldset><br>';

			// Display sold products
			if ( $cst_id != 'new' )
			{
				echo '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Added Comments</font></b></legend>';
				echo ShowEnteredComments($cst_id) . '</fieldset>';
			}
			?>
		</td>
	</tr>
	<tr>
		<td width="49%" valign="top">&nbsp;</td>
		<td width="2%" valign="top">&nbsp;</td>
		<td width="49%" valign="top">&nbsp;</td>
	</tr>
	<tr>
		<td width="100%" colspan="3" align="center"><?= ( $row['customer_marked_dead'] < 3 || in_array($row['customer_marked_dead'], $aAllowAccessToMarkedCustomers) ? '<input type="submit" name="submit" class="submit" value="SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA - SAVE DATA">' : '' ) ?></td>
	</tr>
</table>
</form>

<div id="potential_dupes" style="width: 400px; border: 2px solid #000000; display: none; position: absolute; background: #FFFFFF; padding: 5px;">
<table width="400" cellspacing="0" cellpadding="0">
	<tr>
		<td colspan="2"><b>Potential dupes</b></td>
		<td align="right"><a href="#" onclick="document.getElementById('potential_dupes').style.display='none';">Close</a></td>
	</tr>
	<tr>
		<td colspan="3"><br></td>
	</tr>
	<tr>
		<td width="40%"><b>Company</b></td>
		<td width="30%"><b>Sales Rep.</b></td>
		<td width="30%"><b>Last Activity</b></td>
	</tr>
<?=$sDupeCustomers?>
</table>
<br>
</div>

<?php
// Output footer
echo HTMLFooter();
?>
