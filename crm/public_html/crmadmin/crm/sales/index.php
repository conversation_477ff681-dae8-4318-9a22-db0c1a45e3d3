<?php
require('configuration.php');
// Initialise (function libraries and openening DB connection)
require(INCLUDE_PATH_LIB.'initialise.php');

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');

// Read Demo Account Password
$pFile = fopen('/home/<USER>/capwd/pincode', 'r');
$sPassword = fgets($pFile, 1024);
fclose($pFile);

?>
<br>
<table width="600" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Customers
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="find_customer.php">Find Customer</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sale_report.php">Sale Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="forecast_report.php">Forecast Report</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="offers_report.php">Offers Report</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Inbound Leads
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="inbound_leads.php">Inbound Leads</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Send
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="find_advisory.php">Send Advisory</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Lists
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="appointment_list.php">Appointments List</a>
		</td>
	</tr>
	<?php
	if ( $persondata[5] == 5 || $persondata[0] == 2 )
	{
	?>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sss_account_overview.php">Surveillance Scanner - Account Overview</a>
		</td>
	</tr>
	<?php
	}
	?>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="customer_list.php">Customers List</a>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Random Customer
		</td>
	</tr>
	<?php
	$res = mysql_query("select * from sp_segment_ref where person_id = '" . $persondata[0] . "'");
	while (  $row = mysql_fetch_array($res) )
	{
	?>
		<tr>
			<td width="25">
				&nbsp;
			</td>
			<td width="575">
				<a href="customer_select.php?segment_id=<?=$row['segment_id']?>"><?=ReturnSegmentName($row['segment_id'])?></a>
			</td>
		</tr>
	<?php
	}
	?>
	<tr>
		<td colspan="2" width="600">
			<br>
	</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Customer Area
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="ca_account_management.php">Customer Area Account Administration</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			Current Demo Account Password: <b><?= $sPassword ?></b>
		</td>
	</tr>
	<tr>
		<td colspan="2" width="600">
			<br>
		</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2" width="600">
			Internal
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="internal.php">Contact List</a>
		</td>
	</tr>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="conference.php">Teleconference (How to)</a>
		</td>
	</tr>
	<?php
	if ( getenv('REMOTE_USER') != 'vochanda' && getenv('REMOTE_USER') != 'fosk' )
	{
	?>
	<tr>
		<td width="25">
			&nbsp;
		</td>
		<td width="575">
			<a href="sales_manual.sxw">Sales Manual</a>
		</td>
	</tr>
	<?php
	}
	?>
	<tr>
		<td colspan="2" width="600">
			<br><br>
		</td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
