<?php
//
// 2007-08-06: THIS IS A TEMPORARY FILE!!!
//

// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Limit to JB/TP/GH/RICCARDO/JAX
if ( $persondata[0] != 1 && $persondata[0] != 2 && $persondata[0] != 43 && $persondata[0] != 71 && $persondata[0] != 65 )
{
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');

$rRes = mysql_query("select nsi_account_requests.country, clients_total, clients_responsible, purchase_funds_available, purchase_timeframe, cst.appointment, cst.cst_id, nsi_account_requests.submitted, nsi_account_requests.company, nsi_account_requests.name as company_name, salespeople.name as salesrep_name from (website.nsi_account_requests, crm.cst) LEFT JOIN crm.salespeople on (cst.person_id = salespeople.person_id) where nsi_account_requests.cst_id = cst.cst_id order by submitted desc");
?>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="10%"><b>CST ID</b></td>
		<td width="10%"><b>Requested</b></td>
		<td width="5%"><b>Country</b></td>
		<td width="20%"><b>Company</b></td>
		<td width="10%"><b>Hosts</b></td>
		<td width="15%"><b>Name</b></td>
		<td width="5%"><b>TimeFrame</b></td>
		<td width="5%"><b>Funds</b></td>
		<td width="10%"><b>Sales Rep.</b></td>
		<td width="10%"><b>Next Appointment</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?php
	while ( $aRow = mysql_fetch_array($rRes) )
	{
		if ( !$aRow['salesrep_name'] )
		{
			$sColor = 'blue';
		}
		elseif ( $aRow['appointment'] == '0000-00-00 00:00:00' )
		{
			$sColor = 'red';
		}
		else
		{
			$sColor = '';
		}

		// CRM2: Get NSI Rep. details
		$rNSI = mysql_query("SELECT salespeople.name as salesrep_name, cst.appointment FROM crm.cst LEFT JOIN crm.salespeople on (cst.person_id = salespeople.person_id) WHERE cst.master_id = '" . $aRow['cst_id'] . "' && case_name = 'card_nsi' LIMIT 1");
		$aNSI = mysql_fetch_array($rNSI);

		echo '
        <tr>
                <td><a href="/crmadmin/crm2/sales/?page=customer&amp;cst_id=' . $aRow['cst_id'] . '" style="color: ' . $sColor . ';">' . $aRow['cst_id'] . '</a></td>
                <td style="color: ' . $sColor . ';">' . $aRow['submitted'] . '</td>
		<td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['country']) . '</td>
                <td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['company']) . '</td>
		<td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['clients_responsible']) . ' / ' . htmlspecialchars($aRow['clients_total']) . '</td>
                <td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['company_name']) . '</td>
		<td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['purchase_timeframe']) . '</td>
		<td style="color: ' . $sColor . ';">' . htmlspecialchars($aRow['purchase_funds_available']) . '</td>
                <td style="color: ' . $sColor . ';">' . htmlspecialchars($aNSI['salesrep_name']) . '</td>
                <td style="color: ' . $sColor . ';">' . $aNSI['appointment'] . '</td>
        </tr>';
	}
	?>
</table>
<br><br>
<?php
// Output footer
echo HTMLFooter();
?>
