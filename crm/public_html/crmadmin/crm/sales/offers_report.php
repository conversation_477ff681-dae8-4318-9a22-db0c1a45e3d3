<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Select all salespeople and build select options
$people = '';
$expires = '';

if ( $_GET['submitted'] )
{
	// Period?
	switch ( $_GET['period'] )
	{
		case 1:
			$expires = ' where expires = now()';
			$expires_real = ' where sold_date = now()';
			break;
		case 2:
			$expires = " where expires > '" . date('Y-m-d', mktime(0, 0, 0, date('m'), date('d')-date('w'), date('Y'))) . "' && expires <= '" . date('Y-m-d', mktime(0, 0, 0, date('m'), date('d')+(7-date('w')), date('Y'))) . "'";
			$expires_real = " where sold_date > '" . date('Y-m-d', mktime(0, 0, 0, date('m'), date('d')-date('w'), date('Y'))) . "' && sold_date <= '" . date('Y-m-d', mktime(0, 0, 0, date('m'), date('d')+(7-date('w')), date('Y'))) . "'";
			break;
		case 3:
			$expires = " where expires >= '" . date('Y-m-1') . "' && expires <= '" . date('Y-m-31') . "'";
			$expires_real = " where sold_date >= '" . date('Y-m-1') . "' && sold_date <= '" . date('Y-m-31') . "'";
			break;
		case 4:
                        $expires = " where expires >= '" . date('Y-m-1', mktime(0, 0, 0, date('m')+1, ( date('d') > 27 ? 25 : date('d') ), date('Y'))) . "'&& expires <= '" . date('Y-m-31', mktime(0, 0, 0, date('m')+1, ( date('d') > 27 ? 25 : date('d') ), date('Y'))) . "'";
                        $expires_real = " where sold_date >= '" . date('Y-m-1', mktime(0, 0, 0, date('m')+1, ( date('d') > 27 ? 25 : date('d') ), date('Y'))) . "' && sold_date <= '" . date('Y-m-31', mktime(0, 0, 0, date('m')+1, ( date('d') > 27 ? 25 : date('d') ), date('Y'))) . "'";
                        break;
	}

	// Select all offers
	$res = mysql_query("select * from offers" . $expires . " && person_id = '" . $persondata[0] . "'" . ' order by expires');
	while ( $row = mysql_fetch_array($res) )
	{
		// Select company details
		$c_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
		$c_row = mysql_fetch_array($c_res);

		$offers[$row['cst_id']]['name'] = $c_row['name'];
		$offers[$row['cst_id']]['product'] = $row['product'];
		$offers[$row['cst_id']]['expires'] = $row['expires'];
		$offers[$row['cst_id']]['person_id'] = $row['person_id'];
		$offers[$row['cst_id']]['count']++;
		$offers[$row['cst_id']]['amount'] += ReturnEuro($row['amount'], $row['currency']);
	}

	// Generate output list of offers
	while ( list($cst_id, $data) = @each($offers) )
	{
		// Reset vars
		$realised = NULL;
		$already_paid = NULL;
	
		// Select highest paid sale on this customer (if any)
		$s_res = mysql_query("select max((product_price-discount)) as max_amount, product_name from saleslog where cst_id = '" . $cst_id . "' && status = 2 group by cst_id");
		$s_row = mysql_fetch_array($s_res);
		$already_paid = ReturnEuro($s_row['max_amount'], $s_row['product_name']);

		// Check for realised sales in the period
		$r_res = mysql_query("select * from saleslog " . $expires_real . " && status in(NULL, 0, 2) && cst_id = '" . $cst_id . "'");
		echo mysql_error();
		while ( $r_row = mysql_fetch_array($r_res) )
		{
			$realised += ReturnEuro($r_row['product_price'] - $r_row['discount'], $r_row['product_name']);
			$realised_total += ReturnEuro($r_row['product_price'] - $r_row['discount'], $r_row['product_name']);
		}

		if ( round($data['amount']/$data['count']) >= 10000 || $already_paid >= 10000 )
		{
			$la .= '<tr>
			<td style="padding-left: 10px;"><a href="../sales/customer.php?cst_id=' . $cst_id . '" target="_blank">' . htmlspecialchars($data['name']) . '</a></td>
			<td>' . ReturnPersonNameFromID($data['person_id']) . '</td>
			<td>' . $data['product'] . ( $data['count'] > 1 ? ' (Multi)' : '' ) . '</td>
			<td align="right" style="padding-right: 20px;">' . number_format(round($data['amount']/$data['count'])) . ',-</td>
			<td>' . $data['expires'] . '</td>
			<td align="right" style="padding-right: 70px;">' . number_format($realised) . ',-</td>
			</tr>';
		}
		elseif ( round($data['amount']/$data['count']) < 10000 && $already_paid )
		{
			$pc .= '<tr>
			<td style="padding-left: 10px;"><a href="../sales/customer.php?cst_id=' . $cst_id . '" target="_blank">' . htmlspecialchars($data['name']) . '</a></td>
			<td>' . ReturnPersonNameFromID($data['person_id']) . '</td>
			<td>' . $data['product'] . ( $data['count'] > 1 ? ' (Multi)' : '' ) . '</td>
			<td align="right" style="padding-right: 20px;">' . number_format(round($data['amount']/$data['count'])) . ',-</td>
			<td>' . $data['expires'] . '</td>
			<td align="right" style="padding-right: 70px;">' . number_format($realised) . ',-</td>
			</tr>';
		}
		else
		{
			$nb .= '<tr>
			<td style="padding-left: 10px;"><a href="../sales/customer.php?cst_id=' . $cst_id . '" target="_blank">' . htmlspecialchars($data['name']) . '</a></td>
			<td>' . ReturnPersonNameFromID($data['person_id']) . '</td>
			<td>' . $data['product'] . ( $data['count'] > 1 ? ' (Multi)' : '' ) . '</td>
			<td align="right" style="padding-right: 20px;">' . number_format(round($data['amount']/$data['count'])) . ',-</td>
			<td>' . $data['expires'] . '</td>
			<td align="right" style="padding-right: 70px;">' . number_format($realised) . ',-</td>
			</tr>';
		}

		$total += round( $data['amount'] / $data['count'] );
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>

<br>
<form method="GET" action="offers_report.php">
<input type="hidden" name="submitted" value="1">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="6"><b>Offers Report</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		</td>
		<td colspan="2" valign="top">
			<b>Select Period</b><br>
			<input type="radio" name="period" value="1" style="width: 15px;" <?=( $_GET['period'] == 1 || !$_GET['period'] ? 'checked' : '' )?>> Today<br>
			<input type="radio" name="period" value="2" style="width: 15px;" <?=( $_GET['period'] == 2 ? 'checked' : '' )?>> This Week (Mon-Fri)<br>
			<input type="radio" name="period" value="3" style="width: 15px;" <?=( $_GET['period'] == 3 ? 'checked' : '' )?>> This Month (01-31)<br>
			<input type="radio" name="period" value="4" style="width: 15px;" <?=( $_GET['period'] == 4 ? 'checked' : '' )?>> Next Month (01-31)<br>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td colspan="6">
			<input type="submit" value="Display Report" class="submit">
		</td>
	</tr>
	<tr>
		<td colspan="6"><br><hr><br></td>
	</tr>
	<tr>
		<td width="35%"><b>Company Name</b></td>
		<td width="15%"><b>Sales Person</b></td>
		<td width="15%"><b>Solution</b></td>
		<td width="10%"><b>Amount</b></td>
		<td width="10%"><b>Expires</b></td>
		<td width="15%"><b>Realised</b></td>
	</tr>
	<tr>
		<td><br><br></td>
	</td>
	<tr>
		<td><b>Large Accounts</b></td>
	</tr>
	<?=$la?>
	<tr>
		<td><br><br></td>
	</td>
	<tr>
		<td><b>Paying Customers</b></td>
	</tr>
	<?=$pc?>
	<tr>
		<td><br><br></td>
	</tr>
	<tr>
		<td><b>New Bizz</b></td>
	</tr>
	<?=$nb?>
	<tr>
		<td colspan="6"><br><hr><br></td>
	</tr>
	<tr>
		<td colspan="3" align="right"><b>Total:</b></td>
		<td align="right" style="padding-right: 40px;" colspan="2"><?=number_format($total, 2)?></td>
		<td align="right" style="padding-right: 70px;"><?=number_format($realised_total, 2)?></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
