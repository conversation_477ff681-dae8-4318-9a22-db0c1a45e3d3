<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Variables
$available = '';
$taken = '';
$error = false;

// Lead "grap" request - verify that the lead is still available
if ( $_GET['action'] == 'take' && $_GET['cst_id'] )
{
	// Check that the lead is still available
	if ( mysql_num_rows(mysql_query("SELECT * FROM website.website_leads WHERE id = '"  . $_GET['lead_id'] . "' && status IS NULL")) == 1 )
	{
		// Update lead (cst_id / person_id)
		mysql_query("update website.website_leads set person_id = '" . $persondata[0] . "', cst_id = '" . $_GET['cst_id'] . "', status = 1 where id = '"  . $_GET['lead_id'] . "' limit 1");

		// Redirect to customer pane
		header("Location: customer.php?cst_id=" . $_GET['cst_id']);
		exit();
	}
	elseif ( mysql_num_rows(mysql_query("SELECT * FROM crm.cst WHERE cst_id = '"  . $_GET['cst_id'] . "' && customer_marked_dead = 3")) == 1 || mysql_num_rows(mysql_query("SELECT * FROM crm.cst WHERE cst_id = '"  . $_GET['cst_id'] . "'")) == 0 ) // Validate CST_ID
	{
		$error = 'Not a valid Customer ID.';
	}
	else
	{
		// Someone took the lead in the mean time - display error
		$error = 'The lead you requested appears to taken by another sales person.';
	}
}

// Available leads
if ( $persondata[0] != 75 || $row['country'] == 'DE' )
{
	// Available leads
	$res = returnAvailableLeadsArray();

	// Generate Output List
	while ( $row = @mysql_fetch_array($res) )
	{
		// Change bgcolor
		$bgcolor = ( $bgcolor == '#FFFFFF' ? '#DEDEDE' : '#FFFFFF' );

		$available .= '
	<tr bgcolor="' . $bgcolor . '">
		<td style="padding-left: 10px;" valign="top"><a href="#" onClick="var cst_id = prompt(\'Confirm that you wish to take this inbound lead by entering the leads Customer ID here - you can find the Customer ID by searching the CRM. If the lead does not exist in the CRM, then request a Customer ID through your Sales Manager.\'); ( cst_id.length > 0 ? location = \'inbound_leads.php?action=take&amp;lead_id=' . $row['id'] . '&amp;company=' . urlencode($row['company']) . '&amp;name=' . urlencode($row['name']) . '&amp;phone=' . urlencode($row['telephone']) . '&amp;title=' . urlencode($row['job_title']) . '&amp;email=' . urlencode($row['email']) . '&amp;cst_id=\' + cst_id : \'\' )">' . $row['company'] . '</a></td>
		<td valign="top">' . $row['name'] . '</td>
		<td valign="top">' . $row['telephone'] . '</td>
		<td valign="top">' . $row['email'] . '</td>
		<td valign="top">' . $row['country'] . '</td>
		<td valign="top">' . $row['reference'] . '</td>
		<td valign="top">' . $row['business_need'] . '</td>
		<td valign="top">' . $row['type'] . '</td>
		<td valign="top">' . $row['submitted'] . '</td>
	</tr>';
	}
}

// Taken leads
{
	// Select all taken leads
	$res = mysql_query("SELECT * FROM website.website_leads WHERE person_id = '" . $persondata[0] . "' ORDER BY submitted DESC");

	// Generate Output List
	while ( $row = @mysql_fetch_array($res) )
	{
		$taken .= '
	<tr>
		<td style="padding-left: 10px;" valign="top"><a href="customer.php?cst_id=' . $row['cst_id'] . '">' . $row['company'] . '</a></td>
		<td valign="top">' . $row['name'] . '</td>
		<td valign="top">' . $row['telephone'] . '</td>
		<td valign="top">' . $row['email'] . '</td>
		<td valign="top">' . $row['country'] . '</td>
		<td valign="top">' . $row['reference'] . '</td>
		<td valign="top">' . $row['business_need'] . '</td>
		<td valign="top">' . $row['type'] . '</td>
		<td valign="top">' . $row['submitted'] . '</td>
	</tr>';
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php', 'inbound_leads.js');

// Error?
if ( $error )
{
	echo '<script>alert("' . $error . '");</script>';
}
?>
<br>

<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td><b>Available Inbound Customer Leads:</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 10px;" width="15%" onClick="oSortAvailable.mSort(this, 0, 'string');" class="imitateLink"><b>Company</b></td>
		<td width="10%" onClick="oSortAvailable.mSort(this, 1, 'string');" class="imitateLink"><b>Person</b></td>
		<td width="9%" onClick="oSortAvailable.mSort(this, 2, 'string');" class="imitateLink"><b>Telephone</b></td>
		<td width="9%" onClick="oSortAvailable.mSort(this, 3, 'string');" class="imitateLink"><b>Email</b></td>
		<td width="9%" onClick="oSortAvailable.mSort(this, 4, 'string');" class="imitateLink"><b>Country</b></td>
		<td width="9%" onClick="oSortAvailable.mSort(this, 5, 'string');" class="imitateLink"><b>Reference</b></td>
		<td width="15%" onClick="oSortAvailable.mSort(this, 6, 'string');" class="imitateLink"><b>Business Need</b></td>
		<td width="15%" onClick="oSortAvailable.mSort(this, 7, 'string');" class="imitateLink"><b>Request Type</b></td>
		<td width="9%" onClick="oSortAvailable.mSort(this, 8, 'string');" class="imitateLink"><b>Request Received</b></td>
	</tr>
	<tbody id="available">
	<?= $available ?>
	</tbody>
	<tr>
		<td><br></td>
	</tr>

	<?= returnPageDelimeter(9, '100%', 1) ?>

	<tr>
		<td><b>Your Inbound Customer Leads:</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td style="padding-left: 10px;" onClick="oSortTaken.mSort(this, 0, 'string');" class="imitateLink"><b>Company</b></td>
		<td onClick="oSortTaken.mSort(this, 1, 'string');" class="imitateLink"><b>Person</b></td>
		<td onClick="oSortTaken.mSort(this, 2, 'string');" class="imitateLink"><b>Telephone</b></td>
		<td onClick="oSortTaken.mSort(this, 3, 'string');" class="imitateLink"><b>Email</b></td>
		<td onClick="oSortTaken.mSort(this, 4, 'string');" class="imitateLink"><b>Country</b></td>
		<td onClick="oSortTaken.mSort(this, 5, 'string');" class="imitateLink"><b>Reference</b></td>
		<td onClick="oSortTaken.mSort(this, 6, 'string');" class="imitateLink"><b>Business Need</b></td>
		<td onClick="oSortTaken.mSort(this, 7, 'string');" class="imitateLink"><b>Request Type</b></td>
		<td onClick="oSortTaken.mSort(this, 8, 'string');" class="imitateLink"><b>Request Received</b></td>
	</tr>
	<tbody id="taken">
	<?= $taken ?>
	</tbody>
	<tr>
		<td><br><br><br><br></td>
	</tr>
</table>


<?php
// Output footer
echo HTMLFooter();
?>
