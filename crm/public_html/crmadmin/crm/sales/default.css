td
{
	font-family: verdana, arial;
	font-weight: normal;
	font-size: 11px;
	color: black;
}

td.BlockHeadline
{
	font-family: verdana, arial;
	font-weight: bold;
	font-size: 11px;
	color: black;
}

td.MenuHeadline
{
	font-family: verdana, arial;
	font-weight: bold;
	text-decoration: strong;
	font-size: 14px;
	color: black;
}

td.TopText
{
	font-family: verdana, arial;
	font-weight: normal;
	font-size: 14px;
	color: black;
}

td.PageHeadline
{
	font-family: verdana, arial;
	font-weight: bold;
	font-size: 14px;
	color: black;
}

A:link
{
	font-family: verdana, arial;
	font-size: 11px;
	text-decoration: underline;
	font-weight: normal;
}

A:visited
{
	font-family: verdana, arial;
	FONT-SIZE: 11px;
	text-decoration: underline;
	font-weight: normal;
}


input
{
	font-family: verdana, arial;
	font-size: 11px;
	color: black;
	border: 0px;
	border-bottom: 1px solid white;
	width: 100%;
	background: #D6E4EC;
}

input:hover
{
	border-bottom: 1px solid black;
}

input:focus
{
	border-bottom: 1px solid black;
}

textarea
{
	font-family: verdana, arial;
	font-size: 11px;
	color: black;
	border: 0px;
	width: 100%;
	background: #D6E4EC;
}

select
{
	font-family: verdana, arial;
	font-size: 11px;
	border: 1px solid black;
	background: #D6E4EC;
}


.imitateLink 
{
	cursor: pointer;
	text-decoration: underline;
}

.hoverTRPointer
{
	background: #D6E4EC;
	cursor: pointer;
}

.noHoverTRPointer
{
	background: #FFFFFF;
	cursor: auto;
}
