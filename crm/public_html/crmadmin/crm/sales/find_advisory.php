<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<form method="GET" action="find_advisory.php">
<table width="100%" cellpadding="0" cellspacing="0">
        <tr>
                <td class="MenuHeadline" colspan="2" width="100%">
                        Find advisory, email to customer
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        Search for:<br>
                        <input type="text" name="search" value="<?=htmlspecialchars($search)?>">
                        <input type="hidden" name="email" value="<?=htmlspecialchars($email)?>">
                        <input type="hidden" name="lang_id" value="<?=htmlspecialchars($lang_id)?>">
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <input type="submit" name="submit" value="Search">
                </td>
        </tr>
        </form>
        <tr>
                <td colspan="2" width="100%">
                	<br>
                        <b><?=ReturnLangName( ( $lang_id ? $lang_id : 1 ) )?> advisory will be sent!!!</b><br></b><br>
                        <br>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
			<?php
			// Search
			if ( $search )
			{
				// Output
				echo '<img src="gfx/longline.gif" width="100%" height="1"><br><b>Result:</b><br><br>';

				// Make search
				$s_res = mysql_query("select distinct vuln_id from vuln_track.os_soft, vuln_track.os_soft_rel where os_soft_name like '%" . $search . "%' && (os_soft_rel.os_id = os_soft_id || os_soft_rel.soft_id = os_soft_id)");

				// New table
				echo '<table width="100%" cellpadding="0" cellspacing="0">';

				// Output
				echo '<tr><td width="10%"><b>Date</b></td>';
				echo '<td width="50%"><b>Advisory title</b></td>';
				echo '<td width="20%"><b>E-mail</b></td>';
				echo '<td width="20%"><b>Send</b></td>';
				echo '<tr><td colspan="3"><br></td></tr>';

				// Loop over result
				while ( $s_row = mysql_fetch_array($s_res) )
				{
					// Select advisories
					$res = mysql_query("select " . ( $lang_id > 1 ? 'vuln_reference.' : '' ) . "vuln_title, vuln_create_date from vuln_track.vuln " . ( $lang_id > 1 ? ', vuln_track.vuln_reference' : '' ) . " where vuln_id = '" . $s_row['vuln_id'] . "' " . ( $lang_id > 1 ? ' && m_vuln_id = vuln_id' : '' ) . " && vuln.vuln_status != 4");
					if ( mysql_num_rows($res) )
					{
						$row = mysql_fetch_array($res);
					}
					else
					{
						continue;
					}

					echo '<form method="GET" action="sender/send_advisory_' . ( $lang_id ? $lang_id : 1 ) . '.php"><input type="hidden" name="vuln_id" value="' . $s_row['vuln_id'] . '">';
					echo '<tr><td width="10%">' . $row['vuln_create_date'] . '</td>';
					echo '<td width="50%"><a href="http://www.secunia.com/advisories/' . $s_row['vuln_id'] . '/" target="_blank">' . $row['vuln_title'] . '</a></td>';
					echo '<td width="20%"><input type="text" name="email" value="' . htmlspecialchars($email) . '"></a></td>';
					echo '<td width="20%"><input type="submit" value="Send"></td></tr></form>';
                                }

                                // End table
                                echo '</table>';
                        }

                        ?>
                </td>
        </tr>
        <tr>
                <td colspan="2" width="100%">
                        <br><br>
                </td>
        </tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
