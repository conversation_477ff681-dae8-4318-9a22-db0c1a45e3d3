<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Select all customers which this salesperson administrates and who has been sold to
$res = mysql_query("select distinct cst_id, name, contact from cst where cst.person_id = '" . $persondata[0] . "'");
while ( $row = mysql_fetch_array($res) )
{
	// Reset vars
	$comment = '';
	$font = '';
	$payment_status = '';

	// Select saleslog data
	$s_res = mysql_query("select max(sale_id) as sale_id from saleslog where cst_id = '" . $row['cst_id'] . "' && (status is null || status != 3) && ((product_price - discount) > 0) && product_name not like 'Extra%' group by cst_id");
	if ( !mysql_num_rows($s_res) )
	{
		continue;
	}

	// Get result
	$s_row = mysql_fetch_array($s_res);

	// Select sale data
	$s_res = mysql_query("select * from saleslog where sale_id = '" . $s_row['sale_id'] . "'");
	$s_row = mysql_fetch_array($s_res);

	// Set color
	if ( $s_row['product_trial'] || ($s_row['product_price'] - $s_row['discount']) == 0 )
	{
		#$comment = 'Trial/Free';
		#$font = '<font color="darkgreen">';
	}
	elseif ( $s_row['product_type'] && $s_row['status'] == 2 )
	{
		#$comment = 'OK';
		$font = '<font color="darkgreen">';
	}

	// Check if theres any product at status awaiting and a price > 0
	{
		$payment_status = ( ($s_row['product_price'] - $s_row['discount']) == 0 ? 'N/A' : ReturnSaleStatus($s_row['status']) );
		// Select all sales where status awaiting and a price > 0
		$st_res = mysql_query("select * from saleslog where cst_id = '" . $row['cst_id'] . "' && (status is null || status = 0) && ((product_price - discount) > 0)");
		if ( mysql_num_rows($st_res) > 0 )
		{
			$payment_status = mysql_num_rows($st_res) . ' Sale(s) Awaits';
			$font = '';
		}
	}

	// Check if sold product has expired
	{
		$p_res = mysql_query("select * from saleslog where cst_id = '" . $row['cst_id'] . "' && expires_date > now() && (status is NULL || status != 3)");

		// If this gives no results then it has expired
		if ( !mysql_num_rows($p_res) )
		{
			$comment = 'Product Expired';
			$font = '<font color="red">';
		}
	}

	// Select "largest" expiry date
	{
		$e_res = mysql_query("select max(expires_date) as expiry from saleslog where cst_id = '" . $row['cst_id'] . "' && (status is NULL || status != 3) && product_name not like 'Extra%' limit 1");
		$e_row = mysql_fetch_array($e_res);
	}

	// Check if this product is about to expire within the next 3 months
	$month_3 = time() + (60 * 60 * 24 * 31 * 3);
	if ( $month_3 > strtotime($e_row['expiry']) && strtotime($e_row['expiry']) > time() )
	{
		$comment = 'Within 3 months';
		$font = '<font color="orange">';
	}
	
	// Select extra virus / scans
	{
		$scn_res = mysql_query("select * from ca.extra_scans where cst_id = '" . $row['cst_id'] . "' limit 1");
		$scn_row = mysql_fetch_array($scn_res);
		$vir_res = mysql_query("select * from ca.extra_virus where cst_id = '" . $row['cst_id'] . "' limit 1");
		$vir_row = mysql_fetch_array($vir_res);
		if ( $scn_row['number'] )
		{
			$comment = '+ ' . $scn_row['number'] . ' Scan Account(s)' . '<br>' . $comment;
		}
		if ( $vir_row['number'] )
		{
			$comment = '+ ' . $vir_row['number'] . ' Virus Account(s)' . '<br>' . $comment;
		}
	}

	// Output
	$output[$e_row['expiry'] . '0' . $row['cst_id']] = '<tr>';
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td style="padding-left: 3px;" valign="top"><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '">' . $font . $row['name'] . '</td>';
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td valign="top">' . $font . $s_row['product_name'] . ', ' . ( $s_row['product_price'] - $s_row['discount'] ) . '</td>';
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td valign="top">' . $font . $payment_status . '</td>';
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td valign="top">' . $font . $comment . '</td>';	
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td valign="top">' . $font . $e_row['expiry'] . '</td>';
	$output[$e_row['expiry'] . '0' . $row['cst_id']] .= '<td valign="top">' . $font . $row['appointment'] . '</td></tr>';
}

// Sort by key
@ksort($output);

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="6" width="100%">
			Customer list
		</td>
	</tr>
	<tr>
		<td>
			<br>
		</td>
	</tr>
	<tr>
		<td colspan="3">
		<td colspan="3">
			<b>Colors means the following:</b><br>
			<font color="darkgreen">Green = Payed &amp; Active<br></font>
			<font color="red">Red = Product expired<br></font>
			<font color="orange">Orange = Product Expires within 3 month.<br></font>
			<font color="black">Black = Awaiting &amp; Active<br></font>
			<br><br>
		</td>
	</tr>
	<tr>
		<td>
			<br><br>
		</td>
	</tr>
	<tr bgcolor="#DADADA">
		<td width="20%" style="padding-left: 3px;"><b>Company Name</b>
		</td>
		<td width="30%"><b>Product</b>
		</td>
		<td width="10%"><b>Payment Status</b>
		</td>
		<td width="15%"><b>Comment</b>
		</td>
		<td width="10%"><b>Expires</b>
		</td>
		<td width="20%"><b>Next Appointment</b>
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<?php
	// Loop over result
	while ( list($expires, $data) = @each($output) )
	{
		echo $data;
	}
	?>
	<tr>
		<td><br><br></td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
