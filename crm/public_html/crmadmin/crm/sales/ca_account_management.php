<?php
require('configuration.php');
// Initialise (function libraries and openening DB connection)
require(INCLUDE_PATH_LIB.'initialise.php');

// Set vars
$result = '';

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Select data - if search
if ( $_POST['search'] )
{
	if ( is_numeric($_POST['search']) )
	{
		$res = mysql_query("select * from ca.accounts, cst where accounts.cst_id = '" . $_POST['search'] . "' && accounts.cst_id = cst.cst_id order by name, account_username");
	}
	else
	{
		$res = mysql_query("select * from ca.accounts, cst where account_username like '%" . preg_replace('([%_])', '\\\\0', $_POST['search']) . "%' && accounts.cst_id = cst.cst_id order by name, account_username");
	}

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		// BG color
		if ( $bgcolor == '#FFFFFF' )
		{
			$bgcolor = '#D2D2D2';
		}
		else
		{
			$bgcolor = '#FFFFFF';
		}

		// Build output
		$result .= '
		<tr bgcolor="' . $bgcolor . '">
			<td style="padding-left: 3px;">' .( ($persondata[0] == $row['person_id'] || $persondata[0] == 60) && !$row['account_esm'] ? '<a href="ca_account_management_details.php?account_id=' . $row['account_id'] . '&amp;cst_id=' . $row['cst_id'] . '">' : ( $row['account_esm']  ? '[ESM-SUB] ' : '' ) ) . htmlspecialchars($row['account_username']) . '</td>
			<td><a href="customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . htmlspecialchars($row['name']) . '</a> [ID: ' . $row['cst_id'] . ']</td>
			<td>' . ReturnPersonNameFromID($row['person_id']) . '</td>
		</tr>';
	}
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>

<form method="POST" action="ca_account_management.php">
<table width="100%" cellpadding="1" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="3">
			Advanced Customer Area Administration
		</td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td valign="top" width="30%">Search (Username or Customer ID):<br><input type="text" name="search" value="<?=htmlspecialchars($_POST['search'])?>"></td>
		<td valign="top" width="30%" style="padding-left: 10px;"><br><input type="submit" value="Search" class="submit"></td>
		<td width="40%"></td>
	</tr>
	<tr>
		<td><br><br></td>
	</tr>
	<?php
	if ( $result )
	{
		?>
		<tr>
			<td colspan="2"><b>Result:</b></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Username [EDIT]</b></td>
			<td><b>Customer Name</b></td>
			<td><b>Sales Person</b></td>
		</tr>
		<?php
		echo $result;
		?>
		<tr>
			<td><br><br></td>
		</tr>
		<?php
	}
	?>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
