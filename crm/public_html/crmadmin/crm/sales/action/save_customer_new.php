<?php
require('../configuration.php');
// Initialise (function libraries and openening DB connection)
require(INCLUDE_PATH_LIB.'initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Get lang id
if ( !$cst_lang_id )
{
	$cst_lang_id = ReturnCustomerLangID($cst_id);
}

// Check if cst_lang_id isset else set to be input or salespersons
if ( !$cst_lang_id )
{
	if ( $lang_id )
	{
		$cst_lang_id = $lang_id;
	}
	else
	{
		$cst_lang_id = $persondata[1];
	}
}

// Save the customer, insert if cst = new else update
if ( $cst_id == 'new' && ( $persondata[0] == 2 || $persondata[0] == 3 || $persondata[0] == 1 || $persondata[0] == 48 || $persondata[0] == 73 || $persondata[0] == 74 ) )
{
	// Insert
	mysql_query("INSERT INTO crm.cst (name, phone, fax, contact, email, web, company_number, field_1, field_2, field_3, field_4, field_5, field_6, field_7, field_8, field_9, field_10, field_11, field_12, field_13, field_14, field_15, appointment, person_id, lang_id, vuln_lang_id, crm_lang_id, phone_2 ,contact_2 ,email_2, phone_3, contact_3, email_3, contact_type_1, contact_type_2, contact_type_3, po_number, case_status, case_name, case_opened, invoice_country, category, category_clients, category_servers, category_itpeople, forecast_expectancy, forecast_amount, forecast_date, customer_marked_dead) VALUES('" . $name . "', '" . $phone . "', '" . $fax . "', '" . $contact . "', '" . $email . "', '" . $web . "', '" . $company_number . "', '" . $field_1 . "', '" . $field_2 . "', '" . $field_3 . "', '" . $field_4 . "', '" . $field_5 . "', '" . $field_6 . "', '" . $field_7 . "', '" . $field_8 . "', '" . $field_9 . "', '" . $field_10 . "', '" . $field_11 . "', '" . $field_12 . "', '" . $field_13 . "', '" . $field_14 . "', '" . $field_15 . "', '" . $return_date . "', '" . $persondata[0] . "', '" . $cst_lang_id . "', '" . $cst_vuln_lang_id . "', '" . $crm_lang_id . "', '" . $_GET['phone_2'] . "', '" . $_GET['contact_2'] . "', '" . $_GET['email_2'] . "', '" . $_GET['phone_3'] . "', '" . $_GET['contact_3'] . "', '" . $_GET['email_3'] . "', '" . $_GET['contact_type_1'] . "', '" . $_GET['contact_type_2'] . "', '" . $_GET['contact_type_3'] . "', '" . $_GET['po_number'] . "', 1, 'Default', now(), '" . $_GET['invoice_country'] . "', '" . $_GET['category'] . "', '" . $_GET['category_clients'] . "', '" . $_GET['category_servers'] . "', '" . $_GET['category_itpeople'] . "', '" . $_GET['forecast_expectancy'] . "', '" . preg_replace('[^0-9]*', '', $_GET['forecast_amount']) . "', '" . $_GET['forecast_date'] . "', '" . ( $_GET['marked_sv'] == 1 ? 6 : 0 ) . "')");

	// Get cst_id
	$cst_id = mysql_insert_id();

	// Log forecast
	mysql_query("INSERT INTO crm.forecast_log (cst_id, person_id, logged, expectancy, amount) VALUES('" . $cst_id . "', '" . $persondata[0] . "', now(), '" . $_GET['forecast_expectancy'] . "', '" . preg_replace('[^0-9]*', '', $_GET['forecast_amount']) . "')");
}
else
{
	// Check if customer has en owner
	$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
	$row = mysql_fetch_array($res);
	if ( !$row['person_id'] )
	{
		// Update person id, set appointment
		$update_person_id = ", person_id = '" . $persondata[0] . "', appointment = '" . $return_date . "'";
	}
	elseif ( $persondata[0] == $row['person_id'] )
	{
		// Update appointment
		$update_person_id = ", appointment = '" . $return_date . "'";
	}

	// Check if we should update accounts table with new vulnerability language
	if ( ( intval($cst_lang_id) || intval($cst_vuln_lang_id) ) && $email )
	{
		mysql_query("update ca.accounts set lang_id = '" . $cst_vuln_lang_id . "', ca_lang_id = '" . $cst_lang_id . "' where cst_id = '" . $row['cst_id'] . "' && account_username = '" . $email . "'");
	}

	// Update
	mysql_query("update cst set name = '" . $name . "', phone = '" . $phone . "', fax = '" . $fax . "', contact = '" . $contact . "', email = '" . $email . "', web = '" . $web . "', company_number = '" . $company_number . "', lang_id = '" . $cst_lang_id . "', vuln_lang_id = '" . $cst_vuln_lang_id . "', crm_lang_id = '" . $crm_lang_id . "', field_1 = '" . $field_1 . "', field_2 = '" . $field_2 . "', field_3 = '" . $field_3 . "', field_4 = '" . $field_4 . "', field_5 = '" . $field_5 . "', field_6 = '" . $field_6 . "', field_7 = '" . $field_7 . "', field_8 = '" . $field_8 . "', field_9 = '" . $field_9 . "', field_10 = '" . $field_10 . "', field_11 = '" . $field_11 . "', field_12 = '" . $field_12 . "', field_13 = '" . $field_13 . "', field_14 = '" . $field_14 . "', field_15 = '" . $field_15 . "', phone_2 = '" . $_GET['phone_2'] . "', contact_2 = '" . $_GET['contact_2'] . "', email_2 = '" . $_GET['email_2'] . "', phone_3 = '" . $_GET['phone_3'] . "', contact_3 = '" . $_GET['contact_3'] . "', email_3 = '" . $_GET['email_3'] . "', contact_type_1 = '" . $_GET['contact_type_1'] . "', contact_type_2 = '" . $_GET['contact_type_2'] . "', contact_type_3 = '" . $_GET['contact_type_3'] . "', invoice_country = '" . $_GET['invoice_country'] . "', po_number = '" . $_GET['po_number'] . "', category_clients = '" . preg_replace('[^0-9]*', '', $_GET['category_clients']) . "', category_servers = '" . preg_replace('[^0-9]*', '', $_GET['category_servers']) . "', category_itpeople = '" . preg_replace('[^0-9]*', '', $_GET['category_itpeople']) . "', category = '" . $_GET['category'] . "', forecast_expectancy = '" . $_GET['forecast_expectancy'] . "', forecast_amount = '" . preg_replace('[^0-9]*', '', $_GET['forecast_amount']) . "', forecast_date = '" . $_GET['forecast_date'] . "'" . $update_person_id . ( $_GET['marked_sv'] == 1 ? ', customer_marked_dead = 6' : '' ) . " where cst_id = '" . $cst_id . "'");
	
	// Check if forecast changed since last, then log it
	if ( $_GET['forecast_expectancy'] != $row['forecast_expectancy'] || preg_replace('[^0-9]*', '', $_GET['forecast_amount']) != $row['forecast_amount'] )
	{
		mysql_query("INSERT INTO crm.forecast_log (cst_id, person_id, logged, expectancy, amount) VALUES('" . $cst_id . "', '" . $persondata[0] . "', now(), '" . $_GET['forecast_expectancy'] . "', '" . preg_replace('[^0-9]*', '', $_GET['forecast_amount']) . "')");
	}
}

// Allow sending mails to this customer from app. list.
{
	// Remove from list
	mysql_query("delete from app_send_mail where cst_id = '" . $cst_id . "' limit 1");

	if ( $_GET['app_mail'] )
	{
		// Insert into list
		mysql_query("insert into app_send_mail (cst_id) values('" . $cst_id . "')");
	}
}

// Save the AIDA Icons
{
	// Clean up aida ref table
	mysql_query("delete from icons_aida_ref where cst_id = '" . $cst_id . "'");

	// 'c'
	if ( $_GET['icon_c'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_c'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_c'] . "', now())");
	}

	// 'o'
	if ( $_GET['icon_o'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_o'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_o'] . "', now())");
	}

	// 's'
	if ( $_GET['icon_s'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_s'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_s'] . "', now())");
	}

	// 'v'
	if ( $_GET['icon_v'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_v'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_v'] . "', now())");
	}

	// 'e'
	if ( $_GET['icon_e'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_e'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_e'] . "', now())");
	}

	// 'd'
	if ( $_GET['icon_d'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_d'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_d'] . "', now())");
	}

	// '$'
	if ( $_GET['icon_$'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_$'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_$'] . "', now())");
	}

	// '$$'
	if ( $_GET['icon_$$'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_$$'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_$$'] . "', now())");
	}

	// 'U'
	if ( $_GET['icon_u'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_u'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_u'] . "', now())");
	}

	// 'star'
	if ( $_GET['icon_star'] )
	{
		mysql_query("insert into icons_aida_ref (cst_id, icon_id) values('" . $cst_id . "', '" . $_GET['icon_star'] . "')");
		mysql_query("insert into icons_aida_log (cst_id, icon_id, registered) values('" . $cst_id . "', '" . $_GET['icon_star'] . "', now())");
	}
}

// Save Comments
{
	if ( $_GET['it_org'] ) // 1
	{
		$_GET['it_org'] = preg_replace("/\n/", '<br>', $_GET['it_org']);
		mysql_query("insert into comments (comment, added, return_time, cst_id, person_id, type) values('" . $_GET['it_org'] . "', now(), '" . $return_date . "', '" . $cst_id . "', '" . $persondata[0] . "', 1)");
	}
	if ( $_GET['cust_rel'] ) // 2
	{
		$_GET['cust_rel'] = preg_replace("/\n/", '<br>', $_GET['cust_rel']);
		mysql_query("insert into comments (comment, added, return_time, cst_id, person_id, type) values('" . $_GET['cust_rel'] . "', now(), '" . $return_date . "', '" . $cst_id . "', '" . $persondata[0] . "', 2)");
	}
	if ( $_GET['setup'] ) // 3
	{
		$_GET['setup'] = preg_replace("/\n/", '<br>', $_GET['setup']);
		mysql_query("insert into comments (comment, added, return_time, cst_id, person_id, type) values('" . $_GET['setup'] . "', now(), '" . $return_date . "', '" . $cst_id . "', '" . $persondata[0] . "', 3)");
	}
	if ( $_GET['sales_buying'] ) // 4
	{
		$_GET['sales_buying'] = preg_replace("/\n/", '<br>', $_GET['sales_buying']);
		mysql_query("insert into comments (comment, added, return_time, cst_id, person_id, type) values('" . $_GET['sales_buying'] . "', now(), '" . $return_date . "', '" . $cst_id . "', '" . $persondata[0] . "', 4)");
	}
}

// Save New Offer?
if ( $_GET['offer_solution'] && $_GET['offer_amount'] )
{
	mysql_query("insert into offers (cst_id, person_id, amount, currency, product, expires, expectancy) values('" . $cst_id . "', '" . $persondata[0] . "', '" . $_GET['offer_amount'] . "', '" . $_GET['offer_currency'] . "', '" . $_GET['offer_solution'] . "', '" . $_GET['offer_expires'] . "', '" . $_GET['offer_expectancy'] . "')");
}

// Save icon references
mysql_query("delete from icon_ref where cst_id = '" . $cst_id . "'");
while ( list($key, $icon_id) = @each($icons) )
{
	mysql_query("insert into icon_ref (icon_id, cst_id) values('" . $icon_id . "', '" . $cst_id . "')");
}

// Open and Register a NSI Account
if ( $_GET['nsi_account_open'] )
{
	// Set today+1 month expiry date
	$sExpire = date('Y-m-d', time()+604800);

	// Select SP data
	$rSalesPerson = mysql_query("SELECT * FROM crm.salespeople WHERE person_id = '" . $persondata[0] . "' LIMIT 1");
	$aSalesPerson = mysql_fetch_array($rSalesPerson);

	// Save sale
	mysql_query("INSERT INTO crm.saleslog (sold_date, expires_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id) VALUES(NOW(), '" . $sExpire . "', '0', 'NSI Trial Account', 0, 9, 1, '" . $cst_id . "', '" . $persondata[0] . "', '9', 0, '" . $crm_lang_id . "')");

	// Generate Activation Pin Code
	$iPinCode = rand(1000,9999);

	// Register account in Customer Area
	mysql_query("INSERT INTO ca.accounts (account_name, account_username, account_password, account_login_type, account_product_type, modules, show_modules, account_recv_all, account_expires, cst_id, account_gen_pwd, lang_id, ca_lang_id) VALUES('" . $contact . "', '" . $_GET['nsi_account_username'] . "', PASSWORD('" . $iPinCode . "'), '1', 9, " . $aDefaultModuleMappings[9]['modules'] . ", " . $aDefaultModuleMappings[9]['show_modules'] . ", '1', '" . $sExpire . "', '" . $cst_id . "', 1, 1, 1)");

	// Get Account ID
	$iAccountID = mysql_insert_id();

	// Store 'NSI Base Setting' for a trial
	mysql_query("INSERT INTO ca.nsi_base_settings VALUES('', '" . $iAccountID . "', '" . $cst_id . "', 0, 3, 1)");

	// Send password
	if ( $iPinCode && $iAccountID )
	{
		mail($aSalesPerson['email'], 'Account Password', 'Username: ' . $_GET['nsi_account_username'] . "\n" . 'Pincode: ' . $iPinCode, 'From: <EMAIL>');
	}
}

// Open and Register a Surveillance Scanner Account
if ( $_GET['surveillance_scanner_account_open'] )
{
	// Set today+1 month expiry date
	$expire = date('Y-m-d', time()+604800);

	// Select SP data
	$sp_res = mysql_query("select * from salespeople where person_id = '" . $persondata[0] . "' limit 1");
	$sp_row = mysql_fetch_array($sp_res);

	// Save sale
	mysql_query("insert into saleslog (sold_date, expires_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id) values(now(), '" . $expire . "', '0', 'Surveillance Scanner Trial Account', 0, 5, 0, '" . $cst_id . "', '" . $persondata[0] . "', '10000', 0, '" . $crm_lang_id . "')");

	// Generate Activation string
	$password = GenerateRandomPassword(8);

	// Register account in Customer Area
	mysql_query("insert into ca.accounts (account_name, account_username, account_password, account_login_type, account_product_type, modules, show_modules, account_recv_all, account_expires, cst_id, account_gen_pwd, lang_id, ca_lang_id) values('" . $contact . "', '" . $_GET['surveillance_scanner_account_username'] . "', password('" . $password . "'), '1', 5, " . $aDefaultModuleMappings[5]['modules'] . ", " . $aDefaultModuleMappings[5]['show_modules'] . ", '1', '" . $expire . "', '" . $cst_id . "', 1, 1, 1)");

	// Send password
	if ( $password )
	{
		mail($sp_row['email'], 'Account Password', 'Username: ' . $_GET['surveillance_scanner_account_username'] . "\n" . 'Password: ' . $password, 'From: <EMAIL>');
	}
}

// Save sale
if ( $product_id )
{
	// Select product details
	$res = mysql_query("select * from products where product_id = '" . $product_id . "' limit 1");
	$row = mysql_fetch_array($res);
	
	if ( preg_match("[0-9]{4}.[0-9]{1,2}.[0-9]{1,2}", $input_expire) )
	{
		$expire = "'" . $input_expire . "'";
	}
	else
	{
		$expire = "date_add(now(), INTERVAL " . $row['period'] . " MONTH)";
	}

	// Save sale
	mysql_query("insert into saleslog (sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date) values(now(), " . $expire . ", '0', '" . $row['name'] . ', ' . $_GET['prod_currency'] . "', '" . $row['period'] . "', " . $_GET['prod_price'] . ", " . $row['product_type'] . ", " . $row['trial'] . ", '" . $cst_id . "', '" . $persondata[0] . "', '" . $row['product_id'] . "', '" . ( intval($input_payment) ? $input_payment : 8 ) . "', '" . $crm_lang_id . "', '" . $_GET['prod_currency'] . "', '" . $_GET['input_start'] . "')");

	// Check if they have an account
	$c_res = mysql_query("select * from ca.accounts where cst_id = '" . $cst_id . "'");

	// If there's no account add new else update expire date
	if ( mysql_num_rows($c_res) == 0 && $email )
	{
		// Password
		$password = GenerateRandomPassword(8);

		// Save password in temporary table ( for use in email )
		mysql_query("delete from one_time_passwords where cst_id = '" . $cst_id . "' && email = '" . $email . "'");
		mysql_query("insert into one_time_passwords (password, email, cst_id) values('" . $password . "', '" . $email . "', '" . $cst_id . "')");

		// Select SP data
		$sp_res = mysql_query("select * from salespeople where person_id = '" . $persondata[0] . "' limit 1");
		$sp_row = mysql_fetch_array($sp_res);

		// Send password
		if ( $password )
		{
			mail($sp_row['email'], 'Account Password', 'Username: ' . $email . "\n" . 'Password: ' . $password, 'From: <EMAIL>');
		}

		// Insert account
		mysql_query("insert into ca.accounts (account_name, account_username, account_password, account_login_type, account_product_type, modules, show_modules, account_recv_all, account_expires, cst_id, account_gen_pwd, lang_id, ca_lang_id) values('" . $contact . "', '" . $email . "', password('" . $password . "'), '1', '" . $row['product_type'] . "', " . $aDefaultModuleMappings[$row['product_type']]['modules'] . ", " . $aDefaultModuleMappings[$row['product_type']]['show_modules'] . ", '1', " . $expire . ", '" . $cst_id . "', 1, '" . $_GET['cst_vuln_lang_id'] . "', '" . $_GET['cst_lang_id'] . "')");
		$account_id = mysql_insert_id();

		// Insert 5 esm accounts - if esm
		if ( $row['product_type'] == 3 )
		{
			mysql_query("insert into ca.esm (no_users, master_account_id, cst_id) values(5, '" . intval($account_id) . "', '" . intval($cst_id) . "')");
		}
	}
	elseif ( mysql_num_rows($c_res) > 0 )
	{
		// Update all account expire dates
		mysql_query("update ca.accounts set account_expires = " . $expire . " where cst_id = '" . $cst_id . "'");
	}
}

// Save SSS Sale
if ( $_GET['sss_ip_addresses'] && $_GET['sss_expires'] )
{
	// Save sale
	mysql_query("insert into saleslog (sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date) values(now(), '" . $_GET['sss_expires'] . "', '0', '" . 'Secunia Surveillance Scanner, ' . $_GET['sss_ip_addresses'] . ' IP Addresses, ' . FrequencyName($_GET['sss_frequency']) . ' Scanning, ' . $_GET['sss_currency'] . "', '0', " . $_GET['sss_amount'] . ", 5, 0, '" . $cst_id . "', '" . $persondata[0] . "', 10001, '" . ( intval($sss_payment) ? $sss_payment : 8 ) . "', '" . $crm_lang_id . "', '" . $_GET['sss_currency'] . "', '" . $_GET['sss_start'] . "')");

	// Check if scan limits are already set-up, else insert them
	$res = mysql_query("select * from ca.accounts where cst_id = '" . $cst_id . "' && modules & " . MOD_VSS . " limit 1");
	if ( mysql_num_rows($res) )
	{
		// Seletc data
		$row = mysql_fetch_array($res);

		// Update account 'expires' date
		mysql_query("update ca.accounts set account_expires = '" . $_GET['sss_expires'] . "' where account_id = '" . $row['account_id'] . "' limit 1");

		// Update "Scan Limits"
		{
			// Delete
			mysql_query("delete from ca.vss_scan_limits where account_id = '" . $row['account_id'] . "'");

			// Insert
			mysql_query("insert into ca.vss_scan_limits (account_id, number, type) values('" . $row['account_id'] . "', '" . $_GET['sss_ip_addresses'] . "', '" . $_GET['sss_frequency'] . "')");
		}
	}
}

// NSI Sale registration
if ( $_GET['nsi_host_licenses'] && $_GET['nsi_expires'] )
{
	// Save sale
	mysql_query("INSERT INTO crm.saleslog (sold_date, expires_date, discount, product_name, product_period, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency, invoice_start_date) VALUES(NOW(), '" . $_GET['nsi_expires'] . "', '0', '" . 'Network Software Inspector, ' . $_GET['nsi_host_licenses'] . ' Host Licenses, ' . $_GET['nsi_currency'] . "', '0', " . $_GET['nsi_amount'] . ", 9, 0, '" . $cst_id . "', '" . $persondata[0] . "', 10002, '" . ( intval($nsi_payment) ? $nsi_payment : 8 ) . "', '" . $crm_lang_id . "', '" . $_GET['nsi_currency'] . "', '" . $_GET['nsi_start'] . "')");

	// Check if scan limits are already set-up, else insert them
	$rRes = mysql_query("SELECT * FROM ca.accounts WHERE cst_id = '" . $cst_id . "' && modules & " . MOD_NSI . " LIMIT 1");
	if ( mysql_num_rows($rRes) )
	{
		// Seletc data
		$aRow = mysql_fetch_array($rRes);

		// Update account 'expires' date
		mysql_query("UPDATE ca.accounts SET account_expires = '" . $_GET['nsi_expires'] . "' WHERE account_id = '" . $row['account_id'] . "' LIMIT 1");

		// Update "NSI Base Settings"
		{
			// Already inserted
			if ( mysql_num_rows(mysql_query("SELECT * FROM ca.nsi_base_settings WHERE account_id = '" . $row['account_id'] . "' LIMIT 1")) > 0 )
			{
				// Update
				mysql_query("UPDATE ca.nsi_base_settings SET s_trial = 0, s_unique_hosts = '" . $_GET['nsi_host_licenses'] . "' WHERE account_id = '" . $row['account_id'] . "'");
			}
			else
			{
				// Insert
				mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, s_unique_hosts, s_trial) VALUES('" . $row['account_id'] . "', '" . $cst_id . "', '" . $_GET['nsi_host_licenses'] . "', '0')");
			}
		}
	}
}

// NSD Sale registration
// nsd_currency / nsd_price / nsd_detections / nsd_time
if ( $_GET['nsd_detections'] )
{
	// Update CA: Detection limit (detect_limits{account_id, detect_limit})
	{
		// Select all CA accounts for this CST_ID
		$ca_res = mysql_query("select * from ca.accounts where cst_id = '" . $_GET['cst_id'] . "'");
		while ( $ca_row = mysql_fetch_array($ca_res) )
		{
			// Check if limit is already inserted for this account_id, else add
			$test_res = mysql_query("select * from ca.detect_limits where account_id = '" . $ca_row['account_id'] . "'");
			if ( mysql_num_rows($test_res) )
			{
				// Update
				mysql_query("update ca.detect_limits set detect_limit = '" . $_GET['nsd_detections'] . "' where account_id = '" . $ca_row['account_id'] . "' limit 1");
			}
			else
			{
				// Insert
				mysql_query("insert into ca.detect_limits (account_id, detect_limit) values('" . $ca_row['account_id'] . "', '" . $_GET['nsd_detections'] . "')");
			}
		}
	}

	// Insert into saleslog
	mysql_query("insert into saleslog (sold_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, currency) values(now(), '0', '" . 'Network Software Detector, ' . $_GET['nsd_detections'] . ' Detections, ' . $_GET['nsd_currency'] . "', " . $_GET['nsd_price'] . ", 6, 0, '" . $cst_id . "', '" . $persondata[0] . "', 11000, '" . ( $nsd_time ? intval($nsd_time) : 8 ) . "', '" . $crm_lang_id . "', '" . $_GET['nsd_currency'] . "')");
}


// Add-on save registration
if ( $addon_prod > 0 && $addon_units )
{
	// Generate Product Name
	{
		// Product
		if ( $addon_prod == 2 )
		{
			$product = 'Extra Virus Accounts, ' . $addon_units . ' Accounts, ' . $addon_currency;

			// Insert to CA
			$res = mysql_query("select * from ca.extra_virus where cst_id = '" . $cst_id . "'");
			if ( !mysql_num_rows($res) )
			{
				mysql_query("insert into ca.extra_virus (cst_id, number) values('" . $cst_id . "', '" . $addon_units . "')");
			}
			else
			{
				mysql_query("update ca.extra_virus set number = (number + '" . $addon_units . "') where cst_id = '" . $cst_id . "'");
			}
		}
		elseif ( $addon_prod == 3 )
		{
			$product = 'Extra Contact Profiles, ' . $addon_units . ' Contacts, ' . $addon_currency;

			// Select account ID from CA
			$res = mysql_query("select * from ca.accounts where cst_id = '" . $cst_id . "' && !(modules & " . MOD_ESM . ") limit 1");
			$acc_row = mysql_fetch_array($res);

			// Check if update or insert
			$res = mysql_query("select * from ca.extra_contact where account_id = '" . $acc_row['account_id'] . "'");
			if ( !mysql_num_rows($res) )
			{
				mysql_query("insert into ca.extra_contact (account_id, number, type) values('" . $acc_row['account_id'] . "', '" . $addon_units . "', 'adv')");
			}
			else
			{
				mysql_query("update ca.extra_contact set number = (number + '" . $addon_units . "') where account_id = '" . $acc_row['account_id'] . "'");
			}
		}
	}

	// Save Sale
	mysql_query("insert into saleslog (sold_date, discount, product_name, product_price, cst_id, person_id, payment_time, lang_id, currency) values(now(), 0, '" . $product . "', " . ( $addon_price * $addon_units ) . ", '" . $cst_id . "', '" . $persondata[0] . "', '" . ( intval($addon_payment) ? $addon_payment : 8 ) . "', '" . $crm_lang_id . "', '" . $addon_currency . "')");
}

// Redirect
if ( $_GET['submit_newrand'] )
{
	header("Location: ../customer_select.php?segment_id=" . $_GET['segment_id']);
}
else
{
	header("Location: ../customer.php?cst_id=" . $cst_id);
}
?>
