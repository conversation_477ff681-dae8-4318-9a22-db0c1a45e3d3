<?php
// Require functions
require("../includes/functions.php");

// Open database connection
OpenDatabase();

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<br>
<table width="600" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline" colspan="2">Internal Contact List</td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2"><br></td>
	</tr>
	<?php
	$res = mysql_query("select * from salespeople where display = 1 order by name");
	while ( $row = mysql_fetch_array($res) )
	{
	?>
	<tr height="15">
		<td width="50"><?=$row['local_number']?></td>
		<td width="200"><?=$row['name']?></td>
		<td width="350"><?=$row['email']?></td>
	</tr>
	<?php
	}
	?>
	<tr>
		<td class="MenuHeadline" colspan="2"><br></td>
	</tr>
	<tr>
		<td class="MenuHeadline" colspan="2"><br><br></td>
	</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
