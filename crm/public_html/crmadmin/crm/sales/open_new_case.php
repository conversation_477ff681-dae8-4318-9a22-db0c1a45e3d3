<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

$res = mysql_query("select * from cst where cst_id = '" . $_GET['cst_id'] . "' limit 1");
$row = mysql_fetch_array($res);

// Save Case?
if ( $_GET['case_name'] )
{
	mysql_query("insert into cst (name, phone, fax, contact, email, web, company_number, field_1, field_2, field_3, field_4, field_5, field_6, field_7, field_8, field_9, field_10, field_11, field_12, field_13, field_14, field_15, person_id, lang_id, vuln_lang_id, crm_lang_id, phone_2 ,contact_2 ,email_2, phone_3, contact_3, email_3, case_status, case_name, case_opened, master_id, segment_id, customer_marked_dead) values('" . mysql_escape_string($row['name']) . "', '" . mysql_escape_string($row['phone']) . "', '" . mysql_escape_string($row['fax']) . "', '" . mysql_escape_string($row['contact']) . "', '" . mysql_escape_string($row['email']) . "', '" . mysql_escape_string($row['web']) . "', '" . mysql_escape_string($row['company_number']) . "', '" . mysql_escape_string($row['field_1']) . "', '" . mysql_escape_string($row['field_2']) . "', '" . mysql_escape_string($row['field_3']) . "', '" . mysql_escape_string($row['field_4']) . "', '" . mysql_escape_string($row['field_5']) . "', '" . mysql_escape_string($row['field_6']) . "', '" . mysql_escape_string($row['field_7']) . "', '" . mysql_escape_string($row['field_8']) . "', '" . mysql_escape_string($row['field_9']) . "', '" . mysql_escape_string($row['field_10']) . "', '" . mysql_escape_string($row['field_11']) . "', '" . mysql_escape_string($row['field_12']) . "', '" . mysql_escape_string($row['field_13']) . "', '" . mysql_escape_string($row['field_14']) . "', '" . mysql_escape_string($row['field_15']) . "', '" . $persondata[0] . "', '" . mysql_escape_string($row['lang_id']) . "', '" . mysql_escape_string($row['vuln_lang_id']) . "', '" . mysql_escape_string($row['crm_lang_id']) . "', '" . mysql_escape_string($row['phone_2']) . "', '" . mysql_escape_string($row['contact_2']) . "', '" . mysql_escape_string($row['email_2']) . "', '" . mysql_escape_string($row['phone_3']) . "', '" . mysql_escape_string($row['contact_3']) . "', '" . mysql_escape_string($row['email_3']) . "', 1, '" . $_GET['case_name'] . "', now(), '" . ( $row['master_id'] ? $row['master_id'] : $row['cst_id'] ) . "', '" . mysql_escape_string($row['segment_id']) . "', 0)");

	// Get ID
	$cst_id = mysql_insert_id();

	// Go back to new Customer
	header("Location: customer.php?cst_id=" . $cst_id);
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>

<br>
<form method="GET" action="open_new_case.php">
<input type="hidden" name="cst_id" value="<?=htmlspecialchars( ( $row['master_id'] ? $row['master_id'] : $row['cst_id'] ) )?>">
<table width="50%" cellpadding="0" cellspacing="0">
	<tr>
		<td><b>Open New Case</b></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td width="30%">Case Name</td>
		<td width="70%"><input type="text" name="case_name"></td>
	</tr>
	<tr>
		<td><br></td>
	</tr>
	<tr>
		<td></td>
		<td><input type="submit" value="Open Case" class="submit"></td>
	</tr>
</table>
</form>

<?php
// Output footer
echo HTMLFooter();
?>
