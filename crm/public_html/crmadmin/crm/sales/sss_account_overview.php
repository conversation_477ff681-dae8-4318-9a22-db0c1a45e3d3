<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

if ( $persondata[5] != 5 && $persondata[0] != 2 )
{
	exit();
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');

// Generate output
{
$res = mysql_query("select * from cst, ca.accounts where person_id = '" . $persondata[0] . "' && cst.cst_id = accounts.cst_id && accounts.account_product_type = 5");
while ( $row = mysql_fetch_array($res) )
{
	// Determine how many hosts have been set-up
	$counted = 0;
	$c_res = mysql_query("select * from ca.vss_scan_profiles where account_id = '" . $row['account_id'] . "'");
	while ( $c_row = mysql_fetch_array($c_res) )
	{
		$counted += count(returnVSSTargetsArray($c_row['targets']));
	}

	// Credits used
	$c_res = mysql_query("select id from ca.vss_nessus_reports_raw where account_id = '" . $row['account_id'] . "'");
	$credits = @mysql_num_rows($c_res);

	// Which var to dump data to
	if ( $counted < 3 )
	{
		$var = 'output_0_2';
	}
	elseif ( $counted < 6 )
	{
		$var = 'output_3_5';
	}
	elseif ( $counted < 11 )
	{
		$var = 'output_6_10';
	}
	else
	{
		$var = 'output_max';
	}

	// Build data output
	$$var .= '<tr>
		<td style="padding-left: 15px;"><a href="customer.php?cst_id=' . $row['cst_id'] . '">' . htmlspecialchars($row['name']) . '</a>
		</td>
		<td>' . htmlspecialchars($row['account_username']) . '
		</td>
		<td>' . htmlspecialchars($row['last_login']) . '
		</td>
		<td>' . number_format($counted, 0) . '
		</td>
		<td>' . number_format($credits, 0) . '
		</td>
	</tr>';
}

}
?>
<br>
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td class="MenuHeadline">
			Customer Account Overview - Secunia Surveillance Scanner
		</td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline" style="padding-left: 15px;">Company
		</td>
		<td class="MenuHeadline">Username
		</td>
		<td class="MenuHeadline">Last Login
		</td>
		<td class="MenuHeadline">Hosts Setup
		</td>
		<td class="MenuHeadline">Credits Used
		</td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			0 - 2 Hosts
		</td>
	</tr>
	<?=$output_0_2?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			3 - 5 Hosts
		</td>
	</tr>
	<?=$output_3_5?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			6 - 10 Hosts
		</td>
	</tr>
	<?=$output_6_10?>
	<tr><td><br></td></tr>
	<tr>
		<td class="MenuHeadline">
			> 10 Hosts
		</td>
	</tr>
	<?=$output_max?>
	<tr><td><br><br><br></td></tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
