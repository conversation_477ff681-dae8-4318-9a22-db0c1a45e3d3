<?php
// Initialise (function libraries and openening DB connection)
require('/home/<USER>/public_html/crmadmin/crm/includes/initialise.php');

// Get data for salesperson
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));
$person_id = $persondata[0];

// No injection
$good = '';
$good_total = '';
$payed_total = '';
$canvas_revenue = '';
$resale_revenue = '';
$trials = '';
$bad = '';
$bad_total = '';
$good_output = '';
$t_good_output = '';
$bad_output = '';

// Select sales from period
if ( $from && $to )
{
	// Select all from saleslog
	$res = mysql_query("select * from saleslog where ((sold_date >= '" . $from . "' && sold_date <= '" . $to . "') || (status = 3 && (status_date >= '" . $from . "' && status_date <= '" . $to . "'))) " . ( $person_id ? " && person_id = '" . $person_id . "'" : '' ) . ( $_GET['this_lang_id'] ? " && lang_id = '" . $_GET['this_lang_id'] . "'" : '' ) . " order by sold_date");

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		// Select other data about customer
		$x_res = mysql_query("select * from cst where cst_id = '" . $row['cst_id'] . "' limit 1");
		$x_row = mysql_fetch_array($x_res);
		
		// Canvas or Recurrence
		{
			$sale_type = '';
			$t_res = mysql_query("select * from saleslog where sold_date < '" . $row['sold_date'] . "' && cst_id = '" . $row['cst_id'] . "' && (product_price - discount) > 0 && status = 2");
			if ( mysql_num_rows($t_res) == 0 && $row['status'] != 3 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$canvas_revenue += ReturnEuro(($row['product_price'] - $row['discount']), $row['product_name']);
					$sale_type = 'Canv.';
				}
			}
			elseif ( mysql_num_rows($t_res) > 0 && $row['status'] != 3 )
			{
				if ( ($row['product_price'] - $row['discount']) > 0 )
				{
					$resale_revenue += ReturnEuro(($row['product_price'] - $row['discount']), $row['product_name']);
					$sale_type = 'Recu.';
				}
			}
		}

		// Check if been cancelled or is good
		if ( $row['status'] != 3 )
		{
			// Output
			if ( ($row['product_price'] - $row['discount']) )
			{
				$good_output .= '<tr><td><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
				$good_output .= '<td>' . $row['product_name'] . ' (' . number_format( ($row['product_price'] - $row['discount']), 2) . ')</td>';
				$good_output .= '<td>' . number_format( ReturnEuro($row['product_price'] - $row['discount'], $row['product_name']), 2) . ' (' . $sale_type . ')</td>';
				$good_output .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
				$good_output .= '<td>' . ReturnSaleStatus($row['status']) . ( $row['status'] == 2 ? ' (' . $row['status_date'] . ')' : '' ) . '</td>';
				$good_output .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';
			}
			else
			{
				$t_good_output .= '<tr><td><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
				$t_good_output .= '<td>' . $row['product_name'] . '</td>';
				$t_good_output .= '<td>' . number_format( ReturnEuro($row['product_price'] - $row['discount'], $row['product_name']), 2) . '</td>';
				$t_good_output .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
				$t_good_output .= '<td>' . ( time() > strtotime($row['expires_date']) ? 'Expired' : 'Active' ) . '</td>';
				$t_good_output .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';
			}

			// Sold total
			$good_total += ReturnEuro($row['product_price'] - $row['discount'], $row['product_name']);

			// Totalt payed revenue
			if ( $row['status'] == 2 )
			{
				$payed_total += ReturnEuro(($row['product_price'] - $row['discount']), $row['product_name']);
				$payed++;
			}

			// Unit counter
			if ( ($row['product_price'] - $row['discount']) )
			{
				$good++;
			}
			else
			{
				$trials++;
			}
		}
		else
		{
			// Output
			$bad_output .= '<tr><td><a href="../sales/customer.php?cst_id=' . $row['cst_id'] . '" target="_blank">' . $x_row['name'] . '</a></td>';
			$bad_output .= '<td>' . $row['product_name'] . ' (' . number_format( ($row['product_price'] - $row['discount']), 2) . ')</td>';
			$bad_output .= '<td>' . number_format( ReturnEuro($row['product_price'] - $row['discount'], $row['product_name']), 2) . '</td>';
			$bad_output .= '<td>' . ReturnMonths($row['sold_date'], $row['expires_date']) . ' months</td>';
			$bad_output .= '<td>' . ReturnSaleStatus($row['status']) . '</td>';
			$bad_output .= '<td>' . ( $row['commision_amount'] ? 'Yes' : 'No' ) . '</td></tr>';

			// Sold total
			$bad_total += ReturnEuro($row['product_price'] - $row['discount'], $row['product_name']);

			// Unit counter
			$bad++;
		}
	}

	// Totals
	$good_output .= '<tr><td colspan="6"><br></td></tr>';
	$good_output .= '<tr><td colspan="1"><b>Total revenue</b></td><td><b>' . $good . '</b></td><td colspan="3"><b>' . number_format( $good_total, 2) . '</b></td></tr>';
	$good_output .= '<tr><td colspan="6"><br></td></tr>';
	$good_output .= '<tr><td colspan="1"><b>Total payed revenue</b></td><td><b>' . $payed . '</b></td><td colspan="3"><b>' . number_format( $payed_total, 2) . '</b></td></tr>';

	$t_good_output .= '<tr><td colspan="6"><br></td></tr>';
	$t_good_output .= '<tr><td colspan="1"><b>Total</b></td><td><b>' . $trials . '</b></td><td colspan="3"></td></tr>';

	$bad_output .= '<tr><td colspan="6"><br></td></tr>';
	$bad_output .= '<tr><td colspan="1"><b>Total revenue</b></td><td><b>' . $bad . '</b></td><td colspan="3"><b>' . number_format( $bad_total, 2) . '</b></td></tr>';
}
else
{
	$from = ConvertGetDate('', 'MySQL-Date');
	$to = ConvertGetDate('', 'MySQL-Date');
}

// Output HTML header
echo HTMLHeader('Secunia CRM - Sales', '/crmadmin/crm/sales/index.php');
?>
<table width="100%" cellpadding="0" cellspacing="0">
<tr>
<td class="MenuHeadline" colspan="6" width="100%">
Sale Report
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<form method="GET" action="sale_report.php">
<tr>
<td colspan="1">
<b>Language</b><br>
<br>
<select name="this_lang_id">
<option value="0" <?=( !$_GET['this_lang_id'] ? "selected" : "" )?>>Show All</option>
<?php
$l_res = mysql_query("select * from vuln_track.language order by lang_id");
while ( $l_row = mysql_fetch_array($l_res) )
{
?>
	<option value="<?=$l_row['lang_id']?>" <?=( $_GET['this_lang_id'] == $l_row['lang_id'] ? "selected" : "" )?>><?=$l_row['lang_name']?></option>
<?php
}
?>
</select>
</td>
<td colspan="2">
<b>Select period to display</b><br>
<input type="text" value="<?=htmlspecialchars($from)?>" name="from" style="width: 150px;"> - <input type="text" value="<?=htmlspecialchars($to)?>" name="to" style="width: 150px;"><br>
</td>
<td colspan="3">
</td>
</tr>

<tr>
<td colspan="6" width="100%"><br></td>
</tr>

<tr>
<td colspan="6" width="100%"><input type="submit" value="Display"></td>
</tr>

</form>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>

<tr>
<td colspan="6" width="100%">
<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<b>Short Report</b>
</td>
</tr>

<tr>
<td colspan="6" width="100%"><br></td>
</tr>

<tr>
<td colspan="1">
No. of Customers:<br>
<br>
Total Revenue:<br>
Total Revenue Paid:<br>
<br>
Total Revenue Canvas:<br>
Total Revenue Recurrence:<br>
<br>
No. of Free Trials:<br>
<br>
No. of Cancellations:<br>
Total Revenue Cancelled:<br>
</td>
<td colspan="4" valign="top">
<?=$good?><br>
<br>
<?=number_format( ($good_total ? $good_total : 0), 2)?><br>
<?=number_format( ($payed_total ? $payed_total : 0), 2)?><br>
<br>
<?=number_format( ($canvas_revenue ? $canvas_revenue : 0), 2)?><br>
<?=number_format( ($resale_revenue ? $resale_revenue : 0), 2)?><br>
<br>
<?=$trials?><br>
<br>
<?=($bad ? $bad : 0)?><br>
<?=number_format( ($bad_total ? $bad_total : 0), 2)?><br>
</td>
</tr>

<tr>
<td colspan="6" width="100%"><br></td>
</tr>

<tr>
<td colspan="6" width="100%">
<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<b>Sales</b>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>

<tr>
<td width="22%"><b>Company</b></td>
<td width="32%"><b>Product</b></td>
<td width="10%"><b>Amount</b></td>
<td width="20%"><b>Period</b></td>
<td width="10%"><b>Status</b></td>
<td width="6%"><b>Commi.</b></td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<?=$good_output?>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr><tr>
<td colspan="6" width="100%">
<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<b>Free Trials</b>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td width="22%"><b>Company</b></td>
<td width="32%"><b>Product</b></td>
<td width="10%"><b>Amount</b></td>
<td width="20%"><b>Period</b></td>
<td width="10%"><b>Status</b></td>
<td width="6%"><b>Commi.</b></td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<?=$t_good_output?>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<img src="../sales/gfx/orangebottom.gif" width="100%" height="4">
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<b>Cancelled Sales</b>
</td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<tr>
<td width="22%"><b>Company</b></td>
<td width="32%"><b>Product</b></td>
<td width="10%"><b>Amount</b></td>
<td width="20%"><b>Period</b></td>
<td width="10%"><b>Status</b></td>
<td width="6%"><b>Commi.</b></td>
</tr>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
<?=$bad_output?>
<tr>
<td colspan="6" width="100%">
<br>
</td>
</tr>
</table>

<?php
// Output footer
echo HTMLFooter();
?>
