<?php
// Function for displaying mail templates
function DisplayMailTemplates()
{
	// Select all mail templates
	$res = mysql_query("select * from mail_templates where prod_limit = '5' order by title");

	// Start output
	$output = '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Mail Templates</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">';

	// Generate list of templates
	while ( $row = mysql_fetch_array($res) )
	{
		$output .= '<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="send_mail_template.php?id=' . $row['id'] . '&amp;cst_id=' . intval($_GET['cst_id']) . '"><b>' . htmlspecialchars($row['title']) . '</b></a></td>
		</tr>';
	}

	// End output
	$output .= '</table></fieldset><br>';

	return $output;
}

// Function for displaying customerdata
function ShowCustomerData($cst_id, $cst_lang_id, $persondata)
{
	// MAke email global
	global $email;

	// Select customer data, if any
	if ( $cst_id != 'new' )
	{
		// Select data
		$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "'");
		$row = mysql_fetch_array($res);
	}

	// Get data template for this lang_id
	if ( $row['crm_lang_id'] )
	{
		require('../includes/customer_temp_' . $row['crm_lang_id'] . '.php');
	}
	else
	{
		require('../includes/customer_temp_' . $cst_lang_id . '.php');
	}

	// Set email
	$email = $row['email'];

	// Start output - standard fields
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Customer Data</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td colspan="2" style="padding-left: 10px;"><b>1. Contact Person</b></td>
		</tr>
		<tr>
			<td width="30%" style="padding-left: 10px;">Type:</td>
			<td width="70%">
				<select name="contact_type_1">
					<option value="N/A"' . ( $row['contact_type_1'] == 'N/A' ? ' selected' : '' ) . '>N/A</option>
					<option value="Financial"' . ( $row['contact_type_1'] == 'Financial' ? ' selected' : '' ) . '>Financial</option>
					<option value="Techie"' . ( $row['contact_type_1'] == 'Techie' ? ' selected' : '' ) . '>Techie</option>
				</select>
			</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;">Main / Dir. / Mob.:</td>
			<td><input type="text" name="phone" value="' . htmlspecialchars($row['phone']) . '" size="45"></td>
		</tr>
		<tr>
			<td style="padding-left: 10px;">Contact person, title:</td>
			<td><input type="text" name="contact" value="' . htmlspecialchars($row['contact']) . '" size="45"></td>
		</tr>
		<tr>
			<td style="padding-left: 10px;">Email:</td>
			<td><input type="text" name="email" value="' . htmlspecialchars($row['email']) . '" size="45"></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'contact_2\')"><b>2. Contact Person</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="contact_2">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Type:</td>
							<td width="70%">
								<select name="contact_type_2">
									<option value="N/A"' . ( $row['contact_type_2'] == 'N/A' ? ' selected' : '' ) . '>N/A</option>
									<option value="Financial"' . ( $row['contact_type_2'] == 'Financial' ? ' selected' : '' ) . '>Financial</option>
									<option value="Techie"' . ( $row['contact_type_2'] == 'Techie' ? ' selected' : '' ) . '>Techie</option>
								</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Main / Dir. / Mob.:</td>
							<td width="70%"><input type="text" name="phone_2" value="' . htmlspecialchars($row['phone_2']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Contact person, title:</td>
							<td><input type="text" name="contact_2" value="' . htmlspecialchars($row['contact_2']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Email:</td>
							<td><input type="text" name="email_2" value="' . htmlspecialchars($row['email_2']) . '" size="45"></td>
						</tr>
						<tr>
							<td><br></td>
						</tr>
					</table>
				</div>
				' . ( !$row['phone_2'] && !$row['contact_2'] && !$row['email_2'] ? '<script>ToggleDiv(\'contact_2\');</script>' : '' ) . '
			</td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'contact_3\')"><b>3. Contact Person</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="contact_3">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Type:</td>
							<td width="70%">
								<select name="contact_type_3">
									<option value="N/A"' . ( $row['contact_type_3'] == 'N/A' ? ' selected' : '' ) . '>N/A</option>
									<option value="Financial"' . ( $row['contact_type_3'] == 'Financial' ? ' selected' : '' ) . '>Financial</option>
									<option value="Techie"' . ( $row['contact_type_3'] == 'Techie' ? ' selected' : '' ) . '>Techie</option>
								</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Main / Dir. / Mob.:</td>
							<td width="70%"><input type="text" name="phone_3" value="' . htmlspecialchars($row['phone_3']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Contact person, title:</td>
							<td><input type="text" name="contact_3" value="' . htmlspecialchars($row['contact_3']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Email:</td>
							<td><input type="text" name="email_3" value="' . htmlspecialchars($row['email_3']) . '" size="45"></td>
						</tr>
						<tr>
							<td><br></td>
						</tr>
					</table>
				</div>
				' . ( !$row['phone_3'] && !$row['contact_3'] && !$row['email_3'] ? '<script>ToggleDiv(\'contact_3\');</script>' : '' ) . '
			</td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'invoice_address\')"><b>Invoice Address / Other Details</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="invoice_address">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Company name:</td>
							<td width="70%"><input type="text" name="name" value="' . htmlspecialchars($row['name']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Company number:</td>
							<td><input type="text" name="company_number" value="' . htmlspecialchars($row['company_number']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">PO number:</td>
							<td><input type="text" name="po_number" value="' . htmlspecialchars($row['po_number']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;" width="30%">Fax:</td>
							<td width="70%"><input type="text" name="fax" value="' . htmlspecialchars($row['fax']) . '" size="45"></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Web:</td>
							<td ><input type="text" name="web" value="' . htmlspecialchars($row['web']) . '" size="45"></td>
						</tr>';

						// Randow fields - language defined fields
						while ( list($name, $field) = each($customer_data) )
						{
							// Check if set
							if ( $name )
							{
								// Output
								$output .= '<tr>';
								$output .= '<td style="padding-left: 10px;">' . $name . ':</td>';
								$output .= '<td><input type="text" name="' . $field . '" value="' . htmlspecialchars($row[$field]) . '" size="45"></td>';
								$output .= '</tr>';
							}
						}

						$output .= '
						<tr>
							<td style="padding-left: 10px;">Invoice Country:</td>
							<td><select name="invoice_country" style="width: 100%"><option>- Please Select Invoice Country -</option>';

						// Build list of countries
						$c_res = mysql_query("select * from countries order by country");
						while ( $c_row = mysql_fetch_array($c_res) )
						{
							$output .= '<option value="' . $c_row['id'] . '"' . ( $row['invoice_country'] == $c_row['id'] ? ' selected' : '' ) . '>' . htmlspecialchars($c_row['country']) . '</option>';
						}

						$output .= '</select></td>
						</tr>
						<tr>
							<td style="padding-left: 10px;">Segment:</td>
							<td>' . ReturnSegmentName($row['segment_id']) . '</td>
						</tr>
						<tr>
							<td><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'invoice_address\');</script>
			</td>
		</tr>';

	//Spacer
	$output .= '<tr>
					<td colspan="2" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'language_details\')"><b>Language Details</b></a></td>
				</tr>
				<tr>
					<td colspan="2">
						<div id="language_details">
							<table width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2" style="padding-left: 10px;"><b>CRM:</b></td>
								</tr>';

	// Customer country
	{
		// Select all country options
		$co_res = mysql_query("select * from vuln_track.language order by lang_id");
		while (  $co_row = mysql_fetch_array($co_res) )
		{
			$crm_options .= '<option value="' . $co_row['lang_id'] . '" ' . ( $co_row['lang_id'] == ($row['crm_lang_id'] ? $row['crm_lang_id'] : $cst_lang_id ) ? 'selected' : '' ) . '>' . $co_row['lang_name'] . '</option>';
		}
	
		// Output
		$output .= '<tr>';
		$output .= '<td width="30%" valign="top" style="padding-left: 10px;">Language</td>';
		$output .= '<td width="70%"><select name="crm_lang_id">' . $crm_options . '</select><br>This "Language" setting will only be used within the CRM system. It controls the contact data fields above and the "Language" setting that will be used to select customers in e.g. "Appointment list".</td>';
		$output .= '</tr>';
	}

	//Spacer
	$output .= '<tr>';
	$output .= '<td colspan="2" style="padding-left: 10px;"><br><b>Customer Area:</b></td>';
	$output .= '</tr>';

	// Country
	{
		// Options
		$l_options = '<option value="1" ' . ( $cst_lang_id == 1 ? 'selected' : '' ) .'>English</option>';
		$l_options .= '<option value="2" ' . ( $cst_lang_id == 2 ? 'selected' : '' ) .'>Danish</option>';
		$l_options .= '<option value="4" ' . ( $cst_lang_id == 4 ? 'selected' : '' ) .'>Norwegian</option>';

		// Output
		$output .= '	<tr>
							<td width="25%" style="padding-left: 10px;">Interface</td>
							<td width="75%"><select name="cst_lang_id">' . $l_options . '</select></td>
						</tr>';
	}

	// Vulnerabilities language
	{
		// If no vuln language selected
		if ( !intval($row['vuln_lang_id']) )
		{
			$vuln_lang_id = $persondata[4];
		}
		else
		{
			$vuln_lang_id = $row['vuln_lang_id'];
		}

		// Options
		$v_options = '<option value="1" ' . ( $vuln_lang_id == 1 ? 'selected' : '' ) . '>English</option>';
		$v_options .= '<option value="2" ' . ( $vuln_lang_id == 2 ? 'selected' : '' ) . '>Danish</option>';

		// Output
		$output .= '<tr>';
		$output .= '<td width="30%" valign="top" style="padding-left: 10px;">Advisories</td>';
		$output .= '<td width="70%"><select name="cst_vuln_lang_id">' . $v_options . '</select><br><b>Beware!</b> When changing these it will immediately affect the account registered under the address present in the "Email" field above.</td>';
		$output .= '</tr>';
	}
	$output .= '				</table>
				</div>
				<script>ToggleDiv(\'language_details\');</script>
			</td>
		</tr>';


	// End table
	$output .= '</table></fieldset><br>';

	// Return
	return $output;
}

// Function for showing sold products
function ShowSoldProducts($cst_id)
{
	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Sold Products</font></b></legend>';
	$output .= '<table width="100%" cellpadding="0" cellspacing="0">';

	// Select all sold prod
	$res = mysql_query("select * from saleslog where cst_id = '" . $cst_id . "'");

	// Check if sold any
	if ( mysql_num_rows($res) )
	{
		// Loop over result
		while ( $row = mysql_fetch_array($res) )
		{
			// Start output
			$output .= '
						<tr>
							<td colspan="2" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'prod_' . $row['sale_id'] . '\')"><b>' . $row['product_name'] . ' sold by ' . ReturnPersonNameFromID($row['person_id']) . '</b></a></td>
						</tr>
						<tr>
							<td colspan="2">
								<div id="prod_' . $row['sale_id'] . '">
									<table width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td width="30%" style="padding-left: 10px;">Status</td>
											<td width="70%">' . ReturnSaleStatus($row[status]) . '</td>
										</tr>
										<tr>
											<td style="padding-left: 10px;">Payment time</td>
											<td>' . $row['payment_time'] . ' days</td>
										</tr>
										<tr>
											<td style="padding-left: 10px;">Period</td>
											<td>' . $row['sold_date'] . ' - ' . $row['expires_date'] . '</td>
										</tr>
										<tr>
											<td colspan="2"><br></td>
										</tr>
										<tr>
											<td style="padding-left: 10px;">Price</td>
											<td>' . $row['product_price'] . ',-</td>
										</tr>
										<tr>
											<td style="padding-left: 10px;">Discount</td>
											<td>' . $row['discount'] . ',-</td>
										</tr>
										<tr>
											<td style="padding-left: 10px;"><b>Total</b></td>
											<td><b>' . ($row['product_price'] - $row['discount']) . ',-</b></td>
										</tr>
										<tr>
											<td colspan="2"><br></td>
										</tr>
									</table>
								</div>
								<script>ToggleDiv(\'prod_' . $row['sale_id'] . '\');</script>
							</td>
						</tr>';
		}
	}
	else
	{
		$output .= '<tr>
			<td colspan="2" style="padding-left: 10px;"><i>N/A</i></td>
		</tr>';
	}

	// End table
	$output .= '</table></fieldset>';

	// Return
	return $output;
}

// Function for showing Offer Sent
function ShowOffersSent($cst_lang_id)
{
	// Active: Select all offers sent to this customer
	$res = mysql_query("select * from offers where cst_id = '" . $_GET['cst_id'] . "' && expires >= now() order by expires");
	while ( $row = mysql_fetch_array($res) )
	{
		$active .= '<tr>
			<td style="padding-left: 10px;">' . $row['product'] . ', ' . htmlentities($row['currency']) . $row['amount'] . ', ' . $row['expires'] . '</td>
		</tr>';
	}

	// Expired: Select all offers sent to this customer
	$res = mysql_query("select * from offers where cst_id = '" . $_GET['cst_id'] . "' && expires < now() order by expires");
	while ( $row = mysql_fetch_array($res) )
	{
		$expired .= '<tr>
			<td style="padding-left: 10px;">' . $row['product'] . ', ' . htmlentities($row['currency']) . $row['amount'] . ', ' . $row['expires'] . '</td>
		</tr>';
	}

	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Offers Sent</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td style="padding-left: 10px;"><b>Active Offers</b></td>
		</tr>
		' . ( $active ? $active : '<tr><td style="padding-left: 10px;"><i>N/A</i></td></tr>' ) . '
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><b>Expired Offers</b></td>
		</tr>
		' . ( $expired ? $expired : '<tr><td style="padding-left: 10px;"><i>N/A</i></td></tr>' ) . '
	</table>
	</fieldset>
	<br>';

	// Return output
	return $output;
}

// Function for showing Product Actions
function ShowProductActions($cst_lang_id)
{
	// Select prodcts
	$res = mysql_query("select * from products where lang_id = 1 order by name");
	while ( $row = mysql_fetch_array($res) )
	{
		$product_options .= '<option value="' . $row['product_id'] . '">' . $row['name'] . '</option>';
	}

	// Select details required for the opening of a surveillance scanner account
	{
		// This user is allowed to open an Surveillance Scanner Account
		$sss = TRUE;

		// Select Customer details
		$res = mysql_query("select * from cst where cst_id = '" . $_GET['cst_id'] . "' limit 1");
		$row = mysql_fetch_array($res);

		// Check if username is already used or cst_id already have an account registered in Customer Area 
		if ( $row['email'] )
		{
			// Cst ID
			$ca_res = mysql_query("select * from ca.accounts where cst_id = '" . $_GET['cst_id'] . "' limit 1");
			if ( mysql_num_rows($ca_res) )
			{
				$ca_row = mysql_fetch_array($ca_res);
				$username_taken = 'Not possible. This Customer ID is already used in the customer area.';
			}

			// Suggest Username?
			if ( $row['email'] )
			{
				$ca_res = mysql_query("select * from ca.accounts where account_username = '" . $row['email'] . "' limit 1");
				if ( mysql_num_rows($ca_res) )
				{
					$used_suggest_username = TRUE;
				}
			}
		}
	}

	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Products Actions</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td width="100%" style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'new_product\');"><b>Sell Standard Product</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="new_product">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Product</td>
							<td width="70%"><select name="product_id"><option value="0">Choose Product</option>
							' . $product_options . '
							</select></td></tr>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Currency</td>
							<td width="70%">
							<select name="prod_currency">
								<option value="CAD">CAD</option>
								<option value="CHF">CHF</option>
								<option value="DKK">DK</option>
								<option value="EURO">EURO</option>
								<option value="NOK">NO</option>
								<option value="SEK">SEK</option>
								<option value="GBP">GBP (UK)</option>
								<option value="USD">USD</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Price</td>
							<td width="70%"><input type="text" name="prod_price" value="0"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Period Start (Invoice):</td>
							<td width="70%"><input type="text" name="input_start" value="' . date('Y-m-d') . '"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Expires (YYYY-MM-DD)</td>
							<td width="70%"><input type="text" name="input_expire" value=""></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Payment Time</td>
							<td width="70%"><input type="text" name="input_payment" value="8"></td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'new_product\');</script>
			</td>
		</tr>

		<tr>
			<td style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'new_special_product\');"><b>Sell Special Add-on Product</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="new_special_product">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Add-on Product:</td>
							<td width="70%">
								<select name="addon_prod">
									<option value="0">Choose Add-on Product</option>
									<option value="1">Extra Scans</option>
									<option value="2">Extra Virus Account</option>
									<option value="3">Extra Contact Profile</option>
								</select>
							</td></tr>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Currency</td>
							<td width="70%">
							<select name="addon_currency">
								<option value="CAD">CAD</option>
                                                                <option value="CHF">CHF</option>
								<option value="DKK">DK</option>
								<option value="EURO">EURO</option>
								<option value="NOK">NO</option>
								<option value="SEK">SEK</option>
								<option value="GBP">GBP (UK)</option>
								<option value="USD">USD</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Price pr. unit:</td>
							<td width="70%"><input type="text" name="addon_price"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Number of Units</td>
							<td width="70%"><input type="text" name="addon_units"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Payment Time</td>
							<td width="70%"><input type="text" name="addon_time"></td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'new_special_product\');</script>
			</td>
		</tr>

		<tr>
			<td style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'new_nsd_sale\');"><b>Sell NSD Detections</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="new_nsd_sale">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Currency</td>
							<td width="70%">
							<select name="nsd_currency">
								<option value="CAD">CAD</option>
                                                                <option value="CHF">CHF</option>
								<option value="DKK">DK</option>
								<option value="EURO">EURO</option>
								<option value="NOK">NO</option>
								<option value="SEK">SEK</option>
								<option value="GBP">GBP (UK)</option>
								<option value="USD">USD</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Total Price:</td>
							<td width="70%"><input type="text" name="nsd_price"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Number of Detections</td>
							<td width="70%"><input type="text" name="nsd_detections"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Payment Time</td>
							<td width="70%"><input type="text" name="nsd_time"></td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'new_nsd_sale\');</script>
			</td>
		</tr>
		' . ( $sss ? '
		<tr>
			<td style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'surveillance_scanner_sale\');"><b>Sell Surveillance Scanner</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="surveillance_scanner_sale">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Number of IP Addresses:</td>
							<td width="70%"><input type="text" name="sss_ip_addresses"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Frequency:</td>
							<td width="70%">
							<select name="sss_frequency">
								<option>- Select Scan Frequency -</option>
								<option value="1">Daily</option>
								<option value="2">Weekly</option>
								<option value="3">Monthly</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Period Start (Invoice):</td>
							<td width="70%"><input type="text" name="sss_start" value="' . date('Y-m-d') . '"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Expires: (YYYY-MM-DD)</td>
							<td width="70%"><input type="text" name="sss_expires"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Amount:</td>
							<td width="70%"><input type="text" name="sss_amount"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Currency:</td>
							<td width="70%">
							<select name="sss_currency">
								<option value="CAD">CAD</option>
                                                                <option value="CHF">CHF</option>
								<option value="DKK">DK</option>
								<option value="EURO">EURO</option>
								<option value="NOK">NO</option>
								<option value="SEK">SEK</option>
								<option value="GBP">GBP (UK)</option>
								<option value="USD">USD</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Payment Time:</td>
							<td width="70%"><input type="text" name="sss_payment" value="8"></td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'surveillance_scanner_sale\');</script>
			</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'surveillance_scanner\');"><b>Open Surveillance Scanner 7 Day Trial Account</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="surveillance_scanner">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Username</td>
							<td width="70%"><input type="text" name="surveillance_scanner_account_username" value="' . ( $suggest_username ? htmlspecialchars($row['email']) : '' ) . '"><br>
							' . ( $used_suggest_username ? '<font color="red">Note. "' . htmlspecialchars($row['email']) . '" is already used in the Customer Area. Please select a new one.</font>' : '' ) . '</td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
						<tr>
							<td width="30%" valign="top" style="padding-left: 10px;">Open Account</td>
							' . ( !$username_taken ? '
							<td width="70%"><input type="checkbox" style="width: 13px;" onChange="if (!document.forms[\'data\'].surveillance_scanner_account_username.value || !document.forms[\'data\'].surveillance_scanner_account_email.value) {alert(\'Check values!\'); this.checked = false;}" name="surveillance_scanner_account_open"><br>
							Check this box to open a trial account.</td>
							' : '<td>' . $username_taken . '</td>' ) . '
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'surveillance_scanner\');</script>
			</td>
		</tr>
		' : '' ) . '

		<tr>
			<td style="padding-left: 10px;"><a href="javascript:ToggleDiv(\'new_offer\');"><b>New Offer</b></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div id="new_offer">
					<table width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="30%" style="padding-left: 10px;">Solution</td>
							<td width="70%"><input type="text" name="offer_solution"><br>
							e.g.: "VTS, 12 Months" / "SM, 18 Months" / "PoC VTS, 6 Months"</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Offer</td>
							<td width="70%"><input type="text" name="offer_amount"></td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;">Currency</td>
							<td width="70%">
							<select name="offer_currency">
								<option value="CAD">CAD</option>
								<option value="CHF">CHF</option>
								<option value="DKK">DK</option>
								<option value="EURO">EURO</option>
								<option value="NOK">NO</option>
								<option value="SEK">SEK</option>
								<option value="GBP">GBP (UK)</option>
								<option value="USD">USD</option>
							</select>
							</td>
						</tr>
						<tr>
							<td width="30%" style="padding-left: 10px;" valign="top">Expires</td>
							<td width="70%"><input type="text" name="offer_expires" value="' . date('Y-m-d', time()+2592000) . '"><br>Recommended: After 30 days</td>
						</tr>
						<tr>
							<td colspan="2"><br></td>
						</tr>
					</table>
				</div>
				<script>ToggleDiv(\'new_offer\');</script>
			</td>
		</tr>
	</table>
</fieldset>
<br>';

	// Return output
	return $output;
}

// Function for showing Customer Cases
function ShowCustomerCases($cst_lang_id, $persondata)
{
	// Select 'main customer case'
	$res = mysql_query("select * from cst where cst_id = '" . $_GET['cst_id'] . "' limit 1");
	$row = mysql_fetch_array($res);
	$case = $row['case_name'];
	$case_status = $row['case_status'];
	$person_id = $row['person_id'];
	$cases[$row['case_status']] .= '<tr><td style="padding-left: 10px;">' . htmlspecialchars($row['case_name']) . ' [' . ReturnPersonNameFromID($row['person_id']) . ']</td></tr>';

	// IF Master
	if ( $row['master_id'] )
	{
		$res = mysql_query("select * from cst where cst_id = '" . $row['master_id'] . "' limit 1");
		$row = mysql_fetch_array($res);

		// case
		$cases[($row['case_status'] ? $row['case_status'] : 0 )] .= '<tr><td style="padding-left: 10px;"><a href="customer.php?cst_id=' . $row['cst_id'] . '">' . ( $row['case_name'] ? htmlspecialchars($row['case_name']) : 'Not named' ) . ' [' . ReturnPersonNameFromID($row['person_id']) . ']</a></td></tr>';
	}

	// Log master id
	$iMasterID = $row['cst_id'];

	// Select cases to master
	$m_res = mysql_query("select * from cst where master_id = '" . $row['cst_id'] . "' && cst_id != '" . $_GET['cst_id'] . "'");
	while ( $m_row = mysql_fetch_array($m_res) )
	{
		// case
		$cases[($m_row['case_status'] ? $m_row['case_status'] : 0 )] .= '<tr><td style="padding-left: 10px;"><a href="customer.php?cst_id=' . $m_row['cst_id'] . '">' . ( $m_row['case_name'] ? htmlspecialchars($m_row['case_name']) : 'Not named' ) . ' [' . ReturnPersonNameFromID($m_row['person_id']) . ']</a></td></tr>';
	}

	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Customer Case: ' . htmlspecialchars($case) . '</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td width="100%" style="padding-left: 10px;"><b>Open Cases</b></td>
		</tr>
		' . ( $cases[1] ? $cases[1] : '<tr><td style="padding-left: 10px;"><i> - No cases - </i></td></tr>' ) . '
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td width="100%" style="padding-left: 10px;"><b>Closed Cases</b></td>
		</tr>
		' . ( $cases[0] ? $cases[0] : '<tr><td style="padding-left: 10px;"><i> - No cases - </i></td></tr>' ) . '
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td style="padding-left: 10px;">[<a href="open_new_case.php?cst_id=' . $row['cst_id'] . '">Open New Case</a>]' . ( $person_id == $persondata[0] ? ( $case_status ? ' [<a href="customer.php?cst_id=' . intval($_GET['cst_id']) . '&amp;close_case=1">Close Case</a>]' : ' [<a href="customer.php?cst_id=' . intval($_GET['cst_id']) . '&amp;open_case=1">Re-Open Case</a>]' ) : '' ) . ' [<a href="#" onClick="var iCustomerID = prompt(\'Enter Customer ID:\'); location=\'customer.php?cst_id=' . intval($_GET['cst_id']) . '&master_id=' . $iMasterID . '&add_id=\' + iCustomerID;">Attach Customer ID as Case</a>]</td>
		</tr>
	</table>
</fieldset>
<br>';

	// Return output
	return $output;
}

// Function for showing Customer Facts
function ShowCustomerFacts($cst_id)
{
	// Select data
	$res = mysql_query("select * from ca.accounts where cst_id = '" . $cst_id . "' order by account_id");

	// Return if no account is setup
	if ( !mysql_num_rows($res) )
	{
		return false;
	}

	// Select data
	$sd_devices = 0;
	$email = 0;
	$sms = 0;
	$devices = 0;
	while ( $row = mysql_fetch_array($res) )
	{
		// Used Accounts Counter
		$accounts++;

		// Log account details ( first account )
		if ( !$account_id )
		{
			$account_product_type = $row['account_product_type'];
			$account_id = $row['account_id'];
			$account_expires = $row['account_expires'];
			$account_username = $row['account_username'];
			$last_login = $row['last_login'];
		}

		// Select and count number of alert recieved
		$a_res = mysql_query("select * from ca.usage_alerts where account_id = '" . $row['account_id'] . "'");
		while ( $a_row = mysql_fetch_array($a_res) )
		{
			// SMS / Email
			if ( $a_row['type'] == 1 && strlen($a_row['recipient']) > 4 )
			{
				$email++;
			}
			elseif ( $a_row['type'] == 2 && strlen($a_row['recipient']) > 4 )
			{
				$sms++;
			}
		}
	
		// Number of devices
		$d_res = mysql_query("select * from ca.devices where account_id = '" . $row['account_id'] . "'");
		while ( $d_row = mysql_fetch_array($d_res) )
		{
			$devices++;

			// OS
			$os[$d_row['os_id']] = 1;

			// Software
			$s_res = mysql_query("select * from ca.device_software where device_id = '" . $d_row['device_id'] . "'");
			while ( $s_row = mysql_fetch_array($s_res) )
			{
				$software[$s_row['software_id']] = 1;
			}
		}
	
		// Number of SD Devices submitted
		$d_res = mysql_query("select * from ca.im_devices where account_id = '" . $row['account_id'] . "'");
		while ( $d_row = mysql_fetch_array($d_res) )
		{
			$sd_devices++;
		}
	
		// Number of scans conducted
		$s_res = mysql_query("select id from testzone.ca_vulnerability_scan_usage_log where account_id = '" . $row['account_id'] . "'");
		$vuln_scans += mysql_num_rows($s_res);
	}

	// Product
	switch ( $account_product_type )
	{
		case 1:
			$product = 'VTS';
			break;
		case 2:
			$product = 'SM';
			break;
		case 3:
			$product = 'ESM';

			// Number of ESM accounts
			$e_res = mysql_query("select * from ca.esm where cst_id = '" . intval($cst_id) . "' limit 1");
			$e_row = mysql_fetch_array($e_res);

			// Append to product string
			$product .= ' ( ' . ($accounts-1) . '/' . $e_row['no_users'] . ' Accounts )';

			break;
		case 4:
			$product = 'VTS-E';
			break;
		case 5:
			$product = 'SS';

			// Select number of hosts under surveillance
			$e_res = mysql_query("select * from ca.vss_scan_profiles where cst_id = '" . intval($cst_id) . "'");
			while ( $e_row = mysql_fetch_array($e_res) )
			{
				$total_surveillance += count(returnVSSTargetsArray($e_row['targets']));
			}

			// Select currenct Scan Limits
			$l_res = mysql_query("select * from ca.vss_scan_limits where account_id = '" . $account_id . "' limit 1");
			$l_row = mysql_fetch_array($l_res);

			break;
		case 6:
			$product = 'SM with NSD';
			break;
		case 7:
			$product = 'ESM with NSD';
			break;
		default:
			$product = 'Unknown';
			break;
	}
	
	// Start output
	{
		// Start
		$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Customer Area Details</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">';

		// "Account Details"
		{
			$output .= '
		<tr>
			<td style="padding-left: 10px;"><b>Account Details</b></td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Product:</td>
			<td>' . $product . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Username:</td>
			<td>' . htmlspecialchars($account_username) . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Last Login:</td>
			<td>' . $last_login . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;" width="30%">Expires:</td>
			<td width="70%">' . ( $account_expires ? $account_expires : 'Expired' ) . '</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>';
		}

		// Product line 1 details (First VTS-E)
		if ( $account_product_type == 4 )
		{
			// Select VTS-E product monitored
			$v_res = mysql_query("select * from ca.vtse_products where account_id = '" . $account_id . "'");

			// Monitoring
			{
				$output .= '
				<tr>
					<td style="padding-left: 10px;"><b>Monitoring</b></td>
				</tr>
				<tr>
					<td style="padding-left: 20px;">Products:</td>
					<td>' . mysql_num_rows($v_res) . '</td>
				</tr>';
			}
		}
		elseif ( $account_product_type != 5 )
		{
			// Monitoring
			{
				$output .= '
		<tr>
			<td style="padding-left: 10px;"><b>Monitoring</b></td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Network Devices:</td>
			<td>' . $devices . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Unique Operating Systems:</td>
			<td>' . count($os) . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Unique Software:</td>
			<td>' . count($software) . '</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>';
			}

			// Alerts
			{
				$output .= '
		<tr>
			<td style="padding-left: 10px;"><b>Alerts Recieved</b></td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Emails:</td>
			<td>' . $email . '</td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">SMS:</td>
			<td>' . $sms . '</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>';
			}

			// Software Detector
			{
				$output .= '
		<tr>
			<td style="padding-left: 10px;"><b>Software Detector</b></td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Submitted Devices:</td>
			<td>' . $sd_devices . '</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>';
			}

			// Vulnerability Scans
			{
				$output .= '
		<tr>
			<td style="padding-left: 10px;"><b>Vulnerability Scanner</b></td>
		</tr>
		<tr>
			<td style="padding-left: 20px;">Scans Requested:</td>
			<td>' . $vuln_scans . '</td>
		</tr>
		<tr>
			<td style="padding-left: 10px;"><br></td>
		</tr>';
			}
		}
		elseif ( $account_product_type == 5 ) // SS
		{
			// Check if trial account
			$c_res = mysql_query("select * from ca.vss_scan_limits where account_id = '" . $account_id . "'");

			// Trial
			if ( !mysql_num_rows($c_res) )
			{
				// Select trial scan details
				$s_res = mysql_query("select * from ca.vss_nessus_reports_raw where account_id = '" . $account_id . "' limit 1");
				$s_row = mysql_fetch_array($s_res);

				// Display when scan started / ended
				{
					$output .= '
			<tr>
				<td style="padding-left: 10px;"><b>Trial Scan</b></td>
			</tr>
			<tr>
				<td style="padding-left: 20px;">Scan Started:</td>
				<td>' . $s_row['scan_started'] . '</td>
			</tr>
			<tr>
				<td style="padding-left: 20px;">Scan Ended:</td>
				<td>' . $s_row['scan_ended'] . '</td>
			</tr>
			<tr>
				<td style="padding-left: 10px;"><br></td>
			</tr>';
				}
			}
			else
			{
				// Fetch limit data
				$l_row = mysql_fetch_array($c_res);

				// Display number of targets set up
				{
					$output .= '
			<tr>
				<td style="padding-left: 10px;"><b>Account Settings</b></td>
			</tr>
			<tr>
				<td style="padding-left: 20px;">Available Slots:</td>
				<td>' . $l_row['number'] . ' / ' . FrequencyName($l_row['type'])  . '</td>
			</tr>
			<tr>
				<td style="padding-left: 20px;">Slots Used:</td>
				<td>' . $total_surveillance . '</td>
			</tr>
			<tr>
				<td style="padding-left: 10px;"><br></td>
			</tr>';
				}
			}
		}

		// End
		$output .= '
	</table>
	</fieldset>
<br>';
	}

	// Return output
	return $output;
}

// Function for showing products avalible
function ShowProducts($cst_lang_id)
{
	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Product Actions</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td width="30%">Product:</td>
			<td width="70%"><select name="product_id"><option value="0">Choose Product</option>';

	// Select prodcts
	$res = mysql_query("select * from products where lang_id = '" . $cst_lang_id . "' order by name");

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		$output .= '<option value="' . $row['product_id'] . '">' . $row['name'] . ( !$row['trial'] ? ', ' . $row['price'] . ',-' : ', Free' ) . '</option>';
	}

	// Output
	$output .= '</select></td></tr>';
	$output .= '<tr>';
	$output .= '<td valign="top">Discount:</td>';
	$output .= '<td valign="top"><input type="text" name="discount" style="width: 80px;"></td>';
	$output .= '</tr>';
	$output .= '<tr>';
	$output .= '<td valign="top">Expire Date:</td>';
	$output .= '<td valign="top"><input type="text" name="input_expire" style="width: 80px;"></td>';
	$output .= '</tr>';
	$output .= '<tr>';
	$output .= '<td valign="top">Payment time:</td>';
	$output .= '<td valign="top"><input type="text" name="input_payment" value="8" style="width: 80px;"><br><font color="red">8 days standard. Only 30 days if customer demands by own request.<br>
All other requests needs to be approved IN ADVANCE by resp. manager.</font></td>';
	$output .= '</tr>';

	// End table
	$output .= '</table></fieldset>';

	// Return
	return $output;
}

// Function for showing appointment field
function ShowAppointmentField($cst_id, $persondata)
{
	// Get this return date, if this person owns this customer
	$res = mysql_query("select person_id, appointment from cst where cst_id = '" . $cst_id . "'");
	$row = mysql_fetch_array($res);
	$app = $row['appointment'];

	// Check if this person is owner
	if ( $row['person_id'] == $persondata[0] || !$row['person_id'] )
	{
		$allowed = 1;
	}

	// Output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Next Appointment</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		' . ( $allowed ? '
		<tr>
			<td width="20%" style="padding-left: 10px;"><a href="#" onClick="document.forms.data.return_date.value=\'' . date('Y-m-d G:i:s') . '\';"><b>Appointment</b></a></td>
			<td width="80%"><input type="text" name="return_date" value="' . $app . '" size="45"></td>
		</tr>' : '
		<tr>
			<td style="padding-left: 10px;"><b>Appointment</b></td>
			<td>' . $app . '</td>
		</tr>' ) . '
	</table>
	</fieldset><br>';

	return $output;
}

// Function for showing comment field
function ShowCommentField($cst_id, $persondata)
{
	// Get this return date, if this person owns this customer
	$res = mysql_query("select person_id, appointment from cst where cst_id = '" . $cst_id . "'");
	$row = mysql_fetch_array($res);
	$app = $row['appointment'];

	// Start output
	$output .= '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Comment</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="#" onClick="document.getElementById(\'it_org\').innerHTML=\'<textarea cols=\\\'45\\\' rows=\\\'5\\\' name=\\\'it_org\\\'></textarea><br><br>\';document.forms.data.it_org.focus();"><b>Research re. IT & IT Organisation</b></a></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;" id="it_org"></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="#" onClick="document.getElementById(\'cust_rel\').innerHTML=\'<textarea cols=\\\'45\\\' rows=\\\'5\\\' name=\\\'cust_rel\\\'></textarea><br><br>\';document.forms.data.cust_rel.focus();"><b>Customer Relation</b></a></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;" id="cust_rel"></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="#" onClick="document.getElementById(\'setup\').innerHTML=\'<textarea cols=\\\'45\\\' rows=\\\'5\\\' name=\\\'setup\\\'></textarea><br><br>\';document.forms.data.setup.focus();"><b>Set Up & Solution Feedback</b></a></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;" id="setup"></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;"><a href="#" onClick="document.getElementById(\'sales_buying\').innerHTML=\'<textarea cols=\\\'45\\\' rows=\\\'5\\\' name=\\\'sales_buying\\\'></textarea><br><br>\';document.forms.data.sales_buying.focus();"><b>Sales; Buying Signals, Gains & Avoids</b></a></td>
		</tr>
		<tr>
			<td colspan="2" style="padding-left: 10px;" id="sales_buying"></td>
		</tr>';

	// End table
	$output .= '</table></fieldset><br>';

	// Return
	return $output;
}

// Function for showing entered comments
function ShowEnteredComments($cst_id)
{
	// Start output
	$output .= '<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="2" style="padding-left: 10px;">
			<table width="100%" cellpadding="0" cellspacing="0">';

	// Select all comments
	$res = mysql_query("select * from comments where cst_id = '" . $cst_id . "' order by added desc");

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		if ( strlen(trim($row['comment'])) > 0 )
		{
			// Output
			$output .= OutputComment($row['added'], $row['person_id'], $row['comment'], $row['type']);
		}
	}

	// End table
	$output .= '</table></tr></td></table>';

	// Return
	return $output;
}

function OutputComment($added, $person_id, $comment, $type)
{
	switch ( $type )
	{
		case 1:
			$name = 'Research re. IT & IT Organisation';
			$color = '#EAFFD0';
			break;
		case 2:
			$name = 'Customer Relation';
			$color = '#FFE7E7';
			break;
		case 3:
			$name = 'Set Up & Solution Feedback';
			$color = '#FFF8E4';
			break;
		case 4:
			$name = 'Sales; Buying Signals, Gains & Avoids';
			$color = '#DBF0FF';
			break;
		default:
			$color = '#FFFFFF';
			$name = 'N/A';
	}

	// Prepare comment for output
	$comment = htmlspecialchars(wordwrap($comment, 80, ' ', 1));
	$comment = str_replace('&lt;br&gt;', '<br>', $comment);

	$o = '<tr>
		<td colspan="2" style="border: 1px solid; padding: 2px;" bgcolor="' . $color . '"><b>' . $added . ' - ' . $name . ' - ' . ReturnPersonNameFromID($person_id) . '</b></td>
	</tr>
	<tr>
		<td colspan="2" style="border-left: 1px solid; border-right: 1px solid; border-bottom: 1px solid; padding-left: 3px; padding-bottom: 3px; padding-right: 3px; padding-top: 3px; font-size: 12px;" bgcolor="' . $color . '">' . $comment . '</td>
	</tr>
	<tr>
		<td colspan="2"><br></td>
	</tr>';

	// Return output
	return $o;
}

// Display icons for customer
function ShowIcon($cst_id, $cst_lang_id)
{
	// Start output
	$output .= '<table width="100%" cellpadding="0" cellspacing="0">
		<tr><td colspan="2">';

	// Select all icons available
	$res = mysql_query("select * from icon_types where lang_id = '" . $cst_lang_id . "'");
	while ( $row = mysql_fetch_array($res) )
	{
		// Check if icon is selected
		$c_res = mysql_query("select * from icon_ref where icon_id = '" . $row['icon_id'] . "' && cst_id = '" . $cst_id . "' limit 1");

		$output .= '<input type="checkbox" name="icons[]" style="width: 2%;" value="' . $row['icon_id'] . '" ' . ( mysql_num_rows($c_res) ? 'checked' : '' ) . '> <img src="gfx/' . $row['icon'] . '.gif" alt="">';
	}

	// End output table
	$output .= '</td></tr></table>';

	// Return output
	return $output;
}

// Function for displaying Category Numbers
function ShowCategoryNumbers($clients, $servers, $itpeople, $lead = false, $segment_id, $forecast_expectancy, $forecast_amount, $forecast_date)
{
	// Select Segment name
	if ( $segment_id )
	{
		$res = mysql_query("select * from crm.segments where segment_id = '" . $segment_id . "'");
		$row = mysql_fetch_array($res);
	}

	// Return HTML output
	return '<fieldset><legend><b><font style="font-size: 12px;" color="#417999">Company Category</font></b></legend>
	<table width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td width="20%" style="padding-left: 10px;" valign="top"><b>Segment</b></td>
			<td width="80%" valign="top"><b>' . ( $row['segment_name'] ? htmlspecialchars($row['segment_name']) : ( $lead ? 'Inbound Customer Lead!' : 'N/A' ) ) . '</b></td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		' . ( $GLOBALS['persondata'][5] == 6 || $GLOBALS['row']['customer_marked_dead'] == 6 ? '
		<tr>
			<td valign="top" style="padding-left: 10px;"><b>Mark SV</b></td>
			<td>
				' . ( $GLOBALS['row']['customer_marked_dead'] == 6 ? 'Customer marked as "Security Vendor"' : '<label><input type="checkbox" style="width: 15px;" value="1" name="marked_sv"> Mark customer as "Security Vendor"</label>' ) . '
			</td>
		</tr>
		<tr>
			<td><br></td>
		</tr>
		' : '' ) . '
		<tr>
			<td valign="top" style="padding-left: 10px;"><b>Forecast</b></td>
			<td>
				<select name="forecast_expectancy">
					<option value="0">N/A</option>
					<option value="30"' . ( $forecast_expectancy == 30 ? ' selected' : '' ) . '>30%</option>
					<option value="40"' . ( $forecast_expectancy == 40 ? ' selected' : '' ) . '>40%</option>
					<option value="50"' . ( $forecast_expectancy == 50 ? ' selected' : '' ) . '>50%</option>
					<option value="75"' . ( $forecast_expectancy == 75 ? ' selected' : '' ) . '>75%</option>
					<option value="90"' . ( $forecast_expectancy == 90 ? ' selected' : '' ) . '>90%</option>
					<option value="100"' . ( $forecast_expectancy == 100 ? ' selected' : '' ) . '>100% (WON)</option>
					<option value="101"' . ( $forecast_expectancy == 101 ? ' selected' : '' ) . '>LOST</option>
				</select> Expectancy<br>
				<input type="text" style="width: 75px;" name="forecast_amount" value="' . number_format(intval($forecast_amount)) . '"> Amount in &euro;<br>
				<input type="text" style="width: 75px;" name="forecast_date" value="' . htmlspecialchars($forecast_date) . '"> Expected Closed (Format: YYYY-MM-DD)
			</td>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td valign="top" style="padding-left: 10px;"><b>Facts</b></td>
			<td>
				<input type="text" style="width: 50px; text-align: right;" name="category_clients" value="' . number_format(intval($clients)) . '"> Number of clients<br>
				<input type="text" style="width: 50px; text-align: right;" name="category_servers" value="' . number_format(intval($servers)) . '"> Number of servers<br>
				<input type="text" style="width: 50px; text-align: right;" name="category_itpeople" value="' . number_format(intval($itpeople)) . '"> Number of IT-People<br>
			</td>
		</tr>
	</table>
	</fieldset><br>';
}

// SS
{
	// Generate array of targets
	function returnVSSTargetsArray($targets)
	{
		// Explode by ','
		$array = explode(',', $targets);

		// Loop over result
		while ( list($key, $target) = each($array) )
		{
			$target = trim($target);
			if ( preg_match('^([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)([0-9]{1,3})-([0-9]{1,3})$', $target, $regs) )
			{
				// Check values
				if ( $regs[3] > 254 )
				{
					$regs[3] = 254;
				}
				if ( $regs[2] > 254 )
				{
					$regs[2] = 254;
				}
				if ( $regs[3] < 1 )
				{
					$regs[3] = 1;
				}
				if ( $regs[2] < 1 )
				{
					$regs[2] = 1;
				}

				// Parse range
				for ( $i = $regs[2] ; $i<=$regs[3] ; $i++ )
				{
					$result[$regs[1] . $i] = $regs[1] . $i;
				}
			}
			else
			{
				if ( $target )
				{
					$result[$target] = $target;
				}
			}
		}

		return $result;
	}
}

// Function for returning frequency name
function FrequencyName($type)
{
	// Frequency Name
	switch ($type)
	{
		case 1:
			$tmp = 'Daily';
			break;
		case 2:
			$tmp = 'Weekly';
			break;
		case 3:
			$tmp = 'Monthly';
			break;
	}

	return $tmp;
}
?>
