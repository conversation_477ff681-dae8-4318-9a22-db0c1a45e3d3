<?php
function segmentCombo( $selected_parent_segment_id, $parent_segment_id = "", $space = "" ) {
	$res = "";

	if ( $parent_segment_id != "" ) {
		$parent_segment_id = " AND parent_segment_id = '".(int)$parent_segment_id."'";
	} else {
		$parent_segment_id = " AND ( parent_segment_id is NULL OR parent_segment_id = 0 )";
		$res = "<option value='0'>- Select Segment -</option>";
	}

	$result = DBQueryGetRows("SELECT segment_name, segment_id, parent_segment_id, (SELECT COUNT(segment_id) FROM crm.segments WHERE parent_segment_id = A.segment_id AND segment_name like \"crm2 - %\" AND segment_status = 0 ) AS children FROM crm.segments AS A WHERE segment_name like \"crm2 - %\" AND segment_status = 0 ".$parent_segment_id);

	for ( $i = 0; $i < count( $result ); $i++ ) {
		$selected = "";
		if ( $result[$i]['segment_id'] == $selected_parent_segment_id ) {
			$selected = " selected ";
		}
		$res .= "<option ".$selected." value='".$result[$i]['segment_id']."'>".$space.htmlspecialchars( $result[$i]['segment_name'] )."</option>";
		if ( $result[$i]['children'] > 0 ) {
			$res .= segmentCombo( $selected_parent_segment_id, $result[$i]['segment_id'], $space." |_" );
		}
	}

	return $res;
}

function makeSegmentCombo( $selected_parent_segment_id = "" ) {
	return  "<select name='parent_segment_id'>" . segmentCombo( $selected_parent_segment_id ) . "</select>";
}

// Function for opening the database connection
function OpenDatabase()
{
	// open connection
	mysql_connect(ADMIN_DB_HOST, ADMIN_DB_USER, ADMIN_DB_PASS);

	// Choose database
	ChooseDatabase(ADMIN_DB_NAME);

	// Return
	return 1;
}

// Function for choosing database
function ChooseDatabase($database)
{
	// Select database
	mysql_select_db($database);

	// Return
	return 1;
}

// Function for showing icons: Awareness
function ShowAwarenessIcons()
{
	global $cst_id;

	// Headline
	$output = '<br><b>Awareness</b><br>';

	// Show all 'c' options
	$res = mysql_query("select * from icons_aida where icon_type = 'c'");
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_c"><option value="0">- C -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all 'c' options
	$res = mysql_query("select * from icons_aida where icon_type = 'o'");
	$c_options = '';
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_o"><option value="0">- O -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '<br><br>';

	// Return output
	return $output;
}

// Function for showing icons: Interest
function ShowInterestIcons()
{
	global $cst_id;

	// Headline
	$output = '<b>Interest</b><br>';

	// Show all 's' options
	$res = mysql_query("select * from icons_aida where icon_type = 's'");
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_s"><option value="0">- S -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all 'v' options
	$res = mysql_query("select * from icons_aida where icon_type = 'v'");
	$c_options = '';
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_v"><option value="0">- V -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all 'e' options
	$res = mysql_query("select * from icons_aida where icon_type = 'e'");
	$c_options = '';
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_e"><option value="0">- E -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '<br><br>';

	// Return output
	return $output;
}

// Function for showing icons: Desire
function ShowDesireIcons()
{
	global $cst_id;

	// Headline
	$output = '<b>Desire</b><br>';

	// Show all 's' options
	$res = mysql_query("select * from icons_aida where icon_type = 'd'");
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_d"><option value="0">- D -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '<br><br>';

	return $output;
}

// Function for showing icons: Action
function ShowActionIcons()
{
	global $cst_id;

	// Headline
	$output = '<b>Action</b><br>';

	// Show all '$' options
	$res = mysql_query("select * from icons_aida where icon_type = '$'");
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_$"><option value="0">- $ -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all '$' options
	$res = mysql_query("select * from icons_aida where icon_type = '$$' limit 1");
	$row = mysql_fetch_array($res);
	$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
	$output .= '<input type="checkbox" style="width: 5%;" name="icon_$$" value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'checked' : '' ) . '> ' . $row['icon_display'];

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all 'u' options
	$res = mysql_query("select * from icons_aida where icon_type = 'u'");
	$c_options = '';
	while ( $row = mysql_fetch_array($res) )
	{
		$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
		$c_options .= '<option value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'selected' : '' ) . '>' . $row['icon_display'] . '</option>';
	}
	$output .= '<select name="icon_u"><option value="0">- U -</option>' . $c_options . '</select>';

	// Spacer
	$output .= '&nbsp;&nbsp;';

	// Show all '$' options
	$res = mysql_query("select * from icons_aida where icon_type = 'star' limit 1");
	$row = mysql_fetch_array($res);
	$c_res = mysql_query("select * from icons_aida_ref where cst_id = '" . $cst_id . "' && icon_id = '" . $row['icon_id'] . "' limit 1");
	$output .= '<input type="checkbox" style="width: 5%;" name="icon_star" value="' . $row['icon_id'] . '"' . ( mysql_num_rows($c_res) == 1 ? 'checked' : '' ) . '> ' . $row['icon_display'];

	// Spacer
	$output .= '<br><br>';

	return $output;
}

// Function for showing the language choices available
function DisplayLanguageRadioChoices($lang_id = 0)
{
	// Select all language choices
	$res = mysql_query("select * from vuln_track.language");

	// Loop over result
	while ( $row = mysql_fetch_array($res) )
	{
		$output .= '<input type="radio" name="lang_id" value="' . $row['lang_id'] . '"' . ( $lang_id == $row['lang_id'] ? ' checked' : '' ) . '> ' . $row['lang_name'] . '<br>';
	}

	// Return outout
	return $output;
}

// Function for returning segment name
function ReturnSegmentName($segment_id)
{
	$res = mysql_query("select * from segments where segment_id = '" . $segment_id . "' limit 1");
	$row = mysql_fetch_array($res);

	return( ($row['segment_name'] ? $row['segment_name'] : 'Not in a segment' ) );
}


// Function for returning status of a sale
function ReturnSaleStatus($status)
{
	// Switch
	switch($status)
	{
		case '':
			return 'Awaiting';
		case 0:
			return 'Awaiting';
		case 1:
			return '';
		case 2:
			return 'Paid';
		case 3:
			return 'Cancelled';
		case 4:
			return '<span style="color: #DD0000;">Rejected (CRC)</span>';
		case 5:
			return '<span style="color: #009900;">Accepted (CRC)</span>';
	}
}

// Function for returning Vuln lang_id for a customer
function ReturnCustomerVulnLangID($cst_id)
{
	// Select customer data
	$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Return
	return $row['vuln_lang_id'];
}

// Function for returning lang_id for a customer
function ReturnCustomerLangID($cst_id)
{
	// Select customer data
	$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
	$row = mysql_fetch_array($res);
	$lang_id = $row['lang_id'];

	// Return
	return $lang_id;
}

// Function for returning lang_id for a customer
function ReturnCRMLangID($cst_id)
{
	// Select customer data
	$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Return
	return $row['crm_lang_id'];
}

// Function for returning country name
function ReturnCountryName($lang_id)
{
	// Switch database
	ChooseDatabase('vuln_track');

	// Select from table
	$res = mysql_query("select * from language where lang_id = '" . $lang_id . "'");
	$row = mysql_fetch_array($res);

	// Switch database
	ChooseDatabase('crm');

	// Return result
	return $row['lang_name'];
}

// Function for getting data about a salesperson
function GetSalesPersonData($init)
{
	// Select from table
	$res = mysql_query("select * from salespeople where init = '" . $init . "'");
	$row = @mysql_fetch_array($res);

	// Return
	return array($row['person_id'], $row['lang_id'], $row['name'], $row['email'], $row['vuln_lang_id'], $row['product'], $row['lead_countries']);
}

// Function for converting or getting dates
function ConvertGetDate($date, $format)
{
	// Switch for format
	switch ($format)
	{
		case 'MySQL-Date':
			$date = date("Y-m-d");
			break;
		case 'MySQL-DateTime':
			$date = date("Y-m-d G:i:s");
			break;
		case 'DK-Date':
			$date = date("d-m-Y");
			break;
		case 'DK->MySQL':
			break;
		case 'MySQL->DK':
			break;
		default:
			break;
	}

	// Return
	return $date;
}

// Function for returning person name from ID
function ReturnPersonNameFromID($person_id)
{
	// Select from table
	$res = mysql_query("select name from salespeople where person_id = '" . $person_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Return name
	return $row['name'];
}

// Function for making standard HTML header
function HTMLHeader($title, $link, $js_file = false, $bEXTJS = false)
{
	// Get person data
	$persondata = GetSalesPersonData(getenv('REMOTE_USER'));

	// Start header
	$header = '<html>';
	$header .= '<head>';
	$header .= '<title> ' . $title . ' </title>';
	$header .= '<link rel="stylesheet" TYPE="text/css" HREF="/crmadmin/crm/sales/default.css">';
	$header .= '<link rel="stylesheet" TYPE="text/css" media="print" HREF="/crmadmin/crm/sales/default_print.css">';
	$header .= '<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">';

	// EXTJS?
	if ( $bEXTJS ) {
		$header .= '
    <!-- ** CSS ** -->
    <!-- base library -->
    <link rel="stylesheet" type="text/css" href="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/resources/css/ext-all.css" />

    <!-- overrides to base library -->
    <link rel="stylesheet" type="text/css" href="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/ux/css/GroupSummary.css" />

    <!-- page specific -->
    <link rel="stylesheet" type="text/css" href="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/shared/examples.css" />
    <link rel="stylesheet" type="text/css" href="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/grid-examples.css" />

    <style type="text/css">
        .x-grid3-cell-inner {
            font-family:"segoe ui",tahoma, arial, sans-serif;
        }
        .x-grid-group-hd div {
            font-family:"segoe ui",tahoma, arial, sans-serif;
        }
        .x-grid3-hd-inner {
            font-family:"segoe ui",tahoma, arial, sans-serif;
            font-size:12px;
        }
        .x-grid3-body .x-grid3-td-cost {
            background-color:#f1f2f4;
        }
        .x-grid3-summary-row .x-grid3-td-cost {
            background-color:#e1e2e4;
        }
        .icon-grid {
            background-image:url(http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/shared/icons/fam/grid.png) !important;
        }
        .x-grid3-dirty-cell {
            background-image:none;
        }
    </style>

    <!-- ** Javascript ** -->
    <!-- ExtJS library: base/adapter -->
    <script type="text/javascript" src="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/adapter/ext/ext-base.js"></script>

    <!-- ExtJS library: all widgets -->
    <script type="text/javascript" src="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/ext-all.js"></script>

    <!-- overrides to base library -->

    <!-- extensions -->
    <script type="text/javascript" src="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/ux/GroupSummary.js"></script>

    <!-- page specific -->
    <script type="text/javascript" src="http://dev.intnet/~jb/extjs/ext-3.0.0/crm/totals.js"></script>

	<!-- Important overrides -->
	<script>
	Ext.BLANK_IMAGE_URL = "http://dev.intnet/~jb/extjs/ext-3.0.0/crm/ext-3.0.0/resources/images/default/s.gif";
	</script>
';
	}

	// Load JavaScript default JS and the "Sort Table" object
	$header .= '<script src="/crmadmin/crm/sales/javascript/defaults.js"></script>';
	$header .= '<script src="/crmadmin/crm/sales/javascript/sorttable.js"></script>';

	// Load default JavaScript File
	if ( file_exists('/home/<USER>/public_html/crmadmin/crm/sales/javascript/' . $js_file) && $js_file )
	{
		$header .= '<script src="/crmadmin/crm/sales/javascript/' . $js_file . '"></script>';
	}

	$header .= '</head>';
	$header .= '<body topmargin="0" margintop="0">';

	// This has to be in a div/span
	{
		$header .= '<div style="width: 100%; position: fixed; left: 0; top: 0;" class="no_print_display">';
		$header .= '<table width="100%" cellpadding="0" cellspacing="0" bgcolor="white">';
		$header .= '<tr>';
		$header .= '<td class="TopText" width="25%" valign="top">' . $title . '<br>[ <a href="' . $link . '">Main Menu</a> ] [ <a href="'.HTTP_PATH.'appointment_list.php#' . date("Y-m-d") . '">Appointment List</a> ]<br>';
		$header .= '<form method="GET" action="find_customer.php"><input type="text" name="search" style="width: 75%;"><input type="submit" value="Search" style="width: 25%;" class="submit"></form></td>';
		$header .= '<td width="75%" valign="top">';

		// Content here
		{
			// CRM message
			$res = mysql_query("select * from crm.crm_message");
			$row = @mysql_fetch_array($res);

			$header .= '
			<table width="100%" cellpadding="0" cellspacing="0">
				<tr>
					<td width="33%" valign="top">
						<b>Message:</b><br>
						' . htmlentities($row['message']) . '
						' . ( @mysql_num_rows(returnAvailableLeadsArray()) > 0 ? '<br><br>&nbsp;&nbsp;<blink><a href="/crmadmin/crm/sales/inbound_leads.php"><b><font color="RED">Inbound Lead Available!</font></b></a></blink>' : '' ) . '
					</td>
					<td width="33%" valign="top"><b>Top 5 This Month:</b><br>
					';

					// Select all sales from the saleslog
					$res = mysql_query("select * from crm.saleslog where sold_date >= '" . date('Y-m-1') . "' && (status is null || (status != 3 && status != 6 && status != 7) ) && ((product_price - discount) > 0 )");
					while ( $row = @mysql_fetch_array($res) )
					{
						$month[$row['person_id']] += ReturnEuro(($row['product_price'] - $row['discount']), $row['product_name']);
					}

					@arsort($month);

					while ( list($person_id, $amount) = @each($month) )
					{
						// Exclude TP, HZ, and STS
						if ( $person_id == 2 || $person_id == 3 || $person_id == 102 || $person_id == 103  || $person_id == 99 || $person_id == 74 )
						{
							continue;
						}

						$header .= ++$qt . '. ' . ReturnPersonNameFromID($person_id) . ' (&euro; ' . number_format( $amount , 2) . ")<br>";
						if ( $qt == 5 )
						{
							break;
						}
					}

					// Select target
					$res = mysql_query("select * from crm.forecast where person_id = '" . $persondata[0] . "' && month = '" . ReturnPersonForecastMonth($persondata[0]) . "' limit 1");
					$row = mysql_fetch_array($res);

					// Output
					$header .= '
					<br>You: &euro; ' . number_format( ( $month[$persondata[0]] ? $month[$persondata[0]] : 0 ), 2 ) . ' / Target: &euro; ' . number_format( $row['revenue'] , 2 ) . '
					</td>
					<td width="33%" valign="top"><b>Top 5 This Week:</b><br>
					';

					// Select all sales from the saleslog
					$res = mysql_query("select * from crm.saleslog where sold_date >= '" . date('Y-m-d', mktime(0, 0, 0, date('m'), (date('d') - date('w') + 1), date('Y'))) . "' && (status is null || (status != 3 && status != 6 && status != 7) ) && ((product_price - discount) > 0 ) && person_id not in(2,3,40)");
					while ( $row = @mysql_fetch_array($res) )
					{
						$week[$row['person_id']] += ReturnEuro(($row['product_price'] - $row['discount']), $row['product_name']);
					}

					@arsort($week);

					$qt = 0;
					while ( list($person_id, $amount) = @each($week) )
					{
						if ( $person_id == 2 || $person_id == 3 || $person_id == 102 || $person_id == 103  || $person_id == 99 || $person_id == 74 ) {
							continue;
						}

						$header .= ++$qt . '. ' . ReturnPersonNameFromID($person_id) . ' (&euro; ' . number_format( $amount , 2 ) . ")<br>";
						if ( $qt == 5 )
						{
							break;
						}
					}

					$header .= '<br>You: &euro; ' . number_format( ( $week[$persondata[0]] ? $week[$persondata[0]] : 0 ), 2);

					$header .= '
					</td>
				</tr>
			</table>
			';
		}

		$header .= '</td>
				</tr>
				<tr>
					<td colspan="3"><img src="/crmadmin/crm/sales/gfx/orangebottom.gif" width="100%" height="4"></td>
				</tr>
			</table>
		</div><br><br><br><br><br><br>';
	}

	// Return
	return $header;
}

// Function for making standard HTML Footer
function HTMLFooter()
{
	// Start footer
	$footer .= '<table width="100%" cellpadding="0" cellspacing="0">';
	$footer .= '<tr>';
	$footer .= '<td><img src="/crmadmin/crm/sales/gfx/orangebottom.gif" width="100%" height="4"></td>';
	$footer .= '</tr>';
	$footer .= '<tr>';
	$footer .= '<td align="center">Secunia</td>';
	$footer .= '</tr>';
	$footer .= '</table>';
	$footer .= '</body>';
	$footer .= '</html>';

	// Return
	return $footer;
}

// Function for generating random password
function GenerateRandomPassword($len)
{
	// Numbers (48-57)
	for ( $i=48 ; $i<=57 ; $i++ )
	{
		$chars[] = chr($i);
	}

	// LC Letters (97-122)
	for ( $i=97 ; $i<=122 ; $i++ )
	{
		$chars[] = chr($i);
	}

	// Make id
	for ( $i=0 ; $i<$len ; $i++ )
	{
		$id .= $chars[rand(0, count($chars)-1)];
	}

	// Return ID
	return $id;
}

// Send one-time-usage password
function SendOneTimeUsagePassword($cst_id, $email)
{
	// Select the data from the one_time_passwords table
	$p_res = mysql_query("select * from one_time_passwords where cst_id = '" . $cst_id . "' && email = '" . $email . "' limit 1");

	// Check that there is an password else return
	if ( !mysql_num_rows($p_res) )
	{
		return 'No password generated. Please contact technical support, if you have followed the right procedures.';
	}

	// Get result
	$p_row = mysql_fetch_array($p_res);

	// Get customer details
	$res = mysql_query("select * from cst where cst_id = '" . $cst_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Salesperson data
	$s_res = mysql_query("select * from salespeople where person_id = '" . $row['person_id'] . "' limit 1");
	$s_row = mysql_fetch_array($s_res);

	// Email
	$content = "Dear " . $row['contact'] .  "

Thank you for your interest in Secunia Security Advisories.

I hereby send your username and password, which gives you access to the
Secunia solution.

The Secunia solution provides you timely information regarding new
vulnerabilities in your IT systems, giving you time to act and prevent
vulnerabilities from being exploited.

Username: " . $p_row['email'] . "
Password: " . $p_row['password'] . "

NOTE: The password is a one time password, which needs to be updated
when you log on to the system.

Click below to gain access to your account now:

https://ca.secunia.com/index.php?page=login

Please note that the account expires automatically. To continue using
the service, you need to contact me.

Secunia offers:
 - Validated information
 - Timely advisories
 - Only relevant advisories
 - Fast and easy to configure filtering system
 - A comprehensive database
 - Coverage of all systems
 - Vendor independent information
 - Advisories via email
 - Alerts via SMS
 - Hotline to security experts regarding vulnerabilities

Please feel free to contact me, if you would like assistance to
complete the setup of the solution.

Thank you for your interest in Secunia.


--
Kind Regards,

" . $s_row['name'] . "
" . $s_row['title'] . "

Secunia
Toldbodgade 37B
1253 Copenhagen
Denmark

Tel : +45 7020 5144
Fax : +45 7020 5145
";


	// Send email
	mail($email, 'Secunia - Customer Area Login Information', $content, "From: " . $s_row['name'] . " <" . $s_row['email'] . ">\r\nReply-To: " . $s_row['email'] ."\r\nContent-Type: text/plain; charset=\"iso-8859-1\"\r\nContent-Transfer-Encoding: 8bit\r\n");
	mail($s_row['email'], 'Secunia - Customer Area Login Information', $content, "From: " . $s_row['name'] . " <" . $s_row['email'] . ">\r\nReply-To: " . $s_row['email'] ."\r\nContent-Type: text/plain; charset=\"iso-8859-1\"\r\nContent-Transfer-Encoding: 8bit\r\n");

	// Delete the one time password entry in to sales system
	mysql_query("delete from one_time_passwords where cst_id = '" . $cst_id . "' && email = '" . $email . "' limit 1");

	// Return
	return 'Password has been e-mailed.';
}

// Return lang name
function ReturnLangName($lang_id)
{
	// Select language name
	$res = mysql_query("select * from vuln_track.language where lang_id = '" . $lang_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Return lang name
	return($row['lang_name']);
}

// Function to convert currency from ANY to ANY
function fFOREX( $fFrom, $fTo ){
	// Convert from any to any
	// E.G.:
	//fFOREX( "5.5610", "7.4424" ); // USD to EURO
	//fFOREX( "7.4424", "5.5610" ); // EURO to USD
	$fDKK_EURO = 1.0 / (float)$fTo;
	$fResult = (float) $fDKK_EURO * (float) $fFrom;
	return $fResult;
}

// Function for calculating the current amount in EURO
function ReturnEuro($amount, $product_name)
{
	// Currencies
	$euro = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'EURO'");
	$pund = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'GBP'");
	$sek  = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'SEK'");
	$nok  = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'NOK'");
	$usd  = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'USD'");
	$cand = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'CAD'");
	$chf  = DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'CHF'");

	// Determine currency
	if ( (strstr($product_name, 'UK') || strstr($product_name, 'GBP')) && !strstr($product_name, 'EURO') )
	{
		$amount = round($amount * $pund);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'SEK') )
	{
		$amount = round($amount * $sek);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'CHF') )
	{
		$amount = round($amount * $chf);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'NO') )
	{
		$amount = round($amount * $nok);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'USD') )
	{
		$amount = round($amount * $usd);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'US, Dollars') )
	{
		$amount = round($amount * $usd);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'US, Dollers') )
	{
		$amount = round($amount * $usd);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'Canadian Dollars') || strstr($product_name, 'CAD') || strstr($product_name, 'CAN') )
	{
		$amount = round($amount * $cand);
		$amount = round($amount / $euro);
	}
	elseif ( strstr($product_name, 'DK') )
	{
		$amount = round($amount / $euro);
	}

	return $amount;
}

function ReturnMonths($from, $to)
{
	// Check if period exceeds 2020 (JB: hack to fix the date problem with 2037 or something.)
	if ( substr($to, 0, 4) > 2020 )
	{
		$to = '2020' . substr($to, 4, 10);
	}
	if ( substr($from, 0, 4) > 2020 )
	{
		$from = '2020' . substr($from, 4, 10);
	}

	$seconds = strtotime($to) - strtotime($from);

	// Make it days
	$days = $seconds / (60*60*24);

	// Devide by 31
	$months = round($days / 31);

	// Rest
	$days = $days % 31;
	if ( $days > 15 )
	{
		$months++;
	}

	return $months;
}

// Function for generating standard html elements
{
	// Function for generating html a select box
	function returnSelectBox($selected, $select_name, $multiple = false, $sql, $sql_value, $sql_name )
	{
		// Begin select box
		$output = '
		<SELECT NAME="' . $select_name . '"' . ( $multiple ? ' MULTIPLE SIZE="7"' : '' ) . '>
			<OPTION VALUE=""> - Select - </OPTION>';

		// Generate select box for sales people
		$s_res = mysql_query($sql);
		while ( $s_row = mysql_fetch_array($s_res) )
		{
			$output .= '
			<option value="' . $s_row[$sql_value] . '"' . ( @in_array($s_row[$sql_value], $selected) ? ' selected' : '' ) . '>' . $s_row[$sql_name] .  '</option>';
		}

		// End select box
		$output .= '
		</select>';

		return $output;
	}

	function returnPageDelimeter($colspan = 1, $width = '100%', $height = 4)
	{
		return '
		<tr><td><br></td></tr>
		<tr>
			<td colspan="' . $colspan . '">
				<img src="/crmadmin/crm/sales/gfx/orangebottom.gif" width="' . $width . '" height="' . $height . '">
			</td>
		</tr>
		<tr><td><br></td></tr>';
	}
}

// Function for calculating the current forecast month for a SP
function ReturnPersonForecastMonth($person_id)
{
	// Select start month as entered
	$res = mysql_query("select * from salespeople where person_id = '" . $person_id . "' limit 1");
	$row = mysql_fetch_array($res);

	// Calculate which month we are in, based on selection
	if ( $row['forecast_start_month'] )
	{
		// Split Start date
		list($start_year, $start_month) = split('-', $row['forecast_start_month']);

		// Loop until we find todays month
		while ( 1 )
		{
			if ( date('Y-m', mktime(0, 0, 0, ($start_month+$months), 1, $start_year) ) == date('Y-m') )
			{
				return 1+$months;
			}
			else
			{
				$months++;
			}
		}
	}
	else
	{
		return 0;
	}
}

// Function for returning a MySQL array with available leads for the sales person
function returnAvailableLeadsArray()
{
	// Select all available leads
	if ( date('G') >= 8 && date('G') < 17 )
	{
		// Only select those leads the SP is "subscribed" to
		return mysql_query("SELECT * FROM website.website_leads WHERE status IS NULL && country in (" . $GLOBALS['persondata'][6] . ") ORDER BY submitted DESC");
	}
	else
	{
		// Select all available leads
		return mysql_query("SELECT * FROM website.website_leads WHERE status IS NULL ORDER BY submitted DESC");
	}
}

// Custom Secunia DB functions
{
        function DBBuildWhere($where_data)
        {
                $where = '';

                // Build up where if necessary
                if($where_data)
                {
                        if(is_string($where_data))
                        {
                                $where = " WHERE $where_data";
                        }
                        else if(is_array($where_data))
                        {
                                $where = " WHERE ";
                                foreach($where_data as $name=>$value)
                                        $where .= $name . " = '" . mysql_escape_string($value) . "' AND ";

                                // Remove extra " AND "
                                $where = preg_replace(" AND $", "", $where);
                        }
                }
                return $where;
        }

        function DBQuery($query)
        {
                return mysql_query($query);
        }

        function DBQueryGetRows($query)
        {
                $res = DBQuery($query);
                $rows = array();
                while($row = mysql_fetch_assoc($res))
                        array_push($rows, $row);
                return $rows;
        }

        function DBGetRows($table, $where='', $order='')
        {
                $query = "SELECT * FROM " . $table . DBBuildWhere($where);

                if($order)
                        $query .= " ORDER BY $order";

                $res = DBQuery($query);
                $rows = array();

                while($row = mysql_fetch_assoc($res))
                        array_push($rows, $row);

                return $rows;
        }

        function DBGetRow($table, $where='', $order='')
        {
                $query = "SELECT * FROM " . $table . DBBuildWhere($where) . ( $order ? ' ORDER BY ' . $order : '' ) . " LIMIT 1";
                return mysql_fetch_assoc(DBQuery($query));
        }

        function DBGetRowValue($table, $value, $where='')
        {
                $query = "SELECT $value FROM " . $table . DBBuildWhere($where) . " LIMIT 1";
                $row = mysql_fetch_assoc(DBQuery($query));
                return $row[$value];
        }

        function DBNumRows($table, $where='')
        {
                $query = "SELECT COUNT(*) FROM " . $table . DBBuildWhere($where);
                return mysql_result(DBQuery($query), 0, "COUNT(*)");
        }
}

// Function for getting both "Company Card" and "Case Card"
function fGetCustomerDetails( $iCustomerID ) {
        // Get entry details
        $aBaseDetails = DBGetRow('crm.cst', "cst.cst_id = '" . $iCustomerID . "'");

        // Check if there is a "Company Card" above this ID, if not, then this is the "Company Card"
        if ( $aBaseDetails['master_id'] > 0 ) {
                // Get "Customer Card" Details
                $aCCDetails = DBGetRow('crm.cst', "cst.cst_id = '" . $aBaseDetails['master_id'] . "'");

                // Return Data
                $aCustomer = array('Company' => $aCCDetails, 'Case' => $aBaseDetails);
        } else {
                $aCustomer = array('Company' => $aBaseDetails, 'Case' => NULL);
        }

        // Build list of all Customer ID's with this master
        $aCustomers = DBGetRows('crm.cst', "master_id = '" . $aCustomer['Company']['cst_id'] . "'");

        // Loop through list of customers
        $sCases = '';
        $sShadows = '';
        while ( list($iKey, $aData) = each($aCustomers) ) {
                if ( substr($aData['case_name'], 0, 5) == 'card_' ) {
                        $sCases .= $aData['cst_id'] . ',';
                } else {
                        $sShadows .= $aData['cst_id'] . ',';
                }
        }

        // Append to output
        $aCustomer['CaseIDs'] = trim($sCases, ',');
        $aCustomer['ShadowIDs'] = trim($sShadows, ',');
        $aCustomer['AllIDs'] = trim( trim( $aCustomer['CaseIDs'] . ',' . $aCustomer['ShadowIDs'], ',') . ',' . $aCustomer['Company']['cst_id'], ',');

        return $aCustomer;
}

// Function for testing a sale for being a 'Canvas' or 'Recurrence'
function fIsCanvas( $sSoldDate, $sCustomerIDs, $iProductCategory ) {
	return ( mysql_num_rows(mysql_query("select * from crm.saleslog where sold_date < '" . $sSoldDate . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $iProductCategory . "'")) == 0 ? true : false );
}

/**
 * Checks if the input is a positive integer.
 *
 * @param mixed $var String or Integer
 * @return bool
 */
function is_pint( $var ) {
	return ctype_digit( $x = strval($var) ) && $x > 0;
}