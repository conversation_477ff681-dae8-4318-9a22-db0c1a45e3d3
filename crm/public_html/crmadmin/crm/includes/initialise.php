<?php
// require functions
require(INCLUDE_PATH_LIB.'functions.php');

// require customer functions
require(INCLUDE_PATH_LIB.'customer_functions.php');

// Product Modules
// DO NOT CHANGE ORDERING OF MODULES
define('MOD_NONE',    0x0000);
define('MOD_SM',      0x0001);
define('MOD_ESM',     0x0002);
define('MOD_VTS',     0x0004);
define('MOD_VTSE',    0x0008);
define('MOD_VSS',     0x0010);
define('MOD_NSI',     0x0020);
define('MOD_BA',      0x0040);
define('MOD_VDB',     0x0080);
define('MOD_VWA',     0x0100);
define('MOD_SUPPORT', 0x0200);
define('MOD_ACCOUNT', 0x0400);

// Restrictions
$aModuleRestrictions = array
(
	MOD_BA => array(48,73)
);
$aModuleRestrictionsEnable = array
(
	MOD_NSI => array(1,2,37,43,71,74,72,75,42,46,78,79,80)
);

// Default Product to module mapping
$aDefaultModuleMappings = array(
	1    => array(
		'modules' => MOD_VTS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_SM | MOD_VTS | MOD_VSS | MOD_NSI | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	2     => array(
		'modules' => MOD_SM | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_ESM | MOD_SM | MOD_VSS | MOD_NSI | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	3    => array(
		'modules' => MOD_ESM | MOD_SM | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_ESM | MOD_SM | MOD_VSS | MOD_NSI | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	4   => array(
		'modules' => MOD_VTSE | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_VTSE | MOD_VSS | MOD_NSI | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	5    => array(
		'modules' => MOD_VSS | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_SM | MOD_VSS | MOD_NSI | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT),
	8     => array(
		'modules' => MOD_BA | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_VTSE | MOD_BA | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT),
	9    => array(
		'modules' => MOD_NSI | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_NSI | MOD_SM | MOD_VSS | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT),
);

// Open database connection
OpenDatabase();

// Get data on sales person
$persondata = GetSalesPersonData(getenv('REMOTE_USER'));
?>
