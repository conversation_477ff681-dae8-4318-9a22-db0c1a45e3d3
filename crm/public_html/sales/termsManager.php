<?php

if ( !empty( $_GET['cst_id'] ) ) {
	$cstId = (int) $_GET['cst_id'];
} else {
	exit( 'Halting. Customer ID required.' );
}

require_once( 'configuration.php' );
require_once(INCLUDE_PATH . 'sales_functions.php');
require_once(INCLUDE_PATH . 'global_functions.php');
require_once(INCLUDE_PATH . 'LicenseRestrictions.class.php');

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

$salesPerson = DBGetRow('crm.salespeople', "init = '" . getenv('REMOTE_USER') . "'");
if ( $salesPerson['profile_type_id'] != LicenseRestrictions::USER_CEC ) {
	die('No access');
}

$fileErrors[UPLOAD_ERR_INI_SIZE] = "The uploaded file exceeds the upload_max_filesize directive in php.ini";
$fileErrors[UPLOAD_ERR_FORM_SIZE] = "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form";
$fileErrors[UPLOAD_ERR_PARTIAL] = "The uploaded file was only partially uploaded";
$fileErrors[UPLOAD_ERR_NO_FILE] = "No file was uploaded";
$fileErrors[UPLOAD_ERR_NO_TMP_DIR] = "Missing a temporary folder";
$fileErrors[UPLOAD_ERR_CANT_WRITE] = "Failed to write file to disk";
$fileErrors[UPLOAD_ERR_EXTENSION] = "File upload stopped by extension";

if ( isset( $_FILES['terms_file'] ) ) {
	$file = $_FILES['terms_file'];
	if ( $file['error'] ) {
		echo 'Error while uploading a file: ' .  $fileErrors[ $file['error'] ];
	} else if( !trim( $_POST['terms_name'] ) ) {
		echo 'You must provide a name for the file.';
	} else {
		$name = trim( $_POST['terms_name'] );
		$testName = str_replace( array( '_', '-' ), '', $name );
		if ( !ctype_alnum( $testName ) ) {
			echo "Allowed characters for filename are underscore (_), dash (-) and all letters and digits.";
		} else {
			$newFileName = sha1_file( $file['tmp_name'] );
			$newFilePath = TERMS_PATH . '/' . $newFileName;
			if ( file_exists( $newFilePath ) ) {
				echo 'File already exists!';
			} else {
				if ( !move_uploaded_file( $file['tmp_name'], $newFilePath ) ) {
					echo "Could not upload file. Permissions?";
				} else {
					DBQuery( 'INSERT INTO ca.terms
						SET name = "' . mysql_real_escape_string( $name ) . '",
						path = "' . mysql_real_escape_string( $newFilePath ) . '"' );
					echo "File successfully added.";
				}
			}
		}
	}
}

$terms = DBQueryGetRows( 'SELECT id, name, path FROM ca.terms ORDER BY id DESC' );

echo '
<form action="" method="post" enctype="multipart/form-data">
	<p><input type="file" name="terms_file" size="25" /><br />
	<label for="terms_name">Enter name: <input type="text" name="terms_name" size="25" /></label>
	<br />
	<input type="submit" name="submit" value="Add new file" />
	</p>
</form>
<table>
<tr>
<th>List of Terms & Conditions</th>
<th>Action</th>
</tr>';

foreach( $terms as $row ){
	echo '<tr>
		<td><a href="../terms_and_conditions/getfile.php?id=' . $row['id'] . '">' . $row['name'] . '</a></td>
		<td><a href="#" onClick="useVerification(' . $row['id'] . ');">Assign</a></td>
	</tr>';
}
echo '</table>';

?>
<link rel="stylesheet" type="text/css" href="account_management.css" />
<script type="text/javascript">
	var useVerification = function( id ){
		var confirmation = confirm('Are you sure you want to assign selected Terms & Conditions to current customer?\n\nAny Terms that have not been accepted will be replaced with this one.');
		if ( confirmation ){
			var url = 'index.php?page=ca_account_management&action=assign_active_terms&assign_terms=' + id + '&account_id=<?= (int) $_GET['account_id'] ?>&cst_id=<?= (int) $_GET['cst_id'] ?>';
			window.open( url, '_self' );
		}
	};

	var get = function( name ){
		if( name = ( new RegExp('[?&]' + encodeURIComponent( name ) + '=([^&]*)') ).exec( location.search ) ) {
			return decodeURIComponent( name[1] );
		}
		return '';
	};
</script>
