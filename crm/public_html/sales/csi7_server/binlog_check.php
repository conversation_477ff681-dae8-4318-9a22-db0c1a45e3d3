<!DOCTYPE html>
<html>
<head>
    <title>CRM clents Module</title>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.3.1.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.2.7/js/dataTables.select.min.js"></script>
    <script type="text/javascript" src="../Editor-PHP-1.8.1/js/dataTables.editor.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/buttons.colVis.min.js"></script>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.5.4/css/buttons.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.2.7/css/select.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="../Editor-PHP-1.8.1/css/editor.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="csi7_sever.css">
</head>
<body>

<div class="container">
    <h2 id="mainTitle" class="inBlock">Binlog Check List</h2>
    <table id="my-example" class="display" style="width:100%">
        <thead>
        <tr>
            <th>Id</th>
            <th>Binlog Id</th>
            <th>Configured Table Id</th>
            <th>Check</th>
            <th>Last Id</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Id</th>
            <th>Binlog Id</th>
            <th>Configured Table Id</th>
            <th>Check</th>
            <th>Last Id</th>
        </tr>
        </tfoot>
    </table>
</div>
</body>
<script type="text/javascript">
    var editor, table; // use a global for the submit and return data rendering in the examples

    $(document).ready(function() {
        editor = new $.fn.dataTable.Editor( {
            "ajax": "binlog_checks.php",
            "table": "#my-example",

            "fields": [ {
                "label": "Id",
                "name": "id",
                "type" : 'readonly'
            },{
                "label": "Binlog Id",
                "name": "binlog_id",
                "type" : 'readonly'
            }, {
                "label": "Configured Table Id",
                "name": "configured_table_id"
            }, {
                "label": "Check",
                "name": "check"
            }, {
                "label": "Last Id",
                "name": "last_id"
            }
            ]
        } );

        table = $('#my-example').DataTable( {
            "order": [[ 0, 'asc' ]],
            "pageLength": 30,

            columnDefs: [
                { targets: [0, 1, 2, 3, 4], visible: true},
                { targets: '_all', visible: false } ],

            dom: "lBfrtip",

            ajax: {
                url: "binlog_checks.php",
                type: "POST"
            },
            serverSide: true,
            columns: [
                { data: "id" },
                { data: "binlog_id" },
                { data: "configured_table_id" },
                { data: "check" },
                { data: "last_id" },
            ],
            select: true,
            buttons: [
                'colvis',
            ]
        } );

    } );
</script>
<script type="text/javascript" src="csi7_server.js"></script>
</html>
