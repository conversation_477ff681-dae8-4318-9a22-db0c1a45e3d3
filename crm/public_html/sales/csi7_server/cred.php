<?php
// Set Credentials the way Datatables likes it.
// https://editor.datatables.net/
$config_array = parse_ini_file("csi7_server.ini",true);

$sql_details = array(
    "type" => "Mysql",    // Database type: "Mysql", "Postgres", "Sqlserver", "Sqlite" or "Oracle"
    "user" => $config_array['csi7_server']['db_user'] ? $config_array['csi7_server']['db_user'] : 'root',    // User name
    "pass" => $config_array['csi7_server']['db_pass'] ? $config_array['csi7_server']['db_pass'] : '',    // Password
    "host" => $config_array['csi7_server']['db_host'] ? $config_array['csi7_server']['db_host'] : 'localhost',    // Database server
    "port" => "",    // Database port (can be left empty for default)
    "db"   => $config_array['csi7_server']['db_name'] ? $config_array['csi7_server']['db_name'] : "csi7server_encrypted",    // Database name
    "dsn"  => "charset=utf8",    // PHP DSN extra information. Set as `charset=utf8mb4` if you are using MySQL
    "pdo"  => null   // Existing PDO connection. Use with `type` and no other parameters.
);



