$(document).ready(function() {
    var url_path = window.location.pathname;
    var url_dir = url_path.substring(0, url_path.lastIndexOf('/')) + "/";
    var url_file = url_path.substr(url_path.lastIndexOf('/') + 1);
    var links = {
        'client.php': 'Client',
        'client_ip.php': 'Client IP',
        'backup.php': 'Backup',
        'binlog_check.php': 'Binlog Check',
        'binlog.php': 'Binlog',
        'configured_table.php': 'Configured Tables',
        'process.php': 'Processes',
        'request.php': 'Requests'
    };
    delete links[url_file];
    $.each(links, function (key, value) {
        var link = "<h2 class='inBlock padLeft20'><a class='font18' href='" + url_dir + key + "'>" + value + "</a></h2>";
        $("#mainTitle").after(link);
    });
});