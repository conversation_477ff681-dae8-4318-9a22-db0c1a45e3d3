<?php
require("cred.php");
// DataTables PHP library
include( "../Editor-PHP-1.8.1/lib/DataTables.php" );

// Alias Editor classes so they are easy to use
use
    DataTables\Editor,
    DataTables\Editor\Field,
    DataTables\Editor\Format,
    DataTables\Editor\Mjoin,
    DataTables\Editor\Options,
    DataTables\Editor\Upload,
    DataTables\Editor\Validate,
    DataTables\Editor\ValidateOptions;

// Build our Editor instance and process the data coming from _POST
Editor::inst( $db , 'clients_ips', 'id' )
    ->fields(
        Field::inst( 'id' ),
        Field::inst( 'client_id' )
            ->validator( Validate::notEmpty() )->Xss( false ),
        Field::inst( 'ip' )
            ->validator( Validate::notEmpty() )->Xss( false ),
        Field::inst( 'enabled' )->Xss( false ),
        Field::inst( 'type' )->Xss( false ),
        Field::inst( 'comment' )->Xss( false ),
        Field::inst( 'created_at' )
            ->validator(
                Validate::dateFormat(
                'Y-m-d H:i:s'
            ) )
    )
    ->process( $_POST )
    ->json();