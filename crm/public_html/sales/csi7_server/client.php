<!DOCTYPE html>
<html>
<head>
    <title>CRM clents Module</title>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.3.1.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.2.7/js/dataTables.select.min.js"></script>
    <script type="text/javascript" src="../Editor-PHP-1.8.1/js/dataTables.editor.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/buttons.colVis.min.js"></script>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.5.4/css/buttons.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.2.7/css/select.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="../Editor-PHP-1.8.1/css/editor.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="csi7_sever.css">
</head>
<body>

<div class="container">
    <h2 id="mainTitle" class="inBlock">Client List</h2>
    <table id="my-example" class="display" style="width:100%">
        <thead>
        <tr>
            <th>Id</th>
            <th>Enabled</th>
            <th>Code</th>
            <th>Last Update Id</th>
            <th>Last Update Type</th>
            <th>Force Dump</th>
            <th>Force Next Dump</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Id</th>
            <th>Enabled</th>
            <th>Code</th>
            <th>Last Update Id</th>
            <th>Last Update Type</th>
            <th>Force Dump</th>
            <th>Force Next Dump</th>

        </tr>
        </tfoot>
    </table>
</div>
</body>
<script type="text/javascript">
    var editor, table; // use a global for the submit and return data rendering in the examples

    $(document).ready(function() {
        editor = new $.fn.dataTable.Editor( {
            "ajax": "clients.php",
            "table": "#my-example",

            "fields": [ {
                "label": "Id",
                "name": "id",
                "type" : 'readonly'
            },{
                "label": "Enabled",
                "name": "enabled",
                "type":  "select",
                "options": [
                    { "label": "0", "value": "0" },
                    { "label": "1",  "value": "1" }
                ]
            },{
                "label": "Code",
                "name": "code",
                "type" : 'readonly'
            },{
                "label": "Last Update Id",
                "name": "last_update_id",
                "type" : 'readonly'
            },{
                "label": "Last Update Type",
                "name": "last_update_type",
                "type" : 'readonly'
            }, {
                "label": "Force Dump",
                "name": "force_dump",
                "type":  "select",
                "options": [
                    { "label": "0", "value": "0" },
                    { "label": "1",  "value": "1" }
                ]
            }, {
                "label": "Force Next Dump",
                "name": "force_next_dump",
                "type":  "select",
                "options": [
                    { "label": "0", "value": "0" },
                    { "label": "1",  "value": "1" }
                ]
            }
            ]
        } );

        table = $('#my-example').DataTable( {
            "order": [[ 0, 'asc' ]],
            "pageLength": 30,

            columnDefs: [
                { targets: [0, 1, 2, 3, 4, 5, 6], visible: true},
                { targets: '_all', visible: false } ],


            dom: "lBfrtip",

            ajax: {
                url: "clients.php",
                type: "POST"
            },
            serverSide: true,
            columns: [
                { data: "id" },
                { data: "enabled" },
                { data: "code" },
                { data: "last_update_id" },
                { data: "last_update_type" },
                { data: "force_dump" },
                { data: "force_next_dump" }
            ],
            select: true,
            buttons: [
                'colvis',
                { extend: "edit",   editor: editor },
                'selectRows',
                'selectColumns',
                'selectNone'
            ]
        } );

        //double click edit option for data table row
        $('#my-example tbody').on( 'dblclick', 'tr', function () {
            table.rows(this).select();
            editor.edit( this, {
                title: 'Edit entry',
                buttons: 'Update'
            } );
        });

    } );

</script>
<script type="text/javascript" src="csi7_server.js"></script>
</html>
