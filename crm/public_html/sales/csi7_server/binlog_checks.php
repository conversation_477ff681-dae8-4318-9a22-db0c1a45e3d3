<?php


require("cred.php");
// DataTables PHP library
include("../Editor-PHP-1.8.1/lib/DataTables.php");

// Alias Editor classes so they are easy to use
use
    DataTables\Editor,
    DataTables\Editor\Field,
    DataTables\Editor\Format,
    DataTables\Editor\Mjoin,
    DataTables\Editor\Options,
    DataTables\Editor\Upload,
    DataTables\Editor\Validate,
    DataTables\Editor\ValidateOptions;

// Build our Editor instance and process the data coming from _POST
Editor::inst($db, 'binlog_check', 'id')
    ->fields(
        Field::inst('id'),
        Field::inst('binlog_id')->Xss(false),
        Field::inst('configured_table_id')->Xss(false),
        Field::inst('check')->Xss(false),
        Field::inst('last_id')->Xss(false)
    )
    ->process($_POST)
    ->json();