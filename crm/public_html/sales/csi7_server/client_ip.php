<!DOCTYPE html>
<html>
<head>
    <title>CRM clents Module</title>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.3.1.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.2.7/js/dataTables.select.min.js"></script>
    <script type="text/javascript" src="../Editor-PHP-1.8.1/js/dataTables.editor.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/1.5.4/js/buttons.colVis.min.js"></script>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.5.4/css/buttons.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.2.7/css/select.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="../Editor-PHP-1.8.1/css/editor.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="csi7_sever.css">
</head>
<body>

<div class="container">
    <h2 id="mainTitle" class="inBlock">Client IP List</h2>
    <table id="my-example" class="display" style="width:100%">
        <thead>
        <tr>
            <th>Id</th>
            <th>Client Id</th>
            <th>IP</th>
            <th>Enabled</th>
            <th>Type</th>
            <th>Comment</th>
            <th>Created_at</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Id</th>
            <th>Client Id</th>
            <th>IP</th>
            <th>Enabled</th>
            <th>Type</th>
            <th>Comment</th>
            <th>Created_at</th>

        </tr>
        </tfoot>
    </table>
</div>
</body>
<script type="text/javascript">
    var editor, table; // use a global for the submit and return data rendering in the examples

    $(document).ready(function() {
        editor = new $.fn.dataTable.Editor( {
            "ajax": "client_ips.php",
            "table": "#my-example",

            "fields": [ {
                "label": "Id",
                "name": "id",
                "type" : 'readonly'
            },{
                "label": "Client Id",
                "name": "client_id"
            },{
                "label": "IP",
                "name": "ip"
            },{
                "label": "Enabled",
                "name": "enabled",
                "type":  "select",
                "options": [
                    { "label": "0", "value": "0" },
                    { "label": "1",  "value": "1" }
                ]
            },{
                "label": "Type",
                "name": "type",
                "type":  "select",
                "options": [
                    { "label": "STAND_ALONE", "value": "STAND_ALONE" },
                    { "label": "RANGE", "value": "RANGE" }
                ]
            }, {
                "label": "Comment",
                "name": "comment"
            }, {
                "label": "Created At",
                "name": "Created_at",
                "type":  'datetime',
                "def":   function () { return new Date(); },
                "format":    'YYYY-MM-DD HH:mm:ss',
                "fieldInfo": 'yyyy-mm-dd hh:mm:ss date input with 24 hour clock'
            }
            ]
        } );

        table = $('#my-example').DataTable( {
            "order": [[ 0, 'asc' ]],
            "pageLength": 30,

            columnDefs: [
                { targets: [0, 1, 2, 3, 4, 5, 6], visible: true},
                { targets: '_all', visible: false } ],

            dom: "lBfrtip",

            ajax: {
                url: "client_ips.php",
                type: "POST"
            },
            serverSide: true,
            columns: [
                { data: "id" },
                { data: "client_id" },
                { data: "ip" },
                { data: "enabled" },
                { data: "type" },
                { data: "comment" },
                { data: "created_at" }
            ],
            select: true,
            buttons: [
                'colvis',
                { extend: "create", editor: editor },
                { extend: "edit",   editor: editor },
            ]
        } );

        //double click edit option for data table row
        $('#my-example tbody').on( 'dblclick', 'tr', function () {
            table.rows(this).select();
            editor.edit( this, {
                title: 'Edit entry',
                buttons: 'Update'
            } );
        });

    } );
</script>
<script type="text/javascript" src="csi7_server.js"></script>
</html>
