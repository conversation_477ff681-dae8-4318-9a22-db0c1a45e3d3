<?php
require("cred.php");
// DataTables PHP library
include( "../Editor-PHP-1.8.1/lib/DataTables.php" );

// Alias Editor classes so they are easy to use
use
    DataTables\Editor,
    DataTables\Editor\Field,
    DataTables\Editor\Format,
    DataTables\Editor\Mjoin,
    DataTables\Editor\Options,
    DataTables\Editor\Upload,
    DataTables\Editor\Validate,
    DataTables\Editor\ValidateOptions;

// Build our Editor instance and process the data coming from _POST
Editor::inst( $db , 'clients', 'id' )
    ->fields(
        Field::inst( 'id' ),
        Field::inst( 'enabled' )->Xss( false ),
        Field::inst( 'code' )->Xss( false ),
        Field::inst( 'last_update_id' )->Xss( false ),
        Field::inst( 'last_update_type' )->Xss( false ),
        Field::inst( 'force_dump' )->Xss( false ),
        Field::inst( 'force_next_dump' )->Xss( false )
    )
    ->process( $_POST )
    ->json();