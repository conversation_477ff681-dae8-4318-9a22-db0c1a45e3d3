<?php
// Get subsegments
function getSubsegments( $segmentId, &$result = null ) {
	$segments = DBQueryGetRows( "SELECT segment_id, ( SELECT COUNT(*) FROM crm.segments WHERE parent_segment_id = A.segment_id ) AS children FROM crm.segments AS A WHERE parent_segment_id = '".(int)$segmentId."'" );
	if ( $result == null ) {
		$array = explode( ",", $segmentId );
		foreach ( $array as &$value ) {
			$value = intval( $value );
		}
		$result = $array;
	}
	for ( $i = 0; $i < count( $segments ); $i++ ) {
		$result[] = $segments[$i]['segment_id'];
		if ( $segments[$i]['children'] > 0 ) {
			getSubsegments( $segments[$i]['segment_id'], $result );
		}
	}

	return $result;
}

function arrayToSqlIn( $array ) {
	$sqlIn = "";
	reset( $array );
	while ( key( $array ) !== null ) {
		$sqlIn .= ( $sqlIn != "" ? "," : "" ) . (int) current( $array );
		next( $array );
	}
	return $sqlIn;
}

// Function for opening DB connection
function fOpenDatabase()
{
	$conn = mysql_connect(DB_HOST_CRM, DB_USER_CRM, DB_PASS_CRM);
	mysql_select_db(DB_NAME_CRM);
	return $conn;

}

function getChildren( $accountId ) {
	$childIds = array();
	$result = DBGetRows("ca.accounts", "account_esm = '".(int)$accountId."'");

	foreach( $result as $row ) {
		$childIds[] = $row['account_id'];
		foreach( getChildren( $row['account_id'] ) as $childAccountId ) {
			$childIds[] = $childAccountId;
		}
	}

	return $childIds;
}

function returnSelectBox( $selected, $select_name, $multiple, $sql, $sql_value, $sql_name, $extra = '' ) {
	// Begin select box
	$output = '
	<SELECT '.$extra.' NAME="' . $select_name . '"' . ( $multiple ? ' MULTIPLE SIZE="7"' : '' ) . '>
		<OPTION VALUE=""> - Select - </OPTION>';

	// Generate select box for sales people
	$s_res = mysql_query($sql);
	while ( $s_row = mysql_fetch_array($s_res) )
	{
		$output .= '
		<option value="' . $s_row[$sql_value] . '"' . ( @in_array($s_row[$sql_value], $selected) ? ' selected' : '' ) . '>' . $s_row[$sql_name] .  '</option>';
	}

	// End select box
	$output .= '
	</select>';

	return $output;
}

function segmentCombo( $selected_parent_segment_id, $parent_segment_id = "", $space = "" ) {
	$res = "";

	if ( $parent_segment_id != "" ) {
		$parent_segment_id = " AND parent_segment_id = '".(int)$parent_segment_id."'";
	} else {
		$parent_segment_id = " AND ( parent_segment_id is NULL OR parent_segment_id = 0 )";
		$res = "<option value='0'>- Select Segment -</option>";
	}

	$result = DBQueryGetRows("SELECT segment_name, segment_id, parent_segment_id, (SELECT COUNT(segment_id) FROM crm.segments WHERE parent_segment_id = A.segment_id AND segment_name like \"crm2 - %\" AND segment_status = 0 ) AS children FROM crm.segments AS A WHERE segment_name like \"crm2 - %\" AND segment_status = 0 ".$parent_segment_id." ORDER by segment_name ASC");

	for ( $i = 0; $i < count( $result ); $i++ ) {
		$selected = "";
		if ( $result[$i]['segment_id'] == $selected_parent_segment_id ) {
			$selected = " selected ";
		}
		$res .= "<option ".$selected." value='".$result[$i]['segment_id']."'>".$space.htmlspecialchars( str_replace( "CRM2 - ", "", $result[$i]['segment_name'] ) )."</option>";
		if ( $result[$i]['children'] > 0 ) {
			$res .= segmentCombo( $selected_parent_segment_id, $result[$i]['segment_id'], $space." |_" );
		}
	}

	return $res;
}

function makeSegmentCombo( $selected_parent_segment_id = "", $js = "", $name = "parent_segment_id" ) {
	return  "<select ".$js." name='".$name."'>" . segmentCombo( $selected_parent_segment_id ) . "</select>";
}

// Function for "calculating" the account_status for a customer
function fCalculateAccountStatus( $iCSTID, $aCustomer = null, $isCSI = false ) {
	// Get customer details
	if ( !$aCustomer ) {
		$aCustomer = fGetCustomerDetails( $iCSTID );
	}

	// Any sales on customer
	$aSales = DBGetRows('crm.saleslog', "cst_id IN(" . $aCustomer['AllIDs'] . ") AND (product_price-discount) > 0 AND (status IS NULL || status != 3)" . ( $isCSI ? ' && product_category = 3' : '' ));
	if ( $aSales ) {
		// Sales: Customer or Lost Customer
		foreach($aSales as $aSale) {
			if ( strtotime($aSale['expires_date']) > time() ) {
				return 'Customer';
			}
		}

		return 'Lost Customer';
	} else {
		// No sales: Lead or Prospect
		if ( DBNumRows('crm.cst', "cst.case_name IN('card_vi', 'card_nsi', 'card_crc', 'card_partner', 'card_csc') AND cst_id IN (" . $aCustomer['AllIDs'] . ") AND person_id > 0") > 0 ) {
			return 'Prospect';
		} else {
			return 'Lead';
		}
	}
}

// Function to get owner of a case
function fGetCaseOwner( $iCaseID ){
	$aCaseRow = DBGetRow('crm.cst', "cst.cst_id = '" . $iCaseID . "'");
	return $aCaseRow['person_id'];
}

// Function for returning lead source name
function fReturnLeadSource( $iID ) {
	return $GLOBALS['aLeadSources'][$iID];
}

// Function for receiving master ID
function fGetMasterID($iCstID){
	$rQuery = mysql_query("SELECT master_id FROM crm.cst WHERE cst_id=".$iCstID);
	$aData = mysql_fetch_array($rQuery, MYSQL_ASSOC);
	if (is_numeric($aData['master_id'])){
		//If this is not final master ID
		$iReturnID = fGetMasterID($aData['master_id']);
	} else {
		$iReturnID = $iCstID;
	}
	return $iReturnID;
}

// Function to get strings to be nice
function fCutOff($sString, $iLength){
	if (strlen($sString) <= $iLength){
		return $sString;
	} else {
		return substr($sString, 0, $iLength)."...";
	}
}

// Function for getting detail from sales rep. based on person id. Defaults to name.
function fReturnRepDetailFromID( $iPersonID, $sField = 'name' )
{
	$aRow = DBGetRow('crm.salespeople', "salespeople.person_id = '" . $iPersonID . "'");
	return $aRow[$sField];
}

// Function for changing segment of a card
function fSelectChangeSegment($iSegmentID, $iCstID){
	$aSegments = DBGetRows('crm.segments', "segments.segment_name LIKE 'CRM2%' AND segments.segment_name NOT LIKE '% - Lost Leads'", 'segments.segment_name');
	$sReturn = '<select onchange="location = \'?page=customer&cst_id='.$iCstID.'&action=new_segment&new_segment=\' + this.value;">
			<option value="">- Select Segment -</option>';
	foreach($aSegments as $aSegment){
		if ($aSegment['segment_id'] == $iSegmentID){
			$sReturn .= '<option value="'.htmlspecialchars($aSegment['segment_id']).'" selected>'.htmlspecialchars(str_replace('CRM2 - ', '', $aSegment['segment_name'])).'</option>';
		} else {
			$sReturn .= '<option value="'.htmlspecialchars($aSegment['segment_id']).'">'.htmlspecialchars(str_replace('CRM2 - ', '', $aSegment['segment_name'])).'</option>';
		}
	}
	$sReturn .= '</select>';
	return $sReturn;
}

// Function for changing owner of a card
function fSelectChangeOwner( $iPersonID, $iCSTID, $iMasterID, $iDepartment = "" ) {
	// Begin select
	echo '<select onchange="location = \'?page=customer&cst_id=' . $iMasterID . '&action=new_owner&new_owner=\' + this.value + \'&case_id=' . $iCSTID . '\';"><option value="">- No Owner / Remove Owner -</option>';

	$where = "";
	if ( $iDepartment != "" ) {
		$where = "department = '".(int)$iDepartment."'";
	}

	// Select all salesreps
	$aReps = DBGetRows('salespeople', $where, 'name');
	foreach ($aReps as $aRep) {
		echo '<option value="' . $aRep['person_id'] . '"' . ( $aRep['person_id'] == $iPersonID ? ' selected' : '' ) . '>' . htmlspecialchars($aRep['name']) . '</option>';
	}
	echo '</select>';
}

// Function for checking if a canvas lead should be displayed or not
function fSkipCanvasLead( $aData, $aRepData ) {
	if ( isISA( $aRepData['person_id'] ) ){ // For isa remove those canvases that have card_nsi person_id
		$iAssignedPerson = DBGetRowValue( "crm.cst", "person_id", "master_id = '".$aData['cst_id']."' AND case_name = 'card_nsi'" );
		if ( $iAssignedPerson != "" ){
//			if ( ( $iAssignedPerson != $aRepData['person_id'] ) && ( $iAssignedPerson != 0 ) ){
				return true;
//			}
		}
	}

	$aActive = DBGetRows('crm.cst', "(cst.master_id = '" . $aData['cst_id'] . "' || cst.master_id = '" . $aData['master_id'] . "') AND case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc') AND cst.person_id = '" . $aRepData['person_id'] . "' AND cst.appointment");
	if ( $aActive ) {
		return true;
	}

/*	// Don't display lead if rep. has marked it as "Lost"
	$aCustomer = fGetCustomerDetails( $aData['cst_id'] );
	$aLost = DBGetRows('crm.lost_log', "lost_log.cst_id IN(" . $aCustomer['CaseIDs'] . ") AND lost_log.person_id = '" . $aRepData['person_id'] . "'");
	if ( $aLost ) {
		return true;
	}*/

	return false;
}

// Function for calculating the current forecast month for a SP
function fReturnPersonForecastMonth($iPersonID) {
	// Default start date
	$row['forecast_start_month'] = '2007-01';

	// Calculate which month we are in, based on selection
	if ( $row['forecast_start_month'] ) {
		// Split Start date
		list($start_year, $start_month) = explode('-', $row['forecast_start_month']);

		// Loop until we find todays month
		$months = 0;
		while ( 1 ) {
			if ( date('Y-m', mktime(0, 0, 0, ($start_month+$months), 1, $start_year) ) == date('Y-m') ) {
				return 1+$months;
			} else {
				$months++;
			}
		}
	} else {
		return 0;
	}
}

// Function for returning the status of a case
function fReturnCaseStatusName( $iStatus, $iExpectancy, $iForecastAmount, $sForecastExpected, $sAppointment ) {
	switch ( $iStatus ) {
		case '3': // Won
			return 'Won';
		case '4': // Lost
			return 'Lost';
		default: // In progress || Not approached
			if ( $iExpectancy || $sAppointment ) {
				// Forecast ?
				if ( $iExpectancy >= 40 ) {
					// Days until expected
					$iDays = round( (strtotime($sForecastExpected) - time()) / 86400 );

					return intval($iExpectancy) . '% / &euro; ' . number_format($iForecastAmount) . ( $sForecastExpected != '0000-00-00' ? ' / ' . ( $iDays < 0 ? '<font color="red">' : '' ) . 'within ' . $iDays  . ' days' : '' );
				} else {
					return intval($iExpectancy) . '%';
				}
			} else {
				return 'Not approached';
			}
	}
}

function fReturnCaseInitials( $sCardName )
{
	switch ( $sCardName )
	{
		case 'card_vi':
			return 'VI';
		case 'card_nsi':
			return 'CSI';
		case 'card_sv':
			return 'SV';
		case 'card_crc':
			return 'CRC';
		case 'card_csc':
			return 'CSC';
		default:
			return 'CC';
	}
}

// Function for returning name of lost reason
function fReturnLostReason( $iReason )
{
	switch ( $iReason )
	{
		case 1:
			return 'No contact identified';
		case 2:
			return 'Too small for CSI';
		case 3:
			return 'Too small for TLA';
		case 4:
			return 'Give to TLA';
		case 5:
			return 'Qualified; no budget';
		case 6:
			return 'Using a competing product';
		case 7:
			return 'Qualified; no decision power';
		case 8:
			return 'Outsourced';
		case 9:
			return 'No access to decision maker';
		case 10:
			return 'On price';
		case 11:
			return 'To competitor';
		case 12:
			return 'Give to other salesrep';
		case 13:
			return 'Other reasons';
		case 14:
			return 'Inbound; No contact';
		case 15:
			return 'Inbound; Company not qualified';
		case 16:
			return 'Inbound; Contact not qualified';
		case 17:
			return 'CP - Lack of Awareness';
		case 18:
			return 'Bankrupt';
		case 19:
			return 'Reseller';
		case 20:
			return 'Not using Windows';
		case 21:
			return 'Holding company';
		case 22:
			return 'Not interested';
		case 23:
			return 'Language problem';
		case 24:
			return 'Duplicate card';
		case 25:
			return 'Too small';
		case 26:
			return 'Recycle';
		case 27:
			return 'Objection';
		case 28:
			return 'Too small for Partners';
		case 29:
			return 'Lost to VI Large Accounts 2011';
		case 30:
			return 'Lost to CSI New-bizz 2011';
	}
}

// Function for returning the product category
function fReturnProductCategory( $iProductType ) {
	switch ( $iProductType ) {
		// SVP
		case 12:
		case 13:
		case 14:
		case 15:
		case 16:
		case 21:
			return 4;

		// SS
		case 5:
			return 2;

		// CSI (NSD/NSI)
		case 9:
		case 6:
		case 0:
		case 17:
		case 18:
		case 19:
		case 20:
		case 200:
		case 201:
		case 202:
		case 203:
		case 204:
		case 205:
		case 206:
		case 207:
			return 3;

		// VI
		case 1:   // VTS
		case 2:   // VM
		case 3:   // EVM
		case 4:   // VIF
		case 127: // VIF (SVP)
		case 11:  // EVM-S
		default:  // Default to VI
			return 1;
	}
}

// Function for verifying access to privileged pages
// 4:	Admin
// 3:	Sales Management
// 2:	Lead Management
// 1:	Full Search
function fVerifyAccess( $iPersonID, $iAccessLevelRequired ) {
	// Access
	require("user_access.php");

	// Determine access
	$bAllowed = false;
	if ( empty( $aUserAccess[$iPersonID] ) ) {
		// NA
		$bAllowed = false;
	} elseif ( $aUserAccess[$iPersonID] >= $iAccessLevelRequired ) {
		// OK
		$bAllowed = true;
	}

	// Allowed
	if ( $bAllowed == true ) {
		return true;
	} else {
		return false;
	}
}

// Function for adding link functionality to table row
function fRowLink( $sTarget, $sFrame = 'main' )
{
	return
	' onMouseOver="this.style.background=\'#E0EDF5\'; document.body.style.cursor=\'pointer\';"' .
	' onClick="try { parent.frames[\'' . $sFrame . '\'].location = \'' . $sTarget . '\'; } catch (e) { location=\'' . $sTarget . '\'; }"' .
	' onMouseOut="this.style.background=\'\'; document.body.style.cursor=\'\';"';
}

// Function to include and wrap a "Customer Card" box
function fIncludeCardBox( $sName, $sFile )
{
	// Start "<Fieldset>" and display "</Legend>"
	echo '
	<fieldset>
		<legend>' . $sName .' </legend>';

	// Include box content
	include INCLUDE_PATH . $sFile;

	// End "</Fieldset>"
	echo '</Fieldset>';
}

function fSegmentNameFromID( $iSegmentID ) {
	$aSegment = DBGetRow('crm.segments', "segment_id = '" . $iSegmentID . "'");

	// Return segment name
	if ( $aSegment['segment_name'] ) {
		return str_replace('CRM2 - ', '', $aSegment['segment_name']);
	} else {
		return 'n/a';
	}
}

// More detailed function for returning the region according to BI
function fReturnRegionDetailed( $cstID ) {
	return DBGetRowValue('crm.cst, crm.countries', 'region_detailed', "cst_id = '" . $cstID . "' && cst.invoice_country = countries.id");
}

// Return name of region
function fReturnRegionName( $iRegion )
{
	switch ( $iRegion )
	{
		case 1:
			return 'Europe';
		case 2:
			return 'America';
		case 3:
			return 'Asia';
		case 4:
			return 'Africa';
		case 5:
			return 'M.E.'; // Middle East
		case 6:
			return 'L.A.'; // Latin America
		default:
			return 'n/a';
	}
}

// Return name of region from country id
function fReturnRegionNameFromCountryID ( $iCountryID )
{
	$aCountry = DBGetRow('crm.countries', "id = '" . $iCountryID . "'");

	return fReturnRegionName( $aCountry['region'] );
}

// Build segment drop down list
function fBuildSegmentDD() {
	$sSegments = '<option value="0" '.( $_GET['segment_id'] == 0 || $_GET['segment_id'] == "" || (int)$_GET['segment_id'] == 0 ? 'selected' : '' ).' >- Select Segment -</option>';
	$aSegments = DBGetRows('crm.segments', "segments.segment_status = 0 AND segments.segment_name LIKE 'CRM2%' AND segments.segment_name NOT LIKE '% - Lost Leads'", 'segment_name');
	foreach ($aSegments  as $aSegment) {
		$aSegment['segment_name'] = str_replace('CRM2 - ', '', $aSegment['segment_name']);

		$sSegments .= '<option value="' . (int)$aSegment['segment_id'] . '" '.( $_GET['segment_id'] == $aSegment['segment_id'] ? 'selected' : '' ).' >' . htmlspecialchars($aSegment['segment_name']) . '</option>';
	}

	return $sSegments;
}

function fCountryNameFromID( $iCountryID )
{
	$aCountry = DBGetRow('crm.countries', "id = '" . $iCountryID . "'");

	// Return segment name
	if ( $aCountry['country'] )
	{
		return $aCountry['country'];
	}
	else
	{
		return 'No country';
	}
}

// Function for converting any currency to euro
function fConvertToEuro( $iAmount, $sCurrency ) {
	// Look up currency
	$aData = DBGetRow('crm.country_currency', "country_currency.currency_name = '" . $sCurrency . "'");

	// Return euro
	return round(($iAmount * $aData['currency']) / CURRENCY_EURO);
}

// Function for getting both "Company Card" and "Case Card"
function fGetCustomerDetails( $iCustomerID ) {
	// Get entry details
	$aBaseDetails = DBGetRow('crm.cst', "cst.cst_id = '" . $iCustomerID . "'");

	// Check if there is a "Company Card" above this ID, if not, then this is the "Company Card"
	if ( $aBaseDetails['master_id'] > 0 ) {
		// Get "Customer Card" Details
		$aCCDetails = DBGetRow('crm.cst', "cst.cst_id = '" . $aBaseDetails['master_id'] . "'");

		// Return Data
		$aCustomer = array('Company' => $aCCDetails, 'Case' => $aBaseDetails);
	} else {
		$aCustomer = array('Company' => $aBaseDetails, 'Case' => NULL);
	}

	//Suppress the errors for no records
	if (!isset($aCustomer['Company']['cst_id'])) {
		$aCustomer['Company']['cst_id'] = 0;
	}
	// Build list of all Customer ID's with this master
	$aCustomers = DBGetRows('crm.cst', "master_id = '" . $aCustomer['Company']['cst_id'] . "'");

	// Loop through list of customers
	$sCases = '';
	$sShadows = '';
	foreach($aCustomers as $aData) {
		if ( substr($aData['case_name'], 0, 5) == 'card_' ) {
			$sCases .= $aData['cst_id'] . ',';
		} else {
			$sShadows .= $aData['cst_id'] . ',';
		}
	}

	// Append to output
	$aCustomer['CaseIDs'] = trim($sCases, ',');
	$aCustomer['ShadowIDs'] = trim($sShadows, ',');
	$aCustomer['AllIDs'] = trim( trim( $aCustomer['CaseIDs'] . ',' . $aCustomer['ShadowIDs'], ',') . ',' . $aCustomer['Company']['cst_id'], ',');

	return $aCustomer;
}

// Function for returning online sale status
function fOnlineSaleStatus( $iStatus, $sOnlineID ) {
        switch ( $iStatus ) {
                case 1:
                        return 'Confirmed / Approved';
                case 3:
                        return 'Refunded / Cancelled';
                default:
                        return 'Awaiting';
        }
}


// Function for returning the status of a sale
function fSaleStatus( $iStatus, $sOnlineID = 0, $iProductCategory = 0, $iSaleID = 0 )
{
	switch ( $iStatus )
	{
		case 2:
			return 'Paid';
		case 3:
			return 'Cancelled';
		case 4:
			return '<span style="color: #DD0000;">Rejected (CRC)</span>';
		case 5:
			return '<span style="color: #009900;">Approved (CRC)</span>';
		case 6:
			return 'Cancelled - Wrong registration';
		case 7:
			return 'Cancelled - Invoice sent';
		default:
			// Allow user to export the sale to the online shop
			if ( $iProductCategory == 3 && !$sOnlineID ) {
				return '<a href="?page=customer&amp;cst_id=' . intval($_GET['cst_id']) . '&export_online_sale=' . $iSaleID . '" onClick="return confirm(\'Do you wish to prepare this sale for online payment through our shop?\');">Awaiting</a>';
			} else {
				return 'Awaiting' . ( $sOnlineID ? ' (<a href="https://shop.secunia.com/?page=crmsale&pmnt_id=' . $sOnlineID . '" target="_blank" title="Link to online payment of this sale, send this link to your customer">ONL</a>)' : '' );
			}
	}
}

// Function for calculating the approx. number of months between two dates
function fDifferenceMonths( $sFrom, $sTo )
{
	// Check if period exceeds 2020 (JB: hack to fix the date problem with 2037 or similar.)
	if ( substr($sTo, 0, 4) > 2020 )
	{
		$sTo = '2020' . substr($sTo, 4, 10);
	}
	if ( substr($sFrom, 0, 4) > 2020 )
	{
		$sFrom = '2020' . substr($sFrom, 4, 10);
	}

	$iSeconds = strtotime($sTo) - strtotime($sFrom);

	// Make it days
	$iDays = $iSeconds / (60*60*24);

	// Devide by 31
	$iMonths = round($iDays / 31);

	// Rest
	$iDays = $iDays % 31;
	if ( $iDays > 15 )
	{
		$iMonths++;
	}

	return $iMonths;
}


// Custom Secunia DB functions
{
	function DBBuildWhere($where_data)
	{
		$where = '';

		// Build up where if necessary
		if($where_data)
		{
			if(is_string($where_data))
			{
				$where = " WHERE $where_data";
			}
			else if(is_array($where_data))
			{
				$where = " WHERE ";
				foreach($where_data as $name=>$value)
					$where .= $name . " = '" . mysql_escape_string($value) . "' AND ";

				// Remove extra " AND "
				$where = preg_replace("/ AND $/", "", $where);
			}
		}
		return $where;
	}

	function DBQuery($query)
	{
		$res = mysql_query($query);
		if ( !$res ) {
			// throw new Exception( 'DBQuery Error : ' . mysql_error() );
		}
		return $res;
	}

	function DBQueryGetRows($query)
	{
		$res = DBQuery($query);
		$rows = array();
		while($row = mysql_fetch_assoc($res))
			array_push($rows, $row);
		return $rows;
	}

	function DBGetRows($table, $where='', $order='')
	{
		$query = "SELECT * FROM " . $table . DBBuildWhere($where);

		if($order)
			$query .= " ORDER BY $order";

		if (getenv('REMOTE_USER') == "stimmerman"){
			$f = fopen("/tmp/log", "a+");
			fputs($f, $query."\n");
			fclose($f);
		}

		$res = DBQuery($query);
		$rows = array();

		while($row = mysql_fetch_assoc($res))
			array_push($rows, $row);

		return $rows;
	}

	function DBGetRow($table, $where='', $order='')
	{
		$query = "SELECT * FROM " . $table . DBBuildWhere($where) . ( $order ? ' ORDER BY ' . $order : '' ) . " LIMIT 1";
		return mysql_fetch_assoc(DBQuery($query));
	}

	function DBGetRowValue($table, $value, $where='')
	{
		$query = "SELECT $value FROM " . $table . DBBuildWhere($where) . " LIMIT 1";
		$row = mysql_fetch_array(DBQuery($query));
		return $row ? $row[0] : 0; //Safer to return 0 than null
	}

	function DBNumRows($table, $where='')
	{
		$query = "SELECT COUNT(*) FROM " . $table . DBBuildWhere($where);
		return mysql_result(DBQuery($query), 0, "COUNT(*)");
	}
}

//Sort array, make sure the value "Other" is last
function fSortArrayOtherCB($sValue){
	if (stristr($sValue, "other")){
		return false;
	} else {
		return true;
	}
}

function fSortArrayOther($aList){
	$aNewList = array_filter($aList, "fSortArrayOtherCB");
	$aOther = array_diff_key($aList, $aNewList);
	asort($aNewList);
	$aReturn = $aNewList+$aOther;
	return $aReturn;
}

function fnGetCustomerIDs($iCstID){
	$iMasterID = fGetMasterID($iCstID);
	$rQuery = mysql_query("SELECT cst_id FROM crm.cst WHERE master_id='".$iMasterID."'");
	$aCustomerIDs[] = $iMasterID;
	while($aRow = mysql_fetch_assoc($rQuery)){
		$aCustomerIDs[] = $aRow['cst_id'];
	}
	return $aCustomerIDs;
}

// Function for testing a sale for being a 'Canvas' or 'Recurrence'
function fIsCanvas( $sSoldDate, $sCustomerIDs, $iProductCategory ) {
	return ( mysql_num_rows(mysql_query("select * from crm.saleslog where sold_date < '" . $sSoldDate . "' && cst_id IN(" . trim($sCustomerIDs, ',') . ") && (product_price - discount) > 0 && status = 2 && product_category = '" . $iProductCategory . "'")) == 0 ? true : false );
}

// Function for testing is user is ISA
function isISA( $iPersonId ){
	$iPersonDepartment = DBGetRowValue("crm.salespeople", "department", "person_id = '".(int) $iPersonId."'");
	$iPersonLevel = DBGetRowValue("crm.salespeople", "person_level", "person_id = '".(int) $iPersonId."'");

	if ( ( $iPersonLevel == 3 ) && ( $iPersonDepartment == 2 ) ){
		return true; // Person is ISA
	} else {
		return false;
	}
}

// ISA persons should only be able to assign a maximum of 30 days for an appointment
function checkISADate( $sDate ){
	// Format date
	$sDate = trim( $sDate );
	$sTimeStamp = strtotime( $sDate );

	// Now + 30 days
	$sFutureStamp = strtotime('+30 days');

	// Check it
	if ( $sTimeStamp > $sFutureStamp ){
		return false; // It is abote 30 days
	} else {
		return true; // It is alright
	}
}

// Determine if client has CSI Trial accounts
function hasCSITrial( $aCSTIDs ){
	$aSales = DBQuery("SELECT COUNT(*) as NB FROM saleslog WHERE cst_id IN (". $GLOBALS['aCustomer']['AllIDs'] .") AND product_trial = 1 AND product_type = 9");
	$aRow = mysql_fetch_array( $aSales );
	if (  $aRow['NB'] > 0 ){
		return true;
	} else {
		return false;
	}
}

// Determine if the current ISA user has reached the maximum number of assigned leads
function checkMoreLeads( $iPersonId ){
	$iLimit = 500; // Maximum number of leads assigned to current user AND with an appointment
	$sQuery = "SELECT
			COUNT(*) AS NB
		FROM
			crm.cst
		WHERE
			cst.case_name IN ('Company Card', 'card_vi', 'card_nsi', 'card_partner', 'card_crc') AND
			cst.person_id = '" . $iPersonId . "' AND
			cst.appointment IS NOT NULL";
	$rResult = DBQuery( $sQuery );
	$aRow = mysql_fetch_array( $rResult );
	if ( $aRow['NB'] >= $iLimit ){
		return false;
	} else {
		return true;
	}
}


/**
 * Checks if the iput is a positive integer.
 *
 * @param mixed $var String or Integer
 * @return bool
 */
function is_pint( $var ) {
	return ctype_digit( $x = strval($var) ) && $x > 0;
}