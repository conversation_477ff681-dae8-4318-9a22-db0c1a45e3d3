<link rel="stylesheet" type="text/css" href="account_management.css" />
<?php

if ( !empty( $_GET['cst_id'] ) ) {
	$cstId = (int) $_GET['cst_id'];
} else {
	exit( 'Halting. Customer ID required.' );
}

require_once('TermsAndConditions.php');
require_once( 'configuration.php' );
require_once(INCLUDE_PATH . 'sales_functions.php');
require_once(INCLUDE_PATH . 'global_functions.php');

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

$terms = new TermsAndConditions( $cstId );
$logs = $terms->getLogs();

echo '<table>
<tr>
<th>Date</th>
<th>Username</th>
<th>IP Address</th>
<th>Terms</th>
<th>Action</th>
</tr>';

foreach( $logs as $row ){
	switch( $row['action'] ){
		case TermsAndConditions::ACTION_ACCEPTED:
			$action = 'Accepted';
			break;
		case TermsAndConditions::ACTION_VIEWED:
			$action = 'Viewed';
			break;
		default:
			$action = 'Unknown';
			break;
	}

	echo '<tr>
		<td>' . $row['created_at'] . '</td>
		<td><a href="index.php?page=ca_account_management&account_id=' . $row['account_id'] . '&cst_id=' . $cstId . '&iframe=1" target="_blank">' . $row['account_username'] . '</a></td>
		<td>' . $row['ip_address'] . '</td>
		<td><a href="../terms_and_conditions/getfile.php?id=' . $row['terms_id'] . '">' . $row['name'] . '</a></td>
		<td>' . $action . '</td>
	</tr>';
}
echo '</table>';