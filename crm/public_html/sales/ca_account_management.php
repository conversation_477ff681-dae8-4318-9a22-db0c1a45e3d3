<?php
require_once('CustomerState.class.php');
require_once('LicenseRestrictions.class.php');
require_once('TermsAndConditions.php');
require_once('csi7MspLicense.php');

// Set vars
$iVirus = 0;
$iAdvisory = 0;
$sUpdate = '';
$sPassword = '';
$iSuccess = 0;
$sComment = '';
$bAccountsUpdate = FALSE;
$bUsernameFailed = FALSE;
$bAdvisoryFailed = FALSE;
$bVirusFailed = FALSE;
$bESMFailed = FALSE;
$iModulesVisible = 0;
$iModulesEnabled = 0;
$iOptionsEnabled = 0;

const OTHER_OPTIONS_ATTACH_PDF = 0x0001;

// Set account_id
if ( !empty( $_GET['account_id'] ) ) {
	$iAccountID = $_GET['account_id'];
} elseif ( !empty( $_POST['account_id'] ) ) {
	$iAccountID = $_POST['account_id'];
} else {
	exit( 'Halting. Account ID required.' );
}

// Select 'account' and 'customer' details
$aAccountCustomer = DBGetRow('ca.accounts, crm.cst', "accounts.account_id = '" . $iAccountID . "' AND cst.cst_id = accounts.cst_id");
if (empty($aAccountCustomer)) {
	echo "No data for given account_id";
	exit();
}
$license = new csi7MspLicense();
$salesforceId = $license->fixSalesforceId( array( 0 => array( 'cst_id' => (int) $aAccountCustomer['cst_id'] ) ) );
$salesforceId = $salesforceId[0]['salesforce_id'];

$customer = DBGetRow( 'ca.customers', "id = " . (int) $aAccountCustomer['cst_id'] );
// Determine if CSI-5 is an option
// JB: Edit here, remove if and iDevices and make isCSI5Available default to true
$isCSI5Available = true;
//$iDevices = DBNumRows('ca.nsi_devices', "account_id = '" . (int) $iAccountID . "'");
//if ( !$iDevices && ( !$aAccountCustomer['special_limits'] || $aAccountCustomer['special_limits'] == 'csi_50' ) ) {
//	$isCSI5Available = true;
//}

function subAccountsSqlIn( $iAccountID ) {
	// Build the VIM stats.

	$children = getChildren( $iAccountID );

	if ( false === $children ) {
		return (int)$iAccountID;;
	}
	$sqlIn = implode( ",", $children );
	$sqlIn .= ( $sqlIn == "" ? "" : ", " ).(int)$iAccountID;

	return $sqlIn;
}

if ( isset($_GET['type']) && $_GET['type'] == 'vim_stats' ) {
	$sqlIn = subAccountsSqlIn( $iAccountID );
	$assetLists = DBNumRows("ca.vi_assets", "account_id IN ( ".$sqlIn." )");
	$recipients = DBNumRows("ca.contact_method", "account_id IN ( ".$sqlIn." ) AND contact_method_type = 1");
	echo "<br>Sub accounts: " . ( count( $children ) - 1 );
	echo "<br>Configured Asset List(s) - including sub accounts: " . $assetLists;
	echo "<br>Configured Recipient(s): " . $recipients;

	die();
}

// Modular Product Structure Setup
{
	// Modules
	$aModules = array(
		"Vulnerability Intelligence Feed (VIF) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_VTSE,
		"Enterprise Vulnerability Manager (EVM) / Management and Administration in VIM 4.x &amp; VIM 3.x" => MOD_ESM,
		"User Management (UM)" => MOD_UM,
		"Vulnerability Manager (VM) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_SM,
		"Vulnerability Tracking (VTS) / Vulnerability Manager in VIM 4.x &amp; VIM 3.x" => MOD_VTS,
		"Surveillance Scanner (SS)" => MOD_VSS,
		"Corporate Software Inspector (CSI)" => MOD_NSI,
		"Binary Analysis (BA) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_BA,
		"Vulnerability Database</b> (default module)" => MOD_VDB,
		/* "Technical Support</b> (default module)" => MOD_SUPPORT, */
		"Account Information</b> (default module) / <b>N/A</b> in VIM 4.x &amp; VIM 3.x" => MOD_ACCOUNT
	);
}

// Only CEC people can change modules
$iModulesEnabled = $iModulesVisible = false;
$salesPerson = DBGetRow('crm.salespeople', "init = '" . getenv('REMOTE_USER') . "'");

// if ( $salesPerson['profile_type_id'] == LicenseRestrictions::USER_CEC ) {
	if ( !empty($_POST['modules_enabled']) || !empty($_POST['modules_visible']) ) {
		// Loop through modules Enabled
		foreach( $aModules as $sModuleName => $iModuleValue ) {
			// Surveillance Scanner can only be disabled, not enabled
			if ( $iModuleValue != MOD_VSS || ($aAccountCustomer['modules'] & $iModuleValue) ) {
				// Enabled Modules
				if ( isset($_POST['modules_enabled'][$iModuleValue]) && $_POST['modules_enabled'][$iModuleValue] == 1 ) {
					$iModulesEnabled |= $iModuleValue;
				}

				// Displayed Modules
				if ( isset($_POST['modules_visible'][$iModuleValue]) && $_POST['modules_visible'][$iModuleValue] == 1 ) {
					$iModulesVisible |= $iModuleValue;
				}
			}
		}
	}
// }

// Verify owner - only the owner can edit
// TODO: Allow any rep to edit if is attached to a case/company card
//if ( $aAccountCustomer['person_id'] != $aRepData['person_id'] || !$aAccountCustomer['person_id'] || $aAccountCustomer['account_esm'] )
//{
//	echo 'You do not appear to be the creator of this account. Access is therefore restricted. <a href="javascript:history.back();">Return to customer</a>';
//	exit();
//}

// Select add-on details
{
	// EVM
	$aESM = DBGetRow('ca.esm', "esm.master_account_id = '" . $aAccountCustomer['account_id'] . "'");

	// Virus
	$aViruses = DBGetRows('ca.extra_virus', "extra_virus.cst_id = '" . $aAccountCustomer['cst_id'] . "'");
	foreach($aViruses as $aVirus)
	{
		$iVirus += $aVirus['number'];
	}

	// Advisory contacts
	$aAdvisories = DBGetRows('ca.extra_contact', "extra_contact.account_id = '" . $aAccountCustomer['account_id'] . "'");
	foreach($aAdvisories as $aAdvisory)
	{
		$iAdvisory += $aAdvisory['number'];
	}
}

// Save data
if ( isset( $_GET['s'] ) && $_GET['s'] == 1 )
{
	$account_options = 0;
	$account_options |= !empty( $_POST['OPT_ADVISORY_DEEP_LINKS'] ) ? OPT_ADVISORY_DEEP_LINKS : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_EXTENDED_DATA'] ) ? OPT_ADVISORY_EXTENDED_DATA : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_EXTENDED_DESCRIPTION'] ) ? OPT_ADVISORY_EXTENDED_DESCRIPTION : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ADVISORY_POC_DATA'] ) ? OPT_ADVISORY_POC_DATA : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_ASSET_RECEIVE_ALL'] ) ? OPT_ASSET_RECEIVE_ALL : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_XML_V1'] ) ? OPT_XML_V1 : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_INCLUDE_TICKET_ID'] ) ? OPT_INCLUDE_TICKET_ID : OPT_NONE;
	$account_options |= !empty( $_POST['OPT_REJECTED'] ) ? OPT_REJECTED : OPT_NONE;
	
	// Update 'accounts' table
	// If vim_40 then include saving with account options unchecked
	if ( !empty($_POST['new_username'])
		 || !empty($_POST['new_name'])
		 || !empty($_POST['new_email'])
		 || !empty($_POST['recipient_email'])
		 || !empty($_POST['new_expire'])
		 || !empty($_POST['new_password'])
		 || !empty($_POST['new_product'])
		 || $iModulesEnabled
		 || $iModulesVisible
		 || !empty($_POST['account_company'])
		 || 'vim_40' === $aAccountCustomer['special_limits']
	) {
		if ( !empty($_POST['new_username']) )
		{
			// Check username availaility
			if ( !count(DBGetRows('ca.accounts', "accounts.account_username = '" . $_POST['new_username'] . "'")) )
			{
				$sUpdate = "accounts.account_username = '" . trim($_POST['new_username']) . "'";
				$sComment = 'Changed username (' . htmlspecialchars($_POST['new_username']) . ')<br>';
			}
			else
			{
				$bUsernameFailed = true;
			}
		}

		if ( !empty($_POST['new_name']) ) {
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.account_name = '" . trim($_POST['new_name']) . "'";
			$sComment = 'Changed name (' . htmlspecialchars($_POST['new_name']) . ')<br>';
		}

		if ( !empty($_POST['new_email']) ) {
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.account_email = '" . trim($_POST['new_email']) . "'";
			$sComment = 'Changed email (' . htmlspecialchars($_POST['new_email']) . ')<br>';
		}

		if ( !empty($_POST['recipient_email']) ) {
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.recipient_email = '" . trim($_POST['recipient_email']) . "'";
			$sComment = 'Changed email (' . htmlspecialchars($_POST['recipient_email']) . ')<br>';
		}

		// Expires date - must be run on 'cst_id' not account_id, in order to affect sub-esm accounts too.
		if ( isset($_POST['new_expire']) && preg_match_all('/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/', $_POST['new_expire']) )
		{
			// Prevent extending account expiry if the Surveillance Scanner is enabled
			if ( $aAccountCustomer['modules'] & MOD_VSS ) {
				echo '<font color="RED"><b>ERROR: Not allowed: You cannot change expiry date on the account if you have Surveillance Scanner enabled, since it is in End Of Sale state. Please remove the Surveillance Scanner first.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
				exit();
			}

			// Check if the the active period for the account is valid
			// NOTE: the account validity period starts from NOW since we don't have the account creation date.
			if ( !LicenseRestrictions::canCreateLicense( $aRepData, array( 'quantity' => 1, 'start_date' => date("Y-m-d"), 'end_date' => $_POST['new_expire'] ) ) ) {
				echo '<font color="RED"><b>ERROR: Not allowed: ' . htmlspecialchars( LicenseRestrictions::getLastError() ) . '.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
				exit();
			}

			// Execute query here based on "cst_id" as it needs to affect any and all sub-esm accounts also
			$res = DBQuery("UPDATE ca.accounts SET accounts.account_expires = '" . mysql_real_escape_string( $_POST['new_expire'] ) . "' WHERE accounts.cst_id = '" . (int) $aAccountCustomer['cst_id'] . "'");
			$sComment .= 'Expiry date changed (' . htmlspecialchars($_POST['new_expire']) . ')<br>';

			if ( $res ) {
				// Since we have updated the accounts expiry date, we also need to update the license expiry dates.
				DBQuery("UPDATE ca.partition_license_pool SET expiry = '" . mysql_real_escape_string( $_POST['new_expire'] ) . "' WHERE cst_id = '" . (int) $aAccountCustomer['cst_id'] . "'" );
				$sComment .= 'License Expiry date changed (' . htmlspecialchars($_POST['new_expire']) . ')<br>';
			}
		}

		$msp = 0;
		if ( isset($_POST['msp']) && $_POST['msp'] ) {
			$msp = 1;
		}
		$isCustomerMsp= DBQuery( 'INSERT INTO ca.customers (id, msp) VALUES (' . (int) $aAccountCustomer['cst_id'] . ',' . $msp . ') ON DUPLICATE KEY UPDATE msp = ' . $msp );

		if ( $_POST['salesforceId'] ) {
			if ( ctype_digit( $_POST['salesforceId'] ) ) {
				$license->insertSalesforceId( (int) $aAccountCustomer['cst_id'], $_POST['salesforceId'] );
			}
		}

		// New Password
		if ( !empty($_POST['new_password']) )
		{
			// Generate random password
			$sPassword = rand(********,********);

			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.account_password = password('" . $sPassword . "'), accounts.account_gen_pwd = 1";
			$sComment .= 'Password reset<br>';
		}

		// New Module Product
		if ( $iModulesEnabled || $iModulesVisible )
		{
			$sUpdate .= ( $sUpdate ? ', ' : '' ) . "accounts.modules = '" . $iModulesEnabled . "', accounts.show_modules = '" . $iModulesVisible . "'";
			// now that show_modules is saved, update the current account/customer $var
			// so it's up to date when querying it for which visibility checkboxes are checked
			$aAccountCustomer['show_modules'] = $iModulesVisible;

			$sComment .= 'Product Modules changed<br>';

			// If new product module == MOD_ESM, then insert 5 esm accounts if not already there
			if ( $iModulesEnabled & MOD_ESM )
			{
				// Check if there already an entry in 'ca.esm'
				if ( !mysql_num_rows(DBQuery("SELECT * FROM ca.esm WHERE esm.master_account_id = '" . intval($aAccountCustomer['account_id']) . "' LIMIT 1")) )
				{
					DBQuery("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES(5, '" . intval($aAccountCustomer['account_id']) . "', '" . intval($aAccountCustomer['cst_id']) . "')");
				}
			}
		}

		if ( in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) ) {
			if ( !empty($_POST['account_company']) ) {
				$sUpdate .= ( $sUpdate ? ', ' : '' ) . " account_company = '".$_POST['account_company']."' ";
				$sComment .= "Account company name changed: ".$_POST['account_company']." <br/>";
			}
			$subaccounts = subAccountsSqlIn( $aAccountCustomer['account_id'] );

			if ( isset($_POST['xml_access']) && $_POST['xml_access'] == '' ) {
				$_POST['xml_access'] = 0;
			}
			$xmlAccess = isset($_POST['xml_access']) ? (int)$_POST['xml_access'] : 0;

			// Update sub accounts Options and XML access
			DBQuery("UPDATE ca.accounts SET account_options = '" .  (int)$account_options . "', xml_access = '" . $xmlAccess . "' WHERE accounts.account_id IN ( " . $subaccounts . " )");

			if ( $xmlAccess === 0 ) {
				// if we removed XML access we need to remove PDF attachment options also
				DBQuery( "UPDATE ca.contact_method SET other_options = other_options & ~" . OTHER_OPTIONS_ATTACH_PDF . " WHERE account_id IN (" . $subaccounts . ") " );
			}
		}

		// Execute Changes
		DBQuery("UPDATE ca.accounts SET " . $sUpdate . " WHERE accounts.account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
	}

	#####################################################
	# SAVE CHANGES TO `ca`.`modules` RELATED SELECTIONS #
	#####################################################
	if ( is_pint($aAccountCustomer['cst_id']) && is_pint($aAccountCustomer['account_id'] ) /* && $salesPerson['profile_type_id'] == LicenseRestrictions::USER_CEC */ ) {
		//ob_start(); // make sure nothing comes before header
		$cstId = $aAccountCustomer['cst_id'];
		$accountId = $aAccountCustomer['account_id'];
		require_once INCLUDE_PATH . 'CommonModules.class.php';
		require_once INCLUDE_PATH . 'CommonModule.class.php';
		CommonModules::setConn( $GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] ); // set MySQL conn to use

		$moduleIds = array();
		if ( isset($_POST['common_modules_enabled'][$cstId]) ) {
			//$moduleIds = array_filter( $_POST['common_modules_enabled'][$cstId], create_function( '$val', 'return is_pint($val);' ) ); // remove non-integers
			$moduleIds = array_filter( $_POST['common_modules_enabled'][$cstId], function( $val) { return is_pint($val); } );
		}
		// Save any changes to ca.module selection
		CommonModules::updateAccount( $aAccountCustomer, $moduleIds );
		// Update the module visibility for the Account
		//$bitmask = array_reduce( array_keys($_POST['modules_visible']), create_function( '$v, $k', 'return $v |= $k;'), 0 );
		$bitmask = array_reduce( array_keys($_POST['modules_visible']), function( $v, $k) { return $v |= $k; }, 0 );
		$bitmask &= 0x1FFFFFFFC000; // mask to ca.modules only
		if ( $bitmask > 0 ) {
			CommonModules::updateVisibility( $aAccountCustomer, $bitmask );
		}
	}
	// Update number of extra virus
	if ( isset($_POST['new_virus']) && is_numeric($_POST['new_virus']) )
	{
		// Check if customer is using more advisory contacts, than what is being set
		$iCount = (mysql_num_rows(DBQuery('ca.virus_contact', "virus_contact.account_id = '" . $aAccountCustomer['account_id'] . "'")) / 2) - 1;

		if ( $iCount <= $_POST['new_virus'] )
		{
			DBQuery("DELETE FROM ca.extra_virus WHERE extra_virus.cst_id = '" . $aAccountCustomer['cst_id'] . "'");
			DBQuery("INSERT INTO ca.extra_virus (cst_id, number) VALUES('" . $aAccountCustomer['cst_id'] . "', '" . intval($_POST['new_virus']) . "')");

			$sComment .= 'VI: Extra Virus Contact Changed (' . intval($_POST['new_virus']) . ')<br>';
		}
		else
		{
			$bVirusFailed = TRUE;
		}
	}

	// Update number of extra advisory
	if ( isset($_POST['new_advisory']) && is_numeric($_POST['new_advisory']) )
	{
		// Check if customer is using more virus contacts, than what is being set
		$iCount = (mysql_num_rows(DBQuery('ca.contact_method', "contact_method.account_id = '" . $aAccountCustomer['account_id'] . "'")) / 2) - 1;

		if ( $iCount <= $_POST['new_advisory'] )
		{
			DBQuery("DELETE FROM ca.extra_contact WHERE extra_contact.account_id = '" . $aAccountCustomer['account_id'] . "'");
			DBQuery("INSERT INTO ca.extra_contact (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . intval($_POST['new_advisory']) . "', 'ADV')");

			$sComment .= 'VI: Extra Advisory Contact Changed (' . intval($_POST['new_advisory']) . ')<br>';
		}
		else
		{
			$bAdvisoryFailed = TRUE;
		}
	}

	// Update ESM
	if ( isset($_POST['new_esm']) && is_numeric($_POST['new_esm']) )
	{
		// Check if customer is using more accounts, than what is being set
		$iCount = DBNumRows('ca.accounts', "accounts.cst_id = '" . $aAccountCustomer['cst_id'] . "'") - 1;
		if ( $iCount <= $_POST['new_esm'] )
		{
			// Update or Insert
			if ( mysql_num_rows(DBQuery("SELECT * FROM ca.esm WHERE esm.master_account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1")) )
			{
				DBQuery("UPDATE ca.esm SET esm.no_users = '" . intval($_POST['new_esm']) . "' WHERE esm.master_account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
			}
			else
			{
				DBQuery("INSERT INTO ca.esm (no_users, master_account_id, cst_id) VALUES('" . intval($_POST['new_esm']) . "', '" . $aAccountCustomer['account_id'] . "', '" . $aAccountCustomer['cst_id'] . "')");
			}

			$sComment .= 'EVM/UM: Account Number Changed (' . intval($_POST['new_esm']) . ')<br>';
		}
		else
		{
			$bESMFailed = TRUE;
		}
	}

	// Update SS
	if ( !empty($_POST['ss_update']) )
	{
		// Delete all entries
		DBQuery("DELETE FROM ca.vss_scan_limits WHERE vss_scan_limits.account_id = '" . $aAccountCustomer['account_id'] . "'");

		// New entries (Daily, Weekly, Monthly)
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_daily_number'] . "', '1')");
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_weekly_number'] . "', '2')");
		DBQuery("INSERT INTO ca.vss_scan_limits (account_id, number, type) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $_POST['new_ss_monthly_number'] . "', '3')");

		$sComment .= 'SS: Available Scan Slots (' . intval($_POST['new_ss_daily_number']) . ' ' . fSSFrequencyName(1) . ' / ' . intval($_POST['new_ss_weekly_number']) . ' ' . fSSFrequencyName(2) . ' / ' . intval($_POST['new_ss_monthly_number']) . ' ' . fSSFrequencyName(3) . ')<br>';
	}

	$ignore = false;
	// Only account type can be chosen at a time
	if ( !empty($_POST['csi_40']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_40' WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 4.x.<br>';
	} elseif ( !empty($_POST['csi_50']) && $isCSI5Available ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_50'  WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )" );
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 5.x.<br>';
	} elseif ( !empty($_POST['csi_60']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_60'  WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )" );
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 6.x.<br>';
	} elseif ( !empty($_POST['csi_70']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_70'  WHERE accounts.account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )" );
		$status = DBQuery('INSERT INTO ca.process SET '
						  . 'cst_id = "' . (int) $aAccountCustomer['cst_id'] . '"'
						  . ',process_type = 3' // Migration
						  );
		$ignore = true;
		$sComment .= 'Account enabled for the Secunia CSI 7.x.<br>';
	} elseif ( !empty($_POST['vim_40']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'vim_40' WHERE account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )");
		$sComment .= 'Account enabled for the Secunia VIM 4.x.<br>';
	} elseif ( !empty($_POST['vim_30']) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = 'vim_30' WHERE account_id IN ( " . subAccountsSqlIn( $aAccountCustomer['account_id'] ) . " )");
		$sComment .= 'Account enabled for the Secunia VIM 3.x.<br>';
	} elseif ( in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) ) {
		DBQuery("UPDATE ca.accounts SET special_limits = NULL WHERE account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1");
		$sComment .= 'Account disabled for the Secunia VIM 3.x and VIM 4.x.<br>';
	}

	// Update CSI Base Settings
/*	if ( $_POST['s_trial'] || $_POST['s_unique_hosts'] )
	{
		// Verify that we have an entry in this table - insert vs. update
		if ( mysql_num_rows(DBQuery("SELECT * FROM ca.nsi_base_settings WHERE nsi_base_settings.account_id = '" . $aAccountCustomer['account_id'] . "' LIMIT 1")) )
		{
			// Update Base Settings
			DBQuery("UPDATE ca.nsi_base_settings SET nsi_base_settings.s_trial = '" . $_POST['s_trial'] . "', nsi_base_settings.s_unique_hosts = '" . $_POST['s_unique_hosts'] . "' WHERE nsi_base_settings.account_id = '" . $aAccountCustomer['account_id'] . "'");
		}
		else
		{
			DBQuery("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $aAccountCustomer['account_id'] . "', '" . $aAccountCustomer['cst_id'] . "', '0', '" . $_POST['s_unique_hosts'] . "', '" . $_POST['s_trial'] . "')");
		}

		$sComment .= 'CSI: Trial / Host Licenses Changed (' . ( $_POST['s_trial'] ? 'Trial Account' : 'Customer Account' ). ' / ' . intval($_POST['s_unique_hosts']) . ')<br>';
	}*/

	// Save comment - if available
	if ( $sComment )
	{
		DBQuery("INSERT INTO crm.comments (comment, added, cst_id, person_id, type) VALUES('" . mysql_escape_string($sComment) . "', NOW(), '" . $aAccountCustomer['cst_id'] .  "', '" . $aRepData['person_id'] . "', '3')");
	}

	// Save CSI 7 Licenses
	for( $i = 1; $i <= 3; $i++ ) {
		if ( $salesPerson['profile_type_id'] != LicenseRestrictions::USER_CEC && $i == 3 ) {
			error_log( 'Error - cannot change Partition Licenses - not enough permissions' );
			continue;
		}
		$fieldName = 'license_' . $i . '_num_granted';

		if ( !isset( $_POST[$fieldName] ) ) {
			continue;
		}
		$value = (int) $_POST[$fieldName];

		if ( !LicenseRestrictions::canCreateLicense( $GLOBALS['aRepData'], array( 'quantity' => $value ), true ) ) {
			error_log( 'Error - LicenseRestrictions - Type: ' . $i . ', Quantity : ' . $value . ' : ' . LicenseRestrictions::getLastError() );
			continue;
		}
		//CSIL-9571 Check if license value has decreased and update to csi_large_customers table if so.
		if (count(DBGetRows('ca.partition_license_pool', "partition_license_pool.cst_id = '" . (int) $aAccountCustomer['cst_id'] . "' and partition_id = 0 and license_type='".(int) $i."' and num_granted > '".$value."' ")) )
		{
			error_log( 'License count decreased for  -Customer :'.(int) $aAccountCustomer['cst_id'].' Type: ' . $i . ', Quantity : ' . $value );
			$query_lg_customer = 'Replace into  ca.csi_large_customers  values ("' . (int) $aAccountCustomer['cst_id'] . '",1,NOW())';
			DBQuery( $query_lg_customer );
		}

		/* Update the value if the licenses have increased in
		   number for the Customer and partition 0 */
		$query = 'UPDATE ca.partition_license_pool
			  SET
			   num_available = CASE
			   WHEN num_granted < '.$value.' THEN ABS( cast(' . $value . ' as signed) - cast(num_granted as signed) ) + num_available
			   WHEN cast(num_available as signed) - ABS((cast(' . $value . ' as signed) - cast(num_granted as signed) )) > 0 THEN  cast(num_available as signed) - ABS(cast(' . $value . ' as signed) - cast(num_granted as signed))
			   ELSE 0
			   END
			   ,num_granted = "' . $value . '"
			  WHERE cst_id = "' . (int) $aAccountCustomer['cst_id'] . '"
			  AND partition_id = 0
			  AND license_type = ' . (int) $i;

		DBQuery( $query );
	}
}

// Redirect to customer
if ( isset( $_GET['s'] ) && $_GET['s'] == 1 )
{
	// If no errors or data to display
	if ( !$bUsernameFailed && !$bAdvisoryFailed && !$bVirusFailed && !$bESMFailed && !$sPassword ) {
		if ( $_GET['iframe'] == 1 ) {
			echo '<script>window.opener.location = window.opener.location; window.close();</script>';
//			header("Location: iframe_ca_accounts.php?cst_id=" . intval($_GET['cst_id']));
		} else {
			header("Location: ?page=" . urlencode( $_GET['page'] ). "&cst_id=" . intval($_GET['cst_id']) . "&account_id=" . intval($_GET['account_id']));
		}
		exit();
	}
}

?>
<link rel="stylesheet" type="text/css" href="account_management.css" />
<form method="POST" action="?page=ca_account_management&amp;s=1&amp;account_id=<?= intval($_GET['account_id']) ?>&amp;cst_id=<?= intval($_GET['cst_id']) ?>&amp;iframe=<?= ( isset($_GET['iframe']) && $_GET['iframe'] ? 1 : 0 ) ?>">
<div class="limited-table">
<table>
	<?php
	$sTitle = 'CA - Account Administration';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

	if ( isset( $_GET['s'] ) && $_GET['s'] == 1 ) {
		?>
		<tr><td colspan="3"><?=( $bUsernameFailed ? 'Failed. The chosen username is not available. <a href="?page=ca_account_management&amp;account_id=' . intval($_GET['account_id']) . '&amp;cst_id=' . intval($_GET['cst_id']) . '">Try again</a> or' : 'Data Updated.' ) . ' <a href="?page=customer&amp;cst_id=' . intval($_GET['cst_id']) . '">Return to customer</a>.' ?></td></tr>
		<?php
		if ( $bAdvisoryFailed ) {
			?>
			<tr><td colspan="3">Advisory Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td></tr>
			<?php
		}

		if ( $bVirusFailed ) {
			?>
			<tr><td colspan="3">Virus Contact change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some contacts first.</td></tr>
			<?php
		}

		if ( $bESMFailed ) {
			?>
			<tr><td colspan="3">EVM/UM license change failed. You cannot set a limit lower than what you customer is using. Ask your customer to remove some accounts first.</td></tr>
			<?php
		}

		if ( $sPassword ) {
			?>
			<tr><td colspan="3"><b>New Password: <?= htmlspecialchars($sPassword) ?></b></td></tr>
			<?php
		}
	} else {
	?>
	<tr>
		<th colspan="3">Customer</th>
	</tr>
	<tr>
		<td>Company Name</td>
		<td colspan="2"><a href="?page=customer&amp;cst_id=<?= $aAccountCustomer['cst_id'] ?>"><?= htmlspecialchars($aAccountCustomer['name']) ?></a></td>
	</tr>
	<tr>
		<td><label for="msp">MSP (Managed Service Provider)</label></td>
		<td colspan="2"><input type="checkbox" name="msp" id="msp"<?= ( $customer['msp'] ? ' checked' : '' ) ?> /></td>
	</tr>
	<tr>
		<td><label for="salesforceId">Salesforce ID</label></td>
		<td colspan="2"><input type="text" name="salesforceId" id="salesforceId" value="<?= $salesforceId ?>" /></td>
	</tr>
	<tr>
		<td>Next Appointment</td>
		<td colspan="2"><?= htmlspecialchars($aAccountCustomer['appointment']) ?></td>
	</tr>
	<tr>
		<th>Customer Area Details</th>
		<th>Current Value</th>
		<th>New Value</th>
	</tr>
	<tr>
		<td>Username</td>
		<td><?= htmlspecialchars($aAccountCustomer['account_username']) ?></td>
		<td><input type="text" name="new_username"></td>
	</tr>
	<tr>
		<td>Name</td>
		<td><?= htmlspecialchars($aAccountCustomer['account_name']) ?></td>
		<td><input type="text" name="new_name"></td>
	</tr>
	<tr>
		<td>Password</td>
		<td colspan="2"><input type="checkbox" name="new_password" style="width: 15px;"> Generate New Password</td>
	</tr>
	<tr>
		<td>Email</td>
		<td><?=htmlspecialchars($aAccountCustomer['account_email'])?></td>
		<td><input type="text" name="new_email"></td>
	</tr>
        <tr>
            <td> Recipient Email</td>
            <td><?=htmlspecialchars($aAccountCustomer['recipient_email'])?></td>
            <td><input type="text" name="recipient_email"></td>
        </tr>
	<tr>
		<td>Expires</td>
		<td><?=htmlspecialchars($aAccountCustomer['account_expires'])?></td>
		<td><input type="text" name="new_expire"></td>
	</tr>
	<?php

	// If not SS, BA, and CSI
	if ( !($aAccountCustomer['modules'] & MOD_VSS) && !($aAccountCustomer['modules'] & MOD_BA) && !($aAccountCustomer['modules'] & MOD_NSI) && 1 == 2 )
	{
		?>
		<tr>
			<td><br></td>
		</tr>
		<tr>
			<td><b>Extra Contacts</b></td>
			<td><b>Current Value</b></td>
			<td><b>New Value</b></td>
		</tr>
		<tr>
			<td>Extra Advisory Contact</td>
			<td><?=intval($iAdvisory)?></td>
			<td><input type="text" name="new_advisory"></td>
		</tr>
		<?php
	}

	// todo: replace this checkeboxes with radiobuttons
	?>
	<tr>
		<th>Access to</th>
		<th colspan="2">Values <span style="color: gray; font-size: 0.9em;">(Choose One Only)</span></th>
	</tr>
	<tr>
		<td>Migration output</td>
		<td class="notice" colspan="2">
			<?php
			$info = new CustomerState( $aAccountCustomer['cst_id'] );
			$info->display();
			?>
		</td>
	</tr>
	<tr>
		<td>CSI 4.x</td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="csi_40"<?= ( $aAccountCustomer['special_limits'] == 'csi_40' ? ' checked' : '' ) ?> <?= ( $aAccountCustomer['special_limits'] == 'csi_50' || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the CSI 4.x</label></td>
	</tr>
	<tr>
		<td>CSI 5.x<br /><span class="note-message">(NOTE: Downgrade from CSI 5.x is NOT possible)</span></td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="csi_50"<?= ( $aAccountCustomer['special_limits'] == 'csi_50' ? ' checked' : '' ) ?> <?= ( !$isCSI5Available || $aAccountCustomer['special_limits'] == 'csi_50' || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the CSI 5.x</label></td>
	</tr>
	<tr>
		<td>CSI 6.x Beta<br /><span class="note-message">(NOTE: Downgrade from CSI 6.x is NOT possible)</span></td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="csi_60"<?= ( $aAccountCustomer['special_limits'] == 'csi_60' ? ' checked' : '' ) ?> <?= ( !$isCSI5Available || $aAccountCustomer['special_limits'] == 'csi_60' ? ' disabled' : '' ) ?>> Check this box, to enable the <b>CSI 6.x</b></label></td>
	</tr>
	<tr>
		<td>CSI 7.x Beta<br /><span class="note-message">(NOTE: Downgrade from CSI 7.x is NOT possible)</span></td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="csi_70"<?= ( $aAccountCustomer['special_limits'] == 'csi_70' ? ' checked' : '' ) ?> <?= ( $aAccountCustomer['special_limits'] == 'csi_70' ? ' disabled' : '' ) ?>> Check this box, to enable the <b>CSI 7.x</b></label></td>
	</tr>
	<tr>
		<td>VIM 3.x</td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="vim_30"<?= ( $aAccountCustomer['special_limits'] == 'vim_30' ? ' checked' : '' ) ?>> Check this box, to enable access to VIM 3.x</label></td>
	</tr>
	<tr>
		<td>VIM 4.x</td>
		<td colspan="2"><label><input class="accounttypecb" type="checkbox" value="1" name="vim_40"<?= ( $aAccountCustomer['special_limits'] == 'vim_40' ? ' checked' : '' ) ?>> Check this box, to enable access to <b>VIM 4.x</b></label></td>
	</tr>
	<?php if ( $aAccountCustomer['special_limits'] == 'vim_30' ) { ?>
		<tr>
			<th>VIM 3.x Options</th>
			<th colspan="2">Values</th>
		</tr>
		<tr>
			<td>Access to XML Feeds and PDF advisories</td>
			<td colspan="2"><input type="checkbox" value="1" name="xml_access" <?= ( $aAccountCustomer['xml_access'] == 1 ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Company Name</td>
			<td colspan="2"><input type="text" name="account_company" id="account_company" value="<?= htmlspecialchars( $aAccountCustomer['account_company'] ) ?>"></td>
		</tr>
		<tr>
			<td>View Deep Links</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_DEEP_LINKS" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View PoC Data</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_POC_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Solution</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Description</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DESCRIPTION" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Asset List Receive All</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ASSET_RECEIVE_ALL" <?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "checked" : "" ) ?>></td>
		</tr>
		<tr class="no-print">
			<td>Statistics</td>
			<td colspan="2"><input type="button" onClick="window.open('?page=ca_account_management&type=vim_stats&account_id=<?= (int)$_GET['account_id']; ?>', 'VIM Account Statistics', 'height=500, width=500');" value="Click to View"></td>
		</tr>
	<?php } // END vim_30 ?>
	<?php if ( $aAccountCustomer['special_limits'] == 'vim_40' ) { ?>
		<tr>
			<th>VIM 4.x Options</th>
			<th colspan="2">Values</th>
		</tr>
		<tr>
			<td>Access to XML Feeds and PDF advisories</td>
			<td colspan="2"><input type="checkbox" value="1" name="xml_access" <?= ( $aAccountCustomer['xml_access'] == 1 ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Company Name</td>
 			<td colspan="2"><input type="text" name="account_company" value="<?= htmlspecialchars( $aAccountCustomer['account_company'] ) ?>"></td>
		</tr>
		<tr>
			<td>View Deep Links</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_DEEP_LINKS" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_DEEP_LINKS ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View PoC Data</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_POC_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_POC_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Solution</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DATA" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DATA ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>View Extended Description</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ADVISORY_EXTENDED_DESCRIPTION" <?= ( $aAccountCustomer['account_options'] & OPT_ADVISORY_EXTENDED_DESCRIPTION ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Asset List Receive All</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_ASSET_RECEIVE_ALL" <?= ( $aAccountCustomer['account_options'] & OPT_ASSET_RECEIVE_ALL ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Include VIM ticket ID in XML mails</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_INCLUDE_TICKET_ID" <?= ( $aAccountCustomer['account_options'] & OPT_INCLUDE_TICKET_ID ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Allow rejected advisories</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_REJECTED" <?= ( $aAccountCustomer['account_options'] & OPT_REJECTED ? "checked" : "" ) ?>></td>
		</tr>
		<tr>
			<td>Downgrade XML to version 1</td>
			<td colspan="2"><input type="checkbox" value="1" name="OPT_XML_V1" <?= ( $aAccountCustomer['account_options'] & OPT_XML_V1 ? "checked" : "" ) ?>></td>
		</tr>
		
		<tr class="no-print">
			<td>Statistics</td>
			<td colspan="2"><input type="button" onClick="window.open('?page=ca_account_management&type=vim_stats&account_id=<?= (int)$_GET['account_id']; ?>', 'VIM Account Statistics', 'height=500, width=500');" value="Click to View"></td>
		</tr>
	<?php } // END VIM 4.x ?>
	<?php
	// SS Options
	if ( $aAccountCustomer['modules'] & MOD_VSS )
	{
		// Select SS options
		$aSSDailyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 1");
		$aSSWeeklyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 2");
		$aSSMonthlyDetails = DBGetRow('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $_GET['account_id'] . "' AND vss_scan_limits.type = 3");

		?>
		<tr>
			<th>Available Slots Settings</th>
			<th colspan="2">Values</th>
		</tr>
		<tr>
			<td>Change Values</td>
			<td colspan="2"><input type="checkbox" value="1" name="ss_update"> Check this box, to change values below</td>
		</tr>
		<tr>
			<td>Daily Scan Slots</td>
			<td colspan="2"><input type="text" value="<?= number_format($aSSDailyDetails['number']) ?>" name="new_ss_daily_number"></td>
		</tr>
		<tr>
			<td>Weekly Scan Slots</td>
			<td colspan="2"><input type="text" value="<?= number_format($aSSWeeklyDetails['number']) ?>" name="new_ss_weekly_number"></td>
		</tr>
		<tr>
			<td>Monthly Scan Slots</td>
			<td colspan="2"><input type="text" value="<?= number_format($aSSMonthlyDetails['number']) ?>" name="new_ss_monthly_number"></td>
		</tr>
		<?php
	}

	// ESM settings
	if ( $aAccountCustomer['modules'] & MOD_ESM || $aAccountCustomer['modules'] & MOD_UM || in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40') ) )
	{
		?>
		<tr>
			<th>EVM/UM Details</th>
			<th colspan="2">Values</th>
		</tr>
		<tr>
			<td>Licenses</td>
			<td colspan="2"><input type="text" name="new_esm" value="<?=intval($aESM['no_users'])?>"></td>
		</tr>
		<?php
	}

	if ( $aAccountCustomer['special_limits'] === 'csi_70' ) {
		// CSI 7 Licenses
		$licenses = DBGetRows('ca.partition_license_pool', "cst_id = '" . $aAccountCustomer['cst_id'] . "' AND partition_id = 0", "license_type DESC");

		$html = '';
		foreach ( $licenses as $license ) {
			$text = '-';
			$minimum = 0;
			$granted = (int) $license['num_granted'];
			$available = (int) $license['num_available'];
			$licenseType = (int) $license['license_type'];
			switch( $license['license_type'] ) {
			case '1':
				$text = 'Host Licenses';
				break;
			case '2':
				$text = 'User Licenses';
				break;
			case '3':
				$text = 'Partition Licenses';
				break;
			}
			if($licenseType == 2 || $licenseType == 3)
			{
				$minimum = $granted - $available;
			}
			$html .= '<tr><td>' . $text . '<br/><span class="note-message">(Granted: ' . $granted . ', Available: ' . $available . ')</span></td>';
			if ( $salesPerson['profile_type_id'] != LicenseRestrictions::USER_CEC && $license['license_type'] == 3 ) {
				$html .= '<td colspan="2">' . $granted . '</td>';
			} else {
				$html .= '<td colspan="2"><input type="number" name="license_' . $licenseType . '_num_granted" value="' . $granted . '" min ="'.$minimum.'" ></td>';
			}
			$html .= '</tr>';
		}
		echo $html ?
			'<tr>
			   <th>Root partition licenses</th>
			   <th colspan="2">Values</th>
			  </tr>' . $html
			:
			'<tr>
               <td colspan=3 style="color:maroon">No licenses found. Seems like a problem with account creation. Please contact IT support.</td>
             </tr>';

		$otherLicenses = DBQueryGetRows( 'SELECT ca.partition_license_pool.*, ca.accounts.account_username
			FROM ca.partition_license_pool
			JOIN ca.accounts USING (cst_id, partition_id)
			WHERE cst_id = ' . (int) $aAccountCustomer['cst_id'] . '
			AND is_partition_admin = 1
			AND ca.partition_license_pool.partition_id > 0
			ORDER BY ca.accounts.account_username' );

		$html = array();
		foreach ( $otherLicenses as $license ) {
			$text = '-';
			$granted = (int) $license['num_granted'];
			$available = (int) $license['num_available'];
			switch( $license['license_type'] ) {
				case '1':
					$text = 'Host Licenses';
					break;
				case '2':
					$text = 'User Licenses';
					break;
			}

			if ( $text != '-' ) {
				$html[ $license['account_username'] ][ $license['license_type'] ] = array( 'granted' => $granted, 'available' => $available );//'Granted: ' . $granted . ', Available: ' . $available;
			}
		}

		if ( !empty( $html )  ) {
			echo '<tr><th>Partition license distribution <span class="note-message">Account Name</span></th>
				<th>Host license <span class="note-message">Granted | Available</span></th>
				<th>User license <span class="note-message">Granted | Available</span></th></tr>';

			foreach( $html as $partitionId => $partitionLicense ) {
				echo '<tr><td>' . $partitionId . '</td>
						<td>' . $partitionLicense[1]['granted'] . ' | ' . $partitionLicense[1]['available'] . '</td>
						<td>' . $partitionLicense[2]['granted'] . ' | ' . $partitionLicense[2]['available'] . '</td>
					</tr>';
			}
		}
	}

	if ( in_array( $aAccountCustomer['special_limits'], array('vim_30', 'vim_40', 'csi_70') ) ) {
		echo '<tr><th colspan="3">Terms & Conditions</th></tr>';

		$termsAndConditions = new TermsAndConditions( $aAccountCustomer['cst_id'] );
		if ( isset( $_GET['action'] ) && $salesPerson['profile_type_id'] == LicenseRestrictions::USER_CEC ) {
			switch( $_GET['action'] ) {
				case 'assign_active_terms':
					if ( isset( $_GET['assign_terms'] ) && $termsAndConditions->assignTerms( $_GET['assign_terms'] ) ) {
						echo '<tr><td>Message</td><td colspan="2" class="notice">Terms successfully assigned.</td></tr>';
					} else {
						echo '<tr><td>Message</td><td colspan="2" class="notice">Error while assigning terms.</td></tr>';
					}
					break;

				case 'delete_active_terms':
					if ( $termsAndConditions->removeTerms() ) {
						echo '<tr><td>Message</td><td colspan="2" class="notice">Terms & Conditions successfully removed.</td></tr>';
					} else {
						echo '<tr><td>Message</td><td colspan="2" class="notice">Terms & Conditions removal failed.</td></tr>';
					}
					break;
			}
		}

		$terms = $termsAndConditions->getTermsMessage();
		$disabled = '';
		if ( !$terms['enabled'] ) {
			$disabled = 'disabled="disabled"';
		}

		echo '<tr><td colspan="2" style="text-align: center;">' . $terms['msg'] . '</td><td>';
		if ( $salesPerson['profile_type_id'] == LicenseRestrictions::USER_CEC ) {
			echo '<input type="button" id="terms_add_new" value="Select new terms" class="bigbutton" onClick="terms.termsManager();" />
		   <br /><input type="button" id="terms_remove_current" value="Remove current terms" class="bigbutton" ' . $disabled  . ' onClick="terms.removeVerification()" />';
		}
		echo '<br /><input type="button" id="terms_view" value="View terms logs" class="bigbutton" onClick="terms.viewTerms();" /></td></tr>';

		if( isset( $_POST['action'] ) && $_POST['action'] == 'remove_terms' && $terms['enabled'] ) {
			$termsAndConditions->removeTerms();
		}
	}
	/*
	 * CSIL-9077 Generating modules & licenses encrypted string for On-Prem Customers
	 *  
	 */
	if ( $aAccountCustomer['special_limits'] === 'csi_70' ) {
		echo '<tr><th colspan="3">Generate License String & Copy</th></tr>';

		echo '<tr><td colspan="2" style="text-align: center;">Please copy the License string for <b>this customer</b> in file <b>license.lic </b> at SVM after clicking on button <b>Generate License String</b><br/> <b>SVM file path:</b> csi/api/config/ </td><td>';
		
		echo '<br /><input type="button" id="lices_view" value="Generate License String" class="bigbutton" onClick="licesMod.viewRecords();" /></td></tr>';
	}
	
	// Include the modules from `ca`.`modules`
	include_once '_common_module_list.php';
	?>
		<!-- VIF/EVM Modules -->
		<tr>
			<th>VIF/EVM Account Product Modules</th>
			<th>Enabled Modules</th>
			<th>Displayed Modules</th>
		</tr>
		<?php

		// Loop Through Modules
		foreach( $aModules as $sModuleName => $iModuleValue ) {
			$checkedModules = $checkedShowModules = '';
			if ( $aAccountCustomer['modules'] & $iModuleValue )  {
				$checkedModules = ' checked="checked"';
			}
			if ( $aAccountCustomer['show_modules'] & $iModuleValue )  {
				$checkedShowModules = ' checked="checked"';
			}

			// Special case is Surveillance Scanner, it should only be shown if it is enabled, so that it is only possible to disable it and not be possible to enable it.
			if ( $checkedModules !== '' || $iModuleValue !== MOD_VSS ) {
				$enabledHtml = '<input type="checkbox" id="cbEnable' . $iModuleValue . '" name="modules_enabled[' . $iModuleValue . ']" value="1" ' . $checkedModules . '>';
				$displayedHtml = '<input type="checkbox" name="modules_visible[' . $iModuleValue . ']" value="1" ' . $checkedShowModules . '>';

				// If there are no module restrictions or there are module restricts and the rep is in the restriction
				echo '
			<tr>
				<td><label for="cbEnable' . $iModuleValue . '">' . $sModuleName . '</label></td>
				<td>' . $enabledHtml . '</td>
				<td>' . $displayedHtml . '</td>
			</tr>';
			}
		}
		?>
	<tr>
		<td colspan="2"></td>
		<td><input type="submit" value="Submit Changes" class="submit"></td>
	</tr>
	<?php
	}
	?>
</table>
</div>
</form>
<script type="text/javascript">

	(function () { // Link the EVM & UM checkboxes together when on VIM 4.x
		// Create vars that reference the enable EVM and UM checkboxes
		var evmCheckbox = document.getElementsByName( "modules_enabled[2]" )[0];
		var umCheckbox = document.getElementsByName( "modules_enabled[2048]" )[0];
		/**
		 * Handles the onclick event for the VIF/EVM's enable EVM and UM checkboxes.
		 * This makes both checkboxes function as a single item if VIM 40 is selected.
		 * @param {Event} e Not required by IE
		 */
		function evmUmClicked( e ) {
			if ( !document.getElementsByName( "vim_40" )[0].checked ) return;
			evmCheckbox.checked = umCheckbox.checked = (e.target || event.srcElement).checked;
		}
		// Bind onclick handlers to the enable EVM and UM checkboxes
		if ( undefined !== evmCheckbox && undefined !== umCheckbox ) {
			if ( document.addEventListener ) {
				// Webkit + FF
				evmCheckbox.addEventListener( "click", evmUmClicked, false ); // EVM
				umCheckbox.addEventListener( "click", evmUmClicked, false ); // UM
			} else {
				// IE
				evmCheckbox.attachEvent( "onclick", evmUmClicked ); // EVM
				umCheckbox.attachEvent( "onclick", evmUmClicked ); // UM
			}
		}
	})();

	(function () { // Make the Account Type checkboxes act like Radio Buttons
		/**
		 * Handles the onclick event for the account type checkboxes
		 * This unchecks all the account type checkboxes except for the
		 * box that was the source of the event.
		 * This is to limit the account type selection to a single type without
		 * resorting to radio buttons which requires rewriting part of the code.
		 * @param {Event} e Not required by IE
		 */
		function accounttypeSelected( e ) {
			var checkbox = e.target || event.srcElement;
			var a = document.getElementsByTagName( "input" );
			for ( var i = 0, len = a.length; i <  len; i++ ) {
				if ( "checkbox" === a[i].type && "accounttypecb" === a[i].className ) {
					if ( checkbox !== a[i] ) {
						a[i].checked = false;
					}
				}
			}
			// Toggle display of old modules when account type is changed
			if ( undefined !== toggleOldModulesDisplay ) {
				toggleOldModulesDisplay( checkbox.name );
			}
		}
		// Bind onclick handlers to the account type checkboxes
		var a = document.getElementsByTagName( "input" );
		for ( var i = 0, len = a.length; i <  len; i++ ) {
			if ( "checkbox" === a[i].type && "accounttypecb" === a[i].className ) {
				if ( a[i].addEventListener) {
					// Webkit + FF
					a[i].addEventListener( "click", accounttypeSelected, false );
				} else {
					// IE
					a[i].attachEvent( "onclick", accounttypeSelected );
				}
			}
		}
	})();

	var terms = {
		viewTerms: function(){
			var url = 'termsLogs.php?cst_id=' + get( 'cst_id' );
			window.open( url, '_blank' );
		}
		,removeVerification: function(){
			var confirmation = confirm('Are you sure you want to remove Terms & Conditions from current customer?');
			if ( confirmation ){
				var url = 'index.php?page=ca_account_management&action=delete_active_terms&account_id=<?= (int) $_GET['account_id'] ?>&cst_id=<?= (int) $_GET['cst_id'] ?>';
				window.open( url, '_self' );
			}
		}
		,termsManager: function() {
			window.open( 'termsManager.php?cst_id=' + get( 'cst_id' ) + '&account_id=' + get( 'account_id' ), '_self' );
		}
	};

	var licesMod = {
			viewRecords: function(){
				var url = 'getlicensesModulesinfo.php?cst_id=' + get( 'cst_id' );
				 newwindow=window.open(url,'Key','height=600,width=600');
			       if (window.focus) {newwindow.focus()}
			       return false;
				//window.open( url, '_blank' );
			}
		};

	var get = function( name ){
		if( name = ( new RegExp('[?&]' + encodeURIComponent( name ) + '=([^&]*)') ).exec( location.search ) ) {
			return decodeURIComponent( name[1] );
		}
		return '';
	};

</script>
