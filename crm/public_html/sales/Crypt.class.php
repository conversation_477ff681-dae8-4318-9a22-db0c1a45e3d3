<?php

/**
 * Contains cryptographic functions used for VT decryption
 *
 * Note that we are hiding errors using @ so we don't leak the key
 *
 * {verbatim}
 * Creating a test DB
 * CREATE TABLE `vuln_track`.`vuln_enc` LIKE `vuln_track`.`vuln`;
 * INSERT `vuln_track`.`vuln_enc` SELECT * FROM `vuln_track`.`vuln`;
 * {endverbatim}
 *
 * <AUTHOR>
 * @since 04.06.15
 * @package CSI
 * @subpackage Encryption
 */
class Crypt
{
    // If changing note this class is duplicated in patches.class.php for ver 7 migration & csiagent.class.php
	/**
	 * Decryption key
	 * @var string
	 */
	private $key = '';

	/**
	 * Headers that identifies a string as AES-256 CBC encrypted
	 * @var string
	 */
	const HEADER_AES256 = "\x11\x01";
	
	/**
	 * Separator encrypted string & iv
	 * CSIL-9077 Separator encrypted string & iv. Dont change it, if change is requires then change at SVM 2019 side as well (path: esm-s/csi/php/loca/Crypt.php)
	 * Use same iv for encryption as well
	 * @var string
	 */
	
	const IV_STRING = 'SA!@#$%^Sddsf5>45Dasdasd234SD(764FSD#$@#SA=D#$2^Ksdq12|&*%&2!32' ;

	/**
	 * @param string $key Decryption key
	 *
	 */
	public function __construct($key)
	{
		$this->key = $key;
	}

	/**
	 * Encrypts a string and returns it in a vNext compatible AES-256 CBC data structure
	 * This is commented out and kept as reference in case we want to test the encryption.
	 *
	 * @param binary string $string
	 * @param string $key
	 * @return string
	 */
	
	public function encryptAes256Cbc($string, $key)
	{
		$iv = openssl_random_pseudo_bytes(16);

		$enc = @openssl_encrypt($string, 'aes-256-cbc', $key, 1, $iv);

		return self::HEADER . $iv . $enc;
	}
	
	/** CSIL-9077
	 * Encrypts a string and returns it in a text format AES-256 CBC
	 *@param string $string
	 * @param string $key
	 * @return string
	 */
	
	public function encryptAes256CbcString($string, $key)
	{
		$iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
		$enc = @openssl_encrypt($string, 'aes-256-cbc', $key, 0, $iv). self::IV_STRING . bin2hex($iv);
		
		return $enc;
	}
}
