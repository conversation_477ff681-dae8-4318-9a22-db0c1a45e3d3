<?php

require_once INCLUDE_PATH . 'LicenseRestrictions.class.php';
require_once INCLUDE_PATH . 'CommonModules.class.php';
require_once INCLUDE_PATH . 'CommonModule.class.php';
CommonModules::setConn( $GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] ); // set MySQL conn to use

// Any account creation errors
if ( isset( $_GET['error'] ) && $_GET['error'] == 'username_taken' ) {
	echo "<script>alert('ERROR: Username already used!\\n\\nPlease select a new username and try creating the account again.');</script>";
}

// Container
$sOutput = '
<style>
.CustomerDetailsTable {
	border: 1px solid #C1C3D1;
	margin-bottom: 1em;
}
.CustomerDetailsTable tr {
  border-top: 1px solid #C1C3D1;
  border-bottom: 1px solid #C1C3D1;
  font-weight:normal;
}

.CustomerDetailsTable tr:first-child {
  border-top:none;
}

.CustomerDetailsTable tr:last-child {
  border-bottom:none;
}

.CustomerDetailsTable tr:nth-child(odd) td {
  background:#EBEBEB;
}

.CustomerDetailsTable td {
  background:#FFFFFF;
  text-align:left;
  vertical-align:middle;
  border-right: 1px solid #C1C3D1;
}

.CustomerDetailsTable td:last-child {
  border-right: 0;
}
</style>
';
$iLastCST = 0;
$sLicenseKeyOptions = '';
if (!empty($GLOBALS['aCustomer']['AllIDs'])) {

	// Get all accounts only for SVM. Discard old VOM, CSI 5, CSI 6 accounts
	$accounts = array();
$aAccounts = DBGetRows('ca.accounts', "special_limits = 'csi_70' AND accounts.cst_id IN(" . $GLOBALS['aCustomer']['AllIDs'] . ")", 'accounts.cst_id, accounts.account_id DESC, accounts.account_expires');

// Loop through accounts
foreach($aAccounts as $aAccount) {
	// Build list of AccountIDs (if EVM)
	{
		// Reset container
		if ( $iLastCST != $aAccount['cst_id'] ) {
			$sAccountIDs = '';
		}
		$iLastCST = $aAccount['cst_id'];

		// Append AccountID
		$sAccountIDs .= $aAccount['account_id'] . ',';

		// Proceed if this is an EVM sub-account
		if ( $aAccount['account_esm'] ) {
			continue;
		}
	}

	// Modules
	{
		// Initialise
		$iModules = $aAccount['show_modules'];
		$iModMask = 0x1;
		$sModules = '';

		// Loop through modules
		while( $iModules ) {
			$sModuleName = fModuleName( $iModules & $iModMask );

			// Show status of ca.modules which have been flagged as displayed
			$visibilityBit = $iModules & $iModMask & 0x1FFFFFFFC000;
			if ( $visibilityBit ) {
				// Get the module id that matches `ca`.`modules` table. Module visibility is stored in the bitmask
				// so we get our module id by counting how many bits have been shifted.
				$moduleId = -13; // coincides with the reserved bits (15 thru 45)
				while ( $visibilityBit >>= 1 ) {
					$moduleId ++;
				}
				// check if the module is enabled for the account it's listing under.
				if ( $module = CommonModules::getById( $moduleId ) ) {
					if ( $module->isEnabledForAccount($aAccount) ) {
						$sModules .= $module->getName() . ' (' . $module->getVersionSupport() . ')<br>';
					} else {
						// not enabled so show it greyed out
						$sModules .= '<font color="grey">' . $module->getName() . ' (' . $module->getVersionSupport() . ') (inactive)</font><br>';
					}
				}
			}
			if( $sModuleName ) {
				if ( ($iModules & $iModMask) & $aAccount['modules'] ) {
					$sModules .= $sModuleName . '<br>';
				} else {
					$sModules .= '<font color="grey">' . $sModuleName . ' (inactive)</font><br>';
				}
			}
			$iModules = $iModules & ~$iModMask;
			$iModMask = $iModMask << 1;
		}
	}

	// Prepare AccountIDs
	$sAccountIDs = substr($sAccountIDs, 0, -1);

	$accounts[] = '"' . urlencode($aAccount['account_username']) . '"';
	$sOutput .= '
	<tr class="TableGreyBackground">
		<td colspan="2"><b>' . htmlspecialchars($aAccount['account_username']) . ' (' . (int) $aAccount['cst_id'] . ')</b> - <a href="index.php?page=ca_account_management&account_id=' . $aAccount['account_id'] . '&cst_id=' . $aAccount['cst_id'] . '&iframe=' . ( $_GET['iframe'] ? 1 : 0 ) . '"' . ( $_GET['iframe'] ? ' target="_blank"' : '' ) . '>Edit Account</a> - ' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'Expired' : $aAccount['account_expires'] ) . ' - <a href="javascript:void(0)" onClick="document.getElementById(\'' . urlencode($aAccount['account_username']) . '\').style.display = ( document.getElementById(\'' . urlencode($aAccount['account_username']) . '\').style.display != \'none\' ? \'none\' : \'block\' );">Toggle Details</a></td>
	</tr>
	<tr>
		<td colspan="2">
			<div id="' . urlencode($aAccount['account_username']) . '"' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'style="display: none;"' : '' ) . '>
			<table class="CustomerDetailsTable">
				<tr>
					<td>Customer ID (CST_ID)</td>
					<td>' . (int) $aAccount['cst_id'] . '</td>
				</tr>
				<tr>
					<td>CA Modules</td>
					<td>' . $sModules . '</td>
				</tr>
				<tr>
					<td>Last Login</td>
					<td>' . ( $aAccount['last_login'] ? ( $aAccount['special_limits'] == 'csi_50' ? date('Y-m-d G:i:s', strtotime($aAccount['last_login'] . ' UTC')) : $aAccount['last_login'] ) : 'Never logged in' ) . '</td>
				</tr>
				<tr>
					<td>Expires</td>
					<td>' . ( !$aAccount['account_expires'] || (strtotime($aAccount['account_expires']) < time() && substr($aAccount['account_expires'], 0, 4) < 2035 ) ? 'Expired' : $aAccount['account_expires'] ) . '</td>
				</tr>';

				// VI?
				if ( $aAccount['modules'] & ( MOD_VTS | MOD_SM | MOD_ESM ) )
				{
					// Number of devices
					$iDevices = DBGetRowValue('ca.devices', 'count(*)', "devices.account_id IN(" . $sAccountIDs . ")");

					// Number of apps.
					$iDeviceSoftware = DBGetRowValue('ca.devices, ca.device_software', 'count(*)', "devices.account_id IN(" . $sAccountIDs . ") AND devices.device_id = device_software.device_id");

					// Number of advisories received (email)
					$iEmails = DBGetRowValue('ca.usage_alerts', 'count(*)', "usage_alerts.account_id IN(" . $sAccountIDs . ") AND usage_alerts.type = 1");

					// Number of advisories received (sms)
					$iSMS = DBGetRowValue('ca.usage_alerts', 'count(*)', "usage_alerts.account_id IN(" . $sAccountIDs . ") AND usage_alerts.type = 2");

					/*
					$sOutput .= '
				<tr>
					<td colspan="2"><b>VI - Details</b></td>
				</tr>
				<tr>
					<td>Network Devices</td>
					<td>' . number_format($iDevices) . '</td>
				</tr>
				<tr>
					<td>Applications</td>
					<td>' . number_format($iDeviceSoftware) . '</td>
				</tr>
				<tr>
					<td>Emails Received</td>
					<td>' . number_format($iEmails) . '</td>
				</tr>
				<tr>
					<td>SMS Received</td>
					<td>' . number_format($iSMS) . '</td>
				</tr>';
					*/
				}

				// VIF / VIM
				if ( $aAccount['modules'] & MOD_VTSE || in_array( $aAccount['special_limits'], array('vim_beta', 'vim_30', 'vim_40') ) ) {
					// VIF
					$iAssets = 0; //DBGetRowValue('ca.vtse_assets', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ")");
					$iAssetsAll = 0; //DBGetRowValue('ca.vtse_assets', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && xml_receive_all = 1");
					$iProducts = 0; //DBGetRowValue('ca.vtse_assets, ca.vtse_asset_products', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && vtse_asset_products.asset_id = vtse_assets.asset_id");
					$iVendors = 0; //DBGetRowValue('ca.vtse_assets, ca.vtse_asset_vendors', 'count(*)', "vtse_assets.account_id IN(" . $sAccountIDs . ") && vtse_asset_vendors.asset_id = vtse_assets.asset_id");
					// VIM
					$iAssets += 0; //DBGetRowValue('ca.vi_assets', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ")");
					$iAssetsAll += 0; //DBGetRowValue('ca.vi_assets', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && xml_receive_all = 1");
					$iProducts += 0; //DBGetRowValue('ca.vi_assets, ca.vi_asset_products', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && vi_asset_products.asset_id = vi_assets.asset_id");
					$iVendors += 0; //DBGetRowValue('ca.vi_assets, ca.vi_asset_vendors', 'count(*)', "vi_assets.account_id IN(" . $sAccountIDs . ") && vi_asset_vendors.asset_id = vi_assets.asset_id");

					$sRecipients = "";
					if ( in_array($aAccount['special_limits'], array('vim_30', 'vim_40') ) ) {
						// For the new vim, list configured recipients:
						// Build the VIM stats.

						$children = getChildren( (int)$aAccount['account_id'] );

						$sqlIn = implode( ",", $children );
						$sqlIn .= ( $sqlIn == "" ? "" : ", " ) . (int)$aAccount['account_id'];

						$query = 'SELECT account_id, account_username, name, contact_method_value
							FROM ca.accounts
							JOIN ca.contact_method USING ( account_id )
							WHERE account_id IN (' . $sqlIn. ')
							AND contact_method_type = 1';
						$resource = DBQuery( $query );

						$sRecipients = '';
						$_tmp = 0;
						$class = '';
						while( $row = mysql_fetch_assoc( $resource ) ) {
							if ( $_tmp != $row['account_id'] ) {
								$_tmp = $row['account_id'];
								$class = $class ? '' : 'class="TableAlternateBackground"';
							}
							$sRecipients .= '<tr ' . $class . '>';
							$sRecipients .= "<td>".htmlspecialchars($row['account_username'])."</td>";
							$sRecipients .= "<td>".htmlspecialchars($row['name'])."</td>";
							$sRecipients .= "<td>".htmlspecialchars($row['contact_method_value'])."</td>";
							$sRecipients .= "</tr>";
						}

						if ( $sRecipients ) {
							$sRecipients = '<table cellspacing="0" cellpadding="0"><tr><td><b>Username</b></td><td><b>Recipient Name</b></td><td><b>Email Address</b></td></tr>'
								. $sRecipients
								. '</table>';
						}
					}
					$vimVersionNumber = '4.x';
					if ( 'vim_30' === $aAccount['special_limits'] ) {
						$vimVersionNumber = '3.x';
					}
					$sOutput .= '
				<tr>
					<td colspan="2"><b>VIM - Details</b></td>
				</tr>
				<tr>
					<td>Asset Lists</td>
					<td>' . number_format($iAssets) . ' (' . number_format($iAssetsAll) . ' receive all)</td>
				</tr>
				<tr>
					<td>Products</td>
					<td>' . number_format($iProducts) . '</td>
				</tr>
				<tr>
					<td>Vendors</td>
					<td>' . number_format($iVendors) . '</td>
				</tr>';
					if ( $aAccount['account_version'] != "" ) {
						$sOutput .= '<tr>
						<td>Recipients (VIM ' . $vimVersionNumber . ')</td>
						<td>' . $sRecipients . '</td>
					</tr>';
					}
				}

				// CSI? //If accounts.modules set to 0 when expiring customer, it won't go inside the condition and all will be good.
				if ( $aAccount['modules'] & MOD_NSI ) {
					// Base settings
					$aBaseSettings = DBGetRow('ca.nsi_base_settings', "nsi_base_settings.account_id = '" . (int) $aAccount['account_id'] . "'");
					$custId = (!empty($aBaseSettings['cst_id'])) ? $aBaseSettings['cst_id'] : $aAccount['cst_id'];
					$partitionId = (int)$aAccount['partition_id'];
					$connString = $custId;
					if ($partitionId > 0) {
						$connString = $custId . '_' . $partitionId;
					}
					$db = '';
					// Open connection to MariaDB
					switch( $aAccount['special_limits'] ) {
					case 'csi_70':
						$dbhost = DBGetRowValue('ca.csi_pdb_info', 'db_client_host', "cst_id = '" . (int) $aAccount['cst_id'] . "' AND db_client_host NOT LIKE '%REMOVED%'");
						//Skip connecting to PDB
						if (empty($dbhost)) {
							goto skip_fetch_from_pdb;
						}
						//mysql_close(); //Close crm db connection and open PDB connection
						mysql_connect($dbhost ? $dbhost : DEFAULT_PDB_HOST_CSI7, $connString, DB_PASS_PVTDB);
						$db = 'ca_' . $custId . '.';
						break;
					case 'csi_60':
						$dbhost = DBGetRowValue('ca.csi_pdb_info', 'db_client_host', "cst_id = '" . (int) $aAccount['cst_id'] . "'");
						mysql_close();
						mysql_connect($dbhost ? $dbhost : DEFAULT_PDB_HOST_CSI6, DEFAULT_PDB_USER_CSI6, DEFAULT_PDB_PASS_CSI6);
						$db = 'ca_' . $aBaseSettings['cst_id'] . '.';
						break;
					case 'csi_50':
						$dbhost = DBGetRowValue('ca.csi_db_client_hosts', 'db_client_host', "cst_id = '" . (int) $aAccount['cst_id'] . "'");
						mysql_close();
						mysql_connect($dbhost ? $dbhost : DEFAULT_PDB_HOST_CSI5, DEFAULT_PDB_USER_CSI5, DEFAULT_PDB_PASS_CSI5);
						$db = 'ca_' . $aBaseSettings['cst_id'] . '.';
						break;
					default:
						mysql_close();
						mysql_connect(DEFAULT_PDB_HOST, DEFAULT_PDB_USER, DEFAULT_PDB_PASS);
						$db = 'ca.';
						break;
					}

					echo mysql_error();


					// Hosts imported
					$iHosts_val = DBGetRowValue($db . 'nsi_devices', 'count(*)', "nsi_devices.account_id IN(" . $sAccountIDs . ")");
					$iHosts = (!empty($iHosts_val))? $iHosts_val : 0;


					// Hosts imported using agent
					$iHostsAgent_val = DBGetRowValue($db . 'nsi_device_agent_conf', 'count(*)', "nsi_device_agent_conf.account_id IN(" . $sAccountIDs . ")");
					$iHostsAgent = (!empty($iHostsAgent_val))? $iHostsAgent_val : 0;

					// Total Appllications
					$iApplications_val = DBGetRowValue($db . 'nsi_devices', 'sum(no_total)', "account_id IN(" . $sAccountIDs . ")");
					$iApplications = (!empty($iApplications_val))? $iApplications_val : 0;

					// Total Appllications Secure
					$iApplicationsSecure_val = DBGetRowValue($db . 'nsi_devices', 'sum(no_patched)', "account_id IN(" . $sAccountIDs . ")");
					$iApplicationsSecure = (!empty($iApplicationsSecure_val))? $iApplicationsSecure_val : 0;

					// Total Appllications Insecure
					$iApplicationsInsecure_val = DBGetRowValue($db . 'nsi_devices', 'sum(no_insecure)', "account_id IN(" . $sAccountIDs . ")");
					$iApplicationsInsecure = (!empty($iApplicationsInsecure_val))? $iApplicationsInsecure_val : 0;


					// Open connection to master db
					mysql_close();
					fOpenDatabase();

					$sOutput .= '
				<tr>
					<td colspan="2"><b>CSI - Details</b></td>
				</tr>
				<tr>
					<td>Trial Account</td>
					<td>' . ( $aBaseSettings['s_trial'] ? 'Yes' : 'No' ) . '</td>
				</tr>
				<tr>
					<td>Hosts Imported</td>
					<td>' . number_format($iHosts) . '</td>
				</tr>
				<tr>
					<td>Hosts w/ Agent</td>
					<td>' . number_format($iHostsAgent) . '</td>
				</tr>
				<tr>
					<td>Applications</td>
					<td>' . number_format($iApplications) . '</td>
				</tr>
				<tr>
					<td>Insecure</td>
					<td>' . number_format($iApplicationsInsecure) . '</td>
				</tr>
				<tr>
					<td>Secure</td>
					<td>' . number_format($iApplicationsSecure) . '</td>
				</tr>
				<tr>
					<td>End-of-Life</td>
					<td>' . number_format($iApplications - $iApplicationsInsecure - $iApplicationsSecure) . '</td>
				</tr>';

					skip_fetch_from_pdb:
				}

				if ( ( $aAccount['modules'] & MOD_NSI
					   || $aAccount['modules'] & MOD_BA
					   || $aAccount['modules'] & MOD_VTSE )
					 /* We don't care about the licenses if it is a
						CSI7 account */
					&& ( $aAccount['special_limits'] !== 'csi_70' )
					) {
					$sLicenseKeyOptions = '';
//					if ( $aAccount['modules'] & MOD_NSI ) {
						$sLicenseKeyOptions .= '<option value="32">CSI</option>';
//					}
//					if ( $aAccount['modules'] & MOD_VTSE ) {
						$sLicenseKeyOptions .= '<option value="8">VIF</option>';
//					}
					if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable'][MOD_BA]) ) {
						$sLicenseKeyOptions .= '<option value="64">BA</option>';
					}

					$sOutput .= '
                	<tr>
                	  <td colspan="2">
					    <b>License Keys - <a href="javascript:void(0);" onClick="fToggleLicenseKey( document.getElementById(\'ca_new_license_key\'), this, \'' . $aAccount['account_id'] . '\');">Add License Key</a></b>
					  </td>
					</tr>';

					// License Keys
					$aLicenseKeys = DBGetRows('ca.license_keys', "account_id = '" . $aAccount['account_id'] . "'");
					foreach( $aLicenseKeys as $aLicense ) {
						if ( $aLicense['type'] == 64 ) {
							$sType = 'BA';
							$sTypeValue = 'Credits';
							$iUsed = DBGetRowValue('ca.license_exploits', 'sum(credits)', "license_id = '" . $aLicense['id'] . "'");
						} elseif ( $aLicense['type'] & MOD_VTSE ) {
							$sType = 'VIF';
							$sTypeValue = 'Asset Lists';
							$iUsed = DBGetRowValue('ca.license_vtse_assets', 'count(*)', "license_id = '" . $aLicense['id'] . "'");
						} else {
							$sType = 'CSI';
							// $sTypeValue = ( $aLicense['license_type'] == 2 ? 'User' : 'Host' ) . ' Licenses';
							$sTypeValue = 'Host Licenses';
							$iUsed = DBGetRowValue('ca.license_hosts', 'count(*)', "license_id = '" . $aLicense['id'] . "'");
						}

						/* @deprecated:
						   The license_type is deprecated now. It was
						   supposed to be for the CSI7 but the license
						   management changed completely. So it is a
						   remnant of a POC. */

						$sOutput .= '<tr>
						<td colspan="2">&nbsp;' . $aLicense['license'] . ', ' . $sType . ', ' . $aLicense['quantity'] . ' ' . $sTypeValue . ', Used: ' . ( $aLicense['license_type'] == 2 ? 'N/A' : $iUsed ) . ', expires ' . substr($aLicense['valid_to'], 0, 10) . '</td>
                                </tr>';
					}
				}

				// SS?
				if ( $aAccount['modules'] & MOD_VSS )
				{
					// Containers
					$sAvailableSlots = '';
					$sSlotsUsed = '';

					// SS limits
					$aScanLimits = DBGetRows('ca.vss_scan_limits', "vss_scan_limits.account_id = '" . $aAccount['account_id'] . "'", 'vss_scan_limits.type');

					// Trial or Live?
					if ( !$aScanLimits )
					{
						// Trial
						$sAvailableSlots = 'Trial account (1 scan, no scheduling)';

						// Check if the scan has been executed
						$aScan = DBGetRow('ca.vss_nessus_reports_raw', "vss_nessus_reports_raw.account_id = '" . $aAccount['account_id'] . "'");

//						if ( $sScan['scan_ended'] )
//						{
//							$sSlotsUsed = 'Trial scan finished at ' . $sScan['scan_ended'];
//						}
//						else
//						{
							$sSlotsUsed = 'Trial scan not used/finished.';
//						}
					}
					else
					{
						// Live
						foreach($aScanLimits as $aScanLimit)
						{
							$sAvailableSlots .= $aScanLimit['number'] . ' / ' . fSSFrequencyName($aScanLimit['type']) . '<br>';
						}

						// Select scan profiles
						$aScanProfiles = DBGetRows('ca.vss_scan_profiles', "vss_scan_profiles.account_id = '" . $aAccount['account_id'] . "'");
						foreach($aScanProfiles as $aScanProfile)
						{
							$aSSResult[$aScanProfile['frequency']] += count( fGenerateSSTargetsArray($aScanProfile['targets']) );
						}

						// Generate output
						$sSlotsUsed = number_format($aSSResult[1]) . ' / Daily<br>
						' . number_format($aSSResult[2]) . ' / Weekly<br>
						' . number_format($aSSResult[3]) . ' / Monthly<br>';
					}

					$sOutput .= '
				<tr>
					<td colspan="2"><b>SS - Details</b></td>
				</tr>
				<tr>
					<td>Available slots</td>
					<td>' . $sAvailableSlots . '</td>
				</tr>
				<tr>
					<td>Slots used</td>
					<td>' . $sSlotsUsed . '</td>
				</tr>';
				}

				$sOutput .= '
			</table>
			</div>
		</td>
	</tr>';
}

# Get host license restrictions for the current Sales Person so we can show them on the Add License Key form
$licenseRestrictions = LicenseRestrictions::getForSalesPerson( $GLOBALS['aRepData'] );
if ( false === $licenseRestrictions ) { // If no restrictions found then show 0, 0
	$licenseRestrictions = array(
		'maxLicenseDays' => 0
		,'maxLicenseQuantity' => 0
	);
}

$accounts = implode(',', $accounts);
?>
<link rel="stylesheet" type="text/css" href="datepickr.css" />
<script type="text/javascript" src="datepickr.js"></script>
<script type="text/javascript">
	var toggle = function( on ) {
		var toggle = 'block';
		if (!on) {
			toggle = 'none';
		}
		var accounts = [<?= $accounts ?>];
		for(var i=0; i < accounts.length; i++){
			document.getElementById(accounts[i]).style.display = toggle;
		}
	}
</script>

<p><a href="javascript:void(0)" onClick="toggle(true);">Toggle All On</a> | <a href="javascript:void(0)" onClick="toggle(false);">Toggle All Off</a></p>

<table width="100%" cellspacing="0" cellpadding="0">
<?= $sOutput ?>
</table>

<div align="left" id="ca_new_license_key" style="display: none; position: absolute; padding: 2px; width: 350px; background: #FFFFFF; border: 1px solid #000000;">
<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			Type
		</td>
		<td>
			<select name="lk_type" id="lk_type">
				<?= $sLicenseKeyOptions ?>
			</select>
		</td>
	</tr>
	<tr>
		<td>
			License Type
		</td>
		<td>
			<select name="lk_license_type" id="lk_license_type">
				<option value="1">Host</option>
	<!--
				<option value="2">User</option>
	-->
			</select>
		</td>
	</tr>
	<tr>
		<td>
			Starts
		</td>
		<td>
			<input type="text" name="lk_starts" value="<?= date('Y-m-d') ?>">
		</td>
	</tr>
	<tr>
		<td>
			Expires
			<br><span style="font-size: 0.8em; color: gray;">Max: <?php echo $licenseRestrictions['maxLicenseDays']; ?> days</span>
		</td>
		<td>
			<input type="text" name="lk_expires">
		</td>
	</tr>
	<tr>
		<td>
			Quantity
			<br><span style="font-size: 0.8em; color: gray;">Max: <?php echo $licenseRestrictions['maxLicenseQuantity']; ?> hosts</span>
		</td>
		<td>
			<input type="text" name="lk_quantity">
		</td>
	</tr>
	<tr>
		<td>
			&nbsp;
		</td>
		<td>
			<br>
			<input type="submit" value="Save - Create and Insert License Key">
		</td>
	</tr>
</table>
</div>
<?php } ?>
<input type="hidden" name="lk_account_id" id="lk_account_id" value="">

<br>

<table width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="20%"><br></td>
		<td width="80%">
			<a href="javascript:void(0);" onClick="fToggleNewAccount( document.getElementById('ca_new_account'), this ); new datepickr('ca_expires', { 'dateFormat': 'Y-m-d' });">New account</a><br>
			<div align="left" id="ca_new_account" style="display: none; position: absolute; padding: 2px; width: 350px; background: #FFFFFF; border: 1px solid #000000;">
			<table width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td>
						Username
					</td>
					<td>
						<input type="text" id="ca_username" name="ca_username">
					</td>
				</tr>
				<tr>
					<td>
						Email
					</td>
					<td>
						<input type="text" id="ca_email" name="ca_email">
					</td>
				</tr>
				<tr>
					<td>
						Product
					</td>
					<td>
						<select id="ca_product_type" name="ca_product_type">
							<option> - Select Product - </option>
							<?php
								echo '<option value="' . Product::VIM4 . '"> ' . Product::getName( Product::VIM4 ) . '</option>';
								if ( in_array($GLOBALS['aRepData']['person_id'], $GLOBALS['aModuleRestrictionsEnable'][MOD_NSI]) ) {
									echo '<option value="' . Product::CSI6 . '"> ' . Product::getName( Product::CSI6 ) . '</option>';
									echo '<option value="' . Product::CSI7 . '"> ' . Product::getName( Product::CSI7 ) . '</option>';
								}
							?>
							</select>
					</td>
				</tr>
				<tr>
					<td>
						Expires
					</td>
					<td>
						<input type="text" name="ca_expires" value="<?= date('Y-m-d', time() + (86400*7)) ?>" id="ca_expires">
					</td>
				</tr>
				<tr>
					<td>
						&nbsp;
					</td>
					<td>
						<br>
						<input type="submit" value="Save - Create Account - Edit Details &gt;&gt;" id="btnSubmitAccount">
					</td>
				</tr>
			</table>
			</div>
		</td>
	</tr>
</table>