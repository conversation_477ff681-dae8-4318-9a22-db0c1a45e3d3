<?php

include 'configuration.php';
include INCLUDE_PATH . 'global_functions.php';
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

include('csi7MspLicense.php');

$licenses = new csi7MspLicense();
$range = $licenses->sanitizeDateRange( $_GET['from'], $_GET['to'] );
$customers = $licenses->getMspLicenseHistory( true, $range['from'], $range['to'] );

header('Content-Type: application/csv');
header('Content-Disposition: attachment; filename=msp_license_usage.csv');
header('Pragma: no-cache');
$out = fopen('php://output', 'w' );

fputcsv( $out, array('Date','CST ID','Partition','Partition Created','Salesforce ID','Name','Username','Hosts Granted','Hosts Scanned','Hosts Available','Days Old','Chargeable'), ',', '"' );
foreach( $customers as $customer ) {
	fputcsv( $out, $customer, ',', '"' );
}
