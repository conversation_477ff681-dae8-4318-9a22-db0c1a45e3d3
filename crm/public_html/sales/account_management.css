table {border-spacing: 0;}
.limited-table { width: 700px; }
.limited-table table { width: 100%; }
table a:link {
    color: #666;
    font-weight: bold;
    text-decoration:none;
}
table a:visited {
    color: #999999;
    font-weight:bold;
    text-decoration:none;
}
table a:active,
table a:hover {
    color: #bd5a35;
    text-decoration:underline;
}
table {
    font-family:Arial, Helvetica, sans-serif;
    color:#666;
    font-size:12px;
    text-shadow: 1px 1px 0 #fff;
    background:#eaebec;
    margin:1em;
    border:#ccc 1px solid;
    box-shadow: 0 1px 2px #d1d1d1;
}
table th {
    padding:0.5em;
    border-top:1px solid #fafafa;
    border-bottom:1px solid #e0e0e0;
    background: #ededed;
    border-left: 1px solid #e0e0e0;
}
table th:first-child {
    text-align: left;
    padding-left:0.5em;
}
table tr {
    text-align: center;
    padding-left:0.5em;
}
table td:first-child {
    text-align: left;
    padding-left:20px;
    border-left: 0;
}
table td {
    padding:0.4em;
    border-top: 1px solid #ffffff;
    border-bottom:1px solid #e0e0e0;
    /*border-left: 1px solid #e0e0e0;*/
    background: #fafafa;
}
table tr.even td {
    background: #f6f6f6;
}
table tr:last-child td {
    border-bottom:0;
}
table tr:hover td {
    background: #f2f2f2;
}
.note-message {
    color: gray;
    font-size: 0.8em;
    display: block;
}
table tr td input[type="text"] {
    background: #FFF;
    padding: 2px;
    width: auto;
}

@media print
{
    .no-print, .no-print *, input[type="button"]
    {
        display: none !important;
    }
}
.notice {
    border: 1px solid #6c7678;
    background: #bde5f8;
    padding: 4px;
    color: #00529b;
    text-shadow: none;
}
.bigbutton {
    width: 100%;
}