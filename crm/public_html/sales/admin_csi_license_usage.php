<?php
require_once('csi7MspLicense.php');

function fCompare( $current, $old, $reverse = false ) {
	if ( empty( $_GET['compare'] ) ) {
		return false;
	}

	$diff = ($current - $old);

	if ( ($diff > 0 && !$reverse) || ($diff < 0 && $reverse) ) {
		return '(<font color="DARKGREEN">' . ($diff) . '</font>)&nbsp;';
	}
	if ( ($diff < 0 && !$reverse) || ($diff > 0 && $reverse) ) {
		return '(<font color="RED">' . $diff . '</font>)&nbsp;';
	}

	return false;
}

function formatLastLogin( $lastLogin ) {
	if ( !$lastLogin ) {
		$ret = 'Never logged in';
		$color = 'red';
		$GLOBALS['redLogin']++;
	} else {
		$days = round((time() - strtotime($lastLogin)) / 86400 );
		$ret = $days;// . ' days ago';

		if ( $days > 30 ) {
			$color = 'red';
			$GLOBALS['redLogin']++;
		} else if ( $days > 14 ) {
			$color = '#EBAB00';
			$GLOBALS['yellowLogin']++;
		} else {
			$color = '#000000';
			$GLOBALS['greenLogin']++;
		}
	}
	return '<font style="color: ' . $color . '; padding-right: 3px;">' . $ret . "</font>";
}

function formatLicenseUsage( $total, $used ) {
	if ( !$total ) {
		return '-';
	}

	// 1% = 2.25 pixels
	$unit = 2.25;
	$percentage = round(($used / $total) * 100);

	// Determine base color
	/*if ( $percentage > 75 ) {
		$color = '9aFFB9';
	} else if ( $percentage > 25 ) {
		$color = '#FFFF9A';
	} else {
		$color = '#FF9A9A';
	}*/

	// "progress bar"
	$ret = '<div style="border: 1px solid #000000; width: 225px; background: #FFFFFF; height: 10px;"><div style="background: lightgreen; height: 10px; width: ' . ($unit * $percentage) . 'px "></div></div>';

	return $ret;
}

// Running through browser or as logging cronjob
$isCron = false;
if ( isset( $argv[0] ) ) {
	$isCron = true;

	// Load configuration
	include '/home/<USER>/public_html/crmadmin/crm2/sales/configuration.php';

	// Load sales functions
	include INCLUDE_PATH . 'sales_functions.php';

	// Load global functions
	include INCLUDE_PATH . 'global_functions.php';

	// Open DB Connection
	fOpenDatabase();
} else {
	// Access Level: 1
	// Admin
	// SM
	// Lead Management
	fVerifyAccess( $aRepData['person_id'], 1 );
}

// Input requests
$compare = NULL;
if ( isset( $_GET['compare'] ) ) {
	$data = DBGetRows('ca.nsi_customer_usage_data', "logged LIKE '" . mysql_real_escape_string( $_GET['compare'] ) . "%'");
	foreach( $data as $values ) {
		$compare[$values['account_id']] = $values;
	}
}

$output = array(); $csi4Accounts = 0;
$GLOBALS['redLogin'] = $GLOBALS['yellowLogin'] = $GLOBALS['greenLogin'] = $GLOBALS['redLicense'] = $xx = 0;
$GLOBALS['yellowLicense'] = $GLOBALS['greenLicense'] = $allLicensesAvailable = $allLicensesUsed = $totalAccounts = 0;

$sql =
	"SELECT account_email, account_expires, a.account_id, account_username, a.cst_id, last_login, special_limits
	FROM ca.accounts AS a
	WHERE ( account_esm IS NULL || account_esm = 0 )
	AND special_limits IS NOT NULL AND special_limits NOT IN ( '', 'partner_portal_trial' )
	AND " . ( !empty( $_GET['expires_to'] ) && !empty( $_GET['expires_from'] ) ? "( account_expires >= '" . $_GET['expires_from'] . "' && account_expires <= '" . $_GET['expires_to'] . "' )" : "account_expires >= UTC_TIMESTAMP()" );

$accounts = DBQueryGetRows($sql);

//Get SFDC ID - MSF-xxxxx format
$sfDc = array();
$sfPivotSql = 'SELECT salesforce_id, sfdc_id FROM crm.sfdc';
$sfDcIds = DBQueryGetRows($sfPivotSql);
foreach ($sfDcIds as $value) {
    $sfDc[$value['salesforce_id']] = $value['sfdc_id'];
}

foreach( $accounts as $values ) {
	// CRM Details
	$customer = fGetCustomerDetails( $values['cst_id'] );
	$license = new csi7MspLicense();
	$salesForceId = $license->fixSalesforceId( array( 0 => array( 'cst_id' => $values['cst_id' ] ) ) );
	$salesForceId = $salesForceId[0]['salesforce_id'];
	$sfDcId = '-';
	if ( $salesForceId == '' ) {
		$salesForceId = '-';
	}
	if ($salesForceId != '-') {
		if (isset($sfDc[$salesForceId])) {
			$sfDcId = $sfDc[$salesForceId];
		}
	}

	// Longest duration between start and end of license
	/*$maxDuration = DBGetRowValue('ca.license_keys', 'max(datediff(valid_to, valid_from))', "account_id = '" . $values['account_id'] . "'");
	if ( $maxDuration < 60 ) {
		continue;
	}*/

	$totalAccounts++;
	$csi4Accounts += ( strstr($values['special_limits'], 'csi_40') ? 1 : 0 );

	$max_last_login = DBGetRowValue('ca.accounts ', 'MAX(last_login) as max_last_login', 'cst_id = ' . (int)$values['cst_id']);

	// License Details
	if ( $values['special_limits'] == 'csi_70' ) {
		$csi7licenses = DBQueryGetRows( 'SELECT num_granted, num_available
			FROM ca.partition_license_pool
			WHERE cst_id = ' . (int) $values['cst_id'] . '
			AND license_type = 1
			AND partition_id = 0
			AND expiry > UTC_TIMESTAMP()'
		);
		if( !empty( $csi7licenses ) ) {
			$licensesAvailable = $csi7licenses[0]['num_granted'];
			$licensesUsed = $csi7licenses[0]['num_granted'] - $csi7licenses[0]['num_available'];
		} else {
			$licensesAvailable = 0;
			$licensesUsed = 0;
		}
	} else {
		$licensesAvailable = DBGetRowValue('ca.license_keys', 'sum(quantity)', "account_id = '" . $values['account_id'] . "' && valid_from < now() && valid_to > now()");
		$licensesUsed = DBGetRowValue('ca.license_hosts, ca.license_keys', 'count(*)', "license_keys.account_id = '" . $values['account_id'] . "' && valid_from < now() && valid_to > now() && license_keys.id = license_hosts.license_id");
	}
	$allLicensesAvailable += $licensesAvailable;
	$allLicensesUsed += $licensesUsed;

	if ( $licensesAvailable > 0 ) {
		$licensePercentage = round(($licensesUsed / $licensesAvailable) * 100);
	} else {
		$licensePercentage = 0;
	}
	if ( $licensePercentage > 75 ) {
		$licensePercentageColor = 'darkgreen';
		$GLOBALS['redLicense']++;
	} else if ( $licensePercentage > 25 ) {
		$licensePercentageColor = '#EBAB00';
		$GLOBALS['yellowLicense']++;
	} else {
		$licensePercentageColor = 'red';
		$GLOBALS['greenLicense']++;
	}

	$product = explode('_', $values['special_limits']);
	$module = array(
		'ip_access' => 'No',
		'user_management' => 'No',
		'sccm' => 'No',
		'active_directory' => 'No',
		'zeroday' => 'No',
		'threat_basic' => 'No', 
		'threat_adv' => 'No',
		'vpm' => 'No'
	);
	switch( $product[0] ){
		case 'csi':
			$productName = 'CSI';
			if ( $values['special_limits'] == 'csi_70' ) {
				$noPartitions = DBQueryGetRows( 'SELECT DISTINCT partition_id
					FROM ca.partition_license_pool
					WHERE cst_id = ' . (int) $values['cst_id'] . ''
				);
				$noPartitions = count($noPartitions);
			} else {
				$noPartitions = 0;
			}
			$query = DBQueryGetRows( 'SELECT module_id FROM ca.modules_customers
				WHERE cst_id = ' . (int) $values['cst_id'] );

			if ( !empty( $query ) ) {
				foreach( $query as $val ) {
					switch( $val['module_id'] ) {
						case 1:
							$module['ip_access'] = 'Yes';
							break;
						case 2:
							$module['user_management'] = 'Yes';
							break;
						case 3:
							$module['sccm'] = 'Yes';
							break;
						case 4:
							$module['active_directory'] = 'Yes';
							break;
						case 5:
							$module['zeroday'] = 'Yes';
							break;
						case 6:
							$module['threat_basic'] = 'Yes';
							break;
						case 7:
							$module['threat_adv'] = 'Yes';
							break;
						case 8:
							$module['vpm'] = 'Yes';
							break;
					}
				}
			}
			break;
		case 'vim':
			$productName = 'VIM';
			$noPartitions = 0;
			break;
		default:
			$productName = $values['special_limits'];
			$noPartitions = 0;
			break;
	}
	$productVersion = substr( $product[1], 0, 1 ) . '.' . substr( $product[1], 1 );

	// Logging data?
	if ( $isCron ) {
		// Patching?
		$wsus = DBGetRowValue('ca.nsi_usage_log', 'sum(value)', "account_id = '" . $values['account_id'] . "' && type = 'WSUS-SRV-HOSTS'");
		$wsusPatches = DBGetRowValue('ca.nsi_usage_log', 'count(*)', "account_id = '" . $values['account_id'] . "' && type = 'WSUS-SRV-PACKAGE'");

		DBQuery("INSERT INTO ca.nsi_customer_usage_data SET logged = NOW(), account_id = '" . $values['account_id'] ."', cst_id = '" . $values['cst_id'] . "', csi_version = '" . mysql_real_escape_string( $csiVersion ) . "', licenses_available = '" . $licensesAvailable . "', licenses_used = '" . $licensesUsed . "', last_login = '" . $values['last_login'] . "', wsus_hosts = '" . (int) $wsus . "', wsus_packages = '" . (int) $wsusPatches . "'");
		echo ++$xx . "INSERT INTO ca.nsi_customer_usage_data SET logged = NOW(), account_id = '" . $values['account_id'] ."', cst_id = '" . $values['cst_id'] . "', csi_version = '" . mysql_real_escape_string($csiVersion) . "', licenses_available = '" . $licensesAvailable . "', licenses_used = '" . $licensesUsed . "', last_login = '" . $values['last_login'] . "', wsus_hosts = '" . (int) $wsus . "', wsus_packages = '" . (int) $wsusPatches . "'";
		continue;
	}

	// Sort key
	$sortKey = '';
	if ( isset( $_GET['sort'] ) ) {
		switch ( $_GET['sort'] ) {
			case 'lastLogin':
				$sortKey = $values['last_login'];
				$sortFlag = SORT_REGULAR;
				break;
			case 'licensePercentage':
				$sortKey = $licensePercentage;
				$sortFlag = SORT_NUMERIC;
				break;
			case 'licensesAvailable':
				$sortKey = $licensesAvailable;
				$sortFlag = SORT_NUMERIC;
				break;
			case 'licensesUsed':
				$sortKey = $licensesUsed;
				$sortFlag = SORT_NUMERIC;
				break;
			case 'wsusHosts':
				$sortKey = $wsus;
				$sortFlag = SORT_NUMERIC;
				break;
			case 'wsusPatches':
				$sortKey = $wsusPatches;
				$sortFlag = SORT_NUMERIC;
				break;
			default:
				$sortKey = $values['account_expires'];
				$sortFlag = SORT_REGULAR;
				break;
		}
	}
	$sortKey .= '___' . $values['account_id'];

	$divider = isset($compare[$values['account_id']]) && isset($compare[$values['account_id']]['licenses_available']) ? $compare[$values['account_id']]['licenses_available'] : 0;
	if ( $divider == 0 ) {
		$percentage = 0;
	} else {
		$percentage = round( $compare[$values['account_id']]['licenses_used'] / $divider * 100);
	}

	if ( empty( $_GET['compare'] ) ) {
		$getCompare = 0;
	} else {
		$getCompare = $_GET['compare'];
	}

	$output[$sortKey] = '
<tr>
	<td>' . $customer['Company']['cst_id'] . '</td>
	<td>' . $sfDcId . ' </td>
	<td>' . $salesForceId . ' </td>
	<td>' . htmlspecialchars( $customer['Company']['name'] ) . '</td>
	<td><a href="?page=ca_account_management&cst_id=' . $values['cst_id'] . '&account_id=' . $values['account_id'] . '">' . htmlspecialchars($values['account_username']) . '</a></td>
	<td>' . $values['account_expires'] . '</td>
	<td>' . $productName . '</td>
	<td>' . $productVersion . '</td>
	<td align="right">' . $licensesAvailable . '</td>
	<td align="right">' . $licensesUsed . '</td>
	<td align="right" style="padding-right: 5px; color: ' . $licensePercentageColor . ';">' . $licensePercentage . '%</td>
	<td align="right">' . $noPartitions . '</td>
	<td align="right">' . formatLastLogin( $values['last_login'] ) . '</td>
	<td align="right">' . formatLastLogin( $max_last_login ) . '</td>
	<td>' . htmlspecialchars( $values['account_email']) . '</td>
	<td>' . $module['sccm'] . '</td>
	<td>' . $module['zeroday'] . '</td>
	<td>' . $module['ip_access'] . '</td>
	<td>' . $module['user_management'] . '</td>
	<td>' . $module['active_directory'] . '</td>
	<td>' . $module['threat_basic'] . '</td>
	<td>' . $module['threat_adv'] . '</td>
	<td>' . $module['vpm'] . '</td>
</tr>';
}
if ( isset( $_GET['sort'] ) ) {
	ksort( $output, $sortFlag );
}

if ( $isCron ) {
	exit();
}

$options = DBQueryGetRows("SELECT DISTINCT LEFT(logged, 10) AS date FROM ca.nsi_customer_usage_data ORDER BY date");
$compareOptions = '';
foreach( $options as $values ) {
	$compareOptions .= '<option value="' . $values['date'] . '"' . ( $values['date'] == $_GET['compare'] ? ' selected' : '' ) . '>' . $values['date'] . '</option>';
}
?>
<link rel="stylesheet" type="text/css" href="account_management.css" />
<table width="99%">
	<?php
	// Head line
	$sTitle = 'Flexera SVM Account Overview and License Usage';
	$iColSpan = 3;
	$bPrint = true;
	$bWindowTitle = true;
	include INCLUDE_PATH . 'page_header.php';

	if ( isset( $_GET['expires_from'] ) ) {
		$expiresFrom = $_GET['expires_from'];
	} else {
		$expiresFrom = '';
	}
	if ( isset( $_GET['expires_to'] ) ) {
		$expiresTo = $_GET['expires_to'];
	} else {
		$expiresTo = '';
	}
	if ( isset( $_GET['sort'] ) ) {
		$sort = $_GET['sort'];
	} else {
		$sort = '';
	}
?>
</table>
<form method="GET" action="?">
	<input type="hidden" name="page" value="admin_csi_license_usage">
	<table>
		<tr>
			<td><label for="expires_from">Account Expires From <input type="text" name="expires_from" value="<?= htmlspecialchars($expiresFrom) ?>" /></label></td>
			<td><label for="expires_to">Account Expires To <input type="text" name="expires_to" value="<?= htmlspecialchars($expiresTo) ?>" /></label></td>
			<td><label for="compare">Compare Against
				<select name="compare">
					<option>- Select Logged Data -</option>
					<?= $compareOptions ?>
				</select>
				</label>
			</td>
			<td><label for="sort">Sorted By
				<select name="sort">
					<option>Account Expires</option>
					<option value="lastLogin"<?= ( $sort == 'lastLogin' ? ' selected' : '' ) ?>>Last Login</option>
					<option value="licensePercentage"<?= ( $sort == 'licensePercentage' ? ' selected' : '' ) ?>>License Usage Percent</option>
					<option value="licensesAvailable"<?= ( $sort == 'licensesAvailable' ? ' selected' : '' ) ?>>Licenses Available</option>
					<option value="licensesUsed"<?= ( $sort == 'licensesUsed' ? ' selected' : '' ) ?>>Licenses Used</option>
				</select>
			</td>
			<td><input type="submit" value="Update"></td>
			<td><a href="#" class="export">Export data into Excel</a></td>
		</tr>
	</table>
</form>

<form method="POST" action="?<?= htmlspecialchars($_SERVER['QUERY_STRING']) ?>">
<div id="dvData">
	<table>
		<tr>
			<th>CRM ID</th>
			<th>SFDC ID</th>
			<th>SF ID</th>
			<th>Customer Name</th>
			<th>Username</th>
			<th>Expires</th>
			<th>Product</th>
			<th>Version</th>
			<th>Licenses Available</th>
			<th>Licenses Used</th>
			<th>License Usage</th>
			<th>Partitions</th>
			<th>Last Login (days ago)</th>
			<th>Recent Login (days ago)</th>
			<th>E-mail address</th>
			<th>SCCM Plugin</th>
			<th>0-Day</th>
			<th>IP Access</th>
			<th>User Management</th>
			<th>Active Directory</th>
			<th>Threat Intel Basic</th>
			<th>Threat Intel Adv</th>
			<th>VPM</th>
		</tr>
<?= implode($output) ?>
		<tr>
			<td class="horline" colspan="6"><b>Total / Averages:</b></td>
			<td class="horline" ><?= number_format($csi4Accounts) ?> SVM of <?= number_format($totalAccounts) ?></td>
			<td class="horline right"><?= number_format( $allLicensesAvailable ) ?></td>
			<td class="horline right"><?= number_format( $allLicensesUsed ) ?></td>
			<td class="horline right"><?= $allLicensesAvailable ? round($allLicensesUsed / $allLicensesAvailable * 100, 2) : 0 ?>%<br><?= '<font color="red">' . $GLOBALS['redLicense'] . '</font> / <font color="#EBAB00">' . $GLOBALS['yellowLicense'] . '</font> / ' . $GLOBALS['greenLicense'] ?></td>
			<td class="horline"></td>
			<td class="horline right" colspan="7"><?= '<font color="red">' . $GLOBALS['redLogin'] . '</font> / <font color="#EBAB00">' . $GLOBALS['yellowLogin'] . '</font> / ' . $GLOBALS['greenLogin'] ?></td>
		</tr>
	</table>
</div>
</form>

<style type="text/css">
	.horline{
		border-top: 1px solid black;
	}
	.right {
		text-align: right;
	}
</style>
<script type="text/javascript" src="https://code.jquery.com/jquery-latest.min.js"></script>
<script type="text/javascript">
$(document).ready(function () {
	function exportTableToCSV(table, filename) {

		var rows = table.find('tr:has(td)'),

		// Temporary delimiter characters unlikely to be typed by keyboard
		// This is to avoid accidentally splitting the actual contents
		tmpColDelim = String.fromCharCode(11), // vertical tab character
		tmpRowDelim = String.fromCharCode(0), // null character

		// actual delimiter characters for CSV format
		colDelim = '","',
		rowDelim = '"\r\n"',

		// Grab text from table into CSV formatted string
		csv = '"' + rows.map(function (i, row) {
			var $row = $(row),
			$cols = $row.find('td');

			return $cols.map(function (j, col) {
				var $col = $(col),
				text = $col.text();

				return text.replace('"', '""'); // escape double quotes

			}).get().join(tmpColDelim);
		}).get().join(tmpRowDelim)
		.split(tmpRowDelim).join(rowDelim)
		.split(tmpColDelim).join(colDelim) + '"',

		// Data URI
		csvData = 'data:application/csv;charset=utf-8,' + encodeURIComponent(csv);

		$(this).attr({
			'download': filename,
			'href': csvData,
			'target': '_blank'
		});
	}

	// This must be a hyperlink
	$(".export").on('click', function (event) {
		// CSV
		exportTableToCSV.apply(this, [$('#dvData>table'), 'export.csv']);

		// IF CSV, don't do event.preventDefault() or return false
		// We actually need this to be a typical hyperlink
	});
});
</script>
