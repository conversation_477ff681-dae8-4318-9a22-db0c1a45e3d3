<?php
// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

if ( empty($_GET['cst_id']) && empty($_GET['accountId']) ) {
	exit();
}

// Hide Internal IT's account from everyone
if ( isset( $_GET['accountId'] ) && $_GET['accountId'] == "055819634218080903" ) {
//	exit();
}

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

if (getenv('REMOTE_USER') == "stimmerman"){
	$aRepData = DBGetRow('crm.salespeople', "init = 'nschwensfeier'");
	print_r($aRepData);
} else {
	$aRepData = DBGetRow('crm.salespeople', "init = '" . getenv('REMOTE_USER') . "'");
}

// Special handler: Incoming new SalesForce account
if ( !$_GET['cst_id'] || !DBGetRowValue('crm.cst', 'cst_id', "cst_id = '" . $_GET['cst_id'] . "'") ) {
	// Test if we have an account for this SF id
	if ( !DBGetRow('crm.cst', "salesforce_id = '" . $_GET['accountId'] . "'") ) {
		// IF NO: Create company card, and 'card_sf' with ref to new company card
		// Company Card
		DBQuery("INSERT INTO crm.cst SET name = 'SF Account: " . $_GET['accountId'] . "', case_name = 'Company Card', salesforce_id = '" . $_GET['accountId'] . "'");
		$companyID = mysql_insert_id();

		// SF Case
		DBQuery("INSERT INTO crm.cst SET name = 'SF Account: " . $_GET['accountId'] . "', case_name = 'card_sf', master_id = '" . $companyID . "'");
		$_GET['cst_id'] = mysql_insert_id();

	} else {
		// Override '$_GET['cst_id']' with ID of 'card_sf'
		$_GET['cst_id'] = DBGetRowValue('crm.cst', 'cst_id', "salesforce_id = '" . $_GET['accountId'] . "'");
	}
}

$aCustomer = fGetCustomerDetails( $_GET['cst_id'] );

// TMP: Override (we are in iframe)
$_GET['iframe'] = 1;

?>

<html>
<head>
        <title>Secunia CRM 2.0 - Sales</title>
        <link rel="stylesheet" TYPE="text/css" HREF="default.css">
        <script src="customer.js"></script>

<script>
        // Make sure that we always have the correct mouse pos.
        {
                // Detect if Internet Explorer
                var IE = ( document.all ? true : false );
                var last_focus = '';
                var cshow;

                // If NS -- that is, !IE -- then set up for mouse capture
                if ( !IE ) {
                        document.captureEvents(Event.MOUSEMOVE);
                }

                // Set-up to use getMouseXY function onMouseMove
                document.onmousemove = getMouseXY;

                // Temporary variables to hold mouse x-y pos.s
                var tempX = 0;
                var tempY = 0;
        }

        // Main function to retrieve mouse x-y pos.s
        function getMouseXY(e) {
                // grab the x-y pos.s if browser is IE
                if (IE) {
                        tempX = event.clientX + document.body.scrollLeft
                        tempY = event.clientY + document.body.scrollTop
                } else {
                        // grab the x-y pos.s if browser is NS
                        tempX = e.pageX
                        tempY = e.pageY
                }

                // catch possible negative values in NS4
                if ( tempX < 0 ) {
                        tempX = 0
                }
                if ( tempY < 0 ) {
                        tempY = 0
                }
                return true
        }
        </script>

</head>
<body>

<form method="POST" action="index.php?page=save_iframe" target="_blank">
<input type="hidden" name="cst_id" value="<?= intval($_GET['cst_id']) ?>">
<?php

// Load content
require 'c_ca_accounts.php';

?>
</form>
<script type="text/javascript">

	/**
	 * Validates the New Account form for VIM 4 accounts
	 * @bug 5499
	 * @note All forms on this page are inside a single FORM tag.
	 * @return {Boolean}
	 *	false if the form is invalid
	 *	true if the form is valid
	 */
	function validatePopupAccountForm() {
		var productTypeSelect = document.getElementById( "ca_product_type" );
		var emailAddress = document.getElementById("ca_email").value;

		// Require the username field to be filled out
		var error = "";
		if ( "" === document.getElementById("ca_username").value ) {
			error += "Please enter a username\n";
		}

		// Only VIM 4 is currently enforcing email addresses on New Account creation
		if ( <?=Product::VIM4?> === parseInt( productTypeSelect.options[productTypeSelect.selectedIndex].value, 10 ) ) {
			if ( "" === emailAddress ) {
				error += "Please enter a valid email address\n";
			}
		}

		// Rudimentary email address validation if an email is supplied
		if ( emailAddress && false === /\S+@\S+\.\S+/.test( emailAddress ) ) {
			error += "The email address is not valid\n";
		}

		// Handle any error and then return validation
		if ( "" !== error ) {
			alert( error );
			return false;
		}
		return true;
	};
	/**
	 * Binds an onclick listener to the Create Account button so we can validate
	 * the New Account form only when Create Account is clicked. The reason for
	 * doing it this way instead of via onsubmit is due to the Create License
	 * Key forms sharing the New Account form's FORM tag.
	 */
	(function () {
		var btn = document.getElementById( "btnSubmitAccount" );
		if ( btn.addEventListener) {
			// Webkit + FF
			btn.addEventListener( "click", function( e ) {
				if ( !validatePopupAccountForm() ) { e.preventDefault(); }
			}, false );
		} else {
			// IE
			btn.attachEvent( "onclick", function( e ) {
				if ( !validatePopupAccountForm() ) { e.returnValue = false; }
			} );
		}
	})();
</script>
</body>
</html>
