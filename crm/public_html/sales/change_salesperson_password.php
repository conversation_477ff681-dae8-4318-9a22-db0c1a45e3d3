
<?php

// Load configuration
include 'configuration.php';

$username = getenv('REMOTE_USER');
if (!isset($username)){
	header("WWW-Authenticate: Basic realm=\"".$realmName."\"");
	header("HTTP/1.0 401 Authorized");
	echo "Please authenticate";
	exit;
}

$conn = mysql_connect(DB_HOST_CRM, DB_USER_CRM, DB_PASS_CRM) or die("Connection Error: " . mysql_error($conn));
$result = mysql_query("SELECT count(*) as cnt from crm.salespeople WHERE init='" . $username . "'");
$row = mysql_fetch_array($result);

if ($row["cnt"] != 1) {
    echo "Logged user `" . $username . "` doesn't exist in the database. Check with SRE.";
    die();
}

?>

<html>
<head>
    <title>Setup password for AWS CSI <?php echo $username; ?></title>
    <script>

function scorePassword(pass) {
    var score = 0;
    if (!pass)
        return score;

    // award every unique letter until 5 repetitions
    var letters = new Object();
    for (var i=0; i<pass.length; i++) {
        letters[pass[i]] = (letters[pass[i]] || 0) + 1;
        score += 5.0 / letters[pass[i]];
    }

    // bonus points for mixing it up
    var variations = {
        digits: /\d/.test(pass),
        lower: /[a-z]/.test(pass),
        upper: /[A-Z]/.test(pass),
        nonWords: /\W/.test(pass),
    }

    variationCount = 0;
    for (var check in variations) {
        variationCount += (variations[check] == true) ? 1 : 0;
    }
    score += (variationCount - 1) * 10;

    return parseInt(score);
}

function checkPassStrength() {
    newPassword = document.frmChange.newPassword;
    var score = scorePassword(newPassword.value);
    if (score === 0)
        document.getElementById("newPassword").innerHTML = "required";
    if (score > 80)
        document.getElementById("newPassword").innerHTML = "strong";
    else if (score >= 60)
        document.getElementById("newPassword").innerHTML = "good";
    else if (score >= 30)
        document.getElementById("newPassword").innerHTML = "weak";
    else {
        document.getElementById("newPassword").innerHTML = "too weak, will not save";
    }
    console.log('checkPassStrength ' + score + ' for ' + newPassword.value);
}

function validatePassword() {
var newPassword,confirmPassword,output = true;

newPassword = document.frmChange.newPassword;
confirmPassword = document.frmChange.confirmPassword;

var score = scorePassword(newPassword.value);
if(score < 30) {
newPassword.focus();
document.getElementById("newPassword").innerHTML = "required and / or too weak password";
output = false;
}
else if(!confirmPassword.value) {
confirmPassword.focus();
document.getElementById("confirmPassword").innerHTML = "required";
output = false;
}
if(newPassword.value != confirmPassword.value) {
newPassword.value="";
confirmPassword.value="";
newPassword.focus();
document.getElementById("confirmPassword").innerHTML = "not same";
output = false;
}
return output;
}
</script>
</head>
<body>
<form name="frmChange" method="post" action="" onSubmit="return validatePassword()">
<div style="width:500px;">
<div class="message"><?php if(isset($message)) { echo $message; } ?></div>
<table border="0" cellpadding="10" cellspacing="0" width="500" align="center" class="tblSaveForm">
<tr class="tableheader">
<td colspan="2">Change Password <?php echo $username; ?></td>
</tr>
<tr>
<td><label>New Password</label></td>
<td><input type="password" name="newPassword" class="txtField" onfocusout="checkPassStrength()"/><span id="newPassword" class="required"></span></td>
</tr>
<td><label>Confirm Password</label></td>
<td><input type="password" name="confirmPassword" class="txtField"/><span id="confirmPassword" class="required"></span></td>
</tr>
<tr>
<td colspan="2"><input type="submit" name="submit" value="Submit" class="btnSubmit"></td>
</tr>
</table>
</div>
</form>
</body></html>



<?php

function crypt_apr1_md5($plainpasswd)
{
    $salt = substr(str_shuffle("abcdefghijklmnopqrstuvwxyz0123456789"), 0, 8);
    $len = strlen($plainpasswd);
    $text = $plainpasswd.'$apr1$'.$salt;
    $bin = pack("H32", md5($plainpasswd.$salt.$plainpasswd));
    for($i = $len; $i > 0; $i -= 16) { $text .= substr($bin, 0, min(16, $i)); }
    for($i = $len; $i > 0; $i >>= 1) { $text .= ($i & 1) ? chr(0) : $plainpasswd[0]; }
    $bin = pack("H32", md5($text));
    for($i = 0; $i < 1000; $i++)
    {
        $new = ($i & 1) ? $plainpasswd : $bin;
        if ($i % 3) $new .= $salt;
        if ($i % 7) $new .= $plainpasswd;
        $new .= ($i & 1) ? $bin : $plainpasswd;
        $bin = pack("H32", md5($new));
    }
    $tmp = '';
    for ($i = 0; $i < 5; $i++)
    {
        $k = $i + 6;
        $j = $i + 12;
        if ($j == 16) $j = 5;
        $tmp = $bin[$i].$bin[$k].$bin[$j].$tmp;
    }
    $tmp = chr(0).chr(0).$bin[11].$tmp;
    $tmp = strtr(strrev(substr(base64_encode($tmp), 2)),
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
    "./0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");

    return "$"."apr1"."$".$salt."$".$tmp;
}


if (count($_POST) > 0) {
    // Encrypt password
    $password = $_POST["newPassword"];
    $encrypted_password = crypt_apr1_md5($password);

    $filepass = $username . ':' . $encrypted_password;

    mysql_query("UPDATE crm.salespeople set htpasswd='" . $filepass . "' WHERE init='" . $username . "'");

    echo 'Password was updated: ' . $filepass;
}

?>
