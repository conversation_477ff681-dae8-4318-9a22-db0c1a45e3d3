/*!
 Foundation integration for DataTables' Editor
 ©2015 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-zf","datatables.net-editor"],function(b){return c(b,window,document)}):"object"===typeof exports?module.exports=function(b,e){b||(b=window);e&&e.fn.dataTable||(e=require("datatables.net-zf")(b,e).$);e.fn.dataTable.Editor||require("datatables.net-editor")(b,e);return c(e,b,b.document)}:c(jQuery,window,document)})(function(c,b,e,d){d=c.fn.dataTable;d.Editor.defaults.display="foundation";c.extend(!0,c.fn.dataTable.Editor.classes,
{field:{wrapper:"DTE_Field row",label:"small-4 columns inline",input:"small-8 columns",error:"error",multiValue:"panel radius multi-value",multiInfo:"small",multiRestore:"panel radius multi-restore","msg-labelInfo":"label secondary","msg-info":"label secondary","msg-message":"label secondary","msg-error":"label alert"},form:{button:"button small"}});d.Editor.display.foundation=c.extend(!0,{},d.Editor.models.displayController,{init:function(b){a._dom.content=c('<div class="reveal reveal-modal DTED" data-reveal />');
a._dom.close=c('<button class="close close-button">&times;</div>');a._dom.close.click(function(){a._dte.close("icon")});return a},open:function(f,d,g){a._shown?g&&g():(a._dte=f,a._shown=!0,f=a._dom.content,f.children().detach(),f.append(d),f.prepend(a._dom.close),c(a._dom.content).one("open.zf.reveal",function(){g&&g()}).one("closed.zf.reveal",function(){a._shown=!1}),b.Foundation&&b.Foundation.Reveal?(a._reveal||(a._reveal=new b.Foundation.Reveal(a._dom.content,{closeOnClick:!1})),a._reveal.open()):
c(a._dom.content).foundation("reveal","open"),c(e).on("click.dte-zf","div.reveal-modal-bg, div.reveal-overlay",function(b){c(b.target).closest(a._dom.content).length||a._dte.background()}))},close:function(b,d){a._shown&&(a._reveal?a._reveal.close():c(a._dom.content).foundation("reveal","close"),c(e).off("click.dte-zf"),a._dte=b,a._shown=!1);d&&d()},node:function(b){return a._dom.content[0]},_shown:!1,_dte:null,_dom:{}});var a=d.Editor.display.foundation;return d.Editor});
