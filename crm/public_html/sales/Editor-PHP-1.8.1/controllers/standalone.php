<?php

// The Editor standalone examples don't actually save to the database as the
// behaviour of such an edit will depend highly upon the structure of your
// database. However using the Database methods such as update() this can easily
// be done.
//
// See the PHP manual - http://editor.datatables.net/manual/php - for
// information on how to use the Editor PHP libraries.

echo json_encode( array(
	"data" => array( $_POST['data']['keyless'] )
) );

