<?php
/**
 * SQL Server driver for DataTables PHP libraries
 *
 *  <AUTHOR>
 *  @copyright 2013 SpryMedia ( http://sprymedia.co.uk )
 *  @license   http://editor.datatables.net/license DataTables Editor
 *  @link      http://editor.datatables.net
 */

namespace DataTables\Database\Driver;
if (!defined('DATATABLES')) exit();

use PDO;
use DataTables\Database\Result;


/**
 * SQL Server driver for DataTables Database Result class
 *  @internal
 */
class SqlserverResult extends Result {
	/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 * Constructor
	 */

	function __construct( $dbh, $stmt )
	{
		$this->_dbh = $dbh;
		$this->_stmt = $stmt;
	}



	/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 * Private properties
	 */

	private $_stmt;
	private $_dbh;



	/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 * Public methods
	 */

	public function count ()
	{
		return count($this->fetchAll());
	}


	public function fetch ( $fetchType=\PDO::FETCH_ASSOC )
	{
		return $this->_stmt->fetch( $fetchType );
	}


	public function fetchAll ( $fetchType=\PDO::FETCH_ASSOC )
	{
		return $this->_stmt->fetchAll( $fetchType );
	}


	public function insertId ()
	{
		return $this->_dbh->lastInsertId();
	}
}

