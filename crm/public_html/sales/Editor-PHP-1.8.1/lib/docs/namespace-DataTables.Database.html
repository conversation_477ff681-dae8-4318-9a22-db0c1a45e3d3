<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Namespace DataTables\Database | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li class="active">
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.Query.html">Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">Result</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li class="active">
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="namespace">
	<h1>Namespace <a href="namespace-DataTables.html">DataTables</a>\Database</h1>



<table class="summary" id="classes">
<caption>Classes summary</caption>
<tr>
	<td class="name"><a href="class-DataTables.Database.Query.html">Query</a></td>
	<td>Perform an individual query on the database.</td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Database.Result.html">Result</a></td>
	<td>Result object given by a <code><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></code> performed on a database.</td>
</tr>
</table>





</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
