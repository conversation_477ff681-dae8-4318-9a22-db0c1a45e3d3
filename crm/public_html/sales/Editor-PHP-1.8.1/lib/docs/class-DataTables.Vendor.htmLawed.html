<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Class DataTables\Vendor\htmLawed | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li class="active">
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">Htmlaw</a></li>
				<li class="active"><a href="class-DataTables.Vendor.htmLawed.html">htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
				<a href="namespace-DataTables.Vendor.html" title="Summary of DataTables\Vendor"><span>Namespace</span></a>
			</li>
			<li class="active">
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="class">
	<h1>Class htmLawed</h1>











	<div class="info">
		
		
		<b>Namespace:</b> <a href="namespace-DataTables.html">DataTables</a>\<a href="namespace-DataTables.Vendor.html">Vendor</a><br>
		

			<b>Located at</b> <a href="source-class-DataTables.Vendor.htmLawed.html#15-737" title="Go to source code">Vendor/htmLawed/htmLawed.php</a>
		<br>
	</div>



	<table class="summary methods" id="methods">
	<caption>Methods summary</caption>
	<tr data-order="hl" id="_hl">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#17-118" title="Go to source code">hl</a>( <span> <var>$t</var></span>, <span> <var>$C</var> = <span class="php-num">1</span></span>, <span> <var>$S</var> = <span class="php-keyword1">array</span>()</span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_attrval" id="_hl_attrval">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_attrval">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#120-154" title="Go to source code">hl_attrval</a>( <span> <var>$a</var></span>, <span> <var>$t</var></span>, <span> <var>$p</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_bal" id="_hl_bal">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_bal">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#156-310" title="Go to source code">hl_bal</a>( <span> <var>$t</var></span>, <span> <var>$do</var> = <span class="php-num">1</span></span>, <span> <var>$in</var> = <span class="php-quote">'div'</span></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_cmtcd" id="_hl_cmtcd">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_cmtcd">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#312-325" title="Go to source code">hl_cmtcd</a>( <span> <var>$t</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_ent" id="_hl_ent">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_ent">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#327-341" title="Go to source code">hl_ent</a>( <span> <var>$t</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_prot" id="_hl_prot">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_prot">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#343-374" title="Go to source code">hl_prot</a>( <span> <var>$p</var></span>, <span> <var>$c</var> = <span class="php-keyword1">null</span></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_regex" id="_hl_regex">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_regex">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#376-390" title="Go to source code">hl_regex</a>( <span> <var>$p</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_spec" id="_hl_spec">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_spec">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#392-421" title="Go to source code">hl_spec</a>( <span> <var>$t</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_tag" id="_hl_tag">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_tag">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#423-630" title="Go to source code">hl_tag</a>( <span> <var>$t</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_tag2" id="_hl_tag2">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_tag2">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#632-654" title="Go to source code">hl_tag2</a>( <span> <var>&amp; $e</var></span>, <span> <var>&amp; $a</var></span>, <span> <var>$t</var> = <span class="php-num">1</span></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_tidy" id="_hl_tidy">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_tidy">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#656-709" title="Go to source code">hl_tidy</a>( <span> <var>$t</var></span>, <span> <var>$w</var></span>, <span> <var>$p</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="hl_version" id="_hl_version">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_hl_version">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#711-715" title="Go to source code">hl_version</a>( )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="kses" id="_kses">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_kses">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#717-729" title="Go to source code">kses</a>( <span> <var>$t</var></span>, <span> <var>$h</var></span>, <span> <var>$p</var> = <span class="php-keyword1">array</span>(<span class="php-quote">'http'</span>, <span class="php-quote">'https'</span>, <span class="php-quote">'ftp'</span>, <span class="php-quote">'news'</span>, <span class="php-quote">'nntp'</span>, <span class="php-quote">'telnet'</span>, <span class="php-quote">'gopher'</span>, <span class="php-quote">'mailto'</span>)</span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="kses_hook" id="_kses_hook">

		<td class="attributes"><code>
			 public static

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_kses_hook">#</a>
		<code><a href="source-class-DataTables.Vendor.htmLawed.html#731-735" title="Go to source code">kses_hook</a>( <span> <var>$t</var></span>, <span> <var>&amp; $C</var></span>, <span> <var>&amp; $S</var></span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	</table>


















</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js"></script>
<script src="elementlist.js"></script>
</body>
</html>
