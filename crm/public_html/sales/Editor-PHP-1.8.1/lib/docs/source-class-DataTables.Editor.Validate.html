<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Validate.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">   1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">   2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">   3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">   4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">   5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">   6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">   7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">   8: </a><span class="php-comment"> *  @copyright 2012-2014 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">   9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10">  10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11">  11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12">  12: </a>
</span><span id="13" class="l"><a href="#13">  13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14">  14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15">  15: </a>
</span><span id="16" class="l"><a href="#16">  16: </a><span class="php-keyword1">use</span> DataTables\Editor\ValidateOptions;
</span><span id="17" class="l"><a href="#17">  17: </a>
</span><span id="18" class="l"><a href="#18">  18: </a><span class="php-comment">/**
</span></span><span id="19" class="l"><a href="#19">  19: </a><span class="php-comment"> * Validation methods for DataTables Editor fields.
</span></span><span id="20" class="l"><a href="#20">  20: </a><span class="php-comment"> *
</span></span><span id="21" class="l"><a href="#21">  21: </a><span class="php-comment"> * These methods will typically be applied through the {@link Field::validator}
</span></span><span id="22" class="l"><a href="#22">  22: </a><span class="php-comment"> * method and thus the arguments to be passed will be automatically resolved
</span></span><span id="23" class="l"><a href="#23">  23: </a><span class="php-comment"> * by Editor.
</span></span><span id="24" class="l"><a href="#24">  24: </a><span class="php-comment"> *
</span></span><span id="25" class="l"><a href="#25">  25: </a><span class="php-comment"> * The validation methods in this class all take three parameters:
</span></span><span id="26" class="l"><a href="#26">  26: </a><span class="php-comment"> *
</span></span><span id="27" class="l"><a href="#27">  27: </a><span class="php-comment"> * 1. Data to be validated
</span></span><span id="28" class="l"><a href="#28">  28: </a><span class="php-comment"> * 2. Full data from the form (this can be used with a custom validation method
</span></span><span id="29" class="l"><a href="#29">  29: </a><span class="php-comment"> *    for dependent validation).
</span></span><span id="30" class="l"><a href="#30">  30: </a><span class="php-comment"> * 3. Validation configuration options.
</span></span><span id="31" class="l"><a href="#31">  31: </a><span class="php-comment"> *
</span></span><span id="32" class="l"><a href="#32">  32: </a><span class="php-comment"> * When using the `Validate` class functions with the {@link Field::validator}
</span></span><span id="33" class="l"><a href="#33">  33: </a><span class="php-comment"> * method, the second parameter passed into {@link Field::validator} is given
</span></span><span id="34" class="l"><a href="#34">  34: </a><span class="php-comment"> * to the validation functions here as the third parameter. The first and
</span></span><span id="35" class="l"><a href="#35">  35: </a><span class="php-comment"> * second parameters are automatically resolved by the {@link Field} class.
</span></span><span id="36" class="l"><a href="#36">  36: </a><span class="php-comment"> *
</span></span><span id="37" class="l"><a href="#37">  37: </a><span class="php-comment"> * The validation configuration options is an array of options that can be used
</span></span><span id="38" class="l"><a href="#38">  38: </a><span class="php-comment"> * to customise the validation - for example defining a date format for date
</span></span><span id="39" class="l"><a href="#39">  39: </a><span class="php-comment"> * validation. Each validation method has the option of defining its own
</span></span><span id="40" class="l"><a href="#40">  40: </a><span class="php-comment"> * validation options, but all validation methods provide four common options:
</span></span><span id="41" class="l"><a href="#41">  41: </a><span class="php-comment"> *
</span></span><span id="42" class="l"><a href="#42">  42: </a><span class="php-comment"> * * `{boolean} optional` - Require the field to be submitted (`false`) or not
</span></span><span id="43" class="l"><a href="#43">  43: </a><span class="php-comment"> *   (`true` - default). When set to `true` the field does not need to be
</span></span><span id="44" class="l"><a href="#44">  44: </a><span class="php-comment"> *   included in the list of parameters sent by the client - if set to `false`
</span></span><span id="45" class="l"><a href="#45">  45: </a><span class="php-comment"> *   then it must be included. This option can be be particularly used in Editor
</span></span><span id="46" class="l"><a href="#46">  46: </a><span class="php-comment"> *   as Editor will not set a value for fields which have not been submitted -
</span></span><span id="47" class="l"><a href="#47">  47: </a><span class="php-comment"> *   giving the ability to submit just a partial list of options.
</span></span><span id="48" class="l"><a href="#48">  48: </a><span class="php-comment"> * * `{boolean} empty` - Allow a field to be empty, i.e. a zero length string -
</span></span><span id="49" class="l"><a href="#49">  49: </a><span class="php-comment"> *   `''` (`true` - default) or require it to be non-zero length (`false`).
</span></span><span id="50" class="l"><a href="#50">  50: </a><span class="php-comment"> * * `{boolean} required` - Short-cut for `optional=false` and `empty=false`.
</span></span><span id="51" class="l"><a href="#51">  51: </a><span class="php-comment"> *   Note that if this option is set the `optional` and `empty` parameters are
</span></span><span id="52" class="l"><a href="#52">  52: </a><span class="php-comment"> *   automatically set and cannot be overridden by passing in different values.
</span></span><span id="53" class="l"><a href="#53">  53: </a><span class="php-comment"> * * `{string} message` - Error message shown should validation fail. This
</span></span><span id="54" class="l"><a href="#54">  54: </a><span class="php-comment"> *   provides complete control over the message shown to the end user, including
</span></span><span id="55" class="l"><a href="#55">  55: </a><span class="php-comment"> *   internationalisation (i.e. to provide a translation that is not in the
</span></span><span id="56" class="l"><a href="#56">  56: </a><span class="php-comment"> *   English language).
</span></span><span id="57" class="l"><a href="#57">  57: </a><span class="php-comment"> *
</span></span><span id="58" class="l"><a href="#58">  58: </a><span class="php-comment"> *  @example
</span></span><span id="59" class="l"><a href="#59">  59: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="60" class="l"><a href="#60">  60: </a><span class="php-comment"> *      // Ensure that a non-empty value is given for a field
</span></span><span id="61" class="l"><a href="#61">  61: </a><span class="php-comment"> *      Field::inst( 'engine' )-&gt;validator( Validate::required() )
</span></span><span id="62" class="l"><a href="#62">  62: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="63" class="l"><a href="#63">  63: </a><span class="php-comment"> *
</span></span><span id="64" class="l"><a href="#64">  64: </a><span class="php-comment"> *  @example
</span></span><span id="65" class="l"><a href="#65">  65: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="66" class="l"><a href="#66">  66: </a><span class="php-comment"> *      // Don't require a field to be submitted, but if it is submitted, it
</span></span><span id="67" class="l"><a href="#67">  67: </a><span class="php-comment"> *      // must be non-empty
</span></span><span id="68" class="l"><a href="#68">  68: </a><span class="php-comment"> *      Field::inst( 'reg_date' )-&gt;validator( Validate::notEmpty() )
</span></span><span id="69" class="l"><a href="#69">  69: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="70" class="l"><a href="#70">  70: </a><span class="php-comment"> *
</span></span><span id="71" class="l"><a href="#71">  71: </a><span class="php-comment"> *  @example
</span></span><span id="72" class="l"><a href="#72">  72: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="73" class="l"><a href="#73">  73: </a><span class="php-comment"> *      // Date validation
</span></span><span id="74" class="l"><a href="#74">  74: </a><span class="php-comment"> *      Field::inst( 'reg_date' )-&gt;validator( Validate::dateFormat( 'D, d M y' ) )
</span></span><span id="75" class="l"><a href="#75">  75: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="76" class="l"><a href="#76">  76: </a><span class="php-comment"> *
</span></span><span id="77" class="l"><a href="#77">  77: </a><span class="php-comment"> *  @example
</span></span><span id="78" class="l"><a href="#78">  78: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="79" class="l"><a href="#79">  79: </a><span class="php-comment"> *      // Date validation with a custom error message
</span></span><span id="80" class="l"><a href="#80">  80: </a><span class="php-comment"> *      Field::inst( 'reg_date' )-&gt;validator( Validate::dateFormat( 'D, d M y',
</span></span><span id="81" class="l"><a href="#81">  81: </a><span class="php-comment"> *          ValidateOptions::inst()
</span></span><span id="82" class="l"><a href="#82">  82: </a><span class="php-comment"> *              -&gt;message( 'Invalid date' )
</span></span><span id="83" class="l"><a href="#83">  83: </a><span class="php-comment"> *      ) )
</span></span><span id="84" class="l"><a href="#84">  84: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="85" class="l"><a href="#85">  85: </a><span class="php-comment"> *
</span></span><span id="86" class="l"><a href="#86">  86: </a><span class="php-comment"> *  @example
</span></span><span id="87" class="l"><a href="#87">  87: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="88" class="l"><a href="#88">  88: </a><span class="php-comment"> *      // Require a non-empty e-mail address
</span></span><span id="89" class="l"><a href="#89">  89: </a><span class="php-comment"> *      Field::inst( 'reg_date' )-&gt;validator( Validate::email( ValidateOptions::inst()
</span></span><span id="90" class="l"><a href="#90">  90: </a><span class="php-comment"> *        -&gt;empty( false )
</span></span><span id="91" class="l"><a href="#91">  91: </a><span class="php-comment"> *      ) )
</span></span><span id="92" class="l"><a href="#92">  92: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="93" class="l"><a href="#93">  93: </a><span class="php-comment"> *
</span></span><span id="94" class="l"><a href="#94">  94: </a><span class="php-comment"> *  @example
</span></span><span id="95" class="l"><a href="#95">  95: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="96" class="l"><a href="#96">  96: </a><span class="php-comment"> *      // Custom validation - closure
</span></span><span id="97" class="l"><a href="#97">  97: </a><span class="php-comment"> *      Field::inst( 'engine' )-&gt;validator( function($val, $data, $opts) {
</span></span><span id="98" class="l"><a href="#98">  98: </a><span class="php-comment"> *         if ( ! preg_match( '/^1/', $val ) ) {
</span></span><span id="99" class="l"><a href="#99">  99: </a><span class="php-comment"> *           return &quot;Value &lt;b&gt;must&lt;/b&gt; start with a 1&quot;;
</span></span><span id="100" class="l"><a href="#100"> 100: </a><span class="php-comment"> *         }
</span></span><span id="101" class="l"><a href="#101"> 101: </a><span class="php-comment"> *         return true;
</span></span><span id="102" class="l"><a href="#102"> 102: </a><span class="php-comment"> *      } )
</span></span><span id="103" class="l"><a href="#103"> 103: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="104" class="l"><a href="#104"> 104: </a><span class="php-comment"> */</span>
</span><span id="105" class="l"><a href="#105"> 105: </a><span class="php-keyword1">class</span> Validate {
</span><span id="106" class="l"><a href="#106"> 106: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="107" class="l"><a href="#107"> 107: </a><span class="php-comment">     * Internal functions
</span></span><span id="108" class="l"><a href="#108"> 108: </a><span class="php-comment">     */</span>
</span><span id="109" class="l"><a href="#109"> 109: </a>
</span><span id="110" class="l"><a href="#110"> 110: </a>    <span class="php-comment">/**
</span></span><span id="111" class="l"><a href="#111"> 111: </a><span class="php-comment">     * &quot;Magic&quot; method to automatically apply the required check to any of the
</span></span><span id="112" class="l"><a href="#112"> 112: </a><span class="php-comment">     * static methods simply by adding '_required' or '_empty' to the end of the
</span></span><span id="113" class="l"><a href="#113"> 113: </a><span class="php-comment">     * method's name, depending on if you need the field to be submitted or not.
</span></span><span id="114" class="l"><a href="#114"> 114: </a><span class="php-comment">     *
</span></span><span id="115" class="l"><a href="#115"> 115: </a><span class="php-comment">     * This is retained for backwards compatibility, but the validation options
</span></span><span id="116" class="l"><a href="#116"> 116: </a><span class="php-comment">     * are now the recommended way of indicating that a field is required.
</span></span><span id="117" class="l"><a href="#117"> 117: </a><span class="php-comment">     *
</span></span><span id="118" class="l"><a href="#118"> 118: </a><span class="php-comment">     * @internal
</span></span><span id="119" class="l"><a href="#119"> 119: </a><span class="php-comment">     * @param string $name Function name
</span></span><span id="120" class="l"><a href="#120"> 120: </a><span class="php-comment">     * @param array $arguments Function arguments
</span></span><span id="121" class="l"><a href="#121"> 121: </a><span class="php-comment">     * @return mixed|string
</span></span><span id="122" class="l"><a href="#122"> 122: </a><span class="php-comment">     */</span>
</span><span id="123" class="l"><a href="#123"> 123: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> __callStatic( <span class="php-var">$name</span>, <span class="php-var">$arguments</span> ) {
</span><span id="124" class="l"><a href="#124"> 124: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">preg_match</span>( <span class="php-quote">'/_required$/'</span>, <span class="php-var">$name</span> ) ) {
</span><span id="125" class="l"><a href="#125"> 125: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$arguments</span>[<span class="php-num">0</span>] === <span class="php-keyword1">null</span> || <span class="php-var">$arguments</span>[<span class="php-num">0</span>] === <span class="php-quote">''</span> ) {
</span><span id="126" class="l"><a href="#126"> 126: </a>                <span class="php-keyword1">return</span> <span class="php-quote">'This field is required'</span>;
</span><span id="127" class="l"><a href="#127"> 127: </a>            }
</span><span id="128" class="l"><a href="#128"> 128: </a>
</span><span id="129" class="l"><a href="#129"> 129: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">call_user_func_array</span>( 
</span><span id="130" class="l"><a href="#130"> 130: </a>                __NAMESPACE__.<span class="php-quote">'\Validate::'</span>.<span class="php-keyword2">str_replace</span>( <span class="php-quote">'_required'</span>, <span class="php-quote">''</span>, <span class="php-var">$name</span> ),
</span><span id="131" class="l"><a href="#131"> 131: </a>                <span class="php-var">$arguments</span>
</span><span id="132" class="l"><a href="#132"> 132: </a>            );
</span><span id="133" class="l"><a href="#133"> 133: </a>        }
</span><span id="134" class="l"><a href="#134"> 134: </a>    }
</span><span id="135" class="l"><a href="#135"> 135: </a>
</span><span id="136" class="l"><a href="#136"> 136: </a>
</span><span id="137" class="l"><a href="#137"> 137: </a>    <span class="php-comment">/**
</span></span><span id="138" class="l"><a href="#138"> 138: </a><span class="php-comment">     * Extend the options from the user function and the validation function
</span></span><span id="139" class="l"><a href="#139"> 139: </a><span class="php-comment">     * with core defaults.
</span></span><span id="140" class="l"><a href="#140"> 140: </a><span class="php-comment">     *
</span></span><span id="141" class="l"><a href="#141"> 141: </a><span class="php-comment">     *  @internal
</span></span><span id="142" class="l"><a href="#142"> 142: </a><span class="php-comment">     */</span>
</span><span id="143" class="l"><a href="#143"> 143: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> _extend( <span class="php-var">$userOpts</span>, <span class="php-var">$prop</span>, <span class="php-var">$fnOpts</span> ) {
</span><span id="144" class="l"><a href="#144"> 144: </a>        <span class="php-var">$cfg</span> = <span class="php-keyword1">array</span>(
</span><span id="145" class="l"><a href="#145"> 145: </a>            <span class="php-quote">'message'</span>  =&gt; <span class="php-quote">'Input not valid'</span>,
</span><span id="146" class="l"><a href="#146"> 146: </a>            <span class="php-quote">'required'</span> =&gt; <span class="php-keyword1">false</span>,
</span><span id="147" class="l"><a href="#147"> 147: </a>            <span class="php-quote">'empty'</span>    =&gt; <span class="php-keyword1">true</span>,
</span><span id="148" class="l"><a href="#148"> 148: </a>            <span class="php-quote">'optional'</span> =&gt; <span class="php-keyword1">true</span>
</span><span id="149" class="l"><a href="#149"> 149: </a>        );
</span><span id="150" class="l"><a href="#150"> 150: </a>
</span><span id="151" class="l"><a href="#151"> 151: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_array</span>( <span class="php-var">$userOpts</span> ) ) {
</span><span id="152" class="l"><a href="#152"> 152: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$prop</span> ) {
</span><span id="153" class="l"><a href="#153"> 153: </a>                <span class="php-var">$cfg</span>[ <span class="php-var">$prop</span> ] = <span class="php-var">$userOpts</span>;
</span><span id="154" class="l"><a href="#154"> 154: </a>            }
</span><span id="155" class="l"><a href="#155"> 155: </a>
</span><span id="156" class="l"><a href="#156"> 156: </a>            <span class="php-comment">// Not an array, but the non-array case has been handled above, so</span>
</span><span id="157" class="l"><a href="#157"> 157: </a>            <span class="php-comment">// just an empty array</span>
</span><span id="158" class="l"><a href="#158"> 158: </a>            <span class="php-var">$userOpts</span> = <span class="php-keyword1">array</span>();
</span><span id="159" class="l"><a href="#159"> 159: </a>        }
</span><span id="160" class="l"><a href="#160"> 160: </a>
</span><span id="161" class="l"><a href="#161"> 161: </a>        <span class="php-comment">// Merge into a single array - first array gets priority if there is a</span>
</span><span id="162" class="l"><a href="#162"> 162: </a>        <span class="php-comment">// key clash, so user first, then function commands and finally the</span>
</span><span id="163" class="l"><a href="#163"> 163: </a>        <span class="php-comment">// global options </span>
</span><span id="164" class="l"><a href="#164"> 164: </a>        <span class="php-var">$cfg</span> = <span class="php-var">$userOpts</span> + <span class="php-var">$fnOpts</span> + <span class="php-var">$cfg</span>;
</span><span id="165" class="l"><a href="#165"> 165: </a>
</span><span id="166" class="l"><a href="#166"> 166: </a>        <span class="php-keyword1">return</span> <span class="php-var">$cfg</span>;
</span><span id="167" class="l"><a href="#167"> 167: </a>    }
</span><span id="168" class="l"><a href="#168"> 168: </a>
</span><span id="169" class="l"><a href="#169"> 169: </a>
</span><span id="170" class="l"><a href="#170"> 170: </a>    <span class="php-comment">/**
</span></span><span id="171" class="l"><a href="#171"> 171: </a><span class="php-comment">     * Perform common validation using the configuration parameters
</span></span><span id="172" class="l"><a href="#172"> 172: </a><span class="php-comment">     *
</span></span><span id="173" class="l"><a href="#173"> 173: </a><span class="php-comment">     *  @internal
</span></span><span id="174" class="l"><a href="#174"> 174: </a><span class="php-comment">     */</span>
</span><span id="175" class="l"><a href="#175"> 175: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> _common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> ) {
</span><span id="176" class="l"><a href="#176"> 176: </a>        <span class="php-var">$optional</span> = <span class="php-var">$opts</span>-&gt;optional();
</span><span id="177" class="l"><a href="#177"> 177: </a>        <span class="php-var">$empty</span>    = <span class="php-var">$opts</span>-&gt;allowEmpty();
</span><span id="178" class="l"><a href="#178"> 178: </a>
</span><span id="179" class="l"><a href="#179"> 179: </a>        <span class="php-comment">// Error state tests</span>
</span><span id="180" class="l"><a href="#180"> 180: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$optional</span> &amp;&amp; <span class="php-var">$val</span> === <span class="php-keyword1">null</span>  ) {
</span><span id="181" class="l"><a href="#181"> 181: </a>            <span class="php-comment">// Value must be given</span>
</span><span id="182" class="l"><a href="#182"> 182: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="183" class="l"><a href="#183"> 183: </a>        }
</span><span id="184" class="l"><a href="#184"> 184: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$empty</span> === <span class="php-keyword1">false</span> &amp;&amp; <span class="php-var">$val</span> === <span class="php-quote">''</span> ) {
</span><span id="185" class="l"><a href="#185"> 185: </a>            <span class="php-comment">// Value must not be empty</span>
</span><span id="186" class="l"><a href="#186"> 186: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="187" class="l"><a href="#187"> 187: </a>        }
</span><span id="188" class="l"><a href="#188"> 188: </a>        
</span><span id="189" class="l"><a href="#189"> 189: </a>        <span class="php-comment">// Validation passed states</span>
</span><span id="190" class="l"><a href="#190"> 190: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$optional</span> &amp;&amp; <span class="php-var">$val</span> === <span class="php-keyword1">null</span> ) {
</span><span id="191" class="l"><a href="#191"> 191: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="192" class="l"><a href="#192"> 192: </a>        }
</span><span id="193" class="l"><a href="#193"> 193: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$empty</span> === <span class="php-keyword1">true</span> &amp;&amp; <span class="php-var">$val</span> === <span class="php-quote">''</span> ) {
</span><span id="194" class="l"><a href="#194"> 194: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="195" class="l"><a href="#195"> 195: </a>        }
</span><span id="196" class="l"><a href="#196"> 196: </a>
</span><span id="197" class="l"><a href="#197"> 197: </a>        <span class="php-comment">// Have the specific validation function perform its tests</span>
</span><span id="198" class="l"><a href="#198"> 198: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="199" class="l"><a href="#199"> 199: </a>    }
</span><span id="200" class="l"><a href="#200"> 200: </a>
</span><span id="201" class="l"><a href="#201"> 201: </a>    <span class="php-comment">/**
</span></span><span id="202" class="l"><a href="#202"> 202: </a><span class="php-comment">     * Convert the old style validation parameters into ValidateOptions
</span></span><span id="203" class="l"><a href="#203"> 203: </a><span class="php-comment">     *
</span></span><span id="204" class="l"><a href="#204"> 204: </a><span class="php-comment">     *  @internal
</span></span><span id="205" class="l"><a href="#205"> 205: </a><span class="php-comment">     */</span>
</span><span id="206" class="l"><a href="#206"> 206: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> _commonLegacy( <span class="php-var">$cfg</span> ) {
</span><span id="207" class="l"><a href="#207"> 207: </a>        <span class="php-var">$opts</span> = <span class="php-keyword1">new</span> ValidateOptions();
</span><span id="208" class="l"><a href="#208"> 208: </a>
</span><span id="209" class="l"><a href="#209"> 209: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_array</span>( <span class="php-var">$cfg</span> ) ) {
</span><span id="210" class="l"><a href="#210"> 210: </a>            <span class="php-comment">// `required` is a legacy shortcut for optional=false, empty=false</span>
</span><span id="211" class="l"><a href="#211"> 211: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$cfg</span>[<span class="php-quote">'required'</span>] ) ) {
</span><span id="212" class="l"><a href="#212"> 212: </a>                <span class="php-var">$opts</span>-&gt;optional( <span class="php-keyword1">false</span> );
</span><span id="213" class="l"><a href="#213"> 213: </a>                <span class="php-var">$opts</span>-&gt;allowEmpty( <span class="php-keyword1">false</span> );
</span><span id="214" class="l"><a href="#214"> 214: </a>            }
</span><span id="215" class="l"><a href="#215"> 215: </a>
</span><span id="216" class="l"><a href="#216"> 216: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$cfg</span>[<span class="php-quote">'empty'</span>] ) ) {
</span><span id="217" class="l"><a href="#217"> 217: </a>                <span class="php-var">$opts</span>-&gt;allowEmpty( <span class="php-var">$cfg</span>[<span class="php-quote">'empty'</span>] );
</span><span id="218" class="l"><a href="#218"> 218: </a>            }
</span><span id="219" class="l"><a href="#219"> 219: </a>
</span><span id="220" class="l"><a href="#220"> 220: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$cfg</span>[<span class="php-quote">'message'</span>] ) ) {
</span><span id="221" class="l"><a href="#221"> 221: </a>                <span class="php-var">$opts</span>-&gt;message( <span class="php-var">$cfg</span>[<span class="php-quote">'message'</span>] );
</span><span id="222" class="l"><a href="#222"> 222: </a>            }
</span><span id="223" class="l"><a href="#223"> 223: </a>
</span><span id="224" class="l"><a href="#224"> 224: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$cfg</span>[<span class="php-quote">'optional'</span>] ) ) {
</span><span id="225" class="l"><a href="#225"> 225: </a>                <span class="php-var">$opts</span>-&gt;optional( <span class="php-var">$cfg</span>[<span class="php-quote">'optional'</span>] );
</span><span id="226" class="l"><a href="#226"> 226: </a>            }
</span><span id="227" class="l"><a href="#227"> 227: </a>        }
</span><span id="228" class="l"><a href="#228"> 228: </a>
</span><span id="229" class="l"><a href="#229"> 229: </a>        <span class="php-keyword1">return</span> <span class="php-var">$opts</span>;
</span><span id="230" class="l"><a href="#230"> 230: </a>    }
</span><span id="231" class="l"><a href="#231"> 231: </a>
</span><span id="232" class="l"><a href="#232"> 232: </a>
</span><span id="233" class="l"><a href="#233"> 233: </a>
</span><span id="234" class="l"><a href="#234"> 234: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="235" class="l"><a href="#235"> 235: </a><span class="php-comment">     * Basic validation
</span></span><span id="236" class="l"><a href="#236"> 236: </a><span class="php-comment">     */</span>
</span><span id="237" class="l"><a href="#237"> 237: </a>
</span><span id="238" class="l"><a href="#238"> 238: </a>    <span class="php-comment">/**
</span></span><span id="239" class="l"><a href="#239"> 239: </a><span class="php-comment">     * No validation - all inputs are valid.
</span></span><span id="240" class="l"><a href="#240"> 240: </a><span class="php-comment">     *  @return callable Validation function
</span></span><span id="241" class="l"><a href="#241"> 241: </a><span class="php-comment">     */</span>
</span><span id="242" class="l"><a href="#242"> 242: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> none() {
</span><span id="243" class="l"><a href="#243"> 243: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) {
</span><span id="244" class="l"><a href="#244"> 244: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="245" class="l"><a href="#245"> 245: </a>        };
</span><span id="246" class="l"><a href="#246"> 246: </a>    }
</span><span id="247" class="l"><a href="#247"> 247: </a>
</span><span id="248" class="l"><a href="#248"> 248: </a>
</span><span id="249" class="l"><a href="#249"> 249: </a>    <span class="php-comment">/**
</span></span><span id="250" class="l"><a href="#250"> 250: </a><span class="php-comment">     * Basic validation - this is used to perform the validation provided by the
</span></span><span id="251" class="l"><a href="#251"> 251: </a><span class="php-comment">     * validation options only. If the validation options pass (e.g. `required`,
</span></span><span id="252" class="l"><a href="#252"> 252: </a><span class="php-comment">     * `empty` and `optional`) then the validation will pass regardless of the
</span></span><span id="253" class="l"><a href="#253"> 253: </a><span class="php-comment">     * actual value.
</span></span><span id="254" class="l"><a href="#254"> 254: </a><span class="php-comment">     *
</span></span><span id="255" class="l"><a href="#255"> 255: </a><span class="php-comment">     * Note that there are two helper short-cut methods that provide the same
</span></span><span id="256" class="l"><a href="#256"> 256: </a><span class="php-comment">     * function as this method, but are slightly shorter:
</span></span><span id="257" class="l"><a href="#257"> 257: </a><span class="php-comment">     *
</span></span><span id="258" class="l"><a href="#258"> 258: </a><span class="php-comment">     * ```
</span></span><span id="259" class="l"><a href="#259"> 259: </a><span class="php-comment">     * // Required:
</span></span><span id="260" class="l"><a href="#260"> 260: </a><span class="php-comment">     * Validate::required()
</span></span><span id="261" class="l"><a href="#261"> 261: </a><span class="php-comment">     *
</span></span><span id="262" class="l"><a href="#262"> 262: </a><span class="php-comment">     * // is the same as
</span></span><span id="263" class="l"><a href="#263"> 263: </a><span class="php-comment">     * Validate::basic( $val, $data, array(
</span></span><span id="264" class="l"><a href="#264"> 264: </a><span class="php-comment">     *   &quot;required&quot; =&gt; true
</span></span><span id="265" class="l"><a href="#265"> 265: </a><span class="php-comment">     * );
</span></span><span id="266" class="l"><a href="#266"> 266: </a><span class="php-comment">     * ```
</span></span><span id="267" class="l"><a href="#267"> 267: </a><span class="php-comment">     *
</span></span><span id="268" class="l"><a href="#268"> 268: </a><span class="php-comment">     * ```
</span></span><span id="269" class="l"><a href="#269"> 269: </a><span class="php-comment">     * // Optional, but not empty if given:
</span></span><span id="270" class="l"><a href="#270"> 270: </a><span class="php-comment">     * Validate::notEmpty()
</span></span><span id="271" class="l"><a href="#271"> 271: </a><span class="php-comment">     *
</span></span><span id="272" class="l"><a href="#272"> 272: </a><span class="php-comment">     * // is the same as
</span></span><span id="273" class="l"><a href="#273"> 273: </a><span class="php-comment">     * Validate::basic( $val, $data, array(
</span></span><span id="274" class="l"><a href="#274"> 274: </a><span class="php-comment">     *   &quot;empty&quot; =&gt; false
</span></span><span id="275" class="l"><a href="#275"> 275: </a><span class="php-comment">     * );
</span></span><span id="276" class="l"><a href="#276"> 276: </a><span class="php-comment">     * ```
</span></span><span id="277" class="l"><a href="#277"> 277: </a><span class="php-comment">     *
</span></span><span id="278" class="l"><a href="#278"> 278: </a><span class="php-comment">     * @param string $val The value to check for validity
</span></span><span id="279" class="l"><a href="#279"> 279: </a><span class="php-comment">     * @param string[] $data The full data set submitted
</span></span><span id="280" class="l"><a href="#280"> 280: </a><span class="php-comment">     * @param array $opts Validation options. No additional options are
</span></span><span id="281" class="l"><a href="#281"> 281: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="282" class="l"><a href="#282"> 282: </a><span class="php-comment">     * @param array $host Host information
</span></span><span id="283" class="l"><a href="#283"> 283: </a><span class="php-comment">     * @return string|true true if the value is valid, a string with an error
</span></span><span id="284" class="l"><a href="#284"> 284: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="285" class="l"><a href="#285"> 285: </a><span class="php-comment">     */</span>
</span><span id="286" class="l"><a href="#286"> 286: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> basic( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="287" class="l"><a href="#287"> 287: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="288" class="l"><a href="#288"> 288: </a>
</span><span id="289" class="l"><a href="#289"> 289: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="290" class="l"><a href="#290"> 290: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="291" class="l"><a href="#291"> 291: </a>
</span><span id="292" class="l"><a href="#292"> 292: </a>            <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="293" class="l"><a href="#293"> 293: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="294" class="l"><a href="#294"> 294: </a>                <span class="php-keyword1">true</span>;
</span><span id="295" class="l"><a href="#295"> 295: </a>        };
</span><span id="296" class="l"><a href="#296"> 296: </a>    }
</span><span id="297" class="l"><a href="#297"> 297: </a>
</span><span id="298" class="l"><a href="#298"> 298: </a>
</span><span id="299" class="l"><a href="#299"> 299: </a>    <span class="php-comment">/**
</span></span><span id="300" class="l"><a href="#300"> 300: </a><span class="php-comment">     * Required field - there must be a value and it must be a non-empty value
</span></span><span id="301" class="l"><a href="#301"> 301: </a><span class="php-comment">     *
</span></span><span id="302" class="l"><a href="#302"> 302: </a><span class="php-comment">     * This is a helper short-cut method which is the same as:
</span></span><span id="303" class="l"><a href="#303"> 303: </a><span class="php-comment">     * 
</span></span><span id="304" class="l"><a href="#304"> 304: </a><span class="php-comment">     * ```
</span></span><span id="305" class="l"><a href="#305"> 305: </a><span class="php-comment">     * Validate::basic( $val, $data, array(
</span></span><span id="306" class="l"><a href="#306"> 306: </a><span class="php-comment">     *   &quot;required&quot; =&gt; true
</span></span><span id="307" class="l"><a href="#307"> 307: </a><span class="php-comment">     * );
</span></span><span id="308" class="l"><a href="#308"> 308: </a><span class="php-comment">     * ```
</span></span><span id="309" class="l"><a href="#309"> 309: </a><span class="php-comment">     * 
</span></span><span id="310" class="l"><a href="#310"> 310: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="311" class="l"><a href="#311"> 311: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="312" class="l"><a href="#312"> 312: </a><span class="php-comment">     *  @param array $opts Validation options. No additional options are
</span></span><span id="313" class="l"><a href="#313"> 313: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="314" class="l"><a href="#314"> 314: </a><span class="php-comment">     * @param array $host Host information
</span></span><span id="315" class="l"><a href="#315"> 315: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="316" class="l"><a href="#316"> 316: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="317" class="l"><a href="#317"> 317: </a><span class="php-comment">     */</span>
</span><span id="318" class="l"><a href="#318"> 318: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> required( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="319" class="l"><a href="#319"> 319: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="320" class="l"><a href="#320"> 320: </a>        <span class="php-var">$opts</span>-&gt;allowEmpty( <span class="php-keyword1">false</span> );
</span><span id="321" class="l"><a href="#321"> 321: </a>        <span class="php-var">$opts</span>-&gt;optional( <span class="php-keyword1">false</span> );
</span><span id="322" class="l"><a href="#322"> 322: </a>
</span><span id="323" class="l"><a href="#323"> 323: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="324" class="l"><a href="#324"> 324: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="325" class="l"><a href="#325"> 325: </a>
</span><span id="326" class="l"><a href="#326"> 326: </a>            <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="327" class="l"><a href="#327"> 327: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="328" class="l"><a href="#328"> 328: </a>                <span class="php-keyword1">true</span>;
</span><span id="329" class="l"><a href="#329"> 329: </a>        };
</span><span id="330" class="l"><a href="#330"> 330: </a>    }
</span><span id="331" class="l"><a href="#331"> 331: </a>
</span><span id="332" class="l"><a href="#332"> 332: </a>
</span><span id="333" class="l"><a href="#333"> 333: </a>    <span class="php-comment">/**
</span></span><span id="334" class="l"><a href="#334"> 334: </a><span class="php-comment">     * Optional field, but if given there must be a non-empty value
</span></span><span id="335" class="l"><a href="#335"> 335: </a><span class="php-comment">     *
</span></span><span id="336" class="l"><a href="#336"> 336: </a><span class="php-comment">     * This is a helper short-cut method which is the same as:
</span></span><span id="337" class="l"><a href="#337"> 337: </a><span class="php-comment">     * 
</span></span><span id="338" class="l"><a href="#338"> 338: </a><span class="php-comment">     * ```
</span></span><span id="339" class="l"><a href="#339"> 339: </a><span class="php-comment">     * Validate::basic( $val, $data, array(
</span></span><span id="340" class="l"><a href="#340"> 340: </a><span class="php-comment">     *   &quot;empty&quot; =&gt; false
</span></span><span id="341" class="l"><a href="#341"> 341: </a><span class="php-comment">     * );
</span></span><span id="342" class="l"><a href="#342"> 342: </a><span class="php-comment">     * ```
</span></span><span id="343" class="l"><a href="#343"> 343: </a><span class="php-comment">     *
</span></span><span id="344" class="l"><a href="#344"> 344: </a><span class="php-comment">     *  @param ValidateOptions $cfg Validation options
</span></span><span id="345" class="l"><a href="#345"> 345: </a><span class="php-comment">     *  @return callable Validation function
</span></span><span id="346" class="l"><a href="#346"> 346: </a><span class="php-comment">     */</span>
</span><span id="347" class="l"><a href="#347"> 347: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> notEmpty( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="348" class="l"><a href="#348"> 348: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="349" class="l"><a href="#349"> 349: </a>        <span class="php-var">$opts</span>-&gt;allowEmpty( <span class="php-keyword1">false</span> );
</span><span id="350" class="l"><a href="#350"> 350: </a>
</span><span id="351" class="l"><a href="#351"> 351: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="352" class="l"><a href="#352"> 352: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="353" class="l"><a href="#353"> 353: </a>
</span><span id="354" class="l"><a href="#354"> 354: </a>            <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="355" class="l"><a href="#355"> 355: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="356" class="l"><a href="#356"> 356: </a>                <span class="php-keyword1">true</span>;
</span><span id="357" class="l"><a href="#357"> 357: </a>        };
</span><span id="358" class="l"><a href="#358"> 358: </a>    }
</span><span id="359" class="l"><a href="#359"> 359: </a>
</span><span id="360" class="l"><a href="#360"> 360: </a>
</span><span id="361" class="l"><a href="#361"> 361: </a>    <span class="php-comment">/**
</span></span><span id="362" class="l"><a href="#362"> 362: </a><span class="php-comment">     * Validate an input as a boolean value.
</span></span><span id="363" class="l"><a href="#363"> 363: </a><span class="php-comment">     *
</span></span><span id="364" class="l"><a href="#364"> 364: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="365" class="l"><a href="#365"> 365: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="366" class="l"><a href="#366"> 366: </a><span class="php-comment">     *  @param array $opts Validation options. No additional options are
</span></span><span id="367" class="l"><a href="#367"> 367: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="368" class="l"><a href="#368"> 368: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="369" class="l"><a href="#369"> 369: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="370" class="l"><a href="#370"> 370: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="371" class="l"><a href="#371"> 371: </a><span class="php-comment">     */</span>
</span><span id="372" class="l"><a href="#372"> 372: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> boolean( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="373" class="l"><a href="#373"> 373: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="374" class="l"><a href="#374"> 374: </a>
</span><span id="375" class="l"><a href="#375"> 375: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="376" class="l"><a href="#376"> 376: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="377" class="l"><a href="#377"> 377: </a>
</span><span id="378" class="l"><a href="#378"> 378: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="379" class="l"><a href="#379"> 379: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="380" class="l"><a href="#380"> 380: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="381" class="l"><a href="#381"> 381: </a>                    <span class="php-var">$common</span>;
</span><span id="382" class="l"><a href="#382"> 382: </a>            }
</span><span id="383" class="l"><a href="#383"> 383: </a>    
</span><span id="384" class="l"><a href="#384"> 384: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">filter_var</span>(<span class="php-var">$val</span>, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) === <span class="php-keyword1">null</span> ) {
</span><span id="385" class="l"><a href="#385"> 385: </a>                <span class="php-keyword1">return</span> <span class="php-var">$opt</span>-&gt;message();
</span><span id="386" class="l"><a href="#386"> 386: </a>            }
</span><span id="387" class="l"><a href="#387"> 387: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="388" class="l"><a href="#388"> 388: </a>        };
</span><span id="389" class="l"><a href="#389"> 389: </a>    }
</span><span id="390" class="l"><a href="#390"> 390: </a>
</span><span id="391" class="l"><a href="#391"> 391: </a>
</span><span id="392" class="l"><a href="#392"> 392: </a>
</span><span id="393" class="l"><a href="#393"> 393: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="394" class="l"><a href="#394"> 394: </a><span class="php-comment">     * Number validation methods
</span></span><span id="395" class="l"><a href="#395"> 395: </a><span class="php-comment">     */</span>
</span><span id="396" class="l"><a href="#396"> 396: </a>
</span><span id="397" class="l"><a href="#397"> 397: </a>    <span class="php-comment">/**
</span></span><span id="398" class="l"><a href="#398"> 398: </a><span class="php-comment">     * Check that any input is numeric.
</span></span><span id="399" class="l"><a href="#399"> 399: </a><span class="php-comment">     *
</span></span><span id="400" class="l"><a href="#400"> 400: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="401" class="l"><a href="#401"> 401: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="402" class="l"><a href="#402"> 402: </a><span class="php-comment">     *  @param array $opts Validation options. Additional options:
</span></span><span id="403" class="l"><a href="#403"> 403: </a><span class="php-comment">     *     * `decimal`: is available to indicate what character should be used
</span></span><span id="404" class="l"><a href="#404"> 404: </a><span class="php-comment">     *       as the decimal
</span></span><span id="405" class="l"><a href="#405"> 405: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="406" class="l"><a href="#406"> 406: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="407" class="l"><a href="#407"> 407: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="408" class="l"><a href="#408"> 408: </a><span class="php-comment">     */</span>
</span><span id="409" class="l"><a href="#409"> 409: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> numeric ( <span class="php-var">$decimal</span>=<span class="php-quote">&quot;.&quot;</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="410" class="l"><a href="#410"> 410: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="411" class="l"><a href="#411"> 411: </a>
</span><span id="412" class="l"><a href="#412"> 412: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$decimal</span> ) {
</span><span id="413" class="l"><a href="#413"> 413: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="414" class="l"><a href="#414"> 414: </a>
</span><span id="415" class="l"><a href="#415"> 415: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="416" class="l"><a href="#416"> 416: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="417" class="l"><a href="#417"> 417: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="418" class="l"><a href="#418"> 418: </a>                    <span class="php-var">$common</span>;
</span><span id="419" class="l"><a href="#419"> 419: </a>            }
</span><span id="420" class="l"><a href="#420"> 420: </a>    
</span><span id="421" class="l"><a href="#421"> 421: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$decimal</span> !== <span class="php-quote">'.'</span> ) {
</span><span id="422" class="l"><a href="#422"> 422: </a>                <span class="php-var">$val</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$decimal</span>, <span class="php-quote">'.'</span>, <span class="php-var">$val</span> );
</span><span id="423" class="l"><a href="#423"> 423: </a>            }
</span><span id="424" class="l"><a href="#424"> 424: </a>    
</span><span id="425" class="l"><a href="#425"> 425: </a>            <span class="php-keyword1">return</span> ! <span class="php-keyword2">is_numeric</span>( <span class="php-var">$val</span> ) ?
</span><span id="426" class="l"><a href="#426"> 426: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="427" class="l"><a href="#427"> 427: </a>                <span class="php-keyword1">true</span>;
</span><span id="428" class="l"><a href="#428"> 428: </a>        };
</span><span id="429" class="l"><a href="#429"> 429: </a>    }
</span><span id="430" class="l"><a href="#430"> 430: </a>
</span><span id="431" class="l"><a href="#431"> 431: </a>    <span class="php-comment">/**
</span></span><span id="432" class="l"><a href="#432"> 432: </a><span class="php-comment">     * Check for a numeric input and that it is greater than a given value.
</span></span><span id="433" class="l"><a href="#433"> 433: </a><span class="php-comment">     *
</span></span><span id="434" class="l"><a href="#434"> 434: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="435" class="l"><a href="#435"> 435: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="436" class="l"><a href="#436"> 436: </a><span class="php-comment">     *  @param int|array $opts Validation options. Additional options:
</span></span><span id="437" class="l"><a href="#437"> 437: </a><span class="php-comment">     *     * `min`: indicate the minimum value. If only the default validation
</span></span><span id="438" class="l"><a href="#438"> 438: </a><span class="php-comment">     *       options are required, this parameter can be given as an integer
</span></span><span id="439" class="l"><a href="#439"> 439: </a><span class="php-comment">     *       value, which will be used as the minimum value.
</span></span><span id="440" class="l"><a href="#440"> 440: </a><span class="php-comment">     *     * `decimal`: is available to indicate what character should be used
</span></span><span id="441" class="l"><a href="#441"> 441: </a><span class="php-comment">     *       as the decimal
</span></span><span id="442" class="l"><a href="#442"> 442: </a><span class="php-comment">     *    separator (default '.').
</span></span><span id="443" class="l"><a href="#443"> 443: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="444" class="l"><a href="#444"> 444: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="445" class="l"><a href="#445"> 445: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="446" class="l"><a href="#446"> 446: </a><span class="php-comment">     */</span>
</span><span id="447" class="l"><a href="#447"> 447: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minNum ( <span class="php-var">$min</span>, <span class="php-var">$decimal</span>=<span class="php-quote">&quot;.&quot;</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="448" class="l"><a href="#448"> 448: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="449" class="l"><a href="#449"> 449: </a>
</span><span id="450" class="l"><a href="#450"> 450: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$min</span>, <span class="php-var">$decimal</span> ) {
</span><span id="451" class="l"><a href="#451"> 451: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="452" class="l"><a href="#452"> 452: </a>            
</span><span id="453" class="l"><a href="#453"> 453: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="454" class="l"><a href="#454"> 454: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="455" class="l"><a href="#455"> 455: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="456" class="l"><a href="#456"> 456: </a>                    <span class="php-var">$common</span>;
</span><span id="457" class="l"><a href="#457"> 457: </a>            }
</span><span id="458" class="l"><a href="#458"> 458: </a>            
</span><span id="459" class="l"><a href="#459"> 459: </a>            <span class="php-var">$fn</span> = Validate::numeric( <span class="php-var">$decimal</span>, <span class="php-var">$opts</span> );
</span><span id="460" class="l"><a href="#460"> 460: </a>            <span class="php-var">$numeric</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="461" class="l"><a href="#461"> 461: </a>
</span><span id="462" class="l"><a href="#462"> 462: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$numeric</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="463" class="l"><a href="#463"> 463: </a>                <span class="php-keyword1">return</span> <span class="php-var">$numeric</span>;
</span><span id="464" class="l"><a href="#464"> 464: </a>            }
</span><span id="465" class="l"><a href="#465"> 465: </a>    
</span><span id="466" class="l"><a href="#466"> 466: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$decimal</span> !== <span class="php-quote">'.'</span> ) {
</span><span id="467" class="l"><a href="#467"> 467: </a>                <span class="php-var">$val</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$decimal</span>, <span class="php-quote">'.'</span>, <span class="php-var">$val</span> );
</span><span id="468" class="l"><a href="#468"> 468: </a>            }
</span><span id="469" class="l"><a href="#469"> 469: </a>    
</span><span id="470" class="l"><a href="#470"> 470: </a>            <span class="php-keyword1">return</span> <span class="php-var">$val</span> &lt; <span class="php-var">$min</span> ?
</span><span id="471" class="l"><a href="#471"> 471: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="472" class="l"><a href="#472"> 472: </a>                <span class="php-keyword1">true</span>;
</span><span id="473" class="l"><a href="#473"> 473: </a>        };
</span><span id="474" class="l"><a href="#474"> 474: </a>    }
</span><span id="475" class="l"><a href="#475"> 475: </a>
</span><span id="476" class="l"><a href="#476"> 476: </a>    <span class="php-comment">/**
</span></span><span id="477" class="l"><a href="#477"> 477: </a><span class="php-comment">     * Check for a numeric input and that it is less than a given value.
</span></span><span id="478" class="l"><a href="#478"> 478: </a><span class="php-comment">     *
</span></span><span id="479" class="l"><a href="#479"> 479: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="480" class="l"><a href="#480"> 480: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="481" class="l"><a href="#481"> 481: </a><span class="php-comment">     *  @param int|array $opts Validation options.
</span></span><span id="482" class="l"><a href="#482"> 482: </a><span class="php-comment">     *     * `max`: indicate the maximum value. If only the default validation
</span></span><span id="483" class="l"><a href="#483"> 483: </a><span class="php-comment">     *       options are required, this parameter can be given as an integer
</span></span><span id="484" class="l"><a href="#484"> 484: </a><span class="php-comment">     *       value, which will be used as the maximum value.
</span></span><span id="485" class="l"><a href="#485"> 485: </a><span class="php-comment">     *     * `decimal`: is available to indicate what character should be used
</span></span><span id="486" class="l"><a href="#486"> 486: </a><span class="php-comment">     *       as the decimal
</span></span><span id="487" class="l"><a href="#487"> 487: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="488" class="l"><a href="#488"> 488: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="489" class="l"><a href="#489"> 489: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="490" class="l"><a href="#490"> 490: </a><span class="php-comment">     */</span>
</span><span id="491" class="l"><a href="#491"> 491: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> maxNum ( <span class="php-var">$max</span>, <span class="php-var">$decimal</span>=<span class="php-quote">&quot;.&quot;</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="492" class="l"><a href="#492"> 492: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="493" class="l"><a href="#493"> 493: </a>
</span><span id="494" class="l"><a href="#494"> 494: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$max</span>, <span class="php-var">$decimal</span> ) {
</span><span id="495" class="l"><a href="#495"> 495: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="496" class="l"><a href="#496"> 496: </a>            
</span><span id="497" class="l"><a href="#497"> 497: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="498" class="l"><a href="#498"> 498: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="499" class="l"><a href="#499"> 499: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="500" class="l"><a href="#500"> 500: </a>                    <span class="php-var">$common</span>;
</span><span id="501" class="l"><a href="#501"> 501: </a>            }
</span><span id="502" class="l"><a href="#502"> 502: </a>            
</span><span id="503" class="l"><a href="#503"> 503: </a>            <span class="php-var">$fn</span> = Validate::numeric( <span class="php-var">$decimal</span>, <span class="php-var">$opts</span> );
</span><span id="504" class="l"><a href="#504"> 504: </a>            <span class="php-var">$numeric</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="505" class="l"><a href="#505"> 505: </a>
</span><span id="506" class="l"><a href="#506"> 506: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$numeric</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="507" class="l"><a href="#507"> 507: </a>                <span class="php-keyword1">return</span> <span class="php-var">$numeric</span>;
</span><span id="508" class="l"><a href="#508"> 508: </a>            }
</span><span id="509" class="l"><a href="#509"> 509: </a>    
</span><span id="510" class="l"><a href="#510"> 510: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$decimal</span> !== <span class="php-quote">'.'</span> ) {
</span><span id="511" class="l"><a href="#511"> 511: </a>                <span class="php-var">$val</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$decimal</span>, <span class="php-quote">'.'</span>, <span class="php-var">$val</span> );
</span><span id="512" class="l"><a href="#512"> 512: </a>            }
</span><span id="513" class="l"><a href="#513"> 513: </a>    
</span><span id="514" class="l"><a href="#514"> 514: </a>            <span class="php-keyword1">return</span> <span class="php-var">$val</span> &gt; <span class="php-var">$max</span> ?
</span><span id="515" class="l"><a href="#515"> 515: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="516" class="l"><a href="#516"> 516: </a>                <span class="php-keyword1">true</span>;
</span><span id="517" class="l"><a href="#517"> 517: </a>        };
</span><span id="518" class="l"><a href="#518"> 518: </a>    }
</span><span id="519" class="l"><a href="#519"> 519: </a>
</span><span id="520" class="l"><a href="#520"> 520: </a>
</span><span id="521" class="l"><a href="#521"> 521: </a>    <span class="php-comment">/**
</span></span><span id="522" class="l"><a href="#522"> 522: </a><span class="php-comment">     * Check for a numeric input and that it is both greater and smaller than
</span></span><span id="523" class="l"><a href="#523"> 523: </a><span class="php-comment">     * given numbers.
</span></span><span id="524" class="l"><a href="#524"> 524: </a><span class="php-comment">     *
</span></span><span id="525" class="l"><a href="#525"> 525: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="526" class="l"><a href="#526"> 526: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="527" class="l"><a href="#527"> 527: </a><span class="php-comment">     *  @param int|array $opts Validation options. Additional options:
</span></span><span id="528" class="l"><a href="#528"> 528: </a><span class="php-comment">     *     * `min`: indicate the minimum value.
</span></span><span id="529" class="l"><a href="#529"> 529: </a><span class="php-comment">     *     * `max`: indicate the maximum value.
</span></span><span id="530" class="l"><a href="#530"> 530: </a><span class="php-comment">     *     * `decimal`: is available to indicate what character should be used
</span></span><span id="531" class="l"><a href="#531"> 531: </a><span class="php-comment">     *       as the decimal
</span></span><span id="532" class="l"><a href="#532"> 532: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="533" class="l"><a href="#533"> 533: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="534" class="l"><a href="#534"> 534: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="535" class="l"><a href="#535"> 535: </a><span class="php-comment">     */</span>
</span><span id="536" class="l"><a href="#536"> 536: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minMaxNum ( <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-var">$decimal</span>=<span class="php-quote">'.'</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="537" class="l"><a href="#537"> 537: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="538" class="l"><a href="#538"> 538: </a>        
</span><span id="539" class="l"><a href="#539"> 539: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-var">$decimal</span> ) {
</span><span id="540" class="l"><a href="#540"> 540: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="541" class="l"><a href="#541"> 541: </a>            
</span><span id="542" class="l"><a href="#542"> 542: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="543" class="l"><a href="#543"> 543: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="544" class="l"><a href="#544"> 544: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="545" class="l"><a href="#545"> 545: </a>                    <span class="php-var">$common</span>;
</span><span id="546" class="l"><a href="#546"> 546: </a>            }
</span><span id="547" class="l"><a href="#547"> 547: </a>            
</span><span id="548" class="l"><a href="#548"> 548: </a>            <span class="php-var">$fn</span> = Validate::numeric( <span class="php-var">$decimal</span>, <span class="php-var">$opts</span> );
</span><span id="549" class="l"><a href="#549"> 549: </a>            <span class="php-var">$numeric</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="550" class="l"><a href="#550"> 550: </a>
</span><span id="551" class="l"><a href="#551"> 551: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$numeric</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="552" class="l"><a href="#552"> 552: </a>                <span class="php-keyword1">return</span> <span class="php-var">$numeric</span>;
</span><span id="553" class="l"><a href="#553"> 553: </a>            }
</span><span id="554" class="l"><a href="#554"> 554: </a>
</span><span id="555" class="l"><a href="#555"> 555: </a>            <span class="php-var">$fn</span> = Validate::minNum( <span class="php-var">$min</span>, <span class="php-var">$decimal</span>, <span class="php-var">$opts</span> );
</span><span id="556" class="l"><a href="#556"> 556: </a>            <span class="php-var">$numeric</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="557" class="l"><a href="#557"> 557: </a>
</span><span id="558" class="l"><a href="#558"> 558: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$numeric</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="559" class="l"><a href="#559"> 559: </a>                <span class="php-keyword1">return</span> <span class="php-var">$numeric</span>;
</span><span id="560" class="l"><a href="#560"> 560: </a>            }
</span><span id="561" class="l"><a href="#561"> 561: </a>            
</span><span id="562" class="l"><a href="#562"> 562: </a>            <span class="php-var">$fn</span> = Validate::maxNum( <span class="php-var">$max</span>, <span class="php-var">$decimal</span>, <span class="php-var">$opts</span> );
</span><span id="563" class="l"><a href="#563"> 563: </a>            <span class="php-var">$numeric</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="564" class="l"><a href="#564"> 564: </a>
</span><span id="565" class="l"><a href="#565"> 565: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$numeric</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="566" class="l"><a href="#566"> 566: </a>                <span class="php-keyword1">return</span> <span class="php-var">$numeric</span>;
</span><span id="567" class="l"><a href="#567"> 567: </a>            }
</span><span id="568" class="l"><a href="#568"> 568: </a>
</span><span id="569" class="l"><a href="#569"> 569: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="570" class="l"><a href="#570"> 570: </a>        };
</span><span id="571" class="l"><a href="#571"> 571: </a>    }
</span><span id="572" class="l"><a href="#572"> 572: </a>
</span><span id="573" class="l"><a href="#573"> 573: </a>
</span><span id="574" class="l"><a href="#574"> 574: </a>
</span><span id="575" class="l"><a href="#575"> 575: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="576" class="l"><a href="#576"> 576: </a><span class="php-comment">     * String validation methods
</span></span><span id="577" class="l"><a href="#577"> 577: </a><span class="php-comment">     */</span>
</span><span id="578" class="l"><a href="#578"> 578: </a>
</span><span id="579" class="l"><a href="#579"> 579: </a>    <span class="php-comment">/**
</span></span><span id="580" class="l"><a href="#580"> 580: </a><span class="php-comment">     * Validate an input as an e-mail address.
</span></span><span id="581" class="l"><a href="#581"> 581: </a><span class="php-comment">     *
</span></span><span id="582" class="l"><a href="#582"> 582: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="583" class="l"><a href="#583"> 583: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="584" class="l"><a href="#584"> 584: </a><span class="php-comment">     *  @param array $opts Validation options. No additional options are
</span></span><span id="585" class="l"><a href="#585"> 585: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="586" class="l"><a href="#586"> 586: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="587" class="l"><a href="#587"> 587: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="588" class="l"><a href="#588"> 588: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="589" class="l"><a href="#589"> 589: </a><span class="php-comment">     */</span>
</span><span id="590" class="l"><a href="#590"> 590: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> email( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="591" class="l"><a href="#591"> 591: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="592" class="l"><a href="#592"> 592: </a>        
</span><span id="593" class="l"><a href="#593"> 593: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="594" class="l"><a href="#594"> 594: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="595" class="l"><a href="#595"> 595: </a>
</span><span id="596" class="l"><a href="#596"> 596: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="597" class="l"><a href="#597"> 597: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="598" class="l"><a href="#598"> 598: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="599" class="l"><a href="#599"> 599: </a>                    <span class="php-var">$common</span>;
</span><span id="600" class="l"><a href="#600"> 600: </a>            }
</span><span id="601" class="l"><a href="#601"> 601: </a>
</span><span id="602" class="l"><a href="#602"> 602: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">filter_var</span>(<span class="php-var">$val</span>, FILTER_VALIDATE_EMAIL) !== <span class="php-keyword1">false</span> ?
</span><span id="603" class="l"><a href="#603"> 603: </a>                <span class="php-keyword1">true</span> :
</span><span id="604" class="l"><a href="#604"> 604: </a>                <span class="php-var">$opts</span>-&gt;message();
</span><span id="605" class="l"><a href="#605"> 605: </a>        };
</span><span id="606" class="l"><a href="#606"> 606: </a>    }
</span><span id="607" class="l"><a href="#607"> 607: </a>
</span><span id="608" class="l"><a href="#608"> 608: </a>
</span><span id="609" class="l"><a href="#609"> 609: </a>    <span class="php-comment">/**
</span></span><span id="610" class="l"><a href="#610"> 610: </a><span class="php-comment">     * Validate a string has a minimum length.
</span></span><span id="611" class="l"><a href="#611"> 611: </a><span class="php-comment">     *
</span></span><span id="612" class="l"><a href="#612"> 612: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="613" class="l"><a href="#613"> 613: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="614" class="l"><a href="#614"> 614: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional option of
</span></span><span id="615" class="l"><a href="#615"> 615: </a><span class="php-comment">     *    `min` is available for this method to indicate the minimum string
</span></span><span id="616" class="l"><a href="#616"> 616: </a><span class="php-comment">     *    length. If only the default validation options are required, this
</span></span><span id="617" class="l"><a href="#617"> 617: </a><span class="php-comment">     *    parameter can be given as an integer value, which will be used as the
</span></span><span id="618" class="l"><a href="#618"> 618: </a><span class="php-comment">     *    minimum string length.
</span></span><span id="619" class="l"><a href="#619"> 619: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="620" class="l"><a href="#620"> 620: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="621" class="l"><a href="#621"> 621: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="622" class="l"><a href="#622"> 622: </a><span class="php-comment">     */</span>
</span><span id="623" class="l"><a href="#623"> 623: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minLen( <span class="php-var">$min</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="624" class="l"><a href="#624"> 624: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="625" class="l"><a href="#625"> 625: </a>        
</span><span id="626" class="l"><a href="#626"> 626: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$min</span>, <span class="php-var">$opts</span> ) {
</span><span id="627" class="l"><a href="#627"> 627: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="628" class="l"><a href="#628"> 628: </a>
</span><span id="629" class="l"><a href="#629"> 629: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="630" class="l"><a href="#630"> 630: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="631" class="l"><a href="#631"> 631: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="632" class="l"><a href="#632"> 632: </a>                    <span class="php-var">$common</span>;
</span><span id="633" class="l"><a href="#633"> 633: </a>            }
</span><span id="634" class="l"><a href="#634"> 634: </a>    
</span><span id="635" class="l"><a href="#635"> 635: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">strlen</span>( <span class="php-var">$val</span> ) &lt; <span class="php-var">$min</span> ?
</span><span id="636" class="l"><a href="#636"> 636: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="637" class="l"><a href="#637"> 637: </a>                <span class="php-keyword1">true</span>;
</span><span id="638" class="l"><a href="#638"> 638: </a>        };
</span><span id="639" class="l"><a href="#639"> 639: </a>    }
</span><span id="640" class="l"><a href="#640"> 640: </a>
</span><span id="641" class="l"><a href="#641"> 641: </a>
</span><span id="642" class="l"><a href="#642"> 642: </a>    <span class="php-comment">/**
</span></span><span id="643" class="l"><a href="#643"> 643: </a><span class="php-comment">     * Validate a string does not exceed a maximum length.
</span></span><span id="644" class="l"><a href="#644"> 644: </a><span class="php-comment">     *
</span></span><span id="645" class="l"><a href="#645"> 645: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="646" class="l"><a href="#646"> 646: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="647" class="l"><a href="#647"> 647: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional option of
</span></span><span id="648" class="l"><a href="#648"> 648: </a><span class="php-comment">     *    `max` is available for this method to indicate the maximum string
</span></span><span id="649" class="l"><a href="#649"> 649: </a><span class="php-comment">     *    length. If only the default validation options are required, this
</span></span><span id="650" class="l"><a href="#650"> 650: </a><span class="php-comment">     *    parameter can be given as an integer value, which will be used as the
</span></span><span id="651" class="l"><a href="#651"> 651: </a><span class="php-comment">     *    maximum string length.
</span></span><span id="652" class="l"><a href="#652"> 652: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="653" class="l"><a href="#653"> 653: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="654" class="l"><a href="#654"> 654: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="655" class="l"><a href="#655"> 655: </a><span class="php-comment">     */</span>
</span><span id="656" class="l"><a href="#656"> 656: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> maxLen( <span class="php-var">$max</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="657" class="l"><a href="#657"> 657: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="658" class="l"><a href="#658"> 658: </a>        
</span><span id="659" class="l"><a href="#659"> 659: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$max</span>, <span class="php-var">$opts</span> ) {
</span><span id="660" class="l"><a href="#660"> 660: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="661" class="l"><a href="#661"> 661: </a>
</span><span id="662" class="l"><a href="#662"> 662: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="663" class="l"><a href="#663"> 663: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="664" class="l"><a href="#664"> 664: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="665" class="l"><a href="#665"> 665: </a>                    <span class="php-var">$common</span>;
</span><span id="666" class="l"><a href="#666"> 666: </a>            }
</span><span id="667" class="l"><a href="#667"> 667: </a>    
</span><span id="668" class="l"><a href="#668"> 668: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">strlen</span>( <span class="php-var">$val</span> ) &gt; <span class="php-var">$max</span> ?
</span><span id="669" class="l"><a href="#669"> 669: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="670" class="l"><a href="#670"> 670: </a>                <span class="php-keyword1">true</span>;
</span><span id="671" class="l"><a href="#671"> 671: </a>        };
</span><span id="672" class="l"><a href="#672"> 672: </a>    }
</span><span id="673" class="l"><a href="#673"> 673: </a>
</span><span id="674" class="l"><a href="#674"> 674: </a>    <span class="php-comment">/**
</span></span><span id="675" class="l"><a href="#675"> 675: </a><span class="php-comment">     * Require a string with a certain minimum or maximum number of characters.
</span></span><span id="676" class="l"><a href="#676"> 676: </a><span class="php-comment">     *
</span></span><span id="677" class="l"><a href="#677"> 677: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="678" class="l"><a href="#678"> 678: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="679" class="l"><a href="#679"> 679: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="680" class="l"><a href="#680"> 680: </a><span class="php-comment">     *    `min` and `max` are available for this method to indicate the minimum
</span></span><span id="681" class="l"><a href="#681"> 681: </a><span class="php-comment">     *    and maximum string lengths, respectively.
</span></span><span id="682" class="l"><a href="#682"> 682: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="683" class="l"><a href="#683"> 683: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="684" class="l"><a href="#684"> 684: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="685" class="l"><a href="#685"> 685: </a><span class="php-comment">     */</span>
</span><span id="686" class="l"><a href="#686"> 686: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minMaxLen( <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="687" class="l"><a href="#687"> 687: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="688" class="l"><a href="#688"> 688: </a>        
</span><span id="689" class="l"><a href="#689"> 689: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$min</span>, <span class="php-var">$max</span> ) {
</span><span id="690" class="l"><a href="#690"> 690: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="691" class="l"><a href="#691"> 691: </a>            
</span><span id="692" class="l"><a href="#692"> 692: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="693" class="l"><a href="#693"> 693: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="694" class="l"><a href="#694"> 694: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="695" class="l"><a href="#695"> 695: </a>                    <span class="php-var">$common</span>;
</span><span id="696" class="l"><a href="#696"> 696: </a>            }
</span><span id="697" class="l"><a href="#697"> 697: </a>            
</span><span id="698" class="l"><a href="#698"> 698: </a>            <span class="php-var">$fn</span> = Validate::minLen( <span class="php-var">$min</span>, <span class="php-var">$opts</span> );
</span><span id="699" class="l"><a href="#699"> 699: </a>            <span class="php-var">$res</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="700" class="l"><a href="#700"> 700: </a>
</span><span id="701" class="l"><a href="#701"> 701: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="702" class="l"><a href="#702"> 702: </a>                <span class="php-keyword1">return</span> <span class="php-var">$res</span>;
</span><span id="703" class="l"><a href="#703"> 703: </a>            }
</span><span id="704" class="l"><a href="#704"> 704: </a>            
</span><span id="705" class="l"><a href="#705"> 705: </a>            <span class="php-var">$fn</span> = Validate::maxLen( <span class="php-var">$max</span>, <span class="php-var">$opts</span> );
</span><span id="706" class="l"><a href="#706"> 706: </a>            <span class="php-var">$res</span> = <span class="php-var">$fn</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> );
</span><span id="707" class="l"><a href="#707"> 707: </a>
</span><span id="708" class="l"><a href="#708"> 708: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="709" class="l"><a href="#709"> 709: </a>                <span class="php-keyword1">return</span> <span class="php-var">$res</span>;
</span><span id="710" class="l"><a href="#710"> 710: </a>            }
</span><span id="711" class="l"><a href="#711"> 711: </a>
</span><span id="712" class="l"><a href="#712"> 712: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="713" class="l"><a href="#713"> 713: </a>        };
</span><span id="714" class="l"><a href="#714"> 714: </a>    }
</span><span id="715" class="l"><a href="#715"> 715: </a>
</span><span id="716" class="l"><a href="#716"> 716: </a>
</span><span id="717" class="l"><a href="#717"> 717: </a>    <span class="php-comment">/**
</span></span><span id="718" class="l"><a href="#718"> 718: </a><span class="php-comment">     * Validate as an IP address.
</span></span><span id="719" class="l"><a href="#719"> 719: </a><span class="php-comment">     *
</span></span><span id="720" class="l"><a href="#720"> 720: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="721" class="l"><a href="#721"> 721: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="722" class="l"><a href="#722"> 722: </a><span class="php-comment">     *  @param array $opts Validation options. No additional options are
</span></span><span id="723" class="l"><a href="#723"> 723: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="724" class="l"><a href="#724"> 724: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="725" class="l"><a href="#725"> 725: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="726" class="l"><a href="#726"> 726: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="727" class="l"><a href="#727"> 727: </a><span class="php-comment">     */</span>
</span><span id="728" class="l"><a href="#728"> 728: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> ip( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="729" class="l"><a href="#729"> 729: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="730" class="l"><a href="#730"> 730: </a>        
</span><span id="731" class="l"><a href="#731"> 731: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="732" class="l"><a href="#732"> 732: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="733" class="l"><a href="#733"> 733: </a>
</span><span id="734" class="l"><a href="#734"> 734: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="735" class="l"><a href="#735"> 735: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="736" class="l"><a href="#736"> 736: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="737" class="l"><a href="#737"> 737: </a>                    <span class="php-var">$common</span>;
</span><span id="738" class="l"><a href="#738"> 738: </a>            }
</span><span id="739" class="l"><a href="#739"> 739: </a>
</span><span id="740" class="l"><a href="#740"> 740: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">filter_var</span>(<span class="php-var">$val</span>, FILTER_VALIDATE_IP) !== <span class="php-keyword1">false</span> ?
</span><span id="741" class="l"><a href="#741"> 741: </a>                <span class="php-keyword1">true</span> :
</span><span id="742" class="l"><a href="#742"> 742: </a>                <span class="php-var">$opts</span>-&gt;message();
</span><span id="743" class="l"><a href="#743"> 743: </a>        };
</span><span id="744" class="l"><a href="#744"> 744: </a>    }
</span><span id="745" class="l"><a href="#745"> 745: </a>
</span><span id="746" class="l"><a href="#746"> 746: </a>
</span><span id="747" class="l"><a href="#747"> 747: </a>    <span class="php-comment">/**
</span></span><span id="748" class="l"><a href="#748"> 748: </a><span class="php-comment">     * Validate as an URL address.
</span></span><span id="749" class="l"><a href="#749"> 749: </a><span class="php-comment">     *
</span></span><span id="750" class="l"><a href="#750"> 750: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="751" class="l"><a href="#751"> 751: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="752" class="l"><a href="#752"> 752: </a><span class="php-comment">     *  @param array $opts Validation options. No additional options are
</span></span><span id="753" class="l"><a href="#753"> 753: </a><span class="php-comment">     *    available or required for this validation method.
</span></span><span id="754" class="l"><a href="#754"> 754: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="755" class="l"><a href="#755"> 755: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="756" class="l"><a href="#756"> 756: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="757" class="l"><a href="#757"> 757: </a><span class="php-comment">     */</span>
</span><span id="758" class="l"><a href="#758"> 758: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> url( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="759" class="l"><a href="#759"> 759: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="760" class="l"><a href="#760"> 760: </a>        
</span><span id="761" class="l"><a href="#761"> 761: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="762" class="l"><a href="#762"> 762: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="763" class="l"><a href="#763"> 763: </a>
</span><span id="764" class="l"><a href="#764"> 764: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="765" class="l"><a href="#765"> 765: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="766" class="l"><a href="#766"> 766: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="767" class="l"><a href="#767"> 767: </a>                    <span class="php-var">$common</span>;
</span><span id="768" class="l"><a href="#768"> 768: </a>            }
</span><span id="769" class="l"><a href="#769"> 769: </a>
</span><span id="770" class="l"><a href="#770"> 770: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">filter_var</span>(<span class="php-var">$val</span>, FILTER_VALIDATE_URL) !== <span class="php-keyword1">false</span> ?
</span><span id="771" class="l"><a href="#771"> 771: </a>                <span class="php-keyword1">true</span> :
</span><span id="772" class="l"><a href="#772"> 772: </a>                <span class="php-var">$opts</span>-&gt;message();
</span><span id="773" class="l"><a href="#773"> 773: </a>        };
</span><span id="774" class="l"><a href="#774"> 774: </a>    }
</span><span id="775" class="l"><a href="#775"> 775: </a>
</span><span id="776" class="l"><a href="#776"> 776: </a>
</span><span id="777" class="l"><a href="#777"> 777: </a>    <span class="php-comment">/**
</span></span><span id="778" class="l"><a href="#778"> 778: </a><span class="php-comment">     * Check if string could contain an XSS attack string
</span></span><span id="779" class="l"><a href="#779"> 779: </a><span class="php-comment">     *
</span></span><span id="780" class="l"><a href="#780"> 780: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="781" class="l"><a href="#781"> 781: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="782" class="l"><a href="#782"> 782: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="783" class="l"><a href="#783"> 783: </a><span class="php-comment">     *    `db` - database connection object, `table` - database table to use and
</span></span><span id="784" class="l"><a href="#784"> 784: </a><span class="php-comment">     *    `column` - the column to check this value against as value, are also
</span></span><span id="785" class="l"><a href="#785"> 785: </a><span class="php-comment">     *    available. These options are not required and if not given are
</span></span><span id="786" class="l"><a href="#786"> 786: </a><span class="php-comment">     *    automatically derived from the Editor and Field instances.
</span></span><span id="787" class="l"><a href="#787"> 787: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="788" class="l"><a href="#788"> 788: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="789" class="l"><a href="#789"> 789: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="790" class="l"><a href="#790"> 790: </a><span class="php-comment">     */</span>
</span><span id="791" class="l"><a href="#791"> 791: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> xss ( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="792" class="l"><a href="#792"> 792: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="793" class="l"><a href="#793"> 793: </a>        
</span><span id="794" class="l"><a href="#794"> 794: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="795" class="l"><a href="#795"> 795: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="796" class="l"><a href="#796"> 796: </a>
</span><span id="797" class="l"><a href="#797"> 797: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="798" class="l"><a href="#798"> 798: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="799" class="l"><a href="#799"> 799: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="800" class="l"><a href="#800"> 800: </a>                    <span class="php-var">$common</span>;
</span><span id="801" class="l"><a href="#801"> 801: </a>            }
</span><span id="802" class="l"><a href="#802"> 802: </a>
</span><span id="803" class="l"><a href="#803"> 803: </a>            <span class="php-keyword1">return</span> <span class="php-var">$field</span>-&gt;xssSafety( <span class="php-var">$val</span> ) != <span class="php-var">$val</span> ?
</span><span id="804" class="l"><a href="#804"> 804: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="805" class="l"><a href="#805"> 805: </a>                <span class="php-keyword1">true</span>;
</span><span id="806" class="l"><a href="#806"> 806: </a>        };
</span><span id="807" class="l"><a href="#807"> 807: </a>    }
</span><span id="808" class="l"><a href="#808"> 808: </a>
</span><span id="809" class="l"><a href="#809"> 809: </a>
</span><span id="810" class="l"><a href="#810"> 810: </a>    <span class="php-comment">/**
</span></span><span id="811" class="l"><a href="#811"> 811: </a><span class="php-comment">     * Confirm that the value submitted is in a list of allowable values
</span></span><span id="812" class="l"><a href="#812"> 812: </a><span class="php-comment">     *
</span></span><span id="813" class="l"><a href="#813"> 813: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="814" class="l"><a href="#814"> 814: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="815" class="l"><a href="#815"> 815: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="816" class="l"><a href="#816"> 816: </a><span class="php-comment">     *    `db` - database connection object, `table` - database table to use and
</span></span><span id="817" class="l"><a href="#817"> 817: </a><span class="php-comment">     *    `column` - the column to check this value against as value, are also
</span></span><span id="818" class="l"><a href="#818"> 818: </a><span class="php-comment">     *    available. These options are not required and if not given are
</span></span><span id="819" class="l"><a href="#819"> 819: </a><span class="php-comment">     *    automatically derived from the Editor and Field instances.
</span></span><span id="820" class="l"><a href="#820"> 820: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="821" class="l"><a href="#821"> 821: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="822" class="l"><a href="#822"> 822: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="823" class="l"><a href="#823"> 823: </a><span class="php-comment">     */</span>
</span><span id="824" class="l"><a href="#824"> 824: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> values( <span class="php-var">$values</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="825" class="l"><a href="#825"> 825: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="826" class="l"><a href="#826"> 826: </a>        
</span><span id="827" class="l"><a href="#827"> 827: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$values</span>, <span class="php-var">$opts</span> ) {
</span><span id="828" class="l"><a href="#828"> 828: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="829" class="l"><a href="#829"> 829: </a>
</span><span id="830" class="l"><a href="#830"> 830: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="831" class="l"><a href="#831"> 831: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="832" class="l"><a href="#832"> 832: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="833" class="l"><a href="#833"> 833: </a>                    <span class="php-var">$common</span>;
</span><span id="834" class="l"><a href="#834"> 834: </a>            }
</span><span id="835" class="l"><a href="#835"> 835: </a>
</span><span id="836" class="l"><a href="#836"> 836: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">in_array</span>(<span class="php-var">$val</span>, <span class="php-var">$values</span>) ?
</span><span id="837" class="l"><a href="#837"> 837: </a>                <span class="php-keyword1">true</span> :
</span><span id="838" class="l"><a href="#838"> 838: </a>                <span class="php-var">$opts</span>-&gt;message();
</span><span id="839" class="l"><a href="#839"> 839: </a>        };
</span><span id="840" class="l"><a href="#840"> 840: </a>    }
</span><span id="841" class="l"><a href="#841"> 841: </a>
</span><span id="842" class="l"><a href="#842"> 842: </a>
</span><span id="843" class="l"><a href="#843"> 843: </a>    <span class="php-comment">/**
</span></span><span id="844" class="l"><a href="#844"> 844: </a><span class="php-comment">     * Check if there are any tags in the submitted value
</span></span><span id="845" class="l"><a href="#845"> 845: </a><span class="php-comment">     *
</span></span><span id="846" class="l"><a href="#846"> 846: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="847" class="l"><a href="#847"> 847: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="848" class="l"><a href="#848"> 848: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="849" class="l"><a href="#849"> 849: </a><span class="php-comment">     *    `db` - database connection object, `table` - database table to use and
</span></span><span id="850" class="l"><a href="#850"> 850: </a><span class="php-comment">     *    `column` - the column to check this value against as value, are also
</span></span><span id="851" class="l"><a href="#851"> 851: </a><span class="php-comment">     *    available. These options are not required and if not given are
</span></span><span id="852" class="l"><a href="#852"> 852: </a><span class="php-comment">     *    automatically derived from the Editor and Field instances.
</span></span><span id="853" class="l"><a href="#853"> 853: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="854" class="l"><a href="#854"> 854: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="855" class="l"><a href="#855"> 855: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="856" class="l"><a href="#856"> 856: </a><span class="php-comment">     */</span>
</span><span id="857" class="l"><a href="#857"> 857: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> noTags ( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="858" class="l"><a href="#858"> 858: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="859" class="l"><a href="#859"> 859: </a>        
</span><span id="860" class="l"><a href="#860"> 860: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span> ) {
</span><span id="861" class="l"><a href="#861"> 861: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="862" class="l"><a href="#862"> 862: </a>
</span><span id="863" class="l"><a href="#863"> 863: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="864" class="l"><a href="#864"> 864: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="865" class="l"><a href="#865"> 865: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="866" class="l"><a href="#866"> 866: </a>                    <span class="php-var">$common</span>;
</span><span id="867" class="l"><a href="#867"> 867: </a>            }
</span><span id="868" class="l"><a href="#868"> 868: </a>
</span><span id="869" class="l"><a href="#869"> 869: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">strip_tags</span>( <span class="php-var">$val</span> ) != <span class="php-var">$val</span> ?
</span><span id="870" class="l"><a href="#870"> 870: </a>                <span class="php-var">$opts</span>-&gt;message() :
</span><span id="871" class="l"><a href="#871"> 871: </a>                <span class="php-keyword1">true</span>;
</span><span id="872" class="l"><a href="#872"> 872: </a>        };
</span><span id="873" class="l"><a href="#873"> 873: </a>    }
</span><span id="874" class="l"><a href="#874"> 874: </a>
</span><span id="875" class="l"><a href="#875"> 875: </a>
</span><span id="876" class="l"><a href="#876"> 876: </a>
</span><span id="877" class="l"><a href="#877"> 877: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="878" class="l"><a href="#878"> 878: </a><span class="php-comment">     * Date validation methods
</span></span><span id="879" class="l"><a href="#879"> 879: </a><span class="php-comment">     */</span>
</span><span id="880" class="l"><a href="#880"> 880: </a>
</span><span id="881" class="l"><a href="#881"> 881: </a>    <span class="php-comment">/**
</span></span><span id="882" class="l"><a href="#882"> 882: </a><span class="php-comment">     * Check that a valid date input is given
</span></span><span id="883" class="l"><a href="#883"> 883: </a><span class="php-comment">     *
</span></span><span id="884" class="l"><a href="#884"> 884: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="885" class="l"><a href="#885"> 885: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="886" class="l"><a href="#886"> 886: </a><span class="php-comment">     *  @param array|string $opts If given as a string, then $opts is the date
</span></span><span id="887" class="l"><a href="#887"> 887: </a><span class="php-comment">     *    format to check the validity of. If given as an array, then the
</span></span><span id="888" class="l"><a href="#888"> 888: </a><span class="php-comment">     *    date format is in the 'format' parameter, and the return error
</span></span><span id="889" class="l"><a href="#889"> 889: </a><span class="php-comment">     *    message in the 'message' parameter.
</span></span><span id="890" class="l"><a href="#890"> 890: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="891" class="l"><a href="#891"> 891: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="892" class="l"><a href="#892"> 892: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="893" class="l"><a href="#893"> 893: </a><span class="php-comment">     */</span>
</span><span id="894" class="l"><a href="#894"> 894: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dateFormat( <span class="php-var">$format</span>, <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span> ) {
</span><span id="895" class="l"><a href="#895"> 895: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="896" class="l"><a href="#896"> 896: </a>        
</span><span id="897" class="l"><a href="#897"> 897: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$format</span>, <span class="php-var">$opts</span> ) {
</span><span id="898" class="l"><a href="#898"> 898: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="899" class="l"><a href="#899"> 899: </a>
</span><span id="900" class="l"><a href="#900"> 900: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="901" class="l"><a href="#901"> 901: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="902" class="l"><a href="#902"> 902: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="903" class="l"><a href="#903"> 903: </a>                    <span class="php-var">$common</span>;
</span><span id="904" class="l"><a href="#904"> 904: </a>            }
</span><span id="905" class="l"><a href="#905"> 905: </a>
</span><span id="906" class="l"><a href="#906"> 906: </a>            <span class="php-var">$date</span> = \DateTime::createFromFormat( <span class="php-var">$format</span>, <span class="php-var">$val</span>) ;
</span><span id="907" class="l"><a href="#907"> 907: </a>            
</span><span id="908" class="l"><a href="#908"> 908: </a>            <span class="php-keyword1">return</span> <span class="php-var">$date</span> &amp;&amp; <span class="php-var">$date</span>-&gt;format( <span class="php-var">$format</span> ) == <span class="php-var">$val</span> ?
</span><span id="909" class="l"><a href="#909"> 909: </a>                <span class="php-keyword1">true</span> :
</span><span id="910" class="l"><a href="#910"> 910: </a>                <span class="php-var">$opts</span>-&gt;message();
</span><span id="911" class="l"><a href="#911"> 911: </a>        };
</span><span id="912" class="l"><a href="#912"> 912: </a>    }
</span><span id="913" class="l"><a href="#913"> 913: </a>
</span><span id="914" class="l"><a href="#914"> 914: </a>
</span><span id="915" class="l"><a href="#915"> 915: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="916" class="l"><a href="#916"> 916: </a><span class="php-comment">     * Database validation methods
</span></span><span id="917" class="l"><a href="#917"> 917: </a><span class="php-comment">     */</span>
</span><span id="918" class="l"><a href="#918"> 918: </a>
</span><span id="919" class="l"><a href="#919"> 919: </a>    <span class="php-comment">/**
</span></span><span id="920" class="l"><a href="#920"> 920: </a><span class="php-comment">     * Check that the given value is unique in the database
</span></span><span id="921" class="l"><a href="#921"> 921: </a><span class="php-comment">     *
</span></span><span id="922" class="l"><a href="#922"> 922: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="923" class="l"><a href="#923"> 923: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="924" class="l"><a href="#924"> 924: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="925" class="l"><a href="#925"> 925: </a><span class="php-comment">     *    `db` - database connection object, `table` - database table to use and
</span></span><span id="926" class="l"><a href="#926"> 926: </a><span class="php-comment">     *    `column` - the column to check this value against as value, are also
</span></span><span id="927" class="l"><a href="#927"> 927: </a><span class="php-comment">     *    available. These options are not required and if not given are
</span></span><span id="928" class="l"><a href="#928"> 928: </a><span class="php-comment">     *    automatically derived from the Editor and Field instances.
</span></span><span id="929" class="l"><a href="#929"> 929: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="930" class="l"><a href="#930"> 930: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="931" class="l"><a href="#931"> 931: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="932" class="l"><a href="#932"> 932: </a><span class="php-comment">     */</span>
</span><span id="933" class="l"><a href="#933"> 933: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> unique( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span>, <span class="php-var">$column</span>=<span class="php-keyword1">null</span>, <span class="php-var">$table</span>=<span class="php-keyword1">null</span>, <span class="php-var">$db</span>=<span class="php-keyword1">null</span> ) {
</span><span id="934" class="l"><a href="#934"> 934: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="935" class="l"><a href="#935"> 935: </a>        
</span><span id="936" class="l"><a href="#936"> 936: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$column</span>, <span class="php-var">$table</span>, <span class="php-var">$db</span> ) {
</span><span id="937" class="l"><a href="#937"> 937: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="938" class="l"><a href="#938"> 938: </a>
</span><span id="939" class="l"><a href="#939"> 939: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="940" class="l"><a href="#940"> 940: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="941" class="l"><a href="#941"> 941: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="942" class="l"><a href="#942"> 942: </a>                    <span class="php-var">$common</span>;
</span><span id="943" class="l"><a href="#943"> 943: </a>            }
</span><span id="944" class="l"><a href="#944"> 944: </a>
</span><span id="945" class="l"><a href="#945"> 945: </a>            <span class="php-var">$editor</span> = <span class="php-var">$host</span>[<span class="php-quote">'editor'</span>];
</span><span id="946" class="l"><a href="#946"> 946: </a>
</span><span id="947" class="l"><a href="#947"> 947: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$db</span> ) {
</span><span id="948" class="l"><a href="#948"> 948: </a>                <span class="php-var">$db</span> = <span class="php-var">$host</span>[<span class="php-quote">'db'</span>];
</span><span id="949" class="l"><a href="#949"> 949: </a>            }
</span><span id="950" class="l"><a href="#950"> 950: </a>
</span><span id="951" class="l"><a href="#951"> 951: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$table</span> ) {
</span><span id="952" class="l"><a href="#952"> 952: </a>                <span class="php-var">$table</span> = <span class="php-var">$editor</span>-&gt;table(); <span class="php-comment">// Returns an array, but `select` will take an array</span>
</span><span id="953" class="l"><a href="#953"> 953: </a>            }
</span><span id="954" class="l"><a href="#954"> 954: </a>
</span><span id="955" class="l"><a href="#955"> 955: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$column</span> ) {
</span><span id="956" class="l"><a href="#956"> 956: </a>                <span class="php-var">$column</span> = <span class="php-var">$field</span>-&gt;dbField();
</span><span id="957" class="l"><a href="#957"> 957: </a>            }
</span><span id="958" class="l"><a href="#958"> 958: </a>
</span><span id="959" class="l"><a href="#959"> 959: </a>            <span class="php-var">$query</span> = <span class="php-var">$db</span>
</span><span id="960" class="l"><a href="#960"> 960: </a>                -&gt;query( <span class="php-quote">'select'</span>, <span class="php-var">$table</span> )
</span><span id="961" class="l"><a href="#961"> 961: </a>                -&gt;get( <span class="php-var">$column</span> )
</span><span id="962" class="l"><a href="#962"> 962: </a>                -&gt;where( <span class="php-var">$column</span>, <span class="php-var">$val</span> );
</span><span id="963" class="l"><a href="#963"> 963: </a>
</span><span id="964" class="l"><a href="#964"> 964: </a>            <span class="php-comment">// If doing an edit, then we need to also discount the current row,</span>
</span><span id="965" class="l"><a href="#965"> 965: </a>            <span class="php-comment">// since it is of course already validly unique</span>
</span><span id="966" class="l"><a href="#966"> 966: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$host</span>[<span class="php-quote">'action'</span>] === <span class="php-quote">'edit'</span> ) {
</span><span id="967" class="l"><a href="#967"> 967: </a>                <span class="php-var">$cond</span> = <span class="php-var">$editor</span>-&gt;pkeyToArray( <span class="php-var">$host</span>[<span class="php-quote">'id'</span>], <span class="php-keyword1">true</span> );
</span><span id="968" class="l"><a href="#968"> 968: </a>                <span class="php-var">$query</span>-&gt;where( <span class="php-var">$cond</span>, <span class="php-keyword1">null</span>, <span class="php-quote">'!='</span> );
</span><span id="969" class="l"><a href="#969"> 969: </a>            }
</span><span id="970" class="l"><a href="#970"> 970: </a>
</span><span id="971" class="l"><a href="#971"> 971: </a>            <span class="php-var">$res</span> = <span class="php-var">$query</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="972" class="l"><a href="#972"> 972: </a>
</span><span id="973" class="l"><a href="#973"> 973: </a>            <span class="php-keyword1">return</span> <span class="php-var">$res</span>-&gt;<span class="php-keyword2">count</span>() === <span class="php-num">0</span> ?
</span><span id="974" class="l"><a href="#974"> 974: </a>                <span class="php-keyword1">true</span> :
</span><span id="975" class="l"><a href="#975"> 975: </a>                <span class="php-var">$opts</span>-&gt;message(); 
</span><span id="976" class="l"><a href="#976"> 976: </a>        };
</span><span id="977" class="l"><a href="#977"> 977: </a>    }
</span><span id="978" class="l"><a href="#978"> 978: </a>
</span><span id="979" class="l"><a href="#979"> 979: </a>    <span class="php-comment">/**
</span></span><span id="980" class="l"><a href="#980"> 980: </a><span class="php-comment">     * Check that the given value is a value that is available in a database -
</span></span><span id="981" class="l"><a href="#981"> 981: </a><span class="php-comment">     * i.e. a join primary key. This will attempt to automatically use the table
</span></span><span id="982" class="l"><a href="#982"> 982: </a><span class="php-comment">     * name and value column from the field's `options` method (under the
</span></span><span id="983" class="l"><a href="#983"> 983: </a><span class="php-comment">     * assumption that it will typically be used with a joined field), but the
</span></span><span id="984" class="l"><a href="#984"> 984: </a><span class="php-comment">     * table and field can also be specified via the options.
</span></span><span id="985" class="l"><a href="#985"> 985: </a><span class="php-comment">     *
</span></span><span id="986" class="l"><a href="#986"> 986: </a><span class="php-comment">     *  @param string $val The value to check for validity
</span></span><span id="987" class="l"><a href="#987"> 987: </a><span class="php-comment">     *  @param string[] $data The full data set submitted
</span></span><span id="988" class="l"><a href="#988"> 988: </a><span class="php-comment">     *  @param int|array $opts Validation options. The additional options of
</span></span><span id="989" class="l"><a href="#989"> 989: </a><span class="php-comment">     *    `db` - database connection object, `table` - database table to use and
</span></span><span id="990" class="l"><a href="#990"> 990: </a><span class="php-comment">     *    `column` - the column to check this value against as value, are also
</span></span><span id="991" class="l"><a href="#991"> 991: </a><span class="php-comment">     *    available. These options are not required and if not given are
</span></span><span id="992" class="l"><a href="#992"> 992: </a><span class="php-comment">     *    automatically derived from the Editor and Field instances.
</span></span><span id="993" class="l"><a href="#993"> 993: </a><span class="php-comment">     *  @param array $host Host information
</span></span><span id="994" class="l"><a href="#994"> 994: </a><span class="php-comment">     *  @return string|true true if the value is valid, a string with an error
</span></span><span id="995" class="l"><a href="#995"> 995: </a><span class="php-comment">     *    message otherwise.
</span></span><span id="996" class="l"><a href="#996"> 996: </a><span class="php-comment">     */</span>
</span><span id="997" class="l"><a href="#997"> 997: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dbValues( <span class="php-var">$cfg</span>=<span class="php-keyword1">null</span>, <span class="php-var">$column</span>=<span class="php-keyword1">null</span>, <span class="php-var">$table</span>=<span class="php-keyword1">null</span>, <span class="php-var">$db</span>=<span class="php-keyword1">null</span>, <span class="php-var">$values</span>=<span class="php-keyword1">array</span>() ) {
</span><span id="998" class="l"><a href="#998"> 998: </a>        <span class="php-var">$opts</span> = ValidateOptions::select( <span class="php-var">$cfg</span> );
</span><span id="999" class="l"><a href="#999"> 999: </a>        
</span><span id="1000" class="l"><a href="#1000">1000: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$field</span>, <span class="php-var">$host</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$opts</span>, <span class="php-var">$column</span>, <span class="php-var">$table</span>, <span class="php-var">$db</span>, <span class="php-var">$values</span> ) {
</span><span id="1001" class="l"><a href="#1001">1001: </a>            <span class="php-var">$common</span> = Validate::_common( <span class="php-var">$val</span>, <span class="php-var">$opts</span> );
</span><span id="1002" class="l"><a href="#1002">1002: </a>
</span><span id="1003" class="l"><a href="#1003">1003: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$common</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="1004" class="l"><a href="#1004">1004: </a>                <span class="php-keyword1">return</span> <span class="php-var">$common</span> === <span class="php-keyword1">false</span> ?
</span><span id="1005" class="l"><a href="#1005">1005: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="1006" class="l"><a href="#1006">1006: </a>                    <span class="php-var">$common</span>;
</span><span id="1007" class="l"><a href="#1007">1007: </a>            }
</span><span id="1008" class="l"><a href="#1008">1008: </a>
</span><span id="1009" class="l"><a href="#1009">1009: </a>            <span class="php-comment">// Allow local values to be defined - for example null</span>
</span><span id="1010" class="l"><a href="#1010">1010: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">in_array</span>(<span class="php-var">$val</span>, <span class="php-var">$values</span>) ) {
</span><span id="1011" class="l"><a href="#1011">1011: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="1012" class="l"><a href="#1012">1012: </a>            }
</span><span id="1013" class="l"><a href="#1013">1013: </a>
</span><span id="1014" class="l"><a href="#1014">1014: </a>            <span class="php-var">$editor</span> = <span class="php-var">$host</span>[<span class="php-quote">'editor'</span>];
</span><span id="1015" class="l"><a href="#1015">1015: </a>            <span class="php-var">$options</span> = <span class="php-var">$field</span>-&gt;options();
</span><span id="1016" class="l"><a href="#1016">1016: </a>
</span><span id="1017" class="l"><a href="#1017">1017: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$db</span> ) {
</span><span id="1018" class="l"><a href="#1018">1018: </a>                <span class="php-var">$db</span> = <span class="php-var">$host</span>[<span class="php-quote">'db'</span>];
</span><span id="1019" class="l"><a href="#1019">1019: </a>            }
</span><span id="1020" class="l"><a href="#1020">1020: </a>
</span><span id="1021" class="l"><a href="#1021">1021: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$table</span> ) {
</span><span id="1022" class="l"><a href="#1022">1022: </a>                <span class="php-var">$table</span> = <span class="php-var">$options</span>-&gt;table(); <span class="php-comment">// Returns an array, but `select` will take an array</span>
</span><span id="1023" class="l"><a href="#1023">1023: </a>            }
</span><span id="1024" class="l"><a href="#1024">1024: </a>
</span><span id="1025" class="l"><a href="#1025">1025: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$column</span> ) {
</span><span id="1026" class="l"><a href="#1026">1026: </a>                <span class="php-var">$column</span> = <span class="php-var">$options</span>-&gt;value();
</span><span id="1027" class="l"><a href="#1027">1027: </a>            }
</span><span id="1028" class="l"><a href="#1028">1028: </a>
</span><span id="1029" class="l"><a href="#1029">1029: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$table</span> ) {
</span><span id="1030" class="l"><a href="#1030">1030: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Table for database value check is not defined for field '</span>.<span class="php-var">$field</span>-&gt;name());
</span><span id="1031" class="l"><a href="#1031">1031: </a>            }
</span><span id="1032" class="l"><a href="#1032">1032: </a>
</span><span id="1033" class="l"><a href="#1033">1033: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$column</span> ) {
</span><span id="1034" class="l"><a href="#1034">1034: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Value column for database value check is not defined for field '</span>.<span class="php-var">$field</span>-&gt;name());
</span><span id="1035" class="l"><a href="#1035">1035: </a>            }
</span><span id="1036" class="l"><a href="#1036">1036: </a>
</span><span id="1037" class="l"><a href="#1037">1037: </a>            <span class="php-comment">// Try / catch in case the submitted value can't be represented as the</span>
</span><span id="1038" class="l"><a href="#1038">1038: </a>            <span class="php-comment">// database type (e.g. an empty string as an integer)</span>
</span><span id="1039" class="l"><a href="#1039">1039: </a>            <span class="php-keyword1">try</span> {
</span><span id="1040" class="l"><a href="#1040">1040: </a>                <span class="php-var">$count</span> = <span class="php-var">$db</span>
</span><span id="1041" class="l"><a href="#1041">1041: </a>                    -&gt;query( <span class="php-quote">'select'</span>, <span class="php-var">$table</span> )
</span><span id="1042" class="l"><a href="#1042">1042: </a>                    -&gt;get( <span class="php-var">$column</span> )
</span><span id="1043" class="l"><a href="#1043">1043: </a>                    -&gt;where( <span class="php-var">$column</span>, <span class="php-var">$val</span> )
</span><span id="1044" class="l"><a href="#1044">1044: </a>                    -&gt;<span class="php-keyword2">exec</span>()
</span><span id="1045" class="l"><a href="#1045">1045: </a>                    -&gt;<span class="php-keyword2">count</span>();
</span><span id="1046" class="l"><a href="#1046">1046: </a>
</span><span id="1047" class="l"><a href="#1047">1047: </a>                <span class="php-keyword1">return</span> <span class="php-var">$count</span> === <span class="php-num">0</span> ?
</span><span id="1048" class="l"><a href="#1048">1048: </a>                    <span class="php-var">$opts</span>-&gt;message() :
</span><span id="1049" class="l"><a href="#1049">1049: </a>                    <span class="php-keyword1">true</span>;
</span><span id="1050" class="l"><a href="#1050">1050: </a>            }
</span><span id="1051" class="l"><a href="#1051">1051: </a>            <span class="php-keyword1">catch</span> (\Exception <span class="php-var">$e</span>) {
</span><span id="1052" class="l"><a href="#1052">1052: </a>                <span class="php-keyword1">return</span> <span class="php-var">$opts</span>-&gt;message();
</span><span id="1053" class="l"><a href="#1053">1053: </a>            }
</span><span id="1054" class="l"><a href="#1054">1054: </a>        };
</span><span id="1055" class="l"><a href="#1055">1055: </a>    }
</span><span id="1056" class="l"><a href="#1056">1056: </a>    
</span><span id="1057" class="l"><a href="#1057">1057: </a>    
</span><span id="1058" class="l"><a href="#1058">1058: </a>
</span><span id="1059" class="l"><a href="#1059">1059: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="1060" class="l"><a href="#1060">1060: </a><span class="php-comment">    * File validation methods
</span></span><span id="1061" class="l"><a href="#1061">1061: </a><span class="php-comment">    */</span>
</span><span id="1062" class="l"><a href="#1062">1062: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> fileExtensions ( <span class="php-var">$extensions</span>, <span class="php-var">$msg</span>=<span class="php-quote">&quot;This file type cannot be uploaded.&quot;</span> ) {
</span><span id="1063" class="l"><a href="#1063">1063: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$file</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$extensions</span>, <span class="php-var">$msg</span> ) {
</span><span id="1064" class="l"><a href="#1064">1064: </a>            <span class="php-var">$extn</span> = <span class="php-keyword2">pathinfo</span>(<span class="php-var">$file</span>[<span class="php-quote">'name'</span>], PATHINFO_EXTENSION);
</span><span id="1065" class="l"><a href="#1065">1065: </a>
</span><span id="1066" class="l"><a href="#1066">1066: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$extensions</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1067" class="l"><a href="#1067">1067: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword2">strtolower</span>( <span class="php-var">$extn</span> ) === <span class="php-keyword2">strtolower</span>( <span class="php-var">$extensions</span>[<span class="php-var">$i</span>] ) ) {
</span><span id="1068" class="l"><a href="#1068">1068: </a>                    <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="1069" class="l"><a href="#1069">1069: </a>                }
</span><span id="1070" class="l"><a href="#1070">1070: </a>            }
</span><span id="1071" class="l"><a href="#1071">1071: </a>
</span><span id="1072" class="l"><a href="#1072">1072: </a>            <span class="php-keyword1">return</span> <span class="php-var">$msg</span>;
</span><span id="1073" class="l"><a href="#1073">1073: </a>        };
</span><span id="1074" class="l"><a href="#1074">1074: </a>    }
</span><span id="1075" class="l"><a href="#1075">1075: </a>
</span><span id="1076" class="l"><a href="#1076">1076: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> <span class="php-keyword2">fileSize</span> ( <span class="php-var">$size</span>, <span class="php-var">$msg</span>=<span class="php-quote">&quot;Uploaded file is too large.&quot;</span> ) {
</span><span id="1077" class="l"><a href="#1077">1077: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$file</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$size</span>, <span class="php-var">$msg</span> ) {
</span><span id="1078" class="l"><a href="#1078">1078: </a>            <span class="php-keyword1">return</span> <span class="php-var">$file</span>[<span class="php-quote">'size'</span>] &gt; <span class="php-var">$size</span> ?
</span><span id="1079" class="l"><a href="#1079">1079: </a>                <span class="php-var">$msg</span> :
</span><span id="1080" class="l"><a href="#1080">1080: </a>                <span class="php-keyword1">true</span>;
</span><span id="1081" class="l"><a href="#1081">1081: </a>        };
</span><span id="1082" class="l"><a href="#1082">1082: </a>    }
</span><span id="1083" class="l"><a href="#1083">1083: </a>
</span><span id="1084" class="l"><a href="#1084">1084: </a>
</span><span id="1085" class="l"><a href="#1085">1085: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="1086" class="l"><a href="#1086">1086: </a><span class="php-comment">     * Internal functions
</span></span><span id="1087" class="l"><a href="#1087">1087: </a><span class="php-comment">     * These legacy methods are for backwards compatibility with the old way of
</span></span><span id="1088" class="l"><a href="#1088">1088: </a><span class="php-comment">     * using the validation methods. They basically do argument swapping.
</span></span><span id="1089" class="l"><a href="#1089">1089: </a><span class="php-comment">     */</span>
</span><span id="1090" class="l"><a href="#1090">1090: </a>
</span><span id="1091" class="l"><a href="#1091">1091: </a>    <span class="php-comment">/**
</span></span><span id="1092" class="l"><a href="#1092">1092: </a><span class="php-comment">     * @internal
</span></span><span id="1093" class="l"><a href="#1093">1093: </a><span class="php-comment">     */</span>
</span><span id="1094" class="l"><a href="#1094">1094: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> noneLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1095" class="l"><a href="#1095">1095: </a>        <span class="php-keyword1">return</span> Validate::none();
</span><span id="1096" class="l"><a href="#1096">1096: </a>    }
</span><span id="1097" class="l"><a href="#1097">1097: </a>    
</span><span id="1098" class="l"><a href="#1098">1098: </a>    <span class="php-comment">/**
</span></span><span id="1099" class="l"><a href="#1099">1099: </a><span class="php-comment">     * @internal
</span></span><span id="1100" class="l"><a href="#1100">1100: </a><span class="php-comment">     */</span>
</span><span id="1101" class="l"><a href="#1101">1101: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> basicLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1102" class="l"><a href="#1102">1102: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>() );
</span><span id="1103" class="l"><a href="#1103">1103: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1104" class="l"><a href="#1104">1104: </a>
</span><span id="1105" class="l"><a href="#1105">1105: </a>        <span class="php-keyword1">return</span> Validate::required( <span class="php-var">$opts</span> );
</span><span id="1106" class="l"><a href="#1106">1106: </a>    }
</span><span id="1107" class="l"><a href="#1107">1107: </a>    
</span><span id="1108" class="l"><a href="#1108">1108: </a>    <span class="php-comment">/**
</span></span><span id="1109" class="l"><a href="#1109">1109: </a><span class="php-comment">     * @internal
</span></span><span id="1110" class="l"><a href="#1110">1110: </a><span class="php-comment">     */</span>
</span><span id="1111" class="l"><a href="#1111">1111: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> requiredLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1112" class="l"><a href="#1112">1112: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1113" class="l"><a href="#1113">1113: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This field is required.&quot;</span>
</span><span id="1114" class="l"><a href="#1114">1114: </a>        ) );
</span><span id="1115" class="l"><a href="#1115">1115: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1116" class="l"><a href="#1116">1116: </a>
</span><span id="1117" class="l"><a href="#1117">1117: </a>        <span class="php-keyword1">return</span> Validate::required( <span class="php-var">$opts</span> );
</span><span id="1118" class="l"><a href="#1118">1118: </a>    }
</span><span id="1119" class="l"><a href="#1119">1119: </a>    
</span><span id="1120" class="l"><a href="#1120">1120: </a>    <span class="php-comment">/**
</span></span><span id="1121" class="l"><a href="#1121">1121: </a><span class="php-comment">     * @internal
</span></span><span id="1122" class="l"><a href="#1122">1122: </a><span class="php-comment">     */</span>
</span><span id="1123" class="l"><a href="#1123">1123: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> notEmptyLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1124" class="l"><a href="#1124">1124: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1125" class="l"><a href="#1125">1125: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This field is required.&quot;</span>
</span><span id="1126" class="l"><a href="#1126">1126: </a>        ) );
</span><span id="1127" class="l"><a href="#1127">1127: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1128" class="l"><a href="#1128">1128: </a>
</span><span id="1129" class="l"><a href="#1129">1129: </a>        <span class="php-keyword1">return</span> Validate::notEmpty( <span class="php-var">$opts</span> );
</span><span id="1130" class="l"><a href="#1130">1130: </a>    }
</span><span id="1131" class="l"><a href="#1131">1131: </a>    
</span><span id="1132" class="l"><a href="#1132">1132: </a>    <span class="php-comment">/**
</span></span><span id="1133" class="l"><a href="#1133">1133: </a><span class="php-comment">     * @internal
</span></span><span id="1134" class="l"><a href="#1134">1134: </a><span class="php-comment">     */</span>
</span><span id="1135" class="l"><a href="#1135">1135: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> booleanLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1136" class="l"><a href="#1136">1136: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1137" class="l"><a href="#1137">1137: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Please enter true or false.&quot;</span>
</span><span id="1138" class="l"><a href="#1138">1138: </a>        ) );
</span><span id="1139" class="l"><a href="#1139">1139: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1140" class="l"><a href="#1140">1140: </a>
</span><span id="1141" class="l"><a href="#1141">1141: </a>        <span class="php-keyword1">return</span> Validate::notEmpty( <span class="php-var">$opts</span> );
</span><span id="1142" class="l"><a href="#1142">1142: </a>    }
</span><span id="1143" class="l"><a href="#1143">1143: </a>    
</span><span id="1144" class="l"><a href="#1144">1144: </a>    <span class="php-comment">/**
</span></span><span id="1145" class="l"><a href="#1145">1145: </a><span class="php-comment">     * @internal
</span></span><span id="1146" class="l"><a href="#1146">1146: </a><span class="php-comment">     */</span>
</span><span id="1147" class="l"><a href="#1147">1147: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> numericLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1148" class="l"><a href="#1148">1148: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1149" class="l"><a href="#1149">1149: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This input must be given as a number.&quot;</span>
</span><span id="1150" class="l"><a href="#1150">1150: </a>        ) );
</span><span id="1151" class="l"><a href="#1151">1151: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1152" class="l"><a href="#1152">1152: </a>
</span><span id="1153" class="l"><a href="#1153">1153: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>] ) ?
</span><span id="1154" class="l"><a href="#1154">1154: </a>            Validate::numeric( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>], <span class="php-var">$opts</span> ) :
</span><span id="1155" class="l"><a href="#1155">1155: </a>            Validate::numeric( <span class="php-quote">'.'</span>, <span class="php-var">$opts</span> );
</span><span id="1156" class="l"><a href="#1156">1156: </a>    }
</span><span id="1157" class="l"><a href="#1157">1157: </a>    
</span><span id="1158" class="l"><a href="#1158">1158: </a>    <span class="php-comment">/**
</span></span><span id="1159" class="l"><a href="#1159">1159: </a><span class="php-comment">     * @internal
</span></span><span id="1160" class="l"><a href="#1160">1160: </a><span class="php-comment">     */</span>
</span><span id="1161" class="l"><a href="#1161">1161: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minNumLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1162" class="l"><a href="#1162">1162: </a>        <span class="php-var">$min</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$legacyOpts</span>) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'min'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1163" class="l"><a href="#1163">1163: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1164" class="l"><a href="#1164">1164: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Number is too small, must be &quot;</span>.<span class="php-var">$min</span>.<span class="php-quote">&quot; or larger.&quot;</span>
</span><span id="1165" class="l"><a href="#1165">1165: </a>        ) );
</span><span id="1166" class="l"><a href="#1166">1166: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1167" class="l"><a href="#1167">1167: </a>
</span><span id="1168" class="l"><a href="#1168">1168: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>] ) ?
</span><span id="1169" class="l"><a href="#1169">1169: </a>            Validate::minNum( <span class="php-var">$min</span>, <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>], <span class="php-var">$opts</span> ) :
</span><span id="1170" class="l"><a href="#1170">1170: </a>            Validate::minNum( <span class="php-var">$min</span>, <span class="php-quote">'.'</span>, <span class="php-var">$opts</span> );
</span><span id="1171" class="l"><a href="#1171">1171: </a>    }
</span><span id="1172" class="l"><a href="#1172">1172: </a>    
</span><span id="1173" class="l"><a href="#1173">1173: </a>    <span class="php-comment">/**
</span></span><span id="1174" class="l"><a href="#1174">1174: </a><span class="php-comment">     * @internal
</span></span><span id="1175" class="l"><a href="#1175">1175: </a><span class="php-comment">     */</span>
</span><span id="1176" class="l"><a href="#1176">1176: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> maxNumLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1177" class="l"><a href="#1177">1177: </a>        <span class="php-var">$max</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$legacyOpts</span>) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'max'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1178" class="l"><a href="#1178">1178: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1179" class="l"><a href="#1179">1179: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Number is too large, must be &quot;</span>.<span class="php-var">$max</span>.<span class="php-quote">&quot; or smaller.&quot;</span>
</span><span id="1180" class="l"><a href="#1180">1180: </a>        ) );
</span><span id="1181" class="l"><a href="#1181">1181: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1182" class="l"><a href="#1182">1182: </a>
</span><span id="1183" class="l"><a href="#1183">1183: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>] ) ?
</span><span id="1184" class="l"><a href="#1184">1184: </a>            Validate::maxNum( <span class="php-var">$max</span>, <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>], <span class="php-var">$opts</span> ) :
</span><span id="1185" class="l"><a href="#1185">1185: </a>            Validate::maxNum( <span class="php-var">$max</span>, <span class="php-quote">'.'</span>, <span class="php-var">$opts</span> );
</span><span id="1186" class="l"><a href="#1186">1186: </a>    }
</span><span id="1187" class="l"><a href="#1187">1187: </a>    
</span><span id="1188" class="l"><a href="#1188">1188: </a>    <span class="php-comment">/**
</span></span><span id="1189" class="l"><a href="#1189">1189: </a><span class="php-comment">     * @internal
</span></span><span id="1190" class="l"><a href="#1190">1190: </a><span class="php-comment">     */</span>
</span><span id="1191" class="l"><a href="#1191">1191: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minMaxNumLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1192" class="l"><a href="#1192">1192: </a>        <span class="php-var">$min</span> = <span class="php-var">$legacyOpts</span>[<span class="php-quote">'min'</span>];
</span><span id="1193" class="l"><a href="#1193">1193: </a>        <span class="php-var">$max</span> = <span class="php-var">$legacyOpts</span>[<span class="php-quote">'max'</span>];
</span><span id="1194" class="l"><a href="#1194">1194: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>() );
</span><span id="1195" class="l"><a href="#1195">1195: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1196" class="l"><a href="#1196">1196: </a>
</span><span id="1197" class="l"><a href="#1197">1197: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>] ) ?
</span><span id="1198" class="l"><a href="#1198">1198: </a>            Validate::minMaxNum( <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-var">$legacyOpts</span>[<span class="php-quote">'decimal'</span>], <span class="php-var">$opts</span> ) :
</span><span id="1199" class="l"><a href="#1199">1199: </a>            Validate::minMaxNum( <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-quote">'.'</span>, <span class="php-var">$opts</span> );
</span><span id="1200" class="l"><a href="#1200">1200: </a>    }
</span><span id="1201" class="l"><a href="#1201">1201: </a>    
</span><span id="1202" class="l"><a href="#1202">1202: </a>    <span class="php-comment">/**
</span></span><span id="1203" class="l"><a href="#1203">1203: </a><span class="php-comment">     * @internal
</span></span><span id="1204" class="l"><a href="#1204">1204: </a><span class="php-comment">     */</span>
</span><span id="1205" class="l"><a href="#1205">1205: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> emailLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1206" class="l"><a href="#1206">1206: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1207" class="l"><a href="#1207">1207: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Please enter a valid e-mail address.&quot;</span>
</span><span id="1208" class="l"><a href="#1208">1208: </a>        ) );
</span><span id="1209" class="l"><a href="#1209">1209: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1210" class="l"><a href="#1210">1210: </a>
</span><span id="1211" class="l"><a href="#1211">1211: </a>        <span class="php-keyword1">return</span> Validate::email( <span class="php-var">$opts</span> );
</span><span id="1212" class="l"><a href="#1212">1212: </a>    }
</span><span id="1213" class="l"><a href="#1213">1213: </a>    
</span><span id="1214" class="l"><a href="#1214">1214: </a>    <span class="php-comment">/**
</span></span><span id="1215" class="l"><a href="#1215">1215: </a><span class="php-comment">     * @internal
</span></span><span id="1216" class="l"><a href="#1216">1216: </a><span class="php-comment">     */</span>
</span><span id="1217" class="l"><a href="#1217">1217: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minLenLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1218" class="l"><a href="#1218">1218: </a>        <span class="php-var">$min</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$legacyOpts</span>) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'min'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1219" class="l"><a href="#1219">1219: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1220" class="l"><a href="#1220">1220: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;The input is too short. &quot;</span>.<span class="php-var">$min</span>.<span class="php-quote">&quot; characters required.&quot;</span>
</span><span id="1221" class="l"><a href="#1221">1221: </a>        ) );
</span><span id="1222" class="l"><a href="#1222">1222: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1223" class="l"><a href="#1223">1223: </a>
</span><span id="1224" class="l"><a href="#1224">1224: </a>        <span class="php-keyword1">return</span> Validate::minLen( <span class="php-var">$min</span>, <span class="php-var">$opts</span> );
</span><span id="1225" class="l"><a href="#1225">1225: </a>    }
</span><span id="1226" class="l"><a href="#1226">1226: </a>    
</span><span id="1227" class="l"><a href="#1227">1227: </a>    <span class="php-comment">/**
</span></span><span id="1228" class="l"><a href="#1228">1228: </a><span class="php-comment">     * @internal
</span></span><span id="1229" class="l"><a href="#1229">1229: </a><span class="php-comment">     */</span>
</span><span id="1230" class="l"><a href="#1230">1230: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> maxLenLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1231" class="l"><a href="#1231">1231: </a>        <span class="php-var">$max</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$legacyOpts</span>) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'max'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1232" class="l"><a href="#1232">1232: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1233" class="l"><a href="#1233">1233: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;The input is too long. &quot;</span>.<span class="php-var">$max</span>.<span class="php-quote">&quot; characters maximum.&quot;</span>
</span><span id="1234" class="l"><a href="#1234">1234: </a>        ) );
</span><span id="1235" class="l"><a href="#1235">1235: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1236" class="l"><a href="#1236">1236: </a>
</span><span id="1237" class="l"><a href="#1237">1237: </a>        <span class="php-keyword1">return</span> Validate::maxLen( <span class="php-var">$max</span>, <span class="php-var">$opts</span> );
</span><span id="1238" class="l"><a href="#1238">1238: </a>    }
</span><span id="1239" class="l"><a href="#1239">1239: </a>    
</span><span id="1240" class="l"><a href="#1240">1240: </a>    <span class="php-comment">/**
</span></span><span id="1241" class="l"><a href="#1241">1241: </a><span class="php-comment">     * @internal
</span></span><span id="1242" class="l"><a href="#1242">1242: </a><span class="php-comment">     */</span>
</span><span id="1243" class="l"><a href="#1243">1243: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> minMaxLenLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1244" class="l"><a href="#1244">1244: </a>        <span class="php-var">$min</span> = <span class="php-var">$legacyOpts</span>[<span class="php-quote">'min'</span>];
</span><span id="1245" class="l"><a href="#1245">1245: </a>        <span class="php-var">$max</span> = <span class="php-var">$legacyOpts</span>[<span class="php-quote">'max'</span>];
</span><span id="1246" class="l"><a href="#1246">1246: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>() );
</span><span id="1247" class="l"><a href="#1247">1247: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1248" class="l"><a href="#1248">1248: </a>
</span><span id="1249" class="l"><a href="#1249">1249: </a>        <span class="php-keyword1">return</span> Validate::minMaxLen( <span class="php-var">$min</span>, <span class="php-var">$max</span>, <span class="php-var">$opts</span> );
</span><span id="1250" class="l"><a href="#1250">1250: </a>    }
</span><span id="1251" class="l"><a href="#1251">1251: </a>    
</span><span id="1252" class="l"><a href="#1252">1252: </a>    <span class="php-comment">/**
</span></span><span id="1253" class="l"><a href="#1253">1253: </a><span class="php-comment">     * @internal
</span></span><span id="1254" class="l"><a href="#1254">1254: </a><span class="php-comment">     */</span>
</span><span id="1255" class="l"><a href="#1255">1255: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> ipLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1256" class="l"><a href="#1256">1256: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1257" class="l"><a href="#1257">1257: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Please enter a valid IP address.&quot;</span>
</span><span id="1258" class="l"><a href="#1258">1258: </a>        ) );
</span><span id="1259" class="l"><a href="#1259">1259: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1260" class="l"><a href="#1260">1260: </a>
</span><span id="1261" class="l"><a href="#1261">1261: </a>        <span class="php-keyword1">return</span> Validate::ip( <span class="php-var">$opts</span> );
</span><span id="1262" class="l"><a href="#1262">1262: </a>    }
</span><span id="1263" class="l"><a href="#1263">1263: </a>    
</span><span id="1264" class="l"><a href="#1264">1264: </a>    <span class="php-comment">/**
</span></span><span id="1265" class="l"><a href="#1265">1265: </a><span class="php-comment">     * @internal
</span></span><span id="1266" class="l"><a href="#1266">1266: </a><span class="php-comment">     */</span>
</span><span id="1267" class="l"><a href="#1267">1267: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> urlLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1268" class="l"><a href="#1268">1268: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1269" class="l"><a href="#1269">1269: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Please enter a valid URL.&quot;</span>
</span><span id="1270" class="l"><a href="#1270">1270: </a>        ) );
</span><span id="1271" class="l"><a href="#1271">1271: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1272" class="l"><a href="#1272">1272: </a>
</span><span id="1273" class="l"><a href="#1273">1273: </a>        <span class="php-keyword1">return</span> Validate::url( <span class="php-var">$opts</span> );
</span><span id="1274" class="l"><a href="#1274">1274: </a>    }
</span><span id="1275" class="l"><a href="#1275">1275: </a>    
</span><span id="1276" class="l"><a href="#1276">1276: </a>    <span class="php-comment">/**
</span></span><span id="1277" class="l"><a href="#1277">1277: </a><span class="php-comment">     * @internal
</span></span><span id="1278" class="l"><a href="#1278">1278: </a><span class="php-comment">     */</span>
</span><span id="1279" class="l"><a href="#1279">1279: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> xssLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1280" class="l"><a href="#1280">1280: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1281" class="l"><a href="#1281">1281: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This field contains potentially unsafe data.&quot;</span>
</span><span id="1282" class="l"><a href="#1282">1282: </a>        ) );
</span><span id="1283" class="l"><a href="#1283">1283: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1284" class="l"><a href="#1284">1284: </a>
</span><span id="1285" class="l"><a href="#1285">1285: </a>        <span class="php-keyword1">return</span> Validate::xss( <span class="php-var">$opts</span> );
</span><span id="1286" class="l"><a href="#1286">1286: </a>    }
</span><span id="1287" class="l"><a href="#1287">1287: </a>    
</span><span id="1288" class="l"><a href="#1288">1288: </a>    <span class="php-comment">/**
</span></span><span id="1289" class="l"><a href="#1289">1289: </a><span class="php-comment">     * @internal
</span></span><span id="1290" class="l"><a href="#1290">1290: </a><span class="php-comment">     */</span>
</span><span id="1291" class="l"><a href="#1291">1291: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> valuesLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1292" class="l"><a href="#1292">1292: </a>        <span class="php-var">$values</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$legacyOpts</span>[<span class="php-quote">'valid'</span>]) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'valid'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1293" class="l"><a href="#1293">1293: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1294" class="l"><a href="#1294">1294: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This value is not valid.&quot;</span>
</span><span id="1295" class="l"><a href="#1295">1295: </a>        ) );
</span><span id="1296" class="l"><a href="#1296">1296: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1297" class="l"><a href="#1297">1297: </a>
</span><span id="1298" class="l"><a href="#1298">1298: </a>        <span class="php-keyword1">return</span> Validate::values( <span class="php-var">$values</span>, <span class="php-var">$opts</span> );
</span><span id="1299" class="l"><a href="#1299">1299: </a>    }
</span><span id="1300" class="l"><a href="#1300">1300: </a>    
</span><span id="1301" class="l"><a href="#1301">1301: </a>    <span class="php-comment">/**
</span></span><span id="1302" class="l"><a href="#1302">1302: </a><span class="php-comment">     * @internal
</span></span><span id="1303" class="l"><a href="#1303">1303: </a><span class="php-comment">     */</span>
</span><span id="1304" class="l"><a href="#1304">1304: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> noTagsLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1305" class="l"><a href="#1305">1305: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1306" class="l"><a href="#1306">1306: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This field may not contain HTML.&quot;</span>
</span><span id="1307" class="l"><a href="#1307">1307: </a>        ) );
</span><span id="1308" class="l"><a href="#1308">1308: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1309" class="l"><a href="#1309">1309: </a>
</span><span id="1310" class="l"><a href="#1310">1310: </a>        <span class="php-keyword1">return</span> Validate::noTags( <span class="php-var">$opts</span> );
</span><span id="1311" class="l"><a href="#1311">1311: </a>    }
</span><span id="1312" class="l"><a href="#1312">1312: </a>    
</span><span id="1313" class="l"><a href="#1313">1313: </a>    <span class="php-comment">/**
</span></span><span id="1314" class="l"><a href="#1314">1314: </a><span class="php-comment">     * @internal
</span></span><span id="1315" class="l"><a href="#1315">1315: </a><span class="php-comment">     */</span>
</span><span id="1316" class="l"><a href="#1316">1316: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dateFormatLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1317" class="l"><a href="#1317">1317: </a>        <span class="php-var">$format</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$legacyOpts</span>) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'format'</span>] : <span class="php-var">$legacyOpts</span>;
</span><span id="1318" class="l"><a href="#1318">1318: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1319" class="l"><a href="#1319">1319: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;Date is not in the expected format.&quot;</span>
</span><span id="1320" class="l"><a href="#1320">1320: </a>        ) );
</span><span id="1321" class="l"><a href="#1321">1321: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1322" class="l"><a href="#1322">1322: </a>
</span><span id="1323" class="l"><a href="#1323">1323: </a>        <span class="php-keyword1">return</span> Validate::dateFormat( <span class="php-var">$format</span>, <span class="php-var">$opts</span> );
</span><span id="1324" class="l"><a href="#1324">1324: </a>    }
</span><span id="1325" class="l"><a href="#1325">1325: </a>    
</span><span id="1326" class="l"><a href="#1326">1326: </a>    <span class="php-comment">/**
</span></span><span id="1327" class="l"><a href="#1327">1327: </a><span class="php-comment">     * @internal
</span></span><span id="1328" class="l"><a href="#1328">1328: </a><span class="php-comment">     */</span>
</span><span id="1329" class="l"><a href="#1329">1329: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> uniqueLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1330" class="l"><a href="#1330">1330: </a>        <span class="php-var">$table</span> =  <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'table'</span>] )  ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'table'</span>]  : <span class="php-keyword1">null</span>;
</span><span id="1331" class="l"><a href="#1331">1331: </a>        <span class="php-var">$column</span> = <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'column'</span>] ) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'column'</span>] : <span class="php-keyword1">null</span>;
</span><span id="1332" class="l"><a href="#1332">1332: </a>        <span class="php-var">$db</span> =     <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'db'</span>] )     ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'db'</span>]     : <span class="php-keyword1">null</span>;
</span><span id="1333" class="l"><a href="#1333">1333: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1334" class="l"><a href="#1334">1334: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This field must have a unique value.&quot;</span>
</span><span id="1335" class="l"><a href="#1335">1335: </a>        ) );
</span><span id="1336" class="l"><a href="#1336">1336: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1337" class="l"><a href="#1337">1337: </a>
</span><span id="1338" class="l"><a href="#1338">1338: </a>        <span class="php-keyword1">return</span> Validate::unique( <span class="php-var">$opts</span>, <span class="php-var">$column</span>, <span class="php-var">$table</span>, <span class="php-var">$db</span> );
</span><span id="1339" class="l"><a href="#1339">1339: </a>    }
</span><span id="1340" class="l"><a href="#1340">1340: </a>    
</span><span id="1341" class="l"><a href="#1341">1341: </a>    <span class="php-comment">/**
</span></span><span id="1342" class="l"><a href="#1342">1342: </a><span class="php-comment">     * @internal
</span></span><span id="1343" class="l"><a href="#1343">1343: </a><span class="php-comment">     */</span>
</span><span id="1344" class="l"><a href="#1344">1344: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dbValuesLegacy( <span class="php-var">$legacyOpts</span> ) {
</span><span id="1345" class="l"><a href="#1345">1345: </a>        <span class="php-var">$table</span> =  <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'table'</span>] )  ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'table'</span>]  : <span class="php-keyword1">null</span>;
</span><span id="1346" class="l"><a href="#1346">1346: </a>        <span class="php-var">$column</span> = <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'column'</span>] ) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'column'</span>] : <span class="php-keyword1">null</span>;
</span><span id="1347" class="l"><a href="#1347">1347: </a>        <span class="php-var">$db</span> =     <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'db'</span>] )     ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'db'</span>]     : <span class="php-keyword1">null</span>;
</span><span id="1348" class="l"><a href="#1348">1348: </a>        <span class="php-var">$values</span> = <span class="php-keyword1">isset</span>( <span class="php-var">$legacyOpts</span>[<span class="php-quote">'values'</span>] ) ? <span class="php-var">$legacyOpts</span>[<span class="php-quote">'values'</span>] : <span class="php-keyword1">array</span>();
</span><span id="1349" class="l"><a href="#1349">1349: </a>        <span class="php-var">$cfg</span> = Validate::_extend( <span class="php-var">$legacyOpts</span>, <span class="php-keyword1">null</span>, <span class="php-keyword1">array</span>(
</span><span id="1350" class="l"><a href="#1350">1350: </a>            <span class="php-quote">'message'</span> =&gt; <span class="php-quote">&quot;This value is not valid.&quot;</span>
</span><span id="1351" class="l"><a href="#1351">1351: </a>        ) );
</span><span id="1352" class="l"><a href="#1352">1352: </a>        <span class="php-var">$opts</span> = Validate::_commonLegacy( <span class="php-var">$cfg</span> );
</span><span id="1353" class="l"><a href="#1353">1353: </a>
</span><span id="1354" class="l"><a href="#1354">1354: </a>        <span class="php-keyword1">return</span> Validate::dbValues( <span class="php-var">$opts</span>, <span class="php-var">$column</span>, <span class="php-var">$table</span>, <span class="php-var">$db</span>, <span class="php-var">$values</span> );
</span><span id="1355" class="l"><a href="#1355">1355: </a>    }
</span><span id="1356" class="l"><a href="#1356">1356: </a>}
</span><span id="1357" class="l"><a href="#1357">1357: </a>
</span><span id="1358" class="l"><a href="#1358">1358: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
