<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Upload.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2015 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-keyword1">use</span> DataTables;
</span><span id="17" class="l"><a href="#17"> 17: </a>
</span><span id="18" class="l"><a href="#18"> 18: </a>
</span><span id="19" class="l"><a href="#19"> 19: </a><span class="php-comment">/**
</span></span><span id="20" class="l"><a href="#20"> 20: </a><span class="php-comment"> * Upload class for Editor. This class provides the ability to easily specify
</span></span><span id="21" class="l"><a href="#21"> 21: </a><span class="php-comment"> * file upload information, specifically how the file should be recorded on
</span></span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-comment"> * the server (database and file system).
</span></span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment"> *
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment"> * An instance of this class is attached to a field using the {@link
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment"> * Field.upload} method. When Editor detects a file upload for that file the
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment"> * information provided for this instance is executed.
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment"> *
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment"> * The configuration is primarily driven through the {@link db} and {@link
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment"> * action} methods:
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment"> *
</span></span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-comment"> * * {@link db} Describes how information about the uploaded file is to be
</span></span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-comment"> *   stored on the database.
</span></span><span id="33" class="l"><a href="#33"> 33: </a><span class="php-comment"> * * {@link action} Describes where the file should be stored on the file system
</span></span><span id="34" class="l"><a href="#34"> 34: </a><span class="php-comment"> *   and provides the option of specifying a custom action when a file is
</span></span><span id="35" class="l"><a href="#35"> 35: </a><span class="php-comment"> *   uploaded.
</span></span><span id="36" class="l"><a href="#36"> 36: </a><span class="php-comment"> *
</span></span><span id="37" class="l"><a href="#37"> 37: </a><span class="php-comment"> * Both methods are optional - you can store the file on the server using the
</span></span><span id="38" class="l"><a href="#38"> 38: </a><span class="php-comment"> * {@link db} method only if you want to store the file in the database, or if
</span></span><span id="39" class="l"><a href="#39"> 39: </a><span class="php-comment"> * you don't want to store relational data on the database us only {@link
</span></span><span id="40" class="l"><a href="#40"> 40: </a><span class="php-comment"> * action}. However, the majority of the time it is best to use both - store
</span></span><span id="41" class="l"><a href="#41"> 41: </a><span class="php-comment"> * information about the file on the database for fast retrieval (using a {@link
</span></span><span id="42" class="l"><a href="#42"> 42: </a><span class="php-comment"> * Editor.leftJoin()} for example) and the file on the file system for direct
</span></span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment"> * web access.
</span></span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-comment"> *
</span></span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-comment"> * @example
</span></span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-comment"> *   Store information about a file in a table called `files` and the actual
</span></span><span id="47" class="l"><a href="#47"> 47: </a><span class="php-comment"> *   file in an `uploads` directory.
</span></span><span id="48" class="l"><a href="#48"> 48: </a><span class="php-comment"> *   &lt;code&gt;
</span></span><span id="49" class="l"><a href="#49"> 49: </a><span class="php-comment"> *      Field::inst( 'imageId' )
</span></span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-comment"> *          -&gt;upload(
</span></span><span id="51" class="l"><a href="#51"> 51: </a><span class="php-comment"> *              Upload::inst( $_SERVER['DOCUMENT_ROOT'].'/uploads/__ID__.__EXTN__' )
</span></span><span id="52" class="l"><a href="#52"> 52: </a><span class="php-comment"> *                  -&gt;db( 'files', 'id', array(
</span></span><span id="53" class="l"><a href="#53"> 53: </a><span class="php-comment"> *                      'webPath'     =&gt; Upload::DB_WEB_PATH,
</span></span><span id="54" class="l"><a href="#54"> 54: </a><span class="php-comment"> *                      'fileName'    =&gt; Upload::DB_FILE_NAME,
</span></span><span id="55" class="l"><a href="#55"> 55: </a><span class="php-comment"> *                      'fileSize'    =&gt; Upload::DB_FILE_SIZE,
</span></span><span id="56" class="l"><a href="#56"> 56: </a><span class="php-comment"> *                      'systemPath'  =&gt; Upload::DB_SYSTEM_PATH
</span></span><span id="57" class="l"><a href="#57"> 57: </a><span class="php-comment"> *                  ) )
</span></span><span id="58" class="l"><a href="#58"> 58: </a><span class="php-comment"> *                  -&gt;allowedExtensions( array( 'png', 'jpg' ), &quot;Please upload an image file&quot; )
</span></span><span id="59" class="l"><a href="#59"> 59: </a><span class="php-comment"> *          )
</span></span><span id="60" class="l"><a href="#60"> 60: </a><span class="php-comment"> *  &lt;/code&gt;
</span></span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-comment"> *
</span></span><span id="62" class="l"><a href="#62"> 62: </a><span class="php-comment"> * @example
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment"> *   As above, but with PHP 5.4 (which allows chaining from new instances of a
</span></span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-comment"> *   class)
</span></span><span id="65" class="l"><a href="#65"> 65: </a><span class="php-comment"> *   &lt;code&gt;
</span></span><span id="66" class="l"><a href="#66"> 66: </a><span class="php-comment"> *      newField( 'imageId' )
</span></span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-comment"> *          -&gt;upload(
</span></span><span id="68" class="l"><a href="#68"> 68: </a><span class="php-comment"> *              new Upload( $_SERVER['DOCUMENT_ROOT'].'/uploads/__ID__.__EXTN__' )
</span></span><span id="69" class="l"><a href="#69"> 69: </a><span class="php-comment"> *                  -&gt;db( 'files', 'id', array(
</span></span><span id="70" class="l"><a href="#70"> 70: </a><span class="php-comment"> *                      'webPath'     =&gt; Upload::DB_WEB_PATH,
</span></span><span id="71" class="l"><a href="#71"> 71: </a><span class="php-comment"> *                      'fileName'    =&gt; Upload::DB_FILE_NAME,
</span></span><span id="72" class="l"><a href="#72"> 72: </a><span class="php-comment"> *                      'fileSize'    =&gt; Upload::DB_FILE_SIZE,
</span></span><span id="73" class="l"><a href="#73"> 73: </a><span class="php-comment"> *                      'systemPath'  =&gt; Upload::DB_SYSTEM_PATH
</span></span><span id="74" class="l"><a href="#74"> 74: </a><span class="php-comment"> *                  ) )
</span></span><span id="75" class="l"><a href="#75"> 75: </a><span class="php-comment"> *                  -&gt;allowedExtensions( array( 'png', 'jpg' ), &quot;Please upload an image file&quot; )
</span></span><span id="76" class="l"><a href="#76"> 76: </a><span class="php-comment"> *          )
</span></span><span id="77" class="l"><a href="#77"> 77: </a><span class="php-comment"> *  &lt;/code&gt;
</span></span><span id="78" class="l"><a href="#78"> 78: </a><span class="php-comment"> */</span>
</span><span id="79" class="l"><a href="#79"> 79: </a><span class="php-keyword1">class</span> Upload <span class="php-keyword1">extends</span> DataTables\Ext {
</span><span id="80" class="l"><a href="#80"> 80: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="81" class="l"><a href="#81"> 81: </a><span class="php-comment">     * Constants
</span></span><span id="82" class="l"><a href="#82"> 82: </a><span class="php-comment">     */</span>
</span><span id="83" class="l"><a href="#83"> 83: </a>    
</span><span id="84" class="l"><a href="#84"> 84: </a>    <span class="php-comment">/** Database value option (`Db()`) - File content. This should be written to
</span></span><span id="85" class="l"><a href="#85"> 85: </a><span class="php-comment">     * a blob. Typically this should be avoided and the file saved on the file
</span></span><span id="86" class="l"><a href="#86"> 86: </a><span class="php-comment">     * system, but there are cases where it can be useful to store the file in
</span></span><span id="87" class="l"><a href="#87"> 87: </a><span class="php-comment">     * the database.
</span></span><span id="88" class="l"><a href="#88"> 88: </a><span class="php-comment">     */</span>
</span><span id="89" class="l"><a href="#89"> 89: </a>    <span class="php-keyword1">const</span> DB_CONTENT      = <span class="php-quote">'editor-content'</span>;
</span><span id="90" class="l"><a href="#90"> 90: </a>
</span><span id="91" class="l"><a href="#91"> 91: </a>    <span class="php-comment">/** Database value option (`Db()`) - Content type */</span>
</span><span id="92" class="l"><a href="#92"> 92: </a>    <span class="php-keyword1">const</span> DB_CONTENT_TYPE = <span class="php-quote">'editor-contentType'</span>;
</span><span id="93" class="l"><a href="#93"> 93: </a>
</span><span id="94" class="l"><a href="#94"> 94: </a>    <span class="php-comment">/** Database value option (`Db()`) - File extension */</span>
</span><span id="95" class="l"><a href="#95"> 95: </a>    <span class="php-keyword1">const</span> DB_EXTN         = <span class="php-quote">'editor-extn'</span>;
</span><span id="96" class="l"><a href="#96"> 96: </a>
</span><span id="97" class="l"><a href="#97"> 97: </a>    <span class="php-comment">/** Database value option (`Db()`) - File name (with extension) */</span>
</span><span id="98" class="l"><a href="#98"> 98: </a>    <span class="php-keyword1">const</span> DB_FILE_NAME    = <span class="php-quote">'editor-fileName'</span>;
</span><span id="99" class="l"><a href="#99"> 99: </a>
</span><span id="100" class="l"><a href="#100">100: </a>    <span class="php-comment">/** Database value option (`Db()`) - File size (bytes) */</span>
</span><span id="101" class="l"><a href="#101">101: </a>    <span class="php-keyword1">const</span> DB_FILE_SIZE    = <span class="php-quote">'editor-fileSize'</span>;
</span><span id="102" class="l"><a href="#102">102: </a>
</span><span id="103" class="l"><a href="#103">103: </a>    <span class="php-comment">/** Database value option (`Db()`) - MIME type */</span>
</span><span id="104" class="l"><a href="#104">104: </a>    <span class="php-keyword1">const</span> DB_MIME_TYPE    = <span class="php-quote">'editor-mimeType'</span>;
</span><span id="105" class="l"><a href="#105">105: </a>
</span><span id="106" class="l"><a href="#106">106: </a>    <span class="php-comment">/** Database value option (`Db()`) - Full system path to the file */</span>
</span><span id="107" class="l"><a href="#107">107: </a>    <span class="php-keyword1">const</span> DB_SYSTEM_PATH  = <span class="php-quote">'editor-systemPath'</span>;
</span><span id="108" class="l"><a href="#108">108: </a>
</span><span id="109" class="l"><a href="#109">109: </a>    <span class="php-comment">/** Database value option (`Db()`) - HTTP path to the file. This is derived 
</span></span><span id="110" class="l"><a href="#110">110: </a><span class="php-comment">     * from the system path by removing `$_SERVER['DOCUMENT_ROOT']`. If your
</span></span><span id="111" class="l"><a href="#111">111: </a><span class="php-comment">     * images live outside of the document root a custom value would be to be
</span></span><span id="112" class="l"><a href="#112">112: </a><span class="php-comment">     * used.
</span></span><span id="113" class="l"><a href="#113">113: </a><span class="php-comment">     */</span>
</span><span id="114" class="l"><a href="#114">114: </a>    <span class="php-keyword1">const</span> DB_WEB_PATH     = <span class="php-quote">'editor-webPath'</span>;
</span><span id="115" class="l"><a href="#115">115: </a>
</span><span id="116" class="l"><a href="#116">116: </a>    <span class="php-comment">/** Read from the database - don't write to it
</span></span><span id="117" class="l"><a href="#117">117: </a><span class="php-comment">     */</span>
</span><span id="118" class="l"><a href="#118">118: </a>    <span class="php-keyword1">const</span> DB_READ_ONLY    = <span class="php-quote">'editor-readOnly'</span>;
</span><span id="119" class="l"><a href="#119">119: </a>
</span><span id="120" class="l"><a href="#120">120: </a>
</span><span id="121" class="l"><a href="#121">121: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="122" class="l"><a href="#122">122: </a><span class="php-comment">     * Private parameters
</span></span><span id="123" class="l"><a href="#123">123: </a><span class="php-comment">     */</span>
</span><span id="124" class="l"><a href="#124">124: </a>    
</span><span id="125" class="l"><a href="#125">125: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_action</span> = <span class="php-keyword1">null</span>;
</span><span id="126" class="l"><a href="#126">126: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbCleanCallback</span> = <span class="php-keyword1">null</span>;
</span><span id="127" class="l"><a href="#127">127: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbCleanTableField</span> = <span class="php-keyword1">null</span>;
</span><span id="128" class="l"><a href="#128">128: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbTable</span> = <span class="php-keyword1">null</span>;
</span><span id="129" class="l"><a href="#129">129: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbPKey</span> = <span class="php-keyword1">null</span>;
</span><span id="130" class="l"><a href="#130">130: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbFields</span> = <span class="php-keyword1">null</span>;
</span><span id="131" class="l"><a href="#131">131: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_extns</span> = <span class="php-keyword1">null</span>;
</span><span id="132" class="l"><a href="#132">132: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_extnError</span> = <span class="php-keyword1">null</span>;
</span><span id="133" class="l"><a href="#133">133: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_error</span> = <span class="php-keyword1">null</span>;
</span><span id="134" class="l"><a href="#134">134: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_validators</span> = <span class="php-keyword1">array</span>();
</span><span id="135" class="l"><a href="#135">135: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_where</span> = <span class="php-keyword1">array</span>();
</span><span id="136" class="l"><a href="#136">136: </a>
</span><span id="137" class="l"><a href="#137">137: </a>
</span><span id="138" class="l"><a href="#138">138: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="139" class="l"><a href="#139">139: </a><span class="php-comment">     * Constructor
</span></span><span id="140" class="l"><a href="#140">140: </a><span class="php-comment">     */</span>
</span><span id="141" class="l"><a href="#141">141: </a>
</span><span id="142" class="l"><a href="#142">142: </a>    <span class="php-comment">/**
</span></span><span id="143" class="l"><a href="#143">143: </a><span class="php-comment">     * Upload instance constructor
</span></span><span id="144" class="l"><a href="#144">144: </a><span class="php-comment">     * @param string|callable $action Action to take on upload - this is applied
</span></span><span id="145" class="l"><a href="#145">145: </a><span class="php-comment">     *     directly to {@link action}.
</span></span><span id="146" class="l"><a href="#146">146: </a><span class="php-comment">     */</span>
</span><span id="147" class="l"><a href="#147">147: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$action</span>=<span class="php-keyword1">null</span> )
</span><span id="148" class="l"><a href="#148">148: </a>    {
</span><span id="149" class="l"><a href="#149">149: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$action</span> ) {
</span><span id="150" class="l"><a href="#150">150: </a>            <span class="php-var">$this</span>-&gt;action( <span class="php-var">$action</span> );
</span><span id="151" class="l"><a href="#151">151: </a>        }
</span><span id="152" class="l"><a href="#152">152: </a>    }
</span><span id="153" class="l"><a href="#153">153: </a>
</span><span id="154" class="l"><a href="#154">154: </a>
</span><span id="155" class="l"><a href="#155">155: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="156" class="l"><a href="#156">156: </a><span class="php-comment">     * Public methods
</span></span><span id="157" class="l"><a href="#157">157: </a><span class="php-comment">     */</span>
</span><span id="158" class="l"><a href="#158">158: </a>
</span><span id="159" class="l"><a href="#159">159: </a>    <span class="php-comment">/**
</span></span><span id="160" class="l"><a href="#160">160: </a><span class="php-comment">     * Set the action to take when a file is uploaded. This can be either of:
</span></span><span id="161" class="l"><a href="#161">161: </a><span class="php-comment">     *
</span></span><span id="162" class="l"><a href="#162">162: </a><span class="php-comment">     * * A string - the value given is the full system path to where the
</span></span><span id="163" class="l"><a href="#163">163: </a><span class="php-comment">     *   uploaded file is written to. The value given can include three &quot;macros&quot;
</span></span><span id="164" class="l"><a href="#164">164: </a><span class="php-comment">     *   which are replaced by the script dependent on the uploaded file:
</span></span><span id="165" class="l"><a href="#165">165: </a><span class="php-comment">     *   * `__EXTN__` - the file extension
</span></span><span id="166" class="l"><a href="#166">166: </a><span class="php-comment">     *   * `__NAME__` - the uploaded file's name (including the extension)
</span></span><span id="167" class="l"><a href="#167">167: </a><span class="php-comment">     *   * `__ID__` - Database primary key value if the {@link db} method is
</span></span><span id="168" class="l"><a href="#168">168: </a><span class="php-comment">     *     used.
</span></span><span id="169" class="l"><a href="#169">169: </a><span class="php-comment">     * * A closure - if a function is given the responsibility of what to do
</span></span><span id="170" class="l"><a href="#170">170: </a><span class="php-comment">     *   with the uploaded file is transferred to this function. That will
</span></span><span id="171" class="l"><a href="#171">171: </a><span class="php-comment">     *   typically involve writing it to the file system so it can be used
</span></span><span id="172" class="l"><a href="#172">172: </a><span class="php-comment">     *   later.
</span></span><span id="173" class="l"><a href="#173">173: </a><span class="php-comment">     * 
</span></span><span id="174" class="l"><a href="#174">174: </a><span class="php-comment">     * @param  string|callable $action Action to take - see description above.
</span></span><span id="175" class="l"><a href="#175">175: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="176" class="l"><a href="#176">176: </a><span class="php-comment">     */</span>
</span><span id="177" class="l"><a href="#177">177: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> action ( <span class="php-var">$action</span> )
</span><span id="178" class="l"><a href="#178">178: </a>    {
</span><span id="179" class="l"><a href="#179">179: </a>        <span class="php-var">$this</span>-&gt;_action = <span class="php-var">$action</span>;
</span><span id="180" class="l"><a href="#180">180: </a>
</span><span id="181" class="l"><a href="#181">181: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="182" class="l"><a href="#182">182: </a>    }
</span><span id="183" class="l"><a href="#183">183: </a>
</span><span id="184" class="l"><a href="#184">184: </a>
</span><span id="185" class="l"><a href="#185">185: </a>    <span class="php-comment">/**
</span></span><span id="186" class="l"><a href="#186">186: </a><span class="php-comment">     * An array of valid file extensions that can be uploaded. This is for
</span></span><span id="187" class="l"><a href="#187">187: </a><span class="php-comment">     * simple validation that the file is of the expected type - for example you
</span></span><span id="188" class="l"><a href="#188">188: </a><span class="php-comment">     * might use `[ 'png', 'jpg', 'jpeg', 'gif' ]` for images. The check is
</span></span><span id="189" class="l"><a href="#189">189: </a><span class="php-comment">     * case-insensitive. If no extensions are given, no validation is performed
</span></span><span id="190" class="l"><a href="#190">190: </a><span class="php-comment">     * on the file extension.
</span></span><span id="191" class="l"><a href="#191">191: </a><span class="php-comment">     *
</span></span><span id="192" class="l"><a href="#192">192: </a><span class="php-comment">     * @param  string[] $extn  List of file extensions that are allowable for
</span></span><span id="193" class="l"><a href="#193">193: </a><span class="php-comment">     *     the upload
</span></span><span id="194" class="l"><a href="#194">194: </a><span class="php-comment">     * @param  string $error Error message if a file is uploaded that doesn't
</span></span><span id="195" class="l"><a href="#195">195: </a><span class="php-comment">     *     match the valid list of extensions.
</span></span><span id="196" class="l"><a href="#196">196: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="197" class="l"><a href="#197">197: </a><span class="php-comment">     * @deprecated Use Validate::fileExtensions
</span></span><span id="198" class="l"><a href="#198">198: </a><span class="php-comment">     */</span>
</span><span id="199" class="l"><a href="#199">199: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> allowedExtensions ( <span class="php-var">$extn</span>, <span class="php-var">$error</span>=<span class="php-quote">&quot;This file type cannot be uploaded&quot;</span> )
</span><span id="200" class="l"><a href="#200">200: </a>    {
</span><span id="201" class="l"><a href="#201">201: </a>        <span class="php-var">$this</span>-&gt;_extns = <span class="php-var">$extn</span>;
</span><span id="202" class="l"><a href="#202">202: </a>        <span class="php-var">$this</span>-&gt;_extnError = <span class="php-var">$error</span>;
</span><span id="203" class="l"><a href="#203">203: </a>
</span><span id="204" class="l"><a href="#204">204: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="205" class="l"><a href="#205">205: </a>    }
</span><span id="206" class="l"><a href="#206">206: </a>
</span><span id="207" class="l"><a href="#207">207: </a>
</span><span id="208" class="l"><a href="#208">208: </a>    <span class="php-comment">/**
</span></span><span id="209" class="l"><a href="#209">209: </a><span class="php-comment">     * Database configuration method. When used, this method will tell Editor
</span></span><span id="210" class="l"><a href="#210">210: </a><span class="php-comment">     * what information you want written to a database on file upload, should
</span></span><span id="211" class="l"><a href="#211">211: </a><span class="php-comment">     * you wish to store relational information about your file on the database
</span></span><span id="212" class="l"><a href="#212">212: </a><span class="php-comment">     * (this is generally recommended).
</span></span><span id="213" class="l"><a href="#213">213: </a><span class="php-comment">     *
</span></span><span id="214" class="l"><a href="#214">214: </a><span class="php-comment">     * @param  string $table  The name of the table where the file information
</span></span><span id="215" class="l"><a href="#215">215: </a><span class="php-comment">     *     should be stored
</span></span><span id="216" class="l"><a href="#216">216: </a><span class="php-comment">     * @param  string $pkey   Primary key column name. The `Upload` class
</span></span><span id="217" class="l"><a href="#217">217: </a><span class="php-comment">     *     requires that the database table have a single primary key so each
</span></span><span id="218" class="l"><a href="#218">218: </a><span class="php-comment">     *     row can be uniquely identified.
</span></span><span id="219" class="l"><a href="#219">219: </a><span class="php-comment">     * @param  array $fields A list of the fields to be written to on upload.
</span></span><span id="220" class="l"><a href="#220">220: </a><span class="php-comment">     *     The property names are the database columns and the values can be
</span></span><span id="221" class="l"><a href="#221">221: </a><span class="php-comment">     *     defined by the constants of this class. The value can also be a
</span></span><span id="222" class="l"><a href="#222">222: </a><span class="php-comment">     *     string or a closure function if you wish to send custom information
</span></span><span id="223" class="l"><a href="#223">223: </a><span class="php-comment">     *     to the database.
</span></span><span id="224" class="l"><a href="#224">224: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="225" class="l"><a href="#225">225: </a><span class="php-comment">     */</span>
</span><span id="226" class="l"><a href="#226">226: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> db ( <span class="php-var">$table</span>, <span class="php-var">$pkey</span>, <span class="php-var">$fields</span> )
</span><span id="227" class="l"><a href="#227">227: </a>    {
</span><span id="228" class="l"><a href="#228">228: </a>        <span class="php-var">$this</span>-&gt;_dbTable = <span class="php-var">$table</span>;
</span><span id="229" class="l"><a href="#229">229: </a>        <span class="php-var">$this</span>-&gt;_dbPKey = <span class="php-var">$pkey</span>;
</span><span id="230" class="l"><a href="#230">230: </a>        <span class="php-var">$this</span>-&gt;_dbFields = <span class="php-var">$fields</span>;
</span><span id="231" class="l"><a href="#231">231: </a>
</span><span id="232" class="l"><a href="#232">232: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="233" class="l"><a href="#233">233: </a>    }
</span><span id="234" class="l"><a href="#234">234: </a>
</span><span id="235" class="l"><a href="#235">235: </a>
</span><span id="236" class="l"><a href="#236">236: </a>    <span class="php-comment">/**
</span></span><span id="237" class="l"><a href="#237">237: </a><span class="php-comment">     * Set a callback function that is used to remove files which no longer have
</span></span><span id="238" class="l"><a href="#238">238: </a><span class="php-comment">     * a reference in a source table.
</span></span><span id="239" class="l"><a href="#239">239: </a><span class="php-comment">     *
</span></span><span id="240" class="l"><a href="#240">240: </a><span class="php-comment">     * @param  callable $callback Function that will be executed on clean. It is
</span></span><span id="241" class="l"><a href="#241">241: </a><span class="php-comment">     *     given an array of information from the database about the orphaned
</span></span><span id="242" class="l"><a href="#242">242: </a><span class="php-comment">     *     rows, and can return true to indicate that the rows should be
</span></span><span id="243" class="l"><a href="#243">243: </a><span class="php-comment">     *     removed from the database. Any other return value (including none)
</span></span><span id="244" class="l"><a href="#244">244: </a><span class="php-comment">     *     will result in the records being retained.
</span></span><span id="245" class="l"><a href="#245">245: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="246" class="l"><a href="#246">246: </a><span class="php-comment">     */</span>
</span><span id="247" class="l"><a href="#247">247: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> dbClean( <span class="php-var">$tableField</span>, <span class="php-var">$callback</span>=<span class="php-keyword1">null</span> )
</span><span id="248" class="l"><a href="#248">248: </a>    {
</span><span id="249" class="l"><a href="#249">249: </a>        <span class="php-comment">// Argument swapping</span>
</span><span id="250" class="l"><a href="#250">250: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$callback</span> === <span class="php-keyword1">null</span> ) {
</span><span id="251" class="l"><a href="#251">251: </a>            <span class="php-var">$callback</span> = <span class="php-var">$tableField</span>;
</span><span id="252" class="l"><a href="#252">252: </a>            <span class="php-var">$tableField</span> = <span class="php-keyword1">null</span>;
</span><span id="253" class="l"><a href="#253">253: </a>        }
</span><span id="254" class="l"><a href="#254">254: </a>
</span><span id="255" class="l"><a href="#255">255: </a>        <span class="php-var">$this</span>-&gt;_dbCleanCallback = <span class="php-var">$callback</span>;
</span><span id="256" class="l"><a href="#256">256: </a>        <span class="php-var">$this</span>-&gt;_dbCleanTableField = <span class="php-var">$tableField</span>;
</span><span id="257" class="l"><a href="#257">257: </a>
</span><span id="258" class="l"><a href="#258">258: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="259" class="l"><a href="#259">259: </a>    }
</span><span id="260" class="l"><a href="#260">260: </a>
</span><span id="261" class="l"><a href="#261">261: </a>
</span><span id="262" class="l"><a href="#262">262: </a>    <span class="php-comment">/**
</span></span><span id="263" class="l"><a href="#263">263: </a><span class="php-comment">     * Add a validation method to check file uploads. Multiple validators can be
</span></span><span id="264" class="l"><a href="#264">264: </a><span class="php-comment">     * added by calling this method multiple times - they will be executed in
</span></span><span id="265" class="l"><a href="#265">265: </a><span class="php-comment">     * sequence when a file has been uploaded.
</span></span><span id="266" class="l"><a href="#266">266: </a><span class="php-comment">     *
</span></span><span id="267" class="l"><a href="#267">267: </a><span class="php-comment">     * @param  callable $fn Validation function. A PHP `$_FILES` parameter is
</span></span><span id="268" class="l"><a href="#268">268: </a><span class="php-comment">     *     passed in for the uploaded file and the return is either a string
</span></span><span id="269" class="l"><a href="#269">269: </a><span class="php-comment">     *     (validation failed and error message), or `null` (validation passed).
</span></span><span id="270" class="l"><a href="#270">270: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="271" class="l"><a href="#271">271: </a><span class="php-comment">     */</span>
</span><span id="272" class="l"><a href="#272">272: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validator ( <span class="php-var">$fn</span> )
</span><span id="273" class="l"><a href="#273">273: </a>    {
</span><span id="274" class="l"><a href="#274">274: </a>        <span class="php-var">$this</span>-&gt;_validators[] = <span class="php-var">$fn</span>;
</span><span id="275" class="l"><a href="#275">275: </a>
</span><span id="276" class="l"><a href="#276">276: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="277" class="l"><a href="#277">277: </a>    }
</span><span id="278" class="l"><a href="#278">278: </a>
</span><span id="279" class="l"><a href="#279">279: </a>
</span><span id="280" class="l"><a href="#280">280: </a>    <span class="php-comment">/**
</span></span><span id="281" class="l"><a href="#281">281: </a><span class="php-comment">     * Add a condition to the data to be retrieved from the database. This
</span></span><span id="282" class="l"><a href="#282">282: </a><span class="php-comment">     * must be given as a function to be executed (usually anonymous) and
</span></span><span id="283" class="l"><a href="#283">283: </a><span class="php-comment">     * will be passed in a single argument, the `Query` object, to which
</span></span><span id="284" class="l"><a href="#284">284: </a><span class="php-comment">     * conditions can be added. Multiple calls to this method can be made.
</span></span><span id="285" class="l"><a href="#285">285: </a><span class="php-comment">     *
</span></span><span id="286" class="l"><a href="#286">286: </a><span class="php-comment">     * @param  callable $fn Where function.
</span></span><span id="287" class="l"><a href="#287">287: </a><span class="php-comment">     * @return self Current instance, used for chaining
</span></span><span id="288" class="l"><a href="#288">288: </a><span class="php-comment">     */</span>
</span><span id="289" class="l"><a href="#289">289: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> where ( <span class="php-var">$fn</span> )
</span><span id="290" class="l"><a href="#290">290: </a>    {
</span><span id="291" class="l"><a href="#291">291: </a>        <span class="php-var">$this</span>-&gt;_where[] = <span class="php-var">$fn</span>;
</span><span id="292" class="l"><a href="#292">292: </a>
</span><span id="293" class="l"><a href="#293">293: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="294" class="l"><a href="#294">294: </a>    }
</span><span id="295" class="l"><a href="#295">295: </a>
</span><span id="296" class="l"><a href="#296">296: </a>
</span><span id="297" class="l"><a href="#297">297: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="298" class="l"><a href="#298">298: </a><span class="php-comment">     * Internal methods
</span></span><span id="299" class="l"><a href="#299">299: </a><span class="php-comment">     */</span>
</span><span id="300" class="l"><a href="#300">300: </a>    
</span><span id="301" class="l"><a href="#301">301: </a>    <span class="php-comment">/**
</span></span><span id="302" class="l"><a href="#302">302: </a><span class="php-comment">     * Get database information data from the table
</span></span><span id="303" class="l"><a href="#303">303: </a><span class="php-comment">     *
</span></span><span id="304" class="l"><a href="#304">304: </a><span class="php-comment">     * @param \DataTables\Database $db Database
</span></span><span id="305" class="l"><a href="#305">305: </a><span class="php-comment">     * @param number[] [$ids=null] Limit to a specific set of ids
</span></span><span id="306" class="l"><a href="#306">306: </a><span class="php-comment">     * @return array Database information
</span></span><span id="307" class="l"><a href="#307">307: </a><span class="php-comment">     * @internal
</span></span><span id="308" class="l"><a href="#308">308: </a><span class="php-comment">     */</span>
</span><span id="309" class="l"><a href="#309">309: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> data ( <span class="php-var">$db</span>, <span class="php-var">$ids</span>=<span class="php-keyword1">null</span> )
</span><span id="310" class="l"><a href="#310">310: </a>    {
</span><span id="311" class="l"><a href="#311">311: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_dbTable ) {
</span><span id="312" class="l"><a href="#312">312: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="313" class="l"><a href="#313">313: </a>        }
</span><span id="314" class="l"><a href="#314">314: </a>
</span><span id="315" class="l"><a href="#315">315: </a>        <span class="php-comment">// Select the details requested, for the columns requested</span>
</span><span id="316" class="l"><a href="#316">316: </a>        <span class="php-var">$q</span> = <span class="php-var">$db</span>
</span><span id="317" class="l"><a href="#317">317: </a>            -&gt;query( <span class="php-quote">'select'</span> )
</span><span id="318" class="l"><a href="#318">318: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_dbTable )
</span><span id="319" class="l"><a href="#319">319: </a>            -&gt;get( <span class="php-var">$this</span>-&gt;_dbPKey );
</span><span id="320" class="l"><a href="#320">320: </a>
</span><span id="321" class="l"><a href="#321">321: </a>        <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_dbFields <span class="php-keyword1">as</span> <span class="php-var">$column</span> =&gt; <span class="php-var">$prop</span> ) {
</span><span id="322" class="l"><a href="#322">322: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$prop</span> !== self::DB_CONTENT ) {
</span><span id="323" class="l"><a href="#323">323: </a>                <span class="php-var">$q</span>-&gt;get( <span class="php-var">$column</span> );
</span><span id="324" class="l"><a href="#324">324: </a>            }
</span><span id="325" class="l"><a href="#325">325: </a>        }
</span><span id="326" class="l"><a href="#326">326: </a>
</span><span id="327" class="l"><a href="#327">327: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$ids</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="328" class="l"><a href="#328">328: </a>            <span class="php-var">$q</span>-&gt;where_in( <span class="php-var">$this</span>-&gt;_dbPKey, <span class="php-var">$ids</span> );
</span><span id="329" class="l"><a href="#329">329: </a>        }
</span><span id="330" class="l"><a href="#330">330: </a>
</span><span id="331" class="l"><a href="#331">331: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="332" class="l"><a href="#332">332: </a>            <span class="php-var">$q</span>-&gt;where( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>] );
</span><span id="333" class="l"><a href="#333">333: </a>        }
</span><span id="334" class="l"><a href="#334">334: </a>
</span><span id="335" class="l"><a href="#335">335: </a>        <span class="php-var">$result</span> = <span class="php-var">$q</span>-&gt;<span class="php-keyword2">exec</span>()-&gt;fetchAll();
</span><span id="336" class="l"><a href="#336">336: </a>        <span class="php-var">$out</span> = <span class="php-keyword1">array</span>();
</span><span id="337" class="l"><a href="#337">337: </a>
</span><span id="338" class="l"><a href="#338">338: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$result</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="339" class="l"><a href="#339">339: </a>            <span class="php-var">$out</span>[ <span class="php-var">$result</span>[<span class="php-var">$i</span>][ <span class="php-var">$this</span>-&gt;_dbPKey ] ] = <span class="php-var">$result</span>[<span class="php-var">$i</span>];
</span><span id="340" class="l"><a href="#340">340: </a>        }
</span><span id="341" class="l"><a href="#341">341: </a>
</span><span id="342" class="l"><a href="#342">342: </a>        <span class="php-keyword1">return</span> <span class="php-var">$out</span>;
</span><span id="343" class="l"><a href="#343">343: </a>    }
</span><span id="344" class="l"><a href="#344">344: </a>
</span><span id="345" class="l"><a href="#345">345: </a>
</span><span id="346" class="l"><a href="#346">346: </a>    <span class="php-comment">/**
</span></span><span id="347" class="l"><a href="#347">347: </a><span class="php-comment">     * Clean the database
</span></span><span id="348" class="l"><a href="#348">348: </a><span class="php-comment">     * @param  \DataTables\Editor $editor Calling Editor instance
</span></span><span id="349" class="l"><a href="#349">349: </a><span class="php-comment">     * @param  Field $field   Host field
</span></span><span id="350" class="l"><a href="#350">350: </a><span class="php-comment">     * @internal
</span></span><span id="351" class="l"><a href="#351">351: </a><span class="php-comment">     */</span>
</span><span id="352" class="l"><a href="#352">352: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> dbCleanExec ( <span class="php-var">$editor</span>, <span class="php-var">$field</span> )
</span><span id="353" class="l"><a href="#353">353: </a>    {
</span><span id="354" class="l"><a href="#354">354: </a>        <span class="php-comment">// Database and file system clean up BEFORE adding the new file to</span>
</span><span id="355" class="l"><a href="#355">355: </a>        <span class="php-comment">// the db, otherwise it will be removed immediately</span>
</span><span id="356" class="l"><a href="#356">356: </a>        <span class="php-var">$tables</span> = <span class="php-var">$editor</span>-&gt;table();
</span><span id="357" class="l"><a href="#357">357: </a>        <span class="php-var">$this</span>-&gt;_dbClean( <span class="php-var">$editor</span>-&gt;db(), <span class="php-var">$tables</span>[<span class="php-num">0</span>], <span class="php-var">$field</span>-&gt;dbField() );
</span><span id="358" class="l"><a href="#358">358: </a>    }
</span><span id="359" class="l"><a href="#359">359: </a>
</span><span id="360" class="l"><a href="#360">360: </a>
</span><span id="361" class="l"><a href="#361">361: </a>    <span class="php-comment">/**
</span></span><span id="362" class="l"><a href="#362">362: </a><span class="php-comment">     * Get the set error message
</span></span><span id="363" class="l"><a href="#363">363: </a><span class="php-comment">     * 
</span></span><span id="364" class="l"><a href="#364">364: </a><span class="php-comment">     * @return string Class error
</span></span><span id="365" class="l"><a href="#365">365: </a><span class="php-comment">     * @internal
</span></span><span id="366" class="l"><a href="#366">366: </a><span class="php-comment">     */</span>
</span><span id="367" class="l"><a href="#367">367: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> error ()
</span><span id="368" class="l"><a href="#368">368: </a>    {
</span><span id="369" class="l"><a href="#369">369: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_error;
</span><span id="370" class="l"><a href="#370">370: </a>    }
</span><span id="371" class="l"><a href="#371">371: </a>
</span><span id="372" class="l"><a href="#372">372: </a>
</span><span id="373" class="l"><a href="#373">373: </a>    <span class="php-comment">/**
</span></span><span id="374" class="l"><a href="#374">374: </a><span class="php-comment">     * Execute an upload
</span></span><span id="375" class="l"><a href="#375">375: </a><span class="php-comment">     *
</span></span><span id="376" class="l"><a href="#376">376: </a><span class="php-comment">     * @param  \DataTables\Editor $editor Calling Editor instance
</span></span><span id="377" class="l"><a href="#377">377: </a><span class="php-comment">     * @return int Primary key value
</span></span><span id="378" class="l"><a href="#378">378: </a><span class="php-comment">     * @internal
</span></span><span id="379" class="l"><a href="#379">379: </a><span class="php-comment">     */</span>
</span><span id="380" class="l"><a href="#380">380: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">exec</span> ( <span class="php-var">$editor</span> )
</span><span id="381" class="l"><a href="#381">381: </a>    {
</span><span id="382" class="l"><a href="#382">382: </a>        <span class="php-var">$id</span> = <span class="php-keyword1">null</span>;
</span><span id="383" class="l"><a href="#383">383: </a>        <span class="php-var">$upload</span> = <span class="php-var">$_FILES</span>[<span class="php-quote">'upload'</span>];
</span><span id="384" class="l"><a href="#384">384: </a>
</span><span id="385" class="l"><a href="#385">385: </a>        <span class="php-comment">// Validation - PHP standard validation</span>
</span><span id="386" class="l"><a href="#386">386: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$upload</span>[<span class="php-quote">'error'</span>] !== UPLOAD_ERR_OK ) {
</span><span id="387" class="l"><a href="#387">387: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$upload</span>[<span class="php-quote">'error'</span>] === UPLOAD_ERR_INI_SIZE ) {
</span><span id="388" class="l"><a href="#388">388: </a>                <span class="php-var">$this</span>-&gt;_error = <span class="php-quote">&quot;File exceeds maximum file upload size&quot;</span>; 
</span><span id="389" class="l"><a href="#389">389: </a>            }
</span><span id="390" class="l"><a href="#390">390: </a>            <span class="php-keyword1">else</span> {
</span><span id="391" class="l"><a href="#391">391: </a>                <span class="php-var">$this</span>-&gt;_error = <span class="php-quote">&quot;There was an error uploading the file (&quot;</span>.<span class="php-var">$upload</span>[<span class="php-quote">'error'</span>].<span class="php-quote">&quot;)&quot;</span>;
</span><span id="392" class="l"><a href="#392">392: </a>            }
</span><span id="393" class="l"><a href="#393">393: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="394" class="l"><a href="#394">394: </a>        }
</span><span id="395" class="l"><a href="#395">395: </a>
</span><span id="396" class="l"><a href="#396">396: </a>        <span class="php-comment">// Validation - acceptable file extensions</span>
</span><span id="397" class="l"><a href="#397">397: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_array</span>( <span class="php-var">$this</span>-&gt;_extns ) ) {
</span><span id="398" class="l"><a href="#398">398: </a>            <span class="php-var">$extn</span> = <span class="php-keyword2">pathinfo</span>(<span class="php-var">$upload</span>[<span class="php-quote">'name'</span>], PATHINFO_EXTENSION);
</span><span id="399" class="l"><a href="#399">399: </a>
</span><span id="400" class="l"><a href="#400">400: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">in_array</span>( <span class="php-keyword2">strtolower</span>(<span class="php-var">$extn</span>), <span class="php-keyword2">array_map</span>( <span class="php-quote">'strtolower'</span>, <span class="php-var">$this</span>-&gt;_extns ) ) === <span class="php-keyword1">false</span> ) {
</span><span id="401" class="l"><a href="#401">401: </a>                <span class="php-var">$this</span>-&gt;_error = <span class="php-var">$this</span>-&gt;_extnError;
</span><span id="402" class="l"><a href="#402">402: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="403" class="l"><a href="#403">403: </a>            }
</span><span id="404" class="l"><a href="#404">404: </a>        }
</span><span id="405" class="l"><a href="#405">405: </a>
</span><span id="406" class="l"><a href="#406">406: </a>        <span class="php-comment">// Validation - custom callback</span>
</span><span id="407" class="l"><a href="#407">407: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_validators) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="408" class="l"><a href="#408">408: </a>            <span class="php-var">$res</span> = <span class="php-var">$this</span>-&gt;_validators[<span class="php-var">$i</span>]( <span class="php-var">$upload</span> );
</span><span id="409" class="l"><a href="#409">409: </a>
</span><span id="410" class="l"><a href="#410">410: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_string</span>( <span class="php-var">$res</span> ) ) {
</span><span id="411" class="l"><a href="#411">411: </a>                <span class="php-var">$this</span>-&gt;_error = <span class="php-var">$res</span>;
</span><span id="412" class="l"><a href="#412">412: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="413" class="l"><a href="#413">413: </a>            }
</span><span id="414" class="l"><a href="#414">414: </a>        }
</span><span id="415" class="l"><a href="#415">415: </a>
</span><span id="416" class="l"><a href="#416">416: </a>        <span class="php-comment">// Database</span>
</span><span id="417" class="l"><a href="#417">417: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_dbTable ) {
</span><span id="418" class="l"><a href="#418">418: </a>            <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_dbFields <span class="php-keyword1">as</span> <span class="php-var">$column</span> =&gt; <span class="php-var">$prop</span> ) {
</span><span id="419" class="l"><a href="#419">419: </a>                <span class="php-comment">// We can't know what the path is, if it has moved into place</span>
</span><span id="420" class="l"><a href="#420">420: </a>                <span class="php-comment">// by an external function - throw an error if this does happen</span>
</span><span id="421" class="l"><a href="#421">421: </a>                <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_string</span>( <span class="php-var">$this</span>-&gt;_action ) &amp;&amp;
</span><span id="422" class="l"><a href="#422">422: </a>                     (<span class="php-var">$prop</span> === self::DB_SYSTEM_PATH || <span class="php-var">$prop</span> === self::DB_WEB_PATH )
</span><span id="423" class="l"><a href="#423">423: </a>                ) {
</span><span id="424" class="l"><a href="#424">424: </a>                    <span class="php-var">$this</span>-&gt;_error = <span class="php-quote">&quot;Cannot set path information in database &quot;</span>.
</span><span id="425" class="l"><a href="#425">425: </a>                        <span class="php-quote">&quot;if a custom method is used to save the file.&quot;</span>;
</span><span id="426" class="l"><a href="#426">426: </a>
</span><span id="427" class="l"><a href="#427">427: </a>                    <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="428" class="l"><a href="#428">428: </a>                }
</span><span id="429" class="l"><a href="#429">429: </a>            }
</span><span id="430" class="l"><a href="#430">430: </a>
</span><span id="431" class="l"><a href="#431">431: </a>            <span class="php-comment">// Commit to the database</span>
</span><span id="432" class="l"><a href="#432">432: </a>            <span class="php-var">$id</span> = <span class="php-var">$this</span>-&gt;_dbExec( <span class="php-var">$editor</span>-&gt;db() );
</span><span id="433" class="l"><a href="#433">433: </a>        }
</span><span id="434" class="l"><a href="#434">434: </a>
</span><span id="435" class="l"><a href="#435">435: </a>        <span class="php-comment">// Perform file system actions</span>
</span><span id="436" class="l"><a href="#436">436: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_actionExec( <span class="php-var">$id</span> );
</span><span id="437" class="l"><a href="#437">437: </a>    }
</span><span id="438" class="l"><a href="#438">438: </a>
</span><span id="439" class="l"><a href="#439">439: </a>
</span><span id="440" class="l"><a href="#440">440: </a>    <span class="php-comment">/**
</span></span><span id="441" class="l"><a href="#441">441: </a><span class="php-comment">     * Get the primary key column for the table
</span></span><span id="442" class="l"><a href="#442">442: </a><span class="php-comment">     *
</span></span><span id="443" class="l"><a href="#443">443: </a><span class="php-comment">     * @return string Primary key column name
</span></span><span id="444" class="l"><a href="#444">444: </a><span class="php-comment">     * @internal
</span></span><span id="445" class="l"><a href="#445">445: </a><span class="php-comment">     */</span>
</span><span id="446" class="l"><a href="#446">446: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> pkey ()
</span><span id="447" class="l"><a href="#447">447: </a>    {
</span><span id="448" class="l"><a href="#448">448: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_dbPKey;
</span><span id="449" class="l"><a href="#449">449: </a>    }
</span><span id="450" class="l"><a href="#450">450: </a>
</span><span id="451" class="l"><a href="#451">451: </a>
</span><span id="452" class="l"><a href="#452">452: </a>    <span class="php-comment">/**
</span></span><span id="453" class="l"><a href="#453">453: </a><span class="php-comment">     * Get the db table name
</span></span><span id="454" class="l"><a href="#454">454: </a><span class="php-comment">     *
</span></span><span id="455" class="l"><a href="#455">455: </a><span class="php-comment">     * @return string DB table name
</span></span><span id="456" class="l"><a href="#456">456: </a><span class="php-comment">     * @internal
</span></span><span id="457" class="l"><a href="#457">457: </a><span class="php-comment">     */</span>
</span><span id="458" class="l"><a href="#458">458: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> table ()
</span><span id="459" class="l"><a href="#459">459: </a>    {
</span><span id="460" class="l"><a href="#460">460: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_dbTable;
</span><span id="461" class="l"><a href="#461">461: </a>    }
</span><span id="462" class="l"><a href="#462">462: </a>
</span><span id="463" class="l"><a href="#463">463: </a>
</span><span id="464" class="l"><a href="#464">464: </a>
</span><span id="465" class="l"><a href="#465">465: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="466" class="l"><a href="#466">466: </a><span class="php-comment">     * Private methods
</span></span><span id="467" class="l"><a href="#467">467: </a><span class="php-comment">     */</span>
</span><span id="468" class="l"><a href="#468">468: </a>
</span><span id="469" class="l"><a href="#469">469: </a>    <span class="php-comment">/**
</span></span><span id="470" class="l"><a href="#470">470: </a><span class="php-comment">     * Execute the configured action for the upload
</span></span><span id="471" class="l"><a href="#471">471: </a><span class="php-comment">     *
</span></span><span id="472" class="l"><a href="#472">472: </a><span class="php-comment">     * @param  int $id Primary key value
</span></span><span id="473" class="l"><a href="#473">473: </a><span class="php-comment">     * @return int File identifier - typically the primary key
</span></span><span id="474" class="l"><a href="#474">474: </a><span class="php-comment">     */</span>
</span><span id="475" class="l"><a href="#475">475: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _actionExec ( <span class="php-var">$id</span> )
</span><span id="476" class="l"><a href="#476">476: </a>    {
</span><span id="477" class="l"><a href="#477">477: </a>        <span class="php-var">$upload</span> = <span class="php-var">$_FILES</span>[<span class="php-quote">'upload'</span>];
</span><span id="478" class="l"><a href="#478">478: </a>
</span><span id="479" class="l"><a href="#479">479: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_string</span>( <span class="php-var">$this</span>-&gt;_action ) ) {
</span><span id="480" class="l"><a href="#480">480: </a>            <span class="php-comment">// Custom function</span>
</span><span id="481" class="l"><a href="#481">481: </a>            <span class="php-var">$action</span> = <span class="php-var">$this</span>-&gt;_action;
</span><span id="482" class="l"><a href="#482">482: </a>            <span class="php-keyword1">return</span> <span class="php-var">$action</span>( <span class="php-var">$upload</span>, <span class="php-var">$id</span> );
</span><span id="483" class="l"><a href="#483">483: </a>        }
</span><span id="484" class="l"><a href="#484">484: </a>
</span><span id="485" class="l"><a href="#485">485: </a>        <span class="php-comment">// Default action - move the file to the location specified by the</span>
</span><span id="486" class="l"><a href="#486">486: </a>        <span class="php-comment">// action string</span>
</span><span id="487" class="l"><a href="#487">487: </a>        <span class="php-var">$to</span>  = <span class="php-var">$this</span>-&gt;_path( <span class="php-var">$upload</span>[<span class="php-quote">'name'</span>], <span class="php-var">$id</span> );
</span><span id="488" class="l"><a href="#488">488: </a>        <span class="php-var">$res</span> = <span class="php-keyword2">move_uploaded_file</span>( <span class="php-var">$upload</span>[<span class="php-quote">'tmp_name'</span>], <span class="php-var">$to</span> );
</span><span id="489" class="l"><a href="#489">489: </a>
</span><span id="490" class="l"><a href="#490">490: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> === <span class="php-keyword1">false</span> ) {
</span><span id="491" class="l"><a href="#491">491: </a>            <span class="php-var">$this</span>-&gt;_error = <span class="php-quote">&quot;An error occurred while moving the uploaded file.&quot;</span>;
</span><span id="492" class="l"><a href="#492">492: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="493" class="l"><a href="#493">493: </a>        }
</span><span id="494" class="l"><a href="#494">494: </a>
</span><span id="495" class="l"><a href="#495">495: </a>        <span class="php-keyword1">return</span> <span class="php-var">$id</span> !== <span class="php-keyword1">null</span> ?
</span><span id="496" class="l"><a href="#496">496: </a>            <span class="php-var">$id</span> :
</span><span id="497" class="l"><a href="#497">497: </a>            <span class="php-var">$to</span>;
</span><span id="498" class="l"><a href="#498">498: </a>    }
</span><span id="499" class="l"><a href="#499">499: </a>
</span><span id="500" class="l"><a href="#500">500: </a>    <span class="php-comment">/**
</span></span><span id="501" class="l"><a href="#501">501: </a><span class="php-comment">     * Perform the database clean by first getting the information about the
</span></span><span id="502" class="l"><a href="#502">502: </a><span class="php-comment">     * orphaned rows and then calling the callback function. The callback can
</span></span><span id="503" class="l"><a href="#503">503: </a><span class="php-comment">     * then instruct the rows to be removed through the return value.
</span></span><span id="504" class="l"><a href="#504">504: </a><span class="php-comment">     *
</span></span><span id="505" class="l"><a href="#505">505: </a><span class="php-comment">     * @param  \DataTables\Database $db Database instance
</span></span><span id="506" class="l"><a href="#506">506: </a><span class="php-comment">     * @param  string $editorTable Editor Editor instance table name
</span></span><span id="507" class="l"><a href="#507">507: </a><span class="php-comment">     * @param  string $fieldName   Host field's name
</span></span><span id="508" class="l"><a href="#508">508: </a><span class="php-comment">     */</span>
</span><span id="509" class="l"><a href="#509">509: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _dbClean ( <span class="php-var">$db</span>, <span class="php-var">$editorTable</span>, <span class="php-var">$fieldName</span> )
</span><span id="510" class="l"><a href="#510">510: </a>    {
</span><span id="511" class="l"><a href="#511">511: </a>        <span class="php-var">$callback</span> = <span class="php-var">$this</span>-&gt;_dbCleanCallback;
</span><span id="512" class="l"><a href="#512">512: </a>
</span><span id="513" class="l"><a href="#513">513: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_dbTable || ! <span class="php-var">$callback</span> ) {
</span><span id="514" class="l"><a href="#514">514: </a>            <span class="php-keyword1">return</span>;
</span><span id="515" class="l"><a href="#515">515: </a>        }
</span><span id="516" class="l"><a href="#516">516: </a>
</span><span id="517" class="l"><a href="#517">517: </a>        <span class="php-comment">// If there is a table / field that we should use to check if the value</span>
</span><span id="518" class="l"><a href="#518">518: </a>        <span class="php-comment">// is in use, then use that. Otherwise we'll try to use the information</span>
</span><span id="519" class="l"><a href="#519">519: </a>        <span class="php-comment">// from the Editor / Field instance.</span>
</span><span id="520" class="l"><a href="#520">520: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_dbCleanTableField ) {
</span><span id="521" class="l"><a href="#521">521: </a>            <span class="php-var">$fieldName</span> = <span class="php-var">$this</span>-&gt;_dbCleanTableField;
</span><span id="522" class="l"><a href="#522">522: </a>        }
</span><span id="523" class="l"><a href="#523">523: </a>
</span><span id="524" class="l"><a href="#524">524: </a>        <span class="php-var">$a</span> = <span class="php-keyword2">explode</span>(<span class="php-quote">'.'</span>, <span class="php-var">$fieldName</span>);
</span><span id="525" class="l"><a href="#525">525: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$a</span>) === <span class="php-num">1</span> ) {
</span><span id="526" class="l"><a href="#526">526: </a>            <span class="php-var">$table</span> = <span class="php-var">$editorTable</span>;
</span><span id="527" class="l"><a href="#527">527: </a>            <span class="php-var">$field</span> = <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="528" class="l"><a href="#528">528: </a>        }
</span><span id="529" class="l"><a href="#529">529: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$a</span>) === <span class="php-num">2</span> ) {
</span><span id="530" class="l"><a href="#530">530: </a>            <span class="php-var">$table</span> = <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="531" class="l"><a href="#531">531: </a>            <span class="php-var">$field</span> = <span class="php-var">$a</span>[<span class="php-num">1</span>];
</span><span id="532" class="l"><a href="#532">532: </a>        }
</span><span id="533" class="l"><a href="#533">533: </a>        <span class="php-keyword1">else</span> {
</span><span id="534" class="l"><a href="#534">534: </a>            <span class="php-var">$table</span> = <span class="php-var">$a</span>[<span class="php-num">1</span>];
</span><span id="535" class="l"><a href="#535">535: </a>            <span class="php-var">$field</span> = <span class="php-var">$a</span>[<span class="php-num">2</span>];
</span><span id="536" class="l"><a href="#536">536: </a>        }
</span><span id="537" class="l"><a href="#537">537: </a>
</span><span id="538" class="l"><a href="#538">538: </a>        <span class="php-comment">// Select the details requested, for the columns requested</span>
</span><span id="539" class="l"><a href="#539">539: </a>        <span class="php-var">$q</span> = <span class="php-var">$db</span>
</span><span id="540" class="l"><a href="#540">540: </a>            -&gt;query( <span class="php-quote">'select'</span> )
</span><span id="541" class="l"><a href="#541">541: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_dbTable )
</span><span id="542" class="l"><a href="#542">542: </a>            -&gt;get( <span class="php-var">$this</span>-&gt;_dbPKey );
</span><span id="543" class="l"><a href="#543">543: </a>
</span><span id="544" class="l"><a href="#544">544: </a>        <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_dbFields <span class="php-keyword1">as</span> <span class="php-var">$column</span> =&gt; <span class="php-var">$prop</span> ) {
</span><span id="545" class="l"><a href="#545">545: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$prop</span> !== self::DB_CONTENT ) {
</span><span id="546" class="l"><a href="#546">546: </a>                <span class="php-var">$q</span>-&gt;get( <span class="php-var">$column</span> );
</span><span id="547" class="l"><a href="#547">547: </a>            }
</span><span id="548" class="l"><a href="#548">548: </a>        }
</span><span id="549" class="l"><a href="#549">549: </a>
</span><span id="550" class="l"><a href="#550">550: </a>        <span class="php-var">$q</span>-&gt;where( <span class="php-var">$this</span>-&gt;_dbPKey, <span class="php-quote">'(SELECT '</span>.<span class="php-var">$field</span>.<span class="php-quote">' FROM '</span>.<span class="php-var">$table</span>.<span class="php-quote">'  WHERE '</span>.<span class="php-var">$field</span>.<span class="php-quote">' IS NOT NULL)'</span>, <span class="php-quote">'NOT IN'</span>, <span class="php-keyword1">false</span> );
</span><span id="551" class="l"><a href="#551">551: </a>
</span><span id="552" class="l"><a href="#552">552: </a>        <span class="php-var">$data</span> = <span class="php-var">$q</span>-&gt;<span class="php-keyword2">exec</span>()-&gt;fetchAll();
</span><span id="553" class="l"><a href="#553">553: </a>
</span><span id="554" class="l"><a href="#554">554: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$data</span> ) === <span class="php-num">0</span> ) {
</span><span id="555" class="l"><a href="#555">555: </a>            <span class="php-keyword1">return</span>;
</span><span id="556" class="l"><a href="#556">556: </a>        }
</span><span id="557" class="l"><a href="#557">557: </a>
</span><span id="558" class="l"><a href="#558">558: </a>        <span class="php-var">$result</span> = <span class="php-var">$callback</span>( <span class="php-var">$data</span> );
</span><span id="559" class="l"><a href="#559">559: </a>
</span><span id="560" class="l"><a href="#560">560: </a>        <span class="php-comment">// Delete the selected rows, iff the developer says to do so with the</span>
</span><span id="561" class="l"><a href="#561">561: </a>        <span class="php-comment">// returned value (i.e. acknowledge that the files have be removed from</span>
</span><span id="562" class="l"><a href="#562">562: </a>        <span class="php-comment">// the file system)</span>
</span><span id="563" class="l"><a href="#563">563: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$result</span> === <span class="php-keyword1">true</span> ) {
</span><span id="564" class="l"><a href="#564">564: </a>            <span class="php-var">$qDelete</span> = <span class="php-var">$db</span>
</span><span id="565" class="l"><a href="#565">565: </a>                -&gt;query( <span class="php-quote">'delete'</span> )
</span><span id="566" class="l"><a href="#566">566: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_dbTable );
</span><span id="567" class="l"><a href="#567">567: </a>
</span><span id="568" class="l"><a href="#568">568: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>( <span class="php-var">$data</span> ) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="569" class="l"><a href="#569">569: </a>                <span class="php-var">$qDelete</span>-&gt;or_where( <span class="php-var">$this</span>-&gt;_dbPKey, <span class="php-var">$data</span>[<span class="php-var">$i</span>][ <span class="php-var">$this</span>-&gt;_dbPKey ] );
</span><span id="570" class="l"><a href="#570">570: </a>            }
</span><span id="571" class="l"><a href="#571">571: </a>
</span><span id="572" class="l"><a href="#572">572: </a>            <span class="php-var">$qDelete</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="573" class="l"><a href="#573">573: </a>        }
</span><span id="574" class="l"><a href="#574">574: </a>    }
</span><span id="575" class="l"><a href="#575">575: </a>
</span><span id="576" class="l"><a href="#576">576: </a>    <span class="php-comment">/**
</span></span><span id="577" class="l"><a href="#577">577: </a><span class="php-comment">     * Add a record to the database for a newly uploaded file
</span></span><span id="578" class="l"><a href="#578">578: </a><span class="php-comment">     *
</span></span><span id="579" class="l"><a href="#579">579: </a><span class="php-comment">     * @param  \DataTables\Database $db Database instance
</span></span><span id="580" class="l"><a href="#580">580: </a><span class="php-comment">     * @return int Primary key value for the newly uploaded file
</span></span><span id="581" class="l"><a href="#581">581: </a><span class="php-comment">     */</span>
</span><span id="582" class="l"><a href="#582">582: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _dbExec ( <span class="php-var">$db</span> )
</span><span id="583" class="l"><a href="#583">583: </a>    {
</span><span id="584" class="l"><a href="#584">584: </a>        <span class="php-var">$upload</span> = <span class="php-var">$_FILES</span>[<span class="php-quote">'upload'</span>];
</span><span id="585" class="l"><a href="#585">585: </a>        <span class="php-var">$pathFields</span> = <span class="php-keyword1">array</span>();
</span><span id="586" class="l"><a href="#586">586: </a>
</span><span id="587" class="l"><a href="#587">587: </a>        <span class="php-comment">// Insert the details requested, for the columns requested</span>
</span><span id="588" class="l"><a href="#588">588: </a>        <span class="php-var">$q</span> = <span class="php-var">$db</span>
</span><span id="589" class="l"><a href="#589">589: </a>            -&gt;query( <span class="php-quote">'insert'</span> )
</span><span id="590" class="l"><a href="#590">590: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_dbTable )
</span><span id="591" class="l"><a href="#591">591: </a>            -&gt;pkey( <span class="php-var">$this</span>-&gt;_dbPKey );
</span><span id="592" class="l"><a href="#592">592: </a>
</span><span id="593" class="l"><a href="#593">593: </a>        <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_dbFields <span class="php-keyword1">as</span> <span class="php-var">$column</span> =&gt; <span class="php-var">$prop</span> ) {
</span><span id="594" class="l"><a href="#594">594: </a>            <span class="php-keyword1">switch</span> ( <span class="php-var">$prop</span> ) {
</span><span id="595" class="l"><a href="#595">595: </a>                <span class="php-keyword1">case</span> self::DB_READ_ONLY:
</span><span id="596" class="l"><a href="#596">596: </a>                    <span class="php-keyword1">break</span>;
</span><span id="597" class="l"><a href="#597">597: </a>
</span><span id="598" class="l"><a href="#598">598: </a>                <span class="php-keyword1">case</span> self::DB_CONTENT:
</span><span id="599" class="l"><a href="#599">599: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-keyword2">file_get_contents</span>(<span class="php-var">$upload</span>[<span class="php-quote">'tmp_name'</span>]) );
</span><span id="600" class="l"><a href="#600">600: </a>                    <span class="php-keyword1">break</span>;
</span><span id="601" class="l"><a href="#601">601: </a>
</span><span id="602" class="l"><a href="#602">602: </a>                <span class="php-keyword1">case</span> self::DB_CONTENT_TYPE:
</span><span id="603" class="l"><a href="#603">603: </a>                <span class="php-keyword1">case</span> self::DB_MIME_TYPE:
</span><span id="604" class="l"><a href="#604">604: </a>                    <span class="php-var">$finfo</span> = <span class="php-keyword2">finfo_open</span>(FILEINFO_MIME);
</span><span id="605" class="l"><a href="#605">605: </a>                    <span class="php-var">$mime</span> = <span class="php-keyword2">finfo_file</span>(<span class="php-var">$finfo</span>, <span class="php-var">$upload</span>[<span class="php-quote">'tmp_name'</span>]);
</span><span id="606" class="l"><a href="#606">606: </a>                    <span class="php-keyword2">finfo_close</span>(<span class="php-var">$finfo</span>);
</span><span id="607" class="l"><a href="#607">607: </a>
</span><span id="608" class="l"><a href="#608">608: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$mime</span> );
</span><span id="609" class="l"><a href="#609">609: </a>                    <span class="php-keyword1">break</span>;
</span><span id="610" class="l"><a href="#610">610: </a>
</span><span id="611" class="l"><a href="#611">611: </a>                <span class="php-keyword1">case</span> self::DB_EXTN:
</span><span id="612" class="l"><a href="#612">612: </a>                    <span class="php-var">$extn</span> = <span class="php-keyword2">pathinfo</span>(<span class="php-var">$upload</span>[<span class="php-quote">'name'</span>], PATHINFO_EXTENSION);
</span><span id="613" class="l"><a href="#613">613: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$extn</span> );
</span><span id="614" class="l"><a href="#614">614: </a>                    <span class="php-keyword1">break</span>;
</span><span id="615" class="l"><a href="#615">615: </a>
</span><span id="616" class="l"><a href="#616">616: </a>                <span class="php-keyword1">case</span> self::DB_FILE_NAME:
</span><span id="617" class="l"><a href="#617">617: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$upload</span>[<span class="php-quote">'name'</span>] );
</span><span id="618" class="l"><a href="#618">618: </a>                    <span class="php-keyword1">break</span>;
</span><span id="619" class="l"><a href="#619">619: </a>
</span><span id="620" class="l"><a href="#620">620: </a>                <span class="php-keyword1">case</span> self::DB_FILE_SIZE:
</span><span id="621" class="l"><a href="#621">621: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$upload</span>[<span class="php-quote">'size'</span>] );
</span><span id="622" class="l"><a href="#622">622: </a>                    <span class="php-keyword1">break</span>;
</span><span id="623" class="l"><a href="#623">623: </a>
</span><span id="624" class="l"><a href="#624">624: </a>                <span class="php-keyword1">case</span> self::DB_SYSTEM_PATH:
</span><span id="625" class="l"><a href="#625">625: </a>                    <span class="php-var">$pathFields</span>[ <span class="php-var">$column</span> ] = self::DB_SYSTEM_PATH;
</span><span id="626" class="l"><a href="#626">626: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-quote">'-'</span> ); <span class="php-comment">// Use a temporary value to avoid cases </span>
</span><span id="627" class="l"><a href="#627">627: </a>                    <span class="php-keyword1">break</span>;                   <span class="php-comment">// where the db will reject empty values</span>
</span><span id="628" class="l"><a href="#628">628: </a>
</span><span id="629" class="l"><a href="#629">629: </a>                <span class="php-keyword1">case</span> self::DB_WEB_PATH:
</span><span id="630" class="l"><a href="#630">630: </a>                    <span class="php-var">$pathFields</span>[ <span class="php-var">$column</span> ] = self::DB_WEB_PATH;
</span><span id="631" class="l"><a href="#631">631: </a>                    <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-quote">'-'</span> ); <span class="php-comment">// Use a temporary value (as above)</span>
</span><span id="632" class="l"><a href="#632">632: </a>                    <span class="php-keyword1">break</span>;
</span><span id="633" class="l"><a href="#633">633: </a>
</span><span id="634" class="l"><a href="#634">634: </a>                <span class="php-keyword1">default</span>:
</span><span id="635" class="l"><a href="#635">635: </a>                    <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>(<span class="php-var">$prop</span>) &amp;&amp; <span class="php-keyword2">is_object</span>(<span class="php-var">$prop</span>) ) { <span class="php-comment">// is a closure</span>
</span><span id="636" class="l"><a href="#636">636: </a>                        <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$prop</span>( <span class="php-var">$db</span>, <span class="php-var">$upload</span> ) );
</span><span id="637" class="l"><a href="#637">637: </a>                    }
</span><span id="638" class="l"><a href="#638">638: </a>                    <span class="php-keyword1">else</span> {
</span><span id="639" class="l"><a href="#639">639: </a>                        <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$prop</span> );
</span><span id="640" class="l"><a href="#640">640: </a>                    }
</span><span id="641" class="l"><a href="#641">641: </a>
</span><span id="642" class="l"><a href="#642">642: </a>                    <span class="php-keyword1">break</span>;
</span><span id="643" class="l"><a href="#643">643: </a>            }
</span><span id="644" class="l"><a href="#644">644: </a>        }
</span><span id="645" class="l"><a href="#645">645: </a>
</span><span id="646" class="l"><a href="#646">646: </a>        <span class="php-var">$res</span> = <span class="php-var">$q</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="647" class="l"><a href="#647">647: </a>        <span class="php-var">$id</span>  = <span class="php-var">$res</span>-&gt;insertId();
</span><span id="648" class="l"><a href="#648">648: </a>
</span><span id="649" class="l"><a href="#649">649: </a>        <span class="php-comment">// Update the newly inserted row with the path information. We have to</span>
</span><span id="650" class="l"><a href="#650">650: </a>        <span class="php-comment">// use a second statement here as we don't know in advance what the</span>
</span><span id="651" class="l"><a href="#651">651: </a>        <span class="php-comment">// database schema is and don't want to prescribe that certain triggers</span>
</span><span id="652" class="l"><a href="#652">652: </a>        <span class="php-comment">// etc be created. It makes it a bit less efficient but much more</span>
</span><span id="653" class="l"><a href="#653">653: </a>        <span class="php-comment">// compatible</span>
</span><span id="654" class="l"><a href="#654">654: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$pathFields</span> ) ) {
</span><span id="655" class="l"><a href="#655">655: </a>            <span class="php-comment">// For this to operate the action must be a string, which is</span>
</span><span id="656" class="l"><a href="#656">656: </a>            <span class="php-comment">// validated in the `exec` method</span>
</span><span id="657" class="l"><a href="#657">657: </a>            <span class="php-var">$path</span> = <span class="php-var">$this</span>-&gt;_path( <span class="php-var">$upload</span>[<span class="php-quote">'name'</span>], <span class="php-var">$id</span> );
</span><span id="658" class="l"><a href="#658">658: </a>            <span class="php-var">$webPath</span> = <span class="php-keyword2">str_replace</span>(<span class="php-var">$_SERVER</span>[<span class="php-quote">'DOCUMENT_ROOT'</span>], <span class="php-quote">''</span>, <span class="php-var">$path</span>);
</span><span id="659" class="l"><a href="#659">659: </a>            <span class="php-var">$q</span> = <span class="php-var">$db</span>
</span><span id="660" class="l"><a href="#660">660: </a>                -&gt;query( <span class="php-quote">'update'</span> )
</span><span id="661" class="l"><a href="#661">661: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_dbTable )
</span><span id="662" class="l"><a href="#662">662: </a>                -&gt;where( <span class="php-var">$this</span>-&gt;_dbPKey, <span class="php-var">$id</span> );
</span><span id="663" class="l"><a href="#663">663: </a>
</span><span id="664" class="l"><a href="#664">664: </a>            <span class="php-keyword1">foreach</span> ( <span class="php-var">$pathFields</span> <span class="php-keyword1">as</span> <span class="php-var">$column</span> =&gt; <span class="php-var">$type</span> ) {
</span><span id="665" class="l"><a href="#665">665: </a>                <span class="php-var">$q</span>-&gt;set( <span class="php-var">$column</span>, <span class="php-var">$type</span> === self::DB_WEB_PATH ? <span class="php-var">$webPath</span> : <span class="php-var">$path</span> );
</span><span id="666" class="l"><a href="#666">666: </a>            }
</span><span id="667" class="l"><a href="#667">667: </a>
</span><span id="668" class="l"><a href="#668">668: </a>            <span class="php-var">$q</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="669" class="l"><a href="#669">669: </a>        }
</span><span id="670" class="l"><a href="#670">670: </a>
</span><span id="671" class="l"><a href="#671">671: </a>        <span class="php-keyword1">return</span> <span class="php-var">$id</span>;
</span><span id="672" class="l"><a href="#672">672: </a>    }
</span><span id="673" class="l"><a href="#673">673: </a>
</span><span id="674" class="l"><a href="#674">674: </a>
</span><span id="675" class="l"><a href="#675">675: </a>    <span class="php-comment">/**
</span></span><span id="676" class="l"><a href="#676">676: </a><span class="php-comment">     * Apply macros to a user specified path
</span></span><span id="677" class="l"><a href="#677">677: </a><span class="php-comment">     *
</span></span><span id="678" class="l"><a href="#678">678: </a><span class="php-comment">     * @param  string $name File path
</span></span><span id="679" class="l"><a href="#679">679: </a><span class="php-comment">     * @param  int $id Primary key value for the file
</span></span><span id="680" class="l"><a href="#680">680: </a><span class="php-comment">     * @return string Resolved path
</span></span><span id="681" class="l"><a href="#681">681: </a><span class="php-comment">     */</span>
</span><span id="682" class="l"><a href="#682">682: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _path ( <span class="php-var">$name</span>, <span class="php-var">$id</span> )
</span><span id="683" class="l"><a href="#683">683: </a>    {
</span><span id="684" class="l"><a href="#684">684: </a>        <span class="php-var">$extn</span> = <span class="php-keyword2">pathinfo</span>( <span class="php-var">$name</span>, PATHINFO_EXTENSION );
</span><span id="685" class="l"><a href="#685">685: </a>
</span><span id="686" class="l"><a href="#686">686: </a>        <span class="php-var">$to</span> = <span class="php-var">$this</span>-&gt;_action;
</span><span id="687" class="l"><a href="#687">687: </a>        <span class="php-var">$to</span> = <span class="php-keyword2">str_replace</span>( <span class="php-quote">&quot;__NAME__&quot;</span>, <span class="php-var">$name</span>, <span class="php-var">$to</span>   );
</span><span id="688" class="l"><a href="#688">688: </a>        <span class="php-var">$to</span> = <span class="php-keyword2">str_replace</span>( <span class="php-quote">&quot;__ID__&quot;</span>,   <span class="php-var">$id</span>,   <span class="php-var">$to</span>   );
</span><span id="689" class="l"><a href="#689">689: </a>        <span class="php-var">$to</span> = <span class="php-keyword2">str_replace</span>( <span class="php-quote">&quot;__EXTN__&quot;</span>, <span class="php-var">$extn</span>, <span class="php-var">$to</span> );
</span><span id="690" class="l"><a href="#690">690: </a>
</span><span id="691" class="l"><a href="#691">691: </a>        <span class="php-keyword1">return</span> <span class="php-var">$to</span>;
</span><span id="692" class="l"><a href="#692">692: </a>    }
</span><span id="693" class="l"><a href="#693">693: </a>}
</span><span id="694" class="l"><a href="#694">694: </a>
</span><span id="695" class="l"><a href="#695">695: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
