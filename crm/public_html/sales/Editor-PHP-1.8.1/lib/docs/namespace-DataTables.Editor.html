<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Namespace DataTables\Editor | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li class="active">
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Editor.Field.html">Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">ValidateOptions</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li class="active">
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="namespace">
	<h1>Namespace <a href="namespace-DataTables.html">DataTables</a>\Editor</h1>



<table class="summary" id="classes">
<caption>Classes summary</caption>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Field.html">Field</a></td>
	<td>Field definitions for the DataTables Editor.</td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Format.html">Format</a></td>
	<td>Formatter methods for the DataTables Editor</td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Join.html">Join</a></td>
	<td>Join table class for DataTables Editor.</td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.MJoin.html">MJoin</a></td>
	<td><p>The <code>Mjoin</code> class extends the <code>Join</code> class with the join data type set to
'array', whereas the <code>Join</code> default is <code>object</code> which has been rendered
obsolete by the <code>Editor-&gt;leftJoin()</code> method. The API API is otherwise
identical.</p></td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Options.html">Options</a></td>
	<td><p>The Options class provides a convenient method of specifying where Editor
should get the list of options for a <code>select</code>, <code>radio</code> or <code>checkbox</code> field.
This is normally from a table that is <em>left joined</em> to the main table being
edited, and a list of the values available from the joined table is shown to
the end user to let them select from.</p></td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Upload.html">Upload</a></td>
	<td><p>Upload class for Editor. This class provides the ability to easily specify
file upload information, specifically how the file should be recorded on
the server (database and file system).</p></td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.Validate.html">Validate</a></td>
	<td>Validation methods for DataTables Editor fields.</td>
</tr>
<tr>
	<td class="name"><a href="class-DataTables.Editor.ValidateOptions.html">ValidateOptions</a></td>
	<td>Common validation options that can be specified for all validation methods.</td>
</tr>
</table>





</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
