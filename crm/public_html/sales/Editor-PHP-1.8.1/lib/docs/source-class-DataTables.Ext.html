<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Ext.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a>
</span><span id="17" class="l"><a href="#17"> 17: </a><span class="php-comment">/**
</span></span><span id="18" class="l"><a href="#18"> 18: </a><span class="php-comment"> * Base class for DataTables classes.
</span></span><span id="19" class="l"><a href="#19"> 19: </a><span class="php-comment"> */</span>
</span><span id="20" class="l"><a href="#20"> 20: </a><span class="php-keyword1">class</span> Ext {
</span><span id="21" class="l"><a href="#21"> 21: </a>    <span class="php-comment">/**
</span></span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-comment">     * Static method to instantiate a new instance of a class.
</span></span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment">     *
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment">     * A factory method that will create a new instance of the class
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment">     * that has extended 'Ext'. This allows classes to be instantiated
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment">     * and then chained - which otherwise isn't available until PHP 5.4.
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment">     * If using PHP 5.4 or later, simply create a 'new' instance of the
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment">     * target class and chain methods as normal.
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment">     *  @return \DataTables\Editor|\DataTables\Editor\Field|\DataTables\Editor\Join|\DataTables\Editor\Upload Instantiated class
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment">     *  @static
</span></span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-comment">     */</span>
</span><span id="32" class="l"><a href="#32"> 32: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> instantiate ()
</span><span id="33" class="l"><a href="#33"> 33: </a>    {
</span><span id="34" class="l"><a href="#34"> 34: </a>        <span class="php-var">$rc</span> = <span class="php-keyword1">new</span> \ReflectionClass( <span class="php-keyword2">get_called_class</span>() );
</span><span id="35" class="l"><a href="#35"> 35: </a>        <span class="php-var">$args</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="36" class="l"><a href="#36"> 36: </a>
</span><span id="37" class="l"><a href="#37"> 37: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">count</span>( <span class="php-var">$args</span> ) === <span class="php-num">0</span> ?
</span><span id="38" class="l"><a href="#38"> 38: </a>            <span class="php-var">$rc</span>-&gt;newInstance() :
</span><span id="39" class="l"><a href="#39"> 39: </a>            <span class="php-var">$rc</span>-&gt;newInstanceArgs( <span class="php-var">$args</span> );
</span><span id="40" class="l"><a href="#40"> 40: </a>    }
</span><span id="41" class="l"><a href="#41"> 41: </a>
</span><span id="42" class="l"><a href="#42"> 42: </a>    <span class="php-comment">/**
</span></span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment">     * Static method to instantiate a new instance of a class (shorthand of 
</span></span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-comment">     * 'instantiate').
</span></span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-comment">     *
</span></span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-comment">     * This method performs exactly the same actions as the 'instantiate'
</span></span><span id="47" class="l"><a href="#47"> 47: </a><span class="php-comment">     * static method, but is simply shorter and easier to type!
</span></span><span id="48" class="l"><a href="#48"> 48: </a><span class="php-comment">     *  @return \DataTables\Editor|\DataTables\Editor\Field|\DataTables\Editor\Join|\DataTables\Editor\Upload class
</span></span><span id="49" class="l"><a href="#49"> 49: </a><span class="php-comment">     *  @static
</span></span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-comment">     */</span>
</span><span id="51" class="l"><a href="#51"> 51: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> inst ()
</span><span id="52" class="l"><a href="#52"> 52: </a>    {
</span><span id="53" class="l"><a href="#53"> 53: </a>        <span class="php-var">$rc</span> = <span class="php-keyword1">new</span> \ReflectionClass( <span class="php-keyword2">get_called_class</span>() );
</span><span id="54" class="l"><a href="#54"> 54: </a>        <span class="php-var">$args</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="55" class="l"><a href="#55"> 55: </a>
</span><span id="56" class="l"><a href="#56"> 56: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">count</span>( <span class="php-var">$args</span> ) === <span class="php-num">0</span> ?
</span><span id="57" class="l"><a href="#57"> 57: </a>            <span class="php-var">$rc</span>-&gt;newInstance() :
</span><span id="58" class="l"><a href="#58"> 58: </a>            <span class="php-var">$rc</span>-&gt;newInstanceArgs( <span class="php-var">$args</span> );
</span><span id="59" class="l"><a href="#59"> 59: </a>    }
</span><span id="60" class="l"><a href="#60"> 60: </a>
</span><span id="61" class="l"><a href="#61"> 61: </a>    <span class="php-comment">/**
</span></span><span id="62" class="l"><a href="#62"> 62: </a><span class="php-comment">     * Common getter / setter function for DataTables classes.
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment">     *
</span></span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-comment">     * This getter / setter method makes building getter / setting methods
</span></span><span id="65" class="l"><a href="#65"> 65: </a><span class="php-comment">     * easier, by abstracting everything to a single function call.
</span></span><span id="66" class="l"><a href="#66"> 66: </a><span class="php-comment">     *  @param mixed &amp;$prop The property to set
</span></span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-comment">     *  @param mixed $val The value to set - if given as null, then we assume
</span></span><span id="68" class="l"><a href="#68"> 68: </a><span class="php-comment">     *    that the function is being used as a getter.
</span></span><span id="69" class="l"><a href="#69"> 69: </a><span class="php-comment">     *  @param boolean $array Treat the target property as an array or not
</span></span><span id="70" class="l"><a href="#70"> 70: </a><span class="php-comment">     *    (default false). If used as an array, then values passed in are added
</span></span><span id="71" class="l"><a href="#71"> 71: </a><span class="php-comment">     *    to the $prop array.
</span></span><span id="72" class="l"><a href="#72"> 72: </a><span class="php-comment">     *  @return self|mixed Class instance if setting (allowing chaining), or
</span></span><span id="73" class="l"><a href="#73"> 73: </a><span class="php-comment">     *    the value requested if getting.
</span></span><span id="74" class="l"><a href="#74"> 74: </a><span class="php-comment">     */</span>
</span><span id="75" class="l"><a href="#75"> 75: </a>    <span class="php-keyword1">protected</span> <span class="php-keyword1">function</span> _getSet( &amp;<span class="php-var">$prop</span>, <span class="php-var">$val</span>, <span class="php-var">$array</span>=<span class="php-keyword1">false</span> )
</span><span id="76" class="l"><a href="#76"> 76: </a>    {
</span><span id="77" class="l"><a href="#77"> 77: </a>        <span class="php-comment">// Get</span>
</span><span id="78" class="l"><a href="#78"> 78: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$val</span> === <span class="php-keyword1">null</span> ) {
</span><span id="79" class="l"><a href="#79"> 79: </a>            <span class="php-keyword1">return</span> <span class="php-var">$prop</span>;
</span><span id="80" class="l"><a href="#80"> 80: </a>        }
</span><span id="81" class="l"><a href="#81"> 81: </a>
</span><span id="82" class="l"><a href="#82"> 82: </a>        <span class="php-comment">// Set</span>
</span><span id="83" class="l"><a href="#83"> 83: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$array</span> ) {
</span><span id="84" class="l"><a href="#84"> 84: </a>            <span class="php-comment">// Property is an array, merge or add to array</span>
</span><span id="85" class="l"><a href="#85"> 85: </a>            <span class="php-keyword2">is_array</span>( <span class="php-var">$val</span> ) ?
</span><span id="86" class="l"><a href="#86"> 86: </a>                <span class="php-var">$prop</span> = <span class="php-keyword2">array_merge</span>( <span class="php-var">$prop</span>, <span class="php-var">$val</span> ) :
</span><span id="87" class="l"><a href="#87"> 87: </a>                <span class="php-var">$prop</span>[] = <span class="php-var">$val</span>;
</span><span id="88" class="l"><a href="#88"> 88: </a>        }
</span><span id="89" class="l"><a href="#89"> 89: </a>        <span class="php-keyword1">else</span> {
</span><span id="90" class="l"><a href="#90"> 90: </a>            <span class="php-comment">// Property is just a value</span>
</span><span id="91" class="l"><a href="#91"> 91: </a>            <span class="php-var">$prop</span> = <span class="php-var">$val</span>;
</span><span id="92" class="l"><a href="#92"> 92: </a>        }
</span><span id="93" class="l"><a href="#93"> 93: </a>
</span><span id="94" class="l"><a href="#94"> 94: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="95" class="l"><a href="#95"> 95: </a>    }
</span><span id="96" class="l"><a href="#96"> 96: </a>
</span><span id="97" class="l"><a href="#97"> 97: </a>    <span class="php-comment">/**
</span></span><span id="98" class="l"><a href="#98"> 98: </a><span class="php-comment">     * Determine if a property is available in a data set (allowing `null` to be
</span></span><span id="99" class="l"><a href="#99"> 99: </a><span class="php-comment">     * a valid value)
</span></span><span id="100" class="l"><a href="#100">100: </a><span class="php-comment">     * @param  string $name  Javascript dotted object name to write to
</span></span><span id="101" class="l"><a href="#101">101: </a><span class="php-comment">     * @param  array  $data  Data source array to read from
</span></span><span id="102" class="l"><a href="#102">102: </a><span class="php-comment">     * @return boolean       true if present, false otherwise
</span></span><span id="103" class="l"><a href="#103">103: </a><span class="php-comment">     * @private
</span></span><span id="104" class="l"><a href="#104">104: </a><span class="php-comment">     */</span>
</span><span id="105" class="l"><a href="#105">105: </a>    <span class="php-keyword1">protected</span> <span class="php-keyword1">function</span> _propExists ( <span class="php-var">$name</span>, <span class="php-var">$data</span> )
</span><span id="106" class="l"><a href="#106">106: </a>    {
</span><span id="107" class="l"><a href="#107">107: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$name</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> ) {
</span><span id="108" class="l"><a href="#108">108: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$data</span>[ <span class="php-var">$name</span> ] );
</span><span id="109" class="l"><a href="#109">109: </a>        }
</span><span id="110" class="l"><a href="#110">110: </a>
</span><span id="111" class="l"><a href="#111">111: </a>        <span class="php-var">$names</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$name</span> );
</span><span id="112" class="l"><a href="#112">112: </a>        <span class="php-var">$inner</span> = <span class="php-var">$data</span>;
</span><span id="113" class="l"><a href="#113">113: </a>
</span><span id="114" class="l"><a href="#114">114: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="115" class="l"><a href="#115">115: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ] ) ) {
</span><span id="116" class="l"><a href="#116">116: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="117" class="l"><a href="#117">117: </a>            }
</span><span id="118" class="l"><a href="#118">118: </a>
</span><span id="119" class="l"><a href="#119">119: </a>            <span class="php-var">$inner</span> = <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ];
</span><span id="120" class="l"><a href="#120">120: </a>        }
</span><span id="121" class="l"><a href="#121">121: </a>
</span><span id="122" class="l"><a href="#122">122: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>] ) ) {
</span><span id="123" class="l"><a href="#123">123: </a>            <span class="php-var">$idx</span> = <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>];
</span><span id="124" class="l"><a href="#124">124: </a>
</span><span id="125" class="l"><a href="#125">125: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$idx</span> ] );
</span><span id="126" class="l"><a href="#126">126: </a>        }
</span><span id="127" class="l"><a href="#127">127: </a>
</span><span id="128" class="l"><a href="#128">128: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="129" class="l"><a href="#129">129: </a>    }
</span><span id="130" class="l"><a href="#130">130: </a>
</span><span id="131" class="l"><a href="#131">131: </a>    <span class="php-comment">/**
</span></span><span id="132" class="l"><a href="#132">132: </a><span class="php-comment">     * Read a value from a data structure, using Javascript dotted object
</span></span><span id="133" class="l"><a href="#133">133: </a><span class="php-comment">     * notation. This is the inverse of the `_writeProp` method and provides
</span></span><span id="134" class="l"><a href="#134">134: </a><span class="php-comment">     * the same support, matching DataTables' ability to read nested JSON
</span></span><span id="135" class="l"><a href="#135">135: </a><span class="php-comment">     * data objects.
</span></span><span id="136" class="l"><a href="#136">136: </a><span class="php-comment">     *
</span></span><span id="137" class="l"><a href="#137">137: </a><span class="php-comment">     * @param  string $name  Javascript dotted object name to write to
</span></span><span id="138" class="l"><a href="#138">138: </a><span class="php-comment">     * @param  array  $data  Data source array to read from
</span></span><span id="139" class="l"><a href="#139">139: </a><span class="php-comment">     * @return mixed         The read value, or null if no value found.
</span></span><span id="140" class="l"><a href="#140">140: </a><span class="php-comment">     * @private
</span></span><span id="141" class="l"><a href="#141">141: </a><span class="php-comment">     */</span>
</span><span id="142" class="l"><a href="#142">142: </a>    <span class="php-keyword1">protected</span> <span class="php-keyword1">function</span> _readProp ( <span class="php-var">$name</span>, <span class="php-var">$data</span> )
</span><span id="143" class="l"><a href="#143">143: </a>    {
</span><span id="144" class="l"><a href="#144">144: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$name</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> ) {
</span><span id="145" class="l"><a href="#145">145: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$data</span>[ <span class="php-var">$name</span> ] ) ?
</span><span id="146" class="l"><a href="#146">146: </a>                <span class="php-var">$data</span>[ <span class="php-var">$name</span> ] :
</span><span id="147" class="l"><a href="#147">147: </a>                <span class="php-keyword1">null</span>;
</span><span id="148" class="l"><a href="#148">148: </a>        }
</span><span id="149" class="l"><a href="#149">149: </a>
</span><span id="150" class="l"><a href="#150">150: </a>        <span class="php-var">$names</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$name</span> );
</span><span id="151" class="l"><a href="#151">151: </a>        <span class="php-var">$inner</span> = <span class="php-var">$data</span>;
</span><span id="152" class="l"><a href="#152">152: </a>
</span><span id="153" class="l"><a href="#153">153: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="154" class="l"><a href="#154">154: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ] ) ) {
</span><span id="155" class="l"><a href="#155">155: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="156" class="l"><a href="#156">156: </a>            }
</span><span id="157" class="l"><a href="#157">157: </a>
</span><span id="158" class="l"><a href="#158">158: </a>            <span class="php-var">$inner</span> = <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ];
</span><span id="159" class="l"><a href="#159">159: </a>        }
</span><span id="160" class="l"><a href="#160">160: </a>
</span><span id="161" class="l"><a href="#161">161: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>] ) ) {
</span><span id="162" class="l"><a href="#162">162: </a>            <span class="php-var">$idx</span> = <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>];
</span><span id="163" class="l"><a href="#163">163: </a>
</span><span id="164" class="l"><a href="#164">164: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$idx</span> ] ) ?
</span><span id="165" class="l"><a href="#165">165: </a>                <span class="php-var">$inner</span>[ <span class="php-var">$idx</span> ] :
</span><span id="166" class="l"><a href="#166">166: </a>                <span class="php-keyword1">null</span>;
</span><span id="167" class="l"><a href="#167">167: </a>        }
</span><span id="168" class="l"><a href="#168">168: </a>
</span><span id="169" class="l"><a href="#169">169: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="170" class="l"><a href="#170">170: </a>    }
</span><span id="171" class="l"><a href="#171">171: </a>
</span><span id="172" class="l"><a href="#172">172: </a>    <span class="php-comment">/**
</span></span><span id="173" class="l"><a href="#173">173: </a><span class="php-comment">     * Write the field's value to an array structure, using Javascript dotted
</span></span><span id="174" class="l"><a href="#174">174: </a><span class="php-comment">     * object notation to indicate JSON data structure. For example `name.first`
</span></span><span id="175" class="l"><a href="#175">175: </a><span class="php-comment">     * gives the data structure: `name: { first: ... }`. This matches DataTables
</span></span><span id="176" class="l"><a href="#176">176: </a><span class="php-comment">     * own ability to do this on the client-side, although this doesn't
</span></span><span id="177" class="l"><a href="#177">177: </a><span class="php-comment">     * implement implement quite such a complex structure (no array / function
</span></span><span id="178" class="l"><a href="#178">178: </a><span class="php-comment">     * support).
</span></span><span id="179" class="l"><a href="#179">179: </a><span class="php-comment">     *
</span></span><span id="180" class="l"><a href="#180">180: </a><span class="php-comment">     * @param  array  &amp;$out   Array to write the data to
</span></span><span id="181" class="l"><a href="#181">181: </a><span class="php-comment">     * @param  string  $name  Javascript dotted object name to write to
</span></span><span id="182" class="l"><a href="#182">182: </a><span class="php-comment">     * @param  mixed   $value Value to write
</span></span><span id="183" class="l"><a href="#183">183: </a><span class="php-comment">     * @throws \Exception Information about duplicate properties
</span></span><span id="184" class="l"><a href="#184">184: </a><span class="php-comment">     * @private
</span></span><span id="185" class="l"><a href="#185">185: </a><span class="php-comment">     */</span>
</span><span id="186" class="l"><a href="#186">186: </a>    <span class="php-keyword1">protected</span> <span class="php-keyword1">function</span> _writeProp( &amp;<span class="php-var">$out</span>, <span class="php-var">$name</span>, <span class="php-var">$value</span> )
</span><span id="187" class="l"><a href="#187">187: </a>    {
</span><span id="188" class="l"><a href="#188">188: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$name</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> ) {
</span><span id="189" class="l"><a href="#189">189: </a>            <span class="php-var">$out</span>[ <span class="php-var">$name</span> ] = <span class="php-var">$value</span>;
</span><span id="190" class="l"><a href="#190">190: </a>            <span class="php-keyword1">return</span>;
</span><span id="191" class="l"><a href="#191">191: </a>        }
</span><span id="192" class="l"><a href="#192">192: </a>
</span><span id="193" class="l"><a href="#193">193: </a>        <span class="php-var">$names</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$name</span> );
</span><span id="194" class="l"><a href="#194">194: </a>        <span class="php-var">$inner</span> = &amp;<span class="php-var">$out</span>;
</span><span id="195" class="l"><a href="#195">195: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="196" class="l"><a href="#196">196: </a>            <span class="php-var">$loopName</span> = <span class="php-var">$names</span>[<span class="php-var">$i</span>];
</span><span id="197" class="l"><a href="#197">197: </a>
</span><span id="198" class="l"><a href="#198">198: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$loopName</span> ] ) ) {
</span><span id="199" class="l"><a href="#199">199: </a>                <span class="php-var">$inner</span>[ <span class="php-var">$loopName</span> ] = <span class="php-keyword1">array</span>();
</span><span id="200" class="l"><a href="#200">200: </a>            }
</span><span id="201" class="l"><a href="#201">201: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_array</span>( <span class="php-var">$inner</span>[ <span class="php-var">$loopName</span> ] ) ) {
</span><span id="202" class="l"><a href="#202">202: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(
</span><span id="203" class="l"><a href="#203">203: </a>                    <span class="php-quote">'A property with the name `'</span>.<span class="php-var">$name</span>.<span class="php-quote">'` already exists. This '</span>.
</span><span id="204" class="l"><a href="#204">204: </a>                    <span class="php-quote">'can occur if you have properties which share a prefix - '</span>.
</span><span id="205" class="l"><a href="#205">205: </a>                    <span class="php-quote">'for example `name` and `name.first`.'</span>
</span><span id="206" class="l"><a href="#206">206: </a>                );
</span><span id="207" class="l"><a href="#207">207: </a>            }
</span><span id="208" class="l"><a href="#208">208: </a>
</span><span id="209" class="l"><a href="#209">209: </a>            <span class="php-var">$inner</span> = &amp;<span class="php-var">$inner</span>[ <span class="php-var">$loopName</span> ];
</span><span id="210" class="l"><a href="#210">210: </a>        }
</span><span id="211" class="l"><a href="#211">211: </a>
</span><span id="212" class="l"><a href="#212">212: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>] ] ) ) {
</span><span id="213" class="l"><a href="#213">213: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(
</span><span id="214" class="l"><a href="#214">214: </a>                <span class="php-quote">'Duplicate field detected - a field with the name `'</span>.<span class="php-var">$name</span>.<span class="php-quote">'` '</span>.
</span><span id="215" class="l"><a href="#215">215: </a>                <span class="php-quote">'already exists.'</span>
</span><span id="216" class="l"><a href="#216">216: </a>            );
</span><span id="217" class="l"><a href="#217">217: </a>        }
</span><span id="218" class="l"><a href="#218">218: </a>
</span><span id="219" class="l"><a href="#219">219: </a>        <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>] ] = <span class="php-var">$value</span>;
</span><span id="220" class="l"><a href="#220">220: </a>    }
</span><span id="221" class="l"><a href="#221">221: </a>}
</span><span id="222" class="l"><a href="#222">222: </a>
</span><span id="223" class="l"><a href="#223">223: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
