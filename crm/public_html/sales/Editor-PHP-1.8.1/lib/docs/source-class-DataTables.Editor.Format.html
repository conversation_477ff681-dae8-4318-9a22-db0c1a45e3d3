<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Format.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a>
</span><span id="17" class="l"><a href="#17"> 17: </a><span class="php-comment">/**
</span></span><span id="18" class="l"><a href="#18"> 18: </a><span class="php-comment"> * Formatter methods for the DataTables Editor
</span></span><span id="19" class="l"><a href="#19"> 19: </a><span class="php-comment"> * 
</span></span><span id="20" class="l"><a href="#20"> 20: </a><span class="php-comment"> * All methods in this class are static with common inputs and returns.
</span></span><span id="21" class="l"><a href="#21"> 21: </a><span class="php-comment"> */</span>
</span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-keyword1">class</span> Format {
</span><span id="23" class="l"><a href="#23"> 23: </a>    <span class="php-comment">/** Date format: 2012-03-09. jQuery UI equivalent format: yy-mm-dd */</span>
</span><span id="24" class="l"><a href="#24"> 24: </a>    <span class="php-keyword1">const</span> DATE_ISO_8601 = <span class="php-quote">&quot;Y-m-d&quot;</span>;
</span><span id="25" class="l"><a href="#25"> 25: </a>
</span><span id="26" class="l"><a href="#26"> 26: </a>    <span class="php-comment">/** Date format: Fri, 9 Mar 12. jQuery UI equivalent format: D, d M y */</span>
</span><span id="27" class="l"><a href="#27"> 27: </a>    <span class="php-keyword1">const</span> DATE_ISO_822 = <span class="php-quote">&quot;D, j M y&quot;</span>;
</span><span id="28" class="l"><a href="#28"> 28: </a>    
</span><span id="29" class="l"><a href="#29"> 29: </a>    <span class="php-comment">/** Date format: Friday, 09-Mar-12.  jQuery UI equivalent format: DD, dd-M-y */</span>
</span><span id="30" class="l"><a href="#30"> 30: </a>    <span class="php-keyword1">const</span> DATE_ISO_850 = <span class="php-quote">&quot;l, d-M-y&quot;</span>;
</span><span id="31" class="l"><a href="#31"> 31: </a>    
</span><span id="32" class="l"><a href="#32"> 32: </a>    <span class="php-comment">/** Date format: Fri, 9 Mar 12. jQuery UI equivalent format: D, d M y */</span>
</span><span id="33" class="l"><a href="#33"> 33: </a>    <span class="php-keyword1">const</span> DATE_ISO_1036 = <span class="php-quote">&quot;D, j M y&quot;</span>;
</span><span id="34" class="l"><a href="#34"> 34: </a>    
</span><span id="35" class="l"><a href="#35"> 35: </a>    <span class="php-comment">/** Date format: Fri, 9 Mar 2012. jQuery UI equivalent format: D, d M yy */</span>
</span><span id="36" class="l"><a href="#36"> 36: </a>    <span class="php-keyword1">const</span> DATE_ISO_1123 = <span class="php-quote">&quot;D, j M Y&quot;</span>;
</span><span id="37" class="l"><a href="#37"> 37: </a>    
</span><span id="38" class="l"><a href="#38"> 38: </a>    <span class="php-comment">/** Date format: Fri, 9 Mar 2012. jQuery UI equivalent format: D, d M yy */</span>
</span><span id="39" class="l"><a href="#39"> 39: </a>    <span class="php-keyword1">const</span> DATE_ISO_2822 = <span class="php-quote">&quot;D, j M Y&quot;</span>;
</span><span id="40" class="l"><a href="#40"> 40: </a>
</span><span id="41" class="l"><a href="#41"> 41: </a>    <span class="php-comment">/** Date format: March-. jQuery UI equivalent format: D, d M yy */</span>
</span><span id="42" class="l"><a href="#42"> 42: </a>    <span class="php-keyword1">const</span> DATE_USA = <span class="php-quote">&quot;m-d-Y&quot;</span>;
</span><span id="43" class="l"><a href="#43"> 43: </a>    
</span><span id="44" class="l"><a href="#44"> 44: </a>    <span class="php-comment">/** Date format: 1331251200. jQuery UI equivalent format: @ */</span>
</span><span id="45" class="l"><a href="#45"> 45: </a>    <span class="php-keyword1">const</span> DATE_TIMESTAMP = <span class="php-quote">&quot;U&quot;</span>;
</span><span id="46" class="l"><a href="#46"> 46: </a>    
</span><span id="47" class="l"><a href="#47"> 47: </a>    <span class="php-comment">/** Date format: 1331251200. jQuery UI equivalent format: @ */</span>
</span><span id="48" class="l"><a href="#48"> 48: </a>    <span class="php-keyword1">const</span> DATE_EPOCH = <span class="php-quote">&quot;U&quot;</span>;
</span><span id="49" class="l"><a href="#49"> 49: </a>
</span><span id="50" class="l"><a href="#50"> 50: </a>
</span><span id="51" class="l"><a href="#51"> 51: </a>    <span class="php-comment">/**
</span></span><span id="52" class="l"><a href="#52"> 52: </a><span class="php-comment">     * Convert from SQL date / date time format to a format given by the options
</span></span><span id="53" class="l"><a href="#53"> 53: </a><span class="php-comment">     * parameter.
</span></span><span id="54" class="l"><a href="#54"> 54: </a><span class="php-comment">     *
</span></span><span id="55" class="l"><a href="#55"> 55: </a><span class="php-comment">     * Typical use of this method is to use it with the 
</span></span><span id="56" class="l"><a href="#56"> 56: </a><span class="php-comment">     * {@link Field::getFormatter} and {@link Field::setFormatter} methods of
</span></span><span id="57" class="l"><a href="#57"> 57: </a><span class="php-comment">     * {@link Field} where the parameters required for this method will be 
</span></span><span id="58" class="l"><a href="#58"> 58: </a><span class="php-comment">     * automatically satisfied.
</span></span><span id="59" class="l"><a href="#59"> 59: </a><span class="php-comment">     *   @param string $val Value to convert from MySQL date format
</span></span><span id="60" class="l"><a href="#60"> 60: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-comment">     *   @param string $opts Format to convert to using PHP date() options.
</span></span><span id="62" class="l"><a href="#62"> 62: </a><span class="php-comment">     *   @return string Formatted date or empty string on error.
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment">     */</span>
</span><span id="64" class="l"><a href="#64"> 64: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dateSqlToFormat( <span class="php-var">$format</span> ) {
</span><span id="65" class="l"><a href="#65"> 65: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$format</span> ) {
</span><span id="66" class="l"><a href="#66"> 66: </a>            <span class="php-var">$date</span> = <span class="php-keyword1">new</span> \DateTime( <span class="php-var">$val</span> );
</span><span id="67" class="l"><a href="#67"> 67: </a>
</span><span id="68" class="l"><a href="#68"> 68: </a>            <span class="php-comment">// Allow empty strings or invalid dates</span>
</span><span id="69" class="l"><a href="#69"> 69: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$val</span> &amp;&amp; <span class="php-var">$date</span> ) {
</span><span id="70" class="l"><a href="#70"> 70: </a>                <span class="php-keyword1">return</span> <span class="php-keyword2">date_format</span>( <span class="php-var">$date</span>, <span class="php-var">$format</span> );
</span><span id="71" class="l"><a href="#71"> 71: </a>            }
</span><span id="72" class="l"><a href="#72"> 72: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="73" class="l"><a href="#73"> 73: </a>        };
</span><span id="74" class="l"><a href="#74"> 74: </a>    }
</span><span id="75" class="l"><a href="#75"> 75: </a>
</span><span id="76" class="l"><a href="#76"> 76: </a>
</span><span id="77" class="l"><a href="#77"> 77: </a>    <span class="php-comment">/**
</span></span><span id="78" class="l"><a href="#78"> 78: </a><span class="php-comment">     * Convert from a format given by the options parameter to a format that
</span></span><span id="79" class="l"><a href="#79"> 79: </a><span class="php-comment">     * SQL servers will recognise as a date.
</span></span><span id="80" class="l"><a href="#80"> 80: </a><span class="php-comment">     *
</span></span><span id="81" class="l"><a href="#81"> 81: </a><span class="php-comment">     * Typical use of this method is to use it with the 
</span></span><span id="82" class="l"><a href="#82"> 82: </a><span class="php-comment">     * {@link Field::getFormatter} and {@link Field::setFormatter} methods of
</span></span><span id="83" class="l"><a href="#83"> 83: </a><span class="php-comment">     * {@link Field} where the parameters required for this method will be 
</span></span><span id="84" class="l"><a href="#84"> 84: </a><span class="php-comment">     * automatically satisfied.
</span></span><span id="85" class="l"><a href="#85"> 85: </a><span class="php-comment">     *   @param string $val Value to convert to SQL date format
</span></span><span id="86" class="l"><a href="#86"> 86: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="87" class="l"><a href="#87"> 87: </a><span class="php-comment">     *   @param string $opts Format to convert from using PHP date() options.
</span></span><span id="88" class="l"><a href="#88"> 88: </a><span class="php-comment">     *   @return string Formatted date or null on error.
</span></span><span id="89" class="l"><a href="#89"> 89: </a><span class="php-comment">     */</span>
</span><span id="90" class="l"><a href="#90"> 90: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> dateFormatToSql( <span class="php-var">$format</span> ) {
</span><span id="91" class="l"><a href="#91"> 91: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$format</span> ) {
</span><span id="92" class="l"><a href="#92"> 92: </a>            <span class="php-comment">// Note that this assumes the date is in the correct format (should be</span>
</span><span id="93" class="l"><a href="#93"> 93: </a>            <span class="php-comment">// checked by validation before being used here!)</span>
</span><span id="94" class="l"><a href="#94"> 94: </a>            <span class="php-var">$date</span> = <span class="php-keyword2">date_create_from_format</span>(<span class="php-var">$format</span>, <span class="php-var">$val</span>);
</span><span id="95" class="l"><a href="#95"> 95: </a>
</span><span id="96" class="l"><a href="#96"> 96: </a>            <span class="php-comment">// Invalid dates or empty string are replaced with null. Use the</span>
</span><span id="97" class="l"><a href="#97"> 97: </a>            <span class="php-comment">// validation to ensure the date given is valid if you don't want this!</span>
</span><span id="98" class="l"><a href="#98"> 98: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$val</span> &amp;&amp; <span class="php-var">$date</span> ) {
</span><span id="99" class="l"><a href="#99"> 99: </a>                <span class="php-keyword1">return</span> <span class="php-keyword2">date_format</span>( <span class="php-var">$date</span>, <span class="php-quote">'Y-m-d'</span> );
</span><span id="100" class="l"><a href="#100">100: </a>            }
</span><span id="101" class="l"><a href="#101">101: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="102" class="l"><a href="#102">102: </a>        };
</span><span id="103" class="l"><a href="#103">103: </a>    }
</span><span id="104" class="l"><a href="#104">104: </a>
</span><span id="105" class="l"><a href="#105">105: </a>
</span><span id="106" class="l"><a href="#106">106: </a>    <span class="php-comment">/**
</span></span><span id="107" class="l"><a href="#107">107: </a><span class="php-comment">     * Convert from one date time format to another
</span></span><span id="108" class="l"><a href="#108">108: </a><span class="php-comment">     *
</span></span><span id="109" class="l"><a href="#109">109: </a><span class="php-comment">     * Typical use of this method is to use it with the 
</span></span><span id="110" class="l"><a href="#110">110: </a><span class="php-comment">     * {@link Field::getFormatter} and {@link Field::setFormatter} methods of
</span></span><span id="111" class="l"><a href="#111">111: </a><span class="php-comment">     * {@link Field} where the parameters required for this method will be 
</span></span><span id="112" class="l"><a href="#112">112: </a><span class="php-comment">     * automatically satisfied.
</span></span><span id="113" class="l"><a href="#113">113: </a><span class="php-comment">     *   @param string $val Value to convert
</span></span><span id="114" class="l"><a href="#114">114: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="115" class="l"><a href="#115">115: </a><span class="php-comment">     *   @param string $opts Array with `from` and `to` properties which are the
</span></span><span id="116" class="l"><a href="#116">116: </a><span class="php-comment">     *     formats to convert from and to
</span></span><span id="117" class="l"><a href="#117">117: </a><span class="php-comment">     *   @return string Formatted date or null on error.
</span></span><span id="118" class="l"><a href="#118">118: </a><span class="php-comment">     */</span>
</span><span id="119" class="l"><a href="#119">119: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> datetime( <span class="php-var">$from</span>, <span class="php-var">$to</span> ) {
</span><span id="120" class="l"><a href="#120">120: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$from</span>, <span class="php-var">$to</span> ) {
</span><span id="121" class="l"><a href="#121">121: </a>            <span class="php-var">$date</span> = <span class="php-keyword2">date_create_from_format</span>( <span class="php-var">$from</span>, <span class="php-var">$val</span> );
</span><span id="122" class="l"><a href="#122">122: </a>
</span><span id="123" class="l"><a href="#123">123: </a>            <span class="php-comment">// Allow empty strings or invalid dates</span>
</span><span id="124" class="l"><a href="#124">124: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$date</span> ) {
</span><span id="125" class="l"><a href="#125">125: </a>                <span class="php-keyword1">return</span> <span class="php-keyword2">date_format</span>( <span class="php-var">$date</span>, <span class="php-var">$to</span> );
</span><span id="126" class="l"><a href="#126">126: </a>            }
</span><span id="127" class="l"><a href="#127">127: </a>
</span><span id="128" class="l"><a href="#128">128: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="129" class="l"><a href="#129">129: </a>        };
</span><span id="130" class="l"><a href="#130">130: </a>    }
</span><span id="131" class="l"><a href="#131">131: </a>
</span><span id="132" class="l"><a href="#132">132: </a>
</span><span id="133" class="l"><a href="#133">133: </a>    <span class="php-comment">/**
</span></span><span id="134" class="l"><a href="#134">134: </a><span class="php-comment">     * Convert a string of values into an array for use with checkboxes.
</span></span><span id="135" class="l"><a href="#135">135: </a><span class="php-comment">     *   @param string $val Value to convert to from a string to an array
</span></span><span id="136" class="l"><a href="#136">136: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="137" class="l"><a href="#137">137: </a><span class="php-comment">     *   @param string $opts Field delimiter
</span></span><span id="138" class="l"><a href="#138">138: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="139" class="l"><a href="#139">139: </a><span class="php-comment">     */</span>
</span><span id="140" class="l"><a href="#140">140: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> <span class="php-keyword2">explode</span>( <span class="php-var">$char</span>=<span class="php-quote">'|'</span> ) {
</span><span id="141" class="l"><a href="#141">141: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$char</span> ) {
</span><span id="142" class="l"><a href="#142">142: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">explode</span>(<span class="php-var">$char</span>, <span class="php-var">$val</span>);
</span><span id="143" class="l"><a href="#143">143: </a>        };
</span><span id="144" class="l"><a href="#144">144: </a>    }
</span><span id="145" class="l"><a href="#145">145: </a>
</span><span id="146" class="l"><a href="#146">146: </a>
</span><span id="147" class="l"><a href="#147">147: </a>    <span class="php-comment">/**
</span></span><span id="148" class="l"><a href="#148">148: </a><span class="php-comment">     * Convert an array of values from a checkbox into a string which can be
</span></span><span id="149" class="l"><a href="#149">149: </a><span class="php-comment">     * used to store in a text field in a database.
</span></span><span id="150" class="l"><a href="#150">150: </a><span class="php-comment">     *   @param string $val Value to convert to from an array to a string
</span></span><span id="151" class="l"><a href="#151">151: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="152" class="l"><a href="#152">152: </a><span class="php-comment">     *   @param string $opts Field delimiter
</span></span><span id="153" class="l"><a href="#153">153: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="154" class="l"><a href="#154">154: </a><span class="php-comment">     */</span>
</span><span id="155" class="l"><a href="#155">155: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> <span class="php-keyword2">implode</span>( <span class="php-var">$char</span>=<span class="php-quote">'|'</span> ) {
</span><span id="156" class="l"><a href="#156">156: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$char</span> ) {
</span><span id="157" class="l"><a href="#157">157: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">implode</span>(<span class="php-var">$char</span>, <span class="php-var">$val</span>);
</span><span id="158" class="l"><a href="#158">158: </a>        };
</span><span id="159" class="l"><a href="#159">159: </a>    }
</span><span id="160" class="l"><a href="#160">160: </a>
</span><span id="161" class="l"><a href="#161">161: </a>
</span><span id="162" class="l"><a href="#162">162: </a>    <span class="php-comment">/**
</span></span><span id="163" class="l"><a href="#163">163: </a><span class="php-comment">     * Convert an empty string to `null`. Null values are very useful in
</span></span><span id="164" class="l"><a href="#164">164: </a><span class="php-comment">     * databases, but HTTP variables have no way of representing `null` as a
</span></span><span id="165" class="l"><a href="#165">165: </a><span class="php-comment">     * value, often leading to an empty string and null overlapping. This method
</span></span><span id="166" class="l"><a href="#166">166: </a><span class="php-comment">     * will check the value to operate on and return null if it is empty.
</span></span><span id="167" class="l"><a href="#167">167: </a><span class="php-comment">     *   @param string $val Value to convert to from a string to an array
</span></span><span id="168" class="l"><a href="#168">168: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="169" class="l"><a href="#169">169: </a><span class="php-comment">     *   @param string $opts Field delimiter
</span></span><span id="170" class="l"><a href="#170">170: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="171" class="l"><a href="#171">171: </a><span class="php-comment">     */</span>
</span><span id="172" class="l"><a href="#172">172: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> nullEmpty () {
</span><span id="173" class="l"><a href="#173">173: </a>        <span class="php-comment">// Legacy function - use `ifEmpty` now</span>
</span><span id="174" class="l"><a href="#174">174: </a>        <span class="php-keyword1">return</span> self::ifEmpty( <span class="php-keyword1">null</span> );
</span><span id="175" class="l"><a href="#175">175: </a>    }
</span><span id="176" class="l"><a href="#176">176: </a>
</span><span id="177" class="l"><a href="#177">177: </a>
</span><span id="178" class="l"><a href="#178">178: </a>    <span class="php-comment">/**
</span></span><span id="179" class="l"><a href="#179">179: </a><span class="php-comment">     * Formatter that can be used to specify what value should be used if an
</span></span><span id="180" class="l"><a href="#180">180: </a><span class="php-comment">     * empty value is submitted by the client-side (e.g. null, 0, 'Not set',
</span></span><span id="181" class="l"><a href="#181">181: </a><span class="php-comment">     * etc)
</span></span><span id="182" class="l"><a href="#182">182: </a><span class="php-comment">     *   @param string $val Value to convert to from a string to an array
</span></span><span id="183" class="l"><a href="#183">183: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="184" class="l"><a href="#184">184: </a><span class="php-comment">     *   @param string $opts Empty value
</span></span><span id="185" class="l"><a href="#185">185: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="186" class="l"><a href="#186">186: </a><span class="php-comment">     */</span>
</span><span id="187" class="l"><a href="#187">187: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> ifEmpty ( <span class="php-var">$ret</span> ) {
</span><span id="188" class="l"><a href="#188">188: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$ret</span> ) {
</span><span id="189" class="l"><a href="#189">189: </a>            <span class="php-keyword1">return</span> <span class="php-var">$val</span> === <span class="php-quote">''</span> ?
</span><span id="190" class="l"><a href="#190">190: </a>                <span class="php-var">$ret</span> :
</span><span id="191" class="l"><a href="#191">191: </a>                <span class="php-var">$val</span>;
</span><span id="192" class="l"><a href="#192">192: </a>        };
</span><span id="193" class="l"><a href="#193">193: </a>    }
</span><span id="194" class="l"><a href="#194">194: </a>
</span><span id="195" class="l"><a href="#195">195: </a>
</span><span id="196" class="l"><a href="#196">196: </a>    <span class="php-comment">/**
</span></span><span id="197" class="l"><a href="#197">197: </a><span class="php-comment">     * Convert a number from using any character other than a period (dot) to
</span></span><span id="198" class="l"><a href="#198">198: </a><span class="php-comment">     * one which does use a period. This is useful for allowing numeric user
</span></span><span id="199" class="l"><a href="#199">199: </a><span class="php-comment">     * input in regions where a comma is used as the decimal character. Use with
</span></span><span id="200" class="l"><a href="#200">200: </a><span class="php-comment">     * a set formatter.
</span></span><span id="201" class="l"><a href="#201">201: </a><span class="php-comment">     *   @param string $val Value to convert to from a string to an array
</span></span><span id="202" class="l"><a href="#202">202: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="203" class="l"><a href="#203">203: </a><span class="php-comment">     *   @param string $opts Decimal place character (default ',')
</span></span><span id="204" class="l"><a href="#204">204: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="205" class="l"><a href="#205">205: </a><span class="php-comment">     */</span>
</span><span id="206" class="l"><a href="#206">206: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> fromDecimalChar ( <span class="php-var">$char</span>=<span class="php-quote">','</span> ) {
</span><span id="207" class="l"><a href="#207">207: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$char</span> ) {
</span><span id="208" class="l"><a href="#208">208: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>( <span class="php-var">$char</span>, <span class="php-quote">'.'</span>, <span class="php-var">$val</span> );
</span><span id="209" class="l"><a href="#209">209: </a>        };
</span><span id="210" class="l"><a href="#210">210: </a>    }
</span><span id="211" class="l"><a href="#211">211: </a>
</span><span id="212" class="l"><a href="#212">212: </a>
</span><span id="213" class="l"><a href="#213">213: </a>    <span class="php-comment">/**
</span></span><span id="214" class="l"><a href="#214">214: </a><span class="php-comment">     * Convert a number with a period (dot) as the decimal character to use
</span></span><span id="215" class="l"><a href="#215">215: </a><span class="php-comment">     * a different character (typically a comma). Use with a get formatter.
</span></span><span id="216" class="l"><a href="#216">216: </a><span class="php-comment">     *   @param string $val Value to convert to from a string to an array
</span></span><span id="217" class="l"><a href="#217">217: </a><span class="php-comment">     *   @param string[] $data Data for the whole row / submitted data
</span></span><span id="218" class="l"><a href="#218">218: </a><span class="php-comment">     *   @param string $opts Decimal place character (default ',')
</span></span><span id="219" class="l"><a href="#219">219: </a><span class="php-comment">     *   @return string Formatted value or null on error.
</span></span><span id="220" class="l"><a href="#220">220: </a><span class="php-comment">     */</span>
</span><span id="221" class="l"><a href="#221">221: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> toDecimalChar ( <span class="php-var">$char</span>=<span class="php-quote">','</span> ) {
</span><span id="222" class="l"><a href="#222">222: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">function</span> ( <span class="php-var">$val</span>, <span class="php-var">$data</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$char</span> ) {
</span><span id="223" class="l"><a href="#223">223: </a>            <span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>( <span class="php-quote">'.'</span>, <span class="php-var">$char</span>, <span class="php-var">$val</span> );
</span><span id="224" class="l"><a href="#224">224: </a>        };
</span><span id="225" class="l"><a href="#225">225: </a>    }
</span><span id="226" class="l"><a href="#226">226: </a>
</span><span id="227" class="l"><a href="#227">227: </a>
</span><span id="228" class="l"><a href="#228">228: </a>
</span><span id="229" class="l"><a href="#229">229: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="230" class="l"><a href="#230">230: </a><span class="php-comment">     * Internal functions
</span></span><span id="231" class="l"><a href="#231">231: </a><span class="php-comment">     * These legacy methods are for backwards compatibility with the old way of
</span></span><span id="232" class="l"><a href="#232">232: </a><span class="php-comment">     * using the formatter methods. They basically do argument swapping.
</span></span><span id="233" class="l"><a href="#233">233: </a><span class="php-comment">     */</span>
</span><span id="234" class="l"><a href="#234">234: </a>
</span><span id="235" class="l"><a href="#235">235: </a>    <span class="php-comment">/**
</span></span><span id="236" class="l"><a href="#236">236: </a><span class="php-comment">     * @internal
</span></span><span id="237" class="l"><a href="#237">237: </a><span class="php-comment">     */</span>
</span><span id="238" class="l"><a href="#238">238: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> date_sql_to_format ( <span class="php-var">$opts</span> ) {
</span><span id="239" class="l"><a href="#239">239: </a>        <span class="php-keyword1">return</span> self::dateSqlToFormat( <span class="php-var">$opts</span> );
</span><span id="240" class="l"><a href="#240">240: </a>    }
</span><span id="241" class="l"><a href="#241">241: </a>
</span><span id="242" class="l"><a href="#242">242: </a>    <span class="php-comment">/**
</span></span><span id="243" class="l"><a href="#243">243: </a><span class="php-comment">     * @internal
</span></span><span id="244" class="l"><a href="#244">244: </a><span class="php-comment">     */</span>
</span><span id="245" class="l"><a href="#245">245: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> date_sql_to_formatLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="246" class="l"><a href="#246">246: </a>        <span class="php-keyword1">return</span> self::dateSqlToFormat( <span class="php-var">$opts</span> );
</span><span id="247" class="l"><a href="#247">247: </a>    }
</span><span id="248" class="l"><a href="#248">248: </a>
</span><span id="249" class="l"><a href="#249">249: </a>    <span class="php-comment">/**
</span></span><span id="250" class="l"><a href="#250">250: </a><span class="php-comment">     * @internal
</span></span><span id="251" class="l"><a href="#251">251: </a><span class="php-comment">     */</span>
</span><span id="252" class="l"><a href="#252">252: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> date_format_to_sql ( <span class="php-var">$opts</span> ) {
</span><span id="253" class="l"><a href="#253">253: </a>        <span class="php-keyword1">return</span> self::dateFormatToSql( <span class="php-var">$opts</span> );
</span><span id="254" class="l"><a href="#254">254: </a>    }
</span><span id="255" class="l"><a href="#255">255: </a>
</span><span id="256" class="l"><a href="#256">256: </a>    <span class="php-comment">/**
</span></span><span id="257" class="l"><a href="#257">257: </a><span class="php-comment">     * @internal
</span></span><span id="258" class="l"><a href="#258">258: </a><span class="php-comment">     */</span>
</span><span id="259" class="l"><a href="#259">259: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> date_format_to_sqlLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="260" class="l"><a href="#260">260: </a>        <span class="php-keyword1">return</span> self::dateFormatToSql( <span class="php-var">$opts</span> );
</span><span id="261" class="l"><a href="#261">261: </a>    }
</span><span id="262" class="l"><a href="#262">262: </a>    
</span><span id="263" class="l"><a href="#263">263: </a>    <span class="php-comment">/**
</span></span><span id="264" class="l"><a href="#264">264: </a><span class="php-comment">     * @internal
</span></span><span id="265" class="l"><a href="#265">265: </a><span class="php-comment">     */</span>
</span><span id="266" class="l"><a href="#266">266: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> datetimeLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="267" class="l"><a href="#267">267: </a>        <span class="php-keyword1">return</span> self::datetime( <span class="php-var">$opts</span>[<span class="php-quote">'from'</span>], <span class="php-var">$opts</span>[<span class="php-quote">'to'</span>] );
</span><span id="268" class="l"><a href="#268">268: </a>    }
</span><span id="269" class="l"><a href="#269">269: </a>    
</span><span id="270" class="l"><a href="#270">270: </a>    <span class="php-comment">/**
</span></span><span id="271" class="l"><a href="#271">271: </a><span class="php-comment">     * @internal
</span></span><span id="272" class="l"><a href="#272">272: </a><span class="php-comment">     */</span>
</span><span id="273" class="l"><a href="#273">273: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> explodeLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="274" class="l"><a href="#274">274: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> === <span class="php-keyword1">null</span> ) {
</span><span id="275" class="l"><a href="#275">275: </a>            <span class="php-var">$opts</span> = <span class="php-quote">'|'</span>;
</span><span id="276" class="l"><a href="#276">276: </a>        }
</span><span id="277" class="l"><a href="#277">277: </a>        <span class="php-keyword1">return</span> self::<span class="php-keyword2">explode</span>( <span class="php-var">$opts</span> );
</span><span id="278" class="l"><a href="#278">278: </a>    }
</span><span id="279" class="l"><a href="#279">279: </a>
</span><span id="280" class="l"><a href="#280">280: </a>    <span class="php-comment">/**
</span></span><span id="281" class="l"><a href="#281">281: </a><span class="php-comment">     * @internal
</span></span><span id="282" class="l"><a href="#282">282: </a><span class="php-comment">     */</span>
</span><span id="283" class="l"><a href="#283">283: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> implodeLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="284" class="l"><a href="#284">284: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> === <span class="php-keyword1">null</span> ) {
</span><span id="285" class="l"><a href="#285">285: </a>            <span class="php-var">$opts</span> = <span class="php-quote">'|'</span>;
</span><span id="286" class="l"><a href="#286">286: </a>        }
</span><span id="287" class="l"><a href="#287">287: </a>        <span class="php-keyword1">return</span> self::<span class="php-keyword2">implode</span>( <span class="php-var">$opts</span> );
</span><span id="288" class="l"><a href="#288">288: </a>    }
</span><span id="289" class="l"><a href="#289">289: </a>
</span><span id="290" class="l"><a href="#290">290: </a>    <span class="php-comment">/**
</span></span><span id="291" class="l"><a href="#291">291: </a><span class="php-comment">     * @internal
</span></span><span id="292" class="l"><a href="#292">292: </a><span class="php-comment">     */</span>
</span><span id="293" class="l"><a href="#293">293: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> nullEmptyLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="294" class="l"><a href="#294">294: </a>        <span class="php-keyword1">return</span> self::nullEmpty( <span class="php-keyword1">null</span> );
</span><span id="295" class="l"><a href="#295">295: </a>    }
</span><span id="296" class="l"><a href="#296">296: </a>    
</span><span id="297" class="l"><a href="#297">297: </a>    <span class="php-comment">/**
</span></span><span id="298" class="l"><a href="#298">298: </a><span class="php-comment">     * @internal
</span></span><span id="299" class="l"><a href="#299">299: </a><span class="php-comment">     */</span>
</span><span id="300" class="l"><a href="#300">300: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> ifEmptyLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="301" class="l"><a href="#301">301: </a>        <span class="php-keyword1">return</span> self::ifEmpty( <span class="php-var">$opts</span> );
</span><span id="302" class="l"><a href="#302">302: </a>    }
</span><span id="303" class="l"><a href="#303">303: </a>
</span><span id="304" class="l"><a href="#304">304: </a>    <span class="php-comment">/**
</span></span><span id="305" class="l"><a href="#305">305: </a><span class="php-comment">     * @internal
</span></span><span id="306" class="l"><a href="#306">306: </a><span class="php-comment">     */</span>
</span><span id="307" class="l"><a href="#307">307: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> fromDecimalCharLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="308" class="l"><a href="#308">308: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> === <span class="php-keyword1">null</span> ) {
</span><span id="309" class="l"><a href="#309">309: </a>            <span class="php-var">$opts</span> = <span class="php-quote">','</span>;
</span><span id="310" class="l"><a href="#310">310: </a>        }
</span><span id="311" class="l"><a href="#311">311: </a>        <span class="php-keyword1">return</span> self::fromDecimalChar( <span class="php-var">$opts</span> );
</span><span id="312" class="l"><a href="#312">312: </a>    }
</span><span id="313" class="l"><a href="#313">313: </a>    
</span><span id="314" class="l"><a href="#314">314: </a>    <span class="php-comment">/**
</span></span><span id="315" class="l"><a href="#315">315: </a><span class="php-comment">     * @internal
</span></span><span id="316" class="l"><a href="#316">316: </a><span class="php-comment">     */</span>
</span><span id="317" class="l"><a href="#317">317: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> toDecimalCharLegacy ( <span class="php-var">$opts</span> ) {
</span><span id="318" class="l"><a href="#318">318: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> === <span class="php-keyword1">null</span> ) {
</span><span id="319" class="l"><a href="#319">319: </a>            <span class="php-var">$opts</span> = <span class="php-quote">','</span>;
</span><span id="320" class="l"><a href="#320">320: </a>        }
</span><span id="321" class="l"><a href="#321">321: </a>        <span class="php-keyword1">return</span> self::toDecimalChar( <span class="php-var">$opts</span> );
</span><span id="322" class="l"><a href="#322">322: </a>    }
</span><span id="323" class="l"><a href="#323">323: </a>}
</span><span id="324" class="l"><a href="#324">324: </a>
</span><span id="325" class="l"><a href="#325">325: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
