<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Class DataTables\Vendor\Htmlaw | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li class="active">
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li class="active"><a href="class-DataTables.Vendor.Htmlaw.html">Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
				<a href="namespace-DataTables.Vendor.html" title="Summary of DataTables\Vendor"><span>Namespace</span></a>
			</li>
			<li class="active">
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="class">
	<h1>Class Htmlaw</h1>


	<div class="description">
	<p>A class wrapper for the htmLawed library.</p>
	</div>









	<div class="info">
		
		
		<b>Namespace:</b> <a href="namespace-DataTables.html">DataTables</a>\<a href="namespace-DataTables.Vendor.html">Vendor</a><br>
		

			<b>Located at</b> <a href="source-class-DataTables.Vendor.Htmlaw.html#34-114" title="Go to source code">Vendor/Htmlaw.php</a>
		<br>
	</div>



	<table class="summary methods" id="methods">
	<caption>Methods summary</caption>
	<tr data-order="filter" id="_filter">

		<td class="attributes"><code>
			 public static

			string
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_filter">#</a>
		<code><a href="source-class-DataTables.Vendor.Htmlaw.html#60-85" title="Go to source code">filter</a>( <span>string <var>$html</var></span>, <span>array <var>$config</var> = <span class="php-keyword1">null</span></span>, <span>string|array|null <var>$spec</var> = <span class="php-keyword1">null</span></span> )</code>

		<div class="description short">
			<p>Filters a string of html with the htmLawed library.</p>
		</div>

		<div class="description detailed hidden">
			<p>Filters a string of html with the htmLawed library.</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$html</var></dt>
					<dd>The text to filter.</dd>
					<dt><var>$config</var></dt>
					<dd>Config settings for the array.</dd>
					<dt><var>$spec</var></dt>
					<dd>A specification to further limit the allowed attribute values in the html.</dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					string<br>Returns the filtered html.
				</div>


				<h4>See</h4>
				<div class="list">
						http://www.bioinformatics.org/phplabware/internal_utilities/htmLawed/htmLawed_README.htm<br>
				</div>


		</div>
		</div></td>
	</tr>
	<tr data-order="filterRSS" id="_filterRSS">

		<td class="attributes"><code>
			 public static

			string
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_filterRSS">#</a>
		<code><a href="source-class-DataTables.Vendor.Htmlaw.html#88-113" title="Go to source code">filterRSS</a>( <span> <var>$html</var></span> )</code>

		<div class="description short">
			<p>Filter a string of html so that it can be put into an rss feed.</p>
		</div>

		<div class="description detailed hidden">
			<p>Filter a string of html so that it can be put into an rss feed.</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$html</var></dt>
					<dd>html text to fitlter.</dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					string<br>Returns the filtered html.
				</div>


				<h4>See</h4>
				<div class="list">
						Htmlawed::filter().<br>
				</div>


		</div>
		</div></td>
	</tr>
	</table>












	<table class="summary properties" id="properties">
	<caption>Properties summary</caption>
	<tr data-order="defaultConfig" id="$defaultConfig">
		<td class="attributes"><code>
			public static 
			array
		</code></td>

		<td class="name">
				<a href="source-class-DataTables.Vendor.Htmlaw.html#40-53" title="Go to source code"><var>$defaultConfig</var></a>

			<div class="description short">
				
			</div>

			<div class="description detailed hidden">
				

			</div>
		</td>
		<td class="value">
			<div>
				<a href="#$defaultConfig" class="anchor">#</a>
				<code><span class="php-keyword1">array</span>(
    <span class="php-quote">'anti_link_spam'</span> =&gt; <span class="php-keyword1">array</span>(<span class="php-quote">'`.`'</span>, <span class="php-quote">''</span>),
    <span class="php-quote">'comment'</span> =&gt; <span class="php-num">1</span>,
    <span class="php-quote">'cdata'</span> =&gt; <span class="php-num">3</span>,
    <span class="php-quote">'css_expression'</span> =&gt; <span class="php-num">1</span>,
    <span class="php-quote">'deny_attribute'</span> =&gt; <span class="php-quote">'on*'</span>,
    <span class="php-quote">'unique_ids'</span> =&gt; <span class="php-num">0</span>,
    <span class="php-quote">'elements'</span> =&gt; <span class="php-quote">'*-applet-form-input-textarea-iframe-script-style-embed-object'</span>,
    <span class="php-quote">'keep_bad'</span> =&gt; <span class="php-num">1</span>,
    <span class="php-quote">'schemes'</span> =&gt; <span class="php-quote">'classid:clsid; href: aim, feed, file, ftp, gopher, http, https, irc, mailto, news, nntp, sftp, ssh, telnet; style: nil; *:file, http, https'</span>, <span class="php-comment">// clsid allowed in class</span>
    <span class="php-quote">'valid_xhtml'</span> =&gt; <span class="php-num">0</span>,
    <span class="php-quote">'direct_list_nest'</span> =&gt; <span class="php-num">1</span>,
    <span class="php-quote">'balance'</span> =&gt; <span class="php-num">1</span>
)</code>
			</div>
		</td>
	</tr>
	<tr data-order="defaultSpec" id="$defaultSpec">
		<td class="attributes"><code>
			public static 
			array
		</code></td>

		<td class="name">
				<a href="source-class-DataTables.Vendor.Htmlaw.html#55-58" title="Go to source code"><var>$defaultSpec</var></a>

			<div class="description short">
				
			</div>

			<div class="description detailed hidden">
				

			</div>
		</td>
		<td class="value">
			<div>
				<a href="#$defaultSpec" class="anchor">#</a>
				<code><span class="php-keyword1">array</span>(
    <span class="php-quote">'object=-classid-type, -codebase'</span>,
    <span class="php-quote">'embed=type(oneof=application/x-shockwave-flash)'</span>
)</code>
			</div>
		</td>
	</tr>
	</table>






</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js"></script>
<script src="elementlist.js"></script>
</body>
</html>
