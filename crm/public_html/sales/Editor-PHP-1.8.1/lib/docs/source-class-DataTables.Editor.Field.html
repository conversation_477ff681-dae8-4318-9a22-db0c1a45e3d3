<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Field.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-keyword1">use</span>
</span><span id="17" class="l"><a href="#17"> 17: </a>    DataTables,
</span><span id="18" class="l"><a href="#18"> 18: </a>    DataTables\Editor,
</span><span id="19" class="l"><a href="#19"> 19: </a>    DataTables\Editor\Options,
</span><span id="20" class="l"><a href="#20"> 20: </a>    DataTables\Editor\<span class="php-keyword2">Join</span>;
</span><span id="21" class="l"><a href="#21"> 21: </a>
</span><span id="22" class="l"><a href="#22"> 22: </a>
</span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment">/**
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment"> * Field definitions for the DataTables Editor.
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment"> *
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment"> * Each Database column that is used with Editor can be described with this 
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment"> * Field method (both for Editor and Join instances). It basically tells
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment"> * Editor what table column to use, how to format the data and if you want
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment"> * to read and/or write this column.
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment"> *
</span></span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-comment"> * Field instances are used with the {@link Editor::field} and 
</span></span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-comment"> * {@link Join::field} methods to describe what fields should be interacted
</span></span><span id="33" class="l"><a href="#33"> 33: </a><span class="php-comment"> * with by the editable table.
</span></span><span id="34" class="l"><a href="#34"> 34: </a><span class="php-comment"> *
</span></span><span id="35" class="l"><a href="#35"> 35: </a><span class="php-comment"> *  @example
</span></span><span id="36" class="l"><a href="#36"> 36: </a><span class="php-comment"> *    Simply get a column with the name &quot;city&quot;. No validation is performed.
</span></span><span id="37" class="l"><a href="#37"> 37: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="38" class="l"><a href="#38"> 38: </a><span class="php-comment"> *      Field::inst( 'city' )
</span></span><span id="39" class="l"><a href="#39"> 39: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="40" class="l"><a href="#40"> 40: </a><span class="php-comment"> *
</span></span><span id="41" class="l"><a href="#41"> 41: </a><span class="php-comment"> *  @example
</span></span><span id="42" class="l"><a href="#42"> 42: </a><span class="php-comment"> *    Get a column with the name &quot;first_name&quot; - when edited a value must
</span></span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment"> *    be given due to the &quot;required&quot; validation from the {@link Validate} class.
</span></span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-comment"> *      Field::inst( 'first_name' )-&gt;validator( 'Validate::required' )
</span></span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="47" class="l"><a href="#47"> 47: </a><span class="php-comment"> *
</span></span><span id="48" class="l"><a href="#48"> 48: </a><span class="php-comment"> *  @example
</span></span><span id="49" class="l"><a href="#49"> 49: </a><span class="php-comment"> *    Working with a date field, which is validated, and also has *get* and
</span></span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-comment"> *    *set* formatters.
</span></span><span id="51" class="l"><a href="#51"> 51: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="52" class="l"><a href="#52"> 52: </a><span class="php-comment"> *      Field::inst( 'registered_date' )
</span></span><span id="53" class="l"><a href="#53"> 53: </a><span class="php-comment"> *          -&gt;validator( 'Validate::dateFormat', 'D, d M y' )
</span></span><span id="54" class="l"><a href="#54"> 54: </a><span class="php-comment"> *          -&gt;getFormatter( 'Format::date_sql_to_format', 'D, d M y' )
</span></span><span id="55" class="l"><a href="#55"> 55: </a><span class="php-comment"> *          -&gt;setFormatter( 'Format::date_format_to_sql', 'D, d M y' )
</span></span><span id="56" class="l"><a href="#56"> 56: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="57" class="l"><a href="#57"> 57: </a><span class="php-comment"> *
</span></span><span id="58" class="l"><a href="#58"> 58: </a><span class="php-comment"> *  @example
</span></span><span id="59" class="l"><a href="#59"> 59: </a><span class="php-comment"> *    Using an alias in the first parameter
</span></span><span id="60" class="l"><a href="#60"> 60: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-comment"> *      Field::inst( 'name.first as first_name' )
</span></span><span id="62" class="l"><a href="#62"> 62: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment"> */</span>
</span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-keyword1">class</span> Field <span class="php-keyword1">extends</span> DataTables\Ext {
</span><span id="65" class="l"><a href="#65"> 65: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="66" class="l"><a href="#66"> 66: </a><span class="php-comment">     * Statics
</span></span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-comment">     */</span>
</span><span id="68" class="l"><a href="#68"> 68: </a>    
</span><span id="69" class="l"><a href="#69"> 69: </a>    <span class="php-comment">/** Set option flag (`set()`) - do not set data */</span>
</span><span id="70" class="l"><a href="#70"> 70: </a>    <span class="php-keyword1">const</span> SET_NONE = <span class="php-quote">'none'</span>;
</span><span id="71" class="l"><a href="#71"> 71: </a>
</span><span id="72" class="l"><a href="#72"> 72: </a>    <span class="php-comment">/** Set option flag (`set()`) - write to database on both create and edit */</span>
</span><span id="73" class="l"><a href="#73"> 73: </a>    <span class="php-keyword1">const</span> SET_BOTH = <span class="php-quote">'both'</span>;
</span><span id="74" class="l"><a href="#74"> 74: </a>
</span><span id="75" class="l"><a href="#75"> 75: </a>    <span class="php-comment">/** Set option flag (`set()`) - write to database only on create */</span>
</span><span id="76" class="l"><a href="#76"> 76: </a>    <span class="php-keyword1">const</span> SET_CREATE = <span class="php-quote">'create'</span>;
</span><span id="77" class="l"><a href="#77"> 77: </a>
</span><span id="78" class="l"><a href="#78"> 78: </a>    <span class="php-comment">/** Set option flag (`set()`) - write to database only on edit */</span>
</span><span id="79" class="l"><a href="#79"> 79: </a>    <span class="php-keyword1">const</span> SET_EDIT = <span class="php-quote">'edit'</span>;
</span><span id="80" class="l"><a href="#80"> 80: </a>
</span><span id="81" class="l"><a href="#81"> 81: </a>
</span><span id="82" class="l"><a href="#82"> 82: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="83" class="l"><a href="#83"> 83: </a><span class="php-comment">     * Constructor
</span></span><span id="84" class="l"><a href="#84"> 84: </a><span class="php-comment">     */</span>
</span><span id="85" class="l"><a href="#85"> 85: </a>
</span><span id="86" class="l"><a href="#86"> 86: </a>    <span class="php-comment">/**
</span></span><span id="87" class="l"><a href="#87"> 87: </a><span class="php-comment">     * Field instance constructor.
</span></span><span id="88" class="l"><a href="#88"> 88: </a><span class="php-comment">     *  @param string $dbField Name of the database column
</span></span><span id="89" class="l"><a href="#89"> 89: </a><span class="php-comment">     *  @param string $name Name to use in the JSON output from Editor and the
</span></span><span id="90" class="l"><a href="#90"> 90: </a><span class="php-comment">     *    HTTP submit from the client-side when editing. If not given then the
</span></span><span id="91" class="l"><a href="#91"> 91: </a><span class="php-comment">     *    $dbField name is used.
</span></span><span id="92" class="l"><a href="#92"> 92: </a><span class="php-comment">     */</span>
</span><span id="93" class="l"><a href="#93"> 93: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$dbField</span>=<span class="php-keyword1">null</span>, <span class="php-var">$name</span>=<span class="php-keyword1">null</span> )
</span><span id="94" class="l"><a href="#94"> 94: </a>    {
</span><span id="95" class="l"><a href="#95"> 95: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$dbField</span> !== <span class="php-keyword1">null</span> &amp;&amp; <span class="php-var">$name</span> === <span class="php-keyword1">null</span> ) {
</span><span id="96" class="l"><a href="#96"> 96: </a>            <span class="php-comment">// Allow just a single parameter to be passed - each can be </span>
</span><span id="97" class="l"><a href="#97"> 97: </a>            <span class="php-comment">// overridden if needed later using the API.</span>
</span><span id="98" class="l"><a href="#98"> 98: </a>            <span class="php-var">$this</span>-&gt;name( <span class="php-var">$dbField</span> );
</span><span id="99" class="l"><a href="#99"> 99: </a>            <span class="php-var">$this</span>-&gt;dbField( <span class="php-var">$dbField</span> );
</span><span id="100" class="l"><a href="#100">100: </a>        }
</span><span id="101" class="l"><a href="#101">101: </a>        <span class="php-keyword1">else</span> {
</span><span id="102" class="l"><a href="#102">102: </a>            <span class="php-var">$this</span>-&gt;name( <span class="php-var">$name</span> );
</span><span id="103" class="l"><a href="#103">103: </a>            <span class="php-var">$this</span>-&gt;dbField( <span class="php-var">$dbField</span> );
</span><span id="104" class="l"><a href="#104">104: </a>        }
</span><span id="105" class="l"><a href="#105">105: </a>    }
</span><span id="106" class="l"><a href="#106">106: </a>
</span><span id="107" class="l"><a href="#107">107: </a>
</span><span id="108" class="l"><a href="#108">108: </a>
</span><span id="109" class="l"><a href="#109">109: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="110" class="l"><a href="#110">110: </a><span class="php-comment">     * Private parameters
</span></span><span id="111" class="l"><a href="#111">111: </a><span class="php-comment">     */</span>
</span><span id="112" class="l"><a href="#112">112: </a>
</span><span id="113" class="l"><a href="#113">113: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="114" class="l"><a href="#114">114: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbField</span> = <span class="php-keyword1">null</span>;
</span><span id="115" class="l"><a href="#115">115: </a>
</span><span id="116" class="l"><a href="#116">116: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="117" class="l"><a href="#117">117: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_get</span> = <span class="php-keyword1">true</span>;
</span><span id="118" class="l"><a href="#118">118: </a>
</span><span id="119" class="l"><a href="#119">119: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="120" class="l"><a href="#120">120: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_getFormatter</span> = <span class="php-keyword1">null</span>;
</span><span id="121" class="l"><a href="#121">121: </a>
</span><span id="122" class="l"><a href="#122">122: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="123" class="l"><a href="#123">123: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_getFormatterOpts</span> = <span class="php-keyword1">null</span>;
</span><span id="124" class="l"><a href="#124">124: </a>
</span><span id="125" class="l"><a href="#125">125: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="126" class="l"><a href="#126">126: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_getValue</span> = <span class="php-keyword1">null</span>;
</span><span id="127" class="l"><a href="#127">127: </a>
</span><span id="128" class="l"><a href="#128">128: </a>    <span class="php-comment">/** @var Options */</span>
</span><span id="129" class="l"><a href="#129">129: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_opts</span> = <span class="php-keyword1">null</span>;
</span><span id="130" class="l"><a href="#130">130: </a>
</span><span id="131" class="l"><a href="#131">131: </a>    <span class="php-comment">/** @var callable */</span>
</span><span id="132" class="l"><a href="#132">132: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_optsFn</span> = <span class="php-keyword1">null</span>;
</span><span id="133" class="l"><a href="#133">133: </a>
</span><span id="134" class="l"><a href="#134">134: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="135" class="l"><a href="#135">135: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_name</span> = <span class="php-keyword1">null</span>;
</span><span id="136" class="l"><a href="#136">136: </a>
</span><span id="137" class="l"><a href="#137">137: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="138" class="l"><a href="#138">138: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_set</span> = Field::SET_BOTH;
</span><span id="139" class="l"><a href="#139">139: </a>
</span><span id="140" class="l"><a href="#140">140: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="141" class="l"><a href="#141">141: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_setFormatter</span> = <span class="php-keyword1">null</span>;
</span><span id="142" class="l"><a href="#142">142: </a>
</span><span id="143" class="l"><a href="#143">143: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="144" class="l"><a href="#144">144: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_setFormatterOpts</span> = <span class="php-keyword1">null</span>;
</span><span id="145" class="l"><a href="#145">145: </a>
</span><span id="146" class="l"><a href="#146">146: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="147" class="l"><a href="#147">147: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_setValue</span> = <span class="php-keyword1">null</span>;
</span><span id="148" class="l"><a href="#148">148: </a>
</span><span id="149" class="l"><a href="#149">149: </a>    <span class="php-comment">/** @var mixed */</span>
</span><span id="150" class="l"><a href="#150">150: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_validator</span> = <span class="php-keyword1">array</span>();
</span><span id="151" class="l"><a href="#151">151: </a>
</span><span id="152" class="l"><a href="#152">152: </a>    <span class="php-comment">/** @var Upload */</span>
</span><span id="153" class="l"><a href="#153">153: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_upload</span> = <span class="php-keyword1">null</span>;
</span><span id="154" class="l"><a href="#154">154: </a>
</span><span id="155" class="l"><a href="#155">155: </a>    <span class="php-comment">/** @var callable */</span>
</span><span id="156" class="l"><a href="#156">156: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_xss</span> = <span class="php-keyword1">null</span>;
</span><span id="157" class="l"><a href="#157">157: </a>
</span><span id="158" class="l"><a href="#158">158: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="159" class="l"><a href="#159">159: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_xssFormat</span> = <span class="php-keyword1">true</span>;
</span><span id="160" class="l"><a href="#160">160: </a>
</span><span id="161" class="l"><a href="#161">161: </a>
</span><span id="162" class="l"><a href="#162">162: </a>
</span><span id="163" class="l"><a href="#163">163: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="164" class="l"><a href="#164">164: </a><span class="php-comment">     * Public methods
</span></span><span id="165" class="l"><a href="#165">165: </a><span class="php-comment">     */</span>
</span><span id="166" class="l"><a href="#166">166: </a>
</span><span id="167" class="l"><a href="#167">167: </a>
</span><span id="168" class="l"><a href="#168">168: </a>    <span class="php-comment">/**
</span></span><span id="169" class="l"><a href="#169">169: </a><span class="php-comment">     * Get / set the DB field name.
</span></span><span id="170" class="l"><a href="#170">170: </a><span class="php-comment">     * 
</span></span><span id="171" class="l"><a href="#171">171: </a><span class="php-comment">     * Note that when used as a setter, an alias can be given for the field
</span></span><span id="172" class="l"><a href="#172">172: </a><span class="php-comment">     * using the SQL `as` keyword - for example: `firstName as name`. In this
</span></span><span id="173" class="l"><a href="#173">173: </a><span class="php-comment">     * situation the dbField is set to the field name before the `as`, and the
</span></span><span id="174" class="l"><a href="#174">174: </a><span class="php-comment">     * field's name (`name()`) is set to the name after the ` as `.
</span></span><span id="175" class="l"><a href="#175">175: </a><span class="php-comment">     *
</span></span><span id="176" class="l"><a href="#176">176: </a><span class="php-comment">     * As a result of this, the following constructs have identical
</span></span><span id="177" class="l"><a href="#177">177: </a><span class="php-comment">     * functionality:
</span></span><span id="178" class="l"><a href="#178">178: </a><span class="php-comment">     *
</span></span><span id="179" class="l"><a href="#179">179: </a><span class="php-comment">     *    Field::inst( 'firstName as name' );
</span></span><span id="180" class="l"><a href="#180">180: </a><span class="php-comment">     *    Field::inst( 'firstName', 'name' );
</span></span><span id="181" class="l"><a href="#181">181: </a><span class="php-comment">     *
</span></span><span id="182" class="l"><a href="#182">182: </a><span class="php-comment">     *  @param string $_ Value to set if using as a setter.
</span></span><span id="183" class="l"><a href="#183">183: </a><span class="php-comment">     *  @return string|self The name of the db field if no parameter is given,
</span></span><span id="184" class="l"><a href="#184">184: </a><span class="php-comment">     *    or self if used as a setter.
</span></span><span id="185" class="l"><a href="#185">185: </a><span class="php-comment">     */</span>
</span><span id="186" class="l"><a href="#186">186: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> dbField ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="187" class="l"><a href="#187">187: </a>    {
</span><span id="188" class="l"><a href="#188">188: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> === <span class="php-keyword1">null</span> ) {
</span><span id="189" class="l"><a href="#189">189: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_dbField;
</span><span id="190" class="l"><a href="#190">190: </a>        }
</span><span id="191" class="l"><a href="#191">191: </a>
</span><span id="192" class="l"><a href="#192">192: </a>        <span class="php-comment">// Don't split on an `as` inside paraenthesis</span>
</span><span id="193" class="l"><a href="#193">193: </a>        <span class="php-var">$a</span> = <span class="php-keyword2">preg_split</span>( <span class="php-quote">'/ as (?![^\(]*\))/i'</span>, <span class="php-var">$_</span> );
</span><span id="194" class="l"><a href="#194">194: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$a</span>) &gt; <span class="php-num">1</span> ) {
</span><span id="195" class="l"><a href="#195">195: </a>            <span class="php-var">$this</span>-&gt;_dbField = <span class="php-keyword2">trim</span>( <span class="php-var">$a</span>[<span class="php-num">0</span>] );
</span><span id="196" class="l"><a href="#196">196: </a>            <span class="php-var">$this</span>-&gt;_name = <span class="php-keyword2">trim</span>( <span class="php-var">$a</span>[<span class="php-num">1</span>] );
</span><span id="197" class="l"><a href="#197">197: </a>        }
</span><span id="198" class="l"><a href="#198">198: </a>        <span class="php-keyword1">else</span> {
</span><span id="199" class="l"><a href="#199">199: </a>            <span class="php-var">$this</span>-&gt;_dbField = <span class="php-var">$_</span>;
</span><span id="200" class="l"><a href="#200">200: </a>        }
</span><span id="201" class="l"><a href="#201">201: </a>
</span><span id="202" class="l"><a href="#202">202: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="203" class="l"><a href="#203">203: </a>    }
</span><span id="204" class="l"><a href="#204">204: </a>
</span><span id="205" class="l"><a href="#205">205: </a>
</span><span id="206" class="l"><a href="#206">206: </a>    <span class="php-comment">/**
</span></span><span id="207" class="l"><a href="#207">207: </a><span class="php-comment">     * Get / set the 'get' property of the field.
</span></span><span id="208" class="l"><a href="#208">208: </a><span class="php-comment">     *
</span></span><span id="209" class="l"><a href="#209">209: </a><span class="php-comment">     * A field can be marked as write only when setting the get property to false
</span></span><span id="210" class="l"><a href="#210">210: </a><span class="php-comment">     * here.
</span></span><span id="211" class="l"><a href="#211">211: </a><span class="php-comment">     *  @param boolean $_ Value to set if using as a setter.
</span></span><span id="212" class="l"><a href="#212">212: </a><span class="php-comment">     *  @return boolean|self The get property if no parameter is given, or self
</span></span><span id="213" class="l"><a href="#213">213: </a><span class="php-comment">     *    if used as a setter.
</span></span><span id="214" class="l"><a href="#214">214: </a><span class="php-comment">     */</span>
</span><span id="215" class="l"><a href="#215">215: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> get ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="216" class="l"><a href="#216">216: </a>    {
</span><span id="217" class="l"><a href="#217">217: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_get, <span class="php-var">$_</span> );
</span><span id="218" class="l"><a href="#218">218: </a>    }
</span><span id="219" class="l"><a href="#219">219: </a>
</span><span id="220" class="l"><a href="#220">220: </a>
</span><span id="221" class="l"><a href="#221">221: </a>    <span class="php-comment">/**
</span></span><span id="222" class="l"><a href="#222">222: </a><span class="php-comment">     * Get formatter for the field's data.
</span></span><span id="223" class="l"><a href="#223">223: </a><span class="php-comment">     *
</span></span><span id="224" class="l"><a href="#224">224: </a><span class="php-comment">     * When the data has been retrieved from the server, it can be passed through
</span></span><span id="225" class="l"><a href="#225">225: </a><span class="php-comment">     * a formatter here, which will manipulate (format) the data as required. This
</span></span><span id="226" class="l"><a href="#226">226: </a><span class="php-comment">     * can be useful when, for example, working with dates and a particular format
</span></span><span id="227" class="l"><a href="#227">227: </a><span class="php-comment">     * is required on the client-side.
</span></span><span id="228" class="l"><a href="#228">228: </a><span class="php-comment">     *
</span></span><span id="229" class="l"><a href="#229">229: </a><span class="php-comment">     * Editor has a number of formatters available with the {@link Format} class
</span></span><span id="230" class="l"><a href="#230">230: </a><span class="php-comment">     * which can be used directly with this method.
</span></span><span id="231" class="l"><a href="#231">231: </a><span class="php-comment">     *  @param callable|string $_ Value to set if using as a setter. Can be given as
</span></span><span id="232" class="l"><a href="#232">232: </a><span class="php-comment">     *    a closure function or a string with a reference to a function that will
</span></span><span id="233" class="l"><a href="#233">233: </a><span class="php-comment">     *    be called with call_user_func().
</span></span><span id="234" class="l"><a href="#234">234: </a><span class="php-comment">     *  @param mixed $opts Variable that is passed through to the get formatting
</span></span><span id="235" class="l"><a href="#235">235: </a><span class="php-comment">     *    function - can be useful for passing through extra information such as
</span></span><span id="236" class="l"><a href="#236">236: </a><span class="php-comment">     *    date formatting string, or a required flag. The actual options available
</span></span><span id="237" class="l"><a href="#237">237: </a><span class="php-comment">     *    depend upon the formatter used.
</span></span><span id="238" class="l"><a href="#238">238: </a><span class="php-comment">     *  @return callable|string|self The get formatter if no parameter is given, or
</span></span><span id="239" class="l"><a href="#239">239: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="240" class="l"><a href="#240">240: </a><span class="php-comment">     */</span>
</span><span id="241" class="l"><a href="#241">241: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> getFormatter ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span>, <span class="php-var">$opts</span>=<span class="php-keyword1">null</span> )
</span><span id="242" class="l"><a href="#242">242: </a>    {
</span><span id="243" class="l"><a href="#243">243: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="244" class="l"><a href="#244">244: </a>            <span class="php-var">$this</span>-&gt;_getFormatterOpts = <span class="php-var">$opts</span>;
</span><span id="245" class="l"><a href="#245">245: </a>        }
</span><span id="246" class="l"><a href="#246">246: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_getFormatter, <span class="php-var">$_</span> );
</span><span id="247" class="l"><a href="#247">247: </a>    }
</span><span id="248" class="l"><a href="#248">248: </a>
</span><span id="249" class="l"><a href="#249">249: </a>
</span><span id="250" class="l"><a href="#250">250: </a>    <span class="php-comment">/**
</span></span><span id="251" class="l"><a href="#251">251: </a><span class="php-comment">     * Get / set a get value. If given, then this value is used to send to the
</span></span><span id="252" class="l"><a href="#252">252: </a><span class="php-comment">     * client-side, regardless of what value is held by the database.
</span></span><span id="253" class="l"><a href="#253">253: </a><span class="php-comment">     * 
</span></span><span id="254" class="l"><a href="#254">254: </a><span class="php-comment">     * @param callable|string|number $_ Value to set, or no value to use as a
</span></span><span id="255" class="l"><a href="#255">255: </a><span class="php-comment">     *     getter
</span></span><span id="256" class="l"><a href="#256">256: </a><span class="php-comment">     * @return callable|string|self Value if used as a getter, or self if used
</span></span><span id="257" class="l"><a href="#257">257: </a><span class="php-comment">     *     as a setter.
</span></span><span id="258" class="l"><a href="#258">258: </a><span class="php-comment">     */</span>
</span><span id="259" class="l"><a href="#259">259: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> getValue ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="260" class="l"><a href="#260">260: </a>    {
</span><span id="261" class="l"><a href="#261">261: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_getValue, <span class="php-var">$_</span> );
</span><span id="262" class="l"><a href="#262">262: </a>    }
</span><span id="263" class="l"><a href="#263">263: </a>
</span><span id="264" class="l"><a href="#264">264: </a>
</span><span id="265" class="l"><a href="#265">265: </a>    <span class="php-comment">/**
</span></span><span id="266" class="l"><a href="#266">266: </a><span class="php-comment">     * Get / set the 'name' property of the field.
</span></span><span id="267" class="l"><a href="#267">267: </a><span class="php-comment">     *
</span></span><span id="268" class="l"><a href="#268">268: </a><span class="php-comment">     * The name is typically the same as the dbField name, since it makes things
</span></span><span id="269" class="l"><a href="#269">269: </a><span class="php-comment">     * less confusing(!), but it is possible to set a different name for the data
</span></span><span id="270" class="l"><a href="#270">270: </a><span class="php-comment">     * which is used in the JSON returned to DataTables in a 'get' operation and
</span></span><span id="271" class="l"><a href="#271">271: </a><span class="php-comment">     * the field name used in a 'set' operation.
</span></span><span id="272" class="l"><a href="#272">272: </a><span class="php-comment">     *  @param string $_ Value to set if using as a setter.
</span></span><span id="273" class="l"><a href="#273">273: </a><span class="php-comment">     *  @return string|self The name property if no parameter is given, or self
</span></span><span id="274" class="l"><a href="#274">274: </a><span class="php-comment">     *    if used as a setter.
</span></span><span id="275" class="l"><a href="#275">275: </a><span class="php-comment">     */</span>
</span><span id="276" class="l"><a href="#276">276: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> name ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="277" class="l"><a href="#277">277: </a>    {
</span><span id="278" class="l"><a href="#278">278: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_name, <span class="php-var">$_</span> );
</span><span id="279" class="l"><a href="#279">279: </a>    }
</span><span id="280" class="l"><a href="#280">280: </a>
</span><span id="281" class="l"><a href="#281">281: </a>
</span><span id="282" class="l"><a href="#282">282: </a>    <span class="php-comment">/**
</span></span><span id="283" class="l"><a href="#283">283: </a><span class="php-comment">     * Get a list of values that can be used for the options list in radio,
</span></span><span id="284" class="l"><a href="#284">284: </a><span class="php-comment">     * select and checkbox inputs from the database for this field.
</span></span><span id="285" class="l"><a href="#285">285: </a><span class="php-comment">     *
</span></span><span id="286" class="l"><a href="#286">286: </a><span class="php-comment">     * Note that this is for simple 'label / value' pairs only. For more complex
</span></span><span id="287" class="l"><a href="#287">287: </a><span class="php-comment">     * data, including pairs that require joins and where conditions, use a
</span></span><span id="288" class="l"><a href="#288">288: </a><span class="php-comment">     * closure to provide a query
</span></span><span id="289" class="l"><a href="#289">289: </a><span class="php-comment">     *
</span></span><span id="290" class="l"><a href="#290">290: </a><span class="php-comment">     * @param  string|callable $table Database table name to use to get the
</span></span><span id="291" class="l"><a href="#291">291: </a><span class="php-comment">     *     paired data from, or a closure function if providing a method
</span></span><span id="292" class="l"><a href="#292">292: </a><span class="php-comment">     * @param  string          $value Table column name that contains the pair's
</span></span><span id="293" class="l"><a href="#293">293: </a><span class="php-comment">     *     value. Not used if the first parameter is given as a closure
</span></span><span id="294" class="l"><a href="#294">294: </a><span class="php-comment">     * @param  string          $label Table column name that contains the pair's
</span></span><span id="295" class="l"><a href="#295">295: </a><span class="php-comment">     *     label. Not used if the first parameter is given as a closure
</span></span><span id="296" class="l"><a href="#296">296: </a><span class="php-comment">     * @param  callable        $condition Function that will add `where`
</span></span><span id="297" class="l"><a href="#297">297: </a><span class="php-comment">     *     conditions to the query
</span></span><span id="298" class="l"><a href="#298">298: </a><span class="php-comment">     * @param  callable        $format Function will render each label
</span></span><span id="299" class="l"><a href="#299">299: </a><span class="php-comment">     * @param  string          $order SQL ordering
</span></span><span id="300" class="l"><a href="#300">300: </a><span class="php-comment">     * @return Field                  Self for chaining
</span></span><span id="301" class="l"><a href="#301">301: </a><span class="php-comment">     */</span>
</span><span id="302" class="l"><a href="#302">302: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> options ( <span class="php-var">$table</span>=<span class="php-keyword1">null</span>, <span class="php-var">$value</span>=<span class="php-keyword1">null</span>, <span class="php-var">$label</span>=<span class="php-keyword1">null</span>, <span class="php-var">$condition</span>=<span class="php-keyword1">null</span>, <span class="php-var">$format</span>=<span class="php-keyword1">null</span>, <span class="php-var">$order</span>=<span class="php-keyword1">null</span> )
</span><span id="303" class="l"><a href="#303">303: </a>    {
</span><span id="304" class="l"><a href="#304">304: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$table</span> === <span class="php-keyword1">null</span> ) {
</span><span id="305" class="l"><a href="#305">305: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_opts;
</span><span id="306" class="l"><a href="#306">306: </a>        }
</span><span id="307" class="l"><a href="#307">307: </a>
</span><span id="308" class="l"><a href="#308">308: </a>        <span class="php-comment">// Overloads for backwards compatibility</span>
</span><span id="309" class="l"><a href="#309">309: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_a</span>( <span class="php-var">$table</span>, <span class="php-quote">'\DataTables\Editor\Options'</span> ) ) {
</span><span id="310" class="l"><a href="#310">310: </a>            <span class="php-comment">// Options class</span>
</span><span id="311" class="l"><a href="#311">311: </a>            <span class="php-var">$this</span>-&gt;_optsFn = <span class="php-keyword1">null</span>;
</span><span id="312" class="l"><a href="#312">312: </a>            <span class="php-var">$this</span>-&gt;_opts = <span class="php-var">$table</span>;
</span><span id="313" class="l"><a href="#313">313: </a>        }
</span><span id="314" class="l"><a href="#314">314: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>(<span class="php-var">$table</span>) &amp;&amp; <span class="php-keyword2">is_object</span>(<span class="php-var">$table</span>) ) {
</span><span id="315" class="l"><a href="#315">315: </a>            <span class="php-comment">// Function</span>
</span><span id="316" class="l"><a href="#316">316: </a>            <span class="php-var">$this</span>-&gt;_opts = <span class="php-keyword1">null</span>;
</span><span id="317" class="l"><a href="#317">317: </a>            <span class="php-var">$this</span>-&gt;_optsFn = <span class="php-var">$table</span>;
</span><span id="318" class="l"><a href="#318">318: </a>        }
</span><span id="319" class="l"><a href="#319">319: </a>        <span class="php-keyword1">else</span> {
</span><span id="320" class="l"><a href="#320">320: </a>            <span class="php-var">$this</span>-&gt;_optsFn = <span class="php-keyword1">null</span>;
</span><span id="321" class="l"><a href="#321">321: </a>            <span class="php-var">$this</span>-&gt;_opts = Options::inst()
</span><span id="322" class="l"><a href="#322">322: </a>                -&gt;table( <span class="php-var">$table</span> )
</span><span id="323" class="l"><a href="#323">323: </a>                -&gt;value( <span class="php-var">$value</span> )
</span><span id="324" class="l"><a href="#324">324: </a>                -&gt;label( <span class="php-var">$label</span> );
</span><span id="325" class="l"><a href="#325">325: </a>
</span><span id="326" class="l"><a href="#326">326: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$condition</span> ) {
</span><span id="327" class="l"><a href="#327">327: </a>                <span class="php-var">$this</span>-&gt;_opts-&gt;where( <span class="php-var">$condition</span> );
</span><span id="328" class="l"><a href="#328">328: </a>            }
</span><span id="329" class="l"><a href="#329">329: </a>
</span><span id="330" class="l"><a href="#330">330: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$format</span> ) {
</span><span id="331" class="l"><a href="#331">331: </a>                <span class="php-var">$this</span>-&gt;_opts-&gt;render( <span class="php-var">$format</span> );
</span><span id="332" class="l"><a href="#332">332: </a>            }
</span><span id="333" class="l"><a href="#333">333: </a>
</span><span id="334" class="l"><a href="#334">334: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$order</span> ) {
</span><span id="335" class="l"><a href="#335">335: </a>                <span class="php-var">$this</span>-&gt;_opts-&gt;order( <span class="php-var">$order</span> );
</span><span id="336" class="l"><a href="#336">336: </a>            }
</span><span id="337" class="l"><a href="#337">337: </a>        }
</span><span id="338" class="l"><a href="#338">338: </a>
</span><span id="339" class="l"><a href="#339">339: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="340" class="l"><a href="#340">340: </a>    }
</span><span id="341" class="l"><a href="#341">341: </a>
</span><span id="342" class="l"><a href="#342">342: </a>
</span><span id="343" class="l"><a href="#343">343: </a>    <span class="php-comment">/**
</span></span><span id="344" class="l"><a href="#344">344: </a><span class="php-comment">     * Get / set the 'set' property of the field.
</span></span><span id="345" class="l"><a href="#345">345: </a><span class="php-comment">     *
</span></span><span id="346" class="l"><a href="#346">346: </a><span class="php-comment">     * A field can be marked as read only using this option, to be set only
</span></span><span id="347" class="l"><a href="#347">347: </a><span class="php-comment">     * during an create or edit action or to be set during both actions. This
</span></span><span id="348" class="l"><a href="#348">348: </a><span class="php-comment">     * provides the ability to have fields that are only set when a new row is
</span></span><span id="349" class="l"><a href="#349">349: </a><span class="php-comment">     * created (for example a &quot;created&quot; time stamp).
</span></span><span id="350" class="l"><a href="#350">350: </a><span class="php-comment">     *  @param string|boolean $_ Value to set when the method is being used as a
</span></span><span id="351" class="l"><a href="#351">351: </a><span class="php-comment">     *    setter (leave as undefined to use as a getter). This can take the
</span></span><span id="352" class="l"><a href="#352">352: </a><span class="php-comment">     *    value of:
</span></span><span id="353" class="l"><a href="#353">353: </a><span class="php-comment">     *    
</span></span><span id="354" class="l"><a href="#354">354: </a><span class="php-comment">     *    * `true`              - Same as `Field::SET_BOTH`
</span></span><span id="355" class="l"><a href="#355">355: </a><span class="php-comment">     *    * `false`             - Same as `Field::SET_NONE`
</span></span><span id="356" class="l"><a href="#356">356: </a><span class="php-comment">     *    * `Field::SET_BOTH`   - Set the database value on both create and edit commands
</span></span><span id="357" class="l"><a href="#357">357: </a><span class="php-comment">     *    * `Field::SET_NONE`   - Never set the database value
</span></span><span id="358" class="l"><a href="#358">358: </a><span class="php-comment">     *    * `Field::SET_CREATE` - Set the database value only on create
</span></span><span id="359" class="l"><a href="#359">359: </a><span class="php-comment">     *    * `Field::SET_EDIT`   - Set the database value only on edit
</span></span><span id="360" class="l"><a href="#360">360: </a><span class="php-comment">     *  @return string|self The set property if no parameter is given, or self
</span></span><span id="361" class="l"><a href="#361">361: </a><span class="php-comment">     *    if used as a setter.
</span></span><span id="362" class="l"><a href="#362">362: </a><span class="php-comment">     */</span>
</span><span id="363" class="l"><a href="#363">363: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> set ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="364" class="l"><a href="#364">364: </a>    {
</span><span id="365" class="l"><a href="#365">365: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> === <span class="php-keyword1">true</span> ) {
</span><span id="366" class="l"><a href="#366">366: </a>            <span class="php-var">$_</span> = Field::SET_BOTH;
</span><span id="367" class="l"><a href="#367">367: </a>        }
</span><span id="368" class="l"><a href="#368">368: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> === <span class="php-keyword1">false</span> ) {
</span><span id="369" class="l"><a href="#369">369: </a>            <span class="php-var">$_</span> = Field::SET_NONE;
</span><span id="370" class="l"><a href="#370">370: </a>        }
</span><span id="371" class="l"><a href="#371">371: </a>
</span><span id="372" class="l"><a href="#372">372: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_set, <span class="php-var">$_</span> );
</span><span id="373" class="l"><a href="#373">373: </a>    }
</span><span id="374" class="l"><a href="#374">374: </a>
</span><span id="375" class="l"><a href="#375">375: </a>
</span><span id="376" class="l"><a href="#376">376: </a>    <span class="php-comment">/**
</span></span><span id="377" class="l"><a href="#377">377: </a><span class="php-comment">     * Set formatter for the field's data.
</span></span><span id="378" class="l"><a href="#378">378: </a><span class="php-comment">     *
</span></span><span id="379" class="l"><a href="#379">379: </a><span class="php-comment">     * When the data has been retrieved from the server, it can be passed through
</span></span><span id="380" class="l"><a href="#380">380: </a><span class="php-comment">     * a formatter here, which will manipulate (format) the data as required. This
</span></span><span id="381" class="l"><a href="#381">381: </a><span class="php-comment">     * can be useful when, for example, working with dates and a particular format
</span></span><span id="382" class="l"><a href="#382">382: </a><span class="php-comment">     * is required on the client-side.
</span></span><span id="383" class="l"><a href="#383">383: </a><span class="php-comment">     *
</span></span><span id="384" class="l"><a href="#384">384: </a><span class="php-comment">     * Editor has a number of formatters available with the {@link Format} class
</span></span><span id="385" class="l"><a href="#385">385: </a><span class="php-comment">     * which can be used directly with this method.
</span></span><span id="386" class="l"><a href="#386">386: </a><span class="php-comment">     *  @param callable|string $_ Value to set if using as a setter. Can be given as
</span></span><span id="387" class="l"><a href="#387">387: </a><span class="php-comment">     *    a closure function or a string with a reference to a function that will
</span></span><span id="388" class="l"><a href="#388">388: </a><span class="php-comment">     *    be called with call_user_func().
</span></span><span id="389" class="l"><a href="#389">389: </a><span class="php-comment">     *  @param mixed $opts Variable that is passed through to the get formatting
</span></span><span id="390" class="l"><a href="#390">390: </a><span class="php-comment">     *    function - can be useful for passing through extra information such as
</span></span><span id="391" class="l"><a href="#391">391: </a><span class="php-comment">     *    date formatting string, or a required flag. The actual options available
</span></span><span id="392" class="l"><a href="#392">392: </a><span class="php-comment">     *    depend upon the formatter used.
</span></span><span id="393" class="l"><a href="#393">393: </a><span class="php-comment">     *  @return callable|string|self The set formatter if no parameter is given, or
</span></span><span id="394" class="l"><a href="#394">394: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="395" class="l"><a href="#395">395: </a><span class="php-comment">     */</span>
</span><span id="396" class="l"><a href="#396">396: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> setFormatter ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span>, <span class="php-var">$opts</span>=<span class="php-keyword1">null</span> )
</span><span id="397" class="l"><a href="#397">397: </a>    {
</span><span id="398" class="l"><a href="#398">398: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="399" class="l"><a href="#399">399: </a>            <span class="php-var">$this</span>-&gt;_setFormatterOpts = <span class="php-var">$opts</span>;
</span><span id="400" class="l"><a href="#400">400: </a>        }
</span><span id="401" class="l"><a href="#401">401: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_setFormatter, <span class="php-var">$_</span> );
</span><span id="402" class="l"><a href="#402">402: </a>    }
</span><span id="403" class="l"><a href="#403">403: </a>
</span><span id="404" class="l"><a href="#404">404: </a>
</span><span id="405" class="l"><a href="#405">405: </a>    <span class="php-comment">/**
</span></span><span id="406" class="l"><a href="#406">406: </a><span class="php-comment">     * Get / set a set value. If given, then this value is used to write to the
</span></span><span id="407" class="l"><a href="#407">407: </a><span class="php-comment">     * database regardless of what data is sent from the client-side.
</span></span><span id="408" class="l"><a href="#408">408: </a><span class="php-comment">     * 
</span></span><span id="409" class="l"><a href="#409">409: </a><span class="php-comment">     * @param callable|string|number $_ Value to set, or no value to use as a
</span></span><span id="410" class="l"><a href="#410">410: </a><span class="php-comment">     *     getter
</span></span><span id="411" class="l"><a href="#411">411: </a><span class="php-comment">     * @return callable|string|self Value if used as a getter, or self if used
</span></span><span id="412" class="l"><a href="#412">412: </a><span class="php-comment">     *     as a setter.
</span></span><span id="413" class="l"><a href="#413">413: </a><span class="php-comment">     */</span>
</span><span id="414" class="l"><a href="#414">414: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> setValue ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="415" class="l"><a href="#415">415: </a>    {
</span><span id="416" class="l"><a href="#416">416: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_setValue, <span class="php-var">$_</span> );
</span><span id="417" class="l"><a href="#417">417: </a>    }
</span><span id="418" class="l"><a href="#418">418: </a>
</span><span id="419" class="l"><a href="#419">419: </a>
</span><span id="420" class="l"><a href="#420">420: </a>    <span class="php-comment">/**
</span></span><span id="421" class="l"><a href="#421">421: </a><span class="php-comment">     * Get / set the upload class for this field.
</span></span><span id="422" class="l"><a href="#422">422: </a><span class="php-comment">     * @param  Upload $_ Upload class if used as a setter
</span></span><span id="423" class="l"><a href="#423">423: </a><span class="php-comment">     * @return Upload|self Value if used as a getter, or self if used
</span></span><span id="424" class="l"><a href="#424">424: </a><span class="php-comment">     *     as a setter.
</span></span><span id="425" class="l"><a href="#425">425: </a><span class="php-comment">     */</span>
</span><span id="426" class="l"><a href="#426">426: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> upload ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="427" class="l"><a href="#427">427: </a>    {
</span><span id="428" class="l"><a href="#428">428: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_upload, <span class="php-var">$_</span> );
</span><span id="429" class="l"><a href="#429">429: </a>    }
</span><span id="430" class="l"><a href="#430">430: </a>
</span><span id="431" class="l"><a href="#431">431: </a>
</span><span id="432" class="l"><a href="#432">432: </a>    <span class="php-comment">/**
</span></span><span id="433" class="l"><a href="#433">433: </a><span class="php-comment">     * Get / set the 'validator' of the field.
</span></span><span id="434" class="l"><a href="#434">434: </a><span class="php-comment">     *
</span></span><span id="435" class="l"><a href="#435">435: </a><span class="php-comment">     * The validator can be used to check if any abstract piece of data is valid
</span></span><span id="436" class="l"><a href="#436">436: </a><span class="php-comment">     * or not according to the given rules of the validation function used.
</span></span><span id="437" class="l"><a href="#437">437: </a><span class="php-comment">     *
</span></span><span id="438" class="l"><a href="#438">438: </a><span class="php-comment">     * Multiple validation options can be applied to a field instance by calling
</span></span><span id="439" class="l"><a href="#439">439: </a><span class="php-comment">     * this method multiple times. For example, it would be possible to have a
</span></span><span id="440" class="l"><a href="#440">440: </a><span class="php-comment">     * 'required' validation and a 'maxLength' validation with multiple calls.
</span></span><span id="441" class="l"><a href="#441">441: </a><span class="php-comment">     * 
</span></span><span id="442" class="l"><a href="#442">442: </a><span class="php-comment">     * Editor has a number of validation available with the {@link Validate} class
</span></span><span id="443" class="l"><a href="#443">443: </a><span class="php-comment">     * which can be used directly with this method.
</span></span><span id="444" class="l"><a href="#444">444: </a><span class="php-comment">     *  @param callable|string $_ Value to set if using as the validation method.
</span></span><span id="445" class="l"><a href="#445">445: </a><span class="php-comment">     *    Can be given as a closure function or a string with a reference to a 
</span></span><span id="446" class="l"><a href="#446">446: </a><span class="php-comment">     *    function that will be called with call_user_func().
</span></span><span id="447" class="l"><a href="#447">447: </a><span class="php-comment">     *  @param mixed $opts Variable that is passed through to the validation
</span></span><span id="448" class="l"><a href="#448">448: </a><span class="php-comment">     *    function - can be useful for passing through extra information such as
</span></span><span id="449" class="l"><a href="#449">449: </a><span class="php-comment">     *    date formatting string, or a required flag. The actual options available
</span></span><span id="450" class="l"><a href="#450">450: </a><span class="php-comment">     *    depend upon the validation function used.
</span></span><span id="451" class="l"><a href="#451">451: </a><span class="php-comment">     *  @return callable|string|self The validation method if no parameter is given,
</span></span><span id="452" class="l"><a href="#452">452: </a><span class="php-comment">     *    or self if used as a setter.
</span></span><span id="453" class="l"><a href="#453">453: </a><span class="php-comment">     */</span>
</span><span id="454" class="l"><a href="#454">454: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validator ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span>, <span class="php-var">$opts</span>=<span class="php-keyword1">null</span> )
</span><span id="455" class="l"><a href="#455">455: </a>    {
</span><span id="456" class="l"><a href="#456">456: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> === <span class="php-keyword1">null</span> ) {
</span><span id="457" class="l"><a href="#457">457: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_validator;
</span><span id="458" class="l"><a href="#458">458: </a>        }
</span><span id="459" class="l"><a href="#459">459: </a>        <span class="php-keyword1">else</span> {
</span><span id="460" class="l"><a href="#460">460: </a>            <span class="php-var">$this</span>-&gt;_validator[] = <span class="php-keyword1">array</span>(
</span><span id="461" class="l"><a href="#461">461: </a>                <span class="php-quote">&quot;func&quot;</span> =&gt; <span class="php-var">$_</span>,
</span><span id="462" class="l"><a href="#462">462: </a>                <span class="php-quote">&quot;opts&quot;</span> =&gt; <span class="php-var">$opts</span>
</span><span id="463" class="l"><a href="#463">463: </a>            );
</span><span id="464" class="l"><a href="#464">464: </a>        }
</span><span id="465" class="l"><a href="#465">465: </a>
</span><span id="466" class="l"><a href="#466">466: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="467" class="l"><a href="#467">467: </a>    }
</span><span id="468" class="l"><a href="#468">468: </a>
</span><span id="469" class="l"><a href="#469">469: </a>
</span><span id="470" class="l"><a href="#470">470: </a>    <span class="php-comment">/**
</span></span><span id="471" class="l"><a href="#471">471: </a><span class="php-comment">     * Set a formatting method that will be used for XSS checking / removal.
</span></span><span id="472" class="l"><a href="#472">472: </a><span class="php-comment">     * This should be a function that takes a single argument (the value to be
</span></span><span id="473" class="l"><a href="#473">473: </a><span class="php-comment">     * cleaned) and returns the cleaned value.
</span></span><span id="474" class="l"><a href="#474">474: </a><span class="php-comment">     *
</span></span><span id="475" class="l"><a href="#475">475: </a><span class="php-comment">     * Editor will use HtmLawed by default for this operation, which is built
</span></span><span id="476" class="l"><a href="#476">476: </a><span class="php-comment">     * into the software and no additional configuration is required, but a
</span></span><span id="477" class="l"><a href="#477">477: </a><span class="php-comment">     * custom function can be used if you wish to use a different formatter such
</span></span><span id="478" class="l"><a href="#478">478: </a><span class="php-comment">     * as HTMLPurifier.
</span></span><span id="479" class="l"><a href="#479">479: </a><span class="php-comment">     *
</span></span><span id="480" class="l"><a href="#480">480: </a><span class="php-comment">     * If you wish to disable this option (which you would only do if you are
</span></span><span id="481" class="l"><a href="#481">481: </a><span class="php-comment">     * absolutely confident that your validation will pick up on any XSS inputs)
</span></span><span id="482" class="l"><a href="#482">482: </a><span class="php-comment">     * simply provide a closure function that returns the value given to the
</span></span><span id="483" class="l"><a href="#483">483: </a><span class="php-comment">     * function. This is _not_ recommended.
</span></span><span id="484" class="l"><a href="#484">484: </a><span class="php-comment">     *
</span></span><span id="485" class="l"><a href="#485">485: </a><span class="php-comment">     * @param  callable|false $xssFormatter XSS cleaner function, use `false` or
</span></span><span id="486" class="l"><a href="#486">486: </a><span class="php-comment">     *   `null` to disable XSS cleaning.
</span></span><span id="487" class="l"><a href="#487">487: </a><span class="php-comment">     * @return Field                        Self for chaining.
</span></span><span id="488" class="l"><a href="#488">488: </a><span class="php-comment">     */</span>
</span><span id="489" class="l"><a href="#489">489: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> xss ( <span class="php-var">$xssFormatter</span> )
</span><span id="490" class="l"><a href="#490">490: </a>    {
</span><span id="491" class="l"><a href="#491">491: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$xssFormatter</span> === <span class="php-keyword1">true</span> || <span class="php-var">$xssFormatter</span> === <span class="php-keyword1">false</span> || <span class="php-var">$xssFormatter</span> === <span class="php-keyword1">null</span> ) {
</span><span id="492" class="l"><a href="#492">492: </a>            <span class="php-var">$this</span>-&gt;_xssFormat = <span class="php-var">$xssFormatter</span>;
</span><span id="493" class="l"><a href="#493">493: </a>        }
</span><span id="494" class="l"><a href="#494">494: </a>        <span class="php-keyword1">else</span> {
</span><span id="495" class="l"><a href="#495">495: </a>            <span class="php-var">$this</span>-&gt;_xss = <span class="php-var">$xssFormatter</span>;
</span><span id="496" class="l"><a href="#496">496: </a>        }
</span><span id="497" class="l"><a href="#497">497: </a>
</span><span id="498" class="l"><a href="#498">498: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="499" class="l"><a href="#499">499: </a>    }
</span><span id="500" class="l"><a href="#500">500: </a>
</span><span id="501" class="l"><a href="#501">501: </a>
</span><span id="502" class="l"><a href="#502">502: </a>
</span><span id="503" class="l"><a href="#503">503: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="504" class="l"><a href="#504">504: </a><span class="php-comment">     * Internal methods
</span></span><span id="505" class="l"><a href="#505">505: </a><span class="php-comment">     * Used by the Editor class and not generally for public use
</span></span><span id="506" class="l"><a href="#506">506: </a><span class="php-comment">     */</span>
</span><span id="507" class="l"><a href="#507">507: </a>
</span><span id="508" class="l"><a href="#508">508: </a>    <span class="php-comment">/**
</span></span><span id="509" class="l"><a href="#509">509: </a><span class="php-comment">     * Check to see if a field should be used for a particular action (get or set).
</span></span><span id="510" class="l"><a href="#510">510: </a><span class="php-comment">     *
</span></span><span id="511" class="l"><a href="#511">511: </a><span class="php-comment">     * Called by the Editor / Join class instances - not expected for general
</span></span><span id="512" class="l"><a href="#512">512: </a><span class="php-comment">     * consumption - internal.
</span></span><span id="513" class="l"><a href="#513">513: </a><span class="php-comment">     *  @param string $action Direction that the data is travelling  - 'get' is
</span></span><span id="514" class="l"><a href="#514">514: </a><span class="php-comment">     *    reading DB data, `create` and `edit` for writing to the DB
</span></span><span id="515" class="l"><a href="#515">515: </a><span class="php-comment">     *  @param array $data Data submitted from the client-side when setting.
</span></span><span id="516" class="l"><a href="#516">516: </a><span class="php-comment">     *  @return boolean true if the field should be used in the get / set.
</span></span><span id="517" class="l"><a href="#517">517: </a><span class="php-comment">     *  @internal
</span></span><span id="518" class="l"><a href="#518">518: </a><span class="php-comment">     */</span>
</span><span id="519" class="l"><a href="#519">519: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> apply ( <span class="php-var">$action</span>, <span class="php-var">$data</span>=<span class="php-keyword1">null</span> )
</span><span id="520" class="l"><a href="#520">520: </a>    {
</span><span id="521" class="l"><a href="#521">521: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$action</span> === <span class="php-quote">'get'</span> ) {
</span><span id="522" class="l"><a href="#522">522: </a>            <span class="php-comment">// Get action - can we get this field</span>
</span><span id="523" class="l"><a href="#523">523: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_get;
</span><span id="524" class="l"><a href="#524">524: </a>        }
</span><span id="525" class="l"><a href="#525">525: </a>        <span class="php-keyword1">else</span> {
</span><span id="526" class="l"><a href="#526">526: </a>            <span class="php-comment">// Note that validation must be done on input data before we get here</span>
</span><span id="527" class="l"><a href="#527">527: </a>
</span><span id="528" class="l"><a href="#528">528: </a>            <span class="php-comment">// Create or edit action, are we configured to use this field</span>
</span><span id="529" class="l"><a href="#529">529: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$action</span> === <span class="php-quote">'create'</span> &amp;&amp;
</span><span id="530" class="l"><a href="#530">530: </a>                (<span class="php-var">$this</span>-&gt;_set === Field::SET_NONE || <span class="php-var">$this</span>-&gt;_set === Field::SET_EDIT)
</span><span id="531" class="l"><a href="#531">531: </a>            ) {
</span><span id="532" class="l"><a href="#532">532: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="533" class="l"><a href="#533">533: </a>            }
</span><span id="534" class="l"><a href="#534">534: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$action</span> === <span class="php-quote">'edit'</span> &amp;&amp;
</span><span id="535" class="l"><a href="#535">535: </a>                (<span class="php-var">$this</span>-&gt;_set === Field::SET_NONE || <span class="php-var">$this</span>-&gt;_set === Field::SET_CREATE)
</span><span id="536" class="l"><a href="#536">536: </a>            ) {
</span><span id="537" class="l"><a href="#537">537: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="538" class="l"><a href="#538">538: </a>            }
</span><span id="539" class="l"><a href="#539">539: </a>
</span><span id="540" class="l"><a href="#540">540: </a>            <span class="php-comment">// Check it was in the submitted data. If not, then not required</span>
</span><span id="541" class="l"><a href="#541">541: </a>            <span class="php-comment">// (validation would have failed if it was) and therefore we don't</span>
</span><span id="542" class="l"><a href="#542">542: </a>            <span class="php-comment">// set it. Check for a value as well, as it can format data from</span>
</span><span id="543" class="l"><a href="#543">543: </a>            <span class="php-comment">// some other source</span>
</span><span id="544" class="l"><a href="#544">544: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_setValue === <span class="php-keyword1">null</span> &amp;&amp; ! <span class="php-var">$this</span>-&gt;_inData( <span class="php-var">$this</span>-&gt;name(), <span class="php-var">$data</span> ) ) {
</span><span id="545" class="l"><a href="#545">545: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="546" class="l"><a href="#546">546: </a>            }
</span><span id="547" class="l"><a href="#547">547: </a>
</span><span id="548" class="l"><a href="#548">548: </a>            <span class="php-comment">// In the data set, so use it</span>
</span><span id="549" class="l"><a href="#549">549: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="550" class="l"><a href="#550">550: </a>        }
</span><span id="551" class="l"><a href="#551">551: </a>    }
</span><span id="552" class="l"><a href="#552">552: </a>
</span><span id="553" class="l"><a href="#553">553: </a>
</span><span id="554" class="l"><a href="#554">554: </a>    <span class="php-comment">/**
</span></span><span id="555" class="l"><a href="#555">555: </a><span class="php-comment">     * Execute the ipOpts to get the list of options to return to the client-
</span></span><span id="556" class="l"><a href="#556">556: </a><span class="php-comment">     * side
</span></span><span id="557" class="l"><a href="#557">557: </a><span class="php-comment">     *
</span></span><span id="558" class="l"><a href="#558">558: </a><span class="php-comment">     * @param  \DataTables\Database $db Database instance
</span></span><span id="559" class="l"><a href="#559">559: </a><span class="php-comment">     * @return Array        Array of value / label options for the list
</span></span><span id="560" class="l"><a href="#560">560: </a><span class="php-comment">     * @internal
</span></span><span id="561" class="l"><a href="#561">561: </a><span class="php-comment">     */</span>
</span><span id="562" class="l"><a href="#562">562: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> optionsExec ( <span class="php-var">$db</span> )
</span><span id="563" class="l"><a href="#563">563: </a>    {
</span><span id="564" class="l"><a href="#564">564: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_optsFn ) {
</span><span id="565" class="l"><a href="#565">565: </a>            <span class="php-var">$fn</span> = <span class="php-var">$this</span>-&gt;_optsFn;
</span><span id="566" class="l"><a href="#566">566: </a>            <span class="php-keyword1">return</span> <span class="php-var">$fn</span>();
</span><span id="567" class="l"><a href="#567">567: </a>        }
</span><span id="568" class="l"><a href="#568">568: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_opts ) {
</span><span id="569" class="l"><a href="#569">569: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_opts-&gt;<span class="php-keyword2">exec</span>( <span class="php-var">$db</span> );
</span><span id="570" class="l"><a href="#570">570: </a>        }
</span><span id="571" class="l"><a href="#571">571: </a>
</span><span id="572" class="l"><a href="#572">572: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="573" class="l"><a href="#573">573: </a>    }
</span><span id="574" class="l"><a href="#574">574: </a>
</span><span id="575" class="l"><a href="#575">575: </a>
</span><span id="576" class="l"><a href="#576">576: </a>    <span class="php-comment">/**
</span></span><span id="577" class="l"><a href="#577">577: </a><span class="php-comment">     * Get the value of the field, taking into account if it is coming from the
</span></span><span id="578" class="l"><a href="#578">578: </a><span class="php-comment">     * DB or from a POST. If formatting has been specified for this field, it
</span></span><span id="579" class="l"><a href="#579">579: </a><span class="php-comment">     * will be applied here.
</span></span><span id="580" class="l"><a href="#580">580: </a><span class="php-comment">     *
</span></span><span id="581" class="l"><a href="#581">581: </a><span class="php-comment">     * Called by the Editor / Join class instances - not expected for general
</span></span><span id="582" class="l"><a href="#582">582: </a><span class="php-comment">     * consumption - internal.
</span></span><span id="583" class="l"><a href="#583">583: </a><span class="php-comment">     *  @param string $direction Direction that the data is travelling  - 'get' is
</span></span><span id="584" class="l"><a href="#584">584: </a><span class="php-comment">     *    reading data, and 'set' is writing it to the DB.
</span></span><span id="585" class="l"><a href="#585">585: </a><span class="php-comment">     *  @param array $data Data submitted from the client-side when setting or the
</span></span><span id="586" class="l"><a href="#586">586: </a><span class="php-comment">     *    data for the row when getting data from the DB.
</span></span><span id="587" class="l"><a href="#587">587: </a><span class="php-comment">     *  @return string Value for the field
</span></span><span id="588" class="l"><a href="#588">588: </a><span class="php-comment">     *  @internal
</span></span><span id="589" class="l"><a href="#589">589: </a><span class="php-comment">     */</span>
</span><span id="590" class="l"><a href="#590">590: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> val ( <span class="php-var">$direction</span>, <span class="php-var">$data</span> )
</span><span id="591" class="l"><a href="#591">591: </a>    {
</span><span id="592" class="l"><a href="#592">592: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$direction</span> === <span class="php-quote">'get'</span> ) {
</span><span id="593" class="l"><a href="#593">593: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_getValue !== <span class="php-keyword1">null</span> ) {
</span><span id="594" class="l"><a href="#594">594: </a>                <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;_getAssignedValue( <span class="php-var">$this</span>-&gt;_getValue );
</span><span id="595" class="l"><a href="#595">595: </a>            }
</span><span id="596" class="l"><a href="#596">596: </a>            <span class="php-keyword1">else</span> {
</span><span id="597" class="l"><a href="#597">597: </a>                <span class="php-comment">// Getting data, so the db field name</span>
</span><span id="598" class="l"><a href="#598">598: </a>                <span class="php-var">$val</span> = <span class="php-keyword1">isset</span>( <span class="php-var">$data</span>[ <span class="php-var">$this</span>-&gt;_dbField ] ) ?
</span><span id="599" class="l"><a href="#599">599: </a>                    <span class="php-var">$data</span>[ <span class="php-var">$this</span>-&gt;_dbField ] :
</span><span id="600" class="l"><a href="#600">600: </a>                    <span class="php-keyword1">null</span>;
</span><span id="601" class="l"><a href="#601">601: </a>            }
</span><span id="602" class="l"><a href="#602">602: </a>
</span><span id="603" class="l"><a href="#603">603: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_format(
</span><span id="604" class="l"><a href="#604">604: </a>                <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$this</span>-&gt;_getFormatter, <span class="php-var">$this</span>-&gt;_getFormatterOpts
</span><span id="605" class="l"><a href="#605">605: </a>            );
</span><span id="606" class="l"><a href="#606">606: </a>        }
</span><span id="607" class="l"><a href="#607">607: </a>        <span class="php-keyword1">else</span> {
</span><span id="608" class="l"><a href="#608">608: </a>            <span class="php-comment">// Sanity check that we aren't operating on a function</span>
</span><span id="609" class="l"><a href="#609">609: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$this</span>-&gt;dbField(), <span class="php-quote">'('</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="610" class="l"><a href="#610">610: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Cannot set the value for an SQL function field. These fields are read only.'</span>);
</span><span id="611" class="l"><a href="#611">611: </a>            }
</span><span id="612" class="l"><a href="#612">612: </a>
</span><span id="613" class="l"><a href="#613">613: </a>            <span class="php-comment">// Setting data, so using from the payload (POST usually) and thus</span>
</span><span id="614" class="l"><a href="#614">614: </a>            <span class="php-comment">// use the 'name'</span>
</span><span id="615" class="l"><a href="#615">615: </a>            <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;_setValue !== <span class="php-keyword1">null</span> ?
</span><span id="616" class="l"><a href="#616">616: </a>                <span class="php-var">$this</span>-&gt;_getAssignedValue( <span class="php-var">$this</span>-&gt;_setValue ) :
</span><span id="617" class="l"><a href="#617">617: </a>                <span class="php-var">$this</span>-&gt;_readProp( <span class="php-var">$this</span>-&gt;name(), <span class="php-var">$data</span> );
</span><span id="618" class="l"><a href="#618">618: </a>
</span><span id="619" class="l"><a href="#619">619: </a>            <span class="php-comment">// XSS removal / checker</span>
</span><span id="620" class="l"><a href="#620">620: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_xssFormat ) {
</span><span id="621" class="l"><a href="#621">621: </a>                <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;xssSafety( <span class="php-var">$val</span> );
</span><span id="622" class="l"><a href="#622">622: </a>            }
</span><span id="623" class="l"><a href="#623">623: </a>
</span><span id="624" class="l"><a href="#624">624: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_format(
</span><span id="625" class="l"><a href="#625">625: </a>                <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$this</span>-&gt;_setFormatter, <span class="php-var">$this</span>-&gt;_setFormatterOpts
</span><span id="626" class="l"><a href="#626">626: </a>            );
</span><span id="627" class="l"><a href="#627">627: </a>        }
</span><span id="628" class="l"><a href="#628">628: </a>    }
</span><span id="629" class="l"><a href="#629">629: </a>
</span><span id="630" class="l"><a href="#630">630: </a>
</span><span id="631" class="l"><a href="#631">631: </a>    <span class="php-comment">/**
</span></span><span id="632" class="l"><a href="#632">632: </a><span class="php-comment">     * Check the validity of the field based on the data submitted. Note that
</span></span><span id="633" class="l"><a href="#633">633: </a><span class="php-comment">     * this validation is performed on the wire data - i.e. that which is
</span></span><span id="634" class="l"><a href="#634">634: </a><span class="php-comment">     * submitted, before any setFormatter is run
</span></span><span id="635" class="l"><a href="#635">635: </a><span class="php-comment">     *
</span></span><span id="636" class="l"><a href="#636">636: </a><span class="php-comment">     * Called by the Editor / Join class instances - not expected for general
</span></span><span id="637" class="l"><a href="#637">637: </a><span class="php-comment">     * consumption - internal.
</span></span><span id="638" class="l"><a href="#638">638: </a><span class="php-comment">     *
</span></span><span id="639" class="l"><a href="#639">639: </a><span class="php-comment">     * @param array $data Data submitted from the client-side 
</span></span><span id="640" class="l"><a href="#640">640: </a><span class="php-comment">     * @param Editor $editor Editor instance
</span></span><span id="641" class="l"><a href="#641">641: </a><span class="php-comment">     * @param mixed $id Row id that is being validated
</span></span><span id="642" class="l"><a href="#642">642: </a><span class="php-comment">     * @return boolean|string `true` if valid, string with error message if not
</span></span><span id="643" class="l"><a href="#643">643: </a><span class="php-comment">     * @internal
</span></span><span id="644" class="l"><a href="#644">644: </a><span class="php-comment">     */</span>
</span><span id="645" class="l"><a href="#645">645: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validate ( <span class="php-var">$data</span>, <span class="php-var">$editor</span>, <span class="php-var">$id</span>=<span class="php-keyword1">null</span> )
</span><span id="646" class="l"><a href="#646">646: </a>    {
</span><span id="647" class="l"><a href="#647">647: </a>        <span class="php-comment">// Three cases for the validator - closure, string or null</span>
</span><span id="648" class="l"><a href="#648">648: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_validator ) ) {
</span><span id="649" class="l"><a href="#649">649: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="650" class="l"><a href="#650">650: </a>        }
</span><span id="651" class="l"><a href="#651">651: </a>
</span><span id="652" class="l"><a href="#652">652: </a>        <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;_readProp( <span class="php-var">$this</span>-&gt;name(), <span class="php-var">$data</span> );
</span><span id="653" class="l"><a href="#653">653: </a>        <span class="php-var">$processData</span> = <span class="php-var">$editor</span>-&gt;inData();
</span><span id="654" class="l"><a href="#654">654: </a>        <span class="php-var">$instances</span> = <span class="php-keyword1">array</span>(
</span><span id="655" class="l"><a href="#655">655: </a>            <span class="php-quote">'action'</span> =&gt; <span class="php-var">$processData</span>[<span class="php-quote">'action'</span>],
</span><span id="656" class="l"><a href="#656">656: </a>            <span class="php-quote">'id'</span>     =&gt; <span class="php-var">$id</span>,
</span><span id="657" class="l"><a href="#657">657: </a>            <span class="php-quote">'field'</span>  =&gt; <span class="php-var">$this</span>,
</span><span id="658" class="l"><a href="#658">658: </a>            <span class="php-quote">'editor'</span> =&gt; <span class="php-var">$editor</span>,
</span><span id="659" class="l"><a href="#659">659: </a>            <span class="php-quote">'db'</span>     =&gt; <span class="php-var">$editor</span>-&gt;db()
</span><span id="660" class="l"><a href="#660">660: </a>        );
</span><span id="661" class="l"><a href="#661">661: </a>
</span><span id="662" class="l"><a href="#662">662: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_validator ) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="663" class="l"><a href="#663">663: </a>            <span class="php-var">$validator</span> = <span class="php-var">$this</span>-&gt;_validator[<span class="php-var">$i</span>];
</span><span id="664" class="l"><a href="#664">664: </a>
</span><span id="665" class="l"><a href="#665">665: </a>            <span class="php-comment">// Backwards compatibility</span>
</span><span id="666" class="l"><a href="#666">666: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_string</span>( <span class="php-var">$validator</span>[<span class="php-quote">'func'</span>] ) ) {
</span><span id="667" class="l"><a href="#667">667: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$validator</span>[<span class="php-quote">'func'</span>], <span class="php-quote">&quot;Validate::&quot;</span>) === <span class="php-num">0</span> ) {
</span><span id="668" class="l"><a href="#668">668: </a>                    <span class="php-var">$a</span> = <span class="php-keyword2">explode</span>(<span class="php-quote">&quot;::&quot;</span>, <span class="php-var">$validator</span>[<span class="php-quote">'func'</span>]);
</span><span id="669" class="l"><a href="#669">669: </a>
</span><span id="670" class="l"><a href="#670">670: </a>                    <span class="php-comment">// Validate class static methods - they have `Legacy` counter parts that</span>
</span><span id="671" class="l"><a href="#671">671: </a>                    <span class="php-comment">// convert from the old style to the new so the old style options still work.</span>
</span><span id="672" class="l"><a href="#672">672: </a>                    <span class="php-keyword1">if</span> ( <span class="php-keyword2">method_exists</span>( <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$a</span>[<span class="php-num">0</span>], <span class="php-var">$a</span>[<span class="php-num">1</span>].<span class="php-quote">'Legacy'</span> ) ) {
</span><span id="673" class="l"><a href="#673">673: </a>                        <span class="php-var">$func</span> = <span class="php-keyword2">call_user_func</span>( <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$validator</span>[<span class="php-quote">'func'</span>].<span class="php-quote">'Legacy'</span>, <span class="php-var">$validator</span>[<span class="php-quote">'opts'</span>] );
</span><span id="674" class="l"><a href="#674">674: </a>                        <span class="php-var">$res</span> = <span class="php-keyword2">call_user_func</span>( <span class="php-var">$func</span>, <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$this</span>, <span class="php-var">$instances</span> );
</span><span id="675" class="l"><a href="#675">675: </a>                    }
</span><span id="676" class="l"><a href="#676">676: </a>                    <span class="php-keyword1">else</span> {
</span><span id="677" class="l"><a href="#677">677: </a>                        <span class="php-comment">// User style legacy function. Call it directly</span>
</span><span id="678" class="l"><a href="#678">678: </a>                        <span class="php-var">$func</span> = <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$validator</span>[<span class="php-quote">'func'</span>];
</span><span id="679" class="l"><a href="#679">679: </a>                        <span class="php-var">$res</span> = <span class="php-keyword2">call_user_func</span>( <span class="php-var">$func</span>, <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$this</span>, <span class="php-var">$instances</span> );
</span><span id="680" class="l"><a href="#680">680: </a>                    }
</span><span id="681" class="l"><a href="#681">681: </a>                }
</span><span id="682" class="l"><a href="#682">682: </a>                <span class="php-keyword1">else</span> {
</span><span id="683" class="l"><a href="#683">683: </a>                    <span class="php-comment">// And for cases where a string was used to point to a function,</span>
</span><span id="684" class="l"><a href="#684">684: </a>                    <span class="php-comment">// which was not in the Validate class</span>
</span><span id="685" class="l"><a href="#685">685: </a>                    <span class="php-var">$res</span> = <span class="php-keyword2">call_user_func</span>( <span class="php-var">$validator</span>[<span class="php-quote">'func'</span>], <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$validator</span>[<span class="php-quote">'opts'</span>], <span class="php-var">$instances</span> );
</span><span id="686" class="l"><a href="#686">686: </a>                }
</span><span id="687" class="l"><a href="#687">687: </a>            }
</span><span id="688" class="l"><a href="#688">688: </a>            <span class="php-keyword1">else</span> {
</span><span id="689" class="l"><a href="#689">689: </a>                <span class="php-var">$func</span> = <span class="php-var">$validator</span>[<span class="php-quote">'func'</span>];
</span><span id="690" class="l"><a href="#690">690: </a>                <span class="php-var">$res</span> = <span class="php-var">$func</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$this</span>, <span class="php-var">$instances</span> );
</span><span id="691" class="l"><a href="#691">691: </a>            }
</span><span id="692" class="l"><a href="#692">692: </a>
</span><span id="693" class="l"><a href="#693">693: </a>            <span class="php-comment">// Check if there was a validation error and if so, return it</span>
</span><span id="694" class="l"><a href="#694">694: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="695" class="l"><a href="#695">695: </a>                <span class="php-keyword1">return</span> <span class="php-var">$res</span>;
</span><span id="696" class="l"><a href="#696">696: </a>            }
</span><span id="697" class="l"><a href="#697">697: </a>        }
</span><span id="698" class="l"><a href="#698">698: </a>
</span><span id="699" class="l"><a href="#699">699: </a>        <span class="php-comment">// Validation methods all run, must be valid</span>
</span><span id="700" class="l"><a href="#700">700: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="701" class="l"><a href="#701">701: </a>    }
</span><span id="702" class="l"><a href="#702">702: </a>
</span><span id="703" class="l"><a href="#703">703: </a>
</span><span id="704" class="l"><a href="#704">704: </a>    <span class="php-comment">/**
</span></span><span id="705" class="l"><a href="#705">705: </a><span class="php-comment">     * Write the value for this field to the output array for a read operation
</span></span><span id="706" class="l"><a href="#706">706: </a><span class="php-comment">     *
</span></span><span id="707" class="l"><a href="#707">707: </a><span class="php-comment">     * @param  array $out     Row output data (to the JSON)
</span></span><span id="708" class="l"><a href="#708">708: </a><span class="php-comment">     * @param  mixed $srcData Row input data (raw, from the database)
</span></span><span id="709" class="l"><a href="#709">709: </a><span class="php-comment">     * @internal
</span></span><span id="710" class="l"><a href="#710">710: </a><span class="php-comment">     */</span>
</span><span id="711" class="l"><a href="#711">711: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> write( &amp;<span class="php-var">$out</span>, <span class="php-var">$srcData</span> )
</span><span id="712" class="l"><a href="#712">712: </a>    {
</span><span id="713" class="l"><a href="#713">713: </a>        <span class="php-var">$this</span>-&gt;_writeProp( <span class="php-var">$out</span>, <span class="php-var">$this</span>-&gt;name(), <span class="php-var">$this</span>-&gt;val(<span class="php-quote">'get'</span>, <span class="php-var">$srcData</span>) );
</span><span id="714" class="l"><a href="#714">714: </a>    }
</span><span id="715" class="l"><a href="#715">715: </a>
</span><span id="716" class="l"><a href="#716">716: </a>
</span><span id="717" class="l"><a href="#717">717: </a>    <span class="php-comment">/**
</span></span><span id="718" class="l"><a href="#718">718: </a><span class="php-comment">     * Perform XSS prevention on an input.
</span></span><span id="719" class="l"><a href="#719">719: </a><span class="php-comment">     *
</span></span><span id="720" class="l"><a href="#720">720: </a><span class="php-comment">     * @param  mixed $val Value to be escaped
</span></span><span id="721" class="l"><a href="#721">721: </a><span class="php-comment">     * @return string Safe value
</span></span><span id="722" class="l"><a href="#722">722: </a><span class="php-comment">     */</span>
</span><span id="723" class="l"><a href="#723">723: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> xssSafety ( <span class="php-var">$val</span> ) {
</span><span id="724" class="l"><a href="#724">724: </a>        <span class="php-var">$xss</span> = <span class="php-var">$this</span>-&gt;_xss;
</span><span id="725" class="l"><a href="#725">725: </a>
</span><span id="726" class="l"><a href="#726">726: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_array</span>( <span class="php-var">$val</span> ) ) {
</span><span id="727" class="l"><a href="#727">727: </a>            <span class="php-var">$res</span> = <span class="php-keyword1">array</span>();
</span><span id="728" class="l"><a href="#728">728: </a>
</span><span id="729" class="l"><a href="#729">729: </a>            <span class="php-keyword1">foreach</span> ( <span class="php-var">$val</span> <span class="php-keyword1">as</span> <span class="php-var">$individual</span> ) {
</span><span id="730" class="l"><a href="#730">730: </a>                <span class="php-var">$res</span>[] = <span class="php-var">$xss</span> ?
</span><span id="731" class="l"><a href="#731">731: </a>                    <span class="php-var">$xss</span>( <span class="php-var">$individual</span> ) :
</span><span id="732" class="l"><a href="#732">732: </a>                    DataTables\Vendor\Htmlaw::filter( <span class="php-var">$individual</span> );
</span><span id="733" class="l"><a href="#733">733: </a>            }
</span><span id="734" class="l"><a href="#734">734: </a>
</span><span id="735" class="l"><a href="#735">735: </a>            <span class="php-keyword1">return</span> <span class="php-var">$res</span>;
</span><span id="736" class="l"><a href="#736">736: </a>        }
</span><span id="737" class="l"><a href="#737">737: </a>
</span><span id="738" class="l"><a href="#738">738: </a>        <span class="php-keyword1">return</span> <span class="php-var">$xss</span> ?
</span><span id="739" class="l"><a href="#739">739: </a>            <span class="php-var">$xss</span>( <span class="php-var">$val</span> ) :
</span><span id="740" class="l"><a href="#740">740: </a>            DataTables\Vendor\Htmlaw::filter( <span class="php-var">$val</span> );
</span><span id="741" class="l"><a href="#741">741: </a>    }
</span><span id="742" class="l"><a href="#742">742: </a>
</span><span id="743" class="l"><a href="#743">743: </a>
</span><span id="744" class="l"><a href="#744">744: </a>
</span><span id="745" class="l"><a href="#745">745: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="746" class="l"><a href="#746">746: </a><span class="php-comment">     * Private methods
</span></span><span id="747" class="l"><a href="#747">747: </a><span class="php-comment">     */</span>
</span><span id="748" class="l"><a href="#748">748: </a>
</span><span id="749" class="l"><a href="#749">749: </a>    <span class="php-comment">/**
</span></span><span id="750" class="l"><a href="#750">750: </a><span class="php-comment">     * Apply a formatter to data. The caller will decide what formatter to apply
</span></span><span id="751" class="l"><a href="#751">751: </a><span class="php-comment">     * (get or set)
</span></span><span id="752" class="l"><a href="#752">752: </a><span class="php-comment">     *
</span></span><span id="753" class="l"><a href="#753">753: </a><span class="php-comment">     * @param  mixed    $val       Value to be formatted
</span></span><span id="754" class="l"><a href="#754">754: </a><span class="php-comment">     * @param  mixed    $data      Full row data
</span></span><span id="755" class="l"><a href="#755">755: </a><span class="php-comment">     * @param  callable $formatter Formatting function to be called
</span></span><span id="756" class="l"><a href="#756">756: </a><span class="php-comment">     * @param  array    $opts      Array of options to be passed to the formatter
</span></span><span id="757" class="l"><a href="#757">757: </a><span class="php-comment">     * @return mixed               Formatted value
</span></span><span id="758" class="l"><a href="#758">758: </a><span class="php-comment">     */</span>
</span><span id="759" class="l"><a href="#759">759: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _format( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$formatter</span>, <span class="php-var">$opts</span> )
</span><span id="760" class="l"><a href="#760">760: </a>    {
</span><span id="761" class="l"><a href="#761">761: </a>        <span class="php-comment">// Three cases for the formatter - closure, string or null</span>
</span><span id="762" class="l"><a href="#762">762: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$formatter</span> ) {
</span><span id="763" class="l"><a href="#763">763: </a>            <span class="php-keyword1">return</span> <span class="php-var">$val</span>;
</span><span id="764" class="l"><a href="#764">764: </a>        }
</span><span id="765" class="l"><a href="#765">765: </a>
</span><span id="766" class="l"><a href="#766">766: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_string</span>( <span class="php-var">$formatter</span> ) ) {
</span><span id="767" class="l"><a href="#767">767: </a>            <span class="php-keyword1">return</span> <span class="php-var">$formatter</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$opts</span> );
</span><span id="768" class="l"><a href="#768">768: </a>        }
</span><span id="769" class="l"><a href="#769">769: </a>
</span><span id="770" class="l"><a href="#770">770: </a>        <span class="php-comment">// Backwards compatibility - strings will not be supported in v2</span>
</span><span id="771" class="l"><a href="#771">771: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$formatter</span>, <span class="php-quote">&quot;Format::&quot;</span>) === <span class="php-num">0</span> ) {
</span><span id="772" class="l"><a href="#772">772: </a>            <span class="php-var">$a</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'::'</span>, <span class="php-var">$formatter</span> );
</span><span id="773" class="l"><a href="#773">773: </a>
</span><span id="774" class="l"><a href="#774">774: </a>            <span class="php-comment">// Old style Editor formatter - use the legacy functions to</span>
</span><span id="775" class="l"><a href="#775">775: </a>            <span class="php-comment">// convert to the new style</span>
</span><span id="776" class="l"><a href="#776">776: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">method_exists</span>( <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$a</span>[<span class="php-num">0</span>], <span class="php-var">$a</span>[<span class="php-num">1</span>].<span class="php-quote">'Legacy'</span> ) ) {
</span><span id="777" class="l"><a href="#777">777: </a>                <span class="php-var">$func</span> = <span class="php-keyword2">call_user_func</span>( <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$formatter</span>.<span class="php-quote">'Legacy'</span>, <span class="php-var">$opts</span> );
</span><span id="778" class="l"><a href="#778">778: </a>
</span><span id="779" class="l"><a href="#779">779: </a>                <span class="php-keyword1">return</span> <span class="php-var">$func</span>( <span class="php-var">$val</span>, <span class="php-var">$data</span> );
</span><span id="780" class="l"><a href="#780">780: </a>            }
</span><span id="781" class="l"><a href="#781">781: </a>            <span class="php-keyword1">else</span> {
</span><span id="782" class="l"><a href="#782">782: </a>                <span class="php-comment">// User added old style methods</span>
</span><span id="783" class="l"><a href="#783">783: </a>                <span class="php-keyword1">return</span> <span class="php-keyword2">call_user_func</span>( <span class="php-quote">&quot;\\DataTables\\Editor\\&quot;</span>.<span class="php-var">$formatter</span>, <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$opts</span> );
</span><span id="784" class="l"><a href="#784">784: </a>            }
</span><span id="785" class="l"><a href="#785">785: </a>        }
</span><span id="786" class="l"><a href="#786">786: </a>
</span><span id="787" class="l"><a href="#787">787: </a>        <span class="php-comment">// User function (string identifier)</span>
</span><span id="788" class="l"><a href="#788">788: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">call_user_func</span>( <span class="php-var">$formatter</span>, <span class="php-var">$val</span>, <span class="php-var">$data</span>, <span class="php-var">$opts</span> );
</span><span id="789" class="l"><a href="#789">789: </a>    }
</span><span id="790" class="l"><a href="#790">790: </a>
</span><span id="791" class="l"><a href="#791">791: </a>    <span class="php-comment">/**
</span></span><span id="792" class="l"><a href="#792">792: </a><span class="php-comment">     * Get the value from `_[gs]etValue` - taking into account if it is callable
</span></span><span id="793" class="l"><a href="#793">793: </a><span class="php-comment">     * function or not
</span></span><span id="794" class="l"><a href="#794">794: </a><span class="php-comment">     *
</span></span><span id="795" class="l"><a href="#795">795: </a><span class="php-comment">     * @param  mixed $val Value to be evaluated
</span></span><span id="796" class="l"><a href="#796">796: </a><span class="php-comment">     * @return mixed      Value assigned, or returned from the function
</span></span><span id="797" class="l"><a href="#797">797: </a><span class="php-comment">     */</span>
</span><span id="798" class="l"><a href="#798">798: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _getAssignedValue ( <span class="php-var">$val</span> )
</span><span id="799" class="l"><a href="#799">799: </a>    {
</span><span id="800" class="l"><a href="#800">800: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">is_callable</span>(<span class="php-var">$val</span>) &amp;&amp; <span class="php-keyword2">is_object</span>(<span class="php-var">$val</span>) ?
</span><span id="801" class="l"><a href="#801">801: </a>            <span class="php-var">$val</span>() :
</span><span id="802" class="l"><a href="#802">802: </a>            <span class="php-var">$val</span>;
</span><span id="803" class="l"><a href="#803">803: </a>    }
</span><span id="804" class="l"><a href="#804">804: </a>
</span><span id="805" class="l"><a href="#805">805: </a>    <span class="php-comment">/**
</span></span><span id="806" class="l"><a href="#806">806: </a><span class="php-comment">     * Check is a parameter is in the submitted data set. This is functionally
</span></span><span id="807" class="l"><a href="#807">807: </a><span class="php-comment">     * the same as the `_readProp()` method, but in this case a binary value
</span></span><span id="808" class="l"><a href="#808">808: </a><span class="php-comment">     * is required to indicate if the value is present or not.
</span></span><span id="809" class="l"><a href="#809">809: </a><span class="php-comment">     *
</span></span><span id="810" class="l"><a href="#810">810: </a><span class="php-comment">     * @param  string $name  Javascript dotted object name to write to
</span></span><span id="811" class="l"><a href="#811">811: </a><span class="php-comment">     * @param  array  $data   Data source array to read from
</span></span><span id="812" class="l"><a href="#812">812: </a><span class="php-comment">     * @return boolean       `true` if present, `false` otherwise
</span></span><span id="813" class="l"><a href="#813">813: </a><span class="php-comment">     * @private
</span></span><span id="814" class="l"><a href="#814">814: </a><span class="php-comment">     */</span>
</span><span id="815" class="l"><a href="#815">815: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _inData ( <span class="php-var">$name</span>, <span class="php-var">$data</span> )
</span><span id="816" class="l"><a href="#816">816: </a>    {
</span><span id="817" class="l"><a href="#817">817: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$name</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> ) {
</span><span id="818" class="l"><a href="#818">818: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$data</span>[ <span class="php-var">$name</span> ] ) ?
</span><span id="819" class="l"><a href="#819">819: </a>                <span class="php-keyword1">true</span> :
</span><span id="820" class="l"><a href="#820">820: </a>                <span class="php-keyword1">false</span>;
</span><span id="821" class="l"><a href="#821">821: </a>        }
</span><span id="822" class="l"><a href="#822">822: </a>
</span><span id="823" class="l"><a href="#823">823: </a>        <span class="php-var">$names</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$name</span> );
</span><span id="824" class="l"><a href="#824">824: </a>        <span class="php-var">$inner</span> = <span class="php-var">$data</span>;
</span><span id="825" class="l"><a href="#825">825: </a>
</span><span id="826" class="l"><a href="#826">826: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="827" class="l"><a href="#827">827: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ] ) ) {
</span><span id="828" class="l"><a href="#828">828: </a>                <span class="php-keyword1">return</span> <span class="php-keyword1">false</span>;
</span><span id="829" class="l"><a href="#829">829: </a>            }
</span><span id="830" class="l"><a href="#830">830: </a>
</span><span id="831" class="l"><a href="#831">831: </a>            <span class="php-var">$inner</span> = <span class="php-var">$inner</span>[ <span class="php-var">$names</span>[<span class="php-var">$i</span>] ];
</span><span id="832" class="l"><a href="#832">832: </a>        }
</span><span id="833" class="l"><a href="#833">833: </a>
</span><span id="834" class="l"><a href="#834">834: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">isset</span>( <span class="php-var">$inner</span> [ <span class="php-var">$names</span>[<span class="php-keyword2">count</span>(<span class="php-var">$names</span>)-<span class="php-num">1</span>] ] ) ?
</span><span id="835" class="l"><a href="#835">835: </a>            <span class="php-keyword1">true</span> :
</span><span id="836" class="l"><a href="#836">836: </a>            <span class="php-keyword1">false</span>;
</span><span id="837" class="l"><a href="#837">837: </a>    }
</span><span id="838" class="l"><a href="#838">838: </a>}
</span><span id="839" class="l"><a href="#839">839: </a>
</span><span id="840" class="l"><a href="#840">840: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
