<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Database/Result.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1"> 1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2"> 2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3"> 3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4"> 4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5"> 5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6"> 6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7"> 7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8"> 8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9"> 9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10">10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11">11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12">12: </a>
</span><span id="13" class="l"><a href="#13">13: </a><span class="php-keyword1">namespace</span> DataTables\Database;
</span><span id="14" class="l"><a href="#14">14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15">15: </a>
</span><span id="16" class="l"><a href="#16">16: </a>
</span><span id="17" class="l"><a href="#17">17: </a><span class="php-comment">//</span>
</span><span id="18" class="l"><a href="#18">18: </a><span class="php-comment">// This is a stub class that a driver must extend and complete</span>
</span><span id="19" class="l"><a href="#19">19: </a><span class="php-comment">//</span>
</span><span id="20" class="l"><a href="#20">20: </a>
</span><span id="21" class="l"><a href="#21">21: </a><span class="php-comment">/**
</span></span><span id="22" class="l"><a href="#22">22: </a><span class="php-comment"> * Result object given by a {@link Query} performed on a database.
</span></span><span id="23" class="l"><a href="#23">23: </a><span class="php-comment"> * 
</span></span><span id="24" class="l"><a href="#24">24: </a><span class="php-comment"> * The typical pattern for using this class is to receive an instance of it as a
</span></span><span id="25" class="l"><a href="#25">25: </a><span class="php-comment"> * result of using the {@link Database} and {@link Query} class methods that
</span></span><span id="26" class="l"><a href="#26">26: </a><span class="php-comment"> * return a result. This class should not be initialised independently.
</span></span><span id="27" class="l"><a href="#27">27: </a><span class="php-comment"> *
</span></span><span id="28" class="l"><a href="#28">28: </a><span class="php-comment"> * Note that this is a stub class that a driver will extend and complete as
</span></span><span id="29" class="l"><a href="#29">29: </a><span class="php-comment"> * required for individual database types. Individual drivers could add
</span></span><span id="30" class="l"><a href="#30">30: </a><span class="php-comment"> * additional methods, but this is discouraged to ensure that the API is the
</span></span><span id="31" class="l"><a href="#31">31: </a><span class="php-comment"> * same for all database types.
</span></span><span id="32" class="l"><a href="#32">32: </a><span class="php-comment"> */</span>
</span><span id="33" class="l"><a href="#33">33: </a><span class="php-keyword1">class</span> Result {
</span><span id="34" class="l"><a href="#34">34: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="35" class="l"><a href="#35">35: </a><span class="php-comment">     * Public methods
</span></span><span id="36" class="l"><a href="#36">36: </a><span class="php-comment">     */</span>
</span><span id="37" class="l"><a href="#37">37: </a>
</span><span id="38" class="l"><a href="#38">38: </a>    <span class="php-comment">/**
</span></span><span id="39" class="l"><a href="#39">39: </a><span class="php-comment">     * Count the number of rows in the result set.
</span></span><span id="40" class="l"><a href="#40">40: </a><span class="php-comment">     *  @return int
</span></span><span id="41" class="l"><a href="#41">41: </a><span class="php-comment">     */</span>
</span><span id="42" class="l"><a href="#42">42: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">count</span> ()
</span><span id="43" class="l"><a href="#43">43: </a>    {
</span><span id="44" class="l"><a href="#44">44: </a>        <span class="php-keyword1">return</span> <span class="php-num">0</span>;
</span><span id="45" class="l"><a href="#45">45: </a>    }
</span><span id="46" class="l"><a href="#46">46: </a>
</span><span id="47" class="l"><a href="#47">47: </a>
</span><span id="48" class="l"><a href="#48">48: </a>    <span class="php-comment">/**
</span></span><span id="49" class="l"><a href="#49">49: </a><span class="php-comment">     * Get the next row in a result set
</span></span><span id="50" class="l"><a href="#50">50: </a><span class="php-comment">     *  @param int PDO row fetch style - PDO::FETCH_ASSOC is the default
</span></span><span id="51" class="l"><a href="#51">51: </a><span class="php-comment">     *  @return array
</span></span><span id="52" class="l"><a href="#52">52: </a><span class="php-comment">     */</span>
</span><span id="53" class="l"><a href="#53">53: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> fetch ( <span class="php-var">$fetchType</span>=\PDO::FETCH_ASSOC )
</span><span id="54" class="l"><a href="#54">54: </a>    {
</span><span id="55" class="l"><a href="#55">55: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">array</span>();
</span><span id="56" class="l"><a href="#56">56: </a>    }
</span><span id="57" class="l"><a href="#57">57: </a>
</span><span id="58" class="l"><a href="#58">58: </a>
</span><span id="59" class="l"><a href="#59">59: </a>    <span class="php-comment">/**
</span></span><span id="60" class="l"><a href="#60">60: </a><span class="php-comment">     * Get all rows in the result set
</span></span><span id="61" class="l"><a href="#61">61: </a><span class="php-comment">     *  @param int PDO row fetch style - PDO::FETCH_ASSOC is the default
</span></span><span id="62" class="l"><a href="#62">62: </a><span class="php-comment">     *  @return array
</span></span><span id="63" class="l"><a href="#63">63: </a><span class="php-comment">     */</span>
</span><span id="64" class="l"><a href="#64">64: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> fetchAll ( <span class="php-var">$fetchType</span>=\PDO::FETCH_ASSOC )
</span><span id="65" class="l"><a href="#65">65: </a>    {
</span><span id="66" class="l"><a href="#66">66: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">array</span>();
</span><span id="67" class="l"><a href="#67">67: </a>    }
</span><span id="68" class="l"><a href="#68">68: </a>
</span><span id="69" class="l"><a href="#69">69: </a>
</span><span id="70" class="l"><a href="#70">70: </a>    <span class="php-comment">/**
</span></span><span id="71" class="l"><a href="#71">71: </a><span class="php-comment">     * After an INSERT query, get the ID that was inserted.
</span></span><span id="72" class="l"><a href="#72">72: </a><span class="php-comment">     *  @return int
</span></span><span id="73" class="l"><a href="#73">73: </a><span class="php-comment">     */</span>
</span><span id="74" class="l"><a href="#74">74: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> insertId ()
</span><span id="75" class="l"><a href="#75">75: </a>    {
</span><span id="76" class="l"><a href="#76">76: </a>        <span class="php-keyword1">return</span> <span class="php-num">0</span>;
</span><span id="77" class="l"><a href="#77">77: </a>    }
</span><span id="78" class="l"><a href="#78">78: </a>};
</span><span id="79" class="l"><a href="#79">79: </a>
</span><span id="80" class="l"><a href="#80">80: </a>
</span><span id="81" class="l"><a href="#81">81: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
