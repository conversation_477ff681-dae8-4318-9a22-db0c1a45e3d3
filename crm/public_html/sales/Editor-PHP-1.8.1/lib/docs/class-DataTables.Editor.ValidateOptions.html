<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Class DataTables\Editor\ValidateOptions | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li class="active">
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Editor.Field.html">Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">Validate</a></li>
				<li class="active"><a href="class-DataTables.Editor.ValidateOptions.html">ValidateOptions</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
				<a href="namespace-DataTables.Editor.html" title="Summary of DataTables\Editor"><span>Namespace</span></a>
			</li>
			<li class="active">
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="class">
	<h1>Class ValidateOptions</h1>


	<div class="description">
	<p>Common validation options that can be specified for all validation methods.</p>
	</div>

	<dl class="tree">
		<dd style="padding-left:0px">
			<a href="class-DataTables.Ext.html"><span>DataTables\Ext</span></a>
			
			
			
		</dd>
		<dd style="padding-left:30px">
			<img src="resources/inherit.png" alt="Extended by">
<b><span>DataTables\Editor\ValidateOptions</span></b>			
			
			
		</dd>
	</dl>








	<div class="info">
		
		
		<b>Namespace:</b> <a href="namespace-DataTables.html">DataTables</a>\<a href="namespace-DataTables.Editor.html">Editor</a><br>
		

			<b>Located at</b> <a href="source-class-DataTables.Editor.ValidateOptions.html#18-99" title="Go to source code">Editor/ValidateOptions.php</a>
		<br>
	</div>



	<table class="summary methods" id="methods">
	<caption>Methods summary</caption>
	<tr data-order="__construct" id="___construct">

		<td class="attributes"><code>
			 public 

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#___construct">#</a>
		<code><a href="source-class-DataTables.Editor.ValidateOptions.html#26-41" title="Go to source code">__construct</a>( <span> <var>$opts</var> = <span class="php-keyword1">null</span> </span> )</code>

		<div class="description short">
			
		</div>

		<div class="description detailed hidden">
			







		</div>
		</div></td>
	</tr>
	<tr data-order="message" id="_message">

		<td class="attributes"><code>
			 public 

			<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|string
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_message">#</a>
		<code><a href="source-class-DataTables.Editor.ValidateOptions.html#43-56" title="Go to source code">message</a>( <span>string <var>$msg</var> = <span class="php-keyword1">null</span> </span> )</code>

		<div class="description short">
			<p>Get / set the error message to use if validation fails</p>
		</div>

		<div class="description detailed hidden">
			<p>Get / set the error message to use if validation fails</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$msg</var></dt>
					<dd><p>Error message to use. If not given, the currently
  set message will be returned.</p></dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|string<br>Self if setting, message if getting.
				</div>




		</div>
		</div></td>
	</tr>
	<tr data-order="allowEmpty" id="_allowEmpty">

		<td class="attributes"><code>
			 public 

			<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|boolean
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_allowEmpty">#</a>
		<code><a href="source-class-DataTables.Editor.ValidateOptions.html#58-71" title="Go to source code">allowEmpty</a>( <span>boolean <var>$empty</var> = <span class="php-keyword1">null</span> </span> )</code>

		<div class="description short">
			<p>Get / set the field empty option</p>
		</div>

		<div class="description detailed hidden">
			<p>Get / set the field empty option</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$empty</var></dt>
					<dd><p><code>false</code> if the field is not allowed to be
  empty. <code>true</code> if it can be.</p></dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|boolean<br>Self if setting, current value if getting.
				</div>




		</div>
		</div></td>
	</tr>
	<tr data-order="optional" id="_optional">

		<td class="attributes"><code>
			 public 

			<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|boolean
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_optional">#</a>
		<code><a href="source-class-DataTables.Editor.ValidateOptions.html#73-86" title="Go to source code">optional</a>( <span>boolean <var>$optional</var> = <span class="php-keyword1">null</span> </span> )</code>

		<div class="description short">
			<p>Get / set the field optional option</p>
		</div>

		<div class="description detailed hidden">
			<p>Get / set the field optional option</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$optional</var></dt>
					<dd><p><code>false</code> if the field does not need to be
  submitted. <code>true</code> if it must be.</p></dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></code>|boolean<br>Self if setting, current value if getting.
				</div>




		</div>
		</div></td>
	</tr>
	</table>

	<table class="summary inherited">
	<caption>Methods inherited from <a href="class-DataTables.Ext.html#methods">DataTables\Ext</a></caption>
	<tr>
		<td><code>
			<a href="class-DataTables.Ext.html#__getSet">_getSet()</a>, 
			<a href="class-DataTables.Ext.html#__propExists">_propExists()</a>, 
			<a href="class-DataTables.Ext.html#__readProp">_readProp()</a>, 
			<a href="class-DataTables.Ext.html#__writeProp">_writeProp()</a>, 
			<a href="class-DataTables.Ext.html#_inst">inst()</a>, 
			<a href="class-DataTables.Ext.html#_instantiate">instantiate()</a>
		</code></td>
	</tr>
	</table>

















</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js"></script>
<script src="elementlist.js"></script>
</body>
</html>
