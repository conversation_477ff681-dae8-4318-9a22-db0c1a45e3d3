<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Mjoin.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1"> 1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2"> 2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3"> 3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4"> 4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5"> 5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6"> 6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7"> 7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8"> 8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9"> 9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10">10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11">11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12">12: </a>
</span><span id="13" class="l"><a href="#13">13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14">14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15">15: </a>
</span><span id="16" class="l"><a href="#16">16: </a><span class="php-keyword1">use</span> DataTables\Editor\<span class="php-keyword2">Join</span>;
</span><span id="17" class="l"><a href="#17">17: </a>
</span><span id="18" class="l"><a href="#18">18: </a>
</span><span id="19" class="l"><a href="#19">19: </a><span class="php-comment">/**
</span></span><span id="20" class="l"><a href="#20">20: </a><span class="php-comment"> * The `Mjoin` class extends the `Join` class with the join data type set to
</span></span><span id="21" class="l"><a href="#21">21: </a><span class="php-comment"> * 'array', whereas the `Join` default is `object` which has been rendered
</span></span><span id="22" class="l"><a href="#22">22: </a><span class="php-comment"> * obsolete by the `Editor-&gt;leftJoin()` method. The API API is otherwise
</span></span><span id="23" class="l"><a href="#23">23: </a><span class="php-comment"> * identical.
</span></span><span id="24" class="l"><a href="#24">24: </a><span class="php-comment"> *
</span></span><span id="25" class="l"><a href="#25">25: </a><span class="php-comment"> * This class is recommended over the `Join` class.
</span></span><span id="26" class="l"><a href="#26">26: </a><span class="php-comment"> */</span>
</span><span id="27" class="l"><a href="#27">27: </a><span class="php-keyword1">class</span> MJoin <span class="php-keyword1">extends</span> <span class="php-keyword2">Join</span>
</span><span id="28" class="l"><a href="#28">28: </a>{
</span><span id="29" class="l"><a href="#29">29: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$table</span>=<span class="php-keyword1">null</span> )
</span><span id="30" class="l"><a href="#30">30: </a>    {
</span><span id="31" class="l"><a href="#31">31: </a>        parent::__construct( <span class="php-var">$table</span>, <span class="php-quote">'array'</span> );
</span><span id="32" class="l"><a href="#32">32: </a>    }
</span><span id="33" class="l"><a href="#33">33: </a>}
</span><span id="34" class="l"><a href="#34">34: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
