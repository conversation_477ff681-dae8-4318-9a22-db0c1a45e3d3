<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">   1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">   2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">   3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">   4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">   5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">   6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">   7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">   8: </a><span class="php-comment"> *  @version   __VERSION__
</span></span><span id="9" class="l"><a href="#9">   9: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="10" class="l"><a href="#10">  10: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="11" class="l"><a href="#11">  11: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="12" class="l"><a href="#12">  12: </a><span class="php-comment"> */</span>
</span><span id="13" class="l"><a href="#13">  13: </a>
</span><span id="14" class="l"><a href="#14">  14: </a><span class="php-keyword1">namespace</span> DataTables;
</span><span id="15" class="l"><a href="#15">  15: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="16" class="l"><a href="#16">  16: </a>
</span><span id="17" class="l"><a href="#17">  17: </a><span class="php-keyword1">use</span>
</span><span id="18" class="l"><a href="#18">  18: </a>    DataTables,
</span><span id="19" class="l"><a href="#19">  19: </a>    DataTables\Editor\<span class="php-keyword2">Join</span>,
</span><span id="20" class="l"><a href="#20">  20: </a>    DataTables\Editor\Field;
</span><span id="21" class="l"><a href="#21">  21: </a>
</span><span id="22" class="l"><a href="#22">  22: </a>
</span><span id="23" class="l"><a href="#23">  23: </a><span class="php-comment">/**
</span></span><span id="24" class="l"><a href="#24">  24: </a><span class="php-comment"> * DataTables Editor base class for creating editable tables.
</span></span><span id="25" class="l"><a href="#25">  25: </a><span class="php-comment"> *
</span></span><span id="26" class="l"><a href="#26">  26: </a><span class="php-comment"> * Editor class instances are capable of servicing all of the requests that
</span></span><span id="27" class="l"><a href="#27">  27: </a><span class="php-comment"> * DataTables and Editor will make from the client-side - specifically:
</span></span><span id="28" class="l"><a href="#28">  28: </a><span class="php-comment"> * 
</span></span><span id="29" class="l"><a href="#29">  29: </a><span class="php-comment"> * * Get data
</span></span><span id="30" class="l"><a href="#30">  30: </a><span class="php-comment"> * * Create new record
</span></span><span id="31" class="l"><a href="#31">  31: </a><span class="php-comment"> * * Edit existing record
</span></span><span id="32" class="l"><a href="#32">  32: </a><span class="php-comment"> * * Delete existing records
</span></span><span id="33" class="l"><a href="#33">  33: </a><span class="php-comment"> *
</span></span><span id="34" class="l"><a href="#34">  34: </a><span class="php-comment"> * The Editor instance is configured with information regarding the
</span></span><span id="35" class="l"><a href="#35">  35: </a><span class="php-comment"> * database table fields that you wish to make editable, and other information
</span></span><span id="36" class="l"><a href="#36">  36: </a><span class="php-comment"> * needed to read and write to the database (table name for example!).
</span></span><span id="37" class="l"><a href="#37">  37: </a><span class="php-comment"> *
</span></span><span id="38" class="l"><a href="#38">  38: </a><span class="php-comment"> * This documentation is very much focused on describing the API presented
</span></span><span id="39" class="l"><a href="#39">  39: </a><span class="php-comment"> * by these DataTables Editor classes. For a more general overview of how
</span></span><span id="40" class="l"><a href="#40">  40: </a><span class="php-comment"> * the Editor class is used, and how to install Editor on your server, please
</span></span><span id="41" class="l"><a href="#41">  41: </a><span class="php-comment"> * refer to the {@link https://editor.datatables.net/manual Editor manual}.
</span></span><span id="42" class="l"><a href="#42">  42: </a><span class="php-comment"> *
</span></span><span id="43" class="l"><a href="#43">  43: </a><span class="php-comment"> *  @example 
</span></span><span id="44" class="l"><a href="#44">  44: </a><span class="php-comment"> *    A very basic example of using Editor to create a table with four fields.
</span></span><span id="45" class="l"><a href="#45">  45: </a><span class="php-comment"> *    This is all that is needed on the server-side to create a editable
</span></span><span id="46" class="l"><a href="#46">  46: </a><span class="php-comment"> *    table - the {@link process} method determines what action DataTables /
</span></span><span id="47" class="l"><a href="#47">  47: </a><span class="php-comment"> *    Editor is requesting from the server-side and will correctly action it.
</span></span><span id="48" class="l"><a href="#48">  48: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="49" class="l"><a href="#49">  49: </a><span class="php-comment"> *      Editor::inst( $db, 'browsers' )
</span></span><span id="50" class="l"><a href="#50">  50: </a><span class="php-comment"> *          -&gt;fields(
</span></span><span id="51" class="l"><a href="#51">  51: </a><span class="php-comment"> *              Field::inst( 'first_name' )-&gt;validator( Validate::required() ),
</span></span><span id="52" class="l"><a href="#52">  52: </a><span class="php-comment"> *              Field::inst( 'last_name' )-&gt;validator( Validate::required() ),
</span></span><span id="53" class="l"><a href="#53">  53: </a><span class="php-comment"> *              Field::inst( 'country' ),
</span></span><span id="54" class="l"><a href="#54">  54: </a><span class="php-comment"> *              Field::inst( 'details' )
</span></span><span id="55" class="l"><a href="#55">  55: </a><span class="php-comment"> *          )
</span></span><span id="56" class="l"><a href="#56">  56: </a><span class="php-comment"> *          -&gt;process( $_POST )
</span></span><span id="57" class="l"><a href="#57">  57: </a><span class="php-comment"> *          -&gt;json();
</span></span><span id="58" class="l"><a href="#58">  58: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="59" class="l"><a href="#59">  59: </a><span class="php-comment"> */</span>
</span><span id="60" class="l"><a href="#60">  60: </a><span class="php-keyword1">class</span> Editor <span class="php-keyword1">extends</span> Ext {
</span><span id="61" class="l"><a href="#61">  61: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="62" class="l"><a href="#62">  62: </a><span class="php-comment">     * Statics
</span></span><span id="63" class="l"><a href="#63">  63: </a><span class="php-comment">     */</span>
</span><span id="64" class="l"><a href="#64">  64: </a>
</span><span id="65" class="l"><a href="#65">  65: </a>    <span class="php-comment">/** Request type - read */</span>
</span><span id="66" class="l"><a href="#66">  66: </a>    <span class="php-keyword1">const</span> ACTION_READ = <span class="php-quote">'read'</span>;
</span><span id="67" class="l"><a href="#67">  67: </a>
</span><span id="68" class="l"><a href="#68">  68: </a>    <span class="php-comment">/** Request type - create */</span>
</span><span id="69" class="l"><a href="#69">  69: </a>    <span class="php-keyword1">const</span> ACTION_CREATE = <span class="php-quote">'create'</span>;
</span><span id="70" class="l"><a href="#70">  70: </a>
</span><span id="71" class="l"><a href="#71">  71: </a>    <span class="php-comment">/** Request type - edit */</span>
</span><span id="72" class="l"><a href="#72">  72: </a>    <span class="php-keyword1">const</span> ACTION_EDIT = <span class="php-quote">'edit'</span>;
</span><span id="73" class="l"><a href="#73">  73: </a>
</span><span id="74" class="l"><a href="#74">  74: </a>    <span class="php-comment">/** Request type - delete */</span>
</span><span id="75" class="l"><a href="#75">  75: </a>    <span class="php-keyword1">const</span> ACTION_DELETE = <span class="php-quote">'remove'</span>;
</span><span id="76" class="l"><a href="#76">  76: </a>
</span><span id="77" class="l"><a href="#77">  77: </a>    <span class="php-comment">/** Request type - upload */</span>
</span><span id="78" class="l"><a href="#78">  78: </a>    <span class="php-keyword1">const</span> ACTION_UPLOAD = <span class="php-quote">'upload'</span>;
</span><span id="79" class="l"><a href="#79">  79: </a>
</span><span id="80" class="l"><a href="#80">  80: </a>
</span><span id="81" class="l"><a href="#81">  81: </a>    <span class="php-comment">/**
</span></span><span id="82" class="l"><a href="#82">  82: </a><span class="php-comment">     * Determine the request type from an HTTP request.
</span></span><span id="83" class="l"><a href="#83">  83: </a><span class="php-comment">     * 
</span></span><span id="84" class="l"><a href="#84">  84: </a><span class="php-comment">     * @param array $http Typically $_POST, but can be any array used to carry
</span></span><span id="85" class="l"><a href="#85">  85: </a><span class="php-comment">     *   an Editor payload
</span></span><span id="86" class="l"><a href="#86">  86: </a><span class="php-comment">     * @return string `Editor::ACTION_READ`, `Editor::ACTION_CREATE`,
</span></span><span id="87" class="l"><a href="#87">  87: </a><span class="php-comment">     *   `Editor::ACTION_EDIT` or `Editor::ACTION_DELETE` indicating the request
</span></span><span id="88" class="l"><a href="#88">  88: </a><span class="php-comment">     *   type.
</span></span><span id="89" class="l"><a href="#89">  89: </a><span class="php-comment">     */</span>
</span><span id="90" class="l"><a href="#90">  90: </a>    <span class="php-keyword1">static</span> <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> action ( <span class="php-var">$http</span> )
</span><span id="91" class="l"><a href="#91">  91: </a>    {
</span><span id="92" class="l"><a href="#92">  92: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$http</span>[<span class="php-quote">'action'</span>] ) ) {
</span><span id="93" class="l"><a href="#93">  93: </a>            <span class="php-keyword1">return</span> self::ACTION_READ;
</span><span id="94" class="l"><a href="#94">  94: </a>        }
</span><span id="95" class="l"><a href="#95">  95: </a>
</span><span id="96" class="l"><a href="#96">  96: </a>        <span class="php-keyword1">switch</span> ( <span class="php-var">$http</span>[<span class="php-quote">'action'</span>] ) {
</span><span id="97" class="l"><a href="#97">  97: </a>            <span class="php-keyword1">case</span> <span class="php-quote">'create'</span>:
</span><span id="98" class="l"><a href="#98">  98: </a>                <span class="php-keyword1">return</span> self::ACTION_CREATE;
</span><span id="99" class="l"><a href="#99">  99: </a>
</span><span id="100" class="l"><a href="#100"> 100: </a>            <span class="php-keyword1">case</span> <span class="php-quote">'edit'</span>:
</span><span id="101" class="l"><a href="#101"> 101: </a>                <span class="php-keyword1">return</span> self::ACTION_EDIT;
</span><span id="102" class="l"><a href="#102"> 102: </a>
</span><span id="103" class="l"><a href="#103"> 103: </a>            <span class="php-keyword1">case</span> <span class="php-quote">'remove'</span>:
</span><span id="104" class="l"><a href="#104"> 104: </a>                <span class="php-keyword1">return</span> self::ACTION_DELETE;
</span><span id="105" class="l"><a href="#105"> 105: </a>
</span><span id="106" class="l"><a href="#106"> 106: </a>            <span class="php-keyword1">case</span> <span class="php-quote">'upload'</span>:
</span><span id="107" class="l"><a href="#107"> 107: </a>                <span class="php-keyword1">return</span> self::ACTION_UPLOAD;
</span><span id="108" class="l"><a href="#108"> 108: </a>
</span><span id="109" class="l"><a href="#109"> 109: </a>            <span class="php-keyword1">default</span>:
</span><span id="110" class="l"><a href="#110"> 110: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Unknown Editor action: &quot;</span>.<span class="php-var">$http</span>[<span class="php-quote">'action'</span>]);
</span><span id="111" class="l"><a href="#111"> 111: </a>        }
</span><span id="112" class="l"><a href="#112"> 112: </a>    }
</span><span id="113" class="l"><a href="#113"> 113: </a>
</span><span id="114" class="l"><a href="#114"> 114: </a>
</span><span id="115" class="l"><a href="#115"> 115: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="116" class="l"><a href="#116"> 116: </a><span class="php-comment">     * Constructor
</span></span><span id="117" class="l"><a href="#117"> 117: </a><span class="php-comment">     */</span>
</span><span id="118" class="l"><a href="#118"> 118: </a>
</span><span id="119" class="l"><a href="#119"> 119: </a>    <span class="php-comment">/**
</span></span><span id="120" class="l"><a href="#120"> 120: </a><span class="php-comment">     * Constructor.
</span></span><span id="121" class="l"><a href="#121"> 121: </a><span class="php-comment">     *  @param Database $db An instance of the DataTables Database class that we can
</span></span><span id="122" class="l"><a href="#122"> 122: </a><span class="php-comment">     *    use for the DB connection. Can be given here or with the 'db' method.
</span></span><span id="123" class="l"><a href="#123"> 123: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="124" class="l"><a href="#124"> 124: </a><span class="php-comment">     *      456
</span></span><span id="125" class="l"><a href="#125"> 125: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="126" class="l"><a href="#126"> 126: </a><span class="php-comment">     *  @param string|array $table The table name in the database to read and write
</span></span><span id="127" class="l"><a href="#127"> 127: </a><span class="php-comment">     *    information from and to. Can be given here or with the 'table' method.
</span></span><span id="128" class="l"><a href="#128"> 128: </a><span class="php-comment">     *  @param string|array $pkey Primary key column name in the table given in
</span></span><span id="129" class="l"><a href="#129"> 129: </a><span class="php-comment">     *    the $table parameter. Can be given here or with the 'pkey' method.
</span></span><span id="130" class="l"><a href="#130"> 130: </a><span class="php-comment">     */</span>
</span><span id="131" class="l"><a href="#131"> 131: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$db</span>=<span class="php-keyword1">null</span>, <span class="php-var">$table</span>=<span class="php-keyword1">null</span>, <span class="php-var">$pkey</span>=<span class="php-keyword1">null</span> )
</span><span id="132" class="l"><a href="#132"> 132: </a>    {
</span><span id="133" class="l"><a href="#133"> 133: </a>        <span class="php-comment">// Set constructor parameters using the API - note that the get/set will</span>
</span><span id="134" class="l"><a href="#134"> 134: </a>        <span class="php-comment">// ignore null values if they are used (i.e. not passed in)</span>
</span><span id="135" class="l"><a href="#135"> 135: </a>        <span class="php-var">$this</span>-&gt;db( <span class="php-var">$db</span> );
</span><span id="136" class="l"><a href="#136"> 136: </a>        <span class="php-var">$this</span>-&gt;table( <span class="php-var">$table</span> );
</span><span id="137" class="l"><a href="#137"> 137: </a>        <span class="php-var">$this</span>-&gt;pkey( <span class="php-var">$pkey</span> );
</span><span id="138" class="l"><a href="#138"> 138: </a>    }
</span><span id="139" class="l"><a href="#139"> 139: </a>
</span><span id="140" class="l"><a href="#140"> 140: </a>
</span><span id="141" class="l"><a href="#141"> 141: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="142" class="l"><a href="#142"> 142: </a><span class="php-comment">     * Public properties
</span></span><span id="143" class="l"><a href="#143"> 143: </a><span class="php-comment">     */</span>
</span><span id="144" class="l"><a href="#144"> 144: </a>
</span><span id="145" class="l"><a href="#145"> 145: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="146" class="l"><a href="#146"> 146: </a>    <span class="php-keyword1">public</span> <span class="php-var">$version</span> = <span class="php-quote">'1.8.1'</span>;
</span><span id="147" class="l"><a href="#147"> 147: </a>
</span><span id="148" class="l"><a href="#148"> 148: </a>
</span><span id="149" class="l"><a href="#149"> 149: </a>
</span><span id="150" class="l"><a href="#150"> 150: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="151" class="l"><a href="#151"> 151: </a><span class="php-comment">     * Private properties
</span></span><span id="152" class="l"><a href="#152"> 152: </a><span class="php-comment">     */</span>
</span><span id="153" class="l"><a href="#153"> 153: </a>
</span><span id="154" class="l"><a href="#154"> 154: </a>    <span class="php-comment">/** @var DataTables\Database */</span>
</span><span id="155" class="l"><a href="#155"> 155: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_db</span> = <span class="php-keyword1">null</span>;
</span><span id="156" class="l"><a href="#156"> 156: </a>
</span><span id="157" class="l"><a href="#157"> 157: </a>    <span class="php-comment">/** @var DataTables\Editor\Field[] */</span>
</span><span id="158" class="l"><a href="#158"> 158: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_fields</span> = <span class="php-keyword1">array</span>();
</span><span id="159" class="l"><a href="#159"> 159: </a>
</span><span id="160" class="l"><a href="#160"> 160: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="161" class="l"><a href="#161"> 161: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_formData</span>;
</span><span id="162" class="l"><a href="#162"> 162: </a>
</span><span id="163" class="l"><a href="#163"> 163: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="164" class="l"><a href="#164"> 164: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_processData</span>;
</span><span id="165" class="l"><a href="#165"> 165: </a>
</span><span id="166" class="l"><a href="#166"> 166: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="167" class="l"><a href="#167"> 167: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_idPrefix</span> = <span class="php-quote">'row_'</span>;
</span><span id="168" class="l"><a href="#168"> 168: </a>
</span><span id="169" class="l"><a href="#169"> 169: </a>    <span class="php-comment">/** @var DataTables\Editor\Join[] */</span>
</span><span id="170" class="l"><a href="#170"> 170: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_join</span> = <span class="php-keyword1">array</span>();
</span><span id="171" class="l"><a href="#171"> 171: </a>
</span><span id="172" class="l"><a href="#172"> 172: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="173" class="l"><a href="#173"> 173: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_pkey</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'id'</span>);
</span><span id="174" class="l"><a href="#174"> 174: </a>
</span><span id="175" class="l"><a href="#175"> 175: </a>    <span class="php-comment">/** @var string[] */</span>
</span><span id="176" class="l"><a href="#176"> 176: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_table</span> = <span class="php-keyword1">array</span>();
</span><span id="177" class="l"><a href="#177"> 177: </a>
</span><span id="178" class="l"><a href="#178"> 178: </a>    <span class="php-comment">/** @var string[] */</span>
</span><span id="179" class="l"><a href="#179"> 179: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_readTableNames</span> = <span class="php-keyword1">array</span>();
</span><span id="180" class="l"><a href="#180"> 180: </a>
</span><span id="181" class="l"><a href="#181"> 181: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="182" class="l"><a href="#182"> 182: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_transaction</span> = <span class="php-keyword1">true</span>;
</span><span id="183" class="l"><a href="#183"> 183: </a>
</span><span id="184" class="l"><a href="#184"> 184: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="185" class="l"><a href="#185"> 185: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_where</span> = <span class="php-keyword1">array</span>();
</span><span id="186" class="l"><a href="#186"> 186: </a>
</span><span id="187" class="l"><a href="#187"> 187: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="188" class="l"><a href="#188"> 188: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_leftJoin</span> = <span class="php-keyword1">array</span>();
</span><span id="189" class="l"><a href="#189"> 189: </a>
</span><span id="190" class="l"><a href="#190"> 190: </a>    <span class="php-comment">/** @var boolean - deprecated */</span>
</span><span id="191" class="l"><a href="#191"> 191: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_whereSet</span> = <span class="php-keyword1">false</span>;
</span><span id="192" class="l"><a href="#192"> 192: </a>
</span><span id="193" class="l"><a href="#193"> 193: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="194" class="l"><a href="#194"> 194: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_out</span> = <span class="php-keyword1">array</span>();
</span><span id="195" class="l"><a href="#195"> 195: </a>
</span><span id="196" class="l"><a href="#196"> 196: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="197" class="l"><a href="#197"> 197: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_events</span> = <span class="php-keyword1">array</span>();
</span><span id="198" class="l"><a href="#198"> 198: </a>
</span><span id="199" class="l"><a href="#199"> 199: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="200" class="l"><a href="#200"> 200: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_debug</span> = <span class="php-keyword1">false</span>;
</span><span id="201" class="l"><a href="#201"> 201: </a>
</span><span id="202" class="l"><a href="#202"> 202: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="203" class="l"><a href="#203"> 203: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_debugInfo</span> = <span class="php-keyword1">array</span>();
</span><span id="204" class="l"><a href="#204"> 204: </a>
</span><span id="205" class="l"><a href="#205"> 205: </a>    <span class="php-comment">/** @var string Log output path */</span>
</span><span id="206" class="l"><a href="#206"> 206: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_debugLog</span> = <span class="php-quote">''</span>;
</span><span id="207" class="l"><a href="#207"> 207: </a>
</span><span id="208" class="l"><a href="#208"> 208: </a>    <span class="php-comment">/** @var callback */</span>
</span><span id="209" class="l"><a href="#209"> 209: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_validator</span> = <span class="php-keyword1">null</span>;
</span><span id="210" class="l"><a href="#210"> 210: </a>
</span><span id="211" class="l"><a href="#211"> 211: </a>    <span class="php-comment">/** @var boolean Enable true / catch when processing */</span>
</span><span id="212" class="l"><a href="#212"> 212: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_tryCatch</span> = <span class="php-keyword1">true</span>;
</span><span id="213" class="l"><a href="#213"> 213: </a>
</span><span id="214" class="l"><a href="#214"> 214: </a>    <span class="php-comment">/** @var boolean Enable / disable delete on left joined tables */</span>
</span><span id="215" class="l"><a href="#215"> 215: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_leftJoinRemove</span> = <span class="php-keyword1">false</span>;
</span><span id="216" class="l"><a href="#216"> 216: </a>
</span><span id="217" class="l"><a href="#217"> 217: </a>
</span><span id="218" class="l"><a href="#218"> 218: </a>
</span><span id="219" class="l"><a href="#219"> 219: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="220" class="l"><a href="#220"> 220: </a><span class="php-comment">     * Public methods
</span></span><span id="221" class="l"><a href="#221"> 221: </a><span class="php-comment">     */</span>
</span><span id="222" class="l"><a href="#222"> 222: </a>
</span><span id="223" class="l"><a href="#223"> 223: </a>    <span class="php-comment">/**
</span></span><span id="224" class="l"><a href="#224"> 224: </a><span class="php-comment">     * Get the data constructed in this instance.
</span></span><span id="225" class="l"><a href="#225"> 225: </a><span class="php-comment">     * 
</span></span><span id="226" class="l"><a href="#226"> 226: </a><span class="php-comment">     * This will get the PHP array of data that has been constructed for the 
</span></span><span id="227" class="l"><a href="#227"> 227: </a><span class="php-comment">     * command that has been processed by this instance. Therefore only useful after
</span></span><span id="228" class="l"><a href="#228"> 228: </a><span class="php-comment">     * process has been called.
</span></span><span id="229" class="l"><a href="#229"> 229: </a><span class="php-comment">     *  @return array Processed data array.
</span></span><span id="230" class="l"><a href="#230"> 230: </a><span class="php-comment">     */</span>
</span><span id="231" class="l"><a href="#231"> 231: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> data ()
</span><span id="232" class="l"><a href="#232"> 232: </a>    {
</span><span id="233" class="l"><a href="#233"> 233: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_out;
</span><span id="234" class="l"><a href="#234"> 234: </a>    }
</span><span id="235" class="l"><a href="#235"> 235: </a>
</span><span id="236" class="l"><a href="#236"> 236: </a>
</span><span id="237" class="l"><a href="#237"> 237: </a>    <span class="php-comment">/**
</span></span><span id="238" class="l"><a href="#238"> 238: </a><span class="php-comment">     * Get / set the DB connection instance
</span></span><span id="239" class="l"><a href="#239"> 239: </a><span class="php-comment">     *  @param Database $_ DataTable's Database class instance to use for database
</span></span><span id="240" class="l"><a href="#240"> 240: </a><span class="php-comment">     *    connectivity. If not given, then used as a getter.
</span></span><span id="241" class="l"><a href="#241"> 241: </a><span class="php-comment">     *  @return Database|self The Database connection instance if no parameter
</span></span><span id="242" class="l"><a href="#242"> 242: </a><span class="php-comment">     *    is given, or self if used as a setter.
</span></span><span id="243" class="l"><a href="#243"> 243: </a><span class="php-comment">     */</span>
</span><span id="244" class="l"><a href="#244"> 244: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> db ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="245" class="l"><a href="#245"> 245: </a>    {
</span><span id="246" class="l"><a href="#246"> 246: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_db, <span class="php-var">$_</span> );
</span><span id="247" class="l"><a href="#247"> 247: </a>    }
</span><span id="248" class="l"><a href="#248"> 248: </a>
</span><span id="249" class="l"><a href="#249"> 249: </a>
</span><span id="250" class="l"><a href="#250"> 250: </a>    <span class="php-comment">/**
</span></span><span id="251" class="l"><a href="#251"> 251: </a><span class="php-comment">     * Get / set debug mode and set a debug message.
</span></span><span id="252" class="l"><a href="#252"> 252: </a><span class="php-comment">     *
</span></span><span id="253" class="l"><a href="#253"> 253: </a><span class="php-comment">     * It can be useful to see the SQL statements that Editor is using. This
</span></span><span id="254" class="l"><a href="#254"> 254: </a><span class="php-comment">     * method enables that ability. Information about the queries used is
</span></span><span id="255" class="l"><a href="#255"> 255: </a><span class="php-comment">     * automatically added to the output data array / JSON under the property
</span></span><span id="256" class="l"><a href="#256"> 256: </a><span class="php-comment">     * name `debugSql`.
</span></span><span id="257" class="l"><a href="#257"> 257: </a><span class="php-comment">     * 
</span></span><span id="258" class="l"><a href="#258"> 258: </a><span class="php-comment">     * This method can also be called with a string parameter, which will be
</span></span><span id="259" class="l"><a href="#259"> 259: </a><span class="php-comment">     * added to the debug information sent back to the client-side. This can
</span></span><span id="260" class="l"><a href="#260"> 260: </a><span class="php-comment">     * be useful when debugging event listeners, etc.
</span></span><span id="261" class="l"><a href="#261"> 261: </a><span class="php-comment">     * 
</span></span><span id="262" class="l"><a href="#262"> 262: </a><span class="php-comment">     *  @param boolean|mixed $_ Debug mode state. If not given, then used as a
</span></span><span id="263" class="l"><a href="#263"> 263: </a><span class="php-comment">     *    getter. If given as anything other than a boolean, it will be added
</span></span><span id="264" class="l"><a href="#264"> 264: </a><span class="php-comment">     *    to the debug information sent back to the client.
</span></span><span id="265" class="l"><a href="#265"> 265: </a><span class="php-comment">     *  @param string [$path=null] Set an output path to log debug information
</span></span><span id="266" class="l"><a href="#266"> 266: </a><span class="php-comment">     *  @return boolean|self Debug mode state if no parameter is given, or
</span></span><span id="267" class="l"><a href="#267"> 267: </a><span class="php-comment">     *    self if used as a setter or when adding a debug message.
</span></span><span id="268" class="l"><a href="#268"> 268: </a><span class="php-comment">     */</span>
</span><span id="269" class="l"><a href="#269"> 269: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> debug ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span>, <span class="php-var">$path</span>=<span class="php-keyword1">null</span> )
</span><span id="270" class="l"><a href="#270"> 270: </a>    {
</span><span id="271" class="l"><a href="#271"> 271: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_bool</span>( <span class="php-var">$_</span> ) ) {
</span><span id="272" class="l"><a href="#272"> 272: </a>            <span class="php-var">$this</span>-&gt;_debugInfo[] = <span class="php-var">$_</span>;
</span><span id="273" class="l"><a href="#273"> 273: </a>
</span><span id="274" class="l"><a href="#274"> 274: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="275" class="l"><a href="#275"> 275: </a>        }
</span><span id="276" class="l"><a href="#276"> 276: </a>
</span><span id="277" class="l"><a href="#277"> 277: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$path</span> ) {
</span><span id="278" class="l"><a href="#278"> 278: </a>            <span class="php-var">$this</span>-&gt;_debugLog = <span class="php-var">$path</span>;
</span><span id="279" class="l"><a href="#279"> 279: </a>        }
</span><span id="280" class="l"><a href="#280"> 280: </a>
</span><span id="281" class="l"><a href="#281"> 281: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_debug, <span class="php-var">$_</span> );
</span><span id="282" class="l"><a href="#282"> 282: </a>    }
</span><span id="283" class="l"><a href="#283"> 283: </a>
</span><span id="284" class="l"><a href="#284"> 284: </a>
</span><span id="285" class="l"><a href="#285"> 285: </a>    <span class="php-comment">/**
</span></span><span id="286" class="l"><a href="#286"> 286: </a><span class="php-comment">     * Get / set field instance.
</span></span><span id="287" class="l"><a href="#287"> 287: </a><span class="php-comment">     * 
</span></span><span id="288" class="l"><a href="#288"> 288: </a><span class="php-comment">     * The list of fields designates which columns in the table that Editor will work
</span></span><span id="289" class="l"><a href="#289"> 289: </a><span class="php-comment">     * with (both get and set).
</span></span><span id="290" class="l"><a href="#290"> 290: </a><span class="php-comment">     *  @param Field|string $_... This parameter effects the return value of the
</span></span><span id="291" class="l"><a href="#291"> 291: </a><span class="php-comment">     *      function:
</span></span><span id="292" class="l"><a href="#292"> 292: </a><span class="php-comment">     *
</span></span><span id="293" class="l"><a href="#293"> 293: </a><span class="php-comment">     *      * `null` - Get an array of all fields assigned to the instance
</span></span><span id="294" class="l"><a href="#294"> 294: </a><span class="php-comment">     *      * `string` - Get a specific field instance whose 'name' matches the
</span></span><span id="295" class="l"><a href="#295"> 295: </a><span class="php-comment">     *           field passed in
</span></span><span id="296" class="l"><a href="#296"> 296: </a><span class="php-comment">     *      * {@link Field} - Add a field to the instance's list of fields. This
</span></span><span id="297" class="l"><a href="#297"> 297: </a><span class="php-comment">     *           can be as many fields as required (i.e. multiple arguments)
</span></span><span id="298" class="l"><a href="#298"> 298: </a><span class="php-comment">     *      * `array` - An array of {@link Field} instances to add to the list
</span></span><span id="299" class="l"><a href="#299"> 299: </a><span class="php-comment">     *        of fields.
</span></span><span id="300" class="l"><a href="#300"> 300: </a><span class="php-comment">     *  @return Field|Field[]|Editor The selected field, an array of fields, or
</span></span><span id="301" class="l"><a href="#301"> 301: </a><span class="php-comment">     *      the Editor instance for chaining, depending on the input parameter.
</span></span><span id="302" class="l"><a href="#302"> 302: </a><span class="php-comment">     *  @throws \Exception Unkown field error
</span></span><span id="303" class="l"><a href="#303"> 303: </a><span class="php-comment">     *  @see {@link Field} for field documentation.
</span></span><span id="304" class="l"><a href="#304"> 304: </a><span class="php-comment">     */</span>
</span><span id="305" class="l"><a href="#305"> 305: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> field ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="306" class="l"><a href="#306"> 306: </a>    {
</span><span id="307" class="l"><a href="#307"> 307: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_string</span>( <span class="php-var">$_</span> ) ) {
</span><span id="308" class="l"><a href="#308"> 308: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="309" class="l"><a href="#309"> 309: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>]-&gt;name() === <span class="php-var">$_</span> ) {
</span><span id="310" class="l"><a href="#310"> 310: </a>                    <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="311" class="l"><a href="#311"> 311: </a>                }
</span><span id="312" class="l"><a href="#312"> 312: </a>            }
</span><span id="313" class="l"><a href="#313"> 313: </a>
</span><span id="314" class="l"><a href="#314"> 314: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Unknown field: '</span>.<span class="php-var">$_</span>);
</span><span id="315" class="l"><a href="#315"> 315: </a>        }
</span><span id="316" class="l"><a href="#316"> 316: </a>
</span><span id="317" class="l"><a href="#317"> 317: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="318" class="l"><a href="#318"> 318: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="319" class="l"><a href="#319"> 319: </a>        }
</span><span id="320" class="l"><a href="#320"> 320: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_fields, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="321" class="l"><a href="#321"> 321: </a>    }
</span><span id="322" class="l"><a href="#322"> 322: </a>
</span><span id="323" class="l"><a href="#323"> 323: </a>
</span><span id="324" class="l"><a href="#324"> 324: </a>    <span class="php-comment">/**
</span></span><span id="325" class="l"><a href="#325"> 325: </a><span class="php-comment">     * Get / set field instances.
</span></span><span id="326" class="l"><a href="#326"> 326: </a><span class="php-comment">     * 
</span></span><span id="327" class="l"><a href="#327"> 327: </a><span class="php-comment">     * An alias of {@link field}, for convenience.
</span></span><span id="328" class="l"><a href="#328"> 328: </a><span class="php-comment">     *  @param Field $_... Instances of the {@link Field} class, given as a single 
</span></span><span id="329" class="l"><a href="#329"> 329: </a><span class="php-comment">     *    instance of {@link Field}, an array of {@link Field} instances, or multiple
</span></span><span id="330" class="l"><a href="#330"> 330: </a><span class="php-comment">     *    {@link Field} instance parameters for the function.
</span></span><span id="331" class="l"><a href="#331"> 331: </a><span class="php-comment">     *  @return Field[]|self Array of fields, or self if used as a setter.
</span></span><span id="332" class="l"><a href="#332"> 332: </a><span class="php-comment">     *  @see {@link Field} for field documentation.
</span></span><span id="333" class="l"><a href="#333"> 333: </a><span class="php-comment">     */</span>
</span><span id="334" class="l"><a href="#334"> 334: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> fields ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="335" class="l"><a href="#335"> 335: </a>    {
</span><span id="336" class="l"><a href="#336"> 336: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="337" class="l"><a href="#337"> 337: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="338" class="l"><a href="#338"> 338: </a>        }
</span><span id="339" class="l"><a href="#339"> 339: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_fields, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="340" class="l"><a href="#340"> 340: </a>    }
</span><span id="341" class="l"><a href="#341"> 341: </a>
</span><span id="342" class="l"><a href="#342"> 342: </a>
</span><span id="343" class="l"><a href="#343"> 343: </a>    <span class="php-comment">/**
</span></span><span id="344" class="l"><a href="#344"> 344: </a><span class="php-comment">     * Get / set the DOM prefix.
</span></span><span id="345" class="l"><a href="#345"> 345: </a><span class="php-comment">     *
</span></span><span id="346" class="l"><a href="#346"> 346: </a><span class="php-comment">     * Typically primary keys are numeric and this is not a valid ID value in an
</span></span><span id="347" class="l"><a href="#347"> 347: </a><span class="php-comment">     * HTML document - is also increases the likelihood of an ID clash if multiple
</span></span><span id="348" class="l"><a href="#348"> 348: </a><span class="php-comment">     * tables are used on a single page. As such, a prefix is assigned to the 
</span></span><span id="349" class="l"><a href="#349"> 349: </a><span class="php-comment">     * primary key value for each row, and this is used as the DOM ID, so Editor
</span></span><span id="350" class="l"><a href="#350"> 350: </a><span class="php-comment">     * can track individual rows.
</span></span><span id="351" class="l"><a href="#351"> 351: </a><span class="php-comment">     *  @param string $_ Primary key's name. If not given, then used as a getter.
</span></span><span id="352" class="l"><a href="#352"> 352: </a><span class="php-comment">     *  @return string|self Primary key value if no parameter is given, or
</span></span><span id="353" class="l"><a href="#353"> 353: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="354" class="l"><a href="#354"> 354: </a><span class="php-comment">     */</span>
</span><span id="355" class="l"><a href="#355"> 355: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> idPrefix ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="356" class="l"><a href="#356"> 356: </a>    {
</span><span id="357" class="l"><a href="#357"> 357: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_idPrefix, <span class="php-var">$_</span> );
</span><span id="358" class="l"><a href="#358"> 358: </a>    }
</span><span id="359" class="l"><a href="#359"> 359: </a>
</span><span id="360" class="l"><a href="#360"> 360: </a>
</span><span id="361" class="l"><a href="#361"> 361: </a>    <span class="php-comment">/**
</span></span><span id="362" class="l"><a href="#362"> 362: </a><span class="php-comment">     * Get the data that is being processed by the Editor instance. This is only
</span></span><span id="363" class="l"><a href="#363"> 363: </a><span class="php-comment">     * useful once the `process()` method has been called, and is available for
</span></span><span id="364" class="l"><a href="#364"> 364: </a><span class="php-comment">     * use in validation and formatter methods.
</span></span><span id="365" class="l"><a href="#365"> 365: </a><span class="php-comment">     *
</span></span><span id="366" class="l"><a href="#366"> 366: </a><span class="php-comment">     *   @return array Data given to `process()`.
</span></span><span id="367" class="l"><a href="#367"> 367: </a><span class="php-comment">     */</span>
</span><span id="368" class="l"><a href="#368"> 368: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> inData ()
</span><span id="369" class="l"><a href="#369"> 369: </a>    {
</span><span id="370" class="l"><a href="#370"> 370: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_processData;
</span><span id="371" class="l"><a href="#371"> 371: </a>    }
</span><span id="372" class="l"><a href="#372"> 372: </a>
</span><span id="373" class="l"><a href="#373"> 373: </a>
</span><span id="374" class="l"><a href="#374"> 374: </a>    <span class="php-comment">/**
</span></span><span id="375" class="l"><a href="#375"> 375: </a><span class="php-comment">     * Get / set join instances. Note that for the majority of use cases you
</span></span><span id="376" class="l"><a href="#376"> 376: </a><span class="php-comment">     * will want to use the `leftJoin()` method. It is significantly easier
</span></span><span id="377" class="l"><a href="#377"> 377: </a><span class="php-comment">     * to use if you are just doing a simple left join!
</span></span><span id="378" class="l"><a href="#378"> 378: </a><span class="php-comment">     * 
</span></span><span id="379" class="l"><a href="#379"> 379: </a><span class="php-comment">     * The list of Join instances that Editor will join the parent table to
</span></span><span id="380" class="l"><a href="#380"> 380: </a><span class="php-comment">     * (i.e. the one that the {@link table} and {@link fields} methods refer to
</span></span><span id="381" class="l"><a href="#381"> 381: </a><span class="php-comment">     * in this class instance).
</span></span><span id="382" class="l"><a href="#382"> 382: </a><span class="php-comment">     *
</span></span><span id="383" class="l"><a href="#383"> 383: </a><span class="php-comment">     *  @param Join $_,... Instances of the {@link Join} class, given as a
</span></span><span id="384" class="l"><a href="#384"> 384: </a><span class="php-comment">     *    single instance of {@link Join}, an array of {@link Join} instances,
</span></span><span id="385" class="l"><a href="#385"> 385: </a><span class="php-comment">     *    or multiple {@link Join} instance parameters for the function.
</span></span><span id="386" class="l"><a href="#386"> 386: </a><span class="php-comment">     *  @return Join[]|self Array of joins, or self if used as a setter.
</span></span><span id="387" class="l"><a href="#387"> 387: </a><span class="php-comment">     *  @see {@link Join} for joining documentation.
</span></span><span id="388" class="l"><a href="#388"> 388: </a><span class="php-comment">     */</span>
</span><span id="389" class="l"><a href="#389"> 389: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">join</span> ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="390" class="l"><a href="#390"> 390: </a>    {
</span><span id="391" class="l"><a href="#391"> 391: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="392" class="l"><a href="#392"> 392: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="393" class="l"><a href="#393"> 393: </a>        }
</span><span id="394" class="l"><a href="#394"> 394: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_join, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="395" class="l"><a href="#395"> 395: </a>    }
</span><span id="396" class="l"><a href="#396"> 396: </a>
</span><span id="397" class="l"><a href="#397"> 397: </a>
</span><span id="398" class="l"><a href="#398"> 398: </a>    <span class="php-comment">/**
</span></span><span id="399" class="l"><a href="#399"> 399: </a><span class="php-comment">     * Get the JSON for the data constructed in this instance.
</span></span><span id="400" class="l"><a href="#400"> 400: </a><span class="php-comment">     * 
</span></span><span id="401" class="l"><a href="#401"> 401: </a><span class="php-comment">     * Basically the same as the {@link data} method, but in this case we echo, or
</span></span><span id="402" class="l"><a href="#402"> 402: </a><span class="php-comment">     * return the JSON string of the data.
</span></span><span id="403" class="l"><a href="#403"> 403: </a><span class="php-comment">     *  @param boolean $print Echo the JSON string out (true, default) or return it
</span></span><span id="404" class="l"><a href="#404"> 404: </a><span class="php-comment">     *    (false).
</span></span><span id="405" class="l"><a href="#405"> 405: </a><span class="php-comment">     *  @return string|self self if printing the JSON, or JSON representation of 
</span></span><span id="406" class="l"><a href="#406"> 406: </a><span class="php-comment">     *    the processed data if false is given as the first parameter.
</span></span><span id="407" class="l"><a href="#407"> 407: </a><span class="php-comment">     */</span>
</span><span id="408" class="l"><a href="#408"> 408: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> json ( <span class="php-var">$print</span>=<span class="php-keyword1">true</span> )
</span><span id="409" class="l"><a href="#409"> 409: </a>    {
</span><span id="410" class="l"><a href="#410"> 410: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$print</span> ) {
</span><span id="411" class="l"><a href="#411"> 411: </a>            <span class="php-var">$json</span> = <span class="php-keyword2">json_encode</span>( <span class="php-var">$this</span>-&gt;_out );
</span><span id="412" class="l"><a href="#412"> 412: </a>
</span><span id="413" class="l"><a href="#413"> 413: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$json</span> !== <span class="php-keyword1">false</span> ) {
</span><span id="414" class="l"><a href="#414"> 414: </a>                <span class="php-keyword1">echo</span> <span class="php-var">$json</span>;
</span><span id="415" class="l"><a href="#415"> 415: </a>            }
</span><span id="416" class="l"><a href="#416"> 416: </a>            <span class="php-keyword1">else</span> {
</span><span id="417" class="l"><a href="#417"> 417: </a>                <span class="php-keyword1">echo</span> <span class="php-keyword2">json_encode</span>( <span class="php-keyword1">array</span>(
</span><span id="418" class="l"><a href="#418"> 418: </a>                    <span class="php-quote">&quot;error&quot;</span> =&gt; <span class="php-quote">&quot;JSON encoding error: &quot;</span>.json_last_error_msg()
</span><span id="419" class="l"><a href="#419"> 419: </a>                ) );
</span><span id="420" class="l"><a href="#420"> 420: </a>            }
</span><span id="421" class="l"><a href="#421"> 421: </a>
</span><span id="422" class="l"><a href="#422"> 422: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="423" class="l"><a href="#423"> 423: </a>        }
</span><span id="424" class="l"><a href="#424"> 424: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">json_encode</span>( <span class="php-var">$this</span>-&gt;_out );
</span><span id="425" class="l"><a href="#425"> 425: </a>    }
</span><span id="426" class="l"><a href="#426"> 426: </a>
</span><span id="427" class="l"><a href="#427"> 427: </a>
</span><span id="428" class="l"><a href="#428"> 428: </a>    <span class="php-comment">/**
</span></span><span id="429" class="l"><a href="#429"> 429: </a><span class="php-comment">     * Echo out JSONP for the data constructed and processed in this instance.
</span></span><span id="430" class="l"><a href="#430"> 430: </a><span class="php-comment">     * This is basically the same as {@link json} but wraps the return in a
</span></span><span id="431" class="l"><a href="#431"> 431: </a><span class="php-comment">     * JSONP callback.
</span></span><span id="432" class="l"><a href="#432"> 432: </a><span class="php-comment">     *
</span></span><span id="433" class="l"><a href="#433"> 433: </a><span class="php-comment">     * @param string $callback The callback function name to use. If not given
</span></span><span id="434" class="l"><a href="#434"> 434: </a><span class="php-comment">     *    or `null`, then `$_GET['callback']` is used (the jQuery default).
</span></span><span id="435" class="l"><a href="#435"> 435: </a><span class="php-comment">     * @return self Self for chaining.
</span></span><span id="436" class="l"><a href="#436"> 436: </a><span class="php-comment">     * @throws \Exception JSONP function name validation
</span></span><span id="437" class="l"><a href="#437"> 437: </a><span class="php-comment">     */</span>
</span><span id="438" class="l"><a href="#438"> 438: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> jsonp ( <span class="php-var">$callback</span>=<span class="php-keyword1">null</span> )
</span><span id="439" class="l"><a href="#439"> 439: </a>    {
</span><span id="440" class="l"><a href="#440"> 440: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$callback</span> ) {
</span><span id="441" class="l"><a href="#441"> 441: </a>            <span class="php-var">$callback</span> = <span class="php-var">$_GET</span>[<span class="php-quote">'callback'</span>];
</span><span id="442" class="l"><a href="#442"> 442: </a>        }
</span><span id="443" class="l"><a href="#443"> 443: </a>
</span><span id="444" class="l"><a href="#444"> 444: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">preg_match</span>(<span class="php-quote">'/[^a-zA-Z0-9_]/'</span>, <span class="php-var">$callback</span>) ) {
</span><span id="445" class="l"><a href="#445"> 445: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Invalid JSONP callback function name&quot;</span>);
</span><span id="446" class="l"><a href="#446"> 446: </a>        }
</span><span id="447" class="l"><a href="#447"> 447: </a>
</span><span id="448" class="l"><a href="#448"> 448: </a>        <span class="php-keyword1">echo</span> <span class="php-var">$callback</span>.<span class="php-quote">'('</span>.<span class="php-keyword2">json_encode</span>( <span class="php-var">$this</span>-&gt;_out ).<span class="php-quote">');'</span>;
</span><span id="449" class="l"><a href="#449"> 449: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="450" class="l"><a href="#450"> 450: </a>    }
</span><span id="451" class="l"><a href="#451"> 451: </a>
</span><span id="452" class="l"><a href="#452"> 452: </a>
</span><span id="453" class="l"><a href="#453"> 453: </a>    <span class="php-comment">/**
</span></span><span id="454" class="l"><a href="#454"> 454: </a><span class="php-comment">     * Add a left join condition to the Editor instance, allowing it to operate
</span></span><span id="455" class="l"><a href="#455"> 455: </a><span class="php-comment">     * over multiple tables. Multiple `leftJoin()` calls can be made for a
</span></span><span id="456" class="l"><a href="#456"> 456: </a><span class="php-comment">     * single Editor instance to join multiple tables.
</span></span><span id="457" class="l"><a href="#457"> 457: </a><span class="php-comment">     *
</span></span><span id="458" class="l"><a href="#458"> 458: </a><span class="php-comment">     * A left join is the most common type of join that is used with Editor
</span></span><span id="459" class="l"><a href="#459"> 459: </a><span class="php-comment">     * so this method is provided to make its use very easy to configure. Its
</span></span><span id="460" class="l"><a href="#460"> 460: </a><span class="php-comment">     * parameters are basically the same as writing an SQL left join statement,
</span></span><span id="461" class="l"><a href="#461"> 461: </a><span class="php-comment">     * but in this case Editor will handle the create, update and remove
</span></span><span id="462" class="l"><a href="#462"> 462: </a><span class="php-comment">     * requirements of the join for you:
</span></span><span id="463" class="l"><a href="#463"> 463: </a><span class="php-comment">     *
</span></span><span id="464" class="l"><a href="#464"> 464: </a><span class="php-comment">     * * Create - On create Editor will insert the data into the primary table
</span></span><span id="465" class="l"><a href="#465"> 465: </a><span class="php-comment">     *   and then into the joined tables - selecting the required data for each
</span></span><span id="466" class="l"><a href="#466"> 466: </a><span class="php-comment">     *   table.
</span></span><span id="467" class="l"><a href="#467"> 467: </a><span class="php-comment">     * * Edit - On edit Editor will update the main table, and then either
</span></span><span id="468" class="l"><a href="#468"> 468: </a><span class="php-comment">     *   update the existing rows in the joined table that match the join and
</span></span><span id="469" class="l"><a href="#469"> 469: </a><span class="php-comment">     *   edit conditions, or insert a new row into the joined table if required.
</span></span><span id="470" class="l"><a href="#470"> 470: </a><span class="php-comment">     * * Remove - On delete Editor will remove the main row and then loop over
</span></span><span id="471" class="l"><a href="#471"> 471: </a><span class="php-comment">     *   each of the joined tables and remove the joined data matching the join
</span></span><span id="472" class="l"><a href="#472"> 472: </a><span class="php-comment">     *   link from the main table.
</span></span><span id="473" class="l"><a href="#473"> 473: </a><span class="php-comment">     *
</span></span><span id="474" class="l"><a href="#474"> 474: </a><span class="php-comment">     * Please note that when using join tables, Editor requires that you fully
</span></span><span id="475" class="l"><a href="#475"> 475: </a><span class="php-comment">     * qualify each field with the field's table name. SQL can result table
</span></span><span id="476" class="l"><a href="#476"> 476: </a><span class="php-comment">     * names for ambiguous field names, but for Editor to provide its full CRUD
</span></span><span id="477" class="l"><a href="#477"> 477: </a><span class="php-comment">     * options, the table name must also be given. For example the field
</span></span><span id="478" class="l"><a href="#478"> 478: </a><span class="php-comment">     * `first_name` in the table `users` would be given as `users.first_name`.
</span></span><span id="479" class="l"><a href="#479"> 479: </a><span class="php-comment">     *
</span></span><span id="480" class="l"><a href="#480"> 480: </a><span class="php-comment">     * @param string $table Table name to do a join onto
</span></span><span id="481" class="l"><a href="#481"> 481: </a><span class="php-comment">     * @param string $field1 Field from the parent table to use as the join link
</span></span><span id="482" class="l"><a href="#482"> 482: </a><span class="php-comment">     * @param string $operator Join condition (`=`, '&lt;`, etc)
</span></span><span id="483" class="l"><a href="#483"> 483: </a><span class="php-comment">     * @param string $field2 Field from the child table to use as the join link
</span></span><span id="484" class="l"><a href="#484"> 484: </a><span class="php-comment">     * @return self Self for chaining.
</span></span><span id="485" class="l"><a href="#485"> 485: </a><span class="php-comment">     *
</span></span><span id="486" class="l"><a href="#486"> 486: </a><span class="php-comment">     * @example 
</span></span><span id="487" class="l"><a href="#487"> 487: </a><span class="php-comment">     *    Simple join:
</span></span><span id="488" class="l"><a href="#488"> 488: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="489" class="l"><a href="#489"> 489: </a><span class="php-comment">     *        -&gt;field( 
</span></span><span id="490" class="l"><a href="#490"> 490: </a><span class="php-comment">     *          Field::inst( 'users.first_name as myField' ),
</span></span><span id="491" class="l"><a href="#491"> 491: </a><span class="php-comment">     *          Field::inst( 'users.last_name' ),
</span></span><span id="492" class="l"><a href="#492"> 492: </a><span class="php-comment">     *          Field::inst( 'users.dept_id' ),
</span></span><span id="493" class="l"><a href="#493"> 493: </a><span class="php-comment">     *          Field::inst( 'dept.name' )
</span></span><span id="494" class="l"><a href="#494"> 494: </a><span class="php-comment">     *        )
</span></span><span id="495" class="l"><a href="#495"> 495: </a><span class="php-comment">     *        -&gt;leftJoin( 'dept', 'users.dept_id', '=', 'dept.id' )
</span></span><span id="496" class="l"><a href="#496"> 496: </a><span class="php-comment">     *        -&gt;process($_POST)
</span></span><span id="497" class="l"><a href="#497"> 497: </a><span class="php-comment">     *        -&gt;json();
</span></span><span id="498" class="l"><a href="#498"> 498: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="499" class="l"><a href="#499"> 499: </a><span class="php-comment">     *
</span></span><span id="500" class="l"><a href="#500"> 500: </a><span class="php-comment">     *    This is basically the same as the following SQL statement:
</span></span><span id="501" class="l"><a href="#501"> 501: </a><span class="php-comment">     * 
</span></span><span id="502" class="l"><a href="#502"> 502: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="503" class="l"><a href="#503"> 503: </a><span class="php-comment">     *      SELECT users.first_name, users.last_name, user.dept_id, dept.name
</span></span><span id="504" class="l"><a href="#504"> 504: </a><span class="php-comment">     *      FROM users
</span></span><span id="505" class="l"><a href="#505"> 505: </a><span class="php-comment">     *      LEFT JOIN dept ON users.dept_id = dept.id
</span></span><span id="506" class="l"><a href="#506"> 506: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="507" class="l"><a href="#507"> 507: </a><span class="php-comment">     */</span>
</span><span id="508" class="l"><a href="#508"> 508: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> leftJoin ( <span class="php-var">$table</span>, <span class="php-var">$field1</span>, <span class="php-var">$operator</span>, <span class="php-var">$field2</span> )
</span><span id="509" class="l"><a href="#509"> 509: </a>    {
</span><span id="510" class="l"><a href="#510"> 510: </a>        <span class="php-var">$this</span>-&gt;_leftJoin[] = <span class="php-keyword1">array</span>(
</span><span id="511" class="l"><a href="#511"> 511: </a>            <span class="php-quote">&quot;table&quot;</span>    =&gt; <span class="php-var">$table</span>,
</span><span id="512" class="l"><a href="#512"> 512: </a>            <span class="php-quote">&quot;field1&quot;</span>   =&gt; <span class="php-var">$field1</span>,
</span><span id="513" class="l"><a href="#513"> 513: </a>            <span class="php-quote">&quot;field2&quot;</span>   =&gt; <span class="php-var">$field2</span>,
</span><span id="514" class="l"><a href="#514"> 514: </a>            <span class="php-quote">&quot;operator&quot;</span> =&gt; <span class="php-var">$operator</span>
</span><span id="515" class="l"><a href="#515"> 515: </a>        );
</span><span id="516" class="l"><a href="#516"> 516: </a>
</span><span id="517" class="l"><a href="#517"> 517: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="518" class="l"><a href="#518"> 518: </a>    }
</span><span id="519" class="l"><a href="#519"> 519: </a>
</span><span id="520" class="l"><a href="#520"> 520: </a>
</span><span id="521" class="l"><a href="#521"> 521: </a>    <span class="php-comment">/**
</span></span><span id="522" class="l"><a href="#522"> 522: </a><span class="php-comment">     * Indicate if a remove should be performed on left joined tables when deleting
</span></span><span id="523" class="l"><a href="#523"> 523: </a><span class="php-comment">     * from the parent row. Note that this is disabled by default and will be
</span></span><span id="524" class="l"><a href="#524"> 524: </a><span class="php-comment">     * removed completely in v2. Use `ON DELETE CASCADE` in your database instead.
</span></span><span id="525" class="l"><a href="#525"> 525: </a><span class="php-comment">     * 
</span></span><span id="526" class="l"><a href="#526"> 526: </a><span class="php-comment">     *  @deprecated
</span></span><span id="527" class="l"><a href="#527"> 527: </a><span class="php-comment">     *  @param boolean $_ Value to set. If not given, then used as a getter.
</span></span><span id="528" class="l"><a href="#528"> 528: </a><span class="php-comment">     *  @return boolean|self Value if no parameter is given, or
</span></span><span id="529" class="l"><a href="#529"> 529: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="530" class="l"><a href="#530"> 530: </a><span class="php-comment">     */</span>
</span><span id="531" class="l"><a href="#531"> 531: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> leftJoinRemove ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="532" class="l"><a href="#532"> 532: </a>    {
</span><span id="533" class="l"><a href="#533"> 533: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_leftJoinRemove, <span class="php-var">$_</span> );
</span><span id="534" class="l"><a href="#534"> 534: </a>    }
</span><span id="535" class="l"><a href="#535"> 535: </a>
</span><span id="536" class="l"><a href="#536"> 536: </a>
</span><span id="537" class="l"><a href="#537"> 537: </a>    <span class="php-comment">/**
</span></span><span id="538" class="l"><a href="#538"> 538: </a><span class="php-comment">     * Add an event listener. The `Editor` class will trigger an number of
</span></span><span id="539" class="l"><a href="#539"> 539: </a><span class="php-comment">     * events that some action can be taken on.
</span></span><span id="540" class="l"><a href="#540"> 540: </a><span class="php-comment">     *
</span></span><span id="541" class="l"><a href="#541"> 541: </a><span class="php-comment">     * @param  string $name     Event name
</span></span><span id="542" class="l"><a href="#542"> 542: </a><span class="php-comment">     * @param  callable $callback Callback function to execute when the event
</span></span><span id="543" class="l"><a href="#543"> 543: </a><span class="php-comment">     *     occurs
</span></span><span id="544" class="l"><a href="#544"> 544: </a><span class="php-comment">     * @return self Self for chaining.
</span></span><span id="545" class="l"><a href="#545"> 545: </a><span class="php-comment">     */</span>
</span><span id="546" class="l"><a href="#546"> 546: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> on ( <span class="php-var">$name</span>, <span class="php-var">$callback</span> )
</span><span id="547" class="l"><a href="#547"> 547: </a>    {
</span><span id="548" class="l"><a href="#548"> 548: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$this</span>-&gt;_events[ <span class="php-var">$name</span> ] ) ) {
</span><span id="549" class="l"><a href="#549"> 549: </a>            <span class="php-var">$this</span>-&gt;_events[ <span class="php-var">$name</span> ] = <span class="php-keyword1">array</span>();
</span><span id="550" class="l"><a href="#550"> 550: </a>        }
</span><span id="551" class="l"><a href="#551"> 551: </a>
</span><span id="552" class="l"><a href="#552"> 552: </a>        <span class="php-var">$this</span>-&gt;_events[ <span class="php-var">$name</span> ][] = <span class="php-var">$callback</span>;
</span><span id="553" class="l"><a href="#553"> 553: </a>
</span><span id="554" class="l"><a href="#554"> 554: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="555" class="l"><a href="#555"> 555: </a>    }
</span><span id="556" class="l"><a href="#556"> 556: </a>
</span><span id="557" class="l"><a href="#557"> 557: </a>
</span><span id="558" class="l"><a href="#558"> 558: </a>    <span class="php-comment">/**
</span></span><span id="559" class="l"><a href="#559"> 559: </a><span class="php-comment">     * Get / set the primary key.
</span></span><span id="560" class="l"><a href="#560"> 560: </a><span class="php-comment">     *
</span></span><span id="561" class="l"><a href="#561"> 561: </a><span class="php-comment">     * The primary key must be known to Editor so it will know which rows are being
</span></span><span id="562" class="l"><a href="#562"> 562: </a><span class="php-comment">     * edited / deleted upon those actions. The default value is ['id'].
</span></span><span id="563" class="l"><a href="#563"> 563: </a><span class="php-comment">     *
</span></span><span id="564" class="l"><a href="#564"> 564: </a><span class="php-comment">     *  @param string|array $_ Primary key's name. If not given, then used as a
</span></span><span id="565" class="l"><a href="#565"> 565: </a><span class="php-comment">     *    getter. An array of column names can be given to allow composite keys to
</span></span><span id="566" class="l"><a href="#566"> 566: </a><span class="php-comment">     *    be used.
</span></span><span id="567" class="l"><a href="#567"> 567: </a><span class="php-comment">     *  @return string|self Primary key value if no parameter is given, or
</span></span><span id="568" class="l"><a href="#568"> 568: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="569" class="l"><a href="#569"> 569: </a><span class="php-comment">     */</span>
</span><span id="570" class="l"><a href="#570"> 570: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> pkey ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="571" class="l"><a href="#571"> 571: </a>    {
</span><span id="572" class="l"><a href="#572"> 572: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_string</span>( <span class="php-var">$_</span> ) ) {
</span><span id="573" class="l"><a href="#573"> 573: </a>            <span class="php-var">$this</span>-&gt;_pkey = <span class="php-keyword1">array</span>( <span class="php-var">$_</span> );
</span><span id="574" class="l"><a href="#574"> 574: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="575" class="l"><a href="#575"> 575: </a>        }
</span><span id="576" class="l"><a href="#576"> 576: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_pkey, <span class="php-var">$_</span> );
</span><span id="577" class="l"><a href="#577"> 577: </a>    }
</span><span id="578" class="l"><a href="#578"> 578: </a>
</span><span id="579" class="l"><a href="#579"> 579: </a>
</span><span id="580" class="l"><a href="#580"> 580: </a>    <span class="php-comment">/**
</span></span><span id="581" class="l"><a href="#581"> 581: </a><span class="php-comment">     * Convert a primary key array of field values to a combined value.
</span></span><span id="582" class="l"><a href="#582"> 582: </a><span class="php-comment">     *
</span></span><span id="583" class="l"><a href="#583"> 583: </a><span class="php-comment">     * @param  string  $row   The row of data that the primary key value should
</span></span><span id="584" class="l"><a href="#584"> 584: </a><span class="php-comment">     *   be extracted from.
</span></span><span id="585" class="l"><a href="#585"> 585: </a><span class="php-comment">     * @param  boolean $flat  Flag to indicate if the given array is flat
</span></span><span id="586" class="l"><a href="#586"> 586: </a><span class="php-comment">     *   (useful for `where` conditions) or nested for join tables.
</span></span><span id="587" class="l"><a href="#587"> 587: </a><span class="php-comment">     * @return string The created primary key value.
</span></span><span id="588" class="l"><a href="#588"> 588: </a><span class="php-comment">     * @throws \Exception If one of the values that the primary key is made up
</span></span><span id="589" class="l"><a href="#589"> 589: </a><span class="php-comment">     *    of cannot be found in the data set given, an Exception will be thrown.
</span></span><span id="590" class="l"><a href="#590"> 590: </a><span class="php-comment">     */</span>
</span><span id="591" class="l"><a href="#591"> 591: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> pkeyToValue ( <span class="php-var">$row</span>, <span class="php-var">$flat</span>=<span class="php-keyword1">false</span> )
</span><span id="592" class="l"><a href="#592"> 592: </a>    {
</span><span id="593" class="l"><a href="#593"> 593: </a>        <span class="php-var">$pkey</span> = <span class="php-var">$this</span>-&gt;_pkey;
</span><span id="594" class="l"><a href="#594"> 594: </a>        <span class="php-var">$id</span> = <span class="php-keyword1">array</span>();
</span><span id="595" class="l"><a href="#595"> 595: </a>
</span><span id="596" class="l"><a href="#596"> 596: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$pkey</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="597" class="l"><a href="#597"> 597: </a>            <span class="php-var">$column</span> = <span class="php-var">$pkey</span>[ <span class="php-var">$i</span> ];
</span><span id="598" class="l"><a href="#598"> 598: </a>
</span><span id="599" class="l"><a href="#599"> 599: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$flat</span> ) {
</span><span id="600" class="l"><a href="#600"> 600: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$row</span>[ <span class="php-var">$column</span> ] ) ) {
</span><span id="601" class="l"><a href="#601"> 601: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$row</span>[ <span class="php-var">$column</span> ] === <span class="php-keyword1">null</span> ) {
</span><span id="602" class="l"><a href="#602"> 602: </a>                        <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Primary key value is null.&quot;</span>, <span class="php-num">1</span>);
</span><span id="603" class="l"><a href="#603"> 603: </a>                    }
</span><span id="604" class="l"><a href="#604"> 604: </a>                    <span class="php-var">$val</span> = <span class="php-var">$row</span>[ <span class="php-var">$column</span> ];
</span><span id="605" class="l"><a href="#605"> 605: </a>                }
</span><span id="606" class="l"><a href="#606"> 606: </a>                <span class="php-keyword1">else</span> {
</span><span id="607" class="l"><a href="#607"> 607: </a>                    <span class="php-var">$val</span> = <span class="php-keyword1">null</span>;
</span><span id="608" class="l"><a href="#608"> 608: </a>                }
</span><span id="609" class="l"><a href="#609"> 609: </a>            }
</span><span id="610" class="l"><a href="#610"> 610: </a>            <span class="php-keyword1">else</span> {
</span><span id="611" class="l"><a href="#611"> 611: </a>                <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;_readProp( <span class="php-var">$column</span>, <span class="php-var">$row</span> );
</span><span id="612" class="l"><a href="#612"> 612: </a>            }
</span><span id="613" class="l"><a href="#613"> 613: </a>
</span><span id="614" class="l"><a href="#614"> 614: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$val</span> === <span class="php-keyword1">null</span> ) {
</span><span id="615" class="l"><a href="#615"> 615: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Primary key element is not available in data set.&quot;</span>, <span class="php-num">1</span>);
</span><span id="616" class="l"><a href="#616"> 616: </a>            }
</span><span id="617" class="l"><a href="#617"> 617: </a>
</span><span id="618" class="l"><a href="#618"> 618: </a>            <span class="php-var">$id</span>[] = <span class="php-var">$val</span>;
</span><span id="619" class="l"><a href="#619"> 619: </a>        }
</span><span id="620" class="l"><a href="#620"> 620: </a>
</span><span id="621" class="l"><a href="#621"> 621: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">implode</span>( <span class="php-var">$this</span>-&gt;_pkey_separator(), <span class="php-var">$id</span> );
</span><span id="622" class="l"><a href="#622"> 622: </a>    }
</span><span id="623" class="l"><a href="#623"> 623: </a>
</span><span id="624" class="l"><a href="#624"> 624: </a>
</span><span id="625" class="l"><a href="#625"> 625: </a>    <span class="php-comment">/**
</span></span><span id="626" class="l"><a href="#626"> 626: </a><span class="php-comment">     * Convert a primary key combined value to an array of field values.
</span></span><span id="627" class="l"><a href="#627"> 627: </a><span class="php-comment">     *
</span></span><span id="628" class="l"><a href="#628"> 628: </a><span class="php-comment">     * @param  string  $value The id that should be split apart
</span></span><span id="629" class="l"><a href="#629"> 629: </a><span class="php-comment">     * @param  boolean $flat  Flag to indicate if the returned array should be
</span></span><span id="630" class="l"><a href="#630"> 630: </a><span class="php-comment">     *   flat (useful for `where` conditions) or nested for join tables.
</span></span><span id="631" class="l"><a href="#631"> 631: </a><span class="php-comment">     * @param  string[] $pkey The primary key name - will use the instance value
</span></span><span id="632" class="l"><a href="#632"> 632: </a><span class="php-comment">     *   if not given
</span></span><span id="633" class="l"><a href="#633"> 633: </a><span class="php-comment">     * @return array          Array of field values that the id was made up of.
</span></span><span id="634" class="l"><a href="#634"> 634: </a><span class="php-comment">     * @throws \Exception If the primary key value does not match the expected
</span></span><span id="635" class="l"><a href="#635"> 635: </a><span class="php-comment">     *   length based on the primary key configuration, an exception will be
</span></span><span id="636" class="l"><a href="#636"> 636: </a><span class="php-comment">     *   thrown.
</span></span><span id="637" class="l"><a href="#637"> 637: </a><span class="php-comment">     */</span>
</span><span id="638" class="l"><a href="#638"> 638: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> pkeyToArray ( <span class="php-var">$value</span>, <span class="php-var">$flat</span>=<span class="php-keyword1">false</span>, <span class="php-var">$pkey</span>=<span class="php-keyword1">null</span> )
</span><span id="639" class="l"><a href="#639"> 639: </a>    {
</span><span id="640" class="l"><a href="#640"> 640: </a>        <span class="php-var">$arr</span> = <span class="php-keyword1">array</span>();
</span><span id="641" class="l"><a href="#641"> 641: </a>        <span class="php-var">$value</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;idPrefix(), <span class="php-quote">''</span>, <span class="php-var">$value</span> );
</span><span id="642" class="l"><a href="#642"> 642: </a>        <span class="php-var">$idParts</span> = <span class="php-keyword2">explode</span>( <span class="php-var">$this</span>-&gt;_pkey_separator(), <span class="php-var">$value</span> );
</span><span id="643" class="l"><a href="#643"> 643: </a>
</span><span id="644" class="l"><a href="#644"> 644: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$pkey</span> === <span class="php-keyword1">null</span> ) {
</span><span id="645" class="l"><a href="#645"> 645: </a>            <span class="php-var">$pkey</span> = <span class="php-var">$this</span>-&gt;_pkey;
</span><span id="646" class="l"><a href="#646"> 646: </a>        }
</span><span id="647" class="l"><a href="#647"> 647: </a>
</span><span id="648" class="l"><a href="#648"> 648: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$pkey</span>) !== <span class="php-keyword2">count</span>(<span class="php-var">$idParts</span>) ) {
</span><span id="649" class="l"><a href="#649"> 649: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Primary key data doesn't match submitted data&quot;</span>, <span class="php-num">1</span>);
</span><span id="650" class="l"><a href="#650"> 650: </a>        }
</span><span id="651" class="l"><a href="#651"> 651: </a>
</span><span id="652" class="l"><a href="#652"> 652: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$idParts</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="653" class="l"><a href="#653"> 653: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$flat</span> ) {
</span><span id="654" class="l"><a href="#654"> 654: </a>                <span class="php-var">$arr</span>[ <span class="php-var">$pkey</span>[<span class="php-var">$i</span>] ] = <span class="php-var">$idParts</span>[<span class="php-var">$i</span>];
</span><span id="655" class="l"><a href="#655"> 655: </a>            }
</span><span id="656" class="l"><a href="#656"> 656: </a>            <span class="php-keyword1">else</span> {
</span><span id="657" class="l"><a href="#657"> 657: </a>                <span class="php-var">$this</span>-&gt;_writeProp( <span class="php-var">$arr</span>, <span class="php-var">$pkey</span>[<span class="php-var">$i</span>], <span class="php-var">$idParts</span>[<span class="php-var">$i</span>] );
</span><span id="658" class="l"><a href="#658"> 658: </a>            }
</span><span id="659" class="l"><a href="#659"> 659: </a>        }
</span><span id="660" class="l"><a href="#660"> 660: </a>
</span><span id="661" class="l"><a href="#661"> 661: </a>        <span class="php-keyword1">return</span> <span class="php-var">$arr</span>;
</span><span id="662" class="l"><a href="#662"> 662: </a>    }
</span><span id="663" class="l"><a href="#663"> 663: </a>
</span><span id="664" class="l"><a href="#664"> 664: </a>
</span><span id="665" class="l"><a href="#665"> 665: </a>    <span class="php-comment">/**
</span></span><span id="666" class="l"><a href="#666"> 666: </a><span class="php-comment">     * Process a request from the Editor client-side to get / set data.
</span></span><span id="667" class="l"><a href="#667"> 667: </a><span class="php-comment">     *
</span></span><span id="668" class="l"><a href="#668"> 668: </a><span class="php-comment">     *  @param array $data Typically $_POST or $_GET as required by what is sent
</span></span><span id="669" class="l"><a href="#669"> 669: </a><span class="php-comment">     *  by Editor
</span></span><span id="670" class="l"><a href="#670"> 670: </a><span class="php-comment">     *  @return self
</span></span><span id="671" class="l"><a href="#671"> 671: </a><span class="php-comment">     */</span>
</span><span id="672" class="l"><a href="#672"> 672: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> process ( <span class="php-var">$data</span> )
</span><span id="673" class="l"><a href="#673"> 673: </a>    {
</span><span id="674" class="l"><a href="#674"> 674: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_debug ) {
</span><span id="675" class="l"><a href="#675"> 675: </a>            <span class="php-var">$debugInfo</span> = &amp;<span class="php-var">$this</span>-&gt;_debugInfo;
</span><span id="676" class="l"><a href="#676"> 676: </a>            <span class="php-var">$debugVal</span> = <span class="php-var">$this</span>-&gt;_db-&gt;debug( <span class="php-keyword1">function</span> ( <span class="php-var">$mess</span> ) <span class="php-keyword1">use</span> ( &amp;<span class="php-var">$debugInfo</span> ) {
</span><span id="677" class="l"><a href="#677"> 677: </a>                <span class="php-var">$debugInfo</span>[] = <span class="php-var">$mess</span>;
</span><span id="678" class="l"><a href="#678"> 678: </a>            } );
</span><span id="679" class="l"><a href="#679"> 679: </a>        }
</span><span id="680" class="l"><a href="#680"> 680: </a>
</span><span id="681" class="l"><a href="#681"> 681: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_tryCatch ) {
</span><span id="682" class="l"><a href="#682"> 682: </a>            <span class="php-keyword1">try</span> {
</span><span id="683" class="l"><a href="#683"> 683: </a>                <span class="php-var">$this</span>-&gt;_process( <span class="php-var">$data</span> );
</span><span id="684" class="l"><a href="#684"> 684: </a>            }
</span><span id="685" class="l"><a href="#685"> 685: </a>            <span class="php-keyword1">catch</span> (\Exception <span class="php-var">$e</span>) {
</span><span id="686" class="l"><a href="#686"> 686: </a>                <span class="php-comment">// Error feedback</span>
</span><span id="687" class="l"><a href="#687"> 687: </a>                <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'error'</span>] = <span class="php-var">$e</span>-&gt;getMessage();
</span><span id="688" class="l"><a href="#688"> 688: </a>                
</span><span id="689" class="l"><a href="#689"> 689: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_transaction ) {
</span><span id="690" class="l"><a href="#690"> 690: </a>                    <span class="php-var">$this</span>-&gt;_db-&gt;rollback();
</span><span id="691" class="l"><a href="#691"> 691: </a>                }
</span><span id="692" class="l"><a href="#692"> 692: </a>            }
</span><span id="693" class="l"><a href="#693"> 693: </a>        }
</span><span id="694" class="l"><a href="#694"> 694: </a>        <span class="php-keyword1">else</span> {
</span><span id="695" class="l"><a href="#695"> 695: </a>            <span class="php-var">$this</span>-&gt;_process( <span class="php-var">$data</span> );
</span><span id="696" class="l"><a href="#696"> 696: </a>        }
</span><span id="697" class="l"><a href="#697"> 697: </a>
</span><span id="698" class="l"><a href="#698"> 698: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_debug ) {
</span><span id="699" class="l"><a href="#699"> 699: </a>            <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'debug'</span>] = <span class="php-var">$this</span>-&gt;_debugInfo;
</span><span id="700" class="l"><a href="#700"> 700: </a>
</span><span id="701" class="l"><a href="#701"> 701: </a>            <span class="php-comment">// Save to a log file</span>
</span><span id="702" class="l"><a href="#702"> 702: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_debugLog ) {
</span><span id="703" class="l"><a href="#703"> 703: </a>                <span class="php-keyword2">file_put_contents</span>( <span class="php-var">$this</span>-&gt;_debugLog, <span class="php-keyword2">json_encode</span>( <span class="php-var">$this</span>-&gt;_debugInfo ).<span class="php-quote">&quot;\n&quot;</span>, FILE_APPEND );
</span><span id="704" class="l"><a href="#704"> 704: </a>            }
</span><span id="705" class="l"><a href="#705"> 705: </a>
</span><span id="706" class="l"><a href="#706"> 706: </a>            <span class="php-var">$this</span>-&gt;_db-&gt;debug( <span class="php-keyword1">false</span> );
</span><span id="707" class="l"><a href="#707"> 707: </a>        }
</span><span id="708" class="l"><a href="#708"> 708: </a>
</span><span id="709" class="l"><a href="#709"> 709: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="710" class="l"><a href="#710"> 710: </a>    }
</span><span id="711" class="l"><a href="#711"> 711: </a>
</span><span id="712" class="l"><a href="#712"> 712: </a>
</span><span id="713" class="l"><a href="#713"> 713: </a>    <span class="php-comment">/**
</span></span><span id="714" class="l"><a href="#714"> 714: </a><span class="php-comment">     * The CRUD read table name. If this method is used, Editor will create from the
</span></span><span id="715" class="l"><a href="#715"> 715: </a><span class="php-comment">     * table name(s) given rather than those given by `Editor-&gt;table()`. This can be
</span></span><span id="716" class="l"><a href="#716"> 716: </a><span class="php-comment">     * a useful distinction to allow a read from a VIEW (which could make use of a
</span></span><span id="717" class="l"><a href="#717"> 717: </a><span class="php-comment">     * complex SELECT) while writing to a different table.
</span></span><span id="718" class="l"><a href="#718"> 718: </a><span class="php-comment">     *
</span></span><span id="719" class="l"><a href="#719"> 719: </a><span class="php-comment">     *  @param string|array $_,... Read table names given as a single string, an array
</span></span><span id="720" class="l"><a href="#720"> 720: </a><span class="php-comment">     *    of strings or multiple string parameters for the function.
</span></span><span id="721" class="l"><a href="#721"> 721: </a><span class="php-comment">     *  @return string[]|self Array of read tables names, or self if used as a setter.
</span></span><span id="722" class="l"><a href="#722"> 722: </a><span class="php-comment">     */</span>
</span><span id="723" class="l"><a href="#723"> 723: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> readTable ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="724" class="l"><a href="#724"> 724: </a>    {
</span><span id="725" class="l"><a href="#725"> 725: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="726" class="l"><a href="#726"> 726: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="727" class="l"><a href="#727"> 727: </a>        }
</span><span id="728" class="l"><a href="#728"> 728: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_readTableNames, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="729" class="l"><a href="#729"> 729: </a>    }
</span><span id="730" class="l"><a href="#730"> 730: </a>
</span><span id="731" class="l"><a href="#731"> 731: </a>
</span><span id="732" class="l"><a href="#732"> 732: </a>    <span class="php-comment">/**
</span></span><span id="733" class="l"><a href="#733"> 733: </a><span class="php-comment">     * Get / set the table name.
</span></span><span id="734" class="l"><a href="#734"> 734: </a><span class="php-comment">     * 
</span></span><span id="735" class="l"><a href="#735"> 735: </a><span class="php-comment">     * The table name designated which DB table Editor will use as its data
</span></span><span id="736" class="l"><a href="#736"> 736: </a><span class="php-comment">     * source for working with the database. Table names can be given with an
</span></span><span id="737" class="l"><a href="#737"> 737: </a><span class="php-comment">     * alias, which can be used to simplify larger table names. The field
</span></span><span id="738" class="l"><a href="#738"> 738: </a><span class="php-comment">     * names would also need to reflect the alias, just like an SQL query. For
</span></span><span id="739" class="l"><a href="#739"> 739: </a><span class="php-comment">     * example: `users as a`.
</span></span><span id="740" class="l"><a href="#740"> 740: </a><span class="php-comment">     *
</span></span><span id="741" class="l"><a href="#741"> 741: </a><span class="php-comment">     *  @param string|array $_,... Table names given as a single string, an array of
</span></span><span id="742" class="l"><a href="#742"> 742: </a><span class="php-comment">     *    strings or multiple string parameters for the function.
</span></span><span id="743" class="l"><a href="#743"> 743: </a><span class="php-comment">     *  @return string[]|self Array of tables names, or self if used as a setter.
</span></span><span id="744" class="l"><a href="#744"> 744: </a><span class="php-comment">     */</span>
</span><span id="745" class="l"><a href="#745"> 745: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> table ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="746" class="l"><a href="#746"> 746: </a>    {
</span><span id="747" class="l"><a href="#747"> 747: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="748" class="l"><a href="#748"> 748: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="749" class="l"><a href="#749"> 749: </a>        }
</span><span id="750" class="l"><a href="#750"> 750: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_table, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="751" class="l"><a href="#751"> 751: </a>    }
</span><span id="752" class="l"><a href="#752"> 752: </a>
</span><span id="753" class="l"><a href="#753"> 753: </a>
</span><span id="754" class="l"><a href="#754"> 754: </a>    <span class="php-comment">/**
</span></span><span id="755" class="l"><a href="#755"> 755: </a><span class="php-comment">     * Get / set transaction support.
</span></span><span id="756" class="l"><a href="#756"> 756: </a><span class="php-comment">     *
</span></span><span id="757" class="l"><a href="#757"> 757: </a><span class="php-comment">     * When enabled (which it is by default) Editor will use an SQL transaction
</span></span><span id="758" class="l"><a href="#758"> 758: </a><span class="php-comment">     * to ensure data integrity while it is performing operations on the table.
</span></span><span id="759" class="l"><a href="#759"> 759: </a><span class="php-comment">     * This can be optionally disabled using this method, if required by your
</span></span><span id="760" class="l"><a href="#760"> 760: </a><span class="php-comment">     * database configuration.
</span></span><span id="761" class="l"><a href="#761"> 761: </a><span class="php-comment">     *
</span></span><span id="762" class="l"><a href="#762"> 762: </a><span class="php-comment">     *  @param boolean $_ Enable (`true`) or disabled (`false`) transactions.
</span></span><span id="763" class="l"><a href="#763"> 763: </a><span class="php-comment">     *    If not given, then used as a getter.
</span></span><span id="764" class="l"><a href="#764"> 764: </a><span class="php-comment">     *  @return boolean|self Transactions enabled flag, or self if used as a
</span></span><span id="765" class="l"><a href="#765"> 765: </a><span class="php-comment">     *    setter.
</span></span><span id="766" class="l"><a href="#766"> 766: </a><span class="php-comment">     */</span>
</span><span id="767" class="l"><a href="#767"> 767: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> transaction ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="768" class="l"><a href="#768"> 768: </a>    {
</span><span id="769" class="l"><a href="#769"> 769: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_transaction, <span class="php-var">$_</span> );
</span><span id="770" class="l"><a href="#770"> 770: </a>    }
</span><span id="771" class="l"><a href="#771"> 771: </a>
</span><span id="772" class="l"><a href="#772"> 772: </a>
</span><span id="773" class="l"><a href="#773"> 773: </a>    <span class="php-comment">/**
</span></span><span id="774" class="l"><a href="#774"> 774: </a><span class="php-comment">     * Enable / try catch when `process()` is called. Disabling this can be
</span></span><span id="775" class="l"><a href="#775"> 775: </a><span class="php-comment">     * useful for debugging, but is not recommended for production.
</span></span><span id="776" class="l"><a href="#776"> 776: </a><span class="php-comment">     *
</span></span><span id="777" class="l"><a href="#777"> 777: </a><span class="php-comment">     * @param  boolean $_ `true` to enable (default), otherwise false to disable
</span></span><span id="778" class="l"><a href="#778"> 778: </a><span class="php-comment">     * @return boolean|Editor Value if used as a getter, otherwise `$this` when
</span></span><span id="779" class="l"><a href="#779"> 779: </a><span class="php-comment">     *   used as a setter.
</span></span><span id="780" class="l"><a href="#780"> 780: </a><span class="php-comment">     */</span>
</span><span id="781" class="l"><a href="#781"> 781: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> tryCatch ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="782" class="l"><a href="#782"> 782: </a>    {
</span><span id="783" class="l"><a href="#783"> 783: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_tryCatch, <span class="php-var">$_</span> );
</span><span id="784" class="l"><a href="#784"> 784: </a>    }
</span><span id="785" class="l"><a href="#785"> 785: </a>
</span><span id="786" class="l"><a href="#786"> 786: </a>
</span><span id="787" class="l"><a href="#787"> 787: </a>    <span class="php-comment">/**
</span></span><span id="788" class="l"><a href="#788"> 788: </a><span class="php-comment">     * Perform validation on a data set.
</span></span><span id="789" class="l"><a href="#789"> 789: </a><span class="php-comment">     *
</span></span><span id="790" class="l"><a href="#790"> 790: </a><span class="php-comment">     * Note that validation is performed on data only when the action is
</span></span><span id="791" class="l"><a href="#791"> 791: </a><span class="php-comment">     * `create` or `edit`. Additionally, validation is performed on the _wire
</span></span><span id="792" class="l"><a href="#792"> 792: </a><span class="php-comment">     * data_ - i.e. that which is submitted from the client, without formatting.
</span></span><span id="793" class="l"><a href="#793"> 793: </a><span class="php-comment">     * Any formatting required by `setFormatter` is performed after the data
</span></span><span id="794" class="l"><a href="#794"> 794: </a><span class="php-comment">     * from the client has been validated.
</span></span><span id="795" class="l"><a href="#795"> 795: </a><span class="php-comment">     *
</span></span><span id="796" class="l"><a href="#796"> 796: </a><span class="php-comment">     *  @param array $errors Output array to which field error information will
</span></span><span id="797" class="l"><a href="#797"> 797: </a><span class="php-comment">     *      be written. Each element in the array represents a field in an error
</span></span><span id="798" class="l"><a href="#798"> 798: </a><span class="php-comment">     *      condition. These elements are themselves arrays with two properties
</span></span><span id="799" class="l"><a href="#799"> 799: </a><span class="php-comment">     *      set; `name` and `status`.
</span></span><span id="800" class="l"><a href="#800"> 800: </a><span class="php-comment">     *  @param array $data The format data to check
</span></span><span id="801" class="l"><a href="#801"> 801: </a><span class="php-comment">     *  @return boolean `true` if the data is valid, `false` if not.
</span></span><span id="802" class="l"><a href="#802"> 802: </a><span class="php-comment">     */</span>
</span><span id="803" class="l"><a href="#803"> 803: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validate ( &amp;<span class="php-var">$errors</span>, <span class="php-var">$data</span> )
</span><span id="804" class="l"><a href="#804"> 804: </a>    {
</span><span id="805" class="l"><a href="#805"> 805: </a>        <span class="php-comment">// Validation is only performed on create and edit</span>
</span><span id="806" class="l"><a href="#806"> 806: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] != <span class="php-quote">&quot;create&quot;</span> &amp;&amp; <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] != <span class="php-quote">&quot;edit&quot;</span> ) {
</span><span id="807" class="l"><a href="#807"> 807: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="808" class="l"><a href="#808"> 808: </a>        }
</span><span id="809" class="l"><a href="#809"> 809: </a>
</span><span id="810" class="l"><a href="#810"> 810: </a>        <span class="php-keyword1">foreach</span>( <span class="php-var">$data</span>[<span class="php-quote">'data'</span>] <span class="php-keyword1">as</span> <span class="php-var">$id</span> =&gt; <span class="php-var">$values</span> ) {
</span><span id="811" class="l"><a href="#811"> 811: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="812" class="l"><a href="#812"> 812: </a>                <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="813" class="l"><a href="#813"> 813: </a>                <span class="php-var">$validation</span> = <span class="php-var">$field</span>-&gt;validate( <span class="php-var">$values</span>, <span class="php-var">$this</span>,
</span><span id="814" class="l"><a href="#814"> 814: </a>                    <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;idPrefix(), <span class="php-quote">''</span>, <span class="php-var">$id</span> )
</span><span id="815" class="l"><a href="#815"> 815: </a>                );
</span><span id="816" class="l"><a href="#816"> 816: </a>
</span><span id="817" class="l"><a href="#817"> 817: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$validation</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="818" class="l"><a href="#818"> 818: </a>                    <span class="php-var">$errors</span>[] = <span class="php-keyword1">array</span>(
</span><span id="819" class="l"><a href="#819"> 819: </a>                        <span class="php-quote">&quot;name&quot;</span> =&gt; <span class="php-var">$field</span>-&gt;name(),
</span><span id="820" class="l"><a href="#820"> 820: </a>                        <span class="php-quote">&quot;status&quot;</span> =&gt; <span class="php-var">$validation</span>
</span><span id="821" class="l"><a href="#821"> 821: </a>                    );
</span><span id="822" class="l"><a href="#822"> 822: </a>                }
</span><span id="823" class="l"><a href="#823"> 823: </a>            }
</span><span id="824" class="l"><a href="#824"> 824: </a>
</span><span id="825" class="l"><a href="#825"> 825: </a>            <span class="php-comment">// MJoin validation</span>
</span><span id="826" class="l"><a href="#826"> 826: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="827" class="l"><a href="#827"> 827: </a>                <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;validate( <span class="php-var">$errors</span>, <span class="php-var">$this</span>, <span class="php-var">$values</span> );
</span><span id="828" class="l"><a href="#828"> 828: </a>            }
</span><span id="829" class="l"><a href="#829"> 829: </a>        }
</span><span id="830" class="l"><a href="#830"> 830: </a>
</span><span id="831" class="l"><a href="#831"> 831: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">count</span>( <span class="php-var">$errors</span> ) &gt; <span class="php-num">0</span> ? <span class="php-keyword1">false</span> : <span class="php-keyword1">true</span>;
</span><span id="832" class="l"><a href="#832"> 832: </a>    }
</span><span id="833" class="l"><a href="#833"> 833: </a>
</span><span id="834" class="l"><a href="#834"> 834: </a>
</span><span id="835" class="l"><a href="#835"> 835: </a>    <span class="php-comment">/**
</span></span><span id="836" class="l"><a href="#836"> 836: </a><span class="php-comment">     * Get / set a global validator that will be triggered for the create, edit
</span></span><span id="837" class="l"><a href="#837"> 837: </a><span class="php-comment">     * and remove actions performed from the client-side.
</span></span><span id="838" class="l"><a href="#838"> 838: </a><span class="php-comment">     *
</span></span><span id="839" class="l"><a href="#839"> 839: </a><span class="php-comment">     * @param  callable $_ Function to execute when validating the input data.
</span></span><span id="840" class="l"><a href="#840"> 840: </a><span class="php-comment">     *   It is passed three parameters: 1. The editor instance, 2. The action
</span></span><span id="841" class="l"><a href="#841"> 841: </a><span class="php-comment">     *   and 3. The values.
</span></span><span id="842" class="l"><a href="#842"> 842: </a><span class="php-comment">     * @return Editor|callback Editor instance if called as a setter, or the
</span></span><span id="843" class="l"><a href="#843"> 843: </a><span class="php-comment">     *   validator function if not.
</span></span><span id="844" class="l"><a href="#844"> 844: </a><span class="php-comment">     */</span>
</span><span id="845" class="l"><a href="#845"> 845: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validator ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="846" class="l"><a href="#846"> 846: </a>    {
</span><span id="847" class="l"><a href="#847"> 847: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_validator, <span class="php-var">$_</span> );
</span><span id="848" class="l"><a href="#848"> 848: </a>    }
</span><span id="849" class="l"><a href="#849"> 849: </a>
</span><span id="850" class="l"><a href="#850"> 850: </a>
</span><span id="851" class="l"><a href="#851"> 851: </a>    <span class="php-comment">/**
</span></span><span id="852" class="l"><a href="#852"> 852: </a><span class="php-comment">     * Where condition to add to the query used to get data from the database.
</span></span><span id="853" class="l"><a href="#853"> 853: </a><span class="php-comment">     * 
</span></span><span id="854" class="l"><a href="#854"> 854: </a><span class="php-comment">     * Can be used in two different ways:
</span></span><span id="855" class="l"><a href="#855"> 855: </a><span class="php-comment">     * 
</span></span><span id="856" class="l"><a href="#856"> 856: </a><span class="php-comment">     * * Simple case: `where( field, value, operator )`
</span></span><span id="857" class="l"><a href="#857"> 857: </a><span class="php-comment">     * * Complex: `where( fn )`
</span></span><span id="858" class="l"><a href="#858"> 858: </a><span class="php-comment">     *
</span></span><span id="859" class="l"><a href="#859"> 859: </a><span class="php-comment">     * The simple case is fairly self explanatory, a condition is applied to the
</span></span><span id="860" class="l"><a href="#860"> 860: </a><span class="php-comment">     * data that looks like `field operator value` (e.g. `name = 'Allan'`). The
</span></span><span id="861" class="l"><a href="#861"> 861: </a><span class="php-comment">     * complex case allows full control over the query conditions by providing a
</span></span><span id="862" class="l"><a href="#862"> 862: </a><span class="php-comment">     * closure function that has access to the database Query that Editor is
</span></span><span id="863" class="l"><a href="#863"> 863: </a><span class="php-comment">     * using, so you can use the `where()`, `or_where()`, `and_where()` and
</span></span><span id="864" class="l"><a href="#864"> 864: </a><span class="php-comment">     * `where_group()` methods as you require.
</span></span><span id="865" class="l"><a href="#865"> 865: </a><span class="php-comment">     *
</span></span><span id="866" class="l"><a href="#866"> 866: </a><span class="php-comment">     * Please be very careful when using this method! If an edit made by a user
</span></span><span id="867" class="l"><a href="#867"> 867: </a><span class="php-comment">     * using Editor removes the row from the where condition, the result is
</span></span><span id="868" class="l"><a href="#868"> 868: </a><span class="php-comment">     * undefined (since Editor expects the row to still be available, but the
</span></span><span id="869" class="l"><a href="#869"> 869: </a><span class="php-comment">     * condition removes it from the result set).
</span></span><span id="870" class="l"><a href="#870"> 870: </a><span class="php-comment">     * 
</span></span><span id="871" class="l"><a href="#871"> 871: </a><span class="php-comment">     * @param string|callable $key   Single field name or a closure function
</span></span><span id="872" class="l"><a href="#872"> 872: </a><span class="php-comment">     * @param string          $value Single field value.
</span></span><span id="873" class="l"><a href="#873"> 873: </a><span class="php-comment">     * @param string          $op    Condition operator: &lt;, &gt;, = etc
</span></span><span id="874" class="l"><a href="#874"> 874: </a><span class="php-comment">     * @return string[]|self Where condition array, or self if used as a setter.
</span></span><span id="875" class="l"><a href="#875"> 875: </a><span class="php-comment">     */</span>
</span><span id="876" class="l"><a href="#876"> 876: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> where ( <span class="php-var">$key</span>=<span class="php-keyword1">null</span>, <span class="php-var">$value</span>=<span class="php-keyword1">null</span>, <span class="php-var">$op</span>=<span class="php-quote">'='</span> )
</span><span id="877" class="l"><a href="#877"> 877: </a>    {
</span><span id="878" class="l"><a href="#878"> 878: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$key</span> === <span class="php-keyword1">null</span> ) {
</span><span id="879" class="l"><a href="#879"> 879: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_where;
</span><span id="880" class="l"><a href="#880"> 880: </a>        }
</span><span id="881" class="l"><a href="#881"> 881: </a>
</span><span id="882" class="l"><a href="#882"> 882: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>(<span class="php-var">$key</span>) &amp;&amp; <span class="php-keyword2">is_object</span>(<span class="php-var">$key</span>) ) {
</span><span id="883" class="l"><a href="#883"> 883: </a>            <span class="php-var">$this</span>-&gt;_where[] = <span class="php-var">$key</span>;
</span><span id="884" class="l"><a href="#884"> 884: </a>        }
</span><span id="885" class="l"><a href="#885"> 885: </a>        <span class="php-keyword1">else</span> {
</span><span id="886" class="l"><a href="#886"> 886: </a>            <span class="php-var">$this</span>-&gt;_where[] = <span class="php-keyword1">array</span>(
</span><span id="887" class="l"><a href="#887"> 887: </a>                <span class="php-quote">&quot;key&quot;</span>   =&gt; <span class="php-var">$key</span>,
</span><span id="888" class="l"><a href="#888"> 888: </a>                <span class="php-quote">&quot;value&quot;</span> =&gt; <span class="php-var">$value</span>,
</span><span id="889" class="l"><a href="#889"> 889: </a>                <span class="php-quote">&quot;op&quot;</span>    =&gt; <span class="php-var">$op</span>
</span><span id="890" class="l"><a href="#890"> 890: </a>            );
</span><span id="891" class="l"><a href="#891"> 891: </a>        }
</span><span id="892" class="l"><a href="#892"> 892: </a>
</span><span id="893" class="l"><a href="#893"> 893: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="894" class="l"><a href="#894"> 894: </a>    }
</span><span id="895" class="l"><a href="#895"> 895: </a>
</span><span id="896" class="l"><a href="#896"> 896: </a>
</span><span id="897" class="l"><a href="#897"> 897: </a>    <span class="php-comment">/**
</span></span><span id="898" class="l"><a href="#898"> 898: </a><span class="php-comment">     * Get / set if the WHERE conditions should be included in the create and
</span></span><span id="899" class="l"><a href="#899"> 899: </a><span class="php-comment">     * edit actions.
</span></span><span id="900" class="l"><a href="#900"> 900: </a><span class="php-comment">     * 
</span></span><span id="901" class="l"><a href="#901"> 901: </a><span class="php-comment">     *  @param boolean $_ Include (`true`), or not (`false`)
</span></span><span id="902" class="l"><a href="#902"> 902: </a><span class="php-comment">     *  @return boolean Current value
</span></span><span id="903" class="l"><a href="#903"> 903: </a><span class="php-comment">     *  @deprecated Note that `whereSet` is now deprecated and replaced with the
</span></span><span id="904" class="l"><a href="#904"> 904: </a><span class="php-comment">     *    ability to set values for columns on create and edit. The C# libraries
</span></span><span id="905" class="l"><a href="#905"> 905: </a><span class="php-comment">     *    do not support this option at all.
</span></span><span id="906" class="l"><a href="#906"> 906: </a><span class="php-comment">     */</span>
</span><span id="907" class="l"><a href="#907"> 907: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> whereSet ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="908" class="l"><a href="#908"> 908: </a>    {
</span><span id="909" class="l"><a href="#909"> 909: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_whereSet, <span class="php-var">$_</span> );
</span><span id="910" class="l"><a href="#910"> 910: </a>    }
</span><span id="911" class="l"><a href="#911"> 911: </a>
</span><span id="912" class="l"><a href="#912"> 912: </a>
</span><span id="913" class="l"><a href="#913"> 913: </a>
</span><span id="914" class="l"><a href="#914"> 914: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="915" class="l"><a href="#915"> 915: </a><span class="php-comment">     * Private methods
</span></span><span id="916" class="l"><a href="#916"> 916: </a><span class="php-comment">     */</span>
</span><span id="917" class="l"><a href="#917"> 917: </a>
</span><span id="918" class="l"><a href="#918"> 918: </a>    <span class="php-comment">/**
</span></span><span id="919" class="l"><a href="#919"> 919: </a><span class="php-comment">     * Process a request from the Editor client-side to get / set data.
</span></span><span id="920" class="l"><a href="#920"> 920: </a><span class="php-comment">     *
</span></span><span id="921" class="l"><a href="#921"> 921: </a><span class="php-comment">     *  @param array $data Data to process
</span></span><span id="922" class="l"><a href="#922"> 922: </a><span class="php-comment">     *  @private
</span></span><span id="923" class="l"><a href="#923"> 923: </a><span class="php-comment">     */</span>
</span><span id="924" class="l"><a href="#924"> 924: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _process( <span class="php-var">$data</span> )
</span><span id="925" class="l"><a href="#925"> 925: </a>    {
</span><span id="926" class="l"><a href="#926"> 926: </a>        <span class="php-var">$this</span>-&gt;_out = <span class="php-keyword1">array</span>(
</span><span id="927" class="l"><a href="#927"> 927: </a>            <span class="php-quote">&quot;fieldErrors&quot;</span> =&gt; <span class="php-keyword1">array</span>(),
</span><span id="928" class="l"><a href="#928"> 928: </a>            <span class="php-quote">&quot;error&quot;</span> =&gt; <span class="php-quote">&quot;&quot;</span>,
</span><span id="929" class="l"><a href="#929"> 929: </a>            <span class="php-quote">&quot;data&quot;</span> =&gt; <span class="php-keyword1">array</span>(),
</span><span id="930" class="l"><a href="#930"> 930: </a>            <span class="php-quote">&quot;ipOpts&quot;</span> =&gt; <span class="php-keyword1">array</span>(),
</span><span id="931" class="l"><a href="#931"> 931: </a>            <span class="php-quote">&quot;cancelled&quot;</span> =&gt; <span class="php-keyword1">array</span>()
</span><span id="932" class="l"><a href="#932"> 932: </a>        );
</span><span id="933" class="l"><a href="#933"> 933: </a>
</span><span id="934" class="l"><a href="#934"> 934: </a>        <span class="php-var">$this</span>-&gt;_processData = <span class="php-var">$data</span>;
</span><span id="935" class="l"><a href="#935"> 935: </a>        <span class="php-var">$this</span>-&gt;_formData = <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-quote">'data'</span>]) ? <span class="php-var">$data</span>[<span class="php-quote">'data'</span>] : <span class="php-keyword1">null</span>;
</span><span id="936" class="l"><a href="#936"> 936: </a>        <span class="php-var">$validator</span> = <span class="php-var">$this</span>-&gt;_validator;
</span><span id="937" class="l"><a href="#937"> 937: </a>
</span><span id="938" class="l"><a href="#938"> 938: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_transaction ) {
</span><span id="939" class="l"><a href="#939"> 939: </a>            <span class="php-var">$this</span>-&gt;_db-&gt;transaction();
</span><span id="940" class="l"><a href="#940"> 940: </a>        }
</span><span id="941" class="l"><a href="#941"> 941: </a>
</span><span id="942" class="l"><a href="#942"> 942: </a>        <span class="php-var">$this</span>-&gt;_prepJoin();
</span><span id="943" class="l"><a href="#943"> 943: </a>
</span><span id="944" class="l"><a href="#944"> 944: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$validator</span> ) {
</span><span id="945" class="l"><a href="#945"> 945: </a>            <span class="php-var">$ret</span> = <span class="php-var">$validator</span>( <span class="php-var">$this</span>, !<span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-quote">'action'</span>]) ? self::ACTION_READ : <span class="php-var">$data</span>[<span class="php-quote">'action'</span>], <span class="php-var">$data</span> );
</span><span id="946" class="l"><a href="#946"> 946: </a>
</span><span id="947" class="l"><a href="#947"> 947: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$ret</span> ) {
</span><span id="948" class="l"><a href="#948"> 948: </a>                <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'error'</span>] = <span class="php-var">$ret</span>;
</span><span id="949" class="l"><a href="#949"> 949: </a>            }
</span><span id="950" class="l"><a href="#950"> 950: </a>        }
</span><span id="951" class="l"><a href="#951"> 951: </a>
</span><span id="952" class="l"><a href="#952"> 952: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'error'</span>] ) {
</span><span id="953" class="l"><a href="#953"> 953: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-quote">'action'</span>]) ) {
</span><span id="954" class="l"><a href="#954"> 954: </a>                <span class="php-comment">/* Get data */</span>
</span><span id="955" class="l"><a href="#955"> 955: </a>                <span class="php-var">$this</span>-&gt;_out = <span class="php-keyword2">array_merge</span>( <span class="php-var">$this</span>-&gt;_out, <span class="php-var">$this</span>-&gt;_get( <span class="php-keyword1">null</span>, <span class="php-var">$data</span> ) );
</span><span id="956" class="l"><a href="#956"> 956: </a>            }
</span><span id="957" class="l"><a href="#957"> 957: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] == <span class="php-quote">&quot;upload&quot;</span> ) {
</span><span id="958" class="l"><a href="#958"> 958: </a>                <span class="php-comment">/* File upload */</span>
</span><span id="959" class="l"><a href="#959"> 959: </a>                <span class="php-var">$this</span>-&gt;_upload( <span class="php-var">$data</span> );
</span><span id="960" class="l"><a href="#960"> 960: </a>            }
</span><span id="961" class="l"><a href="#961"> 961: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] == <span class="php-quote">&quot;remove&quot;</span> ) {
</span><span id="962" class="l"><a href="#962"> 962: </a>                <span class="php-comment">/* Remove rows */</span>
</span><span id="963" class="l"><a href="#963"> 963: </a>                <span class="php-var">$this</span>-&gt;_remove( <span class="php-var">$data</span> );
</span><span id="964" class="l"><a href="#964"> 964: </a>                <span class="php-var">$this</span>-&gt;_fileClean();
</span><span id="965" class="l"><a href="#965"> 965: </a>            }
</span><span id="966" class="l"><a href="#966"> 966: </a>            <span class="php-keyword1">else</span> {
</span><span id="967" class="l"><a href="#967"> 967: </a>                <span class="php-comment">/* Create or edit row */</span>
</span><span id="968" class="l"><a href="#968"> 968: </a>                <span class="php-comment">// Pre events so they can occur before the validation</span>
</span><span id="969" class="l"><a href="#969"> 969: </a>                <span class="php-keyword1">foreach</span> (<span class="php-var">$data</span>[<span class="php-quote">'data'</span>] <span class="php-keyword1">as</span> <span class="php-var">$idSrc</span> =&gt; &amp;<span class="php-var">$values</span>) {
</span><span id="970" class="l"><a href="#970"> 970: </a>                    <span class="php-var">$cancel</span> = <span class="php-keyword1">null</span>;
</span><span id="971" class="l"><a href="#971"> 971: </a>
</span><span id="972" class="l"><a href="#972"> 972: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] == <span class="php-quote">'create'</span> ) {
</span><span id="973" class="l"><a href="#973"> 973: </a>                        <span class="php-var">$cancel</span> = <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'preCreate'</span>, <span class="php-var">$values</span> );
</span><span id="974" class="l"><a href="#974"> 974: </a>                    }
</span><span id="975" class="l"><a href="#975"> 975: </a>                    <span class="php-keyword1">else</span> {
</span><span id="976" class="l"><a href="#976"> 976: </a>                        <span class="php-var">$id</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;_idPrefix, <span class="php-quote">''</span>, <span class="php-var">$idSrc</span> );
</span><span id="977" class="l"><a href="#977"> 977: </a>                        <span class="php-var">$cancel</span> = <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'preEdit'</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="978" class="l"><a href="#978"> 978: </a>                    }
</span><span id="979" class="l"><a href="#979"> 979: </a>
</span><span id="980" class="l"><a href="#980"> 980: </a>                    <span class="php-comment">// One of the event handlers returned false - don't continue</span>
</span><span id="981" class="l"><a href="#981"> 981: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$cancel</span> === <span class="php-keyword1">false</span> ) {
</span><span id="982" class="l"><a href="#982"> 982: </a>                        <span class="php-comment">// Remove the data from the data set so it won't be processed</span>
</span><span id="983" class="l"><a href="#983"> 983: </a>                        <span class="php-keyword1">unset</span>( <span class="php-var">$data</span>[<span class="php-quote">'data'</span>][<span class="php-var">$idSrc</span>] );
</span><span id="984" class="l"><a href="#984"> 984: </a>
</span><span id="985" class="l"><a href="#985"> 985: </a>                        <span class="php-comment">// Tell the client-side we aren't updating this row</span>
</span><span id="986" class="l"><a href="#986"> 986: </a>                        <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'cancelled'</span>][] = <span class="php-var">$idSrc</span>;
</span><span id="987" class="l"><a href="#987"> 987: </a>                    }
</span><span id="988" class="l"><a href="#988"> 988: </a>                }
</span><span id="989" class="l"><a href="#989"> 989: </a>
</span><span id="990" class="l"><a href="#990"> 990: </a>                <span class="php-comment">// Validation</span>
</span><span id="991" class="l"><a href="#991"> 991: </a>                <span class="php-var">$valid</span> = <span class="php-var">$this</span>-&gt;validate( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'fieldErrors'</span>], <span class="php-var">$data</span> );
</span><span id="992" class="l"><a href="#992"> 992: </a>
</span><span id="993" class="l"><a href="#993"> 993: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$valid</span> ) {
</span><span id="994" class="l"><a href="#994"> 994: </a>                    <span class="php-keyword1">foreach</span> (<span class="php-var">$data</span>[<span class="php-quote">'data'</span>] <span class="php-keyword1">as</span> <span class="php-var">$id</span> =&gt; &amp;<span class="php-var">$values</span>) {
</span><span id="995" class="l"><a href="#995"> 995: </a>                        <span class="php-var">$d</span> = <span class="php-var">$data</span>[<span class="php-quote">'action'</span>] == <span class="php-quote">&quot;create&quot;</span> ?
</span><span id="996" class="l"><a href="#996"> 996: </a>                            <span class="php-var">$this</span>-&gt;_insert( <span class="php-var">$values</span> ) :
</span><span id="997" class="l"><a href="#997"> 997: </a>                            <span class="php-var">$this</span>-&gt;_update( <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="998" class="l"><a href="#998"> 998: </a>
</span><span id="999" class="l"><a href="#999"> 999: </a>                        <span class="php-keyword1">if</span> ( <span class="php-var">$d</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="1000" class="l"><a href="#1000">1000: </a>                            <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'data'</span>][] = <span class="php-var">$d</span>;
</span><span id="1001" class="l"><a href="#1001">1001: </a>                        }
</span><span id="1002" class="l"><a href="#1002">1002: </a>                    }
</span><span id="1003" class="l"><a href="#1003">1003: </a>                }
</span><span id="1004" class="l"><a href="#1004">1004: </a>
</span><span id="1005" class="l"><a href="#1005">1005: </a>                <span class="php-var">$this</span>-&gt;_fileClean();
</span><span id="1006" class="l"><a href="#1006">1006: </a>            }
</span><span id="1007" class="l"><a href="#1007">1007: </a>        }
</span><span id="1008" class="l"><a href="#1008">1008: </a>
</span><span id="1009" class="l"><a href="#1009">1009: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_transaction ) {
</span><span id="1010" class="l"><a href="#1010">1010: </a>            <span class="php-var">$this</span>-&gt;_db-&gt;commit();
</span><span id="1011" class="l"><a href="#1011">1011: </a>        }
</span><span id="1012" class="l"><a href="#1012">1012: </a>
</span><span id="1013" class="l"><a href="#1013">1013: </a>        <span class="php-comment">// Tidy up the reply</span>
</span><span id="1014" class="l"><a href="#1014">1014: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'fieldErrors'</span>] ) === <span class="php-num">0</span> ) {
</span><span id="1015" class="l"><a href="#1015">1015: </a>            <span class="php-keyword1">unset</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'fieldErrors'</span>] );
</span><span id="1016" class="l"><a href="#1016">1016: </a>        }
</span><span id="1017" class="l"><a href="#1017">1017: </a>
</span><span id="1018" class="l"><a href="#1018">1018: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'error'</span>] === <span class="php-quote">''</span> ) {
</span><span id="1019" class="l"><a href="#1019">1019: </a>            <span class="php-keyword1">unset</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'error'</span>] );
</span><span id="1020" class="l"><a href="#1020">1020: </a>        }
</span><span id="1021" class="l"><a href="#1021">1021: </a>
</span><span id="1022" class="l"><a href="#1022">1022: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'ipOpts'</span>] ) === <span class="php-num">0</span> ) {
</span><span id="1023" class="l"><a href="#1023">1023: </a>            <span class="php-keyword1">unset</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'ipOpts'</span>] );
</span><span id="1024" class="l"><a href="#1024">1024: </a>        }
</span><span id="1025" class="l"><a href="#1025">1025: </a>
</span><span id="1026" class="l"><a href="#1026">1026: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'cancelled'</span>] ) === <span class="php-num">0</span> ) {
</span><span id="1027" class="l"><a href="#1027">1027: </a>            <span class="php-keyword1">unset</span>( <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'cancelled'</span>] );
</span><span id="1028" class="l"><a href="#1028">1028: </a>        }
</span><span id="1029" class="l"><a href="#1029">1029: </a>    }
</span><span id="1030" class="l"><a href="#1030">1030: </a>
</span><span id="1031" class="l"><a href="#1031">1031: </a>
</span><span id="1032" class="l"><a href="#1032">1032: </a>    <span class="php-comment">/**
</span></span><span id="1033" class="l"><a href="#1033">1033: </a><span class="php-comment">     * Get an array of objects from the database to be given to DataTables as a
</span></span><span id="1034" class="l"><a href="#1034">1034: </a><span class="php-comment">     * result of an sAjaxSource request, such that DataTables can display the information
</span></span><span id="1035" class="l"><a href="#1035">1035: </a><span class="php-comment">     * from the DB in the table.
</span></span><span id="1036" class="l"><a href="#1036">1036: </a><span class="php-comment">     *
</span></span><span id="1037" class="l"><a href="#1037">1037: </a><span class="php-comment">     *  @param integer|string $id Primary key value to get an individual row
</span></span><span id="1038" class="l"><a href="#1038">1038: </a><span class="php-comment">     *    (after create or update operations). Gets the full set if not given.
</span></span><span id="1039" class="l"><a href="#1039">1039: </a><span class="php-comment">     *    If a compound key is being used, this should be the string
</span></span><span id="1040" class="l"><a href="#1040">1040: </a><span class="php-comment">     *    representation of it (i.e. joined together) rather than an array form.
</span></span><span id="1041" class="l"><a href="#1041">1041: </a><span class="php-comment">     *  @param array $http HTTP parameters from GET or POST request (so we can service
</span></span><span id="1042" class="l"><a href="#1042">1042: </a><span class="php-comment">     *    server-side processing requests from DataTables).
</span></span><span id="1043" class="l"><a href="#1043">1043: </a><span class="php-comment">     *  @return array DataTables get information
</span></span><span id="1044" class="l"><a href="#1044">1044: </a><span class="php-comment">     *  @throws \Exception Error on SQL execution
</span></span><span id="1045" class="l"><a href="#1045">1045: </a><span class="php-comment">     *  @private
</span></span><span id="1046" class="l"><a href="#1046">1046: </a><span class="php-comment">     */</span>
</span><span id="1047" class="l"><a href="#1047">1047: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _get( <span class="php-var">$id</span>=<span class="php-keyword1">null</span>, <span class="php-var">$http</span>=<span class="php-keyword1">null</span> )
</span><span id="1048" class="l"><a href="#1048">1048: </a>    {
</span><span id="1049" class="l"><a href="#1049">1049: </a>
</span><span id="1050" class="l"><a href="#1050">1050: </a>        <span class="php-var">$cancel</span> = <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'preGet'</span>, <span class="php-var">$id</span> );
</span><span id="1051" class="l"><a href="#1051">1051: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$cancel</span> === <span class="php-keyword1">false</span> ) {
</span><span id="1052" class="l"><a href="#1052">1052: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">array</span>();
</span><span id="1053" class="l"><a href="#1053">1053: </a>        }
</span><span id="1054" class="l"><a href="#1054">1054: </a>
</span><span id="1055" class="l"><a href="#1055">1055: </a>        <span class="php-var">$query</span> = <span class="php-var">$this</span>-&gt;_db
</span><span id="1056" class="l"><a href="#1056">1056: </a>            -&gt;query(<span class="php-quote">'select'</span>)
</span><span id="1057" class="l"><a href="#1057">1057: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_read_table() )
</span><span id="1058" class="l"><a href="#1058">1058: </a>            -&gt;get( <span class="php-var">$this</span>-&gt;_pkey );
</span><span id="1059" class="l"><a href="#1059">1059: </a>
</span><span id="1060" class="l"><a href="#1060">1060: </a>        <span class="php-comment">// Add all fields that we need to get from the database</span>
</span><span id="1061" class="l"><a href="#1061">1061: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="1062" class="l"><a href="#1062">1062: </a>            <span class="php-comment">// Don't reselect a pkey column if it was already added</span>
</span><span id="1063" class="l"><a href="#1063">1063: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">in_array</span>( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-var">$this</span>-&gt;_pkey ) ) {
</span><span id="1064" class="l"><a href="#1064">1064: </a>                <span class="php-keyword1">continue</span>;
</span><span id="1065" class="l"><a href="#1065">1065: </a>            }
</span><span id="1066" class="l"><a href="#1066">1066: </a>
</span><span id="1067" class="l"><a href="#1067">1067: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply(<span class="php-quote">'get'</span>) &amp;&amp; <span class="php-var">$field</span>-&gt;getValue() === <span class="php-keyword1">null</span> ) {
</span><span id="1068" class="l"><a href="#1068">1068: </a>                <span class="php-var">$query</span>-&gt;get( <span class="php-var">$field</span>-&gt;dbField() );
</span><span id="1069" class="l"><a href="#1069">1069: </a>            }
</span><span id="1070" class="l"><a href="#1070">1070: </a>        }
</span><span id="1071" class="l"><a href="#1071">1071: </a>
</span><span id="1072" class="l"><a href="#1072">1072: </a>        <span class="php-var">$this</span>-&gt;_get_where( <span class="php-var">$query</span> );
</span><span id="1073" class="l"><a href="#1073">1073: </a>        <span class="php-var">$this</span>-&gt;_perform_left_join( <span class="php-var">$query</span> );
</span><span id="1074" class="l"><a href="#1074">1074: </a>        <span class="php-var">$ssp</span> = <span class="php-var">$this</span>-&gt;_ssp_query( <span class="php-var">$query</span>, <span class="php-var">$http</span> );
</span><span id="1075" class="l"><a href="#1075">1075: </a>
</span><span id="1076" class="l"><a href="#1076">1076: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$id</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="1077" class="l"><a href="#1077">1077: </a>            <span class="php-var">$query</span>-&gt;where( <span class="php-var">$this</span>-&gt;pkeyToArray( <span class="php-var">$id</span>, <span class="php-keyword1">true</span> ) );
</span><span id="1078" class="l"><a href="#1078">1078: </a>        }
</span><span id="1079" class="l"><a href="#1079">1079: </a>
</span><span id="1080" class="l"><a href="#1080">1080: </a>        <span class="php-var">$res</span> = <span class="php-var">$query</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="1081" class="l"><a href="#1081">1081: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$res</span> ) {
</span><span id="1082" class="l"><a href="#1082">1082: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Error executing SQL for data get. Enable SQL debug using `-&gt;debug(true)`'</span>);
</span><span id="1083" class="l"><a href="#1083">1083: </a>        }
</span><span id="1084" class="l"><a href="#1084">1084: </a>
</span><span id="1085" class="l"><a href="#1085">1085: </a>        <span class="php-var">$out</span> = <span class="php-keyword1">array</span>();
</span><span id="1086" class="l"><a href="#1086">1086: </a>        <span class="php-keyword1">while</span> ( <span class="php-var">$row</span>=<span class="php-var">$res</span>-&gt;fetch() ) {
</span><span id="1087" class="l"><a href="#1087">1087: </a>            <span class="php-var">$inner</span> = <span class="php-keyword1">array</span>();
</span><span id="1088" class="l"><a href="#1088">1088: </a>            <span class="php-var">$inner</span>[<span class="php-quote">'DT_RowId'</span>] = <span class="php-var">$this</span>-&gt;_idPrefix . <span class="php-var">$this</span>-&gt;pkeyToValue( <span class="php-var">$row</span>, <span class="php-keyword1">true</span> );
</span><span id="1089" class="l"><a href="#1089">1089: </a>
</span><span id="1090" class="l"><a href="#1090">1090: </a>            <span class="php-keyword1">foreach</span> (<span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="1091" class="l"><a href="#1091">1091: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply(<span class="php-quote">'get'</span>) ) {
</span><span id="1092" class="l"><a href="#1092">1092: </a>                    <span class="php-var">$field</span>-&gt;write( <span class="php-var">$inner</span>, <span class="php-var">$row</span> );
</span><span id="1093" class="l"><a href="#1093">1093: </a>                }
</span><span id="1094" class="l"><a href="#1094">1094: </a>            }
</span><span id="1095" class="l"><a href="#1095">1095: </a>
</span><span id="1096" class="l"><a href="#1096">1096: </a>            <span class="php-var">$out</span>[] = <span class="php-var">$inner</span>;
</span><span id="1097" class="l"><a href="#1097">1097: </a>        }
</span><span id="1098" class="l"><a href="#1098">1098: </a>
</span><span id="1099" class="l"><a href="#1099">1099: </a>        <span class="php-comment">// Field options</span>
</span><span id="1100" class="l"><a href="#1100">1100: </a>        <span class="php-var">$options</span> = <span class="php-keyword1">array</span>();
</span><span id="1101" class="l"><a href="#1101">1101: </a>
</span><span id="1102" class="l"><a href="#1102">1102: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$id</span> === <span class="php-keyword1">null</span> ) {
</span><span id="1103" class="l"><a href="#1103">1103: </a>            <span class="php-keyword1">foreach</span> (<span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="1104" class="l"><a href="#1104">1104: </a>                <span class="php-var">$opts</span> = <span class="php-var">$field</span>-&gt;optionsExec( <span class="php-var">$this</span>-&gt;_db );
</span><span id="1105" class="l"><a href="#1105">1105: </a>
</span><span id="1106" class="l"><a href="#1106">1106: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> !== <span class="php-keyword1">false</span> ) {
</span><span id="1107" class="l"><a href="#1107">1107: </a>                    <span class="php-var">$options</span>[ <span class="php-var">$field</span>-&gt;name() ] = <span class="php-var">$opts</span>;
</span><span id="1108" class="l"><a href="#1108">1108: </a>                }
</span><span id="1109" class="l"><a href="#1109">1109: </a>            }
</span><span id="1110" class="l"><a href="#1110">1110: </a>        }
</span><span id="1111" class="l"><a href="#1111">1111: </a>
</span><span id="1112" class="l"><a href="#1112">1112: </a>        <span class="php-comment">// Row based &quot;joins&quot;</span>
</span><span id="1113" class="l"><a href="#1113">1113: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1114" class="l"><a href="#1114">1114: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;data( <span class="php-var">$this</span>, <span class="php-var">$out</span>, <span class="php-var">$options</span> );
</span><span id="1115" class="l"><a href="#1115">1115: </a>        }
</span><span id="1116" class="l"><a href="#1116">1116: </a>
</span><span id="1117" class="l"><a href="#1117">1117: </a>        <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'postGet'</span>, <span class="php-var">$out</span>, <span class="php-var">$id</span> );
</span><span id="1118" class="l"><a href="#1118">1118: </a>
</span><span id="1119" class="l"><a href="#1119">1119: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">array_merge</span>(
</span><span id="1120" class="l"><a href="#1120">1120: </a>            <span class="php-keyword1">array</span>(
</span><span id="1121" class="l"><a href="#1121">1121: </a>                <span class="php-quote">'data'</span>    =&gt; <span class="php-var">$out</span>,
</span><span id="1122" class="l"><a href="#1122">1122: </a>                <span class="php-quote">'options'</span> =&gt; <span class="php-var">$options</span>,
</span><span id="1123" class="l"><a href="#1123">1123: </a>                <span class="php-quote">'files'</span>   =&gt; <span class="php-var">$this</span>-&gt;_fileData( <span class="php-keyword1">null</span>, <span class="php-keyword1">null</span>, <span class="php-var">$out</span> )
</span><span id="1124" class="l"><a href="#1124">1124: </a>            ),
</span><span id="1125" class="l"><a href="#1125">1125: </a>            <span class="php-var">$ssp</span>
</span><span id="1126" class="l"><a href="#1126">1126: </a>        );
</span><span id="1127" class="l"><a href="#1127">1127: </a>    }
</span><span id="1128" class="l"><a href="#1128">1128: </a>
</span><span id="1129" class="l"><a href="#1129">1129: </a>
</span><span id="1130" class="l"><a href="#1130">1130: </a>    <span class="php-comment">/**
</span></span><span id="1131" class="l"><a href="#1131">1131: </a><span class="php-comment">     * Insert a new row in the database
</span></span><span id="1132" class="l"><a href="#1132">1132: </a><span class="php-comment">     *  @private
</span></span><span id="1133" class="l"><a href="#1133">1133: </a><span class="php-comment">     */</span>
</span><span id="1134" class="l"><a href="#1134">1134: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _insert( <span class="php-var">$values</span> )
</span><span id="1135" class="l"><a href="#1135">1135: </a>    {
</span><span id="1136" class="l"><a href="#1136">1136: </a>        <span class="php-comment">// Only allow a composite insert if the values for the key are</span>
</span><span id="1137" class="l"><a href="#1137">1137: </a>        <span class="php-comment">// submitted. This is required because there is no reliable way in MySQL</span>
</span><span id="1138" class="l"><a href="#1138">1138: </a>        <span class="php-comment">// to return the newly inserted row, so we can't know any newly</span>
</span><span id="1139" class="l"><a href="#1139">1139: </a>        <span class="php-comment">// generated values.</span>
</span><span id="1140" class="l"><a href="#1140">1140: </a>        <span class="php-var">$this</span>-&gt;_pkey_validate_insert( <span class="php-var">$values</span> );
</span><span id="1141" class="l"><a href="#1141">1141: </a>
</span><span id="1142" class="l"><a href="#1142">1142: </a>        <span class="php-comment">// Insert the new row</span>
</span><span id="1143" class="l"><a href="#1143">1143: </a>        <span class="php-var">$id</span> = <span class="php-var">$this</span>-&gt;_insert_or_update( <span class="php-keyword1">null</span>, <span class="php-var">$values</span> );
</span><span id="1144" class="l"><a href="#1144">1144: </a>
</span><span id="1145" class="l"><a href="#1145">1145: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$id</span> === <span class="php-keyword1">null</span> ) {
</span><span id="1146" class="l"><a href="#1146">1146: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="1147" class="l"><a href="#1147">1147: </a>        }
</span><span id="1148" class="l"><a href="#1148">1148: </a>
</span><span id="1149" class="l"><a href="#1149">1149: </a>        <span class="php-comment">// Was the primary key altered as part of the edit, if so use the</span>
</span><span id="1150" class="l"><a href="#1150">1150: </a>        <span class="php-comment">// submitted values</span>
</span><span id="1151" class="l"><a href="#1151">1151: </a>        <span class="php-var">$id</span> = <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_pkey ) &gt; <span class="php-num">1</span> ?
</span><span id="1152" class="l"><a href="#1152">1152: </a>            <span class="php-var">$this</span>-&gt;pkeyToValue( <span class="php-var">$values</span> ) :
</span><span id="1153" class="l"><a href="#1153">1153: </a>            <span class="php-var">$this</span>-&gt;_pkey_submit_merge( <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1154" class="l"><a href="#1154">1154: </a>
</span><span id="1155" class="l"><a href="#1155">1155: </a>        <span class="php-comment">// Join tables</span>
</span><span id="1156" class="l"><a href="#1156">1156: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1157" class="l"><a href="#1157">1157: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;create( <span class="php-var">$this</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1158" class="l"><a href="#1158">1158: </a>        }
</span><span id="1159" class="l"><a href="#1159">1159: </a>
</span><span id="1160" class="l"><a href="#1160">1160: </a>        <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'writeCreate'</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1161" class="l"><a href="#1161">1161: </a>
</span><span id="1162" class="l"><a href="#1162">1162: </a>        <span class="php-comment">// Full data set for the created row</span>
</span><span id="1163" class="l"><a href="#1163">1163: </a>        <span class="php-var">$row</span> = <span class="php-var">$this</span>-&gt;_get( <span class="php-var">$id</span> );
</span><span id="1164" class="l"><a href="#1164">1164: </a>        <span class="php-var">$row</span> = <span class="php-keyword2">count</span>( <span class="php-var">$row</span>[<span class="php-quote">'data'</span>] ) &gt; <span class="php-num">0</span> ?
</span><span id="1165" class="l"><a href="#1165">1165: </a>            <span class="php-var">$row</span>[<span class="php-quote">'data'</span>][<span class="php-num">0</span>] :
</span><span id="1166" class="l"><a href="#1166">1166: </a>            <span class="php-keyword1">null</span>;
</span><span id="1167" class="l"><a href="#1167">1167: </a>
</span><span id="1168" class="l"><a href="#1168">1168: </a>        <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'postCreate'</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span>, <span class="php-var">$row</span> );
</span><span id="1169" class="l"><a href="#1169">1169: </a>
</span><span id="1170" class="l"><a href="#1170">1170: </a>        <span class="php-keyword1">return</span> <span class="php-var">$row</span>;
</span><span id="1171" class="l"><a href="#1171">1171: </a>    }
</span><span id="1172" class="l"><a href="#1172">1172: </a>
</span><span id="1173" class="l"><a href="#1173">1173: </a>
</span><span id="1174" class="l"><a href="#1174">1174: </a>    <span class="php-comment">/**
</span></span><span id="1175" class="l"><a href="#1175">1175: </a><span class="php-comment">     * Update a row in the database
</span></span><span id="1176" class="l"><a href="#1176">1176: </a><span class="php-comment">     *  @param string $id The DOM ID for the row that is being edited.
</span></span><span id="1177" class="l"><a href="#1177">1177: </a><span class="php-comment">     *  @return array Row's data
</span></span><span id="1178" class="l"><a href="#1178">1178: </a><span class="php-comment">     *  @private
</span></span><span id="1179" class="l"><a href="#1179">1179: </a><span class="php-comment">     */</span>
</span><span id="1180" class="l"><a href="#1180">1180: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _update( <span class="php-var">$id</span>, <span class="php-var">$values</span> )
</span><span id="1181" class="l"><a href="#1181">1181: </a>    {
</span><span id="1182" class="l"><a href="#1182">1182: </a>        <span class="php-var">$id</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;_idPrefix, <span class="php-quote">''</span>, <span class="php-var">$id</span> );
</span><span id="1183" class="l"><a href="#1183">1183: </a>
</span><span id="1184" class="l"><a href="#1184">1184: </a>        <span class="php-comment">// Update or insert the rows for the parent table and the left joined</span>
</span><span id="1185" class="l"><a href="#1185">1185: </a>        <span class="php-comment">// tables</span>
</span><span id="1186" class="l"><a href="#1186">1186: </a>        <span class="php-var">$this</span>-&gt;_insert_or_update( <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1187" class="l"><a href="#1187">1187: </a>
</span><span id="1188" class="l"><a href="#1188">1188: </a>        <span class="php-comment">// And the join tables</span>
</span><span id="1189" class="l"><a href="#1189">1189: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1190" class="l"><a href="#1190">1190: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;update( <span class="php-var">$this</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1191" class="l"><a href="#1191">1191: </a>        }
</span><span id="1192" class="l"><a href="#1192">1192: </a>
</span><span id="1193" class="l"><a href="#1193">1193: </a>        <span class="php-comment">// Was the primary key altered as part of the edit, if so use the</span>
</span><span id="1194" class="l"><a href="#1194">1194: </a>        <span class="php-comment">// submitted values</span>
</span><span id="1195" class="l"><a href="#1195">1195: </a>        <span class="php-var">$getId</span> = <span class="php-var">$this</span>-&gt;_pkey_submit_merge( <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1196" class="l"><a href="#1196">1196: </a>
</span><span id="1197" class="l"><a href="#1197">1197: </a>        <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'writeEdit'</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span> );
</span><span id="1198" class="l"><a href="#1198">1198: </a>
</span><span id="1199" class="l"><a href="#1199">1199: </a>        <span class="php-comment">// Full data set for the modified row</span>
</span><span id="1200" class="l"><a href="#1200">1200: </a>        <span class="php-var">$row</span> = <span class="php-var">$this</span>-&gt;_get( <span class="php-var">$getId</span> );
</span><span id="1201" class="l"><a href="#1201">1201: </a>        <span class="php-var">$row</span> = <span class="php-keyword2">count</span>( <span class="php-var">$row</span>[<span class="php-quote">'data'</span>] ) &gt; <span class="php-num">0</span> ?
</span><span id="1202" class="l"><a href="#1202">1202: </a>            <span class="php-var">$row</span>[<span class="php-quote">'data'</span>][<span class="php-num">0</span>] :
</span><span id="1203" class="l"><a href="#1203">1203: </a>            <span class="php-keyword1">null</span>;
</span><span id="1204" class="l"><a href="#1204">1204: </a>
</span><span id="1205" class="l"><a href="#1205">1205: </a>        <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'postEdit'</span>, <span class="php-var">$id</span>, <span class="php-var">$values</span>, <span class="php-var">$row</span> );
</span><span id="1206" class="l"><a href="#1206">1206: </a>
</span><span id="1207" class="l"><a href="#1207">1207: </a>        <span class="php-keyword1">return</span> <span class="php-var">$row</span>;
</span><span id="1208" class="l"><a href="#1208">1208: </a>    }
</span><span id="1209" class="l"><a href="#1209">1209: </a>
</span><span id="1210" class="l"><a href="#1210">1210: </a>
</span><span id="1211" class="l"><a href="#1211">1211: </a>    <span class="php-comment">/**
</span></span><span id="1212" class="l"><a href="#1212">1212: </a><span class="php-comment">     * Delete one or more rows from the database
</span></span><span id="1213" class="l"><a href="#1213">1213: </a><span class="php-comment">     *  @private
</span></span><span id="1214" class="l"><a href="#1214">1214: </a><span class="php-comment">     */</span>
</span><span id="1215" class="l"><a href="#1215">1215: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _remove( <span class="php-var">$data</span> )
</span><span id="1216" class="l"><a href="#1216">1216: </a>    {
</span><span id="1217" class="l"><a href="#1217">1217: </a>        <span class="php-var">$ids</span> = <span class="php-keyword1">array</span>();
</span><span id="1218" class="l"><a href="#1218">1218: </a>
</span><span id="1219" class="l"><a href="#1219">1219: </a>        <span class="php-comment">// Get the ids to delete from the data source</span>
</span><span id="1220" class="l"><a href="#1220">1220: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$data</span>[<span class="php-quote">'data'</span>] <span class="php-keyword1">as</span> <span class="php-var">$idSrc</span> =&gt; <span class="php-var">$rowData</span>) {
</span><span id="1221" class="l"><a href="#1221">1221: </a>            <span class="php-comment">// Strip the ID prefix that the client-side sends back</span>
</span><span id="1222" class="l"><a href="#1222">1222: </a>            <span class="php-var">$id</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;_idPrefix, <span class="php-quote">&quot;&quot;</span>, <span class="php-var">$idSrc</span> );
</span><span id="1223" class="l"><a href="#1223">1223: </a>
</span><span id="1224" class="l"><a href="#1224">1224: </a>            <span class="php-var">$res</span> = <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'preRemove'</span>, <span class="php-var">$id</span>, <span class="php-var">$rowData</span> );
</span><span id="1225" class="l"><a href="#1225">1225: </a>
</span><span id="1226" class="l"><a href="#1226">1226: </a>            <span class="php-comment">// Allow the event to be cancelled and inform the client-side</span>
</span><span id="1227" class="l"><a href="#1227">1227: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> === <span class="php-keyword1">false</span> ) {
</span><span id="1228" class="l"><a href="#1228">1228: </a>                <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'cancelled'</span>][] = <span class="php-var">$idSrc</span>;
</span><span id="1229" class="l"><a href="#1229">1229: </a>            }
</span><span id="1230" class="l"><a href="#1230">1230: </a>            <span class="php-keyword1">else</span> {
</span><span id="1231" class="l"><a href="#1231">1231: </a>                <span class="php-var">$ids</span>[] = <span class="php-var">$id</span>;
</span><span id="1232" class="l"><a href="#1232">1232: </a>            }
</span><span id="1233" class="l"><a href="#1233">1233: </a>        }
</span><span id="1234" class="l"><a href="#1234">1234: </a>
</span><span id="1235" class="l"><a href="#1235">1235: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$ids</span> ) === <span class="php-num">0</span> ) {
</span><span id="1236" class="l"><a href="#1236">1236: </a>            <span class="php-keyword1">return</span>;
</span><span id="1237" class="l"><a href="#1237">1237: </a>        }
</span><span id="1238" class="l"><a href="#1238">1238: </a>
</span><span id="1239" class="l"><a href="#1239">1239: </a>        <span class="php-comment">// Row based joins - remove first as the host row will be removed which</span>
</span><span id="1240" class="l"><a href="#1240">1240: </a>        <span class="php-comment">// is a dependency</span>
</span><span id="1241" class="l"><a href="#1241">1241: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1242" class="l"><a href="#1242">1242: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;remove( <span class="php-var">$this</span>, <span class="php-var">$ids</span> );
</span><span id="1243" class="l"><a href="#1243">1243: </a>        }
</span><span id="1244" class="l"><a href="#1244">1244: </a>
</span><span id="1245" class="l"><a href="#1245">1245: </a>        <span class="php-comment">// Remove from the left join tables</span>
</span><span id="1246" class="l"><a href="#1246">1246: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_leftJoinRemove ) {
</span><span id="1247" class="l"><a href="#1247">1247: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_leftJoin) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1248" class="l"><a href="#1248">1248: </a>                <span class="php-var">$join</span> = <span class="php-var">$this</span>-&gt;_leftJoin[<span class="php-var">$i</span>];
</span><span id="1249" class="l"><a href="#1249">1249: </a>                <span class="php-var">$table</span> = <span class="php-var">$this</span>-&gt;_alias( <span class="php-var">$join</span>[<span class="php-quote">'table'</span>], <span class="php-quote">'orig'</span> );
</span><span id="1250" class="l"><a href="#1250">1250: </a>
</span><span id="1251" class="l"><a href="#1251">1251: </a>                <span class="php-comment">// which side of the join refers to the parent table?</span>
</span><span id="1252" class="l"><a href="#1252">1252: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>], <span class="php-var">$join</span>[<span class="php-quote">'table'</span>] ) === <span class="php-num">0</span> ) {
</span><span id="1253" class="l"><a href="#1253">1253: </a>                    <span class="php-var">$parentLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field2'</span>];
</span><span id="1254" class="l"><a href="#1254">1254: </a>                    <span class="php-var">$childLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>];
</span><span id="1255" class="l"><a href="#1255">1255: </a>                }
</span><span id="1256" class="l"><a href="#1256">1256: </a>                <span class="php-keyword1">else</span> {
</span><span id="1257" class="l"><a href="#1257">1257: </a>                    <span class="php-var">$parentLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>];
</span><span id="1258" class="l"><a href="#1258">1258: </a>                    <span class="php-var">$childLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field2'</span>];
</span><span id="1259" class="l"><a href="#1259">1259: </a>                }
</span><span id="1260" class="l"><a href="#1260">1260: </a>
</span><span id="1261" class="l"><a href="#1261">1261: </a>                <span class="php-comment">// Only delete on the primary key, since that is what the ids refer</span>
</span><span id="1262" class="l"><a href="#1262">1262: </a>                <span class="php-comment">// to - otherwise we'd be deleting random data! Note that this</span>
</span><span id="1263" class="l"><a href="#1263">1263: </a>                <span class="php-comment">// won't work with compound keys since the parent link would be</span>
</span><span id="1264" class="l"><a href="#1264">1264: </a>                <span class="php-comment">// over multiple fields.</span>
</span><span id="1265" class="l"><a href="#1265">1265: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$parentLink</span> === <span class="php-var">$this</span>-&gt;_pkey[<span class="php-num">0</span>] &amp;&amp; <span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_pkey) === <span class="php-num">1</span> ) {
</span><span id="1266" class="l"><a href="#1266">1266: </a>                    <span class="php-var">$this</span>-&gt;_remove_table( <span class="php-var">$join</span>[<span class="php-quote">'table'</span>], <span class="php-var">$ids</span>, <span class="php-keyword1">array</span>(<span class="php-var">$childLink</span>) );
</span><span id="1267" class="l"><a href="#1267">1267: </a>                }
</span><span id="1268" class="l"><a href="#1268">1268: </a>            }
</span><span id="1269" class="l"><a href="#1269">1269: </a>        }
</span><span id="1270" class="l"><a href="#1270">1270: </a>
</span><span id="1271" class="l"><a href="#1271">1271: </a>        <span class="php-comment">// Remove from the primary tables</span>
</span><span id="1272" class="l"><a href="#1272">1272: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_table) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1273" class="l"><a href="#1273">1273: </a>            <span class="php-var">$this</span>-&gt;_remove_table( <span class="php-var">$this</span>-&gt;_table[<span class="php-var">$i</span>], <span class="php-var">$ids</span> );
</span><span id="1274" class="l"><a href="#1274">1274: </a>        }
</span><span id="1275" class="l"><a href="#1275">1275: </a>
</span><span id="1276" class="l"><a href="#1276">1276: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$data</span>[<span class="php-quote">'data'</span>] <span class="php-keyword1">as</span> <span class="php-var">$idSrc</span> =&gt; <span class="php-var">$rowData</span>) {
</span><span id="1277" class="l"><a href="#1277">1277: </a>            <span class="php-var">$id</span> = <span class="php-keyword2">str_replace</span>( <span class="php-var">$this</span>-&gt;_idPrefix, <span class="php-quote">&quot;&quot;</span>, <span class="php-var">$idSrc</span> );
</span><span id="1278" class="l"><a href="#1278">1278: </a>
</span><span id="1279" class="l"><a href="#1279">1279: </a>            <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'postRemove'</span>, <span class="php-var">$id</span>, <span class="php-var">$rowData</span> );
</span><span id="1280" class="l"><a href="#1280">1280: </a>        }
</span><span id="1281" class="l"><a href="#1281">1281: </a>    }
</span><span id="1282" class="l"><a href="#1282">1282: </a>
</span><span id="1283" class="l"><a href="#1283">1283: </a>
</span><span id="1284" class="l"><a href="#1284">1284: </a>    <span class="php-comment">/**
</span></span><span id="1285" class="l"><a href="#1285">1285: </a><span class="php-comment">     * File upload
</span></span><span id="1286" class="l"><a href="#1286">1286: </a><span class="php-comment">     *  @param array $data Upload data
</span></span><span id="1287" class="l"><a href="#1287">1287: </a><span class="php-comment">     *  @throws \Exception File upload name error
</span></span><span id="1288" class="l"><a href="#1288">1288: </a><span class="php-comment">     *  @private
</span></span><span id="1289" class="l"><a href="#1289">1289: </a><span class="php-comment">     */</span>
</span><span id="1290" class="l"><a href="#1290">1290: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _upload( <span class="php-var">$data</span> )
</span><span id="1291" class="l"><a href="#1291">1291: </a>    {
</span><span id="1292" class="l"><a href="#1292">1292: </a>        <span class="php-comment">// Search for upload field in local fields</span>
</span><span id="1293" class="l"><a href="#1293">1293: </a>        <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$data</span>[<span class="php-quote">'uploadField'</span>], <span class="php-quote">'name'</span> );
</span><span id="1294" class="l"><a href="#1294">1294: </a>        <span class="php-var">$fieldName</span> = <span class="php-quote">''</span>;
</span><span id="1295" class="l"><a href="#1295">1295: </a>
</span><span id="1296" class="l"><a href="#1296">1296: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> ) {
</span><span id="1297" class="l"><a href="#1297">1297: </a>            <span class="php-comment">// Perhaps it is in a join instance</span>
</span><span id="1298" class="l"><a href="#1298">1298: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1299" class="l"><a href="#1299">1299: </a>                <span class="php-var">$join</span> = <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>];
</span><span id="1300" class="l"><a href="#1300">1300: </a>                <span class="php-var">$fields</span> = <span class="php-var">$join</span>-&gt;fields();
</span><span id="1301" class="l"><a href="#1301">1301: </a>
</span><span id="1302" class="l"><a href="#1302">1302: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$j</span>=<span class="php-num">0</span>, <span class="php-var">$jen</span>=<span class="php-keyword2">count</span>(<span class="php-var">$fields</span>) ; <span class="php-var">$j</span>&lt;<span class="php-var">$jen</span> ; <span class="php-var">$j</span>++ ) {
</span><span id="1303" class="l"><a href="#1303">1303: </a>                    <span class="php-var">$joinField</span> = <span class="php-var">$fields</span>[ <span class="php-var">$j</span> ];
</span><span id="1304" class="l"><a href="#1304">1304: </a>                    <span class="php-var">$name</span> = <span class="php-var">$join</span>-&gt;name().<span class="php-quote">'[].'</span>.<span class="php-var">$joinField</span>-&gt;name();
</span><span id="1305" class="l"><a href="#1305">1305: </a>
</span><span id="1306" class="l"><a href="#1306">1306: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$name</span> === <span class="php-var">$data</span>[<span class="php-quote">'uploadField'</span>] ) {
</span><span id="1307" class="l"><a href="#1307">1307: </a>                        <span class="php-var">$field</span> = <span class="php-var">$joinField</span>;
</span><span id="1308" class="l"><a href="#1308">1308: </a>                        <span class="php-var">$fieldName</span> = <span class="php-var">$name</span>;
</span><span id="1309" class="l"><a href="#1309">1309: </a>                    }
</span><span id="1310" class="l"><a href="#1310">1310: </a>                }
</span><span id="1311" class="l"><a href="#1311">1311: </a>            }
</span><span id="1312" class="l"><a href="#1312">1312: </a>        }
</span><span id="1313" class="l"><a href="#1313">1313: </a>        <span class="php-keyword1">else</span> {
</span><span id="1314" class="l"><a href="#1314">1314: </a>            <span class="php-var">$fieldName</span> = <span class="php-var">$field</span>-&gt;name();
</span><span id="1315" class="l"><a href="#1315">1315: </a>        }
</span><span id="1316" class="l"><a href="#1316">1316: </a>
</span><span id="1317" class="l"><a href="#1317">1317: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> ) {
</span><span id="1318" class="l"><a href="#1318">1318: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Unknown upload field name submitted&quot;</span>);
</span><span id="1319" class="l"><a href="#1319">1319: </a>        }
</span><span id="1320" class="l"><a href="#1320">1320: </a>
</span><span id="1321" class="l"><a href="#1321">1321: </a>        <span class="php-var">$res</span> = <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'preUpload'</span>, <span class="php-var">$data</span> );
</span><span id="1322" class="l"><a href="#1322">1322: </a>        
</span><span id="1323" class="l"><a href="#1323">1323: </a>        <span class="php-comment">// Allow the event to be cancelled and inform the client-side</span>
</span><span id="1324" class="l"><a href="#1324">1324: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> === <span class="php-keyword1">false</span> ) {
</span><span id="1325" class="l"><a href="#1325">1325: </a>            <span class="php-keyword1">return</span>;
</span><span id="1326" class="l"><a href="#1326">1326: </a>        }
</span><span id="1327" class="l"><a href="#1327">1327: </a>
</span><span id="1328" class="l"><a href="#1328">1328: </a>        <span class="php-var">$upload</span> = <span class="php-var">$field</span>-&gt;upload();
</span><span id="1329" class="l"><a href="#1329">1329: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$upload</span> ) {
</span><span id="1330" class="l"><a href="#1330">1330: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;File uploaded to a field that does not have upload options configured&quot;</span>);
</span><span id="1331" class="l"><a href="#1331">1331: </a>        }
</span><span id="1332" class="l"><a href="#1332">1332: </a>
</span><span id="1333" class="l"><a href="#1333">1333: </a>        <span class="php-var">$res</span> = <span class="php-var">$upload</span>-&gt;<span class="php-keyword2">exec</span>( <span class="php-var">$this</span> );
</span><span id="1334" class="l"><a href="#1334">1334: </a>
</span><span id="1335" class="l"><a href="#1335">1335: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> === <span class="php-keyword1">false</span> ) {
</span><span id="1336" class="l"><a href="#1336">1336: </a>            <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'fieldErrors'</span>][] = <span class="php-keyword1">array</span>(
</span><span id="1337" class="l"><a href="#1337">1337: </a>                <span class="php-quote">&quot;name&quot;</span>   =&gt; <span class="php-var">$fieldName</span>,      <span class="php-comment">// field name can be just the field's</span>
</span><span id="1338" class="l"><a href="#1338">1338: </a>                <span class="php-quote">&quot;status&quot;</span> =&gt; <span class="php-var">$upload</span>-&gt;error() <span class="php-comment">// name or a join combination</span>
</span><span id="1339" class="l"><a href="#1339">1339: </a>            );
</span><span id="1340" class="l"><a href="#1340">1340: </a>        }
</span><span id="1341" class="l"><a href="#1341">1341: </a>        <span class="php-keyword1">else</span> {
</span><span id="1342" class="l"><a href="#1342">1342: </a>            <span class="php-var">$files</span> = <span class="php-var">$this</span>-&gt;_fileData( <span class="php-var">$upload</span>-&gt;table(), <span class="php-keyword1">array</span>(<span class="php-var">$res</span>) );
</span><span id="1343" class="l"><a href="#1343">1343: </a>
</span><span id="1344" class="l"><a href="#1344">1344: </a>            <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'files'</span>] = <span class="php-var">$files</span>;
</span><span id="1345" class="l"><a href="#1345">1345: </a>            <span class="php-var">$this</span>-&gt;_out[<span class="php-quote">'upload'</span>][<span class="php-quote">'id'</span>] = <span class="php-var">$res</span>;
</span><span id="1346" class="l"><a href="#1346">1346: </a>        
</span><span id="1347" class="l"><a href="#1347">1347: </a>            <span class="php-var">$this</span>-&gt;_trigger( <span class="php-quote">'postUpload'</span>, <span class="php-var">$res</span>, <span class="php-var">$files</span>, <span class="php-var">$data</span> );
</span><span id="1348" class="l"><a href="#1348">1348: </a>        }
</span><span id="1349" class="l"><a href="#1349">1349: </a>    }
</span><span id="1350" class="l"><a href="#1350">1350: </a>
</span><span id="1351" class="l"><a href="#1351">1351: </a>
</span><span id="1352" class="l"><a href="#1352">1352: </a>    <span class="php-comment">/**
</span></span><span id="1353" class="l"><a href="#1353">1353: </a><span class="php-comment">     * Get information about the files that are detailed in the database for
</span></span><span id="1354" class="l"><a href="#1354">1354: </a><span class="php-comment">     * the fields which have an upload method defined on them.
</span></span><span id="1355" class="l"><a href="#1355">1355: </a><span class="php-comment">     *
</span></span><span id="1356" class="l"><a href="#1356">1356: </a><span class="php-comment">     * @param  string [$limitTable=null] Limit the data gathering to a single
</span></span><span id="1357" class="l"><a href="#1357">1357: </a><span class="php-comment">     *     table only
</span></span><span id="1358" class="l"><a href="#1358">1358: </a><span class="php-comment">     * @param number[] [$id=null] Limit to a specific set of ids
</span></span><span id="1359" class="l"><a href="#1359">1359: </a><span class="php-comment">     * @return array File information
</span></span><span id="1360" class="l"><a href="#1360">1360: </a><span class="php-comment">     * @private
</span></span><span id="1361" class="l"><a href="#1361">1361: </a><span class="php-comment">     */</span>
</span><span id="1362" class="l"><a href="#1362">1362: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _fileData ( <span class="php-var">$limitTable</span>=<span class="php-keyword1">null</span>, <span class="php-var">$ids</span>=<span class="php-keyword1">null</span>, <span class="php-var">$data</span>=<span class="php-keyword1">null</span> )
</span><span id="1363" class="l"><a href="#1363">1363: </a>    {
</span><span id="1364" class="l"><a href="#1364">1364: </a>        <span class="php-var">$files</span> = <span class="php-keyword1">array</span>();
</span><span id="1365" class="l"><a href="#1365">1365: </a>
</span><span id="1366" class="l"><a href="#1366">1366: </a>        <span class="php-comment">// The fields in this instance</span>
</span><span id="1367" class="l"><a href="#1367">1367: </a>        <span class="php-var">$this</span>-&gt;_fileDataFields( <span class="php-var">$files</span>, <span class="php-var">$this</span>-&gt;_fields, <span class="php-var">$limitTable</span>, <span class="php-var">$ids</span>, <span class="php-var">$data</span> );
</span><span id="1368" class="l"><a href="#1368">1368: </a>        
</span><span id="1369" class="l"><a href="#1369">1369: </a>        <span class="php-comment">// From joined tables</span>
</span><span id="1370" class="l"><a href="#1370">1370: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1371" class="l"><a href="#1371">1371: </a>            <span class="php-var">$joinData</span> = <span class="php-keyword1">null</span>;
</span><span id="1372" class="l"><a href="#1372">1372: </a>
</span><span id="1373" class="l"><a href="#1373">1373: </a>            <span class="php-comment">// If we have data from the get, it is nested from the join, so we need to</span>
</span><span id="1374" class="l"><a href="#1374">1374: </a>            <span class="php-comment">// un-nest it (i.e. get the array of joined data for each row)</span>
</span><span id="1375" class="l"><a href="#1375">1375: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$data</span> ) {
</span><span id="1376" class="l"><a href="#1376">1376: </a>                <span class="php-var">$joinData</span> = <span class="php-keyword1">array</span>();
</span><span id="1377" class="l"><a href="#1377">1377: </a>
</span><span id="1378" class="l"><a href="#1378">1378: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$j</span>=<span class="php-num">0</span>, <span class="php-var">$jen</span>=<span class="php-keyword2">count</span>(<span class="php-var">$data</span>) ; <span class="php-var">$j</span>&lt;<span class="php-var">$jen</span> ; <span class="php-var">$j</span>++ ) {
</span><span id="1379" class="l"><a href="#1379">1379: </a>                    <span class="php-var">$joinData</span> = <span class="php-keyword2">array_merge</span>( <span class="php-var">$joinData</span>, <span class="php-var">$data</span>[<span class="php-var">$j</span>][<span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;name()] );
</span><span id="1380" class="l"><a href="#1380">1380: </a>                }
</span><span id="1381" class="l"><a href="#1381">1381: </a>            }
</span><span id="1382" class="l"><a href="#1382">1382: </a>
</span><span id="1383" class="l"><a href="#1383">1383: </a>            <span class="php-var">$this</span>-&gt;_fileDataFields( <span class="php-var">$files</span>, <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;fields(), <span class="php-var">$limitTable</span>, <span class="php-var">$ids</span>, <span class="php-var">$joinData</span> );
</span><span id="1384" class="l"><a href="#1384">1384: </a>        }
</span><span id="1385" class="l"><a href="#1385">1385: </a>
</span><span id="1386" class="l"><a href="#1386">1386: </a>        <span class="php-keyword1">return</span> <span class="php-var">$files</span>;
</span><span id="1387" class="l"><a href="#1387">1387: </a>    }
</span><span id="1388" class="l"><a href="#1388">1388: </a>
</span><span id="1389" class="l"><a href="#1389">1389: </a>
</span><span id="1390" class="l"><a href="#1390">1390: </a>    <span class="php-comment">/**
</span></span><span id="1391" class="l"><a href="#1391">1391: </a><span class="php-comment">     * Common file get method for any array of fields
</span></span><span id="1392" class="l"><a href="#1392">1392: </a><span class="php-comment">     * @param  array &amp;$files File output array
</span></span><span id="1393" class="l"><a href="#1393">1393: </a><span class="php-comment">     * @param  Field[] $fields Fields to get file information about
</span></span><span id="1394" class="l"><a href="#1394">1394: </a><span class="php-comment">     * @param  string[] $limitTable Limit the data gathering to a single table
</span></span><span id="1395" class="l"><a href="#1395">1395: </a><span class="php-comment">     *     only
</span></span><span id="1396" class="l"><a href="#1396">1396: </a><span class="php-comment">     * @private
</span></span><span id="1397" class="l"><a href="#1397">1397: </a><span class="php-comment">     */</span>
</span><span id="1398" class="l"><a href="#1398">1398: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _fileDataFields ( &amp;<span class="php-var">$files</span>, <span class="php-var">$fields</span>, <span class="php-var">$limitTable</span>, <span class="php-var">$ids</span>=<span class="php-keyword1">null</span>, <span class="php-var">$data</span>=<span class="php-keyword1">null</span> )
</span><span id="1399" class="l"><a href="#1399">1399: </a>    {
</span><span id="1400" class="l"><a href="#1400">1400: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$fields</span> <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="1401" class="l"><a href="#1401">1401: </a>            <span class="php-var">$upload</span> = <span class="php-var">$field</span>-&gt;upload();
</span><span id="1402" class="l"><a href="#1402">1402: </a>
</span><span id="1403" class="l"><a href="#1403">1403: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$upload</span> ) {
</span><span id="1404" class="l"><a href="#1404">1404: </a>                <span class="php-var">$table</span> = <span class="php-var">$upload</span>-&gt;table();
</span><span id="1405" class="l"><a href="#1405">1405: </a>
</span><span id="1406" class="l"><a href="#1406">1406: </a>                <span class="php-keyword1">if</span> ( ! <span class="php-var">$table</span> ) {
</span><span id="1407" class="l"><a href="#1407">1407: </a>                    <span class="php-keyword1">continue</span>;
</span><span id="1408" class="l"><a href="#1408">1408: </a>                }
</span><span id="1409" class="l"><a href="#1409">1409: </a>
</span><span id="1410" class="l"><a href="#1410">1410: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$limitTable</span> !== <span class="php-keyword1">null</span> &amp;&amp; <span class="php-var">$table</span> !== <span class="php-var">$limitTable</span> ) {
</span><span id="1411" class="l"><a href="#1411">1411: </a>                    <span class="php-keyword1">continue</span>;
</span><span id="1412" class="l"><a href="#1412">1412: </a>                }
</span><span id="1413" class="l"><a href="#1413">1413: </a>
</span><span id="1414" class="l"><a href="#1414">1414: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$files</span>[ <span class="php-var">$table</span> ] ) ) {
</span><span id="1415" class="l"><a href="#1415">1415: </a>                    <span class="php-keyword1">continue</span>;
</span><span id="1416" class="l"><a href="#1416">1416: </a>                }
</span><span id="1417" class="l"><a href="#1417">1417: </a>
</span><span id="1418" class="l"><a href="#1418">1418: </a>                <span class="php-comment">// Make a collection of the ids used in this data set to get a limited data set</span>
</span><span id="1419" class="l"><a href="#1419">1419: </a>                <span class="php-comment">// in return (security and performance)</span>
</span><span id="1420" class="l"><a href="#1420">1420: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$ids</span> === <span class="php-keyword1">null</span> ) {
</span><span id="1421" class="l"><a href="#1421">1421: </a>                    <span class="php-var">$ids</span> = <span class="php-keyword1">array</span>();
</span><span id="1422" class="l"><a href="#1422">1422: </a>                }
</span><span id="1423" class="l"><a href="#1423">1423: </a>
</span><span id="1424" class="l"><a href="#1424">1424: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$data</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="1425" class="l"><a href="#1425">1425: </a>                    <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$data</span>); <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1426" class="l"><a href="#1426">1426: </a>                        <span class="php-var">$val</span> = <span class="php-var">$field</span>-&gt;val( <span class="php-quote">'set'</span>, <span class="php-var">$data</span>[<span class="php-var">$i</span>] );
</span><span id="1427" class="l"><a href="#1427">1427: </a>
</span><span id="1428" class="l"><a href="#1428">1428: </a>                        <span class="php-keyword1">if</span> ( <span class="php-var">$val</span> ) {
</span><span id="1429" class="l"><a href="#1429">1429: </a>                            <span class="php-var">$ids</span>[] = <span class="php-var">$val</span>;
</span><span id="1430" class="l"><a href="#1430">1430: </a>                        }
</span><span id="1431" class="l"><a href="#1431">1431: </a>                    }
</span><span id="1432" class="l"><a href="#1432">1432: </a>
</span><span id="1433" class="l"><a href="#1433">1433: </a>                    <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$ids</span>) === <span class="php-num">0</span> ) {
</span><span id="1434" class="l"><a href="#1434">1434: </a>                        <span class="php-comment">// If no data to fetch, then don't bother</span>
</span><span id="1435" class="l"><a href="#1435">1435: </a>                        <span class="php-keyword1">return</span>;
</span><span id="1436" class="l"><a href="#1436">1436: </a>                    }
</span><span id="1437" class="l"><a href="#1437">1437: </a>                    <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$ids</span>) &gt; <span class="php-num">1000</span> ) {
</span><span id="1438" class="l"><a href="#1438">1438: </a>                        <span class="php-comment">// Don't use `where_in` for really large data sets</span>
</span><span id="1439" class="l"><a href="#1439">1439: </a>                        <span class="php-var">$ids</span> = <span class="php-keyword1">array</span>();
</span><span id="1440" class="l"><a href="#1440">1440: </a>                    }
</span><span id="1441" class="l"><a href="#1441">1441: </a>                }
</span><span id="1442" class="l"><a href="#1442">1442: </a>
</span><span id="1443" class="l"><a href="#1443">1443: </a>                <span class="php-var">$fileData</span> = <span class="php-var">$upload</span>-&gt;data( <span class="php-var">$this</span>-&gt;_db, <span class="php-var">$ids</span> );
</span><span id="1444" class="l"><a href="#1444">1444: </a>
</span><span id="1445" class="l"><a href="#1445">1445: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$fileData</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="1446" class="l"><a href="#1446">1446: </a>                    <span class="php-var">$files</span>[ <span class="php-var">$table</span> ] = <span class="php-var">$fileData</span>;
</span><span id="1447" class="l"><a href="#1447">1447: </a>                }
</span><span id="1448" class="l"><a href="#1448">1448: </a>            }
</span><span id="1449" class="l"><a href="#1449">1449: </a>        }
</span><span id="1450" class="l"><a href="#1450">1450: </a>    }
</span><span id="1451" class="l"><a href="#1451">1451: </a>
</span><span id="1452" class="l"><a href="#1452">1452: </a>    <span class="php-comment">/**
</span></span><span id="1453" class="l"><a href="#1453">1453: </a><span class="php-comment">     * Run the file clean up
</span></span><span id="1454" class="l"><a href="#1454">1454: </a><span class="php-comment">     *
</span></span><span id="1455" class="l"><a href="#1455">1455: </a><span class="php-comment">     * @private
</span></span><span id="1456" class="l"><a href="#1456">1456: </a><span class="php-comment">     */</span>
</span><span id="1457" class="l"><a href="#1457">1457: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _fileClean ()
</span><span id="1458" class="l"><a href="#1458">1458: </a>    {
</span><span id="1459" class="l"><a href="#1459">1459: </a>        <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span> ) {
</span><span id="1460" class="l"><a href="#1460">1460: </a>            <span class="php-var">$upload</span> = <span class="php-var">$field</span>-&gt;upload();
</span><span id="1461" class="l"><a href="#1461">1461: </a>
</span><span id="1462" class="l"><a href="#1462">1462: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$upload</span> ) {
</span><span id="1463" class="l"><a href="#1463">1463: </a>                <span class="php-var">$upload</span>-&gt;dbCleanExec( <span class="php-var">$this</span>, <span class="php-var">$field</span> );
</span><span id="1464" class="l"><a href="#1464">1464: </a>            }
</span><span id="1465" class="l"><a href="#1465">1465: </a>        }
</span><span id="1466" class="l"><a href="#1466">1466: </a>
</span><span id="1467" class="l"><a href="#1467">1467: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_join) ; <span class="php-var">$i</span>++ ) {
</span><span id="1468" class="l"><a href="#1468">1468: </a>            <span class="php-keyword1">foreach</span> ( <span class="php-var">$this</span>-&gt;_join[<span class="php-var">$i</span>]-&gt;fields() <span class="php-keyword1">as</span> <span class="php-var">$field</span> ) {
</span><span id="1469" class="l"><a href="#1469">1469: </a>                <span class="php-var">$upload</span> = <span class="php-var">$field</span>-&gt;upload();
</span><span id="1470" class="l"><a href="#1470">1470: </a>
</span><span id="1471" class="l"><a href="#1471">1471: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$upload</span> ) {
</span><span id="1472" class="l"><a href="#1472">1472: </a>                    <span class="php-var">$upload</span>-&gt;dbCleanExec( <span class="php-var">$this</span>, <span class="php-var">$field</span> );
</span><span id="1473" class="l"><a href="#1473">1473: </a>                }
</span><span id="1474" class="l"><a href="#1474">1474: </a>            }
</span><span id="1475" class="l"><a href="#1475">1475: </a>        }
</span><span id="1476" class="l"><a href="#1476">1476: </a>    }
</span><span id="1477" class="l"><a href="#1477">1477: </a>
</span><span id="1478" class="l"><a href="#1478">1478: </a>
</span><span id="1479" class="l"><a href="#1479">1479: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="1480" class="l"><a href="#1480">1480: </a><span class="php-comment">     * Server-side processing methods
</span></span><span id="1481" class="l"><a href="#1481">1481: </a><span class="php-comment">     */</span>
</span><span id="1482" class="l"><a href="#1482">1482: </a>
</span><span id="1483" class="l"><a href="#1483">1483: </a>    <span class="php-comment">/**
</span></span><span id="1484" class="l"><a href="#1484">1484: </a><span class="php-comment">     * When server-side processing is being used, modify the query with // the
</span></span><span id="1485" class="l"><a href="#1485">1485: </a><span class="php-comment">     * required extra conditions
</span></span><span id="1486" class="l"><a href="#1486">1486: </a><span class="php-comment">     *
</span></span><span id="1487" class="l"><a href="#1487">1487: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the SSP commands to
</span></span><span id="1488" class="l"><a href="#1488">1488: </a><span class="php-comment">     *  @param array $http Parameters from HTTP request
</span></span><span id="1489" class="l"><a href="#1489">1489: </a><span class="php-comment">     *  @return array Server-side processing information array
</span></span><span id="1490" class="l"><a href="#1490">1490: </a><span class="php-comment">     *  @private
</span></span><span id="1491" class="l"><a href="#1491">1491: </a><span class="php-comment">     */</span>
</span><span id="1492" class="l"><a href="#1492">1492: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _ssp_query ( <span class="php-var">$query</span>, <span class="php-var">$http</span> )
</span><span id="1493" class="l"><a href="#1493">1493: </a>    {
</span><span id="1494" class="l"><a href="#1494">1494: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$http</span>[<span class="php-quote">'draw'</span>] ) ) {
</span><span id="1495" class="l"><a href="#1495">1495: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">array</span>();
</span><span id="1496" class="l"><a href="#1496">1496: </a>        }
</span><span id="1497" class="l"><a href="#1497">1497: </a>
</span><span id="1498" class="l"><a href="#1498">1498: </a>        <span class="php-comment">// Add the server-side processing conditions</span>
</span><span id="1499" class="l"><a href="#1499">1499: </a>        <span class="php-var">$this</span>-&gt;_ssp_limit( <span class="php-var">$query</span>, <span class="php-var">$http</span> );
</span><span id="1500" class="l"><a href="#1500">1500: </a>        <span class="php-var">$this</span>-&gt;_ssp_sort( <span class="php-var">$query</span>, <span class="php-var">$http</span> );
</span><span id="1501" class="l"><a href="#1501">1501: </a>        <span class="php-var">$this</span>-&gt;_ssp_filter( <span class="php-var">$query</span>, <span class="php-var">$http</span> );
</span><span id="1502" class="l"><a href="#1502">1502: </a>
</span><span id="1503" class="l"><a href="#1503">1503: </a>        <span class="php-comment">// Get the number of rows in the result set</span>
</span><span id="1504" class="l"><a href="#1504">1504: </a>        <span class="php-var">$ssp_set_count</span> = <span class="php-var">$this</span>-&gt;_db
</span><span id="1505" class="l"><a href="#1505">1505: </a>            -&gt;query(<span class="php-quote">'select'</span>)
</span><span id="1506" class="l"><a href="#1506">1506: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_read_table() )
</span><span id="1507" class="l"><a href="#1507">1507: </a>            -&gt;get( <span class="php-quote">'COUNT('</span>.<span class="php-var">$this</span>-&gt;_pkey[<span class="php-num">0</span>].<span class="php-quote">') as cnt'</span> );
</span><span id="1508" class="l"><a href="#1508">1508: </a>        <span class="php-var">$this</span>-&gt;_get_where( <span class="php-var">$ssp_set_count</span> );
</span><span id="1509" class="l"><a href="#1509">1509: </a>        <span class="php-var">$this</span>-&gt;_ssp_filter( <span class="php-var">$ssp_set_count</span>, <span class="php-var">$http</span> );
</span><span id="1510" class="l"><a href="#1510">1510: </a>        <span class="php-var">$this</span>-&gt;_perform_left_join( <span class="php-var">$ssp_set_count</span> );
</span><span id="1511" class="l"><a href="#1511">1511: </a>        <span class="php-var">$ssp_set_count</span> = <span class="php-var">$ssp_set_count</span>-&gt;<span class="php-keyword2">exec</span>()-&gt;fetch();
</span><span id="1512" class="l"><a href="#1512">1512: </a>
</span><span id="1513" class="l"><a href="#1513">1513: </a>        <span class="php-comment">// Get the number of rows in the full set</span>
</span><span id="1514" class="l"><a href="#1514">1514: </a>        <span class="php-var">$ssp_full_count</span> = <span class="php-var">$this</span>-&gt;_db
</span><span id="1515" class="l"><a href="#1515">1515: </a>            -&gt;query(<span class="php-quote">'select'</span>)
</span><span id="1516" class="l"><a href="#1516">1516: </a>            -&gt;table( <span class="php-var">$this</span>-&gt;_read_table() )
</span><span id="1517" class="l"><a href="#1517">1517: </a>            -&gt;get( <span class="php-quote">'COUNT('</span>.<span class="php-var">$this</span>-&gt;_pkey[<span class="php-num">0</span>].<span class="php-quote">') as cnt'</span> );
</span><span id="1518" class="l"><a href="#1518">1518: </a>        <span class="php-var">$this</span>-&gt;_get_where( <span class="php-var">$ssp_full_count</span> );
</span><span id="1519" class="l"><a href="#1519">1519: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_where ) ) { <span class="php-comment">// only needed if there is a where condition</span>
</span><span id="1520" class="l"><a href="#1520">1520: </a>            <span class="php-var">$this</span>-&gt;_perform_left_join( <span class="php-var">$ssp_full_count</span> );
</span><span id="1521" class="l"><a href="#1521">1521: </a>        }
</span><span id="1522" class="l"><a href="#1522">1522: </a>        <span class="php-var">$ssp_full_count</span> = <span class="php-var">$ssp_full_count</span>-&gt;<span class="php-keyword2">exec</span>()-&gt;fetch();
</span><span id="1523" class="l"><a href="#1523">1523: </a>
</span><span id="1524" class="l"><a href="#1524">1524: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">array</span>(
</span><span id="1525" class="l"><a href="#1525">1525: </a>            <span class="php-quote">&quot;draw&quot;</span> =&gt; <span class="php-keyword2">intval</span>( <span class="php-var">$http</span>[<span class="php-quote">'draw'</span>] ),
</span><span id="1526" class="l"><a href="#1526">1526: </a>            <span class="php-quote">&quot;recordsTotal&quot;</span> =&gt; <span class="php-var">$ssp_full_count</span>[<span class="php-quote">'cnt'</span>],
</span><span id="1527" class="l"><a href="#1527">1527: </a>            <span class="php-quote">&quot;recordsFiltered&quot;</span> =&gt; <span class="php-var">$ssp_set_count</span>[<span class="php-quote">'cnt'</span>]
</span><span id="1528" class="l"><a href="#1528">1528: </a>        );
</span><span id="1529" class="l"><a href="#1529">1529: </a>    }
</span><span id="1530" class="l"><a href="#1530">1530: </a>
</span><span id="1531" class="l"><a href="#1531">1531: </a>
</span><span id="1532" class="l"><a href="#1532">1532: </a>    <span class="php-comment">/**
</span></span><span id="1533" class="l"><a href="#1533">1533: </a><span class="php-comment">     * Convert a column index to a database field name - used for server-side
</span></span><span id="1534" class="l"><a href="#1534">1534: </a><span class="php-comment">     * processing requests.
</span></span><span id="1535" class="l"><a href="#1535">1535: </a><span class="php-comment">     *  @param array $http HTTP variables (i.e. GET or POST)
</span></span><span id="1536" class="l"><a href="#1536">1536: </a><span class="php-comment">     *  @param int $index Index in the DataTables' submitted data
</span></span><span id="1537" class="l"><a href="#1537">1537: </a><span class="php-comment">     *  @returns string DB field name
</span></span><span id="1538" class="l"><a href="#1538">1538: </a><span class="php-comment">     *  @throws \Exception Unknown fields
</span></span><span id="1539" class="l"><a href="#1539">1539: </a><span class="php-comment">     *  @private Note that it is actually public for PHP 5.3 - thread 39810
</span></span><span id="1540" class="l"><a href="#1540">1540: </a><span class="php-comment">     */</span>
</span><span id="1541" class="l"><a href="#1541">1541: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> _ssp_field( <span class="php-var">$http</span>, <span class="php-var">$index</span> )
</span><span id="1542" class="l"><a href="#1542">1542: </a>    {
</span><span id="1543" class="l"><a href="#1543">1543: </a>        <span class="php-var">$name</span> = <span class="php-var">$http</span>[<span class="php-quote">'columns'</span>][<span class="php-var">$index</span>][<span class="php-quote">'data'</span>];
</span><span id="1544" class="l"><a href="#1544">1544: </a>        <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$name</span>, <span class="php-quote">'name'</span> );
</span><span id="1545" class="l"><a href="#1545">1545: </a>
</span><span id="1546" class="l"><a href="#1546">1546: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> ) {
</span><span id="1547" class="l"><a href="#1547">1547: </a>            <span class="php-comment">// Is it the primary key?</span>
</span><span id="1548" class="l"><a href="#1548">1548: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$name</span> === <span class="php-quote">'DT_RowId'</span> ) {
</span><span id="1549" class="l"><a href="#1549">1549: </a>                <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_pkey[<span class="php-num">0</span>];
</span><span id="1550" class="l"><a href="#1550">1550: </a>            }
</span><span id="1551" class="l"><a href="#1551">1551: </a>
</span><span id="1552" class="l"><a href="#1552">1552: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">'Unknown field: '</span>.<span class="php-var">$name</span> .<span class="php-quote">' (index '</span>.<span class="php-var">$index</span>.<span class="php-quote">')'</span>);
</span><span id="1553" class="l"><a href="#1553">1553: </a>        }
</span><span id="1554" class="l"><a href="#1554">1554: </a>
</span><span id="1555" class="l"><a href="#1555">1555: </a>        <span class="php-keyword1">return</span> <span class="php-var">$field</span>-&gt;dbField();
</span><span id="1556" class="l"><a href="#1556">1556: </a>    }
</span><span id="1557" class="l"><a href="#1557">1557: </a>
</span><span id="1558" class="l"><a href="#1558">1558: </a>
</span><span id="1559" class="l"><a href="#1559">1559: </a>    <span class="php-comment">/**
</span></span><span id="1560" class="l"><a href="#1560">1560: </a><span class="php-comment">     * Sorting requirements to a server-side processing query.
</span></span><span id="1561" class="l"><a href="#1561">1561: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply sorting to
</span></span><span id="1562" class="l"><a href="#1562">1562: </a><span class="php-comment">     *  @param array $http HTTP variables (i.e. GET or POST)
</span></span><span id="1563" class="l"><a href="#1563">1563: </a><span class="php-comment">     *  @private
</span></span><span id="1564" class="l"><a href="#1564">1564: </a><span class="php-comment">     */</span>
</span><span id="1565" class="l"><a href="#1565">1565: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _ssp_sort ( <span class="php-var">$query</span>, <span class="php-var">$http</span> )
</span><span id="1566" class="l"><a href="#1566">1566: </a>    {
</span><span id="1567" class="l"><a href="#1567">1567: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$http</span>[<span class="php-quote">'order'</span>]) ; <span class="php-var">$i</span>++ ) {
</span><span id="1568" class="l"><a href="#1568">1568: </a>            <span class="php-var">$order</span> = <span class="php-var">$http</span>[<span class="php-quote">'order'</span>][<span class="php-var">$i</span>];
</span><span id="1569" class="l"><a href="#1569">1569: </a>
</span><span id="1570" class="l"><a href="#1570">1570: </a>            <span class="php-var">$query</span>-&gt;order(
</span><span id="1571" class="l"><a href="#1571">1571: </a>                <span class="php-var">$this</span>-&gt;_ssp_field( <span class="php-var">$http</span>, <span class="php-var">$order</span>[<span class="php-quote">'column'</span>] ) .<span class="php-quote">' '</span>.
</span><span id="1572" class="l"><a href="#1572">1572: </a>                (<span class="php-var">$order</span>[<span class="php-quote">'dir'</span>]===<span class="php-quote">'asc'</span> ? <span class="php-quote">'asc'</span> : <span class="php-quote">'desc'</span>)
</span><span id="1573" class="l"><a href="#1573">1573: </a>            );
</span><span id="1574" class="l"><a href="#1574">1574: </a>        }
</span><span id="1575" class="l"><a href="#1575">1575: </a>    }
</span><span id="1576" class="l"><a href="#1576">1576: </a>
</span><span id="1577" class="l"><a href="#1577">1577: </a>
</span><span id="1578" class="l"><a href="#1578">1578: </a>    <span class="php-comment">/**
</span></span><span id="1579" class="l"><a href="#1579">1579: </a><span class="php-comment">     * Add DataTables' 'where' condition to a server-side processing query. This
</span></span><span id="1580" class="l"><a href="#1580">1580: </a><span class="php-comment">     * works for both global and individual column filtering.
</span></span><span id="1581" class="l"><a href="#1581">1581: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the WHERE conditions to
</span></span><span id="1582" class="l"><a href="#1582">1582: </a><span class="php-comment">     *  @param array $http HTTP variables (i.e. GET or POST)
</span></span><span id="1583" class="l"><a href="#1583">1583: </a><span class="php-comment">     *  @private
</span></span><span id="1584" class="l"><a href="#1584">1584: </a><span class="php-comment">     */</span>
</span><span id="1585" class="l"><a href="#1585">1585: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _ssp_filter ( <span class="php-var">$query</span>, <span class="php-var">$http</span> )
</span><span id="1586" class="l"><a href="#1586">1586: </a>    {
</span><span id="1587" class="l"><a href="#1587">1587: </a>        <span class="php-var">$that</span> = <span class="php-var">$this</span>;
</span><span id="1588" class="l"><a href="#1588">1588: </a>
</span><span id="1589" class="l"><a href="#1589">1589: </a>        <span class="php-comment">// Global filter</span>
</span><span id="1590" class="l"><a href="#1590">1590: </a>        <span class="php-var">$fields</span> = <span class="php-var">$this</span>-&gt;_fields;
</span><span id="1591" class="l"><a href="#1591">1591: </a>
</span><span id="1592" class="l"><a href="#1592">1592: </a>        <span class="php-comment">// Global search, add a ( ... or ... ) set of filters for each column</span>
</span><span id="1593" class="l"><a href="#1593">1593: </a>        <span class="php-comment">// in the table (not the fields, just the columns submitted)</span>
</span><span id="1594" class="l"><a href="#1594">1594: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$http</span>[<span class="php-quote">'search'</span>][<span class="php-quote">'value'</span>] ) {
</span><span id="1595" class="l"><a href="#1595">1595: </a>            <span class="php-var">$query</span>-&gt;where( <span class="php-keyword1">function</span> (<span class="php-var">$q</span>) <span class="php-keyword1">use</span> (&amp;<span class="php-var">$that</span>, &amp;<span class="php-var">$fields</span>, <span class="php-var">$http</span>) {
</span><span id="1596" class="l"><a href="#1596">1596: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$http</span>[<span class="php-quote">'columns'</span>]) ; <span class="php-var">$i</span>++ ) {
</span><span id="1597" class="l"><a href="#1597">1597: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$http</span>[<span class="php-quote">'columns'</span>][<span class="php-var">$i</span>][<span class="php-quote">'searchable'</span>] == <span class="php-quote">'true'</span> ) {
</span><span id="1598" class="l"><a href="#1598">1598: </a>                        <span class="php-var">$field</span> = <span class="php-var">$that</span>-&gt;_ssp_field( <span class="php-var">$http</span>, <span class="php-var">$i</span> );
</span><span id="1599" class="l"><a href="#1599">1599: </a>
</span><span id="1600" class="l"><a href="#1600">1600: </a>                        <span class="php-keyword1">if</span> ( <span class="php-var">$field</span> ) {
</span><span id="1601" class="l"><a href="#1601">1601: </a>                            <span class="php-var">$q</span>-&gt;or_where( <span class="php-var">$field</span>, <span class="php-quote">'%'</span>.<span class="php-var">$http</span>[<span class="php-quote">'search'</span>][<span class="php-quote">'value'</span>].<span class="php-quote">'%'</span>, <span class="php-quote">'like'</span> );
</span><span id="1602" class="l"><a href="#1602">1602: </a>                        }
</span><span id="1603" class="l"><a href="#1603">1603: </a>                    }
</span><span id="1604" class="l"><a href="#1604">1604: </a>                }
</span><span id="1605" class="l"><a href="#1605">1605: </a>            } );
</span><span id="1606" class="l"><a href="#1606">1606: </a>        }
</span><span id="1607" class="l"><a href="#1607">1607: </a>
</span><span id="1608" class="l"><a href="#1608">1608: </a>        <span class="php-comment">// if ( $http['search']['value'] ) {</span>
</span><span id="1609" class="l"><a href="#1609">1609: </a>        <span class="php-comment">//  $words = explode(&quot; &quot;, $http['search']['value']);</span>
</span><span id="1610" class="l"><a href="#1610">1610: </a>
</span><span id="1611" class="l"><a href="#1611">1611: </a>        <span class="php-comment">//  $query-&gt;where( function ($q) use (&amp;$that, &amp;$fields, $http, $words) {</span>
</span><span id="1612" class="l"><a href="#1612">1612: </a>        <span class="php-comment">//      for ( $j=0, $jen=count($words) ; $j&lt;$jen ; $j++ ) {</span>
</span><span id="1613" class="l"><a href="#1613">1613: </a>        <span class="php-comment">//          if ( $words[$j] ) {</span>
</span><span id="1614" class="l"><a href="#1614">1614: </a>        <span class="php-comment">//              $q-&gt;where_group( true );</span>
</span><span id="1615" class="l"><a href="#1615">1615: </a>
</span><span id="1616" class="l"><a href="#1616">1616: </a>        <span class="php-comment">//              for ( $i=0, $ien=count($http['columns']) ; $i&lt;$ien ; $i++ ) {</span>
</span><span id="1617" class="l"><a href="#1617">1617: </a>        <span class="php-comment">//                  if ( $http['columns'][$i]['searchable'] == 'true' ) {</span>
</span><span id="1618" class="l"><a href="#1618">1618: </a>        <span class="php-comment">//                      $field = $that-&gt;_ssp_field( $http, $i );</span>
</span><span id="1619" class="l"><a href="#1619">1619: </a>
</span><span id="1620" class="l"><a href="#1620">1620: </a>        <span class="php-comment">//                      $q-&gt;or_where( $field, $words[$j].'%', 'like' );</span>
</span><span id="1621" class="l"><a href="#1621">1621: </a>        <span class="php-comment">//                      $q-&gt;or_where( $field, '% '.$words[$j].'%', 'like' );</span>
</span><span id="1622" class="l"><a href="#1622">1622: </a>        <span class="php-comment">//                  }</span>
</span><span id="1623" class="l"><a href="#1623">1623: </a>        <span class="php-comment">//              }</span>
</span><span id="1624" class="l"><a href="#1624">1624: </a>
</span><span id="1625" class="l"><a href="#1625">1625: </a>        <span class="php-comment">//              $q-&gt;where_group( false );</span>
</span><span id="1626" class="l"><a href="#1626">1626: </a>        <span class="php-comment">//          }</span>
</span><span id="1627" class="l"><a href="#1627">1627: </a>        <span class="php-comment">//      }</span>
</span><span id="1628" class="l"><a href="#1628">1628: </a>        <span class="php-comment">//  } );</span>
</span><span id="1629" class="l"><a href="#1629">1629: </a>        <span class="php-comment">// }</span>
</span><span id="1630" class="l"><a href="#1630">1630: </a>
</span><span id="1631" class="l"><a href="#1631">1631: </a>        <span class="php-comment">// Column filters</span>
</span><span id="1632" class="l"><a href="#1632">1632: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$http</span>[<span class="php-quote">'columns'</span>]) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1633" class="l"><a href="#1633">1633: </a>            <span class="php-var">$column</span> = <span class="php-var">$http</span>[<span class="php-quote">'columns'</span>][<span class="php-var">$i</span>];
</span><span id="1634" class="l"><a href="#1634">1634: </a>            <span class="php-var">$search</span> = <span class="php-var">$column</span>[<span class="php-quote">'search'</span>][<span class="php-quote">'value'</span>];
</span><span id="1635" class="l"><a href="#1635">1635: </a>
</span><span id="1636" class="l"><a href="#1636">1636: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$search</span> !== <span class="php-quote">''</span> &amp;&amp; <span class="php-var">$column</span>[<span class="php-quote">'searchable'</span>] == <span class="php-quote">'true'</span> ) {
</span><span id="1637" class="l"><a href="#1637">1637: </a>                <span class="php-var">$query</span>-&gt;where( <span class="php-var">$this</span>-&gt;_ssp_field( <span class="php-var">$http</span>, <span class="php-var">$i</span> ), <span class="php-quote">'%'</span>.<span class="php-var">$search</span>.<span class="php-quote">'%'</span>, <span class="php-quote">'like'</span> );
</span><span id="1638" class="l"><a href="#1638">1638: </a>            }
</span><span id="1639" class="l"><a href="#1639">1639: </a>        }
</span><span id="1640" class="l"><a href="#1640">1640: </a>    }
</span><span id="1641" class="l"><a href="#1641">1641: </a>
</span><span id="1642" class="l"><a href="#1642">1642: </a>
</span><span id="1643" class="l"><a href="#1643">1643: </a>    <span class="php-comment">/**
</span></span><span id="1644" class="l"><a href="#1644">1644: </a><span class="php-comment">     * Add a limit / offset to a server-side processing query
</span></span><span id="1645" class="l"><a href="#1645">1645: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the offset / limit to
</span></span><span id="1646" class="l"><a href="#1646">1646: </a><span class="php-comment">     *  @param array $http HTTP variables (i.e. GET or POST)
</span></span><span id="1647" class="l"><a href="#1647">1647: </a><span class="php-comment">     *  @private
</span></span><span id="1648" class="l"><a href="#1648">1648: </a><span class="php-comment">     */</span>
</span><span id="1649" class="l"><a href="#1649">1649: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _ssp_limit ( <span class="php-var">$query</span>, <span class="php-var">$http</span> )
</span><span id="1650" class="l"><a href="#1650">1650: </a>    {
</span><span id="1651" class="l"><a href="#1651">1651: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$http</span>[<span class="php-quote">'length'</span>] != -<span class="php-num">1</span> ) { <span class="php-comment">// -1 is 'show all' in DataTables</span>
</span><span id="1652" class="l"><a href="#1652">1652: </a>            <span class="php-var">$query</span>
</span><span id="1653" class="l"><a href="#1653">1653: </a>                -&gt;offset( <span class="php-var">$http</span>[<span class="php-quote">'start'</span>] )
</span><span id="1654" class="l"><a href="#1654">1654: </a>                -&gt;limit( <span class="php-var">$http</span>[<span class="php-quote">'length'</span>] );
</span><span id="1655" class="l"><a href="#1655">1655: </a>        }
</span><span id="1656" class="l"><a href="#1656">1656: </a>    }
</span><span id="1657" class="l"><a href="#1657">1657: </a>
</span><span id="1658" class="l"><a href="#1658">1658: </a>
</span><span id="1659" class="l"><a href="#1659">1659: </a>    <span class="php-comment">/*  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *  *
</span></span><span id="1660" class="l"><a href="#1660">1660: </a><span class="php-comment">     * Internal helper methods
</span></span><span id="1661" class="l"><a href="#1661">1661: </a><span class="php-comment">     */</span>
</span><span id="1662" class="l"><a href="#1662">1662: </a>
</span><span id="1663" class="l"><a href="#1663">1663: </a>    <span class="php-comment">/**
</span></span><span id="1664" class="l"><a href="#1664">1664: </a><span class="php-comment">     * Add left join commands for the instance to a query.
</span></span><span id="1665" class="l"><a href="#1665">1665: </a><span class="php-comment">     *
</span></span><span id="1666" class="l"><a href="#1666">1666: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the joins to
</span></span><span id="1667" class="l"><a href="#1667">1667: </a><span class="php-comment">     *  @private
</span></span><span id="1668" class="l"><a href="#1668">1668: </a><span class="php-comment">     */</span>
</span><span id="1669" class="l"><a href="#1669">1669: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _perform_left_join ( <span class="php-var">$query</span> )
</span><span id="1670" class="l"><a href="#1670">1670: </a>    {
</span><span id="1671" class="l"><a href="#1671">1671: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_leftJoin) ) {
</span><span id="1672" class="l"><a href="#1672">1672: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_leftJoin) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1673" class="l"><a href="#1673">1673: </a>                <span class="php-var">$join</span> = <span class="php-var">$this</span>-&gt;_leftJoin[<span class="php-var">$i</span>];
</span><span id="1674" class="l"><a href="#1674">1674: </a>
</span><span id="1675" class="l"><a href="#1675">1675: </a>                <span class="php-var">$query</span>-&gt;<span class="php-keyword2">join</span>( <span class="php-var">$join</span>[<span class="php-quote">'table'</span>], <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>].<span class="php-quote">' '</span>.<span class="php-var">$join</span>[<span class="php-quote">'operator'</span>].<span class="php-quote">' '</span>.<span class="php-var">$join</span>[<span class="php-quote">'field2'</span>], <span class="php-quote">'LEFT'</span> );
</span><span id="1676" class="l"><a href="#1676">1676: </a>            }
</span><span id="1677" class="l"><a href="#1677">1677: </a>        }
</span><span id="1678" class="l"><a href="#1678">1678: </a>    }
</span><span id="1679" class="l"><a href="#1679">1679: </a>
</span><span id="1680" class="l"><a href="#1680">1680: </a>
</span><span id="1681" class="l"><a href="#1681">1681: </a>    <span class="php-comment">/**
</span></span><span id="1682" class="l"><a href="#1682">1682: </a><span class="php-comment">     * Add local WHERE condition to query
</span></span><span id="1683" class="l"><a href="#1683">1683: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the WHERE conditions to
</span></span><span id="1684" class="l"><a href="#1684">1684: </a><span class="php-comment">     *  @private
</span></span><span id="1685" class="l"><a href="#1685">1685: </a><span class="php-comment">     */</span>
</span><span id="1686" class="l"><a href="#1686">1686: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _get_where ( <span class="php-var">$query</span> )
</span><span id="1687" class="l"><a href="#1687">1687: </a>    {
</span><span id="1688" class="l"><a href="#1688">1688: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$i</span>++ ) {
</span><span id="1689" class="l"><a href="#1689">1689: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>] ) ) {
</span><span id="1690" class="l"><a href="#1690">1690: </a>                <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>]( <span class="php-var">$query</span> );
</span><span id="1691" class="l"><a href="#1691">1691: </a>            }
</span><span id="1692" class="l"><a href="#1692">1692: </a>            <span class="php-keyword1">else</span> {
</span><span id="1693" class="l"><a href="#1693">1693: </a>                <span class="php-var">$query</span>-&gt;where(
</span><span id="1694" class="l"><a href="#1694">1694: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'key'</span>],
</span><span id="1695" class="l"><a href="#1695">1695: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'value'</span>],
</span><span id="1696" class="l"><a href="#1696">1696: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'op'</span>]
</span><span id="1697" class="l"><a href="#1697">1697: </a>                );
</span><span id="1698" class="l"><a href="#1698">1698: </a>            }
</span><span id="1699" class="l"><a href="#1699">1699: </a>        }
</span><span id="1700" class="l"><a href="#1700">1700: </a>    }
</span><span id="1701" class="l"><a href="#1701">1701: </a>
</span><span id="1702" class="l"><a href="#1702">1702: </a>
</span><span id="1703" class="l"><a href="#1703">1703: </a>    <span class="php-comment">/**
</span></span><span id="1704" class="l"><a href="#1704">1704: </a><span class="php-comment">     * Get a field instance from a known field name
</span></span><span id="1705" class="l"><a href="#1705">1705: </a><span class="php-comment">     *
</span></span><span id="1706" class="l"><a href="#1706">1706: </a><span class="php-comment">     *  @param string $name Field name
</span></span><span id="1707" class="l"><a href="#1707">1707: </a><span class="php-comment">     *  @param string $type Matching name type
</span></span><span id="1708" class="l"><a href="#1708">1708: </a><span class="php-comment">     *  @return Field Field instance
</span></span><span id="1709" class="l"><a href="#1709">1709: </a><span class="php-comment">     *  @private
</span></span><span id="1710" class="l"><a href="#1710">1710: </a><span class="php-comment">     */</span>
</span><span id="1711" class="l"><a href="#1711">1711: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _find_field ( <span class="php-var">$name</span>, <span class="php-var">$type</span> )
</span><span id="1712" class="l"><a href="#1712">1712: </a>    {
</span><span id="1713" class="l"><a href="#1713">1713: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1714" class="l"><a href="#1714">1714: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[ <span class="php-var">$i</span> ];
</span><span id="1715" class="l"><a href="#1715">1715: </a>
</span><span id="1716" class="l"><a href="#1716">1716: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$type</span> === <span class="php-quote">'name'</span> &amp;&amp; <span class="php-var">$field</span>-&gt;name() === <span class="php-var">$name</span> ) {
</span><span id="1717" class="l"><a href="#1717">1717: </a>                <span class="php-keyword1">return</span> <span class="php-var">$field</span>;
</span><span id="1718" class="l"><a href="#1718">1718: </a>            }
</span><span id="1719" class="l"><a href="#1719">1719: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$type</span> === <span class="php-quote">'db'</span> &amp;&amp; <span class="php-var">$field</span>-&gt;dbField() === <span class="php-var">$name</span> ) {
</span><span id="1720" class="l"><a href="#1720">1720: </a>                <span class="php-keyword1">return</span> <span class="php-var">$field</span>;
</span><span id="1721" class="l"><a href="#1721">1721: </a>            }
</span><span id="1722" class="l"><a href="#1722">1722: </a>        }
</span><span id="1723" class="l"><a href="#1723">1723: </a>
</span><span id="1724" class="l"><a href="#1724">1724: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="1725" class="l"><a href="#1725">1725: </a>    }
</span><span id="1726" class="l"><a href="#1726">1726: </a>
</span><span id="1727" class="l"><a href="#1727">1727: </a>
</span><span id="1728" class="l"><a href="#1728">1728: </a>    <span class="php-comment">/**
</span></span><span id="1729" class="l"><a href="#1729">1729: </a><span class="php-comment">     * Insert or update a row for all main tables and left joined tables.
</span></span><span id="1730" class="l"><a href="#1730">1730: </a><span class="php-comment">     *
</span></span><span id="1731" class="l"><a href="#1731">1731: </a><span class="php-comment">     *  @param int|string $id ID to use to condition the update. If null is
</span></span><span id="1732" class="l"><a href="#1732">1732: </a><span class="php-comment">     *      given, the first query performed is an insert and the inserted id
</span></span><span id="1733" class="l"><a href="#1733">1733: </a><span class="php-comment">     *      used as the value should there be any subsequent tables to operate
</span></span><span id="1734" class="l"><a href="#1734">1734: </a><span class="php-comment">     *      on. Mote that for compound keys, this should be the &quot;joined&quot; value
</span></span><span id="1735" class="l"><a href="#1735">1735: </a><span class="php-comment">     *      (i.e. a single string rather than an array).
</span></span><span id="1736" class="l"><a href="#1736">1736: </a><span class="php-comment">     *  @return \DataTables\Database\Result Result from the query or null if no
</span></span><span id="1737" class="l"><a href="#1737">1737: </a><span class="php-comment">     *      query performed.
</span></span><span id="1738" class="l"><a href="#1738">1738: </a><span class="php-comment">     *  @private
</span></span><span id="1739" class="l"><a href="#1739">1739: </a><span class="php-comment">     */</span>
</span><span id="1740" class="l"><a href="#1740">1740: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _insert_or_update ( <span class="php-var">$id</span>, <span class="php-var">$values</span> )
</span><span id="1741" class="l"><a href="#1741">1741: </a>    {
</span><span id="1742" class="l"><a href="#1742">1742: </a>        <span class="php-comment">// Loop over all tables in _table, doing the insert or update as needed</span>
</span><span id="1743" class="l"><a href="#1743">1743: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_table ) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1744" class="l"><a href="#1744">1744: </a>            <span class="php-var">$res</span> = <span class="php-var">$this</span>-&gt;_insert_or_update_table(
</span><span id="1745" class="l"><a href="#1745">1745: </a>                <span class="php-var">$this</span>-&gt;_table[<span class="php-var">$i</span>],
</span><span id="1746" class="l"><a href="#1746">1746: </a>                <span class="php-var">$values</span>,
</span><span id="1747" class="l"><a href="#1747">1747: </a>                <span class="php-var">$id</span> !== <span class="php-keyword1">null</span> ?
</span><span id="1748" class="l"><a href="#1748">1748: </a>                    <span class="php-var">$this</span>-&gt;pkeyToArray( <span class="php-var">$id</span>, <span class="php-keyword1">true</span> ) :
</span><span id="1749" class="l"><a href="#1749">1749: </a>                    <span class="php-keyword1">null</span>
</span><span id="1750" class="l"><a href="#1750">1750: </a>            );
</span><span id="1751" class="l"><a href="#1751">1751: </a>
</span><span id="1752" class="l"><a href="#1752">1752: </a>            <span class="php-comment">// If we don't have an id yet, then the first insert will return</span>
</span><span id="1753" class="l"><a href="#1753">1753: </a>            <span class="php-comment">// the id we want</span>
</span><span id="1754" class="l"><a href="#1754">1754: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> !== <span class="php-keyword1">null</span> &amp;&amp; <span class="php-var">$id</span> === <span class="php-keyword1">null</span> ) {
</span><span id="1755" class="l"><a href="#1755">1755: </a>                <span class="php-var">$id</span> = <span class="php-var">$res</span>-&gt;insertId();
</span><span id="1756" class="l"><a href="#1756">1756: </a>            }
</span><span id="1757" class="l"><a href="#1757">1757: </a>        }
</span><span id="1758" class="l"><a href="#1758">1758: </a>
</span><span id="1759" class="l"><a href="#1759">1759: </a>        <span class="php-comment">// And for the left join tables as well</span>
</span><span id="1760" class="l"><a href="#1760">1760: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_leftJoin ) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1761" class="l"><a href="#1761">1761: </a>            <span class="php-var">$join</span> = <span class="php-var">$this</span>-&gt;_leftJoin[<span class="php-var">$i</span>];
</span><span id="1762" class="l"><a href="#1762">1762: </a>
</span><span id="1763" class="l"><a href="#1763">1763: </a>            <span class="php-comment">// which side of the join refers to the parent table?</span>
</span><span id="1764" class="l"><a href="#1764">1764: </a>            <span class="php-var">$joinTable</span> = <span class="php-var">$this</span>-&gt;_alias( <span class="php-var">$join</span>[<span class="php-quote">'table'</span>], <span class="php-quote">'alias'</span> );
</span><span id="1765" class="l"><a href="#1765">1765: </a>            <span class="php-var">$tablePart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>] );
</span><span id="1766" class="l"><a href="#1766">1766: </a>
</span><span id="1767" class="l"><a href="#1767">1767: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>], <span class="php-quote">'db'</span> ) ) {
</span><span id="1768" class="l"><a href="#1768">1768: </a>                <span class="php-var">$tablePart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>], <span class="php-quote">'db'</span> ).<span class="php-quote">'.'</span>.<span class="php-var">$tablePart</span>;
</span><span id="1769" class="l"><a href="#1769">1769: </a>            }
</span><span id="1770" class="l"><a href="#1770">1770: </a>
</span><span id="1771" class="l"><a href="#1771">1771: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$tablePart</span> === <span class="php-var">$joinTable</span> ) {
</span><span id="1772" class="l"><a href="#1772">1772: </a>                <span class="php-var">$parentLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field2'</span>];
</span><span id="1773" class="l"><a href="#1773">1773: </a>                <span class="php-var">$childLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>];
</span><span id="1774" class="l"><a href="#1774">1774: </a>            }
</span><span id="1775" class="l"><a href="#1775">1775: </a>            <span class="php-keyword1">else</span> {
</span><span id="1776" class="l"><a href="#1776">1776: </a>                <span class="php-var">$parentLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field1'</span>];
</span><span id="1777" class="l"><a href="#1777">1777: </a>                <span class="php-var">$childLink</span> = <span class="php-var">$join</span>[<span class="php-quote">'field2'</span>];
</span><span id="1778" class="l"><a href="#1778">1778: </a>            }
</span><span id="1779" class="l"><a href="#1779">1779: </a>
</span><span id="1780" class="l"><a href="#1780">1780: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$parentLink</span> === <span class="php-var">$this</span>-&gt;_pkey[<span class="php-num">0</span>] &amp;&amp; <span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_pkey) === <span class="php-num">1</span> ) {
</span><span id="1781" class="l"><a href="#1781">1781: </a>                <span class="php-var">$whereVal</span> = <span class="php-var">$id</span>;
</span><span id="1782" class="l"><a href="#1782">1782: </a>            }
</span><span id="1783" class="l"><a href="#1783">1783: </a>            <span class="php-keyword1">else</span> {
</span><span id="1784" class="l"><a href="#1784">1784: </a>                <span class="php-comment">// We need submitted information about the joined data to be</span>
</span><span id="1785" class="l"><a href="#1785">1785: </a>                <span class="php-comment">// submitted as well as the new value. We first check if the</span>
</span><span id="1786" class="l"><a href="#1786">1786: </a>                <span class="php-comment">// host field was submitted</span>
</span><span id="1787" class="l"><a href="#1787">1787: </a>                <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$parentLink</span>, <span class="php-quote">'db'</span> );
</span><span id="1788" class="l"><a href="#1788">1788: </a>
</span><span id="1789" class="l"><a href="#1789">1789: </a>                <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> || ! <span class="php-var">$field</span>-&gt;apply( <span class="php-quote">'set'</span>, <span class="php-var">$values</span> ) ) {
</span><span id="1790" class="l"><a href="#1790">1790: </a>                    <span class="php-comment">// If not, then check if the child id was submitted</span>
</span><span id="1791" class="l"><a href="#1791">1791: </a>                    <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$childLink</span>, <span class="php-quote">'db'</span> );
</span><span id="1792" class="l"><a href="#1792">1792: </a>
</span><span id="1793" class="l"><a href="#1793">1793: </a>                    <span class="php-comment">// No data available, so we can't do anything</span>
</span><span id="1794" class="l"><a href="#1794">1794: </a>                    <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> || ! <span class="php-var">$field</span>-&gt;apply( <span class="php-quote">'set'</span>, <span class="php-var">$values</span> ) ) {
</span><span id="1795" class="l"><a href="#1795">1795: </a>                        <span class="php-keyword1">continue</span>;
</span><span id="1796" class="l"><a href="#1796">1796: </a>                    }
</span><span id="1797" class="l"><a href="#1797">1797: </a>                }
</span><span id="1798" class="l"><a href="#1798">1798: </a>
</span><span id="1799" class="l"><a href="#1799">1799: </a>                <span class="php-var">$whereVal</span> = <span class="php-var">$field</span>-&gt;val(<span class="php-quote">'set'</span>, <span class="php-var">$values</span>);
</span><span id="1800" class="l"><a href="#1800">1800: </a>            }
</span><span id="1801" class="l"><a href="#1801">1801: </a>
</span><span id="1802" class="l"><a href="#1802">1802: </a>            <span class="php-var">$whereName</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$childLink</span>, <span class="php-quote">'field'</span> );
</span><span id="1803" class="l"><a href="#1803">1803: </a>
</span><span id="1804" class="l"><a href="#1804">1804: </a>            <span class="php-var">$this</span>-&gt;_insert_or_update_table(
</span><span id="1805" class="l"><a href="#1805">1805: </a>                <span class="php-var">$join</span>[<span class="php-quote">'table'</span>],
</span><span id="1806" class="l"><a href="#1806">1806: </a>                <span class="php-var">$values</span>,
</span><span id="1807" class="l"><a href="#1807">1807: </a>                <span class="php-keyword1">array</span>( <span class="php-var">$whereName</span> =&gt; <span class="php-var">$whereVal</span> )
</span><span id="1808" class="l"><a href="#1808">1808: </a>            );
</span><span id="1809" class="l"><a href="#1809">1809: </a>        }
</span><span id="1810" class="l"><a href="#1810">1810: </a>
</span><span id="1811" class="l"><a href="#1811">1811: </a>        <span class="php-keyword1">return</span> <span class="php-var">$id</span>;
</span><span id="1812" class="l"><a href="#1812">1812: </a>    }
</span><span id="1813" class="l"><a href="#1813">1813: </a>
</span><span id="1814" class="l"><a href="#1814">1814: </a>
</span><span id="1815" class="l"><a href="#1815">1815: </a>    <span class="php-comment">/**
</span></span><span id="1816" class="l"><a href="#1816">1816: </a><span class="php-comment">     * Insert or update a row in a single database table, based on the data
</span></span><span id="1817" class="l"><a href="#1817">1817: </a><span class="php-comment">     * given and the fields configured for the instance.
</span></span><span id="1818" class="l"><a href="#1818">1818: </a><span class="php-comment">     *
</span></span><span id="1819" class="l"><a href="#1819">1819: </a><span class="php-comment">     * The function will find the fields which are required for this specific
</span></span><span id="1820" class="l"><a href="#1820">1820: </a><span class="php-comment">     * table, based on the names of fields and use only the appropriate data for
</span></span><span id="1821" class="l"><a href="#1821">1821: </a><span class="php-comment">     * this table. Therefore the full submitted data set can be passed in.
</span></span><span id="1822" class="l"><a href="#1822">1822: </a><span class="php-comment">     *
</span></span><span id="1823" class="l"><a href="#1823">1823: </a><span class="php-comment">     *  @param string $table Database table name to use (can include an alias)
</span></span><span id="1824" class="l"><a href="#1824">1824: </a><span class="php-comment">     *  @param array $where Update condition
</span></span><span id="1825" class="l"><a href="#1825">1825: </a><span class="php-comment">     *  @return \DataTables\Database\Result Result from the query or null if no query
</span></span><span id="1826" class="l"><a href="#1826">1826: </a><span class="php-comment">     *      performed.
</span></span><span id="1827" class="l"><a href="#1827">1827: </a><span class="php-comment">     *  @throws \Exception Where set error
</span></span><span id="1828" class="l"><a href="#1828">1828: </a><span class="php-comment">     *  @private
</span></span><span id="1829" class="l"><a href="#1829">1829: </a><span class="php-comment">     */</span>
</span><span id="1830" class="l"><a href="#1830">1830: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _insert_or_update_table ( <span class="php-var">$table</span>, <span class="php-var">$values</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span> )
</span><span id="1831" class="l"><a href="#1831">1831: </a>    {
</span><span id="1832" class="l"><a href="#1832">1832: </a>        <span class="php-var">$set</span> = <span class="php-keyword1">array</span>();
</span><span id="1833" class="l"><a href="#1833">1833: </a>        <span class="php-var">$action</span> = (<span class="php-var">$where</span> === <span class="php-keyword1">null</span>) ? <span class="php-quote">'create'</span> : <span class="php-quote">'edit'</span>;
</span><span id="1834" class="l"><a href="#1834">1834: </a>        <span class="php-var">$tableAlias</span> = <span class="php-var">$this</span>-&gt;_alias( <span class="php-var">$table</span>, <span class="php-quote">'alias'</span> );
</span><span id="1835" class="l"><a href="#1835">1835: </a>
</span><span id="1836" class="l"><a href="#1836">1836: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="1837" class="l"><a href="#1837">1837: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="1838" class="l"><a href="#1838">1838: </a>            <span class="php-var">$tablePart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$field</span>-&gt;dbField() );
</span><span id="1839" class="l"><a href="#1839">1839: </a>
</span><span id="1840" class="l"><a href="#1840">1840: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-quote">'db'</span> ) ) {
</span><span id="1841" class="l"><a href="#1841">1841: </a>                <span class="php-var">$tablePart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-quote">'db'</span> ).<span class="php-quote">'.'</span>.<span class="php-var">$tablePart</span>;
</span><span id="1842" class="l"><a href="#1842">1842: </a>            }
</span><span id="1843" class="l"><a href="#1843">1843: </a>
</span><span id="1844" class="l"><a href="#1844">1844: </a>            <span class="php-comment">// Does this field apply to this table (only check when a join is</span>
</span><span id="1845" class="l"><a href="#1845">1845: </a>            <span class="php-comment">// being used)</span>
</span><span id="1846" class="l"><a href="#1846">1846: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_leftJoin) &amp;&amp; <span class="php-var">$tablePart</span> !== <span class="php-var">$tableAlias</span> ) {
</span><span id="1847" class="l"><a href="#1847">1847: </a>                <span class="php-keyword1">continue</span>;
</span><span id="1848" class="l"><a href="#1848">1848: </a>            }
</span><span id="1849" class="l"><a href="#1849">1849: </a>
</span><span id="1850" class="l"><a href="#1850">1850: </a>            <span class="php-comment">// Check if this field should be set, based on options and</span>
</span><span id="1851" class="l"><a href="#1851">1851: </a>            <span class="php-comment">// submitted data</span>
</span><span id="1852" class="l"><a href="#1852">1852: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span>-&gt;apply( <span class="php-var">$action</span>, <span class="php-var">$values</span> ) ) {
</span><span id="1853" class="l"><a href="#1853">1853: </a>                <span class="php-keyword1">continue</span>;
</span><span id="1854" class="l"><a href="#1854">1854: </a>            }
</span><span id="1855" class="l"><a href="#1855">1855: </a>
</span><span id="1856" class="l"><a href="#1856">1856: </a>            <span class="php-comment">// Some db's (specifically postgres) don't like having the table</span>
</span><span id="1857" class="l"><a href="#1857">1857: </a>            <span class="php-comment">// name prefixing the column name. Todo: it might be nicer to have</span>
</span><span id="1858" class="l"><a href="#1858">1858: </a>            <span class="php-comment">// the db layer abstract this out?</span>
</span><span id="1859" class="l"><a href="#1859">1859: </a>            <span class="php-var">$fieldPart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-quote">'field'</span> );
</span><span id="1860" class="l"><a href="#1860">1860: </a>            <span class="php-var">$set</span>[ <span class="php-var">$fieldPart</span> ] = <span class="php-var">$field</span>-&gt;val( <span class="php-quote">'set'</span>, <span class="php-var">$values</span> );
</span><span id="1861" class="l"><a href="#1861">1861: </a>        }
</span><span id="1862" class="l"><a href="#1862">1862: </a>
</span><span id="1863" class="l"><a href="#1863">1863: </a>        <span class="php-comment">// Add where fields if setting where values and required for this</span>
</span><span id="1864" class="l"><a href="#1864">1864: </a>        <span class="php-comment">// table</span>
</span><span id="1865" class="l"><a href="#1865">1865: </a>        <span class="php-comment">// Note that `whereSet` is now deprecated</span>
</span><span id="1866" class="l"><a href="#1866">1866: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_whereSet ) {
</span><span id="1867" class="l"><a href="#1867">1867: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$j</span>=<span class="php-num">0</span>, <span class="php-var">$jen</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$j</span>&lt;<span class="php-var">$jen</span> ; <span class="php-var">$j</span>++ ) {
</span><span id="1868" class="l"><a href="#1868">1868: </a>                <span class="php-var">$cond</span> = <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$j</span>];
</span><span id="1869" class="l"><a href="#1869">1869: </a>
</span><span id="1870" class="l"><a href="#1870">1870: </a>                <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_callable</span>( <span class="php-var">$cond</span> ) ) {
</span><span id="1871" class="l"><a href="#1871">1871: </a>                    <span class="php-comment">// Make sure the value wasn't in the submitted data set,</span>
</span><span id="1872" class="l"><a href="#1872">1872: </a>                    <span class="php-comment">// otherwise we would be overwriting it</span>
</span><span id="1873" class="l"><a href="#1873">1873: </a>                    <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$set</span>[ <span class="php-var">$cond</span>[<span class="php-quote">'key'</span>] ] ) )
</span><span id="1874" class="l"><a href="#1874">1874: </a>                    {
</span><span id="1875" class="l"><a href="#1875">1875: </a>                        <span class="php-var">$whereTablePart</span> = <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$cond</span>[<span class="php-quote">'key'</span>], <span class="php-quote">'table'</span> );
</span><span id="1876" class="l"><a href="#1876">1876: </a>
</span><span id="1877" class="l"><a href="#1877">1877: </a>                        <span class="php-comment">// No table part on the where condition to match against</span>
</span><span id="1878" class="l"><a href="#1878">1878: </a>                        <span class="php-comment">// or table operating on matches table part from cond.</span>
</span><span id="1879" class="l"><a href="#1879">1879: </a>                        <span class="php-keyword1">if</span> ( ! <span class="php-var">$whereTablePart</span> || <span class="php-var">$tableAlias</span> == <span class="php-var">$whereTablePart</span> ) {
</span><span id="1880" class="l"><a href="#1880">1880: </a>                            <span class="php-var">$set</span>[ <span class="php-var">$cond</span>[<span class="php-quote">'key'</span>] ] = <span class="php-var">$cond</span>[<span class="php-quote">'value'</span>];
</span><span id="1881" class="l"><a href="#1881">1881: </a>                        }
</span><span id="1882" class="l"><a href="#1882">1882: </a>                    }
</span><span id="1883" class="l"><a href="#1883">1883: </a>                    <span class="php-keyword1">else</span> {
</span><span id="1884" class="l"><a href="#1884">1884: </a>                        <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception( <span class="php-quote">'Where condition used as a setter, '</span>.
</span><span id="1885" class="l"><a href="#1885">1885: </a>                            <span class="php-quote">'but value submitted for field: '</span>.<span class="php-var">$cond</span>[<span class="php-quote">'key'</span>]
</span><span id="1886" class="l"><a href="#1886">1886: </a>                        );
</span><span id="1887" class="l"><a href="#1887">1887: </a>                    }
</span><span id="1888" class="l"><a href="#1888">1888: </a>                }
</span><span id="1889" class="l"><a href="#1889">1889: </a>            }
</span><span id="1890" class="l"><a href="#1890">1890: </a>        }
</span><span id="1891" class="l"><a href="#1891">1891: </a>
</span><span id="1892" class="l"><a href="#1892">1892: </a>        <span class="php-comment">// If nothing to do, then do nothing!</span>
</span><span id="1893" class="l"><a href="#1893">1893: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">count</span>( <span class="php-var">$set</span> ) ) {
</span><span id="1894" class="l"><a href="#1894">1894: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">null</span>;
</span><span id="1895" class="l"><a href="#1895">1895: </a>        }
</span><span id="1896" class="l"><a href="#1896">1896: </a>
</span><span id="1897" class="l"><a href="#1897">1897: </a>        <span class="php-comment">// Use pkey only for the host table</span>
</span><span id="1898" class="l"><a href="#1898">1898: </a>        <span class="php-var">$pkey</span> = <span class="php-keyword2">in_array</span>( <span class="php-var">$table</span>, <span class="php-var">$this</span>-&gt;_table ) !== <span class="php-keyword1">false</span> ?
</span><span id="1899" class="l"><a href="#1899">1899: </a>            <span class="php-var">$this</span>-&gt;_pkey :
</span><span id="1900" class="l"><a href="#1900">1900: </a>            <span class="php-quote">''</span>;
</span><span id="1901" class="l"><a href="#1901">1901: </a>
</span><span id="1902" class="l"><a href="#1902">1902: </a>        <span class="php-comment">// Insert or update</span>
</span><span id="1903" class="l"><a href="#1903">1903: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$action</span> === <span class="php-quote">'create'</span> ) {
</span><span id="1904" class="l"><a href="#1904">1904: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_db-&gt;insert( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$pkey</span> );
</span><span id="1905" class="l"><a href="#1905">1905: </a>        }
</span><span id="1906" class="l"><a href="#1906">1906: </a>        <span class="php-keyword1">else</span> {
</span><span id="1907" class="l"><a href="#1907">1907: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_db-&gt;push( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$where</span>, <span class="php-var">$pkey</span> );
</span><span id="1908" class="l"><a href="#1908">1908: </a>        }
</span><span id="1909" class="l"><a href="#1909">1909: </a>    }
</span><span id="1910" class="l"><a href="#1910">1910: </a>
</span><span id="1911" class="l"><a href="#1911">1911: </a>
</span><span id="1912" class="l"><a href="#1912">1912: </a>    <span class="php-comment">/**
</span></span><span id="1913" class="l"><a href="#1913">1913: </a><span class="php-comment">     * Delete one or more rows from the database for an individual table
</span></span><span id="1914" class="l"><a href="#1914">1914: </a><span class="php-comment">     *
</span></span><span id="1915" class="l"><a href="#1915">1915: </a><span class="php-comment">     * @param string $table Database table name to use
</span></span><span id="1916" class="l"><a href="#1916">1916: </a><span class="php-comment">     * @param array $ids Array of ids to remove
</span></span><span id="1917" class="l"><a href="#1917">1917: </a><span class="php-comment">     * @param string $pkey Database column name to match the ids on for the
</span></span><span id="1918" class="l"><a href="#1918">1918: </a><span class="php-comment">     *   delete condition. If not given the instance's base primary key is
</span></span><span id="1919" class="l"><a href="#1919">1919: </a><span class="php-comment">     *   used.
</span></span><span id="1920" class="l"><a href="#1920">1920: </a><span class="php-comment">     * @private
</span></span><span id="1921" class="l"><a href="#1921">1921: </a><span class="php-comment">     */</span>
</span><span id="1922" class="l"><a href="#1922">1922: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _remove_table ( <span class="php-var">$table</span>, <span class="php-var">$ids</span>, <span class="php-var">$pkey</span>=<span class="php-keyword1">null</span> )
</span><span id="1923" class="l"><a href="#1923">1923: </a>    {
</span><span id="1924" class="l"><a href="#1924">1924: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$pkey</span> === <span class="php-keyword1">null</span> ) {
</span><span id="1925" class="l"><a href="#1925">1925: </a>            <span class="php-var">$pkey</span> = <span class="php-var">$this</span>-&gt;_pkey;
</span><span id="1926" class="l"><a href="#1926">1926: </a>        }
</span><span id="1927" class="l"><a href="#1927">1927: </a>
</span><span id="1928" class="l"><a href="#1928">1928: </a>        <span class="php-comment">// Check there is a field which has a set option for this table</span>
</span><span id="1929" class="l"><a href="#1929">1929: </a>        <span class="php-var">$count</span> = <span class="php-num">0</span>;
</span><span id="1930" class="l"><a href="#1930">1930: </a>
</span><span id="1931" class="l"><a href="#1931">1931: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="1932" class="l"><a href="#1932">1932: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> || (
</span><span id="1933" class="l"><a href="#1933">1933: </a>                    <span class="php-var">$this</span>-&gt;_part( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-quote">'table'</span> ) === <span class="php-var">$table</span> &amp;&amp;
</span><span id="1934" class="l"><a href="#1934">1934: </a>                    <span class="php-var">$field</span>-&gt;set() !== Field::SET_NONE
</span><span id="1935" class="l"><a href="#1935">1935: </a>                )
</span><span id="1936" class="l"><a href="#1936">1936: </a>            ) {
</span><span id="1937" class="l"><a href="#1937">1937: </a>                <span class="php-var">$count</span>++;
</span><span id="1938" class="l"><a href="#1938">1938: </a>            }
</span><span id="1939" class="l"><a href="#1939">1939: </a>        }
</span><span id="1940" class="l"><a href="#1940">1940: </a>
</span><span id="1941" class="l"><a href="#1941">1941: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$count</span> &gt; <span class="php-num">0</span> ) {
</span><span id="1942" class="l"><a href="#1942">1942: </a>            <span class="php-var">$q</span> = <span class="php-var">$this</span>-&gt;_db
</span><span id="1943" class="l"><a href="#1943">1943: </a>                -&gt;query( <span class="php-quote">'delete'</span> )
</span><span id="1944" class="l"><a href="#1944">1944: </a>                -&gt;table( <span class="php-var">$table</span> );
</span><span id="1945" class="l"><a href="#1945">1945: </a>
</span><span id="1946" class="l"><a href="#1946">1946: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$ids</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1947" class="l"><a href="#1947">1947: </a>                <span class="php-var">$cond</span> = <span class="php-var">$this</span>-&gt;pkeyToArray( <span class="php-var">$ids</span>[<span class="php-var">$i</span>], <span class="php-keyword1">true</span>, <span class="php-var">$pkey</span> );
</span><span id="1948" class="l"><a href="#1948">1948: </a>
</span><span id="1949" class="l"><a href="#1949">1949: </a>                <span class="php-var">$q</span>-&gt;or_where( <span class="php-keyword1">function</span> (<span class="php-var">$q2</span>) <span class="php-keyword1">use</span> (<span class="php-var">$cond</span>) {
</span><span id="1950" class="l"><a href="#1950">1950: </a>                    <span class="php-var">$q2</span>-&gt;where( <span class="php-var">$cond</span> );
</span><span id="1951" class="l"><a href="#1951">1951: </a>                } );
</span><span id="1952" class="l"><a href="#1952">1952: </a>            }
</span><span id="1953" class="l"><a href="#1953">1953: </a>
</span><span id="1954" class="l"><a href="#1954">1954: </a>            <span class="php-var">$q</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="1955" class="l"><a href="#1955">1955: </a>        }
</span><span id="1956" class="l"><a href="#1956">1956: </a>    }
</span><span id="1957" class="l"><a href="#1957">1957: </a>
</span><span id="1958" class="l"><a href="#1958">1958: </a>
</span><span id="1959" class="l"><a href="#1959">1959: </a>    <span class="php-comment">/**
</span></span><span id="1960" class="l"><a href="#1960">1960: </a><span class="php-comment">     * Check the validity of the set options if  we are doing a join, since
</span></span><span id="1961" class="l"><a href="#1961">1961: </a><span class="php-comment">     * there are some conditions for this state. Will throw an error if not
</span></span><span id="1962" class="l"><a href="#1962">1962: </a><span class="php-comment">     * valid.
</span></span><span id="1963" class="l"><a href="#1963">1963: </a><span class="php-comment">     *
</span></span><span id="1964" class="l"><a href="#1964">1964: </a><span class="php-comment">     *  @private
</span></span><span id="1965" class="l"><a href="#1965">1965: </a><span class="php-comment">     */</span>
</span><span id="1966" class="l"><a href="#1966">1966: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _prepJoin ()
</span><span id="1967" class="l"><a href="#1967">1967: </a>    {
</span><span id="1968" class="l"><a href="#1968">1968: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_leftJoin ) === <span class="php-num">0</span> ) {
</span><span id="1969" class="l"><a href="#1969">1969: </a>            <span class="php-keyword1">return</span>;
</span><span id="1970" class="l"><a href="#1970">1970: </a>        }
</span><span id="1971" class="l"><a href="#1971">1971: </a>
</span><span id="1972" class="l"><a href="#1972">1972: </a>        <span class="php-comment">// Check if the primary key has a table identifier - if not - add one</span>
</span><span id="1973" class="l"><a href="#1973">1973: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_pkey) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1974" class="l"><a href="#1974">1974: </a>            <span class="php-var">$val</span> = <span class="php-var">$this</span>-&gt;_pkey[<span class="php-var">$i</span>];
</span><span id="1975" class="l"><a href="#1975">1975: </a>
</span><span id="1976" class="l"><a href="#1976">1976: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$val</span>, <span class="php-quote">'.'</span> ) === <span class="php-keyword1">false</span> ) {
</span><span id="1977" class="l"><a href="#1977">1977: </a>                <span class="php-var">$this</span>-&gt;_pkey[<span class="php-var">$i</span>] = <span class="php-var">$this</span>-&gt;_alias( <span class="php-var">$this</span>-&gt;_table[<span class="php-num">0</span>], <span class="php-quote">'alias'</span> ).<span class="php-quote">'.'</span>.<span class="php-var">$val</span>;
</span><span id="1978" class="l"><a href="#1978">1978: </a>            }
</span><span id="1979" class="l"><a href="#1979">1979: </a>        }
</span><span id="1980" class="l"><a href="#1980">1980: </a>
</span><span id="1981" class="l"><a href="#1981">1981: </a>        <span class="php-comment">// Check that all fields have a table selector, otherwise, we'd need to</span>
</span><span id="1982" class="l"><a href="#1982">1982: </a>        <span class="php-comment">// know the structure of the tables, to know which fields belong in</span>
</span><span id="1983" class="l"><a href="#1983">1983: </a>        <span class="php-comment">// which. This extra requirement on the fields removes that</span>
</span><span id="1984" class="l"><a href="#1984">1984: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="1985" class="l"><a href="#1985">1985: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="1986" class="l"><a href="#1986">1986: </a>            <span class="php-var">$name</span> = <span class="php-var">$field</span>-&gt;dbField();
</span><span id="1987" class="l"><a href="#1987">1987: </a>
</span><span id="1988" class="l"><a href="#1988">1988: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$name</span>, <span class="php-quote">'.'</span> ) === <span class="php-keyword1">false</span> ) {
</span><span id="1989" class="l"><a href="#1989">1989: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception( <span class="php-quote">'Table part of the field &quot;'</span>.<span class="php-var">$name</span>.<span class="php-quote">'&quot; was not found. '</span>.
</span><span id="1990" class="l"><a href="#1990">1990: </a>                    <span class="php-quote">'In Editor instances that use a join, all fields must have the '</span>.
</span><span id="1991" class="l"><a href="#1991">1991: </a>                    <span class="php-quote">'database table set explicitly.'</span>
</span><span id="1992" class="l"><a href="#1992">1992: </a>                );
</span><span id="1993" class="l"><a href="#1993">1993: </a>            }
</span><span id="1994" class="l"><a href="#1994">1994: </a>        }
</span><span id="1995" class="l"><a href="#1995">1995: </a>    }
</span><span id="1996" class="l"><a href="#1996">1996: </a>
</span><span id="1997" class="l"><a href="#1997">1997: </a>
</span><span id="1998" class="l"><a href="#1998">1998: </a>    <span class="php-comment">/**
</span></span><span id="1999" class="l"><a href="#1999">1999: </a><span class="php-comment">     * Get one side or the other of an aliased SQL field name.
</span></span><span id="2000" class="l"><a href="#2000">2000: </a><span class="php-comment">     *
</span></span><span id="2001" class="l"><a href="#2001">2001: </a><span class="php-comment">     *  @param string $name SQL field
</span></span><span id="2002" class="l"><a href="#2002">2002: </a><span class="php-comment">     *  @param string $type Which part to get: `alias` (default) or `orig`.
</span></span><span id="2003" class="l"><a href="#2003">2003: </a><span class="php-comment">     *  @returns string Alias
</span></span><span id="2004" class="l"><a href="#2004">2004: </a><span class="php-comment">     *  @private
</span></span><span id="2005" class="l"><a href="#2005">2005: </a><span class="php-comment">     */</span>
</span><span id="2006" class="l"><a href="#2006">2006: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _alias ( <span class="php-var">$name</span>, <span class="php-var">$type</span>=<span class="php-quote">'alias'</span> )
</span><span id="2007" class="l"><a href="#2007">2007: </a>    {
</span><span id="2008" class="l"><a href="#2008">2008: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">stripos</span>( <span class="php-var">$name</span>, <span class="php-quote">' as '</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="2009" class="l"><a href="#2009">2009: </a>            <span class="php-var">$a</span> = <span class="php-keyword2">preg_split</span>( <span class="php-quote">'/ as /i'</span>, <span class="php-var">$name</span> );
</span><span id="2010" class="l"><a href="#2010">2010: </a>            <span class="php-keyword1">return</span> <span class="php-var">$type</span> === <span class="php-quote">'alias'</span> ?
</span><span id="2011" class="l"><a href="#2011">2011: </a>                <span class="php-var">$a</span>[<span class="php-num">1</span>] :
</span><span id="2012" class="l"><a href="#2012">2012: </a>                <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="2013" class="l"><a href="#2013">2013: </a>        }
</span><span id="2014" class="l"><a href="#2014">2014: </a>
</span><span id="2015" class="l"><a href="#2015">2015: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">stripos</span>( <span class="php-var">$name</span>, <span class="php-quote">' '</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="2016" class="l"><a href="#2016">2016: </a>            <span class="php-var">$a</span> = <span class="php-keyword2">preg_split</span>( <span class="php-quote">'/ /i'</span>, <span class="php-var">$name</span> );
</span><span id="2017" class="l"><a href="#2017">2017: </a>            <span class="php-keyword1">return</span> <span class="php-var">$type</span> === <span class="php-quote">'alias'</span> ?
</span><span id="2018" class="l"><a href="#2018">2018: </a>                <span class="php-var">$a</span>[<span class="php-num">1</span>] :
</span><span id="2019" class="l"><a href="#2019">2019: </a>                <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="2020" class="l"><a href="#2020">2020: </a>        }
</span><span id="2021" class="l"><a href="#2021">2021: </a>
</span><span id="2022" class="l"><a href="#2022">2022: </a>        <span class="php-keyword1">return</span> <span class="php-var">$name</span>;
</span><span id="2023" class="l"><a href="#2023">2023: </a>    }
</span><span id="2024" class="l"><a href="#2024">2024: </a>
</span><span id="2025" class="l"><a href="#2025">2025: </a>
</span><span id="2026" class="l"><a href="#2026">2026: </a>    <span class="php-comment">/**
</span></span><span id="2027" class="l"><a href="#2027">2027: </a><span class="php-comment">     * Get part of an SQL field definition regardless of how deeply defined it
</span></span><span id="2028" class="l"><a href="#2028">2028: </a><span class="php-comment">     * is
</span></span><span id="2029" class="l"><a href="#2029">2029: </a><span class="php-comment">     *
</span></span><span id="2030" class="l"><a href="#2030">2030: </a><span class="php-comment">     *  @param string $name SQL field
</span></span><span id="2031" class="l"><a href="#2031">2031: </a><span class="php-comment">     *  @param string $type Which part to get: `table` (default) or `db` or
</span></span><span id="2032" class="l"><a href="#2032">2032: </a><span class="php-comment">     *      `column`
</span></span><span id="2033" class="l"><a href="#2033">2033: </a><span class="php-comment">     *  @return string Part name
</span></span><span id="2034" class="l"><a href="#2034">2034: </a><span class="php-comment">     *  @private
</span></span><span id="2035" class="l"><a href="#2035">2035: </a><span class="php-comment">     */</span>
</span><span id="2036" class="l"><a href="#2036">2036: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _part ( <span class="php-var">$name</span>, <span class="php-var">$type</span>=<span class="php-quote">'table'</span> )
</span><span id="2037" class="l"><a href="#2037">2037: </a>    {
</span><span id="2038" class="l"><a href="#2038">2038: </a>        <span class="php-var">$db</span> = <span class="php-keyword1">null</span>;
</span><span id="2039" class="l"><a href="#2039">2039: </a>        <span class="php-var">$table</span> = <span class="php-keyword1">null</span>;
</span><span id="2040" class="l"><a href="#2040">2040: </a>        <span class="php-var">$column</span> = <span class="php-keyword1">null</span>;
</span><span id="2041" class="l"><a href="#2041">2041: </a>
</span><span id="2042" class="l"><a href="#2042">2042: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$name</span>, <span class="php-quote">'.'</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="2043" class="l"><a href="#2043">2043: </a>            <span class="php-var">$a</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$name</span> );
</span><span id="2044" class="l"><a href="#2044">2044: </a>
</span><span id="2045" class="l"><a href="#2045">2045: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$a</span>) === <span class="php-num">3</span> ) {
</span><span id="2046" class="l"><a href="#2046">2046: </a>                <span class="php-var">$db</span> = <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="2047" class="l"><a href="#2047">2047: </a>                <span class="php-var">$table</span> = <span class="php-var">$a</span>[<span class="php-num">1</span>];
</span><span id="2048" class="l"><a href="#2048">2048: </a>                <span class="php-var">$column</span> = <span class="php-var">$a</span>[<span class="php-num">2</span>];
</span><span id="2049" class="l"><a href="#2049">2049: </a>            }
</span><span id="2050" class="l"><a href="#2050">2050: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$a</span>) === <span class="php-num">2</span> ) {
</span><span id="2051" class="l"><a href="#2051">2051: </a>                <span class="php-var">$table</span> = <span class="php-var">$a</span>[<span class="php-num">0</span>];
</span><span id="2052" class="l"><a href="#2052">2052: </a>                <span class="php-var">$column</span> = <span class="php-var">$a</span>[<span class="php-num">1</span>];
</span><span id="2053" class="l"><a href="#2053">2053: </a>            }
</span><span id="2054" class="l"><a href="#2054">2054: </a>        }
</span><span id="2055" class="l"><a href="#2055">2055: </a>        <span class="php-keyword1">else</span> {
</span><span id="2056" class="l"><a href="#2056">2056: </a>            <span class="php-var">$column</span> = <span class="php-var">$name</span>;
</span><span id="2057" class="l"><a href="#2057">2057: </a>        }
</span><span id="2058" class="l"><a href="#2058">2058: </a>
</span><span id="2059" class="l"><a href="#2059">2059: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$type</span> === <span class="php-quote">'db'</span> ) {
</span><span id="2060" class="l"><a href="#2060">2060: </a>            <span class="php-keyword1">return</span> <span class="php-var">$db</span>;
</span><span id="2061" class="l"><a href="#2061">2061: </a>        }
</span><span id="2062" class="l"><a href="#2062">2062: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$type</span> === <span class="php-quote">'table'</span> ) {
</span><span id="2063" class="l"><a href="#2063">2063: </a>            <span class="php-keyword1">return</span> <span class="php-var">$table</span>;
</span><span id="2064" class="l"><a href="#2064">2064: </a>        }
</span><span id="2065" class="l"><a href="#2065">2065: </a>        <span class="php-keyword1">return</span> <span class="php-var">$column</span>;
</span><span id="2066" class="l"><a href="#2066">2066: </a>    }
</span><span id="2067" class="l"><a href="#2067">2067: </a>
</span><span id="2068" class="l"><a href="#2068">2068: </a>
</span><span id="2069" class="l"><a href="#2069">2069: </a>    <span class="php-comment">/**
</span></span><span id="2070" class="l"><a href="#2070">2070: </a><span class="php-comment">     * Trigger an event
</span></span><span id="2071" class="l"><a href="#2071">2071: </a><span class="php-comment">     * 
</span></span><span id="2072" class="l"><a href="#2072">2072: </a><span class="php-comment">     * @private
</span></span><span id="2073" class="l"><a href="#2073">2073: </a><span class="php-comment">     */</span>
</span><span id="2074" class="l"><a href="#2074">2074: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _trigger ( <span class="php-var">$eventName</span>, &amp;<span class="php-var">$arg1</span>=<span class="php-keyword1">null</span>, &amp;<span class="php-var">$arg2</span>=<span class="php-keyword1">null</span>, &amp;<span class="php-var">$arg3</span>=<span class="php-keyword1">null</span>, &amp;<span class="php-var">$arg4</span>=<span class="php-keyword1">null</span>, &amp;<span class="php-var">$arg5</span>=<span class="php-keyword1">null</span> )
</span><span id="2075" class="l"><a href="#2075">2075: </a>    {
</span><span id="2076" class="l"><a href="#2076">2076: </a>        <span class="php-var">$out</span> = <span class="php-keyword1">null</span>;
</span><span id="2077" class="l"><a href="#2077">2077: </a>        <span class="php-var">$argc</span> = <span class="php-keyword2">func_num_args</span>();
</span><span id="2078" class="l"><a href="#2078">2078: </a>        <span class="php-var">$args</span> = <span class="php-keyword1">array</span>( <span class="php-var">$this</span> );
</span><span id="2079" class="l"><a href="#2079">2079: </a>
</span><span id="2080" class="l"><a href="#2080">2080: </a>        <span class="php-comment">// Hack to enable pass by reference with a &quot;variable&quot; number of parameters</span>
</span><span id="2081" class="l"><a href="#2081">2081: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">1</span> ; <span class="php-var">$i</span>&lt;<span class="php-var">$argc</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="2082" class="l"><a href="#2082">2082: </a>            <span class="php-var">$name</span> = <span class="php-quote">'arg'</span>.<span class="php-var">$i</span>;
</span><span id="2083" class="l"><a href="#2083">2083: </a>            <span class="php-var">$args</span>[] = &amp;<span class="php-var">$$name</span>;
</span><span id="2084" class="l"><a href="#2084">2084: </a>        }
</span><span id="2085" class="l"><a href="#2085">2085: </a>
</span><span id="2086" class="l"><a href="#2086">2086: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$this</span>-&gt;_events[ <span class="php-var">$eventName</span> ] ) ) {
</span><span id="2087" class="l"><a href="#2087">2087: </a>            <span class="php-keyword1">return</span>;
</span><span id="2088" class="l"><a href="#2088">2088: </a>        }
</span><span id="2089" class="l"><a href="#2089">2089: </a>
</span><span id="2090" class="l"><a href="#2090">2090: </a>        <span class="php-var">$events</span> = <span class="php-var">$this</span>-&gt;_events[ <span class="php-var">$eventName</span> ];
</span><span id="2091" class="l"><a href="#2091">2091: </a>
</span><span id="2092" class="l"><a href="#2092">2092: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$events</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="2093" class="l"><a href="#2093">2093: </a>            <span class="php-var">$res</span> = <span class="php-keyword2">call_user_func_array</span>( <span class="php-var">$events</span>[<span class="php-var">$i</span>], <span class="php-var">$args</span> );
</span><span id="2094" class="l"><a href="#2094">2094: </a>
</span><span id="2095" class="l"><a href="#2095">2095: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$res</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="2096" class="l"><a href="#2096">2096: </a>                <span class="php-var">$out</span> = <span class="php-var">$res</span>;
</span><span id="2097" class="l"><a href="#2097">2097: </a>            }
</span><span id="2098" class="l"><a href="#2098">2098: </a>        }
</span><span id="2099" class="l"><a href="#2099">2099: </a>
</span><span id="2100" class="l"><a href="#2100">2100: </a>        <span class="php-keyword1">return</span> <span class="php-var">$out</span>;
</span><span id="2101" class="l"><a href="#2101">2101: </a>    }
</span><span id="2102" class="l"><a href="#2102">2102: </a>
</span><span id="2103" class="l"><a href="#2103">2103: </a>
</span><span id="2104" class="l"><a href="#2104">2104: </a>    <span class="php-comment">/**
</span></span><span id="2105" class="l"><a href="#2105">2105: </a><span class="php-comment">     * Merge a primary key value with an updated data source.
</span></span><span id="2106" class="l"><a href="#2106">2106: </a><span class="php-comment">     *
</span></span><span id="2107" class="l"><a href="#2107">2107: </a><span class="php-comment">     * @param  string $pkeyVal Old primary key value to merge into
</span></span><span id="2108" class="l"><a href="#2108">2108: </a><span class="php-comment">     * @param  array  $row     Data source for update
</span></span><span id="2109" class="l"><a href="#2109">2109: </a><span class="php-comment">     * @return string          Merged value
</span></span><span id="2110" class="l"><a href="#2110">2110: </a><span class="php-comment">     */</span>
</span><span id="2111" class="l"><a href="#2111">2111: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _pkey_submit_merge ( <span class="php-var">$pkeyVal</span>, <span class="php-var">$row</span> )
</span><span id="2112" class="l"><a href="#2112">2112: </a>    {
</span><span id="2113" class="l"><a href="#2113">2113: </a>        <span class="php-var">$pkey</span> = <span class="php-var">$this</span>-&gt;_pkey;
</span><span id="2114" class="l"><a href="#2114">2114: </a>        <span class="php-var">$arr</span> = <span class="php-var">$this</span>-&gt;pkeyToArray( <span class="php-var">$pkeyVal</span>, <span class="php-keyword1">true</span> );
</span><span id="2115" class="l"><a href="#2115">2115: </a>
</span><span id="2116" class="l"><a href="#2116">2116: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$pkey</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="2117" class="l"><a href="#2117">2117: </a>            <span class="php-var">$column</span> = <span class="php-var">$pkey</span>[ <span class="php-var">$i</span> ];
</span><span id="2118" class="l"><a href="#2118">2118: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$column</span>, <span class="php-quote">'db'</span> );
</span><span id="2119" class="l"><a href="#2119">2119: </a>
</span><span id="2120" class="l"><a href="#2120">2120: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$field</span> &amp;&amp; <span class="php-var">$field</span>-&gt;apply( <span class="php-quote">'edit'</span>, <span class="php-var">$row</span> ) ) {
</span><span id="2121" class="l"><a href="#2121">2121: </a>                <span class="php-var">$arr</span>[ <span class="php-var">$column</span> ] = <span class="php-var">$field</span>-&gt;val( <span class="php-quote">'set'</span>, <span class="php-var">$row</span> );
</span><span id="2122" class="l"><a href="#2122">2122: </a>            }
</span><span id="2123" class="l"><a href="#2123">2123: </a>        }
</span><span id="2124" class="l"><a href="#2124">2124: </a>
</span><span id="2125" class="l"><a href="#2125">2125: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;pkeyToValue( <span class="php-var">$arr</span>, <span class="php-keyword1">true</span> );
</span><span id="2126" class="l"><a href="#2126">2126: </a>    }
</span><span id="2127" class="l"><a href="#2127">2127: </a>
</span><span id="2128" class="l"><a href="#2128">2128: </a>
</span><span id="2129" class="l"><a href="#2129">2129: </a>    <span class="php-comment">/**
</span></span><span id="2130" class="l"><a href="#2130">2130: </a><span class="php-comment">     * Validate that all primary key fields have values for create.
</span></span><span id="2131" class="l"><a href="#2131">2131: </a><span class="php-comment">     *
</span></span><span id="2132" class="l"><a href="#2132">2132: </a><span class="php-comment">     * @param  array $row Row's data
</span></span><span id="2133" class="l"><a href="#2133">2133: </a><span class="php-comment">     * @return boolean    `true` if valid, `false` otherwise
</span></span><span id="2134" class="l"><a href="#2134">2134: </a><span class="php-comment">     */</span>
</span><span id="2135" class="l"><a href="#2135">2135: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _pkey_validate_insert ( <span class="php-var">$row</span> )
</span><span id="2136" class="l"><a href="#2136">2136: </a>    {
</span><span id="2137" class="l"><a href="#2137">2137: </a>        <span class="php-var">$pkey</span> = <span class="php-var">$this</span>-&gt;_pkey;
</span><span id="2138" class="l"><a href="#2138">2138: </a>
</span><span id="2139" class="l"><a href="#2139">2139: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$pkey</span> ) === <span class="php-num">1</span> ) {
</span><span id="2140" class="l"><a href="#2140">2140: </a>            <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="2141" class="l"><a href="#2141">2141: </a>        }
</span><span id="2142" class="l"><a href="#2142">2142: </a>
</span><span id="2143" class="l"><a href="#2143">2143: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$pkey</span>) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="2144" class="l"><a href="#2144">2144: </a>            <span class="php-var">$column</span> = <span class="php-var">$pkey</span>[ <span class="php-var">$i</span> ];
</span><span id="2145" class="l"><a href="#2145">2145: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_find_field( <span class="php-var">$column</span>, <span class="php-quote">'db'</span> );
</span><span id="2146" class="l"><a href="#2146">2146: </a>
</span><span id="2147" class="l"><a href="#2147">2147: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$field</span> || ! <span class="php-var">$field</span>-&gt;apply(<span class="php-quote">&quot;create&quot;</span>, <span class="php-var">$row</span>) ) {
</span><span id="2148" class="l"><a href="#2148">2148: </a>                <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception( <span class="php-quote">&quot;When inserting into a compound key table, &quot;</span>.
</span><span id="2149" class="l"><a href="#2149">2149: </a>                    <span class="php-quote">&quot;all fields that are part of the compound key must be &quot;</span>.
</span><span id="2150" class="l"><a href="#2150">2150: </a>                    <span class="php-quote">&quot;submitted with a specific value.&quot;</span>, <span class="php-num">1</span>
</span><span id="2151" class="l"><a href="#2151">2151: </a>                );
</span><span id="2152" class="l"><a href="#2152">2152: </a>            }
</span><span id="2153" class="l"><a href="#2153">2153: </a>        }
</span><span id="2154" class="l"><a href="#2154">2154: </a>
</span><span id="2155" class="l"><a href="#2155">2155: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">true</span>;
</span><span id="2156" class="l"><a href="#2156">2156: </a>    }
</span><span id="2157" class="l"><a href="#2157">2157: </a>
</span><span id="2158" class="l"><a href="#2158">2158: </a>
</span><span id="2159" class="l"><a href="#2159">2159: </a>    <span class="php-comment">/**
</span></span><span id="2160" class="l"><a href="#2160">2160: </a><span class="php-comment">     * Create the separator value for a compound primary key.
</span></span><span id="2161" class="l"><a href="#2161">2161: </a><span class="php-comment">     *
</span></span><span id="2162" class="l"><a href="#2162">2162: </a><span class="php-comment">     * @return string Calculated separator
</span></span><span id="2163" class="l"><a href="#2163">2163: </a><span class="php-comment">     */</span>
</span><span id="2164" class="l"><a href="#2164">2164: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _pkey_separator ()
</span><span id="2165" class="l"><a href="#2165">2165: </a>    {
</span><span id="2166" class="l"><a href="#2166">2166: </a>        <span class="php-var">$str</span> = <span class="php-keyword2">implode</span>(<span class="php-quote">','</span>, <span class="php-var">$this</span>-&gt;_pkey);
</span><span id="2167" class="l"><a href="#2167">2167: </a>
</span><span id="2168" class="l"><a href="#2168">2168: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">hash</span>( <span class="php-quote">'crc32'</span>, <span class="php-var">$str</span> );
</span><span id="2169" class="l"><a href="#2169">2169: </a>    }
</span><span id="2170" class="l"><a href="#2170">2170: </a>
</span><span id="2171" class="l"><a href="#2171">2171: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _read_table ()
</span><span id="2172" class="l"><a href="#2172">2172: </a>    {
</span><span id="2173" class="l"><a href="#2173">2173: </a>        <span class="php-keyword1">return</span> <span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_readTableNames) ?
</span><span id="2174" class="l"><a href="#2174">2174: </a>            <span class="php-var">$this</span>-&gt;_readTableNames :
</span><span id="2175" class="l"><a href="#2175">2175: </a>            <span class="php-var">$this</span>-&gt;_table;
</span><span id="2176" class="l"><a href="#2176">2176: </a>    }
</span><span id="2177" class="l"><a href="#2177">2177: </a>}
</span><span id="2178" class="l"><a href="#2178">2178: </a>
</span><span id="2179" class="l"><a href="#2179">2179: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
