<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>Class DataTables\Ext | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li class="active">
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">Database</a></li>
				<li><a href="class-DataTables.Editor.html">Editor</a></li>
				<li class="active"><a href="class-DataTables.Ext.html">Ext</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
				<a href="namespace-DataTables.html" title="Summary of DataTables"><span>Namespace</span></a>
			</li>
			<li class="active">
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<div id="content" class="class">
	<h1>Class Ext</h1>


	<div class="description">
	<p>Base class for DataTables classes.</p>
	</div>



	<div>
		<h4>Direct known subclasses</h4>
			<a href="class-DataTables.Editor.html">DataTables\Editor</a>, 
			<a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a>, 
			<a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a>, 
			<a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a>, 
			<a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a>, 
			<a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a>
	</div>

	<div>
		<h4>Indirect known subclasses</h4>
			<a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a>
	</div>





	<div class="info">
		
		
		<b>Namespace:</b> <a href="namespace-DataTables.html">DataTables</a><br>
		

			<b>Located at</b> <a href="source-class-DataTables.Ext.html#17-221" title="Go to source code">Ext.php</a>
		<br>
	</div>



	<table class="summary methods" id="methods">
	<caption>Methods summary</caption>
	<tr data-order="instantiate" id="_instantiate">

		<td class="attributes"><code>
			 public static

			<code><a href="class-DataTables.Editor.html">DataTables\Editor</a></code>|<code><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></code>|<code><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></code>|<code><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></code>
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_instantiate">#</a>
		<code><a href="source-class-DataTables.Ext.html#21-40" title="Go to source code">instantiate</a>( )</code>

		<div class="description short">
			<p>Static method to instantiate a new instance of a class.</p>
		</div>

		<div class="description detailed hidden">
			<p>Static method to instantiate a new instance of a class.</p>

<p>A factory method that will create a new instance of the class
that has extended 'Ext'. This allows classes to be instantiated
and then chained - which otherwise isn't available until PHP 5.4.
If using PHP 5.4 or later, simply create a 'new' instance of the
target class and chain methods as normal.</p>



				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Editor.html">DataTables\Editor</a></code>|<code><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></code>|<code><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></code>|<code><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></code><br>Instantiated class
				</div>




		</div>
		</div></td>
	</tr>
	<tr data-order="inst" id="_inst">

		<td class="attributes"><code>
			 public static

			<code><a href="class-DataTables.Editor.html">DataTables\Editor</a></code>|<code><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></code>|<code><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></code>|<code><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></code>
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#_inst">#</a>
		<code><a href="source-class-DataTables.Ext.html#42-59" title="Go to source code">inst</a>( )</code>

		<div class="description short">
			<p>Static method to instantiate a new instance of a class (shorthand of
'instantiate').</p>
		</div>

		<div class="description detailed hidden">
			<p>Static method to instantiate a new instance of a class (shorthand of
'instantiate').</p>

<p>This method performs exactly the same actions as the 'instantiate'
static method, but is simply shorter and easier to type!</p>



				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Editor.html">DataTables\Editor</a></code>|<code><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></code>|<code><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></code>|<code><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></code><br>class
				</div>




		</div>
		</div></td>
	</tr>
	<tr data-order="_getSet" id="__getSet">

		<td class="attributes"><code>
			 protected 

			<code><a href="class-DataTables.Ext.html">DataTables\Ext</a></code>|mixed
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#__getSet">#</a>
		<code><a href="source-class-DataTables.Ext.html#61-95" title="Go to source code">_getSet</a>( <span>mixed <var>&amp; $prop</var></span>, <span>mixed <var>$val</var></span>, <span>boolean <var>$array</var> = <span class="php-keyword1">false</span> </span> )</code>

		<div class="description short">
			<p>Common getter / setter function for DataTables classes.</p>
		</div>

		<div class="description detailed hidden">
			<p>Common getter / setter function for DataTables classes.</p>

<p>This getter / setter method makes building getter / setting methods
easier, by abstracting everything to a single function call.</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$prop</var></dt>
					<dd>$prop The property to set</dd>
					<dt><var>$val</var></dt>
					<dd><p>The value to set - if given as null, then we assume
   that the function is being used as a getter.</p></dd>
					<dt><var>$array</var></dt>
					<dd><p>Treat the target property as an array or not
   (default false). If used as an array, then values passed in are added
   to the $prop array.</p></dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					<code><a href="class-DataTables.Ext.html">DataTables\Ext</a></code>|mixed<br><p>Class instance if setting (allowing chaining), or
   the value requested if getting.</p>
				</div>




		</div>
		</div></td>
	</tr>
	<tr data-order="_propExists" id="__propExists">

		<td class="attributes"><code>
			 protected 

			boolean
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#__propExists">#</a>
		<code><a href="source-class-DataTables.Ext.html#97-129" title="Go to source code">_propExists</a>( <span>string <var>$name</var></span>, <span>array <var>$data</var></span> )</code>

		<div class="description short">
			<p>Determine if a property is available in a data set (allowing <code>null</code> to be
a valid value)</p>
		</div>

		<div class="description detailed hidden">
			<p>Determine if a property is available in a data set (allowing <code>null</code> to be
a valid value)</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$name</var></dt>
					<dd>Javascript dotted object name to write to</dd>
					<dt><var>$data</var></dt>
					<dd>Data source array to read from</dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					boolean<br>true if present, false otherwise
				</div>


				<h4>Private</h4>
				<div class="list">
				</div>


		</div>
		</div></td>
	</tr>
	<tr data-order="_readProp" id="__readProp">

		<td class="attributes"><code>
			 protected 

			mixed
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#__readProp">#</a>
		<code><a href="source-class-DataTables.Ext.html#131-170" title="Go to source code">_readProp</a>( <span>string <var>$name</var></span>, <span>array <var>$data</var></span> )</code>

		<div class="description short">
			<p>Read a value from a data structure, using Javascript dotted object
notation. This is the inverse of the <code>_writeProp</code> method and provides
the same support, matching DataTables' ability to read nested JSON
data objects.</p>
		</div>

		<div class="description detailed hidden">
			<p>Read a value from a data structure, using Javascript dotted object
notation. This is the inverse of the <code>_writeProp</code> method and provides
the same support, matching DataTables' ability to read nested JSON
data objects.</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$name</var></dt>
					<dd>Javascript dotted object name to write to</dd>
					<dt><var>$data</var></dt>
					<dd>Data source array to read from</dd>
				</dl></div>

				<h4>Returns</h4>
				<div class="list">
					mixed<br>The read value, or null if no value found.
				</div>


				<h4>Private</h4>
				<div class="list">
				</div>


		</div>
		</div></td>
	</tr>
	<tr data-order="_writeProp" id="__writeProp">

		<td class="attributes"><code>
			 protected 

			
			
			</code>
		</td>

		<td class="name"><div>
		<a class="anchor" href="#__writeProp">#</a>
		<code><a href="source-class-DataTables.Ext.html#172-220" title="Go to source code">_writeProp</a>( <span>array <var>&amp; $out</var></span>, <span>string <var>$name</var></span>, <span>mixed <var>$value</var></span> )</code>

		<div class="description short">
			<p>Write the field's value to an array structure, using Javascript dotted
object notation to indicate JSON data structure. For example <code>name.first</code>
gives the data structure: <code>name: { first: ... }</code>. This matches DataTables
own ability to do this on the client-side, although this doesn't
implement implement quite such a complex structure (no array / function
support).</p>
		</div>

		<div class="description detailed hidden">
			<p>Write the field's value to an array structure, using Javascript dotted
object notation to indicate JSON data structure. For example <code>name.first</code>
gives the data structure: <code>name: { first: ... }</code>. This matches DataTables
own ability to do this on the client-side, although this doesn't
implement implement quite such a complex structure (no array / function
support).</p>


				<h4>Parameters</h4>
				<div class="list"><dl>
					<dt><var>$out</var></dt>
					<dd>$out   Array to write the data to</dd>
					<dt><var>$name</var></dt>
					<dd>Javascript dotted object name to write to</dd>
					<dt><var>$value</var></dt>
					<dd>Value to write</dd>
				</dl></div>


				<h4>Throws</h4>
				<div class="list">
					Exception<br>Information about duplicate properties
				</div>

				<h4>Private</h4>
				<div class="list">
				</div>


		</div>
		</div></td>
	</tr>
	</table>


















</div>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js"></script>
<script src="elementlist.js"></script>
</body>
</html>
