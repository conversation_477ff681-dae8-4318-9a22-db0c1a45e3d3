<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Vendor/Htmlaw.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * HtmLawed is used here to provide protection against XSS attacks with Editor
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> * input - see the `Field-&gt;xss()` method. The Vanilla forums wrapper is used
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * to provide sensible defaults and a clean interface for HtmLawed.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> * 
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> * Changes:
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> * 
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  * Add `DataTables/Vendor` namespace to this and htmLawed - this is to ensure
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *    that if htmLawed is included by any other aspect of the site it will not
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> *    result in a conflict.
</span></span><span id="12" class="l"><a href="#12"> 12: </a><span class="php-comment"> *  * Use the OOP version of htmLawed (required a single updated to call it) to
</span></span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-comment"> *    make the namespacing relatively easy.
</span></span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-comment"> *  * Change the name of the Vanilla class so it don't conflict with the
</span></span><span id="15" class="l"><a href="#15"> 15: </a><span class="php-comment"> *    htmLawed OOP class
</span></span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-comment"> *  * Update all `htmLawed::` references to `\DataTables\Vendor\htmLawed::` in
</span></span><span id="17" class="l"><a href="#17"> 17: </a><span class="php-comment"> *    the htmLawed file (to allow callbacks to operate correctly)
</span></span><span id="18" class="l"><a href="#18"> 18: </a><span class="php-comment"> *  * Updated Vanilla wrapper to operate on PHP 5.3
</span></span><span id="19" class="l"><a href="#19"> 19: </a><span class="php-comment"> * 
</span></span><span id="20" class="l"><a href="#20"> 20: </a><span class="php-comment"> * HtmLawed:
</span></span><span id="21" class="l"><a href="#21"> 21: </a><span class="php-comment"> *   http://www.bioinformatics.org/phplabware/internal_utilities/htmLawed/
</span></span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-comment"> *   Copyright: Santosh Patnaik
</span></span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment"> *   License: Dual licensed with LGPL 3 and GPL 2+
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment"> *
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment"> * Vanilla wrapper for HtmLawed:
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment"> *   https://github.com/vanilla/htmlawed/
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment"> *   Author: Todd Burry &lt;<EMAIL>&gt;
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment"> *   Copyright: 2009-2014 Vanilla Forums Inc.
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment"> *   License: LGPL-3.0
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment"> */</span>
</span><span id="31" class="l"><a href="#31"> 31: </a>
</span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-keyword1">namespace</span> DataTables\Vendor;
</span><span id="33" class="l"><a href="#33"> 33: </a>
</span><span id="34" class="l"><a href="#34"> 34: </a><span class="php-comment">/**
</span></span><span id="35" class="l"><a href="#35"> 35: </a><span class="php-comment"> * A class wrapper for the htmLawed library.
</span></span><span id="36" class="l"><a href="#36"> 36: </a><span class="php-comment"> */</span>
</span><span id="37" class="l"><a href="#37"> 37: </a><span class="php-keyword1">class</span> Htmlaw {
</span><span id="38" class="l"><a href="#38"> 38: </a>    <span class="php-comment">/// Methods ///</span>
</span><span id="39" class="l"><a href="#39"> 39: </a>
</span><span id="40" class="l"><a href="#40"> 40: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-var">$defaultConfig</span> = <span class="php-keyword1">array</span>(
</span><span id="41" class="l"><a href="#41"> 41: </a>        <span class="php-quote">'anti_link_spam'</span> =&gt; <span class="php-keyword1">array</span>(<span class="php-quote">'`.`'</span>, <span class="php-quote">''</span>),
</span><span id="42" class="l"><a href="#42"> 42: </a>        <span class="php-quote">'comment'</span> =&gt; <span class="php-num">1</span>,
</span><span id="43" class="l"><a href="#43"> 43: </a>        <span class="php-quote">'cdata'</span> =&gt; <span class="php-num">3</span>,
</span><span id="44" class="l"><a href="#44"> 44: </a>        <span class="php-quote">'css_expression'</span> =&gt; <span class="php-num">1</span>,
</span><span id="45" class="l"><a href="#45"> 45: </a>        <span class="php-quote">'deny_attribute'</span> =&gt; <span class="php-quote">'on*'</span>,
</span><span id="46" class="l"><a href="#46"> 46: </a>        <span class="php-quote">'unique_ids'</span> =&gt; <span class="php-num">0</span>,
</span><span id="47" class="l"><a href="#47"> 47: </a>        <span class="php-quote">'elements'</span> =&gt; <span class="php-quote">'*-applet-form-input-textarea-iframe-script-style-embed-object'</span>,
</span><span id="48" class="l"><a href="#48"> 48: </a>        <span class="php-quote">'keep_bad'</span> =&gt; <span class="php-num">1</span>,
</span><span id="49" class="l"><a href="#49"> 49: </a>        <span class="php-quote">'schemes'</span> =&gt; <span class="php-quote">'classid:clsid; href: aim, feed, file, ftp, gopher, http, https, irc, mailto, news, nntp, sftp, ssh, telnet; style: nil; *:file, http, https'</span>, <span class="php-comment">// clsid allowed in class</span>
</span><span id="50" class="l"><a href="#50"> 50: </a>        <span class="php-quote">'valid_xhtml'</span> =&gt; <span class="php-num">0</span>,
</span><span id="51" class="l"><a href="#51"> 51: </a>        <span class="php-quote">'direct_list_nest'</span> =&gt; <span class="php-num">1</span>,
</span><span id="52" class="l"><a href="#52"> 52: </a>        <span class="php-quote">'balance'</span> =&gt; <span class="php-num">1</span>
</span><span id="53" class="l"><a href="#53"> 53: </a>    );
</span><span id="54" class="l"><a href="#54"> 54: </a>
</span><span id="55" class="l"><a href="#55"> 55: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-var">$defaultSpec</span> = <span class="php-keyword1">array</span>(
</span><span id="56" class="l"><a href="#56"> 56: </a>        <span class="php-quote">'object=-classid-type, -codebase'</span>,
</span><span id="57" class="l"><a href="#57"> 57: </a>        <span class="php-quote">'embed=type(oneof=application/x-shockwave-flash)'</span>
</span><span id="58" class="l"><a href="#58"> 58: </a>    );
</span><span id="59" class="l"><a href="#59"> 59: </a>
</span><span id="60" class="l"><a href="#60"> 60: </a>    <span class="php-comment">/**
</span></span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-comment">     * Filters a string of html with the htmLawed library.
</span></span><span id="62" class="l"><a href="#62"> 62: </a><span class="php-comment">     *
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment">     * @param string $html The text to filter.
</span></span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-comment">     * @param array|null $config Config settings for the array.
</span></span><span id="65" class="l"><a href="#65"> 65: </a><span class="php-comment">     * @param string|array|null $spec A specification to further limit the allowed attribute values in the html.
</span></span><span id="66" class="l"><a href="#66"> 66: </a><span class="php-comment">     * @return string Returns the filtered html.
</span></span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-comment">     * @see http://www.bioinformatics.org/phplabware/internal_utilities/htmLawed/htmLawed_README.htm
</span></span><span id="68" class="l"><a href="#68"> 68: </a><span class="php-comment">     */</span>
</span><span id="69" class="l"><a href="#69"> 69: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> filter(<span class="php-var">$html</span>, <span class="php-keyword1">array</span> <span class="php-var">$config</span> = <span class="php-keyword1">null</span>, <span class="php-var">$spec</span> = <span class="php-keyword1">null</span>) {
</span><span id="70" class="l"><a href="#70"> 70: </a>        <span class="php-keyword1">require_once</span> __DIR__.<span class="php-quote">'/htmLawed/htmLawed.php'</span>;
</span><span id="71" class="l"><a href="#71"> 71: </a>
</span><span id="72" class="l"><a href="#72"> 72: </a>        <span class="php-keyword1">if</span> (<span class="php-var">$config</span> === <span class="php-keyword1">null</span>) {
</span><span id="73" class="l"><a href="#73"> 73: </a>            <span class="php-var">$config</span> = self::<span class="php-var">$defaultConfig</span>;
</span><span id="74" class="l"><a href="#74"> 74: </a>        }
</span><span id="75" class="l"><a href="#75"> 75: </a>
</span><span id="76" class="l"><a href="#76"> 76: </a>        <span class="php-keyword1">if</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$config</span>[<span class="php-quote">'spec'</span>]) &amp;&amp; !<span class="php-var">$spec</span>) {
</span><span id="77" class="l"><a href="#77"> 77: </a>            <span class="php-var">$spec</span> = <span class="php-var">$config</span>[<span class="php-quote">'spec'</span>];
</span><span id="78" class="l"><a href="#78"> 78: </a>        }
</span><span id="79" class="l"><a href="#79"> 79: </a>
</span><span id="80" class="l"><a href="#80"> 80: </a>        <span class="php-keyword1">if</span> (<span class="php-var">$spec</span> === <span class="php-keyword1">null</span>) {
</span><span id="81" class="l"><a href="#81"> 81: </a>            <span class="php-var">$spec</span> = <span class="php-keyword1">static</span>::<span class="php-var">$defaultSpec</span>;
</span><span id="82" class="l"><a href="#82"> 82: </a>        }
</span><span id="83" class="l"><a href="#83"> 83: </a>
</span><span id="84" class="l"><a href="#84"> 84: </a>        <span class="php-keyword1">return</span> htmLawed::hl(<span class="php-var">$html</span>, <span class="php-var">$config</span>, <span class="php-var">$spec</span>);
</span><span id="85" class="l"><a href="#85"> 85: </a>    }
</span><span id="86" class="l"><a href="#86"> 86: </a>
</span><span id="87" class="l"><a href="#87"> 87: </a>
</span><span id="88" class="l"><a href="#88"> 88: </a>    <span class="php-comment">/**
</span></span><span id="89" class="l"><a href="#89"> 89: </a><span class="php-comment">     * Filter a string of html so that it can be put into an rss feed.
</span></span><span id="90" class="l"><a href="#90"> 90: </a><span class="php-comment">     *
</span></span><span id="91" class="l"><a href="#91"> 91: </a><span class="php-comment">     * @param $html The html text to fitlter.
</span></span><span id="92" class="l"><a href="#92"> 92: </a><span class="php-comment">     * @return string Returns the filtered html.
</span></span><span id="93" class="l"><a href="#93"> 93: </a><span class="php-comment">     * @see Htmlawed::filter().
</span></span><span id="94" class="l"><a href="#94"> 94: </a><span class="php-comment">     */</span>
</span><span id="95" class="l"><a href="#95"> 95: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> filterRSS(<span class="php-var">$html</span>) {
</span><span id="96" class="l"><a href="#96"> 96: </a>        <span class="php-var">$config</span> = <span class="php-keyword1">array</span>(
</span><span id="97" class="l"><a href="#97"> 97: </a>            <span class="php-quote">'anti_link_spam'</span> =&gt; <span class="php-keyword1">array</span>(<span class="php-quote">'`.`'</span>, <span class="php-quote">''</span>),
</span><span id="98" class="l"><a href="#98"> 98: </a>            <span class="php-quote">'comment'</span> =&gt; <span class="php-num">1</span>,
</span><span id="99" class="l"><a href="#99"> 99: </a>            <span class="php-quote">'cdata'</span> =&gt; <span class="php-num">3</span>,
</span><span id="100" class="l"><a href="#100">100: </a>            <span class="php-quote">'css_expression'</span> =&gt; <span class="php-num">1</span>,
</span><span id="101" class="l"><a href="#101">101: </a>            <span class="php-quote">'deny_attribute'</span> =&gt; <span class="php-quote">'on*,style,class'</span>,
</span><span id="102" class="l"><a href="#102">102: </a>            <span class="php-quote">'elements'</span> =&gt; <span class="php-quote">'*-applet-form-input-textarea-iframe-script-style-object-embed-comment-link-listing-meta-noscript-plaintext-xmp'</span>,
</span><span id="103" class="l"><a href="#103">103: </a>            <span class="php-quote">'keep_bad'</span> =&gt; <span class="php-num">0</span>,
</span><span id="104" class="l"><a href="#104">104: </a>            <span class="php-quote">'schemes'</span> =&gt; <span class="php-quote">'classid:clsid; href: aim, feed, file, ftp, gopher, http, https, irc, mailto, news, nntp, sftp, ssh, telnet; style: nil; *:file, http, https'</span>, <span class="php-comment">// clsid allowed in class</span>
</span><span id="105" class="l"><a href="#105">105: </a>            <span class="php-quote">'valid_xml'</span> =&gt; <span class="php-num">2</span>,
</span><span id="106" class="l"><a href="#106">106: </a>            <span class="php-quote">'balance'</span> =&gt; <span class="php-num">1</span>
</span><span id="107" class="l"><a href="#107">107: </a>        );
</span><span id="108" class="l"><a href="#108">108: </a>        <span class="php-var">$spec</span> = <span class="php-keyword1">static</span>::<span class="php-var">$defaultSpec</span>;
</span><span id="109" class="l"><a href="#109">109: </a>
</span><span id="110" class="l"><a href="#110">110: </a>        <span class="php-var">$result</span> = <span class="php-keyword1">static</span>::filter(<span class="php-var">$html</span>, <span class="php-var">$config</span>, <span class="php-var">$spec</span>);
</span><span id="111" class="l"><a href="#111">111: </a>
</span><span id="112" class="l"><a href="#112">112: </a>        <span class="php-keyword1">return</span> <span class="php-var">$result</span>;
</span><span id="113" class="l"><a href="#113">113: </a>    }
</span><span id="114" class="l"><a href="#114">114: </a>}
</span><span id="115" class="l"><a href="#115">115: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
