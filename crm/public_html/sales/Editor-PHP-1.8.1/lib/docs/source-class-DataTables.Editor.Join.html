<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Editor/Join.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables\Editor;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-keyword1">use</span>
</span><span id="17" class="l"><a href="#17"> 17: </a>    DataTables,
</span><span id="18" class="l"><a href="#18"> 18: </a>    DataTables\Editor,
</span><span id="19" class="l"><a href="#19"> 19: </a>    DataTables\Editor\Field;
</span><span id="20" class="l"><a href="#20"> 20: </a>
</span><span id="21" class="l"><a href="#21"> 21: </a>
</span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-comment">/**
</span></span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment"> * Join table class for DataTables Editor.
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment"> *
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment"> * The Join class can be used with {@link Editor::join} to allow Editor to
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment"> * obtain joined information from the database.
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment"> *
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment"> * For an overview of how Join tables work, please refer to the 
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment"> * {@link https://editor.datatables.net/manual/php/ Editor manual} as it is
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment"> * useful to understand how this class represents the links between tables, 
</span></span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-comment"> * before fully getting to grips with it's API.
</span></span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-comment"> *
</span></span><span id="33" class="l"><a href="#33"> 33: </a><span class="php-comment"> *  @example
</span></span><span id="34" class="l"><a href="#34"> 34: </a><span class="php-comment"> *    Join the parent table (the one specified in the {@link Editor::table}
</span></span><span id="35" class="l"><a href="#35"> 35: </a><span class="php-comment"> *    method) with the table *access*, with a link table *user__access*, which
</span></span><span id="36" class="l"><a href="#36"> 36: </a><span class="php-comment"> *    allows multiple properties to be found for each row in the parent table.
</span></span><span id="37" class="l"><a href="#37"> 37: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="38" class="l"><a href="#38"> 38: </a><span class="php-comment"> *      Join::inst( 'access', 'array' )
</span></span><span id="39" class="l"><a href="#39"> 39: </a><span class="php-comment"> *          -&gt;link( 'users.id', 'user_access.user_id' )
</span></span><span id="40" class="l"><a href="#40"> 40: </a><span class="php-comment"> *          -&gt;link( 'access.id', 'user_access.access_id' )
</span></span><span id="41" class="l"><a href="#41"> 41: </a><span class="php-comment"> *          -&gt;field(
</span></span><span id="42" class="l"><a href="#42"> 42: </a><span class="php-comment"> *              Field::inst( 'id' )-&gt;validator( 'Validate::required' ),
</span></span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment"> *              Field::inst( 'name' )
</span></span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-comment"> *          )
</span></span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-comment"> *
</span></span><span id="47" class="l"><a href="#47"> 47: </a><span class="php-comment"> *  @example
</span></span><span id="48" class="l"><a href="#48"> 48: </a><span class="php-comment"> *    Single row join - here we join the parent table with a single row in
</span></span><span id="49" class="l"><a href="#49"> 49: </a><span class="php-comment"> *    the child table, without an intermediate link table. In this case the
</span></span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-comment"> *    child table is called *extra* and the two fields give the columns that
</span></span><span id="51" class="l"><a href="#51"> 51: </a><span class="php-comment"> *    Editor will read from that table.
</span></span><span id="52" class="l"><a href="#52"> 52: </a><span class="php-comment"> *    &lt;code&gt;
</span></span><span id="53" class="l"><a href="#53"> 53: </a><span class="php-comment"> *        Join::inst( 'extra', 'object' )
</span></span><span id="54" class="l"><a href="#54"> 54: </a><span class="php-comment"> *            -&gt;link( 'user.id', 'extra.user_id' )
</span></span><span id="55" class="l"><a href="#55"> 55: </a><span class="php-comment"> *            -&gt;field(
</span></span><span id="56" class="l"><a href="#56"> 56: </a><span class="php-comment"> *                Field::inst( 'comments' ),
</span></span><span id="57" class="l"><a href="#57"> 57: </a><span class="php-comment"> *                Field::inst( 'review' )
</span></span><span id="58" class="l"><a href="#58"> 58: </a><span class="php-comment"> *            )
</span></span><span id="59" class="l"><a href="#59"> 59: </a><span class="php-comment"> *    &lt;/code&gt;
</span></span><span id="60" class="l"><a href="#60"> 60: </a><span class="php-comment"> */</span>
</span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-keyword1">class</span> <span class="php-keyword2">Join</span> <span class="php-keyword1">extends</span> DataTables\Ext {
</span><span id="62" class="l"><a href="#62"> 62: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="63" class="l"><a href="#63"> 63: </a><span class="php-comment">     * Constructor
</span></span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-comment">     */</span>
</span><span id="65" class="l"><a href="#65"> 65: </a>
</span><span id="66" class="l"><a href="#66"> 66: </a>    <span class="php-comment">/**
</span></span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-comment">     * Join instance constructor.
</span></span><span id="68" class="l"><a href="#68"> 68: </a><span class="php-comment">     *  @param string $table Table name to get the joined data from.
</span></span><span id="69" class="l"><a href="#69"> 69: </a><span class="php-comment">     *  @param string $type Work with a single result ('object') or an array of 
</span></span><span id="70" class="l"><a href="#70"> 70: </a><span class="php-comment">     *    results ('array') for the join.
</span></span><span id="71" class="l"><a href="#71"> 71: </a><span class="php-comment">     */</span>
</span><span id="72" class="l"><a href="#72"> 72: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$table</span>=<span class="php-keyword1">null</span>, <span class="php-var">$type</span>=<span class="php-quote">'object'</span> )
</span><span id="73" class="l"><a href="#73"> 73: </a>    {
</span><span id="74" class="l"><a href="#74"> 74: </a>        <span class="php-var">$this</span>-&gt;table( <span class="php-var">$table</span> );
</span><span id="75" class="l"><a href="#75"> 75: </a>        <span class="php-var">$this</span>-&gt;type( <span class="php-var">$type</span> );
</span><span id="76" class="l"><a href="#76"> 76: </a>    }
</span><span id="77" class="l"><a href="#77"> 77: </a>
</span><span id="78" class="l"><a href="#78"> 78: </a>
</span><span id="79" class="l"><a href="#79"> 79: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="80" class="l"><a href="#80"> 80: </a><span class="php-comment">     * Private properties
</span></span><span id="81" class="l"><a href="#81"> 81: </a><span class="php-comment">     */</span>
</span><span id="82" class="l"><a href="#82"> 82: </a>
</span><span id="83" class="l"><a href="#83"> 83: </a>    <span class="php-comment">/** @var DataTables\Editor\Field[] */</span>
</span><span id="84" class="l"><a href="#84"> 84: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_fields</span> = <span class="php-keyword1">array</span>();
</span><span id="85" class="l"><a href="#85"> 85: </a>
</span><span id="86" class="l"><a href="#86"> 86: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="87" class="l"><a href="#87"> 87: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_join</span> = <span class="php-keyword1">array</span>(
</span><span id="88" class="l"><a href="#88"> 88: </a>        <span class="php-quote">&quot;parent&quot;</span> =&gt; <span class="php-keyword1">null</span>,
</span><span id="89" class="l"><a href="#89"> 89: </a>        <span class="php-quote">&quot;child&quot;</span> =&gt; <span class="php-keyword1">null</span>,
</span><span id="90" class="l"><a href="#90"> 90: </a>        <span class="php-quote">&quot;table&quot;</span> =&gt; <span class="php-keyword1">null</span>
</span><span id="91" class="l"><a href="#91"> 91: </a>    );
</span><span id="92" class="l"><a href="#92"> 92: </a>
</span><span id="93" class="l"><a href="#93"> 93: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="94" class="l"><a href="#94"> 94: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_table</span> = <span class="php-keyword1">null</span>;
</span><span id="95" class="l"><a href="#95"> 95: </a>
</span><span id="96" class="l"><a href="#96"> 96: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="97" class="l"><a href="#97"> 97: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_type</span> = <span class="php-keyword1">null</span>;
</span><span id="98" class="l"><a href="#98"> 98: </a>
</span><span id="99" class="l"><a href="#99"> 99: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="100" class="l"><a href="#100">100: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_name</span> = <span class="php-keyword1">null</span>;
</span><span id="101" class="l"><a href="#101">101: </a>
</span><span id="102" class="l"><a href="#102">102: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="103" class="l"><a href="#103">103: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_get</span> = <span class="php-keyword1">true</span>;
</span><span id="104" class="l"><a href="#104">104: </a>
</span><span id="105" class="l"><a href="#105">105: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="106" class="l"><a href="#106">106: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_set</span> = <span class="php-keyword1">true</span>;
</span><span id="107" class="l"><a href="#107">107: </a>
</span><span id="108" class="l"><a href="#108">108: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="109" class="l"><a href="#109">109: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_aliasParentTable</span> = <span class="php-keyword1">null</span>;
</span><span id="110" class="l"><a href="#110">110: </a>
</span><span id="111" class="l"><a href="#111">111: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="112" class="l"><a href="#112">112: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_where</span> = <span class="php-keyword1">array</span>();
</span><span id="113" class="l"><a href="#113">113: </a>
</span><span id="114" class="l"><a href="#114">114: </a>    <span class="php-comment">/** @var boolean */</span>
</span><span id="115" class="l"><a href="#115">115: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_whereSet</span> = <span class="php-keyword1">false</span>;
</span><span id="116" class="l"><a href="#116">116: </a>
</span><span id="117" class="l"><a href="#117">117: </a>    <span class="php-comment">/** @var array */</span>
</span><span id="118" class="l"><a href="#118">118: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_links</span> = <span class="php-keyword1">array</span>();
</span><span id="119" class="l"><a href="#119">119: </a>
</span><span id="120" class="l"><a href="#120">120: </a>    <span class="php-comment">/** @var string */</span>
</span><span id="121" class="l"><a href="#121">121: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_customOrder</span> = <span class="php-keyword1">null</span>;
</span><span id="122" class="l"><a href="#122">122: </a>
</span><span id="123" class="l"><a href="#123">123: </a>
</span><span id="124" class="l"><a href="#124">124: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="125" class="l"><a href="#125">125: </a><span class="php-comment">     * Public methods
</span></span><span id="126" class="l"><a href="#126">126: </a><span class="php-comment">     */</span>
</span><span id="127" class="l"><a href="#127">127: </a>    
</span><span id="128" class="l"><a href="#128">128: </a>    <span class="php-comment">/**
</span></span><span id="129" class="l"><a href="#129">129: </a><span class="php-comment">     * Get / set parent table alias.
</span></span><span id="130" class="l"><a href="#130">130: </a><span class="php-comment">     * 
</span></span><span id="131" class="l"><a href="#131">131: </a><span class="php-comment">     * When working with a self referencing table (i.e. a column in the table contains
</span></span><span id="132" class="l"><a href="#132">132: </a><span class="php-comment">     * a primary key value from its own table) it can be useful to set an alias on the
</span></span><span id="133" class="l"><a href="#133">133: </a><span class="php-comment">     * parent table's name, allowing a self referencing Join. For example:
</span></span><span id="134" class="l"><a href="#134">134: </a><span class="php-comment">     *   &lt;code&gt;
</span></span><span id="135" class="l"><a href="#135">135: </a><span class="php-comment">     *   SELECT p2.publisher 
</span></span><span id="136" class="l"><a href="#136">136: </a><span class="php-comment">     *   FROM   publisher as p2
</span></span><span id="137" class="l"><a href="#137">137: </a><span class="php-comment">     *   JOIN   publisher on (publisher.idPublisher = p2.idPublisher)
</span></span><span id="138" class="l"><a href="#138">138: </a><span class="php-comment">     *   &lt;code&gt;
</span></span><span id="139" class="l"><a href="#139">139: </a><span class="php-comment">     * Where, in this case, `publisher` is the table that is used by the Editor instance,
</span></span><span id="140" class="l"><a href="#140">140: </a><span class="php-comment">     * and you want to use `Join` to link back to the table (resolving a name for example).
</span></span><span id="141" class="l"><a href="#141">141: </a><span class="php-comment">     * This method allows that alias to be set. Fields can then use standard SQL notation
</span></span><span id="142" class="l"><a href="#142">142: </a><span class="php-comment">     * to select a field, for example `p2.publisher` or `publisher.publisher`.
</span></span><span id="143" class="l"><a href="#143">143: </a><span class="php-comment">     *  @param string $_ Table alias to use
</span></span><span id="144" class="l"><a href="#144">144: </a><span class="php-comment">     *  @return string|self Table alias set (which is null by default), or self if used as
</span></span><span id="145" class="l"><a href="#145">145: </a><span class="php-comment">     *    a setter.
</span></span><span id="146" class="l"><a href="#146">146: </a><span class="php-comment">     */</span>
</span><span id="147" class="l"><a href="#147">147: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> aliasParentTable ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="148" class="l"><a href="#148">148: </a>    {
</span><span id="149" class="l"><a href="#149">149: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_aliasParentTable, <span class="php-var">$_</span> );
</span><span id="150" class="l"><a href="#150">150: </a>    }
</span><span id="151" class="l"><a href="#151">151: </a>
</span><span id="152" class="l"><a href="#152">152: </a>
</span><span id="153" class="l"><a href="#153">153: </a>    <span class="php-comment">/**
</span></span><span id="154" class="l"><a href="#154">154: </a><span class="php-comment">     * Get / set field instances.
</span></span><span id="155" class="l"><a href="#155">155: </a><span class="php-comment">     * 
</span></span><span id="156" class="l"><a href="#156">156: </a><span class="php-comment">     * The list of fields designates which columns in the table that will be read
</span></span><span id="157" class="l"><a href="#157">157: </a><span class="php-comment">     * from the joined table.
</span></span><span id="158" class="l"><a href="#158">158: </a><span class="php-comment">     *  @param Field $_... Instances of the {@link Field} class, given as a single 
</span></span><span id="159" class="l"><a href="#159">159: </a><span class="php-comment">     *    instance of {@link Field}, an array of {@link Field} instances, or multiple
</span></span><span id="160" class="l"><a href="#160">160: </a><span class="php-comment">     *    {@link Field} instance parameters for the function.
</span></span><span id="161" class="l"><a href="#161">161: </a><span class="php-comment">     *  @return Field[]|self Array of fields, or self if used as a setter.
</span></span><span id="162" class="l"><a href="#162">162: </a><span class="php-comment">     *  @see {@link Field} for field documentation.
</span></span><span id="163" class="l"><a href="#163">163: </a><span class="php-comment">     */</span>
</span><span id="164" class="l"><a href="#164">164: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> field ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="165" class="l"><a href="#165">165: </a>    {
</span><span id="166" class="l"><a href="#166">166: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="167" class="l"><a href="#167">167: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="168" class="l"><a href="#168">168: </a>        }
</span><span id="169" class="l"><a href="#169">169: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_fields, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="170" class="l"><a href="#170">170: </a>    }
</span><span id="171" class="l"><a href="#171">171: </a>
</span><span id="172" class="l"><a href="#172">172: </a>
</span><span id="173" class="l"><a href="#173">173: </a>    <span class="php-comment">/**
</span></span><span id="174" class="l"><a href="#174">174: </a><span class="php-comment">     * Get / set field instances.
</span></span><span id="175" class="l"><a href="#175">175: </a><span class="php-comment">     * 
</span></span><span id="176" class="l"><a href="#176">176: </a><span class="php-comment">     * An alias of {@link field}, for convenience.
</span></span><span id="177" class="l"><a href="#177">177: </a><span class="php-comment">     *  @param Field $_... Instances of the {@link Field} class, given as a single 
</span></span><span id="178" class="l"><a href="#178">178: </a><span class="php-comment">     *    instance of {@link Field}, an array of {@link Field} instances, or multiple
</span></span><span id="179" class="l"><a href="#179">179: </a><span class="php-comment">     *    {@link Field} instance parameters for the function.
</span></span><span id="180" class="l"><a href="#180">180: </a><span class="php-comment">     *  @return Field[]|self Array of fields, or self if used as a setter.
</span></span><span id="181" class="l"><a href="#181">181: </a><span class="php-comment">     *  @see {@link Field} for field documentation.
</span></span><span id="182" class="l"><a href="#182">182: </a><span class="php-comment">     */</span>
</span><span id="183" class="l"><a href="#183">183: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> fields ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="184" class="l"><a href="#184">184: </a>    {
</span><span id="185" class="l"><a href="#185">185: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> &amp;&amp; !<span class="php-keyword2">is_array</span>(<span class="php-var">$_</span>) ) {
</span><span id="186" class="l"><a href="#186">186: </a>            <span class="php-var">$_</span> = <span class="php-keyword2">func_get_args</span>();
</span><span id="187" class="l"><a href="#187">187: </a>        }
</span><span id="188" class="l"><a href="#188">188: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_fields, <span class="php-var">$_</span>, <span class="php-keyword1">true</span> );
</span><span id="189" class="l"><a href="#189">189: </a>    }
</span><span id="190" class="l"><a href="#190">190: </a>
</span><span id="191" class="l"><a href="#191">191: </a>
</span><span id="192" class="l"><a href="#192">192: </a>    <span class="php-comment">/**
</span></span><span id="193" class="l"><a href="#193">193: </a><span class="php-comment">     * Get / set get attribute.
</span></span><span id="194" class="l"><a href="#194">194: </a><span class="php-comment">     * 
</span></span><span id="195" class="l"><a href="#195">195: </a><span class="php-comment">     * When set to false no read operations will occur on the join tables.
</span></span><span id="196" class="l"><a href="#196">196: </a><span class="php-comment">     *  @param boolean $_ Value
</span></span><span id="197" class="l"><a href="#197">197: </a><span class="php-comment">     *  @return boolean|self Name
</span></span><span id="198" class="l"><a href="#198">198: </a><span class="php-comment">     */</span>
</span><span id="199" class="l"><a href="#199">199: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> get ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="200" class="l"><a href="#200">200: </a>    {
</span><span id="201" class="l"><a href="#201">201: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_get, <span class="php-var">$_</span> );
</span><span id="202" class="l"><a href="#202">202: </a>    }
</span><span id="203" class="l"><a href="#203">203: </a>
</span><span id="204" class="l"><a href="#204">204: </a>
</span><span id="205" class="l"><a href="#205">205: </a>    <span class="php-comment">/**
</span></span><span id="206" class="l"><a href="#206">206: </a><span class="php-comment">     * Get / set join properties.
</span></span><span id="207" class="l"><a href="#207">207: </a><span class="php-comment">     *
</span></span><span id="208" class="l"><a href="#208">208: </a><span class="php-comment">     * Define how the SQL will be performed, on what columns. There are
</span></span><span id="209" class="l"><a href="#209">209: </a><span class="php-comment">     * basically two types of join that are supported by Editor here, a direct
</span></span><span id="210" class="l"><a href="#210">210: </a><span class="php-comment">     * foreign key reference in the join table to the parent table's primary
</span></span><span id="211" class="l"><a href="#211">211: </a><span class="php-comment">     * key, or a link table that contains just primary keys for the parent and
</span></span><span id="212" class="l"><a href="#212">212: </a><span class="php-comment">     * child tables (this approach is usually used with a {@link type} of
</span></span><span id="213" class="l"><a href="#213">213: </a><span class="php-comment">     * 'array' since you can often have multiple links between the two tables,
</span></span><span id="214" class="l"><a href="#214">214: </a><span class="php-comment">     * while a direct foreign key reference will typically use a type of
</span></span><span id="215" class="l"><a href="#215">215: </a><span class="php-comment">     * 'object' (i.e. a single entry).
</span></span><span id="216" class="l"><a href="#216">216: </a><span class="php-comment">     *
</span></span><span id="217" class="l"><a href="#217">217: </a><span class="php-comment">     *  @param string|string[] $parent Parent table's primary key names. If used
</span></span><span id="218" class="l"><a href="#218">218: </a><span class="php-comment">     *    with a link table (i.e. third parameter to this method is given, then
</span></span><span id="219" class="l"><a href="#219">219: </a><span class="php-comment">     *    an array should be used, with the first element being the pkey's name
</span></span><span id="220" class="l"><a href="#220">220: </a><span class="php-comment">     *    in the parent table, and the second element being the key's name in
</span></span><span id="221" class="l"><a href="#221">221: </a><span class="php-comment">     *    the link table.
</span></span><span id="222" class="l"><a href="#222">222: </a><span class="php-comment">     *  @param string|string[] $child Child table's primary key names. If used
</span></span><span id="223" class="l"><a href="#223">223: </a><span class="php-comment">     *    with a link table (i.e. third parameter to this method is given, then
</span></span><span id="224" class="l"><a href="#224">224: </a><span class="php-comment">     *    an array should be used, with the first element being the pkey's name
</span></span><span id="225" class="l"><a href="#225">225: </a><span class="php-comment">     *    in the child table, and the second element being the key's name in the
</span></span><span id="226" class="l"><a href="#226">226: </a><span class="php-comment">     *    link table.
</span></span><span id="227" class="l"><a href="#227">227: </a><span class="php-comment">     *  @param string $table Join table name, if using a link table
</span></span><span id="228" class="l"><a href="#228">228: </a><span class="php-comment">     *  @returns Join This for chaining
</span></span><span id="229" class="l"><a href="#229">229: </a><span class="php-comment">     *  @deprecated 1.5 Please use the {@link link} method rather than this
</span></span><span id="230" class="l"><a href="#230">230: </a><span class="php-comment">     *      method now.
</span></span><span id="231" class="l"><a href="#231">231: </a><span class="php-comment">     */</span>
</span><span id="232" class="l"><a href="#232">232: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">join</span> ( <span class="php-var">$parent</span>=<span class="php-keyword1">null</span>, <span class="php-var">$child</span>=<span class="php-keyword1">null</span>, <span class="php-var">$table</span>=<span class="php-keyword1">null</span> )
</span><span id="233" class="l"><a href="#233">233: </a>    {
</span><span id="234" class="l"><a href="#234">234: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$parent</span> === <span class="php-keyword1">null</span> &amp;&amp; <span class="php-var">$child</span> === <span class="php-keyword1">null</span> ) {
</span><span id="235" class="l"><a href="#235">235: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_join;
</span><span id="236" class="l"><a href="#236">236: </a>        }
</span><span id="237" class="l"><a href="#237">237: </a>
</span><span id="238" class="l"><a href="#238">238: </a>        <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>] = <span class="php-var">$parent</span>;
</span><span id="239" class="l"><a href="#239">239: </a>        <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] = <span class="php-var">$child</span>;
</span><span id="240" class="l"><a href="#240">240: </a>        <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] = <span class="php-var">$table</span>;
</span><span id="241" class="l"><a href="#241">241: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="242" class="l"><a href="#242">242: </a>    }
</span><span id="243" class="l"><a href="#243">243: </a>
</span><span id="244" class="l"><a href="#244">244: </a>
</span><span id="245" class="l"><a href="#245">245: </a>    <span class="php-comment">/**
</span></span><span id="246" class="l"><a href="#246">246: </a><span class="php-comment">     * Create a join link between two tables. The order of the fields does not
</span></span><span id="247" class="l"><a href="#247">247: </a><span class="php-comment">     * matter, but each field must contain the table name as well as the field
</span></span><span id="248" class="l"><a href="#248">248: </a><span class="php-comment">     * name.
</span></span><span id="249" class="l"><a href="#249">249: </a><span class="php-comment">     * 
</span></span><span id="250" class="l"><a href="#250">250: </a><span class="php-comment">     * This method can be called a maximum of two times for an Mjoin instance:
</span></span><span id="251" class="l"><a href="#251">251: </a><span class="php-comment">     * 
</span></span><span id="252" class="l"><a href="#252">252: </a><span class="php-comment">     * * First time, creates a link between the Editor host table and a join
</span></span><span id="253" class="l"><a href="#253">253: </a><span class="php-comment">     *   table
</span></span><span id="254" class="l"><a href="#254">254: </a><span class="php-comment">     * * Second time creates the links required for a link table.
</span></span><span id="255" class="l"><a href="#255">255: </a><span class="php-comment">     * 
</span></span><span id="256" class="l"><a href="#256">256: </a><span class="php-comment">     * Please refer to the Editor Mjoin documentation for further details:
</span></span><span id="257" class="l"><a href="#257">257: </a><span class="php-comment">     * https://editor.datatables.net/manual/php
</span></span><span id="258" class="l"><a href="#258">258: </a><span class="php-comment">     *
</span></span><span id="259" class="l"><a href="#259">259: </a><span class="php-comment">     * @param  string $field1 Table and field name
</span></span><span id="260" class="l"><a href="#260">260: </a><span class="php-comment">     * @param  string $field2 Table and field name
</span></span><span id="261" class="l"><a href="#261">261: </a><span class="php-comment">     * @return Join Self for chaining
</span></span><span id="262" class="l"><a href="#262">262: </a><span class="php-comment">     * @throws \Exception Link limitations
</span></span><span id="263" class="l"><a href="#263">263: </a><span class="php-comment">     */</span>
</span><span id="264" class="l"><a href="#264">264: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">link</span> ( <span class="php-var">$field1</span>, <span class="php-var">$field2</span> )
</span><span id="265" class="l"><a href="#265">265: </a>    {
</span><span id="266" class="l"><a href="#266">266: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>(<span class="php-var">$field1</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> || <span class="php-keyword2">strpos</span>(<span class="php-var">$field2</span>, <span class="php-quote">'.'</span>) === <span class="php-keyword1">false</span> ) {
</span><span id="267" class="l"><a href="#267">267: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Link fields must contain both the table name and the column name&quot;</span>);
</span><span id="268" class="l"><a href="#268">268: </a>        }
</span><span id="269" class="l"><a href="#269">269: </a>
</span><span id="270" class="l"><a href="#270">270: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$this</span>-&gt;_links ) &gt;= <span class="php-num">4</span> ) {
</span><span id="271" class="l"><a href="#271">271: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;Link method cannot be called more than twice for a single instance&quot;</span>);
</span><span id="272" class="l"><a href="#272">272: </a>        }
</span><span id="273" class="l"><a href="#273">273: </a>
</span><span id="274" class="l"><a href="#274">274: </a>        <span class="php-var">$this</span>-&gt;_links[] = <span class="php-var">$field1</span>;
</span><span id="275" class="l"><a href="#275">275: </a>        <span class="php-var">$this</span>-&gt;_links[] = <span class="php-var">$field2</span>;
</span><span id="276" class="l"><a href="#276">276: </a>
</span><span id="277" class="l"><a href="#277">277: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="278" class="l"><a href="#278">278: </a>    }
</span><span id="279" class="l"><a href="#279">279: </a>
</span><span id="280" class="l"><a href="#280">280: </a>
</span><span id="281" class="l"><a href="#281">281: </a>    <span class="php-comment">/**
</span></span><span id="282" class="l"><a href="#282">282: </a><span class="php-comment">     * Specify the property that the data will be sorted by.
</span></span><span id="283" class="l"><a href="#283">283: </a><span class="php-comment">     *
</span></span><span id="284" class="l"><a href="#284">284: </a><span class="php-comment">     * @param  string $order SQL column name to order the data by
</span></span><span id="285" class="l"><a href="#285">285: </a><span class="php-comment">     * @return Join Self for chaining
</span></span><span id="286" class="l"><a href="#286">286: </a><span class="php-comment">     */</span>
</span><span id="287" class="l"><a href="#287">287: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> order ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="288" class="l"><a href="#288">288: </a>    {
</span><span id="289" class="l"><a href="#289">289: </a>        <span class="php-comment">// How this works is by setting the SQL order by clause, and since the</span>
</span><span id="290" class="l"><a href="#290">290: </a>        <span class="php-comment">// join that is done in PHP is always done sequentially, the order is</span>
</span><span id="291" class="l"><a href="#291">291: </a>        <span class="php-comment">// retained.</span>
</span><span id="292" class="l"><a href="#292">292: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_customOrder, <span class="php-var">$_</span> );
</span><span id="293" class="l"><a href="#293">293: </a>    }
</span><span id="294" class="l"><a href="#294">294: </a>
</span><span id="295" class="l"><a href="#295">295: </a>
</span><span id="296" class="l"><a href="#296">296: </a>    <span class="php-comment">/**
</span></span><span id="297" class="l"><a href="#297">297: </a><span class="php-comment">     * Get / set name.
</span></span><span id="298" class="l"><a href="#298">298: </a><span class="php-comment">     * 
</span></span><span id="299" class="l"><a href="#299">299: </a><span class="php-comment">     * The `name` of the Join is the JSON property key that is used when 
</span></span><span id="300" class="l"><a href="#300">300: </a><span class="php-comment">     * 'getting' the data, and the HTTP property key (in a JSON style) when
</span></span><span id="301" class="l"><a href="#301">301: </a><span class="php-comment">     * 'setting' data. Typically the name of the db table will be used here,
</span></span><span id="302" class="l"><a href="#302">302: </a><span class="php-comment">     * but this method allows that to be overridden.
</span></span><span id="303" class="l"><a href="#303">303: </a><span class="php-comment">     *  @param string $_ Field name
</span></span><span id="304" class="l"><a href="#304">304: </a><span class="php-comment">     *  @return String|self Name
</span></span><span id="305" class="l"><a href="#305">305: </a><span class="php-comment">     */</span>
</span><span id="306" class="l"><a href="#306">306: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> name ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="307" class="l"><a href="#307">307: </a>    {
</span><span id="308" class="l"><a href="#308">308: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_name, <span class="php-var">$_</span> );
</span><span id="309" class="l"><a href="#309">309: </a>    }
</span><span id="310" class="l"><a href="#310">310: </a>
</span><span id="311" class="l"><a href="#311">311: </a>
</span><span id="312" class="l"><a href="#312">312: </a>    <span class="php-comment">/**
</span></span><span id="313" class="l"><a href="#313">313: </a><span class="php-comment">     * Get / set set attribute.
</span></span><span id="314" class="l"><a href="#314">314: </a><span class="php-comment">     * 
</span></span><span id="315" class="l"><a href="#315">315: </a><span class="php-comment">     * When set to false no write operations will occur on the join tables.
</span></span><span id="316" class="l"><a href="#316">316: </a><span class="php-comment">     * This can be useful when you want to display information which is joined,
</span></span><span id="317" class="l"><a href="#317">317: </a><span class="php-comment">     * but want to only perform write operations on the parent table.
</span></span><span id="318" class="l"><a href="#318">318: </a><span class="php-comment">     *  @param boolean $_ Value
</span></span><span id="319" class="l"><a href="#319">319: </a><span class="php-comment">     *  @return boolean|self Name
</span></span><span id="320" class="l"><a href="#320">320: </a><span class="php-comment">     */</span>
</span><span id="321" class="l"><a href="#321">321: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> set ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="322" class="l"><a href="#322">322: </a>    {
</span><span id="323" class="l"><a href="#323">323: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_set, <span class="php-var">$_</span> );
</span><span id="324" class="l"><a href="#324">324: </a>    }
</span><span id="325" class="l"><a href="#325">325: </a>
</span><span id="326" class="l"><a href="#326">326: </a>
</span><span id="327" class="l"><a href="#327">327: </a>    <span class="php-comment">/**
</span></span><span id="328" class="l"><a href="#328">328: </a><span class="php-comment">     * Get / set join table name.
</span></span><span id="329" class="l"><a href="#329">329: </a><span class="php-comment">     *
</span></span><span id="330" class="l"><a href="#330">330: </a><span class="php-comment">     * Please note that this will also set the {@link name} used by the Join
</span></span><span id="331" class="l"><a href="#331">331: </a><span class="php-comment">     * as well. This is for convenience as the JSON output / HTTP input will
</span></span><span id="332" class="l"><a href="#332">332: </a><span class="php-comment">     * typically use the same name as the database name. If you want to set a
</span></span><span id="333" class="l"><a href="#333">333: </a><span class="php-comment">     * custom name, the {@link name} method must be called ***after*** this one.
</span></span><span id="334" class="l"><a href="#334">334: </a><span class="php-comment">     *  @param string $_ Name of the table to read the join data from
</span></span><span id="335" class="l"><a href="#335">335: </a><span class="php-comment">     *  @return String|self Name of the join table, or self if used as a setter.
</span></span><span id="336" class="l"><a href="#336">336: </a><span class="php-comment">     */</span>
</span><span id="337" class="l"><a href="#337">337: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> table ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="338" class="l"><a href="#338">338: </a>    {
</span><span id="339" class="l"><a href="#339">339: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$_</span> !== <span class="php-keyword1">null</span> ) {
</span><span id="340" class="l"><a href="#340">340: </a>            <span class="php-var">$this</span>-&gt;_name = <span class="php-var">$_</span>;
</span><span id="341" class="l"><a href="#341">341: </a>        }
</span><span id="342" class="l"><a href="#342">342: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_table, <span class="php-var">$_</span> );
</span><span id="343" class="l"><a href="#343">343: </a>    }
</span><span id="344" class="l"><a href="#344">344: </a>
</span><span id="345" class="l"><a href="#345">345: </a>
</span><span id="346" class="l"><a href="#346">346: </a>    <span class="php-comment">/**
</span></span><span id="347" class="l"><a href="#347">347: </a><span class="php-comment">     * Get / set the join type.
</span></span><span id="348" class="l"><a href="#348">348: </a><span class="php-comment">     * 
</span></span><span id="349" class="l"><a href="#349">349: </a><span class="php-comment">     * The join type allows the data that is returned from the join to be given
</span></span><span id="350" class="l"><a href="#350">350: </a><span class="php-comment">     * as an array (i.e. working with multiple possibly results for each record from
</span></span><span id="351" class="l"><a href="#351">351: </a><span class="php-comment">     * the parent table), or as an object (i.e. working which one and only one result
</span></span><span id="352" class="l"><a href="#352">352: </a><span class="php-comment">     * for each record form the parent table).
</span></span><span id="353" class="l"><a href="#353">353: </a><span class="php-comment">     *  @param string $_ Join type ('object') or an array of 
</span></span><span id="354" class="l"><a href="#354">354: </a><span class="php-comment">     *    results ('array') for the join.
</span></span><span id="355" class="l"><a href="#355">355: </a><span class="php-comment">     *  @return String|self Join type, or self if used as a setter.
</span></span><span id="356" class="l"><a href="#356">356: </a><span class="php-comment">     */</span>
</span><span id="357" class="l"><a href="#357">357: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> type ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="358" class="l"><a href="#358">358: </a>    {
</span><span id="359" class="l"><a href="#359">359: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_type, <span class="php-var">$_</span> );
</span><span id="360" class="l"><a href="#360">360: </a>    }
</span><span id="361" class="l"><a href="#361">361: </a>
</span><span id="362" class="l"><a href="#362">362: </a>
</span><span id="363" class="l"><a href="#363">363: </a>    <span class="php-comment">/**
</span></span><span id="364" class="l"><a href="#364">364: </a><span class="php-comment">     * Where condition to add to the query used to get data from the database.
</span></span><span id="365" class="l"><a href="#365">365: </a><span class="php-comment">     * Note that this is applied to the child table.
</span></span><span id="366" class="l"><a href="#366">366: </a><span class="php-comment">     * 
</span></span><span id="367" class="l"><a href="#367">367: </a><span class="php-comment">     * Can be used in two different ways:
</span></span><span id="368" class="l"><a href="#368">368: </a><span class="php-comment">     * 
</span></span><span id="369" class="l"><a href="#369">369: </a><span class="php-comment">     * * Simple case: `where( field, value, operator )`
</span></span><span id="370" class="l"><a href="#370">370: </a><span class="php-comment">     * * Complex: `where( fn )`
</span></span><span id="371" class="l"><a href="#371">371: </a><span class="php-comment">     *
</span></span><span id="372" class="l"><a href="#372">372: </a><span class="php-comment">     *  @param string|callable $key   Single field name or a closure function
</span></span><span id="373" class="l"><a href="#373">373: </a><span class="php-comment">     *  @param string|string[] $value Single field value, or an array of values.
</span></span><span id="374" class="l"><a href="#374">374: </a><span class="php-comment">     *  @param string          $op    Condition operator: &lt;, &gt;, = etc
</span></span><span id="375" class="l"><a href="#375">375: </a><span class="php-comment">     *  @return string[]|self Where condition array, or self if used as a setter.
</span></span><span id="376" class="l"><a href="#376">376: </a><span class="php-comment">     */</span>
</span><span id="377" class="l"><a href="#377">377: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> where ( <span class="php-var">$key</span>=<span class="php-keyword1">null</span>, <span class="php-var">$value</span>=<span class="php-keyword1">null</span>, <span class="php-var">$op</span>=<span class="php-quote">'='</span> )
</span><span id="378" class="l"><a href="#378">378: </a>    {
</span><span id="379" class="l"><a href="#379">379: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$key</span> === <span class="php-keyword1">null</span> ) {
</span><span id="380" class="l"><a href="#380">380: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_where;
</span><span id="381" class="l"><a href="#381">381: </a>        }
</span><span id="382" class="l"><a href="#382">382: </a>
</span><span id="383" class="l"><a href="#383">383: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>(<span class="php-var">$key</span>) &amp;&amp; <span class="php-keyword2">is_object</span>(<span class="php-var">$key</span>) ) {
</span><span id="384" class="l"><a href="#384">384: </a>            <span class="php-var">$this</span>-&gt;_where[] = <span class="php-var">$key</span>;
</span><span id="385" class="l"><a href="#385">385: </a>        }
</span><span id="386" class="l"><a href="#386">386: </a>        <span class="php-keyword1">else</span> {
</span><span id="387" class="l"><a href="#387">387: </a>            <span class="php-var">$this</span>-&gt;_where[] = <span class="php-keyword1">array</span>(
</span><span id="388" class="l"><a href="#388">388: </a>                <span class="php-quote">&quot;key&quot;</span>   =&gt; <span class="php-var">$key</span>,
</span><span id="389" class="l"><a href="#389">389: </a>                <span class="php-quote">&quot;value&quot;</span> =&gt; <span class="php-var">$value</span>,
</span><span id="390" class="l"><a href="#390">390: </a>                <span class="php-quote">&quot;op&quot;</span>    =&gt; <span class="php-var">$op</span>
</span><span id="391" class="l"><a href="#391">391: </a>            );
</span><span id="392" class="l"><a href="#392">392: </a>        }
</span><span id="393" class="l"><a href="#393">393: </a>
</span><span id="394" class="l"><a href="#394">394: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="395" class="l"><a href="#395">395: </a>    }
</span><span id="396" class="l"><a href="#396">396: </a>
</span><span id="397" class="l"><a href="#397">397: </a>
</span><span id="398" class="l"><a href="#398">398: </a>    <span class="php-comment">/**
</span></span><span id="399" class="l"><a href="#399">399: </a><span class="php-comment">     * Get / set if the WHERE conditions should be included in the create and
</span></span><span id="400" class="l"><a href="#400">400: </a><span class="php-comment">     * edit actions.
</span></span><span id="401" class="l"><a href="#401">401: </a><span class="php-comment">     * 
</span></span><span id="402" class="l"><a href="#402">402: </a><span class="php-comment">     * This means that the fields which have been used as part of the 'get'
</span></span><span id="403" class="l"><a href="#403">403: </a><span class="php-comment">     * WHERE condition (using the `where()` method) will be set as the values
</span></span><span id="404" class="l"><a href="#404">404: </a><span class="php-comment">     * given.
</span></span><span id="405" class="l"><a href="#405">405: </a><span class="php-comment">     *
</span></span><span id="406" class="l"><a href="#406">406: </a><span class="php-comment">     * This is default false (i.e. they are not included).
</span></span><span id="407" class="l"><a href="#407">407: </a><span class="php-comment">     *
</span></span><span id="408" class="l"><a href="#408">408: </a><span class="php-comment">     *  @param boolean $_ Include (`true`), or not (`false`)
</span></span><span id="409" class="l"><a href="#409">409: </a><span class="php-comment">     *  @return boolean Current value
</span></span><span id="410" class="l"><a href="#410">410: </a><span class="php-comment">     */</span>
</span><span id="411" class="l"><a href="#411">411: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> whereSet ( <span class="php-var">$_</span>=<span class="php-keyword1">null</span> )
</span><span id="412" class="l"><a href="#412">412: </a>    {
</span><span id="413" class="l"><a href="#413">413: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_getSet( <span class="php-var">$this</span>-&gt;_whereSet, <span class="php-var">$_</span> );
</span><span id="414" class="l"><a href="#414">414: </a>    }
</span><span id="415" class="l"><a href="#415">415: </a>
</span><span id="416" class="l"><a href="#416">416: </a>
</span><span id="417" class="l"><a href="#417">417: </a>
</span><span id="418" class="l"><a href="#418">418: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="419" class="l"><a href="#419">419: </a><span class="php-comment">     * Internal methods
</span></span><span id="420" class="l"><a href="#420">420: </a><span class="php-comment">     */</span>
</span><span id="421" class="l"><a href="#421">421: </a>
</span><span id="422" class="l"><a href="#422">422: </a>    <span class="php-comment">/**
</span></span><span id="423" class="l"><a href="#423">423: </a><span class="php-comment">     * Get data
</span></span><span id="424" class="l"><a href="#424">424: </a><span class="php-comment">     *  @param Editor $editor Host Editor instance
</span></span><span id="425" class="l"><a href="#425">425: </a><span class="php-comment">     *  @param string[] $data Data from the parent table's get and were we need
</span></span><span id="426" class="l"><a href="#426">426: </a><span class="php-comment">     *    to add out output.
</span></span><span id="427" class="l"><a href="#427">427: </a><span class="php-comment">     *  @param array $options options array for fields
</span></span><span id="428" class="l"><a href="#428">428: </a><span class="php-comment">     *  @internal
</span></span><span id="429" class="l"><a href="#429">429: </a><span class="php-comment">     */</span>
</span><span id="430" class="l"><a href="#430">430: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> data( <span class="php-var">$editor</span>, &amp;<span class="php-var">$data</span>, &amp;<span class="php-var">$options</span> )
</span><span id="431" class="l"><a href="#431">431: </a>    {
</span><span id="432" class="l"><a href="#432">432: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_get ) {
</span><span id="433" class="l"><a href="#433">433: </a>            <span class="php-keyword1">return</span>;
</span><span id="434" class="l"><a href="#434">434: </a>        }
</span><span id="435" class="l"><a href="#435">435: </a>
</span><span id="436" class="l"><a href="#436">436: </a>        <span class="php-var">$this</span>-&gt;_prep( <span class="php-var">$editor</span> );
</span><span id="437" class="l"><a href="#437">437: </a>        <span class="php-var">$db</span>       = <span class="php-var">$editor</span>-&gt;db();
</span><span id="438" class="l"><a href="#438">438: </a>        <span class="php-var">$dteTable</span> = <span class="php-var">$editor</span>-&gt;table();
</span><span id="439" class="l"><a href="#439">439: </a>        <span class="php-var">$pkey</span>     = <span class="php-var">$editor</span>-&gt;pkey();
</span><span id="440" class="l"><a href="#440">440: </a>        <span class="php-var">$idPrefix</span> = <span class="php-var">$editor</span>-&gt;idPrefix();
</span><span id="441" class="l"><a href="#441">441: </a>
</span><span id="442" class="l"><a href="#442">442: </a>        <span class="php-var">$dteTable</span> = <span class="php-var">$dteTable</span>[<span class="php-num">0</span>];
</span><span id="443" class="l"><a href="#443">443: </a>        <span class="php-var">$dteTableLocal</span> = <span class="php-var">$this</span>-&gt;_aliasParentTable ? <span class="php-comment">// Can be aliased to allow a self join</span>
</span><span id="444" class="l"><a href="#444">444: </a>            <span class="php-var">$this</span>-&gt;_aliasParentTable :
</span><span id="445" class="l"><a href="#445">445: </a>            <span class="php-var">$dteTable</span>;
</span><span id="446" class="l"><a href="#446">446: </a>        <span class="php-var">$joinField</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>]) ?
</span><span id="447" class="l"><a href="#447">447: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">0</span>] :
</span><span id="448" class="l"><a href="#448">448: </a>            <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>];
</span><span id="449" class="l"><a href="#449">449: </a>
</span><span id="450" class="l"><a href="#450">450: </a>        <span class="php-comment">// This is something that will likely come in a future version, but it</span>
</span><span id="451" class="l"><a href="#451">451: </a>        <span class="php-comment">// is a relatively low use feature. Please get in touch if this is</span>
</span><span id="452" class="l"><a href="#452">452: </a>        <span class="php-comment">// something you require.</span>
</span><span id="453" class="l"><a href="#453">453: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$pkey</span> ) &gt; <span class="php-num">1</span> ) {
</span><span id="454" class="l"><a href="#454">454: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(<span class="php-quote">&quot;MJoin is not currently supported with a compound primary key for the main table&quot;</span>, <span class="php-num">1</span>);
</span><span id="455" class="l"><a href="#455">455: </a>        }
</span><span id="456" class="l"><a href="#456">456: </a>
</span><span id="457" class="l"><a href="#457">457: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$data</span>) &gt; <span class="php-num">0</span> ) {
</span><span id="458" class="l"><a href="#458">458: </a>            <span class="php-var">$pkey</span> = <span class="php-var">$pkey</span>[<span class="php-num">0</span>];
</span><span id="459" class="l"><a href="#459">459: </a>            <span class="php-var">$pkeyIsJoin</span> = <span class="php-var">$pkey</span> === <span class="php-var">$joinField</span> || <span class="php-var">$pkey</span> === <span class="php-var">$dteTable</span>.<span class="php-quote">'.'</span>.<span class="php-var">$joinField</span>;
</span><span id="460" class="l"><a href="#460">460: </a>
</span><span id="461" class="l"><a href="#461">461: </a>            <span class="php-comment">// Sanity check that table selector fields are read only, and have an name without</span>
</span><span id="462" class="l"><a href="#462">462: </a>            <span class="php-comment">// a dot (for DataTables mData to be able to read it)</span>
</span><span id="463" class="l"><a href="#463">463: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="464" class="l"><a href="#464">464: </a>                <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="465" class="l"><a href="#465">465: </a>
</span><span id="466" class="l"><a href="#466">466: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$field</span>-&gt;dbField() , <span class="php-quote">&quot;.&quot;</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="467" class="l"><a href="#467">467: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;set() !== Field::SET_NONE &amp;&amp; <span class="php-var">$this</span>-&gt;_set ) {
</span><span id="468" class="l"><a href="#468">468: </a>                        <span class="php-keyword1">echo</span> <span class="php-keyword2">json_encode</span>( <span class="php-keyword1">array</span>(
</span><span id="469" class="l"><a href="#469">469: </a>                            <span class="php-quote">&quot;sError&quot;</span> =&gt; <span class="php-quote">&quot;Table selected fields (i.e. '{table}.{column}') in `Join` &quot;</span>.
</span><span id="470" class="l"><a href="#470">470: </a>                                <span class="php-quote">&quot;must be read only. Use `set(false)` for the field to disable writing.&quot;</span>
</span><span id="471" class="l"><a href="#471">471: </a>                        ) );
</span><span id="472" class="l"><a href="#472">472: </a>                        <span class="php-keyword1">exit</span>(<span class="php-num">0</span>);
</span><span id="473" class="l"><a href="#473">473: </a>                    }
</span><span id="474" class="l"><a href="#474">474: </a>
</span><span id="475" class="l"><a href="#475">475: </a>                    <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$field</span>-&gt;name() , <span class="php-quote">&quot;.&quot;</span> ) !== <span class="php-keyword1">false</span> ) {
</span><span id="476" class="l"><a href="#476">476: </a>                        <span class="php-keyword1">echo</span> <span class="php-keyword2">json_encode</span>( <span class="php-keyword1">array</span>(
</span><span id="477" class="l"><a href="#477">477: </a>                            <span class="php-quote">&quot;sError&quot;</span> =&gt; <span class="php-quote">&quot;Table selected fields (i.e. '{table}.{column}') in `Join` &quot;</span>.
</span><span id="478" class="l"><a href="#478">478: </a>                                <span class="php-quote">&quot;must have a name alias which does not contain a period ('.'). Use &quot;</span>.
</span><span id="479" class="l"><a href="#479">479: </a>                                <span class="php-quote">&quot;name('---') to set a name for the field&quot;</span>
</span><span id="480" class="l"><a href="#480">480: </a>                        ) );
</span><span id="481" class="l"><a href="#481">481: </a>                        <span class="php-keyword1">exit</span>(<span class="php-num">0</span>);
</span><span id="482" class="l"><a href="#482">482: </a>                    }
</span><span id="483" class="l"><a href="#483">483: </a>                }
</span><span id="484" class="l"><a href="#484">484: </a>            }
</span><span id="485" class="l"><a href="#485">485: </a>
</span><span id="486" class="l"><a href="#486">486: </a>            <span class="php-comment">// Set up the JOIN query</span>
</span><span id="487" class="l"><a href="#487">487: </a>            <span class="php-var">$stmt</span> = <span class="php-var">$db</span>
</span><span id="488" class="l"><a href="#488">488: </a>                -&gt;query( <span class="php-quote">'select'</span> )
</span><span id="489" class="l"><a href="#489">489: </a>                -&gt;distinct( <span class="php-keyword1">true</span> )
</span><span id="490" class="l"><a href="#490">490: </a>                -&gt;get( <span class="php-var">$dteTableLocal</span>.<span class="php-quote">'.'</span>.<span class="php-var">$joinField</span>.<span class="php-quote">' as dteditor_pkey'</span> )
</span><span id="491" class="l"><a href="#491">491: </a>                -&gt;get( <span class="php-var">$this</span>-&gt;_fields(<span class="php-quote">'get'</span>) )
</span><span id="492" class="l"><a href="#492">492: </a>                -&gt;table( <span class="php-var">$dteTable</span> .<span class="php-quote">' as '</span>. <span class="php-var">$dteTableLocal</span> );
</span><span id="493" class="l"><a href="#493">493: </a>
</span><span id="494" class="l"><a href="#494">494: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;order() ) {
</span><span id="495" class="l"><a href="#495">495: </a>                <span class="php-var">$stmt</span>-&gt;order( <span class="php-var">$this</span>-&gt;order() );
</span><span id="496" class="l"><a href="#496">496: </a>            }
</span><span id="497" class="l"><a href="#497">497: </a>
</span><span id="498" class="l"><a href="#498">498: </a>            <span class="php-var">$this</span>-&gt;_apply_where( <span class="php-var">$stmt</span> );
</span><span id="499" class="l"><a href="#499">499: </a>
</span><span id="500" class="l"><a href="#500">500: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>]) ) {
</span><span id="501" class="l"><a href="#501">501: </a>                <span class="php-comment">// Working with a link table</span>
</span><span id="502" class="l"><a href="#502">502: </a>                <span class="php-var">$stmt</span>
</span><span id="503" class="l"><a href="#503">503: </a>                    -&gt;<span class="php-keyword2">join</span>(
</span><span id="504" class="l"><a href="#504">504: </a>                        <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>],
</span><span id="505" class="l"><a href="#505">505: </a>                        <span class="php-var">$dteTableLocal</span>.<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">0</span>] .<span class="php-quote">' = '</span>. <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>].<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">1</span>]
</span><span id="506" class="l"><a href="#506">506: </a>                    )
</span><span id="507" class="l"><a href="#507">507: </a>                    -&gt;<span class="php-keyword2">join</span>(
</span><span id="508" class="l"><a href="#508">508: </a>                        <span class="php-var">$this</span>-&gt;_table,
</span><span id="509" class="l"><a href="#509">509: </a>                        <span class="php-var">$this</span>-&gt;_table.<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">0</span>] .<span class="php-quote">' = '</span>. <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>].<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">1</span>]
</span><span id="510" class="l"><a href="#510">510: </a>                    );
</span><span id="511" class="l"><a href="#511">511: </a>            }
</span><span id="512" class="l"><a href="#512">512: </a>            <span class="php-keyword1">else</span> {
</span><span id="513" class="l"><a href="#513">513: </a>                <span class="php-comment">// No link table in the middle</span>
</span><span id="514" class="l"><a href="#514">514: </a>                <span class="php-var">$stmt</span>
</span><span id="515" class="l"><a href="#515">515: </a>                    -&gt;<span class="php-keyword2">join</span>(
</span><span id="516" class="l"><a href="#516">516: </a>                        <span class="php-var">$this</span>-&gt;_table,
</span><span id="517" class="l"><a href="#517">517: </a>                        <span class="php-var">$this</span>-&gt;_table.<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] .<span class="php-quote">' = '</span>. <span class="php-var">$dteTableLocal</span>.<span class="php-quote">'.'</span>.<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>]
</span><span id="518" class="l"><a href="#518">518: </a>                    );
</span><span id="519" class="l"><a href="#519">519: </a>            }
</span><span id="520" class="l"><a href="#520">520: </a>
</span><span id="521" class="l"><a href="#521">521: </a>            <span class="php-comment">// Check that the joining field is available.  The joining key can</span>
</span><span id="522" class="l"><a href="#522">522: </a>            <span class="php-comment">// come from the Editor instance's primary key, or any other field,</span>
</span><span id="523" class="l"><a href="#523">523: </a>            <span class="php-comment">// including a nested value (from a left join). If the instance's </span>
</span><span id="524" class="l"><a href="#524">524: </a>            <span class="php-comment">// pkey, then we've got that in the DT_RowId parameter, so we can</span>
</span><span id="525" class="l"><a href="#525">525: </a>            <span class="php-comment">// use that. Otherwise, the key must be in the field list.</span>
</span><span id="526" class="l"><a href="#526">526: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_propExists( <span class="php-var">$dteTable</span>.<span class="php-quote">'.'</span>.<span class="php-var">$joinField</span>, <span class="php-var">$data</span>[<span class="php-num">0</span>] ) ) {
</span><span id="527" class="l"><a href="#527">527: </a>                <span class="php-var">$readField</span> = <span class="php-var">$dteTable</span>.<span class="php-quote">'.'</span>.<span class="php-var">$joinField</span>;
</span><span id="528" class="l"><a href="#528">528: </a>            }
</span><span id="529" class="l"><a href="#529">529: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_propExists( <span class="php-var">$joinField</span>, <span class="php-var">$data</span>[<span class="php-num">0</span>] ) ) {
</span><span id="530" class="l"><a href="#530">530: </a>                <span class="php-var">$readField</span> = <span class="php-var">$joinField</span>;
</span><span id="531" class="l"><a href="#531">531: </a>            }
</span><span id="532" class="l"><a href="#532">532: </a>            <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( ! <span class="php-var">$pkeyIsJoin</span> ) {
</span><span id="533" class="l"><a href="#533">533: </a>                <span class="php-keyword1">echo</span> <span class="php-keyword2">json_encode</span>( <span class="php-keyword1">array</span>(
</span><span id="534" class="l"><a href="#534">534: </a>                    <span class="php-quote">&quot;sError&quot;</span> =&gt; <span class="php-quote">&quot;Join was performed on the field '</span><span class="php-var">{$joinField}</span><span class="php-quote">' which was not &quot;</span>
</span><span id="535" class="l"><a href="#535">535: </a>                        .<span class="php-quote">&quot;included in the Editor field list. The join field must be included &quot;</span>
</span><span id="536" class="l"><a href="#536">536: </a>                        .<span class="php-quote">&quot;as a regular field in the Editor instance.&quot;</span>
</span><span id="537" class="l"><a href="#537">537: </a>                ) );
</span><span id="538" class="l"><a href="#538">538: </a>                <span class="php-keyword1">exit</span>(<span class="php-num">0</span>);
</span><span id="539" class="l"><a href="#539">539: </a>            }
</span><span id="540" class="l"><a href="#540">540: </a>
</span><span id="541" class="l"><a href="#541">541: </a>            <span class="php-comment">// Get list of pkey values and apply as a WHERE IN condition</span>
</span><span id="542" class="l"><a href="#542">542: </a>            <span class="php-comment">// This is primarily useful in server-side processing mode and when filtering</span>
</span><span id="543" class="l"><a href="#543">543: </a>            <span class="php-comment">// the table as it means only a sub-set will be selected</span>
</span><span id="544" class="l"><a href="#544">544: </a>            <span class="php-comment">// This is only applied for &quot;sensible&quot; data sets. It will just complicate</span>
</span><span id="545" class="l"><a href="#545">545: </a>            <span class="php-comment">// matters for really large data sets:</span>
</span><span id="546" class="l"><a href="#546">546: </a>            <span class="php-comment">// https://stackoverflow.com/questions/21178390/in-clause-limitation-in-sql-server</span>
</span><span id="547" class="l"><a href="#547">547: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>(<span class="php-var">$data</span>) &lt; <span class="php-num">1000</span> ) {
</span><span id="548" class="l"><a href="#548">548: </a>                <span class="php-var">$whereIn</span> = <span class="php-keyword1">array</span>();
</span><span id="549" class="l"><a href="#549">549: </a>
</span><span id="550" class="l"><a href="#550">550: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$data</span>) ; <span class="php-var">$i</span>++ ) {
</span><span id="551" class="l"><a href="#551">551: </a>                    <span class="php-var">$whereIn</span>[] = <span class="php-var">$pkeyIsJoin</span> ? 
</span><span id="552" class="l"><a href="#552">552: </a>                        <span class="php-keyword2">str_replace</span>( <span class="php-var">$idPrefix</span>, <span class="php-quote">''</span>, <span class="php-var">$data</span>[<span class="php-var">$i</span>][<span class="php-quote">'DT_RowId'</span>] ) :
</span><span id="553" class="l"><a href="#553">553: </a>                        <span class="php-var">$this</span>-&gt;_readProp( <span class="php-var">$readField</span>, <span class="php-var">$data</span>[<span class="php-var">$i</span>] );
</span><span id="554" class="l"><a href="#554">554: </a>                }
</span><span id="555" class="l"><a href="#555">555: </a>
</span><span id="556" class="l"><a href="#556">556: </a>                <span class="php-var">$stmt</span>-&gt;where_in( <span class="php-var">$dteTableLocal</span>.<span class="php-quote">'.'</span>.<span class="php-var">$joinField</span>, <span class="php-var">$whereIn</span> );
</span><span id="557" class="l"><a href="#557">557: </a>            }
</span><span id="558" class="l"><a href="#558">558: </a>
</span><span id="559" class="l"><a href="#559">559: </a>            <span class="php-comment">// Go!</span>
</span><span id="560" class="l"><a href="#560">560: </a>            <span class="php-var">$res</span> = <span class="php-var">$stmt</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="561" class="l"><a href="#561">561: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-var">$res</span> ) {
</span><span id="562" class="l"><a href="#562">562: </a>                <span class="php-keyword1">return</span>;
</span><span id="563" class="l"><a href="#563">563: </a>            }
</span><span id="564" class="l"><a href="#564">564: </a>
</span><span id="565" class="l"><a href="#565">565: </a>            <span class="php-comment">// Map to primary key for fast lookup</span>
</span><span id="566" class="l"><a href="#566">566: </a>            <span class="php-var">$join</span> = <span class="php-keyword1">array</span>();
</span><span id="567" class="l"><a href="#567">567: </a>            <span class="php-keyword1">while</span> ( <span class="php-var">$row</span>=<span class="php-var">$res</span>-&gt;fetch() ) {
</span><span id="568" class="l"><a href="#568">568: </a>                <span class="php-var">$inner</span> = <span class="php-keyword1">array</span>();
</span><span id="569" class="l"><a href="#569">569: </a>
</span><span id="570" class="l"><a href="#570">570: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$j</span>=<span class="php-num">0</span> ; <span class="php-var">$j</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$j</span>++ ) {
</span><span id="571" class="l"><a href="#571">571: </a>                    <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$j</span>];
</span><span id="572" class="l"><a href="#572">572: </a>                    <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply(<span class="php-quote">'get'</span>) ) {
</span><span id="573" class="l"><a href="#573">573: </a>                        <span class="php-var">$inner</span>[ <span class="php-var">$field</span>-&gt;name() ] = <span class="php-var">$field</span>-&gt;val(<span class="php-quote">'get'</span>, <span class="php-var">$row</span>);
</span><span id="574" class="l"><a href="#574">574: </a>                    }
</span><span id="575" class="l"><a href="#575">575: </a>                }
</span><span id="576" class="l"><a href="#576">576: </a>
</span><span id="577" class="l"><a href="#577">577: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span> ) {
</span><span id="578" class="l"><a href="#578">578: </a>                    <span class="php-var">$join</span>[ <span class="php-var">$row</span>[<span class="php-quote">'dteditor_pkey'</span>] ] = <span class="php-var">$inner</span>;
</span><span id="579" class="l"><a href="#579">579: </a>                }
</span><span id="580" class="l"><a href="#580">580: </a>                <span class="php-keyword1">else</span> {
</span><span id="581" class="l"><a href="#581">581: </a>                    <span class="php-keyword1">if</span> ( !<span class="php-keyword1">isset</span>( <span class="php-var">$join</span>[ <span class="php-var">$row</span>[<span class="php-quote">'dteditor_pkey'</span>] ] ) ) {
</span><span id="582" class="l"><a href="#582">582: </a>                        <span class="php-var">$join</span>[ <span class="php-var">$row</span>[<span class="php-quote">'dteditor_pkey'</span>] ] = <span class="php-keyword1">array</span>();
</span><span id="583" class="l"><a href="#583">583: </a>                    }
</span><span id="584" class="l"><a href="#584">584: </a>                    <span class="php-var">$join</span>[ <span class="php-var">$row</span>[<span class="php-quote">'dteditor_pkey'</span>] ][] = <span class="php-var">$inner</span>;
</span><span id="585" class="l"><a href="#585">585: </a>                }
</span><span id="586" class="l"><a href="#586">586: </a>            }
</span><span id="587" class="l"><a href="#587">587: </a>
</span><span id="588" class="l"><a href="#588">588: </a>            <span class="php-comment">// Loop over the data and do a join based on the data available</span>
</span><span id="589" class="l"><a href="#589">589: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$data</span>) ; <span class="php-var">$i</span>++ ) {
</span><span id="590" class="l"><a href="#590">590: </a>                <span class="php-var">$rowPKey</span> = <span class="php-var">$pkeyIsJoin</span> ? 
</span><span id="591" class="l"><a href="#591">591: </a>                    <span class="php-keyword2">str_replace</span>( <span class="php-var">$idPrefix</span>, <span class="php-quote">''</span>, <span class="php-var">$data</span>[<span class="php-var">$i</span>][<span class="php-quote">'DT_RowId'</span>] ) :
</span><span id="592" class="l"><a href="#592">592: </a>                    <span class="php-var">$this</span>-&gt;_readProp( <span class="php-var">$readField</span>, <span class="php-var">$data</span>[<span class="php-var">$i</span>] );
</span><span id="593" class="l"><a href="#593">593: </a>
</span><span id="594" class="l"><a href="#594">594: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>( <span class="php-var">$join</span>[<span class="php-var">$rowPKey</span>] ) ) {
</span><span id="595" class="l"><a href="#595">595: </a>                    <span class="php-var">$data</span>[<span class="php-var">$i</span>][ <span class="php-var">$this</span>-&gt;_name ] = <span class="php-var">$join</span>[<span class="php-var">$rowPKey</span>];
</span><span id="596" class="l"><a href="#596">596: </a>                }
</span><span id="597" class="l"><a href="#597">597: </a>                <span class="php-keyword1">else</span> {
</span><span id="598" class="l"><a href="#598">598: </a>                    <span class="php-var">$data</span>[<span class="php-var">$i</span>][ <span class="php-var">$this</span>-&gt;_name ] = (<span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span>) ?
</span><span id="599" class="l"><a href="#599">599: </a>                        (object)<span class="php-keyword1">array</span>() : <span class="php-keyword1">array</span>();
</span><span id="600" class="l"><a href="#600">600: </a>                }
</span><span id="601" class="l"><a href="#601">601: </a>            }
</span><span id="602" class="l"><a href="#602">602: </a>        }
</span><span id="603" class="l"><a href="#603">603: </a>
</span><span id="604" class="l"><a href="#604">604: </a>        <span class="php-comment">// Field options</span>
</span><span id="605" class="l"><a href="#605">605: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$this</span>-&gt;_fields <span class="php-keyword1">as</span> <span class="php-var">$field</span>) {
</span><span id="606" class="l"><a href="#606">606: </a>            <span class="php-var">$opts</span> = <span class="php-var">$field</span>-&gt;optionsExec( <span class="php-var">$db</span> );
</span><span id="607" class="l"><a href="#607">607: </a>
</span><span id="608" class="l"><a href="#608">608: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$opts</span> !== <span class="php-keyword1">false</span> ) {
</span><span id="609" class="l"><a href="#609">609: </a>                <span class="php-var">$name</span> = <span class="php-var">$this</span>-&gt;name().
</span><span id="610" class="l"><a href="#610">610: </a>                    (<span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span> ? <span class="php-quote">'.'</span> : <span class="php-quote">'[].'</span>).
</span><span id="611" class="l"><a href="#611">611: </a>                    <span class="php-var">$field</span>-&gt;name();
</span><span id="612" class="l"><a href="#612">612: </a>                <span class="php-var">$options</span>[ <span class="php-var">$name</span> ] = <span class="php-var">$opts</span>;
</span><span id="613" class="l"><a href="#613">613: </a>            }
</span><span id="614" class="l"><a href="#614">614: </a>        }
</span><span id="615" class="l"><a href="#615">615: </a>    }
</span><span id="616" class="l"><a href="#616">616: </a>
</span><span id="617" class="l"><a href="#617">617: </a>
</span><span id="618" class="l"><a href="#618">618: </a>    <span class="php-comment">/**
</span></span><span id="619" class="l"><a href="#619">619: </a><span class="php-comment">     * Create a row.
</span></span><span id="620" class="l"><a href="#620">620: </a><span class="php-comment">     *  @param Editor $editor Host Editor instance
</span></span><span id="621" class="l"><a href="#621">621: </a><span class="php-comment">     *  @param int $parentId Parent row's primary key value
</span></span><span id="622" class="l"><a href="#622">622: </a><span class="php-comment">     *  @param string[] $data Data to be set for the join
</span></span><span id="623" class="l"><a href="#623">623: </a><span class="php-comment">     *  @internal
</span></span><span id="624" class="l"><a href="#624">624: </a><span class="php-comment">     */</span>
</span><span id="625" class="l"><a href="#625">625: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> create ( <span class="php-var">$editor</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span> )
</span><span id="626" class="l"><a href="#626">626: </a>    {
</span><span id="627" class="l"><a href="#627">627: </a>        <span class="php-comment">// If not settable, or the many count for the join was not submitted</span>
</span><span id="628" class="l"><a href="#628">628: </a>        <span class="php-comment">// there we do nothing</span>
</span><span id="629" class="l"><a href="#629">629: </a>        <span class="php-keyword1">if</span> (
</span><span id="630" class="l"><a href="#630">630: </a>            ! <span class="php-var">$this</span>-&gt;_set ||
</span><span id="631" class="l"><a href="#631">631: </a>            ! <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name]) || 
</span><span id="632" class="l"><a href="#632">632: </a>            ! <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name.<span class="php-quote">'-many-count'</span>])
</span><span id="633" class="l"><a href="#633">633: </a>        ) {
</span><span id="634" class="l"><a href="#634">634: </a>            <span class="php-keyword1">return</span>;
</span><span id="635" class="l"><a href="#635">635: </a>        }
</span><span id="636" class="l"><a href="#636">636: </a>
</span><span id="637" class="l"><a href="#637">637: </a>        <span class="php-var">$this</span>-&gt;_prep( <span class="php-var">$editor</span> );
</span><span id="638" class="l"><a href="#638">638: </a>        <span class="php-var">$db</span> = <span class="php-var">$editor</span>-&gt;db();
</span><span id="639" class="l"><a href="#639">639: </a>        
</span><span id="640" class="l"><a href="#640">640: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span> ) {
</span><span id="641" class="l"><a href="#641">641: </a>            <span class="php-var">$this</span>-&gt;_insert( <span class="php-var">$db</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name] );
</span><span id="642" class="l"><a href="#642">642: </a>        }
</span><span id="643" class="l"><a href="#643">643: </a>        <span class="php-keyword1">else</span> {
</span><span id="644" class="l"><a href="#644">644: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name]) ; <span class="php-var">$i</span>++ ) {
</span><span id="645" class="l"><a href="#645">645: </a>                <span class="php-var">$this</span>-&gt;_insert( <span class="php-var">$db</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name][<span class="php-var">$i</span>] );
</span><span id="646" class="l"><a href="#646">646: </a>            }
</span><span id="647" class="l"><a href="#647">647: </a>        }
</span><span id="648" class="l"><a href="#648">648: </a>    }
</span><span id="649" class="l"><a href="#649">649: </a>
</span><span id="650" class="l"><a href="#650">650: </a>
</span><span id="651" class="l"><a href="#651">651: </a>    <span class="php-comment">/**
</span></span><span id="652" class="l"><a href="#652">652: </a><span class="php-comment">     * Update a row.
</span></span><span id="653" class="l"><a href="#653">653: </a><span class="php-comment">     *  @param Editor $editor Host Editor instance
</span></span><span id="654" class="l"><a href="#654">654: </a><span class="php-comment">     *  @param int $parentId Parent row's primary key value
</span></span><span id="655" class="l"><a href="#655">655: </a><span class="php-comment">     *  @param string[] $data Data to be set for the join
</span></span><span id="656" class="l"><a href="#656">656: </a><span class="php-comment">     *  @internal
</span></span><span id="657" class="l"><a href="#657">657: </a><span class="php-comment">     */</span>
</span><span id="658" class="l"><a href="#658">658: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> update ( <span class="php-var">$editor</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span> )
</span><span id="659" class="l"><a href="#659">659: </a>    {
</span><span id="660" class="l"><a href="#660">660: </a>        <span class="php-comment">// If not settable, or the many count for the join was not submitted</span>
</span><span id="661" class="l"><a href="#661">661: </a>        <span class="php-comment">// there we do nothing</span>
</span><span id="662" class="l"><a href="#662">662: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_set || ! <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name.<span class="php-quote">'-many-count'</span>]) ) {
</span><span id="663" class="l"><a href="#663">663: </a>            <span class="php-keyword1">return</span>;
</span><span id="664" class="l"><a href="#664">664: </a>        }
</span><span id="665" class="l"><a href="#665">665: </a>
</span><span id="666" class="l"><a href="#666">666: </a>        <span class="php-var">$this</span>-&gt;_prep( <span class="php-var">$editor</span> );
</span><span id="667" class="l"><a href="#667">667: </a>        <span class="php-var">$db</span> = <span class="php-var">$editor</span>-&gt;db();
</span><span id="668" class="l"><a href="#668">668: </a>        
</span><span id="669" class="l"><a href="#669">669: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span> ) {
</span><span id="670" class="l"><a href="#670">670: </a>            <span class="php-comment">// update or insert</span>
</span><span id="671" class="l"><a href="#671">671: </a>            <span class="php-var">$this</span>-&gt;_update_row( <span class="php-var">$db</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name] );
</span><span id="672" class="l"><a href="#672">672: </a>        }
</span><span id="673" class="l"><a href="#673">673: </a>        <span class="php-keyword1">else</span> {
</span><span id="674" class="l"><a href="#674">674: </a>            <span class="php-comment">// WARNING - this will remove rows and then readd them. Any</span>
</span><span id="675" class="l"><a href="#675">675: </a>            <span class="php-comment">// data not in the field list WILL BE LOST</span>
</span><span id="676" class="l"><a href="#676">676: </a>            <span class="php-comment">// todo - is there a better way of doing this?</span>
</span><span id="677" class="l"><a href="#677">677: </a>            <span class="php-var">$this</span>-&gt;remove( <span class="php-var">$editor</span>, <span class="php-keyword1">array</span>(<span class="php-var">$parentId</span>) );
</span><span id="678" class="l"><a href="#678">678: </a>            <span class="php-var">$this</span>-&gt;create( <span class="php-var">$editor</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span> );
</span><span id="679" class="l"><a href="#679">679: </a>        }
</span><span id="680" class="l"><a href="#680">680: </a>    }
</span><span id="681" class="l"><a href="#681">681: </a>
</span><span id="682" class="l"><a href="#682">682: </a>
</span><span id="683" class="l"><a href="#683">683: </a>    <span class="php-comment">/**
</span></span><span id="684" class="l"><a href="#684">684: </a><span class="php-comment">     * Delete rows
</span></span><span id="685" class="l"><a href="#685">685: </a><span class="php-comment">     *  @param Editor $editor Host Editor instance
</span></span><span id="686" class="l"><a href="#686">686: </a><span class="php-comment">     *  @param int[] $ids Parent row IDs to delete
</span></span><span id="687" class="l"><a href="#687">687: </a><span class="php-comment">     *  @internal
</span></span><span id="688" class="l"><a href="#688">688: </a><span class="php-comment">     */</span>
</span><span id="689" class="l"><a href="#689">689: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> remove ( <span class="php-var">$editor</span>, <span class="php-var">$ids</span> )
</span><span id="690" class="l"><a href="#690">690: </a>    {
</span><span id="691" class="l"><a href="#691">691: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_set ) {
</span><span id="692" class="l"><a href="#692">692: </a>            <span class="php-keyword1">return</span>;
</span><span id="693" class="l"><a href="#693">693: </a>        }
</span><span id="694" class="l"><a href="#694">694: </a>
</span><span id="695" class="l"><a href="#695">695: </a>        <span class="php-var">$that</span> = <span class="php-var">$this</span>;
</span><span id="696" class="l"><a href="#696">696: </a>        <span class="php-var">$this</span>-&gt;_prep( <span class="php-var">$editor</span> );
</span><span id="697" class="l"><a href="#697">697: </a>        <span class="php-var">$db</span> = <span class="php-var">$editor</span>-&gt;db();
</span><span id="698" class="l"><a href="#698">698: </a>        
</span><span id="699" class="l"><a href="#699">699: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>]) ) {
</span><span id="700" class="l"><a href="#700">700: </a>            <span class="php-var">$stmt</span> = <span class="php-var">$db</span>
</span><span id="701" class="l"><a href="#701">701: </a>                -&gt;query( <span class="php-quote">'delete'</span> )
</span><span id="702" class="l"><a href="#702">702: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] )
</span><span id="703" class="l"><a href="#703">703: </a>                -&gt;or_where( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">1</span>], <span class="php-var">$ids</span> )
</span><span id="704" class="l"><a href="#704">704: </a>                -&gt;<span class="php-keyword2">exec</span>();
</span><span id="705" class="l"><a href="#705">705: </a>        }
</span><span id="706" class="l"><a href="#706">706: </a>        <span class="php-keyword1">else</span> {
</span><span id="707" class="l"><a href="#707">707: </a>            <span class="php-var">$stmt</span> = <span class="php-var">$db</span>
</span><span id="708" class="l"><a href="#708">708: </a>                -&gt;query( <span class="php-quote">'delete'</span> )
</span><span id="709" class="l"><a href="#709">709: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_table )
</span><span id="710" class="l"><a href="#710">710: </a>                -&gt;where_group( <span class="php-keyword1">function</span> ( <span class="php-var">$q</span> ) <span class="php-keyword1">use</span> ( <span class="php-var">$that</span>, <span class="php-var">$ids</span> ) {
</span><span id="711" class="l"><a href="#711">711: </a>                    <span class="php-var">$q</span>-&gt;or_where( <span class="php-var">$that</span>-&gt;_join[<span class="php-quote">'child'</span>], <span class="php-var">$ids</span> );
</span><span id="712" class="l"><a href="#712">712: </a>                } );
</span><span id="713" class="l"><a href="#713">713: </a>
</span><span id="714" class="l"><a href="#714">714: </a>            <span class="php-var">$this</span>-&gt;_apply_where( <span class="php-var">$stmt</span> );
</span><span id="715" class="l"><a href="#715">715: </a>
</span><span id="716" class="l"><a href="#716">716: </a>            <span class="php-var">$stmt</span>-&gt;<span class="php-keyword2">exec</span>();
</span><span id="717" class="l"><a href="#717">717: </a>        }
</span><span id="718" class="l"><a href="#718">718: </a>    }
</span><span id="719" class="l"><a href="#719">719: </a>
</span><span id="720" class="l"><a href="#720">720: </a>
</span><span id="721" class="l"><a href="#721">721: </a>    <span class="php-comment">/**
</span></span><span id="722" class="l"><a href="#722">722: </a><span class="php-comment">     * Validate input data
</span></span><span id="723" class="l"><a href="#723">723: </a><span class="php-comment">     *
</span></span><span id="724" class="l"><a href="#724">724: </a><span class="php-comment">     * @param array $errors Errors array
</span></span><span id="725" class="l"><a href="#725">725: </a><span class="php-comment">     * @param Editor $editor Editor instance
</span></span><span id="726" class="l"><a href="#726">726: </a><span class="php-comment">     * @param string[] $data Data to validate
</span></span><span id="727" class="l"><a href="#727">727: </a><span class="php-comment">     * @internal
</span></span><span id="728" class="l"><a href="#728">728: </a><span class="php-comment">     */</span>
</span><span id="729" class="l"><a href="#729">729: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> validate ( &amp;<span class="php-var">$errors</span>, <span class="php-var">$editor</span>, <span class="php-var">$data</span> )
</span><span id="730" class="l"><a href="#730">730: </a>    {
</span><span id="731" class="l"><a href="#731">731: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-var">$this</span>-&gt;_set || ! <span class="php-keyword1">isset</span>(<span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name]) ) {
</span><span id="732" class="l"><a href="#732">732: </a>            <span class="php-keyword1">return</span>;
</span><span id="733" class="l"><a href="#733">733: </a>        }
</span><span id="734" class="l"><a href="#734">734: </a>
</span><span id="735" class="l"><a href="#735">735: </a>        <span class="php-var">$this</span>-&gt;_prep( <span class="php-var">$editor</span> );
</span><span id="736" class="l"><a href="#736">736: </a>
</span><span id="737" class="l"><a href="#737">737: </a>        <span class="php-var">$joinData</span> = <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_name];
</span><span id="738" class="l"><a href="#738">738: </a>
</span><span id="739" class="l"><a href="#739">739: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_type === <span class="php-quote">'object'</span> ) {
</span><span id="740" class="l"><a href="#740">740: </a>            <span class="php-var">$this</span>-&gt;_validateFields( <span class="php-var">$errors</span>, <span class="php-var">$editor</span>, <span class="php-var">$joinData</span>, <span class="php-var">$this</span>-&gt;_name.<span class="php-quote">'.'</span> );
</span><span id="741" class="l"><a href="#741">741: </a>        }
</span><span id="742" class="l"><a href="#742">742: </a>        <span class="php-keyword1">else</span> {
</span><span id="743" class="l"><a href="#743">743: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$joinData</span>) ; <span class="php-var">$i</span>++ ) {
</span><span id="744" class="l"><a href="#744">744: </a>                <span class="php-var">$this</span>-&gt;_validateFields( <span class="php-var">$errors</span>, <span class="php-var">$editor</span>, <span class="php-var">$joinData</span>[<span class="php-var">$i</span>], <span class="php-var">$this</span>-&gt;_name.<span class="php-quote">'[].'</span> );
</span><span id="745" class="l"><a href="#745">745: </a>            }
</span><span id="746" class="l"><a href="#746">746: </a>        }
</span><span id="747" class="l"><a href="#747">747: </a>    }
</span><span id="748" class="l"><a href="#748">748: </a>
</span><span id="749" class="l"><a href="#749">749: </a>
</span><span id="750" class="l"><a href="#750">750: </a>
</span><span id="751" class="l"><a href="#751">751: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="752" class="l"><a href="#752">752: </a><span class="php-comment">     * Private methods
</span></span><span id="753" class="l"><a href="#753">753: </a><span class="php-comment">     */</span>
</span><span id="754" class="l"><a href="#754">754: </a>    
</span><span id="755" class="l"><a href="#755">755: </a>    <span class="php-comment">/**
</span></span><span id="756" class="l"><a href="#756">756: </a><span class="php-comment">     * Add local WHERE condition to query
</span></span><span id="757" class="l"><a href="#757">757: </a><span class="php-comment">     *  @param \DataTables\Database\Query $query Query instance to apply the WHERE conditions to
</span></span><span id="758" class="l"><a href="#758">758: </a><span class="php-comment">     *  @private
</span></span><span id="759" class="l"><a href="#759">759: </a><span class="php-comment">     */</span>
</span><span id="760" class="l"><a href="#760">760: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _apply_where ( <span class="php-var">$query</span> )
</span><span id="761" class="l"><a href="#761">761: </a>    {
</span><span id="762" class="l"><a href="#762">762: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$i</span>++ ) {
</span><span id="763" class="l"><a href="#763">763: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">is_callable</span>( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>] ) ) {
</span><span id="764" class="l"><a href="#764">764: </a>                <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>]( <span class="php-var">$query</span> );
</span><span id="765" class="l"><a href="#765">765: </a>            }
</span><span id="766" class="l"><a href="#766">766: </a>            <span class="php-keyword1">else</span> {
</span><span id="767" class="l"><a href="#767">767: </a>                <span class="php-var">$query</span>-&gt;where(
</span><span id="768" class="l"><a href="#768">768: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'key'</span>],
</span><span id="769" class="l"><a href="#769">769: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'value'</span>],
</span><span id="770" class="l"><a href="#770">770: </a>                    <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'op'</span>]
</span><span id="771" class="l"><a href="#771">771: </a>                );
</span><span id="772" class="l"><a href="#772">772: </a>            }
</span><span id="773" class="l"><a href="#773">773: </a>        }
</span><span id="774" class="l"><a href="#774">774: </a>    }
</span><span id="775" class="l"><a href="#775">775: </a>
</span><span id="776" class="l"><a href="#776">776: </a>
</span><span id="777" class="l"><a href="#777">777: </a>    <span class="php-comment">/**
</span></span><span id="778" class="l"><a href="#778">778: </a><span class="php-comment">     * Create a row.
</span></span><span id="779" class="l"><a href="#779">779: </a><span class="php-comment">     *  @param \DataTables\Database $db Database reference to use
</span></span><span id="780" class="l"><a href="#780">780: </a><span class="php-comment">     *  @param int $parentId Parent row's primary key value
</span></span><span id="781" class="l"><a href="#781">781: </a><span class="php-comment">     *  @param string[] $data Data to be set for the join
</span></span><span id="782" class="l"><a href="#782">782: </a><span class="php-comment">     *  @private
</span></span><span id="783" class="l"><a href="#783">783: </a><span class="php-comment">     */</span>
</span><span id="784" class="l"><a href="#784">784: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _insert( <span class="php-var">$db</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span> )
</span><span id="785" class="l"><a href="#785">785: </a>    {
</span><span id="786" class="l"><a href="#786">786: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>]) ) {
</span><span id="787" class="l"><a href="#787">787: </a>            <span class="php-comment">// Insert keys into the join table</span>
</span><span id="788" class="l"><a href="#788">788: </a>            <span class="php-var">$stmt</span> = <span class="php-var">$db</span>
</span><span id="789" class="l"><a href="#789">789: </a>                -&gt;query(<span class="php-quote">'insert'</span>)
</span><span id="790" class="l"><a href="#790">790: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] )
</span><span id="791" class="l"><a href="#791">791: </a>                -&gt;set( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">1</span>], <span class="php-var">$parentId</span> )
</span><span id="792" class="l"><a href="#792">792: </a>                -&gt;set( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">1</span>], <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">0</span>]] )
</span><span id="793" class="l"><a href="#793">793: </a>                -&gt;<span class="php-keyword2">exec</span>();
</span><span id="794" class="l"><a href="#794">794: </a>        }
</span><span id="795" class="l"><a href="#795">795: </a>        <span class="php-keyword1">else</span> {
</span><span id="796" class="l"><a href="#796">796: </a>            <span class="php-comment">// Insert values into the target table</span>
</span><span id="797" class="l"><a href="#797">797: </a>            <span class="php-var">$stmt</span> = <span class="php-var">$db</span>
</span><span id="798" class="l"><a href="#798">798: </a>                -&gt;query(<span class="php-quote">'insert'</span>)
</span><span id="799" class="l"><a href="#799">799: </a>                -&gt;table( <span class="php-var">$this</span>-&gt;_table )
</span><span id="800" class="l"><a href="#800">800: </a>                -&gt;set( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>], <span class="php-var">$parentId</span> );
</span><span id="801" class="l"><a href="#801">801: </a>
</span><span id="802" class="l"><a href="#802">802: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="803" class="l"><a href="#803">803: </a>                <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="804" class="l"><a href="#804">804: </a>
</span><span id="805" class="l"><a href="#805">805: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply( <span class="php-quote">'set'</span>, <span class="php-var">$data</span> ) ) { <span class="php-comment">// TODO should be create or edit</span>
</span><span id="806" class="l"><a href="#806">806: </a>                    <span class="php-var">$stmt</span>-&gt;set( <span class="php-var">$field</span>-&gt;dbField(), <span class="php-var">$field</span>-&gt;val(<span class="php-quote">'set'</span>, <span class="php-var">$data</span>) );
</span><span id="807" class="l"><a href="#807">807: </a>                }
</span><span id="808" class="l"><a href="#808">808: </a>            }
</span><span id="809" class="l"><a href="#809">809: </a>
</span><span id="810" class="l"><a href="#810">810: </a>            <span class="php-comment">// If the where condition variables should also be added to the database</span>
</span><span id="811" class="l"><a href="#811">811: </a>            <span class="php-comment">// Note that `whereSet` is now deprecated</span>
</span><span id="812" class="l"><a href="#812">812: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_whereSet ) {
</span><span id="813" class="l"><a href="#813">813: </a>                <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="814" class="l"><a href="#814">814: </a>                    <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_callable</span>( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>] ) ) {
</span><span id="815" class="l"><a href="#815">815: </a>                        <span class="php-var">$stmt</span>-&gt;set( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'key'</span>], <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'value'</span>] );
</span><span id="816" class="l"><a href="#816">816: </a>                    }
</span><span id="817" class="l"><a href="#817">817: </a>                }
</span><span id="818" class="l"><a href="#818">818: </a>            }
</span><span id="819" class="l"><a href="#819">819: </a>
</span><span id="820" class="l"><a href="#820">820: </a>            <span class="php-var">$stmt</span>-&gt;<span class="php-keyword2">exec</span>(); 
</span><span id="821" class="l"><a href="#821">821: </a>        }
</span><span id="822" class="l"><a href="#822">822: </a>    }
</span><span id="823" class="l"><a href="#823">823: </a>
</span><span id="824" class="l"><a href="#824">824: </a>
</span><span id="825" class="l"><a href="#825">825: </a>    <span class="php-comment">/**
</span></span><span id="826" class="l"><a href="#826">826: </a><span class="php-comment">     * Prepare the instance to be run.
</span></span><span id="827" class="l"><a href="#827">827: </a><span class="php-comment">     *
</span></span><span id="828" class="l"><a href="#828">828: </a><span class="php-comment">     * @param  Editor $editor Editor instance
</span></span><span id="829" class="l"><a href="#829">829: </a><span class="php-comment">     * @private
</span></span><span id="830" class="l"><a href="#830">830: </a><span class="php-comment">     */</span>
</span><span id="831" class="l"><a href="#831">831: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _prep ( <span class="php-var">$editor</span> )
</span><span id="832" class="l"><a href="#832">832: </a>    {
</span><span id="833" class="l"><a href="#833">833: </a>        <span class="php-var">$links</span> = <span class="php-var">$this</span>-&gt;_links;
</span><span id="834" class="l"><a href="#834">834: </a>
</span><span id="835" class="l"><a href="#835">835: </a>        <span class="php-comment">// Were links used to configure this instance - if so, we need to</span>
</span><span id="836" class="l"><a href="#836">836: </a>        <span class="php-comment">// back them onto the join array</span>
</span><span id="837" class="l"><a href="#837">837: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>] === <span class="php-keyword1">null</span> &amp;&amp; <span class="php-keyword2">count</span>(<span class="php-var">$links</span>) ) {
</span><span id="838" class="l"><a href="#838">838: </a>            <span class="php-var">$editorTable</span> = <span class="php-var">$editor</span>-&gt;table();
</span><span id="839" class="l"><a href="#839">839: </a>            <span class="php-var">$editorTable</span> = <span class="php-var">$editorTable</span>[<span class="php-num">0</span>];
</span><span id="840" class="l"><a href="#840">840: </a>            <span class="php-var">$joinTable</span> = <span class="php-var">$this</span>-&gt;table();
</span><span id="841" class="l"><a href="#841">841: </a>
</span><span id="842" class="l"><a href="#842">842: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_aliasParentTable ) {
</span><span id="843" class="l"><a href="#843">843: </a>                <span class="php-var">$editorTable</span> = <span class="php-var">$this</span>-&gt;_aliasParentTable;
</span><span id="844" class="l"><a href="#844">844: </a>            }
</span><span id="845" class="l"><a href="#845">845: </a>
</span><span id="846" class="l"><a href="#846">846: </a>            <span class="php-keyword1">if</span> ( <span class="php-keyword2">count</span>( <span class="php-var">$links</span> ) === <span class="php-num">2</span> ) {
</span><span id="847" class="l"><a href="#847">847: </a>                <span class="php-comment">// No link table</span>
</span><span id="848" class="l"><a href="#848">848: </a>                <span class="php-var">$f1</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">0</span>] );
</span><span id="849" class="l"><a href="#849">849: </a>                <span class="php-var">$f2</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">1</span>] );
</span><span id="850" class="l"><a href="#850">850: </a>
</span><span id="851" class="l"><a href="#851">851: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$f1</span>[<span class="php-num">0</span>] === <span class="php-var">$editorTable</span> ) {
</span><span id="852" class="l"><a href="#852">852: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>] = <span class="php-var">$f1</span>[<span class="php-num">1</span>];
</span><span id="853" class="l"><a href="#853">853: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] = <span class="php-var">$f2</span>[<span class="php-num">1</span>];
</span><span id="854" class="l"><a href="#854">854: </a>                }
</span><span id="855" class="l"><a href="#855">855: </a>                <span class="php-keyword1">else</span> {
</span><span id="856" class="l"><a href="#856">856: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>] = <span class="php-var">$f2</span>[<span class="php-num">1</span>];
</span><span id="857" class="l"><a href="#857">857: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] = <span class="php-var">$f1</span>[<span class="php-num">1</span>];
</span><span id="858" class="l"><a href="#858">858: </a>                }
</span><span id="859" class="l"><a href="#859">859: </a>            }
</span><span id="860" class="l"><a href="#860">860: </a>            <span class="php-keyword1">else</span> {
</span><span id="861" class="l"><a href="#861">861: </a>                <span class="php-comment">// Link table</span>
</span><span id="862" class="l"><a href="#862">862: </a>                <span class="php-var">$f1</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">0</span>] );
</span><span id="863" class="l"><a href="#863">863: </a>                <span class="php-var">$f2</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">1</span>] );
</span><span id="864" class="l"><a href="#864">864: </a>                <span class="php-var">$f3</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">2</span>] );
</span><span id="865" class="l"><a href="#865">865: </a>                <span class="php-var">$f4</span> = <span class="php-keyword2">explode</span>( <span class="php-quote">'.'</span>, <span class="php-var">$links</span>[<span class="php-num">3</span>] );
</span><span id="866" class="l"><a href="#866">866: </a>
</span><span id="867" class="l"><a href="#867">867: </a>                <span class="php-comment">// Discover the name of the link table</span>
</span><span id="868" class="l"><a href="#868">868: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$f1</span>[<span class="php-num">0</span>] !== <span class="php-var">$editorTable</span> &amp;&amp; <span class="php-var">$f1</span>[<span class="php-num">0</span>] !== <span class="php-var">$joinTable</span> ) {
</span><span id="869" class="l"><a href="#869">869: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] = <span class="php-var">$f1</span>[<span class="php-num">0</span>];
</span><span id="870" class="l"><a href="#870">870: </a>                }
</span><span id="871" class="l"><a href="#871">871: </a>                <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$f2</span>[<span class="php-num">0</span>] !== <span class="php-var">$editorTable</span> &amp;&amp; <span class="php-var">$f2</span>[<span class="php-num">0</span>] !== <span class="php-var">$joinTable</span> ) {
</span><span id="872" class="l"><a href="#872">872: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] = <span class="php-var">$f2</span>[<span class="php-num">0</span>];
</span><span id="873" class="l"><a href="#873">873: </a>                }
</span><span id="874" class="l"><a href="#874">874: </a>                <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$f3</span>[<span class="php-num">0</span>] !== <span class="php-var">$editorTable</span> &amp;&amp; <span class="php-var">$f3</span>[<span class="php-num">0</span>] !== <span class="php-var">$joinTable</span> ) {
</span><span id="875" class="l"><a href="#875">875: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] = <span class="php-var">$f3</span>[<span class="php-num">0</span>];
</span><span id="876" class="l"><a href="#876">876: </a>                }
</span><span id="877" class="l"><a href="#877">877: </a>                <span class="php-keyword1">else</span> {
</span><span id="878" class="l"><a href="#878">878: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>] = <span class="php-var">$f4</span>[<span class="php-num">0</span>];
</span><span id="879" class="l"><a href="#879">879: </a>                }
</span><span id="880" class="l"><a href="#880">880: </a>
</span><span id="881" class="l"><a href="#881">881: </a>                <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>] = <span class="php-keyword1">array</span>( <span class="php-var">$f1</span>[<span class="php-num">1</span>], <span class="php-var">$f2</span>[<span class="php-num">1</span>] );
</span><span id="882" class="l"><a href="#882">882: </a>                <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] = <span class="php-keyword1">array</span>( <span class="php-var">$f3</span>[<span class="php-num">1</span>], <span class="php-var">$f4</span>[<span class="php-num">1</span>] );
</span><span id="883" class="l"><a href="#883">883: </a>            }
</span><span id="884" class="l"><a href="#884">884: </a>        }
</span><span id="885" class="l"><a href="#885">885: </a>    }
</span><span id="886" class="l"><a href="#886">886: </a>
</span><span id="887" class="l"><a href="#887">887: </a>
</span><span id="888" class="l"><a href="#888">888: </a>    <span class="php-comment">/**
</span></span><span id="889" class="l"><a href="#889">889: </a><span class="php-comment">     * Update a row.
</span></span><span id="890" class="l"><a href="#890">890: </a><span class="php-comment">     *  @param \DataTables\Database $db Database reference to use
</span></span><span id="891" class="l"><a href="#891">891: </a><span class="php-comment">     *  @param int $parentId Parent row's primary key value
</span></span><span id="892" class="l"><a href="#892">892: </a><span class="php-comment">     *  @param string[] $data Data to be set for the join
</span></span><span id="893" class="l"><a href="#893">893: </a><span class="php-comment">     *  @private
</span></span><span id="894" class="l"><a href="#894">894: </a><span class="php-comment">     */</span>
</span><span id="895" class="l"><a href="#895">895: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _update_row ( <span class="php-var">$db</span>, <span class="php-var">$parentId</span>, <span class="php-var">$data</span> )
</span><span id="896" class="l"><a href="#896">896: </a>    {
</span><span id="897" class="l"><a href="#897">897: </a>        <span class="php-keyword1">if</span> ( <span class="php-keyword1">isset</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>]) ) {
</span><span id="898" class="l"><a href="#898">898: </a>            <span class="php-comment">// Got a link table, just insert the pkey references</span>
</span><span id="899" class="l"><a href="#899">899: </a>            <span class="php-var">$db</span>-&gt;push(
</span><span id="900" class="l"><a href="#900">900: </a>                <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'table'</span>],
</span><span id="901" class="l"><a href="#901">901: </a>                <span class="php-keyword1">array</span>(
</span><span id="902" class="l"><a href="#902">902: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">1</span>] =&gt; <span class="php-var">$parentId</span>,
</span><span id="903" class="l"><a href="#903">903: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">1</span>]  =&gt; <span class="php-var">$data</span>[<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>][<span class="php-num">0</span>]]
</span><span id="904" class="l"><a href="#904">904: </a>                ),
</span><span id="905" class="l"><a href="#905">905: </a>                <span class="php-keyword1">array</span>(
</span><span id="906" class="l"><a href="#906">906: </a>                    <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'parent'</span>][<span class="php-num">1</span>] =&gt; <span class="php-var">$parentId</span>
</span><span id="907" class="l"><a href="#907">907: </a>                )
</span><span id="908" class="l"><a href="#908">908: </a>            );
</span><span id="909" class="l"><a href="#909">909: </a>        }
</span><span id="910" class="l"><a href="#910">910: </a>        <span class="php-keyword1">else</span> {
</span><span id="911" class="l"><a href="#911">911: </a>            <span class="php-comment">// No link table, just a direct reference</span>
</span><span id="912" class="l"><a href="#912">912: </a>            <span class="php-var">$set</span> = <span class="php-keyword1">array</span>(
</span><span id="913" class="l"><a href="#913">913: </a>                <span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] =&gt; <span class="php-var">$parentId</span>
</span><span id="914" class="l"><a href="#914">914: </a>            );
</span><span id="915" class="l"><a href="#915">915: </a>
</span><span id="916" class="l"><a href="#916">916: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="917" class="l"><a href="#917">917: </a>                <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="918" class="l"><a href="#918">918: </a>
</span><span id="919" class="l"><a href="#919">919: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply( <span class="php-quote">'set'</span>, <span class="php-var">$data</span> ) ) {
</span><span id="920" class="l"><a href="#920">920: </a>                    <span class="php-var">$set</span>[ <span class="php-var">$field</span>-&gt;dbField() ] = <span class="php-var">$field</span>-&gt;val(<span class="php-quote">'set'</span>, <span class="php-var">$data</span>);
</span><span id="921" class="l"><a href="#921">921: </a>                }
</span><span id="922" class="l"><a href="#922">922: </a>            }
</span><span id="923" class="l"><a href="#923">923: </a>
</span><span id="924" class="l"><a href="#924">924: </a>            <span class="php-comment">// Add WHERE conditions</span>
</span><span id="925" class="l"><a href="#925">925: </a>            <span class="php-var">$where</span> = <span class="php-keyword1">array</span>(<span class="php-var">$this</span>-&gt;_join[<span class="php-quote">'child'</span>] =&gt; <span class="php-var">$parentId</span>);
</span><span id="926" class="l"><a href="#926">926: </a>            <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span>, <span class="php-var">$ien</span>=<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_where) ; <span class="php-var">$i</span>&lt;<span class="php-var">$ien</span> ; <span class="php-var">$i</span>++ ) {
</span><span id="927" class="l"><a href="#927">927: </a>                <span class="php-var">$where</span>[ <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'key'</span>] ] = <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'value'</span>];
</span><span id="928" class="l"><a href="#928">928: </a>
</span><span id="929" class="l"><a href="#929">929: </a>                <span class="php-comment">// Is there any point in this? Is there any harm?</span>
</span><span id="930" class="l"><a href="#930">930: </a>                <span class="php-comment">// Note that `whereSet` is now deprecated</span>
</span><span id="931" class="l"><a href="#931">931: </a>                <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;_whereSet ) {
</span><span id="932" class="l"><a href="#932">932: </a>                    <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">is_callable</span>( <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>] ) ) {
</span><span id="933" class="l"><a href="#933">933: </a>                        <span class="php-var">$set</span>[ <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'key'</span>] ] = <span class="php-var">$this</span>-&gt;_where[<span class="php-var">$i</span>][<span class="php-quote">'value'</span>];
</span><span id="934" class="l"><a href="#934">934: </a>                    }
</span><span id="935" class="l"><a href="#935">935: </a>                }
</span><span id="936" class="l"><a href="#936">936: </a>            }
</span><span id="937" class="l"><a href="#937">937: </a>
</span><span id="938" class="l"><a href="#938">938: </a>            <span class="php-var">$db</span>-&gt;push( <span class="php-var">$this</span>-&gt;_table, <span class="php-var">$set</span>, <span class="php-var">$where</span> );
</span><span id="939" class="l"><a href="#939">939: </a>        }
</span><span id="940" class="l"><a href="#940">940: </a>    }
</span><span id="941" class="l"><a href="#941">941: </a>
</span><span id="942" class="l"><a href="#942">942: </a>
</span><span id="943" class="l"><a href="#943">943: </a>    <span class="php-comment">/**
</span></span><span id="944" class="l"><a href="#944">944: </a><span class="php-comment">     * Create an SQL string from the fields that this instance knows about for
</span></span><span id="945" class="l"><a href="#945">945: </a><span class="php-comment">     * using in queries
</span></span><span id="946" class="l"><a href="#946">946: </a><span class="php-comment">     *  @param string $direction Direction: 'get' or 'set'.
</span></span><span id="947" class="l"><a href="#947">947: </a><span class="php-comment">     *  @returns array Fields to include
</span></span><span id="948" class="l"><a href="#948">948: </a><span class="php-comment">     *  @private
</span></span><span id="949" class="l"><a href="#949">949: </a><span class="php-comment">     */</span>
</span><span id="950" class="l"><a href="#950">950: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _fields ( <span class="php-var">$direction</span> )
</span><span id="951" class="l"><a href="#951">951: </a>    {
</span><span id="952" class="l"><a href="#952">952: </a>        <span class="php-var">$fields</span> = <span class="php-keyword1">array</span>();
</span><span id="953" class="l"><a href="#953">953: </a>
</span><span id="954" class="l"><a href="#954">954: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="955" class="l"><a href="#955">955: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="956" class="l"><a href="#956">956: </a>
</span><span id="957" class="l"><a href="#957">957: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$field</span>-&gt;apply( <span class="php-var">$direction</span>, <span class="php-keyword1">null</span> ) ) {
</span><span id="958" class="l"><a href="#958">958: </a>                <span class="php-keyword1">if</span> ( <span class="php-keyword2">strpos</span>( <span class="php-var">$field</span>-&gt;dbField() , <span class="php-quote">&quot;.&quot;</span> ) === <span class="php-keyword1">false</span> ) {
</span><span id="959" class="l"><a href="#959">959: </a>                    <span class="php-var">$fields</span>[] = <span class="php-var">$this</span>-&gt;_table.<span class="php-quote">'.'</span>.<span class="php-var">$field</span>-&gt;dbField() .<span class="php-quote">&quot; as &quot;</span>.<span class="php-var">$field</span>-&gt;dbField();;
</span><span id="960" class="l"><a href="#960">960: </a>                }
</span><span id="961" class="l"><a href="#961">961: </a>                <span class="php-keyword1">else</span> {
</span><span id="962" class="l"><a href="#962">962: </a>                    <span class="php-var">$fields</span>[] = <span class="php-var">$field</span>-&gt;dbField();<span class="php-comment">// .&quot; as &quot;.$field-&gt;dbField();</span>
</span><span id="963" class="l"><a href="#963">963: </a>                }
</span><span id="964" class="l"><a href="#964">964: </a>            }
</span><span id="965" class="l"><a href="#965">965: </a>        }
</span><span id="966" class="l"><a href="#966">966: </a>
</span><span id="967" class="l"><a href="#967">967: </a>        <span class="php-keyword1">return</span> <span class="php-var">$fields</span>;
</span><span id="968" class="l"><a href="#968">968: </a>    }
</span><span id="969" class="l"><a href="#969">969: </a>
</span><span id="970" class="l"><a href="#970">970: </a>
</span><span id="971" class="l"><a href="#971">971: </a>    <span class="php-comment">/**
</span></span><span id="972" class="l"><a href="#972">972: </a><span class="php-comment">     * Validate input data
</span></span><span id="973" class="l"><a href="#973">973: </a><span class="php-comment">     *
</span></span><span id="974" class="l"><a href="#974">974: </a><span class="php-comment">     * @param array $errors Errors array
</span></span><span id="975" class="l"><a href="#975">975: </a><span class="php-comment">     * @param Editor $editor Editor instance
</span></span><span id="976" class="l"><a href="#976">976: </a><span class="php-comment">     * @param string[] $data Data to validate
</span></span><span id="977" class="l"><a href="#977">977: </a><span class="php-comment">     * @param string $prefix Field error prefix for client-side to show the
</span></span><span id="978" class="l"><a href="#978">978: </a><span class="php-comment">     *   error message on the appropriate field
</span></span><span id="979" class="l"><a href="#979">979: </a><span class="php-comment">     * @internal
</span></span><span id="980" class="l"><a href="#980">980: </a><span class="php-comment">     */</span>
</span><span id="981" class="l"><a href="#981">981: </a>    <span class="php-keyword1">private</span> <span class="php-keyword1">function</span> _validateFields ( &amp;<span class="php-var">$errors</span>, <span class="php-var">$editor</span>, <span class="php-var">$data</span>, <span class="php-var">$prefix</span> )
</span><span id="982" class="l"><a href="#982">982: </a>    {
</span><span id="983" class="l"><a href="#983">983: </a>        <span class="php-keyword1">for</span> ( <span class="php-var">$i</span>=<span class="php-num">0</span> ; <span class="php-var">$i</span>&lt;<span class="php-keyword2">count</span>(<span class="php-var">$this</span>-&gt;_fields) ; <span class="php-var">$i</span>++ ) {
</span><span id="984" class="l"><a href="#984">984: </a>            <span class="php-var">$field</span> = <span class="php-var">$this</span>-&gt;_fields[<span class="php-var">$i</span>];
</span><span id="985" class="l"><a href="#985">985: </a>            <span class="php-var">$validation</span> = <span class="php-var">$field</span>-&gt;validate( <span class="php-var">$data</span>, <span class="php-var">$editor</span> );
</span><span id="986" class="l"><a href="#986">986: </a>
</span><span id="987" class="l"><a href="#987">987: </a>            <span class="php-keyword1">if</span> ( <span class="php-var">$validation</span> !== <span class="php-keyword1">true</span> ) {
</span><span id="988" class="l"><a href="#988">988: </a>                <span class="php-var">$errors</span>[] = <span class="php-keyword1">array</span>(
</span><span id="989" class="l"><a href="#989">989: </a>                    <span class="php-quote">&quot;name&quot;</span> =&gt; <span class="php-var">$prefix</span>.<span class="php-var">$field</span>-&gt;name(),
</span><span id="990" class="l"><a href="#990">990: </a>                    <span class="php-quote">&quot;status&quot;</span> =&gt; <span class="php-var">$validation</span>
</span><span id="991" class="l"><a href="#991">991: </a>                );
</span><span id="992" class="l"><a href="#992">992: </a>            }
</span><span id="993" class="l"><a href="#993">993: </a>        }
</span><span id="994" class="l"><a href="#994">994: </a>    }
</span><span id="995" class="l"><a href="#995">995: </a>}
</span><span id="996" class="l"><a href="#996">996: </a>
</span><span id="997" class="l"><a href="#997">997: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
