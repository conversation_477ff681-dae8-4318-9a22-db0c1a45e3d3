<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Database.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a><span class="php-comment">/**
</span></span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment"> * DataTables PHP libraries.
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment"> *
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment"> * PHP libraries for DataTables and DataTables Editor, utilising PHP 5.3+.
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment"> *
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment"> *  <AUTHOR>
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment"> *  @copyright 2012 SpryMedia ( http://sprymedia.co.uk )
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment"> *  @license   http://editor.datatables.net/license DataTables Editor
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment"> *  @link      http://editor.datatables.net
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment"> */</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables;
</span><span id="14" class="l"><a href="#14"> 14: </a><span class="php-keyword1">if</span> (!<span class="php-keyword2">defined</span>(<span class="php-quote">'DATATABLES'</span>)) <span class="php-keyword1">exit</span>();
</span><span id="15" class="l"><a href="#15"> 15: </a>
</span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-keyword1">use</span>
</span><span id="17" class="l"><a href="#17"> 17: </a>    DataTables\Database\Query,
</span><span id="18" class="l"><a href="#18"> 18: </a>    DataTables\Database\Result;
</span><span id="19" class="l"><a href="#19"> 19: </a>
</span><span id="20" class="l"><a href="#20"> 20: </a>
</span><span id="21" class="l"><a href="#21"> 21: </a><span class="php-comment">/**
</span></span><span id="22" class="l"><a href="#22"> 22: </a><span class="php-comment"> * DataTables Database connection object.
</span></span><span id="23" class="l"><a href="#23"> 23: </a><span class="php-comment"> *
</span></span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment"> * Create a database connection which may then have queries performed upon it.
</span></span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-comment"> * 
</span></span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-comment"> * This is a database abstraction class that can be used on multiple different
</span></span><span id="27" class="l"><a href="#27"> 27: </a><span class="php-comment"> * databases. As a result of this, it might not be suitable to perform complex
</span></span><span id="28" class="l"><a href="#28"> 28: </a><span class="php-comment"> * queries through this interface or vendor specific queries, but everything 
</span></span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-comment"> * required for basic database interaction is provided through the abstracted
</span></span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-comment"> * methods.
</span></span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-comment"> */</span>
</span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-keyword1">class</span> Database {
</span><span id="33" class="l"><a href="#33"> 33: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="34" class="l"><a href="#34"> 34: </a><span class="php-comment">     * Constructor
</span></span><span id="35" class="l"><a href="#35"> 35: </a><span class="php-comment">     */</span>
</span><span id="36" class="l"><a href="#36"> 36: </a>
</span><span id="37" class="l"><a href="#37"> 37: </a>    <span class="php-comment">/**
</span></span><span id="38" class="l"><a href="#38"> 38: </a><span class="php-comment">     * Database instance constructor.
</span></span><span id="39" class="l"><a href="#39"> 39: </a><span class="php-comment">     *  @param string[] $opts Array of connection parameters for the database:
</span></span><span id="40" class="l"><a href="#40"> 40: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="41" class="l"><a href="#41"> 41: </a><span class="php-comment">     *      array(
</span></span><span id="42" class="l"><a href="#42"> 42: </a><span class="php-comment">     *          &quot;user&quot; =&gt; &quot;&quot;, // User name
</span></span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment">     *          &quot;pass&quot; =&gt; &quot;&quot;, // Password
</span></span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-comment">     *          &quot;host&quot; =&gt; &quot;&quot;, // Host name
</span></span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-comment">     *          &quot;port&quot; =&gt; &quot;&quot;, // Port
</span></span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-comment">     *          &quot;db&quot;   =&gt; &quot;&quot;, // Database name
</span></span><span id="47" class="l"><a href="#47"> 47: </a><span class="php-comment">     *          &quot;type&quot; =&gt; &quot;&quot;  // Datable type: &quot;Mysql&quot;, &quot;Postgres&quot; or &quot;Sqlite&quot;
</span></span><span id="48" class="l"><a href="#48"> 48: </a><span class="php-comment">     *      )
</span></span><span id="49" class="l"><a href="#49"> 49: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-comment">     */</span>
</span><span id="51" class="l"><a href="#51"> 51: </a>    <span class="php-keyword1">function</span> __construct( <span class="php-var">$opts</span> )
</span><span id="52" class="l"><a href="#52"> 52: </a>    {
</span><span id="53" class="l"><a href="#53"> 53: </a>        <span class="php-var">$types</span> = <span class="php-keyword1">array</span>( <span class="php-quote">'Mysql'</span>, <span class="php-quote">'Oracle'</span>, <span class="php-quote">'Postgres'</span>, <span class="php-quote">'Sqlite'</span>, <span class="php-quote">'Sqlserver'</span>, <span class="php-quote">'Db2'</span>, <span class="php-quote">'Firebird'</span> );
</span><span id="54" class="l"><a href="#54"> 54: </a>
</span><span id="55" class="l"><a href="#55"> 55: </a>        <span class="php-keyword1">if</span> ( ! <span class="php-keyword2">in_array</span>( <span class="php-var">$opts</span>[<span class="php-quote">'type'</span>], <span class="php-var">$types</span> ) ) {
</span><span id="56" class="l"><a href="#56"> 56: </a>            <span class="php-keyword1">throw</span> <span class="php-keyword1">new</span> \Exception(
</span><span id="57" class="l"><a href="#57"> 57: </a>                <span class="php-quote">&quot;Unknown database driver type. Must be one of &quot;</span>.<span class="php-keyword2">implode</span>(<span class="php-quote">', '</span>, <span class="php-var">$types</span>),
</span><span id="58" class="l"><a href="#58"> 58: </a>                <span class="php-num">1</span>
</span><span id="59" class="l"><a href="#59"> 59: </a>            );
</span><span id="60" class="l"><a href="#60"> 60: </a>        }
</span><span id="61" class="l"><a href="#61"> 61: </a>
</span><span id="62" class="l"><a href="#62"> 62: </a>        <span class="php-var">$this</span>-&gt;query_driver = <span class="php-quote">&quot;DataTables\\Database\\Driver\\&quot;</span>.<span class="php-var">$opts</span>[<span class="php-quote">'type'</span>].<span class="php-quote">'Query'</span>;
</span><span id="63" class="l"><a href="#63"> 63: </a>        <span class="php-var">$this</span>-&gt;_dbResource = <span class="php-keyword1">isset</span>( <span class="php-var">$opts</span>[<span class="php-quote">'pdo'</span>] ) ?
</span><span id="64" class="l"><a href="#64"> 64: </a>            <span class="php-var">$opts</span>[<span class="php-quote">'pdo'</span>] :
</span><span id="65" class="l"><a href="#65"> 65: </a>            <span class="php-keyword2">call_user_func</span>(<span class="php-var">$this</span>-&gt;query_driver.<span class="php-quote">'::connect'</span>, <span class="php-var">$opts</span> );
</span><span id="66" class="l"><a href="#66"> 66: </a>    }
</span><span id="67" class="l"><a href="#67"> 67: </a>
</span><span id="68" class="l"><a href="#68"> 68: </a>
</span><span id="69" class="l"><a href="#69"> 69: </a>
</span><span id="70" class="l"><a href="#70"> 70: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="71" class="l"><a href="#71"> 71: </a><span class="php-comment">     * Private properties
</span></span><span id="72" class="l"><a href="#72"> 72: </a><span class="php-comment">     */</span>
</span><span id="73" class="l"><a href="#73"> 73: </a>
</span><span id="74" class="l"><a href="#74"> 74: </a>    <span class="php-comment">/** @var resource */</span>
</span><span id="75" class="l"><a href="#75"> 75: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_dbResource</span> = <span class="php-keyword1">null</span>;
</span><span id="76" class="l"><a href="#76"> 76: </a>
</span><span id="77" class="l"><a href="#77"> 77: </a>    <span class="php-comment">/** @var callable */</span>
</span><span id="78" class="l"><a href="#78"> 78: </a>    <span class="php-keyword1">private</span> <span class="php-var">$_debugCallback</span> = <span class="php-keyword1">null</span>;
</span><span id="79" class="l"><a href="#79"> 79: </a>
</span><span id="80" class="l"><a href="#80"> 80: </a>
</span><span id="81" class="l"><a href="#81"> 81: </a>
</span><span id="82" class="l"><a href="#82"> 82: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="83" class="l"><a href="#83"> 83: </a><span class="php-comment">     * Public methods
</span></span><span id="84" class="l"><a href="#84"> 84: </a><span class="php-comment">     */</span>
</span><span id="85" class="l"><a href="#85"> 85: </a>
</span><span id="86" class="l"><a href="#86"> 86: </a>    <span class="php-comment">/**
</span></span><span id="87" class="l"><a href="#87"> 87: </a><span class="php-comment">     * Determine if there is any data in the table that matches the query
</span></span><span id="88" class="l"><a href="#88"> 88: </a><span class="php-comment">     * condition
</span></span><span id="89" class="l"><a href="#89"> 89: </a><span class="php-comment">     *
</span></span><span id="90" class="l"><a href="#90"> 90: </a><span class="php-comment">     * @param string|string[] $table Table name(s) to act upon.
</span></span><span id="91" class="l"><a href="#91"> 91: </a><span class="php-comment">     * @param array $where Where condition for what to select - see {@link
</span></span><span id="92" class="l"><a href="#92"> 92: </a><span class="php-comment">     *   Query::where}.
</span></span><span id="93" class="l"><a href="#93"> 93: </a><span class="php-comment">     * @return boolean Boolean flag - true if there were rows
</span></span><span id="94" class="l"><a href="#94"> 94: </a><span class="php-comment">     */</span>
</span><span id="95" class="l"><a href="#95"> 95: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> any( <span class="php-var">$table</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span> )
</span><span id="96" class="l"><a href="#96"> 96: </a>    {
</span><span id="97" class="l"><a href="#97"> 97: </a>        <span class="php-var">$res</span> = <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'select'</span> )
</span><span id="98" class="l"><a href="#98"> 98: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="99" class="l"><a href="#99"> 99: </a>            -&gt;get( <span class="php-quote">'*'</span> )
</span><span id="100" class="l"><a href="#100">100: </a>            -&gt;where( <span class="php-var">$where</span> )
</span><span id="101" class="l"><a href="#101">101: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="102" class="l"><a href="#102">102: </a>
</span><span id="103" class="l"><a href="#103">103: </a>        <span class="php-keyword1">return</span> <span class="php-var">$res</span>-&gt;<span class="php-keyword2">count</span>() &gt; <span class="php-num">0</span>;
</span><span id="104" class="l"><a href="#104">104: </a>    }
</span><span id="105" class="l"><a href="#105">105: </a>
</span><span id="106" class="l"><a href="#106">106: </a>
</span><span id="107" class="l"><a href="#107">107: </a>    <span class="php-comment">/**
</span></span><span id="108" class="l"><a href="#108">108: </a><span class="php-comment">     * Commit a database transaction.
</span></span><span id="109" class="l"><a href="#109">109: </a><span class="php-comment">     *
</span></span><span id="110" class="l"><a href="#110">110: </a><span class="php-comment">     * Use with {@link transaction} and {@link rollback}.
</span></span><span id="111" class="l"><a href="#111">111: </a><span class="php-comment">     *  @return self
</span></span><span id="112" class="l"><a href="#112">112: </a><span class="php-comment">     */</span>
</span><span id="113" class="l"><a href="#113">113: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> commit ()
</span><span id="114" class="l"><a href="#114">114: </a>    {
</span><span id="115" class="l"><a href="#115">115: </a>        <span class="php-keyword2">call_user_func</span>(<span class="php-var">$this</span>-&gt;query_driver.<span class="php-quote">'::commit'</span>, <span class="php-var">$this</span>-&gt;_dbResource );
</span><span id="116" class="l"><a href="#116">116: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="117" class="l"><a href="#117">117: </a>    }
</span><span id="118" class="l"><a href="#118">118: </a>
</span><span id="119" class="l"><a href="#119">119: </a>
</span><span id="120" class="l"><a href="#120">120: </a>    <span class="php-comment">/**
</span></span><span id="121" class="l"><a href="#121">121: </a><span class="php-comment">     * Get / set debug mode.
</span></span><span id="122" class="l"><a href="#122">122: </a><span class="php-comment">     * 
</span></span><span id="123" class="l"><a href="#123">123: </a><span class="php-comment">     *  @param boolean $_ Debug mode state. If not given, then used as a getter.
</span></span><span id="124" class="l"><a href="#124">124: </a><span class="php-comment">     *  @return boolean|self Debug mode state if no parameter is given, or
</span></span><span id="125" class="l"><a href="#125">125: </a><span class="php-comment">     *    self if used as a setter.
</span></span><span id="126" class="l"><a href="#126">126: </a><span class="php-comment">     */</span>
</span><span id="127" class="l"><a href="#127">127: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> debug ( <span class="php-var">$set</span>=<span class="php-keyword1">null</span> )
</span><span id="128" class="l"><a href="#128">128: </a>    {
</span><span id="129" class="l"><a href="#129">129: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$set</span> === <span class="php-keyword1">null</span> ) {
</span><span id="130" class="l"><a href="#130">130: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_debugCallback ? <span class="php-keyword1">true</span> : <span class="php-keyword1">false</span>;
</span><span id="131" class="l"><a href="#131">131: </a>        }
</span><span id="132" class="l"><a href="#132">132: </a>        <span class="php-keyword1">else</span> <span class="php-keyword1">if</span> ( <span class="php-var">$set</span> === <span class="php-keyword1">false</span> ) {
</span><span id="133" class="l"><a href="#133">133: </a>            <span class="php-var">$this</span>-&gt;_debugCallback = <span class="php-keyword1">null</span>;
</span><span id="134" class="l"><a href="#134">134: </a>        }
</span><span id="135" class="l"><a href="#135">135: </a>        <span class="php-keyword1">else</span> {
</span><span id="136" class="l"><a href="#136">136: </a>            <span class="php-var">$this</span>-&gt;_debugCallback = <span class="php-var">$set</span>;
</span><span id="137" class="l"><a href="#137">137: </a>        }
</span><span id="138" class="l"><a href="#138">138: </a>
</span><span id="139" class="l"><a href="#139">139: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="140" class="l"><a href="#140">140: </a>    }
</span><span id="141" class="l"><a href="#141">141: </a>
</span><span id="142" class="l"><a href="#142">142: </a>
</span><span id="143" class="l"><a href="#143">143: </a>    <span class="php-comment">/**
</span></span><span id="144" class="l"><a href="#144">144: </a><span class="php-comment">     * Perform a delete query on a table.
</span></span><span id="145" class="l"><a href="#145">145: </a><span class="php-comment">     *
</span></span><span id="146" class="l"><a href="#146">146: </a><span class="php-comment">     * This is a short cut method that creates an update query and then uses
</span></span><span id="147" class="l"><a href="#147">147: </a><span class="php-comment">     * the query('delete'), table, where and exec methods of the query.
</span></span><span id="148" class="l"><a href="#148">148: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="149" class="l"><a href="#149">149: </a><span class="php-comment">     *  @param array $where Where condition for what to delete - see {@link
</span></span><span id="150" class="l"><a href="#150">150: </a><span class="php-comment">     *    Query::where}.
</span></span><span id="151" class="l"><a href="#151">151: </a><span class="php-comment">     *  @return Result
</span></span><span id="152" class="l"><a href="#152">152: </a><span class="php-comment">     */</span>
</span><span id="153" class="l"><a href="#153">153: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> <span class="php-keyword2">delete</span> ( <span class="php-var">$table</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span> )
</span><span id="154" class="l"><a href="#154">154: </a>    {
</span><span id="155" class="l"><a href="#155">155: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'delete'</span> )
</span><span id="156" class="l"><a href="#156">156: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="157" class="l"><a href="#157">157: </a>            -&gt;where( <span class="php-var">$where</span> )
</span><span id="158" class="l"><a href="#158">158: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="159" class="l"><a href="#159">159: </a>    }
</span><span id="160" class="l"><a href="#160">160: </a>
</span><span id="161" class="l"><a href="#161">161: </a>
</span><span id="162" class="l"><a href="#162">162: </a>    <span class="php-comment">/**
</span></span><span id="163" class="l"><a href="#163">163: </a><span class="php-comment">     * Insert data into a table.
</span></span><span id="164" class="l"><a href="#164">164: </a><span class="php-comment">     *
</span></span><span id="165" class="l"><a href="#165">165: </a><span class="php-comment">     * This is a short cut method that creates an update query and then uses
</span></span><span id="166" class="l"><a href="#166">166: </a><span class="php-comment">     * the query('insert'), table, set and exec methods of the query.
</span></span><span id="167" class="l"><a href="#167">167: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="168" class="l"><a href="#168">168: </a><span class="php-comment">     *  @param array $set Field names and values to set - see {@link
</span></span><span id="169" class="l"><a href="#169">169: </a><span class="php-comment">     *    Query::set}.
</span></span><span id="170" class="l"><a href="#170">170: </a><span class="php-comment">     *  @param  array $pkey Primary key column names (this is an array for
</span></span><span id="171" class="l"><a href="#171">171: </a><span class="php-comment">     *    forwards compt, although only the first item in the array is actually
</span></span><span id="172" class="l"><a href="#172">172: </a><span class="php-comment">     *    used). This doesn't need to be set, but it must be if you want to use
</span></span><span id="173" class="l"><a href="#173">173: </a><span class="php-comment">     *    the `Result-&gt;insertId()` method.
</span></span><span id="174" class="l"><a href="#174">174: </a><span class="php-comment">     *  @return Result
</span></span><span id="175" class="l"><a href="#175">175: </a><span class="php-comment">     */</span>
</span><span id="176" class="l"><a href="#176">176: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> insert ( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$pkey</span>=<span class="php-quote">''</span> )
</span><span id="177" class="l"><a href="#177">177: </a>    {
</span><span id="178" class="l"><a href="#178">178: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'insert'</span> )
</span><span id="179" class="l"><a href="#179">179: </a>            -&gt;pkey( <span class="php-var">$pkey</span> )
</span><span id="180" class="l"><a href="#180">180: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="181" class="l"><a href="#181">181: </a>            -&gt;set( <span class="php-var">$set</span> )
</span><span id="182" class="l"><a href="#182">182: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="183" class="l"><a href="#183">183: </a>    }
</span><span id="184" class="l"><a href="#184">184: </a>
</span><span id="185" class="l"><a href="#185">185: </a>
</span><span id="186" class="l"><a href="#186">186: </a>    <span class="php-comment">/**
</span></span><span id="187" class="l"><a href="#187">187: </a><span class="php-comment">     * Update or Insert data. When doing an insert, the where condition is
</span></span><span id="188" class="l"><a href="#188">188: </a><span class="php-comment">     * added as a set field
</span></span><span id="189" class="l"><a href="#189">189: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="190" class="l"><a href="#190">190: </a><span class="php-comment">     *  @param array $set Field names and values to set - see {@link
</span></span><span id="191" class="l"><a href="#191">191: </a><span class="php-comment">     *    Query::set}.
</span></span><span id="192" class="l"><a href="#192">192: </a><span class="php-comment">     *  @param array $where Where condition for what to update - see {@link
</span></span><span id="193" class="l"><a href="#193">193: </a><span class="php-comment">     *    Query::where}.
</span></span><span id="194" class="l"><a href="#194">194: </a><span class="php-comment">     *  @param  array $pkey Primary key column names (this is an array for
</span></span><span id="195" class="l"><a href="#195">195: </a><span class="php-comment">     *    forwards compt, although only the first item in the array is actually
</span></span><span id="196" class="l"><a href="#196">196: </a><span class="php-comment">     *    used). This doesn't need to be set, but it must be if you want to use
</span></span><span id="197" class="l"><a href="#197">197: </a><span class="php-comment">     *    the `Result-&gt;insertId()` method. Only used if an insert is performed.
</span></span><span id="198" class="l"><a href="#198">198: </a><span class="php-comment">     *  @return Result
</span></span><span id="199" class="l"><a href="#199">199: </a><span class="php-comment">     */</span>
</span><span id="200" class="l"><a href="#200">200: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> push ( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span>, <span class="php-var">$pkey</span>=<span class="php-quote">''</span> )
</span><span id="201" class="l"><a href="#201">201: </a>    {
</span><span id="202" class="l"><a href="#202">202: </a>        <span class="php-var">$selectColumn</span> = <span class="php-quote">'*'</span>;
</span><span id="203" class="l"><a href="#203">203: </a>        
</span><span id="204" class="l"><a href="#204">204: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$pkey</span> ) {
</span><span id="205" class="l"><a href="#205">205: </a>            <span class="php-var">$selectColumn</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$pkey</span>) ?
</span><span id="206" class="l"><a href="#206">206: </a>                <span class="php-var">$pkey</span>[<span class="php-num">0</span>] :
</span><span id="207" class="l"><a href="#207">207: </a>                <span class="php-var">$pkey</span>;
</span><span id="208" class="l"><a href="#208">208: </a>        }
</span><span id="209" class="l"><a href="#209">209: </a>
</span><span id="210" class="l"><a href="#210">210: </a>        <span class="php-comment">// Update or insert</span>
</span><span id="211" class="l"><a href="#211">211: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$this</span>-&gt;select( <span class="php-var">$table</span>, <span class="php-var">$selectColumn</span>, <span class="php-var">$where</span> )-&gt;<span class="php-keyword2">count</span>() &gt; <span class="php-num">0</span> ) {
</span><span id="212" class="l"><a href="#212">212: </a>            <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;update( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$where</span> );
</span><span id="213" class="l"><a href="#213">213: </a>        }
</span><span id="214" class="l"><a href="#214">214: </a>
</span><span id="215" class="l"><a href="#215">215: </a>        <span class="php-comment">// Add the where condition to the values to set</span>
</span><span id="216" class="l"><a href="#216">216: </a>        <span class="php-keyword1">foreach</span> (<span class="php-var">$where</span> <span class="php-keyword1">as</span> <span class="php-var">$key</span> =&gt; <span class="php-var">$value</span>) {
</span><span id="217" class="l"><a href="#217">217: </a>            <span class="php-keyword1">if</span> ( ! <span class="php-keyword1">isset</span>( <span class="php-var">$set</span>[ <span class="php-var">$key</span> ] ) ) {
</span><span id="218" class="l"><a href="#218">218: </a>                <span class="php-var">$set</span>[ <span class="php-var">$key</span> ] = <span class="php-var">$value</span>;
</span><span id="219" class="l"><a href="#219">219: </a>            }
</span><span id="220" class="l"><a href="#220">220: </a>        }
</span><span id="221" class="l"><a href="#221">221: </a>
</span><span id="222" class="l"><a href="#222">222: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;insert( <span class="php-var">$table</span>, <span class="php-var">$set</span>, <span class="php-var">$pkey</span> );
</span><span id="223" class="l"><a href="#223">223: </a>    }
</span><span id="224" class="l"><a href="#224">224: </a>
</span><span id="225" class="l"><a href="#225">225: </a>
</span><span id="226" class="l"><a href="#226">226: </a>    <span class="php-comment">/**
</span></span><span id="227" class="l"><a href="#227">227: </a><span class="php-comment">     * Create a query object to build a database query.
</span></span><span id="228" class="l"><a href="#228">228: </a><span class="php-comment">     *  @param string $type Query type - select, insert, update or delete.
</span></span><span id="229" class="l"><a href="#229">229: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="230" class="l"><a href="#230">230: </a><span class="php-comment">     *  @return Query
</span></span><span id="231" class="l"><a href="#231">231: </a><span class="php-comment">     */</span>
</span><span id="232" class="l"><a href="#232">232: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> query ( <span class="php-var">$type</span>, <span class="php-var">$table</span>=<span class="php-keyword1">null</span> )
</span><span id="233" class="l"><a href="#233">233: </a>    {
</span><span id="234" class="l"><a href="#234">234: </a>        <span class="php-keyword1">return</span> <span class="php-keyword1">new</span> <span class="php-var">$this</span>-&gt;query_driver( <span class="php-var">$this</span>, <span class="php-var">$type</span>, <span class="php-var">$table</span> );
</span><span id="235" class="l"><a href="#235">235: </a>    }
</span><span id="236" class="l"><a href="#236">236: </a>
</span><span id="237" class="l"><a href="#237">237: </a>
</span><span id="238" class="l"><a href="#238">238: </a>    <span class="php-comment">/**
</span></span><span id="239" class="l"><a href="#239">239: </a><span class="php-comment">     * Quote a string for a quote. Note you should generally use a bind!
</span></span><span id="240" class="l"><a href="#240">240: </a><span class="php-comment">     *  @param string $val Value to quote
</span></span><span id="241" class="l"><a href="#241">241: </a><span class="php-comment">     *  @param string $type Value type
</span></span><span id="242" class="l"><a href="#242">242: </a><span class="php-comment">     *  @return string
</span></span><span id="243" class="l"><a href="#243">243: </a><span class="php-comment">     */</span>
</span><span id="244" class="l"><a href="#244">244: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> quote ( <span class="php-var">$val</span>, <span class="php-var">$type</span>=\PDO::PARAM_STR )
</span><span id="245" class="l"><a href="#245">245: </a>    {
</span><span id="246" class="l"><a href="#246">246: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_dbResource-&gt;quote( <span class="php-var">$val</span>, <span class="php-var">$type</span> );
</span><span id="247" class="l"><a href="#247">247: </a>    }
</span><span id="248" class="l"><a href="#248">248: </a>
</span><span id="249" class="l"><a href="#249">249: </a>
</span><span id="250" class="l"><a href="#250">250: </a>    <span class="php-comment">/**
</span></span><span id="251" class="l"><a href="#251">251: </a><span class="php-comment">     * Create a `Query` object that will execute a custom SQL query. This is
</span></span><span id="252" class="l"><a href="#252">252: </a><span class="php-comment">     * similar to the `sql` method, but in this case you must call the `exec()`
</span></span><span id="253" class="l"><a href="#253">253: </a><span class="php-comment">     * method of the returned `Query` object manually. This can be useful if you
</span></span><span id="254" class="l"><a href="#254">254: </a><span class="php-comment">     * wish to bind parameters using the query `bind` method to ensure data is
</span></span><span id="255" class="l"><a href="#255">255: </a><span class="php-comment">     * properly escaped.
</span></span><span id="256" class="l"><a href="#256">256: </a><span class="php-comment">     *
</span></span><span id="257" class="l"><a href="#257">257: </a><span class="php-comment">     *  @return Result
</span></span><span id="258" class="l"><a href="#258">258: </a><span class="php-comment">     *
</span></span><span id="259" class="l"><a href="#259">259: </a><span class="php-comment">     *  @example
</span></span><span id="260" class="l"><a href="#260">260: </a><span class="php-comment">     *    Safely escape user input
</span></span><span id="261" class="l"><a href="#261">261: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="262" class="l"><a href="#262">262: </a><span class="php-comment">     *    $db
</span></span><span id="263" class="l"><a href="#263">263: </a><span class="php-comment">     *      -&gt;raw()
</span></span><span id="264" class="l"><a href="#264">264: </a><span class="php-comment">     *      -&gt;bind( ':date', $_POST['date'] )
</span></span><span id="265" class="l"><a href="#265">265: </a><span class="php-comment">     *      -&gt;exec( 'SELECT * FROM staff where date &lt; :date' );
</span></span><span id="266" class="l"><a href="#266">266: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="267" class="l"><a href="#267">267: </a><span class="php-comment">     */</span>
</span><span id="268" class="l"><a href="#268">268: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> raw ()
</span><span id="269" class="l"><a href="#269">269: </a>    {
</span><span id="270" class="l"><a href="#270">270: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'raw'</span> );
</span><span id="271" class="l"><a href="#271">271: </a>    }
</span><span id="272" class="l"><a href="#272">272: </a>
</span><span id="273" class="l"><a href="#273">273: </a>
</span><span id="274" class="l"><a href="#274">274: </a>    <span class="php-comment">/**
</span></span><span id="275" class="l"><a href="#275">275: </a><span class="php-comment">     * Get the database resource connector. This is typically a PDO object.
</span></span><span id="276" class="l"><a href="#276">276: </a><span class="php-comment">     * @return resource PDO connection resource (driver dependent)
</span></span><span id="277" class="l"><a href="#277">277: </a><span class="php-comment">     */</span>
</span><span id="278" class="l"><a href="#278">278: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> resource ()
</span><span id="279" class="l"><a href="#279">279: </a>    {
</span><span id="280" class="l"><a href="#280">280: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;_dbResource;
</span><span id="281" class="l"><a href="#281">281: </a>    }
</span><span id="282" class="l"><a href="#282">282: </a>
</span><span id="283" class="l"><a href="#283">283: </a>
</span><span id="284" class="l"><a href="#284">284: </a>    <span class="php-comment">/**
</span></span><span id="285" class="l"><a href="#285">285: </a><span class="php-comment">     * Rollback the database state to the start of the transaction.
</span></span><span id="286" class="l"><a href="#286">286: </a><span class="php-comment">     *
</span></span><span id="287" class="l"><a href="#287">287: </a><span class="php-comment">     * Use with {@link transaction} and {@link commit}.
</span></span><span id="288" class="l"><a href="#288">288: </a><span class="php-comment">     *  @return self
</span></span><span id="289" class="l"><a href="#289">289: </a><span class="php-comment">     */</span>
</span><span id="290" class="l"><a href="#290">290: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> rollback ()
</span><span id="291" class="l"><a href="#291">291: </a>    {
</span><span id="292" class="l"><a href="#292">292: </a>        <span class="php-keyword2">call_user_func</span>(<span class="php-var">$this</span>-&gt;query_driver.<span class="php-quote">'::rollback'</span>, <span class="php-var">$this</span>-&gt;_dbResource );
</span><span id="293" class="l"><a href="#293">293: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="294" class="l"><a href="#294">294: </a>    }
</span><span id="295" class="l"><a href="#295">295: </a>
</span><span id="296" class="l"><a href="#296">296: </a>
</span><span id="297" class="l"><a href="#297">297: </a>    <span class="php-comment">/**
</span></span><span id="298" class="l"><a href="#298">298: </a><span class="php-comment">     * Select data from a table.
</span></span><span id="299" class="l"><a href="#299">299: </a><span class="php-comment">     *
</span></span><span id="300" class="l"><a href="#300">300: </a><span class="php-comment">     * This is a short cut method that creates an update query and then uses
</span></span><span id="301" class="l"><a href="#301">301: </a><span class="php-comment">     * the query('select'), table, get, where and exec methods of the query.
</span></span><span id="302" class="l"><a href="#302">302: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="303" class="l"><a href="#303">303: </a><span class="php-comment">     *  @param array $field Fields to get from the table(s) - see {@link
</span></span><span id="304" class="l"><a href="#304">304: </a><span class="php-comment">     *    Query::get}.
</span></span><span id="305" class="l"><a href="#305">305: </a><span class="php-comment">     *  @param array $where Where condition for what to select - see {@link
</span></span><span id="306" class="l"><a href="#306">306: </a><span class="php-comment">     *    Query::where}.
</span></span><span id="307" class="l"><a href="#307">307: </a><span class="php-comment">     *  @param array $orderBy Order condition - see {@link
</span></span><span id="308" class="l"><a href="#308">308: </a><span class="php-comment">     *    Query::order}.
</span></span><span id="309" class="l"><a href="#309">309: </a><span class="php-comment">     *  @return Result
</span></span><span id="310" class="l"><a href="#310">310: </a><span class="php-comment">     */</span>
</span><span id="311" class="l"><a href="#311">311: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> select ( <span class="php-var">$table</span>, <span class="php-var">$field</span>=<span class="php-quote">&quot;*&quot;</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span>, <span class="php-var">$orderBy</span>=<span class="php-keyword1">null</span> )
</span><span id="312" class="l"><a href="#312">312: </a>    {
</span><span id="313" class="l"><a href="#313">313: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'select'</span> )
</span><span id="314" class="l"><a href="#314">314: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="315" class="l"><a href="#315">315: </a>            -&gt;get( <span class="php-var">$field</span> )
</span><span id="316" class="l"><a href="#316">316: </a>            -&gt;where( <span class="php-var">$where</span> )
</span><span id="317" class="l"><a href="#317">317: </a>            -&gt;order( <span class="php-var">$orderBy</span> )
</span><span id="318" class="l"><a href="#318">318: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="319" class="l"><a href="#319">319: </a>    }
</span><span id="320" class="l"><a href="#320">320: </a>
</span><span id="321" class="l"><a href="#321">321: </a>
</span><span id="322" class="l"><a href="#322">322: </a>    <span class="php-comment">/**
</span></span><span id="323" class="l"><a href="#323">323: </a><span class="php-comment">     * Select distinct data from a table.
</span></span><span id="324" class="l"><a href="#324">324: </a><span class="php-comment">     *
</span></span><span id="325" class="l"><a href="#325">325: </a><span class="php-comment">     * This is a short cut method that creates an update query and then uses the
</span></span><span id="326" class="l"><a href="#326">326: </a><span class="php-comment">     * query('select'), distinct ,table, get, where and exec methods of the
</span></span><span id="327" class="l"><a href="#327">327: </a><span class="php-comment">     * query.
</span></span><span id="328" class="l"><a href="#328">328: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="329" class="l"><a href="#329">329: </a><span class="php-comment">     *  @param array $field Fields to get from the table(s) - see {@link
</span></span><span id="330" class="l"><a href="#330">330: </a><span class="php-comment">     *    Query::get}.
</span></span><span id="331" class="l"><a href="#331">331: </a><span class="php-comment">     *  @param array $where Where condition for what to select - see {@link
</span></span><span id="332" class="l"><a href="#332">332: </a><span class="php-comment">     *    Query::where}.
</span></span><span id="333" class="l"><a href="#333">333: </a><span class="php-comment">     *  @param array $orderBy Order condition - see {@link
</span></span><span id="334" class="l"><a href="#334">334: </a><span class="php-comment">     *    Query::order}.
</span></span><span id="335" class="l"><a href="#335">335: </a><span class="php-comment">     *  @return Result
</span></span><span id="336" class="l"><a href="#336">336: </a><span class="php-comment">     */</span>
</span><span id="337" class="l"><a href="#337">337: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> selectDistinct ( <span class="php-var">$table</span>, <span class="php-var">$field</span>=<span class="php-quote">&quot;*&quot;</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span>, <span class="php-var">$orderBy</span>=<span class="php-keyword1">null</span> )
</span><span id="338" class="l"><a href="#338">338: </a>    {
</span><span id="339" class="l"><a href="#339">339: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'select'</span> )
</span><span id="340" class="l"><a href="#340">340: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="341" class="l"><a href="#341">341: </a>            -&gt;distinct( <span class="php-keyword1">true</span> )
</span><span id="342" class="l"><a href="#342">342: </a>            -&gt;get( <span class="php-var">$field</span> )
</span><span id="343" class="l"><a href="#343">343: </a>            -&gt;where( <span class="php-var">$where</span> )
</span><span id="344" class="l"><a href="#344">344: </a>            -&gt;order( <span class="php-var">$orderBy</span> )
</span><span id="345" class="l"><a href="#345">345: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="346" class="l"><a href="#346">346: </a>    }
</span><span id="347" class="l"><a href="#347">347: </a>
</span><span id="348" class="l"><a href="#348">348: </a>
</span><span id="349" class="l"><a href="#349">349: </a>    <span class="php-comment">/**
</span></span><span id="350" class="l"><a href="#350">350: </a><span class="php-comment">     * Execute an raw SQL query - i.e. give the method your own SQL, rather
</span></span><span id="351" class="l"><a href="#351">351: </a><span class="php-comment">     * than having the Database classes building it for you.
</span></span><span id="352" class="l"><a href="#352">352: </a><span class="php-comment">     *
</span></span><span id="353" class="l"><a href="#353">353: </a><span class="php-comment">     * This method will execute the given SQL immediately. Use the `raw()`
</span></span><span id="354" class="l"><a href="#354">354: </a><span class="php-comment">     * method if you need the ability to add bound parameters.
</span></span><span id="355" class="l"><a href="#355">355: </a><span class="php-comment">     *  @param string $sql SQL string to execute (only if _type is 'raw').
</span></span><span id="356" class="l"><a href="#356">356: </a><span class="php-comment">     *  @return Result
</span></span><span id="357" class="l"><a href="#357">357: </a><span class="php-comment">     *
</span></span><span id="358" class="l"><a href="#358">358: </a><span class="php-comment">     *  @example
</span></span><span id="359" class="l"><a href="#359">359: </a><span class="php-comment">     *    Basic select
</span></span><span id="360" class="l"><a href="#360">360: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="361" class="l"><a href="#361">361: </a><span class="php-comment">     *    $result = $db-&gt;sql( 'SELECT * FROM myTable;' );
</span></span><span id="362" class="l"><a href="#362">362: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="363" class="l"><a href="#363">363: </a><span class="php-comment">     *
</span></span><span id="364" class="l"><a href="#364">364: </a><span class="php-comment">     *  @example
</span></span><span id="365" class="l"><a href="#365">365: </a><span class="php-comment">     *    Set the character set of the connection
</span></span><span id="366" class="l"><a href="#366">366: </a><span class="php-comment">     *    &lt;code&gt;
</span></span><span id="367" class="l"><a href="#367">367: </a><span class="php-comment">     *    $db-&gt;sql(&quot;SET character_set_client=utf8&quot;);
</span></span><span id="368" class="l"><a href="#368">368: </a><span class="php-comment">     *    $db-&gt;sql(&quot;SET character_set_connection=utf8&quot;);
</span></span><span id="369" class="l"><a href="#369">369: </a><span class="php-comment">     *    $db-&gt;sql(&quot;SET character_set_results=utf8&quot;);
</span></span><span id="370" class="l"><a href="#370">370: </a><span class="php-comment">     *    &lt;/code&gt;
</span></span><span id="371" class="l"><a href="#371">371: </a><span class="php-comment">     */</span>
</span><span id="372" class="l"><a href="#372">372: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> sql ( <span class="php-var">$sql</span> )
</span><span id="373" class="l"><a href="#373">373: </a>    {
</span><span id="374" class="l"><a href="#374">374: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'raw'</span> )
</span><span id="375" class="l"><a href="#375">375: </a>            -&gt;<span class="php-keyword2">exec</span>( <span class="php-var">$sql</span> );
</span><span id="376" class="l"><a href="#376">376: </a>    }
</span><span id="377" class="l"><a href="#377">377: </a>
</span><span id="378" class="l"><a href="#378">378: </a>
</span><span id="379" class="l"><a href="#379">379: </a>    <span class="php-comment">/**
</span></span><span id="380" class="l"><a href="#380">380: </a><span class="php-comment">     * Start a new database transaction.
</span></span><span id="381" class="l"><a href="#381">381: </a><span class="php-comment">     *
</span></span><span id="382" class="l"><a href="#382">382: </a><span class="php-comment">     * Use with {@link commit} and {@link rollback}.
</span></span><span id="383" class="l"><a href="#383">383: </a><span class="php-comment">     *  @return self
</span></span><span id="384" class="l"><a href="#384">384: </a><span class="php-comment">     */</span>
</span><span id="385" class="l"><a href="#385">385: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> transaction ()
</span><span id="386" class="l"><a href="#386">386: </a>    {
</span><span id="387" class="l"><a href="#387">387: </a>        <span class="php-keyword2">call_user_func</span>(<span class="php-var">$this</span>-&gt;query_driver.<span class="php-quote">'::transaction'</span>, <span class="php-var">$this</span>-&gt;_dbResource );
</span><span id="388" class="l"><a href="#388">388: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="389" class="l"><a href="#389">389: </a>    }
</span><span id="390" class="l"><a href="#390">390: </a>
</span><span id="391" class="l"><a href="#391">391: </a>
</span><span id="392" class="l"><a href="#392">392: </a>    <span class="php-comment">/**
</span></span><span id="393" class="l"><a href="#393">393: </a><span class="php-comment">     * Update data.
</span></span><span id="394" class="l"><a href="#394">394: </a><span class="php-comment">     *
</span></span><span id="395" class="l"><a href="#395">395: </a><span class="php-comment">     * This is a short cut method that creates an update query and then uses
</span></span><span id="396" class="l"><a href="#396">396: </a><span class="php-comment">     * the query('update'), table, set, where and exec methods of the query.
</span></span><span id="397" class="l"><a href="#397">397: </a><span class="php-comment">     *  @param string|string[] $table Table name(s) to act upon.
</span></span><span id="398" class="l"><a href="#398">398: </a><span class="php-comment">     *  @param array $set Field names and values to set - see {@link
</span></span><span id="399" class="l"><a href="#399">399: </a><span class="php-comment">     *    Query::set}.
</span></span><span id="400" class="l"><a href="#400">400: </a><span class="php-comment">     *  @param array $where Where condition for what to update - see {@link
</span></span><span id="401" class="l"><a href="#401">401: </a><span class="php-comment">     *    Query::where}.
</span></span><span id="402" class="l"><a href="#402">402: </a><span class="php-comment">     *  @return Result
</span></span><span id="403" class="l"><a href="#403">403: </a><span class="php-comment">     */</span>
</span><span id="404" class="l"><a href="#404">404: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> update ( <span class="php-var">$table</span>, <span class="php-var">$set</span>=<span class="php-keyword1">null</span>, <span class="php-var">$where</span>=<span class="php-keyword1">null</span> )
</span><span id="405" class="l"><a href="#405">405: </a>    {
</span><span id="406" class="l"><a href="#406">406: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>-&gt;query( <span class="php-quote">'update'</span> )
</span><span id="407" class="l"><a href="#407">407: </a>            -&gt;table( <span class="php-var">$table</span> )
</span><span id="408" class="l"><a href="#408">408: </a>            -&gt;set( <span class="php-var">$set</span> )
</span><span id="409" class="l"><a href="#409">409: </a>            -&gt;where( <span class="php-var">$where</span> )
</span><span id="410" class="l"><a href="#410">410: </a>            -&gt;<span class="php-keyword2">exec</span>();
</span><span id="411" class="l"><a href="#411">411: </a>    }
</span><span id="412" class="l"><a href="#412">412: </a>
</span><span id="413" class="l"><a href="#413">413: </a>
</span><span id="414" class="l"><a href="#414">414: </a>    <span class="php-comment">/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
</span></span><span id="415" class="l"><a href="#415">415: </a><span class="php-comment">     * Internal functions
</span></span><span id="416" class="l"><a href="#416">416: </a><span class="php-comment">     */</span>
</span><span id="417" class="l"><a href="#417">417: </a>
</span><span id="418" class="l"><a href="#418">418: </a>    <span class="php-comment">/**
</span></span><span id="419" class="l"><a href="#419">419: </a><span class="php-comment">     * Get debug query information.
</span></span><span id="420" class="l"><a href="#420">420: </a><span class="php-comment">     *
</span></span><span id="421" class="l"><a href="#421">421: </a><span class="php-comment">     *  @return array Information about the queries used. When this method is
</span></span><span id="422" class="l"><a href="#422">422: </a><span class="php-comment">     *    called it will reset the query cache.
</span></span><span id="423" class="l"><a href="#423">423: </a><span class="php-comment">     *  @internal
</span></span><span id="424" class="l"><a href="#424">424: </a><span class="php-comment">     */</span>
</span><span id="425" class="l"><a href="#425">425: </a>    <span class="php-keyword1">public</span> <span class="php-keyword1">function</span> debugInfo ( <span class="php-var">$query</span>=<span class="php-keyword1">null</span>, <span class="php-var">$bindings</span>=<span class="php-keyword1">null</span> )
</span><span id="426" class="l"><a href="#426">426: </a>    {
</span><span id="427" class="l"><a href="#427">427: </a>        <span class="php-var">$callback</span> = <span class="php-var">$this</span>-&gt;_debugCallback;
</span><span id="428" class="l"><a href="#428">428: </a>
</span><span id="429" class="l"><a href="#429">429: </a>        <span class="php-keyword1">if</span> ( <span class="php-var">$callback</span> ) {
</span><span id="430" class="l"><a href="#430">430: </a>            <span class="php-var">$callback</span>( <span class="php-keyword1">array</span>(
</span><span id="431" class="l"><a href="#431">431: </a>                <span class="php-quote">&quot;query&quot;</span>    =&gt; <span class="php-var">$query</span>,
</span><span id="432" class="l"><a href="#432">432: </a>                <span class="php-quote">&quot;bindings&quot;</span> =&gt; <span class="php-var">$bindings</span>
</span><span id="433" class="l"><a href="#433">433: </a>            ) );
</span><span id="434" class="l"><a href="#434">434: </a>        }
</span><span id="435" class="l"><a href="#435">435: </a>
</span><span id="436" class="l"><a href="#436">436: </a>        <span class="php-keyword1">return</span> <span class="php-var">$this</span>;
</span><span id="437" class="l"><a href="#437">437: </a>    }
</span><span id="438" class="l"><a href="#438">438: </a>};
</span><span id="439" class="l"><a href="#439">439: </a>
</span><span id="440" class="l"><a href="#440">440: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
