<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">

	<title>File Vendor/htmLawed/htmLawed.php | DataTables Editor 1.8.1 - PHP libraries</title>

	<link rel="stylesheet" href="resources/style.css?e99947befd7bf673c6b43ff75e9e0f170c88a60e">

</head>

<body>
<div id="left">
	<div id="menu">
		<a href="index.html" title="Overview"><span>Overview</span></a>


		<div id="groups">
				<h3>Namespaces</h3>
			<ul>
				<li>
					<a href="namespace-DataTables.html">
						DataTables<span></span>
					</a>

						<ul>
				<li>
					<a href="namespace-DataTables.Database.html">
						Database					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Editor.html">
						Editor					</a>

						</li>
				<li>
					<a href="namespace-DataTables.Vendor.html">
						Vendor					</a>

						</li>
							</ul></li>
			</ul>
		</div>

		<hr>


		<div id="elements">
			<h3>Classes</h3>
			<ul>
				<li><a href="class-DataTables.Database.html">DataTables\Database</a></li>
				<li><a href="class-DataTables.Database.Query.html">DataTables\Database\Query</a></li>
				<li><a href="class-DataTables.Database.Result.html">DataTables\Database\Result</a></li>
				<li><a href="class-DataTables.Editor.html">DataTables\Editor</a></li>
				<li><a href="class-DataTables.Editor.Field.html">DataTables\Editor\Field</a></li>
				<li><a href="class-DataTables.Editor.Format.html">DataTables\Editor\Format</a></li>
				<li><a href="class-DataTables.Editor.Join.html">DataTables\Editor\Join</a></li>
				<li><a href="class-DataTables.Editor.MJoin.html">DataTables\Editor\MJoin</a></li>
				<li><a href="class-DataTables.Editor.Options.html">DataTables\Editor\Options</a></li>
				<li><a href="class-DataTables.Editor.Upload.html">DataTables\Editor\Upload</a></li>
				<li><a href="class-DataTables.Editor.Validate.html">DataTables\Editor\Validate</a></li>
				<li><a href="class-DataTables.Editor.ValidateOptions.html">DataTables\Editor\ValidateOptions</a></li>
				<li><a href="class-DataTables.Ext.html">DataTables\Ext</a></li>
				<li><a href="class-DataTables.Vendor.Htmlaw.html">DataTables\Vendor\Htmlaw</a></li>
				<li><a href="class-DataTables.Vendor.htmLawed.html">DataTables\Vendor\htmLawed</a></li>
			</ul>





		</div>
	</div>
</div>

<div id="splitter"></div>

<div id="right">
<div id="rightInner">
	<form id="search">
		<input type="hidden" name="cx" value="">
		<input type="hidden" name="ie" value="UTF-8">
		<input type="text" name="q" class="text" placeholder="Search">
	</form>

	<div id="navigation">
		<ul>
			<li>
				<a href="index.html" title="Overview"><span>Overview</span></a>
			</li>
			<li>
<span>Namespace</span>			</li>
			<li>
<span>Class</span>			</li>
		</ul>
		<ul>
		</ul>
		<ul>
		</ul>
	</div>

<pre><code><span id="1" class="l"><a href="#1">  1: </a><span class="xlang">&lt;?php</span>
</span><span id="2" class="l"><a href="#2">  2: </a>
</span><span id="3" class="l"><a href="#3">  3: </a><span class="php-comment">/*
</span></span><span id="4" class="l"><a href="#4">  4: </a><span class="php-comment">htmLawed 1.1.22, 5 March 2016
</span></span><span id="5" class="l"><a href="#5">  5: </a><span class="php-comment">OOP code, 27 February 2016
</span></span><span id="6" class="l"><a href="#6">  6: </a><span class="php-comment">Copyright Santosh Patnaik
</span></span><span id="7" class="l"><a href="#7">  7: </a><span class="php-comment">Dual LGPL v3 and GPL v2+ license
</span></span><span id="8" class="l"><a href="#8">  8: </a><span class="php-comment">A PHP Labware internal utility; www.bioinformatics.org/phplabware/internal_utilities/htmLawed
</span></span><span id="9" class="l"><a href="#9">  9: </a><span class="php-comment">
</span></span><span id="10" class="l"><a href="#10"> 10: </a><span class="php-comment">See htmLawed_README.txt/htm
</span></span><span id="11" class="l"><a href="#11"> 11: </a><span class="php-comment">*/</span>
</span><span id="12" class="l"><a href="#12"> 12: </a>
</span><span id="13" class="l"><a href="#13"> 13: </a><span class="php-keyword1">namespace</span> DataTables\Vendor;
</span><span id="14" class="l"><a href="#14"> 14: </a>
</span><span id="15" class="l"><a href="#15"> 15: </a><span class="php-keyword1">class</span> htmLawed{
</span><span id="16" class="l"><a href="#16"> 16: </a><span class="php-comment">// begin class</span>
</span><span id="17" class="l"><a href="#17"> 17: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl(<span class="php-var">$t</span>, <span class="php-var">$C</span>=<span class="php-num">1</span>, <span class="php-var">$S</span>=<span class="php-keyword1">array</span>()){
</span><span id="18" class="l"><a href="#18"> 18: </a><span class="php-var">$C</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$C</span>) ? <span class="php-var">$C</span> : <span class="php-keyword1">array</span>();
</span><span id="19" class="l"><a href="#19"> 19: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'valid_xhtml'</span>])){
</span><span id="20" class="l"><a href="#20"> 20: </a> <span class="php-var">$C</span>[<span class="php-quote">'elements'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'elements'</span>]) ? <span class="php-quote">'*-center-dir-font-isindex-menu-s-strike-u'</span> : <span class="php-var">$C</span>[<span class="php-quote">'elements'</span>];
</span><span id="21" class="l"><a href="#21"> 21: </a> <span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] : <span class="php-num">2</span>;
</span><span id="22" class="l"><a href="#22"> 22: </a> <span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] : <span class="php-num">2</span>;
</span><span id="23" class="l"><a href="#23"> 23: </a>}
</span><span id="24" class="l"><a href="#24"> 24: </a><span class="php-comment">// config eles</span>
</span><span id="25" class="l"><a href="#25"> 25: </a><span class="php-var">$e</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'abbr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'acronym'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'address'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'b'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'big'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'center'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'cite'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'code'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dfn'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'em'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'i'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'kbd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'q'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rb'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rbc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'s'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'samp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'small'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'span'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strike'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strong'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'u'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'var'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// 86/deprecated+embed+ruby</span>
</span><span id="26" class="l"><a href="#26"> 26: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'safe'</span>])){
</span><span id="27" class="l"><a href="#27"> 27: </a> <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>[<span class="php-quote">'applet'</span>], <span class="php-var">$e</span>[<span class="php-quote">'embed'</span>], <span class="php-var">$e</span>[<span class="php-quote">'iframe'</span>], <span class="php-var">$e</span>[<span class="php-quote">'object'</span>], <span class="php-var">$e</span>[<span class="php-quote">'script'</span>]);
</span><span id="28" class="l"><a href="#28"> 28: </a>}
</span><span id="29" class="l"><a href="#29"> 29: </a><span class="php-var">$x</span> = !<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'elements'</span>]) ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">' '</span>), <span class="php-quote">''</span>, <span class="php-var">$C</span>[<span class="php-quote">'elements'</span>]) : <span class="php-quote">'*'</span>;
</span><span id="30" class="l"><a href="#30"> 30: </a><span class="php-keyword1">if</span>(<span class="php-var">$x</span> == <span class="php-quote">'-*'</span>){<span class="php-var">$e</span> = <span class="php-keyword1">array</span>();}
</span><span id="31" class="l"><a href="#31"> 31: </a><span class="php-keyword1">elseif</span>(<span class="php-keyword2">strpos</span>(<span class="php-var">$x</span>, <span class="php-quote">'*'</span>) === <span class="php-keyword1">false</span>){<span class="php-var">$e</span> = <span class="php-keyword2">array_flip</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">','</span>, <span class="php-var">$x</span>));}
</span><span id="32" class="l"><a href="#32"> 32: </a><span class="php-keyword1">else</span>{
</span><span id="33" class="l"><a href="#33"> 33: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$x</span>[<span class="php-num">1</span>])){
</span><span id="34" class="l"><a href="#34"> 34: </a>  <span class="php-keyword2">preg_match_all</span>(<span class="php-quote">'`(?:^|-|\+)[^\-+]+?(?=-|\+|$)`'</span>, <span class="php-var">$x</span>, <span class="php-var">$m</span>, PREG_SET_ORDER);
</span><span id="35" class="l"><a href="#35"> 35: </a>  <span class="php-keyword1">for</span>(<span class="php-var">$i</span>=<span class="php-keyword2">count</span>(<span class="php-var">$m</span>); --<span class="php-var">$i</span>&gt;=<span class="php-num">0</span>;){<span class="php-var">$m</span>[<span class="php-var">$i</span>] = <span class="php-var">$m</span>[<span class="php-var">$i</span>][<span class="php-num">0</span>];}
</span><span id="36" class="l"><a href="#36"> 36: </a>  <span class="php-keyword1">foreach</span>(<span class="php-var">$m</span> <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="37" class="l"><a href="#37"> 37: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$v</span>[<span class="php-num">0</span>] == <span class="php-quote">'+'</span>){<span class="php-var">$e</span>[<span class="php-keyword2">substr</span>(<span class="php-var">$v</span>, <span class="php-num">1</span>)] = <span class="php-num">1</span>;}
</span><span id="38" class="l"><a href="#38"> 38: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$v</span>[<span class="php-num">0</span>] == <span class="php-quote">'-'</span> &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$e</span>[(<span class="php-var">$v</span> = <span class="php-keyword2">substr</span>(<span class="php-var">$v</span>, <span class="php-num">1</span>))]) &amp;&amp; !<span class="php-keyword2">in_array</span>(<span class="php-quote">'+'</span>. <span class="php-var">$v</span>, <span class="php-var">$m</span>)){<span class="php-keyword1">unset</span>(<span class="php-var">$e</span>[<span class="php-var">$v</span>]);}
</span><span id="39" class="l"><a href="#39"> 39: </a>  }
</span><span id="40" class="l"><a href="#40"> 40: </a> }
</span><span id="41" class="l"><a href="#41"> 41: </a>}
</span><span id="42" class="l"><a href="#42"> 42: </a><span class="php-var">$C</span>[<span class="php-quote">'elements'</span>] =&amp; <span class="php-var">$e</span>;
</span><span id="43" class="l"><a href="#43"> 43: </a><span class="php-comment">// config attrs</span>
</span><span id="44" class="l"><a href="#44"> 44: </a><span class="php-var">$x</span> = !<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>]) ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">' '</span>), <span class="php-quote">''</span>, <span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>]) : <span class="php-quote">''</span>;
</span><span id="45" class="l"><a href="#45"> 45: </a><span class="php-var">$x</span> = <span class="php-keyword2">array_flip</span>((<span class="php-keyword1">isset</span>(<span class="php-var">$x</span>[<span class="php-num">0</span>]) &amp;&amp; <span class="php-var">$x</span>[<span class="php-num">0</span>] == <span class="php-quote">'*'</span>) ? <span class="php-keyword2">explode</span>(<span class="php-quote">'-'</span>, <span class="php-var">$x</span>) : <span class="php-keyword2">explode</span>(<span class="php-quote">','</span>, <span class="php-var">$x</span>. (!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'safe'</span>]) ? <span class="php-quote">',on*'</span> : <span class="php-quote">''</span>)));
</span><span id="46" class="l"><a href="#46"> 46: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$x</span>[<span class="php-quote">'on*'</span>])){
</span><span id="47" class="l"><a href="#47"> 47: </a> <span class="php-keyword1">unset</span>(<span class="php-var">$x</span>[<span class="php-quote">'on*'</span>]);
</span><span id="48" class="l"><a href="#48"> 48: </a> <span class="php-var">$x</span> += <span class="php-keyword1">array</span>(<span class="php-quote">'onblur'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onchange'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onclick'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ondblclick'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onfocus'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onkeydown'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onkeypress'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onkeyup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onmousedown'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onmousemove'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onmouseout'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onmouseover'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onmouseup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onreset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onselect'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'onsubmit'</span>=&gt;<span class="php-num">1</span>);
</span><span id="49" class="l"><a href="#49"> 49: </a>}
</span><span id="50" class="l"><a href="#50"> 50: </a><span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>] = <span class="php-var">$x</span>;
</span><span id="51" class="l"><a href="#51"> 51: </a><span class="php-comment">// config URL</span>
</span><span id="52" class="l"><a href="#52"> 52: </a><span class="php-var">$x</span> = (<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-num">2</span>]) &amp;&amp; <span class="php-keyword2">strpos</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>], <span class="php-quote">':'</span>)) ? <span class="php-keyword2">strtolower</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>]) : <span class="php-quote">'href: aim, feed, file, ftp, gopher, http, https, irc, mailto, news, nntp, sftp, ssh, telnet; *:file, http, https'</span>;
</span><span id="53" class="l"><a href="#53"> 53: </a><span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>] = <span class="php-keyword1">array</span>();
</span><span id="54" class="l"><a href="#54"> 54: </a><span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">';'</span>, <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">' '</span>, <span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\n&quot;</span>), <span class="php-quote">''</span>, <span class="php-var">$x</span>)) <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="55" class="l"><a href="#55"> 55: </a> <span class="php-var">$x</span> = <span class="php-var">$x2</span> = <span class="php-keyword1">null</span>; <span class="php-keyword1">list</span>(<span class="php-var">$x</span>, <span class="php-var">$x2</span>) = <span class="php-keyword2">explode</span>(<span class="php-quote">':'</span>, <span class="php-var">$v</span>, <span class="php-num">2</span>);
</span><span id="56" class="l"><a href="#56"> 56: </a> <span class="php-keyword1">if</span>(<span class="php-var">$x2</span>){<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-var">$x</span>] = <span class="php-keyword2">array_flip</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">','</span>, <span class="php-var">$x2</span>));}
</span><span id="57" class="l"><a href="#57"> 57: </a>}
</span><span id="58" class="l"><a href="#58"> 58: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-quote">'*'</span>])){<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-quote">'*'</span>] = <span class="php-keyword1">array</span>(<span class="php-quote">'file'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'http'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'https'</span>=&gt;<span class="php-num">1</span>,);}
</span><span id="59" class="l"><a href="#59"> 59: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'safe'</span>]) &amp;&amp; <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-quote">'style'</span>])){<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-quote">'style'</span>] = <span class="php-keyword1">array</span>(<span class="php-quote">'!'</span>=&gt;<span class="php-num">1</span>);}
</span><span id="60" class="l"><a href="#60"> 60: </a><span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>] : <span class="php-num">0</span>;
</span><span id="61" class="l"><a href="#61"> 61: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>]) <span class="php-keyword1">or</span> !<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^[a-zA-Z\d.+\-]+://[^/]+/(.+?/)?$`'</span>, <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>])){
</span><span id="62" class="l"><a href="#62"> 62: </a> <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>] = <span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>] = <span class="php-num">0</span>;
</span><span id="63" class="l"><a href="#63"> 63: </a>}
</span><span id="64" class="l"><a href="#64"> 64: </a><span class="php-comment">// config rest</span>
</span><span id="65" class="l"><a href="#65"> 65: </a><span class="php-var">$C</span>[<span class="php-quote">'and_mark'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'and_mark'</span>]) ? <span class="php-num">0</span> : <span class="php-num">1</span>;
</span><span id="66" class="l"><a href="#66"> 66: </a><span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>] = (<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>]) &amp;&amp; <span class="php-keyword2">is_array</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>]) &amp;&amp; <span class="php-keyword2">count</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>]) == <span class="php-num">2</span> &amp;&amp; (<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">0</span>]) <span class="php-keyword1">or</span> \DataTables\Vendor\htmLawed::hl_regex(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">0</span>])) &amp;&amp; (<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">1</span>]) <span class="php-keyword1">or</span> \DataTables\Vendor\htmLawed::hl_regex(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">1</span>]))) ? <span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>] : <span class="php-num">0</span>;
</span><span id="67" class="l"><a href="#67"> 67: </a><span class="php-var">$C</span>[<span class="php-quote">'anti_mail_spam'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_mail_spam'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'anti_mail_spam'</span>] : <span class="php-num">0</span>;
</span><span id="68" class="l"><a href="#68"> 68: </a><span class="php-var">$C</span>[<span class="php-quote">'balance'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'balance'</span>]) ? (bool)<span class="php-var">$C</span>[<span class="php-quote">'balance'</span>] : <span class="php-num">1</span>;
</span><span id="69" class="l"><a href="#69"> 69: </a><span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>] : (<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'safe'</span>]) ? <span class="php-num">3</span> : <span class="php-num">0</span>);
</span><span id="70" class="l"><a href="#70"> 70: </a><span class="php-var">$C</span>[<span class="php-quote">'clean_ms_char'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'clean_ms_char'</span>]) ? <span class="php-num">0</span> : <span class="php-var">$C</span>[<span class="php-quote">'clean_ms_char'</span>];
</span><span id="71" class="l"><a href="#71"> 71: </a><span class="php-var">$C</span>[<span class="php-quote">'comment'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'comment'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'comment'</span>] : (<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'safe'</span>]) ? <span class="php-num">3</span> : <span class="php-num">0</span>);
</span><span id="72" class="l"><a href="#72"> 72: </a><span class="php-var">$C</span>[<span class="php-quote">'css_expression'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'css_expression'</span>]) ? <span class="php-num">0</span> : <span class="php-num">1</span>;
</span><span id="73" class="l"><a href="#73"> 73: </a><span class="php-var">$C</span>[<span class="php-quote">'direct_list_nest'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'direct_list_nest'</span>]) ? <span class="php-num">0</span> : <span class="php-num">1</span>;
</span><span id="74" class="l"><a href="#74"> 74: </a><span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>] : <span class="php-num">1</span>;
</span><span id="75" class="l"><a href="#75"> 75: </a><span class="php-var">$C</span>[<span class="php-quote">'hook'</span>] = (!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook'</span>]) &amp;&amp; <span class="php-keyword2">function_exists</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook'</span>])) ? <span class="php-var">$C</span>[<span class="php-quote">'hook'</span>] : <span class="php-num">0</span>;
</span><span id="76" class="l"><a href="#76"> 76: </a><span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>] = (!<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>]) &amp;&amp; <span class="php-keyword2">function_exists</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>])) ? <span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>] : <span class="php-num">0</span>;
</span><span id="77" class="l"><a href="#77"> 77: </a><span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>] : <span class="php-num">6</span>;
</span><span id="78" class="l"><a href="#78"> 78: </a><span class="php-var">$C</span>[<span class="php-quote">'lc_std_val'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'lc_std_val'</span>]) ? (bool)<span class="php-var">$C</span>[<span class="php-quote">'lc_std_val'</span>] : <span class="php-num">1</span>;
</span><span id="79" class="l"><a href="#79"> 79: </a><span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] : <span class="php-num">1</span>;
</span><span id="80" class="l"><a href="#80"> 80: </a><span class="php-var">$C</span>[<span class="php-quote">'named_entity'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'named_entity'</span>]) ? (bool)<span class="php-var">$C</span>[<span class="php-quote">'named_entity'</span>] : <span class="php-num">1</span>;
</span><span id="81" class="l"><a href="#81"> 81: </a><span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>] : <span class="php-num">1</span>;
</span><span id="82" class="l"><a href="#82"> 82: </a><span class="php-var">$C</span>[<span class="php-quote">'parent'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'parent'</span>][<span class="php-num">0</span>]) ? <span class="php-keyword2">strtolower</span>(<span class="php-var">$C</span>[<span class="php-quote">'parent'</span>]) : <span class="php-quote">'body'</span>;
</span><span id="83" class="l"><a href="#83"> 83: </a><span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>] = !<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>] : <span class="php-num">0</span>;
</span><span id="84" class="l"><a href="#84"> 84: </a><span class="php-var">$C</span>[<span class="php-quote">'style_pass'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'style_pass'</span>]) ? <span class="php-num">0</span> : <span class="php-num">1</span>;
</span><span id="85" class="l"><a href="#85"> 85: </a><span class="php-var">$C</span>[<span class="php-quote">'tidy'</span>] = <span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'tidy'</span>]) ? <span class="php-num">0</span> : <span class="php-var">$C</span>[<span class="php-quote">'tidy'</span>];
</span><span id="86" class="l"><a href="#86"> 86: </a><span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] : <span class="php-num">1</span>;
</span><span id="87" class="l"><a href="#87"> 87: </a><span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] : <span class="php-num">0</span>;
</span><span id="88" class="l"><a href="#88"> 88: </a>
</span><span id="89" class="l"><a href="#89"> 89: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'C'</span>])){<span class="php-var">$reC</span> = <span class="php-var">$GLOBALS</span>[<span class="php-quote">'C'</span>];}
</span><span id="90" class="l"><a href="#90"> 90: </a><span class="php-var">$GLOBALS</span>[<span class="php-quote">'C'</span>] = <span class="php-var">$C</span>;
</span><span id="91" class="l"><a href="#91"> 91: </a><span class="php-var">$S</span> = <span class="php-keyword2">is_array</span>(<span class="php-var">$S</span>) ? <span class="php-var">$S</span> : \DataTables\Vendor\htmLawed::hl_spec(<span class="php-var">$S</span>);
</span><span id="92" class="l"><a href="#92"> 92: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'S'</span>])){<span class="php-var">$reS</span> = <span class="php-var">$GLOBALS</span>[<span class="php-quote">'S'</span>];}
</span><span id="93" class="l"><a href="#93"> 93: </a><span class="php-var">$GLOBALS</span>[<span class="php-quote">'S'</span>] = <span class="php-var">$S</span>;
</span><span id="94" class="l"><a href="#94"> 94: </a>
</span><span id="95" class="l"><a href="#95"> 95: </a><span class="php-var">$t</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`[\x00-\x08\x0b-\x0c\x0e-\x1f]`'</span>, <span class="php-quote">''</span>, <span class="php-var">$t</span>);
</span><span id="96" class="l"><a href="#96"> 96: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'clean_ms_char'</span>]){
</span><span id="97" class="l"><a href="#97"> 97: </a> <span class="php-var">$x</span> = <span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x7f&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x80&quot;</span>=&gt;<span class="php-quote">'&amp;#8364;'</span>, <span class="php-quote">&quot;\x81&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x83&quot;</span>=&gt;<span class="php-quote">'&amp;#402;'</span>, <span class="php-quote">&quot;\x85&quot;</span>=&gt;<span class="php-quote">'&amp;#8230;'</span>, <span class="php-quote">&quot;\x86&quot;</span>=&gt;<span class="php-quote">'&amp;#8224;'</span>, <span class="php-quote">&quot;\x87&quot;</span>=&gt;<span class="php-quote">'&amp;#8225;'</span>, <span class="php-quote">&quot;\x88&quot;</span>=&gt;<span class="php-quote">'&amp;#710;'</span>, <span class="php-quote">&quot;\x89&quot;</span>=&gt;<span class="php-quote">'&amp;#8240;'</span>, <span class="php-quote">&quot;\x8a&quot;</span>=&gt;<span class="php-quote">'&amp;#352;'</span>, <span class="php-quote">&quot;\x8b&quot;</span>=&gt;<span class="php-quote">'&amp;#8249;'</span>, <span class="php-quote">&quot;\x8c&quot;</span>=&gt;<span class="php-quote">'&amp;#338;'</span>, <span class="php-quote">&quot;\x8d&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x8e&quot;</span>=&gt;<span class="php-quote">'&amp;#381;'</span>, <span class="php-quote">&quot;\x8f&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x90&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x95&quot;</span>=&gt;<span class="php-quote">'&amp;#8226;'</span>, <span class="php-quote">&quot;\x96&quot;</span>=&gt;<span class="php-quote">'&amp;#8211;'</span>, <span class="php-quote">&quot;\x97&quot;</span>=&gt;<span class="php-quote">'&amp;#8212;'</span>, <span class="php-quote">&quot;\x98&quot;</span>=&gt;<span class="php-quote">'&amp;#732;'</span>, <span class="php-quote">&quot;\x99&quot;</span>=&gt;<span class="php-quote">'&amp;#8482;'</span>, <span class="php-quote">&quot;\x9a&quot;</span>=&gt;<span class="php-quote">'&amp;#353;'</span>, <span class="php-quote">&quot;\x9b&quot;</span>=&gt;<span class="php-quote">'&amp;#8250;'</span>, <span class="php-quote">&quot;\x9c&quot;</span>=&gt;<span class="php-quote">'&amp;#339;'</span>, <span class="php-quote">&quot;\x9d&quot;</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">&quot;\x9e&quot;</span>=&gt;<span class="php-quote">'&amp;#382;'</span>, <span class="php-quote">&quot;\x9f&quot;</span>=&gt;<span class="php-quote">'&amp;#376;'</span>);
</span><span id="98" class="l"><a href="#98"> 98: </a> <span class="php-var">$x</span> = <span class="php-var">$x</span> + (<span class="php-var">$C</span>[<span class="php-quote">'clean_ms_char'</span>] == <span class="php-num">1</span> ? <span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x82&quot;</span>=&gt;<span class="php-quote">'&amp;#8218;'</span>, <span class="php-quote">&quot;\x84&quot;</span>=&gt;<span class="php-quote">'&amp;#8222;'</span>, <span class="php-quote">&quot;\x91&quot;</span>=&gt;<span class="php-quote">'&amp;#8216;'</span>, <span class="php-quote">&quot;\x92&quot;</span>=&gt;<span class="php-quote">'&amp;#8217;'</span>, <span class="php-quote">&quot;\x93&quot;</span>=&gt;<span class="php-quote">'&amp;#8220;'</span>, <span class="php-quote">&quot;\x94&quot;</span>=&gt;<span class="php-quote">'&amp;#8221;'</span>) : <span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x82&quot;</span>=&gt;<span class="php-quote">'\''</span>, <span class="php-quote">&quot;\x84&quot;</span>=&gt;<span class="php-quote">'&quot;'</span>, <span class="php-quote">&quot;\x91&quot;</span>=&gt;<span class="php-quote">'\''</span>, <span class="php-quote">&quot;\x92&quot;</span>=&gt;<span class="php-quote">'\''</span>, <span class="php-quote">&quot;\x93&quot;</span>=&gt;<span class="php-quote">'&quot;'</span>, <span class="php-quote">&quot;\x94&quot;</span>=&gt;<span class="php-quote">'&quot;'</span>));
</span><span id="99" class="l"><a href="#99"> 99: </a> <span class="php-var">$t</span> = <span class="php-keyword2">strtr</span>(<span class="php-var">$t</span>, <span class="php-var">$x</span>);
</span><span id="100" class="l"><a href="#100">100: </a>}
</span><span id="101" class="l"><a href="#101">101: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>] <span class="php-keyword1">or</span> <span class="php-var">$C</span>[<span class="php-quote">'comment'</span>]){<span class="php-var">$t</span> = <span class="php-keyword2">preg_replace_callback</span>(<span class="php-quote">'`&lt;!(?:(?:--.*?--)|(?:\[CDATA\[.*?\]\]))&gt;`sm'</span>, <span class="php-quote">'\DataTables\Vendor\htmLawed::hl_cmtcd'</span>, <span class="php-var">$t</span>);}
</span><span id="102" class="l"><a href="#102">102: </a><span class="php-var">$t</span> = <span class="php-keyword2">preg_replace_callback</span>(<span class="php-quote">'`&amp;amp;([A-Za-z][A-Za-z0-9]{1,30}|#(?:[0-9]{1,8}|[Xx][0-9A-Fa-f]{1,7}));`'</span>, <span class="php-quote">'\DataTables\Vendor\htmLawed::hl_ent'</span>, <span class="php-keyword2">str_replace</span>(<span class="php-quote">'&amp;'</span>, <span class="php-quote">'&amp;amp;'</span>, <span class="php-var">$t</span>));
</span><span id="103" class="l"><a href="#103">103: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'hl_Ids'</span>])){<span class="php-var">$GLOBALS</span>[<span class="php-quote">'hl_Ids'</span>] = <span class="php-keyword1">array</span>();}
</span><span id="104" class="l"><a href="#104">104: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook'</span>]){<span class="php-var">$t</span> = <span class="php-var">$C</span>[<span class="php-quote">'hook'</span>](<span class="php-var">$t</span>, <span class="php-var">$C</span>, <span class="php-var">$S</span>);}
</span><span id="105" class="l"><a href="#105">105: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>] &amp;&amp; <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^[a-z][a-z0-9_]*$`i'</span>, <span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>])){
</span><span id="106" class="l"><a href="#106">106: </a> <span class="php-var">$GLOBALS</span>[<span class="php-var">$C</span>[<span class="php-quote">'show_setting'</span>]] = <span class="php-keyword1">array</span>(<span class="php-quote">'config'</span>=&gt;<span class="php-var">$C</span>, <span class="php-quote">'spec'</span>=&gt;<span class="php-var">$S</span>, <span class="php-quote">'time'</span>=&gt;<span class="php-keyword2">microtime</span>());
</span><span id="107" class="l"><a href="#107">107: </a>}
</span><span id="108" class="l"><a href="#108">108: </a><span class="php-comment">// main</span>
</span><span id="109" class="l"><a href="#109">109: </a><span class="php-var">$t</span> = <span class="php-keyword2">preg_replace_callback</span>(<span class="php-quote">'`&lt;(?:(?:\s|$)|(?:[^&gt;]*(?:&gt;|$)))|&gt;`m'</span>, <span class="php-quote">'\DataTables\Vendor\htmLawed::hl_tag'</span>, <span class="php-var">$t</span>);
</span><span id="110" class="l"><a href="#110">110: </a><span class="php-var">$t</span> = <span class="php-var">$C</span>[<span class="php-quote">'balance'</span>] ? \DataTables\Vendor\htmLawed::hl_bal(<span class="php-var">$t</span>, <span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>], <span class="php-var">$C</span>[<span class="php-quote">'parent'</span>]) : <span class="php-var">$t</span>;
</span><span id="111" class="l"><a href="#111">111: </a><span class="php-var">$t</span> = ((<span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>] <span class="php-keyword1">or</span> <span class="php-var">$C</span>[<span class="php-quote">'comment'</span>]) &amp;&amp; <span class="php-keyword2">strpos</span>(<span class="php-var">$t</span>, <span class="php-quote">&quot;\x01&quot;</span>) !== <span class="php-keyword1">false</span>) ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x01&quot;</span>, <span class="php-quote">&quot;\x02&quot;</span>, <span class="php-quote">&quot;\x03&quot;</span>, <span class="php-quote">&quot;\x04&quot;</span>, <span class="php-quote">&quot;\x05&quot;</span>), <span class="php-keyword1">array</span>(<span class="php-quote">''</span>, <span class="php-quote">''</span>, <span class="php-quote">'&amp;'</span>, <span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-var">$t</span>) : <span class="php-var">$t</span>;
</span><span id="112" class="l"><a href="#112">112: </a><span class="php-var">$t</span> = <span class="php-var">$C</span>[<span class="php-quote">'tidy'</span>] ? \DataTables\Vendor\htmLawed::hl_tidy(<span class="php-var">$t</span>, <span class="php-var">$C</span>[<span class="php-quote">'tidy'</span>], <span class="php-var">$C</span>[<span class="php-quote">'parent'</span>]) : <span class="php-var">$t</span>;
</span><span id="113" class="l"><a href="#113">113: </a><span class="php-keyword1">unset</span>(<span class="php-var">$C</span>, <span class="php-var">$e</span>);
</span><span id="114" class="l"><a href="#114">114: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$reC</span>)){<span class="php-var">$GLOBALS</span>[<span class="php-quote">'C'</span>] = <span class="php-var">$reC</span>;}
</span><span id="115" class="l"><a href="#115">115: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$reS</span>)){<span class="php-var">$GLOBALS</span>[<span class="php-quote">'S'</span>] = <span class="php-var">$reS</span>;}
</span><span id="116" class="l"><a href="#116">116: </a><span class="php-keyword1">return</span> <span class="php-var">$t</span>;
</span><span id="117" class="l"><a href="#117">117: </a><span class="php-comment">// eof</span>
</span><span id="118" class="l"><a href="#118">118: </a>}
</span><span id="119" class="l"><a href="#119">119: </a>
</span><span id="120" class="l"><a href="#120">120: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_attrval(<span class="php-var">$a</span>, <span class="php-var">$t</span>, <span class="php-var">$p</span>){
</span><span id="121" class="l"><a href="#121">121: </a><span class="php-comment">// check attr val against $S</span>
</span><span id="122" class="l"><a href="#122">122: </a><span class="php-keyword1">static</span> <span class="php-var">$ma</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'accesskey'</span>, <span class="php-quote">'class'</span>, <span class="php-quote">'rel'</span>);
</span><span id="123" class="l"><a href="#123">123: </a><span class="php-var">$s</span> = <span class="php-keyword2">in_array</span>(<span class="php-var">$a</span>, <span class="php-var">$ma</span>) ? <span class="php-quote">' '</span> : <span class="php-quote">''</span>;
</span><span id="124" class="l"><a href="#124">124: </a><span class="php-var">$r</span> = <span class="php-keyword1">array</span>();
</span><span id="125" class="l"><a href="#125">125: </a><span class="php-var">$t</span> = !<span class="php-keyword1">empty</span>(<span class="php-var">$s</span>) ? <span class="php-keyword2">explode</span>(<span class="php-var">$s</span>, <span class="php-var">$t</span>) : <span class="php-keyword1">array</span>(<span class="php-var">$t</span>);
</span><span id="126" class="l"><a href="#126">126: </a><span class="php-keyword1">foreach</span>(<span class="php-var">$t</span> <span class="php-keyword1">as</span> <span class="php-var">$tk</span>=&gt;<span class="php-var">$tv</span>){
</span><span id="127" class="l"><a href="#127">127: </a> <span class="php-var">$o</span> = <span class="php-num">1</span>; <span class="php-var">$l</span> = <span class="php-keyword2">strlen</span>(<span class="php-var">$tv</span>);
</span><span id="128" class="l"><a href="#128">128: </a> <span class="php-keyword1">foreach</span>(<span class="php-var">$p</span> <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){
</span><span id="129" class="l"><a href="#129">129: </a>  <span class="php-keyword1">switch</span>(<span class="php-var">$k</span>){
</span><span id="130" class="l"><a href="#130">130: </a>   <span class="php-keyword1">case</span> <span class="php-quote">'maxlen'</span>: <span class="php-keyword1">if</span>(<span class="php-var">$l</span> &gt; <span class="php-var">$v</span>){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="131" class="l"><a href="#131">131: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'minlen'</span>: <span class="php-keyword1">if</span>(<span class="php-var">$l</span> &lt; <span class="php-var">$v</span>){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="132" class="l"><a href="#132">132: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'maxval'</span>: <span class="php-keyword1">if</span>((float)(<span class="php-var">$tv</span>) &gt; <span class="php-var">$v</span>){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="133" class="l"><a href="#133">133: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'minval'</span>: <span class="php-keyword1">if</span>((float)(<span class="php-var">$tv</span>) &lt; <span class="php-var">$v</span>){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="134" class="l"><a href="#134">134: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'match'</span>: <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-var">$v</span>, <span class="php-var">$tv</span>)){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="135" class="l"><a href="#135">135: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'nomatch'</span>: <span class="php-keyword1">if</span>(<span class="php-keyword2">preg_match</span>(<span class="php-var">$v</span>, <span class="php-var">$tv</span>)){<span class="php-var">$o</span> = <span class="php-num">0</span>;}
</span><span id="136" class="l"><a href="#136">136: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'oneof'</span>:
</span><span id="137" class="l"><a href="#137">137: </a>    <span class="php-var">$m</span> = <span class="php-num">0</span>;
</span><span id="138" class="l"><a href="#138">138: </a>    <span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">'|'</span>, <span class="php-var">$v</span>) <span class="php-keyword1">as</span> <span class="php-var">$n</span>){<span class="php-keyword1">if</span>(<span class="php-var">$tv</span> == <span class="php-var">$n</span>){<span class="php-var">$m</span> = <span class="php-num">1</span>; <span class="php-keyword1">break</span>;}}
</span><span id="139" class="l"><a href="#139">139: </a>    <span class="php-var">$o</span> = <span class="php-var">$m</span>;
</span><span id="140" class="l"><a href="#140">140: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-quote">'noneof'</span>:
</span><span id="141" class="l"><a href="#141">141: </a>    <span class="php-var">$m</span> = <span class="php-num">1</span>;
</span><span id="142" class="l"><a href="#142">142: </a>    <span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">'|'</span>, <span class="php-var">$v</span>) <span class="php-keyword1">as</span> <span class="php-var">$n</span>){<span class="php-keyword1">if</span>(<span class="php-var">$tv</span> == <span class="php-var">$n</span>){<span class="php-var">$m</span> = <span class="php-num">0</span>; <span class="php-keyword1">break</span>;}}
</span><span id="143" class="l"><a href="#143">143: </a>    <span class="php-var">$o</span> = <span class="php-var">$m</span>;
</span><span id="144" class="l"><a href="#144">144: </a>   <span class="php-keyword1">break</span>; <span class="php-keyword1">default</span>:
</span><span id="145" class="l"><a href="#145">145: </a>   <span class="php-keyword1">break</span>;
</span><span id="146" class="l"><a href="#146">146: </a>  }
</span><span id="147" class="l"><a href="#147">147: </a>  <span class="php-keyword1">if</span>(!<span class="php-var">$o</span>){<span class="php-keyword1">break</span>;}
</span><span id="148" class="l"><a href="#148">148: </a> }
</span><span id="149" class="l"><a href="#149">149: </a> <span class="php-keyword1">if</span>(<span class="php-var">$o</span>){<span class="php-var">$r</span>[] = <span class="php-var">$tv</span>;}
</span><span id="150" class="l"><a href="#150">150: </a>}
</span><span id="151" class="l"><a href="#151">151: </a><span class="php-var">$r</span> = <span class="php-keyword2">implode</span>(<span class="php-var">$s</span>, <span class="php-var">$r</span>);
</span><span id="152" class="l"><a href="#152">152: </a><span class="php-keyword1">return</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$r</span>[<span class="php-num">0</span>]) ? <span class="php-var">$r</span> : (<span class="php-keyword1">isset</span>(<span class="php-var">$p</span>[<span class="php-quote">'default'</span>]) ? <span class="php-var">$p</span>[<span class="php-quote">'default'</span>] : <span class="php-num">0</span>));
</span><span id="153" class="l"><a href="#153">153: </a><span class="php-comment">// eof</span>
</span><span id="154" class="l"><a href="#154">154: </a>}
</span><span id="155" class="l"><a href="#155">155: </a>
</span><span id="156" class="l"><a href="#156">156: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_bal(<span class="php-var">$t</span>, <span class="php-var">$do</span>=<span class="php-num">1</span>, <span class="php-var">$in</span>=<span class="php-quote">'div'</span>){
</span><span id="157" class="l"><a href="#157">157: </a><span class="php-comment">// balance tags</span>
</span><span id="158" class="l"><a href="#158">158: </a><span class="php-comment">// by content</span>
</span><span id="159" class="l"><a href="#159">159: </a><span class="php-var">$cB</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Block</span>
</span><span id="160" class="l"><a href="#160">160: </a><span class="php-var">$cE</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Empty</span>
</span><span id="161" class="l"><a href="#161">161: </a><span class="php-var">$cF</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Flow; later context-wise dynamic move of ins &amp; del to $cI</span>
</span><span id="162" class="l"><a href="#162">162: </a><span class="php-var">$cI</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'abbr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'acronym'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'address'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'b'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'big'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'cite'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'code'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dfn'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'em'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'i'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'kbd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'q'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rb'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'s'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'samp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'small'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'span'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strike'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strong'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'u'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'var'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Inline</span>
</span><span id="163" class="l"><a href="#163">163: </a><span class="php-var">$cN</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'button'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'fieldset'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'form'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'label'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'noscript'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'pre'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'big'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'small'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rb'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rt'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>)); <span class="php-comment">// Illegal</span>
</span><span id="164" class="l"><a href="#164">164: </a><span class="php-var">$cN2</span> = <span class="php-keyword2">array_keys</span>(<span class="php-var">$cN</span>);
</span><span id="165" class="l"><a href="#165">165: </a><span class="php-var">$cR</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rbc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>);
</span><span id="166" class="l"><a href="#166">166: </a><span class="php-var">$cS</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'colgroup'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'dir'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'dl'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'menu'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'ol'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'optgroup'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'option'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rbc'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'rb'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rp'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rtc'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'rt'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'ruby'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'rb'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rbc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtc'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'select'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'script'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'table'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'tbody'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'tfoot'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'textarea'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'thead'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'tr'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'ul'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>)); <span class="php-comment">// Specific - immediate parent-child</span>
</span><span id="167" class="l"><a href="#167">167: </a><span class="php-keyword1">if</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'C'</span>][<span class="php-quote">'direct_list_nest'</span>]){<span class="php-var">$cS</span>[<span class="php-quote">'ol'</span>] = <span class="php-var">$cS</span>[<span class="php-quote">'ul'</span>] += <span class="php-keyword1">array</span>(<span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>);}
</span><span id="168" class="l"><a href="#168">168: </a><span class="php-var">$cO</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'address'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'applet'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'blockquote'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'fieldset'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'form'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'map'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'object'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>)); <span class="php-comment">// Other</span>
</span><span id="169" class="l"><a href="#169">169: </a><span class="php-var">$cT</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Omitable closing</span>
</span><span id="170" class="l"><a href="#170">170: </a><span class="php-comment">// block/inline type; ins &amp; del both type; #pcdata: text</span>
</span><span id="171" class="l"><a href="#171">171: </a><span class="php-var">$eB</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'address'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'center'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>);
</span><span id="172" class="l"><a href="#172">172: </a><span class="php-var">$eI</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'#pcdata'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'abbr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'acronym'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'b'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'big'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'cite'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'code'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dfn'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'em'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'i'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'kbd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'q'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'s'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'samp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'small'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'span'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strike'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strong'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'u'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'var'</span>=&gt;<span class="php-num">1</span>);
</span><span id="173" class="l"><a href="#173">173: </a><span class="php-var">$eN</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'big'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'small'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Exclude from specific ele; $cN values</span>
</span><span id="174" class="l"><a href="#174">174: </a><span class="php-var">$eO</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rb'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rbc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rp'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Missing in $eB &amp; $eI</span>
</span><span id="175" class="l"><a href="#175">175: </a><span class="php-var">$eF</span> = <span class="php-var">$eB</span> + <span class="php-var">$eI</span>;
</span><span id="176" class="l"><a href="#176">176: </a>
</span><span id="177" class="l"><a href="#177">177: </a><span class="php-comment">// $in sets allowed child</span>
</span><span id="178" class="l"><a href="#178">178: </a><span class="php-var">$in</span> = ((<span class="php-keyword1">isset</span>(<span class="php-var">$eF</span>[<span class="php-var">$in</span>]) &amp;&amp; <span class="php-var">$in</span> != <span class="php-quote">'#pcdata'</span>) <span class="php-keyword1">or</span> <span class="php-keyword1">isset</span>(<span class="php-var">$eO</span>[<span class="php-var">$in</span>])) ? <span class="php-var">$in</span> : <span class="php-quote">'div'</span>;
</span><span id="179" class="l"><a href="#179">179: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cE</span>[<span class="php-var">$in</span>])){
</span><span id="180" class="l"><a href="#180">180: </a> <span class="php-keyword1">return</span> (!<span class="php-var">$do</span> ? <span class="php-quote">''</span> : <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>));
</span><span id="181" class="l"><a href="#181">181: </a>}
</span><span id="182" class="l"><a href="#182">182: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cS</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-var">$cS</span>[<span class="php-var">$in</span>];}
</span><span id="183" class="l"><a href="#183">183: </a><span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cI</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-var">$eI</span>; <span class="php-var">$cI</span>[<span class="php-quote">'del'</span>] = <span class="php-num">1</span>; <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>] = <span class="php-num">1</span>;}
</span><span id="184" class="l"><a href="#184">184: </a><span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cF</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-var">$eF</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="185" class="l"><a href="#185">185: </a><span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-var">$eB</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="186" class="l"><a href="#186">186: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cO</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-var">$inOk</span> + <span class="php-var">$cO</span>[<span class="php-var">$in</span>];}
</span><span id="187" class="l"><a href="#187">187: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cN</span>[<span class="php-var">$in</span>])){<span class="php-var">$inOk</span> = <span class="php-keyword2">array_diff_assoc</span>(<span class="php-var">$inOk</span>, <span class="php-var">$cN</span>[<span class="php-var">$in</span>]);}
</span><span id="188" class="l"><a href="#188">188: </a>
</span><span id="189" class="l"><a href="#189">189: </a><span class="php-var">$t</span> = <span class="php-keyword2">explode</span>(<span class="php-quote">'&lt;'</span>, <span class="php-var">$t</span>);
</span><span id="190" class="l"><a href="#190">190: </a><span class="php-var">$ok</span> = <span class="php-var">$q</span> = <span class="php-keyword1">array</span>(); <span class="php-comment">// $q seq list of open non-empty ele</span>
</span><span id="191" class="l"><a href="#191">191: </a><span class="php-keyword2">ob_start</span>();
</span><span id="192" class="l"><a href="#192">192: </a>
</span><span id="193" class="l"><a href="#193">193: </a><span class="php-keyword1">for</span>(<span class="php-var">$i</span>=-<span class="php-num">1</span>, <span class="php-var">$ci</span>=<span class="php-keyword2">count</span>(<span class="php-var">$t</span>); ++<span class="php-var">$i</span>&lt;<span class="php-var">$ci</span>;){
</span><span id="194" class="l"><a href="#194">194: </a> <span class="php-comment">// allowed $ok in parent $p</span>
</span><span id="195" class="l"><a href="#195">195: </a> <span class="php-keyword1">if</span>(<span class="php-var">$ql</span> = <span class="php-keyword2">count</span>(<span class="php-var">$q</span>)){
</span><span id="196" class="l"><a href="#196">196: </a>  <span class="php-var">$p</span> = <span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>);
</span><span id="197" class="l"><a href="#197">197: </a>  <span class="php-var">$q</span>[] = <span class="php-var">$p</span>;
</span><span id="198" class="l"><a href="#198">198: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cS</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$cS</span>[<span class="php-var">$p</span>];}
</span><span id="199" class="l"><a href="#199">199: </a>  <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cI</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eI</span>; <span class="php-var">$cI</span>[<span class="php-quote">'del'</span>] = <span class="php-num">1</span>; <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>] = <span class="php-num">1</span>;}
</span><span id="200" class="l"><a href="#200">200: </a>  <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cF</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eF</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="201" class="l"><a href="#201">201: </a>  <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eB</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="202" class="l"><a href="#202">202: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cO</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$ok</span> + <span class="php-var">$cO</span>[<span class="php-var">$p</span>];}
</span><span id="203" class="l"><a href="#203">203: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cN</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-keyword2">array_diff_assoc</span>(<span class="php-var">$ok</span>, <span class="php-var">$cN</span>[<span class="php-var">$p</span>]);}
</span><span id="204" class="l"><a href="#204">204: </a> }<span class="php-keyword1">else</span>{<span class="php-var">$ok</span> = <span class="php-var">$inOk</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="205" class="l"><a href="#205">205: </a> <span class="php-comment">// bad tags, &amp; ele content</span>
</span><span id="206" class="l"><a href="#206">206: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$e</span>) &amp;&amp; (<span class="php-var">$do</span> == <span class="php-num">1</span> <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-quote">'#pcdata'</span>]) &amp;&amp; (<span class="php-var">$do</span> == <span class="php-num">3</span> <span class="php-keyword1">or</span> <span class="php-var">$do</span> == <span class="php-num">5</span>)))){
</span><span id="207" class="l"><a href="#207">207: </a>  <span class="php-keyword1">echo</span> <span class="php-quote">'&amp;lt;'</span>, <span class="php-var">$s</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-quote">'&amp;gt;'</span>;
</span><span id="208" class="l"><a href="#208">208: </a> }
</span><span id="209" class="l"><a href="#209">209: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$x</span>[<span class="php-num">0</span>])){
</span><span id="210" class="l"><a href="#210">210: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword2">strlen</span>(<span class="php-keyword2">trim</span>(<span class="php-var">$x</span>)) &amp;&amp; ((<span class="php-var">$ql</span> &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$p</span>])) <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$in</span>]) &amp;&amp; !<span class="php-var">$ql</span>))){
</span><span id="211" class="l"><a href="#211">211: </a>   <span class="php-keyword1">echo</span> <span class="php-quote">'&lt;div&gt;'</span>, <span class="php-var">$x</span>, <span class="php-quote">'&lt;/div&gt;'</span>;
</span><span id="212" class="l"><a href="#212">212: </a>  }
</span><span id="213" class="l"><a href="#213">213: </a>  <span class="php-keyword1">elseif</span>(<span class="php-var">$do</span> &lt; <span class="php-num">3</span> <span class="php-keyword1">or</span> <span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-quote">'#pcdata'</span>])){<span class="php-keyword1">echo</span> <span class="php-var">$x</span>;}
</span><span id="214" class="l"><a href="#214">214: </a>  <span class="php-keyword1">elseif</span>(<span class="php-keyword2">strpos</span>(<span class="php-var">$x</span>, <span class="php-quote">&quot;\x02\x04&quot;</span>)){
</span><span id="215" class="l"><a href="#215">215: </a>   <span class="php-keyword1">foreach</span>(<span class="php-keyword2">preg_split</span>(<span class="php-quote">'`(\x01\x02[^\x01\x02]+\x02\x01)`'</span>, <span class="php-var">$x</span>, -<span class="php-num">1</span>, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY) <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="216" class="l"><a href="#216">216: </a>    <span class="php-keyword1">echo</span> (<span class="php-keyword2">substr</span>(<span class="php-var">$v</span>, <span class="php-num">0</span>, <span class="php-num">2</span>) == <span class="php-quote">&quot;\x01\x02&quot;</span> ? <span class="php-var">$v</span> : (<span class="php-var">$do</span> &gt; <span class="php-num">4</span> ? <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\S`'</span>, <span class="php-quote">''</span>, <span class="php-var">$v</span>) : <span class="php-quote">''</span>));
</span><span id="217" class="l"><a href="#217">217: </a>   }
</span><span id="218" class="l"><a href="#218">218: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$do</span> &gt; <span class="php-num">4</span>){<span class="php-keyword1">echo</span> <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\S`'</span>, <span class="php-quote">''</span>, <span class="php-var">$x</span>);}
</span><span id="219" class="l"><a href="#219">219: </a> }
</span><span id="220" class="l"><a href="#220">220: </a> <span class="php-comment">// get markup</span>
</span><span id="221" class="l"><a href="#221">221: </a> <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^(/?)([a-z1-6]+)([^&gt;]*)&gt;(.*)`sm'</span>, <span class="php-var">$t</span>[<span class="php-var">$i</span>], <span class="php-var">$r</span>)){<span class="php-var">$x</span> = <span class="php-var">$t</span>[<span class="php-var">$i</span>]; <span class="php-keyword1">continue</span>;}
</span><span id="222" class="l"><a href="#222">222: </a> <span class="php-var">$s</span> = <span class="php-keyword1">null</span>; <span class="php-var">$e</span> = <span class="php-keyword1">null</span>; <span class="php-var">$a</span> = <span class="php-keyword1">null</span>; <span class="php-var">$x</span> = <span class="php-keyword1">null</span>; <span class="php-keyword1">list</span>(<span class="php-var">$all</span>, <span class="php-var">$s</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-var">$x</span>) = <span class="php-var">$r</span>;
</span><span id="223" class="l"><a href="#223">223: </a> <span class="php-comment">// close tag</span>
</span><span id="224" class="l"><a href="#224">224: </a> <span class="php-keyword1">if</span>(<span class="php-var">$s</span>){
</span><span id="225" class="l"><a href="#225">225: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cE</span>[<span class="php-var">$e</span>]) <span class="php-keyword1">or</span> !<span class="php-keyword2">in_array</span>(<span class="php-var">$e</span>, <span class="php-var">$q</span>)){<span class="php-keyword1">continue</span>;} <span class="php-comment">// Empty/unopen</span>
</span><span id="226" class="l"><a href="#226">226: </a>  <span class="php-keyword1">if</span>(<span class="php-var">$p</span> == <span class="php-var">$e</span>){<span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>); <span class="php-keyword1">echo</span> <span class="php-quote">'&lt;/'</span>, <span class="php-var">$e</span>, <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>); <span class="php-keyword1">continue</span>;} <span class="php-comment">// Last open</span>
</span><span id="227" class="l"><a href="#227">227: </a>  <span class="php-var">$add</span> = <span class="php-quote">''</span>; <span class="php-comment">// Nesting - close open tags that need to be</span>
</span><span id="228" class="l"><a href="#228">228: </a>  <span class="php-keyword1">for</span>(<span class="php-var">$j</span>=-<span class="php-num">1</span>, <span class="php-var">$cj</span>=<span class="php-keyword2">count</span>(<span class="php-var">$q</span>); ++<span class="php-var">$j</span>&lt;<span class="php-var">$cj</span>;){
</span><span id="229" class="l"><a href="#229">229: </a>   <span class="php-keyword1">if</span>((<span class="php-var">$d</span> = <span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>)) == <span class="php-var">$e</span>){<span class="php-keyword1">break</span>;}
</span><span id="230" class="l"><a href="#230">230: </a>   <span class="php-keyword1">else</span>{<span class="php-var">$add</span> .= <span class="php-quote">&quot;&lt;/</span><span class="php-var">{$d}</span><span class="php-quote">&gt;&quot;</span>;}
</span><span id="231" class="l"><a href="#231">231: </a>  }
</span><span id="232" class="l"><a href="#232">232: </a>  <span class="php-keyword1">echo</span> <span class="php-var">$add</span>, <span class="php-quote">'&lt;/'</span>, <span class="php-var">$e</span>, <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>); <span class="php-keyword1">continue</span>;
</span><span id="233" class="l"><a href="#233">233: </a> }
</span><span id="234" class="l"><a href="#234">234: </a> <span class="php-comment">// open tag</span>
</span><span id="235" class="l"><a href="#235">235: </a> <span class="php-comment">// $cB ele needs $eB ele as child</span>
</span><span id="236" class="l"><a href="#236">236: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$e</span>]) &amp;&amp; <span class="php-keyword2">strlen</span>(<span class="php-keyword2">trim</span>(<span class="php-var">$x</span>))){
</span><span id="237" class="l"><a href="#237">237: </a>  <span class="php-var">$t</span>[<span class="php-var">$i</span>] = <span class="php-quote">&quot;</span><span class="php-var">{$e}{$a}</span><span class="php-quote">&gt;&quot;</span>;
</span><span id="238" class="l"><a href="#238">238: </a>  <span class="php-keyword2">array_splice</span>(<span class="php-var">$t</span>, <span class="php-var">$i</span>+<span class="php-num">1</span>, <span class="php-num">0</span>, <span class="php-quote">'div&gt;'</span>. <span class="php-var">$x</span>); <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>, <span class="php-var">$x</span>); ++<span class="php-var">$ci</span>; --<span class="php-var">$i</span>; <span class="php-keyword1">continue</span>;
</span><span id="239" class="l"><a href="#239">239: </a> }
</span><span id="240" class="l"><a href="#240">240: </a> <span class="php-keyword1">if</span>(((<span class="php-var">$ql</span> &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$p</span>])) <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$in</span>]) &amp;&amp; !<span class="php-var">$ql</span>)) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$eB</span>[<span class="php-var">$e</span>]) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-var">$e</span>])){
</span><span id="241" class="l"><a href="#241">241: </a>  <span class="php-keyword2">array_splice</span>(<span class="php-var">$t</span>, <span class="php-var">$i</span>, <span class="php-num">0</span>, <span class="php-quote">'div&gt;'</span>); <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>, <span class="php-var">$x</span>); ++<span class="php-var">$ci</span>; --<span class="php-var">$i</span>; <span class="php-keyword1">continue</span>;
</span><span id="242" class="l"><a href="#242">242: </a> }
</span><span id="243" class="l"><a href="#243">243: </a> <span class="php-comment">// if no open ele, $in = parent; mostly immediate parent-child relation should hold</span>
</span><span id="244" class="l"><a href="#244">244: </a> <span class="php-keyword1">if</span>(!<span class="php-var">$ql</span> <span class="php-keyword1">or</span> !<span class="php-keyword1">isset</span>(<span class="php-var">$eN</span>[<span class="php-var">$e</span>]) <span class="php-keyword1">or</span> !<span class="php-keyword2">array_intersect</span>(<span class="php-var">$q</span>, <span class="php-var">$cN2</span>)){
</span><span id="245" class="l"><a href="#245">245: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-var">$e</span>])){
</span><span id="246" class="l"><a href="#246">246: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$ql</span> &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$cT</span>[<span class="php-var">$p</span>])){<span class="php-keyword1">echo</span> <span class="php-quote">'&lt;/'</span>, <span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>), <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>, <span class="php-var">$x</span>); --<span class="php-var">$i</span>;}
</span><span id="247" class="l"><a href="#247">247: </a>   <span class="php-keyword1">continue</span>;
</span><span id="248" class="l"><a href="#248">248: </a>  }
</span><span id="249" class="l"><a href="#249">249: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$cE</span>[<span class="php-var">$e</span>])){<span class="php-var">$q</span>[] = <span class="php-var">$e</span>;}
</span><span id="250" class="l"><a href="#250">250: </a>  <span class="php-keyword1">echo</span> <span class="php-quote">'&lt;'</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>); <span class="php-keyword1">continue</span>;
</span><span id="251" class="l"><a href="#251">251: </a> }
</span><span id="252" class="l"><a href="#252">252: </a> <span class="php-comment">// specific parent-child</span>
</span><span id="253" class="l"><a href="#253">253: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cS</span>[<span class="php-var">$p</span>][<span class="php-var">$e</span>])){
</span><span id="254" class="l"><a href="#254">254: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$cE</span>[<span class="php-var">$e</span>])){<span class="php-var">$q</span>[] = <span class="php-var">$e</span>;}
</span><span id="255" class="l"><a href="#255">255: </a>  <span class="php-keyword1">echo</span> <span class="php-quote">'&lt;'</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>); <span class="php-keyword1">continue</span>;
</span><span id="256" class="l"><a href="#256">256: </a> }
</span><span id="257" class="l"><a href="#257">257: </a> <span class="php-comment">// nesting</span>
</span><span id="258" class="l"><a href="#258">258: </a> <span class="php-var">$add</span> = <span class="php-quote">''</span>;
</span><span id="259" class="l"><a href="#259">259: </a> <span class="php-var">$q2</span> = <span class="php-keyword1">array</span>();
</span><span id="260" class="l"><a href="#260">260: </a> <span class="php-keyword1">for</span>(<span class="php-var">$k</span>=-<span class="php-num">1</span>, <span class="php-var">$kc</span>=<span class="php-keyword2">count</span>(<span class="php-var">$q</span>); ++<span class="php-var">$k</span>&lt;<span class="php-var">$kc</span>;){
</span><span id="261" class="l"><a href="#261">261: </a>  <span class="php-var">$d</span> = <span class="php-var">$q</span>[<span class="php-var">$k</span>];
</span><span id="262" class="l"><a href="#262">262: </a>  <span class="php-var">$ok2</span> = <span class="php-keyword1">array</span>();
</span><span id="263" class="l"><a href="#263">263: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cS</span>[<span class="php-var">$d</span>])){<span class="php-var">$q2</span>[] = <span class="php-var">$d</span>; <span class="php-keyword1">continue</span>;}
</span><span id="264" class="l"><a href="#264">264: </a>  <span class="php-var">$ok2</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$cI</span>[<span class="php-var">$d</span>]) ? <span class="php-var">$eI</span> : <span class="php-var">$eF</span>;
</span><span id="265" class="l"><a href="#265">265: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cO</span>[<span class="php-var">$d</span>])){<span class="php-var">$ok2</span> = <span class="php-var">$ok2</span> + <span class="php-var">$cO</span>[<span class="php-var">$d</span>];}
</span><span id="266" class="l"><a href="#266">266: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cN</span>[<span class="php-var">$d</span>])){<span class="php-var">$ok2</span> = <span class="php-keyword2">array_diff_assoc</span>(<span class="php-var">$ok2</span>, <span class="php-var">$cN</span>[<span class="php-var">$d</span>]);}
</span><span id="267" class="l"><a href="#267">267: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$ok2</span>[<span class="php-var">$e</span>])){
</span><span id="268" class="l"><a href="#268">268: </a>   <span class="php-keyword1">if</span>(!<span class="php-var">$k</span> &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$inOk</span>[<span class="php-var">$e</span>])){<span class="php-keyword1">continue</span> <span class="php-num">2</span>;}
</span><span id="269" class="l"><a href="#269">269: </a>   <span class="php-var">$add</span> = <span class="php-quote">&quot;&lt;/</span><span class="php-var">{$d}</span><span class="php-quote">&gt;&quot;</span>;
</span><span id="270" class="l"><a href="#270">270: </a>   <span class="php-keyword1">for</span>(;++<span class="php-var">$k</span>&lt;<span class="php-var">$kc</span>;){<span class="php-var">$add</span> = <span class="php-quote">&quot;&lt;/</span><span class="php-var">{$q[$k]}</span><span class="php-quote">&gt;</span><span class="php-var">{$add}</span><span class="php-quote">&quot;</span>;}
</span><span id="271" class="l"><a href="#271">271: </a>   <span class="php-keyword1">break</span>;
</span><span id="272" class="l"><a href="#272">272: </a>  }
</span><span id="273" class="l"><a href="#273">273: </a>  <span class="php-keyword1">else</span>{<span class="php-var">$q2</span>[] = <span class="php-var">$d</span>;}
</span><span id="274" class="l"><a href="#274">274: </a> }
</span><span id="275" class="l"><a href="#275">275: </a> <span class="php-var">$q</span> = <span class="php-var">$q2</span>;
</span><span id="276" class="l"><a href="#276">276: </a> <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$cE</span>[<span class="php-var">$e</span>])){<span class="php-var">$q</span>[] = <span class="php-var">$e</span>;}
</span><span id="277" class="l"><a href="#277">277: </a> <span class="php-keyword1">echo</span> <span class="php-var">$add</span>, <span class="php-quote">'&lt;'</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-quote">'&gt;'</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$e</span>); <span class="php-keyword1">continue</span>;
</span><span id="278" class="l"><a href="#278">278: </a>}
</span><span id="279" class="l"><a href="#279">279: </a>
</span><span id="280" class="l"><a href="#280">280: </a><span class="php-comment">// end</span>
</span><span id="281" class="l"><a href="#281">281: </a><span class="php-keyword1">if</span>(<span class="php-var">$ql</span> = <span class="php-keyword2">count</span>(<span class="php-var">$q</span>)){
</span><span id="282" class="l"><a href="#282">282: </a> <span class="php-var">$p</span> = <span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>);
</span><span id="283" class="l"><a href="#283">283: </a> <span class="php-var">$q</span>[] = <span class="php-var">$p</span>;
</span><span id="284" class="l"><a href="#284">284: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cS</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$cS</span>[<span class="php-var">$p</span>];}
</span><span id="285" class="l"><a href="#285">285: </a> <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cI</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eI</span>; <span class="php-var">$cI</span>[<span class="php-quote">'del'</span>] = <span class="php-num">1</span>; <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>] = <span class="php-num">1</span>;}
</span><span id="286" class="l"><a href="#286">286: </a> <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cF</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eF</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="287" class="l"><a href="#287">287: </a> <span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$eB</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="288" class="l"><a href="#288">288: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cO</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-var">$ok</span> + <span class="php-var">$cO</span>[<span class="php-var">$p</span>];}
</span><span id="289" class="l"><a href="#289">289: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$cN</span>[<span class="php-var">$p</span>])){<span class="php-var">$ok</span> = <span class="php-keyword2">array_diff_assoc</span>(<span class="php-var">$ok</span>, <span class="php-var">$cN</span>[<span class="php-var">$p</span>]);}
</span><span id="290" class="l"><a href="#290">290: </a>}<span class="php-keyword1">else</span>{<span class="php-var">$ok</span> = <span class="php-var">$inOk</span>; <span class="php-keyword1">unset</span>(<span class="php-var">$cI</span>[<span class="php-quote">'del'</span>], <span class="php-var">$cI</span>[<span class="php-quote">'ins'</span>]);}
</span><span id="291" class="l"><a href="#291">291: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$e</span>) &amp;&amp; (<span class="php-var">$do</span> == <span class="php-num">1</span> <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-quote">'#pcdata'</span>]) &amp;&amp; (<span class="php-var">$do</span> == <span class="php-num">3</span> <span class="php-keyword1">or</span> <span class="php-var">$do</span> == <span class="php-num">5</span>)))){
</span><span id="292" class="l"><a href="#292">292: </a> <span class="php-keyword1">echo</span> <span class="php-quote">'&amp;lt;'</span>, <span class="php-var">$s</span>, <span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-quote">'&amp;gt;'</span>;
</span><span id="293" class="l"><a href="#293">293: </a>}
</span><span id="294" class="l"><a href="#294">294: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$x</span>[<span class="php-num">0</span>])){
</span><span id="295" class="l"><a href="#295">295: </a> <span class="php-keyword1">if</span>(<span class="php-keyword2">strlen</span>(<span class="php-keyword2">trim</span>(<span class="php-var">$x</span>)) &amp;&amp; ((<span class="php-var">$ql</span> &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$p</span>])) <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$cB</span>[<span class="php-var">$in</span>]) &amp;&amp; !<span class="php-var">$ql</span>))){
</span><span id="296" class="l"><a href="#296">296: </a>  <span class="php-keyword1">echo</span> <span class="php-quote">'&lt;div&gt;'</span>, <span class="php-var">$x</span>, <span class="php-quote">'&lt;/div&gt;'</span>;
</span><span id="297" class="l"><a href="#297">297: </a> }
</span><span id="298" class="l"><a href="#298">298: </a> <span class="php-keyword1">elseif</span>(<span class="php-var">$do</span> &lt; <span class="php-num">3</span> <span class="php-keyword1">or</span> <span class="php-keyword1">isset</span>(<span class="php-var">$ok</span>[<span class="php-quote">'#pcdata'</span>])){<span class="php-keyword1">echo</span> <span class="php-var">$x</span>;}
</span><span id="299" class="l"><a href="#299">299: </a> <span class="php-keyword1">elseif</span>(<span class="php-keyword2">strpos</span>(<span class="php-var">$x</span>, <span class="php-quote">&quot;\x02\x04&quot;</span>)){
</span><span id="300" class="l"><a href="#300">300: </a>  <span class="php-keyword1">foreach</span>(<span class="php-keyword2">preg_split</span>(<span class="php-quote">'`(\x01\x02[^\x01\x02]+\x02\x01)`'</span>, <span class="php-var">$x</span>, -<span class="php-num">1</span>, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY) <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="301" class="l"><a href="#301">301: </a>   <span class="php-keyword1">echo</span> (<span class="php-keyword2">substr</span>(<span class="php-var">$v</span>, <span class="php-num">0</span>, <span class="php-num">2</span>) == <span class="php-quote">&quot;\x01\x02&quot;</span> ? <span class="php-var">$v</span> : (<span class="php-var">$do</span> &gt; <span class="php-num">4</span> ? <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\S`'</span>, <span class="php-quote">''</span>, <span class="php-var">$v</span>) : <span class="php-quote">''</span>));
</span><span id="302" class="l"><a href="#302">302: </a>  }
</span><span id="303" class="l"><a href="#303">303: </a> }<span class="php-keyword1">elseif</span>(<span class="php-var">$do</span> &gt; <span class="php-num">4</span>){<span class="php-keyword1">echo</span> <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\S`'</span>, <span class="php-quote">''</span>, <span class="php-var">$x</span>);}
</span><span id="304" class="l"><a href="#304">304: </a>}
</span><span id="305" class="l"><a href="#305">305: </a><span class="php-keyword1">while</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$q</span>) &amp;&amp; (<span class="php-var">$e</span> = <span class="php-keyword2">array_pop</span>(<span class="php-var">$q</span>))){<span class="php-keyword1">echo</span> <span class="php-quote">'&lt;/'</span>, <span class="php-var">$e</span>, <span class="php-quote">'&gt;'</span>;}
</span><span id="306" class="l"><a href="#306">306: </a><span class="php-var">$o</span> = <span class="php-keyword2">ob_get_contents</span>();
</span><span id="307" class="l"><a href="#307">307: </a><span class="php-keyword2">ob_end_clean</span>();
</span><span id="308" class="l"><a href="#308">308: </a><span class="php-keyword1">return</span> <span class="php-var">$o</span>;
</span><span id="309" class="l"><a href="#309">309: </a><span class="php-comment">// eof</span>
</span><span id="310" class="l"><a href="#310">310: </a>}
</span><span id="311" class="l"><a href="#311">311: </a>
</span><span id="312" class="l"><a href="#312">312: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_cmtcd(<span class="php-var">$t</span>){
</span><span id="313" class="l"><a href="#313">313: </a><span class="php-comment">// comment/CDATA sec handler</span>
</span><span id="314" class="l"><a href="#314">314: </a><span class="php-var">$t</span> = <span class="php-var">$t</span>[<span class="php-num">0</span>];
</span><span id="315" class="l"><a href="#315">315: </a><span class="php-keyword1">global</span> <span class="php-var">$C</span>;
</span><span id="316" class="l"><a href="#316">316: </a><span class="php-keyword1">if</span>(!(<span class="php-var">$v</span> = <span class="php-var">$C</span>[<span class="php-var">$n</span> = <span class="php-var">$t</span>[<span class="php-num">3</span>] == <span class="php-quote">'-'</span> ? <span class="php-quote">'comment'</span> : <span class="php-quote">'cdata'</span>])){<span class="php-keyword1">return</span> <span class="php-var">$t</span>;}
</span><span id="317" class="l"><a href="#317">317: </a><span class="php-keyword1">if</span>(<span class="php-var">$v</span> == <span class="php-num">1</span>){<span class="php-keyword1">return</span> <span class="php-quote">''</span>;}
</span><span id="318" class="l"><a href="#318">318: </a><span class="php-keyword1">if</span>(<span class="php-var">$n</span> == <span class="php-quote">'comment'</span>){
</span><span id="319" class="l"><a href="#319">319: </a> <span class="php-keyword1">if</span>(<span class="php-keyword2">substr</span>((<span class="php-var">$t</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`--+`'</span>, <span class="php-quote">'-'</span>, <span class="php-keyword2">substr</span>(<span class="php-var">$t</span>, <span class="php-num">4</span>, -<span class="php-num">3</span>))), -<span class="php-num">1</span>) != <span class="php-quote">' '</span>){<span class="php-var">$t</span> .= <span class="php-quote">' '</span>;}
</span><span id="320" class="l"><a href="#320">320: </a>}
</span><span id="321" class="l"><a href="#321">321: </a><span class="php-keyword1">else</span>{<span class="php-var">$t</span> = <span class="php-keyword2">substr</span>(<span class="php-var">$t</span>, <span class="php-num">1</span>, -<span class="php-num">1</span>);}
</span><span id="322" class="l"><a href="#322">322: </a><span class="php-var">$t</span> = <span class="php-var">$v</span> == <span class="php-num">2</span> ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&amp;'</span>, <span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;amp;'</span>, <span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>) : <span class="php-var">$t</span>;
</span><span id="323" class="l"><a href="#323">323: </a><span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&amp;'</span>, <span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x03&quot;</span>, <span class="php-quote">&quot;\x04&quot;</span>, <span class="php-quote">&quot;\x05&quot;</span>), (<span class="php-var">$n</span> == <span class="php-quote">'comment'</span> ? <span class="php-quote">&quot;\x01\x02\x04!--</span><span class="php-var">$t</span><span class="php-quote">--\x05\x02\x01&quot;</span> : <span class="php-quote">&quot;\x01\x01\x04</span><span class="php-var">$t</span><span class="php-quote">\x05\x01\x01&quot;</span>));
</span><span id="324" class="l"><a href="#324">324: </a><span class="php-comment">// eof</span>
</span><span id="325" class="l"><a href="#325">325: </a>}
</span><span id="326" class="l"><a href="#326">326: </a>
</span><span id="327" class="l"><a href="#327">327: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_ent(<span class="php-var">$t</span>){
</span><span id="328" class="l"><a href="#328">328: </a><span class="php-comment">// entitity handler</span>
</span><span id="329" class="l"><a href="#329">329: </a><span class="php-keyword1">global</span> <span class="php-var">$C</span>;
</span><span id="330" class="l"><a href="#330">330: </a><span class="php-var">$t</span> = <span class="php-var">$t</span>[<span class="php-num">1</span>];
</span><span id="331" class="l"><a href="#331">331: </a><span class="php-keyword1">static</span> <span class="php-var">$U</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'quot'</span>=&gt;<span class="php-num">1</span>,<span class="php-quote">'amp'</span>=&gt;<span class="php-num">1</span>,<span class="php-quote">'lt'</span>=&gt;<span class="php-num">1</span>,<span class="php-quote">'gt'</span>=&gt;<span class="php-num">1</span>);
</span><span id="332" class="l"><a href="#332">332: </a><span class="php-keyword1">static</span> <span class="php-var">$N</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'fnof'</span>=&gt;<span class="php-quote">'402'</span>, <span class="php-quote">'Alpha'</span>=&gt;<span class="php-quote">'913'</span>, <span class="php-quote">'Beta'</span>=&gt;<span class="php-quote">'914'</span>, <span class="php-quote">'Gamma'</span>=&gt;<span class="php-quote">'915'</span>, <span class="php-quote">'Delta'</span>=&gt;<span class="php-quote">'916'</span>, <span class="php-quote">'Epsilon'</span>=&gt;<span class="php-quote">'917'</span>, <span class="php-quote">'Zeta'</span>=&gt;<span class="php-quote">'918'</span>, <span class="php-quote">'Eta'</span>=&gt;<span class="php-quote">'919'</span>, <span class="php-quote">'Theta'</span>=&gt;<span class="php-quote">'920'</span>, <span class="php-quote">'Iota'</span>=&gt;<span class="php-quote">'921'</span>, <span class="php-quote">'Kappa'</span>=&gt;<span class="php-quote">'922'</span>, <span class="php-quote">'Lambda'</span>=&gt;<span class="php-quote">'923'</span>, <span class="php-quote">'Mu'</span>=&gt;<span class="php-quote">'924'</span>, <span class="php-quote">'Nu'</span>=&gt;<span class="php-quote">'925'</span>, <span class="php-quote">'Xi'</span>=&gt;<span class="php-quote">'926'</span>, <span class="php-quote">'Omicron'</span>=&gt;<span class="php-quote">'927'</span>, <span class="php-quote">'Pi'</span>=&gt;<span class="php-quote">'928'</span>, <span class="php-quote">'Rho'</span>=&gt;<span class="php-quote">'929'</span>, <span class="php-quote">'Sigma'</span>=&gt;<span class="php-quote">'931'</span>, <span class="php-quote">'Tau'</span>=&gt;<span class="php-quote">'932'</span>, <span class="php-quote">'Upsilon'</span>=&gt;<span class="php-quote">'933'</span>, <span class="php-quote">'Phi'</span>=&gt;<span class="php-quote">'934'</span>, <span class="php-quote">'Chi'</span>=&gt;<span class="php-quote">'935'</span>, <span class="php-quote">'Psi'</span>=&gt;<span class="php-quote">'936'</span>, <span class="php-quote">'Omega'</span>=&gt;<span class="php-quote">'937'</span>, <span class="php-quote">'alpha'</span>=&gt;<span class="php-quote">'945'</span>, <span class="php-quote">'beta'</span>=&gt;<span class="php-quote">'946'</span>, <span class="php-quote">'gamma'</span>=&gt;<span class="php-quote">'947'</span>, <span class="php-quote">'delta'</span>=&gt;<span class="php-quote">'948'</span>, <span class="php-quote">'epsilon'</span>=&gt;<span class="php-quote">'949'</span>, <span class="php-quote">'zeta'</span>=&gt;<span class="php-quote">'950'</span>, <span class="php-quote">'eta'</span>=&gt;<span class="php-quote">'951'</span>, <span class="php-quote">'theta'</span>=&gt;<span class="php-quote">'952'</span>, <span class="php-quote">'iota'</span>=&gt;<span class="php-quote">'953'</span>, <span class="php-quote">'kappa'</span>=&gt;<span class="php-quote">'954'</span>, <span class="php-quote">'lambda'</span>=&gt;<span class="php-quote">'955'</span>, <span class="php-quote">'mu'</span>=&gt;<span class="php-quote">'956'</span>, <span class="php-quote">'nu'</span>=&gt;<span class="php-quote">'957'</span>, <span class="php-quote">'xi'</span>=&gt;<span class="php-quote">'958'</span>, <span class="php-quote">'omicron'</span>=&gt;<span class="php-quote">'959'</span>, <span class="php-quote">'pi'</span>=&gt;<span class="php-quote">'960'</span>, <span class="php-quote">'rho'</span>=&gt;<span class="php-quote">'961'</span>, <span class="php-quote">'sigmaf'</span>=&gt;<span class="php-quote">'962'</span>, <span class="php-quote">'sigma'</span>=&gt;<span class="php-quote">'963'</span>, <span class="php-quote">'tau'</span>=&gt;<span class="php-quote">'964'</span>, <span class="php-quote">'upsilon'</span>=&gt;<span class="php-quote">'965'</span>, <span class="php-quote">'phi'</span>=&gt;<span class="php-quote">'966'</span>, <span class="php-quote">'chi'</span>=&gt;<span class="php-quote">'967'</span>, <span class="php-quote">'psi'</span>=&gt;<span class="php-quote">'968'</span>, <span class="php-quote">'omega'</span>=&gt;<span class="php-quote">'969'</span>, <span class="php-quote">'thetasym'</span>=&gt;<span class="php-quote">'977'</span>, <span class="php-quote">'upsih'</span>=&gt;<span class="php-quote">'978'</span>, <span class="php-quote">'piv'</span>=&gt;<span class="php-quote">'982'</span>, <span class="php-quote">'bull'</span>=&gt;<span class="php-quote">'8226'</span>, <span class="php-quote">'hellip'</span>=&gt;<span class="php-quote">'8230'</span>, <span class="php-quote">'prime'</span>=&gt;<span class="php-quote">'8242'</span>, <span class="php-quote">'Prime'</span>=&gt;<span class="php-quote">'8243'</span>, <span class="php-quote">'oline'</span>=&gt;<span class="php-quote">'8254'</span>, <span class="php-quote">'frasl'</span>=&gt;<span class="php-quote">'8260'</span>, <span class="php-quote">'weierp'</span>=&gt;<span class="php-quote">'8472'</span>, <span class="php-quote">'image'</span>=&gt;<span class="php-quote">'8465'</span>, <span class="php-quote">'real'</span>=&gt;<span class="php-quote">'8476'</span>, <span class="php-quote">'trade'</span>=&gt;<span class="php-quote">'8482'</span>, <span class="php-quote">'alefsym'</span>=&gt;<span class="php-quote">'8501'</span>, <span class="php-quote">'larr'</span>=&gt;<span class="php-quote">'8592'</span>, <span class="php-quote">'uarr'</span>=&gt;<span class="php-quote">'8593'</span>, <span class="php-quote">'rarr'</span>=&gt;<span class="php-quote">'8594'</span>, <span class="php-quote">'darr'</span>=&gt;<span class="php-quote">'8595'</span>, <span class="php-quote">'harr'</span>=&gt;<span class="php-quote">'8596'</span>, <span class="php-quote">'crarr'</span>=&gt;<span class="php-quote">'8629'</span>, <span class="php-quote">'lArr'</span>=&gt;<span class="php-quote">'8656'</span>, <span class="php-quote">'uArr'</span>=&gt;<span class="php-quote">'8657'</span>, <span class="php-quote">'rArr'</span>=&gt;<span class="php-quote">'8658'</span>, <span class="php-quote">'dArr'</span>=&gt;<span class="php-quote">'8659'</span>, <span class="php-quote">'hArr'</span>=&gt;<span class="php-quote">'8660'</span>, <span class="php-quote">'forall'</span>=&gt;<span class="php-quote">'8704'</span>, <span class="php-quote">'part'</span>=&gt;<span class="php-quote">'8706'</span>, <span class="php-quote">'exist'</span>=&gt;<span class="php-quote">'8707'</span>, <span class="php-quote">'empty'</span>=&gt;<span class="php-quote">'8709'</span>, <span class="php-quote">'nabla'</span>=&gt;<span class="php-quote">'8711'</span>, <span class="php-quote">'isin'</span>=&gt;<span class="php-quote">'8712'</span>, <span class="php-quote">'notin'</span>=&gt;<span class="php-quote">'8713'</span>, <span class="php-quote">'ni'</span>=&gt;<span class="php-quote">'8715'</span>, <span class="php-quote">'prod'</span>=&gt;<span class="php-quote">'8719'</span>, <span class="php-quote">'sum'</span>=&gt;<span class="php-quote">'8721'</span>, <span class="php-quote">'minus'</span>=&gt;<span class="php-quote">'8722'</span>, <span class="php-quote">'lowast'</span>=&gt;<span class="php-quote">'8727'</span>, <span class="php-quote">'radic'</span>=&gt;<span class="php-quote">'8730'</span>, <span class="php-quote">'prop'</span>=&gt;<span class="php-quote">'8733'</span>, <span class="php-quote">'infin'</span>=&gt;<span class="php-quote">'8734'</span>, <span class="php-quote">'ang'</span>=&gt;<span class="php-quote">'8736'</span>, <span class="php-quote">'and'</span>=&gt;<span class="php-quote">'8743'</span>, <span class="php-quote">'or'</span>=&gt;<span class="php-quote">'8744'</span>, <span class="php-quote">'cap'</span>=&gt;<span class="php-quote">'8745'</span>, <span class="php-quote">'cup'</span>=&gt;<span class="php-quote">'8746'</span>, <span class="php-quote">'int'</span>=&gt;<span class="php-quote">'8747'</span>, <span class="php-quote">'there4'</span>=&gt;<span class="php-quote">'8756'</span>, <span class="php-quote">'sim'</span>=&gt;<span class="php-quote">'8764'</span>, <span class="php-quote">'cong'</span>=&gt;<span class="php-quote">'8773'</span>, <span class="php-quote">'asymp'</span>=&gt;<span class="php-quote">'8776'</span>, <span class="php-quote">'ne'</span>=&gt;<span class="php-quote">'8800'</span>, <span class="php-quote">'equiv'</span>=&gt;<span class="php-quote">'8801'</span>, <span class="php-quote">'le'</span>=&gt;<span class="php-quote">'8804'</span>, <span class="php-quote">'ge'</span>=&gt;<span class="php-quote">'8805'</span>, <span class="php-quote">'sub'</span>=&gt;<span class="php-quote">'8834'</span>, <span class="php-quote">'sup'</span>=&gt;<span class="php-quote">'8835'</span>, <span class="php-quote">'nsub'</span>=&gt;<span class="php-quote">'8836'</span>, <span class="php-quote">'sube'</span>=&gt;<span class="php-quote">'8838'</span>, <span class="php-quote">'supe'</span>=&gt;<span class="php-quote">'8839'</span>, <span class="php-quote">'oplus'</span>=&gt;<span class="php-quote">'8853'</span>, <span class="php-quote">'otimes'</span>=&gt;<span class="php-quote">'8855'</span>, <span class="php-quote">'perp'</span>=&gt;<span class="php-quote">'8869'</span>, <span class="php-quote">'sdot'</span>=&gt;<span class="php-quote">'8901'</span>, <span class="php-quote">'lceil'</span>=&gt;<span class="php-quote">'8968'</span>, <span class="php-quote">'rceil'</span>=&gt;<span class="php-quote">'8969'</span>, <span class="php-quote">'lfloor'</span>=&gt;<span class="php-quote">'8970'</span>, <span class="php-quote">'rfloor'</span>=&gt;<span class="php-quote">'8971'</span>, <span class="php-quote">'lang'</span>=&gt;<span class="php-quote">'9001'</span>, <span class="php-quote">'rang'</span>=&gt;<span class="php-quote">'9002'</span>, <span class="php-quote">'loz'</span>=&gt;<span class="php-quote">'9674'</span>, <span class="php-quote">'spades'</span>=&gt;<span class="php-quote">'9824'</span>, <span class="php-quote">'clubs'</span>=&gt;<span class="php-quote">'9827'</span>, <span class="php-quote">'hearts'</span>=&gt;<span class="php-quote">'9829'</span>, <span class="php-quote">'diams'</span>=&gt;<span class="php-quote">'9830'</span>, <span class="php-quote">'apos'</span>=&gt;<span class="php-quote">'39'</span>,  <span class="php-quote">'OElig'</span>=&gt;<span class="php-quote">'338'</span>, <span class="php-quote">'oelig'</span>=&gt;<span class="php-quote">'339'</span>, <span class="php-quote">'Scaron'</span>=&gt;<span class="php-quote">'352'</span>, <span class="php-quote">'scaron'</span>=&gt;<span class="php-quote">'353'</span>, <span class="php-quote">'Yuml'</span>=&gt;<span class="php-quote">'376'</span>, <span class="php-quote">'circ'</span>=&gt;<span class="php-quote">'710'</span>, <span class="php-quote">'tilde'</span>=&gt;<span class="php-quote">'732'</span>, <span class="php-quote">'ensp'</span>=&gt;<span class="php-quote">'8194'</span>, <span class="php-quote">'emsp'</span>=&gt;<span class="php-quote">'8195'</span>, <span class="php-quote">'thinsp'</span>=&gt;<span class="php-quote">'8201'</span>, <span class="php-quote">'zwnj'</span>=&gt;<span class="php-quote">'8204'</span>, <span class="php-quote">'zwj'</span>=&gt;<span class="php-quote">'8205'</span>, <span class="php-quote">'lrm'</span>=&gt;<span class="php-quote">'8206'</span>, <span class="php-quote">'rlm'</span>=&gt;<span class="php-quote">'8207'</span>, <span class="php-quote">'ndash'</span>=&gt;<span class="php-quote">'8211'</span>, <span class="php-quote">'mdash'</span>=&gt;<span class="php-quote">'8212'</span>, <span class="php-quote">'lsquo'</span>=&gt;<span class="php-quote">'8216'</span>, <span class="php-quote">'rsquo'</span>=&gt;<span class="php-quote">'8217'</span>, <span class="php-quote">'sbquo'</span>=&gt;<span class="php-quote">'8218'</span>, <span class="php-quote">'ldquo'</span>=&gt;<span class="php-quote">'8220'</span>, <span class="php-quote">'rdquo'</span>=&gt;<span class="php-quote">'8221'</span>, <span class="php-quote">'bdquo'</span>=&gt;<span class="php-quote">'8222'</span>, <span class="php-quote">'dagger'</span>=&gt;<span class="php-quote">'8224'</span>, <span class="php-quote">'Dagger'</span>=&gt;<span class="php-quote">'8225'</span>, <span class="php-quote">'permil'</span>=&gt;<span class="php-quote">'8240'</span>, <span class="php-quote">'lsaquo'</span>=&gt;<span class="php-quote">'8249'</span>, <span class="php-quote">'rsaquo'</span>=&gt;<span class="php-quote">'8250'</span>, <span class="php-quote">'euro'</span>=&gt;<span class="php-quote">'8364'</span>, <span class="php-quote">'nbsp'</span>=&gt;<span class="php-quote">'160'</span>, <span class="php-quote">'iexcl'</span>=&gt;<span class="php-quote">'161'</span>, <span class="php-quote">'cent'</span>=&gt;<span class="php-quote">'162'</span>, <span class="php-quote">'pound'</span>=&gt;<span class="php-quote">'163'</span>, <span class="php-quote">'curren'</span>=&gt;<span class="php-quote">'164'</span>, <span class="php-quote">'yen'</span>=&gt;<span class="php-quote">'165'</span>, <span class="php-quote">'brvbar'</span>=&gt;<span class="php-quote">'166'</span>, <span class="php-quote">'sect'</span>=&gt;<span class="php-quote">'167'</span>, <span class="php-quote">'uml'</span>=&gt;<span class="php-quote">'168'</span>, <span class="php-quote">'copy'</span>=&gt;<span class="php-quote">'169'</span>, <span class="php-quote">'ordf'</span>=&gt;<span class="php-quote">'170'</span>, <span class="php-quote">'laquo'</span>=&gt;<span class="php-quote">'171'</span>, <span class="php-quote">'not'</span>=&gt;<span class="php-quote">'172'</span>, <span class="php-quote">'shy'</span>=&gt;<span class="php-quote">'173'</span>, <span class="php-quote">'reg'</span>=&gt;<span class="php-quote">'174'</span>, <span class="php-quote">'macr'</span>=&gt;<span class="php-quote">'175'</span>, <span class="php-quote">'deg'</span>=&gt;<span class="php-quote">'176'</span>, <span class="php-quote">'plusmn'</span>=&gt;<span class="php-quote">'177'</span>, <span class="php-quote">'sup2'</span>=&gt;<span class="php-quote">'178'</span>, <span class="php-quote">'sup3'</span>=&gt;<span class="php-quote">'179'</span>, <span class="php-quote">'acute'</span>=&gt;<span class="php-quote">'180'</span>, <span class="php-quote">'micro'</span>=&gt;<span class="php-quote">'181'</span>, <span class="php-quote">'para'</span>=&gt;<span class="php-quote">'182'</span>, <span class="php-quote">'middot'</span>=&gt;<span class="php-quote">'183'</span>, <span class="php-quote">'cedil'</span>=&gt;<span class="php-quote">'184'</span>, <span class="php-quote">'sup1'</span>=&gt;<span class="php-quote">'185'</span>, <span class="php-quote">'ordm'</span>=&gt;<span class="php-quote">'186'</span>, <span class="php-quote">'raquo'</span>=&gt;<span class="php-quote">'187'</span>, <span class="php-quote">'frac14'</span>=&gt;<span class="php-quote">'188'</span>, <span class="php-quote">'frac12'</span>=&gt;<span class="php-quote">'189'</span>, <span class="php-quote">'frac34'</span>=&gt;<span class="php-quote">'190'</span>, <span class="php-quote">'iquest'</span>=&gt;<span class="php-quote">'191'</span>, <span class="php-quote">'Agrave'</span>=&gt;<span class="php-quote">'192'</span>, <span class="php-quote">'Aacute'</span>=&gt;<span class="php-quote">'193'</span>, <span class="php-quote">'Acirc'</span>=&gt;<span class="php-quote">'194'</span>, <span class="php-quote">'Atilde'</span>=&gt;<span class="php-quote">'195'</span>, <span class="php-quote">'Auml'</span>=&gt;<span class="php-quote">'196'</span>, <span class="php-quote">'Aring'</span>=&gt;<span class="php-quote">'197'</span>, <span class="php-quote">'AElig'</span>=&gt;<span class="php-quote">'198'</span>, <span class="php-quote">'Ccedil'</span>=&gt;<span class="php-quote">'199'</span>, <span class="php-quote">'Egrave'</span>=&gt;<span class="php-quote">'200'</span>, <span class="php-quote">'Eacute'</span>=&gt;<span class="php-quote">'201'</span>, <span class="php-quote">'Ecirc'</span>=&gt;<span class="php-quote">'202'</span>, <span class="php-quote">'Euml'</span>=&gt;<span class="php-quote">'203'</span>, <span class="php-quote">'Igrave'</span>=&gt;<span class="php-quote">'204'</span>, <span class="php-quote">'Iacute'</span>=&gt;<span class="php-quote">'205'</span>, <span class="php-quote">'Icirc'</span>=&gt;<span class="php-quote">'206'</span>, <span class="php-quote">'Iuml'</span>=&gt;<span class="php-quote">'207'</span>, <span class="php-quote">'ETH'</span>=&gt;<span class="php-quote">'208'</span>, <span class="php-quote">'Ntilde'</span>=&gt;<span class="php-quote">'209'</span>, <span class="php-quote">'Ograve'</span>=&gt;<span class="php-quote">'210'</span>, <span class="php-quote">'Oacute'</span>=&gt;<span class="php-quote">'211'</span>, <span class="php-quote">'Ocirc'</span>=&gt;<span class="php-quote">'212'</span>, <span class="php-quote">'Otilde'</span>=&gt;<span class="php-quote">'213'</span>, <span class="php-quote">'Ouml'</span>=&gt;<span class="php-quote">'214'</span>, <span class="php-quote">'times'</span>=&gt;<span class="php-quote">'215'</span>, <span class="php-quote">'Oslash'</span>=&gt;<span class="php-quote">'216'</span>, <span class="php-quote">'Ugrave'</span>=&gt;<span class="php-quote">'217'</span>, <span class="php-quote">'Uacute'</span>=&gt;<span class="php-quote">'218'</span>, <span class="php-quote">'Ucirc'</span>=&gt;<span class="php-quote">'219'</span>, <span class="php-quote">'Uuml'</span>=&gt;<span class="php-quote">'220'</span>, <span class="php-quote">'Yacute'</span>=&gt;<span class="php-quote">'221'</span>, <span class="php-quote">'THORN'</span>=&gt;<span class="php-quote">'222'</span>, <span class="php-quote">'szlig'</span>=&gt;<span class="php-quote">'223'</span>, <span class="php-quote">'agrave'</span>=&gt;<span class="php-quote">'224'</span>, <span class="php-quote">'aacute'</span>=&gt;<span class="php-quote">'225'</span>, <span class="php-quote">'acirc'</span>=&gt;<span class="php-quote">'226'</span>, <span class="php-quote">'atilde'</span>=&gt;<span class="php-quote">'227'</span>, <span class="php-quote">'auml'</span>=&gt;<span class="php-quote">'228'</span>, <span class="php-quote">'aring'</span>=&gt;<span class="php-quote">'229'</span>, <span class="php-quote">'aelig'</span>=&gt;<span class="php-quote">'230'</span>, <span class="php-quote">'ccedil'</span>=&gt;<span class="php-quote">'231'</span>, <span class="php-quote">'egrave'</span>=&gt;<span class="php-quote">'232'</span>, <span class="php-quote">'eacute'</span>=&gt;<span class="php-quote">'233'</span>, <span class="php-quote">'ecirc'</span>=&gt;<span class="php-quote">'234'</span>, <span class="php-quote">'euml'</span>=&gt;<span class="php-quote">'235'</span>, <span class="php-quote">'igrave'</span>=&gt;<span class="php-quote">'236'</span>, <span class="php-quote">'iacute'</span>=&gt;<span class="php-quote">'237'</span>, <span class="php-quote">'icirc'</span>=&gt;<span class="php-quote">'238'</span>, <span class="php-quote">'iuml'</span>=&gt;<span class="php-quote">'239'</span>, <span class="php-quote">'eth'</span>=&gt;<span class="php-quote">'240'</span>, <span class="php-quote">'ntilde'</span>=&gt;<span class="php-quote">'241'</span>, <span class="php-quote">'ograve'</span>=&gt;<span class="php-quote">'242'</span>, <span class="php-quote">'oacute'</span>=&gt;<span class="php-quote">'243'</span>, <span class="php-quote">'ocirc'</span>=&gt;<span class="php-quote">'244'</span>, <span class="php-quote">'otilde'</span>=&gt;<span class="php-quote">'245'</span>, <span class="php-quote">'ouml'</span>=&gt;<span class="php-quote">'246'</span>, <span class="php-quote">'divide'</span>=&gt;<span class="php-quote">'247'</span>, <span class="php-quote">'oslash'</span>=&gt;<span class="php-quote">'248'</span>, <span class="php-quote">'ugrave'</span>=&gt;<span class="php-quote">'249'</span>, <span class="php-quote">'uacute'</span>=&gt;<span class="php-quote">'250'</span>, <span class="php-quote">'ucirc'</span>=&gt;<span class="php-quote">'251'</span>, <span class="php-quote">'uuml'</span>=&gt;<span class="php-quote">'252'</span>, <span class="php-quote">'yacute'</span>=&gt;<span class="php-quote">'253'</span>, <span class="php-quote">'thorn'</span>=&gt;<span class="php-quote">'254'</span>, <span class="php-quote">'yuml'</span>=&gt;<span class="php-quote">'255'</span>);
</span><span id="333" class="l"><a href="#333">333: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span>[<span class="php-num">0</span>] != <span class="php-quote">'#'</span>){
</span><span id="334" class="l"><a href="#334">334: </a> <span class="php-keyword1">return</span> (<span class="php-var">$C</span>[<span class="php-quote">'and_mark'</span>] ? <span class="php-quote">&quot;\x06&quot;</span> : <span class="php-quote">'&amp;'</span>). (<span class="php-keyword1">isset</span>(<span class="php-var">$U</span>[<span class="php-var">$t</span>]) ? <span class="php-var">$t</span> : (<span class="php-keyword1">isset</span>(<span class="php-var">$N</span>[<span class="php-var">$t</span>]) ? (!<span class="php-var">$C</span>[<span class="php-quote">'named_entity'</span>] ? <span class="php-quote">'#'</span>. (<span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>] &gt; <span class="php-num">1</span> ? <span class="php-quote">'x'</span>. <span class="php-keyword2">dechex</span>(<span class="php-var">$N</span>[<span class="php-var">$t</span>]) : <span class="php-var">$N</span>[<span class="php-var">$t</span>]) : <span class="php-var">$t</span>) : <span class="php-quote">'amp;'</span>. <span class="php-var">$t</span>)). <span class="php-quote">';'</span>;
</span><span id="335" class="l"><a href="#335">335: </a>}
</span><span id="336" class="l"><a href="#336">336: </a><span class="php-keyword1">if</span>((<span class="php-var">$n</span> = <span class="php-keyword2">ctype_digit</span>(<span class="php-var">$t</span> = <span class="php-keyword2">substr</span>(<span class="php-var">$t</span>, <span class="php-num">1</span>)) ? <span class="php-keyword2">intval</span>(<span class="php-var">$t</span>) : <span class="php-keyword2">hexdec</span>(<span class="php-keyword2">substr</span>(<span class="php-var">$t</span>, <span class="php-num">1</span>))) &lt; <span class="php-num">9</span> <span class="php-keyword1">or</span> (<span class="php-var">$n</span> &gt; <span class="php-num">13</span> &amp;&amp; <span class="php-var">$n</span> &lt; <span class="php-num">32</span>) <span class="php-keyword1">or</span> <span class="php-var">$n</span> == <span class="php-num">11</span> <span class="php-keyword1">or</span> <span class="php-var">$n</span> == <span class="php-num">12</span> <span class="php-keyword1">or</span> (<span class="php-var">$n</span> &gt; <span class="php-num">126</span> &amp;&amp; <span class="php-var">$n</span> &lt; <span class="php-num">160</span> &amp;&amp; <span class="php-var">$n</span> != <span class="php-num">133</span>) <span class="php-keyword1">or</span> (<span class="php-var">$n</span> &gt; <span class="php-num">55295</span> &amp;&amp; (<span class="php-var">$n</span> &lt; <span class="php-num">57344</span> <span class="php-keyword1">or</span> (<span class="php-var">$n</span> &gt; <span class="php-num">64975</span> &amp;&amp; <span class="php-var">$n</span> &lt; <span class="php-num">64992</span>) <span class="php-keyword1">or</span> <span class="php-var">$n</span> == <span class="php-num">65534</span> <span class="php-keyword1">or</span> <span class="php-var">$n</span> == <span class="php-num">65535</span> <span class="php-keyword1">or</span> <span class="php-var">$n</span> &gt; <span class="php-num">1114111</span>))){
</span><span id="337" class="l"><a href="#337">337: </a> <span class="php-keyword1">return</span> (<span class="php-var">$C</span>[<span class="php-quote">'and_mark'</span>] ? <span class="php-quote">&quot;\x06&quot;</span> : <span class="php-quote">'&amp;'</span>). <span class="php-quote">&quot;amp;#</span><span class="php-var">{$t}</span><span class="php-quote">;&quot;</span>;
</span><span id="338" class="l"><a href="#338">338: </a>}
</span><span id="339" class="l"><a href="#339">339: </a><span class="php-keyword1">return</span> (<span class="php-var">$C</span>[<span class="php-quote">'and_mark'</span>] ? <span class="php-quote">&quot;\x06&quot;</span> : <span class="php-quote">'&amp;'</span>). <span class="php-quote">'#'</span>. (((<span class="php-keyword2">ctype_digit</span>(<span class="php-var">$t</span>) &amp;&amp; <span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>] &lt; <span class="php-num">2</span>) <span class="php-keyword1">or</span> !<span class="php-var">$C</span>[<span class="php-quote">'hexdec_entity'</span>]) ? <span class="php-var">$n</span> : <span class="php-quote">'x'</span>. <span class="php-keyword2">dechex</span>(<span class="php-var">$n</span>)). <span class="php-quote">';'</span>;
</span><span id="340" class="l"><a href="#340">340: </a><span class="php-comment">// eof</span>
</span><span id="341" class="l"><a href="#341">341: </a>}
</span><span id="342" class="l"><a href="#342">342: </a>
</span><span id="343" class="l"><a href="#343">343: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_prot(<span class="php-var">$p</span>, <span class="php-var">$c</span>=<span class="php-keyword1">null</span>){
</span><span id="344" class="l"><a href="#344">344: </a><span class="php-comment">// check URL scheme</span>
</span><span id="345" class="l"><a href="#345">345: </a><span class="php-keyword1">global</span> <span class="php-var">$C</span>;
</span><span id="346" class="l"><a href="#346">346: </a><span class="php-var">$b</span> = <span class="php-var">$a</span> = <span class="php-quote">''</span>;
</span><span id="347" class="l"><a href="#347">347: </a><span class="php-keyword1">if</span>(<span class="php-var">$c</span> == <span class="php-keyword1">null</span>){<span class="php-var">$c</span> = <span class="php-quote">'style'</span>; <span class="php-var">$b</span> = <span class="php-var">$p</span>[<span class="php-num">1</span>]; <span class="php-var">$a</span> = <span class="php-var">$p</span>[<span class="php-num">3</span>]; <span class="php-var">$p</span> = <span class="php-keyword2">trim</span>(<span class="php-var">$p</span>[<span class="php-num">2</span>]);}
</span><span id="348" class="l"><a href="#348">348: </a><span class="php-var">$c</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-var">$c</span>]) ? <span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-var">$c</span>] : <span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>][<span class="php-quote">'*'</span>];
</span><span id="349" class="l"><a href="#349">349: </a><span class="php-keyword1">static</span> <span class="php-var">$d</span> = <span class="php-quote">'denied:'</span>;
</span><span id="350" class="l"><a href="#350">350: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$c</span>[<span class="php-quote">'!'</span>]) &amp;&amp; <span class="php-keyword2">substr</span>(<span class="php-var">$p</span>, <span class="php-num">0</span>, <span class="php-num">7</span>) != <span class="php-var">$d</span>){<span class="php-var">$p</span> = <span class="php-quote">&quot;</span><span class="php-var">$d$p</span><span class="php-quote">&quot;</span>;}
</span><span id="351" class="l"><a href="#351">351: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$c</span>[<span class="php-quote">'*'</span>]) <span class="php-keyword1">or</span> !<span class="php-keyword2">strcspn</span>(<span class="php-var">$p</span>, <span class="php-quote">'#?;'</span>) <span class="php-keyword1">or</span> (<span class="php-keyword2">substr</span>(<span class="php-var">$p</span>, <span class="php-num">0</span>, <span class="php-num">7</span>) == <span class="php-var">$d</span>)){<span class="php-keyword1">return</span> <span class="php-quote">&quot;</span><span class="php-var">{$b}{$p}{$a}</span><span class="php-quote">&quot;</span>;} <span class="php-comment">// All ok, frag, query, param</span>
</span><span id="352" class="l"><a href="#352">352: </a><span class="php-keyword1">if</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^([^:?[@!$()*,=/\'\]]+?)(:|&amp;#(58|x3a);|%3a|\\\\0{0,4}3a).`i'</span>, <span class="php-var">$p</span>, <span class="php-var">$m</span>) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$c</span>[<span class="php-keyword2">strtolower</span>(<span class="php-var">$m</span>[<span class="php-num">1</span>])])){ <span class="php-comment">// Denied prot</span>
</span><span id="353" class="l"><a href="#353">353: </a> <span class="php-keyword1">return</span> <span class="php-quote">&quot;</span><span class="php-var">{$b}{$d}{$p}{$a}</span><span class="php-quote">&quot;</span>;
</span><span id="354" class="l"><a href="#354">354: </a>}
</span><span id="355" class="l"><a href="#355">355: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>]){
</span><span id="356" class="l"><a href="#356">356: </a> <span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'abs_url'</span>] == -<span class="php-num">1</span> &amp;&amp; <span class="php-keyword2">strpos</span>(<span class="php-var">$p</span>, <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>]) === <span class="php-num">0</span>){ <span class="php-comment">// Make url rel</span>
</span><span id="357" class="l"><a href="#357">357: </a>  <span class="php-var">$p</span> = <span class="php-keyword2">substr</span>(<span class="php-var">$p</span>, <span class="php-keyword2">strlen</span>(<span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>]));
</span><span id="358" class="l"><a href="#358">358: </a> }<span class="php-keyword1">elseif</span>(<span class="php-keyword1">empty</span>(<span class="php-var">$m</span>[<span class="php-num">1</span>])){ <span class="php-comment">// Make URL abs</span>
</span><span id="359" class="l"><a href="#359">359: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword2">substr</span>(<span class="php-var">$p</span>, <span class="php-num">0</span>, <span class="php-num">2</span>) == <span class="php-quote">'//'</span>){<span class="php-var">$p</span> = <span class="php-keyword2">substr</span>(<span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>], <span class="php-num">0</span>, <span class="php-keyword2">strpos</span>(<span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>], <span class="php-quote">':'</span>)+<span class="php-num">1</span>). <span class="php-var">$p</span>;}
</span><span id="360" class="l"><a href="#360">360: </a>  <span class="php-keyword1">elseif</span>(<span class="php-var">$p</span>[<span class="php-num">0</span>] == <span class="php-quote">'/'</span>){<span class="php-var">$p</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`(^.+?://[^/]+)(.*)`'</span>, <span class="php-quote">'$1'</span>, <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>]). <span class="php-var">$p</span>;}
</span><span id="361" class="l"><a href="#361">361: </a>  <span class="php-keyword1">elseif</span>(<span class="php-keyword2">strcspn</span>(<span class="php-var">$p</span>, <span class="php-quote">'./'</span>)){<span class="php-var">$p</span> = <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>]. <span class="php-var">$p</span>;}
</span><span id="362" class="l"><a href="#362">362: </a>  <span class="php-keyword1">else</span>{
</span><span id="363" class="l"><a href="#363">363: </a>   <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^([a-zA-Z\d\-+.]+://[^/]+)(.*)`'</span>, <span class="php-var">$C</span>[<span class="php-quote">'base_url'</span>], <span class="php-var">$m</span>);
</span><span id="364" class="l"><a href="#364">364: </a>   <span class="php-var">$p</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`(?&lt;=/)\./`'</span>, <span class="php-quote">''</span>, <span class="php-var">$m</span>[<span class="php-num">2</span>]. <span class="php-var">$p</span>);
</span><span id="365" class="l"><a href="#365">365: </a>   <span class="php-keyword1">while</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`(?&lt;=/)([^/]{3,}|[^/.]+?|\.[^/.]|[^/.]\.)/\.\./`'</span>, <span class="php-var">$p</span>)){
</span><span id="366" class="l"><a href="#366">366: </a>    <span class="php-var">$p</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`(?&lt;=/)([^/]{3,}|[^/.]+?|\.[^/.]|[^/.]\.)/\.\./`'</span>, <span class="php-quote">''</span>, <span class="php-var">$p</span>);
</span><span id="367" class="l"><a href="#367">367: </a>   }
</span><span id="368" class="l"><a href="#368">368: </a>   <span class="php-var">$p</span> = <span class="php-var">$m</span>[<span class="php-num">1</span>]. <span class="php-var">$p</span>;
</span><span id="369" class="l"><a href="#369">369: </a>  }
</span><span id="370" class="l"><a href="#370">370: </a> }
</span><span id="371" class="l"><a href="#371">371: </a>}
</span><span id="372" class="l"><a href="#372">372: </a><span class="php-keyword1">return</span> <span class="php-quote">&quot;</span><span class="php-var">{$b}{$p}{$a}</span><span class="php-quote">&quot;</span>;
</span><span id="373" class="l"><a href="#373">373: </a><span class="php-comment">// eof</span>
</span><span id="374" class="l"><a href="#374">374: </a>}
</span><span id="375" class="l"><a href="#375">375: </a>
</span><span id="376" class="l"><a href="#376">376: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_regex(<span class="php-var">$p</span>){
</span><span id="377" class="l"><a href="#377">377: </a><span class="php-comment">// ?regex</span>
</span><span id="378" class="l"><a href="#378">378: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">empty</span>(<span class="php-var">$p</span>)){<span class="php-keyword1">return</span> <span class="php-num">0</span>;}
</span><span id="379" class="l"><a href="#379">379: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span> = <span class="php-keyword2">ini_get</span>(<span class="php-quote">'track_errors'</span>)){<span class="php-var">$o</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$php_errormsg</span>) ? <span class="php-var">$php_errormsg</span> : <span class="php-keyword1">null</span>;}
</span><span id="380" class="l"><a href="#380">380: </a><span class="php-keyword1">else</span>{<span class="php-keyword2">ini_set</span>(<span class="php-quote">'track_errors'</span>, <span class="php-num">1</span>);}
</span><span id="381" class="l"><a href="#381">381: </a><span class="php-keyword1">unset</span>(<span class="php-var">$php_errormsg</span>);
</span><span id="382" class="l"><a href="#382">382: </a><span class="php-keyword1">if</span>((<span class="php-var">$d</span> = <span class="php-keyword2">ini_get</span>(<span class="php-quote">'display_errors'</span>))){<span class="php-keyword2">ini_set</span>(<span class="php-quote">'display_errors'</span>, <span class="php-num">0</span>);}
</span><span id="383" class="l"><a href="#383">383: </a><span class="php-keyword2">preg_match</span>(<span class="php-var">$p</span>, <span class="php-quote">''</span>);
</span><span id="384" class="l"><a href="#384">384: </a><span class="php-keyword1">if</span>(<span class="php-var">$d</span>){<span class="php-keyword2">ini_set</span>(<span class="php-quote">'display_errors'</span>, <span class="php-num">1</span>);}
</span><span id="385" class="l"><a href="#385">385: </a><span class="php-var">$r</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$php_errormsg</span>) ? <span class="php-num">0</span> : <span class="php-num">1</span>;
</span><span id="386" class="l"><a href="#386">386: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span>){<span class="php-var">$php_errormsg</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$o</span>) ? <span class="php-var">$o</span> : <span class="php-keyword1">null</span>;}
</span><span id="387" class="l"><a href="#387">387: </a><span class="php-keyword1">else</span>{<span class="php-keyword2">ini_set</span>(<span class="php-quote">'track_errors'</span>, <span class="php-num">0</span>);}
</span><span id="388" class="l"><a href="#388">388: </a><span class="php-keyword1">return</span> <span class="php-var">$r</span>;
</span><span id="389" class="l"><a href="#389">389: </a><span class="php-comment">// eof</span>
</span><span id="390" class="l"><a href="#390">390: </a>}
</span><span id="391" class="l"><a href="#391">391: </a>
</span><span id="392" class="l"><a href="#392">392: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_spec(<span class="php-var">$t</span>){
</span><span id="393" class="l"><a href="#393">393: </a><span class="php-comment">// final $spec</span>
</span><span id="394" class="l"><a href="#394">394: </a><span class="php-var">$s</span> = <span class="php-keyword1">array</span>();
</span><span id="395" class="l"><a href="#395">395: </a><span class="php-var">$t</span> = <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">' '</span>), <span class="php-quote">''</span>, <span class="php-keyword2">preg_replace_callback</span>(<span class="php-quote">'/&quot;(?&gt;(`.|[^&quot;])*)&quot;/sm'</span>, <span class="php-keyword2">create_function</span>(<span class="php-quote">'$m'</span>, <span class="php-quote">'return substr(str_replace(array(&quot;;&quot;, &quot;|&quot;, &quot;~&quot;, &quot; &quot;, &quot;,&quot;, &quot;/&quot;, &quot;(&quot;, &quot;)&quot;, \'`&quot;\'), array(&quot;\x01&quot;, &quot;\x02&quot;, &quot;\x03&quot;, &quot;\x04&quot;, &quot;\x05&quot;, &quot;\x06&quot;, &quot;\x07&quot;, &quot;\x08&quot;, &quot;\&quot;&quot;), $m[0]), 1, -1);'</span>), <span class="php-keyword2">trim</span>(<span class="php-var">$t</span>))); 
</span><span id="396" class="l"><a href="#396">396: </a><span class="php-keyword1">for</span>(<span class="php-var">$i</span> = <span class="php-keyword2">count</span>((<span class="php-var">$t</span> = <span class="php-keyword2">explode</span>(<span class="php-quote">';'</span>, <span class="php-var">$t</span>))); --<span class="php-var">$i</span>&gt;=<span class="php-num">0</span>;){
</span><span id="397" class="l"><a href="#397">397: </a> <span class="php-var">$w</span> = <span class="php-var">$t</span>[<span class="php-var">$i</span>];
</span><span id="398" class="l"><a href="#398">398: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">empty</span>(<span class="php-var">$w</span>) <span class="php-keyword1">or</span> (<span class="php-var">$e</span> = <span class="php-keyword2">strpos</span>(<span class="php-var">$w</span>, <span class="php-quote">'='</span>)) === <span class="php-keyword1">false</span> <span class="php-keyword1">or</span> !<span class="php-keyword2">strlen</span>((<span class="php-var">$a</span> =  <span class="php-keyword2">substr</span>(<span class="php-var">$w</span>, <span class="php-var">$e</span>+<span class="php-num">1</span>)))){<span class="php-keyword1">continue</span>;}
</span><span id="399" class="l"><a href="#399">399: </a> <span class="php-var">$y</span> = <span class="php-var">$n</span> = <span class="php-keyword1">array</span>();
</span><span id="400" class="l"><a href="#400">400: </a> <span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">','</span>, <span class="php-var">$a</span>) <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="401" class="l"><a href="#401">401: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^([a-z:\-\*]+)(?:\((.*?)\))?`i'</span>, <span class="php-var">$v</span>, <span class="php-var">$m</span>)){<span class="php-keyword1">continue</span>;}
</span><span id="402" class="l"><a href="#402">402: </a>  <span class="php-keyword1">if</span>((<span class="php-var">$x</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$m</span>[<span class="php-num">1</span>])) == <span class="php-quote">'-*'</span>){<span class="php-var">$n</span>[<span class="php-quote">'*'</span>] = <span class="php-num">1</span>; <span class="php-keyword1">continue</span>;}
</span><span id="403" class="l"><a href="#403">403: </a>  <span class="php-keyword1">if</span>(<span class="php-var">$x</span>[<span class="php-num">0</span>] == <span class="php-quote">'-'</span>){<span class="php-var">$n</span>[<span class="php-keyword2">substr</span>(<span class="php-var">$x</span>, <span class="php-num">1</span>)] = <span class="php-num">1</span>; <span class="php-keyword1">continue</span>;}
</span><span id="404" class="l"><a href="#404">404: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$m</span>[<span class="php-num">2</span>])){<span class="php-var">$y</span>[<span class="php-var">$x</span>] = <span class="php-num">1</span>; <span class="php-keyword1">continue</span>;}
</span><span id="405" class="l"><a href="#405">405: </a>  <span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">'/'</span>, <span class="php-var">$m</span>[<span class="php-num">2</span>]) <span class="php-keyword1">as</span> <span class="php-var">$m</span>){
</span><span id="406" class="l"><a href="#406">406: </a>   <span class="php-keyword1">if</span>(<span class="php-keyword1">empty</span>(<span class="php-var">$m</span>) <span class="php-keyword1">or</span> (<span class="php-var">$p</span> = <span class="php-keyword2">strpos</span>(<span class="php-var">$m</span>, <span class="php-quote">'='</span>)) == <span class="php-num">0</span> <span class="php-keyword1">or</span> <span class="php-var">$p</span> &lt; <span class="php-num">5</span>){<span class="php-var">$y</span>[<span class="php-var">$x</span>] = <span class="php-num">1</span>; <span class="php-keyword1">continue</span>;}
</span><span id="407" class="l"><a href="#407">407: </a>   <span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-keyword2">strtolower</span>(<span class="php-keyword2">substr</span>(<span class="php-var">$m</span>, <span class="php-num">0</span>, <span class="php-var">$p</span>))] = <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x01&quot;</span>, <span class="php-quote">&quot;\x02&quot;</span>, <span class="php-quote">&quot;\x03&quot;</span>, <span class="php-quote">&quot;\x04&quot;</span>, <span class="php-quote">&quot;\x05&quot;</span>, <span class="php-quote">&quot;\x06&quot;</span>, <span class="php-quote">&quot;\x07&quot;</span>, <span class="php-quote">&quot;\x08&quot;</span>), <span class="php-keyword1">array</span>(<span class="php-quote">&quot;;&quot;</span>, <span class="php-quote">&quot;|&quot;</span>, <span class="php-quote">&quot;~&quot;</span>, <span class="php-quote">&quot; &quot;</span>, <span class="php-quote">&quot;,&quot;</span>, <span class="php-quote">&quot;/&quot;</span>, <span class="php-quote">&quot;(&quot;</span>, <span class="php-quote">&quot;)&quot;</span>), <span class="php-keyword2">substr</span>(<span class="php-var">$m</span>, <span class="php-var">$p</span>+<span class="php-num">1</span>));
</span><span id="408" class="l"><a href="#408">408: </a>  }
</span><span id="409" class="l"><a href="#409">409: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'match'</span>]) &amp;&amp; !\DataTables\Vendor\htmLawed::hl_regex(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'match'</span>])){<span class="php-keyword1">unset</span>(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'match'</span>]);}
</span><span id="410" class="l"><a href="#410">410: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'nomatch'</span>]) &amp;&amp; !\DataTables\Vendor\htmLawed::hl_regex(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'nomatch'</span>])){<span class="php-keyword1">unset</span>(<span class="php-var">$y</span>[<span class="php-var">$x</span>][<span class="php-quote">'nomatch'</span>]);}
</span><span id="411" class="l"><a href="#411">411: </a> }
</span><span id="412" class="l"><a href="#412">412: </a> <span class="php-keyword1">if</span>(!<span class="php-keyword2">count</span>(<span class="php-var">$y</span>) &amp;&amp; !<span class="php-keyword2">count</span>(<span class="php-var">$n</span>)){<span class="php-keyword1">continue</span>;}
</span><span id="413" class="l"><a href="#413">413: </a> <span class="php-keyword1">foreach</span>(<span class="php-keyword2">explode</span>(<span class="php-quote">','</span>, <span class="php-keyword2">substr</span>(<span class="php-var">$w</span>, <span class="php-num">0</span>, <span class="php-var">$e</span>)) <span class="php-keyword1">as</span> <span class="php-var">$v</span>){
</span><span id="414" class="l"><a href="#414">414: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword2">strlen</span>((<span class="php-var">$v</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$v</span>)))){<span class="php-keyword1">continue</span>;}
</span><span id="415" class="l"><a href="#415">415: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword2">count</span>(<span class="php-var">$y</span>)){<span class="php-var">$s</span>[<span class="php-var">$v</span>] = <span class="php-var">$y</span>;}
</span><span id="416" class="l"><a href="#416">416: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword2">count</span>(<span class="php-var">$n</span>)){<span class="php-var">$s</span>[<span class="php-var">$v</span>][<span class="php-quote">'n'</span>] = <span class="php-var">$n</span>;}
</span><span id="417" class="l"><a href="#417">417: </a> }
</span><span id="418" class="l"><a href="#418">418: </a>}
</span><span id="419" class="l"><a href="#419">419: </a><span class="php-keyword1">return</span> <span class="php-var">$s</span>;
</span><span id="420" class="l"><a href="#420">420: </a><span class="php-comment">// eof</span>
</span><span id="421" class="l"><a href="#421">421: </a>}
</span><span id="422" class="l"><a href="#422">422: </a>
</span><span id="423" class="l"><a href="#423">423: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_tag(<span class="php-var">$t</span>){
</span><span id="424" class="l"><a href="#424">424: </a><span class="php-comment">// tag/attribute handler</span>
</span><span id="425" class="l"><a href="#425">425: </a><span class="php-keyword1">global</span> <span class="php-var">$C</span>;
</span><span id="426" class="l"><a href="#426">426: </a><span class="php-var">$t</span> = <span class="php-var">$t</span>[<span class="php-num">0</span>];
</span><span id="427" class="l"><a href="#427">427: </a><span class="php-comment">// invalid &lt; &gt;</span>
</span><span id="428" class="l"><a href="#428">428: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span> == <span class="php-quote">'&lt; '</span>){<span class="php-keyword1">return</span> <span class="php-quote">'&amp;lt; '</span>;}
</span><span id="429" class="l"><a href="#429">429: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span> == <span class="php-quote">'&gt;'</span>){<span class="php-keyword1">return</span> <span class="php-quote">'&amp;gt;'</span>;}
</span><span id="430" class="l"><a href="#430">430: </a><span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^&lt;(/?)([a-zA-Z][a-zA-Z1-6]*)([^&gt;]*?)\s?&gt;$`m'</span>, <span class="php-var">$t</span>, <span class="php-var">$m</span>)){
</span><span id="431" class="l"><a href="#431">431: </a> <span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>);
</span><span id="432" class="l"><a href="#432">432: </a>}<span class="php-keyword1">elseif</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'elements'</span>][(<span class="php-var">$e</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$m</span>[<span class="php-num">2</span>]))])){
</span><span id="433" class="l"><a href="#433">433: </a> <span class="php-keyword1">return</span> ((<span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>]%<span class="php-num">2</span>) ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>) : <span class="php-quote">''</span>);
</span><span id="434" class="l"><a href="#434">434: </a>}
</span><span id="435" class="l"><a href="#435">435: </a><span class="php-comment">// attr string</span>
</span><span id="436" class="l"><a href="#436">436: </a><span class="php-var">$a</span> = <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\t&quot;</span>), <span class="php-quote">' '</span>, <span class="php-keyword2">trim</span>(<span class="php-var">$m</span>[<span class="php-num">3</span>]));
</span><span id="437" class="l"><a href="#437">437: </a><span class="php-comment">// tag transform</span>
</span><span id="438" class="l"><a href="#438">438: </a><span class="php-keyword1">static</span> <span class="php-var">$eD</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'center'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'s'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'strike'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'u'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Deprecated</span>
</span><span id="439" class="l"><a href="#439">439: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$eD</span>[<span class="php-var">$e</span>])){
</span><span id="440" class="l"><a href="#440">440: </a> <span class="php-var">$trt</span> = \DataTables\Vendor\htmLawed::hl_tag2(<span class="php-var">$e</span>, <span class="php-var">$a</span>, <span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>]);
</span><span id="441" class="l"><a href="#441">441: </a> <span class="php-keyword1">if</span>(!<span class="php-var">$e</span>){<span class="php-keyword1">return</span> ((<span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>]%<span class="php-num">2</span>) ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>) : <span class="php-quote">''</span>);}
</span><span id="442" class="l"><a href="#442">442: </a>}
</span><span id="443" class="l"><a href="#443">443: </a><span class="php-comment">// close tag</span>
</span><span id="444" class="l"><a href="#444">444: </a><span class="php-keyword1">static</span> <span class="php-var">$eE</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Empty ele</span>
</span><span id="445" class="l"><a href="#445">445: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$m</span>[<span class="php-num">1</span>])){
</span><span id="446" class="l"><a href="#446">446: </a> <span class="php-keyword1">return</span> (!<span class="php-keyword1">isset</span>(<span class="php-var">$eE</span>[<span class="php-var">$e</span>]) ? (<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>]) ? <span class="php-quote">&quot;&lt;/</span><span class="php-var">$e</span><span class="php-quote">&gt;&quot;</span> : <span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>](<span class="php-var">$e</span>)) : ((<span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>])%<span class="php-num">2</span> ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;lt;'</span>, <span class="php-quote">'&amp;gt;'</span>), <span class="php-var">$t</span>) : <span class="php-quote">''</span>));
</span><span id="447" class="l"><a href="#447">447: </a>}
</span><span id="448" class="l"><a href="#448">448: </a>
</span><span id="449" class="l"><a href="#449">449: </a><span class="php-comment">// open tag &amp; attr</span>
</span><span id="450" class="l"><a href="#450">450: </a><span class="php-keyword1">static</span> <span class="php-var">$aN</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'abbr'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'accept-charset'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'accept'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'accesskey'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'action'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'align'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'allowfullscreen'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'alt'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'archive'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'axis'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'bgcolor'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'border'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'bordercolor'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'cellpadding'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'cellspacing'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'char'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'charoff'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'charset'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'checked'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'cite'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'q'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'classid'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'clear'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'code'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'codebase'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'codetype'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'color'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'cols'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'colspan'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'compact'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'coords'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'data'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'datetime'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'del'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ins'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'declare'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'defer'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'dir'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'disabled'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'enctype'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'face'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'for'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'frame'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'frameborder'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'headers'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'height'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'href'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'hreflang'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'hspace'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'ismap'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'label'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'language'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'longdesc'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'marginheight'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'marginwidth'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'maxlength'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'method'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'model'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'multiple'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'name'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'nohref'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'noshade'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'nowrap'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'object'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onblur'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onchange'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onfocus'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onreset'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onselect'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onsubmit'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'pluginspage'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'pluginurl'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'prompt'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'readonly'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rel'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rev'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rows'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rowspan'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'rules'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'scope'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'scrolling'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'selected'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'shape'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'size'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'span'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'src'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'standby'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'start'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'summary'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'tabindex'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'target'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'type'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'usemap'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'valign'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'value'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'valuetype'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'vspace'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'width'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'wmode'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'embed'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'xml:space'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'style'</span>=&gt;<span class="php-num">1</span>)); <span class="php-comment">// Ele-specific</span>
</span><span id="451" class="l"><a href="#451">451: </a><span class="php-keyword1">static</span> <span class="php-var">$aNE</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'checked'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'compact'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'declare'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'defer'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'disabled'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ismap'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'multiple'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'nohref'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noresize'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noshade'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'nowrap'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'readonly'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'selected'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Empty</span>
</span><span id="452" class="l"><a href="#452">452: </a><span class="php-keyword1">static</span> <span class="php-var">$aNP</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'action'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'cite'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'classid'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'codebase'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'data'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'href'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'longdesc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'model'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pluginspage'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pluginurl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'usemap'</span>=&gt;<span class="php-num">1</span>); <span class="php-comment">// Need scheme check; excludes style, on* &amp; src</span>
</span><span id="453" class="l"><a href="#453">453: </a><span class="php-keyword1">static</span> <span class="php-var">$aNU</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'class'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'dir'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'id'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'lang'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'xml:lang'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onclick'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'ondblclick'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onkeydown'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onkeypress'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onkeyup'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onmousedown'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onmousemove'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onmouseout'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onmouseover'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'onmouseup'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'applet'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'font'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'style'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'title'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>)); <span class="php-comment">// Univ &amp; exceptions</span>
</span><span id="454" class="l"><a href="#454">454: </a>
</span><span id="455" class="l"><a href="#455">455: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'lc_std_val'</span>]){
</span><span id="456" class="l"><a href="#456">456: </a> <span class="php-comment">// predef attr vals for $eAL &amp; $aNE ele</span>
</span><span id="457" class="l"><a href="#457">457: </a> <span class="php-keyword1">static</span> <span class="php-var">$aNL</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'all'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'baseline'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bottom'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'center'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'char'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'checkbox'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'circle'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'cols'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'data'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'default'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'file'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'get'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'groups'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hidden'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'image'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'justify'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'left'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ltr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'middle'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'none'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'password'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'poly'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'post'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'preserve'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'radio'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rect'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ref'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'reset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'right'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'row'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rowgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rows'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'submit'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'text'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'top'</span>=&gt;<span class="php-num">1</span>);
</span><span id="458" class="l"><a href="#458">458: </a> <span class="php-keyword1">static</span> <span class="php-var">$eAL</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'area'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'bdo'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'col'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'xml:space'</span>=&gt;<span class="php-num">1</span>);
</span><span id="459" class="l"><a href="#459">459: </a> <span class="php-var">$lcase</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$eAL</span>[<span class="php-var">$e</span>]) ? <span class="php-num">1</span> : <span class="php-num">0</span>;
</span><span id="460" class="l"><a href="#460">460: </a>}
</span><span id="461" class="l"><a href="#461">461: </a>
</span><span id="462" class="l"><a href="#462">462: </a><span class="php-var">$depTr</span> = <span class="php-num">0</span>;
</span><span id="463" class="l"><a href="#463">463: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>]){
</span><span id="464" class="l"><a href="#464">464: </a> <span class="php-comment">// dep attr:applicable ele</span>
</span><span id="465" class="l"><a href="#465">465: </a> <span class="php-keyword1">static</span> <span class="php-var">$aND</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'align'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'bgcolor'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'border'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'bordercolor'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'clear'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'compact'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'height'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'hspace'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'language'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'name'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'noshade'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'nowrap'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'size'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'start'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'type'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'value'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'vspace'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>), <span class="php-quote">'width'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>));
</span><span id="466" class="l"><a href="#466">466: </a> <span class="php-keyword1">static</span> <span class="php-var">$eAD</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'a'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'img'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>);
</span><span id="467" class="l"><a href="#467">467: </a> <span class="php-var">$depTr</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$eAD</span>[<span class="php-var">$e</span>]) ? <span class="php-num">1</span> : <span class="php-num">0</span>;
</span><span id="468" class="l"><a href="#468">468: </a>}
</span><span id="469" class="l"><a href="#469">469: </a>
</span><span id="470" class="l"><a href="#470">470: </a><span class="php-comment">// attr name-vals</span>
</span><span id="471" class="l"><a href="#471">471: </a><span class="php-keyword1">if</span>(<span class="php-keyword2">strpos</span>(<span class="php-var">$a</span>, <span class="php-quote">&quot;\x01&quot;</span>) !== <span class="php-keyword1">false</span>){<span class="php-var">$a</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\x01[^\x01]*\x01`'</span>, <span class="php-quote">''</span>, <span class="php-var">$a</span>);} <span class="php-comment">// No comment/CDATA sec</span>
</span><span id="472" class="l"><a href="#472">472: </a><span class="php-var">$mode</span> = <span class="php-num">0</span>; <span class="php-var">$a</span> = <span class="php-keyword2">trim</span>(<span class="php-var">$a</span>, <span class="php-quote">' /'</span>); <span class="php-var">$aA</span> = <span class="php-keyword1">array</span>();
</span><span id="473" class="l"><a href="#473">473: </a><span class="php-keyword1">while</span>(<span class="php-keyword2">strlen</span>(<span class="php-var">$a</span>)){
</span><span id="474" class="l"><a href="#474">474: </a> <span class="php-var">$w</span> = <span class="php-num">0</span>;
</span><span id="475" class="l"><a href="#475">475: </a> <span class="php-keyword1">switch</span>(<span class="php-var">$mode</span>){
</span><span id="476" class="l"><a href="#476">476: </a>  <span class="php-keyword1">case</span> <span class="php-num">0</span>: <span class="php-comment">// Name</span>
</span><span id="477" class="l"><a href="#477">477: </a>   <span class="php-keyword1">if</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^[a-zA-Z][\-a-zA-Z:]+`'</span>, <span class="php-var">$a</span>, <span class="php-var">$m</span>)){
</span><span id="478" class="l"><a href="#478">478: </a>    <span class="php-var">$nm</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$m</span>[<span class="php-num">0</span>]);
</span><span id="479" class="l"><a href="#479">479: </a>    <span class="php-var">$w</span> = <span class="php-var">$mode</span> = <span class="php-num">1</span>; <span class="php-var">$a</span> = <span class="php-keyword2">ltrim</span>(<span class="php-keyword2">substr_replace</span>(<span class="php-var">$a</span>, <span class="php-quote">''</span>, <span class="php-num">0</span>, <span class="php-keyword2">strlen</span>(<span class="php-var">$m</span>[<span class="php-num">0</span>])));
</span><span id="480" class="l"><a href="#480">480: </a>   }
</span><span id="481" class="l"><a href="#481">481: </a>  <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-num">1</span>:
</span><span id="482" class="l"><a href="#482">482: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$a</span>[<span class="php-num">0</span>] == <span class="php-quote">'='</span>){ <span class="php-comment">// =</span>
</span><span id="483" class="l"><a href="#483">483: </a>    <span class="php-var">$w</span> = <span class="php-num">1</span>; <span class="php-var">$mode</span> = <span class="php-num">2</span>; <span class="php-var">$a</span> = <span class="php-keyword2">ltrim</span>(<span class="php-var">$a</span>, <span class="php-quote">'= '</span>);
</span><span id="484" class="l"><a href="#484">484: </a>   }<span class="php-keyword1">else</span>{ <span class="php-comment">// No val</span>
</span><span id="485" class="l"><a href="#485">485: </a>    <span class="php-var">$w</span> = <span class="php-num">1</span>; <span class="php-var">$mode</span> = <span class="php-num">0</span>; <span class="php-var">$a</span> = <span class="php-keyword2">ltrim</span>(<span class="php-var">$a</span>);
</span><span id="486" class="l"><a href="#486">486: </a>    <span class="php-var">$aA</span>[<span class="php-var">$nm</span>] = <span class="php-quote">''</span>;
</span><span id="487" class="l"><a href="#487">487: </a>   }
</span><span id="488" class="l"><a href="#488">488: </a>  <span class="php-keyword1">break</span>; <span class="php-keyword1">case</span> <span class="php-num">2</span>: <span class="php-comment">// Val</span>
</span><span id="489" class="l"><a href="#489">489: </a>   <span class="php-keyword1">if</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^((?:&quot;[^&quot;]*&quot;)|(?:\'[^\']*\')|(?:\s*[^\s&quot;\']+))(.*)`'</span>, <span class="php-var">$a</span>, <span class="php-var">$m</span>)){
</span><span id="490" class="l"><a href="#490">490: </a>    <span class="php-var">$a</span> = <span class="php-keyword2">ltrim</span>(<span class="php-var">$m</span>[<span class="php-num">2</span>]); <span class="php-var">$m</span> = <span class="php-var">$m</span>[<span class="php-num">1</span>]; <span class="php-var">$w</span> = <span class="php-num">1</span>; <span class="php-var">$mode</span> = <span class="php-num">0</span>;
</span><span id="491" class="l"><a href="#491">491: </a>    <span class="php-var">$aA</span>[<span class="php-var">$nm</span>] = <span class="php-keyword2">trim</span>(<span class="php-keyword2">str_replace</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&amp;lt;'</span>, (<span class="php-var">$m</span>[<span class="php-num">0</span>] == <span class="php-quote">'&quot;'</span> <span class="php-keyword1">or</span> <span class="php-var">$m</span>[<span class="php-num">0</span>] == <span class="php-quote">'\''</span>) ? <span class="php-keyword2">substr</span>(<span class="php-var">$m</span>, <span class="php-num">1</span>, -<span class="php-num">1</span>) : <span class="php-var">$m</span>));
</span><span id="492" class="l"><a href="#492">492: </a>   }
</span><span id="493" class="l"><a href="#493">493: </a>  <span class="php-keyword1">break</span>;
</span><span id="494" class="l"><a href="#494">494: </a> }
</span><span id="495" class="l"><a href="#495">495: </a> <span class="php-keyword1">if</span>(<span class="php-var">$w</span> == <span class="php-num">0</span>){ <span class="php-comment">// Parse errs, deal with space, &quot; &amp; '</span>
</span><span id="496" class="l"><a href="#496">496: </a>  <span class="php-var">$a</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`^(?:&quot;[^&quot;]*(&quot;|$)|\'[^\']*(\'|$)|\S)*\s*`'</span>, <span class="php-quote">''</span>, <span class="php-var">$a</span>);
</span><span id="497" class="l"><a href="#497">497: </a>  <span class="php-var">$mode</span> = <span class="php-num">0</span>;
</span><span id="498" class="l"><a href="#498">498: </a> }
</span><span id="499" class="l"><a href="#499">499: </a>}
</span><span id="500" class="l"><a href="#500">500: </a><span class="php-keyword1">if</span>(<span class="php-var">$mode</span> == <span class="php-num">1</span>){<span class="php-var">$aA</span>[<span class="php-var">$nm</span>] = <span class="php-quote">''</span>;}
</span><span id="501" class="l"><a href="#501">501: </a>
</span><span id="502" class="l"><a href="#502">502: </a><span class="php-comment">// clean attrs</span>
</span><span id="503" class="l"><a href="#503">503: </a><span class="php-keyword1">global</span> <span class="php-var">$S</span>;
</span><span id="504" class="l"><a href="#504">504: </a><span class="php-var">$rl</span> = <span class="php-keyword1">isset</span>(<span class="php-var">$S</span>[<span class="php-var">$e</span>]) ? <span class="php-var">$S</span>[<span class="php-var">$e</span>] : <span class="php-keyword1">array</span>();
</span><span id="505" class="l"><a href="#505">505: </a><span class="php-var">$a</span> = <span class="php-keyword1">array</span>(); <span class="php-var">$nfr</span> = <span class="php-num">0</span>;
</span><span id="506" class="l"><a href="#506">506: </a><span class="php-keyword1">foreach</span>(<span class="php-var">$aA</span> <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){
</span><span id="507" class="l"><a href="#507">507: </a> <span class="php-keyword1">if</span>(((<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>][<span class="php-quote">'*'</span>]) ? <span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>][<span class="php-var">$k</span>]) : !<span class="php-keyword1">isset</span>(<span class="php-var">$C</span>[<span class="php-quote">'deny_attribute'</span>][<span class="php-var">$k</span>])) &amp;&amp; (<span class="php-keyword1">isset</span>(<span class="php-var">$aN</span>[<span class="php-var">$k</span>][<span class="php-var">$e</span>]) <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$aNU</span>[<span class="php-var">$k</span>]) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$aNU</span>[<span class="php-var">$k</span>][<span class="php-var">$e</span>]))) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$rl</span>[<span class="php-quote">'n'</span>][<span class="php-var">$k</span>]) &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$rl</span>[<span class="php-quote">'n'</span>][<span class="php-quote">'*'</span>])) <span class="php-keyword1">or</span> <span class="php-keyword1">isset</span>(<span class="php-var">$rl</span>[<span class="php-var">$k</span>])){
</span><span id="508" class="l"><a href="#508">508: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$aNE</span>[<span class="php-var">$k</span>])){<span class="php-var">$v</span> = <span class="php-var">$k</span>;}
</span><span id="509" class="l"><a href="#509">509: </a>  <span class="php-keyword1">elseif</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$lcase</span>) &amp;&amp; ((<span class="php-var">$e</span> != <span class="php-quote">'button'</span> <span class="php-keyword1">or</span> <span class="php-var">$e</span> != <span class="php-quote">'input'</span>) <span class="php-keyword1">or</span> <span class="php-var">$k</span> == <span class="php-quote">'type'</span>)){ <span class="php-comment">// Rather loose but ?not cause issues</span>
</span><span id="510" class="l"><a href="#510">510: </a>   <span class="php-var">$v</span> = (<span class="php-keyword1">isset</span>(<span class="php-var">$aNL</span>[(<span class="php-var">$v2</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$v</span>))])) ? <span class="php-var">$v2</span> : <span class="php-var">$v</span>;
</span><span id="511" class="l"><a href="#511">511: </a>  }
</span><span id="512" class="l"><a href="#512">512: </a>  <span class="php-keyword1">if</span>(<span class="php-var">$k</span> == <span class="php-quote">'style'</span> &amp;&amp; !<span class="php-var">$C</span>[<span class="php-quote">'style_pass'</span>]){
</span><span id="513" class="l"><a href="#513">513: </a>   <span class="php-keyword1">if</span>(<span class="php-keyword1">false</span> !== <span class="php-keyword2">strpos</span>(<span class="php-var">$v</span>, <span class="php-quote">'&amp;#'</span>)){
</span><span id="514" class="l"><a href="#514">514: </a>    <span class="php-keyword1">static</span> <span class="php-var">$sC</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'&amp;#x20;'</span>=&gt;<span class="php-quote">' '</span>, <span class="php-quote">'&amp;#32;'</span>=&gt;<span class="php-quote">' '</span>, <span class="php-quote">'&amp;#x45;'</span>=&gt;<span class="php-quote">'e'</span>, <span class="php-quote">'&amp;#69;'</span>=&gt;<span class="php-quote">'e'</span>, <span class="php-quote">'&amp;#x65;'</span>=&gt;<span class="php-quote">'e'</span>, <span class="php-quote">'&amp;#101;'</span>=&gt;<span class="php-quote">'e'</span>, <span class="php-quote">'&amp;#x58;'</span>=&gt;<span class="php-quote">'x'</span>, <span class="php-quote">'&amp;#88;'</span>=&gt;<span class="php-quote">'x'</span>, <span class="php-quote">'&amp;#x78;'</span>=&gt;<span class="php-quote">'x'</span>, <span class="php-quote">'&amp;#120;'</span>=&gt;<span class="php-quote">'x'</span>, <span class="php-quote">'&amp;#x50;'</span>=&gt;<span class="php-quote">'p'</span>, <span class="php-quote">'&amp;#80;'</span>=&gt;<span class="php-quote">'p'</span>, <span class="php-quote">'&amp;#x70;'</span>=&gt;<span class="php-quote">'p'</span>, <span class="php-quote">'&amp;#112;'</span>=&gt;<span class="php-quote">'p'</span>, <span class="php-quote">'&amp;#x53;'</span>=&gt;<span class="php-quote">'s'</span>, <span class="php-quote">'&amp;#83;'</span>=&gt;<span class="php-quote">'s'</span>, <span class="php-quote">'&amp;#x73;'</span>=&gt;<span class="php-quote">'s'</span>, <span class="php-quote">'&amp;#115;'</span>=&gt;<span class="php-quote">'s'</span>, <span class="php-quote">'&amp;#x49;'</span>=&gt;<span class="php-quote">'i'</span>, <span class="php-quote">'&amp;#73;'</span>=&gt;<span class="php-quote">'i'</span>, <span class="php-quote">'&amp;#x69;'</span>=&gt;<span class="php-quote">'i'</span>, <span class="php-quote">'&amp;#105;'</span>=&gt;<span class="php-quote">'i'</span>, <span class="php-quote">'&amp;#x4f;'</span>=&gt;<span class="php-quote">'o'</span>, <span class="php-quote">'&amp;#79;'</span>=&gt;<span class="php-quote">'o'</span>, <span class="php-quote">'&amp;#x6f;'</span>=&gt;<span class="php-quote">'o'</span>, <span class="php-quote">'&amp;#111;'</span>=&gt;<span class="php-quote">'o'</span>, <span class="php-quote">'&amp;#x4e;'</span>=&gt;<span class="php-quote">'n'</span>, <span class="php-quote">'&amp;#78;'</span>=&gt;<span class="php-quote">'n'</span>, <span class="php-quote">'&amp;#x6e;'</span>=&gt;<span class="php-quote">'n'</span>, <span class="php-quote">'&amp;#110;'</span>=&gt;<span class="php-quote">'n'</span>, <span class="php-quote">'&amp;#x55;'</span>=&gt;<span class="php-quote">'u'</span>, <span class="php-quote">'&amp;#85;'</span>=&gt;<span class="php-quote">'u'</span>, <span class="php-quote">'&amp;#x75;'</span>=&gt;<span class="php-quote">'u'</span>, <span class="php-quote">'&amp;#117;'</span>=&gt;<span class="php-quote">'u'</span>, <span class="php-quote">'&amp;#x52;'</span>=&gt;<span class="php-quote">'r'</span>, <span class="php-quote">'&amp;#82;'</span>=&gt;<span class="php-quote">'r'</span>, <span class="php-quote">'&amp;#x72;'</span>=&gt;<span class="php-quote">'r'</span>, <span class="php-quote">'&amp;#114;'</span>=&gt;<span class="php-quote">'r'</span>, <span class="php-quote">'&amp;#x4c;'</span>=&gt;<span class="php-quote">'l'</span>, <span class="php-quote">'&amp;#76;'</span>=&gt;<span class="php-quote">'l'</span>, <span class="php-quote">'&amp;#x6c;'</span>=&gt;<span class="php-quote">'l'</span>, <span class="php-quote">'&amp;#108;'</span>=&gt;<span class="php-quote">'l'</span>, <span class="php-quote">'&amp;#x28;'</span>=&gt;<span class="php-quote">'('</span>, <span class="php-quote">'&amp;#40;'</span>=&gt;<span class="php-quote">'('</span>, <span class="php-quote">'&amp;#x29;'</span>=&gt;<span class="php-quote">')'</span>, <span class="php-quote">'&amp;#41;'</span>=&gt;<span class="php-quote">')'</span>, <span class="php-quote">'&amp;#x20;'</span>=&gt;<span class="php-quote">':'</span>, <span class="php-quote">'&amp;#32;'</span>=&gt;<span class="php-quote">':'</span>, <span class="php-quote">'&amp;#x22;'</span>=&gt;<span class="php-quote">'&quot;'</span>, <span class="php-quote">'&amp;#34;'</span>=&gt;<span class="php-quote">'&quot;'</span>, <span class="php-quote">'&amp;#x27;'</span>=&gt;<span class="php-quote">&quot;'&quot;</span>, <span class="php-quote">'&amp;#39;'</span>=&gt;<span class="php-quote">&quot;'&quot;</span>, <span class="php-quote">'&amp;#x2f;'</span>=&gt;<span class="php-quote">'/'</span>, <span class="php-quote">'&amp;#47;'</span>=&gt;<span class="php-quote">'/'</span>, <span class="php-quote">'&amp;#x2a;'</span>=&gt;<span class="php-quote">'*'</span>, <span class="php-quote">'&amp;#42;'</span>=&gt;<span class="php-quote">'*'</span>, <span class="php-quote">'&amp;#x5c;'</span>=&gt;<span class="php-quote">'\\'</span>, <span class="php-quote">'&amp;#92;'</span>=&gt;<span class="php-quote">'\\'</span>);
</span><span id="515" class="l"><a href="#515">515: </a>    <span class="php-var">$v</span> = <span class="php-keyword2">strtr</span>(<span class="php-var">$v</span>, <span class="php-var">$sC</span>);
</span><span id="516" class="l"><a href="#516">516: </a>   }
</span><span id="517" class="l"><a href="#517">517: </a>   <span class="php-var">$v</span> = <span class="php-keyword2">preg_replace_callback</span>(<span class="php-quote">'`(url(?:\()(?: )*(?:\'|&quot;|&amp;(?:quot|apos);)?)(.+?)((?:\'|&quot;|&amp;(?:quot|apos);)?(?: )*(?:\)))`iS'</span>, <span class="php-quote">'\DataTables\Vendor\htmLawed::hl_prot'</span>, <span class="php-var">$v</span>);
</span><span id="518" class="l"><a href="#518">518: </a>   <span class="php-var">$v</span> = !<span class="php-var">$C</span>[<span class="php-quote">'css_expression'</span>] ? <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`expression`i'</span>, <span class="php-quote">' '</span>, <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\\\\\S|(/|(%2f))(\*|(%2a))`i'</span>, <span class="php-quote">' '</span>, <span class="php-var">$v</span>)) : <span class="php-var">$v</span>;
</span><span id="519" class="l"><a href="#519">519: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$aNP</span>[<span class="php-var">$k</span>]) <span class="php-keyword1">or</span> <span class="php-keyword2">strpos</span>(<span class="php-var">$k</span>, <span class="php-quote">'src'</span>) !== <span class="php-keyword1">false</span> <span class="php-keyword1">or</span> <span class="php-var">$k</span>[<span class="php-num">0</span>] == <span class="php-quote">'o'</span>){
</span><span id="520" class="l"><a href="#520">520: </a>   <span class="php-var">$v</span> = <span class="php-keyword2">str_replace</span>(<span class="php-quote">&quot;­&quot;</span>, <span class="php-quote">' '</span>, (<span class="php-keyword2">strpos</span>(<span class="php-var">$v</span>, <span class="php-quote">'&amp;'</span>) !== <span class="php-keyword1">false</span> ? <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'&amp;#xad;'</span>, <span class="php-quote">'&amp;#173;'</span>, <span class="php-quote">'&amp;shy;'</span>), <span class="php-quote">' '</span>, <span class="php-var">$v</span>) : <span class="php-var">$v</span>)); <span class="php-comment"># double-quoted char is soft-hyphen; appears here as &quot;­&quot; or hyphen or something else depending on viewing software</span>
</span><span id="521" class="l"><a href="#521">521: </a>   <span class="php-var">$v</span> = \DataTables\Vendor\htmLawed::hl_prot(<span class="php-var">$v</span>, <span class="php-var">$k</span>);
</span><span id="522" class="l"><a href="#522">522: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$k</span> == <span class="php-quote">'href'</span>){ <span class="php-comment">// X-spam</span>
</span><span id="523" class="l"><a href="#523">523: </a>    <span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_mail_spam'</span>] &amp;&amp; <span class="php-keyword2">strpos</span>(<span class="php-var">$v</span>, <span class="php-quote">'mailto:'</span>) === <span class="php-num">0</span>){
</span><span id="524" class="l"><a href="#524">524: </a>     <span class="php-var">$v</span> = <span class="php-keyword2">str_replace</span>(<span class="php-quote">'@'</span>, <span class="php-keyword2">htmlspecialchars</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_mail_spam'</span>]), <span class="php-var">$v</span>);
</span><span id="525" class="l"><a href="#525">525: </a>    }<span class="php-keyword1">elseif</span>(<span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>]){
</span><span id="526" class="l"><a href="#526">526: </a>     <span class="php-var">$r1</span> = <span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">1</span>];
</span><span id="527" class="l"><a href="#527">527: </a>     <span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$r1</span>) &amp;&amp; <span class="php-keyword2">preg_match</span>(<span class="php-var">$r1</span>, <span class="php-var">$v</span>)){<span class="php-keyword1">continue</span>;}
</span><span id="528" class="l"><a href="#528">528: </a>     <span class="php-var">$r0</span> = <span class="php-var">$C</span>[<span class="php-quote">'anti_link_spam'</span>][<span class="php-num">0</span>];
</span><span id="529" class="l"><a href="#529">529: </a>     <span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$r0</span>) &amp;&amp; <span class="php-keyword2">preg_match</span>(<span class="php-var">$r0</span>, <span class="php-var">$v</span>)){
</span><span id="530" class="l"><a href="#530">530: </a>      <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'rel'</span>])){
</span><span id="531" class="l"><a href="#531">531: </a>       <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`\bnofollow\b`i'</span>, <span class="php-var">$a</span>[<span class="php-quote">'rel'</span>])){<span class="php-var">$a</span>[<span class="php-quote">'rel'</span>] .= <span class="php-quote">' nofollow'</span>;}
</span><span id="532" class="l"><a href="#532">532: </a>      }<span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$aA</span>[<span class="php-quote">'rel'</span>])){
</span><span id="533" class="l"><a href="#533">533: </a>       <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`\bnofollow\b`i'</span>, <span class="php-var">$aA</span>[<span class="php-quote">'rel'</span>])){<span class="php-var">$nfr</span> = <span class="php-num">1</span>;}
</span><span id="534" class="l"><a href="#534">534: </a>      }<span class="php-keyword1">else</span>{<span class="php-var">$a</span>[<span class="php-quote">'rel'</span>] = <span class="php-quote">'nofollow'</span>;}
</span><span id="535" class="l"><a href="#535">535: </a>     }
</span><span id="536" class="l"><a href="#536">536: </a>    }
</span><span id="537" class="l"><a href="#537">537: </a>   }
</span><span id="538" class="l"><a href="#538">538: </a>  }
</span><span id="539" class="l"><a href="#539">539: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$rl</span>[<span class="php-var">$k</span>]) &amp;&amp; <span class="php-keyword2">is_array</span>(<span class="php-var">$rl</span>[<span class="php-var">$k</span>]) &amp;&amp; (<span class="php-var">$v</span> = \DataTables\Vendor\htmLawed::hl_attrval(<span class="php-var">$k</span>, <span class="php-var">$v</span>, <span class="php-var">$rl</span>[<span class="php-var">$k</span>])) === <span class="php-num">0</span>){<span class="php-keyword1">continue</span>;}
</span><span id="540" class="l"><a href="#540">540: </a>  <span class="php-var">$a</span>[<span class="php-var">$k</span>] = <span class="php-keyword2">str_replace</span>(<span class="php-quote">'&quot;'</span>, <span class="php-quote">'&amp;quot;'</span>, <span class="php-var">$v</span>);
</span><span id="541" class="l"><a href="#541">541: </a> }
</span><span id="542" class="l"><a href="#542">542: </a>}
</span><span id="543" class="l"><a href="#543">543: </a><span class="php-keyword1">if</span>(<span class="php-var">$nfr</span>){<span class="php-var">$a</span>[<span class="php-quote">'rel'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'rel'</span>]) ? <span class="php-var">$a</span>[<span class="php-quote">'rel'</span>]. <span class="php-quote">' nofollow'</span> : <span class="php-quote">'nofollow'</span>;}
</span><span id="544" class="l"><a href="#544">544: </a>
</span><span id="545" class="l"><a href="#545">545: </a><span class="php-comment">// rqd attr</span>
</span><span id="546" class="l"><a href="#546">546: </a><span class="php-keyword1">static</span> <span class="php-var">$eAR</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'area'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'alt'</span>=&gt;<span class="php-quote">'area'</span>), <span class="php-quote">'bdo'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'dir'</span>=&gt;<span class="php-quote">'ltr'</span>), <span class="php-quote">'form'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'action'</span>=&gt;<span class="php-quote">''</span>), <span class="php-quote">'img'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'src'</span>=&gt;<span class="php-quote">''</span>, <span class="php-quote">'alt'</span>=&gt;<span class="php-quote">'image'</span>), <span class="php-quote">'map'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'name'</span>=&gt;<span class="php-quote">''</span>), <span class="php-quote">'optgroup'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'label'</span>=&gt;<span class="php-quote">''</span>), <span class="php-quote">'param'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'name'</span>=&gt;<span class="php-quote">''</span>), <span class="php-quote">'script'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'type'</span>=&gt;<span class="php-quote">'text/javascript'</span>), <span class="php-quote">'textarea'</span>=&gt;<span class="php-keyword1">array</span>(<span class="php-quote">'rows'</span>=&gt;<span class="php-quote">'10'</span>, <span class="php-quote">'cols'</span>=&gt;<span class="php-quote">'50'</span>));
</span><span id="547" class="l"><a href="#547">547: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$eAR</span>[<span class="php-var">$e</span>])){
</span><span id="548" class="l"><a href="#548">548: </a> <span class="php-keyword1">foreach</span>(<span class="php-var">$eAR</span>[<span class="php-var">$e</span>] <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){
</span><span id="549" class="l"><a href="#549">549: </a>  <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-var">$k</span>])){<span class="php-var">$a</span>[<span class="php-var">$k</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$v</span>[<span class="php-num">0</span>]) ? <span class="php-var">$v</span> : <span class="php-var">$k</span>;}
</span><span id="550" class="l"><a href="#550">550: </a> }
</span><span id="551" class="l"><a href="#551">551: </a>}
</span><span id="552" class="l"><a href="#552">552: </a>
</span><span id="553" class="l"><a href="#553">553: </a><span class="php-comment">// depr attrs</span>
</span><span id="554" class="l"><a href="#554">554: </a><span class="php-keyword1">if</span>(<span class="php-var">$depTr</span>){
</span><span id="555" class="l"><a href="#555">555: </a> <span class="php-var">$c</span> = <span class="php-keyword1">array</span>();
</span><span id="556" class="l"><a href="#556">556: </a> <span class="php-keyword1">foreach</span>(<span class="php-var">$a</span> <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){
</span><span id="557" class="l"><a href="#557">557: </a>  <span class="php-keyword1">if</span>(<span class="php-var">$k</span> == <span class="php-quote">'style'</span> <span class="php-keyword1">or</span> !<span class="php-keyword1">isset</span>(<span class="php-var">$aND</span>[<span class="php-var">$k</span>][<span class="php-var">$e</span>])){<span class="php-keyword1">continue</span>;}
</span><span id="558" class="l"><a href="#558">558: </a>  <span class="php-keyword1">if</span>(<span class="php-var">$k</span> == <span class="php-quote">'align'</span>){
</span><span id="559" class="l"><a href="#559">559: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'align'</span>]);
</span><span id="560" class="l"><a href="#560">560: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'img'</span> &amp;&amp; (<span class="php-var">$v</span> == <span class="php-quote">'left'</span> <span class="php-keyword1">or</span> <span class="php-var">$v</span> == <span class="php-quote">'right'</span>)){<span class="php-var">$c</span>[] = <span class="php-quote">'float: '</span>. <span class="php-var">$v</span>;}
</span><span id="561" class="l"><a href="#561">561: </a>   <span class="php-keyword1">elseif</span>((<span class="php-var">$e</span> == <span class="php-quote">'div'</span> <span class="php-keyword1">or</span> <span class="php-var">$e</span> == <span class="php-quote">'table'</span>) &amp;&amp; <span class="php-var">$v</span> == <span class="php-quote">'center'</span>){<span class="php-var">$c</span>[] = <span class="php-quote">'margin: auto'</span>;}
</span><span id="562" class="l"><a href="#562">562: </a>   <span class="php-keyword1">else</span>{<span class="php-var">$c</span>[] = <span class="php-quote">'text-align: '</span>. <span class="php-var">$v</span>;}
</span><span id="563" class="l"><a href="#563">563: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'bgcolor'</span>){
</span><span id="564" class="l"><a href="#564">564: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'bgcolor'</span>]);
</span><span id="565" class="l"><a href="#565">565: </a>   <span class="php-var">$c</span>[] = <span class="php-quote">'background-color: '</span>. <span class="php-var">$v</span>;
</span><span id="566" class="l"><a href="#566">566: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'border'</span>){
</span><span id="567" class="l"><a href="#567">567: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'border'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">&quot;border: </span><span class="php-var">{$v}</span><span class="php-quote">px&quot;</span>;
</span><span id="568" class="l"><a href="#568">568: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'bordercolor'</span>){
</span><span id="569" class="l"><a href="#569">569: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'bordercolor'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'border-color: '</span>. <span class="php-var">$v</span>;
</span><span id="570" class="l"><a href="#570">570: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'clear'</span>){
</span><span id="571" class="l"><a href="#571">571: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'clear'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'clear: '</span>. (<span class="php-var">$v</span> != <span class="php-quote">'all'</span> ? <span class="php-var">$v</span> : <span class="php-quote">'both'</span>);
</span><span id="572" class="l"><a href="#572">572: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'compact'</span>){
</span><span id="573" class="l"><a href="#573">573: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'compact'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'font-size: 85%'</span>;
</span><span id="574" class="l"><a href="#574">574: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'height'</span> <span class="php-keyword1">or</span> <span class="php-var">$k</span> == <span class="php-quote">'width'</span>){
</span><span id="575" class="l"><a href="#575">575: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-var">$k</span>]); <span class="php-var">$c</span>[] = <span class="php-var">$k</span>. <span class="php-quote">': '</span>. (<span class="php-var">$v</span>[<span class="php-num">0</span>] != <span class="php-quote">'*'</span> ? <span class="php-var">$v</span>. (<span class="php-keyword2">ctype_digit</span>(<span class="php-var">$v</span>) ? <span class="php-quote">'px'</span> : <span class="php-quote">''</span>) : <span class="php-quote">'auto'</span>);
</span><span id="576" class="l"><a href="#576">576: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'hspace'</span>){
</span><span id="577" class="l"><a href="#577">577: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'hspace'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">&quot;margin-left: </span><span class="php-var">{$v}</span><span class="php-quote">px; margin-right: </span><span class="php-var">{$v}</span><span class="php-quote">px&quot;</span>;
</span><span id="578" class="l"><a href="#578">578: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'language'</span> &amp;&amp; !<span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'type'</span>])){
</span><span id="579" class="l"><a href="#579">579: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'language'</span>]);
</span><span id="580" class="l"><a href="#580">580: </a>   <span class="php-var">$a</span>[<span class="php-quote">'type'</span>] = <span class="php-quote">'text/'</span>. <span class="php-keyword2">strtolower</span>(<span class="php-var">$v</span>);
</span><span id="581" class="l"><a href="#581">581: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'name'</span>){
</span><span id="582" class="l"><a href="#582">582: </a>   <span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>] == <span class="php-num">2</span> <span class="php-keyword1">or</span> (<span class="php-var">$e</span> != <span class="php-quote">'a'</span> &amp;&amp; <span class="php-var">$e</span> != <span class="php-quote">'map'</span>)){<span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'name'</span>]);}
</span><span id="583" class="l"><a href="#583">583: </a>   <span class="php-keyword1">if</span>(!<span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'id'</span>]) &amp;&amp; <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`[a-zA-Z][a-zA-Z\d.:_\-]*`'</span>, <span class="php-var">$v</span>)){<span class="php-var">$a</span>[<span class="php-quote">'id'</span>] = <span class="php-var">$v</span>;}
</span><span id="584" class="l"><a href="#584">584: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'noshade'</span>){
</span><span id="585" class="l"><a href="#585">585: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'noshade'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'border-style: none; border: 0; background-color: gray; color: gray'</span>;
</span><span id="586" class="l"><a href="#586">586: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'nowrap'</span>){
</span><span id="587" class="l"><a href="#587">587: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'nowrap'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'white-space: nowrap'</span>;
</span><span id="588" class="l"><a href="#588">588: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'size'</span>){
</span><span id="589" class="l"><a href="#589">589: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'size'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">'size: '</span>. <span class="php-var">$v</span>. <span class="php-quote">'px'</span>;
</span><span id="590" class="l"><a href="#590">590: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'start'</span> <span class="php-keyword1">or</span> <span class="php-var">$k</span> == <span class="php-quote">'value'</span>){
</span><span id="591" class="l"><a href="#591">591: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-var">$k</span>]);
</span><span id="592" class="l"><a href="#592">592: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'type'</span>){
</span><span id="593" class="l"><a href="#593">593: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'type'</span>]);
</span><span id="594" class="l"><a href="#594">594: </a>   <span class="php-keyword1">static</span> <span class="php-var">$ol_type</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'i'</span>=&gt;<span class="php-quote">'lower-roman'</span>, <span class="php-quote">'I'</span>=&gt;<span class="php-quote">'upper-roman'</span>, <span class="php-quote">'a'</span>=&gt;<span class="php-quote">'lower-latin'</span>, <span class="php-quote">'A'</span>=&gt;<span class="php-quote">'upper-latin'</span>, <span class="php-quote">'1'</span>=&gt;<span class="php-quote">'decimal'</span>);
</span><span id="595" class="l"><a href="#595">595: </a>   <span class="php-var">$c</span>[] = <span class="php-quote">'list-style-type: '</span>. (<span class="php-keyword1">isset</span>(<span class="php-var">$ol_type</span>[<span class="php-var">$v</span>]) ? <span class="php-var">$ol_type</span>[<span class="php-var">$v</span>] : <span class="php-quote">'decimal'</span>);
</span><span id="596" class="l"><a href="#596">596: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-var">$k</span> == <span class="php-quote">'vspace'</span>){
</span><span id="597" class="l"><a href="#597">597: </a>   <span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'vspace'</span>]); <span class="php-var">$c</span>[] = <span class="php-quote">&quot;margin-top: </span><span class="php-var">{$v}</span><span class="php-quote">px; margin-bottom: </span><span class="php-var">{$v}</span><span class="php-quote">px&quot;</span>;
</span><span id="598" class="l"><a href="#598">598: </a>  }
</span><span id="599" class="l"><a href="#599">599: </a> }
</span><span id="600" class="l"><a href="#600">600: </a> <span class="php-keyword1">if</span>(<span class="php-keyword2">count</span>(<span class="php-var">$c</span>)){
</span><span id="601" class="l"><a href="#601">601: </a>  <span class="php-var">$c</span> = <span class="php-keyword2">implode</span>(<span class="php-quote">'; '</span>, <span class="php-var">$c</span>);
</span><span id="602" class="l"><a href="#602">602: </a>  <span class="php-var">$a</span>[<span class="php-quote">'style'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'style'</span>]) ? <span class="php-keyword2">rtrim</span>(<span class="php-var">$a</span>[<span class="php-quote">'style'</span>], <span class="php-quote">' ;'</span>). <span class="php-quote">'; '</span>. <span class="php-var">$c</span>. <span class="php-quote">';'</span>: <span class="php-var">$c</span>. <span class="php-quote">';'</span>;
</span><span id="603" class="l"><a href="#603">603: </a> }
</span><span id="604" class="l"><a href="#604">604: </a>}
</span><span id="605" class="l"><a href="#605">605: </a><span class="php-comment">// unique ID</span>
</span><span id="606" class="l"><a href="#606">606: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'id'</span>])){
</span><span id="607" class="l"><a href="#607">607: </a> <span class="php-keyword1">if</span>(!<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`^[A-Za-z][A-Za-z0-9_\-.:]*$`'</span>, (<span class="php-var">$id</span> = <span class="php-var">$a</span>[<span class="php-quote">'id'</span>])) <span class="php-keyword1">or</span> (<span class="php-keyword1">isset</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'hl_Ids'</span>][<span class="php-var">$id</span>]) &amp;&amp; <span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] == <span class="php-num">1</span>)){<span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'id'</span>]);
</span><span id="608" class="l"><a href="#608">608: </a> }<span class="php-keyword1">else</span>{
</span><span id="609" class="l"><a href="#609">609: </a>  <span class="php-keyword1">while</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$GLOBALS</span>[<span class="php-quote">'hl_Ids'</span>][<span class="php-var">$id</span>])){<span class="php-var">$id</span> = <span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>]. <span class="php-var">$id</span>;}
</span><span id="610" class="l"><a href="#610">610: </a>  <span class="php-var">$GLOBALS</span>[<span class="php-quote">'hl_Ids'</span>][(<span class="php-var">$a</span>[<span class="php-quote">'id'</span>] = <span class="php-var">$id</span>)] = <span class="php-num">1</span>;
</span><span id="611" class="l"><a href="#611">611: </a> }
</span><span id="612" class="l"><a href="#612">612: </a>}
</span><span id="613" class="l"><a href="#613">613: </a><span class="php-comment">// xml:lang</span>
</span><span id="614" class="l"><a href="#614">614: </a><span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] &amp;&amp; <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'lang'</span>])){
</span><span id="615" class="l"><a href="#615">615: </a> <span class="php-var">$a</span>[<span class="php-quote">'xml:lang'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'xml:lang'</span>]) ? <span class="php-var">$a</span>[<span class="php-quote">'xml:lang'</span>] : <span class="php-var">$a</span>[<span class="php-quote">'lang'</span>];
</span><span id="616" class="l"><a href="#616">616: </a> <span class="php-keyword1">if</span>(<span class="php-var">$C</span>[<span class="php-quote">'xml:lang'</span>] == <span class="php-num">2</span>){<span class="php-keyword1">unset</span>(<span class="php-var">$a</span>[<span class="php-quote">'lang'</span>]);}
</span><span id="617" class="l"><a href="#617">617: </a>}
</span><span id="618" class="l"><a href="#618">618: </a><span class="php-comment">// for transformed tag</span>
</span><span id="619" class="l"><a href="#619">619: </a><span class="php-keyword1">if</span>(!<span class="php-keyword1">empty</span>(<span class="php-var">$trt</span>)){
</span><span id="620" class="l"><a href="#620">620: </a> <span class="php-var">$a</span>[<span class="php-quote">'style'</span>] = <span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-quote">'style'</span>]) ? <span class="php-keyword2">rtrim</span>(<span class="php-var">$a</span>[<span class="php-quote">'style'</span>], <span class="php-quote">' ;'</span>). <span class="php-quote">'; '</span>. <span class="php-var">$trt</span> : <span class="php-var">$trt</span>;
</span><span id="621" class="l"><a href="#621">621: </a>}
</span><span id="622" class="l"><a href="#622">622: </a><span class="php-comment">// return with empty ele /</span>
</span><span id="623" class="l"><a href="#623">623: </a><span class="php-keyword1">if</span>(<span class="php-keyword1">empty</span>(<span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>])){
</span><span id="624" class="l"><a href="#624">624: </a> <span class="php-var">$aA</span> = <span class="php-quote">''</span>;
</span><span id="625" class="l"><a href="#625">625: </a> <span class="php-keyword1">foreach</span>(<span class="php-var">$a</span> <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){<span class="php-var">$aA</span> .= <span class="php-quote">&quot; </span><span class="php-var">{$k}</span><span class="php-quote">=\&quot;</span><span class="php-var">{$v}</span><span class="php-quote">\&quot;&quot;</span>;}
</span><span id="626" class="l"><a href="#626">626: </a> <span class="php-keyword1">return</span> <span class="php-quote">&quot;&lt;</span><span class="php-var">{$e}{$aA}</span><span class="php-quote">&quot;</span>. (<span class="php-keyword1">isset</span>(<span class="php-var">$eE</span>[<span class="php-var">$e</span>]) ? <span class="php-quote">' /'</span> : <span class="php-quote">''</span>). <span class="php-quote">'&gt;'</span>;
</span><span id="627" class="l"><a href="#627">627: </a>}
</span><span id="628" class="l"><a href="#628">628: </a><span class="php-keyword1">else</span>{<span class="php-keyword1">return</span> <span class="php-var">$C</span>[<span class="php-quote">'hook_tag'</span>](<span class="php-var">$e</span>, <span class="php-var">$a</span>);}
</span><span id="629" class="l"><a href="#629">629: </a><span class="php-comment">// eof</span>
</span><span id="630" class="l"><a href="#630">630: </a>}
</span><span id="631" class="l"><a href="#631">631: </a>
</span><span id="632" class="l"><a href="#632">632: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_tag2(&amp;<span class="php-var">$e</span>, &amp;<span class="php-var">$a</span>, <span class="php-var">$t</span>=<span class="php-num">1</span>){
</span><span id="633" class="l"><a href="#633">633: </a><span class="php-comment">// transform tag</span>
</span><span id="634" class="l"><a href="#634">634: </a><span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'center'</span>){<span class="php-var">$e</span> = <span class="php-quote">'div'</span>; <span class="php-keyword1">return</span> <span class="php-quote">'text-align: center;'</span>;}
</span><span id="635" class="l"><a href="#635">635: </a><span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'dir'</span> <span class="php-keyword1">or</span> <span class="php-var">$e</span> == <span class="php-quote">'menu'</span>){<span class="php-var">$e</span> = <span class="php-quote">'ul'</span>; <span class="php-keyword1">return</span> <span class="php-quote">''</span>;}
</span><span id="636" class="l"><a href="#636">636: </a><span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'s'</span> <span class="php-keyword1">or</span> <span class="php-var">$e</span> == <span class="php-quote">'strike'</span>){<span class="php-var">$e</span> = <span class="php-quote">'span'</span>; <span class="php-keyword1">return</span> <span class="php-quote">'text-decoration: line-through;'</span>;}
</span><span id="637" class="l"><a href="#637">637: </a><span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'u'</span>){<span class="php-var">$e</span> = <span class="php-quote">'span'</span>; <span class="php-keyword1">return</span> <span class="php-quote">'text-decoration: underline;'</span>;}
</span><span id="638" class="l"><a href="#638">638: </a><span class="php-keyword1">static</span> <span class="php-var">$fs</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'0'</span>=&gt;<span class="php-quote">'xx-small'</span>, <span class="php-quote">'1'</span>=&gt;<span class="php-quote">'xx-small'</span>, <span class="php-quote">'2'</span>=&gt;<span class="php-quote">'small'</span>, <span class="php-quote">'3'</span>=&gt;<span class="php-quote">'medium'</span>, <span class="php-quote">'4'</span>=&gt;<span class="php-quote">'large'</span>, <span class="php-quote">'5'</span>=&gt;<span class="php-quote">'x-large'</span>, <span class="php-quote">'6'</span>=&gt;<span class="php-quote">'xx-large'</span>, <span class="php-quote">'7'</span>=&gt;<span class="php-quote">'300%'</span>, <span class="php-quote">'-1'</span>=&gt;<span class="php-quote">'smaller'</span>, <span class="php-quote">'-2'</span>=&gt;<span class="php-quote">'60%'</span>, <span class="php-quote">'+1'</span>=&gt;<span class="php-quote">'larger'</span>, <span class="php-quote">'+2'</span>=&gt;<span class="php-quote">'150%'</span>, <span class="php-quote">'+3'</span>=&gt;<span class="php-quote">'200%'</span>, <span class="php-quote">'+4'</span>=&gt;<span class="php-quote">'300%'</span>);
</span><span id="639" class="l"><a href="#639">639: </a><span class="php-keyword1">if</span>(<span class="php-var">$e</span> == <span class="php-quote">'font'</span>){
</span><span id="640" class="l"><a href="#640">640: </a> <span class="php-var">$a2</span> = <span class="php-quote">''</span>;
</span><span id="641" class="l"><a href="#641">641: </a> <span class="php-keyword1">while</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`(^|\s)(color|size)\s*=\s*(\'|&quot;)?(.+?)(\\3|\s|$)`i'</span>, <span class="php-var">$a</span>, <span class="php-var">$m</span>)){
</span><span id="642" class="l"><a href="#642">642: </a>  <span class="php-var">$a</span> = <span class="php-keyword2">str_replace</span>(<span class="php-var">$m</span>[<span class="php-num">0</span>], <span class="php-quote">' '</span>, <span class="php-var">$a</span>);
</span><span id="643" class="l"><a href="#643">643: </a>  <span class="php-var">$a2</span> .= <span class="php-keyword2">strtolower</span>(<span class="php-var">$m</span>[<span class="php-num">2</span>]) == <span class="php-quote">'color'</span> ? (<span class="php-quote">' color: '</span>. <span class="php-keyword2">str_replace</span>(<span class="php-quote">'&quot;'</span>, <span class="php-quote">'\''</span>, <span class="php-keyword2">trim</span>(<span class="php-var">$m</span>[<span class="php-num">4</span>])). <span class="php-quote">';'</span>) : (<span class="php-keyword1">isset</span>(<span class="php-var">$fs</span>[(<span class="php-var">$m</span> = <span class="php-keyword2">trim</span>(<span class="php-var">$m</span>[<span class="php-num">4</span>]))]) ? (<span class="php-var">$a2</span> .= <span class="php-quote">' font-size: '</span>. <span class="php-keyword2">str_replace</span>(<span class="php-quote">'&quot;'</span>, <span class="php-quote">'\''</span>, <span class="php-var">$fs</span>[<span class="php-var">$m</span>]). <span class="php-quote">';'</span>) : <span class="php-quote">''</span>);
</span><span id="644" class="l"><a href="#644">644: </a> }
</span><span id="645" class="l"><a href="#645">645: </a> <span class="php-keyword1">while</span>(<span class="php-keyword2">preg_match</span>(<span class="php-quote">'`(^|\s)face\s*=\s*(\'|&quot;)?([^=]+?)\\2`i'</span>, <span class="php-var">$a</span>, <span class="php-var">$m</span>) <span class="php-keyword1">or</span> <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`(^|\s)face\s*=(\s*)(\S+)`i'</span>, <span class="php-var">$a</span>, <span class="php-var">$m</span>)){
</span><span id="646" class="l"><a href="#646">646: </a>  <span class="php-var">$a</span> = <span class="php-keyword2">str_replace</span>(<span class="php-var">$m</span>[<span class="php-num">0</span>], <span class="php-quote">' '</span>, <span class="php-var">$a</span>);
</span><span id="647" class="l"><a href="#647">647: </a>  <span class="php-var">$a2</span> .= <span class="php-quote">' font-family: '</span>. <span class="php-keyword2">str_replace</span>(<span class="php-quote">'&quot;'</span>, <span class="php-quote">'\''</span>, <span class="php-keyword2">trim</span>(<span class="php-var">$m</span>[<span class="php-num">3</span>])). <span class="php-quote">';'</span>;
</span><span id="648" class="l"><a href="#648">648: </a> }
</span><span id="649" class="l"><a href="#649">649: </a> <span class="php-var">$e</span> = <span class="php-quote">'span'</span>; <span class="php-keyword1">return</span> <span class="php-keyword2">ltrim</span>(<span class="php-keyword2">str_replace</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">''</span>, <span class="php-var">$a2</span>));
</span><span id="650" class="l"><a href="#650">650: </a>}
</span><span id="651" class="l"><a href="#651">651: </a><span class="php-keyword1">if</span>(<span class="php-var">$t</span> == <span class="php-num">2</span>){<span class="php-var">$e</span> = <span class="php-num">0</span>; <span class="php-keyword1">return</span> <span class="php-num">0</span>;}
</span><span id="652" class="l"><a href="#652">652: </a><span class="php-keyword1">return</span> <span class="php-quote">''</span>;
</span><span id="653" class="l"><a href="#653">653: </a><span class="php-comment">// eof</span>
</span><span id="654" class="l"><a href="#654">654: </a>}
</span><span id="655" class="l"><a href="#655">655: </a>
</span><span id="656" class="l"><a href="#656">656: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_tidy(<span class="php-var">$t</span>, <span class="php-var">$w</span>, <span class="php-var">$p</span>){
</span><span id="657" class="l"><a href="#657">657: </a><span class="php-comment">// Tidy/compact HTM</span>
</span><span id="658" class="l"><a href="#658">658: </a><span class="php-keyword1">if</span>(<span class="php-keyword2">strpos</span>(<span class="php-quote">' pre,script,textarea'</span>, <span class="php-quote">&quot;</span><span class="php-var">$p</span><span class="php-quote">,&quot;</span>)){<span class="php-keyword1">return</span> <span class="php-var">$t</span>;}
</span><span id="659" class="l"><a href="#659">659: </a><span class="php-var">$t</span> = <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`\s+`'</span>, <span class="php-quote">' '</span>, <span class="php-keyword2">preg_replace_callback</span>(<span class="php-keyword1">array</span>(<span class="php-quote">'`(&lt;(!\[CDATA\[))(.+?)(\]\]&gt;)`sm'</span>, <span class="php-quote">'`(&lt;(!--))(.+?)(--&gt;)`sm'</span>, <span class="php-quote">'`(&lt;(pre|script|textarea)[^&gt;]*?&gt;)(.+?)(&lt;/\2&gt;)`sm'</span>), <span class="php-keyword2">create_function</span>(<span class="php-quote">'$m'</span>, <span class="php-quote">'return $m[1]. str_replace(array(&quot;&lt;&quot;, &quot;&gt;&quot;, &quot;\n&quot;, &quot;\r&quot;, &quot;\t&quot;, &quot; &quot;), array(&quot;\x01&quot;, &quot;\x02&quot;, &quot;\x03&quot;, &quot;\x04&quot;, &quot;\x05&quot;, &quot;\x07&quot;), $m[3]). $m[4];'</span>), <span class="php-var">$t</span>));
</span><span id="660" class="l"><a href="#660">660: </a><span class="php-keyword1">if</span>((<span class="php-var">$w</span> = <span class="php-keyword2">strtolower</span>(<span class="php-var">$w</span>)) == -<span class="php-num">1</span>){
</span><span id="661" class="l"><a href="#661">661: </a> <span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x01&quot;</span>, <span class="php-quote">&quot;\x02&quot;</span>, <span class="php-quote">&quot;\x03&quot;</span>, <span class="php-quote">&quot;\x04&quot;</span>, <span class="php-quote">&quot;\x05&quot;</span>, <span class="php-quote">&quot;\x07&quot;</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>, <span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">' '</span>), <span class="php-var">$t</span>);
</span><span id="662" class="l"><a href="#662">662: </a>}
</span><span id="663" class="l"><a href="#663">663: </a><span class="php-var">$s</span> = <span class="php-keyword2">strpos</span>(<span class="php-quote">&quot; </span><span class="php-var">$w</span><span class="php-quote">&quot;</span>, <span class="php-quote">'t'</span>) ? <span class="php-quote">&quot;\t&quot;</span> : <span class="php-quote">' '</span>;
</span><span id="664" class="l"><a href="#664">664: </a><span class="php-var">$s</span> = <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`\d`'</span>, <span class="php-var">$w</span>, <span class="php-var">$m</span>) ? <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, <span class="php-var">$m</span>[<span class="php-num">0</span>]) : <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, (<span class="php-var">$s</span> == <span class="php-quote">&quot;\t&quot;</span> ? <span class="php-num">1</span> : <span class="php-num">2</span>));
</span><span id="665" class="l"><a href="#665">665: </a><span class="php-var">$N</span> = <span class="php-keyword2">preg_match</span>(<span class="php-quote">'`[ts]([1-9])`'</span>, <span class="php-var">$w</span>, <span class="php-var">$m</span>) ? <span class="php-var">$m</span>[<span class="php-num">1</span>] : <span class="php-num">0</span>;
</span><span id="666" class="l"><a href="#666">666: </a><span class="php-var">$a</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'br'</span>=&gt;<span class="php-num">1</span>);
</span><span id="667" class="l"><a href="#667">667: </a><span class="php-var">$b</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'button'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'input'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'option'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'param'</span>=&gt;<span class="php-num">1</span>);
</span><span id="668" class="l"><a href="#668">668: </a><span class="php-var">$c</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'caption'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dd'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dt'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h1'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h2'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h3'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h4'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h5'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'h6'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'isindex'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'label'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'legend'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'li'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'object'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'p'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'pre'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'td'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'textarea'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'th'</span>=&gt;<span class="php-num">1</span>);
</span><span id="669" class="l"><a href="#669">669: </a><span class="php-var">$d</span> = <span class="php-keyword1">array</span>(<span class="php-quote">'address'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'blockquote'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'center'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'colgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dir'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'div'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'dl'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'fieldset'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'form'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'hr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'iframe'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'map'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'menu'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'noscript'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ol'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'optgroup'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rbc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'rtc'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ruby'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'script'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'select'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'table'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tbody'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tfoot'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'thead'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'tr'</span>=&gt;<span class="php-num">1</span>, <span class="php-quote">'ul'</span>=&gt;<span class="php-num">1</span>);
</span><span id="670" class="l"><a href="#670">670: </a><span class="php-var">$T</span> = <span class="php-keyword2">explode</span>(<span class="php-quote">'&lt;'</span>, <span class="php-var">$t</span>);
</span><span id="671" class="l"><a href="#671">671: </a><span class="php-var">$X</span> = <span class="php-num">1</span>;
</span><span id="672" class="l"><a href="#672">672: </a><span class="php-keyword1">while</span>(<span class="php-var">$X</span>){
</span><span id="673" class="l"><a href="#673">673: </a> <span class="php-var">$n</span> = <span class="php-var">$N</span>;
</span><span id="674" class="l"><a href="#674">674: </a> <span class="php-var">$t</span> = <span class="php-var">$T</span>;
</span><span id="675" class="l"><a href="#675">675: </a> <span class="php-keyword2">ob_start</span>();
</span><span id="676" class="l"><a href="#676">676: </a> <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$d</span>[<span class="php-var">$p</span>])){<span class="php-keyword1">echo</span> <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, ++<span class="php-var">$n</span>);}
</span><span id="677" class="l"><a href="#677">677: </a> <span class="php-keyword1">echo</span> <span class="php-keyword2">ltrim</span>(<span class="php-keyword2">array_shift</span>(<span class="php-var">$t</span>));
</span><span id="678" class="l"><a href="#678">678: </a> <span class="php-keyword1">for</span>(<span class="php-var">$i</span>=-<span class="php-num">1</span>, <span class="php-var">$j</span>=<span class="php-keyword2">count</span>(<span class="php-var">$t</span>); ++<span class="php-var">$i</span>&lt;<span class="php-var">$j</span>;){
</span><span id="679" class="l"><a href="#679">679: </a>  <span class="php-var">$r</span> = <span class="php-quote">''</span>; <span class="php-keyword1">list</span>(<span class="php-var">$e</span>, <span class="php-var">$r</span>) = <span class="php-keyword2">explode</span>(<span class="php-quote">'&gt;'</span>, <span class="php-var">$t</span>[<span class="php-var">$i</span>]);
</span><span id="680" class="l"><a href="#680">680: </a>  <span class="php-var">$x</span> = <span class="php-var">$e</span>[<span class="php-num">0</span>] == <span class="php-quote">'/'</span> ? <span class="php-num">0</span> : (<span class="php-keyword2">substr</span>(<span class="php-var">$e</span>, -<span class="php-num">1</span>) == <span class="php-quote">'/'</span> ? <span class="php-num">1</span> : (<span class="php-var">$e</span>[<span class="php-num">0</span>] != <span class="php-quote">'!'</span> ? <span class="php-num">2</span> : -<span class="php-num">1</span>));
</span><span id="681" class="l"><a href="#681">681: </a>  <span class="php-var">$y</span> = !<span class="php-var">$x</span> ? <span class="php-keyword2">ltrim</span>(<span class="php-var">$e</span>, <span class="php-quote">'/'</span>) : (<span class="php-var">$x</span> &gt; <span class="php-num">0</span> ? <span class="php-keyword2">substr</span>(<span class="php-var">$e</span>, <span class="php-num">0</span>, <span class="php-keyword2">strcspn</span>(<span class="php-var">$e</span>, <span class="php-quote">' '</span>)) : <span class="php-num">0</span>);
</span><span id="682" class="l"><a href="#682">682: </a>  <span class="php-var">$e</span> = <span class="php-quote">&quot;&lt;</span><span class="php-var">$e</span><span class="php-quote">&gt;&quot;</span>; 
</span><span id="683" class="l"><a href="#683">683: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$d</span>[<span class="php-var">$y</span>])){
</span><span id="684" class="l"><a href="#684">684: </a>   <span class="php-keyword1">if</span>(!<span class="php-var">$x</span>){
</span><span id="685" class="l"><a href="#685">685: </a>    <span class="php-keyword1">if</span>(<span class="php-var">$n</span>){<span class="php-keyword1">echo</span> <span class="php-quote">&quot;\n&quot;</span>, <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, --<span class="php-var">$n</span>), <span class="php-quote">&quot;</span><span class="php-var">$e</span><span class="php-quote">\n&quot;</span>, <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, <span class="php-var">$n</span>);}
</span><span id="686" class="l"><a href="#686">686: </a>    <span class="php-keyword1">else</span>{++<span class="php-var">$N</span>; <span class="php-keyword2">ob_end_clean</span>(); <span class="php-keyword1">continue</span> <span class="php-num">2</span>;}
</span><span id="687" class="l"><a href="#687">687: </a>   }
</span><span id="688" class="l"><a href="#688">688: </a>   <span class="php-keyword1">else</span>{<span class="php-keyword1">echo</span> <span class="php-quote">&quot;\n&quot;</span>, <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, <span class="php-var">$n</span>), <span class="php-quote">&quot;</span><span class="php-var">$e</span><span class="php-quote">\n&quot;</span>, <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, (<span class="php-var">$x</span> != <span class="php-num">1</span> ? ++<span class="php-var">$n</span> : <span class="php-var">$n</span>));}
</span><span id="689" class="l"><a href="#689">689: </a>   <span class="php-keyword1">echo</span> <span class="php-var">$r</span>; <span class="php-keyword1">continue</span>;
</span><span id="690" class="l"><a href="#690">690: </a>  }
</span><span id="691" class="l"><a href="#691">691: </a>  <span class="php-var">$f</span> = <span class="php-quote">&quot;\n&quot;</span>. <span class="php-keyword2">str_repeat</span>(<span class="php-var">$s</span>, <span class="php-var">$n</span>);
</span><span id="692" class="l"><a href="#692">692: </a>  <span class="php-keyword1">if</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$c</span>[<span class="php-var">$y</span>])){
</span><span id="693" class="l"><a href="#693">693: </a>   <span class="php-keyword1">if</span>(!<span class="php-var">$x</span>){<span class="php-keyword1">echo</span> <span class="php-var">$e</span>, <span class="php-var">$f</span>, <span class="php-var">$r</span>;}
</span><span id="694" class="l"><a href="#694">694: </a>   <span class="php-keyword1">else</span>{<span class="php-keyword1">echo</span> <span class="php-var">$f</span>, <span class="php-var">$e</span>, <span class="php-var">$r</span>;}
</span><span id="695" class="l"><a href="#695">695: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$b</span>[<span class="php-var">$y</span>])){<span class="php-keyword1">echo</span> <span class="php-var">$f</span>, <span class="php-var">$e</span>, <span class="php-var">$r</span>;
</span><span id="696" class="l"><a href="#696">696: </a>  }<span class="php-keyword1">elseif</span>(<span class="php-keyword1">isset</span>(<span class="php-var">$a</span>[<span class="php-var">$y</span>])){<span class="php-keyword1">echo</span> <span class="php-var">$e</span>, <span class="php-var">$f</span>, <span class="php-var">$r</span>;
</span><span id="697" class="l"><a href="#697">697: </a>  }<span class="php-keyword1">elseif</span>(!<span class="php-var">$y</span>){<span class="php-keyword1">echo</span> <span class="php-var">$f</span>, <span class="php-var">$e</span>, <span class="php-var">$f</span>, <span class="php-var">$r</span>;
</span><span id="698" class="l"><a href="#698">698: </a>  }<span class="php-keyword1">else</span>{<span class="php-keyword1">echo</span> <span class="php-var">$e</span>, <span class="php-var">$r</span>;}
</span><span id="699" class="l"><a href="#699">699: </a> }
</span><span id="700" class="l"><a href="#700">700: </a> <span class="php-var">$X</span> = <span class="php-num">0</span>;
</span><span id="701" class="l"><a href="#701">701: </a>}
</span><span id="702" class="l"><a href="#702">702: </a><span class="php-var">$t</span> = <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\n &quot;</span>, <span class="php-quote">&quot; \n&quot;</span>), <span class="php-quote">&quot;\n&quot;</span>, <span class="php-keyword2">preg_replace</span>(<span class="php-quote">'`[\n]\s*?[\n]+`'</span>, <span class="php-quote">&quot;\n&quot;</span>, <span class="php-keyword2">ob_get_contents</span>()));
</span><span id="703" class="l"><a href="#703">703: </a><span class="php-keyword2">ob_end_clean</span>();
</span><span id="704" class="l"><a href="#704">704: </a><span class="php-keyword1">if</span>((<span class="php-var">$l</span> = <span class="php-keyword2">strpos</span>(<span class="php-quote">&quot; </span><span class="php-var">$w</span><span class="php-quote">&quot;</span>, <span class="php-quote">'r'</span>) ? (<span class="php-keyword2">strpos</span>(<span class="php-quote">&quot; </span><span class="php-var">$w</span><span class="php-quote">&quot;</span>, <span class="php-quote">'n'</span>) ? <span class="php-quote">&quot;\r\n&quot;</span> : <span class="php-quote">&quot;\r&quot;</span>) : <span class="php-num">0</span>)){
</span><span id="705" class="l"><a href="#705">705: </a> <span class="php-var">$t</span> = <span class="php-keyword2">str_replace</span>(<span class="php-quote">&quot;\n&quot;</span>, <span class="php-var">$l</span>, <span class="php-var">$t</span>);
</span><span id="706" class="l"><a href="#706">706: </a>}
</span><span id="707" class="l"><a href="#707">707: </a><span class="php-keyword1">return</span> <span class="php-keyword2">str_replace</span>(<span class="php-keyword1">array</span>(<span class="php-quote">&quot;\x01&quot;</span>, <span class="php-quote">&quot;\x02&quot;</span>, <span class="php-quote">&quot;\x03&quot;</span>, <span class="php-quote">&quot;\x04&quot;</span>, <span class="php-quote">&quot;\x05&quot;</span>, <span class="php-quote">&quot;\x07&quot;</span>), <span class="php-keyword1">array</span>(<span class="php-quote">'&lt;'</span>, <span class="php-quote">'&gt;'</span>, <span class="php-quote">&quot;\n&quot;</span>, <span class="php-quote">&quot;\r&quot;</span>, <span class="php-quote">&quot;\t&quot;</span>, <span class="php-quote">' '</span>), <span class="php-var">$t</span>);
</span><span id="708" class="l"><a href="#708">708: </a><span class="php-comment">// eof</span>
</span><span id="709" class="l"><a href="#709">709: </a>}
</span><span id="710" class="l"><a href="#710">710: </a>
</span><span id="711" class="l"><a href="#711">711: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> hl_version(){
</span><span id="712" class="l"><a href="#712">712: </a><span class="php-comment">// rel</span>
</span><span id="713" class="l"><a href="#713">713: </a><span class="php-keyword1">return</span> <span class="php-quote">'1.1.22'</span>;
</span><span id="714" class="l"><a href="#714">714: </a><span class="php-comment">// eof</span>
</span><span id="715" class="l"><a href="#715">715: </a>}
</span><span id="716" class="l"><a href="#716">716: </a>
</span><span id="717" class="l"><a href="#717">717: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> kses(<span class="php-var">$t</span>, <span class="php-var">$h</span>, <span class="php-var">$p</span>=<span class="php-keyword1">array</span>(<span class="php-quote">'http'</span>, <span class="php-quote">'https'</span>, <span class="php-quote">'ftp'</span>, <span class="php-quote">'news'</span>, <span class="php-quote">'nntp'</span>, <span class="php-quote">'telnet'</span>, <span class="php-quote">'gopher'</span>, <span class="php-quote">'mailto'</span>)){
</span><span id="718" class="l"><a href="#718">718: </a><span class="php-comment">// kses compat</span>
</span><span id="719" class="l"><a href="#719">719: </a><span class="php-keyword1">foreach</span>(<span class="php-var">$h</span> <span class="php-keyword1">as</span> <span class="php-var">$k</span>=&gt;<span class="php-var">$v</span>){
</span><span id="720" class="l"><a href="#720">720: </a> <span class="php-var">$h</span>[<span class="php-var">$k</span>][<span class="php-quote">'n'</span>][<span class="php-quote">'*'</span>] = <span class="php-num">1</span>;
</span><span id="721" class="l"><a href="#721">721: </a>}
</span><span id="722" class="l"><a href="#722">722: </a><span class="php-var">$C</span>[<span class="php-quote">'cdata'</span>] = <span class="php-var">$C</span>[<span class="php-quote">'comment'</span>] = <span class="php-var">$C</span>[<span class="php-quote">'make_tag_strict'</span>] = <span class="php-var">$C</span>[<span class="php-quote">'no_deprecated_attr'</span>] = <span class="php-var">$C</span>[<span class="php-quote">'unique_ids'</span>] = <span class="php-num">0</span>;
</span><span id="723" class="l"><a href="#723">723: </a><span class="php-var">$C</span>[<span class="php-quote">'keep_bad'</span>] = <span class="php-num">1</span>;
</span><span id="724" class="l"><a href="#724">724: </a><span class="php-var">$C</span>[<span class="php-quote">'elements'</span>] = <span class="php-keyword2">count</span>(<span class="php-var">$h</span>) ? <span class="php-keyword2">strtolower</span>(<span class="php-keyword2">implode</span>(<span class="php-quote">','</span>, <span class="php-keyword2">array_keys</span>(<span class="php-var">$h</span>))) : <span class="php-quote">'-*'</span>;
</span><span id="725" class="l"><a href="#725">725: </a><span class="php-var">$C</span>[<span class="php-quote">'hook'</span>] = <span class="php-quote">'\DataTables\Vendor\htmLawed::kses_hook'</span>;
</span><span id="726" class="l"><a href="#726">726: </a><span class="php-var">$C</span>[<span class="php-quote">'schemes'</span>] = <span class="php-quote">'*:'</span>. <span class="php-keyword2">implode</span>(<span class="php-quote">','</span>, <span class="php-var">$p</span>);
</span><span id="727" class="l"><a href="#727">727: </a><span class="php-keyword1">return</span> \DataTables\Vendor\htmLawed::hl(<span class="php-var">$t</span>, <span class="php-var">$C</span>, <span class="php-var">$h</span>);
</span><span id="728" class="l"><a href="#728">728: </a><span class="php-comment">// eof</span>
</span><span id="729" class="l"><a href="#729">729: </a>}
</span><span id="730" class="l"><a href="#730">730: </a>
</span><span id="731" class="l"><a href="#731">731: </a><span class="php-keyword1">public</span> <span class="php-keyword1">static</span> <span class="php-keyword1">function</span> kses_hook(<span class="php-var">$t</span>, &amp;<span class="php-var">$C</span>, &amp;<span class="php-var">$S</span>){
</span><span id="732" class="l"><a href="#732">732: </a><span class="php-comment">// kses compat</span>
</span><span id="733" class="l"><a href="#733">733: </a><span class="php-keyword1">return</span> <span class="php-var">$t</span>;
</span><span id="734" class="l"><a href="#734">734: </a><span class="php-comment">// eof</span>
</span><span id="735" class="l"><a href="#735">735: </a>}
</span><span id="736" class="l"><a href="#736">736: </a><span class="php-comment">// end class</span>
</span><span id="737" class="l"><a href="#737">737: </a>}
</span><span id="738" class="l"><a href="#738">738: </a></span></code></pre>

	<div id="footer">
		DataTables Editor 1.8.1 - PHP libraries API documentation generated by <a href="http://apigen.org">ApiGen</a>
	</div>
</div>
</div>
<script src="resources/combined.js?ec3c33de34884f5522356ff861223130712ab94c"></script>
<script src="elementlist.js?28355e24969ff60be733fa50a93fc3b17a0c168f"></script>
</body>
</html>
