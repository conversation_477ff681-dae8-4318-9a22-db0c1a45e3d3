<?php

require_once "/home/<USER>/secrets.php";

define('DOMAIN', 'https://crm.secunia.com/');

// Databases
// see secrets.php

// Trial mail sender script path
define('TRIAL_MAIL_PATH', '/home/<USER>/crm_csi_trial_sender/');

// path where CRM can upload terms and conditions files
define('TERMS_PATH', '/home/<USER>/terms_and_conditions_files');

define('CSC_DEPARTMENT', 12);

/*
 * @todo: the root is one directory back. We will try to only use the sales
 * folder on the live side and see if it does the job. If yes, we can remove
 * all the other directories that contain unused code.
 */
// Paths
define('ROOT_PATH', dirname( __FILE__ ) . '/../' );
define('REL_PUBLIC_PATH', 'sales/');
define('INCLUDE_PATH', ROOT_PATH . REL_PUBLIC_PATH );
define('JS_PATH_LOCAL', INCLUDE_PATH);
define('CACHE_DIR', INCLUDE_PATH . 'cache/');

// Urls
define('PUBLIC_URL', DOMAIN . REL_PUBLIC_PATH );
define('JS_PATH_PUBLIC', PUBLIC_URL );

// Time settings
date_default_timezone_set('UTC');

include 'mysql.php';

// Constants
class Product {
	// Product Names
	const CSI6 = 17;
	const CSI7 = 22;
	const VIM4 = 210;

	// Related data
	public static function getName( $type ) {
		switch( $type ) {
		case self::CSI6:
			return 'CSI 6.x';
			break;
		case self::CSI7:
			return 'CSI 7.x';
			break;
		case self::VIM4:
			return 'VIM 4.x';
			break;
		}
		return '-';
	}
}

/*
// Use Products class instead
// We need to keep the following as a reference to product types since the ids
// are hardcoded in a lot of places
// Product Types
$aProductTypes = array();
$aProductTypes[2]  = array('container' => 'VI', 'name' => 'Vulnerability Manager', 'short_name' => 'VM', 'available' => true, 'segment' => 'SMB');
$aProductTypes[3]  = array('container' => 'VI', 'name' => 'Enterprise Vulnerability Manager', 'short_name' => 'EVM', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[11] = array('container' => 'VI', 'name' => 'Enterprise Vulnerability Manager - Server Edition', 'short_name' => 'EVM-S', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[1]  = array('container' => 'VI', 'name' => 'Vulnerability Tracking Service', 'short_name' => 'VTS', 'available' => true, 'segment' => 'SMB');
$aProductTypes[4]  = array('container' => 'VI', 'name' => 'Vulnerability Intelligence Feed', 'short_name' => 'VIF', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[12] = array('container' => 'SVP', 'name' => 'Special Security Vendor Product', 'short_name' => 'SSVP', 'available' => false, 'segment' => 'SA');
$aProductTypes[127] = array('container' => 'SVP', 'name' => 'Special Security Vendor Product', 'short_name' => 'SSVP', 'available' => false, 'segment' => 'SA');
$aProductTypes[13] = array('container' => 'SVP', 'name' => 'OEM/ODM fixed fee', 'short_name' => 'OOFF', 'available' => false, 'segment' => 'SA');
$aProductTypes[14] = array('container' => 'SVP', 'name' => 'OEM/ODM Revenue sharing Fee', 'short_name' => 'OORSF', 'available' => false, 'segment' => 'SA');
$aProductTypes[15] = array('container' => 'SVP', 'name' => 'Vulnerability Feed', 'short_name' => 'VF', 'available' => false, 'segment' => 'SA');
$aProductTypes[16] = array('container' => 'SVP', 'name' => 'Binary Analysis', 'short_name' => 'BA', 'available' => true, 'segment' => 'SA');
$aProductTypes[5]  = array('container' => 'SS', 'name' => 'Surveillance Scanner', 'short_name' => 'SS', 'available' => true, 'segment' => 'SMB');
$aProductTypes[6]  = array('container' => 'NSI', 'name' => 'Network Software Detector', 'short_name' => 'NSD', 'available' => false, 'segment' => 'SMB');
$aProductTypes[9]  = array('container' => 'NSI', 'name' => 'Network Software Inspector', 'short_name' => 'NSI', 'available' => false, 'segment' => 'SMB');
$aProductTypes[10] = array('container' => 'NSI', 'name' => 'Network Software Inspector - Server Edition', 'short_name' => 'NSI-S', 'available' => false, 'segment' => 'Enterprise');
$aProductTypes[17] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector', 'short_name' => 'CSI', 'available' => true, 'segment' => 'SMB');
$aProductTypes[18] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector - Professional', 'short_name' => 'CSI-P', 'available' => true, 'segment' => 'SMB');
$aProductTypes[19] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector - Enterprise', 'short_name' => 'CSI-E', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[20] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector - Server Edition', 'short_name' => 'CSI-S', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[21] = array('container' => 'SVP', 'name' => 'Special Sale', 'short_name' => 'SPEC', 'available' => true, 'segment' => 'SA');
$aProductTypes[200] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector 4.0 - Professional', 'short_name' => 'CSI4-P', 'available' => true, 'segment' => 'SMB');
$aProductTypes[201] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector 4.0 - Enterprise', 'short_name' => 'CSI4-E', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[202] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector 4.0', 'short_name' => 'CSI4', 'available' => true, 'segment' => 'SMB');
$aProductTypes[203] = array('container' => 'NSI', 'name' => 'Server License', 'short_name' => 'SL', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[204] = array('container' => 'NSI', 'name' => 'Corporate Software Inspector 4.0 - Small Business', 'short_name' => 'CSI4-SB', 'available' => true, 'segment' => 'SMB');
$aProductTypes[205] = array('container' => 'NSI', 'name' => 'Standard Support', 'short_name' => 'Standard-Support', 'available' => true, 'segment' => 'SMB');
$aProductTypes[206] = array('container' => 'NSI', 'name' => 'Premium Support', 'short_name' => 'Premium-Support', 'available' => true, 'segment' => 'SMB');
$aProductTypes[207] = array('container' => 'NSI', 'name' => 'Enterprise Support', 'short_name' => 'Enterprise-Support', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[208]  = array('container' => 'VI', 'name' => 'Vulnerability Intelligence Manager - Small Business', 'short_name' => 'VIM-SB', 'available' => true, 'segment' => 'SMB');
$aProductTypes[209]  = array('container' => 'VI', 'name' => 'Vulnerability Intelligence Manager', 'short_name' => 'VIM', 'available' => true, 'segment' => 'Enterprise');
$aProductTypes[210]  = array('container' => 'VI', 'name' => 'Vulnerability Intelligence Manager 4.x', 'short_name' => 'VIM4', 'available' => true, 'segment' => 'Enterprise');
*/

// CA product modules
###############################################################################
# This bitmask is 8 bytes long (ca.accounts.show_modules).                    #
# Bits 15 thru 45 are reserved for the ca.modules table so only use 0x0000    #
# thru 0x2000 and 0x200000000000 thru 0xCCCCCCCCCCCCCCC.                      #
###############################################################################
define('MOD_NONE',    0x0000);
define('MOD_SM',      0x0001);
define('MOD_ESM',     0x0002);
define('MOD_VTS',     0x0004);
define('MOD_VTSE',    0x0008);
define('MOD_VSS',     0x0010);
define('MOD_NSI',     0x0020);
define('MOD_BA',      0x0040);
define('MOD_VDB',     0x0080);
define('MOD_VWA',     0x0100);
define('MOD_SUPPORT', 0x0200);
define('MOD_ACCOUNT', 0x0400);
define('MOD_UM',      0x0800);
//define('NEXT',    0x4000);
###################################################################
# 0x4000 thru 0x100000000000 are reserved for ca.modules.         #
# This is the mask for only ca.modules bits: 0x1FFFFFFFC000       #
###################################################################

define('OPT_NONE', 0x0000);
define('OPT_ADVISORY_DEEP_LINKS', 0x0001);
define('OPT_ADVISORY_POC_DATA', 0x0002);
define('OPT_ADVISORY_EXTENDED_DATA', 0x0004);
define('OPT_ADVISORY_EXTENDED_DESCRIPTION', 0x0008);
define('OPT_ASSET_RECEIVE_ALL', 0x0010);
define('OPT_XML_V1', 0x0020);
define('OPT_INCLUDE_TICKET_ID', 0x0080);
define('OPT_REJECTED', 0x0100);

// People in CRC Team
//$aCRC = array(1,95,106,130);
$aCRC = array(1,82,95,125,155);

//Hidden customers
$aHiddenCST = array(902249, 596936, 902261, 596916, 587991, 644616, 651904, 903568, 861134, 900726);
$aHiddenAllow = array(3, 4, 5, 74, 188); //HZ, TK, NHR, AO, BBIRKVALD

// People who are allowed to issue CSI 4.0 Accounts
$aCSI40 = array(1, 74, 112, 138, 135, 136, 132, 221);

// CA Module availability restrictions
$aModuleRestrictions = array
(
	MOD_BA => array(1,3,5,6,27,47,74,99,103,102,120,131,133,166,167,189,190,194,232,234,277),
	MOD_VDB => array(1,3,5,6,27,47,74,76,95,99,103,102,109,110,112,120,130,131,132,133,136,166,167,189,190,194,221,232,234,277,159,186,221,306,273,226,230,344,459,446,470,492,493)
);
$aModuleRestrictionsEnable = array
(
	MOD_NSI => array(1,2,3,6,27,37,43,47,71,74,72,75,42,46,65,73,76,78,85,86,87,88,90,91,95,99,100,101,102,103,106,107,108,109,110,111,112,113,114,115,117,118,119,120,121,126,128,130,131,133,134,135,136,138,139,144,145,146,150,151,154,155,159,160,161,162,165,166,167,169,170,171,172,173,174,179,181,182,186,187,188,193,194,195,196,197,198,199,200,201,202,203,204,205,206,212,218,219,220,225,226,228,229,230,232,234,235,184,194,236,237,238,239,240,241,242,243,247,248,249,250,251,252,253,254,255,256,259,260,261,262,263,266,267,268,270,272,273,277,278,279,296,298,299,300,301,302,303,304,305,306,308,309,310,311,320,321,322,323,326,327,328,221,316,331,332,333,334,335,336,337,344,345,350,353,355,356,364,367,286,369,370,372,373,377,378,380,381,386,387,388,389,391,393,394,400,401,405,407,413,414,423,424,425,428,430,431,432,433,434,435,437,440,441,442,459,473,446,470,474,475,476,477,481,482,483,484,486,490,491,492,493,494,495,488,496,497,500,501,502,503,504,505),
	MOD_BA => array(1,3,5,6,27,47,74,99,103,102,120,131,133,166,167,189,190,194,232,234,277),
	MOD_VDB => array(1,3,5,6,27,47,74,76,95,99,103,102,109,110,112,120,130,131,132,133,136,166,167,189,190,194,221,232,234,277,159,186,221,226,273,268,187,6,27,272,235,229,226,306,273,230,344,286,369,373,328,203,459,446,470,492,493),
	'Partner' => array(1,74,118)
);

// Default Product to module mapping
$aDefaultModuleMappings = array(
	1    => array(
		'modules' => MOD_VTS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_SM | MOD_VTS | MOD_VSS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	2     => array(
		'modules' => MOD_SM | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_ESM | MOD_SM | MOD_VSS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	3    => array(
		'modules' => MOD_ESM | MOD_SM | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_ESM | MOD_SM | MOD_VSS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	4   => array(
		'modules' => MOD_VTSE | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_VTSE | MOD_UM | MOD_VSS | MOD_VDB | MOD_VWA | MOD_SUPPORT | MOD_ACCOUNT),
	5    => array(
		'modules' => MOD_VSS | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_SM | MOD_VSS | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT),
	8     => array(
		'modules' => MOD_BA | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_VTSE | MOD_BA | MOD_VDB | MOD_SUPPORT | MOD_ACCOUNT),
	17    => array(
		'modules' => MOD_NSI | MOD_SUPPORT | MOD_ACCOUNT,
		'show_modules' => MOD_NSI | MOD_VSS | MOD_SUPPORT | MOD_ACCOUNT),
	209    => array(
		'modules' => MOD_UM | MOD_ESM | MOD_VTS | MOD_VDB,
		'show_modules' => MOD_UM | MOD_ESM | MOD_VTS | MOD_VDB),
	208    => array(
		'modules' => MOD_VTS | MOD_VDB,
		'show_modules' => MOD_VTS | MOD_VDB),
	Product::VIM4    => array(
		'modules' => MOD_VTS | MOD_VDB | MOD_UM | MOD_ESM | MOD_VTS | MOD_VDB,
		'show_modules' => MOD_VTS | MOD_VDB | MOD_UM | MOD_ESM | MOD_VTS | MOD_VDB)
);

$aDefaultOptionMapping = array(
	209 => OPT_ADVISORY_DEEP_LINKS | OPT_ADVISORY_POC_DATA | OPT_ADVISORY_EXTENDED_DATA | OPT_ADVISORY_EXTENDED_DESCRIPTION | OPT_ASSET_RECEIVE_ALL
	,208 => OPT_NONE
	,Product::VIM4 => OPT_ADVISORY_DEEP_LINKS | OPT_ADVISORY_POC_DATA | OPT_ADVISORY_EXTENDED_DATA | OPT_ADVISORY_EXTENDED_DESCRIPTION
);

// Country Regions
$aCountryRegions['NORDIC'] = array(1,2,4,16,19);
$aCountryRegions['DACH'] = array(3,6,7);
$aCountryRegions['BENELUX'] = array(17,23,21);
$aCountryRegions['USA'] = array(10);
$aCountryRegions['Other'] = array_merge($aCountryRegions['NORDIC'], $aCountryRegions['DACH'], $aCountryRegions['BENELUX'], $aCountryRegions['USA']);

// Special rule for reps with LM rights: Who is the rep allowed to move leads for
$aLMAllowedMove[95] = array(82, 95, 110, 125, 130);

// Title Options
// Next: 34
$aTitleOptions[1] = 'Chief Executive Officer';
$aTitleOptions[2] = 'Chief Information Officer';
$aTitleOptions[3] = 'Chief Security Officer';
$aTitleOptions[4] = 'Chief Technology Officer';
$aTitleOptions[5] = 'Chief Information Security Officer';
$aTitleOptions[6] = 'Director';
$aTitleOptions[7] = 'Vice President';
$aTitleOptions[8] = 'IT Director';
$aTitleOptions[9] = 'IT Manager';
$aTitleOptions[10] = 'Senior IT Consultant';
$aTitleOptions[11] = 'IT Consultant';
$aTitleOptions[12] = 'IT Security Consultant';
$aTitleOptions[13] = 'IT Security Officer';
$aTitleOptions[14] = 'IT Security Manager';
$aTitleOptions[15] = 'IT Security Administrator';
$aTitleOptions[27] = 'IT Auditor';
$aTitleOptions[16] = 'Manager';
$aTitleOptions[17] = 'Network Security Manager';
$aTitleOptions[18] = 'Network Systems Administrator';
$aTitleOptions[19] = 'Network Systems Engineer';
$aTitleOptions[20] = 'Project Manager';
$aTitleOptions[21] = 'Project Director';
$aTitleOptions[22] = 'Systems Engineer';
$aTitleOptions[23] = 'Compliance Officer';
$aTitleOptions[24] = 'Auditor';
$aTitleOptions[25] = 'Finance Manager';
$aTitleOptions[26] = 'Other';
$aTitleOptions[27] = 'IT Audit Manager';
$aTitleOptions[28] = 'IT System Administrator';
$aTitleOptions[29] = 'IT Security Analyst';
$aTitleOptions[30] = 'Information Security Analyst';
$aTitleOptions[31] = 'Analyst';
$aTitleOptions[32] = 'Security Manager';
$aTitleOptions[33] = 'President';

// Timezones
$aTimezones[] = '-9';
$aTimezones[] = '-8';
$aTimezones[] = '-7';
$aTimezones[] = '-6';
$aTimezones[] = '-5';
$aTimezones[] = '-4';
$aTimezones[] = '-3';
$aTimezones[] = '-2';
$aTimezones[] = '-1';
$aTimezones[] = '0';
$aTimezones[] = '+1';
$aTimezones[] = '+2';
$aTimezones[] = '+3';
$aTimezones[] = '+4';
$aTimezones[] = '+5';
$aTimezones[] = '+6';
$aTimezones[] = '+7';
$aTimezones[] = '+8';
$aTimezones[] = '+9';
$aTimezones[] = '+10';

// Industries
// Next: 61
$aIndustries[1] = 'Aerospace & Defense';
$aIndustries[2] = 'Agriculture';
$aIndustries[3] = 'Automotive & Transport';
$aIndustries[4] = 'Banking';
$aIndustries[5] = 'Beverages';
$aIndustries[6] = 'Business Services';
$aIndustries[7] = 'Charitable Organizations';
$aIndustries[8] = 'Chemicals';
$aIndustries[9] = 'Computer Hardware';
$aIndustries[10] = 'Computer Services';
$aIndustries[11] = 'Computer Software';
$aIndustries[12] = 'Construction';
$aIndustries[13] = 'Consumer Products Manufacturers';
$aIndustries[14] = 'Consumer Services';
$aIndustries[15] = 'Cultural Institutions';
$aIndustries[16] = 'Education';
$aIndustries[17] = 'Electronics';
$aIndustries[18] = 'Energy & Utilities';
$aIndustries[19] = 'Environmental Services & Equipment';
$aIndustries[20] = 'Financial Services';
$aIndustries[21] = 'Food';
$aIndustries[22] = 'Foundations';
$aIndustries[23] = 'Government';
$aIndustries[24] = 'Health Care';
$aIndustries[25] = 'Industrial Manufacturing';
$aIndustries[26] = 'Insurance';
$aIndustries[27] = 'Leisure';
$aIndustries[39] = 'IT Security';
$aIndustries[40] = 'IT Services Provider';
$aIndustries[28] = 'Media';
$aIndustries[29] = 'Membership Organizations';
$aIndustries[30] = 'Metals & Mining';
$aIndustries[38] = 'Military';
$aIndustries[31] = 'Pharmaceuticals';
$aIndustries[32] = 'Real Estate';
$aIndustries[33] = 'Retail';
$aIndustries[34] = 'Security Products & Services';
$aIndustries[35] = 'Telecommunications Equipment';
$aIndustries[36] = 'Telecommunications Services';
$aIndustries[37] = 'Transportation Services';
$aIndustries[41] = 'Other';
$aIndustries[42] = 'Legal';
$aIndustries[43] = 'Road, Air, Sea transportation';
$aIndustries[44] = 'Telecommunication';
$aIndustries[45] = 'Electronical & Electrical';
$aIndustries[46] = 'Banking & Finance';
$aIndustries[47] = 'Healthcare, Hospital';
$aIndustries[48] = 'Wholesale Durables and Non-Durables';
$aIndustries[49] = 'Construction & Contractors';
$aIndustries[50] = 'IT';
$aIndustries[51] = 'Mineral';
$aIndustries[52] = 'Chemical & Pharmaceutical';
$aIndustries[53] = 'Mechanical';
$aIndustries[54] = 'Services';
$aIndustries[55] = 'Graphical & Publishing';
$aIndustries[56] = 'Utilities';
$aIndustries[57] = 'Miscellaneous';
$aIndustries[58] = 'Food & Beverage';
$aIndustries[59] = 'Wood, Paper, Furniture';
$aIndustries[60] = 'Textile';

// Lead Sources
// Next: 48
$aLeadSources[1] = 'Inbound';
$aLeadSources[2] = 'Import-Hoovers';
$aLeadSources[3] = 'Import-LinkedIn';
$aLeadSources[4] = 'Other';
$aLeadSources[5] = 'Inbound-CSI 3.0 Public Beta';
$aLeadSources[7] = 'Inbound-CSI 4.0 Public Beta';
$aLeadSources[9] = 'Inbound-CSI 4.0 Website Trial';
$aLeadSources[6] = 'Import-Rhetorik';
$aLeadSources[8] = 'Import-RSA 2010';
$aLeadSources[10] = 'Import-InfoSec 2010';
$aLeadSources[11] = 'Import-MMS 2010';
$aLeadSources[12] = 'Import-Jigsaw';
$aLeadSources[13] = 'Import-e.Crime 2010';
$aLeadSources[14] = 'Import-Infosec NL 2010';
$aLeadSources[15] = 'Import-SC Conf 2010';
// Added 15-12-2010
$aLeadSources[16] = 'Inbound-SANS';
$aLeadSources[17] = 'Inbound-Helpnet';
$aLeadSources[18] = 'Inbound-Security_NL';
$aLeadSources[19] = 'Inbound-Factsheets';
$aLeadSources[20] = 'Inbound-Google';
$aLeadSources[21] = 'Inbound-Yahoo';
$aLeadSources[22] = 'Inbound-Bing';
$aLeadSources[23] = 'Inbound-Presentation';
$aLeadSources[24] = 'Inbound-Webinar';
$aLeadSources[25] = 'Inbound-Colleague';
$aLeadSources[26] = 'Import-Sales';
$aLeadSources[27] = 'Import-RSA SF 2011';
$aLeadSources[28] = 'Import-CISO ME 2011';
$aLeadSources[29] = 'Import-Infosec 2011';
$aLeadSources[30] = 'Import-e-Crime 2011';
$aLeadSources[31] = 'Import-MMS 2011';
$aLeadSources[32] = 'Import-SC Conf 2011';
$aLeadSources[33] = 'Import-Infosec NL 2011';
$aLeadSources[34] = 'Import-EDUCAUSE 2011';
$aLeadSources[35] = 'Import-ICT Expo Finland 2011';
$aLeadSources[36] = 'Inbound-RSA London 2011';
$aLeadSources[37] = 'Inbound-CISO ME 2011';
$aLeadSources[38] = 'Inbound-Infosec 2011';
$aLeadSources[39] = 'Inbound-e-Crime 2011';
$aLeadSources[40] = 'Inbound-MMS 2011';
$aLeadSources[41] = 'Inbound-SC Conf 2011';
$aLeadSources[42] = 'Inbound-Infosec NL 2011';
$aLeadSources[43] = 'Inbound-EDUCAUSE 2011';
$aLeadSources[44] = 'Inbound-ICT Expo Finland 2011';
$aLeadSources[45] = 'Inbound-Secunia';
$aLeadSources[46] = 'Inbound-Heise';
$aLeadSources[47] = 'Inbound-Aberdeen';
$aLeadSources[48] = 'Inbound-OVUM 2010';
$aLeadSources[49] = 'Inbound-Yearly Report 2010';
$aLeadSources[50] = 'Inbound-Sales';
$aLeadSources[51] = 'Import - Hoppenstedt';
$aLeadSources[52] = 'Import - Experian DK';
$aLeadSources[53] = 'Import - Experian N';
$aLeadSources[54] = 'Import - Experian S';
$aLeadSources[55] = 'Import - Experian F';
$aLeadSources[56] = 'Import - GlobalETM';
$aLeadSources[57] = 'Inbound-CSI 5.0 Public Beta';

asort($aLeadSources); // Sort the array, preserve indexes
