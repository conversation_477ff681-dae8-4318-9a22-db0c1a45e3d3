<?php

class csi7MspLicense {
	/**
	 * 60 days was decided by Silke - Chargeable should be 60 days please – since the first 45 days should be free of
	 * charge in any case. If we use 60 we can be certain that we are not charging someone we shouldn’t be.
	 *
	 * @var int
     */
	private $daysChargeable = 60;

	public function snapshotMspLicenseUsage() {
		$sql = "SELECT UTC_DATE(), ca.accounts.cst_id AS cst_id, ca.accounts.partition_id, ca.accounts.created, '-' AS salesforce_id, account_name, account_username, ca.partition_license_pool.num_granted, ca.partition_license_pool.num_available
			FROM ca.accounts
			JOIN ca.partition_license_pool ON (
				ca.accounts.cst_id = ca.partition_license_pool.cst_id
				AND ca.accounts.partition_id = ca.partition_license_pool.partition_id
			)
			JOIN crm.cst ON ( ca.accounts.cst_id = crm.cst.cst_id )
			JOIN ca.customers ON ( ca.customers.id = ca.accounts.cst_id )
			WHERE ca.customers.msp = 1
			AND ca.partition_license_pool.license_type = 1
			AND ca.accounts.is_partition_admin = 1
			AND special_limits = 'csi_70'
			AND account_expires >= UTC_TIMESTAMP()
			ORDER BY ca.accounts.cst_id, ca.accounts.partition_id";

		$licenseUsages = DBQueryGetRows( $sql );
		$licenseUsages = $this->fixSalesforceId( $licenseUsages );

		$sql = "INSERT INTO crm.msp_history VALUES ";
		$insertRow = array();
		foreach( $licenseUsages as $val ) {
			$val = array_map( 'mysql_real_escape_string', $val );
			$val = array_map( function( $val ){ return "'" . $val . "'"; }, $val );
			$insertRow[] = '(' . implode( ',', $val ) . ')';
		}
		$sql .= implode( ',', $insertRow );

		return DBQuery( $sql );
	}

	/**
	 *
	 * @param bool $includeGrantedHosts Changes output of license numbers. Licenses that have been assigned to a child
	 * partition are shown as used (scanned) licenses on a parent partition. This option changes the numbers in a way
	 * that granted hosts are deducted from parent account.
	 *
	 * @return array
	 */
	public function getMspLicenseHistory( $includeGrantedHosts = false, $filterDateFrom = false, $filterDateTo = false ) {
		$where = array();

		if ( $filterDateFrom ) {
			$dateFrom  = explode( '.', $filterDateFrom );
			if ( count($dateFrom) == 3 && checkdate($dateFrom[1], $dateFrom[0], $dateFrom[2]) ) {
				$where[] = 'date >= "' . $dateFrom[2] . '-' . $dateFrom[1] . '-' . $dateFrom[0] . '"';
			}
		}

		if ( $filterDateTo ) {
			$dateTo  = explode( '.', $filterDateTo );
			if ( count($dateTo) == 3 && checkdate($dateTo[1], $dateTo[0], $dateTo[2]) ) {
				$where[] = 'date <= "' . $dateTo[2] . '-' . $dateTo[1] . '-' . $dateTo[0] . '"';
			}
		}

		if ( count($where) ) {
			$where = implode(' AND ', $where);
			if ($where) {
				$where = 'WHERE ' . $where;
			}
		} else {
			$where = '';
		}

		$sql = 'SELECT date, cst_id, partition_id, partition_created, salesforce_id, name, username, hosts_granted,
				hosts_granted - hosts_available AS hosts_scanned, hosts_available,
				DATEDIFF(date, partition_created) AS days_old, IF( DATEDIFF(date, partition_created) > ' . $this->daysChargeable . ', "Yes", "No" ) AS chargeable
			FROM crm.msp_history
			' . $where . '
			ORDER BY date DESC, cst_id, partition_id';

		$partitionsLicenses = DBQueryGetRows( $sql );

		if ( $includeGrantedHosts ) {
			$newLicense = array();
			foreach( $partitionsLicenses as $key => $license ) {
				$license['key'] = $key;
				$newLicense[$license['date']][$license['cst_id']][$license['partition_id']] = $license;
				if ( $license['partition_id'] != 0 ) {
					$newLicense[$license['date']][$license['cst_id']][0]['hosts_scanned'] -= $license['hosts_granted'];
				}
			}

			$partitionsLicenses = array();
			foreach( $newLicense as $customer ) {
				foreach( $customer as $partition ) {
					foreach( $partition as $val ) {
						$partitionsLicenses[$val['key']] = $val;
						unset( $partitionsLicenses[$val['key']]['key']);
					}
				}
			}
		}

		return $partitionsLicenses;
	}

	public function sanitizeDateRange($from, $to) {
		$out['from'] = date( 'd.m.Y', strtotime('-' . $this->daysChargeable . ' days') );
		$out['to'] = date('d.m.Y');

		$dateFrom = explode( '.', $from );
		if ( count($dateFrom) === 3 && checkdate($dateFrom[0], $dateFrom[1], $dateFrom[2]) ) {
			$out['from'] = $from;
		}

		$dateTo = explode( '.', $to );
		if ( count($dateTo) === 3 && checkdate($dateTo[0], $dateTo[1], $dateTo[2]) ) {
			$out['to'] = $to;
		}

		return $out;
	}

	/**
	 * @param array $licenseUsages
	 * @return array
	 */
	public function fixSalesforceId( array $licenseUsages ) {
		$ids = array();
		foreach ($licenseUsages as $row) {
			$ids[] = $row['cst_id'];
		}

		$sql = 'SELECT cst_id, master_id FROM crm.cst WHERE cst_id IN (' . implode(',', $ids) . ')';
		$rows = DBQueryGetRows($sql);
		$customerIds = array();
		foreach ($rows as $row) {
			if ($row['master_id']) {
				$customerIds[$row['master_id']] = (int)$row['cst_id'];
			} else {
				$customerIds[$row['cst_id']] = (int)$row['cst_id'];
			}
		}

		$sql = 'SELECT cst_id, salesforce_id FROM crm.cst WHERE cst_id IN (' . implode( ',', array_keys($customerIds) ) . ')';
		$rows = DBQueryGetRows($sql);
		$sfIds = array();
		foreach ($rows as $val) {
			$sfIds[$customerIds[$val['cst_id']]] = $val['salesforce_id'];
		}

		foreach ($licenseUsages as $key => $row) {
			$licenseUsages[$key]['salesforce_id'] = isset($sfIds[$row['cst_id']]) ? $sfIds[$row['cst_id']] : '';
		}

		return $licenseUsages;
	}

	/**
	 * Finds a parent, if it exists, and inserts/updates salesforce ID over there.
	 *
	 * @param int    $cstId
	 * @param string $salesforceId
	 * @return bool  Success
	 */
	public function insertSalesforceId( $cstId, $salesforceId ) {
		if( !ctype_digit( $cstId ) || !ctype_digit( $salesforceId ) ) {
			return false;
		}

		$ids = DBQueryGetRows( 'SELECT cst_id, master_id FROM crm.cst WHERE cst_id = ' . $cstId );
		if ( $ids[0]['master_id'] ) {
			$parentCstId = $ids[0]['master_id'];
		} else {
			$parentCstId = $cstId;
		}

		DBQuery( 'INSERT INTO crm.cst (cst_id, salesforce_id) VALUES (' . (int) $parentCstId . ',"' . $salesforceId . '") ON DUPLICATE KEY UPDATE salesforce_id = "' . $salesforceId . '"');

		return true;
	}
}
