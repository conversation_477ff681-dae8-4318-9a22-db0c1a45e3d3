<?php 
if ( !empty( $_GET['cst_id'] ) ) {
	$cstId = (int) $_GET['cst_id'];
} else {
	exit( 'Halting. Customer ID required.' );
}

require_once( 'configuration.php' );
require_once(INCLUDE_PATH . 'sales_functions.php');
require_once(INCLUDE_PATH . 'global_functions.php');
require_once(INCLUDE_PATH . 'CommonModules.class.php');
require_once(INCLUDE_PATH . 'Crypt.class.php' );
// AES-256 CBC Decryption Key
define('CRYPT_AES_KEY', '579A893CE5CB44F193507985437BC626');

$arrayLices = array(3 => 'PARTITION_LICENSES' ,2 => 'USER_LICENSES' , 1 => 'HOST_LICENSES' ) ;

$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

function searchForId ($id, $array) {
	foreach ($array as $key => $val){
		if ($val['module_id'] == $id) {
			return 1;
		}
	}
	return 0;
}

$modulesExtractInfo = DBQueryGetRows( 'SELECT * FROM ca.modules WHERE id > '.CommonModules::MOD_CSI_ZERO_DAY);

$modulesCustInfo = DBQueryGetRows( 'SELECT module_id FROM ca.modules_customers WHERE cst_id = ' . (int) $cstId .' AND module_id > '.CommonModules::MOD_CSI_ZERO_DAY);

$arrayMod = array() ;
foreach ($modulesExtractInfo as $keyMod => $valMod ) {
	$arrayMod[$valMod['code']] = (searchForId($valMod['id'], $modulesCustInfo)) ? 	1	:	0 ;
}

$custlicesInfo = DBQueryGetRows( 'SELECT license_type, num_granted FROM ca.partition_license_pool WHERE cst_id = ' . (int) $cstId .' AND partition_id = 0');

foreach ($custlicesInfo as $licesKey => $licesVal) {
	$custLicenses[$arrayLices[$licesVal['license_type']]] =  $licesVal['num_granted'] ;
}

$arrayData = array ('CUSTOMERID' 	=> $cstId,
					'MODULES' 		=> $arrayMod,
					'LICENSES'		=> $custLicenses,
					'TIMESTAMP' 	=> time()
					) ;

$jsonLicsModules = json_encode($arrayData) ;
$cryptString = @new Crypt(CRYPT_AES_KEY) ;
$licsModulesStringCpy = $cryptString->encryptAes256CbcString($jsonLicsModules, CRYPT_AES_KEY) ;
?>
<!DOCTYPE html>
<html>
	<link rel="stylesheet" type="text/css" href="account_management.css" />
	<body>

		<p class="notice">Click on the button to copy the License string</p>

		<input type="text" value='<?php echo $licsModulesStringCpy;?>' id="licenseModString" size="500" readonly>
		<button onclick="myFunction()">Copy License String</button>

		<script>
			function myFunction() {
  			var copyText = document.getElementById("licenseModString");
  			copyText.select();
  			document.execCommand("copy");
  			alert("Copied the License String");
			}
		</script>
	</body>
</html>
