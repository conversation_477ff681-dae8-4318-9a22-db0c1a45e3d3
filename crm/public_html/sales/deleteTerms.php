<link rel="stylesheet" type="text/css" href="account_management.css" />
<?php

if ( !empty( $_GET['cst_id'] ) ) {
	$cstId = (int) $_GET['cst_id'];
} else {
	exit( 'Halting. Customer ID required.' );
}

require_once('TermsAndConditions.php');
require_once( 'configuration.php' );
require_once(INCLUDE_PATH . 'sales_functions.php');
require_once(INCLUDE_PATH . 'global_functions.php');

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

$terms = new TermsAndConditions( $cstId );
if ( $terms->removeTerms() ) {
	echo 'Terms & Conditions successfully removed.';
} else {
	echo 'Terms & Conditions removal failed.';
}