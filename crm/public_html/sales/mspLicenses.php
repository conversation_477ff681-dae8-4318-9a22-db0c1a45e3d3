<?php

require_once('csi7MspLicense.php');

$licenses = new csi7MspLicense();
$range = $licenses->sanitizeDateRange( $_GET['from'], $_GET['to'] );
$customers = $licenses->getMspLicenseHistory( true, $range['from'], $range['to'] );

echo '
<link rel="stylesheet" type="text/css" href="account_management.css" />
<div style="margin: 10px;">
<form method="GET">
<input type="hidden" name="page" value="mspLicenses" />
<table>
<tr>
<td><a href="downloadMspCsv.php?from=' . $range['from'] . '&to=' . $range['to'] . '" class="export">Export data into CSV</a></td>
<td>From: <input type="text" name="from" id="datepicker1" value="' . $range['from'] . '"/></td>
<td>To: <input type="text" name="to" id="datepicker2" value="' . $range['to'] . '"/></td>
<td><input type="submit" />
</td>
</tr>
</table>
</form>
</div>
	<table>
	<thead><tr><th>Date</th><th>CST ID</th><th>Partition</th><th>Partition Created</th><th>Salesforce ID</th><th>Name</th><th>Username</th><th>Hosts Granted</th><th>Hosts Scanned</th><th>Hosts Available</th><th>Days Old</th><th>Chargeable</th></tr></thead>
	<tbody>';

foreach( $customers as $customer ) {
	if ( $customer['partition_created'] ) {
		$created = date( 'd.m.Y H:i:s', strtotime( $customer['partition_created'] ) );
	} else {
		$created = '-';
	}

	echo '
	<tr>
		<td>' . date( 'd.m.Y', strtotime( $customer['date'] ) ) . '</td>
		<td align="right">' . $customer['cst_id'] . '</td>
		<td align="right">' . $customer['partition_id'] . '</td>
		<td>' . $created  . '</td>
		<td align="right">' . $customer['salesforce_id'] . '</td>
		<td>' . htmlspecialchars( $customer['name'] ). '</td>
		<td>' . htmlspecialchars( $customer['username'] ). '</td>
		<td align="right">' . $customer['hosts_granted'] . '</td>
		<td align="right">' . $customer['hosts_scanned'] . '</td>
		<td align="right">' . $customer['hosts_available'] . '</td>
		<td align="right">' . $customer['days_old'] . '</td>
		<td>' . $customer['chargeable'] . '</td>
	</tr>';
}
echo '</tbody></table>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/themes/smoothness/jquery-ui.css">
<script>
$(function() {
	$( "#datepicker1,#datepicker2" ).datepicker({
		dateFormat: "dd.mm.yy"
	});
});
</script>
';