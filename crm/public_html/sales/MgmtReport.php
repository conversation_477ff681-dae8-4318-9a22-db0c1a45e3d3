<?php
/**
 * This report will list active customer where any of customers account have not logged in more that 45 days.
 * */
include 'configuration.php';
include INCLUDE_PATH . 'global_functions.php';
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();
$sql = "SELECT X.cst_id
				,TRIM(SUBSTRING(account_email, LOCATE('@', account_email) + 1)) AS domain
				,Z.lastLogin
				,DATEDIFF(NOW(),IFNULL(Z.lastLogin,created)) DaysSinceLogin
				,account_expires,created, DATEDIFF(NOW(),created) ClientNoOfDays
				,(SELECT SUM(used_device_licenses) FROM ca.accounts WHERE cst_id = X.cst_id) HostsUsed
				,(SELECT SUM(num_granted) FROM ca.partition_license_pool WHERE cst_id = X.cst_id AND license_type =1) HostsLicensed	
				,V.name CRM, 
				V.master_id	
				FROM ca.accounts X 
				INNER JOIN 
				(
				SELECT cst_id, MAX(last_login) lastLogin
					FROM ca.accounts 
					GROUP BY cst_id
				
				) Z ON X.cst_id = Z.cst_id
				INNER JOIN ca.csi_pdb_info Y ON X.cst_id = Y.cst_id
				LEFT JOIN crm.cst V ON X.cst_id = V.cst_id
				WHERE X.account_expires > NOW()
				AND LOWER(X.account_email) NOT LIKE '%flexera%' 
				AND LOWER(X.account_email) NOT LIKE '%secunia%' 
				AND LOWER(X.account_username) NOT LIKE '%removed%'
				AND X.partition_id = 0 AND X.account_login_type =1 
				AND LOWER(Y.db_client_host) NOT LIKE '%removed%'
				AND Y.partition_id = 0 
				AND DATEDIFF(NOW(),IFNULL(Z.lastLogin,created)) > 45 
				AND DATEDIFF(X.account_expires,NOW()) < 300
				ORDER BY X.account_expires ASC,DaysSinceLogin DESC;";

$customers = DBQueryGetRows( $sql );

header('Content-Type: application/csv');
header('Content-Disposition: attachment; filename=CustomerLastLogin.csv');
header('Pragma: no-cache');
$out = fopen('php://output', 'w' );

fputcsv( $out, array('CST ID','domain','LastLogin','DaysSinceLogin','Expires','CreatedOn','CustForDays','HostsUsed','HostsLicensed','SF','Master ID'), ',', '"' );
if (count($customers) > 0) {
	foreach( $customers as $customer ) {
		fputcsv( $out, $customer, ',', '"' );
	}
}