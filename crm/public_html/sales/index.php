<?php
//error_reporting(0);

// Load configuration
include 'configuration.php';

// Load sales functions
include INCLUDE_PATH . 'sales_functions.php';

// Load global functions
include INCLUDE_PATH . 'global_functions.php';

/**
 * @global
 * Open DB Connection
 * This global is currently only used by CommonModules
 */
$GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] = fOpenDatabase();

// Euro (Base CRM Currency)
define('CURRENCY_EURO', DBGetRowValue('crm.country_currency', 'currency', "currency_name = 'EURO'") );

// putenv('REMOTE_USER=jfoley');
// Get base details for sales rep.
switch ( getenv('REMOTE_USER') ) {
	case 'jb' :
		$aRepData = DBGetRow('crm.salespeople', "init = '" . 'jb' . "'");
		break;
	case 'stimmerman' :
		$aRepData = DBGetRow('crm.salespeople', "init = '" . 'stimmerman' . "'");
		break;
	case 'jfoley' :
		$aRepData = DBGetRow('crm.salespeople', "init = '" . 'testing123' . "'"); // dev
		break;
	default:
		$aRepData = DBGetRow('crm.salespeople', "init = '" . getenv('REMOTE_USER') . "'");
}

// Pages that should be loaded before any HTML
if (isset($_GET['page'])) {
    switch ($_GET['page']) {
        case 'callcdr':
        case 'isatargets':
        case 'lead_approval':
        case 'callstats':
            include INCLUDE_PATH . $_GET['page'] . '.php';
            break;
        case 'ca_account_management':
            if (isset($_GET['s']) && $_GET['s'] == 1) {
                include INCLUDE_PATH . $_GET['page'] . '.php';
                exit();
            }
            break;
        case 'save_customer_data':
        case 'save_iframe':
            include INCLUDE_PATH . $_GET['page'] . '.php';
            exit();
        case 'hovertable':
            include INCLUDE_PATH . $_GET['page'] . '.php';
            exit();
        case 'invoice':
            // Only if this was a "save"
            if (isset($_POST['save']) && $_POST['save'] == 1) {
                include INCLUDE_PATH . $_GET['page'] . '.php';
            }
            break;
        case 'today':
            if (isset($_GET['limit']) && strlen($_GET['limit']) > 0) {
                setcookie('limit', $_GET['limit']);
            }
            break;
        default:
            /*
            if ($_GET['page'] != "customer" && $_GET['page'] != "today"){
                header("location: https://login.salesforce.com");
                die();
            }
            break;*/
    }
}
?>

<html>
<head>
	<title>Secunia CRM 2.0 - Sales</title>
	<link rel="stylesheet" TYPE="text/css" HREF="default.css">
	<meta charset="utf-8">
	<?php
	// JS File
	if ( isset($_GET['page']) && is_file(JS_PATH_LOCAL . $_GET['page'] . '.js') ) {
		echo '<script src="' . JS_PATH_PUBLIC . $_GET['page'] . '.js"></script>';
	}

	// Update right frame?
	if ( isset($_GET['right']) ) {
		switch ( $_GET['right'] ) {
			case 'overview':
				echo "<script>parent.frames['right'].location = '?page=right&display=overview';</script>";
				break;
			case 'performance':
				echo "<script>parent.frames['right'].location = '?page=right';</script>";
				break;
			default:
		}
	}
	?>
	<script>
	// Default JS

	// Abort bubbling
	function fAbortBubble( oEvent ) {
		if ( !oEvent ) {
			var oEvent = window.event;
		}

		oEvent.cancelBubble = true;

		if ( oEvent.stopPropagation ) {
			oEvent.stopPropagation();
		}
	}

	// Make sure that we always have the correct mouse pos.
	{
		// Detect if Internet Explorer
		var IE = ( document.all ? true : false );
		var last_focus = '';
		var cshow;

		// If NS -- that is, !IE -- then set up for mouse capture
		if ( !IE ) {
			document.captureEvents(Event.MOUSEMOVE);
		}

		// Set-up to use getMouseXY function onMouseMove
		document.onmousemove = getMouseXY;

		// Temporary variables to hold mouse x-y pos.s
		var tempX = 0;
		var tempY = 0;
	}

	// Main function to retrieve mouse x-y pos.s
	function getMouseXY(e) {
		// grab the x-y pos.s if browser is IE
		if (IE) {
			tempX = event.clientX + document.body.scrollLeft
			tempY = event.clientY + document.body.scrollTop
		} else {
			// grab the x-y pos.s if browser is NS
			tempX = e.pageX
			tempY = e.pageY
		}

		// catch possible negative values in NS4
		if ( tempX < 0 ) {
			tempX = 0
		}
		if ( tempY < 0 ) {
			tempY = 0
		}
		return true
	}
	</script>
</head>

<?php
// Choose Content
if ( isset( $_GET['page'] ) ) {
	switch ( $_GET['page'] ) {
	// Approved pages
	case 'top':
	case 'today':
	case 'right':
	case 'customer':
	case 'internal':
	case 'internal_conference':
	case 'calendar':
	case 'search':
	case 'ca_account_management':
	case 'sale_report':
	case 'isa_report':
	case 'forecast_report':
	case 'customer_report':
	case 'customer_report_new':
	case 'send_advisory':
	case 'invoice':
	case 'assigned_leads':
	case 'admin_segment_overview':
	case 'admin_segment_overview_2':
	case 'admin_segment_overview_assign':
	case 'admin_rep_customers':
	case 'admin_cluster':
	case 'admin_online_sales':
	case 'admin_leadflow':
	case 'admin_leadcycle':
	case 'admin_dm_campaign':
	case 'admin_csi_beta':
	case 'admin_csi_license_usage':
	case 'inbound_pipeline':
	case 'search2':
	case 'mspLicenses':
	case 'customers':
		echo '<body topmargin="0" leftmargin="0" rightmargin="0" bottommargin="0">';
		include INCLUDE_PATH . $_GET['page'] . '.php';
		echo '</body>';
		break;
	// Load Frameset
	default:
		include INCLUDE_PATH . 'frameset.php';
	}
} else {
	include INCLUDE_PATH . 'frameset.php';
}
?>

</html>
