BODY,TD,INPUT,SELECT,TEXTAREA
{
	font-family: verdana, arial;
	font-size: 11px;
}

FIELDSET
{
	border: 1px solid #65AEDB;
}

LEGEND
{
	font-size: 11px;
	border: 1px solid #65AEDB;
	padding: 0.2em 0.5em;
	background: #65AEDB;
	background-image: url(header_bg.png);
}

A
{
	color: #000000;
	text-decoration: underline;
}

A:hover
{
	text-decoration: none;
}

INPUT[type="text"]
{
	background: #E0F3FF;
	border: 1px solid #CECECE;
	width: 100%;
}

INPUT[type="text"]:focus
{
	border: 1px solid #9E9E9E;
}

TEXTAREA
{
	background: #E0F3FF;
	border: 1px solid #CECECE;
	width: 100%;
	height: 100px;
}

TEXTAREA:focus
{
	border: 1px solid #9E9E9E;
}

SELECT
{
	background: #E0F3FF;
	border: 1px solid #CECECE;
}

.TableHeadline
{
	background-image: url(header_bg.png);
	color: #000000;
	font-size: 14px;
	font-weight: bold;
	padding-left: 3px;
}

.TableSubHeadline
{
	font-weight: bold;
	border-bottom: 1px solid #CECECE;
}

.TablePadding
{
	padding-left: 3px;
}

.TableGreyBackground
{
	background: #d5d5d5;
}

.TableAlternateBackground
{
	background: #D0D0D0;
}

.TableIsCustomerBackground
{
	color: green;
}

.NumberCell
{
	text-align: right;
	padding-right: 5px;
}