<?php

require( 'Account.class.php' );
require( 'Util.class.php' );
require( 'LicenseRestrictions.class.php' );
require_once( 'configuration.php' );

$aCustomerDetails = fGetCustomerDetails( $_POST['cst_id'] );

// CA: ACCOUNTS
if ( !empty($_POST['ca_username']) ) {

		if ( empty( $_POST['ca_email'] ) ) {
			echo '<font color="RED"><b>ERROR: Account Email address is not provided.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
			exit();
		}

		$email = $_POST['ca_email'];

		if ( !Util::validateEmail( $email ) ) {
			echo '<font color="RED"><b>ERROR: Invalid Email address provided.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
			exit();
		}


        // Verify that ca_username is available
        if ( DBNumRows('ca.accounts', "account_username = '" . mysql_real_escape_string( $_POST['ca_username'] ) ."'") ) {
		echo '<font color="RED"><b>ERROR: Username is already used.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
                exit();
        }

		// Check if the the active period for the account is valid
		if ( !LicenseRestrictions::canCreateLicense( $aRepData, array( 'quantity' => 1, 'start_date' => date("Y-m-d"), 'end_date' => $_POST['ca_expires'] ) ) ) {
			echo '<font color="RED"><b>ERROR: Not allowed: ' . htmlspecialchars( LicenseRestrictions::getLastError() ) . '.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
			exit();
		}

	error_log("st-debug 1");

        // Check if CST_ID has account already, if it is, then create a new Shadow CST_ID
	$aAccount = DBGetRow('ca.accounts', "accounts.cst_id = '" . (int) $_POST['cst_id'] . "'");
        if ( $aAccount ) {
                // Create Shadow CST ID
			$sQuery = "INSERT INTO crm.cst (master_id, person_id) VALUES('" . (int) $aCustomerDetails['Company']['cst_id'] . "', '" . (int) $aRepData['person_id'] . "')";
                DBQuery($sQuery);

                // Get Shadow CST_ID
                $iAccountCSTID = (int) mysql_insert_id();
        } else {
				$iAccountCSTID = (int) $_POST['cst_id'];
        }


	error_log("st-debug 1.5");

		// Insert primary contacts row. This fixes bug #5499
		// Only insert this row if it doesn't exist yet. We only want it to ensure we can fetch the email address later
		$contact = DBGetRow( 'crm.contacts', "cst_id = '" . $iAccountCSTID . "' AND primary_contact = 1" );
		if ( false === $contact ) {
			// The name and email inserted here are fetched later when inserting the contact_method row.
			$sQuery = "INSERT INTO crm.contacts (cst_id, position, name, email, primary_contact) VALUES('" . $iAccountCSTID . "', '1', '" .  mysql_real_escape_string( $_POST['ca_username'] ) . "', '" .  mysql_real_escape_string( $_POST['ca_email'] ) . "', '1' )";
			DBQuery($sQuery);
		}

	error_log("st-debug 2");

        // If Trial - Register trial on customer
        if ( !empty($_POST['ca_trial']) ) {
			$sQuery = "INSERT INTO crm.saleslog (sold_date, expires_date, discount, product_name, product_price, product_type, product_trial, cst_id, person_id, product_id, payment_time, lang_id, product_category) VALUES(NOW(), '" . mysql_real_escape_string( $_POST['ca_expires'] ) . "', '0', '" . Product::getName( $_POST['ca_product_type'] ) . " - Trial Account', 0, '" . mysql_real_escape_string( $_POST['ca_product_type'] ) . "', 1, '" . (int) $_POST['cst_id'] . "', '" . (int) $aRepData['person_id'] . "', '9999', 0, 1, '" . mysql_real_escape_string( fReturnProductCategory( $_POST['ca_product_type'] ) ) . "')";
                DBQuery($sQuery);
        }

        // Password / Pincode
        $iPinCode = rand(1000,9999);

        // Create Account
		$account = new Account( $iAccountCSTID );
        $iAccountID = $account->create( array_merge( $_POST, array( 'password' => $iPinCode ) ) );

		if ( !$iAccountID ) {
			echo '<font color="RED"><b>ERROR: An unknown error occurred, no account was created.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
			exit();
		}


		if ( $_POST['ca_product_type'] == Product::CSI6 ) {
			$account->populateCsi6Data( array( 'license_valid_til' => $_POST['ca_expires'] ) );
        }

		if ( $_POST['ca_product_type'] == Product::CSI7 ) {
			$account->populateCsi7Data(
									   array(
											 'expiry' => $_POST['ca_expires']
											 ,'email' => $email
											 )
									   );
        }

	/*
        if ( ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) ) {
                // Set the VIM trial special limits
                if ( $_POST['ca_product_type'] == 208 ) {
                        $xmlAccess = 0;
                } else {
                        $xmlAccess = 1;
                }

                DBQuery("UPDATE ca.accounts SET special_limits = 'vim_30', account_version = 'vim_30', account_product_type = '".(int)$_POST['ca_product_type']."', account_options = '".(int)$aDefaultOptionMapping[$_POST['ca_product_type']]."', xml_access = '".(int)$xmlAccess."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
        }

	error_log("st-debug 5");
        // If BA, automatically add user to receive emails about new BA's
        if ( ( $_POST['ca_product_type'] == 8 || $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) ) {
                // IF VIM 3.0 Add customer details on the new account
                $contactValue = $_POST['ca_username'];
                if ( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ) {
                        // Fetch primary contact:
                        $result = DBGetRow("crm.contacts", "cst_id = '".(int)$_POST['cst_id']."' AND primary_contact = 1");
                        $name = $result['name']; // There should be one primary contact, for a valid value
                        $contactValue = mysql_real_escape_string( $result['email'] );
                        DBQuery("UPDATE ca.accounts SET account_name = '".mysql_real_escape_string( $name )."', account_email = '".mysql_real_escape_string( $result['email'] )."' WHERE account_id = '".(int)$iAccountID."' LIMIT 1");
                }

                DBQuery("INSERT INTO ca.contact_method SET " .
                        "contact_method_value = '" . $contactValue . "', " .
                        "contact_method_type = 1, " .
                        "account_id = '" . $iAccountID . "', " .
                        "name = '".( $_POST['ca_product_type'] == 209 || $_POST['ca_product_type'] == 208 ? mysql_real_escape_string( $name ) : "Contact" )."', " .
                        "pos = '1', " .
                        "lang_eng = '1'");
        }
	*/
	error_log("st-debug 6");

		// VIM 4.0 - Set the VIM trial special limits
		if ( $_POST['ca_product_type'] ==  Product::VIM4 ) {
			$account->populateVim4Data(
									   array( 'email' => $_POST['ca_email']
											  ,'account_options' => $aDefaultOptionMapping[$_POST['ca_product_type']]
											  )
									   );
        }

	error_log("st-debug 8");
	/*
		// Partner account
        if ( $_POST['ca_product_type'] == 9 ) {
                // Update account with special limit, DEFAUL TO TRIAL!
                if ( $_POST['ca_trial'] ) {
                        $pSpecialLimits = "partner_portal";
                } else {
                        $pSpecialLimits = "partner_portal_trial";
                }
                DBQuery("UPDATE ca.accounts SET special_limits = '".$pSpecialLimits."' WHERE account_id = '" . $iAccountID . "' LIMIT 1");

                // Create entry in 'partner_profile'
                DBQuery("INSERT INTO ca.partner_profile SET account_id = '" . $iAccountID . "', invoice_country = '" . $_POST['country'] . "'");
                $_partnerId = mysql_insert_id();

                // Assign current partner_id to the current customer id
                DBQuery("UPDATE crm.cst SET partner_id = '".(int)$_partnerId."' WHERE cst_id = ( SELECT master_id FROM crm.cst WHERE cst_id = '".(int)$iAccountCSTID."' LIMIT 1 ) LIMIT 1");
        }
	*/

        // Email Pincode
	mail($aRepData['email'], 'Account Pincode', 'Username: ' . htmlspecialchars( $_POST['ca_username'] ) . '
Pincode: ' . htmlspecialchars( $iPinCode ), 'From: <EMAIL>');

	error_log("st-debug 9");

	// Redirect user to CA Account Admin
	header('location: ?page=ca_account_management&iframe=1&account_id=' . $iAccountID . '&cst_id=' . $iAccountCSTID);
	exit();

}

// CA: CSI LICENSE KEY
if ( $_POST['lk_account_id'] && $_POST['lk_starts'] && $_POST['lk_expires'] && $_POST['lk_quantity'] && $_POST['lk_type'] && $_POST['lk_license_type'] ) {

	$accountId = (int) $_POST['lk_account_id'];

	// Check if it's possible to create the license.
	require_once INCLUDE_PATH . 'LicenseRestrictions.class.php';
	$rawLicenseData = array(
		'quantity' => $_POST['lk_quantity']
		,'start_date' => $_POST['lk_starts']
		,'end_date' => $_POST['lk_expires']
		,'type' => $_POST['lk_type']
		,'license_type' => $_POST['lk_license_type']
		,'target_account_id' => $accountId
	);
	$canCreateLicense = LicenseRestrictions::canCreateLicense( $aRepData, $rawLicenseData );
	$account = Account::getByAccountId( $accountId );

	if ( empty( $account ) ) {
		exit();
	}

	/* For the CSI 7, it is not possible to create more than 1
	   license per license type */
	if ( $account['special_limits'] === 'csi_70' )  {
        echo '<script>window.opener.location = window.opener.location;</script><font color="RED"><b>NOT ALLOWED:Additional license keys can not be created for CSI7 accounts.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
		exit();
	}

	if ( $canCreateLicense ) {

		$cstId = (int) $_POST['cst_id'];
		$licenseType = intval($_POST['lk_license_type']);
		$type = intval($_POST['lk_type']);

        // Generate License Key
        $sLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

        // Store License Key
        DBQuery("INSERT INTO ca.license_keys SET " .
				"license = '" . mysql_real_escape_string( $sLicKey ) . "', " .
				"account_id = '" . (int) $accountId . "', " .
				"created = '" . mysql_real_escape_string( $_POST['lk_starts'] ) . " 00:00:00', " .
				"valid_from = created, " .
				"valid_to = '" . mysql_real_escape_string( $_POST['lk_expires'] ) . " 23:59:59', " .
				"activated = created, " .
				"quantity = '" . intval($_POST['lk_quantity']) . "', " .
				"license_type = '" . mysql_real_escape_string( $licenseType ) . "', " .
				"type = '" . mysql_real_escape_string( $type ) . "'");

		$licenseId = mysql_insert_id();

		// If the license type is 'User License', update the accounts table
		// with the license_id if it doesn't already exist
		if ( $licenseType == '2' ) {
			$account = new Account( $cstId, $accountId );
			$account->updateLicenseId( $licenseId, false /* Do not force update */ );
		}
        echo '<script>window.opener.location = window.opener.location;</script><font color="GREEN"><b>SUCCESS: License key created and assigned to account.</b></font><br><br><a href="javascript:window.close();">Close this window</A>';
        exit();
	}

	// TODO log this failure

	// Not possible to create the license key. To check the error related to it
	// call LicenseRestrictions::getLastError();
	// Return the License Restrictions for $repAccount
	$licenseRestrictions = LicenseRestrictions::getForSalesPerson( $aRepData );
	?><script>
		window.opener.location = window.opener.location;
	</script>
	<span style="font-weight: bold; color: red;">
		FAILURE: License Key not created
	</span>
	<br/>
	<b>Error:</b> <?php echo LicenseRestrictions::getLastError(); ?>
	<p>
	Please review the fields in the License Key form and try again.
	<br>
	<ul>
		 <li>Maximum allowed Days per License key: <b><?php echo (int) $licenseRestrictions['maxLicenseDays']; ?></b></li>
		 <li>Maximum allowed Quantity (Hosts/Users) per License key: <b><?php echo (int) $licenseRestrictions['maxLicenseQuantity']; ?></b></li>
	</ul>
	</p>
	<a href="javascript:window.close();">Close this window</a>
	<?php
}

echo 'Invalid Request';
exit();