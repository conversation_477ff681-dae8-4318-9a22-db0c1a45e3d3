<?php

/**
 * @file CommonModules.class.php
 * @requires CommonModule.class.php
 * This file contains the Modules class
 */


/**
 * Class CommonModules
 * Wrapper for working with the common db's ca.modules, ca.modules_customers and
 * ca.modules_accounts tables.
 *
 * All of the DB related functions called from this class assume there is an
 * open MySQL connection to the common db server that hosts the modules tables.
 *
 * @note You must set the MySQL connection before calling this class' static methods
 *
 * @todo TODO: Add a name and description column to the ca.modules table and add
 *       a table which does some sort of product-to-module binding so we can
 *       specify the modules that are available for products and versions.
 *       Also add a way to determine if a Module is enabled per Cst or Acct.
 *
 * @package CRM
 * @subpackage Modules
 * @date 10.17.2012
 * <AUTHOR>
 */
abstract class CommonModules {

	/**
	 * These constants coincide with the module ids in the modules table
	 */
	const MOD_CSI_IP_ACCESS_MANAGEMENT = 1; // IP Access Management - Per Account ID
	const MOD_CSI_USER_MANAGEMENT = 2; // User Management - Per Account ID
	const MOD_CSI_SCCM_PLUGIN = 3; // SCCM Plugin - per Cst ID
	const MOD_CSI_ZERO_DAY = 5; // Zero Day - per Cst ID
	const MOD_CSI_THREAT_BASIC = 6; // Threat Basic- per Cst ID
	const MOD_CSI_THREAT_ADVANCED = 7; // Threat Advanced - per Cst ID
	const MOD_CSI_VPM = 8; // Vendor Patch Module - per Cst ID
	const MOD_CSI_NEW_UI = 9;//

	/**
	 * These constants coincide with `modules`.`applies_to`
	 */
	const APPLIES_TO_CUSTOMER = 1; // This Mod applies to Customers
	const APPLIES_TO_ACCOUNT = 2; // This Mod applies to Accounts

	/**
	 * @var resource
	 * MySQL link - Ensure to set this before using this class
	 */
	private static $_conn = null;

	/**
	 * Sets the MySQL connection used for DB calls. If this code is used in a place
	 * with db related classes then this will be deprecated.
	 *
	 * @param reference $conn MySQL link
	 */
	public static function setConn( $conn )
	{
		//if ( !is_resource( $conn ) ) {
		if ( !($conn instanceof mysqli) ) {
			throw new Exception( 'CommonModules::setConn($conn) requires $conn to be a MySQL Link' );
		}
		self::$_conn = $conn;
	}

	/**
	 * Performs a db query using the MySQL connection set for the class.
	 *
	 * @see mysql_query()
	 * @param string $query MySQL query string
	 * @return mixed
	 */
	public static function dbQuery( $query )
	{
		return mysql_query( $query, self::$_conn );
	}

	/**
	 * Fetches and returns the results of the supplied SQL query using the class'
	 * MySQL connection. We don't use the global MySQL connection because we can't
	 * guarantee it is connected to the correct DB.
	 *
	 * By default the results use normal int array keys.
	 * The keys can be set to a column's value by specifying the column's name
	 * in $keyIndexColumn. If multiple rows have the same value in the column
	 * specified then they will overwrite each other and only the last processed
	 * row will be returned in the results.
	 *
	 * @param string $query SQL query
	 * @param string $keyIndexColumn If supplied, the column to use as the index
	 * @return array
	 */
	private static function dbQueryGetModules( $query, $keyIndexColumn = '' )
	{
		if ( $res = mysql_query( $query, self::$_conn ) ) {
			$rows = array();
			if ( '' !== $keyIndexColumn ) {
				// specify a column as the index
				while ( $row = mysql_fetch_assoc( $res ) ) {
					$rows[$row[$keyIndexColumn]] = new CommonModule( $row );
				}
				return $rows;
			}
			while ( $row = mysql_fetch_assoc( $res ) ) {
				array_push( $rows, new CommonModule( $row ) );
			}
			return $rows;
		}
		return array();
	}

	/**
	 * Gets the modules by executing the supplied query
	 *
	 * @param string $query Complete SQL query
	 * @return array
	 */
	private static function getModulesByQuery( $query )
	{
		$modules = self::dbQueryGetModules( $query, $key = 'id' );
		self::addModuleDescriptions( $modules );
		return $modules;
	}

	/**
	 * Adds module descriptions to the module rows. This solution will be replaced
	 * by having the data in the table at a later date.
	 *
	 * @note $modules is passed by reference
	 * @param array &$modules Modules rows from the db
	 */
	private static function addModuleDescriptions( &$modules )
	{
		foreach ( $modules as &$module ) {
			switch ( $module->getId() ) {
				case self::MOD_CSI_IP_ACCESS_MANAGEMENT;
					$module->setName( 'IP Access Management' );
					$module->setDescription( 'Adds a functionality to CSI which allows whitelisting and blacklisting external IPs to control who can log in to CSI remotely.' );
					$module->setVersionSupport( 'CSI 5+' );
					$module->setAppliesTo( 2 ); // account_id
					break;
				case self::MOD_CSI_USER_MANAGEMENT:
					$module->setName( 'User Management' );
					$module->setDescription( 'Adds the ability to create, manage and remove User Accounts from Secunia CSI.' );
					$module->setVersionSupport( 'CSI 5+' );
					$module->setAppliesTo( 2 ); // account_id
					break;
				case self::MOD_CSI_SCCM_PLUGIN:
					$module->setName( 'SCCM Plugin' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use the SCCM Plugin.' );
					$module->setVersionSupport( 'CSI 6+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				case self::MOD_CSI_ZERO_DAY:
					$module->setName( 'Zero Day' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use the Zero Day Functionality.' );
					$module->setVersionSupport( 'CSI 7+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				case self::MOD_CSI_THREAT_BASIC:
					$module->setName( 'Threat Intel Basic' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use the Threat Intel Basic Functionality.' );
					$module->setVersionSupport( 'CSI 7+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				case self::MOD_CSI_THREAT_ADVANCED:
					$module->setName( 'Threat Intel Advanced' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use the Threat Intel Advanced Functionality.' );
					$module->setVersionSupport( 'CSI 7+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				case self::MOD_CSI_VPM:
					$module->setName( 'Vendor Patch Module' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use the Vendor Patch Module Functionality.' );
					$module->setVersionSupport( 'CSI 7+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				case self::MOD_CSI_NEW_UI:
					$module->setName( 'New SVM UI' );
					$module->setDescription( 'This module allows all of the Customer\'s Accounts to use new SVM UI Functionality.' );
					$module->setVersionSupport( 'CSI 7+' );
					$module->setAppliesTo( 1 ); // cst_id
					break;
				default:
					$module->setName( $module->getCode() );
					$module->setDescription( 'This Module is new and is waiting for more information to be filled in.' );
					$module->setVersionSupport( '' );
					$module->setAppliesTo( 2 ); // account_id
			}
		}
	}

	/**
	 * Gets a Module by  it's ID. If more than one ID is supplied via an array
	 * then an array of Modules will be returned.
	 *
	 * @param mixed $moduleId
	 *	If $moduleId is an int then a single Module will be returned
	 *	If $moduleId is an array then it is treated as an array of ints and any
	 *	modules with ids matching those ints will be returned.
	 * @return mixed
	 *	If an array of ids was supplied then returns an array of Modules
	 *	If an integer id was supplied then the matching CommonModule object will be returned.
	 */
	public static function getById( $moduleId )
	{
		if ( is_array($moduleId) ) {
			$moduleIds = implode("', '", $moduleId);
			return self::getModulesByQuery( "SELECT * FROM ca.modules WHERE id IN ('{$moduleIds}');" );
		}
		$modules = self::getModulesByQuery( "SELECT * FROM ca.modules WHERE id = '{$moduleId}';" );
		if ( count($modules) ) {
			return reset( $modules );
		}
	}

	/**
	 * Returns only the modules enabled for this customer
	 *
	 * @param int $cstId Customer ID
	 * @return array
	 */
	public static function getEnabledByCstId( $cstId )
	{
		// verify that the customer id is actually an id
		if ( !is_pint( $cstId ) ) {
			return array();
		}

		$query = <<<EOSQL
			SELECT m.*, mc.cst_id, 1 as enabled
			FROM ca.modules m
				LEFT JOIN ca.modules_customers mc
					ON (m.id = mc.module_id)
			WHERE mc.cst_id = '{$cstId}' ORDER BY m.code ASC
EOSQL;

		return self::getModulesByQuery( $query );
	}

	/**
	 * Returns all the modules enabled for an account
	 *
	 * @param int $accountId `ca`.`accounts`.`account_id`
	 * @return array
	 */
	public static function getEnabledByAccountId( $accountId )
	{
		// verify that the account id is actually an id
		if ( !is_pint( $accountId ) ) {
			return array();
		}

		$query = <<<EOSQL
			SELECT m.*, ma.account_id, 1 as enabled
			FROM ca.modules m
				LEFT JOIN ca.modules_accounts ma
					ON (m.id = ma.module_id)
			WHERE ma.account_id = '{$accountId}' ORDER BY ma.account_id DESC
EOSQL;

		return self::getModulesByQuery( $query );
	}

	/**
	 * Returns all the modules including their relations to the customer and account
	 *
	 * @return array
	 *	If a module is enabled then it's enabled column will be set to 1. If it
	 *	is disabled then it will be set to 0. To see if the mod is enabled on the
	 *	account, customer or both then test the account_id and cst_id columns.
	 */
	public static function getAllIncludingStatus( $cstId, $accountId )
	{
		if ( !is_pint( $cstId ) && !is_pint( $accountId )) {
			return array();
		}

		$query = <<<EOSQL
			SELECT m.*, mc.cst_id, ma.account_id
			,CASE WHEN ma.account_id IS NULL AND mc.cst_id IS NULL THEN 0 ELSE 1 END AS `enabled`
			FROM ca.modules m
				LEFT JOIN ca.modules_customers mc
					ON (m.id = mc.module_id AND (mc.cst_id = '{$cstId}' OR mc.cst_id IS NULL))
				LEFT JOIN ca.modules_accounts ma
					ON (m.id = ma.module_id AND (ma.account_id = '{$accountId}' OR ma.account_id IS NULL))
			ORDER BY mc.cst_id DESC
EOSQL;

		return self::getModulesByQuery( $query );
	}

	/**
	 * Updates the module's visibility
	 *
	 * @param array $account
	 * @param array $modules
	 * @return bool
	 *	true	: updated
	 *	false	: nothing to update
	 */
	public static function updateVisibility( $account, $bitmask )
	{
		$visibility = (int) $account['show_modules'] | $bitmask;
		$query = "UPDATE ca.accounts SET show_modules = '{$visibility}' WHERE ca.accounts.account_id = '{$account['account_id']}'";
		return (bool) self::dbQuery( $query );
	}

	/**
	 * Updates the enabled Modules for an Account by adding and removing the
	 * module bindings which have changed.
	 *
	 * @param array $account
	 * @param array $moduleIds
	 */
	public static function updateAccount( $account, $moduleIds )
	{
		$cstId = $account['cst_id'];
		$accountId = $account['account_id'];

		// Get the Modules by their ids
		$modules = self::getById( $moduleIds );

		// Iterate thru modules and separate the ones that bind to the account from the ones that bind to the Account
		$cstModuleIds = $accountModuleIds = array();
		foreach ( $modules as $module ) {
			if ( self::APPLIES_TO_CUSTOMER === $module->getAppliesTo() ) { // Customer modules
				$cstModuleIds[] = $module->getId();
			} elseif ( self::APPLIES_TO_ACCOUNT === $module->getAppliesTo() ) { // Account modules
				$accountModuleIds[] = $module->getId();
			}
		}

		######################################
		# Update Mod bindings for the Cst Id #
		######################################
		// Get the currently enabled mods that bind to the Cst Id
		$customerModules = self::getEnabledByCstId( $cstId );
		// Determine what needs to be inserted/deleted to update our current Cst Id mods
		$existingCustomerModuleIds = array_keys( $customerModules );
		$customerModuleIdsToDelete = array_diff( $existingCustomerModuleIds, $cstModuleIds );
		$customerModuleIdsToInsert = array_diff( $cstModuleIds, $existingCustomerModuleIds );
		// Erase any no-longer required Account bindings
		if ( $customerModuleIdsToDelete ) {
			$customerModuleIdsToDelete = implode("', '", $customerModuleIdsToDelete );
			self::dbQuery( "DELETE FROM ca.modules_customers WHERE ca.modules_customers.cst_id = '{$cstId}' AND ca.modules_customers.module_id IN ('{$customerModuleIdsToDelete}')" );
		}
		// Insert any new Account bindings
		if ( count($customerModuleIdsToInsert) ) {
			$query = 'INSERT INTO ca.modules_customers (cst_id, module_id) VALUES';
			$query .= "('{$cstId}', '" . implode( "'), ('{$cstId}', '", $customerModuleIdsToInsert ) . "')";
			$result = self::dbQuery( $query );
		}

		##########################################
		# Update Mod bindings for the Account Id #
		##########################################
		// Get the currently enabled mods that bind to the Account Id
		$accountModules = self::getEnabledByAccountId( $accountId );
		// Determine what needs to be inserted/deleted to update our current Account Id mods
		$existingAccountModulesIds = array_keys( $accountModules );
		$accountModuleIdsToDelete = array_diff( $existingAccountModulesIds, $accountModuleIds );
		$accountModuleIdsToInsert = array_diff( $accountModuleIds, $existingAccountModulesIds );
		// Erase any no-longer required Cst bindings
		if ( $accountModuleIdsToDelete ) {
			$accountModuleIdsToDelete = implode("', '", $accountModuleIdsToDelete );
			self::dbQuery( "DELETE FROM ca.modules_accounts WHERE ca.modules_accounts.account_id = '{$accountId}' AND ca.modules_accounts.module_id IN ('{$accountModuleIdsToDelete}')" );
		}
		// Insert any new Cst bindings
		if ( count($accountModuleIdsToInsert) ) {
			$query = 'INSERT INTO ca.modules_accounts (account_id, module_id) VALUES';
			$query .= "('{$accountId}', '" . implode( "'), ('{$accountId}', '", $accountModuleIdsToInsert ) . "')";
			$result = self::dbQuery( $query );
		}
	}

}


