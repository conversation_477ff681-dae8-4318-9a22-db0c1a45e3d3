<?php

/**
 * @file LicenseRestrictions.class.php
 * This file contains the LicenseRestrictions class
 */


/**
 * Class LicenseRestrictions
 *
 * Handles checking for restrictions on License Key creation.
 *
 * @package CRM
 * @subpackage Licenses
 * @date 10.24.2012
 * <AUTHOR>
 *
 * Use the canCreateLicense(..) method to check whether the license
 * can be created or not.
 *
 * The license restrictions functionality requires the
 * `salespeople_profile_type` table to be created and populated with
 * and the `profile_type_id` column have been added to the
 * `salespeople` table.
 *
 * Below is the SQL that would accomplish this task. By default all
 * Sales People would be set to default Users which means they can't
 * add licenses.
 *
 * -- 1) Create the table
 *	 CREATE TABLE `crm`.`salespeople_profile_type` (
 *		`id` tinyint(3) unsigned NOT NULL auto_increment
 *		,`system_name` varchar(16) NOT NULL COMMENT 'internal code use'
 *		,`name` varchar(32) NOT NULL
 *		,PRIMARY KEY (`id`)
 *	 ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COMMENT 'Profile types of sales people';
 *
 * -- 2) Populate the table
 *	 INSERT INTO `crm`.`salespeople_profile_type` (`id`, `system_name`, `name`) VALUES (10, 'default', 'User'), (20, 'sales_enterprise', 'Sales Enterprise'), (30, 'sales_smb', 'Sales SMB'), (40, 'cec', 'CEC');
 *
 * -- 3) Updates the `salespeople` table
 *	 ALTER TABLE crm.salespeople ADD COLUMN profile_type_id tinyint(3) unsigned NOT NULL DEFAULT 10 COMMENT 'Relates to salespeople_profile_type.id';
 *
 */
abstract class LicenseRestrictions
{

	/**
	 * @var string
	 * Contains the last error message
	 */
	private static $_error = '';

	/**
	 * Returns an error message about why the last method call failed.
	 *
	 * @return string
	 *	If the return value is an empty string then no error occurred.
	 */
	public static function getLastError()
	{
		return self::$_error;
	}

	const USER_REGULAR = 10;
	const USER_SALES_ENTERPRISE = 20;
	const USER_SALES_SMB = 30;
	const USER_CEC = 40;
	/**
	 * Gets the license restrictions for a Sales Person.
	 * ENHANCEMENT Put the license limit info in a table so they can be viewed and managed via the admin panel
	 *
	 * @param array $repAccount Sales Person account
	 * @return mixed
	 *	Returns an array with the below elements if the license restrictions are found for the account:
	 *		maxLicenseDays  : int - Maximum number of days for the license
	 *		maxLicenseQuantity : int - Maximum quantity for the license e.g. for a host license, it will be maximum number of hosts
	 *	Returns false if there is no license restriction for the Sales Persons
	 *
	 */
	public static function getForSalesPerson( $repAccount )
	{
		// These case IDs coincides with `crm`.`salespeople_profile_type`.`id`
		switch ( $repAccount['profile_type_id'] ) {
			case self::USER_SALES_ENTERPRISE:
				// These are the restrictions for the user
				$maxLicenseDays = 30;
				$maxLicenseQuantity = 100;
				break;

			case self::USER_SALES_SMB:
				$maxLicenseDays = 7;
				$maxLicenseQuantity = 5;
				break;

			case self::USER_CEC:
				$maxLicenseDays = *********; // effectively unlimited
				$maxLicenseQuantity = *********;
				break;

			case self::USER_REGULAR:
				$maxLicenseDays = 0; // can not create any
				$maxLicenseQuantity = 0;
				break;
			default:
				self::$_error = 'No License Restrictions found for the Sales Person';
				return false;
		}

		return array(
			'maxLicenseDays' => $maxLicenseDays
			,'maxLicenseQuantity' => $maxLicenseQuantity
		);
	}

	/**
	 * Checks if a license can be created or not based on the Sales Person trying
	 * to create it and the particulars of the license.
	 *
	 * @param array $repAccount
	 * @param array $licenseData
	 *
	 *  Checks related to the Quantity:
	 *  ------------------------------
	 *	quantity     : int Number of hosts
	 *
	 *	Releted to Date:
	 *	---------------
	 *	start_date   : string YYYY-MM-DD  License start date
	 *	end_date     : string YYYY-MM-DD  License is no longer valid on this date
	 *	license_type : int
	 *	type         : int
	 *	target_account_id : int  Refers to ca.accounts.id
	 *
	 * @param bool $onlyCheckForQuantity
	 *  Set to true, if we only want to check the restrictions on the quantity of the license
	 * @return bool
	 */
	public static function canCreateLicense( $repAccount, $licenseData, $onlyCheckForQuantity = false )
	{
		self::$_error = ''; // reset error

		// Ensure the Sales Person's Profile Type can create this license
		if ( !self::canProfileTypeCreateLicense( $repAccount, $licenseData, $onlyCheckForQuantity ) ) {
			self::$_error = 'The sales person\'s account belongs to a profile which is not allowed to create the requested license';
			return false;
		}

		// Ensure it's not 0 or negative
		if ( $licenseData['quantity'] < 1 ) {
			self::$_error = 'Invalid quantity:' . $licenseData['quantity'];
			return false;
		}

		if ( $onlyCheckForQuantity ) {
			return true;
		}

		// Ensure the start date is before the end date
		if ( strtotime($licenseData['start_date']) > strtotime($licenseData['end_date']) ) {
			self::$_error = 'End date must come after the Start date: ' . htmlspecialchars( $licenseData['start_date'] . ' >= ' . $licenseData['end_date'] );
			return false;
		}

		return true;
	}

	/**
	 * Checks if the Sales Person's Profile Type can create the License
	 *
	 * @param array $repAccount
	 * @param array $licenseData
	 * @param bool  $onlyCheckForQuantity
	 * @return bool
	 */
	private static function canProfileTypeCreateLicense( $repAccount, $licenseData, $onlyCheckForQuantity = false )
	{
		// Extract $maxLicenseDays and $maxLicenseQuantity
		$maxLicenseDays = null;
		$maxLicenseQuantity = null;
		extract( self::getForSalesPerson( $repAccount ) );

		// assert quantity
		if ( $licenseData['quantity'] > $maxLicenseQuantity ) {
			self::$_error = 'The quantity exceeds the maximum: ' . (int) $licenseData['quantity'] . ' (' . (int) $maxLicenseQuantity . ')';
			return false;
		}

		if ( $onlyCheckForQuantity ) {
			return true;
		}

		// assert days
		$startTimestamp = strtotime( $licenseData['start_date'] . ' 00:00:00' );
		$endTimestamp = strtotime( $licenseData['end_date'] . ' 00:00:00' );
		$days = floor( ($endTimestamp - $startTimestamp) / 86400 );
		if ( $days > $maxLicenseDays ) {
			self::$_error = 'The number of Days exceeds the maximum: ' . $days;
			return false;
		}

		return true;
	}
}