<?php

include 'configuration.php';
include INCLUDE_PATH . 'global_functions.php';
require_once('csi7MspLicense.php');
$GLOBALS[DB_HOST_CRM . DB_USER_CRM] = fOpenDatabase();
$sql = "SELECT A.cst_id
, DATEDIFF(UTC_TIMESTAMP(), IFNULL(B.lastLogin, A.created)) last_login_days_ago
, (SELECT COALESCE(SUM(used_device_licenses), 0) FROM ca.accounts WHERE cst_id = A.cst_id) licenses_used
, (SELECT COALESCE(SUM(num_granted), 0) FROM ca.partition_license_pool WHERE cst_id = A.cst_id AND license_type = 1) licenses_available
, '' AS sfdc_id
FROM ca.accounts A
INNER JOIN (SELECT cst_id, MAX(last_login) lastLogin
					FROM ca.accounts 
					GROUP BY cst_id) B ON B.cst_id = A.cst_id
WHERE A.special_limits = 'csi_70' AND A.account_login_type = 1
AND A.account_expires >= UTC_TIMESTAMP()
AND A.account_email NOT LIKE '%@flexera.com' 
AND A.account_email NOT LIKE '%@flexerasoftware.com' 
AND A.account_email NOT LIKE '%@secunia.com'
ORDER BY cst_id ASC;";

$accounts = DBQueryGetRows($sql);

$sfDc = array();
$sfPivotSql = 'SELECT salesforce_id, sfdc_id FROM crm.sfdc';
$sfDcIds = DBQueryGetRows($sfPivotSql);
foreach ($sfDcIds as $value) {
	$sfDc[$value['salesforce_id']] = $value['sfdc_id'];
}

$license = new csi7MspLicense();
foreach ($accounts as $key => $values) {
	// CRM Details

	$salesForceId = $license->fixSalesforceId(array(0 => array('cst_id' => $values['cst_id'])));
	$salesForceId = $salesForceId[0]['salesforce_id'];
	$sfDcId = '-';
	if ($salesForceId == '') {
		$salesForceId = '-';
	}
	if ($salesForceId != '-') {
		if (isset($sfDc[$salesForceId])) {
			$sfDcId = $sfDc[$salesForceId];
			$accounts[$key]['sfdc_id'] = $sfDcId;
		}
	}
}

// Check if JSON output is requested
$outputType = isset($_GET['type']) ? strtolower($_GET['type']) : 'csv';

if ($outputType === 'json') {
	// JSON output
	header('Content-Type: application/json');
	header('Pragma: no-cache');
	
	$jsonData = array();
	if (count($accounts) > 0) {
		foreach ($accounts as $customer) {
			$jsonData[] = array(
				'cst_id' => $customer['cst_id'],
				'sfdc_id' => isset($customer['sfdc_id']) ? $customer['sfdc_id'] : '-',
				'last_login_days_ago' => $customer['last_login_days_ago'],
				'licenses_available' => $customer['licenses_available'],
				'licenses_used' => $customer['licenses_used']
			);
		}
	}
	
	echo json_encode($jsonData, JSON_PRETTY_PRINT);
} else {
	// CSV output (default)
	header('Content-Type: text/csv');
	header('Content-Disposition: attachment; filename=usage-report.csv');
	header('Pragma: no-cache');
	$out = fopen('php://output', 'w');

	fputcsv($out, array('cst_id', 'sfdc_id', 'last_login_days_ago', 'licenses_available', 'licenses_used'), ',', '"');
	if (count($accounts) > 0) {
		foreach ($accounts as $customer) {
			$csvRow = array(
				$customer['cst_id'],
				isset($customer['sfdc_id']) ? $customer['sfdc_id'] : '-',
				$customer['last_login_days_ago'],
				$customer['licenses_available'],
				$customer['licenses_used']
			);
			fputcsv($out, $csvRow, ',', '"');
		}
	}
	fclose($out);
}
exit;