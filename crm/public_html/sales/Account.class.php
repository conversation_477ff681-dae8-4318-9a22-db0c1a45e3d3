<?php

/*
 * Helper class for creating accounts.
 */

class Account {

	private $cstId = false;
	private $accountId = false;

	public function __construct( $cstId, $accountId = false ) {
		if ( !is_numeric( $cstId ) || $cstId <= 0  ) {
			throw new Exception('Invalid customer id');
		}
		if ( is_numeric( $accountId ) && $accountId > 0 ) {
			$this->accountId = (int) $accountId;
		}
		$this->cstId = (int) $cstId;
	}

	public function create( array $values ) {

		global $aDefaultModuleMappings;

		$query = 'INSERT INTO ca.accounts '
			. 'SET '
			. ' account_email = "' . mysql_real_escape_string( $values['ca_email'] ) . '"'
			. ',account_username = "' . mysql_real_escape_string( $values['ca_username'] ) . '"'
			. ',account_password = PASSWORD("' . mysql_real_escape_string( $values['password'] ) . '")'
			. ',account_login_type = 1'
			. ',account_product_type = "' . (int) $values['ca_product_type'] . '"'
			. ',modules = "' . (int) @$aDefaultModuleMappings[$values['ca_product_type']]['modules'] . '"'
			. ',show_modules = "' . (int) @$aDefaultModuleMappings[$values['ca_product_type']]['show_modules'] . '"'
			. ',account_recv_all = 1'
			. ',account_expires = "' . mysql_real_escape_string( $values['ca_expires'] ) . '"'
			. ',cst_id = "' . (int) $this->cstId . '"'
			. ',account_gen_pwd = 1'
			. ',lang_id = 1'
			. ',ca_lang_id = 1'
			;

        $status = DBQuery($query);

		if ( !$status ) {
			$this->log( mysql_error() );
			return false;
		}

		$accountId = mysql_insert_id();
		if ( $accountId > 0 ) {
			$this->accountId = $accountId;
		}

		return $this->accountId;
	}

	public function updateLicenseId( $licenseId, $force = false ) {
		$query = 'UPDATE ca.accounts SET license_id = ' . (int) $licenseId . ' WHERE account_id = ' . (int) $this->accountId;
		if ( !$force ) {
			$query .= ' AND license_id = 0';
		}
        $status = DBQuery($query);
		return $status ? true : false;
	}

	public function populateCsi6Data( array $values ) {

		// Update Special Limits
		DBQuery("UPDATE ca.accounts SET special_limits = 'csi_60' WHERE account_id = " . $this->accountId );

		// Store 'CSI Base Setting' for a trial
		mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $this->accountId . "', '" . $this->cstId . "', 0, 3, 1)");

		// Create 1 host license

		$sHostLicKey = fGenerateLicenseKey("BCDFGHJKLMNPQRSTVWXYZ");

		DBQuery("INSERT INTO ca.license_keys SET " .
				"license = '" . mysql_real_escape_string( $sHostLicKey ) . "', " .
				"account_id = '" . (int) $this->accountId . "', " .
				"created = UTC_TIMESTAMP(), " .
				"valid_from = created, " .
				"valid_to = '" . mysql_real_escape_string( $values['license_valid_til'] ) . " 23:59:59', " .
				"activated = created, " .
				"quantity = '1', " .
				"license_type = '1', " . // Hosts License
				"type = 32"
				);

	}

	/**
	 * Inserting the csi 7 licenses (only for partition 0)
	 */
	public function insertCsi7Licenses( $values ) {
		$status = DBQuery('INSERT INTO ca.partition_license_pool SET '
						  . 'cst_id = "' . (int) $this->cstId . '"'
						  . ',partition_id = 0'
						  . ',license_type = ' . (int) $values['license_type']
						  . ',num_granted = ' . (int) $values['num_granted']
						  . ',num_available = ' . (int) $values['num_available']
						  . ',modified = UTC_TIMESTAMP()'
						  . ',expiry = "' . mysql_real_escape_string( $values['expiry'] ) . ' 23:59:59"'
						  );

		if ( !$status ) {
			$this->log( mysql_error() );
			return false;
		}
		return mysql_insert_id();
	}

	public function populateCsi7Data( array $values ) {

		// Store 'CSI Base Setting' for a trial
		// @todo: not sure if we still need grouping_type, s_unique_hosts, s_trial (copied from the populateCsi6Data function )
		mysql_query("INSERT INTO ca.nsi_base_settings (account_id, cst_id, grouping_type, s_unique_hosts, s_trial) VALUES('" . $this->accountId . "', '" . $this->cstId . "', 0, 3, 1)");

		$values['license_type'] = 1; // Host License
		$values['num_granted'] = 1;
		$values['num_available'] = 1;
		$this->insertCsi7Licenses( $values );


		$values['license_type'] = 2; // User License
		$values['num_granted'] = 1;
		$values['num_available'] = 0;
		$licenseId = $this->insertCsi7Licenses( $values ); /* license_id
															  column
															  in
															  accounts
															  has been
															  deprecated. Remove
															  later */

		$values['license_type'] = 3; // Partition Licenses
		$values['num_granted'] = 1;
		$values['num_available'] = 0;
		$this->insertCsi7Licenses( $values );


		// Update CSI 7 specific columns in the accounts table
		$status = DBQuery("UPDATE ca.accounts"
				. " SET is_partition_admin = 1"
				. ", is_admin = 1"
				// . ", license_id = " . (int) $licenseId // column deprecated
				. ", recipient_email = '" . mysql_real_escape_string( $values['email'] ) . "'"
				. ", special_limits = 'csi_70'"
				. ", max_licensed_devices = -1" // No limit on usage from pool
				. ", max_licensed_accounts = -1" // No limit on usage from pool
				. " WHERE account_id = " . (int) $this->accountId
				. " AND cst_id = " . (int) $this->cstId  );

		if ( !$status ) {
			$this->log( mysql_error() );
			return false;
		}

		// Create a process so that the private database is initialized when the cronjob runs
		$status = DBQuery('INSERT INTO ca.process SET '
						  . 'cst_id = "' . (int) $this->cstId . '"'
						  . ',process_type = 1' // New Customer
						  );

		if ( !$status ) {
			$this->log( mysql_error() );
			return false;
		}

		return true;
	}

	public function populateVim4Data( array $values ) {

		DBQuery(
				"UPDATE ca.accounts SET special_limits = 'vim_40', account_version = 'vim_40'"
				. ", account_product_type = '" .  Product::VIM4 . "'"
				. ", account_options = '".(int) $values['account_options'] ."'"
				. ", xml_access = '0' WHERE account_id = '" . (int) $this->accountId . "' LIMIT 1"
				);

		$name = '';

		// Try to get an existing name if one exists
		$result = DBGetRow( 'crm.contacts', "cst_id = '" . $this->cstId . "' AND primary_contact = 1" );
		if ( false !== $result ) {
			$name = $result['name'];
			if ( $name ) {
				DBQuery( "UPDATE ca.accounts SET account_name = '" . mysql_real_escape_string( $name ) . "' WHERE account_id = '" . (int)$this->accountId . "' LIMIT 1");
			}
		}

		// VIM 4.0 - Update contact info
		DBQuery("INSERT INTO ca.contact_method SET " .
				"contact_method_value = '" . mysql_real_escape_string( $values['email'] ) . "', " .
				"contact_method_type = 1, " .
				"account_id = '" . (int) $this->accountId . "', " .
				"name = '" . mysql_real_escape_string( $name ) . "', " .
				"pos = '1', " .
				"lang_eng = '1'"
				);

		// Licenses
		DBQuery("INSERT INTO ca.esm SET esm.no_users = 5, esm.master_account_id = '" . $this->accountId . "'");

	}

	public static function getByAccountId( $accountId ) {
		return DBGetRow( 'ca.accounts' , 'account_id = "' . (int) $accountId . '"' );
	}

	private function log( $msg ) {
		error_log( $msg );
	}
}