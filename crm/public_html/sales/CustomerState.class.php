<?php

  /**
   * Helper class to display version specific details
   */

class CustomerState {

	private $cstId;

	public function __construct( $cstId ) {
		if ( !$cstId || !is_numeric( $cstId ) ) {
			throw new Exception( 'Invalid Customer Id' );
		}
		$this->cstId = (int) $cstId;
	}

	private function getVersionInfo() {
		 $query = "SELECT
		 	  cst_id, csi_version, patch_version, locked, lock_date
		 	FROM ca.csi_pdb_info
		 	WHERE cst_id = '" . $this->cstId . "'";
		 $res = DBQuery( $query );
		 if ( !$res ) {
			 return false;
		 }
		 $row = mysql_fetch_array( $res );
		 return $row;
	}

	private function getMigrationInfo() {
		 $query = "SELECT
			  status, process_type, start_dtm, end_dtm
		 	FROM ca.process
		 	WHERE cst_id = '" . $this->cstId . "'";
		 $res = DBQuery( $query );
		 if ( !$res ) {
			 return false;
		 }
		 $row = mysql_fetch_array( $res );
		 return $row;
	}

	/**
	 * Since the function is private, we expect that all inputs are
	 * valid html and we won't use htmlspecialchars in the function
	 *
	 * @param string $msg String to format as html
	 */
	private static function msg( $msg ) {
		echo date("Y-m-d H:i:s") . " - ". $msg . "<br />";
	}

	public function display() {
		$msg = '';
		 /*
		  * Check the state of migrations
		  */
		 $versionInfo = $this->getVersionInfo();
		 $migrationInfo = $this->getMigrationInfo();

		 /*
		  * If version info is not found and migration process info is
		  * there, tell the user that something is wrong.
		  */
		 if ( empty( $migrationInfo ) ) {
			 $this->msg( 'No information found related to the upgrades/migrations (maybe it was never scheduled).' );
			 return false;
		 } else {
			 // We only care about the version info if the migration info is there.
			 if ( empty( $versionInfo ) ) {
				 $this->msg( 'No version information found related to the customer.<br/>' );
			 } else {
				 $msg .= 'Current CSI Version: ' . htmlspecialchars( $versionInfo['csi_version'] ) . ' and patch version: ' . htmlspecialchars( $versionInfo['patch_version'] ) . '.' ;
				 $this->msg( $msg );
			 }
		 }

		 switch( $migrationInfo['process_type'] ) {
		 case '1':
			 $msg = 'New customer account creation';
			 break;
		 case '2':
			 $msg = 'Trial account creation';
			 break;
		 case '3':
			 $msg = 'Migration';
			 break;
		 case '4':
			 $msg = 'Duplication';
			 break;
		 }

		 switch( $migrationInfo['status'] ) {
		 case 0:
			 $msg .= ' not started yet.';
			 break;
		 case 1:
			 $msg .= ' started at "' . htmlspecialchars( $migrationInfo['start_dtm'] ) . '" and is still running...';
			 break;
		 case 2:
			 $msg .= ' started at "' . htmlspecialchars( $migrationInfo['start_dtm'] ) . '" and ended at "' . htmlspecialchars( $migrationInfo['end_dtm'] ) . '".';
			 break;
		 default:
			 $msg .= ' has an unknown status. Please contact IT.';
		 }
		 if ( $versionInfo['locked'] == 1 ) {
			 $this->msg( 'The process is currently stuck due to a problem in one of the patches.' );
		 }
		 $this->msg( $msg );

		 return true;
	}
}