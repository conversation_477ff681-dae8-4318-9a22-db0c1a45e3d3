<?php

/**
 * @file _common_module.php
 * @requires CommonModule.class.php
 * @requires CommonModules.class.php
 * This file outputs a list of ca.module row and is included by ca_account_management.php
 */

require_once INCLUDE_PATH . 'CommonModule.class.php';
require_once INCLUDE_PATH . 'CommonModules.class.php';
// Assign a db connection to work with
CommonModules::setConn( $GLOBALS[ DB_HOST_CRM . DB_USER_CRM ] );
?>
	</table>
	<!-- Common DB Modules -->
	<table>
		<tr>
			<th colspan="3" class="TableHeadline">Modules</th>
		</tr>
<?php
	$toggleDivId = 0; // id used for setting toggleable DIV's id
	$cstId = $aAccountCustomer['cst_id'];
	$modules = CommonModules::getAllIncludingStatus( $cstId, $iAccountID );

	#########################
	# LIST CUSTOMER MODULES #
	#########################
	$odd = 1; // used for odd row colouring
	//$customerModules = array_filter( $modules, create_function( '$v', 'return $v->getAppliesTo() & CommonModules::APPLIES_TO_CUSTOMER;' ) );
	$customerModules = array_filter( $modules, function($v) {return $v->getAppliesTo() & CommonModules::APPLIES_TO_CUSTOMER;});
	if ( count($customerModules) ) {
		?>
		<tr>
			<th>Customer Modules <span class="note-message">(These apply to all of the Customer's Accounts)</span></th>
			<th>Enabled</th>
			<th>Displayed</th>
		</tr>
		<?php
		foreach ( $customerModules as $module ) {
			if (
				$module->getId() == CommonModules::MOD_CSI_ZERO_DAY
				&& $module->getEnabled() == false
			) {
				continue;
			}
			
			include '_common_module.php';
		}
	}

	#########################
	# LIST ACCOUNT MODULES  #
	#########################
	$odd = 1; // used for odd row colouring
	//$accountModules = array_filter( $modules, create_function( '$v', 'return $v->getAppliesTo() & CommonModules::APPLIES_TO_ACCOUNT;' ) );
	$accountModules = array_filter( $modules, function($v) {return $v->getAppliesTo() & CommonModules::APPLIES_TO_ACCOUNT;});
	if ( count($accountModules) ) {
		?>
		<tr>
			<th>Account Modules <span class="note-message">(These apply to a single Account)</span></th>
			<th>Enabled</th>
			<th>Displayed</th>
		</tr>
		<?php
		foreach ( $accountModules as $module ) {
			include '_common_module.php';
		}
	}
?>
