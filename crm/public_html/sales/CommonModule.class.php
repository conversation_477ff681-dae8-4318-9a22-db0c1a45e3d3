<?php

/**
 * @file CommonModule.class.php
 * This file contains the CommonModule class
 */


/**
 * Class CommonModule
 * Represents a row from the `modules` table with additional methods and fields
 * used for binding the Module to customer or account owners.
 *
 * To add a new Module you just have to insert it in to the ca.modules table.
 * It will then become available to the CRM to be enabled and disabled:
 *
 * INSERT INTO ca.modules (id, code) VALUES (3, 'CSI_SCCM_PLUGIN');
 *
 * Some modules apply at the Account level and some apply at the Customer level.
 * The ca.modules_accounts table and the ca.modules_customers tables handle this.
 * To specify where a module binds and other information about the module like it's
 * name and description modify it's entry in CommonModules::addModuleDescriptions(..)
 *
 * @todo TODO Have extra per-module info stored in the table and not the code
 *
 * @package CRM
 * @subpackage Modules
 * @date 10.17.2012
 * <AUTHOR>
 */
class CommonModule {

	/**
	 * @var int
	 * Module id
	 */
	private $_id = null;
	/**
	 * @var int
	 * Module unique ID
	 */
	private $_code = '';
	/**
	 * @var int
	 * Module's name (in english)
	 */
	private $_name = '';
	/**
	 * @var string
	 * Module's description (in english)
	 */
	private $_description = '';
	/**
	 * @var int (bitmask)
	 * What the module applies to
	 *	1	: customers (cst_id)
	 *	2	: accounts (account_id)
	 *	3	: customers + accounts
	 */
	private $_applies_to = 0;
	/**
	 * @var int
	 *  = 1 if this Module is enabled, =0 is not enabled for the account/cstid
	 */
	private $_enabled = 0;
	/**
	 * @var int
	 * If this Module is also Customer id of
	 */
	private $_cst_id = null;
	/**
	 * @var string
	 * Version support string, ie: 'CSI6+'
	 */
	private $_versionSupport = '';
	/**
	 * @var int
	 * Account id
	 */
	private $_account_id = null;

	/**
	 * Constructor
	 */
	public function __construct( $array = array() )
	{
		$this->fromArray( $array );
	}

	/**
	 * Hydrate the Module from an array
	 *
	 * @param array $array
	 */
	public function fromArray( $array )
	{
		foreach ( $array as $key => $val ) {
			$this->{"_{$key}"} = $val;
		}
	}
	/**
	 * Returns a copy of the object's properties in an array
	 *
	 * @return array
	 */
	public function toArray()
	{
		return (array) $this;
	}

	/**
	 * Gets the Module's ID
	 *
	 * @return int
	 */
	public function getId()
	{
		return $this->_id;
	}
	/**
	 * Gets the Module's code. The code is the module's unique string identifier
	 *
	 * @return string
	 */
	public function getCode()
	{
		return $this->_code;
	}
	/**
	 * Gets the Module's name
	 *
	 * @return string
	 */
	public function getName()
	{
		return $this->_name;
	}
	/**
	 * Gets the Module's description
	 *
	 * @return string
	 */
	public function getDescription()
	{
		return $this->_description;
	}
	/**
	 * Gets what the mod applies to
	 *
	 * @return int
	 *	1	: customers (cst_id)
	 *	2	: accounts (account_id)
	 *	3	: customers + accounts
	 */
	public function getAppliesTo()
	{
		return $this->_applies_to;
	}
	/**
	 * Gets the Customer ID bound to this Module if there is one assigned.
	 *
	 * @return int
	 */
	public function getCstId()
	{
		return $this->cst_id;
	}
	/**
	 * Gets the Customer ID bound to this Module if there is one assigned.
	 *
	 * @return int
	 */
	public function getAccountId()
	{
		return $this->account_id;
	}

	/**
	 * Gets the Module's version support string
	 *
	 * @return string
	 */
	public function getVersionSupport()
	{
		return $this->_versionSupport;
	}
	/**
	 * Returns whether this Module is enabled for the current user
	 *
	 * @return int
	 */
	public function getEnabled()
	{
		return $this->_enabled;
	}
	/**
	 * Returns a bitmask which is used to identify the visibility of this Module
	 * The first 30 Modules can store their visibility. After that it will no
	 * longer be recorded
	 *
	 * @int unsigned
	 */
	public function getVisibilityBitmask()
	{
		if ( $this->_id > 30 ) { // modules 1 - 30 use visibility
			return 0;
		}
		return sprintf( '%u', 1 << (13 + $this->_id) );
	}

	/**
	 * Checks if this module is enabled for an account
	 *
	 * @param array $account
	 * @return bool
	 */
	public function isEnabledForAccount( $account )
	{
		// Check if the module is enabled for the Account
		if ( CommonModules::APPLIES_TO_ACCOUNT & $this->getAppliesTo() ) {
			$res = CommonModules::dbQuery( "SELECT EXISTS( SELECT 1 FROM ca.modules_accounts WHERE account_id = '{$account['account_id']}' AND module_id = '{$this->getId()}')" );
			if ( $res ) {
				if ( ($row = mysql_fetch_row( $res )) && reset($row) ) {
					return true;
				}
			}
		}
		// Else check if it's enabled for the Customer
		if ( CommonModules::APPLIES_TO_CUSTOMER & $this->getAppliesTo() ) {
			$res = CommonModules::dbQuery( "SELECT EXISTS( SELECT 1 FROM ca.modules_customers WHERE cst_id = '{$account['cst_id']}' AND module_id = '{$this->getId()}')" );
			if ( $res ) {
				if ( ($row = mysql_fetch_row( $res )) && reset($row) ) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Sets the Module's name
	 *
	 * @param string $val
	 */
	public function setName( $val )
	{
		$this->_name = $val;
	}
	/**
	 * Sets the Module's description
	 *
	 * @param string $val
	 */
	public function setDescription( $val )
	{
		$this->_description = $val;
	}
	/**
	 * Sets what the mod applies to
	 *
	 * @param int $val Bitmask
	 *	1	: customers (cst_id)
	 *	2	: accounts (account_id)
	 *	3	: customers + accounts
	 */
	public function setAppliesTo( $val )
	{
		$this->_applies_to = $val;
	}
	/**
	 * Sets the Module's version support string
	 *
	 * @param string $val
	 */
	public function setVersionSupport( $val )
	{
		$this->_versionSupport = $val;
	}

}
