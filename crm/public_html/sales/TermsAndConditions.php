<?php

class TermsAndConditions {
	protected $cstId;

	public function __construct( $cstId ) {
		if ( !$cstId || !is_numeric( $cstId ) ) {
			throw new Exception( 'Invalid Customer Id' );
		}
		$this->cstId = (int) $cstId;
	}

	public function getTermsMessage(){
		$terms = DBQueryGetRows("SELECT terms_accepted, terms_enabled FROM ca.customers WHERE id = " . (int) $this->cstId . " LIMIT 1");
		$terms = $terms[0];
		$output = array();

		if ( $terms['terms_enabled'] && !$terms['terms_accepted'] ) {
			$termsFile = $this->getTermsFile( );
			$output['msg'] = 'Current terms (<a href="../terms_and_conditions/getfile.php?id=' . $termsFile['terms_id'] . '">' . $termsFile['name'] . '</a>) are <strong>enabled</strong> and <strong>not accepted</strong>.';
			$output['enabled'] = 1;
			$output['accepted'] = 0;
		} else if ( $terms['terms_enabled'] && $terms['terms_accepted'] ) {
			$termsFile = $this->getTermsFile();
			$log = $this->getAcceptedLog();
			$output['msg'] = 'Current terms (<a href="../terms_and_conditions/getfile.php?id=' . $termsFile['terms_id'] . '">' . $termsFile['name'] . '</a>) have been accepted<br />on <strong>' . $log['created_at'] . '</strong><br /> by user <strong>' . $log['account_username'] . '</strong> from IP <strong>' . $log['ip_address'] . '</strong>.';
			$output['enabled'] = 1;
			$output['accepted'] = 1;
		} else if ( !$terms['terms_enabled'] ) {
			$output['msg'] = 'No terms set for customer.';
			$output['enabled'] = 0;
			$output['accepted'] = 0;
		} else {
			$output['msg'] = 'Unknown state';
			$output['enabled'] = 0;
			$output['accepted'] = 0;
		}

		return $output;
	}

	protected function getTermsFile() {
		$file = DBQueryGetRows("SELECT name, terms_id
			FROM ca.customers
			JOIN ca.terms ON ca.terms.id = ca.customers.terms_id
			WHERE ca.customers.id = " . (int) $this->cstId . " LIMIT 1");

		return $file[0];
	}

	const ACTION_VIEWED = 1;
	const ACTION_ACCEPTED = 2;

	protected function getAcceptedLog() {
		$log = DBQueryGetRows('SELECT created_at, account_username, INET_NTOA(ip_address) AS ip_address
			FROM ca.terms_log
			WHERE cst_id = ' . (int) $this->cstId . '
			AND action = ' . (int) self::ACTION_ACCEPTED . '
			ORDER BY id DESC
			LIMIT 1');

		return $log[0];
	}

	public function getLogs() {
		return DBQueryGetRows('SELECT account_id, account_username, DATE_FORMAT(created_at, "%d.%m.%Y - %H:%i:%s") AS created_at, INET_NTOA(ip_address) AS ip_address, action, terms_id, name
			FROM ca.terms_log
			JOIN ca.terms ON ca.terms.id = ca.terms_log.terms_id
			WHERE cst_id = ' . (int) $this->cstId . '
			ORDER BY ca.terms_log.id DESC');
	}

	public function removeTerms() {
		return DBQuery( 'UPDATE ca.customers SET terms_enabled = 0, terms_accepted = 0 WHERE id = ' . (int) $this->cstId . ' LIMIT 1' );
	}

	public function getAllTerms() {
		return DBQueryGetRows( 'SELECT id, name FROM ca.terms ORDER BY id DESC' );
	}

	public function assignTerms( $termsId ) {
		$customer = DBQuery( 'INSERT INTO ca.customers (id, terms_accepted, terms_enabled, terms_id) VALUES (' . (int) $this->cstId . ',0,1,'. (int) $termsId .') ON DUPLICATE KEY UPDATE terms_accepted = 0, terms_enabled = 1, terms_id = ' . (int) $termsId );
		if ( !$customer ) {
			return false;
		}

		return true;
	}
} 