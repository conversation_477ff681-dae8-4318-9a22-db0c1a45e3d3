 sfw.Panel = sfw.extend( Ext.Panel, function()  {
     return {
         constructor: function( config ) {
             Ext.applyIf( config, {
                 frame: false,
                 border: false,
                 layout: 'fit',
                 tabTip: config.title ? config.title : ''
             });
             if ( typeof config.region !== "undefined" ) {
                 switch ( config.region ) {
                     case "west":
                         config.collapsible = (typeof config.collapsible !== 'undefined' ? config.collapsible : true);
                         config.split = (typeof config.split !== 'undefined' ? config.split : true);
                         config.width = config.width || 250;
                     break;
                     case "center":
                         config.split = (typeof config.split !== 'undefined' ? config.split : true);
                     break;
                     case "south":
                         config.split = (typeof config.split !== 'undefined' ? config.split : true);
                     break;
                 }
             }
             sfw.Panel.superclass.constructor.call(this, config);
         }
     };
 });
