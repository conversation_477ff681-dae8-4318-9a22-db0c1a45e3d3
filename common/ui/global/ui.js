/**
 * @file ui.js
 * Provides default UI layout
 *
 * Configuration options:
 * ui: {
 *	menu: Ext tree panel OPTIONAL
 *	,center: Ext panel OPTIONAL
 *	,interface: Ext viewport OPTIONAL
 *	,menuTitle: 'string'
 *	,home: name of the 'home' page object
 *	,render: function to be called when the main interface is rendered
 *	,afterrender: function to be called after the load mask has been hidden and the user interface has been rendered
 *	,skipCache: boolean true of false to skip chaching or not. If NOT it will IMPROVE the ui.
 *	,south: optional south region, or any kind of panel
 * }
 *
 * Optional functionality ( if providers are loaded ):
 *
 * navigation -> see navigation.js
 *
 * printPage -> see print.js
 *
 * loginWindow -> see loginWindow.js
 *
 * toptoolbar -> see toptoolbar.js
 *
 * Provides a globally available function called activePage. The sfw.util.activePage is only used internally
 *
*/

sfw.ui = {
	/**
	 * @property {String}
	 * Contains the ID of the currently selected Menu Item.
	 * - When CSI loads, this property is initially set to the Home Page
	 * 	in side of {@link sfw.ui#create}.
	 * - When a user clicks on a menu item to change the Page then this
	 *	property is set to the Page's ID in {@link sfw.ui#menuChange}
	 */
	 activeMenuItem : ""
};
sfw.isReady = false;
sfw.ui.pages = {};
sfw.tmp = {}; // Load a generic purpose temp object

/*
 * Auxiliary Viewport i.e. Secondary / Helper Viewport
 * Needs to be setup in the local project
 */
sfw.ui.auxViewport = {};

sfw.doLogin = function() {
	sfw.loginWindow.create();
	if (sfw.isBrowser || sfw.isDesktop){
		sfw.checkSSO();
	} else {
		sfw.loginWindow.checkLogin();
	}
};

/**
 * Called by ExtJS.onLoad, the ui.create() function initializes the main UI layout, and it's objects
 * @return
 * The main UI page container
*/
sfw.ui.create = function() {
	try {

		/**
		 * Load the main configuration object.
		*/
		var uiConfig = sfw.configuration.ui;
		if ( typeof uiConfig === "undefined" ) {
			uiConfig = {};
			sfw.configuration.ui = {};
		}

		if ( typeof uiConfig.skipCache === "undefined" ) {
			uiConfig.skipCache = false;
			sfw.configuration.ui.skipCache = false;
		}

		// Fire the beforerender event
		sfw.events.fireEvent( 'beforerender' );

		/**
		* Build the objects registered in the page stack ( ui.pages )
		*/
		this.pageArray = sfw.util.buildPageArray( sfw.ui.pages );
		// Fire an event to notify listeners that the ui.create has built the page array.
		// This firing is not done at the tail end of buildMenuArray() because it's not expected
		// that buildPageArray is called at 2 separate locations like it was was in CSI6 due to Shadow Users
		// TODO - investigate potential changes now that shadow users are gone
		sfw.events.fireEvent( "afterbuildpagearray", this.pageArray );
		this.menuArray = sfw.util.buildMenuArray( sfw.ui.pages );

		/**
		* Check if navigation mechanism has been loaded, and create it.
		*/
		if ( typeof sfw.navigation !== "undefined" ) {
			sfw.navigation.create();
		}

		/**
		* Check if print mechanism has been loaded, and create it.
		*/
		if ( typeof sfw.printPage !== "undefined" ) {
			sfw.printPage.create();
		}

		/**
		* Check if toptoolbar mechanism has been loaded, and create it. Otherwise create an empty panel.
		*/
		this.toptoolbar = ( typeof sfw.toptoolbar !== "undefined" ) ? sfw.toptoolbar.create() : new sfw.Panel({ region: 'north' });

		/**
		* Check for a south region.
		*/
		if ( typeof uiConfig.south !== "undefined" ) {
			this.south = uiConfig.south.create();
		}

		/**
		 * @method sfw.ui.menuChange()
		 *	- Switches to the selected page
		 *	- Switches the page description
		 * @public
		 * @param {String} pageId
		 */
		this.menuChange = function( pageId ) {
			// Record the ID of the menu item which was clicked. This is used
			// for the Help system
			sfw.ui.activeMenuItem = pageId;

			var currentPage = sfw.ui.pages[pageId];
			if ( !currentPage ) {
				return false;
			}
			// else...
			var title = currentPage.title;

			var switched = sfw.util.switchPage( sfw.ui.center, currentPage );

			// Also change the description panel:
			//	1.	If variable set in the configuration file
			//	2.	If the page was also successfully switched

			if ( sfw.configuration.ui.displayDescription && switched ) {
				var currentPageDescription = false;
				try {
					currentPageDescription = currentPage.getDescription();
				} catch ( ex ) {
					sfw.debug.trigger( ex, 'sfw.ui.menuChange()', 'Unable to get the current page description' );
				}
				if ( currentPageDescription && title ) {
					sfw.util.switchPageDescription( sfw.ui.description, currentPageDescription, title);
				} else {
					sfw.util.switchPageDescription( sfw.ui.description, sfw.ui.getDefaultDescription(), sfw.ui.defaultDescriptionTitle );
				}
			}
			return true;
		};

		/**
		* Construct the left side menu entries.
		*
		* The nodes, must have a corresponding items within the page stack. ( with the same ID ).
		*/
		this.menu = uiConfig.menu || new sfw.TreePanel({
			id: 'mainCSImenu'
			,useArrows: true
			,region: 'center'
			,clearOnLoad: false
			,root: new Ext.tree.AsyncTreeNode({
				expanded: true
				,children: this.menuArray
			})
			,listeners: {
				/**
				 * @method
				 * Called when one of the main page menu items is clicked on
				 * @param {Ext.tree.TreeNode} node
				 */
				click: function( node ) {
					var pageId = node.id;
					sfw.ui.menuChange( pageId );
				}
				/**
				 * @method
				 * Called when one of the main page menu items is right-clicked on
				 * To add a contextmenu to your existing page just define a .contextMenu
				 * property which contains the sfw.Menu({}) instance you would like to show.
				 *
				 * @example
				 * 	csiMyPage.init = function () {
				 * 		this.id = "sfw.csiMyPage";
				 * 		this.menu = {
				 * 			text: "My Page"
				 * 		};
				 *
				 * 		// Create a context menu when someone Right-clicks this menu item
				 * 		this.contextMenu = new sfw.Menu({});
				 * 		this.contextMenu.add( [{
				 * 			text: "My Menu Option"
				 * 			,handler: function() {
				 * 				alert( "Clicked!" );
				 * 			}
				 * 		}] );
				 * 	}
				 *
				 * @param {Ext.tree.TreeNode} node
				 * @param {Ext.EventObject} event
				 */
				,contextmenu: function ( node, event ) {
					if ( sfw.ui.pages[node.id] && sfw.ui.pages[node.id].contextMenu ) {
						sfw.ui.pages[node.id].contextMenu.showAt( event.xy );
					}
				}
				,afterrender: function ( that ) {
					if ( sfw.configuration.ui.displayDescriptionOnHover ) {
						that.root.cascade( sfw.util.addTipsToMenu );
					}
				}
			}
		});

		this.defaultDescriptionTitle = ' - ';

		this.getDefaultDescription = function() {
			var description = new sfw.Panel({
				html: ' - '
				,cls: 'ContentPadding'
			});
			return description;
		};

		this.logo = new sfw.Panel({
			baseCls: 'SecuniaLogo'
			,height: 80
			,hidden: ( sfw.configuration.ui.displayLogo ? false : true )
		});

		this.description = new sfw.Panel({
			title: this.defaultDescriptionTitle // Dynamically update the title - // ENHANCEMENT: change the default title
			,region: 'south'
			,layout: 'anchor'
			,collapsible: true
			,collapsed: false
			,autoScroll: true
			,hidden: ( sfw.configuration.ui.displayDescription ? false : true )
			,items: this.defaultDescription // ENHANCEMENT: change the default item
		});

		/**
		 * Load temp mask, until the main container is done loading data.
		*/
		this.preLoader = function( hide ) {
			if ( typeof hide !== "undefined" && hide == true ) {
				this.mask.hide();
				// This is where we call the user defined afterrender event
				if ( typeof uiConfig.afterrender !== "undefined" ) {
					uiConfig.afterrender();
				}
			} else {
				var actionMask = new Ext.LoadMask(Ext.getBody(), { msg: 'Loading UI...' });
				this.mask = actionMask;
				this.mask.show();
			}
		};

		if ( typeof uiConfig.center === "undefined" ) {
			this.preLoader();
		}

		var pageStack = [];
		if ( uiConfig.skipCache === false ) {
			pageStack = this.pageArray;
		}

		/*
		 *	sfw.ui.tabChange()
		 *
		 *	- updates the page description, if enabled
		 *	- selects the appropriate menu item
		 *
		 *	ENHANCEMENT: should the navigation history be affected on selection of a tab?
		 */
		this.tabChange = function ( panelId ) {
			try {
				// Select the node from the menu
				sfw.util.selectNode( panelId );

				var currentPage = sfw.ui.pages[ panelId ];

				// Also switch the description panel
				if ( sfw.configuration.ui.displayDescription ) {
					var currentPageDescription = false;
					var title = false;
					try {
						currentPageDescription = currentPage.getDescription();
						title = currentPage.title;
					} catch ( ex ) {
						//Silent
					}
					if ( currentPageDescription && title ) {
						sfw.util.switchPageDescription( sfw.ui.description, currentPageDescription, title);
					} else {
						sfw.util.switchPageDescription( sfw.ui.description, sfw.ui.getDefaultDescription(), sfw.ui.defaultDescriptionTitle );
					}
				}
				return true;
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'sfw.TabPanel - tabChange event' );
			}
			return false;
		};

		var item = sfw.TabPanel;
		var panel = null;
		if ( !uiConfig.tabs ) {
			panel = new sfw.Panel({
				items: pageStack
				,hideMode: 'visibility'
				,layout: 'card'
				,listeners: {
					afterrender: this.preLoader.createDelegate( this, [ true ] )
				}
			});
		} else {
			panel = new sfw.TabPanel({
				items: pageStack
				,hideMode: 'visibility'
				,listeners: {
					afterrender: this.preLoader.createDelegate( this, [ true ] )
					,tabchange: function( tabPanel, currentPanel ) {
						if ( currentPanel && currentPanel.id ) {
							sfw.ui.tabChange( currentPanel.id );
						}
					}
					,remove: function ( tabPanel, panel ) {
						if ( sfw.ui.pages[panel.id] ) {
							panel.hide();
							if ( tabPanel.items.getCount() === 0 ) {
								tabPanel.activeTab = null;
								tabPanel.layout.activeItem = null;
							}
						}
					}
				}
			});

			// Keyboard navigation for the TabPanel, if enabled in the ui configuration
			if ( uiConfig && uiConfig.tabbedKeyboardNavigation ) {
				var handleKeyboardNavigation = function( isForward ) {
					try {
						var tabs = panel.items;
						var activeTab = panel.getActiveTab();
						for ( var i = 0 ; i < tabs.length ; i++ ) {
							if ( tabs.items[i].getId() == activeTab.getId() ) {
								var newIndex = 0;
								if ( !isForward ) {
									newIndex = ( (i-1) < 0 ? (tabs.length-1) : (i-1) );
								} else {
									newIndex = ( tabs.length > (i+1) ? (i+1) : 0 );
								}
								panel.setActiveTab( newIndex );
							}
						}
					} catch ( ex ) {
						// unhandled expection
					}
				};
				var handleBack = function() {
					handleKeyboardNavigation( false );
				};
				var handleForward = function() {
					handleKeyboardNavigation( true );
				};
				new Ext.KeyMap(document, [
					{
						key: '\t'
						,ctrl: true
						,shift: true
						,handler: handleBack
					},{
						key: '\t'
						,ctrl: true
						,handler: handleForward
					},{
						key: 'w'
						,ctrl: true
						,handler: function() {
							panel.remove(panel.getActiveTab());
						}
					}
				]);
			}
		}

		/**
		* Load the center object: The main page container.
		*/
		this.center = uiConfig.center || panel;

		/**
		* Navigation menu
		*
		* We use .disableWest so don't create the west panel for the SCCM Plugin
		*/
		if ( 'undefined' === typeof uiConfig.disableWest || false === uiConfig.disableWest )  {
			this.west = new sfw.Panel({
				region: 'west'
				,items: [
					new sfw.Panel({
						region: 'center'
						,layout: {
							type: 'vbox'
							,padding: '0'
							,align: 'stretch'
						}
						,items: [
							new sfw.Panel({
								layout: 'border'
								,flex: 1
								,items: [ this.menu, this.description ]
							})
							,this.logo
						]
					})
				]
				,collapsible: (typeof uiConfig.menuCollapsible !== 'undefined' ? uiConfig.menuCollapsible : true)
				,border: (typeof uiConfig.menuBorder !== 'undefined' ? uiConfig.menuBorder : true)
				,frame: (typeof uiConfig.menuFrame !== 'undefined' ? uiConfig.menuFrame : false)
				,layout: ( typeof uiConfig.menuLayout !== 'undefined' ? uiConfig.menuLayout : 'border' )
				,padding: uiConfig.menuPadding
				,width: uiConfig.menuWidth
				,title: uiConfig.menuTitle
				// ,baseCls: uiConfig.menuBaseCls
				,split: uiConfig.menuSplit
				,listeners: {
					bodyresize: function() {
						this.adjustHeights();
					}
				}
				,adjustHeights: function () {
					// The menu tree will stretch and fit. We only need to set the height for the
					// description panel, if it needs to be displayed
					if ( sfw.configuration.ui.displayDescription ) {
						var parentHeight = sfw.ui.west.getHeight();
						var panelHeight = parentHeight * 0.20;  // 20 percent of the parent panel
						sfw.ui.description.setHeight( panelHeight );
					}
				}
			});
		}

		/**
		* Construct the interface.
		*/
		var items = [];
		// SCCM Plugin doesn't use the toptoolbar
		if ( typeof uiConfig.disableToptoolbar == 'undefined' && uiConfig.disableToptoolbar !== false )  {
			items.push( this.toptoolbar );
		}
		// SCCM Plugin doesn't use the west panel
		if ( typeof uiConfig.disableWest == 'undefined' && uiConfig.disableWest !== false )  {
			items.push( this.west );
		}
		items.push(
			new sfw.Panel({
				region: 'center'
				,border: (typeof uiConfig.mainBorder !== 'undefined' ? uiConfig.mainBorder : true)
				,frame: (typeof uiConfig.mainFrame !== 'undefined' ? uiConfig.mainFrame : false)
				,items: this.center
				,layout: (typeof uiConfig.mainLayout !== 'undefined' ? uiConfig.mainLayout : 'fit')
			})
		);
		if ( typeof this.south !== 'undefined' ) {
			items.push( this.south );
		}

		this.interface = uiConfig.interface ||  new sfw.ViewPort({
			listeners: {
				render: uiConfig.render || function() {}
			}
			,items: items
		});

		/**
		* Load the home page, if defined, otherwise load the first page added in the CARD panel ( assuming the this.center object has not been overwritten through config )
		*/
		var node;

		if ( typeof uiConfig.home !== "undefined" && typeof sfw.tmp.jumpTo === "undefined" ) {
			sfw.util.switchPage( this.center, sfw.ui.pages[uiConfig.home] );
			sfw.util.selectNode( uiConfig.home );
			node = sfw.ui.menu.getNodeById( uiConfig.home );
			if ( typeof node !== "undefined" ) {
				node.select();
			}
		} else if ( sfw.tmp.jumpTo ) {
			sfw.util.switchPage( this.center, sfw.ui.pages[sfw.tmp.jumpTo] );
			sfw.util.selectNode( sfw.tmp.jumpTo );
			node = sfw.ui.menu.getNodeById( sfw.tmp.jumpTo );
			if ( typeof node !== "undefined" ) {
				node.select();
			}
		}

		// Set the value of activeMenuItem to the home page so it's valid when
		// CSI first opens without the user needing to click on a menu item
		if ( "undefined" !== typeof sfw.configuration.ui.home ) {
			sfw.ui.activeMenuItem = sfw.configuration.ui.home;
		}

		/**
		* Load the description page of the page loaded above
		*/
		if ( sfw.configuration.ui.displayDescription ) {
			var homePageDescription = false;
			var homePage = {};
			var homePageTitle = '';
			try {
				if ( typeof uiConfig.home !== "undefined" && typeof sfw.tmp.jumpTo === "undefined" ) {
					homePage = sfw.ui.pages[uiConfig.home];
				} else if ( sfw.tmp.jumpTo ) {
					homePage = sfw.ui.pages[sfw.tmp.jumpTo];
				}

				if ( homePage.getDescription ) {
					homePageDescription = homePage.getDescription();
				}
				if ( homePage.title ) {
					homePageTitle = homePage.title;
				}

			} catch ( ex ) {
				// Silently ignore
			}
			if ( homePageDescription && homePageTitle ) {
				sfw.util.switchPageDescription( sfw.ui.description, homePageDescription, homePageTitle );
			} else {
				sfw.util.switchPageDescription( sfw.ui.description, sfw.ui.getDefaultDescription(), sfw.ui.defaultDescriptionTitle );
			}
		}

		// Fire the render event
		sfw.events.fireEvent( 'render' );

		/**
		* Stub. If the UI should receive a refresh all signal, then loop through all pages.
		*/
		this.refresh = function() {
			// Loop all pages
		};

	} catch ( ex ) {
		sfw.debug.trigger ( ex, "ui.create" );
	}
	return this.interface;
};

sfw.ui.setCenterMaximized = function( maximized ) {
	var items = this.interface.items.items;
	var centerItem = undefined;

	for ( var i = 0, size = items.length; i < size; i++ ) {
		var item = items[i];
		if ( item.region === 'center' ) {
			centerItem = item;
			continue;
		}
		if ( maximized ) {
			item.setVisible( false );
			item.collapse();
		} else {
			item.setVisible( true );
			item.expand();
		}
	}
};