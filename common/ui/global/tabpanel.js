sfw.TabPanel = function( config ) {
	config.frame = config.frame || false;
	config.border = config.border || false;
	//config.layout = (typeof config.layout !== 'undefined' ? config.layout : 'fit' );
	config.enableTabScroll = ( typeof config.enableTabScroll !== 'undefined' ? config.enableTabScroll : true );
	config.defaults = config.defaults || {
		closable: true
	};
	config.layoutOnTabChange = ( typeof config.layoutOnTabChange !== 'undefined' ? config.layoutOnTabChange : true );
	config.deferredRender = config.deferredRender || false;
	config.autoDestroy = config.autoDestroy || false;
	config.minTabWidth = config.minTabWidth || 150;
	config.tabWidth = config.tabWidth || 150;
	config.resizeTabs = false;
	config.defaultType = config.defaultType || 'panel';
	config.width = config.width || '100%'
	return new Ext.TabPanel( config );
}
