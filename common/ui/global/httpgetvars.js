/**
 * @file httpgetvars.js
*/

/**
 * Provides an object somewhat similar to http's HTTP_GET_VARS ( deprecated anyway ).
 * Has different behaviour, compared to he rest of the objects, meaning that this one is loaded, when this JS file is loaded.
 * The final 'shape' of this object is: sfw.HTTP_GET_VARS.param = value;
 * Where param is a GET paramater from the current url.
*/
sfw.HTTP_GET_VARS = {};

/**
 * Initialize the object.
 *
 * This process is supposed to be ugly.
*/
sfw._temp = {};
sfw._temp._currentUrl = window.location.href;
sfw._temp.temp = sfw._temp._currentUrl.split("?");
if ( sfw._temp.temp.length === 2 ) {
	sfw._temp.string = sfw._temp.temp[1].split("&");
	for ( sfw._temp.i = 0; sfw._temp.i < sfw._temp.string.length; sfw._temp.i++ ) {
		sfw._temp.values = sfw._temp.string[sfw._temp.i].split("=");
		if ( sfw._temp.values.length === 2 ) {
			sfw.HTTP_GET_VARS[sfw._temp.values[0]] = sfw._temp.values[1];
		}
	}
}