/**
 * @file forumPostTemplate.js
 *
 * Generic object for creating a new forum post
 *
 */
sfw.forumPostTemplate = {};

sfw.forumPostTemplate.formatQuotes = function( content, threadData, postData, userData ) {

	// Are we quoting the original thread or one of the posts?  Get all the
	// info needed from the quote type
	var regExpPattern = /quote=([pt])([0-9]*)/;
	var temp =  content;
	var quoteType;
	var quoteId;
	var thisUserData;
	var dataToUse = 0;
	var replaceWith;
	var replaceString;

	patternFound = regExpPattern.exec( content );
	while ( patternFound ) {
		replaceString = '[' + patternFound[0] + ']';

		quoteType = patternFound[1];
		quoteId = patternFound[2];
		if ( 't' === quoteType ) {
			if ( quoteId === threadData.id ) {
				dataToUse = threadData;
			}

		} else if ( 'p' === quoteType ) {
			// Then we have to find author of the post with the postId=quoteId
			for ( var i=0; i < postData.length; ++i ) {
				if ( quoteId === postData[i].id ) {
					// Then we found the post to quote
					dataToUse = postData[i];
					break;
				}
			}
		}
		if ( dataToUse ) {
			thisUserData = sfw.forumThreadView.getUserData( userData, dataToUse.author );
			var dateTypeToUse = 'lastedit';
			if ( "0000-00-00 00:00:00" === dataToUse['lastedit'] ) {
				dateTypeToUse = 'posted';
			}
			var date = sfw.util.dateCreate( dataToUse[dateTypeToUse] ).format( globals.dateLongOutput );
			var userName = ( thisUserData.userName ? thisUserData.userName : 'N/A' );

			replaceWith = '<div class=\"ForumQuote\"><b>on ' + date + ', ' + userName + ' wrote:</b><br>';
		} else {
			replaceWith = '<div class=\"ForumQuote\"><b>(unknown source)</b><br>';
		}

		temp = temp.replace( replaceString, replaceWith );
		patternFound = regExpPattern.exec( temp );
	}

	return temp;
}

// Format data to respect our allowed tags and quoting
	sfw.forumPostTemplate.formatPostHtml = function( threadData, postData, index, userData ) {

	var postContents;
	var isFirstPost = false;
	if ( -1 === index ) {
		postContents = threadData.content;
		isFirstPost = true;
	} else {
		postContents = postData[index].content;
	}

	// First replace all the bold and italic tags that we allow
 	var replacements = [ ['[b]' , '<b>']
						 ,['[/b]' , '</b>']
						 ,['[i]' , '<i>']
						 ,['[/i]' , '</i>']
						 ,['[/quote]' , '</div>']
					   ];
	var newContent = postContents;

	for ( var i=0; i < replacements.length; ++i ) {
		newContent = sfw.util.replaceAll( replacements[i][0]
										  ,replacements[i][1]
										  , newContent );
	}

		// Now format any quote data in the post
	if ( !isFirstPost ) {
		newContent = this.formatQuotes( newContent, threadData, postData, userData );
	}

	return newContent;
}

sfw.forumPostTemplate.formatPostRawDisplay = function( content ) {

	// Replace the html line breaks with '\n'
 	var replacements = [ [ '<br />', '\n' ]
						 ,[ '<br>', '\n' ] ];
	var newContent = content;
	for ( var i=0; i < replacements.length; ++i ) {
		newContent = sfw.util.replaceAll( replacements[i][0]
										  ,replacements[i][1]
										  , newContent );
	}

	return newContent;
}


/**
 * Called when creating a new post in the UI
 *
 * @param parent - object calling this
 *
 * @param type - 'thread' or 'post'
 *
 * @param ownerWindow - only non-null if not the same as parent, i.e., in the case of
 *		a post reply or thread edit.
 *
 * @return
 * The form container
 *
*/
sfw.forumPostTemplate.create = function( parent, type, ownerWindow ) {
	try {
		var self = this;

		this.bbar = {};

		// Set defaults for type
		self.isThread = true;
		self.isPost = false;
		if ( "post" === type ) {
			self.isThread = false;
			self.isPost = true;
		}

		self.dataProviderURL = 'action=community&which=';
		self.getAction = 'submitThread';


		this.fileAttachmentPath = '';

		// Types: new thread post, or regular post: 'thread' / 'post'
		//
		// initInfo - object - initial values for the fields.  Only has
		// subForum if type==='thread'. Contains subforum, title, possibly
		// quoted text, and subscribe flag if type=='post'

		this.initialize = function( type, initInfo ) {

			// comon to both types
			var subForum = initInfo.subForum;
			this.subForumCombobox.setValue( subForum );
			this.subForumCombobox.setDisabled( false );

			// 'reset' clears fields and fills in the 'emptyText' strings
			this.topicText.reset();
			this.postContentsText.reset();
			this.subscribeCheckbox.setValue( false );
			this.subscribeCheckbox.show();
			this.fileAttachmentField.reset();
			this.fileAttachmentNameField.reset();
			this.removeAttachmentButton.setDisabled( true );

			this.fileAttachmentPath = '';

			if ( "thread" === type ) {
				self.getAction = 'submitThread';

			} else if ( "post" === type ) {
				this.subForumCombobox.setDisabled( true );
				self.getAction = 'submitPost';
				self.postType = 'new';

				if ( "undefined" !== typeof(initInfo.title) ) {
					if (  initInfo.title &&
						  sfw.util.stripWhitespace( initInfo.title ) ) {
						this.topicText.setValue( initInfo.title );
					}
				}
				if ( "undefined" !== typeof(initInfo.postContents) ) {
					if (  initInfo.postContents &&
						  sfw.util.stripWhitespace( initInfo.postContents ) ) {
//						var modifiedContents = sfw.forumPostTemplate.formatPostHtml( initInfo.postContents, 1 );
						var modifiedContents = initInfo.postContents;
						this.postContentsText.setValue( modifiedContents );
					}
				}

				if ( initInfo.attachmentId && ( ( 'editThread' === initInfo.type ) || ( 'editPost' === initInfo.type ) ) ) {

					var action = 'metadata';
					var params = {
						url: globals.apiPath() + '&' + this.dataProviderURL + action // enhancement: domain specific, shouldn't go in global domain
						,attachmentId: initInfo.attachmentId
					};


					var setAttachmentName = function( metadata ) {
						if ( metadata && metadata.name ) {
							this.fileAttachmentField.setValue( metadata.name ); // This field is hidden but still populating
							this.fileAttachmentNameField.setValue( metadata.name );
							this.removeAttachmentButton.setDisabled( false );
						}
					}
					var callback = {
						fn: setAttachmentName
						,scope: this
					};

					// Requesting the file metadata
					// Also specifying the callback function which will be called if the metadata is received successfully
					sfw.forumAttachment.requestMetaData( params, callback );
				}

				if ( "undefined" !== typeof(initInfo.subscribed) ) {
					this.subscribeCheckbox.setValue( initInfo.subscribed );
				}
				if ( "undefined" !== typeof(initInfo.type) ) {
					if ( ( ('reply' === initInfo.type) ||
						   ('editThread' === initInfo.type) ||
						   ('editPost' === initInfo.type)
						 )
						 && "undefined" !== typeof(initInfo.threadId)
						 && initInfo.threadId ) {

						self.postType = initInfo.type;
						self.threadId = initInfo.threadId;

						// If it was an edit post, we also have a postId field to get
						self.postId = 0;
						if ( 'editPost' === initInfo.type &&
							 "undefined" !== typeof(initInfo.postId) ) {
							self.postId = initInfo.postId;
						}

						// You can only change the subforum if you are the thread owner
						if ( 'editThread' === self.postType ) {
							this.subForumCombobox.setDisabled( true );
						}
					}
				}
			}

			this.validateForm();
			this.interface.doLayout();
		}

		this.validateForm = function() {
			this.bbar.createButton.setDisabled( true );
			if ( this.topicText.isValid() &&
				 this.postContentsText.isValid() ) {
				// Make sure our so called non-empty text is not just
				// blank spaces...
				var topicText = this.topicText.getValue();
				var postText = this.postContentsText.getValue();
				if ( sfw.util.stripWhitespace( topicText ) &&
					 sfw.util.stripWhitespace( postText ) ) {
					this.bbar.createButton.setDisabled( false );
				}
			}
		}

		// Post the data to the server
		this.createThread = function() {

			var url = '';
			//TODO - get this style working: sfw.util.constructApiRequest( dataProviderURL + getAction );
			// for now csi still uses the old method.  Change this eventually.
			//if ( 'csi' === sourceType ) {
			url = globals.apiPath() + '&' + self.dataProviderURL + self.getAction;
			//} // else if ... do other products, vim, psi, etc. as needed.

			var topicText = this.topicText.getValue();

			var postContents = this.postContentsText.getValue();
			var subForum = this.subForumCombobox.getValue();
			var subscribe = ( this.subscribeCheckbox.getValue() ? 1 : 0 );
			var attachmentName = sfw.forumAttachment.extractFilename( this.fileAttachmentPath );
			var attachmentContents = sfw.forumAttachment.getContents( this.fileAttachmentPath );

			var params = {
				topic: topicText
				,contents: postContents
				,subForum: subForum
				,subscribe: subscribe
				,attachment_name: attachmentName
				,attachment_contents: attachmentContents
			}

			// var successMessage = 'New thread created.';
			var relevantParent = parent.parent;
			// this allows us to refresh the main grid with the updated data,
			// i.e., new number of posts, views, etc.

			if ( self.isPost ) {
				// successMessage = 'Message posted.';
				params.postType = self.postType;
				params.threadId = self.threadId;
				params.postId = self.postId;
			}

			//todo: load mask ? Decide if we want this here...
			try {

				this.threadForm.getForm().submit({
					url: url
					,params: params
					,success: function() {
						//The refresh function must be called after we set the last activity, so that
						//the last thread created or updated is not marked as new
						var callback = {
							fn: function() {
								//because the refresh function is called twice when posting and closing
								//the post windows, must override the previousCheck normal flow by setting it forward
								//with one position
								sfw.communityForum.previousCheck = sfw.communityForum.lastCheck;
								relevantParent.refresh();
							}
							,scope: this
						};
						if ( self.isPost ) {
							parent.refresh( 0, self.threadId );
							// this will refresh the current view with the new post
							// (or edit) we just submitted
							ownerWindow.window.hide();
						} else {
							parent.window.hide();
						}
						try {
							sfw.communityForum.setLastActivity( callback );
						} catch ( ex ) {
							sfw.debug.trigger( ex, 'sfw.forumPostTemplate.createThread()' );
						}
						// Ext.Msg.alert( 'Success', successMessage );
					}
					,failure: function( form, action ) {
						try {
							var res = Ext.util.JSON.decode( action.response.responseText );

							switch( res.response ) {
							case 1:
								Ext.Msg.alert( 'Error', 'User info (email address) does not exist.' );
								break;
							default:
								Ext.Msg.alert( 'Error', 'Unexpected error!' );
								break;
							}
						} catch ( ex ) {
							sfw.debug.log( ex, '..threadForm.getForm().submit()' );
							Ext.Msg.alert( 'Error', 'Unexpected error!' );
						}
					}
				});
				// todo: unload mask
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
			}
		}



		// UI
		// --
		this.bbar.createButton = new Ext.Button({
			text: 'Submit Thread'
			,tooltip: 'Create new thread'
			,disabled: true
			,handler: this.createThread.createDelegate( this )
		});

		this.bbar.closeButton = new Ext.Button({
			text: 'Close'
			,tooltip: 'Close window'
			,disabled: false
			,handler: function() {
				if ( ownerWindow ) {
					ownerWindow.window.hide();
				} else {
 					parent.window.hide();
				}
			}
		});

		this.bbar.toolbar = new Ext.Toolbar({
			items: [
				'->'
				,this.bbar.createButton
				,'-'
				,this.bbar.closeButton
			]
		});

		this.subForumCombobox = new Ext.form.ComboBox({
			store: sfw.sharedForum.forumSelectionStore
			,width: 125
			,valueField: 'id'
			,displayField: 'text'
			,mode: 'local'
			,fieldLabel: 'Sub-Forum'
			,allowBlank: false
			,editable: false
			,triggerAction: 'all'
			,autoSelect: true
			,disabled: true
		});

		this.topicText = new Ext.form.TextField({
			allowBlank: false
			,width: 350
			,labelAlign: 'left'
			,fieldLabel: 'Forum Topic'
			,emptyText: "Enter a topic..."
			,value: ''
			,enableKeyEvents: true
			,disabled: false
			,listeners: {
				invalid: function() {
					self.bbar.createButton.setDisabled( true );
				}
				,valid: this.validateForm.createDelegate( this )
			}
		});

		this.postContentsText = new Ext.form.TextArea({
			allowBlank: false
			,width: 350
			,height: 350
			,fieldLabel: 'Forum Post'
			,enableKeyEvents: true
			,emptyText: "Type post contents here..."
			,disabled: false
			,value: ''
			,listeners: {
				invalid: function() {
					self.bbar.createButton.setDisabled( true );
				}
				,valid: this.validateForm.createDelegate( this )
			}
		});

		/*
		 *	params:
		 *	field, the file upload file
		 *	v, dom value
		 */
		this.attachFile = function ( field, v ) {

			// todo:	#community #file #attach
			//			check if the extension is xml

			if ( v ) {

				// If file selected is fake, then prompt user
				// otherwise move on

				var fakepathRegexp = /^C:\\fakepath\\/ ;
				if ( v.match( fakepathRegexp ) ) {
					var trustedSite = 'https://csi5.secunia.com';
					if ( typeof globals.ca_domain === 'string' ) {
						trustedSite = globals.ca_domain;
					}
					Ext.Msg.show({
						title:'Invalid Path'
						,msg: 'The Flexera cannot access the file you specified.<br>Please add the site ' + trustedSite
							+ ' to the list of trusted sites in Internet Explorer and try again.<br>'
							+ 'If the problem persists contact Flexera Support.'
						,buttons: Ext.Msg.OK
						,icon: Ext.MessageBox.STOP
					});
				} else {
					this.fileAttachmentPath = v;
					this.fileAttachmentNameField.setValue( v );
				}

			}
		}

		this.fileAttachmentNameField = new Ext.form.TextField ({
			width: 178
		});

		this.fileAttachmentField = new Ext.ux.form.FileUploadField({
			fieldLabel: 'Attach File'
			,listeners: {
				fileselected: this.attachFile.createDelegate(this)
			}
			,name: 'sps_file'
			,emptyText: '.xml'
			// We are not using the file upload text field as it creates
			// a lot of layout problems, instead we are using the fileAttachmentNameField
			,buttonOnly: true
		});

		this.removeAttachmentButton = new Ext.Button({
			text: 'Remove'
			,disabled: true
			,handler: function() {

				var params = {};

				// enhancement: domain specific, shouldn't go in global domain
				if ( self.postId ) {
					params.url = globals.apiPath() + '&' + self.dataProviderURL + 'remove_post_attachment&post_id=' + self.postId
				} else {
					params.url = globals.apiPath() + '&' + self.dataProviderURL + 'remove_thread_attachment&thread_id=' + self.threadId
				}

				var updateButtonState = function ( scope ) {
					parent.refresh( 0, self.threadId );
					self.removeAttachmentButton.setDisabled( true );
					self.fileAttachmentField.reset();
					self.fileAttachmentNameField.reset();
				}
				var callback = {
					fn: updateButtonState
					,scope: self
				};
				sfw.forumAttachment.removeAttachment( params, callback );
			}
		});

		var space = { width: 5 };

		this.attachmentContainer = new sfw.Panel({
			fieldLabel: 'Attach File'
			,layout: 'table'
			,layoutConfig: {
				columns: 5
			}
			,items: [
				this.fileAttachmentNameField
				,space
				,this.fileAttachmentField
				,space
				,this.removeAttachmentButton
			]
		});

		// ENHANCEMENT - possibly add tooltip: 'Receive an email whenever someone replies to this thread'
		// Note: adding a tooltip to a checkbox is not really supported in extjs and takes some kludging
		this.subscribeCheckbox = new Ext.form.Checkbox({
			fieldLabel: 'Subscribe'
			,checked: false
		});

		this.notesText = new sfw.Panel({
			padding: '0px 0px 10px 0px'
			,html: "<br><b>Formatting your text</b><br>If you wish, you can alter the format of your text by using the following codes:<br>&nbsp [b]Security[/b] would look like <b>Security</b>.<br>&nbsp [i]Updates[/i] would look like <i>Updates</i>.<br>"
		});

		this.threadForm = new sfw.Form({
			bodyStyle: 'padding: 0px'
			,items: [
				this.subForumCombobox
				,this.topicText
				,this.postContentsText
				,this.attachmentContainer
				,this.subscribeCheckbox
				,this.notesText
			]
		});

		this.threadDetails = new Ext.form.FieldSet({
			layout: 'form'
			,title: 'Thread Configuration'
			,autoWidth: true
			,items: [
				this.threadForm
			]
		});

		this.interface = new sfw.Panel({
			autoScroll: true
			,layout: 'auto'
			,items: new sfw.Panel({
				layout: 'auto'
				,items: [ this.threadDetails ]
				,frame: true
			})
			,bbar: this.bbar.toolbar
		});

	} catch ( ex ) {
		sfw.debug.trigger ( ex, "forumPostTemplate.create" );
	}

	return this;
}
