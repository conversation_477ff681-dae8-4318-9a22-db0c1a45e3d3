/**
 * @todo test secuniaSorting and stateful with databoundgrid and multiselect.
 * @file grid.js
 * Provides wrapper for the ExtJS grid object, supporting some default configuration options.
 *
 * Configuration options:
 *
 * .secuniaSorting: true/false to enable/disable multisorting. Allows user to shift click
 *      column header to do sorting by multiple columns
 *      Note if secunia sorting is enabled store.sortInfo will not be used.
 *      When multisorting an array of default sorters should be specified.
 * .defaultSorters: [{field:'mame1', direction: 'ASC'}, {field: 'name2', direction: 'ASC'}]
 *     If default sorters are specified the first sorter, i.e. defaultSorters[0]
 *     will be appended to single sort. Hence defaultSorters[0] should be a field with a
 *     lot of diversity like fx name or fx time created and not a field with
 *     only two possible values. Also the default sorters should have same sortdirection
 *     and the default sorter fields should be indexed columns in database in order
 *     to increase performance.
 *
 * .encodeSorters: true/false should be set to true to if sorters are to be sent to
 *     server side for sorting.
 *     NOTE: if sorting on serverside you have to add sortable fields to
 *     $allowedSortFields in order to be able to sort by them.
 *
 *
 *     Note secuniaSorting will set store.remoteSort to true
 *     and overide any 'headerclick' listener
 *     defined in the gridPanel config.
 *
 * .secuniaPaging: true or false to enable paging. The mechanism attaches a resize
 *     listener, which can be overwritten. Requires some extra configuration options:
 *
 * .pageSize: default pageSize, passed to the store and paging bar
 * .displayInfo: paging bar displayInfo, passed to the paging bar
 * .displayMsg: paging bar displayMsg, passed to the paging bar
 *
 * .stateful: true/false to make grid save its state to dataprovider. Defaults to false
 *    a unique config.id, distinct from all other stateful component ids,
 *    must be set, as Extjs autogenerated component id's are not
 *    garantied to be stable from session to session.
 *
 *    Enabling this option also adds a headerContextMenu that allows user
 *    to reset state of grid to initial state set by programmer.
 *
 *    TODO Initialise stateprovider for VIM and other products that need stateprovider
 *    functionality
 *
 *    NOTE: For debugging purpose set this option to false as programmed state of fx
 *    column sortable and column width might be over written by a saved state.
 *
 *    Also it is of utmost importances to provide stateful component with
 *    unique component id so every grid should have a unique id, distinct from all other
 *    stateful components.
 *
 * .secuniaExportable: { query: SQL query string, columns: columns object, search: search parameters }
 *    Configures grid to have rightmost button in topbar with export options of saving to file or clipboard.
 *    - query is the query that will be run on DB
 *    - columns is the grids columns object, which contains relations between needed fields, their headers and renderes. If there is a situation
 *    where we can't apply current renderer (for example a criticality rendere, which returns image), we need to create similar columns object
 *    which contains a renderer that returns a text value which is usefull in CSV format (in same example, number 5 becomes Not Critical).
 *    - search is the variable that contains all the needed query search parameters for usage in database.applySearch() function. Usually it
 *    should be "this", but in case we are changing query we might want to change search parameters too. It can be undefined, in which case
 *    it will be ignored.
 *
 * .secuniaGrouping: enable grouping
 * .groupField: grouping field name
 * .itemSingular: item text for a single item in the group
 * .itemPlural: item text for more items in the group
 * .secuniaCheck: enable check boxes
 * .store.filterValue: If store has this option set, then a filter will be sent to the data provider
 * .secuniaType: "remote" to enable remote data source ( will cause the wrapper to create a JsonStore )
 *     If secuniaType remote encodeSorters:true, ie. sorters will be sent encoded
 *
 * .editable: will create and ExtJS EditableGrid
 * NOTE: Grouping and Paging don't mix.
 * NOTE: the default paging bar will set the totalProperty to total, and append pageSize to the store url ( see databoundgrid.class.php )
 * NOTE: Group mouse over will store the {group} data in a global variable called sfw.GridPanel.groupStack.
 * NOTE: Group data provider MUST return a group_id and group pair of values.
 * NOTE: Group will create sfw.GridPanel.rid
 * NOTE: Group needs this config entry: ddDropHandlerName -> to provide the full name of the function handling the dd notification
*/

sfw.GridPanel = function( config ) {
	config.frame = config.frame || false;
	config.border = config.border || false;
	config.store = config.store || {};
	config.autoExpandMax = config.autoExpandMax || 10000;
	config.stripeRows = ( typeof config.stripeRows === 'boolean' ? config.stripeRows : true );

	config.isReady = false;
	config.listeners = config.listeners || {};

	/**
     * @private
     * immediate function that returns correct encode function, if sorting is to be
     * done on server side. If sorting is to be done locally it simply returns function
     * that returns sorters object directly.
     * Enhancement: this functionality should go into specific proxy and not be
     * a option here.
     */
	var prepareSorters = (function (){
		if ( config.jsonSorters || config.secuniaType === 'remote' ){
			return function( sorters ){
				return Ext.util.JSON.encode( sorters );
			};
		} else if ( config.encodeSorters || config.secuniaType === 'remote' ){
			return function( sorters ){
				var encodedSorters = '';
				Ext.each(
					sorters
					,function( sorter ){
						if( Ext.isString( sorter.field ) &&
							sorter.field.length > 0 &&
							Ext.isString( sorter.direction ) &&
							sorter.direction.length > 0 &&
							sorter.direction.toUpperCase() === "ASC" ||
							sorter.direction.toUpperCase() === "DESC" ){

							encodedSorters += (encodedSorters ? ',': '') +
								sorter.field +
								'=' +
								sorter.direction.toUpperCase();
						}
					}
				);
				return encodedSorters;
			};
		} else {
			return function( sorters ){
				return sorters;
			};
		}
	})();

	if ( config.secuniaGrouping === true ) {
		var tpl;
		if ( config.enableDragDrop ) {
			tpl = config.groupTextTpl || '<div {[sfw.GridPanel.rid = Ext.id()]} id="{[sfw.GridPanel.rid]}" onmouseup = "if ( ' + config.ddStackName + '[this.id] != true ) { ' + config.ddStackName + '[this.id] = true; d = new Ext.dd.DropTarget(this.id, { ddGroup: \'' + config.ddGroup + '\', notifyDrop: function( source, e, data ) { var extra = \'{group}\'; ' + config.ddDropHandlerName + ' } }); }">'
				+ '{text}';

			if ( config.itemSingular && config.itemPlural ) {
				tpl +=  '({[values.rs.length]} {[values.rs.length > 1 ? "' + config.itemPlural + '" : "' + config.itemSingular + '"]})';
			}
			tpl += '</div>';
		}

		config.view = config.view || new Ext.grid.GroupingView({
			forceFit: ( typeof config.forceFit !== 'undefined' ) ? config.forceFit : true
			,groupTextTpl: tpl ? tpl : '{text}'
			,getRowClass: function( rec ) {
				if ( rec.data[config.store.idProperty] == -1 ) {
					return 'x-hide-display';
				}
			}
		});

		config.store.proxy = new Ext.data.HttpProxy({ url: config.store.url });
		config.store.groupField = config.store.groupField || config.groupField;
	}

	if ( config.secuniaPaging === true ) {
		config.store.totalProperty = config.store.totalProperty || 'total';
		config.store.originalUrl = config.store.url;
		config.store.url = config.store.url + "&paging=true&pageSize=" + config.pageSize;
	}

	// Note config.secuniaType is set to 'remote' - in the sfw.databoundgrid no matter
	// what it was origianally set to in databoundgrid config.
	switch ( config.secuniaType ) {
		case "remote":
			config.loadMask = ( typeof config.loadMask !== "undefined" ) ? config.loadMask : true;
			if ( config.secuniaGrouping === true ) {
				config.store.reader = {};
				config.store.reader.root = config.store.root;
				config.store.reader.fields = config.store.fields;
				config.store.reader.totalProperty = config.store.totalProperty;
			}

			config.store.reader = new Ext.data.JsonReader( config.store.reader );
			config.store.listeners = config.store.listeners || {};

			config.store.listeners.beforeload = config.store.listeners.beforeload || function( me, configuration ) {
				if ( typeof me.filterValue != "undefined" ) {
					configuration.params = configuration.params || {};
					configuration.params.filter = me.filterValue || "";
				}
				return configuration;
			};

			// Note - the previous way of trying to create the store using the store as init object was failing
			// as the listener configuration was actually being lost.  So, add the listener info separately
			var myStore;
			if ( config.secuniaGrouping !== true ) {
				myStore = new Ext.data.JsonStore( config.store );
			} else {
				myStore = new Ext.data.GroupingStore( config.store );
			}

			myStore.listeners = config.store.listeners;

			config.store = myStore;
		break;

		default:
		break;
	}

	// Here we set/create custom store properties related to multisorting.
	if( config.secuniaSorting === true ){
		// Secunia sort requires ExtJS store option remoteSort to be set to true
		config.store.remoteSort = true;
		// add custom store option for multisorting
		config.store.currentlyMultiSorting = false;
		// add custom store option for multisorting
		config.store.defaultSorters = config.defaultSorters;
		// add custom store option for multisorting
		config.store.sorters = new Ext.util.MixedCollection(false, function(el){return el.field;} );

		// add default sorters if specified.
		if ( Ext.isArray(config.defaultSorters) ){
			config.store.sorters.addAll( config.defaultSorters );
			config.store.setBaseParam( 'sorters', prepareSorters( config.store.sorters.items ) );

			//sortInfo is set here to make sure columnHeaders classes are updated.
			//Once grid initialises.
			config.store.sortInfo = config.defaultSorters[0];
		}
	}

	if ( config.secuniaCheck === true ) {
		config.sm = new Ext.grid.CheckboxSelectionModel( config.sm );
		var temp = [ config.sm ], i;
		for ( i = 0; i < config.columns.length; i++ ) {
			temp.push( config.columns[i] );
		}
		config.columns = temp;
	}
	config.store.on( 'beforeload', function( store, options ){
		// Some store grids are loaded for using the data inside them,
		// without the grids to actually be rendered.
		// TODO: Need to optimize some of the grids for which
		// the stores are being loaded without being used.
		if ( typeof grid.body === "undefined" ) {
			return;
		}
		var height = grid.body.getHeight( true );
		if ( typeof grid.getBottomToolbar() !== 'undefined' ) {
			height -= grid.getBottomToolbar().getHeight();
		}

		// fix for itemselector which has bbar on the parent panel instead on the grid
		if ( typeof grid.ownerCt.getBottomToolbar() !== 'undefined' ) {
			height -= grid.ownerCt.getBottomToolbar().getHeight();
		}

		var row = grid.view.getRow(0);
		var rowHeight = row ? Ext.get( row ).getHeight() : 21;

		var rows = Math.floor( height / rowHeight );
		if( rows < 1 ) {
			rows = 1;
		}
		if( rows > 100 ) {
			rows = 100;
		}

		// In case the pageSize has already been computed by other means somewhere else,
		// don't overwrite it with the calculations made here. This is primarly being used
		// in the products and hosts reports windows
		if ( typeof grid.getBottomToolbar() !== 'undefined' && grid.getBottomToolbar().precomputedPageSize !== true ) {
			grid.getBottomToolbar().pageSize = config.pageSize = rows;
		}

		// fix for itemselector which has bbar on the parent panel instead on the grid
		if ( typeof grid.ownerCt.getBottomToolbar() !== 'undefined' ) {
			grid.ownerCt.getBottomToolbar().pageSize = config.pageSize = rows;
		}

		config.store.baseParams['start'] = 0;
		config.store.baseParams['limit'] = rows;
	} );

	if ( config.secuniaPaging === true ) {
		config.bbar = new Ext.PagingToolbar({
			pageSize: config.pageSize
			,store: config.store
			,displayInfo: true
			,displayMsg: config.displayMsg
		});

		/**
		 * CSI has no references to this function anymore. Left inside as VIM dependency.
		 *
		 * @todo:
		 * The function is on the grid object and takes itself as the argument ?!!?!!
		 * It doesn't make sense to pass another grid as an argument.. Either this needs
		 * to change or this function should move to another place e.g. utils etc
		 */
		config.calculateGridSize = function( grid ) {
			var height = 400;
			if ( grid.body && grid.body.getHeight() ) {
				height = grid.body.getHeight();
			}

			var rowHeight = sfw.gridPanelInternalRowHeight ? sfw.gridPanelInternalRowHeight : 21;
			if ( grid.rendered && grid.view && grid.view.getRow(0) ) {
				var row = grid.view.getRow(0);
				if ( row ) {
					rowHeight = Ext.get(row).getHeight();
				}
			}

			var contentHeight = height - 35;
			var size = Math.round( contentHeight / rowHeight );
			size = ( size < 1 ) ? 1 : ( size > 100 ? 100 : size );
			grid.getBottomToolbar().pageSize = size;
		};
	}

	if ( config.stateful === true ){
		// Update Ext.data.gridPanel state related functions getState and applyState
		// only if secuniaSorting is enabled and grid is stateful. Otherwise leave extjs
		// gridpanel state functionality as is.

		// Overides default gridpanel getState function to support secuniaSorting.
		// Added fetching of store.sorters and store.currentlyMultisorting.

		var getState = function(){

			var o = {columns: []},
            store = this.store,
            ss,
            gs,
            cms;

			for(var i = 0, c; (c = this.colModel.config[i]); i++){
				o.columns[i] = {
					id: c.id,
					width: c.width
				};
				if(c.hidden){
					o.columns[i].hidden = true;
				}
				if(c.sortable){
					o.columns[i].sortable = true;
				}
			}
			if(store){
				ss = store.getSortState();
				if(ss){
					o.sort = ss;
				}
				//Here we get the state related to secuniaSorting if enabled.
				if(store.sorters && config.secuniaSorting){
					ss=store.sorters.items;
					cms = store.currentlyMultiSorting;
					if(ss){
						o.currentlyMultiSorting = cms;
						o.sorters = ss;
					}
				}

				if(store.getGroupState){
					gs = store.getGroupState();
					if(gs){
						o.group = gs;
					}
				}
			}
			return o;
		};

		//Overides default Ext.data.GridPanel apply state function.
		//added functionality that applies sorters if secuniaSorting true.

		var applyState = function(state){

			var cm = this.colModel,
            cs = state.columns,
            store = this.store,
            s,
            c,
            colIndex;

			if(cs){
				for(var i = 0, len = cs.length; i < len; i++){
					s = cs[i];
					c = cm.getColumnById(s.id);
					if(c){
						colIndex = cm.getIndexById(s.id);
						cm.setState(colIndex, {
							hidden: s.hidden,
							width: s.width,
							sortable: s.sortable
						});
						if(colIndex != i){
							cm.moveColumn(colIndex, i);
						}
					}
				}
			}
			if(store){
				s = state.sort;
				if(s){
					store[store.remoteSort ? 'setDefaultSort' : 'sort'](s.field, s.direction);
				}
				// apply secuniaSorting specific sorters and currentlyMultiSorting
				// info. state.sorters will only be set if secuniaSorting = true;

				s = state.sorters;
				if(s && config.secuniaSorting){
					//Clear sorters in case default sorters were added.
					store.sorters.clear();
					store.sorters.addAll(s);
					store.currentlyMultiSorting = state.currentlyMultiSorting || false ;
					//Make sure sorters are send in stores load request.
					store.setBaseParam('sorters', prepareSorters( store.sorters.items ) );
				}

				s = state.group;
				if(store.groupBy){
					if(s){
						store.groupBy(s);
					}else{
						store.clearGrouping();
					}
				}

			}
			var o = Ext.apply({}, state);
			delete o.columns;
			delete o.sort;
			Ext.grid.GridPanel.superclass.applyState.call(this, o);
		};

		/**
         *  @Private
         *  beforestaterestore handler function that gets the initial state of
         *  the gridpanel and copies it to grid.defaultState, such that we can
         *  revert to this state.
         *  @param grid Ext.component (this)
         *
         *  @param state hash of state values returned from stateprovider.
         */
		var getDefaultState = function( grid , state ){
			grid.defaultState = Ext.apply( {} , grid.getState() );
			return true;
		};

		config.getState = getState;
		config.applyState = applyState;

		//Note we do overwrite any beforestaterestore listeners that might have been
		//defined.
		config.listeners.beforestaterestore = getDefaultState;
	}

	var exportButtons;

	if ( typeof config.secuniaExportable !== 'undefined'){

		if (typeof config.secuniaExportable.exportOnClientSide !== 'undefined' && config.secuniaExportable.exportOnClientSide === true) {
			exportButtons = sfw.exportsClient.generateButtons(config)
		} else {
			exportButtons = sfw.exports.generateButtons(config)
		}

		if (exportButtons) {
			if (typeof config.secuniaExportableAddOns !== 'undefined') {

				try {
					exportButtons[1].menu.push(config.secuniaExportableAddOns);
				} catch (ex) {
					sfw.debug.trigger(ex, "Unexpected error in global.grid.js, can`t add AddOns for the export button");
				}

			}

			if (typeof config.tbar === 'undefined') {
				// if top bar does not exist, add it.
				config.tbar = [];
				config.tbar.push(exportButtons);
			} else if (typeof config.tbar === 'object' && config.tbar instanceof Ext.Toolbar) {
				// tbar is instance of Ext.Toolbar, use add method
				config.tbar.add(exportButtons);
			} else if (typeof config.tbar === 'object' && typeof config.tbar.push !== 'undefined') {
				config.tbar.push(exportButtons);
			} else {
				config.tbar = [];
				config.tbar.push(exportButtons);
			}
		}
	}

	/**
	 * Return visible columns and headers. It's needed for the exporting data to the customer.
	 *
	 * @return {object} Contains ids and columnHeaders arrays
	 */
	config.getVisibleColumns = function() {
		var columnIds = [];
		var columnHeaders = [];
		var visibleColumns = this.colModel.getColumnsBy( function( c ) {
			return !c.hidden;
		} );
		for( var i = 0; i < visibleColumns.length; i++ ) {
			// if column has property exportable set to false, it will not be sent
			if ( visibleColumns[i].exportable !== false ) {
				columnIds.push( visibleColumns[i].dataIndex );
				columnHeaders.push( visibleColumns[i].header );
			}
		}
		return { ids: columnIds, headers: columnHeaders };
	};

	// Here we instantiate the GridPanel/EditorGridPanel
	var grid;
	if ( typeof config.editable !== "undefined" && config.editable ) {
		grid = new Ext.grid.EditorGridPanel( config );
	} else {
		grid = new Ext.grid.GridPanel( config );
	}

	grid.computeRows = function () {

		if ( typeof this.body === "undefined" ) {
			return;
		}
		
		var height = this.body.getHeight( true );
		if ( typeof this.getBottomToolbar() !== 'undefined' ) {
			height -= this.getBottomToolbar().getHeight();
		}

		// fix for itemselector which has bbar on the parent panel instead on the grid
		if ( typeof this.ownerCt.getBottomToolbar() !== 'undefined' ) {
			height -= this.ownerCt.getBottomToolbar().getHeight();
		}

		var row = this.view.getRow(0);
		var rowHeight = row ? Ext.get( row ).getHeight() : 21;

		var rows = Math.floor( height / rowHeight );
		if( rows < 1 ) {
			rows = 1;
		}
		if( rows > 100 ) {
			rows = 100;
		}
		return rows;
	}

	if ( config.secuniaSorting === true ){

		/**
          * Updates header sort state according to the store.sorters.
          * If store.currentlyMultisorting = true it will set multiple sortstates
          * and otherwise just indicate the sort state for first sorter.
          *
          * overides gridViews default updateHeaderSortState function
          * relies on the gridViews build in way of calling updateHeaderSortState
          */
		grid.getView().updateHeaderSortState = function() {
			var state = this.ds.getSortState();
			if (!state) {
				return;
			}

			if (!this.sortState || (this.sortState.field != state.field || this.sortState.direction != state.direction)) {
				this.grid.fireEvent('sortchange', this.grid, state);
			}

			this.sortState = state;

			//beginning of function is kept to keep default events
			//being fired as extjs expects it.

			var sorters = this.ds.sorters;

			if (sorters) {// SecuniaSorting
				if (this.ds.currentlyMultiSorting){
					this.updateSortIcons(sorters.items);
				} else {
					this.updateSortIcons(sorters.first());
				}
			} else { //Normal Extjs sorting used.
				var sortColumn = this.cm.findColumnIndex(state.field);
				if (sortColumn != -1) {
					var sortDir = state.direction;
					this.updateSortIcon(sortColumn, sortDir);
				}
			}
		};

		/**
         * @private
         * Updates grid header sortIcon classes according to the passed sorters.
         *
         * Note this is custom Secunia method called internally in gridView.
         * @param {array} sorters
         * array of sorters must have the following format
         * [{field: name1, direction: 'ASC'},{field: name2 , direction: 'DESC'}]
         *
         */
		grid.getView().updateSortIcons = function( sorters ) {
			var sortClasses = this.sortClasses;
			//clears headers and removes all sort classes from all columnsheaders.
			var headers = this.mainHd.select('td').removeClass(sortClasses);
			//ENHANCEMENT add additional class that numbers icons according to sorter order.

			Ext.each(
				sorters
				,function( sorter ) {
					var sortClass   = sortClasses[sorter.direction == "DESC" ? 1 : 0];
					var sortColumnIndex = this.cm.findColumnIndex(sorter.field);
					if (sortColumnIndex != -1) {
						headers.item(sortColumnIndex).addClass(sortClass);
					}
				}
				,this
			);
		};

		/**
         * @private overrides gridviews default handleHdMenuClick
         *
         * Is called internally within gridView so this = gridView
         *
         * Attached as the 'itemclick' handler to the header menu
         * and the column show/hide submenu (if available).
         * Performs sorting and updates store.sorters
         * if the sorter buttons were clicked
         * otherwise hides/shows the column that was clicked.
         *
         **/
		grid.getView().handleHdMenuClick = function(item, e) {
			var store = this.ds,
            dataIndex = this.cm.getDataIndex(this.hdCtxIndex);

			switch (item.getItemId()) {
            case 'asc':
				//added update sorters here
				updateSorters( grid, this.hdCtxIndex, e, 'ASC' );
                store.sort(dataIndex, 'ASC');
                break;
            case 'desc':
				//added update sorters here
				updateSorters( grid, this.hdCtxIndex, e, 'DESC' );
                store.sort(dataIndex, 'DESC');
                break;
            default:
                this.handleHdMenuClickDefault(item);
                break;
			}
			return true;
		};

		/**
         * @private
         * Function updateSorters: updates grid.store.sorters according to the
         * passed event and columnIndex. Shift click turns multisorting on or keeps it on
         * Normal click turns single sorting on. When single sorting the first sorter of
         * store.defaultSorters will be appended to sorters.
         *
         * @param {object} gridPanel that contains store with sorters.
         *
         * @columnIndex {number} index of column for which sorter should be updated.
         *
         * @e {Ext.EventObject} Event that triggered the updateSorters
         *
         * @forceDirection {string 'ASC' or 'DESC'} Optinal parameter if set it will
         * overide toggling of sortState for the passed columIndex and
         * set it to forceDirection.
         */
		var	updateSorters = function( grid, columnIndex, e , forceDirection ){
			//this function only sets the sorters and is relying on the grid.store
			//to do request the reload of the data, hence, we  will
			//not manually need to call the reload of the data.

			//Only updateSorters if header is enabled and column sortable.
			if (grid.getView().headersDisabled || !grid.getColumnModel().isSortable(columnIndex)) {
				return;
			}

			var sorters = grid.getStore().sorters;
			var columnName = grid.getColumnModel().getDataIndex(columnIndex);

			//Default sorter for clicked column.
			var sorter = {
				field: columnName
				,direction: forceDirection || 'ASC'
			};

			if( e.shiftKey ){//shift click
				if( grid.getStore().currentlyMultiSorting ){
					if( sorters.containsKey(columnName) ){
						// If sorter is already in sorters we toggle sort direction
						// unless forceDirection was specified.
						if(!forceDirection){
							sorter.direction = sorters.item(columnName).direction.toggle('ASC','DESC');
						}
						sorters.replace( sorter );
					} else {
						//If sorter is not in ds.sorters we add it to the end.
						sorters.add( sorter );
					}
				} else { //currentlyMultiSorting : false

					//Todo check whether I can delete all but first elements
					var first = sorters.first();
					sorters.clear();
					sorters.add(first);
					//note adding sorter will overwrite sorter if it already added.
					sorters.add(sorter);
					grid.getStore().currentlyMultiSorting = true;
				}
			} else {//normal click
				if (  sorters.containsKey(columnName) ){
					if( !forceDirection ){
						sorter.direction = sorters.item(columnName).direction.toUpperCase().toggle('ASC','DESC');
					}
				}
				//Clear sorters
				sorters.clear();
				//Add clicked column as sorter
				sorters.add(sorter);

				grid.getStore().currentlyMultiSorting = false;
			}

			// Add default sorter if there is none
			if ( sorters.length < 1 && typeof grid.getStore().defaultSorters !== 'undefined' ) {
				sorters.addAll( grid.getStore().defaultSorters );
			}

			//This updates the store param that are sent to proxy in a data dorequest by default.
			grid.getStore().setBaseParam( 'sorters', prepareSorters( sorters.items ) );
		};

		// Note, this will overide 'headerclick' listeners in config.listeners
		grid.on('headerclick', updateSorters);
	}

	if( config.stateful ){
		// We have to set grid.defaultState here also, to make sure it is set
		// the first time the user loads the csi on a local machine. The
		// event beforestaterestore is only fired on initialisation of a component
		// if the componentState was previously saved to stateprovider hence it will
		// not be fired on first load of csi.
		grid.defaultState = grid.defaultState || grid.getState();

		/**
         * @private
         * headerContextMenu itemclick handler resets gridView to defaultState, as
         * initially defined by programmer.
         * ENHANCEMENT: Make the resetView available to non stateful grids.
         */
		var resetView  = function( grid , stateId ){
			var sp = Ext.state.Manager.getProvider();

			sp.set( stateId , Ext.apply({}, grid.defaultState ) );
			grid.applyState( Ext.apply({}, grid.defaultState ));

			// We have to manually reset the totalWidth to null here, as
			// grid.applyState does not reset totalWidth property of columnModel and
			// the gridView refresh function will only reapply autoexpand column if
			// totalWidth is set to null.
			grid.getColumnModel().totalWidth = null;

			// Once a userResizes a column the  gridView userResized property will be set
			// to true and gridView refresh will only recalculate column width if
			// userResized = false, hence we set it to false here.
			grid.getView().userResized = false;

			grid.getView().refresh( true );
			//let store use last pagesize, but reset to first page.
			//we could not use paging toolbar as not all grids have that enabled.
			//also one can not set params as config in .reload({params:{start:0}}
			//as ext does not do a deep copy of params and simply overwrites params
			//if specified as reload config as above.
			var lastOptions = grid.getStore().lastOptions;
			if ( lastOptions.params !== undefined ){
				lastOptions.params.start = 0;
			}
			grid.store.reload();
			return true;
		};

		var headerContextMenu = function( grid , columnIndex, e ){
			//Note stateId defaults to grid id and all our grids should have unique id.
			var stateId = grid.getId();
			var headerContextMenu = new sfw.Menu({});

			headerContextMenu.add({
				text: 'Reset to Default View'
				,handler: resetView.createDelegate( this , [ grid , stateId ] )
			});

			headerContextMenu.showAt( e.getXY() );
		};

		grid.on('headercontextmenu', headerContextMenu );
	}

	// adding default right click behaviour to all grids
	// if right click was made outside of selection, select that row
	var rightClickSelection = function( grid, index ) {
		if ( !grid.getSelectionModel().isSelected(index) ) {
			grid.getSelectionModel().selectRow(index);
		}
	};

	// This forces all grid to have same right click functionality which was taken from behaviour of Windows Explorer to make it behave the way users are used to.
	// The behaviour is that right clicking on an item outside of currently selected items will cancel that selection and select item under mouse cursor.
	// The only issue it has currently is that if we have more than one listener on "rowcontextmenu", this will not work well.
	// Although we have only one listener everywhere now, that one being generation of context menu, there is a chance that in the future we could have more of them, and then this needs to be revisited and a new solution applied.
	// The problem was that if there are two listeners on this event, first to change selection and second to generate context menu, we could not rely on which listener will run first. sometimes is was first one, sometimes second one.
	// This solution forces rightClickSelection function to run before first listener.
	if ( grid.hasListener( 'rowcontextmenu' ) ) {
		var tempFunc = grid.events.rowcontextmenu.listeners[0].fn.createInterceptor( rightClickSelection);
		grid.events.rowcontextmenu.clearListeners();
		grid.on( 'rowcontextmenu', tempFunc );
	} else {
		grid.on( 'rowcontextmenu', rightClickSelection );
	}

	grid.on( 'resize', function( grid, adjWidth, adjHeight, rawWidth, rawHeight ){
		if ( adjWidth != rawWidth || adjHeight != rawHeight ) {
			config.store.reload();
		}
	} );

	//TODO Check whether this is required?
	config.store.parent = grid;
	
	return grid;
};