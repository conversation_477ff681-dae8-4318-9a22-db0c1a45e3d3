/**
 * @file cve.js
 */

sfw.cve = {};

/**
 * Provides CVE view object.
 */
sfw.cve.create = function( cveId ) {
	this.dataProviderURL = "action=cve&which=displaycve&cve=" + cveId;

	this.makeReferences = function( sourcesArray ) {
		var html = "";
		var processed = {};

		// If the sourcesArray is empty it comes in as an empty string vs. an array object
		if ( "string" != typeof(sourcesArray) ) {
			for ( key in sourcesArray ) {
				if ( processed[key] != true ) {
					html += '<tr><td><nobr>' + key + '</nobr></td><td>&nbsp;</td></tr>';
				}

				for ( var i = 0; i < sourcesArray[key].length; i++ ) {
					html += '<tr><td>&nbsp;</td><td width="100%">' + Ext.util.Format.htmlDecode( sourcesArray[key][i].reference ) + '</td></tr>';
				}
			}
		}

		if ( html != "" ) {
			html = '<table width="100%">' + html + '</table>';
		}

		return html;
	}

	this.makeHtml = function( data ) {
		var html = 'No CVE data could be retrieved for this product';

		if ( !data ) {
			return html;
		}

		try {
			html = '<table width="100%" style="padding: 4px;">' +
				'<tr>' +
				'<td style="padding-right: 2px; padding-top: 4px; width: 180px;"><b><nobr>Original Page at CVE Mitre: </nobr></b></td>' +
				'<td style="padding-top: 4px;"><a href="http://cve.mitre.org/cgi-bin/cvename.cgi?name=' + cveId + '" target="_blank">' + cveId + '</a></td>' +
				'</tr>' +
				( ( typeof data.description != "undefined" ) ?
				  '<tr valign="top">' +
				  '<td style="padding-right: 2px;padding-top: 4px;"><b>Description: </b></td>' +
				  '<td style="padding-top: 4px;">' + data.description + '</td>' +
				  '</tr>' : '' ) +
				( ( typeof data.opt_type != "undefined" ) ?
				  '<tr valign="top">' +
				  '<td style="padding-right: 2px;padding-top: 4px;"><b>CVE Status: </b></td>' +
				  '<td style="padding-top: 4px;">' + data.opt_type + '</td>' +
				  '</tr>' : '' ) +
				'<tr valign="top">' +
				'<td style="padding-right: 2px;padding-top: 4px;"><b>References: </b></td>' +
				'<td style="padding-top: 4px;">' + this.makeReferences( data.sources ) + '</td>' +
				'</tr></table>' +
				"</td></tr></table>";
		} catch ( ex ) {
			sfw.debug.trigger( ex, 'sfw.cve in makeHtml()' );
		}
		return html;
	}

	this.fetchData = function() {
		var me = this;
		Ext.Ajax.request({
			url: sfw.util.constructApiRequest( this.dataProviderURL )
			,success: function( data ) {
				var response = {};

				var html = '';

				try {
					response = Ext.util.JSON.decode( data.responseText );
					html = me.makeHtml( response.data );
				} catch ( ex ) {
					sfw.debug.trigger( ex, 'sfw.cve in fetchData() - ajax' );
				}

				me.populate( html );

			}
			,failure: function() {
				Ext.Msg.alert( "Error", "Cannot fetch advisory data" );
			}
		});
	}

	// Call this to properly apply the CLS to the panel
	this.populate = function( data ) {
		var newItem = {};

		newItem.cls = 'ContentPadding';
		newItem.border = false;
		newItem.html = data;
		newItem.autoScroll = true;
		this.interface.removeAll();
		this.interface.add( newItem );
		this.interface.doLayout();
	}

	this.interface = new sfw.Panel({
		autoScroll: true
	});

	this.fetchData();

	return this;
}

sfw.cve.view = function( cveId ) {

	var cve = new sfw.cve.create( cveId );

	var win = new Ext.Window({
		layout: 'fit'
		,width: 900
		,height: 500
		,items: cve.interface
		,title: 'CVE Reference: ' + cveId
		,maximizable: true
		,constrainHeader: true
		// ,renderTo: sfw.ui.center.getEl()
		,bbar: [ '->'
				 // ENHANCEMENT: #advisory #port
				 // handle pdf functionality since it is a global object now
				 ,{
					 text: 'Download PDF'
					 ,handler: function() {
						 window.open( sfw.util.constructApiRequest( 'action=pdf&which=cve&cveid=' + cveId ), '_blank', 'width=800,height=600');
					 }
				 }
				 ,'-'
				 ,{
					 text: 'Close'
					 ,handler: function() {
						 win.close();
					 }
				 }
			   ]
		,listeners: {
			show: sfw.sharedFunctions.doSize.createDelegate( this ) // ENHANCEMENT: #advisory #port, doSize is a local functiona in a global file
		}
	})

	win.show();
}