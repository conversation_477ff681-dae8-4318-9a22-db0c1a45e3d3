/**
 * @file reportGenerator.js
 * Provides the common interface for the generate reports page, incorporating all relevant/possible report elements.
 *
 *
 */
sfw.reportGenerator = {};
sfw.reportGenerator.reportElements = {};
sfw.reportGenerator.external = {};

/**
 * Called by the reporting page in the UI - this loads all the relevant reporting elements that are possible, given
 * the users active modules.
 *
 * @return
 * The main reporting page container
*/
sfw.reportGenerator.create = function( parent, sourceType ) {
	try {
		var self = this;

		this.reportId = 0;
		this.configurationString = '';

		this.bbar = {};

		this.reportElementsArray = [];
		this.reportElementsUIArray = [];
		this.actionProviderURL = 'action=reporting&which=';
		this.saveAction = 'save';
		this.editAction = 'edit';

		// Build the objects registered in the report elements array (sfw.reportGenerator.reportElements)
		for ( var key in sfw.reportGenerator.reportElements ) {
			// Control which ones we add based on which modules a user has enabled
			var newReportElement;
			var length = 0;
			//Because the checkModules function actually requires a module, even if logically it is not needed
			//add an exception for the csi when the modules are not defined
			if (
				( sourceType === 'csi' && typeof sfw.reportGenerator.reportElements[key].modules === 'undefined' )
				|| sfw.util.checkModules( sfw.reportGenerator.reportElements[key].modules )
			) {
				newReportElement = new sfw.reportGenerator.reportElements[key].create();

				// If this is a report element that should be available externally
				// (i.e., the config modules), make sure it is.
				if ( 'undefined' !== typeof(sfw.reportGenerator.reportElements[key].externalKey) ) {
					var externalKey = sfw.reportGenerator.reportElements[key].externalKey;
					sfw.reportGenerator.external[externalKey] = newReportElement;
				}

				this.reportElementsArray.push( newReportElement );
				this.reportElementsUIArray.push( newReportElement.interface );
				this.reportElementsUIArray.push(new Ext.Spacer({height: 10}));
				length += 1;
			}
		}

		this.resetAll = function() {
			this.reportId = 0;
			for ( i = 0; i < this.reportElementsArray.length; i++ ) {
				var element = this.reportElementsArray[i];
				if ( !( typeof element.reset == 'undefined' ) ) {
					element.reset();
				}
			}
		}

		this.validateAll = function () {
			var errorMessage = '';
			for ( i = 0; i < this.reportElementsArray.length; i++ ) {
				var element = this.reportElementsArray[i];
				if ( typeof element.validate == 'undefined' ) {
					continue;
				} else {
					response = element.validate();
					if ( !( response === true ) ) {
						Ext.Msg.alert( 'Error - ' + response.reportingElement, response.msg );
						return false;
					}
				}
			}
			return true;
		}

		// Collection configuration settings from all report elements
		this.collectAll = function() {
			var allParams = {};

			try {
				for ( i = 0; i < this.reportElementsArray.length; i++ ) {
					var element = this.reportElementsArray[i];
					if ( !( typeof element.name == 'undefined' ) && !( typeof element.collect == 'undefined' ) ) {
						var params = element.collect();
						for ( var key in params ) {
							allParams[element.name + '_' + key] = params[key];
						}
					}
				}
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
				return false;
			}
			return allParams;
		}


		// Load configuration settings in all report elements
		this.loadAll = function ( configurationAllElements ) {

			if ( typeof configurationAllElements == 'undefined' ) {
				return;
			}
			this.reportId = configurationAllElements.id;
			this.configurationString = configurationAllElements.configuration;

			try {
				// Parse the configuration string and make an object
				// The configuration string has the following format (spaces are added for clarity):
				// module_id_1 : configuration1 ; configuration2 ; | module_id_2 : configuration1 ; configuration2 ;
				var configuration = {};
				var configurationStringArray = this.configurationString.split('|');
				for ( i = 0; i < configurationStringArray.length; i++ ) {
					// get the configuration settings for the report element / module.
					var delimiterIndex = configurationStringArray[i].indexOf( ':' );
					var moduleName = configurationStringArray[i].substring( 0, delimiterIndex );
					var moduleConfig = configurationStringArray[i].substring( delimiterIndex+1 ) ;
					if ( delimiterIndex > 0 ) {
						configuration[moduleName] = moduleConfig;
					}
				}

				// Parse through the report elements and call their load() function with
				// the corresponding configuration string
				for ( var i = 0; i < this.reportElementsArray.length; i++ ) {
					var element = this.reportElementsArray[i];
					if ( !( typeof element.name == 'undefined' ) && !( typeof element.load == 'undefined' ) ) {
						try {
							element.load( configurationAllElements, configuration[element.name] );
						} catch( ex ) {
							// Silently ignore if one of the modules fails to load
						}
					}
				}
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
				return false;
			}
			return true;
		}

		this.updateTitle = function ( type ) {
			if ( type == 'new' ) {
				this.window.setTitle('Configure New Report');
			} else if ( 'edit' ) {
				this.window.setTitle('View/Edit Report Configuration');
			}
		}

		this.saveReportConfiguration = function () {
			//ENHANCEMENT: load mask
			try {
				if ( !this.validateAll() ) {
					return; // The appropriate error message is displayed in the validateAll function.
				} else {
					var allParams = this.collectAll();
					if ( !( typeof this.reportId  == 'undefined' ) && ( this.reportId != 0 ) ) {
						allParams.report_id = this.reportId;
					}
					var numKeys = 0;
					for ( var key in allParams ) {
						numKeys++;
					}

					// Set URL based on sourceType - default is VIM
					var url = sfw.util.constructApiRequest( self.actionProviderURL + ( ( self.reportId > 0 ) ? self.editAction : self.saveAction ) );
					if ( 'csi' === sourceType ) {
						url = globals.apiPath() + '&' + self.actionProviderURL + ( ( self.reportId > 0 ) ? self.editAction : self.saveAction );
					}

					if ( numKeys > 0 ){
						allParams.source_type = sourceType;
						Ext.Ajax.request({
							url: url
							,params: allParams
							,success: function( response ) {
								var status = Ext.decode(response.responseText).status;
								switch ( status ) {
								case 0:
									parent.refresh();
									self.window.hide();
									// Construct the success message based on if this is no_email or not
									var messageText = 'mailed';
									if ( 'csi' === sourceType && allParams['CSI_RCPT_no_email'] ) {
										messageText = 'available for download';
									}
									// Note - once the no_email option exists in the vim, a similar condition should be added, or else the sourceType condition can be removed and the params type made generic, i.e., RCPT_no_email - TODO

									Ext.Msg.alert( 'Success', 'Configuration saved!  Your report will be automatically generated and ' + messageText + ' when the end of the selected data time frame is reached.' );
									break;
								case 1:
									Ext.Msg.alert( 'Error', 'Report configuration could not be saved. Appropriate options must be selected to generate a non-empty report.' );
									break;
								default:
									Ext.Msg.alert( 'Error', 'Unexpected error.' );
									break;
								}
							}
							,failure: function() {
								Ext.Msg.alert( 'Error', 'Unexpected error.' );
							}
						});

					}
					// ENHANCEMENT: unload mask
				}
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
			}
		}

		// UI
		// --
		this.bbar.saveButton = new Ext.Button({
			text: 'Save'
			,id: 'reportGeneratorSaveButton'
			,tooltip: 'Save report configuration'
			,disabled: sfw.sharedFunctions.readOnly( sourceType )
			,handler: this.saveReportConfiguration.createDelegate( this )
		});

		this.bbar.closeButton = new Ext.Button({
			text: 'Close'
			,tooltip: 'Close window'
			,disabled: false
			,handler: function () {
				self.window.hide();
			}
		});

		this.bbar.toolbar = new Ext.Toolbar({
			items: [
				'->'
				,this.bbar.saveButton
				,'-'
				,this.bbar.closeButton
			]
		});

		this.mainPanel = new sfw.Panel({
			layout: 'auto'
			,autoScroll: true
			,items: new sfw.Panel({
				padding: '20px 20px 20px'
				,layout: 'auto'
				,items: this.reportElementsUIArray
			})
		});

		this.interface = new sfw.Panel({
			layout: 'fit'
			,items: this.mainPanel
			,bbar: this.bbar.toolbar
		});

		// Get dynamic height/width in case of small browser window.
		var dynamicDimensions = sfw.sharedFunctions.getDynamicDimensions( 900, 950, self.interface );
 		var interfaceHeight = dynamicDimensions[0];
		var interfaceWidth = dynamicDimensions[1];

		this.window = new Ext.Window({
			title: '' // update title dynamically
			,modal: true
			,constrain: true
			,height: interfaceHeight
			,width: interfaceWidth
			,layout: 'fit'
			,items: this.interface
			,closeAction: 'hide'
			,listeners: {
				show: function() {
					self.window.center();
					self.mainPanel.body.dom.scrollTop = 0;
				}
			}
			,keys: [{ // Handle form ENTER key press
				key: [ Ext.EventObject.ENTER ]
				,handler: function ( keyCode, eventObject ) {
					if ( false !== sfw.sharedFunctions.readOnly( sourceType ) ) {
						// readonly users can't save so don't handle this ENTER keypress
						return;
					}
					var tagName = eventObject.getTarget().tagName;
				    if ( 'BUTTON' === tagName || 'TEXTAREA' === tagName ) {
				        // Don't handle pressing ENTER on buttons or textareas (if added in future)
						return;
				    }
		    		if ( false === Ext.getCmp( 'reportGeneratorSaveButton' ).disabled ) {
		    		    Ext.getCmp( 'reportGeneratorSaveButton' ).handler.call();
		    		}
				}
			}]
		});


	} catch ( ex ) {
		sfw.debug.trigger ( ex, "reportGenerator.create" );
	}

	return this;
}
