/**
 * @file multiselect.js
 * Provides a grid linked directly to the database or to a local database (Array/Json Store) .
 * Requires the global object grid.js.
 *
 * Constructor configuration options:
 *
 * datafields: array of datafield objects. A datafield object should consist of: { name: '', column: [{ extjs column config }] }
 * NOTE: Do not include the 'id' field, as it is included by default.
 *
 * Example:
 * new DataBoundGrid({
 *	datafields: [ { name: 'name', column: [{ header: 'Name', renderer: RENDERER, ... }]  } ]
 *	,title: 'Title'
 *	,store: { url: 'api/provider.php' }
 * })
 *
 * Provider:
 * {
 * data: [ id: 1, name: 'Name Content' ... ]
 * }
 *
*/
sfw.MultiSelect = function( config ) {
	// Construct the store fields
	var storeFields = [];
	var gridColumns = [];
	config.store = config.store || {};
	config.store.idProperty = config.store.idProperty || 'id';

	storeFields.push(  { name: config.store.idProperty }  );

	for ( var i = 0; i < config.datafields.length; i++ ) {
		if ( typeof config.datafields[i].name !== "undefined" ) {
			storeFields.push( { name: config.datafields[i].name } );
		}
	}

	// Construct the grid fields
	for ( var i = 0; i < config.datafields.length; i++ ) {
		if ( typeof config.datafields[i].column !== "undefined" ) {
			config.datafields[i].column.id = config.datafields[i].column.name || config.datafields[i].name;
			config.datafields[i].column.dataIndex  = config.datafields[i].column.dataIndex || config.datafields[i].name;
			config.datafields[i].column.sortable = config.datafields[i].column.sortable || false;
			gridColumns.push( config.datafields[i].column );
		}
		if ( typeof config.datafields[i].column === "undefined" && typeof config.datafields[i].name === "undefined" ) {
			gridColumns.push( config.datafields[i] );
		}
	}

	config.secuniaPaging = false;
	config.store.root = config.store.root || 'data';
	config.store.fields = config.store.fields || storeFields;
	config.columns = config.columns || gridColumns;

	config.frame = config.frame || false;
	config.border = config.border || false;
	config.store = config.store || {};
	config.autoExpandMax = config.autoExpandMax || 10000;
	config.sm =  config.sm || new Ext.grid.RowSelectionModel({ singleSelect: false });
	config.enableHdMenu = false;
	config.enableColumnMove = false;
	config.enableColumnResize = config.enableColumnResize || false;
	config.plugins = config.plugins || false;

	return new sfw.GridPanel( config );
}
