/**
 * @file basicWizard.js
 * Generates a basic wizard, that should be used as a part of a Panel OR an existing wizard.
 *
 * Configuration:
 *	pageArray: array of pages to be added
 *	nextText: text for the next button
 *	previousText: text for the previous button
 *	finishText: text for the finish button
 *	cancelText: text for the cancel button
 *	cancelHandler: function for the cancel button handler
 *	nextHandler: function for the next button handler
 *	previousHandler: function for the previous button handler
 *	finishHandler: function for the finish button handler
 *	frame: interface frame
 *	border: interface border
 *	finishDisabled: boolean - should the finish button for the last page of wizard be enabled or disabled by default
 *	cotinueFinish: boolean - default to false, if true continue and finish are the same, when reaching the last page -> continue becomes finish
 * Methods:
 *	switchPage(direction): -n to go back n steps, n to move forward n steps
*/

sfw.basicWizard = function( config ) {
	try {

		// direction constants
		this.FORWARD = 1;
		this.BACK = -1;

		this.pageStack = [];
		for ( i = 0; i < config.pageArray.length; i++ ) {
			this.pageStack.push( config.pageArray[i] );
		}

		if ( typeof config.continueFinish == "undefined" ) {
			config.continueFinish = false;
		}

		this.switchPage = function( direction ) {
			var activeItem = this.interface.activeItem;

			if ( typeof sfw.sharedFunctions.readOnly != "undefined" && sfw.sharedFunctions.readOnly() == true ) {
				this.nextButton.setDisabled( true );
				this.finishButton.setDisabled( true );
				return;
			}

			if ( typeof activeItem == "undefined" ) {
				activeItem = 0;
			}

			// If we are negative, make sure we can go back n steps
			if ( direction < 0 && activeItem >= Math.abs( direction ) ) {
				activeItem = activeItem + direction;
			}
			// If positive, make sure we have enough frames to move forward n steps
			if ( direction > 0 && ( activeItem + direction ) < this.pageStack.length ) {
				activeItem = activeItem + direction;
			}
			if ( direction === 0 ) {
				activeItem = 0;
			}

			// Set up default values for back/next/finish buttons properly
			if ( activeItem === 0 ) {
				this.previousButton.setDisabled( true );
			} else {
				this.previousButton.setDisabled( false );
			}

			if ( activeItem === this.pageStack.length - 1 ) { // Last step in the wizard
				if ( config.cotinueFinish == false ) { // Load the basic mechanism
					this.nextButton.setDisabled( true ); // Disable the next button
					if ( !config.finishDisabled ) {
						this.finishButton.setDisabled( false ); // Enable the finish button, if any
					}
				} else { // Load the special mechanism, replace Continue with Finish
					this.nextButton.setVisible( false );
					this.continueSeparator.setVisible( false );
					this.finishSeparator.setVisible( true );
					this.finishButton.setVisible( true );
				}
			} else { // We are in the middle of doing something
				if ( config.cotinueFinish == false ) { // Load the basic mechanism
					this.nextButton.setDisabled( false ); // Enable the next button
					this.finishButton.setDisabled( true );  // Disable the finish button
				} else { // Load the special mechanism, replace Finish with Continue
					this.nextButton.setVisible( true );
					this.continueSeparator.setVisible( true );
					this.finishSeparator.setVisible( false );
					this.finishButton.setVisible( false );
				}
			}

			this.interface.getLayout().setActiveItem( activeItem );
			this.interface.activeItem = activeItem;
		}

		this.nextButton = new Ext.Button({
			text: config.nextText
			,disabled: sfw.sharedFunctions.readOnly()
			,hidden: ( config.nextText == "" ? true : false )
			,handler: config.nextHandler
		});

		this.previousButton = new Ext.Button({
			text: config.previousText
			,disabled: true
			,hidden: ( config.previousText == "" ? true : false )
			,handler: config.previousHandler
		});

		this.finishButton = new Ext.Button({
			text: config.finishText
			,handler: config.finishHandler
			,hidden: ( config.finishText == "" || ( config.continueFinish == true && config.pageArray.length != 1 ) ? true : false )
			,disabled: true
		});

		this.finishSeparator = new Ext.Toolbar.Separator({
			hidden: ( config.previousText == "" || ( config.continueFinish == true && config.pageArray.length != 1 ) ? true : false )
		});

		this.continueSeparator = new Ext.Toolbar.Separator({
			hidden: ( ( config.nextText == "" ) || ( config.finishText == "" ) ? true : false )
		});

		var bbarItems = [
			'->'
			,this.previousButton
			,this.continueSeparator
			,this.nextButton
			,this.finishSeparator
			,this.finishButton
		];

		if ( typeof config.cancelHandler != "undefined" ) {
			if ( config.cancelHandler != null ) {
				this.cancelButton = new Ext.Button({
					text: config.cancelText
					,handler: config.cancelHandler
				});
				bbarItems.push( "-" );
				bbarItems.push( this.cancelButton );
			}
		}

		if ( typeof config.frame == "undefined" ) {
			config.frame = false;
		}
		if ( typeof config.border == "undefined" ) {
			config.border = false;
		}

		this.interface = new sfw.Panel({
			layout: 'card'
			,activeItem: 0
			,frame: config.frame
			,border: config.border
			,items: this.pageStack
			,bbar: new Ext.Toolbar({
				items: bbarItems
			})
		});

		return this;
	} catch ( ex ) {
		sfw.debug.trigger ( ex, "basicWizard" );
	}
}