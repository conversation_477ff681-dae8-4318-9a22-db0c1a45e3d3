 sfw.Window = sfw.extend( Ext.Window, function()  {
     return {
         constructor: function( config ) {
         	if (typeof config.constrainHeader === 'undefined' && typeof config.constrain === 'undefined') {
         		if (config.modal) {
		     		config.constrainHeader = true;
		     		config.renderTo = Ext.getBody();
		     	} else {
		     		if ( sfw.ui.center ) {
	 		     		config.constrainHeader = true;
			     		config.renderTo = sfw.ui.center.getEl()
			     	} else {
	 		     		config.constrainHeader = true;
			     		config.renderTo = Ext.getBody();
			     	}
		     	}
	     	}
             sfw.Window.superclass.constructor.call(this, config);
         }
     };
 },'sfwwindow');
