/**
* @class sfw.tasks.AjaxTask
* @extends sfw.tasks.Task
* a specialization of sfw.tasks.Task especially prepared for doing Ajax requests to a server.
*/
(function () {
	var AjaxTask = Ext.extend(sfw.tasks.Task, {

		/**
		* @cfg {String/Function} url (required)
		* The URL to which to send the request, or a function to call which returns a URL string.
		* If a function then it will be called in the scope specified using the scope option if pressent. Else the function will use the config object as scope.
		*
		* @cfg {Object/String/Function} params (optional)
		* An object containing properties which are used as parameters to the request, a url encoded string or a function to call to get either.
		* If a function then it will be called in the scope specified using the scope option if pressent. Else the function will use the config object as scope.
		*
		* @cfg {String} method (optional)
		* the HTTP method to use for the request. (defaults to "GET" if no parameters are being sent and to "POST" if parameters are being sent.)
		*
		* @cfg {Function} success (optional)
		* If a function then it will be called in the scope specified using the scope option if pressent. Else the function will use the config object as scope.
		*
		* @cfg {Function} failure (optional)
		* If a function then it will be called in the scope specified using the scope option if pressent. Else the function will use the config object as scope.
		*
		* @cfg {Object} scope (optional)
		* the scope in which to execute the functions. (defaults to the config object)
		*
		* @cfg {Boolean} [autoStart=true]
		* start the task automatic
		*
		* @cfg {Number} [concurrencyLevel=1]
		* How many simuataneous runs is allowed. any number less than 1 means infinite
		*
		* @cfg {Boolean} noLog (optional)
		* A flag to indicate weather logs about the cron are printed to the console (defaults to false)
		*
		* @cfg {Number} duration (optional)
		* The length of time in milliseconds to invoke the task before stopping it automatically (defaults to indefinite)
		*
		* @cfg {Number} repeat (optional)
		* the number of times to invoke the task before stopping it automatically (defaults to indefinite)
		*
		* @cfg {Number/Function} [interval=300000]
		* if interval is a number then it is the number of milliseconds to the next time the task should be executed.
		* If interval is a function then it depends on the return value of the function as defined below:
		* - Date   : the next time the task should be executed.
		* - Number : the number of milliseconds until the next time the task should be executed.
		* - else   : the default interval is used.
		*
		* @cfg {Number/Function} firstrun (optional)
		* ms until the first run of the task.
		*
		*/

		constructor: function (config) {
			var self = this,
				ajaxConfig;
			config = config || {};
			config.async = true;
			if (config.success && ! Ext.isFunction( config.success )) {
				sfw.debug.log('Error: AjaxTask with illegal config. if success is set it must be a function. File: tasks.js');
			}
			if (config.failure && ! Ext.isFunction( config.failure )) {
				sfw.debug.log('Error: AjaxTask with illegal config. if failure is set it must be a function. File: tasks.js');
			}

			ajaxConfig = Ext.applyIf( {
					success: function( response ) {
						var rt;
						if ( config.success ) {
							rt = config.success.call( config.scope || config, response );
						}
						self.callback(rt);
					},
					failure: function( response, opts ) {
						var rt;
						if (config.failure) {
							rt=config.failure.call( config.scope || config,  response, opts );
						}
						self.callback(rt);
					}
				}, config);
			config.run = function() {
				Ext.Ajax.request( ajaxConfig );
			};
			AjaxTask.superclass.constructor.call(this, config);
		}
	});

	sfw.tasks.AjaxTask = AjaxTask;

})();
