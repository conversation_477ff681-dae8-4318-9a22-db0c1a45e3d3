/**
 * @file print.js
 * Provides printing functionality.
 * The active page must provide function .print(), which will return the text formatted for printing.
 *
 * Requires util.js and ui.js.
 *
 * The object generates a button for the toolbar.
*/
sfw.printPage = {};

sfw.printPage.create = function() {
	/**
	 * Printing function.
	 * Will attempt to call the object's printContent method, otherwise call a generic printing mechanism
	*/
	this.print = function() {
		var printContent = null;
		if ( typeof sfw.ui.pages[sfw.util.activePage].printContent !== "undefined" ) {
			printContent = sfw.ui.pages[sfw.util.activePage].printContent();
		}

		if ( printContent == null ) {
			window.print();
		}
	}
}

/**
 * Print button
*/
sfw.printPage.printButton = new Ext.Button({
	text: 'Print'
	,cls: "x-form-toolbar-standardButton"
	,scale: 'large'
	,iconAlign: 'left'
	,icon: 'gfx/ui/Print_32.png'
	,tooltip: 'Print the active page'
	,handler: function() {
		sfw.printPage.print()
	}
});
