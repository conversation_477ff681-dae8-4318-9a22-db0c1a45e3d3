/*
 *	@file advisory.js
 *
 *	The global advisory file is used by the local objects.
 *
 *	Requirements:
 *	------------
 *	The local projects must over-ride the create() function.
 */

sfw.advisory = {};

/*
 *	The function takes html and renders it in a panel
 */
sfw.advisory.generatePanel = function( title, html ) {
	return new sfw.Panel({
		title: title
		,autoWidth: true
		,border: true
		,autoScroll: true
		,bodyStyle: 'margin-bottom: 5px'
		,items: [{
			border: false
			,autoScroll: true
			,padding: '5px'
			,html: "<table width='100%' height='100%'><tr valign='top'><td>" + html + "</td></tr></table>"
		}]
	})
}

sfw.advisory.formatImpact = function( impactArray ) {
	var impact = "";
	for ( var i = 0; i < impactArray.length; i++ ) {
		impact += impactArray[i].impact_type_name + "<br/>";
	}
	return impact;
}

sfw.advisory.generateWindowId = function( vulnId ) {
	return parseInt( vulnId, 10 )  + '_window';
};

sfw.advisory.changeLanguage = function( langId, vulnId, accountId ) {
	var w = Ext.getCmp( sfw.advisory.generateWindowId( vulnId )  );
	var wasMaximized = w.maximized ? true : false;
	// We close the old one BEFORE creating the new one, otherwise it will get caught in logic that we don't
	// create multiple instances of the same advisory window.
	w.close();
	sfw.advisory.view( vulnId
					   ,accountId
					   ,langId
					   ,{ maximized: wasMaximized }
					 );
}

sfw.advisory.formatLanguage = function( languages, vulnId, accountId ) {
	var html = "";

	var template = '<img onClick="sfw.advisory.changeLanguage( :LANGUAGE_CODE '
		+ ', ' + parseInt( vulnId, 10 )
		+ ', ' + parseInt( accountId, 10 )
		+ ');"'
		+ ' src="gfx/ui/:FLAG_IMAGE_NAME" style="border: 1px solid black;cursor: pointer;" border="0" alt="Advisory Available in :LANGUAGE_NAME">';
	var temp;

	if ( languages.english == true ) {
		temp = template.replace( ':FLAG_IMAGE_NAME', 'english.gif' );
		temp = temp.replace( ':LANGUAGE_NAME', 'English' );
		temp = temp.replace( ':LANGUAGE_CODE', '1' );
		html += temp;
	}
	if ( languages.danish == true ) {
		temp = template.replace( ':FLAG_IMAGE_NAME', 'danish.gif' );
		temp = temp.replace( ':LANGUAGE_NAME', 'Danish' );
		temp = temp.replace( ':LANGUAGE_CODE', '2' );
		html += temp;
	}
	if ( languages.german == true ) {
		temp = template.replace( ':FLAG_IMAGE_NAME', 'german.gif' );
		temp = temp.replace( ':LANGUAGE_NAME', 'German' );
		temp = temp.replace( ':LANGUAGE_CODE', '5' );
		html += temp;
	}

	return html;
}


sfw.advisory.makeWhere = function( whereInfo ) {
	var where = whereInfo.where_type_name;
	return where;
}

sfw.advisory.generateExtendedDescriptionPanel = function( extendedDescription ) {
	// Make sure we are allowed to display extended description
	if ( !sfw.options.OPT_ADVISORY_EXTENDED_DESCRIPTION ) {
		return null;
	}
	var html = '';

	// Some quick sanity checks - check that we have everything we need.
	// NOTE - code for extended description is 8.
	if ( extendedDescription &&
		 extendedDescription.text_text &&
		 8 === parseInt( extendedDescription.text_type_value, 10 ) &&
		 "" != extendedDescription.text_text ) {

		html = extendedDescription.text_text;
		return sfw.advisory.generatePanel( 'Extended Analysis', html );
	} else {
		return null;
	}
}

/*
 *	- The function generates the advisory details extracted from the data param
 *	- If there are some details specific to the local project, the formatAdditionalDetails callback
 *	can be used to add to the advisory details
 */
sfw.advisory.generateDetailsPanel = function( data, formatAdditionalDetails ) {

	// Loop through the sections to be generated.
	// If the account doesn't have the option to view the section, we exclude it in the
	// sections array

	var sections = [ data.description
					 ,sfw.options.OPT_ADVISORY_EXTENDED_DATA ? data.ratingReason : {}
					 ,data.solution
					 ,sfw.options.OPT_ADVISORY_EXTENDED_DATA ? data.extendedSolution : {}
					 ,data.credits
					 ,data.changelog
					 ,data.originalAdvisory
					 ,data.otherReferences ];

	var content = '';

	var size = sections.length;
	for ( var i=0; i < size; i++ ) {
		section = sections[i];

		if ( !section || !section.text_text ) {
			continue;
		}

		content += '<b>' + section.text_type_name + ':</b><br>' +
			Ext.util.Format.htmlDecode( section.text_text ) + '<br><br>';

	}

	if ( typeof formatAdditionalDetails !== 'undefined' ) {
		content += formatAdditionalDetails( data );
	}

	return sfw.advisory.generatePanel( 'Secunia Advisory Details', content );
}

sfw.advisory.generateDeepLinksPanel = function( deepLinks, accountId ) {

	// Make sure we are allowed to display deep links
	if ( !sfw.options.OPT_ADVISORY_DEEP_LINKS ) {
		return null;
	}
	var links = {};
	var html = "";
	var htmlTemp = "";
	var found = false;
	for ( key in deepLinks ) {
		links[key] = [];
		for ( name in deepLinks[key] ) {
			if ( typeof deepLinks[key][name] == "string" ) {
				links[key].push( deepLinks[key][name] );
			}
		}
	}

	for ( key in links ) {
		if ( key.length != 0 ) {
			for ( var i = 0; i < links[key].length; i++ ) {
				htmlTemp += sfw.advisory.formatDeepLink( key, links[key][i], accountId );
				found = true;
			}
			if ( found ) {
				htmlTemp = '<table width="100%">\
						<tr><td style="width: 180px;"><b>' + key + '</b></td><td>' + htmlTemp + '</td></tr>\
					</table>';
				html += htmlTemp;
			}
		}
		found = false;
		htmlTemp = "";
	}
	if ( html != "" ) {
		return sfw.advisory.generatePanel( 'Deep Links', html );
	} else {
		return null;
	}
}

sfw.advisory.renderSAID = function( vulnId, accountId ) {
	return "<a title='Click to view advisory' href='javascript:sfw.advisory.view(" + vulnId + "," + accountId + ")'>" + 'SA' + vulnId + "</a>";
}

/**
 * Function for formatting a 'deepLink' link
 * @param type
 *	String link type. Valid types: SAID, CERT-VN, CVE, BID, NESSUS
 * @return
 *	String URL to the corresponding page
*/
sfw.advisory.formatDeepLink = function( type, value, accountId ) {
	var href = value;

	switch ( type ) {
	case "SAID":
		return sfw.advisory.renderSAID( value, accountId) + '<br/>';
		break;
	case "CERT-VN":
		href = "http://www.kb.cert.org/vuls/id/" + value;
		break;
	case "CVE":
		return "<a href='javascript:sfw.cve.view(\"CVE-" + value + "\")'>" + 'CVE-' + value + "</a><br/>";
		break;
	case "BID":
		href = "http://www.securityfocus.com/bid/" + value;
		break;
	case "NESSUS":
		href = "http://www.nessus.org/plugins/index.php?view=single&id=" + value;
		break;
	case "ST":
		href = "http://www.securitytracker.com/id?" + value;
		break;
	case "OSVDB":
		href = "http://www.osvdb.org/" + value;
		break;
	}

	return '<a href="' + href + '" target="_blank">' + value + "</a><br/>";
}

sfw.advisory.formatCveReferences = function( cveArray, formatLinks ) {
	var cve = "";
	for ( var i = 0; i < cveArray.length; i++ ) {

		if ( formatLinks ) {
			cve += "<a href='javascript:sfw.cve.view(\"" + cveArray[i].cve_reference + "\")'>" + cveArray[i].cve_reference + "</a>";
		} else {
			cve += cveArray[i].cve_reference;
		}

		if ( typeof cveArray[i].cvss_vector != "undefined" ) {
			cve += " / ";
			if ( formatLinks ) {
					cve += "<a href='javascript:sfw.cvss.view(\"" + cveArray[i].cve_reference + "\",\"" + cveArray[i].cvss_vector + "\")'>CVSS: " + cveArray[i].BaseScore + "</a> " + cveArray[i].cvss_vector;
			} else {
				cve += "CVSS: " + cveArray[i].BaseScore + " ";
			}
		}
		cve += "<br/>";
	}
	if ( cve != "" && cve != "<br/>" ) {
		return cve;
	}

	return null;
}

// Use formatLinks to create links of the cvss scores.
sfw.advisory.formatCvssScore = function( data, formatLinks ) {
	if ( typeof data.details.vuln_cvss_score != "undefined" &&
		 data.details.vuln_cvss_score != 0 ) {
		html = '<tr><td>'
			+ ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss_vector +	'\')">' ) : '' )
			+ 'Base: ' + data.details.vuln_cvss_score + ', Overall: ' + data.details.cvss_data.OverallScore
			+ ( formatLinks ? '</a> ' : ' ' )
			+ data.details.vuln_cvss_vector + '</td></tr>';
		return html;
	}

	return null;
}

// Use formatLinks to create links of the cvss scores.
sfw.advisory.formatCvss3Score = function( data, formatLinks ) {
	if ( typeof data.details.vuln_cvss3_score != "undefined" &&
		data.details.vuln_cvss3_score != 0 ) {
		html = '<tr><td>'
			+ ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss3_vector +	'\')">' ) : '' )
			+ 'Base: ' + data.details.vuln_cvss3_score + ', Overall: ' + data.details.cvss3_data.OverallScore
			+ ( formatLinks ? '</a> ' : ' ' )
			+ data.details.vuln_cvss3_vector + '</td></tr>';
		return html;
	}

	return null;
}

sfw.advisory.formatCvss4Score = function( data, formatLinks ) {
	if ( typeof data.details.vuln_cvss4_score != "undefined" &&
		data.details.vuln_cvss4_score != 0 ) {
		html = '<tr><td>'
			+ ( formatLinks ?  ( '<a href="javascript:sfw.cvss.view(\'' + data.details.vuln_id + '\',\'' + data.details.vuln_cvss4_vector +	'\')">' ) : '' )
			+ 'Base: ' + data.details.vuln_cvss4_score + ', CVSS-BT: ' + data.details.cvss4_data.BaseThreatScore
			+ ( formatLinks ? '</a> ' : ' ' )
			+ data.details.vuln_cvss4_vector + '</td></tr>';
		return html;
	}

	return null;
}



/*
 * Provides a generic "Quick Link" to view advisory object. It does not fit as a standalone object.
 */
sfw.advisory.view = function( vulnId, accountId, language, options ) {
	if ( typeof language == "undefined" ) {
		language = 1;
	}
	// make sure we pass around accountID as int, and default to 0
	if ( !accountId ) {
		accountId = 0;
	}
	accountId = parseInt( accountId, 10 );

	// If a user double clicks the link instead of clicking, we try to generate this window multiple
	// times which causes issues.  Assign a unique ID to an advisory window, and only allow it to be
	// generated once.  If a window already exists, bring it into focus (to the front)
	var winId = sfw.advisory.generateWindowId( vulnId );

	var win = Ext.getCmp( winId );
	if ( typeof win !== 'undefined' ) {
		if ( win.isVisible() ) {
			win.toFront();
			return;
		}
	}

	// Create a panel that contains all the required advisory information
	// The sfw.advisory.create() must be overriden by the local advisory object

	var interface = {};
	try {
		interface = new sfw.advisory.create( vulnId, accountId, language );
	} catch( ex ) {
		sfw.debug.trigger( ex, 'sfw.advisory.view()' );
		return false;
	}

	win = new Ext.Window({
		layout: 'fit'
		,id: winId
		,width: 900
		,height: 600
		,items: interface
		,name: 'advisoryWindow'
		,title: 'SA' + vulnId
		,maximizable: true
		,constrainHeader: true
		,maximized: ( typeof options !== 'undefined' ) && options.maximized
		// ,renderTo: sfw.ui.center.getEl()
		,listeners: {
			show: sfw.sharedFunctions.doSize.createDelegate( this )
		}
	});

	interface.parent = win;

	win.show();
}

sfw.advisory.create = function() {

	/*
	 * Stub function
	 *
	 * Needs to be implemented by the local project
	 */

	throw new Error( 'sfw.advisory.create() is not implemented' );

}