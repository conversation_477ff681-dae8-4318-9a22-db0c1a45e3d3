sfw.forumAttachment = {};


// enhanncement: this function can go into the local domain
// and dependency on the this.params can be removed

sfw.forumAttachment.download = function () {
	sfw.debug.log( 'sfw.forumAttachment.download() not implemented' );

	var url = '';
	var attachmentId = '';

	try {
		url = this.params.url;
		attachmentId = this.params.attachmentId;
	} catch ( ex ) {
		sfw.debug.trigger( ex, 'sfw.forumAttachment.download()' );
		return false;
	}


	var self = this;

	this.decodedContents = '';

	Ext.Ajax.request({
		url: url
		,params: {
			attachment_id: attachmentId
		}
		,success: function( data ) {
			try {
				self.container.removeAll();

				response = Ext.util.JSON.decode( data.responseText );
				var contents = response.contents;

				var panel = new sfw.Panel({
					html: 'No contents.'
				});

				if ( contents ) {
					self.decodedContents = Ext.util.Format.htmlDecode( contents );
					self.decodedContents = self.decodedContents.replace(/<br \/>/g,'\r\n');
					panel = new sfw.Panel({
						height: self.container.getHeight() - 10
						,items: new Ext.form.TextArea({ cls: 'ContentPadding', value: self.decodedContents })
					});
				}

				self.container.add( panel );

				self.container.doLayout();
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'sfw.forumAttachment.download() - ajax request' );
				self.container.add( new sfw.Panel({ html: 'Error!'}) );
				self.container.doLayout();
			}
		}
		,failure: function() {
			Ext.Msg.alert( 'Error', 'Unexpected error!' );
		}
	});

	return true;

}

// Create interface
sfw.forumAttachment.create = function() {

	this.container = new sfw.Panel({
		region: 'center'
		,layout: 'anchor'
		,autoScroll: true
		,padding: '5px 5px 5px 5px'
		,frame: false
		,items: new sfw.Panel({ html: 'Loading..' })
	});

	this.interface = new sfw.Panel({
		autoScroll: true
		,parent: null
		,layout: 'fit'
		,listeners: {
			afterrender: this.download.createDelegate(this)
		}
		,items: [
			this.container
		]
	});

	return this.interface;

}

// View attachment
sfw.forumAttachment.view = function( params ) {

	this.params = params;

	var interface = this.create();

	var self = this;

	var win = new Ext.Window({
		layout: 'fit'
		,width: 900
		,height: 600
		,items: interface
		,title: 'View Attachment' // todo: update title?
		,maximizable: true
		,listeners: {
			show: sfw.sharedFunctions.doSize.createDelegate( this )
		}
		,bbar: [
			'->'
			,{
				text: 'Save'
				,handler: function() {
						try {
							var arr = sfw.external.fWUICreateFastArray();
							var obj = sfw.external.fWUICreateFastObject();

							obj['xml'] = self.decodedContents;
							obj['filename'] = 'package.xml';
							arr.push( obj );
							sfw.external.fWUIExportFileDialog( arr );

						} catch ( ex ) {
							sfw.debug.trigger( ex, 'sfw.forumAttachment.view() - save file' );
						}
				}
			}
			,'-'
			,{
				text: 'Close'
				,handler: function() {
					win.close();
				}
			}
		]
	});

	interface.parent = win;

	win.show();
}

sfw.forumAttachment.extractFilename = function( file) {
	var nameArray = file.split('\\');
	var name = nameArray[ nameArray.length-1 ];
	return name;
}

// Takes the file with path
sfw.forumAttachment.getContents = function( file) {
	var contents = sfw.external.fWUIReadFile( file );

	// should we do some validations here?

	return contents;
}

// Function for retrieving file meta data saved at the server
// At the moment the function only returns the file name
sfw.forumAttachment.requestMetaData = function( params, callback ) {

	var url = '';
	var attachmentId = 0;

	try {
		url = params.url;
		attachmentId = params.attachmentId;
	} catch ( ex ) {
		sfw.debug.trigger( ex, 'sfw.forumAttachment.requestMetaData()', 'Invalid params' );
		return false;
	}

	var metaData = {};

	Ext.Ajax.request({
		url: url
		,params: {
			attachment_id: attachmentId
		}
		,success: function( data ) {
			var response = {};
			try {
				var metadata = {};
				response = Ext.util.JSON.decode( data.responseText );
				metadata = response.data;
				callback.fn.call( callback.scope, metadata );
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'sfw.forumAttachment.requestMetaData() - ajax request' );
				return false;
			}
		}
		,failure: function() {
			Ext.Msg.alert( "Unexpected Error", "Unable to fetch metadeta" );
		}
	});

	return true;
}

sfw.forumAttachment.removeAttachment = function( params, callback ) {

	var url = '';

	try {
		url = params.url;
	} catch ( ex ) {
		sfw.debug.trigger( ex, 'sfw.forumAttachment.requestMetaData()', 'Invalid params' );
		return false;
	}

	Ext.Ajax.request({
		url: url
		,success: function( data ) {
			var response = {};
			try {
				callback.fn.call( callback.scope );
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'sfw.forumAttachment.removeAttachment() - ajax request' );
				return false;
			}
		}
		,failure: function() {
			Ext.Msg.alert( "Unexpected Error", "Unable to remove attachment" );
		}
	});

	return true;
}