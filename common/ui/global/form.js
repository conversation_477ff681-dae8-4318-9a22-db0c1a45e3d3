/**
 * @file form.js
 * Provides ExtJS form wrapper.
 * It supports default configuration options, and an extra decodeHtml option for the form fields. When set the html data passed to any field is html decoded.
 * When using decodeHtml ensure the field renders the data as text, not as html.
 *
 * Public methods:
 *
 * .load( config )
 *
 * .setValues( config )
*/

sfw.Form = function( config ) {
	config.frame = config.frame || false;
	config.border = config.border || false;
	config.bodyStyle = config.bodyStyle || 'padding: 15px';
	config.defaultType = config.defaultType || 'textfield';
	config.labelAlign = config.labelAlign || 'right';
	config.width = config.width || 430;
	config.waitMsgTarget = ( typeof config.waitMsgTarget !== 'undefined' ? config.waitMsgTarget : true );
	config.defaults = config.defaults || {
		width: 300
	};

	var form = new Ext.form.FormPanel( config );

	/**
	 * Function for setting text field values, similar to .getForm().setValues() except for the form's decodeHtml option.
	 * @param config
	 *	Object extjs BasicForm setValues() configuration options.
	 * @return
	 *	The result of the BasicForm setValues()
	*/
	form.setValues = function( config ) {
		for ( var key in config ) {
			if ( form.getForm().findField( key ) != null ) {
				if ( form.getForm().findField( key ).initialConfig.decodeHtml === true ) {
					config[key] = Ext.util.Format.htmlDecode( config[key] );
				}
			}
		}

		return form.getForm().setValues( config );
	}

	/**
	 * Function for loading form data, through an Ajax request. Similar to .getForm().load() except for the form's decodeHtml option.
	 * @param config
	 *	Object extjs Ajax request configuration object.
	*/
	form.load = function( config ) {
		var setValues = this.setValues;
		config.success = config.success || function( response ) {
			setValues( Ext.decode( response.responseText ).data );
			if ( config.resume ) {
				config.resume();
			}
		}
		Ext.Ajax.request( config );
	}

	return form;
}
