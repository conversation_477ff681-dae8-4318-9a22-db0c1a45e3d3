/**
 * The sfw.Promise class can be used to handle asynchrnous code. It is
 * a very naive implementation of something that resemble 'Promises'
 *
 * var asynchronousAddition = function( number ) {
 * 	var p = sfw.Promise( function( resolve ) {
 * 			// do something asynchronous e.g. increment asyncronously
 * 			window.setTimeout( function( number ) {
 * 					resolve( data+1 );
 * 				}, 1000 );
 * 		} );
 * 	return p;
 * };
 *
 * // The following prints 5 when called.
 * asynchronousAddition( 4 )
 * 	.then( function( data ) {
 * 			console.log( data);
 * 		} )
 * 	.done();
 *
 *  @see For a simple implementation, see sfw.Ajax (Ajax.js)
 */

!function() {

	// Implementation copied from Ext-js
	var Promise = function( fn ) {

		this.fn = fn; // Initial function that belongs to the promise
		this.thens = this.thens || []; // List of functions to be executed
		this.tail = null; //

		/**
		 * Get the next function to be executed
		 */
		this.getNextFn = function() {
			return this.thens.shift();
		};

		this.resolveNext = function() {
			if ( this.isAbort ) {
				this.thens = [];
			}
			var fn;
			if ( this.thens.length === 0 ) {
				if ( this.tail ) {
					fn = this.tail;
					this.tail = null;
				} else {
					return;
				}
			} else {
				fn = this.thens.shift();
			}
			var res = fn.apply( this, arguments ); // @todo: scope
			if ( res instanceof Promise ) {
				var p = res;
				var me = this;
				//p.fn.apply( this, [ me.resolveNext.bind( me ) ] );
				p.tail = me.resolveNext.bind( me );
				p.done();
			} else {
				this.resolveNext.apply( this, [res] );
			}
		};

		this.then = function( onFulfill, onReject ) {
			this.thens.push( onFulfill );
			return this;
		};

		/**
		 * This method is deprecated
		 */
		this.abort = function() {
			this.isAbort = true;
		};

		/**
		 * This is not tested.
		 */
		this.parallel = function( array, callback ) {
			var resolvedItems = 0;
			var processed = function() {
				if ( resolvedItems === array.length ) {
					callback();
				}
			};
			for ( var i=0 ; i<array.length ; ++i, ++resolvedItems ) {
				var p = array[i];
				if ( p instanceof Promise ) {
					p.then( processed ).done();
				} else {
					p();
					processed();
				}
			}
			return this;
		};

		this.series = function( array ) {
			var p = new sfw.Promise( function( resolve ) {
				resolve();
			});
			p.thens.append( array );
			this.thens.push( p )
			return this;
		};

		this.done = function() {
			this.fn.apply( this, [ this.resolveNext.bind( this ) ] );
		};
	};

	sfw.Promise = Promise;

	/**
	 * Maps a function on each element of an array. Returns a Promise object.
	 * @param {Array} array The contents of the array are not restricted.
	 * @param {Function} func This function will be called on each
	 * element of the array. In each call, the array element is
	 * available as 'this' in the function.
	 * The function can return a Promise. which will be fully resolved
	 * before applying the function to the next element in the array.
	 * The function can also return another function, which will be
	 * called before moving on the next array element.
	 * Any other return value is ignored.
	 * If the function throws an exception, then the iteration process
	 * will abort without processing any further array elements.  Any
	 * 'then' functions that have been set on the returned Promise
	 * object will still be resolved.
	 * @TODO: if an exception is throw, we should rely on the onReject functionality of Promises.
	 */
	var map = function( array, func ) {
		var mk = function( t ) { return function() { return t }; };
		var inner = new Promise( function( resolve ) {
			var f = function() {
				if ( ! array.length ) {
					return;
				}
				var r;
				try {
					r = func.apply( array.shift() );
					if ( r instanceof Promise ) {
						inner.then( mk(r) );
					} else if ( r instanceof Function ) {
						inner.then( r );
					}
					inner.then( f );
				} catch ( ex ) {
					//TODO: call reject
				}
			}
			inner.then( f );
			resolve();
		} );
		var outer = new Promise( function( resolve ) {
			resolve();
		} );
		outer.then( mk(inner) );
		return outer;
	};

	sfw.Promise.map = map;
}();
