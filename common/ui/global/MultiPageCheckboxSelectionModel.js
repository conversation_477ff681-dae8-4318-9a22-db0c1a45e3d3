Ext.ns('sfw');
sfw.MultiPageCheckboxSelectionModel = sfw.extend(Ext.grid.CheckboxSelectionModel, function() {

	function selectionChange( selModel ) {
		var selectedRecords = selModel.getSelections(true);
		if (selectedRecords.length === 0) {
			return;
		}
		this.grid.store.data.each(function(i) {
			this.multipageSelections.removeKey(i.id);
		}, this);

		Ext.each(selectedRecords, function(i) {
			this.multipageSelections.add(i.id, i);
		}, this);
	}
	function restoreSelection () {
		this.suspendEvents();

		this.grid.store.data.each(function(i) {
			if (this.multipageSelections.key(i.id)) {
				this.selectRecords([i], true);
			}
		},this);
		this.resumeEvents();
	}

	function rowSelect() {
		if ( this.multipageSelections.getCount() === 1 ) {
			this.fireEvent('selectionAquired', this);
		}
	}
	function rowDeselect() {
		if ( this.multipageSelections.getCount() === 0 ) {
			this.fireEvent('selectionReleased', this);
		}
	}


	return {
		multipageSelections: new Ext.util.MixedCollection()
		,singleSelect: false
		,restoreSelection: restoreSelection.createDelegate(this)
		,initEvents: function() {
			sfw.MultiPageCheckboxSelectionModel.superclass.initEvents.call(this);
			this.grid.getStore().on('load', restoreSelection, this);
			this.on('rowselect', rowSelect , this);
			this.on('rowdeselect', rowDeselect , this);
		}
		,constructor: function( config ) {
			config = config || {};
			if (config.listeners && config.listeners.selectionchange) {
				config.listeners.selectionchange.createInterceptor( selectionChange, this );
			} else {
				if (! config.listeners ) {
					config.listeners= { };
				}
				config.listeners.selectionchange = selectionChange.createDelegate( this );
			}
			this.addEvents(
				'selectionReleased',
				'selectionAquired'
			);

			sfw.MultiPageCheckboxSelectionModel.superclass.constructor.call(this, config);
		}
		,getCount: function( bPage ) {
			if ( bPage === true ) {
				return sfw.MultiPageCheckboxSelectionModel.superclass.getCount.call(this);
			}
			return this.multipageSelections.getCount();
		}
		,onRefresh: function() {
			var ds = this.grid.store,
				s = this.getSelections(true),
				i,
				len = s.length,
				index, r;

			this.silent = true;
			this.clearSelections(true,true);
			for( i=0; i<len; i++) {
				r = s[i];
				if ((index = ds.indexOfId(r.id)) != -1) {
					this.selectRow(index, true);
				}
			}
			if (s.length != this.selections.getCount()) {
				this.fireEvent('selectionchange', this);
			}
			this.silent = false;
		}
		,each: function( fn, scope, bPage ) {
			var s = this.getSelections( bPage ),
				i = 0,
				len = s.length;

			for (; i< len; i++) {
				if (fn.call( scope || this, s[i], i) === false) {
					return false;
				}
			}
			return true;
		}
		,clearSelections: function ( fast, bPage ) {
			if ( bPage ) {
				sfw.MultiPageCheckboxSelectionModel.superclass.clearSelections.call(this, fast);
			} else {
				sfw.MultiPageCheckboxSelectionModel.superclass.clearSelections.call(this, fast);
				if (this.isLocked()) {
					return;
				}
				if ( fast !== true ) {
					this.multipageSelections.clear();
				} else {
					this.multipageSelections.clear();
				}
			}
		}
		,getSelections: function( bPage ) {
			if ( bPage === true ) {
				return sfw.MultiPageCheckboxSelectionModel.superclass.getSelections.call(this);
			}

			return [].concat(this.multipageSelections.items);

		}
		,isIdSelected: function( id, bPage ) {
			if ( bPage ) {
				return sfw.MultiPageCheckboxSelectionModel.superclass.isIdSelected.call(this, id);
			}
			return this.multipageSelections.indexOfKey( id ) !== -1;
		}
		,selectRow: function(index, keepExisting, preventViewNotify) {
			sfw.MultiPageCheckboxSelectionModel.superclass.selectRow.call(this, index, keepExisting, preventViewNotify);
		}
		,deselectRow: function(index, keepExisting, preventViewNotify) {
			sfw.MultiPageCheckboxSelectionModel.superclass.deselectRow.call(this, index, keepExisting, preventViewNotify);
		}

	};
});
