sfw.communityForum = {};

sfw.communityForum.init = function() {
	//This flag signals if there is new content on the forums that the user has not reviewd
	this.hasUnreadContent = false;
	//When this flag is null, no new image will be displayed throughout the forums
	this.lastCheck = null;
	//This is the first check before the last
	this.previousCheck = null;
	//This is a flag to avoid making request if the user has no associated profile
	this.hasForumProfile = true;
	//This is a special parameter that can be used when first displaying the grid,
	//to search the forums for a program id and its name
	this.startProductId;
};

//Makes a request to get the last forum activity date
//For the CSI, this method should not be called while the community forum
//window is open
sfw.communityForum.getLastActivity = function( callback ) {
	if ( sfw.communityForum.hasForumProfile ) {
		Ext.Ajax.request({
			url: globals.apiPath() + '&action=community&which=hasUnreadContent'
			,success: function( data ) {
				var response = {};
				try {
					response = Ext.util.JSON.decode( data.responseText );
					if ( response.success ) {
						response = response.data;
						sfw.communityForum.hasUnreadContent = response.hasUnreadContent;
						sfw.communityForum.previousCheck = response.lastCheck;
						if ( response.lastCheck != null ) {
							sfw.communityForum.lastCheck = response.lastCheck;
						} else {
							sfw.communityForum.lastCheck = null;
						}
						if ( typeof callback != 'undefined' && callback != null ) {
							callback.fn.call( callback.scope );
						}
					} else {
						throw { number: 1, message: 'Could not fetch the last activity date of the forum beacuase the current user has no forum profile' };
					}
				} catch ( ex ) {
					sfw.communityForum.hasForumProfile = false;
				}
			}
			,failure: function() {
				sfw.communityForum.hasForumProfile = false;
			}
		});
	}
};

//Mark the current timestamp as the last check of the forum content for the current user
sfw.communityForum.setLastActivity = function( callback ) {
	if ( sfw.communityForum.hasForumProfile ) {
		Ext.Ajax.request({
			url: globals.apiPath() + '&action=community&which=setLastCheck'
			,method: 'POST'
			,success: function( data ) {
				var response = {};
				try {
					response = Ext.util.JSON.decode( data.responseText );
					if ( response.success ) {
						response = response.data;
						sfw.communityForum.hasUnreadContent = false;
						sfw.communityForum.previousCheck = sfw.communityForum.lastCheck;
						sfw.communityForum.lastCheck = response.lastCheck;
					} else {
						throw { number: 1, message: 'Could not set the last check date for the forum because the current user has no forum profile.' };
					}
				} catch ( ex ) {
					sfw.communityForum.hasForumProfile = false;
				}
				//The callback function is always called becuase it displays the forum
				if ( typeof callback != 'undefined' && callback != null ) {
					callback.fn.call( callback.scope );
				}
			}
			,failure: function() {
				sfw.communityForum.hasForumProfile = false;
				//The callback function is always called becuase it displays the forum
				if ( typeof callback != 'undefined' && callback != null ) {
					callback.fn.call( callback.scope );
				}
			}
		});
	} else {
		//The callback function is always called becuase it displays the forum
		if ( typeof callback != 'undefined' && callback != null ) {
			callback.fn.call( callback.scope );
		}
	}
};

sfw.communityForum.postedDateRenderer = function( value, metaData, record ) {
	// TODO - format the date
	return value;
};

sfw.communityForum.lastReplyDateRenderer = function( value, metaData, record ) {
	// If last reply === posted date, default to '-'
	var postedDate = record.data.posted;
	if ( value === postedDate ) {
		return '-';
	}

	// TODO - format the date
	return value;
};

sfw.communityForum.isAttachedRenderer = function ( value ) {
	try {
		value = parseInt( value, 10 );
		if ( value > 0 ) {
			return 'yes';
		} else {
			return 'no';
		}
	} catch ( ex ) {}
	return ' - ';
};

sfw.communityForum.topicTitleRenderer = function( value, metaData, record ) {
	var title = value;
	try {
		// Now check the last update to see if it is relatively new
		var lastUpdate = sfw.util.dateCreate( record.data.lastupdate ).getTime();
		var previousCheckTime = 0;
		if ( sfw.communityForum.previousCheck == null) {
			return title;
		}
		if ( sfw.communityForum.previousCheck != null ) {
			previousCheckTime = sfw.util.dateCreate( sfw.communityForum.previousCheck ).getTime();
		}

		if ( lastUpdate - previousCheckTime > 0 ) {
 			return sfw.sharedFunctions.fetchNewFlagImage() + '&nbsp' + title;
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, 'sfw.communityForum.topicTitleRenderer()' );
	}

	return title;
};

// This function is called anytime you want the main community forum overview page.
// The creatingObject can define a default setting to start the combo box on, and can set its own
// type in case of product specific functionality, i.e., CSI vs. PSI, vs. VIM, etc.
// It must also define its sourceType

// TODO - for now type distinguishes VIM vs CSI so we can construct the API request properly.  Presumably this is changing in the new structure to use the same sfw.util.constructApiRequest we use everywhere - get functional for now with the old globals.apiPath for CSI and change later.
sfw.communityForum.buildOverview = function( creatingObject ) {
	if ( "undefined" === typeof(creatingObject) ) {
		return;
	}

	if ( "undefined" === typeof(creatingObject.defaultForum) ) {
		creatingObject.defaultForum = 'all';
	}

	try {
		var self = creatingObject;

		var dataProviderURL = 'action=community&which=';
		var getAction = 'overview';

		self.comboInitValue = 0;
		sfw.communityForum.validForumLogin = false;
		sfw.communityForum.loginName = '';
		sfw.communityForum.confirmString = '';
		sfw.communityForum.profileId = '';

		var urlGetData = sfw.util.constructApiRequest( dataProviderURL + getAction );
		// TODO - this will work for all source types once we have constructApiRequest for the csi...
		// for now it still uses the old method.  Change this eventually.
		if ( 'csi' === self.sourceType ) {
			urlGetData = globals.apiPath() + '&' + dataProviderURL + getAction;

			self.comboInitValue = sfw.sharedForum.csiForumType;
		} else if ( 'psi' === self.sourceType ) { // future thing... not used now
			self.comboInitValue = sfw.sharedForum.psiForumType;
		} else if ( 'vim' === self.sourceType ) { // future thing... not used now
			self.comboInitValue = sfw.sharedForum.vulnForumType;
		}

		self.refresh = function() {
			self.gridRegion.getBottomToolbar().changePage( 1 );
		};

		// This might open an existing thread or a 'create new thread' request
		self.openForumThreadWindow = function( type, threadData ) {
			var title = 'New Forum Thread';
			var forumThreadWindow;

			if ( "new" == type ) {

				// Mos basic check - if we're not logged in, we can't do this.
				if ( !sfw.communityForum.validForumLogin ) {
					Ext.Msg.alert( "Error", "You must be logged in to create a new thread." );
					return;
				}

				// Create an instance of a thread window if it doesn't exist
				if ( 'undefined' === typeof(self.forumThreadNew) ) {
					self.forumThreadNew = new sfw.forumThreadNew.create( self );
				}
				forumThreadWindow = self.forumThreadNew;

			} else if ( "view" == type ) {
				// Create the title first
				title = 'View Forum Thread';
				if ( threadData && threadData.reftype && threadData.topic ) {
					var subForumName = sfw.sharedForum.subforumTitleRenderer( threadData.reftype );
					title = "Viewing Thread in " + subForumName + " Sub-Forum: " + threadData.topic;
				}

				// Create an instance of a thread window if it doesn't exist
				if ( typeof self.forumThreadView == 'undefined' ) {
					self.forumThreadView = new sfw.forumThreadView.create( self );
				}
				forumThreadWindow = self.forumThreadView;
			}

			// Setting title, initialization (reset) and showing window are common to both.  Note, a
			// new thread doesn't actually take an argument for threadData - it is null in this case
			forumThreadWindow.initialize( threadData );
			forumThreadWindow.updateTitle( title );

			if ( !forumThreadWindow.window.isVisible() ) {
				forumThreadWindow.window.show();
			}
		};

		self.viewThread = function ( grid, rowIndex ) {
			var threadData = grid.getStore().getAt( rowIndex ).data;
			self.openForumThreadWindow( 'view', threadData );
		};

		self.rowcontextmenu = function( grid, rowIndex, event ) {
			var selected = grid.getSelectionModel().getSelections();
			var multipleSelect = false;
			var contextMenu = new sfw.Menu({});
			var selectedIdList = '';

			// Support multiple selection for certain options
			if ( 1 < selected.length ) {
				multipleSelect = true;

				var selectionArray = [];
				for ( var i=0; i < selected.length; ++i ) {
					selectionArray.push( selected[i].id );
				}

				selectedIdList = selectionArray.join(",");

			} else {
				selectedIdList = grid.getStore().getAt( rowIndex ).data.id;
			}

			if ( !multipleSelect ) {
				contextMenu.add({
					text: 'View Thread'
					,disabled: sfw.sharedFunctions.readOnly()
					,handler: function() {
						self.viewThread( grid, rowIndex );
					}
 				});
			}

			// TODO - example context items might be:
			// single: subscribe, view author's profile, posts, ...
			// multiple: subscribe

			// Configure the options for single and multiple selects
			contextMenu.add({
				text: 'todo'
				,hidden: true
				,disabled: sfw.sharedFunctions.readOnly()
				,handler: function() {
					Ext.Ajax.request({
						url: 'todoUrl-BasedOnFunction' // TODO - this is a placeholder
						,params: {
							selected_id_list: selectedIdList
						}
						,success: function( data ) {
							var response = {};
							try {
								response = Ext.util.JSON.decode( data.responseText );
								switch( response.error ) {
									// todo
								}
							} catch ( ex ) {
								// Silently ignore the error
							}
						}
						,failure: function() {
							Ext.Msg.alert( "Unexpected Error", "Unable to...todo" );
						}
					});
				}
			});

			contextMenu.showAt( event.getXY() );
		};

		// When we select a different subForum type, set useSpecifc so reload load correct data
		self.handleComboBox = function( combo, record, index ) {

			var myStore = self.gridRegion.getStore();

			// remember that an id of 0 is valid for the 'all threads' case
			// Just set the useSpecific in the load options
			if ( record && record.data && "undefined" != typeof(record.data.id) ) {
				self.useSpecific = record.data.id;
			}
			self.startProductId = '';
			self.search();
		};

		// If called with no args, this makes an ajax call to get the data.  If called with a name
		// it does the same thing, but bypasses the server call and does it directly, but only if confirmString and profileId are already set.
		self.setUpRegistrationInfo = function( specifyName ) {
			if ( specifyName ) {
				// Same things we do for a successful ajax call...
				self.registrationInfo.setText( "Logged In As: " + specifyName );
				sfw.communityForum.validForumLogin = true;
				sfw.communityForum.loginName = specifyName;
				self.interface.doLayout();
				if ( sfw.communityForum.confirmString !== '' && sfw.communityForum.profileId !== '' ) {
					return;
				}
			}

			// Otherwise...
			var action = 'checkForumRegistration';
			var url = globals.apiPath() + '&' + dataProviderURL + action;
			// todo ... as other places

			Ext.Ajax.request({
				url: url
				,success: function( data ) {
					var response = {};
					try {
						// it is empty if there is no data to fetch
						if ( data.responseText ) {
							response = Ext.util.JSON.decode( data.responseText );
							if ( response && response.username ) {
								self.registrationInfo.setText( "Logged In As: " + response.username );
								sfw.communityForum.validForumLogin = true;
								sfw.communityForum.loginName = response.username;
								sfw.communityForum.confirmString = response.confirm_string;
								sfw.communityForum.profileId = response.user_id;
							}
							self.interface.doLayout();
						}
					} catch ( ex ) {
						// silently ignore - just don't log user into forum
					}
				}
				,failure: function() {
					Ext.Msg.alert( "Error", "Cannot check forum registration status." );
				}
			});
		};

		// This is the action taken when we click the registration button, which if we are not logged in
		// will set up our default user profile.  If we are logged in, it will take us to a link to manage
		// and edit our profile
		self.handleRegistration = function() {
			// Create the type of window we want based on if we are already logged in or not
			var profileWindow;
			if ( 'undefined' === typeof(self.manageProfile) ) {
				profileWindow = new sfw.forumProfile.create( self );
			}

			if ( !profileWindow.window.isVisible() ) {
				profileWindow.window.show();
			}

			var loginDefaultName = LoginDetails.loginAccountUsername; // TODO - note, this is CSI specific, change for VIM, etc. someday
			if (sfw.communityForum.validForumLogin ) {
				loginDefaultName = sfw.communityForum.loginName;
			}

			// Must show the window before we init data since there is some dynamically updated text which
			// need the window visible in order for getContentTarget() to succeed.
			profileWindow.initialize( sfw.communityForum.validForumLogin, loginDefaultName );
		};

		//The search is performed on the posts' content and title, restricted by the current forum type
		self.search = function() {
			var myStore = self.gridRegion.getStore();

			// We must update the 'start' param so we always start at page 1 when changing forum type
			if ( myStore.lastOptions ) {
				Ext.apply( myStore.lastOptions.params, { 'start': 0 } );
				Ext.apply( myStore.lastOptions.params, { 'source_type': self.useSpecific } );
				Ext.apply( myStore.lastOptions.params, { 'free_text_search': self.freeTextValue } );
				Ext.apply( myStore.lastOptions.params, { 'start_product_id': self.startProductId } );
				myStore.reload( myStore.lastOptions );
			} else {
				// shouldn't actually happen...
				myStore.reload();
			}
		};

		self.handleTextSearch = function() {
			self.freeTextValue = self.searchField.getValue();
			self.startProductId = '';
			self.search();
		};

		self.searchField = new Ext.form.TextField({
			listeners: {
				specialkey: function( field, e ) {
					if ( e.ENTER == e.getKey() ) {
						self.handleTextSearch();
					}
				}
			}
		});

		self.registrationInfo = new Ext.Button({
			text: 'Register for Forum'
			,tooltip: 'Log-In / Manage Profile'
			,width: 150
			,handler: function() {
				self.handleRegistration();
			}
		});

		self.forumTypeCombobox = new Ext.form.ComboBox({
			store: sfw.sharedForum.forumTypeStore
			,width: 125
			,valueField: 'id'
			,displayField: 'text'
			,mode: 'local'
			,allowBlank: false
			,editable: false
			,triggerAction: 'all'
			,autoSelect: true
			,listeners: {
				select: self.handleComboBox.createDelegate( this )
			}
		});

		self.createThreadButton = new Ext.Button({
			text: 'Create New Thread'
			,tooltip: 'Create a new thread in the forum. You must be logged-in to perform this action.'
			,handler: function() {
				self.openForumThreadWindow( 'new' );
			}
		});

		self.gridDataFields = [
			{ name: 'id' }
			,{ name: 'reftype', column: { header: 'Sub-Forum', align: 'left', renderer: sfw.sharedForum.subforumTitleRenderer } }
			,{ name: 'topic', column: { header: 'Topic', align: 'left', renderer: this.topicTitleRenderer } }
			,{ name: 'posted' ,column: { header: 'Posted', width: 130, renderer: this.postedDateRenderer } }
			,{ name: 'lastupdate' ,column: { header: 'Last Reply', width: 130, renderer: this.lastReplyDateRenderer } }
			,{ name: 'authorName', column: { header: 'Author', width: 130 } }
			,{ name: 'lastAuthorName', column: { header: 'Last Post By', width: 130 } }
			,{ name: 'views', column: { header: 'Views', width: 60, align: 'right' } }
			,{ name: 'posts', column: { header: 'Posts', width: 60, align: 'right' } }
			,{ name: 'threadstatus', column: { header: 'Thread Status', width: 100 } }
			,{ name: 'votes', column: { header: 'Votes', width: 60, align: 'right' } }
 			,{ name: 'content' }
 			,{ name: 'author' }
 			,{ name: 'accepted_answer' }
			,{ name: 'lastedit' }
 			,{ name: 'canEdit' }
 			,{ name: 'attachment_id', column: { header: 'Attachment', width: 80, align: 'right', renderer: this.isAttachedRenderer } }
		];

		self.gridRegion = new sfw.DataBoundGrid({
			id: self.id + '_communityForumGrid'
			,secuniaSorting: self.secuniaSorting
			,defaultSorters: [{field:'lastupdate' ,direction: 'DESC'}]
			,encodeSorters: true
			,stateful: !!self.stateful //cast to boolean if not set its false
			,region: 'center'
			,secuniaPaging: true
			,displayMsg: 'Threads {0} - {1} of {2}'
			,pageSize: 20
			,selModel:  new Ext.grid.RowSelectionModel({})
			,datafields: self.gridDataFields
			,store: {
				url: urlGetData
				,idProperty: 'id'
				,baseParams: { 'source_type':  self.comboInitValue } // initial value
				,ranOnce: false
				,listeners: {
					beforeload: function() {
						// before a fresh load we need to set the params to make sure we are using the right type
						if ( "undefined" === typeof( self.useSpecific ) ) {
							self.useSpecific = self.comboInitValue;
						}
						if ( "undefined" === typeof( self.freeTextValue ) ) {
							self.freeTextValue = '';
						}
						//This is a search parameter which can only be set up programmaticaly
						//at the grid start-up as it searches for the product id and also for the product name
						if ( "undefined" === typeof( self.startProductId ) ) {
							self.startProductId = '';
						}
						Ext.apply( this.baseParams, {
							'source_type': self.useSpecific
							,'free_text_search': self.freeTextValue
							,'start_product_id': self.startProductId
						});
					}
					,load: function ( store, records, options ) {
						//Loop through the records and see if there is any new one
						//If there is we'll just update the lastCheckDate
						if (
							sfw.communityForum.lastCheck != null
							&& self.interface.rendered
							&& !self.interface.hidden
							&& self.gridRegion.rendered
							&& !self.gridRegion.hidden
						) {
							try {
								var lastCheckTime = sfw.util.dateCreate( sfw.communityForum.lastCheck ).getTime();
								var recordTime;
								for ( var i = 0; i < records.length; i++ ) {
									recordTime = sfw.util.dateCreate( records[i].data.lastupdate ).getTime();
									//There's a newer update which has been seen so we need to update the last check
									if ( recordTime - lastCheckTime > 0 ) {
										//The community button should already be without updates so we don't need
										//to call that callback function again
										sfw.communityForum.setLastActivity();
										break;
									}
								}
							} catch ( ex ) {
								sfw.debug.trigger( ex, 'sfw.communityForum.buildOverview()' );
							}
						}
						//This refresh should only run one
						if ( !this.ranOnce ) {
							self.refresh();
							this.ranOnce = true;
						}
					}
				}
			}
			,autoExpandColumn: 'topic'
			,viewConfig: {
				emptyText: 'No Forum Threads Exist.'
				,deferEmptyText: false
			}
			,listeners: {
				rowcontextmenu: self.rowcontextmenu.createDelegate( this )
				,rowdblclick: function( grid, rowIndex ) {
					self.viewThread( grid, rowIndex );
				}

			}
		});

		self.interface = new sfw.Panel({
			title: 'Community Forum'
			,id: self.id
			,layout: 'border'
			,border: false
			,items: [
				new sfw.Panel({
					region: 'center'
					,tbar: new Ext.Toolbar({
						height: 26
						,items: [
							{
								xtype: 'label'
								,text: 'Choose Forum Type'
							},{
								xtype: 'spacer'
								,width: 10
							}
							,self.forumTypeCombobox
							,{
								xtype: 'tbspacer'
								,width: 18
							}
							,self.searchField
							,{
								xtype: 'tbspacer'
								,width: 2
							},{
								xtype: 'button'
								,text: 'Search'
								,handler: self.handleTextSearch.createDelegate( this )
							},{
								xtype: 'tbspacer'
								,width: 18
							},{
								xtype: 'tbseparator'
							},{
								xtype: 'tbspacer'
								,width: 18
							},self.createThreadButton
							,'->'
							,self.registrationInfo
						]
					})
					,items: self.gridRegion
				})
			]
			,listeners: {
				show: function() {
					// set initial data
					if ( "undefined" === typeof( self.useSpecific ) ) {
						self.useSpecific = self.comboInitValue;
					}
					if ( "undefined" === typeof( self.freeTextValue ) ) {
						self.freeTextValue = '';
					}
					self.forumTypeCombobox.setValue( self.useSpecific );
					self.searchField.setValue( self.freeTextValue );
					self.setUpRegistrationInfo();
				}
			}
		});

 		// The new look and feel of the csi requires the description panel to be displayed below the menu
		// We might need to same functionality later on with the vim. For now, if the project type is 'csi'
		// we will not add the description panel here. It will be added using the generic mechanism in which
		// the local file implements the getDescription() function.
		// This is not used now as the forum is only in the csi - this is a placeholder which will be changed
		// or filled in more thoroughly later based on if/how we incorporate the forum into other products.
		if ( 'csi' !== self.sourceType ) {
			var descriptionPanel = new sfw.Panel({
				region: 'north'
				,height: 50
				,items: [
					new sfw.Panel({
						cls: 'ContentPadding'
						,html: 'todo - add generic or vim specific text.  Change later if we will use the same intro for the csi, vim, and all other products. '
						,border: false
						,autoScroll: true
					})
				]
			});
			self.interface.insert( 0, descriptionPanel );
		}

	} catch ( ex ) {
		sfw.debug.trigger( ex, self.id );
	}
};

sfw.communityForum.init();
