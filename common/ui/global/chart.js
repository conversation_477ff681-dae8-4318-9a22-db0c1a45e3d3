/**
 * @file chart.js
*/


/**
 * Provides Secunia Ext Charts, using FlotPanel.
 *
 * @param config
 *	Object containing a variety of configuratble parameters for a chart.  Many will be set by default, but a URL, height and
 * width must be supplied.
 *
 * @return
 *	sfw.Panel containing the chart as its items, or a string indicating there is no chart data to display.
 *
*/
sfw.chart = function( config ) {
	returnFlotGraphColors = function( chartComponent ) {
		switch ( chartComponent ) {
 		case 'background': // white
			return '#FFFFFF';
 		case 'labels': // dark blue
			return '#000066';
		}
		return '#000000';
	}

	// This decides some of the Y axis info in flot graphs used in multiple places
	// We have the option of including an extra step of y-axis in the case of graphs who have labels right
	// above the points and might obscure data points near the top
	getFlotGraphYAxisTicksInfo = function( max, extra ) {

		var returnInfo = [];
		var extraStep = 0;
		if ( extra ) {
			extraStep = 1;
		}

		// for the ticks, we scale them depending on how big the max is - see code below for details.
		if ( max < 1 ) {
			returnInfo.tickSize = 1;
		} else {
			var intervalFound = false;
			var intervalLimits = [10000, 1000, 400, 200, 100, 50, 10, 1 ];
			var steps = [2500, 250, 100, 50, 20, 10, 5, 1];
			var i = 0;
			while (!intervalFound) {
				if ( Math.floor(max/intervalLimits[i]) > 0 ) {
					intervalFound = true;
				} else {
					i++;
				}
			}
			// now we know what our interval size needs to be - round max up to the next multiple of the step
			max = ( Math.floor((max+steps[i])/steps[i]) + extraStep ) * steps[i];  // might add an extra step ( +0 or +1) to give more room for graph labels, if requested
			returnInfo.tickSize = steps[i];
		}

		returnInfo.max = max;
		returnInfo.min = 0;  // can do something more with min later if desired... for now leave at 0;
		returnInfo.tickDecimals = 0; // just use whole numbers in graph
		returnInfo.labelWidth = 40;

		return returnInfo;
	}


	// ----- Set up all the default options for charts -----

	// We expect that height and width are passed in - they are suppossed to be.  Just in case, have a default setting.

	if ( "undefined" == typeof(config.headerCfg) ) {
		// this parameter will be used to set a chart title later if desired
		config.headerCfg = {
			html: ''
			,cls: 'custom-noBackground-header'
		};
	}
	if ( "undefined" == typeof(config.width) ) {
		config.width = 400;
	}
	if ( "undefined" == typeof(config.height) ) {
		config.height = 400;
	}
	if ( "undefined" == typeof(config.type) ) {
		config.type = 'bar';
	}
	if ( "undefined" == typeof(config.border) ) {
		config.border = 'false';
	}

	// We expect that config.options is defined, but just in case, initialize it.
	if ( "undefined" == typeof(config.options) ) {
		config.options = {};
	}

	// Set up the default bars/pie/etc parameters if they are not already explicitly set
	if ( ("bar" === config.type) && ("undefined" === typeof(config.options.bars)) ) {
		config.options.bars = {
			show: true
			,barWidth: 0.4
			,fill: true
		};
	} else if ( ("pie" === config.type) && ("undefined" === typeof(config.options.pies)) ) {
		config.options.pies = {
			show: true
			,bias: null
			,labelFormatter: function(label, value, percent, textValue, pie, series, options) {
				if ( 0 === value ) {
					return '';
				} else {
					return textValue + '%'; // + ' (' +  value + ')';
				}
			}
		};
		if ( "undefined" === typeof(config.options.legend) ) {
			config.options.legend = {
				show: true
				,position: 'nw'
				,labelFormatter: function(label, series) {
					return label + ' (' + series.datapoints.points[1] + ')';
				}
			};
		}
	}

	// Also, with pies, we increase the flot chart width param to force the pie to the right.  The chart
	// always goes in the center, so we fool it by making it think the width is bigger, and therefore
	// the chart gets pushed right a bit (and there is lots of space there for it).  This gives us lots of
	// room for the legend.  This is also consistent with how we do pies in the CSI.
	if ( "pie" === config.type ) {
		var adjustedWidthForPieCharts = config.width * 1.4;  // make 40% wider
		config.width = adjustedWidthForPieCharts;
	}

	// These are the default settings for all graphs unless other options are passed in.
	if ( "undefined" == typeof(config.options.grid) ) {
		config.options.grid = {
			show: false
			,color: returnFlotGraphColors('labels')
			,backgroundColor: returnFlotGraphColors('background')
			,hoverable: true
			,clickable: true
			,autoHightlight: true
		};
	}

	// Set some of the options in config, if asked for, based on the actual data.  Also set labels data.
	getAxisData = function( config, xMin, xMax, yMin, yMax, labelData ) {

		// ENHANCEMENT: Allow for calculating all graph params dynamically later

		// We will do this when a numeric value in the options is entered as 'calculate' - in this case we calculate
		// it and reset the value to the number we find. If it is still a number, we just leave it alone
		var extraVerticalSpace = false;
		if ( config.extraVerticalSpace ) {
			extraVerticalSpace = true;
		}

		var yAxisInfo = getFlotGraphYAxisTicksInfo( yMax, extraVerticalSpace );

		// ENHANCEMENT: Some graphs might want to also leave space on the right for legends
		if ( !config.options.xaxis ) {
			config.options.xaxis = {
				min: -0.5
				,max: xMax + 1 // xMax is the biggest actual value, but we start at 0, so we have xMax+1 points
				,labelWidth: 40
				,labelHeight: 40 // for some reason this is critical, or else labels don't display at all
			};
		}

		if ( !config.options.yaxis ) {
			config.options.yaxis = yAxisInfo;
		}

		// Label Data - first check if we already have defined our xTicks or yTicks - if so, do nothing.
		// We also need to check that we even have something in LabelData
		if ( 0 < labelData.length ) {
			var labelsSet = false;

			// Set the labels to skip printing some if there are too many.  We jump by 2, 3, etc. every group of
			// 11 or more labels.
			var jump = Math.ceil(labelData.length / 11);

			// build ticksArray
			var ticksArray = [];
			var j=0;
			var lastItem;

			for ( var i=0; i < labelData.length; i+=jump ) {
				ticksArray[j] = labelData[i];
				j++;
				lastItem = i;
			}

			// always label the last graph datapoint for visual clarity
			if ( lastItem !== (labelData.length - 1) ) {
				ticksArray[j] = labelData[labelData.length-1];
			}

			if ( "undefined" == typeof(config.options.xaxis.ticks) ) {
				// make sure our orientation is not 'horizontal' - orientation can be 'vertical' or empty
				if ( !config.orientation || "vertical" == config.orientation ) {
					config.options.xaxis.ticks = ticksArray;
					labelsSet = true;
				}
			}
			// If it was not meant for the x-axis, maybe it is for the y-axis
			if ( !labelsSet && "undefined" == typeof(config.options.yaxis.ticks) ) {
				// Orientation must be 'horizontal'
				if ( config.orientation && "vertical" == config.orientation ) {
					config.options.yaxis.ticks = ticksArray;
					labelsSet = true;
				}
			}
		}

	}

	// Set up the hover text for a bart plot.  Note, type is unused so far - might need it later if we add
	// plots that have multiple series, history data, etc.
	barPlotHover = function( event, pos, item, ticksArray, tipsArray, barWidth, customTip, type ) {

		if ( item ) {
			var offset = 0.02; // Not used yet
			var index;
			var label;
			var tipText;
			var epsilon = 0.0001;
			// avoid getting caught by floating point arithmetic imprecision - use: if |a-b| < e vs. if a==b
			// Note: also use math.round on values which are portentially epsilon-close to whole numbers.  parseInt can fail - i.e. parseInt(1e-12) returns 1, not 0
			// Note - not used yet... see csi_dashboard for use if needed later for mulitple series, history, etc.

			// Note that for the different types, offset is different  Also, the index we get in datapoint[] is 0 for
			// vertical bars, 1 for horizontal.
			index = Math.round( item.datapoint[0] + barWidth );
			label = ticksArray[index][1];

			// custom tooltip
			if ( customTip ) {
				tipText = tipsArray[index];
			} else {
				tipText = label + ': ' + tipsArray[index];
			}

			if ( this.barTip ) {
				this.barTip.detach();
			}

			this.barTip = $('<div id="tooltip">' + tipText + '</div>').css( {
				position: 'absolute'
				,'z-index': 10000
				,display: 'none'
				,top: item.pageY
				,left: pos.pageX - pos.x + 15
				,border: '1px solid #000000'
				,padding: '2px'
				,'background-color': '#CCCCCC'
				,opacity: 0.8
			}).appendTo("body").show().fadeOut(3300);
		} else {
			if ( this.barTip ) {
				this.barTip.detach();
			}
		}
	}


	// Populate function takes a holding panel and a chart configuration object.
	// If we have a valid chart we create a flotPanel with the chat and add it to the holding panel.  Otherwise
	// we add an html string to the panel indicating there is no chart data to show.
	// This function uses the URL supplied in config to fetch the chart data, and formats it based on the
	// 'type' and other data also supplied in config
	populate = function( holdingPanel, config ) {
		var params = null;
		if ( config.params ) {
			params = config.params;
		}
		Ext.Ajax.request({
			url: config.url
			,params: params
			,success: function( data ) {
				var valid = false;
				var response = {};
				var chartSeries = [];
				var labelData = [];  // custom axis labels - i.e. week 32
				var tipsArray = [];  // tips holds the hover text

				try {
					// If there is no data to show, we have that data.responseText is empty, or that
					// data.reponseText is {data: []} - check for this.
					if ( !data.responseText ||
						 ( data.responseText && "{\"data\":[]}" == data.responseText ) ) {
						response = false;
					} else {
						response = Ext.util.JSON.decode( data.responseText );
					}
				} catch ( ex ) {
					// Silently ignore the error - we will later load a blank graph if no data
				}
				if ( response ) {
					// Each key in response is a different series.  The fields are name, data, ...
					// We will create the series and add it to our chartSeries array that we build here,
					// and adjust any other fields or options as needed
					var chartSeries = [];

					// initialize the variables we will use that relate the the data series
					var dataPoint;
					var thisSeries;
					var seriesName;
					var seriesColor;
					var seriesData;
					var tip = '';
					var customTip = false;
					var tipIndex;
					var newSeriesData = [];
					// we must also track the min and max for both X and Y axis
					var xMin = 10000000000;
					var yMin = 10000000000;
					var xMax = -10000000000;
					var yMax = -10000000000;

					for ( key in response ) {

						thisSeries = response[key];
						seriesName = thisSeries['name'];
						seriesData = thisSeries['data'];
						seriesColor = ( thisSeries['color'] ? thisSeries['color'] : null );
						// ReInit the values that need it.
						newSeriesData = [];

						for ( i = 0; i < seriesData.length; i++ ) {
							dataPoint = [ parseFloat(seriesData[i][0]), parseFloat(seriesData[i][1]) ];
							newSeriesData.push(dataPoint);
							// if a label was specified, set up the labels for each value
							// recall that the label name is in field 2, and the offset is in field 3
							if ( seriesData[i][2] ) {
								labelData[dataPoint[0]+parseFloat(seriesData[i][3])] = [dataPoint[0] + parseFloat(seriesData[i][3]), seriesData[i][2]];
							}

							// the tipsArray // recall we can add extra hover text in the data returned
							var addHoverText = '';
							if ( thisSeries['hoverText'] ) {
								addHoverText = thisSeries['hoverText'][0]; // use singular version by default
								if ( 1 !== Math.abs(dataPoint[1]) && thisSeries['hoverText'][1] ) {
									// if there is a plural version, use it if value !== 1
									addHoverText = thisSeries['hoverText'][1];
								}
							}

							tipIndex = dataPoint[0] + parseFloat(seriesData[i][3]);
							if ( seriesData[i][4] && !tipsArray[tipIndex]) {
								tip = seriesData[i][4] + ':<br />';
								customTip = true;
							} else {
								tip = '';
							}

							tip += dataPoint[1] + " " + addHoverText;

							if (tipsArray[tipIndex]) {
								tipsArray[tipIndex] += '<br />' + tip;
							} else {
								tipsArray[tipIndex] = tip;
							}

							// deal with max and min
							xMin = ( dataPoint[0] < xMin ) ? dataPoint[0] : xMin;
							xMax = ( dataPoint[0] > xMax ) ? dataPoint[0] : xMax;
							yMin = ( dataPoint[1] < yMin ) ? dataPoint[1] : yMin;
							yMax = ( dataPoint[1] > yMax ) ? dataPoint[1] : yMax;
						}

						// overwrite the thisSeries variable with the series data that actually goes into the graph
						thisSeries = {
							label: seriesName
							,data: newSeriesData
							,color: seriesColor
						};
						chartSeries.push( thisSeries );
					}
					valid = true;

					// If it was requested that we sort the array, sort it
					// i.e. in the case of impact pie chart, where we want it sorted from most to fewest
					if ( true === config.sort ) {
						chartSeries.sort( function(a,b) { return b.data[0][1]-a.data[0][1] } );
					}

					// Get all the axis options - some are based on the data we got back, so we have to
					// do this dynamically AFTER we know the data values
					getAxisData( config, xMin, xMax, yMin, yMax, labelData );

				}

				if ( true == valid ) {
					try {
						// If we want to use a chart title, set it up here
						// config.headerCfg.html = ...  (in the future?)

						// Create the flot panel and add to holdingPanel's items - first remove
						// anything that was there before
						holdingPanel.removeAll();
						var chart = new Ext.FlotPanel( config );

						// Set up the hover text
						if ( config.type && 'bar' == config.type ) {
							chart.addListener( "plothover",
											   barPlotHover.createDelegate(this, [labelData, tipsArray, 0.2, customTip ], true)
											 );
						}
						holdingPanel.add(chart);
						holdingPanel.doLayout();
						chart.plot( chartSeries, config.options  );

					} catch (ex) {
						// Silently ingore the error
					}
				} else {
					try {
						// In the case of no data, we do not create a flot chart - we just add HTML to the
						// holding panel

						holdingPanel.removeAll();
						holdingPanel.add({
							html: '<br>There is no chart data to display'
							,border: false
							,cls: 'custom-noBackground-header' // or use 'ContentPadding' for regular indented text
						});
						holdingPanel.doLayout();

					} catch (ex) {
						// Silently ingore the error
					}
				}
			}
			,failure: function() {
				if ( typeof sfw.debug !== "undefined" ) {
					sfw.debug.notify( "Cannot fetch chart data" );
				} else {
					Ext.Msg.alert( "Error", "Cannot fetch chart data" );
				}
			}
		});
	}

	var holdingPanel = new sfw.Panel({
		layout: 'fit'
		,border: false
	});

	// Call the populate function with the chart config - this does all the work of populating the holding panel.
	populate( holdingPanel, config );

	return holdingPanel;
}
