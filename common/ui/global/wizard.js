/**
 * @file wizard.js
 * Provides the Secunia Web Wizard Application.
*/

/**
 * Secunia Web Wizard Application.
 *
 * Creates a common wizard interface, providing data collection ( based on multiple Wizard 'Pages' ) and submission.
 *
 * Built-in functionality:
 *
 * Next -> Will load the next wizard 'page' from the config array.
 *	If the 'page' has a validate method, and it returns false, then wizard will call invalidError( direction ) for the current page, and abort the 'Next' step.
 *	If however, invalidError( direction ) returns true, then let the user continue.
 * Previous -> Will load the previous wizard 'page' from the config array
 *
 * Finish -> ( enabled when the last page is reached ) Will post the collected data object to an predefined API Url
 *	-> Calls the page's collect() method for each page, to store any collected data
 *	-> Calls config.success or config.failure uppon success or failure of the extjajax object
 * Cancel -> cancel button
 *
 * @event afternavigate
 *	Fired after the navigate method is called.
 *  This is fired when the wizard opens to the default page.
 *  The defined handler is passed the {@link sfw#wizard#interface} for the wizard
 *
 * @param config
 *	Object configuration object
 *		Properties: title: Wizard title
 *				id: String which is used as the base for the ids of the wizard's various components
 *				pageArray: Array of pages that will be used in the wizard.
 *					Must provide collect() method when the Finish button is clicked, which return data stored in the dataArray object
 *				apiUrl: URL to the API that will handle the collected data in the pageArray
 *				finishFunc: Instead of 'apiUrl', a function to be called on finish with the collected data as an argument.
 *				height: height of the wizard window
 *				width: width of the wizard window
 *				success: Ext.Ajax success callback function, or finishFunc callback function
 *				failure: Ext.Ajax failure callback function, or finishFunc callback function
 *				nextHandler: handler for the next button
 *				previousHandler: handler for the previous button
 *  			navigationBar: {Function} which returns a navigation bar. The function is passed the wizard as an argument.
 *  			finishButtonToggle: {Function} which is run at the end of every navigate() call and is responsible for
 *                  enabling/disabling the finish button. If not specified, the finish button will be disabled on all pages
 *                  except the last.
 *              finishButtonText: {String} text to show on the finish button
 *              finishButtonId: {String} id for the finish button
 *              listeners: {Object} listeners to pass to the wizard's window
 *              closeAction: {string} The action to take when the user clicks the top right X or presses ESC, "close" or "hide"
 * @return ExtJS window
 *
 * Example:
 *
 * @code
 * this.stepOne = {};
 * this.stepOne.create = function() {
 *	return new sfw.Panel({
 *	title: 'Step 1: Select something'
 *	,html: 'Welcome to step 1'
 *		,collect: function() {
 *			return { value1: "1" };
 *		}
 *	})
 * }
 *
 * this.stepTwo = {};
 * this.stepTwo.create = function() {
 *	return new sfw.Panel({
 *		title: 'Step 2: Select something'
 *		,html: 'Welcome to step 2'
 *		,collect: function() {
 *			return { value2: "2" };
 *		}
 *	})
 * }
 *
 * this.stepThree = {};
 * this.stepThree.create = function() {
 *	return new sfw.Panel({
 *		title: 'Step 3: Select something'
 *		,html: 'Welcome to step 3'
 *		,collect: function() {
 *			return { value3: "3" };
 *		}
 *	})
 * }
 *
 *wizard = new sfw.wizard({
 *	title: 'Generic Wizard'
 *	,height: 400
 *	,width: 500
 *	,apiUrl: "wizard.php?name=demo"
 *	,pageArray: [
 *		this.stepOne.create()
 *		,this.stepTwo.create()
 *		,this.stepThree.create()
 *	]
 *	,success: function( response ) {
 *		if ( response.responseText == "ok" ) {
 *			Ext.Msg.alert("Congratulations", "The wizard is done. Etc." );
 *		} else {
 *			Ext.Msg.alert("Error", Ext.util.JSON.decode( response.responseText ).error );
 *		}
 *	}
 *	,failure: function() {
 *		Ext.Msg.alert("Error", "Failed submitting wizard data.");
 *	}
 * });
 * wizard.show();
 * @endcode
*/
sfw.wizard = function ( config ) {
	var self = this;
	/* In case this function is extended, it will be called without args and we
	   then want to return as early as possible	*/
	if ( typeof config === "undefined" ) {
		return;
	}

	// Set wizard's identifier so it can be found later
	if ( typeof config.id === "undefined" ) {
		config.id = "wizard_" + Math.floor( Math.random() * 0xFFFFFFFF );
	}
	this.id = config.id;

	// Set the default close action of the wizard to "close"
	if ( typeof config.closeAction === "undefined" ) {
		config.closeAction = "close";
	}

	/**
	 * @method
	 * @private
	 * @param {Ext.Panel} pagePanel
	 * @param {sfw.wizard} wizard
	 */
	this.fireBeforeNavigatePageEvent = function ( pagePanel, wizard ) {
		pagePanel.fireEvent( "beforenavigate", pagePanel, wizard.interface );
	};

	/**
	 * @method
	 * @private
	 * @param {Ext.Panel} pagePanel
	 * @param {sfw.wizard} wizard
	 */
	this.fireAfterNavigatePageEvent = function ( pagePanel, wizard ) {
		pagePanel.fireEvent( "afternavigate", pagePanel, wizard.interface );
	};

	/**
	 * Indicate which direction should we navigate.
	 * @param direction
	 *	Integer if negative, go back, if positive go forward. If _null_, refresh buttons.
	 *		disable or enable buttons, based on the current position.
	 * @param ignoreValidation
	 *	Boolean optional directive for ignoring any validation
	*/
	this.navigate = function ( direction, ignoreValidation ) {
		var activeItem = this.pageArrayContainer.activatedItem;
		ignoreValidation = ( typeof ignoreValidation === "undefined" ) ? false : ignoreValidation;
		if ( direction != null ) {
			if ( ignoreValidation == false ) {
				if ( typeof config.pageArray[activeItem].validate !== "undefined" ) {
					if ( config.pageArray[activeItem].validate() !== false ) {
						// The current step is not done yet, let the user know.
						if ( config.pageArray[activeItem].invalidError( direction ) !== true ) {
							// If the page decided to let the user go ahead, then...go ahead.
							return;
						}
					}
				}
			}
		}

		if ( direction > 0 ) {
			if ( activeItem < config.pageArray.length - 1 ) {
				activeItem = activeItem + direction;
			}
		} else if ( direction < 0 ) {
			if ( activeItem >= 1 ) {
				activeItem = activeItem + direction;
			}
		}

		// Enforce step range (0..max)
		activeItem = Math.max( activeItem, 0 );
		activeItem = Math.min( activeItem, config.pageArray.length - 1 );

		// Update Previous/Next button states before we change pages because
		// doing so afterwards would overwrite the button changes we made
		// via the beforenavigate and afternavigate events
		if ( activeItem == 0 ) {
			this.previousButton.disable();
		} else {
			this.previousButton.enable();
		}
		if ( activeItem < config.pageArray.length - 1 ) {
			this.nextButton.enable();
		} else {
			this.nextButton.disable();
		}

		if ( direction != null ) {

			// fire beforenavigate event before we navigate to a different page
			this.fireBeforeNavigatePageEvent( this.pageArrayContainer.get( activeItem ), this );
			// switch the page
			this.pageArrayContainer.getLayout().setActiveItem( activeItem );
			// fire afternavigate event after we navigated to a different page
			this.fireAfterNavigatePageEvent( this.pageArrayContainer.get( activeItem ), this );

			// Auto focus on the page's defaultButton if one was specified
			if ( typeof this.pageArrayContainer.get( activeItem ).initialConfig.defaultButton !== "undefined" ) {
			    Ext.getCmp( this.pageArrayContainer.get( activeItem ).initialConfig.defaultButton ).focus();
			}
			// re-render for items which didn't render on hidden panels
			try {
				this.pageArrayContainer.get( activeItem ).doLayout();
			} catch( ex ) {
				sfw.debug.log( 'Error occured while doing layout.' );
			}
		}

		// updates finish button's disabled state
		var isLastPage = activeItem == config.pageArray.length - 1;
		var finishButtonToggle = config.finishButtonToggle || this.finishButtonToggle;
		finishButtonToggle( this.finishButton, isLastPage );

		this.pageArrayContainer.activatedItem = activeItem;
		// fire wizard's afternavigate event
		this.interface.fireEvent( "afternavigate", this.interface, this, 33 );
	}

	/**
	 * @method
	 * @private
	 * Toggles the disabled state of the Finish button.
	 * Sets the button as disabled on all pages except the last page
	 * @param {Ext.Button} finishButton
	 * @param {Boolean} isLastPage
	 */
	this.finishButtonToggle = function ( finishButton, isLastPage ) {
		if ( isLastPage ) {
			finishButton.enable();
		} else {
			finishButton.disable();
		}
	};

	/**
	 * @method
	 * Resizes the wizard to the page's width/height if they were specified in
	 * the page's initial options.
	 * @private
	 * @param int pageIndex Index of the page to resize
	 * @return bool
	 *	- true if resize dimensions were found
	 *	- false if resize dimensions were not found
	 */
	this.resizeTo = function ( pageIndex ) {
		var page = config.pageArray[ pageIndex ];
		if ( typeof page.options == "undefined" || typeof page.options.width == "undefined" || typeof page.options.height == "undefined" ) {
			return false;
		}
		this.interface.layout.container.resizer.resizeTo( page.options.width, page.options.height);
		this.interface.layout.container.syncSize();

		return true;
	}

	// Create previous button object
	this.previousButton = new Ext.Button({
		text: 'Previous'
		,id: this.id + "_btnPrevious"
		,disabled: true
		,handler: config.previousHandler || this.navigate.createDelegate( this, [-1] )
	});

	// Create next button object
	this.nextButton = new Ext.Button({
		text: 'Next'
		,id: this.id + "_btnNext"
		,handler: config.nextHandler || this.navigate.createDelegate( this, [1] )
	});

	/**
	 * Finish wizard method.
	 * Logic:
	 *	Collect data for each object
	 *	Pass it to the ExtJj Ajax object
	 *	Submit through post, to the specified apiUrl
	 *  -- or --
	 * Call the finishFunc
	*/
	this.finishWizard = function() {
		var i;
		var dataObject = {};
		var dataResult = {};

		// Handle the case where we need to do some preprocesing of the data before
		// we proceed with the collection - in this case we bypass collection here since
		// we will need to do it again when the data is ready / processed

		var skipCollection = false;
		if ( config.skipCollection ) {
			skipCollection = true;
		}

		for ( i = 0; i < config.pageArray.length && !skipCollection; i++ ) {
			if ( typeof config.pageArray[i].collect !== 'undefined' ) {
				dataResult = config.pageArray[i].collect();
				for ( key in dataResult ) {
					dataObject[key] = dataResult[key];
				}
			}
		}

		if ( config.apiUrl ) {
			Ext.Ajax.request({
				url: config.apiUrl
				,success: config.success || null
				,failure: config.failure || null
				,params: dataObject
			});
		} else if ( config.finishFunc ) {
			if ( config.finishFunc(dataObject) ) {
				if ( config.success ) {
					config.success();
				}
			} else {
				if ( config.failure ) {
					config.failure();
				}
			}
		}
	}

	// Create finish button object
	this.finishButton = new Ext.Button({
		text: ( config.finishButtonText ? config.finishButtonText : 'Finish' )
		,id: config.finishButtonId || ( this.id + "_btnFinish" )
		,disabled: true
		,handler: this.finishWizard.createDelegate( this )
	});

	this.disableNavigation = function( booleanFlag ) {
		this.previousButton.setDisabled( booleanFlag );
		this.nextButton.setDisabled( booleanFlag );
	}

	// Create cancel wizard functionality
	this.cancelWizard = function() {
		Ext.Msg.show({
			title:'Cancel Wizard?'
			,msg: 'Are you sure you want to cancel the current wizard?  Any unsaved data will be lost.'
			,buttons: Ext.Msg.YESNO
			,fn: function( btn ) {
				if ( btn == 'yes' ) {
					self.interface.hide();
				}
			}
			,animEl: 'elId'
			,icon: Ext.MessageBox.QUESTION
		});
	}

	// Create cancel wizard button
	this.cancelButton = new Ext.Button({
		text: 'Cancel'
		,id: this.id + "_btnCancel"
		,handler: this.cancelWizard.createDelegate( this )
	});

	// Create the navigation bar
	if ( typeof config.navigationBar == "undefined" ) {
		this.navigationBar = new Ext.Toolbar({
			items: [
				'->'
				,this.previousButton
				,'-'
				,this.nextButton
				,'-'
				,this.finishButton
				,'-'
				,this.cancelButton
			]
		});
	} else {
		this.navigationBar = config.navigationBar.call( this );
	}

	/**
	 * Page array container panel. Card layout.
	*/
	this.pageArrayContainer = new sfw.Panel({
		layout: 'card'
		,height: 200
		,cls: 'wizard-panel-container'
		,activeItem: 0
		,activatedItem: 0
		,items: config.pageArray
	});

	/**
	 * @method
	 * @public
	 * Gets the index of the currently actived step, counting from 0
	 * @return {Number}
	 */
	this.getActivatedItem = function () {
		return this.pageArrayContainer.activatedItem;
	}

	/**
	 * @method
	 * @public
	 * Sets the index of the currently actived step, counting from 0
	 * @param {Number} activatedItem
	 */
	this.setActivatedItem = function ( activatedItem ) {
		this.pageArrayContainer.activatedItem = activatedItem;
	}

	/**
	 * Wizard interface.
	*/
	this.interface = new Ext.Window({
		title: config.title
		,modal: true
		,constrain: true
		,maximizable: config.maximizable || null
		,height: config.height
		,width: config.width
		,bbar: this.navigationBar
		,layout: 'fit'
		,items: this.pageArrayContainer
		,parent: this
		,pageArray: config.pageArray
		,navigate: this.navigate.createDelegate(this)
		,listeners: {
				beforeClose: function() {
					self.cancelWizard();
					return false;
				}
				,onEsc: function() {
					self.cancelWizard();
					return false;
				}
			}//config.listeners
		,closeAction: config.closeAction
		,getActivatedItem: this.getActivatedItem.createDelegate( this )
		,setActivatedItem: this.setActivatedItem.createDelegate( this )
	});

    // The  window  will  auto  steal the focus when it is rendered. To get around
    // this we override the window's defaultButton property with the defaultButton
    // property of the first step's panel if set.
	if ( typeof this.pageArrayContainer.get( 0 ).initialConfig.defaultButton !== "undefined" ) {
        this.interface.defaultButton = this.pageArrayContainer.get( 0 ).initialConfig.defaultButton;
	}


	// Add custom events to the wizard
	this.interface.addEvents(
		/**
	     * @event afternavigate
    	 * Fired after the navigate method is called.
   		 * This is fired when the wizard opens to the default page.
         * @param {sfw.wizard} instance of the wizard
	     */
		"afternavigate"
	);

	/**
	 * Calibrate buttons.
	*/
	this.pageArrayContainer.doLayout();
	this.navigate( null );

	return this.interface;
}
