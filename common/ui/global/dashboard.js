/**
 * @file dashboard.js
 * Provides generic dashboard functionality.
 *
 * Configuration options:
 *
 * .columnCount: Integer number of columns, default to 3
 * .dashboardApi: URL to the dashboard API, provider for saving and loading a dashboard profile.
 * .name: A dashboard name
 * .portletArray: Array of portlet objects
*/

sfw.dashboard = function( config ) {
	try {
		this.bbar = {};
		this.availablePortlets = {};
		this.selectedPortlets = {};
		this.name = config.name;

		this._firstTime = {}; // Internal variable

		/**
		 * Build default column configuration.
		*/
		if ( typeof config.columnCount == "undefined" ) {
			config.columnCount = 3;
		}

		var temporaryColumnWidth = Math.floor( 100 / config.columnCount ) / 100;
		var temporaryItemStubs = [];
		var i;
		for ( i = config.columnCount - 1; i >= 0; i-- ) {
			temporaryItemStubs.push( new Ext.ux.PortalColumn({
				columnWidth: temporaryColumnWidth
				,style: 'padding: 5px 5px 5px 5px'
				,items: []
			}));
		}

		/**
		 * Function for creating the load mask.
		 * @param message
		 *	String message
		 * @return
		 *	Object reference to the newly created mask
		*/
		this.createMask = function( message ) {
			this.mask = new Ext.LoadMask(Ext.getBody(), { msg: message });
			this.mask.parent = this;
			return this.mask;
		}

		/**
		 * Construct the array of available portlets
		 * @return
		 *	Array of items and ids, to be processesed by this.bbar.selectElements store
		*/
		this.constructAvailablePortlets = function() {
			var i;
			var result = [];
			for ( i = 0; i < config.portletArray.length; i++ ) {
				if ( typeof this.selectedPortlets[config.portletArray[i].portletId] == "undefined" ) {
					// Update the parent dashboard
					config.portletArray[i].parentDashboard = this;
					// Clean current dashboard
					result.push([ config.portletArray[i].title, config.portletArray[i].portletId ]);
					this.availablePortlets[config.portletArray[i].portletId] = config.portletArray[i];
				}
			}
			return result;
		}

		/**
		 * Add selected portlet to the column with the least items.
		 * @param combo
		 *	Object ExtJs combo
		 * @param record
		 *	Object store record
		 * @param index
		 *	Integer store index id
		*/
		this.addPortLet = function( combo, record, index ) {
			var column = null;
			var temporaryColumn = null;
			var lowest = null;
			for ( var columnIndex = 0; columnIndex <= ( config.columnCount - 1 ); columnIndex++ ) {
				temporaryColumn = this.portal.items.items[ columnIndex ];
				if ( temporaryColumn.items.length < lowest || lowest == null ) {
					column = temporaryColumn;
					lowest = temporaryColumn.items.length;
				}
			}
			var portlet = this.availablePortlets[record.get('id')];
			column.insert( lowest, portlet );
			this.selectedPortlets[portlet.portletId] = portlet.portletId;
			this.constructAvailablePortlets( combo );
			combo.getStore().removeAt( index );
			portlet.show();
			this.portal.doLayout();
			if ( portlet.refresh != null ) {
				portlet.refresh();
			}
			combo.setValue();
		}

		/**
		 * Portlet combo.
		*/
		this.bbar.selectElements = new Ext.form.ComboBox({
			emptyText: 'Select portlet...'
			,width: 250
			,store: new Ext.data.ArrayStore({
				idIndex: 'id'
				,fields: [
					'title'
					,'id'
				]
				,data: this.constructAvailablePortlets()
			})
			,valueField: 'id'
			,displayField: 'title'
			,typeAhead: false
			,editable: false
			,mode: 'local'
			,disabled: true
			,triggerAction: 'all'
			,selectOnFocus: false
			,listeners: {
				blur: function() {
					this.reset();
				}
				,select: this.addPortLet.createDelegate( this )
			}
		});

		/**
		 * Save profile.
		*/
		this.saveData = function() {
			// Validate current profile
			var profileError = false;
			if ( typeof this.profileId == "undefined" ) {
				profileError = true;
			} else if ( this.profileId == null ) {
				profileError = true;
			}
			if ( profileError == true ) {
				Ext.Msg.alert( "Error", "No profile selected" );
				return;
			}

			// Construct items _GET paramater
			var temporaryColumn = null;
			var portletKey = null;
			var i;
			var id;
			var request = "data=";
			var items = 0;
			var active = 0;

			for ( var columnIndex = 0; columnIndex < config.columnCount; columnIndex++ ) {
				temporaryColumn = this.portal.items.items[ columnIndex ];
				for ( i = 0; i < temporaryColumn.items.items.length; i++ ) {
					if ( temporaryColumn.items.items[i].isVisible() == true ) {
						id = temporaryColumn.items.items[i].portletId;
						for ( portletKey in this.selectedPortlets ) {
							if ( id === this.selectedPortlets[portletKey] ) {
								active = 1;
							}
						}
						if ( active !== 1 ) {
							continue;
						} else {
							// portlet is active so reset active
							// and proceed to save its coordinates
							active = 0;
						}
						if ( items != 0 ) {
							request += "|";
						}
						request += columnIndex + "," + i + "," + id ;
						items++;
					}
				}
			}

			var mask = this.createMask( "Saving profile..." );
			mask.show();
			var dashboard = this;

			// Begin saving process
			Ext.Ajax.request({
				url: sfw.util.constructApiRequest( config.dashboardApi + "save&" + request + "&profile_id=" + this.profileId )
				,success: function( response ) {
					mask.hide();
					if ( response.responseText == "" ) {
						Ext.Msg.alert( "Error", "Profile save failed: data provider not ready." );
						return;
					}
					try {
						response = Ext.util.JSON.decode( response.responseText );
					} catch ( ex ) {
						Ext.Msg.alert( "Error", "Profile save failed: invalid server response" );
						return;
					}

					if ( typeof response.success !== "undefined" ) {
						if ( response.success == true ) {
							dashboard.bbar.selectProfile.getStore().reload();
							return;
						}
					}
					Ext.Msg.alert( "Error", "Profile save failed: Unexpected error" );
				}
				,failure: function() {
					mask.hide();
					Ext.Msg.alert( "Error", "Cannot save profile: Server connection" );
				}
			});
		}

		/**
		 * Remove all selected items
		*/
		this.removeAll = function() {
			if ( this.selectedPortlets.length == 0 ) {
				return;
			}

			// Hide all items
			var i;
			for ( key in this.selectedPortlets ) {
				if ( typeof this.availablePortlets[this.selectedPortlets[key]] != "undefined" ) {
					this.availablePortlets[this.selectedPortlets[key]].hide();
				}
			}

			// Reset UI and stack
			this.selectedPortlets = {};
			this.portal.doLayout();
		}

		/**
		 * Load profile data.
		 * @param itemsData
		 *	String containing selected items. Format: {(int)Column,(int)Poisition,(string)PortletId}
		*/
		this.loadData = function( itemsData ) {
			try {
				var mask = this.createMask( "Loading profile..." );
				mask.show();
				// Clean UI
				this.removeAll();
				// this.refresh();

				// Construct item array
				var itemArray = itemsData.split("|");
				var i;
				var column;
				var position;
				var itemId;
				var columnData = [];
				var temporaryData = [];

				/**
				* Construct data for each column entry
				*/
				for ( i = 0; i < itemArray.length; i++ ) {
					temporaryData = itemArray[i].split(",");
					if ( typeof columnData[temporaryData[0]] == "undefined" ) {
						columnData[temporaryData[0]] = [];
					}
					columnData[temporaryData[0]][temporaryData[1]] = temporaryData[2];
				}

				for ( var columnIndex = 0; columnIndex <= ( config.columnCount - 1 ); columnIndex++ ) {
					var temporaryColumn = this.portal.items.items[ columnIndex ];
					if ( typeof columnData[columnIndex] == "undefined" ) {
						continue;
					}

					for ( i = 0; i < columnData[columnIndex].length; i++ ) {
						try {
							if ( typeof this.availablePortlets[columnData[columnIndex][i]] !== "undefined" ) {
								temporaryColumn.insert( i, this.availablePortlets[columnData[columnIndex][i]] );
								this.availablePortlets[columnData[columnIndex][i]].show();
								this.selectedPortlets[columnData[columnIndex][i]] = columnData[columnIndex][i];
								this.bbar.selectElements.getStore().removeAt( this.bbar.selectElements.getStore().find('id', columnData[columnIndex][i] ) );
							}
						} catch ( ex ) {
							sfw.debug.trigger( ex, "sfw.dashboard.loadData itemLoad" );
						}
					}
				}

				// Refresh ui
				this.portal.doLayout();
				mask.hide();
				if ( itemsData === "" && typeof this._firstTime[this.profileId] === "undefined" ) {
					//Ext.Msg.alert( "Dashboard", "Selected profile does not have any items. Please select some, and add them to the dashboard." );
					this._firstTime[this.profileId] = true;
				}
			} catch ( ex ) {
				sfw.debug.trigger( ex, "sfw.dashboard.loadData" );
			}
		}

		/**
		 * Save button handler.
		*/
		this.bbar.saveButton = new Ext.Button({
			text: 'Save'
			,tooltip: 'Save current profile changes'
			,disabled: true
			,handler: this.saveData.createDelegate( this )
		});

		/**
		 * Make default profile.
		*/
		this.makeDefault = function() {
			var dashBoard = this;

			Ext.Ajax.request({
				url: sfw.util.constructApiRequest( config.dashboardApi + "default&profile_id=" + this.profileId )
				,success: function( response ) {
					if ( response.responseText == "" ) {
						return;
					}
					try {
						response = Ext.util.JSON.decode( response.responseText );
					} catch ( ex ) {
						return;
					}
					if ( typeof response.success !== "undefined" ) {
						if ( response.success == true ) {
							var selectProfileCombo = dashBoard.bbar.selectProfile;
							var profilesStore = selectProfileCombo.getStore();
							profilesStore.reload();
							var loadEvent = function( store ) {
								var index = store.findExact("default_profile", "1");
								if ( index !== -1 ) {
									store.removeListener( "load", loadEvent );
									var record = store.getAt( index );
									selectProfileCombo.setValue( record.data.name );
								}
							};
							profilesStore.addListener( "load", loadEvent );
							return;
						} else {
							return;
						}
					}
				}
				,failure: function() {
					mask.hide();
				}
			});
		}

		/**
		 * Mark current profile as default (for current user).
		*/
		this.bbar.defaultButton = new Ext.Button({
			text: 'Make Default'
			,tooltip: 'Mark current profile as default'
			,disabled: true
			,handler: this.makeDefault.createDelegate( this )
		});

		/**
		 * Profile delete handler.
		*/
		this.deleteProfile = function() {
			var mask = this.createMask( "Deleting profile..." );
			var id = this.profileId;
			var profileName = this.bbar.selectProfile.getStore().getById( this.profileId ).data.name;

			var dashBoard = this;

			Ext.Msg.show({
				title:'Delete Profile'
				,msg: 'Delete the selected dashboard profile: ' + profileName + '?'
				,buttons: Ext.Msg.YESNO
				,fn: function( btn ) {
					if ( btn == 'yes' ) {
						mask.show();
						Ext.Ajax.request({
							url: sfw.util.constructApiRequest( config.dashboardApi + "delete&profile_id=" + id )
							,success: function( response ) {
								mask.hide();
								if ( response.responseText == "" ) {
									Ext.Msg.alert( "Error", "Profile delete failed: data provider not ready." );
									return;
								}
								try {
									response = Ext.util.JSON.decode( response.responseText );
								} catch ( ex ) {
									Ext.Msg.alert( "Error", "Profile delete failed: invalid server response" );
									return;
								}
								if ( typeof response.success !== "undefined" ) {
									if ( response.success == true ) {
										dashBoard.refresh();
										dashBoard.loadData("");
										dashBoard.profileId = null;
										dashBoard.bbar.selectProfile.reset();
										// Load default profile
										dashBoard.firstTime = false;
										dashBoard.loadDefault();
										return;
									} else {
										Ext.Msg.alert( "Error", "Profile delete failed: " + response.msg );
										return;
									}
								}
								Ext.Msg.alert( "Error", "Profile delete failed: Unexpected error" );
							}
							,failure: function() {
								mask.hide();
								Ext.Msg.alert( "Error", "Cannot delete profile: Server connection" );
							}
						});
					}
				}
				,animEl: 'elId'
				,icon: Ext.MessageBox.QUESTION
			});

		}

		/**
		 * Delete current profile
		*/
		this.bbar.deleteButton = new Ext.Button({
			text: 'Delete'
			,tooltip: 'Delete current profile'
			,disabled: true
			,handler: this.deleteProfile.createDelegate( this )
		});

		/**
		 * Create a new profile
		*/
		this.newProfile = function() {
			var me = this;
			var mask = this.createMask( "Creating profile..." );

			var newProfileForm = new Ext.FormPanel({
				labelWidth: 75
				,frame: true
				,bodyStyle: 'padding:5px 5px 0'
				,width: 350
				,defaults: {
					width: 200
				}
				,defaultType: 'textfield'
				,items: [
					{ fieldLabel: 'Name', name: 'profile_name', allowBlank: false }
				]
			});

			var newProfileWin = new Ext.Window({
				layout:'fit'
				,title: 'New Dashboard Profile'
				,width: 350
				,height: 125
				,closeAction: 'hide'
				,plain: true
				,border: false
				,modal: true
				,constrain: true
				,items: newProfileForm
				,buttons: [{
					text: 'Save'
					,handler: function() {
						mask.show();
						newProfileWin.hide();
						var newName = newProfileForm.getForm().findField('profile_name').getValue();
						newProfileForm.getForm().submit({
							url: sfw.util.constructApiRequest( config.dashboardApi + "new" )
							,param: {
								profile_name: newName
							}
							,success: function( form, action ) {
								try {
									me.profileId = action.result.msg;

									// Load empty data - note, this also calls refresh internally so we don't need to.
									// We need a refresh to get the new profile name/id in the combo box
									me.loadData("");

									// Set the combo box to show the name of the new profile, as well as the dash title
									me.bbar.selectProfile.setValue( newName );
									me.setInterfaceTitle( newName );

									me.bbar.selectProfile.getStore().reload();
									me.groupDisable( false );

								} catch ( ex ) {
									// Something went wrong...
									Ext.Msg.alert( "Error", "Data provider not ready." );
								}
								mask.hide();
							}
							,failure: function( empty, action ) {
								Ext.Msg.alert( "Error", "Cannot save data: " + sfw.util.errorMessage( action ) );
								mask.hide();
							}
						});
					}
				},{
					text: 'Cancel'
					,handler: function() {
						newProfileWin.hide();
					}
				}]
			});
			newProfileWin.show();
		}

		/**
		 * New profile
		*/
		this.bbar.newButton = new Ext.Button({
			text: 'New'
			,disabled: sfw.sharedFunctions.readOnly()
			,tooltip: 'Create new profile'
			,handler: this.newProfile.createDelegate( this )
		});

		/**
		 * Rename profile functionality.
		*/
		this.renameProfile = function() {
			var profileName = this.bbar.selectProfile.getStore().getById( this.profileId ).data.name;

			Ext.Msg.prompt(
				'Rename Profile'
				,'Enter new profile name:'
				,function( button, text ) {
					if ( button == "ok" ) {
						var mask = this.createMask( "Renaming profile..." );
						var id = this.profileId;
						var dashBoard = this;
						mask.show();
						Ext.Ajax.request({
							url: sfw.util.constructApiRequest( config.dashboardApi + "rename&profile_id=" + id + "&name=" + text )
							,success: function( response ) {
								mask.hide();
								if ( response.responseText == "" ) {
									Ext.Msg.alert( "Error", "Profile rename failed: data provider not ready." );
									return;
								}
								try {
									response = Ext.util.JSON.decode( response.responseText );
								} catch ( ex ) {
									Ext.Msg.alert( "Error", "Profile rename failed: invalid server response" );
									return;
								}

								if ( typeof response.success !== "undefined" ) {
									if ( response.success == true ) {
										var selectProfileCombo = dashBoard.bbar.selectProfile;
										var profilesStore = selectProfileCombo.getStore();
										profilesStore.reload();
										var loadEvent = function( store ) {
											var record = store.getById( id );
											if ( typeof record !== 'undefined' ) {
												store.removeListener( "load", loadEvent );
												selectProfileCombo.setValue( record.data.name );
											}
										};
										profilesStore.addListener( "load", loadEvent );
										return;
									} else {
										Ext.Msg.alert( "Error", "Profile rename failed: " + response.msg );
										return;
									}
								}
								Ext.Msg.alert( "Error", "Profile rename failed: Unexpected error" );
							}
							,failure: function() {
								mask.hide();
								Ext.Msg.alert( "Error", "Cannot rename profile: Server connection" );
							}
						});
					}
				}
				,this
				,false
				,Ext.util.Format.htmlDecode( profileName )
			);
		}

		/**
		 * Rename profile
		*/
		this.bbar.renameButton = new Ext.Button({
			text: 'Rename'
			,disabled: true
			,tooltip: 'Rename current profile'
			,handler: this.renameProfile.createDelegate( this )
		});

		/**
		 * Load profile.
		*/
		this.loadProfile = function( combo, record, index ) {

			if ( record && record.get('id') && record.get('name') ) {
				var profileData = record.get('items');
				var profileName = record.get('name');
				this.profileId = record.get('id');
				this.loadData( profileData );
				this.setInterfaceTitle( profileName );
			}

			this.refresh();
		}

		this.datachangedHandler = function( me ) {
			if ( me.getTotalCount() === 0 && me.firstTime == true ) {
				me.firstTime = false;
			}
		}

		/**
		 * Select new profile
		*/
		this.bbar.selectProfile = new sfw.ComboBox({
			secuniaDecode: true
			,store: {
				root: 'data'
				,idProperty: 'id'
				,url: sfw.util.constructApiRequest( config.profileDDUrl )
				,fields: [
					{ name: 'id' }
					,{ name: 'name' }
					,{ name: 'default_profile' }
					,{ name: 'default' }
					,{ name: 'items' }
				]
				,firstTime: true
				,listeners: {
					datachanged: this.datachangedHandler.createDelegate( this )
				}
			}
			,tpl: '<tpl for="."><div class="x-combo-list-item">{name} {default}</div></tpl>'
			,secuniaType: 'remote'
			,valueField: 'name'
			,editable: false
			,allowBlank: true
			,width: 250
			,mode: 'remote'
			,displayField: 'name'
			,displayFieldAppend: 'default'
			,emptyText: 'Select Dashboard Profile...'
			,selectOnFocus: false
			,updatedCombo: true
			,listeners: {
				select: this.loadProfile.createDelegate( this )
			}
		});

		/**
		 * Create the bottom bar
		*/
		this.bbar.toolbar = new Ext.Toolbar({
			layout: 'hbox'
			,items: [
				this.bbar.selectElements
				,' ',this.bbar.selectProfile
				,' ',this.bbar.renameButton
				,' ',this.bbar.saveButton
				,' ',this.bbar.defaultButton
				,' ',this.bbar.deleteButton
				,' ',this.bbar.newButton
			]
		});

		/**
		 * Create the item container
		*/
		this.portal = new Ext.ux.Portal({
			frame: false
			,margins: '5 5 5 5'
			,flex: 1
			,items: temporaryItemStubs
		});

		/**
		 * Enable/disable various buttons that require a profile to be selected.
		 * @param value
		 *	Boolean true or false, to disable or enable
		*/
		this.groupDisable = function( value ) {
			this.bbar.selectElements.setDisabled( value );
			if ( sfw.sharedFunctions.readOnly() === true ) {
				value = true;
			}
			this.bbar.renameButton.setDisabled( value );
			this.bbar.deleteButton.setDisabled( value );
			this.bbar.defaultButton.setDisabled( value );
			this.bbar.saveButton.setDisabled( value );
		}

		/**
		 * Send the refresh signal to all items.
		*/
		this.refresh = function() {
			if ( typeof this.profileId != "undefined" ) {
				if ( this.profileId != null ) {
					this.groupDisable( false );
				} else {
					this.groupDisable( true );
				}
			} else {
				this.groupDisable( true );
			}

			this.bbar.selectElements.getStore().removeAll();
			this.bbar.selectElements.getStore().loadData( this.constructAvailablePortlets() );

			for ( key in this.selectedPortlets ) {
				for ( i = 0; i < config.portletArray.length; i++ ) {
					if ( config.portletArray[i].portletId == key ) {
						if ( config.portletArray[i].refresh != null ) {
							config.portletArray[i].refresh();
						}
					}
				}
			}

			this.interface.doLayout();
		}

		this.loadDefault = function() {
			if ( this.firstTime === false ) {
				this.setInterfaceTitle("");
				this.groupDisable( true );
				this.bbar.selectProfile.getStore().reload();
				var parent = this;
				var loadEvent = function( store ) {
					var index = store.findExact("default_profile", "1");
					if ( index != -1 ) {
						store.removeListener( "load", loadEvent );
						var record = store.getAt( index );
						parent.bbar.selectProfile.setValue( record.data.name );
						parent.loadData( record.data.items );
						parent.setInterfaceTitle( record.data.name );
						parent.profileId = record.data.id;
						parent.refresh();
					}
				};
				this.bbar.selectProfile.getStore().addListener( "load", loadEvent );
			}
			this.firstTime = true;
		}

		this.firstTime = false;

		/**
		 * Set interface title, having "Dashboard: " + profile name
		 * @param title
		 *	String profile name
		*/
		this.setInterfaceTitle = function( title ) {
			try {
				this.interface.setTitle( "Dashboard" + ( title != "" ? ": " : "" ) + title );
				this.interface.doLayout();
			} catch ( ex ) {
				// Do nothig, the interface was not rendered yet
			}
		}

		/**
		 * Create the main interface
		*/
		this.interface = new sfw.Panel({
			bbar: this.bbar.toolbar
			,title: "Dashboard"
			,layout: {
				type: 'vbox'
				,padding: '0'
				,align:'stretch'
			}
			,defaults: {
				border: false
			}
			,items: this.portal
			,refresh: this.refresh.createDelegate( this )
			,listeners: {
				render: this.loadDefault.createDelegate( this )
				,resize: this.refresh.createDelegate( this )
			}
		})
	} catch ( ex ) {
		sfw.debug.trigger( ex, "sfw.dashboard" );
	}

	return this;
}
