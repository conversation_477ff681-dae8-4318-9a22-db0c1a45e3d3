/**
 * @file sharedConstants.js
 * Contains various shared constants used throughout Secunia products
*/
sfw.sharedConstants = {};

sfw.sharedConstants.MOD_NSI = 0x0020;
sfw.sharedConstants.MOD_VDB = 0x0080;
sfw.sharedConstants.MOD_UM = 0x0080;
sfw.sharedConstants.MOD_VTS = 0x0004;
sfw.sharedConstants.MOD_VSS = 0x0010;

sfw.sharedConstants.dateShortInput = 'Y-m-d';
sfw.sharedConstants.dateLongOutput = 'jS M, Y H:i';

sfw.sharedConstants.responses = {
	UNEXPECTED_ERROR: -1,
	SUCCESS: 0,
	GENERIC_ERROR: 1,
	INVALID_ARGUMENTS: 2,
	NOT_ALLOWED: 3,
	CONFIG_ERROR: 4,
	RESOURCE_UNAVAILABLE: 5,
    ERROR_NO_WRITE_PERMISSION: 50,
    ERROR_ACCESS_DENIED: 51
};

/**
 * These constants are used to specify which edition of the CSI is being run.
 */
sfw.sharedConstants.HOSTED_EDITION = 0;
sfw.sharedConstants.SERVER_EDITION = 1;
// change this to set the current edition (overwritten by the Phing build)
sfw.sharedConstants.EDITION = sfw.sharedConstants.HOSTED_EDITION;

sfw.sharedConstants.AUTH_TYPE_NORMAL = 1;
sfw.sharedConstants.AUTH_TYPE_LDAP = 2;
sfw.sharedConstants.AUTH_TYPE_SSO = 7;