sfw.sharedForum = {};

// Set up the store for the forum sub-types - set up a few special vars for the sub-types we might actually want to
// initialize with based on the product supplying the forum.
sfw.sharedForum.psiForumType = 10;
sfw.sharedForum.csiForumType = 11;
sfw.sharedForum.vulnForumType = 13; // for VIM (?)

// The type store lists all possible forum types we can view
sfw.sharedForum.forumTypeStore = new Ext.data.SimpleStore({
	fields: [ 'id', 'text' ]
	,data: [
		[ 0, 'All Threads' ]
		,[ 2, 'Programs' ]
		,[ sfw.sharedForum.psiForumType, 'Personal Software Inspector' ]
		,[ 11, 'Software Vulnerability Manager' ]
		,[ 12, 'OSI' ]
		,[ 13, 'Vulnerabilities' ]
		,[ 14, 'Open Discussions' ]
	]
});

// The 'selection' store lists all possible forum types we can post to - only difference, we cannot post to
// 'all threads', so this one is removed.
sfw.sharedForum.forumSelectionStore = new Ext.data.SimpleStore({
	fields: [ 'id', 'text' ]
	,data: [
		[ 2, 'Programs' ]
		,[ 10, 'Personal Software Inspector' ]
		,[ 11, 'Software Vulnerability Manager' ]
		,[ 12, 'OSI' ]
		,[ 13, 'Vulnerabilities' ]
		,[ 14, 'Open Discussions' ]
	]
});


// Lookup the title from the store
sfw.sharedForum.subforumTitleRenderer = function( value, metaData, record ) {

	var refType = parseInt( value, 10 );

	// get the corresponding title from the store
	var title = '-';
	var index = sfw.sharedForum.forumTypeStore.find( 'id', refType );
	if ( 0 !== refType && -1 !== index ) {
		title = sfw.sharedForum.forumTypeStore.getAt( index ).data.text;
	}

	return title;
}