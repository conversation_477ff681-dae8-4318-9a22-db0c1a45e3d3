/**
 * @file cpe.js
*/

sfw.cpe = {};

/**
 * Function for rendering CPE Data - if it exists, give a link to the info window, otherwise just use N/A.
 * @param
 *	Integer product id
 * @param
 *	Boolean cpeExists Flag
 * @return
 *	String href as appropriate
*/
sfw.cpe.renderCpeInfo = function( productId, cpeExists ) {

	var cpeString = " CPE ";
	var hoverText = "";
	var html = '';

	if ( !cpeExists ) {
		cpeString += ": N/A";
		hoverText = "This product is not currently in the official CPE Dictionary.";
		html = ' <a title = "' + hoverText + '" href="javascript:void(0);" >' + cpeString + '</a>';
	} else {
		cpeString +=  'Exists.  Click for details.';
		hoverText = "Click for CPE details.";
		html = ' <a title = "' + hoverText + '" href="javascript:sfw.cpe.viewCpe(' +  productId + ')" >' + cpeString + '</a>';
	}

	return html;
}

/**
 * Provides CPE view object.
*/
sfw.cpe.create = function( productId ) {

	 var dataProviderURL = "action=cpe&which=data&productId=" + productId; // ENHANCEMENT: #port #advisory, the dataproviderurl and params should come from the local project

	this.makeHtml = function( data, name ) {

		var html = "No CPE data could be retrieved for this product";

		if ( data && (0 < data.length) && name ) {

			var html = '<table width="100%">' +
				'<tr>' +
				'<td style="text-align: left; padding-left: 30px;"><b>Product: &nbsp </b>' + name + '</td>' +
				'</tr><tr>' +
				'<td style="padding-top: 10px; padding-left: 30px;"><b>Associated CPEs:</b></td>' +
				'</tr>';

			for ( var i=0; i < data.length; i++ ) {
				html += '<tr>' +
					'<table width="60%">' +
					'<tr>' +
					'<td colspan="2" style="padding-left: 30px;"><hr></td>' +
					'</tr><tr>' +
					'<td style="text-align: right; width: 140px;"><b>CPE Name:</b></td>' +
					'<td style="padding-left: 10px;">' + data[i].name + '</td>' +
					'</tr><tr>' +
					'<td style="text-align: right; width: 70px;"><b>NVD Id:</b></td>' +
					'<td style="padding-left: 10px;">' + data[i].nvd_id + '</td>' +
					'</tr><tr>' +
					'<td style="text-align: right; width: 70px;"><b>Modification Date:</b></td>' +
					'<td style="padding-left: 10px;">' + data[i].modification_date + '</td>' +
					'</tr></table>';
			}

			html += '</tr></table>';
		}

		return html;
	}

	// ENHANCEMENT: #port #advisory
	// The fetchData function needs to be in the local project
	this.fetchData = function() {
		var me = this;
		Ext.Ajax.request({
			url: sfw.util.constructApiRequest( dataProviderURL )
			,success: function( data ) {
				var response = {};
				try {
					response = Ext.util.JSON.decode( data.responseText );
				} catch ( ex ) {
					// Silently ignore the error
				}
				me.populate( me.makeHtml( response[productId].cpes, response[productId].os_soft_name ) );
			}
			,failure: function() {
				Ext.Msg.alert( "Error", "Cannot fetch advisory data" );
			}
		});
	}

	// Call this to properly apply the CLS to the panel
	this.populate = function( data ) {
		var newItem = {};

		newItem.cls = 'ContentPadding';
		newItem.border = false;
		newItem.html = data;
		newItem.autoScroll = true;
		this.interface.removeAll();
		this.interface.add( newItem );
		this.interface.doLayout();
	}

	this.interface = new sfw.Panel({
		autoScroll: true
	});

	this.fetchData();

	return this;
}


sfw.cpe.viewCpe = function( productId ) {

	var cpe = new sfw.cpe.create( productId );

	var win = new Ext.Window({
		layout: 'fit'
		,width: 700
		,height: 400
		,items: cpe.interface
		,title: 'CPE Details'
		,maximizable: true
		,constrainHeader: true
		// ,renderTo: sfw.ui.center.getEl()
		,bbar: [ '->'
				 ,{
					 text: 'Close'
					 ,handler: function() {
						 win.close();
					 }
				 }
			   ]
		,listeners: {
			show: sfw.sharedFunctions.doSize.createDelegate( this )
		}
	})

	win.show();
}
