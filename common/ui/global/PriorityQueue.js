Ext.ns('sfw.ADT');
/**
* @class
* Implementation of a PriorityQueue
*
* @cfg {Function} compare (optional)
* default function returns -1 if x is higher than y and 1 if y is higher than x and 0 if equal, so with this function the highest number is first in the queue (highest priority)
*     compare = function( x, y ) {
*       return (x > y ) ? -1: ( y > x ) ? 1 : 0 ;
*     }
* more complex compares can be done by implementing your own compare and passing that in config.compare
*
* @cfg {Boolean} [simple=false]
*
*/
sfw.ADT.PriorityQueue = function( config ) {
	config = Ext.applyIf( config || {}, {simple:false} );

	var heap = [],
		parentIndex = function( k ) {
			return Math.floor( ( k-1 ) / 2 );
		},
		/**
		* @cfg {Function} compare (optional)
		* compares 2 items and return which have the highest priority.
		* the default function does a numeric comparison of x,y and returns
		* - -1 if x is higher than y
		* - 1 if y is higher than x
		* - 0 if equal priority
		*
		* if more complex comparing of items than numeric comparing is needed you can supply another compare function
		*
		*/
		compare = function( x, y ) {
			return (x > y ) ? -1: ( ( y > x ) ? 1 : 0 );
		},
		/**
		* @protected
		* @function
		* swaps the element at p1 and p2
		*/
		swap = function(p1,p2) {
			var tmp			= heap[ p1 ].pos;
			heap[ p1 ].pos 	= heap[ p2 ].pos;
			heap[ p2 ].pos 	= tmp;
			tmp 			= heap[ p1 ];
			heap[ p1 ] 		= heap[ p2 ];
			heap[ p2 ] 		= tmp;
		},

		insertLast = function(item){
			var pos = heap.length;
			heap.push({
				pos: pos,
				item: item
			});
			return heap[heap.length-1];
		},
		bubbleUp = function( pos ) {
			var pPos = parentIndex( pos );
			while ( pos > 0 && ( compare( heap[ pos ].item, heap[ pPos ].item ) < 1 ) ) {
				swap( pos, pPos );
				pos = pPos;
				pPos = parentIndex( pos );
			}
		},
		bubbleDown = function ( pos ) {
			var done = false, posLeft, posRight, posPrio, cmp;

			while ( !done ) {
				posLeft = 2*pos+1;
				posRight = posLeft+1;
				// if child pos1 is bigger than the heap length then
				if ( posLeft >= heap.length ) {
					done = true;
				} else {
					if ( posRight >= heap.length ) {
						posPrio = posLeft;
					} else if ( cmp=compare( heap[ posLeft ].item,heap[ posRight ].item ) < 1 ) {
						posPrio = posLeft;
					} else {
						posPrio = posRight;
					}
					if ( cmp=compare( heap[ pos ].item, heap[ posPrio ].item ) < 1 ) {
						done = true;
					} else {
						swap( posPrio, pos );
						pos = posPrio;
					}
				}
			}

		},
		kill = function(pos) {
			var elm=undefined, last = heap.length-1;
			if (last > pos) {
				// swap element at pos with last element
				swap( pos, last );
				// take last element out of the queue
				elm = heap.pop();

				// now lets bubble the new element at pos down toits correct place
				bubbleDown( pos );
			} else {
				if ( last < 0 ){
					return undefined;
				}
				elm = heap.pop();
			}
			elm.pos = undefined;
			return elm.item;
		};

	if ( config.compare && Ext.isFunction( config.compare ) ) {
		compare = config.compare;
	}
	this.peek = function() {
		if ( heap.length > 0 ) {
			return heap[ 0 ].item;
		}
	};
	/**
	* @method
	* remove the element with the highest priority from the PriorityQueue
	* @return
	* the element with the highest priority from the PriorityQueue
	*/
	this.remove = function() {
		if ( heap.length > 0 ) {
			return kill( 0 );
		}
	};
	/**
	* @method
	* add element to PriorityQueue
	*
	*/
	this.insert = function( item ) {
		var pos = heap.length,
			elm = insertLast( item );
		bubbleUp(pos);
		if (!config.simple) {
			return {
				kill: function() {
					if (elm.pos) {
						return kill(elm.pos);
					}
				},
				get: function(){
					return elm.item;
				},
				isQueued: function() {
					return elm.pos!==undefined;
				},
				isTopPriority: function() {
					return elm.pos===0;
				},
				reQueue: function() {
					var len=heap.length;
					if (elm.pos === undefined) {
						heap[len] = elm;
						elm.pos = len;
						bubbleUp(len);
					}
				}
			};
		}
	};
	/**
	* @method
	* Check if the queue is empty
	*
	* @return {Boolean} true if empty else false.
	*/
	this.isEmpty = function () {
		return heap.length === 0;
	};

	this.debugMode=function(bDebug) {
		if (bDebug || bDebug===undefined ) {
			this.heap=heap;
		} else {
			delete this.heap;
		}
	};
};
