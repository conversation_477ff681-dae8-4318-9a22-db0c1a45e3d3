sfw.events = {
	/*
	 * List of events that can be listened to
	 */
	list: [
		'beforerender'
		,'render'
		,'exportGridStore'
		/**
		 * @event afterbuildpagearray
		 * Fired after the pages have been built in sfw.ui.create()
		 * @param {Array} Array of built pages.
		 * @return {Boolean}
		 *	The return value is not currently used but any listeners should
		 *	still return true for success and false for failure
		 */
		,'afterbuildpagearray'
		/**
		 * @event afterbuildmenuarray
		 * Fired after the menus have been built in sfw.ui.create() but before they are rendered
		 * @param {Array} Array of built menus.
		 * @return {Boolean}
		 *	The return value is not currently used but any listeners should
		 *	still return true for success and false for failure
		 */
		,'afterbuildmenuarray'
		/**
		 * @event switchpage
		 * Fired when the page is switched.
		 * @param {Object} Page object
		 * @return {Boolean}
		 *	If any of the listeners return false then the page won't be switched
		 */
		,'switchpage'
	]

	/*
	 * Object containing listeners for the events e.g
	 *  sfw.events.listeners[ '_EVENT_NAME_' ] = [ listener_0, listener_1 ... listener_N ];
	 *
	 * The listeners are added and removed using the sfw.events.addListener()
	 * and sfw.events.removeListener() functions, respectively
	 */
	,listeners: {}
};

/**
 * @method
 * @public
 * Fires an event, executing all of it's handlers.
 * @param {String} eventName
 * @param mixed data
 * @return {Boolean}
 * 	Returns true unless unless the event isn't registered or one of the handlers
 *	has returned false. Handlers with deferred execution do not return values so
 *	they are treated as being successful and return true.
 */
sfw.events.fireEvent = function ( eventName, data ) {
	if ( !sfw.events.isRegistered( eventName ) ) {
		sfw.debug.log('sfw.events.fireEvent() - ' + eventName + ' is not a valid event');
		return false;
	}

	return sfw.events.notifyListeners( eventName, data );
};

/**
 * @method
 * @public
 * Notifies each of the listeners that the event has been fired
 * @param {String} eventName
 * @param mixed data
 * @return {Boolean}
 * 	Returns true unless one of the handlers return false. Handlers with deferred
 * 	execution do not return values so they are assumed to be true.
 */
sfw.events.notifyListeners = function( event, data ) {
	var listeners = sfw.events.listeners[ event ];
	var result = true; // returns true unless an event fails
	var defer; // time to defer handler execution

	if ( !listeners ) {
		return true;
	}

	for ( var i = 0; i < listeners.length; i++ ) {
		var listener = listeners[ i ], args = [ data, event ];
		defer = 25;

		switch ( Ext.type( listener ) ) {
		case 'object':
			if ( "undefined" !== typeof listener.defer ) {
				defer = listener.defer;
			}
			if ( listener.args ) {
				for ( i=0; i < listener.args.length; i++ ) {
					args.push( listener.args[ i ] );
				}
			}
			if ( listener.handler && Ext.isFunction( listener.handler ) ) {
				if ( defer ) {
					if ( listener.scope ) {
						listener.handler.defer( defer, listener.scope, args );
					} else {
						listener.handler.defer( defer, listener, args );
					}
				} else {
					if ( listener.scope ) {
						result = result && listener.handler.apply( listener.scope, args );
					} else {
						result = result && listener.handler.apply( listener, args );
					}
				}
			}
			break;
		case 'function':
			if ( defer ) {
				listener.defer( defer, null , args );
			} else {
				result = result && listener.apply( null, args );
			}
			break;
		case 'array':
			// array of listeners not implemented.
			break;
		default:
			// not implemented functionality for handling raw function callbacks
			break;
		}
	}

	return result;

};

sfw.events.isRegistered = function( eventName ) {
	if ( sfw.events.list.indexOf( eventName ) !== -1 ) {
		return true;
	}

	return false;
};

/**
 * addListener( eventName, listener )
 *
 * @param eventName : string
 * an item in sfw.events.list array
 *
 * @param listener : object | function
 * if a function it works as a callback function ( eventName, response )
 * if an object it is a config object that supports the following properties:
 * .handler	: function that is called each time the event "fires"
 * .scope	: (optional) The scope (this reference) in which to execute the run function. Defaults to the listener object.
 * .args	: (optional) An array of arguments to be added to the function specified by handler. If not specified, none will be added.
 * .defer	: (optional) A number specified in milliseconds that specifies how long to defer the handler's execution. Defaults to 25.
 *
 * todo: update
 */

sfw.events.addListener = function ( eventName, listener ) {
	if ( !sfw.events.isRegistered( eventName ) ) {
		sfw.debug.log( 'Warning: sfw.events.addListener() - ' + eventName + ' is not a registered event.' );
	}
	if ( !sfw.events.listeners[ eventName ] ) {
		sfw.events.listeners[ eventName ] = [ listener ];
	} else {
		sfw.events.listeners[ eventName ].push( listener );
	}

	return true;
};

/**
 * removeListener( eventName, listener )
 *
 * @param eventName : string
 * an item in sfw.events.list
 *
 * @param listener : object | function
 * remove the listener, so it nolonger gets called when the event is fired.
 *
 * stops the polling if its the last listener listening on the event
 *
 */
sfw.events.removeListener = function ( eventName, listener ) {
	if ( sfw.events.listeners[ eventName ] ) {
		var listeners = sfw.events.listeners[ eventName ];
		var index = listeners.indexOf( listener );
		if (index !== -1) {
			listeners.splice( index, 1 );
			/*
			if ( listeners.length == 0 ) {
				delete sfw.events.listeners[eventName];
				sfw.events.taskRunner.stop( sfw.events.active[ eventName ] );
				delete sfw.events.active[ eventName ];
			}
			*/
		}
	}
};

sfw.events.init = function( events ) {
	sfw.events.list = sfw.events.list.concat( events );
};
