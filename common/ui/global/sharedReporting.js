sfw.sharedReporting = {};

// ALIVE is report that needs to be generated. It awaits for the next time it will be generated.
// Related to both one time and reccuring reports.
// IN PROGRESS is active while report is being generated. Relates to both.
// PROCESSED relates only to on one time reports. Once they are generated they have status of processed
// ALIVE_PROCESSED is active on recurring reports when they are processed at least once and they await next time to be processed again.
sfw.sharedReporting.processStatus = {
	ALIVE: 0
	,IN_PROGRESS: 1
	,PROCESSED: 2
	,ALIVE_PROCESSED: 3
	,FAILED_TOO_BIG: 4
};


// This function only modifies the report title if it is empty - i.e., no title was user-selected, thus we enter our default title for display purposes
// Note - this should match the default value set in er_report.php, as that is what is actually printed in the report in the default case.
sfw.sharedReporting.reportTitleRenderer = function( value, metaData, record ) {
	if ( !value ) {
		return 'Flexera Custom Report';
	} else {
		return value;
	}
};

sfw.sharedReporting.recurrenceScheduleRenderer = function( value, metaData, record ) {
	if ( typeof value == 'undefined' ) {
		return '-';
	}

	if ( parseInt(record.data.one_time_gen, 10) == 1 ) {
		return 'None (One-Time Report)';
	}

	var schedule = value.split( ',' );
	if ( schedule.length !== 3 ) {
		return '-';
	}

	if ( !( typeof parseInt( schedule[0], 10 ) == 'number' ) || !( typeof parseInt( schedule[3], 10 ) == 'number' ) || !( typeof parseInt( schedule[3], 10 ) == 'number' ) ) {
		return '-';
	}

	if ( parseInt( schedule[0], 10 ) == 1 ) {
		return "Every Day";
	} else if ( parseInt( schedule[0], 10 ) > 1 ) {
		return "Every " + schedule[0] + " Days";
	} else if ( parseInt( schedule[1], 10 ) == 1 ) {
		return "Every Week";
	} else if ( parseInt( schedule[1], 10 ) > 1 ) {
		return "Every " + schedule[1]  + " Weeks";
	} else if ( parseInt( schedule[2], 10 ) == 1 ) {
		return "Every Month";
	} else if ( parseInt( schedule[2], 10 ) > 1 ) {
		return "Every " + schedule[2]  + " Months";
	}

	return '-';
};

sfw.sharedReporting.processStatusRenderer = function( value, metaData, record ) {
	var status = '';
	var processStatus = parseInt( value, 10 );
	switch ( processStatus ) {
	case sfw.sharedReporting.processStatus.ALIVE:
		status = "Alive";
		break;
	case sfw.sharedReporting.processStatus.IN_PROGRESS:
		status = "In Progress";
		break;
	case sfw.sharedReporting.processStatus.PROCESSED:
		status = "Processed";
		break;
	case sfw.sharedReporting.processStatus.ALIVE_PROCESSED:
		status = "Alive";
		break;
	case sfw.sharedReporting.processStatus.FAILED_TOO_BIG:
		status = '<div qtip="Report could not be generated due to an excessive volume of data requested. Please reconfigure report and try again.">Failed</div>';
		break;
	default:
		status = "Error";
		break;
	}

	return status;
};

/**
* Purpose of this function is to keep data consistently htmlEncoded on user side. That is the way everything else works.
* This function has to be used every time we extract the options from the configuration string that comes from server,
* if there is user submited data inside, that is not integer.
*
* Problem is that we have our own syntax in configuration table, which has special characters ;, :, |. HTML encoded data can
* contain strings like &amp; which have ; inside which could break syntax we are using.
*/
sfw.sharedReporting.extractOptions = function( configurationString ) {
	var option;
	var options = Ext.util.Format.htmlDecode( configurationString ).split(';');
	for ( option in options ) {
		if ( options.hasOwnProperty( option ) ) {
			options[option] = Ext.util.Format.htmlEncode( options[option] );
		}
	}

	return options;
};

// This function is called anytime you want the main reporting overview page.  The creatingObject MUST have
// already defined the gridDataFields property and the id property.
// Modify to pass in any other custom config params if needed as reporting devlops.

// ENHANCEMENT - for now type distinguishes VIM vs CSI so we can construct the API request properly.
// Presumably this is changing in the new structure to use the same sfw.util.constructApiRequest we use everywhere
// - get functional for now with the old globals.apiPath for CSI and change later.
sfw.sharedReporting.buildReportOverview = function( creatingObject, sourceType ) {
	var field;
	if ( "undefined" === typeof(creatingObject) ||
		 "undefined" === typeof(creatingObject.gridDataFields) ||
		 "undefined" === typeof(creatingObject.id) ) {
		return;
	}

	try {
		var self = creatingObject;

		var dataProviderURL = 'action=reporting&which=';
		var getAction = 'overview';
		var deleteAction = 'delete';
		var generateReportAction = 'generate_report';
		var urlGetData = '';

		// default is the VIM for now
		if ( 'csi' === sourceType ) {
			urlGetData = globals.apiPath() + '&' + dataProviderURL + getAction + "&source=csi";
			self.urlDeleteData = globals.apiPath() + '&' + dataProviderURL + deleteAction;
			self.urlGenerateReportData = globals.apiPath() + '&' + dataProviderURL + generateReportAction;

		} else {
			urlGetData = sfw.util.constructApiRequest( dataProviderURL + getAction );
			self.urlDeleteData = sfw.util.constructApiRequest( dataProviderURL + deleteAction );
			self.urlGenerateReportData = sfw.util.constructApiRequest( dataProviderURL + generateReportAction );
			sourceType = 'vim';
		}

		self.refresh = function() {
			self.gridRegion.getStore().reload();
		};

		self.loadReportConfiguration = function( configurationAllElements ) {
			if ( !(typeof configurationAllElements == 'undefined' ) ) {
				self.reportGenerator.loadAll( configurationAllElements );
			}
		};

		self.openReportConfigurationWindow = function( titleType, hidden ) {

			// Create an instance of the reportgenerator
			// If already created, reset all the elements for new configuration
			if ( 'undefined' === typeof( self.reportGenerator ) ) {
				self.reportGenerator = new sfw.reportGenerator.create( self, sourceType );
				self.reportGenerator.updateTitle( titleType );

			} else {
				self.reportGenerator.resetAll();
				self.reportGenerator.updateTitle( titleType );
			}

			if ( !self.reportGenerator.window.isVisible() ) {
				self.reportGenerator.window.show();
			}

			// Call this during init to create the window, but don't show it.  For this we have the 'hidden' parameter set.
			if ( hidden === true ) {
				self.reportGenerator.window.hide();
			}
			sfw.csiReReportFormat.create().setReportFormat(null);

		};

		self.editReportConfiguration = function ( grid, rowIndex ) {
			// Becuase of the way the grid is configured, we need to send more data than
			// it is actually being displayed
			var configurationAllElements = grid.getStore().getAt( rowIndex ).data;
			configurationAllElements.id = parseInt( configurationAllElements.id, 10 );
			self.openReportConfigurationWindow( 'edit' );
			self.loadReportConfiguration( configurationAllElements  );
		};

		self.rowcontextmenu = function( grid, rowIndex, event ) {

			/**
			 * @msp
			 * MSP Users must not have access to Modify Reports
			 */
			if ("csi" == sourceType && LoginDetails.account.isMspUser()) {
				return;
			}

			var additionalDeleteText = 'Report Schedule';

			var selected = grid.getSelectionModel().getSelections();
			var multipleSelect = false;
			var contextMenu = new sfw.Menu({});
			var deleteIdList = '';
			var generateId = '';
			var disabled = false;

			// Support multiple selection for delete
			if ( 1 < selected.length ) {
				multipleSelect = true;
				additionalDeleteText = "All Selected Report Schedules";

				var selectionArray = [];
				for ( var i=0; i < selected.length; ++i ) {
					selectionArray.push( selected[i].id );
				}

				deleteIdList = selectionArray.join(",");
			}

			if ( !multipleSelect ) {
				deleteIdList = grid.getStore().getAt( rowIndex ).data.id;
				generateId = grid.getStore().getAt( rowIndex ).data.id;
				if ( sfw.sharedFunctions.readOnly( sourceType ) == true || grid.getStore().getAt( rowIndex ).data['process_status'] == sfw.sharedReporting.processStatus.FAILED_TOO_BIG ) {
					disabled = true;
				}

				contextMenu.add([{
					text: 'View/Edit Report Schedule'
					,disabled: sfw.sharedFunctions.readOnly( sourceType )
					,handler: function() {
						self.editReportConfiguration( grid, rowIndex );
					}
 				},{
					text: 'Generate Report Now'
					,disabled: disabled
					,handler: function() {

						Ext.Ajax.request({
							url: self.urlGenerateReportData
							,timeout: 60000 // an increase from the default of 30000 ms
							,params: {
								generate_id: generateId
							}
							,success: function( data ) {
								var response = {};
								try {
									response = Ext.util.JSON.decode( data.responseText );
									switch( response.error ) {
									case 0:
										// no alert, because PDF generation gives a lag of few seconds and
										// popup after few seconds is not a expected UI behaviour
										break;
									case 1:
										Ext.Msg.alert( "Error", "Report Id is Incorrect" );
										break;
									case 2:
										Ext.Msg.alert( "Error", "Configuration Id is Incorrect" );
										break;
									case 3:
										Ext.Msg.alert( "Report generation", "The report is being generated and will be available in a few moments." );
										break;
									default:
										Ext.Msg.alert( "Unexpected Error", "Unable To Generate Report..." );
										break;
									}
									self.refresh();
								} catch ( ex ) {
									// Silently ignore the error
								}
							}
							/**
							 * Handles a request failure
							 * @param {Object} response
							 * @param {Number} response.tId
							 * @param {Number} response.status
							 * @param {String} response.statusText
							 * @param {Boolean} response.isAbort
							 * @param {Boolean} response.isTimeout
							 */
							,failure: function( response ) {
								if ( true === response.isTimeout ) {
									Ext.Msg.alert( "Report generation", "The report is still being generated.<br/><br/>Refresh the Report Configuration page in a few seconds to see if it has been completed" );
								} else {
									Ext.Msg.alert( "Unexpected Error", "Unable To Generate Report..." );
								}
							}
						});
					}
 				}]);
			}

			// We always add delete - it is just a matter of if it's multiple delete or not
			contextMenu.add({
				text: 'Delete ' + additionalDeleteText
				,disabled: sfw.sharedFunctions.readOnly( sourceType )
				,handler: function() {
					Ext.Ajax.request({
						url: self.urlDeleteData
						,params: {
							delete_id_list: deleteIdList
						}
						,success: function( data ) {
							var response = {};
							try {
								response = Ext.util.JSON.decode( data.responseText );
								switch( response.error ) {
								case 0:
									var message = 'Report Configuration' + (multipleSelect ? 's' : '') + ' Deleted';
									Ext.Msg.alert( "Success", message );
									self.refresh(); // reload the grid
									break;
								case 1:
									Ext.Msg.alert( "Error", "Report Id is Incorrect" );
									break;
								default:
									Ext.Msg.alert( "Unexpected Error", "Unable to Delete..." );
									break;
								}
							} catch ( ex ) {
								// Silently ignore the error
							}
						}
						,failure: function() {
							Ext.Msg.alert( "Unexpected Error", "Unable to Delete..." );
						}
					});
				}
			});

			contextMenu.showAt( event.getXY() );
		};

		var gridConfigToolbar;
		/**
		 * @msp
		 * MSP Users must not be allowed to create Reports
		 */
		if ("csi" == sourceType && ! LoginDetails.account.isMspUser()) {
			self.viewReportConfigurationButton = new Ext.Button({
				text: 'Generate New Report'
				,disabled: sfw.sharedFunctions.readOnly( sourceType )
				,handler: function() {
					self.openReportConfigurationWindow( 'new' );
				}
			});
			gridConfigToolbar = new Ext.Toolbar({
				height: 26
				,items: [ self.viewReportConfigurationButton ]
			});
		}

		var gridConfig = {
			region: 'center'
			,secuniaSorting: true
			,defaultSorters: [{ field: 'last_gen_date', direction:'DESC'}]
			,jsonSorters: true
			,stateful: !!self.stateful //cast to boolean.
			,secuniaPaging: true
			,displayMsg: 'Reports {0} - {1} of {2}'
			,pageSize: 10
			,selModel: new Ext.grid.RowSelectionModel({})
			,tbar: gridConfigToolbar
			,autoExpandColumn: 'report_title'
			,viewConfig: {
				emptyText: 'No reports scheduled for generation.'
				,deferEmptyText: false
			}
			,listeners: {
				rowcontextmenu: self.rowcontextmenu.createDelegate( this )
				,rowdblclick: function( grid, rowIndex ) {
					if ( sfw.sharedFunctions.readOnly( sourceType ) ) {
						// Readonly users can't open the edit window
						return;
					}
					self.editReportConfiguration( grid, rowIndex );
				}

			}
		};

		if ( 'csi' === sourceType ) {
			// TODO: this is a temporary workaround for moving away from databoundgrid. Internaly, for CSI it will use sfw.GridPanel.
			var exportColumns = [], fields = [], columnsForGrid = [], fieldForStore, fieldForGrid;
			var columns = self.gridDataFields;
			for ( var i = 0, count = columns.length; i < count; i++ ) {
				if ( typeof columns[i].column !== 'undefined' ) {
					field = {
						dataIndex: columns[i].column.dataIndex || columns[i].name
						,renderer: columns[i].column.renderer || undefined
						,header: columns[i].column.header
					};
					fieldForStore = {
						name: columns[i].column.dataIndex || columns[i].name
						,type: 'string'
					};
					fieldForGrid = {
						id: columns[i].column.dataIndex || columns[i].name
						,dataIndex: columns[i].column.dataIndex || columns[i].name
						,header: columns[i].column.header
						,align: columns[i].column.align || undefined
						,sortable: columns[i].column.sortable || true
						,hidden: columns[i].column.hidden || false
						,width: columns[i].column.width || undefined
						,renderer: columns[i].column.renderer || undefined
					};
					fields.push( Ext.apply( {}, fieldForStore ) );
					columnsForGrid.push( Ext.apply( {}, fieldForGrid ) );
				} else {
					field = {
						dataIndex: columns[i].name
						,renderer: undefined
						,header: undefined
					};
					if ( columns[i].name !== "id" ) {
						fields.push( Ext.apply( {}, {
							 name: columns[i].name
							,type: "string"
						} ) );
					}
				}
				exportColumns.push( Ext.apply( {}, field ) );
			}

			fields.splice( 0, 0, { name: 'id', type: 'int' } );

			// Remove download link
			exportColumns[6].renderer = undefined;
			exportColumns.splice(11, 1);

			gridConfig.columns = columnsForGrid;

			gridConfig.store = new sfw.JsonStore({
				idProperty: 'id'
				,fields: fields
				,url: urlGetData
			});
		} else {
			gridConfig.datafields = self.gridDataFields;
			gridConfig.store = {
				url: urlGetData
				,baseParams: {
					'source_type': sourceType
				}
				,idProperty: 'id'
				,listeners: {
					load: {
						fn: function () {
						    self.refresh();
						}
						,single: true
					}
				}
			};
		}

		if ( 'csi' === sourceType ) {
			self.gridRegion = new sfw.GridPanel( gridConfig );
		} else {
			self.gridRegion = new sfw.DataBoundGrid( gridConfig );
		}

		self.title = 'Report Configuration';

		self.interface = new sfw.Panel({
			title: self.title
			,tabTip: self.title
			,id: self.id
			,layout: 'border'
			,border: false
			,items: [
				new sfw.Panel({
					region: 'center'
					,title: self.gridTitle
					,items: self.gridRegion
				})
			]
			,listeners: {
				afterrender: function() {
					self.openReportConfigurationWindow( 'new', true );
				}
			}
		});

	} catch ( ex ) {
		sfw.debug.trigger( ex, self.id );
	}
};
