/**
 * @file toptoolbar.js
 * Provides top toolbar. If not included it will be silently ignored.
 *
 * Configuration options:
 *
 * toptoolbar: {
 * 	buttons: [
 *		button1, button2, button3 - array of buttons to be included
 * 	]
 *	,title: title
 *	,type: "toolbar" || "panel" || "both" ( default to toolbar ) IMPLIES:
 *	,item: panelObject
 *	,height: 42 ( default toolbar height )
 *	,skipLogo: true or false, to skip logo or not
 * }
*/

sfw.toptoolbar = {}

sfw.toptoolbar.create = function() {
	try {
		var items = [];
		var regionItems = new sfw.Panel({});

		var height = 42;
		var title = '';


		// Create toolbar interface, if configuration is defined
		// else, generate an empty panel (regionItems)

		if ( typeof sfw.configuration.toptoolbar !== "undefined" ) {

			if ( sfw.configuration.toptoolbar.title ) {
				title = sfw.configuration.toptoolbar.title;
			}

			// Specify height for the toolbar
			height = sfw.configuration.toptoolbar.height || height;
			if (  sfw.configuration.toptoolbar.title && !sfw.configuration.toptoolbar.height ) {
				height += 26;
			}

			// Make the toolbar according to the type defined in the local configuration
			var toolbarType = sfw.configuration.toptoolbar.type;
			switch ( toolbarType ) {
			case 'panel':
				regionItems = sfw.configuration.toptoolbar.titleBar;
				break;
			case 'toolbar':
			case 'both':
			default:
				if ( sfw.configuration.toptoolbar.buttons.length != 0 ) {
					items.push( sfw.configuration.toptoolbar.buttons );
				}
				if ( !sfw.configuration.toptoolbar.skipLogo ) {
					items.push({ xtype: 'tbfill' }
							   ,new Ext.BoxComponent({
								   autoEl: {
									   tag: 'img'
									   ,src: 'gfx/ui/secunia.png'
								   }
							   })
							   ,{
								   xtype: 'tbspacer'
								   ,width: 20
							   });
				}
				regionItems = new Ext.Toolbar ({
					height: height
					,align: 'left'
					,id: 'toptoolbar'
					,items: items
					,cls: sfw.configuration.toptoolbar.cls
				});
				break;
			}

			if ( toolbarType == "both" ) {
				height = height + sfw.configuration.toptoolbar.titleBar.height;
				items = new sfw.Panel({
					margins: '0 0 4 0'
					,title: title || null
					,layout: 'border'
					,items: [
						new sfw.Panel({
							region: 'north'
							,height: sfw.configuration.toptoolbar.titleBar.height
							,items: sfw.configuration.toptoolbar.titleBar
						})
						,new sfw.Panel({
							region: 'center'
							,items: regionItems
						})
					]
					//,baseCls: sfw.configuration.toptoolbar.baseCls
					//,border:  sfw.configuration.toptoolbar.border
					//,frame:  sfw.configuration.toptoolbar.frame
				});
			} else {
				items = regionItems;
			}
		}

		// Create interface
		this.interface = new sfw.Panel({
			region: 'north'
			,margins: '0 0 4 0'
			,height: height
			,title: title || null
			,items: items
			,baseCls: sfw.configuration.toptoolbar.baseCls
			,border:  sfw.configuration.toptoolbar.border
			,frame:  sfw.configuration.toptoolbar.frame
		});

	} catch ( ex ) {
		sfw.debug.trigger( ex, "toptoolbar" );
	}

	return this.interface;
}
