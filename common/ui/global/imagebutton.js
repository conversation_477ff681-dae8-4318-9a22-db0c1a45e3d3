/**
 * @file imagebutton.js
 * A button that only shows an image, and can do it with no padding or margins.
 *
 * Configuration options: See Ext.button
 *
*/


sfw.ImageButton = Ext.extend(Ext.Button, {
	initComponent: function(){

		var imageBtnTmpl = new Ext.Template('<span class="x-btn {3}" id="{4}"><button type="{0}"></button></span>');
		imageBtnTmpl.compile();

		sfw.ImageButton.superclass.initComponent.call(this);
		Ext.applyIf(this, {
			template: imageBtnTmpl
		});
	}
});

Ext.reg('imagebutton', sfw.ImageButton);
