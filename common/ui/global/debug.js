/**
 * @file util.js
 * Provides debugging functionality or at least error logging
 *
 * Configuration options:
 *	logLevel: 3, 2, 1, 0
 *
 *	logNotify: true/false
*/

sfw.debug = {};

sfw.debug.logLevel = 3;
sfw.debug.logNotify =  true


/**
 * Called uppon an error. Depending on error log level selected, will display a Ext.Msg window or alert() containing error details.
 *
 * @param exception
 *	Object contains the exception values
 * @param name
 *	String name of the function it occured in
 * @param data
 *	String Optional additional data to be logged
*/
sfw.debug.trigger = function( exception, name, data ) {
	if ( typeof sfw.debug.logLevel === "undefined" ) {
		return;
	}

	var message = "Error: " + exception.name + ( name ? '\nFunction: ' + name : '');

	if ( !data ) {
		var data = "";
	} else {
		data = '\nData: ' + data;
	}

	switch ( sfw.debug.logLevel ) {
		case 3:
			if ( ! Ext.isIE ) {
				/* Mozilla/Gecko non-standard extension */
				message += '\nFile: ' + exception.fileName + '\nLine: ' + exception.lineNumber;
			} else {
				/* Internet Explorer non-standard extension */
				message += '\nNumber: ' + exception.number;
			}
		case 2:
			message += '\nDescription: ' + exception.message;
		case 1:
			message += data;
			break;
		case 0:
			return;
	}

	if ( sfw.debug.logNotify === true ) {
		this.notify( message );
	}

	this.log( message );
}

sfw.debug.log = function( message ) {
	// LogMessage( message );

	// Stub function
	// Needs to be implemented / overridden in the local project

	// todo: use CSI logging mechanism i.e. have an internal logger
	try {
		console.log( message );
	} catch ( ex ) {}
}

sfw.debug.showMessage = false;

sfw.debug.notify = function( message, position ) {
	if ( sfw.debug.showMessage === false ) {
		return;
	}

	try {
		Ext.Msg.show({
			title: 'Error'
			,msg: sfw.util.replaceAll( "\n", "<br>", message )
			,button: Ext.Msg.OK
			,icon: Ext.MessageBox.ERROR
		});
	} catch ( ex ) {
		alert( message );
	}
}
