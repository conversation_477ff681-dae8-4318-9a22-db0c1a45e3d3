sfw.miscReportElement = {};

// This is shared between the CSI and the VIM as it is almost identical, modulo the specific strings
// about the details of the user selection to publish
sfw.miscReportElement.buildMiscReportElement = function( creatingObject, name, publishStrings, skipModules ) {
	try {
		var self = creatingObject;

		// VARIABLES
		// ---------
		self.name = name;

		self.errorResponse = {
			reportingElement: 'Miscellaneous'
			,msg: ''
		};

		self.setFilename = false;

		// Set up strings passed in
		self.customizeDisplayTitle = "Customize Display";
		self.publishDetailsString = "Publish all details.";
		self.publishAliasString = "Publish using an alias:";
		self.noPublishString = "Do not publish anything.";

		if ( "undefined" !== typeof(publishStrings.customizeDisplayTitle) &&
			 ( 0 < publishStrings.customizeDisplayTitle.length ) ) {
			self.customizeDisplayTitle = publishStrings.customizeDisplayTitle;
		}
		if ( "undefined" !== typeof(publishStrings.publishDetailsString) &&
			 ( 0 < publishStrings.publishDetailsString.length ) ) {
			self.publishDetailsString = publishStrings.publishDetailsString;
		}
		if ( "undefined" !== typeof(publishStrings.publishAliasString) &&
			 ( 0 < publishStrings.publishAliasString.length ) ) {
			self.publishAliasString = publishStrings.publishAliasString;
		}
		if ( "undefined" !== typeof(publishStrings.noPublishString) &&
			 ( 0 < publishStrings.noPublishString.length ) ) {
			self.noPublishString = publishStrings.noPublishString;
		}


		// GENERIC FUNCTIONS
		// -----------------

		self.reset = function() {
			self.filenameCheckbox.setValue(false);
			self.filenameInput.reset();
			self.filenameInput.setDisabled(true);
			self.reportTitleCheckbox.setValue(false);
			self.reportTitleTextfield.reset();
			self.reportTitleTextfield.setDisabled(true);
			self.reportParamsCheckbox.reset();
			self.disableOptions(true);
		}

		self.validate = function() {
			if ( self.setFilename ) {
				isValid = Ext.form.VTypes.PdfFile( self.filenameInput.getValue() );
				if ( !isValid ) {
					self.errorResponse.msg = 'The format for the PDF file name is incorrect.'
					return self.errorResponse;
				}
			}

			// Check if the custom alias is provided
			if ( self.radioAliasText.getValue() && !self.aliasField.validate() ) {
					self.errorResponse.msg = 'The format of the custom alias is incorrect.';
					return self.errorResponse;
			}

			return true;
		}

		self.collect = function() {
			var params = {};
			if ( self.filenameCheckbox.getValue() ) {
				params['file_name'] = self.filenameInput.getValue();
			}

			if ( self.reportTitleCheckbox.getValue() && self.reportTitleTextfield.getValue() ) {
				// Note - if they choose not to set a title that is ok - we set the default one at the report creation stage
				// rather than save a value to the DB that they did not configure
				params['report_title'] = self.reportTitleTextfield.getValue();
			}

			params['show_report_params'] = self.reportParamsCheckbox.getValue() ? 1 : 0;

			// Details to publish
			if ( self.radioFullText.getValue() ) {
				params['publish_detail_level'] = 1; // Include all
			} else if ( self.radioAliasText.getValue() ) {
				params['publish_detail_level'] = 2; // Include custom alias for the group
				params['publish_group_alias'] =  self.aliasField.getValue();
			} else {
				params['publish_detail_level'] = 3; // Do not include details
			}

			return params;
		}

		self.load = function( configurationAllElements, configurationString ) {

			self.reset(); // make a blank form before populating values

			if ( typeof configurationAllElements == 'undefined' ) {
				return false;
			}

			var fileName = configurationAllElements.filename;
			var reportTitle = configurationAllElements.report_title;

			if ( ( typeof fileName != 'undefined' ) && ( fileName != '' )  ) {
				self.filenameCheckbox.setValue(true);
				self.filenameInput.setDisabled(false);
				self.filenameInput.setValue( Ext.util.Format.htmlDecode( fileName ) );
			}
			if ( ( typeof reportTitle != 'undefined' ) && ( reportTitle != '' ) ) {
				self.reportTitleCheckbox.setValue(true);
				self.reportTitleTextfield.setDisabled(false);
				self.reportTitleTextfield.setValue( Ext.util.Format.htmlDecode( reportTitle ) );
			}

			// Parse the configuration string
			var options = configurationString.split(/[;:]+/);
			for ( var i = 0; i < options.length; i++ ) {
				switch( options[i] ) {
				case 'SHOW':
					self.reportParamsCheckbox.setValue(true);
					self.disableOptions(false);
					break;
				case 'MISC_ALL':
					self.radioFullText.setValue(true);
					break;
				case 'MISC_NONE':
					self.radioNoText.setValue(true);
					break;
				case 'MISC_ALIAS':
					self.radioAliasText.setValue(true);
					( typeof options[i+1] == 'undefined' ) ? self.aliasField.setValue("") : self.aliasField.setValue(  Ext.util.Format.htmlDecode( options[i+1] ) );
					break;
				}
			}
		}

		// HANDLERS
		// --------

		self.handleCheckbox = function( checkbox, checked ) {
			if ( checkbox === self.filenameCheckbox ) {
				if ( checked ) {
					// enable textfield
					self.setFilename = true;
					self.filenameInput.setDisabled(false);
				} else {
					self.setFilename = false;
					self.filenameInput.reset();
					self.filenameInput.setDisabled(true);
				}
			} else if ( checkbox === self.reportTitleCheckbox ) {
				if ( checked ) {
					// enable textfield
					self.setReportTitle = true;
					self.reportTitleTextfield.setDisabled(false);
				} else {
					self.setReportTitle = false;
					self.reportTitleTextfield.reset();
					self.reportTitleTextfield.setDisabled(true);
				}
			} else if ( checkbox === self.reportParamsCheckbox ) {
				if ( checked ) {
					self.disableOptions(false);
				} else {
					self.disableOptions(true);
				}
			}
		}


		// Helper Functions
		// ----------------

		self.disableOptions = function( disable ) {
			if ( disable ) {
				// self.publishDetailsForm.setDisabled(true);
				self.radioFullText.setDisabled(true);
				self.radioFullText.setValue(false);
				self.radioAliasText.setDisabled(true);
				self.radioAliasText.setValue(false);
				self.aliasField.setDisabled(true);
				self.aliasField.setValue("");
				self.radioNoText.setDisabled(true);
				self.radioNoText.setValue(true);
			} else {
				// self.publishDetailsForm.setDisabled(false);
				self.radioFullText.setDisabled(false);
				self.radioAliasText.setDisabled(false);
				self.aliasField.setDisabled(false);
				self.radioNoText.setDisabled(false);
			}
		}

		// UI
		// --

		self.filenameCheckbox = new Ext.form.Checkbox({
			boxLabel: 'Set the file name for the PDF report file generated.'
			,checked: false
			,listeners: {
				check: self.handleCheckbox.createDelegate(this)
			}
		});

		self.filenameInput = new Ext.form.TextField({
			allowBlank: false
			,width: 300
			,labelAlign: 'left'
			,fieldLabel: 'PDF Filename'
			,enableKeyEvents: true
			,invalidText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.pdf\'.'
			,value: ".pdf"
			,disabled: true
			,maxLength: 95
			,validator: function(value) { // custom validator to only validate if checkbox was checked
				if ( self.setFilename ) {
					return Ext.form.VTypes.PdfFile(value);
				} else {
					return true;
				}
			}
			,listeners: {
				invalid: self.validate.createDelegate(this)
				,valid: self.validate.createDelegate(this)
			}
		});

		self.reportTitleCheckbox = new Ext.form.Checkbox({
			boxLabel: 'Set the report title.'
			,checked: false
			,listeners: {
				check: self.handleCheckbox.createDelegate(this)
			}
		});

		self.reportTitleTextfield = new Ext.form.TextField({
			allowBlank: false
			,width: 300
			,labelAlign: 'left'
			,fieldLabel: 'Report Title'
			,enableKeyEvents: true
			,value: "Flexera Custom Report"
			,disabled: true
			,maxLength: 95
		});

		self.fileNameArea =  new Ext.form.FieldSet({
			title: 'Report File Name'
			,autoWidth: true
			,items: [{
				html: 'Here you can specify a custom output file name for the generated report.'
			},{
				padding: '5px 0 0 40px'
				,items: self.filenameCheckbox
			},{
				padding: '15px 0 0 40px'
				,items: [{
					layout: 'form'
					,items: self.filenameInput
				}]
			}]
		});

		self.reportTitleArea =  new Ext.form.FieldSet({
			title: 'Report Title'
			,autoWidth: true
			,items: [{
				html: 'Here you can specify a custom title for the front page of the report.'
			},{
				padding: '5px 0 0 40px'
				,items: self.reportTitleCheckbox
			},{
				padding: '15px 0 0 40px'
				,items: [{
					layout: 'form'
					,items: self.reportTitleTextfield
				}]
			}]
		});

		self.reportParamsCheckbox = new Ext.form.Checkbox({
			boxLabel: 'Show Report Options and Generation Parameters'
			,checked: false
			,listeners: {
				check: self.handleCheckbox.createDelegate(this)
			}
		});

		self.handleDetailsRadio = function( checkbox, checked ) {
			if ( checked ) {
				if ( checkbox === self.radioAliasText ) {
					self.aliasField.setDisabled( false );
					self.aliasField.validate();
				} else {
					self.aliasField.setDisabled( true );
					self.aliasField.clearInvalid();
				}
			}
		}

		self.radioFullText = new Ext.form.Radio({
			boxLabel: self.publishDetailsString
			,name: 'details'
			,checked: false
			,value: 1
			,handler: self.handleDetailsRadio.createDelegate( this )
		});

		self.radioAliasText = new Ext.form.Radio({
			boxLabel: self.publishAliasString
			,name: 'details'
			,checked: false
			,value: 2
			,handler: self.handleDetailsRadio.createDelegate( this )
		});

		self.radioNoText = new Ext.form.Radio({
			boxLabel: self.noPublishString
			,name: 'details'
			,checked: true
			,value: 3
			,handler: self.handleDetailsRadio.createDelegate( this )
		});

		self.aliasField = new Ext.form.TextField({
			allowBlank: false
			,name: 'group_alias'
			,vtype: 'alphanum'
			,disabled: true
			,width: 180
		});

		var hSpace = { xtype: 'spacer', width: 10 };
		var vSpace = { xtype: 'spacer', height: 10 };

		self.publishDetailsForm =  new Ext.form.FieldSet({
			layout: 'auto'
			,title: self.customizeDisplayTitle
			,height: 'auto'
			,items: [
				new sfw.Panel({
					layout: 'auto'
					,cls: 'ContentPadding'
					,items: [
						self.radioFullText
						,new sfw.Panel({
							layout: 'table'
							,layoutConfig: {
								columns: 3
							}
							,items: [
								self.radioAliasText
								,hSpace
								,self.aliasField
							]
						})
						,self.radioNoText
					]
				})
			]
		});

		var items = [ self.reportParamsCheckbox ];

		if ( skipModules.indexOf( 'publishDetailsForm' ) === -1 ) {
			items.push( vSpace );
			items.push( self.publishDetailsForm );
		}

		self.reportParamsFieldSet =  new Ext.form.FieldSet({
			title: 'Publish Report Parameters'
			,autoWidth: true
			,items: [{
				html: 'Here you can choose whether the report parameters (configured here) should be included in the report for reference.'
			},{
				padding: '5px 100px 0px 40px'
				,items: items
			}]
		});

 		self.interface = new sfw.Panel({
			layout: 'auto'
 			,title: 'General Configuration Options'
			,collapsible: true
 			,items: [ self.fileNameArea, self.reportTitleArea, self.reportParamsFieldSet ]
			,frame: true
		});

	} catch ( ex ) {
		sfw.debug.trigger( ex, "Miscellaneous UI Reporting Element" );
	}

	return this;
}
