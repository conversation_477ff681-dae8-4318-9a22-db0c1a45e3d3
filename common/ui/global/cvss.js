/**
 * @file cvss.js
 */

sfw.cvss = {};

sfw.cvss.create = function( config ) {

	if ( !config) {
		return false;
	}

	if ( !config.vector || !config.actionProviderUrl || !config.actions ) {
		return false;
	}

	// Setting the variables from the config
	var vector = config.vector;
	this.actionProviderUrl = config.actionProviderUrl;
	this.actions = config.actions;

	// Calculation result object.
	this.result = {};
	this.result.score = {}; // CVSS Base Score
	this.result.other = {};
	this.result.environmental = {};
	this.result.temporal = {};

	// Vectors that are saved against the vuln id, on user request
	this.environmental_metrics_vector = "";
	this.temporal_metrics_vector = "";

	// We need to get just the base metrics out from the original vector - it is not enough to just
	// take the brackets off what we pass in.  For the secunia cvss scores, temporal information is
	// being passed in already, so we cannot just append more temporal info or it gets ignored.
	//
	// We must remove any temporal or environmental metrics from the original vector.
	this.getBaseVector = function ( vector ) {
		var baseVector = "";
		var baseRegExp = /(\(AV:.\/AC:.\/Au:.\/C:.\/I:.\/A:.)(.*)/;

		var matchArray = vector.match( baseRegExp );
		// The 1st element is the part we want (base metrics including 1st bracket).

		if ( matchArray && matchArray[1] ) {
			baseVector = matchArray[1];
		}

		return baseVector;
	}

	// Extract the base vector fron the passed in vector for later use.  This already includes the
	// first bracket.  We later append the temporal and environmental metrics when we want to
	// manually change them.
	this.baseVector = this.getBaseVector( vector );

	this.change = function( combo, calculate ) {
		switch ( combo.type ) {
			case "CDP":
				this.result.environmental.collateral = combo.getValue();
				break;
			case "TD":
				this.result.environmental.distribution = combo.getValue();
				break;
			case "CR":
				this.result.environmental.confidentiality = combo.getValue();
				break;
			case "IR":
				this.result.environmental.integrity = combo.getValue();
				break;
			case "AR":
				this.result.environmental.availability = combo.getValue();
				break;
			case "E":
				this.result.temporal.exploitability = combo.getValue();
				break;
			case "RL":
				this.result.temporal.remediation = combo.getValue();
				break;
			case "RC":
				this.result.temporal.confidence = combo.getValue();
				break;
		}
		if ( calculate !== false ) {
			this.calculate();
		}
	}

	this.makeCombo = function( value, type, target ) {
		var data = {};
		data.CDP = [ [ "ND", "Not Defined" ]
				,[ "N", "None" ]
				,[ "L", "Low" ]
				,[ "LM", "Low-Medium" ]
				,[ "MH", "Medium-High" ]
				,[ "H", "High" ]
		];

		data.TD = [
			[ "ND", "Not Defined" ]
			,[ "N", "None" ]
			,[ "L", "Low" ]
			,[ "M", "Medium" ]
			,[ "H", "High" ]
		];

		data.CR = [
			[ "ND", "Not Defined" ]
			,[ "L", "Low" ]
			,[ "M", "Medium" ]
			,[ "H", "High" ]
		];
		data.IR = data.CR;
		data.AR = data.IR;
		data.E = [
			[ "ND", "Not Defined" ]
			,[ "U", "Unproven" ]
			,[ "POC", "Proof of concept" ]
			,[ "F", "Functional" ]
			,[ "H", "High" ]
		];

		data.RL = [
			[ "ND", "Not Defined" ]
			,[ "OF", "Official-fix" ]
			,[ "TF", "Temporary-fix" ]
			,[ "W", "Workaround" ]
			,[ "U", "Unavailable" ]
		];

		data.RC = [
			[ "ND", "Not Defined" ]
			,[ "UC", "Unconfirmed" ]
			,[ "UR", "Uncorroborated" ]
			,[ "C", "Confirmed" ]
		];

		var combo = new sfw.ComboBox({
			renderTo: target
			,name: 'dd_' + type
			,allowBlank: false
			,store: new Ext.data.SimpleStore({
				fields: [ 'id', 'text' ]
				,data: data[type]
			})
			,emptyText: 'Select value...'
			,valueField: 'id'
			,hiddenName: 'dd_' + type
			,mode: 'local'
			,type: type
			,typeAhead: false
			,displayField: 'text'
			,value: value
			,editable: false
			,listeners: {
				collapse: this.change.createDelegate( this )
			}
		});

		this.change( combo, false );
	}

	this.populate = function( result ) {
		this.result.score.baseScore = result.BaseScore;
		this.result.score.impactScore = Math.round( result.ImpactSubscore * 10 ) / 10;
		this.result.score.exploitabilityScore = Math.round( result.ExploitabilitySubscore * 10 ) / 10;
		this.result.score.temporalScore = result.TemporalScore ? result.TemporalScore : "Undefined";
		this.result.score.environmentalScore = result.EnvScore ? result.EnvScore : "Undefined";
		this.result.score.modifiedScore = result.ModImpactSubscore ? Math.round( result.ModImpactSubscore * 10 ) / 10 : "Undefined";
		this.result.score.overallScore = result.OverallScore;
		this.result.other = result.other;

		this.makeBaseScore();
		this.makeBaseScoreMetrics();
		this.makeEnvironmentalScoreMetrics();
		this.makeTemporalScoreMetrics();
	}

	this.calculate = function( vector ) {
		// Use wait cursor
		document.body.style.cursor = 'wait';

		// Build vector
		if ( typeof vector == "undefined" ) {

			// recall we extracted the base vector, including 1st bracket, at the top
			vector = this.baseVector;
			vector += "/CDP:" + this.result.environmental.collateral;
			vector += "/TD:" + this.result.environmental.distribution;
			vector += "/CR:" + this.result.environmental.confidentiality;
			vector += "/IR:" + this.result.environmental.integrity;
			vector += "/AR:" + this.result.environmental.availability;
			vector += "/E:" + this.result.temporal.exploitability;
			vector += "/RL:" + this.result.temporal.remediation;
			vector += "/RC:" + this.result.temporal.confidence;
			vector += ")";

			this.environmental_metrics_vector = "CDP:" + this.result.environmental.collateral
				+ "/TD:" + this.result.environmental.distribution
				+ "/CR:" + this.result.environmental.confidentiality
				+ "/IR:" + this.result.environmental.integrity
				+ "/AR:" + this.result.environmental.availability;
			this.temporal_metrics_vector = "E:" + this.result.temporal.exploitability
				+ "/RL:" + this.result.temporal.remediation
				+ "/RC:" + this.result.temporal.confidence;

		} else {
			// Extract the environmental metrics vector and temporal metrics vector from the full vector

			var envVectorRegExp = /.*(CDP:.*\/TD:.*\/CR:.*\/IR:.*\/AR:[A-Z]*).*\)/;
			var envVectorMatches = vector.match( envVectorRegExp );
			try {
				this.environmental_metrics_vector = envVectorMatches[1];
			} catch ( ex ) {
				this.environmental_metrics_vector = "";
			}

			var temVectorRegExp = /.*(E:.*\/RL:.*\/RC:[A-Z]*).*\)/;
			var temVectorMatches = vector.match( temVectorRegExp );
			try {
				this.temporal_metrics_vector = temVectorMatches[1];
			} catch ( ex ) {
				this.temporal_metrics_vector = "";
			}
		}

		// Ajax request, let the server do the math.
		var me = this;
		Ext.Ajax.request({
			url: sfw.util.constructApiRequest( this.actionProviderUrl + this.actions.calculate )
			,params: {
				vector: vector
			}
			,success: function( data ) {
				// Restore cursor
				document.body.style.cursor = 'default';
				var response = {};
				try {
					response = Ext.util.JSON.decode( data.responseText );
				} catch ( ex ) {
					// Silently ignore the error
				}
				me.populate( response.data );
			}
			,failure: function() {
				// Restore cursor
				document.body.style.cursor = 'default';
				Ext.Msg.alert( "Error", "Cannot fetch results" );
			}
		});
	}

	// In order to get our CLS displaying right for the data table, we load it this way
	this.updateWindow = function( dataWindow, newHtml ) {

		var newItem = {};

		newItem.cls = 'ContentPadding';
		newItem.border = false;
		newItem.html = newHtml;
		dataWindow.removeAll();
		dataWindow.add( newItem );
		dataWindow.doLayout();
	}


	// ENHANCEMENT - find way to make tables look good accross browsers - the top padding isn't being respected, and the tables look ok but crowded in Firefox
	// and all smushed up in IE

	this.makeBaseScore = function() {
		var html = "";

		html = '<table width="100%" cellpadding="2">' +
			'<tr><td style="width: 200px;"><b>CVSS Base Score</b></td><td>' + this.result.score.baseScore + '</td>' +
			'</tr><tr><td style="padding-left: 5px;">Impact subscore</td><td>' + this.result.score.impactScore + '</td></tr>\
			<tr><td style="padding-left: 5px;">Exploitability subscore</td><td>' + this.result.score.exploitabilityScore + '</td></tr>\
			<tr><td><b>CVSS Temporal Score</b></td><td>' + this.result.score.temporalScore + '</td></tr>\
			<tr><td><b>CVSS Environmental Score</b></td><td>' + this.result.score.environmentalScore + '</td></tr>\
			<tr><td style="padding-left: 5px;">Modified Impact subscore</td><td>' + this.result.score.modifiedScore + '</td></tr>\
			<tr><td><b>Overall CVSS Score</b></td><td>' + this.result.score.overallScore + '</td></tr>\
			</table>';

		this.updateWindow( this.baseScore, html );
	}

	this.makeBaseScoreMetrics = function() {
		var html = "";

		html = '<table width="100%" cellpadding="2">' +
			'<tr>' +
			'<td colspan="2">These metrics describe inherent characteristics of the vulnerability. ' +
			'These scores have already been calculated for this vulnerability and cannot be modified.</td>' +
			// leave space
			'</tr><tr style="padding-top: 37px;">' +
			'<td><b>Exploitability Metrics</b></td>' +
			'</tr><tr style="padding-top: 7px;" valign="top">' +
			'<td>Related exploit range<br/>(AccessVector)</td><td>' + this.result.other.AV.value_description + '</td>' +
			'</tr><tr style="padding-top: 7px;" valign="top"><td>Attack complexity<br/>(AccessComplexity)</td><td>' + this.result.other.AC.value_description + '</td>' +
			'</tr><tr style="padding-top: 7px;" valign="top"><td>Level of authentication needed<br/>(Authentication)</td>' +
			'<td>' + this.result.other.Au.value_description + '</td>' +
			// leave space
			'</tr><tr style="padding-top: 17px;"><td><b>Impact Metrics</b></td>' +
			'</tr><tr style="padding-top: 7px;" valign="top"><td>Confidentiality impact<br/>(ConfImpact)</td><td>' + this.result.other.C.value_description + '</td>' +
			'</tr><tr style="padding-top: 7px;" valign="top"><td>Integrity impact<br/>(IntegImpact)</td><td>' + this.result.other.I.value_description + '</td>' +
			'</tr><tr style="padding-top: 7px;" valign="top"><td>Availability impact<br/>(AvailImpact)</td><td>' + this.result.other.A.value_description + '</td>' +
			'</tr></table>';

		this.updateWindow( this.baseScoreMetrics, html );
	}

	this.makeEnvironmentalScoreMetrics = function() {
		var html = "";
		var id = Ext.id();

		html = '<table width="100%" cellpadding="2">\
			<tr><td colspan="2">This section addresses metrics that describe the effect of a vulnerability within an organization\'s environment. These metrics must be calculated separately for each organization.</td></tr>\
			<tr style="padding-top: 20px;"><td><b>General Modifiers Metrics</b></td><td></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>Organization specific potential for loss<br/>(CollateralDamagePotential)</td><td id="' + id + '_CDP"></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>Percentage of vulnerable systems<br/>(TargetDistribution)</td><td id="' + id + '_TD"></td></tr>\
			<tr style="padding-top: 20px;"><td><b>Impact Subscore Modifiers</b></td><td></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>System confidentiality requirement<br/>(ConfidentialityRequirement)</td><td id="' + id + '_CR"></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>System integrity requirement<br/>(IntegrityRequirement)</td><td id="' + id + '_IR"></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>System availability requirement<br/>(AvailabilityRequirement)</td><td id="' + id + '_AR"></td></tr>\
			</table>';

		this.updateWindow( this.environmentalScoreMetrics, html );

		this.makeCombo( this.result.other.CDP.value , 'CDP', id + "_CDP" );
		this.makeCombo( this.result.other.TD.value , 'TD', id + "_TD" );
		this.makeCombo( this.result.other.CR.value , 'CR', id + "_CR" );
		this.makeCombo( this.result.other.IR.value , 'IR', id + "_IR" );
		this.makeCombo( this.result.other.AR.value , 'AR', id + "_AR" );
	}

	this.makeTemporalScoreMetrics = function() {
		var html = "";
		var id = Ext.id();

		html = '<table width="100%" cellpadding="2">\
			<tr><td colspan="2">These metrics describe elements about the vulnerability that change over time. If all of these values are left as \'Undefined\', the environmental score will be based on the base score.</td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>Availability of exploit<br/>(Exploitability)</td><td id="' + id + '_E"></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>Type of fix available<br/>(RemediationLevel)</td><td id="' + id + '_RL"></td></tr>\
			<tr style="padding-top: 10px;" valign="top"><td>Level of verification that vulnerability exists<br/>(ReportConfidence)</td><td id="' + id + '_RC"></td></tr>\
			</table>';

		this.updateWindow( this.temporalScoreMetrics, html );

		this.makeCombo( this.result.other.E.value , 'E', id + "_E" );
		this.makeCombo( this.result.other.RL.value , 'RL', id + "_RL" );
		this.makeCombo( this.result.other.RC.value , 'RC', id + "_RC" );
	}

	this.baseScore = new sfw.Panel({
		title: 'CVSS Base Score'
		,border: true
		,height: 195
		,html: ""
	});

	this.environmentalScoreMetrics = new sfw.Panel({
		title: 'Environmental Score Metrics'
		,border: true
		,height: 405
		,html: ""
	});

	this.baseScoreMetrics = new sfw.Panel({
		title: 'Base Score Metrics'
		,border: true
		,height: 365
		,html: ""
	});

	this.temporalScoreMetrics = new sfw.Panel({
		title: 'Temporal Score Metrics'
		,border: true
		,height: 235
		,html: ""
	});

	this.introText = new sfw.Panel({
		region: 'north'
		,border: false
		,height: 50
		,items: [{
			cls: 'ContentPadding'
			,border: false
			,html: "NOTE: The text on this page is written by NIST and reflects neither the opinions of Flexera or the results of our research.<br>" +
				"All data on this page is written and maintained by <a href='http://nvd.nist.gov/' target='_blank'>NIST</a>."
		}]
	});

	this.interface = new sfw.Panel({
		layout: 'border'
		,items: [
			this.introText
			, new sfw.Panel({
				region: 'center'
				,layout: 'column'
				,height: 900
				,autoScroll: true
				,layoutConfig: {
					columns: 2
				}
				,items: [{
					columnWidth: .5
					,frame: false
					,border: false
					,bodyStyle: 'padding: 5px 3px 5px 5px'
					,items: [
						this.baseScore
						,{// this is just a spacer
							cls: 'ContentPadding'
							,border: false
							,html: " "
						}
						,this.environmentalScoreMetrics
					]
				},{
					columnWidth: .5
					,frame: false
					,border: false
					,bodyStyle: 'padding: 5px 5px 5px 3px'
					,items: [
						this.baseScoreMetrics
						,{// this is just a spacer
							cls: 'ContentPadding'
							,border: false
							,html: " "
						}
						,this.temporalScoreMetrics
					]
				}]
			})
		]
	});

	this.calculate( vector );

	return this;
}


sfw.cvss.view = function() {

	/*
	 * Stub function
	 *
	 * Needs to be implemented by the local project
	 */

	sfw.debug.log( 'sfw.cvss.view() is not implemented' );
}