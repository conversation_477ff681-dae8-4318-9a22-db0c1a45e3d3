/**
 * @file util.js
 * Provides basic JavaScript/UI functionality
 */
sfw.util = {
	/**
	 * @property {String}
	 * @public
	 * Stores the ID of the Page that is currently open and displayed to the user.
	 * This property is set in {@link sfw.util#switchPage}
	 */
	activePage: ""
};

/**
 * Call the create() or init() of each object beying loaded in the page array
 * @param rawPageArray
 *	Array of UI pages to be initialized
 * @return pageArray return the initialized items
*/
sfw.util.buildPageArray = function( rawPageArray ) {
	try {
		var key = '';
		if ( typeof sfw.configuration.splashScreen !== "undefined" ) {
			sfw.configuration.splashScreen.init( rawPageArray );
		}
		var pageArray = [];
		for ( key in rawPageArray ) {
			try {
				var ok = true;
				if ( typeof rawPageArray[key].modules !== "undefined" ) {
					if ( sfw.util.checkModules( rawPageArray[key].modules ) === false ) {
						ok = false;
					}
				}

				// From more complex user permissions and roles
				// use the isVisible function
				if ( ok && typeof rawPageArray[key].isVisible === "function" ) {
					ok = rawPageArray[key].isVisible();
				}
				if ( ok ) {
					var start = new Date();
					if ( typeof rawPageArray[key].create !== "undefined" ) {
						pageArray.push( rawPageArray[key].create() );
					} else if ( typeof rawPageArray[key].init !== "undefined" ) {
						rawPageArray[key].init();
					}
					var finish = new Date();
					var executionTime = finish.getTime() - start.getTime();
					if ( executionTime > 100 ) {
						var message = 'Slow initialize in sfw.util.buildPageArray for ' + executionTime / 1000 + 's : ' + key;
						sfw.debug.log( message );
					}
				}
				if ( typeof sfw.configuration.splashScreen !== "undefined" ) {
					sfw.configuration.splashScreen.update( key );
				}
			} catch( ex ) {
				sfw.debug.log( 'Error while createing page, id: ' + key );
			}
		}
		if ( typeof sfw.configuration.splashScreen !== "undefined" ) {
			sfw.configuration.splashScreen.after();
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.buildPageArray", key );
	}

	return pageArray;
};

/**
 * Construct menu items
 * <ul>
 * <li>Prior to CSI7, we didn't build the menuItems that we didn't have permissions for.</li>
 * <li>In CSI7 we change this so all menuItems are built and the ones we lack permissions
 * for are hidden. We are then able to alter the menu afterwards</li>
 * </ul>
 * @param {Object} rawPages
 *	Array of UI pages to be loaded. The function will test for 'object.menu' and 'object.parent'
 * @return {Array} menuArray array of tree nodes
 */
sfw.util.buildMenuArray = function( rawPages ) {
	var ok = true; // If true, menuItem is included in the menu
	var key = '';
	var hidden = false; // If true, the menuItem is hidden visually from the user
	var menuArray = [];
	for ( key in rawPages ) {
		if ( typeof rawPages[key].parent === "undefined" && typeof rawPages[key].menu !== "undefined" ) {
			ok = true;
			hidden = false; // by default all menu items are visible

			// Check if we have the Module permissions for the menu item
			if ( typeof rawPages[key].modules !== "undefined" ) {
				if ( sfw.util.checkModules( rawPages[key].modules ) !== true ) {
					if ( "undefined" === typeof globals.CSIVersion ) {
						ok = false; // VIM & CSI 6
					} else {
						hidden = true; // CSI 7+
					}
				}
			}

			// Hide menu sections that are disallowed by Role restrictions
			if ( "undefined" !== typeof LoginDetails ) { // CSI only
				hidden = ! LoginDetails.account.hasRolesForPage( key ); // The ! is intentional
			}

			if ( ok ) {
				var menuItem = rawPages[key].menu;
				menuItem.hidden = hidden; // set the menu item's hidden state
				var tempChildren = sfw.util.buildChildren( rawPages, key );
				if ( tempChildren.length !== 0 ) {
					menuItem.leaf = false;
					menuItem.children = tempChildren;
				} else {
					menuItem.leaf = true;
				}
				menuItem.id = key;
				menuArray.push( menuItem );
			}
		}
	}

	/// Fire an event to notify listeners that the menu was been built or rebuilt
	/// This is called in the tail end of {@link sfw.util#buildMenuArray} because buildMenuArray() is
	///	called by both {@link sfw.ui#create} and {@link sfw.csiSmartGroupsConfigured#reloadPages} and in CSI 6's Shadow User login.
	// TODO - see comment in ui.js - how to streamline now that shadows aren't relevant?
	sfw.events.fireEvent( "afterbuildmenuarray", menuArray );
	return menuArray;
};

sfw.util.buildChildren = function( rawPages, parentName ) {
	var childArray = [], childrenCount = 0, defaultDescriptionText = 'No Description Available.', key;
	for ( key in rawPages ) {
		if ( rawPages[key].parent != parentName ) {
			if (! sfw.isSccmPlugin && key == 'sfw.csiDashboard') {
				rawPages[key].menu.descriptionText = rawPages[key].description;
			}
			continue;
		}

		var ok = true;
		if ( typeof rawPages[key].modules !== "undefined" ) {
			if ( sfw.util.checkModules( rawPages[key].modules ) === false ) {
				ok = false;
			}
		}
		if ( ok && typeof rawPages[key].isVisible === "function" ) {
			ok = rawPages[key].isVisible();
		}
		if ( ok ) {
			if ( rawPages[key] ) {
				var menuItem = rawPages[key].menu;
				if ( rawPages[parentName] ) {
					// This test above fixes an exception we hit when in the csiConfiguredSmartGroups dynamically created menu item - todo - is this sufficient / optimal, or does it make more sense to create that menu item in a way that matches our other menu items so we don't need this check? I suspect this is sufficient...
					menuItem.hidden = rawPages[parentName].menu.hidden; // inherit the parent's hidden state
					if ( typeof menuItem !== "undefined" ) {
						var tempChildren = sfw.util.buildChildren( rawPages, key );
						if ( tempChildren.length !== 0 ) {
							menuItem.leaf = false;
							menuItem.children = tempChildren;
						} else {
							menuItem.leaf = true;
							if ( sfw.configuration.ui.displayDescriptionOnHover ) {
								if ( typeof rawPages[key].description !== 'undefined' ) {
									menuItem.descriptionText = rawPages[key].description;
								} else if ( typeof rawPages[key].getDescription === 'function' ) {
									menuItem.descriptionText = rawPages[key].getDescription().html;
								} else {
									menuItem.descriptionText = defaultDescriptionText;
								}
							}
						}
						// Temporarily removing the icons
 						if ( !menuItem.cls ) {
							menuItem.cls = 'x-tree-noicon';
						}
						menuItem.id = rawPages[key].id;
						childArray.push( menuItem );
						childrenCount++;
					}
				}
			}
		}
	}

	return childArray;
};

/**
* Adds tip to the node
*/
sfw.util.addTipsToMenu = function( node, title ) {
	if ( node.isLeaf() ) {
		if ( typeof title === 'undefined' ) {
			title = node.ui.node.attributes.text;
		}
		node.setTooltip( node.ui.node.attributes.descriptionText, title );
	}
};

/**
* Node is the id of the node we are replacing
* Children are the Treenode of subnodes we are adding
* Root is root element of Treepanel that is used for replacing node. Usually sfw.ui.menu.root
*/
sfw.util.replaceMenuNode = function ( nodeId, children, root, skipLastSpacerNode ) {
	if ( children.length ) {
		var node = root.findChild( 'id', nodeId, true ), i, count;
		var parentNode = node.parentNode;

		var isExpanded = false;
		if ( node.isExpanded() ) {
			isExpanded = true;
		}

		for ( i = 0, count = children.length; i < count; i++ ) {
			children[i].leaf = true;
			children[i].cls = 'x-tree-noicon';
			children[i].text = children[i].menu.text;
		}

		children = sfw.util.buildChildren( sfw.ui.pages, nodeId );

		var nodeFolder = new Ext.tree.AsyncTreeNode({
			expanded: isExpanded
			,leaf: false
			,id: nodeId
			,text: node.attributes.text
			,singleClickExpand: true
			,children: children
			,cls: 'x-tree-noicon'
		});

		node.remove(); // remove node folder
		if ( parentNode.lastChild.text === '' ) {
			parentNode.lastChild.remove(); // remove spacer node
		}

		var appendedNode = parentNode.appendChild( nodeFolder ); // add changed nodes
		if ( appendedNode.isExpandable() && !appendedNode.isExpanded() && isExpanded ) {
			appendedNode.expand();
		}

		if ( sfw.configuration.ui.displayDescriptionOnHover ) {
			appendedNode.cascade( this.addTipsToMenu );
		}

		if ( typeof skipLastSpacerNode !== 'undefined' && skipLastSpacerNode !== true ) {
			parentNode.appendChild({
				text: ''
				,disabled: true
				,cls: 'x-tree-noicon emptyNode'
				,leaf: true
			}); // add spacer node
		}
	}
};

sfw.util.expandAll = function( node ) {
	try {
		if ( node.parentNode !== null ) {
			sfw.util.expandAll( node.parentNode );
		}
		node.expand( false, false );
	} catch ( ex ) {
		// Ignore anything wrong
	}
};

/**
 * Select a node corresponding to a page, in the UI menu tree
 * @param pageId
 *	String node/page id
*/
sfw.util.selectNode = function( pageId ) {
	try {
 		var node = sfw.ui.menu.getNodeById( pageId );
		if ( typeof node !== "undefined" ) {
			sfw.util.expandAll( node );
			node.select();
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.selectNode" );
	}
};

sfw.centerAfterLayout = function() {
	sfw.isReady = true;
	sfw.util.refreshActive( sfw.currentPage );
	sfw.ui.center.removeListener( "afterlayout", sfw.centerAfterLayout );
};

sfw.parentAfterLayout = function() {
	sfw.util.refreshActive( sfw.currentPage );
};

/**
 * Function for jumping to a selected 'Page'
 * @param page
 *	String page name
*/
sfw.util.jumpTo = function( page ) {
	sfw.util.switchPage( sfw.ui.center, sfw.ui.pages[page] );
	sfw.util.selectNode( page );
};

sfw.util.switchActiveWindows = function( action, activePageId ) {

	var showWindows = function( page ) {
		var windows = page.hiddenWindows;
		if ( typeof windows !== 'object' ) {
			return;
		}
		for( var i = 0, size = windows.length; i < size; i++ ) {
			var win = windows.pop();
			win.show();
		}
	};

	var hideWindows = function( page ) {
		var windows = [];
		Ext.WindowMgr.each(
			function() {
				if ( this.isVisible() ) {
					windows.push( this );
					this.hide();
				}
			}
		);
		page.hiddenWindows = windows;
	};

	var activePage = sfw.ui.pages[ activePageId ];
	if ( typeof activePage === 'undefined' ) {
		return;
	}
	switch( action ) {
	case 'hide':
		hideWindows( activePage );
		break;
	case 'show':
		showWindows( activePage );
		break;
	}
};

/**
 * @function
 * Switch 'pages' in the main UI page container ( has to be type = 'vbox' ).
 * If the navigation object has been loaded, call the navigation.trace( objectId ) function. See global/navigation.js
 * @param {Ext.Panel} parentPanel
 *	ExtJS Panel main container
 * @param {Object} currentPage
 *	Page to be loaded, it must have an .interface property, ExtJS panel type
 * @return {Boolean}
 *	true if the page was switched otherwise false
 */
sfw.util.switchPage = function( parentPanel, currentPage ) {
	var switched = false;

	try {
		// If the hideWindowsOnSwitchPage flag is turned on, hide all the windows on the current page
		// and show the hidden windows for the next page.
		if ( sfw.configuration.ui.hideWindowsOnSwitchPage && this.activePage ) {
			this.switchActiveWindows( 'hide', this.activePage );
		}

		// If switch is related to the auxiliary viewport, make it and return
		if ( sfw.ui.auxViewport.switchPage
			 && sfw.ui.auxViewport.switchPage( parentPanel, currentPage )
		   ) {
			switched = true;
		}

		if ( !switched && typeof currentPage.interface !== "undefined" ) {
			// CSI specific - Allow the page switching to be interrupted or for the page to be modified before the switch
			if ( "object" === typeof LoginDetails && false === sfw.events.fireEvent( "switchpage", currentPage ) ) {
				return false;
			}

			sfw.currentPage = currentPage;
			if ( sfw.isReady === true ) {
				parentPanel.removeListener("afterlayout", sfw.parentAfterLayout ); // Remove any previous listener for this event
				parentPanel.addListener("afterlayout", sfw.parentAfterLayout );
			} else {
				sfw.ui.center.addListener("afterlayout", sfw.centerAfterLayout );
			}
			this.activePage = currentPage.id;

			// If skipping caching, then hide the active item ( if any ), remove it from panel, add the new one, set it as active and do layout

			if ( sfw.configuration.ui.skipCache === true ) {

				if ( sfw.configuration.ui.tabs === true ) {
					var tab = parentPanel.getItem( currentPage.id );
					if ( !tab ) {
						// Tab is not open yet
						parentPanel.add( currentPage.interface ).show();
					}
				} else {
					parentPanel.add( currentPage.interface );
				}
			}

			if ( typeof parentPanel.setActiveTab !== "undefined" ) {
				parentPanel.setActiveTab( currentPage.id );
			} else {
				if ( sfw.configuration.ui.skipCache === true ) {
					if ( parentPanel.layout.activeItem !== null ) {
						parentPanel.layout.activeItem.hide();
					}
					parentPanel.removeAll( false );
					parentPanel.add( currentPage.interface );
					parentPanel.layout.setActiveItem( 0 );
				}
				parentPanel.layout.setActiveItem( currentPage.interface.id );
			}
			switched = true;
		}

		if ( sfw.configuration.ui.hideWindowsOnSwitchPage ) {
			this.switchActiveWindows( 'show', this.activePage );
		}

		if ( switched ) {
			if ( typeof sfw.printPage !== "undefined" ) {
				if ( currentPage.print === false ) {
					sfw.printPage.printButton.disable();
				} else {
					sfw.printPage.printButton.enable();
				}
			}
			if ( typeof sfw.navigation !== "undefined" ) {
				sfw.navigation.trace( currentPage.id );
			}
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.switchPage" );
	}
	return switched;
};

sfw.util.switchPageDescription = function ( parentPanel, descPage, title ) {
	parentPanel.removeAll( true );
	parentPanel.setTitle( title );
	parentPanel.add( descPage );
	parentPanel.doLayout();
};

/**
 * Function for refreshing the active page
*/
sfw.util.refreshActive = function( page ) {
	try {
		if ( typeof page !== "undefined" && typeof page.refresh !== "undefined" ) {
			page.refresh();
			return;
		}
		if ( typeof this.activePage === "undefined" ) {
			return;
		}
		if ( typeof sfw.ui.pages[this.activePage].refresh !== "undefined" ) {
			sfw.ui.pages[this.activePage].refresh();
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.refreshActive" );
	}
};

/**
 * Basic function for replacing all occurences in a string
 * @param search
 *	String text beying searched for
 * @param replace
 *	String text to replace with
 * @param string
 *	String subject
 * @return string result of the replacement
*/
sfw.util.replaceAll = function( search, replace, string ) {
	try {
		while ( string.indexOf( search ) !== -1 ) {
			string = string.replace( search, replace );
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.replaceAll" );
	}

	return string;
};

/**
 * Basic function for removing an item from an array
 * @param array
 *	Array subject
 * @param position
 *	Integer which position to be deleted
 * @return
 *	Array sibject array having the entry removed
*/
sfw.util.arrayRemove = function( array, position ) {
	try {
		var tempArray = [];
		for ( var i = 0; i < array.length; i++ ) {
			if ( i != position ) {
				tempArray.push( array[i] );
			}
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.arrayRemove" );
	}

	return tempArray;
};

/**
 * Basic function for 'browsing' through an array
 * @param array
 *	Array subject
 * @param position
 *	Array position we are browsing from
 * @param ordered
 *	Array force 'before' array to have same order as original array, see bellow
 * @return
 *	Object consisting of 'before' ( array items, default in reverse order starting for position - 1 ) and 'after' ( array items, starting from position + 1 )
*/
sfw.util.browseArray = function( array, position, order ) {
	try {
		if ( typeof order === "undefined" ) {
			order = false;
		}
		var arrayBefore = [];
		var arrayAfter = [];
		var result = {};
		var i;

		if ( order === false ) {
			for ( i = position - 1; i >= 0; i-- ) {
				arrayBefore.push( array[i] );
			}
		} else {
			for ( i = 0; i <= position - 1; i++ ) {
				arrayBefore.push( array[i] );
			}
		}

		for ( i = position; i < array.length; i++ ) {
			arrayAfter.push( array[i] );
		}

		result = { before: arrayBefore, after: arrayAfter };
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.browseArray" );
	}

	return result;
};

/**
 * Basic function for converting ExtJs form submit error into human readable error
 * @param failureType
 *	String see .submit() for BasicForm
*/
sfw.util.errorMessage = function( action ) {
	var toReturn;
	switch ( action.failureType ) {
		case Ext.form.Action.CLIENT_INVALID:
			toReturn = 'Form fields may not be submitted with invalid values';
			break;
		case Ext.form.Action.CONNECT_FAILURE:
			toReturn = 'Ajax communication failed';
			break;
		case Ext.form.Action.SERVER_INVALID:
			if ( typeof action.result.msg === "undefined" ) {
				toReturn = 'Internal error.';
			} else {
				toReturn = action.result.msg;
			}
			break;
		default:
			toReturn = 'Internal error.';
			break;
	}
	return toReturn;
};

/**
 * Function for constructing an api URL request by appending the user session token AND a request for the API.
 *
 * The user session token is store in a cookie, if the user is logged in.
 *
 * Configuration option:
 *
 * APIPath: path to the api, should leave place for appending &token=TOKEN[&what=...]
 *
 * @param what
 *	String what 'item' in the API is being requested. No leading & !
 * @return
 *	String url to the api. E.g.: api/?token=14134&action=ddgrid
*/
sfw.util.constructApiRequest = function ( what ) {
	try {
		var url = "";
		if ( typeof sfw.configuration.APIPath !== "undefined" ) {
			url = url + sfw.configuration.APIPath + "?";
		}

		var cookieToken = Ext.util.Cookies.get("token");

		if ( cookieToken ) {
			url = url + "token=" + cookieToken;
		}

		if ( typeof what !== "undefined" ) {
			url = url + "&" + what;
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.constructApiRequest" );
	}

	return url;
};

// Create date object by parsing the input string,
// which can be provided in UTC with the boolean parameter utc
// If no input is provided, the current date is returned
sfw.util.dateCreate = function( input, utc ) {
	try {
		var utcMode, seconds;
		var utcMiliseconds = 0;
		var oDate = new Date( 0 );
		var defObj = new Object();
		defObj.reDate = /([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/;
		defObj.reDateTime = /([0-9]{4})-([0-9]{1,2})-([0-9]{1,2}) ([0-9]{1,2}):([0-9]{1,2}):?([0-9]{1,2})?/;
		defObj.reTime = /([0-9]{1,2}):([0-9]{1,2}):?([0-9]{1,2})?/;
		defObj.reNumber = /^([0-9]+)$/;

		utcMode = ( typeof utc === 'boolean' ? utc : false );

		if ( typeof input === 'undefined' || input === null ) {
			input = new Date();
		}

		// Convert input to a Date object
		if ( input instanceof Date ) {
			return input;
		} else {
			var str = input.toString();
			var matches = str.match(defObj.reDateTime);
			if ( matches ) {
				seconds = ( typeof matches[6] !== 'undefined' ) ? matches[6] : 0;
				if ( utcMode ) {
					utcMiliseconds = Date.UTC( matches[1], parseInt( matches[2], 10 ) - 1, matches[3], matches[4], matches[5], seconds );
					oDate = new Date( utcMiliseconds );
				} else {
					oDate = new Date( matches[1],parseInt(matches[2],10)-1,matches[3], matches[4],matches[5], seconds );
				}
			} else {
				matches = str.match(defObj.reDate);
				if ( matches ) {
					if ( utcMode ) {
						utcMiliseconds = Date.UTC( matches[1], parseInt( matches[2], 10 )- 1, matches[3] );
						oDate = new Date( utcMiliseconds );
					} else {
						oDate = new Date( matches[1], parseInt( matches[2], 10 ) - 1, matches[3]);
					}
				} else {
					matches = str.match( defObj.reTime );
					if ( matches ) {
						seconds = ( typeof matches[3] !== 'undefined' ) ? matches[3] : 0;
						oDate = new Date();
						if ( utcMode ) {
							oDate.setUTCHours( matches[1] );
							oDate.setUTCMinutes( matches[2] );
							oDate.setUTCSeconds( seconds );
						} else {
							oDate.setHours( matches[1] );
							oDate.setMinutes( matches[2] );
							oDate.setSeconds( seconds );
						}
					} else {
						matches = str.match( defObj.reNumber );
						if ( matches ) {
							oDate = new Date( parseInt( matches[1], 10 ) * 1000 );
						} else {
							throw { number: 1, message: "Invalid Date" };
						}
					}
				}
			}
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.dateCreate" );
	}

	return oDate;
};

// Function for returning a date object, with an optional argument of 'iDays'. Calling 'sfw.util.dateCreateTodayOffset( -1 )' return a date object of 'today - 1 day'
sfw.util.dateCreateTodayOffset = function( iDays ) {
	var oDate = sfw.util.dateCreate();

	if ( typeof iDays !== 'undefined' ) {
		oDate = oDate.add( Date.DAY, iDays );
	}

	return oDate;
};


/**
 * Function for determining difference between dates.
 * Note - it is assumed the 'from' date is BEFORE the 'to' date - otherwise the values requested come back as '0'
 * @param {Date} from
 *	String initial date
 * @param {Date} to
 *	String ending date
 * @param {Number} details
 *	Integer - what details should it return: 1 = days, 2 = days & hours, 3 = days & hours & minutes, 4 = days & hours & minues & seconds. If 0, let the function determine the difference details, based on how great the difference is.  Less than 2 minutes gives "4", 2 minutes to 2 hours gives "3", 2 hours to 2 days gives "2", 2 days to 2 months gives "1", and more than 2 months gives the special case where we only return "X months".
 * @param {Object} options
 *	excludeEmpty : If set to true then empty date segments are excluded from the return string.
 *	               This works best when details is set to 0 so it can figure out what can be displayed.
 *	               If all of the segments are empty then an empty string is returned.
 * @return
 *	Number difference in 'details'.  Note, for details=0 or unsupplied, and for time differences greater than 40 years, the number of months returned could be off by 1 or more since we consider 2 months as 61 days (which is on average very close, but not exact with the calendar year over time).
 *
 * Examples:
 *
 *	@code
 *	// This will return "0 days, 0 hours, 5 minutes"
 *	sfw.util.differenceBetweenDates( fiveMinutesAgo, currentDate, 3 );
 *
 *	// This will return "0 days, 0 hours"
 *	sfw.util.differenceBetweenDates( fiveMinutesAgo, currentDate, 2 );
 *
 *	// This will return "5 minutes" with excludeEmpty = true
 *	sfw.util.differenceBetweenDates( fiveMinutesAgo, currentDate, 3, { excludeEmpty: true } );
 *
 *	// This will return "0 hours" with excludeEmpty = true
 *	sfw.util.differenceBetweenDates( fiveMinutesAgo, currentDate, 2, { excludeEmpty: true } );
 *	@endcode
 */
sfw.util.differenceBetweenDates = function( from, to, details, options ) {
	try {
		var dateFrom = sfw.util.dateCreate( from );
		var dateTo = sfw.util.dateCreate( to );
		var options = options || {};
		var left = "";

		var output;

		// Get difference in seconds
		var difference = Math.round( (dateTo.valueOf() - dateFrom.valueOf() ) / 1000 );

		// The compared dates may not have originated from the system system clock so a discrepancy
		// of a few seconds or minutes is possible and may result in a negative difference
		if ( difference < 0 ) {
			difference = 0;
		}

		if ( !details ) {
			if ( difference < 2*60 ) {
				details = 4; // include from seconds and bigger if under 2 minutes
			} else if ( difference < 2*60*60 ) {
				details = 3; // include from minutes and up if under 2 hours (but > 2 minutes)
			} else if ( difference < 86400 * 2 ) {
				details = 2; // include from hours and up if under 2 days (but > 2 hours)
			} else if ( difference < 86400 * 61 ) {
				details = 1; // only show days if under 61 days months (but > 2 days)
			} else {
				// only show months
				left = Math.round( difference / ( 86400*61 ) );
				output = left + ' month' + ( left != 1 ? 's' : '' );
				return output;
			}
		}

		// What to report
		switch ( details ) {
			case 4: // Include seconds
				left = ( difference % 60 );
				if ( !options.excludeEmpty || left ) {
					output = left + ' second' + ( left !== 1 ? 's' : '' );
				}
			case 3: // Include minutes
				left = Math.floor( ( difference % 3600 ) / 60 );
				if ( !options.excludeEmpty || left ) {
					output = left + ' minute' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
				}
			case 2: // Include hours
				left = Math.floor( ( difference % 86400 ) / 3600 );
				if ( !options.excludeEmpty || left ) {
					output = left + ' hour' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
				}
			case 1: // Include days
				left = Math.floor( difference / 86400 );
				if ( !options.excludeEmpty || left ) {
					output = left + ' day' + ( left !== 1 ? 's' : '' ) + ( output ? ', ' + output : '' );
				}
				if ( options.excludeEmpty | 0 && undefined === output ) {
					output = ''; // if excludeEmpty is enabled and no time segments were included
				}
				break;
			default:
			// Note - there is now a space in: ', ' + output , as we use this for direct display in a few places.
			// This however breaks some places where we split on the array based on ','.  Anywhere we do this,
			// remember to strip the space before splitting!  Also note, must only strip that one space, not
			// all whitespace, so cannot use generic function in this file, stripWhitespace
				break;
		}
	} catch ( ex ) {
		sfw.debug.trigger( ex, "util.differenceBetweenDates" );
	}

	return output;
};
sfw.util.MillisecondsPerHour = 3600000;
sfw.util.MillisecondsPerDay = 86400000;
sfw.util.MillisecondsPerWeek = 604800000;

/**
 * Helper function to strip whitespace from a string - JS lacks a native function like this.
 * (i.e., like PHP's trim function)
 *
*/
sfw.util.stripWhitespace = function( input ) {
	return input.replace( /^\s+|\s+$/g, '' );
};

/**
 * Check if at least one module in an array of potential modules is enabled or not. Requires the common global
 * file modules.js to be loaded.
 * @param modules
 *	Array of modules
 * @return
 *	Boolean true if at least one module is enabled, otherwise false. The UI object will decide how to proceed based
 * on this response.
*/
sfw.util.checkModules = function( modules ) {
	var i;
	if ( typeof modules !== "undefined" )  {
		for ( i = 0; i < modules.length; i++ ) {
			if ( typeof sfw.modules.enabled[modules[i]] !== "undefined" && sfw.modules.enabled[modules[i]].enabled === true ) {
				return true;
			}
		}
	}

	return false;
};

/**
 * Special Container that contains the function argument values. This container should be used
 * by the set/get functions whenever arguments need to be passed to a javascript function dynamically.
 */
sfw.util.specialContainer = [];
sfw.util.setContent = function( input ) {
        var index = this.specialContainer.length;
        this.specialContainer[index] = input;
        return index;
};

sfw.util.getContent = function( index ) {
        return this.specialContainer[index];
};

/*
 * Renderer for a grid element to put its value in the hovertext as well. Generally used for
 * instances where the column may not be wide enough to show the whole thing.
 *
 * @param value
 *	Mixed The value passed in by the grid
 * @return
 *	String HTML formatted string, to be the hovertext of the grid element using this as a renderer.
 *
*/
sfw.util.tipRenderer = function ( value ) {
	// Note - the encoding is intentional here - we must encode the 'value' going into qtip,
	// and not the raw 'value'. This will correctly display html formatted text (as raw text)
	// and is not vulnerable to cross-site-scripting.
	return '<div qtip="' + Ext.util.Format.htmlEncode( value ) + '">' + value + '</div>';
};
// TODO - these hovertexts do not show up in Chrome (possibly others) - look into someday

/**
 *  Sets state provider for stateful components. If sfw.PersistentStateProvider
 *  is available (CSI)
 *  it will be used and otherwise a Ext.state.CookieProvider will be used
 *
 */

sfw.util.setStateProvider = function () {

	var sp;
	if ( Ext.isObject( sfw.PersistentStateProvider ) ) {
		sp = sfw.PersistentStateProvider;
	} else {
		sp = new Ext.state.CookieProvider({
			expires: new Date(new Date().getTime()+(1000*60*60*24*365)) //1 year
		});
	}

	Ext.state.Manager.setProvider( sp );
	return true;
};

sfw.util.isEmptyObject = function( obj ) {
	for (var name in obj) {
		return false;
	}
	return true;
};

/**
 * @function
 * Converts a Javascript Array to a FastArray and then returns the FastArray
 * @public
 * @param {Array} javascriptArray
 * @return c++ IFastArray
 */
sfw.util.arrayToFastArray = function ( javascriptArray ) {
	var fastArray = sfw.external.fWUICreateFastArray(), i, value;
	for ( i = 0; i < javascriptArray.length; i++ ) {
		value = javascriptArray[i];
		switch ( typeof value ) {
		case "object": fastArray.push( sfw.util.objectToFastObject( value ) ); break;
		case "array" : fastArray.push( sfw.util.arrayToFastArray( value ) ); break;
		case "function" : fastArray.push( undefined ); break;
		default: fastArray.push( value ); break;
		}
	}
	return fastArray;
};
/**
 * @function
 * Converts a c++ FastArray to a Javascript Array and then returns the Array
 * @public
 * @param c++ IFastArray fastArray
 * @return {Array}
 */
sfw.util.fastArrayToArray = function ( fastArray ) {
	var javascriptArray = [], i;
	var value;
	for ( i = 0; i < fastArray.length; i++ ) {
		value = fastArray[i];
		switch ( sfw.external.fWUIQueryObjType( value ) ) {
		case "FastObject": javascriptArray.push( sfw.util.fastObjectToObject( value ) ); break;
		case "FastArray": javascriptArray.push( sfw.util.fastArrayToArray( value ) ); break;
		case "Empty": javascriptArray.push( undefined ); break;
		default: javascriptArray.push( value ); break;
		}
	}
	return javascriptArray;
};
/**
 * @function
 * Converts a c++ FastObject to a Javascript Object and then returns the Object
 * @public
 * @param c++ IFastObject fastObject
 * @return {Object}
 */
sfw.util.fastObjectToObject = function ( fastObject ) {
	var javascriptObject = {};
	var value;
	for ( var key in fastObject ) {
		value = fastObject[key];
		switch ( sfw.external.fWUIQueryObjType( value ) ) {
		case "FastObject": javascriptObject[key] = sfw.util.fastObjectToObject( value ); break;
		case "FastArray": javascriptObject[key] = sfw.util.fastArrayToArray( value ); break;
		case "Empty": javascriptObject[key] = undefined; break;
		default: javascriptObject[key] = value; break;
		}
	}
	return javascriptObject;
};
/**
 * @function
 * Converts a Javascript Object to a FastObject and then returns the FastObject
 * @public
 * @param {Object} javascriptObject
 * @return c++ IFastObject
 */
sfw.util.objectToFastObject = function ( javascriptObject ) {
	var fastObject = sfw.external.fWUICreateFastObject(), value;
	for ( var key in javascriptObject ) {
		if ( javascriptObject.hasOwnProperty( key )  ) {
			value = javascriptObject[key];
			switch ( typeof value ) {
			case "object": fastObject.Put( key, sfw.util.objectToFastObject( value ) ); break;
			case "array": fastObject.Put( key, sfw.util.arrayToFastArray( value ) ); break;
			case "function": fastObject.Put( key, undefined ); break;
			default: fastObject.Put( key, value ); break;
			}
		}
	}
	return fastObject;
};

/**
 * @function
 * Converts an IFastArray or IFastObject that is wrapped as an IDispatch object
 * in to a Javascript Object or Array depending on the IDispatch object type.
 * @public
 * This function calls itself recursively to process any nested Objects or
 * IFastArrays that exist inside the iDispatchObject.
 * IDispatch to itself.
 * @param c++ IDispatch iDispatchObject
 * @return Mixed
 *	Array
 */
sfw.util.IDispatchToObjectOrArray = function ( iDispatchObject ) {
	var javascriptObject = {};
	var objectType = "FastObject";
	// Test if it's an Array
	if ( "number" === typeof iDispatchObject.max_length ) {
		objectType = "FastArray";
		javascriptObject = []; // Change type to Array
	} else if ( "object" !== typeof iDispatchObject ) {
		objectType = "other";
	}
	if ( "FastObject" === objectType ) {
		for ( var key in iDispatchObject ) {
			if ( false === iDispatchObject.hasOwnProperty(key) ) {
				continue;
			}
			javascriptObject[key] = objectType === "other" ? iDispatchObject[key] : sfw.util.IDispatchToObjectOrArray( iDispatchObject[key] );
		}
	} else if ( "FastArray" === objectType ) {
		for ( var i = 0; i < iDispatchObject.length; i++ ) {
			javascriptObject.push( objectType === "other" ? iDispatchObject[i] : sfw.util.IDispatchToObjectOrArray( iDispatchObject[i] ) );
		}
	} else {
		// Number, String, Function etc are not processed
		javascriptObject = iDispatchObject;
	}
	return javascriptObject;
};


/*
 * @function
 * Function for calculating the page size.
 * @param {Object} grid.
 * @param {Boolean} Flag for ext 4 grid.
 * @return {Ingeger} page size.
 */
sfw.util.calculatePageSize = function( grid, ext4 ) {
	var height = 400;
	if ( grid.body && grid.body.getHeight() ) {
		height = grid.body.getHeight();
	}
	var rowHeight = 21;
	var contentHeight = height;
	if ( !ext4 ) {
		height -= 35;
	}
	var size = Math.round( contentHeight / rowHeight );
	size = ( size < 1 ) ? 1 : ( size > 100 ? 100 : size );
	return size;
};

/**
 * @function
 * Converts an integer to a colon separated MAC-48 Address
 * @param {Number} macAddressInt
 * @return {String}
 *	Example returned string: "00:50:56:83:00:0D"
 */
sfw.util.intToMacAddress = function ( macAddressInt ) {
	var str = parseInt( macAddressInt ).toString(16); // convert to hex
	str = new Array( 13 - str.length ).join( "0" ) + str; // left pad "0" to 12 chars
	return str.match(/[a-f0-9]{2}/g).join( ":" ); // insert ":"'s and return
};
/**
 * @function
 * Converts an unsigned 32 bit int to an ipv4 address string
 * @param {Number} ipAddressInt unsigned 32 bit int
 * @return {String} IP address
 */
sfw.util.int32ToIpv4 = function ( ipAddressInt ) {
	return String.format(
		"{0}.{1}.{2}.{3}"
		,ipAddressInt >> 24 & 0xff
		,ipAddressInt >> 16 & 0xff
		,ipAddressInt >> 8 & 0xff
		,ipAddressInt & 0xff
	);
};

/**
 * @function
 * Checks if user agent matches a regex.
 * @return {Bool} true if it's IE 11
 */
var checkUserAgent = function( regex ) {
    return regex.test(navigator.userAgent.toLowerCase());
}
/**
 * True if the user is on IE 11
 */
sfw.util.isIE11 = checkUserAgent(/trident/);

/**
 * Sanitizes a filename for use in Windows by removing reserved characters
 * @see https://msdn.microsoft.com/en-us/library/windows/desktop/aa365247%28v=vs.85%29.aspx?f=255&MSPPError=-2147217396
 * @param {String} filename
 * @return {String}
 * @throws {number: 1} Thrown if the sanitized filename is not valid
 */
sfw.util.sanitizeWindowsFilename = function (filename) {
	var santized = filename.replace(/[<>:"/\\|?*]/g, "").trim();
	switch (santized) {
		case "":
		case ".":
		case "..":
		case "...":
		case "CON":
		case "PRN":
		case "AUX":
		case "NUL":
		case "COM1":
		case "COM2":
		case "COM3":
		case "COM4":
		case "COM5":
		case "COM6":
		case "COM7":
		case "COM8":
		case "COM9":
		case "LPT1":
		case "LPT2":
		case "LPT3":
		case "LPT4":
		case "LPT5":
		case "LPT6":
		case "LPT7":
		case "LPT8":
		case "LPT9":
			throw {
				number: 1,
				message: "Invalid Windows Filename - " + Ext.util.Format.htmlEncode(santized)
			};
			break;
		default:
	}
	return santized;
};

// END @file util.js