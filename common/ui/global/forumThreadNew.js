/**
 * @file forumThreadNew.js
 *
 * Generic object for creating a new forum thread
 *
 */
sfw.forumThreadNew = {};

/**
 * Called when creating a new forum thread in the UI
 *
 * @param parent - object calling this
 *
 * @return
 * The form container
 *
*/
sfw.forumThreadNew.create = function( parent ) {
	try {
		var self = this;
		self.parent = parent;
		self.sourceType = parent.sourceType;

		var initInfo = {};

		this.getCurrentSubforum = function() {
			var currentlyViewing = parent.forumTypeCombobox.getValue();
			if ( 0 === currentlyViewing ) {
				// then this is the 'all threads' view - cannot post to this.  Use source type default.
				if ( 'csi' === parent.sourceType ) {
					currentlyViewing = sfw.sharedForum.csiForumType;
				}// todo - add other types
			}
			return currentlyViewing;
		}

		if ( typeof this.forumPost == 'undefined' ) {
			this.forumPost = new sfw.forumPostTemplate.create( self, 'thread' );
		}


		this.initialize = function() {
			initInfo.subForum = this.getCurrentSubforum();
			this.forumPost.initialize( "thread", initInfo );
		}

		this.updateTitle = function ( title ) {
			this.window.setTitle(title);
		}

		// Get dynamic height/width in case of small browser window.
		var dynamicDimensions = sfw.sharedFunctions.getDynamicDimensions( 650, 550, parent.interface );
 		var interfaceHeight = dynamicDimensions[0];
		var interfaceWidth = dynamicDimensions[1];

		this.window = new Ext.Window({
			title: '' // update title dynamically
			,height: interfaceHeight
			,width: interfaceWidth
			,layout: 'fit'
			,items: this.forumPost.interface
			,closeAction: 'hide'
		});

	} catch ( ex ) {
		sfw.debug.trigger ( ex, "forumThreadNew.create" );
	}

	return this;
}
