/**
 * @file fileuploadfield.js
 * Provides the Secunia Resize patch for the UX FileUploadField.
 * NOTE: Requires: ux/FileUploadField.js to be loaded!!
*/
sfw.FileUploadField = function( config ) {
	config.listeners = config.listeners || {};
	config.listeners.resize = config.listeners.resize || function() {
		var buttonWidth = 60;
		var w = this.width - buttonWidth - this.buttonOffset;
		this.el.setWidth(w);
	}

	return new Ext.ux.form.FileUploadField( config );
}