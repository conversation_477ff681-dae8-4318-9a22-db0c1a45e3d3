/**
 * @file forumThreadView.js
 *
 * Generic object for displaying a forum thread - creting new or viewing an existing one.
 *
 */
sfw.forumThreadView = {};

// enhancement: the url's and params are being passed here which are domain specific and shouldn't be mentioned here

// If type is 'quote', an index must be provided.  -1 means use the original thread post, otherwise an integer
// of 0 or greater means use that index from the postData array.  If the type is 'new', we use the original
// threadData to get the id, etc. that we need.
// If type is edit, we need to make sure we distinguish between editing the original thread post, and a
// subsequent post, and track the threadId and possibly post index
sfw.forumThreadView.replyToOrEditPost = function( type, index ) {

	var dataToUse = '';
	if ( ('new' === type) || (-1 === index) ) {
		dataToUse = this.threadData;
	} else {
		dataToUse = this.postData[index];
	}
	var thisUserData = this.getUserData( this.userData, dataToUse['author'] );

	var quoteText = '';
	if ( "quote" === type ) {
		quoteText = dataToUse['content'];
		var quoteType = 't';
		var quoteId = dataToUse['id'];
		var quoteType = 'p';
		if ( -1 === index ) {
			quoteType='t';
		}
		if ( quoteText ) {
			quoteText = '[quote=' + quoteType + quoteId + ']' + quoteText + '[/quote]';
		}
	} else if ( "edit" === type ) {
		quoteText = dataToUse['content'];
	}

	var initInfo = {};

	this.forumPost = new sfw.forumPostTemplate.create( sfw.forumThreadView.owner, 'post', this );
	initInfo.threadId = this.threadData.id;
	initInfo.subForum = this.threadData.reftype;
	initInfo.title = dataToUse.topic;
	initInfo.subscribed = this.currentlySubscribed;
	initInfo.attachmentId = dataToUse.attachment_id;

	// We need to decode the contents which are encoded before being returned from the API via the JSON Object.
	// Further, format the post for raw display by converting html line breaks
	initInfo.postContents = sfw.forumPostTemplate.formatPostRawDisplay( Ext.util.Format.htmlDecode(quoteText) );

	initInfo.type = 'reply'; // If type is 'new' or 'quote'
	if ( 'edit' === type ) {
		if ( -1 === index ) {
			initInfo.type = 'editThread';
		} else {
			initInfo.type = 'editPost';
			initInfo.postId = dataToUse.id;
		}
	}

	this.forumPost.initialize( 'post', initInfo )


	// Create the window and show it
	//------------------------------
	// Get dynamic height/width in case of small browser window.
	var dynamicDimensions = sfw.sharedFunctions.getDynamicDimensions( 650, 550, sfw.forumThreadView.owner.interface );
 	var interfaceHeight = dynamicDimensions[0];
	var interfaceWidth = dynamicDimensions[1];

	this.window = new Ext.Window({
		title: 'Reply to Post'
		,height: interfaceHeight
		,width: interfaceWidth
		,layout: 'fit'
		,items: this.forumPost.interface
		,closeAction: 'hide'
	});

	this.window.show();
}

sfw.forumThreadView.getUserData = function( userData, userId ) {
	var userIdString = 'authorId_' + userId;
	if ( userData && userData[userIdString] ) {
		return userData[userIdString];
	}
	return false;
}


/**
 * Called when showing or creating a forum thread in the UI - this loads the relevant data and options.
 *
 * @return
 * The main form thread container
 *
*/
sfw.forumThreadView.create = function( parent ) {
	try {
		var self = this;
		self.parent = parent;

		self.dataProviderURL = 'action=community&which=';
 		self.getAction = 'getPosts';
 		sfw.forumThreadView.postData = ''; // global so we can use it anywhere based just on the id

		var initInfo = {};

		this.initialize = function( threadData ) {
			// First clear the form so that if it shows before the async data is loaded, we don't
			// see old data from a previous view.  Just need to check that the getContentTarget
			// exists, which it won't the very fist time we load something as window isn't visible yet.
			if ( this.postSummary.getContentTarget() ) {
				this.postSummary.update( "" );
			}

			this.refresh( threadData );
		}

		this.updateTitle = function ( title ) {
			this.window.setTitle(title);
		}

		this.bbar = {};

		this.bbar.closeButton = new Ext.Button({
			text: 'Close'
			,tooltip: 'Close window'
			,handler: function () {
				self.window.hide();
				parent.refresh(); // update the grid in case of changes made here that affect main grid (i.e. views, replies, etc
			}
		});

		this.bbar.replyButton = new Ext.Button({
			text: 'Reply'
			,tooltip: 'Reply to Post.  You must be logged in to perform this action.'
			,disabled: false
			,handler: function () {
				sfw.forumThreadView.replyToOrEditPost( 'new' );
			}
		});


		this.bbar.toolbar = new Ext.Toolbar({
			items: [
				'->'
				,this.bbar.replyButton
				,'-'
				,this.bbar.closeButton
			]
		});


		// -------------------------------------
		// Make/Format the HTML
		// -------------------------------------

		// postData is either the original thread data, or the whole post data arry, in which
		// case we need the index to get the correct post
 		this.createForumPost = function( threadData, postData, index, userData, isThreadResolved, isThreadLocked ) {
			var html = '';
			var postHeaderStyle = 'Normal thread';
			var selectedAnswer = false;
			var textColor = 'color:black;';
			var bgColor = 'whitesmoke';
			var thisPostData;
			var acceptedAnswerPostId = 0;

			// Set up a few things based on if this is the first post i.e. the original thread post
			if ( -1 === index ) {
				thisPostData = threadData;
				textColor = 'color:white;';
				bgColor = '#c51a2c';
				var postHeaderStyle = ' thread';

			} else {
				thisPostData = postData[index];
				acceptedAnswerPostId = threadData.accepted_answer;
				if ( isThreadResolved && thisPostData.id === acceptedAnswerPostId ) {
					postHeaderStyle = 'Selected thread';
					textColor = 'color:white;';
					bgColor = '#c51a2c';
					selectedAnswer = true;
				}
			}

			var thisUserData = sfw.forumThreadView.getUserData( userData, thisPostData.author );

			if ( selectedAnswer ) {
				html += "<br><center><b>The following post has been selected as an answer:</b></center>";
			}

			html += '<table class="forumTable' + postHeaderStyle + '" width="100%" cellpadding="5" cellspacing="2" style="border:1px solid #d1d1d1;">';
			html += '<tbody><tr valign="top">';
			html += '<th style="cursor:default; background: ' + bgColor + '; color:white; font-size:12px; padding: 2px;" width="150">';

			userName = ( thisUserData && thisUserData.userName ? thisUserData.userName : 'N/A' );

			html += '<a style="white-space: nowrap; ' + textColor + '" href="javascript:void(0);" onclick="defaults.externalURL(\'https://secunia.com/community/forum/userthreads/' + thisPostData.author + '/\')">' + userName + '</a>';
			html += '</th>';
			html += '<th style="cursor:default; background: ' + bgColor + '; color:white; font-size:12px; padding: 2px; ' + textColor + '">';
			html += thisPostData.topic;
			html += '</th>';
			html += '</tr><tr>';
			html += '<td valign="top" style="background-color:whitesmoke;  padding: 2px;">';
			html += Ext.util.Format.htmlDecode( thisUserData.userImage );
			html += '</td>';
			html += '<td style="background-color:whitesmoke; padding: 2px;">';
			html += sfw.util.dateCreate( thisPostData.posted ).format( globals.dateLongOutput );
			html += '</td>';
			html += '</tr><tr valign="top">';
			html += '<td>'
			html += 'Ranking: ' + thisUserData.userRank + '<br>';
			html += 'Posts: ' + parseInt(thisUserData.numPosts, 10) + '<br>';
			html += 'User Since: ' + ( thisUserData.joinDate ? sfw.util.dateCreate( thisUserData.joinDate ).format( globals.dateShortOutput ) : 'N/A' ) + '<br>';
			html += 'Location: ' + ( thisUserData.location ? thisUserData.location : 'N/A' ) + '<br>';
			if ( "0000-00-00 00:00:00" !== thisPostData.lastedit ) {
				html += 'Last Edited:<br>' + sfw.util.dateCreate( thisPostData.lastedit ).format( globals.dateLongOutput ) + '<br>';
			}
			html += '<br></td><td>';
			html += sfw.forumPostTemplate.formatPostHtml( threadData, postData, index, userData ); // transform for display

			if ( thisUserData.signature ) {
				html += '<br><br>--<br>' + thisUserData.signature;
			}
			html += '<br></td></tr><br>';

			html += '<tr>';

			// Check for a valid login, else replies and viewing attachments is not allowed.
			if ( sfw.communityForum.validForumLogin ) {
				html += '<td colspan="2" align="right">';

				if ( parseInt( thisPostData.attachment_id ) > 0  ) {
					var action = 'attachment';
					var params = {
						url: globals.apiPath() + '&' + this.dataProviderURL + action // enhancement: domain specific, shouldn't go in global domain
						,attachmentId: thisPostData.attachment_id
					};
					html += ' <a href=# onClick="sfw.forumAttachment.view( sfw.util.getContent( ' + sfw.util.setContent( params ) + ' ))">Attachment</a> ';
				}

				// If thread is locked or resolved, no reply is possible - just don't include buttons
				if ( !isThreadResolved && !isThreadLocked ) {
					var clickFunctionality = 'sfw.forumThreadView.replyToOrEditPost(' + '\'quote\',' + index + ')';
					html += '<input name="btnquote" class="btn" value="Quote" onClick="' + clickFunctionality + '" type="button">';
					if ( thisPostData.canEdit ) {
						clickFunctionality = 'sfw.forumThreadView.replyToOrEditPost(' + '\'edit\',' + index + ')';
						html += '<input name="btnquote" class="btn" value="Edit" onClick="' + clickFunctionality + '" type="button">';
					}
				}

				html += '</td></tr>';
			}

			// Deal with reply / quote buttons later
			html += '</tbody></table>';

			return html;
		}

		this.makeHtml = function( threadData, responseData ) {

			var postData = responseData.data;
			sfw.forumThreadView.postData = postData; // store in our global for future access

			var metaData = responseData.metaData;
			var userData = responseData.userData;
			sfw.forumThreadView.userData = userData; // store in our global for future access

			var threadLocked = ( "locked" === threadData.threadstatus ) ? true : false;
			var threadResolved = ( 0 != threadData.accepted_answer ) ? true : false;

			var html = '';

			if ( !sfw.communityForum.validForumLogin ) {
				html += "<center><b>You are not logged in to the forum, thus replying is not possible.</b></center>";
				this.bbar.replyButton.setDisabled(true);
			}

			// If the thread is locked, print a statement to that effect.
			if ( threadLocked || threadResolved ) {
				html += "<center><b>This thread has been " + ( threadResolved ? "marked as resolved" : "locked" ) + ( sfw.communityForum.validForumLogin ? ", thus replying is not possible." : "." ) + "</b></center>";
				this.bbar.replyButton.setDisabled(true);
			} else {
				if ( sfw.communityForum.validForumLogin ) {
					this.bbar.replyButton.setDisabled(false);
				}
			}

			// First, we print the original post that started the thread - the data for this is in threadData.
 			html += this.createForumPost( threadData, 0, -1, userData, threadResolved, threadLocked );

			// Then we go through each post, format it and add it to the display.
			if ( 0 === postData.length ) {
				html += "<br><center><b>There have been no replies to this thread yet.</b></center>";
			} else {
				for ( var i=0; i < postData.length; i++ ) {
 					html += this.createForumPost( threadData, postData, i, userData, threadResolved, threadLocked );
				}
			}

			return html;
		}

		this.updateSubscribe = function( metaData, forceFlag, showSubscribe ) {

			sfw.forumThreadView.currentlySubscribed = false;
			var buttonText = "Subscribe";
			var introText = 'You are not currently subscribing to this thread.  To recieve emails when there are new replies to this thread, click the "Subscribe" button.';

			if ( (forceFlag && !showSubscribe) || (metaData && metaData.subscribed) ) {
				buttonText = "Unsubscribe";
				introText = 'You currently subscribed to this thread.  To stop recieving emails when there are new replies to this thread, click the "Unsubscribe" button.';
				sfw.forumThreadView.currentlySubscribed = true;
			}

			this.subscribeButton.setText( buttonText );
			this.subscribeFunctionality.update( introText );
			this.subscribeFunctionality.doLayout();
		}

		this.handleSubscribe = function() {

			if ( !sfw.forumThreadView.threadData.id ) {
				return;
			}

			var url = '';
			var action = 'threadSubscribe';
			var forceType = 0;
			if ( sfw.forumThreadView.currentlySubscribed ) {
				action = 'threadUnSubscribe';
				forceType = 1;
			}

			//TODO - get new style working...
			url = globals.apiPath() + '&' + self.dataProviderURL + action;
			var params = {
				threadId: sfw.forumThreadView.threadData.id
			}
			try {
				var mask = new Ext.LoadMask( Ext.getBody(), { msg: "Loading..." });
				mask.show();
				Ext.Ajax.request({
					url: url
					,params: params
					,success: function( data ) {
						var response = {};
						try {
							response = Ext.util.JSON.decode( data.responseText );
							if ( response.success ) {
								self.updateSubscribe( null, true, forceType );
							} else {
								Ext.Msg.alert( 'Error', 'Email address not found.' );
							}
						} catch ( ex ) {
							sfw.debug.trigger( ex, 'handleSubscribe() - ajax request' );
						}

					}
					,failure: function() {
						Ext.Msg.alert( 'Error', 'Unexpected error!' );
					}
				});
				mask.hide();
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
			}
		}


		// -------------------------------------
		// Load the data
		// -------------------------------------

		this.updateAllData = function( threadData ) {

			sfw.forumThreadView.threadData = threadData; // store in global so it is accesible outside of this

			var url = '';
			//TODO - as everywhere else with api call...
			url = globals.apiPath() + '&' + self.dataProviderURL + self.getAction;
			// todo ... do other products, vim, psi, etc. as needed.

			var params = {
				threadId: threadData.id
				,threadAuthorId: threadData.author
			}

			var mask = new Ext.LoadMask( Ext.getBody(), { msg: "Loading..." });
			mask.show();
			Ext.Ajax.request({
				url: url
				,params: params
				,success: function( data ) {
					var response = {};
					try {
						response = Ext.util.JSON.decode( data.responseText );
						self.updateSubscribe( response.metaData, false, false );
						self.postSummary.update( self.makeHtml( threadData, response ) );
						self.interface.doLayout();
					} catch ( ex ) {
						Ext.Msg.alert( "Error", "Cannot fetch post data." );
					}
					mask.hide();
				}
				,failure: function() {
					mask.hide();
					Ext.Msg.alert( "Error", "Cannot fetch post data." );
				}
			});
		}


		this.getFreshDataAndUpdate = function( threadId ) {

			var action = 'getOneThread';
			var url = globals.apiPath() + '&' + self.dataProviderURL + action;
			// todo ... do other products, vim, psi, etc. as needed

			var params = {
				threadId: threadId
			}

			var mask = new Ext.LoadMask( Ext.getBody(), { msg: "Loading..." });
			mask.show();
			Ext.Ajax.request({
				url: url
				,params: params
				,success: function( data ) {
					var response = {};
					try {
						response = Ext.util.JSON.decode( data.responseText );
						var threadData = response.data[0];
						self.updateAllData( threadData );
					} catch ( ex ) {
						Ext.Msg.alert( "Error", "Cannot update view." );
					}
					mask.hide();
				}
				,failure: function() {
					mask.hide();
					Ext.Msg.alert( "Error", "Cannot fetch thread data." );
				}
			});
		}

		this.postSummary = new sfw.Panel({
			html: ""
			,layout: 'auto'
		});


		// forceRefreshOfThread contains a threadId ONLY IF we are calling refresh after creating a new post
		// or editing current thread, and thus need to get all the thread data again fresh from the server.
		// Otherwise, we have the data we need in threadData, and just need to call updateAllData to fetch
		// the posts and display everything properly.
		this.refresh = function( threadData, forceRefreshOfThread ) {

			if ( !forceRefreshOfThread ) {
				// then all the data is already in threadData
				this.updateAllData( threadData );
			} else {
				// then we need to get the data for this thread fresh from the DB
				this.getFreshDataAndUpdate( forceRefreshOfThread );
			}
		}

		this.disclaimer = new sfw.Panel({
			layout: 'auto'
			,items: new sfw.Panel({
				cls: 'ContentPadding'
				,html: "You are currently viewing a forum thread in the Flexera Community Forum. Please note that opinions expressed here are not those of Flexera, and solely reflect those of the user who wrote a given post.<br><br>"
				,border: false
			})
		});

		this.subscribeButton = new Ext.Button({
			text: ''
			,tooltip: 'Manage thread subscription'
			,handler: this.handleSubscribe.createDelegate( this )
		});

		this.subscribeFunctionality = new sfw.Panel({
			html: ''
			,cls: 'ContentPadding'
			,layout: 'auto'
			,buttonAlign: 'left'
			,buttons: [ this.subscribeButton ]
			,hidden: !sfw.communityForum.validForumLogin
		});

		this.interface = new sfw.Panel({
			html: ''
			,layout: 'auto'
			,autoScroll: true
			,items: [
				this.disclaimer
				,this.subscribeFunctionality
				,new sfw.Panel({
					layout: 'auto'
					,padding: '0px 15px 5px 15px'
					,items: [ this.postSummary ]
				})
			]
			,bbar: this.bbar.toolbar
		});

		// Get dynamic height/width in case of small browser window.
		var dynamicDimensions = sfw.sharedFunctions.getDynamicDimensions( 700, 750, parent.interface );
 		var interfaceHeight = dynamicDimensions[0];
		var interfaceWidth = dynamicDimensions[1];

		this.window = new Ext.Window({
			title: '' // update title dynamically
			,height: interfaceHeight
			,width: interfaceWidth
			,layout: 'fit'
			,items: this.interface
			,closeAction: 'hide'
			,listeners: {
				show: function() {
					self.window.center();
				}
			}
		});

	} catch ( ex ) {
		sfw.debug.trigger ( ex, "forumThreadView.create" );
	}

	sfw.forumThreadView.owner = self;
	return this;
}
