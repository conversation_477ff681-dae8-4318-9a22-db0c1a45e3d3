/**
* @method
* creates a new class based on the supplied baseclass
* @param baseclass [String\Object] what to inherit from.
* if a String then it can be namespaced 'sfw.ui.page' would resolve to the sfw.ui.page object
* @param classDefinitionFn [Function] should return an object with the "public" methods and parameters
* @param xtype [String] (optional) the xtype to register the new component type as.
*
*     sfw.Panel = sfw.extend( Ext.Panel, function()  {
*         var some_private_var;
*         return {
*             constructor: function( config ) {
*                 ext.applyIf( config, {
*                     frame: false,
*                     border: false,
*                     layout: 'fit',
*                     tabTip: config.title ? config.title : ''
*                 });
*                 if ( typeof config.region !== "undefined" ) {
*                     switch ( config.region ) {
*                         case "west":
*                             config.collapsible = (typeof config.collapsible !== 'undefined' ? config.collapsible : true);
*                             config.split = (typeof config.split !== 'undefined' ? config.split : true);
*                             config.width = config.width || 250;
*                         break;
*                         case "center":
*                             config.split = (typeof config.split !== 'undefined' ? config.split : true);
*                         break;
*                         case "south":
*                             config.split = (typeof config.split !== 'undefined' ? config.split : true);
*                         break;
*                     }
*                 }
*             }
*         };
*     }, 'sfwpanel');
*
*     // usage
*
*/
sfw.extend = function( superclass, override , xtype ) {
	var newClass, overrideObj;
	if ( Ext.isObject( override ) ) {
		newClass = Ext.extend( superclass, override );
	} else if ( Ext.isFunction( override ) ) {
		newClass = Ext.extend( superclass, override.call() );
	} else {
		throw 'bad class';
	}
	if ( xtype ) {
		Ext.reg( xtype, newClass );
	}
	return newClass;
};
