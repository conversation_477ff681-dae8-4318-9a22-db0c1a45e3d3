/**
 * @file sharedFunctions.js
 * Contains various shared functions used throughout Secunia products
*/
sfw.sharedFunctions = {};

// ENHANCEMENT - convert all the sfw.shared ones in the VIM to this one where applicable


// Level needs to be an int in [1,5], and text is optional text you can pass in for the
// criticality display text
sfw.sharedFunctions.returnCriticalityImage = function( level, text ) {

	var returnVal = '-';

	// We demand that level is between 1 and 5
	level = parseInt( level, 10);
	if ( level < 1 || level > 5 ) {
		return returnVal;
	}

	if ( typeof text == "undefined" ) {
		text = sfw.sharedFunctions.advisoryCriticalityText( level );
	}

	if ( text != "-" ) {
		returnVal = '<img title="' + sfw.sharedFunctions.advisoryCriticalityText( level ) + '" style="padding-top: 4px !important;" src="gfx/advisory/critlow_' + ( 6 - level ) + '.gif" class="critlevimg">';
	} else {
		returnVal = '<i>- Handled -</i>'
	}

	return returnVal;
}

sfw.sharedFunctions.dateRenderer = function( value ) {
	value = value.split( " " );
	return value[0];
}


sfw.sharedFunctions.timeRenderer = function( dtm ) {
	dtm = dtm.split( " " );
	return dtm[1];
}

// Render long date in a grid
sfw.sharedFunctions.longDateRenderer = function( value ) {
	if ( value ) {
		return sfw.util.dateCreate( value ).format( sfw.sharedConstants.dateLongOutput );
	} else {
		return '-';
	}
}

sfw.sharedFunctions.solutionStatus = function( statusId ) {
	statusId = parseInt( statusId );
	switch( statusId ) {
		case 1:
			return 'Unpatched';
			break;
		case 2:
			return 'Vendor Patched';
			break;
		case 3:
			return 'Vendor Workaround';
			break;
		case 4:
			return 'Partial Fix';
			break;
	}

	return "-";
}


sfw.sharedFunctions.unknownVendor = function( value ) {
	if ( value == "" ) {
		return "(unknown)";
	}
	return value;
}

sfw.sharedFunctions.returnStatusColor = function( iType ) {
	switch ( iType ) {
		case 1: // Red
			return '#CC0000';
		case 2: // Yellow
			return '#FFDB00';
		case 3: // Green
			return '#00BF12';
		case 4: // Light Green
			return '#9FFD0B';
		case 5: // Dark Yellow
			return '#D9FC04';
		case 6: // Light Red
			return '#FF9400';
		case 7: // Dark red
			return '#FF7600';
	}

	return '#000000';
}

sfw.sharedFunctions.advisoryCriticalityText = function( criticality ) {
	criticality = parseInt( criticality );
	switch ( criticality ) {
		case 1:
			return 'Extremely critical';
		case 2:
			return 'Highly critical';
		case 3:
			return 'Moderately critical';
		case 4:
			return 'Less critical';
		case 5:
			return 'Not critical';
	}

	return '-';
}

sfw.sharedFunctions.advisoryCriticalityId = function( criticality ) {
	switch ( criticality ) {
		case 'Extremely critical':
			return 5;
		case 'Highly critical':
			return 4;
		case 'Moderately critical':
			return 3;
		case 'Less critical':
			return 2;
		case 'Not critical':
			return 1;
	}

	return '-';
}


/**
 * Provides renderer for notifing the user that this vulnerability has an exploit available.
*/
sfw.sharedFunctions.renderExploitWarning = function( value ) {
	if ( value != 0 ) {
		return '<img title="There ' + ( value == 1 ? "is " : "are " ) + value + ' exploit' + ( value != 1 ? 's' : '' ) + ' available for this Secunia advisory." style="padding-top: 4px !important;" src="gfx/advisory/exploit.png" height="10" width="10">';
	}
	return "-";
}

sfw.sharedFunctions.fetchNewFlagImage = function() {

	var imagePath = 'gfx/volatile/new.gif';
	var formattedPath = "&nbsp" + " <img height='15' style='vertical-align: middle;' src='" + imagePath + "' style='padding-right: 4px;'>";
	return formattedPath;
}

// This is a helper function to append the 'new' image to a piece of text. The endDate, if not supplied, is
// assumed to be Dec. 1, 2012 for now. Or one can input a specific date on which to stop showing the 'new' image.
// This can be modified as needed here, or can take a value from globals, or could take additional config
// params as needed.
// TODO - decide on the default 'new-flag-expiration-date' for CSI-6 items.
sfw.sharedFunctions.addNewFlagToText = function( inputText, endDate ) {

	var result = inputText;
	var today = sfw.util.dateCreate();
	var defaultNewFlagExpirationDate = '2012-12-01'; // TODO
	if ( !endDate ) {
		endDate = defaultNewFlagExpirationDate;
	}

	var difference = sfw.util.differenceBetweenDates( today, endDate, 1 );
	var parsedDifference = difference.split(' ');
	var numDays = parseInt( parsedDifference[0], 10 );
	if ( 0 < numDays ) {
		result += sfw.sharedFunctions.fetchNewFlagImage();
	}

	return result;
}

// TODO - this function is identical to one in util.js with same name. It should probably be removed if it is not used anywhere else.
sfw.sharedFunctions.tipRenderer = function( value ) {
	return '<div qtip="' + Ext.util.Format.htmlEncode( value ) + '">' + value + '</div>';
}


/**
 * Function for rendering product type.
 * @param value
 *	String value, having the product type. 1 - OS, 2 - Software
 * @return
 *	String product type: OS/Software
*/
sfw.sharedFunctions.productTypeRenderer = function( value ) {
	value = parseInt( value );
	switch ( value ) {
		case 2:
			return "Software";
		case 1:
			return "Operating System";
	}
	return "-";
}


// In the CSI, the readonly is taken from the LoginDetails.isReadOnly parameter. Pass in a soure type to
// differentiate VIM from CSI
sfw.sharedFunctions.readOnly = function( source ) {

	// TODO - not all instances of readOnly are called with a sourceType - the readOnly
	// functionality needs to be thoroughly looked over.

	if ( 'csi' === source ) {
		if ( LoginDetails.isReadOnly ) {
			return true;
		} else {
			return false;
		}
	}

	// Else... (VIM is default)
	if ( typeof sfw.modules.user.rights !== "undefined" && sfw.modules.user.rights == 2 ) {
		return true;
	}
	return false;
}


sfw.sharedFunctions.getDocumentSize = function() {
	var documentSize = { height: Ext.getBody().getHeight(), width: Ext.getBody().getWidth() };
	return documentSize;
}


sfw.sharedFunctions.doSize = function( object, width, height ) {
	var documentSize = sfw.sharedFunctions.getDocumentSize(); // Get document ('screen') size.
	var returnArray = false;
	if ( typeof width !== "undefined" && typeof height !== "undefined" ) {
		returnArray = true;
	}
	var width = width || object.getWidth(); // Get object's width and height, or use the default ones
	var height = height || object.getHeight();
	if ( height > documentSize.height ) {
		height = documentSize.height;
		if ( returnArray == false ) {
			object.setHeight( documentSize.height ); // If specified, adjust new size.
			var position = object.getPosition();
			object.setPosition( position[0], 0 );
		}
	}
	if ( width > documentSize.width ) {
		width = documentSize.width;
		if ( returnArray == false ) {
			object.setWidth( documentSize.width );
			var position = object.getPosition();
			object.setPosition( 0, position[1] );
		}
	}
	if ( returnArray == true ) {
		return [ height, width ];
	}
}


/**
 * Function for deciding on dynamic dimensions for height and/or width.
*/
// Input is default height and width.  To not calculate either of them, enter 0.
sfw.sharedFunctions.getDynamicDimensions = function( defaultHeight, defaultWidth, owner ) {
	return this.doSize( owner, defaultWidth, defaultHeight );
}
