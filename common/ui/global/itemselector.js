sfw.ItemSelect = function( options ) {
	var self = this;
	var firstGridPanel, middlePanel, secondGridPanel, selectDialog, localOptions = {};
	var defaultOptions = {
		width: 600
		,height: 400
		,firstGridTitle: 'Available'
		,secondGridTitle: 'Selected'
		,title: 'Multiple Selection'
	};
	var i;

	Ext.apply(localOptions, options, defaultOptions);

	// TODO: see if stateful state of the grids is possible. Multisorting is not really needed here, since for now we only had max of 2 column, but in most cases just 1 column.
	if ( typeof localOptions.firstStore.sortInfo === 'undefined' && typeof localOptions.firstStore.sortInfo === 'undefined' ) {
		if ( typeof localOptions.sortInfo === 'undefined' ) {
			for ( i = 0; i < localOptions.datafields.length; i++ ) {
				if ( typeof localOptions.datafields[i].column !== 'undefined' && typeof localOptions.datafields[i].column.header !== 'undefined' ) {
					localOptions.sortInfo = {field: localOptions.datafields[i].name, direction: 'ASC'};
					break;
				}
			}
		}
		localOptions.firstStore.sortInfo = localOptions.sortInfo;
		localOptions.secondStore.sortInfo = localOptions.sortInfo;
	}

	var moveItems = function ( sourceGrid, targetGrid ) {
		var selectionsArray = sourceGrid.selModel.selections.items;
		var record, records = [], index, i;

		var count = selectionsArray.length;
		if ( count > 0 ) {
			for ( i = 0; i < count; i++ ) {
				index = sourceGrid.store.findExact( localOptions.idProperty, selectionsArray[i].data[localOptions.idProperty] );
				record = sourceGrid.store.getAt( index );
				records.push( record );
			}

			targetGrid.store.add( records );
			sourceGrid.store.remove( records );
		}

		targetGrid.getSelectionModel().selectRecords( records );

		var si = targetGrid.store.sortInfo;
		if (si) {
			targetGrid.store.sort(si.field, si.direction);
		}
		targetGrid.view.refresh();
		sourceGrid.view.refresh();
	};

	// removes items from first grid, based on indexes of values in second grid
	var selections = localOptions.secondStore.data.items;
	if ( selections.length > 0 ) {
		for ( i = 0; i < selections.length; i++ ) {
			var index = localOptions.firstStore.findExact( localOptions.idProperty, selections[i].data[localOptions.idProperty] );
			if ( -1 !== index ) {
				localOptions.firstStore.removeAt( index );
			}
		}
	}

	if ( ! this.firstGrid ) {
		this.firstGrid  = new sfw.MultiSelect({
			autoExpandColumn: localOptions.autoExpandColumn
			,idProperty: localOptions.idProperty
			,store: localOptions.firstStore
			,datafields: localOptions.datafields
			,secuniaType: 'local'
			,sm: new Ext.grid.RowSelectionModel({ singleSelect: false })
			,loadMask: {
				msg: 'Loading...'
			}
			,listeners: {
				rowdblclick: function( grid, rowIndex ) {
					moveItems( self.firstGrid, self.secondGrid );
				}
				,keypress: function( e ) {
				    // Move item to first grid on ENTER keypress
					if ( e.ENTER == e.getKey() ) {
						moveItems( self.firstGrid, self.secondGrid );
					}
				}
			}
		});
	} else {
		Ext.apply(this.firstGrid, {
			autoExpandColumn: localOptions.autoExpandColumn
			,idProperty: localOptions.idProperty
			,store: localOptions.firstStore
			,datafields: localOptions.datafields
		});
	}

	if ( ! this.secondGrid ) {
		this.secondGrid = new sfw.MultiSelect({
			autoExpandColumn: localOptions.autoExpandColumn
			,idProperty: localOptions.idProperty
			,store: localOptions.secondStore
			,datafields: localOptions.datafields
			,secuniaType: 'local'
			,sm: new Ext.grid.RowSelectionModel({ singleSelect: false })
			,loadMask: {
				msg: 'Loading...'
			}
			,listeners: {
				rowdblclick: function( grid, rowIndex ) {
					moveItems( self.secondGrid, self.firstGrid );
				}
				,keypress: function( e ) {
				    // Move item to first grid on ENTER keypress
					if ( e.ENTER == e.getKey() ) {
						moveItems( self.secondGrid, self.firstGrid );
					}
				}
			}
		});
	} else {
		Ext.apply(this.secondGrid, {
			autoExpandColumn: localOptions.autoExpandColumn
			,idProperty: localOptions.idProperty
			,store: localOptions.secondStore
			,datafields: localOptions.datafields
		});
	}

	if ( !itemSelectPanel ) {
		firstGridPanel = new sfw.Panel({
			layout: 'fit'
			,name: 'fromPanel'
			,flex: 1
			,title: localOptions.firstGridTitle
			,border: true
			,items: [ this.firstGrid ]
		});

		secondGridPanel = new sfw.Panel({
			layout: 'fit'
			,name: 'toPanel'
			,flex: 1
			,title: localOptions.secondGridTitle
			,border: true
			,items: [ this.secondGrid ]
		});

		middlePanel = new sfw.Panel({
			layout: 'vbox'
			,name: 'middlePanel'
			,width: 35
			,border: true
			,layout: {
				type:'vbox',
				padding:'5',
				pack:'center',
				align:'center'
			}
			,defaults: {margins:'0 0 5 0'}
			,items: [{
				xtype:'button'
				,text: '&gt;'
				,listeners: {
					click: function() {
						moveItems( self.firstGrid, self.secondGrid );
					}
				}
			},{
				xtype:'button'
				,text: '&lt;'
				,listeners: {
					click: function() {
						moveItems( self.secondGrid, self.firstGrid );
					}
				}
			}]
		});

		var itemSelectPanel = new sfw.Panel({
			layout: 'hbox'
			,name: 'containerPanel'
			,region: 'center'
			,layoutConfig: {
				pack: 'start'
				,align: 'stretch'
			}
			,items: [
				firstGridPanel
				,middlePanel
				,secondGridPanel
			]
			,firstStore: localOptions.firstStore
			,secondStore: localOptions.secondStore
			,getSelected: function () {
				// returns array of indexes of selected values
				var selected = [];
				for ( var i=0, count = self.secondGrid.store.data.items.length; i < count; i++ ) {
					selected.push( self.secondGrid.store.data.items[i].data[localOptions.idProperty] );
				}
				return selected;
			}
		});

	}

	return itemSelectPanel;
};
