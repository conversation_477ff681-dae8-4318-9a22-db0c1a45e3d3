sfw.login = {
	isShadow: false
	,mainUsername: ''
};

sfw.login.create = function ( config ) {

	var handleSubmit = function() {
		config.handler();
	};

	var handleEnter = function(field, e) {
		if ( e.ENTER == e.getKey() ) {
			handleSubmit();
		}
	}

	var usernameTextField = new Ext.form.TextField({
		fieldLabel: 'Username'
		,width: 235
		,inputType: 'text'
		,cls: 'login-form'
		,name: 'username'
		,allowBlank: false
		,listeners: {
			specialkey: handleEnter.createDelegate( this )
		}
	});

	var passwordTextField = new Ext.form.TextField({
		fieldLabel: 'Password'
		,width: 235
		,inputType: 'password'
		,name: 'password'
		,allowBlank: false
		,listeners: {
			specialkey: handleEnter.createDelegate( this )
		}
	});

	if (config.useEmptyText) {
		usernameTextField.fieldLabel = null;
		usernameTextField.emptyText = "Username";
		usernameTextField.hideLabel = true;

		passwordTextField.fieldLabel = null;
		//passwordTextField.emptyText = "Password";
		passwordTextField.cls = "passwordField login-form";
		passwordTextField.on('focus', function(){
			passwordTextField.removeClass('passwordField');
		});
		passwordTextField.on('blur', function(){
			if (passwordTextField.getValue() == "") {
				passwordTextField.addClass('passwordField');
			}
		});
		passwordTextField.hideLabel = true;

		delete config.useEmptyText;
	}

	var items = [];

	var defaultItems = [
		usernameTextField
		,passwordTextField
	];

	if ( config.description ) {
		items.push( config.description );
		delete config.description;
	}

	items = items.concat( defaultItems );

	if ( config.optionalItems ) {
		items.push( config.optionalItems );
		delete config.optionalItems
	}

	config.items = items; // udpdate the items

	var defaultConfig = {
		labelWidth: 65
		,frame: true
		,monitorValid: true
		,buttonAlign: 'right'
		,method: 'POST'
	};

	var newConfig = Ext.apply( defaultConfig, config );

	var panel = new Ext.FormPanel( newConfig );
	return panel;

};