Ext.override( Ext.form.ComboBox, {
	setValue: function( v ) {
		var text = v;
		if(this.valueField){
			var r = this.findRecord(this.valueField, v);
			if(r){
				text = r.data[this.displayField];
			}else if(Ext.isDefined(this.valueNotFoundText)){
				text = this.valueNotFoundText;
			}
		}
		this.lastSelectionText = text;
		if(this.hiddenField){
			this.hiddenField.value = Ext.value(v, '');
		}
		// @todo:
		// Single quotes are not decoded which is fine as the server doesn't encode them (Json class)
		// However, there might be some data sources that do encode single quotes in the CSI.
		text = Ext.util.Format.htmlDecode( text );

		Ext.form.ComboBox.superclass.setValue.call(this, text);
		this.value = v;
		return this;
	}
});

sfw.ComboBox = Ext.extend( Ext.form.ComboBox, {
	constructor: function( config ) {
		config.allowBlank = config.allowBlank || false;
		config.typeAhead = config.typeAhead || false;
		config.mode = config.mode || "remote";
		config.store = config.store || {};
		config.minChars = config.minChars || 0;
		config.listeners = config.listeners || {};
		if ( config.typeAhead === false ) {
			config.triggerAction = config.triggerAction || 'all';
		}

		if ( config.secuniaType === "remote" ) {
			config.store = new Ext.data.JsonStore( config.store );
		}

		sfw.ComboBox.superclass.constructor.call( this, config );
	}
});