sfw.contextMenu = {
	init: function() {
		/**
		* @protected
		* @property
		* number of selected items in the grid displaying the context menu
		*/
		var selectedCount;

		/**
		* @protected
		* @method
		* updates the text for the menu based on number of selected items and the multiple property on the menu item
		* if the multiple property is a string we expect it to be a formatstring (see Ext's String.format)
		* if it is a function then that function is called with item and the selectedCall. It is expected that it returns
		* the new text for the menu item.
		*/
		var update = function( item, index, len) {
			if ( item.setText && item.multiple ) {
				if ( Ext.isFunction( item.multiple ) ) {
					item.setText( item.multiple( item, selectedCount ) );
				} else if ( Ext.isString( item.multiple ) ) {
					item.setText( String.format( item.multiple, selectedCount ) );
				}
			}
			return true;
		};

		/**
		* @protected
		* @method
		* if multiple items is selected then it calls update for each element in the menu to update the menu text based on selected count
		*/
		var beforeShow = function( menu ) {
			if ( selectedCount > 1 ) {
				menu.items.each( update );
			}
		};


		/**
		* @protected
		* @method
		* function injected before the item handler callbacks to ensure uniformity and usability of parameters send to the callback.
		*/
		var handle = function( callback, grid ) {
			callback( grid.selModel.getSelections(), grid );
		};

		/**
		* @protected
		* @method
		* function that builds the menu items based on config.
		*/
		var setupItems = function( grid, items, menu ) {
			for (var i=0;i< items.length; i++) {
				var item = items[i];
				if ( !item.multiple && selectedCount > 1 ) {
					continue;
				}
				if ( item.handler ) {
					item.handler = handle.createCallback( item.handler , grid );
				}
				if ( item.menu && item.menu.items ) {
					setupItems( grid, item.menu.items );
				}
				if ( menu ) {
					menu.addMenuItem( item );
				}
			}
		};

		/**
		* @method
		* Based on the supplied config it created an evenhandler function and and returns it
		*
		* @param {Object} config
		* @param {Ext.menu.BaseItem[]} config.items
		* each item can have a multiple property. If it does and the property resolves to true, then the item will be
		* visible when the context menu is shown while multiple items are selected.
		* The multiple property can be
		* - a boolean
		* - a formatString (@see String.format)
		* - a function that will get called with the item and the number of selected items and is expected to return the string that will be used as the menuitem's text.
		*
		* nested menus can be made by adding a "menu" property to an item in the config.items array.
		* @return {Function}
		*/
		this.create = function ( config ) {
			return function( grid, index, event ) {
				event.stopEvent();
				var show=true,
					sm = grid.getSelectionModel();
				if ( !sm.isSelected( index ) ) {
					sm.selectRow( index );
				}
				if (sm.singleSelect) {
					selectedCount = 1;
				} else {
					selectedCount = sm.getCount();
				}
				if ( Ext.isFunction( config.contextualMenu ) ) {
					showItems = config.contextualMenu(config.items, sm);
				} else {
					showItems = config.items;
				}
				if ( showItems.length > 0 ) {
					var menu = new Ext.menu.Menu({
						listeners: {
							beforeshow: beforeShow
						}
					});
					setupItems( grid, showItems, menu );
					if (typeof menu.items !== 'undefined') {
						menu.showAt( event.xy );
					}
				}
			};
		};
	}
};
sfw.contextMenu.init();
