/**
 * <PERSON><PERSON> for performing ajax requests using sfw promises
 *
 * var ajax = sfw.Ajax( 'http://dev2.intnet/~user/csi/api?uid=XXX' );
 *
 * ajax.request({
 *		params: { action: 'sps_package'
 *				,which: 'fetch_sps_script'
 *				,category: 'install'
 *				,type: 'js'
 *				}
 *	})
 *	.then( function( response ) {
 *			// do something with the response e.g. update UI etc
 *		})
 *	.done();
 *
 *
 */


!function() {

	/**
	 * Ajax wrapper using sfw promises
	 *
	 * @param url
	 *
	 * @require Ext.Ajax
	 * @require sfw.debug
	 */
	var Ajax = function( url ) {
		this.url = '';
		if ( url ) {
			this.setUrl( url );
		}
	};

	/**
	 * Function to set the url
	 * @param {String} url
	 */
	Ajax.prototype.setUrl = function( url ) {
		this.url = url;
	};

	/**
	 * The request function that returns a json response on execution.
	 *
	 * @param {Object} cfg
	 *   - cfg.url: If url is not provided, the url is picked that was
	 * 	 passed during object construction
	 * 	 - cfg.params
	 * 	 - cfg.method 'GET' or 'POST' (Default is GET)
	 *
	 * @return sfw.Promise
	 */
	Ajax.prototype.request = function( cfg ) {
		var me = this;
		var p = new sfw.Promise( function( resolve, reject ) {
				Ext.Ajax.request({
						url: cfg.url ? cfg.url : me.url // @todo: error handling?
						,params: cfg.params
						,method: cfg.method || 'GET'
						,success: function( response ) {
							try {
								var data = Ext.decode( response.responseText );
								resolve( data );
							} catch( ex ) {
								if ( typeof reject === 'function' ) {
									reject( ex );
								} else {
									sfw.debug.log( 'Error during exection of chained function.' );
								}
							}
						}
					});
			});
		return p;
	};


	sfw.Ajax = Ajax;
}();