/**
 * @file databoundgrid.js
 * Provides a grid linked directly to the database.
 * Requires the global object grid.js.
 *
 * Constructor configuration options:
 *
 * datafields: array of datafield objects. A datafield object should consist of: { name: '', column: [{ extjs column config }] }
 * NOTE: Do not include the 'id' field, as it is included by default.
 *
 * Example:
 * new DataBoundGrid({
 *	datafields: [ { name: 'name', column: [{ header: 'Name', renderer: RENDERER, ... }]  } ]
 *	,title: 'Title'
 *	,store: { url: 'api/provider.php' }
 * })
 *
 * Provider:
 * {
 * data: [ id: 1, name: 'Name Content' ... ]
 * }
 *
*/
sfw.DataBoundGrid = function( config ) {
	// Construct the store fields
	var storeFields = [];
	var gridColumns = [];

	config.store = config.store || {};
	config.store.idProperty = config.store.idProperty || 'id';

	// Set default value of config.store.remotesort to true.
	if( typeof config.store.remoteSort !== 'boolean' ){
		config.store.remoteSort = true;
	}

	storeFields.push(  { name: config.store.idProperty }  );

	for ( var i = 0; i < config.datafields.length; i++ ) {
		if ( typeof config.datafields[i].name != "undefined" ) {
			storeFields.push( { name: config.datafields[i].name } );
		}
	}

	// Construct the grid fields
	for ( var i = 0; i < config.datafields.length; i++ ) {
		if ( typeof config.datafields[i].column !== "undefined" ) {
			config.datafields[i].column.id = config.datafields[i].column.name || config.datafields[i].name;
			config.datafields[i].column.dataIndex  = config.datafields[i].column.dataIndex || config.datafields[i].name;

			// set default to true to make compatible with previous (incorrect) way of doing it (which set it to true whenever sortable was undefined, and false otherwise).
			// Note - all grids of this type will need to be double checked (todo)

			if ( typeof config.datafields[i].column.sortable !== 'boolean' ){
				config.datafields[i].column.sortable = true;
			}

			gridColumns.push( config.datafields[i].column );
		}
		if ( typeof config.datafields[i].column == "undefined" && typeof config.datafields[i].name == "undefined" ) {
			gridColumns.push( config.datafields[i] );
		}
	}

	config.secuniaType = "remote";
	config.store.root = config.store.root || 'data';
	config.store.fields = config.store.fields || storeFields;
	config.columns = config.columns || gridColumns;

	return new sfw.GridPanel( config );
}