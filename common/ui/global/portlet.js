/**
 * @file portlet.js
*/

/**
 * Provides generic dashboard portlet.
 *
 * Configuration options:
 *
 * height, width, title, id, tools, html, collapsible, items, portletId, parentDashboard
*/
sfw.portlet = function( config ) {
	if ( typeof config.tools == "undefined" ) {
		this.portletHandler = function(e, target, panel ) {
			panel.hide();
			delete this.content.parentDashboard.selectedPortlets[config.portletId];
			this.content.parentDashboard.refresh();
		}

		config.tools = [{
			id: 'close'
			,handler: this.portletHandler.createDelegate( this )
		}]
	}

	this.content = new Ext.ux.Portlet({
		title: config.title
		,id: config.id
		,portletId: config.portletId
		,tools: config.tools || null
		,html: config.html || null
		,height: config.height || 180
		,width: config.width || 300
		,collapsible: config.collapsible || false
		,items: config.items || null
		,autoScroll: config.autoScroll || false
		,refresh: config.refresh || null
	});

	return this.content;
}