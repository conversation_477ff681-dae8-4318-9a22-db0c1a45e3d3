/**
 * @file forumProfile.js
 *
 * Generic object for a window to register or manage a forum profile
 *
 */
sfw.forumProfile = {};

/**
 * Called when clicking the button to register or manage profile
 *
 * @param parent - object calling this
 *
 * @return
 * The form container
 *
*/
sfw.forumProfile.create = function( parent ) {
	try {
		var self = this;
		this.bbar = {};
		self.dataProviderURL = 'action=community&which=';

		var newProfileIntro = 'Choose a username to be used for your community forum profile. Your current Software Vulnerability Manager login password will be used for your forum profile.  Once registered you can manage and edit profile information.';

		// Do in a function as it needs the loginName, and we need to be able
		// to get it dynamically
		this.getLoggedInIntro = function( loginName ) {
			var loggedInIntro = 'You are logged in as user: <b>' + loginName + '</b>.  You can use this form to update your username in the database. For further options, see below.';
			return loggedInIntro;
		};

		// takes a boolean arg
		this.initialize = function( loggedIn, loginName ) {

			// Fill in fields based on if we are logged in already or not
			if ( loggedIn ) {
				self.getAction = 'updateProfile';
				this.bbar.submitButton.setText( 'Update' );
				this.introText.update( this.getLoggedInIntro( loginName ) );
				this.manageExistingProfile.show();
			} else {
				self.getAction = 'newProfile';
				this.bbar.submitButton.setText( 'Register' );
				this.introText.update( newProfileIntro );
				this.manageExistingProfile.hide();
			}

			this.usernameText.setValue( Ext.util.Format.htmlDecode( loginName ) );

			this.validateForm();
			this.interface.doLayout();
		};

		this.validateForm = function() {
			this.bbar.submitButton.setDisabled( true );
			if ( this.usernameText.isValid() ) {
				// Make sure our so called non-empty text is not just
				// blank spaces...
				var name = this.usernameText.getValue();
				if ( sfw.util.stripWhitespace( name ) ) {
					this.bbar.submitButton.setDisabled( false );
				}
			}
		};

		this.createProfile = function() {
			var url = globals.apiPath() + '&' + self.dataProviderURL + self.getAction;
			var username = this.usernameText.getValue();
			var params = {
				username: username
			};

			try {
				Ext.Ajax.request({
					url: url
					,params: params
					,success: function( data ) {
						try {
							var response = Ext.util.JSON.decode( data.responseText );
							var specific = response.result;
							switch ( specific ) {
							case 0:
								// everything is fine - update the parent
								parent.setUpRegistrationInfo( Ext.util.Format.htmlEncode( username ) );
								try {
									sfw.communityForum.hasForumProfile = true;
									sfw.communityForum.setLastActivity();
								} catch ( ex ) {
									sfw.debug.trigger( ex, 'sfw.forumProfile.createProfile()', 'success branch' );
								}
								if ( 'updateProfile' === self.getAction ) {
									self.introText.update( self.getLoggedInIntro( Ext.util.Format.htmlEncode( username ) ) );
								} else {
									self.initialize( true, Ext.util.Format.htmlEncode( username ) );
								}
								self.window.hide();
								Ext.Msg.show({
									title: "Success"
									,msg: "Profile has been successfully updated."
									,buttons: Ext.Msg.OK
								});
								break;
							case 1:
								// Name is already taken - show warning.
								Ext.Msg.alert( "Error", "Cannot update username - this name is already taken in the database.  Please try another." );
								break;
							case 2:
								Ext.Msg.alert( "Error", "Error retreiving user data from database. Username not updated." );
								break;
							default:
								Ext.Msg.alert( "Error", "Unexpected Error." );
								break;
							}
						} catch ( ex ) {
							Ext.Msg.alert( "Error", "Cannot update data." );
						}
					}
					,failure: function() {
						Ext.Msg.alert( 'Error', 'Unexpected error!' );
					}
				});
			} catch ( ex ) {
				sfw.debug.trigger( ex, 'Unexpected error' );
			}
		};

		// Open browser to access community forum
		this.openCommunityProfile = function() {
			var url = 'https://secunia.com/community/csi/?'; // ENHANCEMENT: make string in globals

			// Get auto-login string
			if ( sfw.communityForum.confirmString && sfw.communityForum.profileId ) {
				url += '&userid=' + sfw.communityForum.profileId;
				url += '&confirm=' + sfw.communityForum.confirmString;
			}

			// Open forum
			defaults.externalURL( url);
		};

		// UI
		// --
		// This will be 'Register' for a new profile, and 'Update' for a change
		this.bbar.submitButton = new Ext.Button({
			text: 'Register'
			,disabled: true
			,handler: this.createProfile.createDelegate( this )
		});

		this.bbar.closeButton = new Ext.Button({
			text: 'Close'
			,tooltip: 'Close window'
			,disabled: false
			,handler: function() {
 				self.window.hide();
			}
		});

		this.usernameText = new Ext.form.TextField({
			allowBlank: false
			,width: 200
			,maxLength: 50 // ? todo - max length of username? No point allowing 200 chars...
			,fieldLabel: 'Username'
			,emptyText: "Choose Username..."
			,value: ''
			,enableKeyEvents: true
			,disabled: false
			,listeners: {
				invalid: function() {
					self.bbar.submitButton.setDisabled( true );
				}
				,valid: this.validateForm.createDelegate( this )
			}
		});

		this.introText = new sfw.Panel({
			html: newProfileIntro // replace dynamically if needed
			,layout: 'auto'
			,padding: '0px 10px 10px 5px'
		});

		this.manageProfileButton = new Ext.Button({
			text: 'Manage Profile'
			,tooltip: 'Update / Manage Profile'
			,handler: this.openCommunityProfile.createDelegate( this )
		});

		this.manageExistingProfile = new sfw.Panel({
			html: "With an existing profile, use the following button to be directed to the public website where you can edit and manage your profile, change  your user information, forum profile password, etc."
			,layout: 'auto'
			,cls: 'ContentPadding'
			,buttonAlign: 'left'
			,buttons: [ this.manageProfileButton ]
			,hidden: true // update dynamically based on type
		});


		this.profileDetails = new Ext.form.FieldSet({
			layout: 'form'
			,title: 'Profile Management'
			,autoWidth: true
			,items: [
				this.introText
				,this.usernameText
				,this.manageExistingProfile
			]
		});

		this.interface = new sfw.Panel({
			layout: 'fit'
			,items: new sfw.Panel({
				layout: 'auto'
				,items: [ this.profileDetails ]
				,frame: true
			})
		});

		// Get dynamic height/width in case of small browser window.
		var dynamicDimensions = sfw.sharedFunctions.getDynamicDimensions( 300, 500, parent.interface );
 		var interfaceHeight = dynamicDimensions[0];
		var interfaceWidth = dynamicDimensions[1];

		this.window = new Ext.Window({
			title: 'Profile Management'
			,height: interfaceHeight
			,width: interfaceWidth
			,layout: 'fit'
			,modal: true
			,items: this.interface
			,closeAction: 'hide'
			,listeners: {
				show: function() {
					self.window.center();
				}
			}
			,buttons: [
				this.bbar.submitButton
				,this.bbar.closeButton
			]
		});

	} catch ( ex ) {
		sfw.debug.trigger ( ex, "forumProfile.create" );
	}

	return this;
};