/**
 * Provides various Ext extensions, that do not fit in a global object, and should be applied as Ext defaults.
 */

// Add validator for IP Addresses
Ext.apply(Ext.form.VTypes, {
	IPAddress:  function(v) {
		return (/^([0-9][0-9]{0,1}|1[013-9][0-9]|12[0-689]|2[01][0-9]|25[0-5])([.]([0-9]{0,1}[0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){2}[.]([0-9][0-9]{0,1}|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/).test(v);
	}
	,IPAddressText: 'Must be a valid IP address (0-255.0-255.0-255.0-255)'
	,IPAddressMask: /[\d\.]/i
});

/**
 * Validates ipv4 netmasks (********* - ***************, CIDR 0-32)
 *
 * For future ipv6 CIDR support:
 *		ipv6 0-64, 128 ( [1-5]?\d|6[0-4]|128 )
 *		var isIpv6Cidr = /^\d{1,3}$/.test( v ) && ( 128 === v || (v >= 0 && v <= 64) );
 */
Ext.apply(Ext.form.VTypes, {
	netmask: function(v) {
		//	ipv4 0-32 ( [12]?\d|3[0-2] )
 		var isIpv4Cidr = /^\d{1,2}$/.test( v ) && v >= 0 && v <= 32;
 		//  ipv4 ********* - ***************
		return isIpv4Cidr || (/^(128|192|224|24[08]|25[245].0.0.0)|(255.(0|128|192|224|24[08]|25[245]).0.0)|(255.255.(0|128|192|224|24[08]|25[245]).0)|(255.255.255.(0|128|192|224|24[08]|25[245]))$/).test(v);
	}
	,netmaskText: 'Must be a valid netmask (********* - ***************) or valid CIDR (0-32)'
	,netmaskMask: /[\d\.]/i
});

var emailTest = /^(\w+)([\-+.][\w]+)*@(\w[\-\w]*\.){1,5}([A-Za-z]){2,9}$/;

/**
 *	Overriding the Extjs email Vtype as it doesn't allow domains ending with more than 6 chars
 *	e.g. Following email addresses are invalid according to the RFC2606 [ http://tools.ietf.org/rfc/rfc2606.txt ] :
 *			<EMAIL>
 *			<EMAIL>
 */

Ext.apply(Ext.form.VTypes, {
	email: function(val) {
		return emailTest.test( val.trim() );
	}
	// ,emailMask: /[a-z0-9_\.\-\+\'@]/i
	,emailText: 'Not a valid email address.'
});

Ext.apply(Ext.form.VTypes, {
	EmailList: function(val) {
		var addresses = val.split(',');
		var idx = Ext.each( addresses, function(item) {
				return emailTest.test( item.trim() );
			}
		);

		return !Ext.type( idx );
	}
	,EmailListText: 'Not a valid email address or comma separated list of email addresses.'
});


var mobileNumberTest = /^(([+]{1}|0{2}){1}\d+)?$/;
Ext.apply(Ext.form.VTypes, {
	MobileNumber: function( val, field ) {
		var numbers = val.split(',');
		var testResult =  Ext.each( numbers, function(number) {
			// each international number should be at least 8 digits
			if ( typeof number === 'undefined' || number.length < 8 || number.length > 15 ) {
				return false;
			}
			return mobileNumberTest.test(number);
		});
		return !Ext.type(testResult);
	}
	,MobileNumberText: 'Invalid Mobile Number'
	,MobileNumberMask: /[+\d,]/
});

Ext.apply(Ext.form.VTypes, {
	SingleMobileNumber: function( number, field ) {
			// each international number should be at least 8 digits
			if ( typeof number === 'undefined' || number.length < 8 || number.length > 15 ) {
				return false;
			}
			return mobileNumberTest.test(number);
	}
	,SingleMobileNumberText: 'Invalid Mobile Number'
	,SingleMobileNumberMask: /[+\d]/
});

// Have an additional mobile number field that allows 6 digit number, as the CSI password recovery
// has a separate field for the country code, thus may have shorter (local) numbers entered.
Ext.apply(Ext.form.VTypes, {
	MobileNumberLocal: function( val, field ) {
		var numbers = val.split(',');
		var testResult =  Ext.each( numbers, function(number) {
			return /^\d{6,15}$/.test(number);
		});
		return !Ext.type(testResult);
	}
	,MobileNumberLocalText: 'Invalid Mobile Number'
	,MobileNumberLocalMask: /[\s+\d,]/
});

var FilePathTest = /^(?:[\w]\:|\\)(\\[A-Za-z_\-\s0-9\.]+)+$/;
Ext.apply(Ext.form.VTypes, {

	FilePath: function(val) {
		return FilePathTest.test(val);
	}
	,FilePathText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\'.'
});

var LongTimeTest = /^([0-1][0-9]|[2][0-3]):([0-5][0-9]):([0-5][0-9])$/;
Ext.apply(Ext.form.VTypes, {

	LongTime: function(val) {
		return LongTimeTest.test(val);
	}
	,LongTimeText: 'Not a valid Long Time. Must be in the format "00:00:00".'
	,LongTimeMask: /[\d:]/
});


var pdfFileTest = /^(\w+)([\-][\w]+)*[\.][Pp][Dd][Ff]$/;

Ext.apply(Ext.form.VTypes, {

	PdfFile: function(val) {
		return pdfFileTest.test(val);
	}
	,PdfFileText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.pdf\'.'
});

var csvFileTest = /^(\w+)([\-][\w]+)*[\.][Cc][Ss][Vv]$/;

Ext.apply(Ext.form.VTypes, {

	csvFile: function(val,field) {
		return csvFileTest.test(val);
	}
	,csvFileText: 'Name may only contain alpha-numeric characters, \'-\' and \'_\', and must end with \'.csv\'.'
});


// Custom validation to make sure the password fields match
Ext.apply(Ext.form.VTypes, {

	password: function( val, field ) {
		if ( field.initialPassField ) {
			var pwd = Ext.getCmp( field.initialPassField );
			return ( pwd.getValue() === val );
		}
		return true;
	}
	,passwordText: 'The passwords entered do not match.'
});

// Custom alphanum but also allowing a space characters and '-' and '_'.
// Logic: cannot start or end with a space or '-'.  This also forces names to be at least 2 characters.
var alphanumWithSpaceTest = /^(\w+)([\w\s\-]+)*(\w+)$/;
Ext.apply(Ext.form.VTypes, {
	alphanumWithSpace: function(val) {
		return alphanumWithSpaceTest.test( val );
	}
	,alphanumWithSpaceText: 'Name may only contain alpha-numeric characters, \'-\', \'_\', and \'space\', must be at least two characters, and must start and end with alphanumeric characters.'
});

var alphanumNoSpaceTest = /^[a-zA-Z0-9]+$/;
Ext.apply(Ext.form.VTypes, {
	alphanumNoSpace: function(val) {
		return alphanumNoSpaceTest.test( val );
	}
	,alphanumNoSpaceText: 'Name may only contain alpha-numeric characters.'
});


var integerTest = /^[1-9]+[0-9]*$/;
Ext.apply(Ext.form.VTypes, {
	integerTest: function(val) {
		return integerTest.test( val );
	}
	,integerTestText: 'Type must be integer'
});

var digitTest = /^[0-9]*$/;
Ext.apply(Ext.form.VTypes, {
	percentageTest: function(val) {
		if ( digitTest.test( val ) && 0 <= val && val <= 100 ) {
			return true;
		} else {
			return false;
		}
	}
	,percentageTestText: 'Must be a percentage (0-100)'
	,percentageTestMask: /\d/
});

Ext.apply(Ext.form.VTypes, {
	integerCsvTest: function(val) {
		var ourInts = val.split(',');
		var idx = Ext.each( ourInts
							,function(item) {
								return integerTest.test( item.trim() );
							}
						  );
		return !Ext.type( idx );
	}
	,integerCsvTestText: 'Type must be integer or comma separated list of integers.'
});

var dateTimeTest = /^20\d{2}\-(0?[1-9]|1[0-2])\-(0?[1-9]|[12]\d|3[01]) ([01]\d|2[0-3]):[0-5]\d$/;
Ext.apply(Ext.form.VTypes, {

	dateTime: function( val ) {
		if ( Date.parseDate( val , 'Y-n-j G:i', true) ) {
			return dateTimeTest.test( val );
		} else {
			return false;
		}
	}
	,dateTimeText: 'Invalid format. Use 20YY-MM-DD HH:MM.'
	,dateTimeMask: /[\d\:\- ]/
});

Ext.apply( Ext.form.VTypes, {
	activeDirectoryContainer: function( val, field ) {
		if ( typeof sfw.ActiveDirectorySettings === 'undefined' || sfw.ActiveDirectorySettings === null ) {
			return false;
		}
		var res = '';
		try {
			res = sfw.ActiveDirectorySettings.getContainerName( val );
		} catch ( ex ) {
			sfw.debug.trigger( ex, 'sfw.csiSettings::context', 'Could not parse container name.' );
		}
		if ( res.length > 0 ) {
			return true;
		}
		return false;
	}
	,activeDirectoryContainerText: 'Please provide an Active Directory distinguished name'
});


Ext.ns( "sfw.form.TimeSplitField" );
/**
 * @class TimeSplitField
 * @extends Ext.form.CompositeField
 * A time field that uses two combo boxes to specify a time, one combo box for
 * the hour and one combo box for the minutes.
 * This field was created for use in the Network Appliance Group's schedule form
 * @param {Object} config
 *	- Uses the same config as {Ext#form#TextField} with the addition of:
 *		- {Number}emptyTextWidth  Extra width for the display field's text
 *			if not supplied then it will use config.width.
 *			if config.width is not supplied then adds 100px
 * @mixins Ext.form.ComboBox
 * @mixins Ext.form.CompositeField
 */
sfw.form.TimeSplitField = Ext.extend( Ext.form.CompositeField, {
	constructor: function ( config ) {
		// build array of hours
	    var hours = [];
	    for ( var h = 0, time; h < 24; h++ ) {
	    	time = String.leftPad( h, 2, "0" ); // haha water
            hours.push( [time] );
        }
        // build array of minutes
		var minutes = [];
        for ( i = 0, time; i < 4; i++ ) {
        	time = String.leftPad( i * 15, 2, "0" );
            minutes.push( [time] );
        }
        // make config for the Hours ComboBox
		var hoursFieldConfig = {
		    id: config.id + "_hour"
		    ,emptyText: "HH"
		    ,store: new Ext.data.ArrayStore({
				idIndex: 0
				,fields: [ "value" ]
				,data: hours
			})
			// shared properties below here
			,width: 45
			,listWidth: 44
			,style: "text-align: center;"
			,valueField: "value"
			,displayField: "value"
			,submitValue: false // don't submit to server
			,mode: "local"
			,triggerAction: "all"
			,typeAhead: false
			,editable: false
			,allowBlank: false
			,listeners: {
				select: function ( field ) {
					var hours = Ext.getCmp( config.id + "_hour" ).getValue();
					var minutes = Ext.getCmp( config.id + "_minute" ).getValue();
					if ( "" !== hours && "" !== minutes ) {
						// update the hidden field with the values from the comboboxes
						Ext.getCmp( config.id ).setValue( hours + ":" + minutes );
						// call with same arg count as delegated fn
						Ext.getCmp( config.id ).fireEvent( "change", Ext.getCmp( config.id ), null, null, null, null );
					}
				}
			}
		};
		// make config for the Minutes ComboBox
		var minutesFieldConfig = Ext.applyIf( {
			id: config.id + "_minute"
			,emptyText: "MM"
			,listWidth: 28
		    ,store: new Ext.data.ArrayStore({
       	        idIndex: 0
				,fields: [ "value", "label" ]
               	,data : minutes
            })
		}, hoursFieldConfig );

		var hourField = new Ext.form.ComboBox( hoursFieldConfig ); // create the hour field
		var minuteField = new Ext.form.ComboBox( minutesFieldConfig ); // create the minute field

		// make config to create composite field
		var compositeConfig = {
			id: config.id
			,width: 120
			,labelStyle: config.labelStyle || {}
			,fieldLabel: config.fieldLabel || ""
			,style: config.style
			,hourField: hourField
			,minuteField: minuteField
			,items : [
				hourField
				,{ // create the separator
				    xtype: "label"
				    ,text: ":"
				    ,style: "padding-top: 3px"
				    ,height: 33
                }
				,minuteField
			]
			,listeners: config.listeners || {}
			,vtype: config.vtype || null
		};
		return sfw.form.TimeSplitField.superclass.constructor.call( this, compositeConfig );
	}
	/**
	 * @method
	 * @private
	 * Updates the combo boxes so they show the field's current value
	 */
	,updateCombos : function () {
		var hours = "";
		var minutes = "";
		var time = this.getValue().split( ":" );
		if ( 2 === time.length ) {
			hours = time[0];
			minutes = time[1];
		}
		Ext.getCmp( this.id + "_hour" ).setValue( hours );
		Ext.getCmp( this.id + "_minute" ).setValue( minutes );
	}
	/**
	 * @method
	 * @publish
	 * Sets the value of the field and then updates the two combo boxes
	 */
	,setValue : function ( value ) {
		this.value = value;
        if ( this.rendered ) {
            this.el.dom.value = ( true === Ext.isEmpty( value ) ? "" : value );
            this.validate();
        }
		this.updateCombos();
	}
});
Ext.reg( "timesplitfield", sfw.form.TimeSplitField );


// Add validator for DateTime and TimeSplit field combos.
Ext.apply( Ext.form.VTypes, {
	dateTimeSplit: function() {
		var dateTimeSplitTest = /^20\d{2}\-(0?[1-9]|1[0-2])\-(0?[1-9]|[12]\d|3[01]) ([01]\d|2[0-3]):[0-5]\d$/;
		/**
		 * @function
		 * Validates a DateTime and SplitTime field pair as a single DateTime.
		 * To use this validator, set it as the vtype for your DateField and
		 * your SplitTime Field. To bind those two fields together you need
		 * to specify the ID of the other field as one of the field's properties:
		 *
		 *  To specify which TimeSplit Field the DateField is attached to put
		 *	the TimeSplit Field's ID in a new timeSplitFieldId property on the DateField.
		 *
		 *  To specify which Date Field the TimeSplit Field is attached to put
		 *	the DateField's ID in a new dateFieldId property on the TimeSplit Field
		 *
		 * @param {String} val
		 * @param {Date}   val
		 * @param {Number} val
		 * @param {Object} val
		 * @param {Ext.form.DateField} field
		 * @param {String} field.timeSplitFieldId
		 * @param {String} field.dateFieldId
		 * @return {Boolean}
		 */
		return function( val, field ) {
			var timeSplitField;
			var dateField;

			if ( "undefined" !== typeof field.timeSplitFieldId ) {
				dateField = field;
				timeSplitField = Ext.getCmp( field.timeSplitFieldId );
			}
			if ( "undefined" !== typeof field.dateFieldId ) {
				dateField = Ext.getCmp( field.dateFieldId );
				timeSplitField = field;
			}
			if ( !timeSplitField || !dateField ) {
				return false; // The two fields were not bound
			}

			// Combine the two fields' values in to one date time string
			var value = dateField.getValue().format( globals.dateShortInput ) + " " + timeSplitField.getValue();

			// Enforce the Min Value if one has been stipulated. Note that when using this vtype,
			// the minValue is specified on the DateField
			if ( "undefined" !== dateField.minValue ) {
				// If the field auto-cleared it's minValue time then use the current time
				if ( 0 === dateField.minValue.getHours() && 0 === dateField.minValue.getMinutes() ) {
					dateField.minValue.setHours( new Date().getHours() );
					dateField.minValue.setMinutes( new Date().getMinutes() );
				}
				// Do the comparison and flag the fields as invalid or valid
				var date = Date.parseDate( value, globals.dateLongHumanInput );
				var backupPreventMark = dateField.preventMark; // backup
				dateField.preventMark = false;
				if ( date.getTime() <= dateField.minValue.getTime() ) {
					// Set all invalid flags
					timeSplitField.hourField.markInvalid( this.dateTimeSplitMinText );
					timeSplitField.minuteField.markInvalid( this.dateTimeSplitMinText );
					dateField.markInvalid( this.dateTimeSplitMinText );
					dateField.vtypeText = this.dateTimeSplitMinText;
					return false;
				} else {
					// Clear all invalid flags
					timeSplitField.hourField.clearInvalid();
					timeSplitField.minuteField.clearInvalid();
					dateField.clearInvalid();
				}
				dateField.preventMark = backupPreventMark; // restore
			}
			// Check date format.
			dateField.vtypeText = this.dateTimeSplitText;
			return dateTimeSplitTest.test( value ) && Date.parseDate( value , "Y-n-j G:i", true );
		}
	}()
	,dateTimeSplitText: "Not a valid date format. The format is YYYY-MM-DD hh:MM."
	,dateTimeSplitMinText: "You must choose a Date and Time in the future."
	,dateTimeSplitMask: /[\d\:\- ]/
} );