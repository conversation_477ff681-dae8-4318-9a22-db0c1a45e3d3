/**
 * @file navigation.js
 * Provides navigation functionality.
 *
 * Configuration options:
 *
 * NOTE: If the file is not included, navigation will silently be ignored.
 * Requires: ui.js, util.js.
 *
 * Main object methods:
 *	goBack(), goHome(), goForward()
 *
*/

sfw.navigation = {};

sfw.navigation.create = function() {
	try {
		this.pageStack = [];
		this.backHistory = [];
		this.forwardHistory = [];
		this.navigationPosition = 0;
		this.navigating = false;

		/**
		* Function for tracing the user navigation, in shorter terms: store loaded 'pages' in an array, for use in history
		* @param pageId
		*	String, name of the 'page' beying loaded. Used for retrieving at a later time
		*/
		this.trace = function( pageId ) {
			if ( this.navigating === true ) {
				return;
			}
			this.pageStack = sfw.util.browseArray( this.pageStack, this.navigationPosition, true ).before;
			// Remove page if already in histoy
			for ( var i = 0; i < this.pageStack.length; i++ ) {
				if ( this.pageStack[i] == pageId ) {
					this.pageStack = sfw.util.arrayRemove( this.pageStack, i );
				}
			}
			this.pageStack[this.pageStack.length] = pageId;
			this.forwardHistory = [];
			this.backHistory = this.pageStack;
			this.navigationPosition = this.pageStack.length;
			this.makeHistory();
		};

		/**
		* Check if a page in the stack is currently active, so it won't be displayed in the history
		*/
		this.isActive = function( pageId ) {
			if ( pageId === sfw.util.activePage ) {
				return true;
			}
			return false;
		};

		this.initToolbar = function() {
			// sfw.configuration.ui.disableCommonToolbarIcons is used to disable the icons for the shared buttons
			if ( !sfw.configuration.ui.disableCommonToolbarIcons ) {
				this.homeButton.setIcon('gfx/ui/Home_32.png');
				this.refreshButton.setIcon('gfx/ui/Refresh_32.png');
			}

			sfw.navigation.backButton.addClass("x-form-toolbar-standardButton");
			sfw.navigation.homeButton.addClass("x-form-toolbar-standardButton");
			sfw.navigation.forwardButton.addClass("x-form-toolbar-standardButton");
			sfw.navigation.refreshButton.addClass("x-form-toolbar-standardButton");
		};

		/**
		* Function for creating history menu items, and arranging them in 'chronological' order
		*/
		this.makeHistory = function() {
			/**
			* Local history object containing menu items for back and fwd
			*/
			var history = {}, i;
			history.back = new sfw.Menu({
				maxHeight: 200
			});
			history.forward = new sfw.Menu({
				maxHeight: 200
			});

			/**
			 * @param history
			 * The array that contains the historic pages
			 * @param index
			 * Index of the page in the history array
			 *
			 * @return title
			 */
			var getTitle = function( history, index ) {
				var title = '-';
				try {
					title = sfw.ui.pages[history[index]].interface.title;
				} catch( ex ){}
				return title;
			};

			/**
			* Generate back menu items
			*/
			this.backHistory = sfw.util.browseArray( this.pageStack, this.navigationPosition ).before;
			for ( i = 0; i < this.backHistory.length; i++ ) {
				if ( !this.isActive( this.backHistory[i] ) ) {
					history.back.add({
						text: getTitle( this.backHistory, i )
						,position: ( this.backHistory.length - i )
						,handler: function() {
							sfw.navigation.go( this.position );
						}
					});
				}
			}

			/**
			* Generate fwd menu items
			*/
			this.forwardHistory = sfw.util.browseArray( this.pageStack, this.navigationPosition ).after;
			for ( i = 0; i < this.forwardHistory.length; i++ ) {
				if ( !this.isActive( this.forwardHistory[i] ) ) {
					history.forward.add({
						text: getTitle( this.forwardHistory, i)
						,position: ( i + this.backHistory.length + 1 )
						,handler: function() {
							sfw.navigation.go( this.position );
						}
					});
				}
			}

			/**
			* Render the items, icons, etc
			*/
			this.backButton.menu = ( ( sfw.configuration.ui.navigationMenuDisabled || this.backHistory.length <= 1 ) ? null : history.back );
			this.forwardButton.menu = ( ( sfw.configuration.ui.navigationMenuDisabled || this.forwardHistory.length === 0 ) ? null : history.forward);

			if ( sfw.configuration.ui.disableCommonToolbarIcons ) {
				this.backButton.setDisabled( ( ( this.navigationPosition > 1 ) ? false : true ) );
				this.forwardButton.setDisabled( ( ( this.navigationPosition === this.pageStack.length ) ? true : false ) );
			} else {
				if ( ( this.navigationPosition > 1 ) && ( this.backButton.icon !== 'gfx/ui/prev_on.png') ) {
					this.backButton.setIcon( 'gfx/ui/prev_on.png' );
				} else if ( ( this.navigationPosition <= 1 ) && ( this.backButton.icon !== 'gfx/ui/prev_off.png') ) {
					this.backButton.setIcon( 'gfx/ui/prev_off.png' );
				}

				if ( this.backButton.isVisible() === true ) {
					this.backButton.render();
				}

				if ( ( this.navigationPosition === this.pageStack.length ) && ( this.forwardButton.icon !== 'gfx/ui/forward_off.png' ) ) {
					this.forwardButton.setIcon( 'gfx/ui/forward_off.png' );
				} else if ( ( this.navigationPosition !== this.pageStack.length ) && ( this.forwardButton.icon !== 'gfx/ui/forward_on.png' ) ) {
					this.forwardButton.setIcon( 'gfx/ui/forward_on.png' );
				}
				if ( this.forwardButton.isVisible() === true ) {
					this.forwardButton.render();
				}
			}
		};

		/**
		* Function for jumping somewhere in history
		* @param where
		*	Integer where in the history to jump to
		*/
		this.go = function( where ) {
			this.navigating = true;
			this.navigationPosition = where;
			sfw.util.switchPage( sfw.ui.center, sfw.ui.pages[this.pageStack[where - 1]] );
			sfw.util.selectNode( this.pageStack[where - 1] );
			this.makeHistory();
			this.navigating = false;
		};

		/**
		* Function for going back in history
		* @param where
		*	Integer where in the pageStack ( history ) should it jump to. If not set, it will go to the previous page.
		*/
		this.goBack = function() {
			var where = sfw.navigation.navigationPosition - 1;
			if ( where <= 0 ) {
				return;
			}
			sfw.navigation.go( where );
		};

		/**
		* Function for going forward in history
		* @param where
		*	Integer where in the pageStack ( history ) should it jump to. If not set, it will go to the next page.
		*/
		this.goForward = function() {
			var where = sfw.navigation.navigationPosition;
			if ( where > sfw.navigation.pageStack.length - 1 ) {
				return;
			}

			sfw.navigation.go( where + 1 );
		};

		/**
		* Buttom for 'going' home
		*/
		this.goHome = function() {
			sfw.util.switchPage( sfw.ui.center, sfw.ui.pages[sfw.configuration.ui.home] );
			sfw.util.selectNode( sfw.configuration.ui.home );
		};

		this.initToolbar();
	} catch ( ex ) {
		sfw.debug.trigger( ex, "navigation" );
	}
};

/**
* Create reusable buttons
*/
sfw.navigation.backButton = new Ext.SplitButton({
	id: 'navigation_back'
	,text: 'Back'
	,scale: 'large'
	,iconAlign: 'left'
	,width: 100
	,tooltip: 'Go back one page'
	,handler: function() {
		sfw.navigation.goBack();
	}
});

sfw.navigation.homeButton = new Ext.Button({
	text: 'Home'
	,scale: 'large'
	,iconAlign: 'left'
	,width: 100
	,tooltip: 'Go to the home page'
	,handler: function() {
		sfw.navigation.goHome();
	}
});

sfw.navigation.refreshButton = new Ext.Button({
	text: 'Refresh'
	,scale: 'large'
	,iconAlign: 'left'
	,width: 100
	,tooltip: 'Refresh the current page'
	,handler: function() {
		sfw.util.refreshActive();
	}
});

sfw.navigation.forwardButton = new Ext.SplitButton({
	text: 'Forward'
	,scale: 'large'
	,iconAlign: 'left'
	,width: 100
	,tooltip: 'Go forward one page'
	,handler: function() {
		sfw.navigation.goForward();
	}
});

/**
 * Button group for the top toolbar.
*/
sfw.navigation.buttonGroup = new Ext.ButtonGroup({
	items: [ sfw.navigation.backButton, sfw.navigation.homeButton, sfw.navigation.refreshButton, sfw.navigation.forwardButton ]
});