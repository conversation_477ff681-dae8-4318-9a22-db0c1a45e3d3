Ext.ns('sfw.tasks');
/**
* a Task object.
*/
(function () {

		var taskInterval= function( newInterval ) {
			if ( !Ext.isNumber( newInterval ) && !Ext.isFunction( newInterval ) ) {
				newInterval = sfw.TaskMgr.DEFAULT_INTERVAL;
			}
			return newInterval;
		};


	var Task = Ext.extend( Object, {
		// note on Ext Objectoriented programming....
		// this object goes into the prototype of the sfw.task.Task.
		// So beware when using properties that themself are objects.
		// example sfw.tasks.Task.mydata = {}
		// adding and removing properties on mydata will affect all objects, unless care is taken in the sfw.tasks.Task's constructor.
		// We need to always do a shallow copy of such... this can easily be done with Ext.apply(this,config) for instance.

		/**
		* the Task class will create a _state property to keep track of various internal states.
		*
		* @cfg run (required) function to call at each scheduled time.
		* If the run function returns false the task will be stopped.
		*
		* @cfg {Object} scope (optional)
		* the scope in which to execute the run function. (defaults to the config object)
		*
		* @cfg {Boolean} [autoStart=true]
		* start the task automatic
		*
		* @cfg {Number} [concurrencyLevel=1]
		* How many simuataneous runs is allowed. any number less than 1 means infinite
		*
		* @cfg {Number} duration (optional)
		* The length of time in milliseconds to invoke the task before stopping it automatically (defaults to indefinite)
		*
		* @cfg {Number} repeat (optional)
		* the number of times to invoke the task before stopping it automatically (defaults to indefinite)
		*
		* @cfg {Boolean} noLog (optional)
		* A flag to indicate weather logs about the cron are printed to the console (defaults to false)
		*
		* @cfg {Number/Function} [interval=300000]
		* if interval is a number then it is the number of milliseconds to the next time the task should be executed.
		* If interval is a function then it depends on the return value of the function as defined below:
		* - Date   : the next time the task should be executed.
		* - Number : the number of milliseconds until the next time the task should be executed.
		* - else   : the default interval is used.
		*
		*     // simpel task that runs every second
		*     var t = new sfw.tasks.Task({
		*			interval:1000,
		*           run: function() { sfw.Events.fireEvent('me.i.have.run'); }
		*         });
		*
		*     // task that runs at 19:00 every 1st day of the month
		*     var t = new sfw.tasks.Task({
		*       interval: function() {
		*         var now = new Date(), next;
		*         if ( now.getMonth() == 11 ) {
		*           next = new Date(now.getYear() + 1, 0, 1, 19);
		*         } else {
		*            next = new Date(now.getYear(), now.getMonth() +1, 1,19);
		*         }
		*         return next;
		*       },
		*       run: function() { sfw.Events.fireEvent('me.i.have.run'); }
		*
		*     };
		* @cfg {Number/Function} firstrun (optional)
		* ms until the first run of the task.
		*/
		constructor: function( config ) {

			config = config || {};

			Ext.apply(this, config);
			this._state ={
				created: new Date().getTime()
			};
			Task.superclass.constructor.call(this, config);
			if ( this.autoStart ) {
				this.start();
			}
		},
		/**
		* @method
		* start the task
		*/
		start: function() {
			sfw.TaskMgr.start( this );
		},
		/**
		* @method
		* stop the task
		*/
		stop: function() {
			sfw.TaskMgr.stop( this );
		},
		/**
		* @method
		* set a new interval for the task.
		*
		*/
		setInterval: function( interval ) {
			interval = taskInterval(interval);
			sfw.TaskMgr.changeInterval(this,interval);
		},
		autoStart : true,
		concurrencyLevel: 1,
		noLog: false
	});

	sfw.tasks.Task = Task;

})();
