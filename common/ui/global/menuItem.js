/**
 * @file menuitem.js
 * Provides wrapper for the ExtJS menuItem object, supporting some default configuration options.
 *
 * Configuration options:
 *
 * disableClick: true or false to disable click on this menu item
*/

sfw.MenuItem = function( config ) {
	if ( config.disableClick === true ) {
		config.listeners = config.listeners || {};
		config.listeners.click = config.listeners.click || function() {
			return false;
		}
	}

	return new Ext.menu.Item( config );
}