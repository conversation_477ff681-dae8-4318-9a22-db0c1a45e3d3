/**
* @singleton
* uses sfw.ADT.PriorityQueue
*/
sfw.TaskMgr = (function() {
	var self = {
			DEFAULT_INTERVAL: 300000 // 300000ms = 5 mins is the default interval
		}
		,runningTasks = []
		,taskRunner = new Ext.util.TaskRunner(50)
		,runTasksTask = {
			interval: 500,
			run: function() {
				var task,
					scope,
					args,
					rt;

				if ( task = runningTasks.shift() ) {
					scope = task.scope || task,
					args = task.args || [];
					task._state.runCount++;
					rt = task.run.apply( scope, args );
					if (! task.async ) {
						afterTaskRun.call(task,rt);
					}
				} else {
					return false;
				}
			}
		}
		/**
		* the next task to run. Undefined if no tasks.
		*/
		,nextTask = {
			task:undefined,
			timeoutId: 0,
			timeToNextRun:0
		 }
		/**
		* @protected
		* id of the current setTimeout, 0 if none.
		*/
		,killTaskTimer = function() {
			if (nextTask.timeoutId !== 0) {
				clearTimeout( nextTask.timeoutId );
				nextTask.timeoutId = 0;
				nextTask.timeToNextRun = 0;
				nextTask.task = undefined;
			}
		}
		,startProcessTimeout = function(task) {
			nextTask.task = task;
			if (nextTask.timeoutId) {
				clearTimeout( nextTask.timeoutId );
			}
			if ( nextTask.task ) {
				nextTask.timeToNextRun = nextTask.task._state.timeForNextRun - new Date().getTime();
				if ( typeof nextTask.timeToNextRun !== 'number' || isNaN( nextTask.timeToNextRun ) || nextTask.timeToNextRun < 5000 ) {
				 	nextTask.timeToNextRun = 5000;
				}
				nextTask.timeoutId = setTimeout( process, nextTask.timeToNextRun );
			}
		}
		/**
		* taskList contains all tasks that have been started, indexed by a unique taskId for each task.
		* tasks can be in the taskList without being in the queue, but never the other way around.
		*/
		,taskList = {}
		/**
		* @protected
		* @method
		* compares two tasks to see which should run first.
		* used as the compare function for the sfw.ADT.priorityQueue instance queue.
		*/
		,compareTasks = function( a, b ) {
			return a._state.timeForNextRun < b._state.timeForNextRun  ? -1 : b._state.timeForNextRun < a._state.timeForNextRun ? 1 : 0 ;
		}

		/**
		* @protected
		* PriorityQueue containing taskId's for the tasks.
		* shortest time to next run is the first in the queue.
		* tasks can be in the taskList without being in the queue, but never the other way around.
		*/

		,queue = new sfw.ADT.PriorityQueue( {
			compare: compareTasks
		})
		/**
		* @protected
		* @method
		* returns a unique task id
		*/
		,uniqueId = (function() {
			var i=0;
			return function (task) {
				return 'task_'+( ++i );
			}
		})()
		/**
		* @protected
		* @method
		* finds the timestamp when the supplied task needs to run.
		* @return {Number} timestamp for next execution.
		*/
		,updateNextRun = function (task) {
			var nextRun,
				lastRun;
			if (task._state.lastRunStartTime || task.firstrun===undefined) {
				lastRun = task._state.lastRunStartTime || task._state.startTime;
				if ( Ext.isFunction( task.interval ) ) {
					nextRun = task.interval();
					if ( Ext.isDate( nextRun ) ) {
						nextRun = nextRun.getTime();
					} else if ( Ext.isNumber( nextRun ) ) {
						nextRun = lastRun + nextRun;
					} else {
						nextRun = lastRun + self.DEFAULT_INTERVAL;
					}
				} else if ( Ext.isNumber( task.interval ) ) {
					nextRun = lastRun + task.interval;
				}
			} else {
				lastRun = task._state.startTime;
				if ( Ext.isFunction( task.firstrun ) ) {
					nextRun = task.firstrun();
					if ( Ext.isDate( nextRun ) ) {
						nextRun = nextRun.getTime();
					} else if ( Ext.isNumber( nextRun ) ) {
						nextRun = lastRun + nextRun;
					} else {
						nextRun = lastRun + self.DEFAULT_INTERVAL;
					}
				} else if ( Ext.isNumber( task.firstrun ) ) {
					nextRun = lastRun + task.firstrun;
				}
			}
			if ( !task.noLog ) {
				if (typeof task.name ==='undefined' || task.name === "") {
					sfw.debug.log( 'Task: <not named>, internal scheduled to:'+new Date( nextRun ) );
				} else {
					sfw.debug.log( 'Task:\''+task.name+'\', internal scheduled to:'+new Date( nextRun ) );
				}
			}
			task._state.timeForNextRun = nextRun;
		}
		,beforeTaskRun = function () {
			var state = this._state;
			state.lastRunStartTime = new Date().getTime();
			updateNextRun( this );
			state.queue.reQueue();
			if ( this.concurrencyLevel > 0) {
				if (state.simultaneousRuns < this.concurrencyLevel) {
					state.simultaneousRuns++;
					return true;
				}
				state.skippedCount++;
				return false;
			} else {
				state.simultaneousRuns++;
				return true;
			}
		}
		,afterTaskRun = function(rt) {
			var state = this._state,
				nTask;

			state.simultaneousRuns--;
			state.currentDuration = state.lastRunStartTime - state.startTime;
			if ( rt === false ) {
				self.stop( this );
			} else if ( taskList[state.id] ) {
				if ( this.repeat && this.runCount >= this.repeat ) {
					// the task have run the requested number of times, so remove it.
					self.stop( this );
				} else if ( this.duration && state.currentDuration > this.duration) {
					// the task have been active for the requested duration, so remove it.
					self.stop( this );
				} else 	if ( !state.queue.isQueued() ) {
					state.queue.reQueue();
				}
			}
			if ( nTask = queue.peek() && nextTask.timeoutId===0) {
				startProcessTimeout( nTask );
			}
		}
		/**
		* @protected
		* @method
		* function that is called each time we need to run a task
		* it also finds the next task to run and perform cleanup of removed (stopped) tasks
		*/
		,process = function() {
			var task = queue.remove(),
				nTask,
				state,
				startTaskRunner = false;

			nextTask.timeoutId = 0;
			if ( task ) {
				state = task._state;
				if ( beforeTaskRun.apply( task ) ) {
					startTaskRunner = ( runningTasks.length === 0 );
					runningTasks.push( task );
					if ( startTaskRunner ) {
						taskRunner.start( runTasksTask );
					}
				}
			}
			if ( nTask = queue.peek() ) {
				startProcessTimeout( nTask );
			}
		};


	self.runningTasks = runningTasks;
	self.taskList = taskList;
	self.nextTask = nextTask;
	self.queue=queue;
//	self.queue.debugMode();


	/**
	* @method
	* schedule a task for execution
	* @see sfw.tasks.Task
	*/
	self.start = function( task ) {
		var result = false,
			state = task._state;
		if ( !state || !state.startTime) {
			var now = new Date().getTime();
			state = Ext.applyIf(task._state, {
				created: now
				,id: uniqueId()
				,startTime: now
				,simultaneousRuns: 0
				,skippedCount: 0
				,runCount: 0
			});
			taskList[ state.id ] = task;
			if (task.async) {
				task.callback = afterTaskRun.createDelegate(task);
			}
			updateNextRun(task);
			if (state.queue) {
				state.queue.reQueue();
			} else {
				state.queue = queue.insert( task );
			}
			// if the new task is set to run before the current next task is set to run (or if there were no task), then we need to update the setTimeout
			if ( state.queue.isTopPriority() ) {
				startProcessTimeout(task);
			}
			result = true;
		}
		return result;
	};

	/**
	* @method
	* stop a task
	*/
	self.stop = function( task ) {
		var result = false,
			state = task._state;
		if ( state && !state.removedTime) {
			if ( taskList[ state.id ] ) {
				delete taskList[ state.id ];
			}
			state.queue.kill();
			state.removedTime = new Date().getTime();
			result = true;
		}
		return result;
	};

	self.changeInterval= function(task, interval) {
		var result = false,
			state = task._state;
		if ( state && state.queue && state.queue.isQueued()) {
			state.queue.kill();
			task.interval = interval;
			state.queue.reQueue();
		} else {
			task.interval = interval;
		}
	};

	self.stopAll = function() {
		var task,item;
		killTaskTimer();
		while ( item = queue.remove() ) {
			item._state.removedTime =  new Date().getTime();
			delete taskList[item._state.id];
		}
		idOfNextTask = undefined;
	};

	return self;
})();
