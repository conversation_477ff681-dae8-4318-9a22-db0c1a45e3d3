/**
* Abstract class for initializing, creating, adding, deleting and updating tasks of similar nature
*/
(function() {

	function removeTaskById( id ) {
		var task = this.tasks.get( id );
		task.stop();
	}

	/**
	 * @class sfw.tasks.TaskGroup
	 * Task group for initializing, creating, adding, deleting and updating tasks of similar nature
	 * @mixins sfw.tasks.Task
	 */
	sfw.tasks.TaskGroup = Ext.extend(Ext.util.Observable, {
		/**
		* @protected
		* @property
		* the tasks in this Task Group
		*/
		tasks: new Ext.util.MixedCollection( false )

		/**
		* @protected
		* @property
		* the data for tasks in this Task group
		*/
		,taskData: new Ext.util.MixedCollection( false )
		/**
		* @constructor
		*
		*/
		/**
		* @cfg {Function} getKey (optional) Function that takes a record and returns the key for the record.
		*/
		/**
		* @cfg {Function} run (required) Function ( data ) or Function ( data, callback ) depending on async
		* if the function returns false the task will be stopped
		* for async the callback can be called with a false parameter to stop the task.
		*/
		/**
		* @cfg {<PERSON><PERSON><PERSON>} [async=false] is the run function an async function
		* note that if it is it will be called with a callback t
		*/

		,constructor: function ( config ) {
			var run;
			if ( config.getKey ) {
				this.taskData = new Ext.util.MixedCollection( false, config.getKey );
				delete config.getKey;
			}
			if (!config.run) {
				config.run = Ext.emptyFn;
			}
			Ext.apply( this, config );
			sfw.tasks.TaskGroup.superclass.constructor.call(this);
		}
		/**
		* takes a record and makes a task
		* that is then scheduled right away according to properties
		*/
		,add: function( taskData ) {
			var task = false;
		 	if ( this.taskData.contains( taskData ) !== true) {
				this.taskData.add( taskData );
				// new unknown task
				task = this.createTask( taskData );
				this.tasks.add( task );
			}
			return task;
		}
		,update: function( taskData ) {
			// get the key
			var key = this.taskData.getKey( taskData );
			var task = this.tasks.removeKey( key );
			this.taskData.removeKey( key );
			if ( task ) {
				task.stop();
				this.add( taskData );
			}
		}
		/**
		 * @method
		 * Checks if a Task exists matching the specified Task data
		 * @public
		 * @param {Object} taskData
		 * @return {Boolean}
		 *	True if task exists otherwise False.
		 */
		,has: function( taskData ) {
			var key = this.taskData.getKey( taskData );
			return this.tasks.containsKey( key );
		}
		,removeKey: function( key ){
			var taskData = this.taskData.removeKey( key );
			var task = this.tasks.removeKey( key );
			if ( task ) {
				task.stop();
			}
			return task;
		}
		/**
		* function that creates a task from a basic record
		* default is to create a normal Task
		*/
		,createTask: function( taskData ) {
			var me = this;
			// we set the async flag in the task
			// this means that the taskmanager adds a .callback to the task that we have to remember to call when we are done processing the run
			var config = {
				async: this.async
				,id: taskData.id
				// run will be called in the scope of the task we create below
				,run: function() {
					return me.run( this.id );
				}
			};
			if ( Ext.isString(taskData.name) ) {
				config.name = taskData.name;
			} else if (Ext.isFunction(this.getTaskName) ) {
				config.name = this.getTaskName( taskData );
			}

			if (Ext.isFunction( this.makeInterval ) ) {
				config.interval = me.makeInterval( taskData );
			}
			if (Ext.isFunction( this.getFirstRun )) {
				config.firstrun = this.getFirstRun( taskData );
			}
			return new sfw.tasks.Task( config );
		}
	});
})();
