<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<!--Style-->
<link rel="stylesheet" type="text/css" href="{extjs_path}resources/css/ext-all.css" />
<link rel="stylesheet" type="text/css" href="{extjs_path}resources/css/xtheme-gray.css" />
<!--User defined CSS files-->
{css_files}
<!--Adapters-->
{adapters}
<!--ExtJS Core-->
<script type="text/javascript" src="{extjs_path}adapter/ext/ext-base.js"></script>
<script type="text/javascript" src="{extjs_path}{extjs_lib}"></script>
<!--User defined JavaScript files-->
{javascript_files}
<!--Project JS-->
<script type="text/javascript">
Ext.BLANK_IMAGE_URL = '{extjs_path}resources/images/default/s.gif';

var sfw = {};
{javascript_code}
document.oncontextmenu = function() {
	return false;
}
Ext.onReady( function() {
	// Get the token passed in and call our getStaticDash function
	if ( sfw.HTTP_GET_VARS.token ) {
		globals.getStaticDash( sfw.HTTP_GET_VARS.token );
	}
});
</script>
<title>{page_title}</title>
</head>

<body>
</body>

</html>
