<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<!--Meta-->
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<meta name="robots" content="noindex" />
<!--Favorite icon-->
<link rel="icon" type="image/x-icon" href="/favicon.ico" />
<!--Style-->
<link rel="stylesheet" type="text/css" href="{extjs_path}resources/css/ext-all.css" />
<link rel="stylesheet" type="text/css" href="{extjs_path}resources/css/xtheme-{theme}.css?v={fversion}" />
<!--User defined CSS files-->
{css_files}
<!--Adapters-->
{adapters}
<!--ExtJS Core-->
<script type="text/javascript" src="{extjs_path}adapter/ext/ext-base.js"></script>
<script type="text/javascript" src="{extjs_path}{extjs_lib}"></script>
<!--User defined JavaScript files-->
{javascript_files}
<!--sfw.external must exist before any user code-->
<script type="text/javascript">var sfw={external:{}};
Ext.BLANK_IMAGE_URL = '{extjs_path}resources/images/default/s.gif';
</script>
<title>{page_title}</title>
</head>

<body>
{custom_html}
<!--Project JS-->
<script type="text/javascript">
{javascript_code}
document.oncontextmenu = function() {
	return false;
}

Ext.onReady(function() {
	if( typeof sfw.isSccmPlugin === 'undefined') {
		Ext.select('.passwordField').set({'placeholder' : "Password" });
		Ext.select('.oldPasswordField').set({'placeholder' : "Old Password" });
		Ext.select('.newPasswordField').set({'placeholder' : "New Password" });
		Ext.select('.confPasswordField').set({'placeholder' : "Confirm Password" });
		Ext.select('.login-form').set({'color' : "#fff" });
	}
});

</script>
</body>


</html>

