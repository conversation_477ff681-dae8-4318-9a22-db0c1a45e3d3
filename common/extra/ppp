#!/usr/bin/php -nd memory_limit=512M
<?php
// Following header can be used on dev1 to get faster compilation
// #!/usr/bin/php -d memory_limit=512M -n
//
// ppp - a simple code pre-processor (intended for use with PHP, but will work
// for any scripting language.
//
// ppp <src> <dst> BLOCK1, [BLOCK2, [BLOCK3, [...]]]
//
// Using <src> as the input file, write its contents to <dst> after removing all
// lines between starting `#ifdef <BLOCK>' and closing `#endif' tags.
//
// For example, given the following file index.php:
//
//     #ifdef TOPSECRET
//         include ("/etc/passwd");
//     #endif
//
//     echo "This file has no more secrets after being stripped.\n";
//
// The following invocation:
//
//     ppp index.php index_stripped.php TOPSECRET
//
// would result in the following contents in index_stripped.php:
//
//     echo "This file has no more secrets after being stripped.\n";
//

function usage($prog) {
	echo "Usage: $prog [-Dmacro...] [-f] infile outfile\n";
	exit(1);
}

// Must be at least 3 arguments
if ($argc < 2)
	usage($argv[0]);

// Second to the last argument is the infile
$infile = $argv[$argc-2];

// Last argument is the outfile
$outfile = $argv[$argc-1];

// Save MACRO statements into an array, look for -f (force) option
$macros = array();
$macro = 0;
$overwrite = 0;
for ($i=0; $i<$argc; $i++) {

	// Look for the force flag
	if($argv[$i] == "-f") {
		$opt_force = 1;
		continue;
	}

	if ($argv[$i] == "-y") {
		$overwrite = 1;
		continue;
	}

	if($argv[$i] == "-D") {
		$macro = 1;
		continue;
	}

	// If previous argument was "-D"
	if($macro || preg_match("/^-D/", $argv[$i])) {
		array_push($macros, preg_replace("/^-D/", "", $argv[$i]));
		$macro = 0;
	}
}

// Read in the contents of the source file
if(!$src = file($infile)) {
	echo $infile . ": Unable to open file\n";
	usage($argv[0]);
}

// Determine whether or not it's ok to overwrite existing dst
if (file_exists($outfile) && empty($opt_force) && ($overwrite==0) && ($argc!=2)) {
	echo basename($argv[0]) . ": overwrite `$outfile'? ";

	$stdin = fopen("php://stdin", "r");

	if(!preg_match("/^[Yy]$/", trim(fgets($stdin))))
		exit(0);

	fclose($stdin);
}

// To keep track of whether we're inside of a #ifdef block or not
$in_ifdef = 0;

// Keep track of line numbers
$line_no = 0;

// Keep track of where the last #ifdef block started
$line_no_last_ifdef = 0;

// Used to tell whether the current line of code should be included or not
$include_code = 1;

// Write contents of file to a temporary file to ensure that if any errors are
// encountered and we're overwriting an existing file, the existing file is
// left unchanged

$tmp_file = "/tmp/" . basename($outfile) . ".tmp";

if(!$dst = fopen($tmp_file, "w")) {
	echo "$tmp_file: Unable to open temporary file for writing\n";
	exit(1);
}

// Split the file on "\n"'s and preserve them using DELIM_CAPTURE
foreach( $src as $line ) {
	$line_no++;

	// Skip line, which will be the "\n" following an #ifdef, #else, or #if
	if(!empty($skip_next_newline) == 1) {
		$skip_next_newline = 0;
		if($line == "\n")
			continue;
	}

	// Is the line #ifdef?
	if ( substr( $line, 0, 6 ) == '#ifdef' ) {
		$matches = trim(substr( $line, 6 ));

		// Check if we're already inside one, nested ifdef's aren't supported
		if($in_ifdef) {
			fclose($dst);
			unlink($tmp_file);
			echo $infile . ": Nested #ifdef not supported (line $line_no, " .
			    "last #ifdef started on line $line_no_last_ifdef)\n";
			exit(1);
		} else {
			$in_ifdef = 1;
			$line_no_last_ifdef = $line_no;

			if(in_array($matches, $macros))
				$include_code = 1;
			else
				$include_code = 0;
		}

		// Don't include #ifdef in output file
		$skip_next_newline = 1;
		continue;
	} else if ( substr( $line, 0, -1 ) == '#else' ) {
		// Include this content if we didn't include content from #ifdef
		$include_code = !$include_code;

		// Don't include #else in output file
		$skip_next_newline = 1;
		continue;
	} else if ( substr( $line, 0, -1 ) == '#endif' ) {
		// Found an #endif, we must be inside an #ifdef, otherwise exit
		if(!$in_ifdef) {
			fclose($dst);
			unlink($tmp_file);
			echo $infile . ": #endif found without matching #ifdef " .
			    "(line $line_no_last_ifdef)\n.";
			exit(1);
		} else {
			$in_ifdef = 0;
			$include_code = 1;
		}

		// Don't include #endif in output file
		$skip_next_newline = 1;
		continue;
	}

	// Write the content to the file
	if($include_code) {
		if(fwrite($dst, $line) === false) {
			fclose($dst);
			unlink($tmp_file);
			echo "$tmp_file: Cannot write to file\n";
			exit(1);
		}
	}
}

if ($in_ifdef) {
	fclose($dst);
	unlink($tmp_file);
	echo $infile . ": #ifdef found without matching #endif " .
	    "(line $line_no_last_ifdef)\n.";
	exit(1);
}

if(@!rename($tmp_file, $outfile)){
	unlink($tmp_file);
}
?>