<?php

  /**
   * @file recipients.class.php
   *
   * Provides VIM's Recipients functionality i.e. the recipients that are associated with
   * the asset lists and receive advisory notifications.
   */

class CONTACTS {

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Fetch account contacts information.
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Array having contact information: total -> total number of contacts, used -> number of used accounts, assigned -> number of assigned contacts, available -> number of available contacts
	 */
	function accountContactInfo( $accountId ) {
		$info = array(
			'total'	=> 0
			,'used'		=> 0
			,'assigned'	=> 0
			,'available'	=> 0
		);

		if ( !$accountId ) {
			return $info;
		}

		// Get account information for the user
		// Get number of users
		$account = $this->database['ca']->getRow( "ca.esm", "master_account_id = '".(int)$accountId."'" );

		// Add contacts that have been assigned to the user
		$info['total'] = (int)$account['no_users']; // If 0, reserve one for himself
		if ( $info['total'] == 0 ) {
			$info['total'] = 1;
		}

		// How many contacts have been used already (excluding the contact associated with the account)?
		$query = "SELECT * FROM ca.contact_method WHERE account_id = '" . (int)$accountId . "' GROUP BY pos";
		$info['used'] = $this->database['ca']->queryNumRows( $this->database['ca']->query( $query ) ) - 1;

		if( $info['used'] < 0 ) { // Count how many have been used
			$info['used'] = 0;
		}

		// How many of these have been assigned to sub-users?
		$sub = $this->database['ca']->getRows( "ca.accounts", "account_esm = '".(int)$accountId."'" ); // Count reserved

		foreach ($sub as $row) {
			// Get number of users, for sub accounts
			$subUserInfo = $this->database['ca']->getRow( "ca.esm", "master_account_id = '".(int)$row['account_id']."'" );
			$subUserInfo['no_users'] = (int)$subUserInfo['no_users'];
			if ( $subUserInfo['no_users'] == 0 ) {
				$subUserInfo['no_users']++;
			}
			$info['assigned'] += $subUserInfo['no_users'];
		}

		// And finally, how many contacts can be assigned by this user to other users?
		$info['available'] = $info['total'] - $info['assigned'] - $info['used'];

		if ( $info['available'] < 0 ) {
			$info['available'] = 0;
		}

		$GLOBALS['debug']->notice( "Account contacts information fetched" );
		return $info;
	}

	/**
	 * Function for flushing the account info.  We add the functionality to return the row IDs we are deleting for both
	 * the Email and SMS contact rows.
	 *
	 * @return (OPTIONAL) Array
	 *   If $returnIdArray is set, we return an array [x,y] representing IDs for email/SMS respectively, 0 if it's not there.
	*/
	public function flushAccountInfo( $accountId, $position, $returnIdArray = false ) {

		$condition = "account_id = '" . (int) $accountId . "' AND pos = '" .(int) $position . "' ";
		if ( true == $returnIdArray ) {
			$returnArray = array();
			$rowId = $this->database['ca']->getRowValue( 'ca.contact_method'
														 ,'contact_method_id'
														 ,$condition . " AND contact_method_type = 1" );
			$returnArray[] = ( $rowId ? (int) $rowId : 0 );

			$rowId = $this->database['ca']->getRowValue( 'ca.contact_method'
														 ,'contact_method_id'
														 ,$condition . " AND contact_method_type = 2" );
			$returnArray[] = ( $rowId ? (int) $rowId : 0 );
		}

		// Can be up to 2 entries - email and sms, but don't limit query
		// Anything else in there is bad data and should be deleted anyway
		$query = "DELETE FROM ca.contact_method WHERE " . $condition;
		$this->database['ca']->query( $query );

		$query = "DELETE FROM ca.processlist_contact_rel WHERE " . $condition;
		$this->database['ca']->query( $query );

		$GLOBALS['debug']->notice( "Account contact data flushed" );

		if ( true == $returnIdArray ) {
			return $returnArray;
		}
	}

	private function flushGroups( $accountId ) {
		// Clean orphan groups
		$result = $this->database['ca']->queryGetRows("SELECT * FROM ca.contact_groups WHERE account_id = '".(int)$accountId."'");
		for ( $i = 0; $i < count( $result ); $i++ ) {
			if ( $this->database['ca']->numRows( "ca.contact_method", "account_id = '".(int)$accountId."' AND group_id = '".(int)$result[$i]['group_id']."'" ) == 0 ) {
				$this->database['ca']->query("DELETE FROM ca.contact_groups WHERE account_id = '".(int)$accountId."' AND group_id = '".(int)$result[$i]['group_id']."' LIMIT 1");
			}
		}
	}

	/**
	 * Function for updating a contact.  Note, we must retain a contact's id if it already exists.
	 *
	 * @param accountId
	 *	Integer account id
	 *
	 * @param values
	 *	Array of values to be added to the database. Array should have these keys:
	 *       name, contact_email, email_xml, alt_lang_id, lang_eng, monthly_report, contact_method_critical, pos
	 *
	 * @param isEscaped
	 *	Boolean true or false if data is SQL escaped or not
	 *
	 * @return
	 *	Mixed false if the user has no more available contacts or id of the new contact.
	 * NOTE: Language Logic description:
	 * NOTE: IF ( ADVISORY LANGUAGE _NOT_ ENGLISH --ALT_LANG_ID NOT 1-- ) THEN ( ADD NEW CONTACT HAVING alt_lang_id = value AND ( IF lang_eng = 1 APPEND lang_eng = 1 ) )
	 * NOTE: IF ( ADVISOTY LANGUAGE _IS_ ENGLISH --ALT_LANG_ID = 1-- ) THEN ( ADD NEW CONTACT HAVING alt_lang_id = 0 AND lang_eng = 1 )
	 * NOTE: Mobile Logic description:
	 * NOTE: IF ( MOBILE ) THEN ( ADD NEW CONTACT HAVING contact_method_type 2 AND contact_method_value MOBILE AND name = SAME AS PRIMARY )
	 * NOTE: IF ( ( !MOBILE ) AND ( MOBILE IN DB ) ) THEN ( DELETE MOBILE CONTACT HAVING contact_method_type = 2 AND name = SAME AS PRIMARY ) )
	 *
	 * @todo: rename the function to addOrEditContact()
	*/
	function addOrEditRecipient( $accountId, $values, $isEscaped = false ) {
		// Flush existing data
		$info = $this->accountContactInfo( $accountId );

		// Check if group already exists
		$group_id = $this->database['ca']->getRowValue( "ca.contact_groups"
						,"group_id"
						,"group_id = '" . (int) $values['group_id'] . "' AND account_id = '" . (int) $accountId."'" );

		if ( $group_id == "" ) { // We have a new group, or selected group does not belong to current user! Add new group.
			$values['group_id'] = $this->database['ca']->edit( "ca.contact_groups"
										,array(	"name" => $values['group_id']
												,"account_id" => (int)$accountId )
										,0, "", $isEscaped );
		}

		if ( $values['pos'] > $info['total'] ) {
			$GLOBALS['debug']->error( "Account contacts add failed ( attempt to add a values greater than the upper limit. Either misuse or UI out of Synch. )" );
			return false;
		}

		if ( !is_numeric( $values['pos'] ) ) {
			// Should not happen unless UI misuse
			$GLOBALS['debug']->error( "Account contacts add failed ( Unknown Error )" );
			return false;
		}

		// Deal with if we are editing a current row.  We still want to delete the rows because we might, for example, be
		// removing an SMS number and keeping the email address, in which case we do want to delete the SMS row.  The difference
		// will just be that where we are editing an existing row, we will insert back into the DB with the same ID

		$editing = false;
		if ( $values['contact_method_id'] != "" ) {
			$editing = true;
			$idArray = $this->flushAccountInfo( $accountId, $values['pos'], true );
			if ( isset($idArray[0]) && 0 != $idArray[0] ) {
				$emailId = $idArray[0];
			}
			if ( isset($idArray[1]) && 0 != $idArray[1] ) {
				$smsId = $idArray[1];
			}
		} else {
			$this->flushAccountInfo( $accountId, $values['pos'] );
		}
		unset( $values['contact_method_id'] ); // remove this entirely - now we only add it when required (editing existing)

		// Language logic:
		if ( $values['alt_lang_id'] == 1 ) {
			$values['alt_lang_id'] = 0;
		}

		// Mobile logic
		if ( $values['mobile'] != "" ) {
			$mobileValues = array( "name" => $values['mobile'] // Note - (todo?) this should probably be either 'name' or empty - not fucntional for the mobile one anyway - we can referecen it by its position value and accountId.  Leave for now in case it is being used in an unexpected way elsewhere...
								   ,"contact_method_type" => 2
								   ,"contact_method_value" => $values['mobile']
								   ,"pos" => $values['pos']
								   ,"account_id" => (int)$accountId
								   ,"contact_method_critical" => $values['contact_method_critical'] );

			if ( $editing && 0 != $smsId ) {
				$mobileValues["contact_method_id"] = $smsId;
			}

			$this->database['ca']->edit( "ca.contact_method", $mobileValues, 0, "", $isEscaped );
			$GLOBALS['debug']->notice( "Account SMS data added" );
		}
		unset( $values['mobile'] );

		// Normalize
		$values['contact_method_type'] = 1;
		$values['contact_method_value'] = $values['contact_email'];
		$values['contact_method_critical'] = $values['contact_method_critical_email'];
		unset( $values['contact_method_critical_email'] ); // Normalize for DB
		$values['account_id'] = (int)$accountId;
		$assetId = $values['assetId'];
		unset( $values['assetId'] );
		unset( $values['contact_email'] );

		if ( $editing && 0 != $emailId ) {
			$values["contact_method_id"] = $emailId;
		}

		$contactId = $this->database['ca']->edit( "ca.contact_method", $values, 0, "", $isEscaped );

		// Store asset id data in processlist_contact_rel
		$temp = explode( ",", $assetId );
		for ( $i = 0; $i < count( $temp ); $i++ ) {
			if ( $temp[$i] != "" ) {
				$this->database['ca']->edit( "ca.processlist_contact_rel"
											 ,array( "account_id" => $accountId
													 ,"pos" => $values['pos']
													 ,"asset_id" => (int)$temp[$i] )
											 ,0, "", $isEscaped );
			}
		}

		$this->flushGroups( $accountId );
		$GLOBALS['debug']->notice( "Account contact added" );

		return $contactId;
	}

	/**
	 * Get profile position.
	 * @param accountId
	 *	Integer account id
	 * @param contactId
	 *	Integer contact id
	 * @return
	 *	Mixed false if failed, position if success
	*/
	public function getPosition( $accountId, $contactId ) {
		$position = $this->database['ca']->getRowValue( "ca.contact_method", "pos", "account_id = '".(int)$accountId."' AND contact_method_id = '".(int)$contactId."'" );
		if ( $position == "" ) {
			return false;
		} else {
			return $position;
		}
	}

	/**
	 * Get the next available position ( which might be the last one + 1, or the first available one in between )
	 * @param accountId
	 *	Integer account id
	*/
	public function getNextPosition( $accountId ) {
		// Get all position numbers
		$position = $this->database['ca']->queryGetRows( "SELECT pos FROM ca.contact_method WHERE account_id = '".(int)$accountId."' GROUP BY pos ORDER BY pos ASC" );

		// Find the lowest, below 0, otherwise start from position 0
		$x = 0; // Position variable
		for ( $i = 0; $i < count( $position );$i++ ){
			if ( $position[$i]['pos'] < $x ) {
				$x = $position[$i]['pos'];
			}
		}

		// See if any slot is empty
		for ( $i = 0; $i < count( $position ); $i++ ) {
			// If the current position, is different then the position in the array...
			if ( $position[$i]['pos'] != $x ) { // ...it is out of sequence...
				return $x; // ...and we use it.
			}
			$x++; // Increment the position variable ($x), which is not the same as the array position variable ($i)
		}

		// No position if out of synch, fetch the last one
		$position = $this->database['ca']->getRow( "ca.contact_method", "account_id = '".(int)$accountId."'", "POS DESC" );
		$position = $position['pos'];
		if ( $position == "" ) { // If user has not contacts defined, start from 0
			$position = 0;
		} else {
			$position++; // Otherwise increment the last position
		}

		return $position;
	}
}