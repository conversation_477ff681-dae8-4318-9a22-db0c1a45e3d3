<?php
/**
 * @file login.class.php
 * Provides generic login functionality. Children classes should provide project specific login functionality.
*/

/**
 * Login/logout class.
 * Database connection(s) shall be created in the local project, rather than the global project.
*/
abstract class LOGIN {
	const AUTH_TYPE_NORMAL = 1;
	const AUTH_TYPE_LDAP = 2;

	/**
	 * Whether the session is writable or not
	 * @var bool
	 */
	private $isWritable;

	function __construct() {
		session_cache_limiter('public');
		session_start();
		$this->isWritable = true;
		$GLOBALS['debug']->notice( "Session started" );
	}

	abstract function login();
	abstract function logout();

	/**
	 * Function for checking if user is logged in.
	 * @return
	 *	Boolean true or false if logged in or not
	*/
	function checkLogin() {
		if ( isset( $_SESSION['isLoggedIn'] ) ) {
			if ( ( $_SESSION['isLoggedIn'] == true ) && ( COOKIE::getCookieValue('token') == $this->getSessionValue('sessionId') ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Return a value stored in the session
	 * @param keyName
	 *	String name of the entry
	 * @return
	 *	String entry value
	*/
	function getSessionValue( $keyName ) {
		if ( isset( $_SESSION[$keyName] ) ) {
			return $_SESSION[$keyName];
		} else {
			return null;
		}
	}

	/**
	 * Set a value in the session array
	 * @param keyName
	 *	String entry name
	 * @param value
	 *	Mixed, entry value
	*/
	function setSessionValue( $keyName, $value ) {
		$_SESSION[$keyName] = $value;
	}

	/**
	 * Function for generating seed, fo the session id
	 * @return
	 *	String random seed
	*/
	private function makeSeed() {
		list( $USec, $sec ) = explode(' ', microtime() );
		return (float) $sec + ( (float) $USec * 100000 );
	}

	/**
	 * Function for generating the random session.
	 * @return
	 *	String md5 encoded session id
	*/
	protected function generateSessionId() {
		$salt = 'ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789';
		$randomString = '';
		for( $i = 0; $i < 64; $i++ ) {
			srand( $this->makeSeed() );
			$randVal = rand( 0, 59 );
			$randomString .= $salt[$randVal];
		}
		return md5( $randomString );
	}

	protected function cookieTime( $values ) {
		if ( isset( $values['time'] ) && $values['time'] != 0 ) {
			$cookieExpire = 60 * $values['time'];
		} else {
			$cookieExpire = 60 * 120; // Default time to 120 minutes
		}
		return $cookieExpire;
	}

	/**
	 * Function for checking the session id, sent by the client.
	 * @param ignoreAnswer
	 *	Boolean default to, if to ignore the Ajax answer ( useful when performing a logout )
	 * @return
	 *	Boolean true if session is all right, else die ( possible XSRF )
	*/
	public function checkSession( $ignoreAnswer = false, $updateCookieExpiration = true ) {
		if ( $this->getSessionValue('sessionId') != COOKIE::getCookieValue('token') || $this->getSessionValue('sessionId') == null || $GLOBALS['input']->MGET('token') != $this->getSessionValue('sessionId') ) {
			$GLOBALS['debug']->error( "Invalid session id (LOGIN::checkSession)" );
			if ( $ignoreAnswer == false ) {
				die("Please login.");
			} else {
				die();
			}
		} else {
			if ( $updateCookieExpiration ) {
				// Update Cookie Expiration
				COOKIE::setCookieValue("token", $_SESSION['sessionId'], $this->cookieTime( $_SESSION ) );
			}
			return true;
		}
	}

	/**
	 * Function for storing an _INITIAL_ array of values in the SESSION array AND setting the session id cookie.
	 * @param values
	 *	Array of values
	*/
	protected function setSessionData( $values ) {
		$values['sessionId'] = $this->generateSessionId();
		$cookieExpire = $this->cookieTime( $values );
		COOKIE::setCookieValue("token", $values['sessionId'], $cookieExpire );
		$_SESSION = $values;
		$GLOBALS['debug']->notice( "Session data saved" );
	}

	/**
	 * Destroy current session AND delete cookies.
	*/
	protected function destroySession() {
		COOKIE::deleteCookie("token");
		session_unset();
		session_destroy();
		$this->isWritable = false;
		$GLOBALS['debug']->notice( "Session data destroyed" );
	}

	/**
	 * Close the Session for writing. It will still be readable.
	 */
	public function closeSessionForWriting()
	{
		session_write_close();
		$this->isWritable = false;
	}

	/**
	 * Checks the credentials in the database.
	 * Since, this is a static function, it needs a link to the database i.e. a db object
	 *
	 * The function takes raw unescaped values.
	 *
	 * @param db
	 *	Object The database object
	 * @param username
	 *	String username
	 * @param password
	 *	String password
	 * @param specialLimits
	 *	String | Array, A single or array of special limits.
	 * @param onlyInternal
	 *	Boolean, Only checks if the account can be authenticated internally
	 *
	 * @return
	 *	Boolean/Integer false if fails, account data if data is valid i.e. account_id, authentication type etc
	 */
	static function authenticate( $db, $username, $password, $specialLimits, $onlyInternal = false ) {
		if ( empty( $username ) ) {
			return false;
		}

		$escapedUsername = $db->escapeString( $username );
		$escapedPassword = $db->escapeString( $password );
		$escapedSpecialLimits = "";

		if ( is_string( $specialLimits ) ) {
			$escapedSpecialLimits = $db->escapeString( $specialLimits );
		} else if ( is_array( $specialLimits ) ) {
			foreach( $specialLimits as $limit ) {
				if ( $escapedSpecialLimits !== "" ) {
					$escapedSpecialLimits .= "','";
				}
				$escapedSpecialLimits .= $db->escapeString( $limit );
			}
		} else {
			return false;
		}

		$externalAuthCondition = "";
		if ( !$onlyInternal ) {
			$externalAuthCondition = " OR auth_type = '" . (int) LOGIN::AUTH_TYPE_LDAP . "' ";
		}

		$query = "SELECT account_id
				,auth_type
				,cst_id
				,account_gen_pwd
				,last_login
				,account_expires
				,uid
				,token
				,special_limits
				,partition_id
			FROM ca.accounts
			WHERE account_username = '" . $escapedUsername . "'
				AND account_gen_pwd < 2
				AND account_expires > NOW()
				AND ( account_password = BINARY PASSWORD('" . $escapedPassword . "') " . $externalAuthCondition . ")
				AND special_limits IN ('" . $escapedSpecialLimits . "')";

		$result = $db->queryGetRows( $query );

		if ( count( $result ) === 1  ) {
			return $result[0];
		}

		return false;
	}

	/**
	 * Authenticate the user and fetch account information.
	 *
	 * @param db
	 *	Object The database object
	 * @param username
	 *	String username
	 * @param password
	 *	String password
	 * @param onlyInternal
	 *	Boolean, Only checks if the account can be authenticated internally
	 * @param excludeShadow
	 *	Boolean, Exclude the shadow accounts from the verification process (for CSI-VIM integration)
	 *
	 * @return
	 *	Boolean/Integer false if fails, account data if data is valid i.e. account_id, authentication type etc
	 */
	static function vimAuthenticate( $db, $username, $password, $specialLimits, $onlyInternal = false, $excludeShadow = false ) {
		$accountData = LOGIN::authenticate( $db, $username, $password, $specialLimits, $onlyInternal, $excludeShadow );
		return $accountData;
	}
}