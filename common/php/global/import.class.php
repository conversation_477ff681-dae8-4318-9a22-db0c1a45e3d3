<?php
/**
 * @file import.class.php
 * @todo: This file contains VIM specific functionality and should go to the VIM dir.
 */

/**
 * Provides software import functionality.
 * Requires: database.class.php, products.class.php, json.class.php and util.class.php
 */
class IMPORT {

	private $db;
	private $accountId;

	// Constants

	// File Error Constants
	const UNKNOWN = -1;
	const SUCCESS = 0;
	const BIG_FILE = 1;		// File size too big
	const EMPTY_FILE = 2;	// File is empty
	const INVALID_INPUT = 3;	// File is empty

	// Data Types
	const PRODUCT_NAMES = 0;
	const PRODUCT_IDS = 1;
	const CPE_NAMES = 2; // to be used in future
	const NVD_IDS = 3; // to be used in future

	function __construct( DB $db, $accountId ) {
		$this->db = $db;
		$this->accountId = $accountId;
	}

	/**
	 * Format data for the Json Encode function, in pairs of [id] and [name]
	 * @param matchArray
	 *	Array of matches, default is 'matched'
	 * @param isNumeric
	 *	Boolean if true, it will process matched array type, otherwise unmatched array type
	*/
	private function buildForJson( $matchArray, $isNumeric = false ) {
		$matched = array();
		$i = 0;
		foreach ( $matchArray as $key => $value ) {
			if ( $isNumeric == false ) {
				foreach ( $value as $id => $name ) {
					// Double escape, since ExtJS runs eval() against this content!
					$matched[$i]['id'] = (int)$id;
					$matched[$i]['name'] = UTIL::htmlspecialchars( $name );
					$i++;
				}
			} else {
				// Double escape, since ExtJS runs eval() against this content!
				$matched[$i]['id'] = (int)$i;
				$matched[$i]['name'] = UTIL::htmlspecialchars( $value );
				$i++;
			}
		}
		return $matched;
	}

	private function storeMatchResults( array $results, $automaticSelection = false ) {
		/*
		 * We can't use the BulkInsert Wrapper for the following reasons:
		 * - The results array can be very large. There is a limit on the named params
		 *   that can be created.
		 * - The results array is in the form of a tree and needs to be converted.
		 * - Binding so many named params can cause performance problems.
		 *
		 * @todo: benchmark
		 */

		$selected = $automaticSelection ? 1 : 0;
		$query = '';
		foreach( $results as $input => $matches ) {
			if ( empty( $matches ) ) {
				if ( $query !== '' ) {
					$query .= ',';
				}
				$query .=
					'(' . (int) $this->accountId
					. ',' . $this->db->quote( $input )
					. ', NULL'
					. ', NULL'
					. ', 0'
					. ')';
			} else {
				foreach ( $matches as $osSoftId => $distance ) {
					if ( $query !== '' ) {
						$query .= ',';
					}
					$query .=
						'(' . (int) $this->accountId
						. ',' . $this->db->quote( $input )
						. ',' . (int) $osSoftId
						. ',' . (int) $distance
						. ',' . $selected
						. ')';
				}
			}
		}
		$query = 'INSERT INTO import_products ( account_id, input, matched_os_soft_id, distance, selected ) VALUES '
			. $query;

		$this->db->execRaw( $query );
	}

	function processCSV( $contents ) {
		$success = false;

		$delimiter = isset( $_POST['delimiter']) ? $GLOBALS['input']->MPOST('delimiter') : ',';
		$column = isset( $_POST['column']) ? $GLOBALS['input']->MPOST('column') : 0;
		// Data type to be processed
		$dataType = isset( $_POST['data_type'] ) ? $GLOBALS['input']->MPOST('data_type') : 0;

		// Check if the first row is required
		$header = $contents[0]; // storing the first line of the file in case it is needed later on
		if ( !isset( $_POST['header'] ) || !$GLOBALS['input']->MPOST('header') ) {
			unset( $contents[0] );
		}

		// Remove existing matches
		$this->deleteMatches();

		$fileContents = $GLOBALS['util']->parseCSVFile( $contents, $delimiter, $column );
		$automaticSelection = false;
		switch( $dataType ) {
		case self::PRODUCT_NAMES:
			$results = $GLOBALS['products']->productsSearch( $fileContents );
			break;
		case self::PRODUCT_IDS:
			$results = $GLOBALS['products']->productIdSearch( $fileContents );
			$automaticSelection = true;
			break;
		default:
			return false;
		}
		if ( !empty( $results ) ) {
			$this->storeMatchResults( $results, $automaticSelection );
			$success = true;
		}

		return $success;
	}

	/**
	 * Get the file contents of the file uploaded
	 */
	function getFileContents( ) {

		$response = array( "success" => false, "contents" => "",  "error_code" => self::UNKNOWN );

		// Get file contents
		$fileContents = $GLOBALS['util']->uploadFileToText( "file" );

		// The uploadFileToText() function is also returning new lines despite the fact that we are using the flag FILE_IGNORE_NEW_LINES.
		// For now manually removing the new lines
		// Also, we remove the double quotes which break the json response for some reason even through it is html encoded
		$size = count( $fileContents );
		for ( $i = 0; $i < $size; $i++ ) {
			$fileContents[$i] = rtrim( $fileContents[$i] );
			$fileContents[$i] = str_replace( "\"", "", $fileContents[$i] );
		}

		if ( $fileContents === false ) {
			$GLOBALS['debug']->error( "getFileContents() - File size too big" );
			$response['success'] = false;
			$response['error_code'] = self::BIG_FILE;
			return $response;
		}

		// Check if the file is empty
		if ( !$fileContents ) {
			$GLOBALS['debug']->error( "getFileContents() - No file contents" );
			$response['success'] = false;
			$response['error_code'] = self::EMPTY_FILE;
			return $response;
		}

		$GLOBALS['debug']->notice( "Fetched file contents" );
		$response['success'] = true;
		$response['error_code'] = self::SUCCESS;
		$response['contents'] = $fileContents;
		return $response;
	}

	/**
	 *
	 * Function for importing software from file.
	 *
	 * Requires _POST['file_type'] to be set in the calling form. Values: 0 - text file, 1 - csv file
	 */
	function importFile() {

		$fileData = $this->getFileContents();
		if ( !$fileData['success'] ) {
			return false;
		}

		$success = $this->processCSV( $fileData['contents'] );
		return $success;
	}

	function previewFile( $numOfLines ) {

		if ( !$numOfLines ) {
			$numOfLines = 19;
		}

		$response = array( "success" => false, "error_code" => self::UNKNOWN, "contents" => array() );

		$fileData = $this->getFileContents();
		if ( !$fileData['success'] ) {
			$response['success'] = false;
			$response['error_code'] = $fileData['error_code'];
			return $GLOBALS['json']->json_encode( $response );
		}

		$contents = array();

		for ( $i = 0; $i < min( count( $fileData['contents'] ), (int) $numOfLines ); $i++ ) {
			$contents[] =  $fileData['contents'][$i];
		}

		$response['success'] = true;
		$response['error_code'] = self::SUCCESS;

		// @todo: contents and header should be encapsulated in 'data'
		$response['contents'] = $contents;
		$response['header'] = isset( $contents[0] ) ? $contents[0] : "" ;
		return $response;
	}

	/*
	 * Whitelist for viewing the 'Matched products'
	 */
	private static $whitelist =
		array(
			  'allowed' => array( 'id'
								  ,'account_id'
								  ,'input'
								  ,'matched_os_soft_name' => 'os_soft.os_soft_name'
								  ,'vendor_name' => 'vendor.vendor_name'
								  ,'matched_os_soft_id'
								  ,'distance'
								  ,'selected'
								  )
			  ,'searchable' => array( 'matched_filter' )

			  ,'orderable' => array( 'id'
									 ,'input'
									 ,'selected'
									 )

			  ,'groupable' => array( 'input'
									 ,'selected' )

			  );

	private function getMatchedProducts( array $modifiers, $count = false ) {
		$columns = !empty( $modifiers['columns'] ) ? $modifiers['columns'] : self::$whitelist['allowed'];
		$where = $modifiers['where'];
		$order = $modifiers['order'];
		$limit = $modifiers['limit'];

		/*
		 * Filters
		 */

		$matchedFilterValue = false;
		if ( isset( $where['matched_filter'] ) ) {
			$matchedFilterValue = $where['matched_filter'];
			unset( $where['matched_filter'] );
		}

		switch ( $matchedFilterValue ) {
		case 'matched':
			$where['distance'] = Exp::IS_NOT_NULL();
			break;
		case 'unmatched':
			$where['distance'] = Exp::IS_NULL();
			break;
		case 'selected':
			$where['selected'] = 1;
			break;
		case 'all':
		default:
			break;
		}

		/*
		 * Access Control
		 */
		$where['account_id'] = $this->accountId;

		$select = $this->db->select()
			->columns( $columns )
			->from( 'import_products' )
			->leftJoin( 'vuln_track.os_soft' )
			->on( array( 'matched_os_soft_id' => 'os_soft_id' ) )
			->leftJoin( 'vuln_track.vendor' )
			->using( array( 'vendor_id' ) )
			->where( $where );


		if ( !empty( $order ) ) {
			$order['distance'] = 'ASC';
			$select->orderBy( $order );
		}

		if ( !empty( $limit ) ) {
			$select->limit( $limit['start'] , $limit['limit'] );
		}

		$response = array( 'rows' => $select->exec() );

		if ( $count ) {
			$response['total'] = $this->db->select()
				->from( 'import_products' )
				->where( $where )
				->rowCountIgnoreLimit();
		}

		return $response;
	}

	/*
	 * @param $data Array|Object
	 *    Single or Multiple rows to be edited.
	 *
	 * @return Boolean|Integer
	 *    Failure: False
	 *    Success: Affected Number of Rows
	 */
	private function updateSavedMatches( $matches ) {
		if ( empty( $matches ) ) {
			return false;
		}

		/*
		 * Check if the we have a single row or multiple.
		 * If we have a single match, put it in an array so that single and multiple
		 * matches can be treated in a similar manner.
		 */
		if ( !is_array( $matches ) ) {
			$matches = array( $matches );
		}

		$updatedCount = 0;
		foreach( $matches as $match ) {
			/*
			 * The $match should always be an object. If not, we ignore it.
			 */
			if ( !is_object( $match ) ) {
				continue;
			}

			/*
			 * Access Control
			 */
			if ( $match->account_id !== $this->accountId ) {
				continue;
			}

			$numAffected = $this->db->update()
				->table( 'import_products' )
				->set( array( 'selected' => $match->selected ? 1 : 0 ) )
				->where( array(
							   'account_id' => $this->accountId
							   ,'id' => $match->id
							   ,'matched_os_soft_id' => Exp::gt( 0 )
							   ) )
				->exec();

			$updatedCount += $numAffected;
		}

		return $updatedCount;
	}

	/*
	 * Automatically select matches based on the specified distance
	 *
	 * @param Integet $distance
	 *
	 * @return Integer
	 * Number of matches selected
	 */
	private function selectMatches( $distance ) {

		$numAffected = 0;

		$where = array( 'account_id' => $this->accountId );

		$update = $this->db->update()
			->table( 'import_products' )
			->where( $where );

		// Remove all selections
		$update->set( array( 'selected' => 0 ) )->exec();

		// Automatically select matches based on the distance
		if ( $distance >= 0 ) {
			$where['matched_os_soft_id'] = Exp::gt( 0 );
			$where['distance'] = Exp::le( $distance );

			$numAffected += $update
				->set( array( 'selected' => 1 ) )
				->where( $where )
				->exec();
		}

		return $numAffected;
	}

	/*
	 * Get all products to be matched (includes matches and non matches)
	 */
	private function getNumberOfMatches() {
		$numMatches = $this->db->select()
			->from( 'import_products' )
			->where( array( 'account_id' => $this->accountId ) )
			->rowCountIgnoreLimit();

		return $numMatches;
	}

	public function getSelectedMatches() {
		$rows = $this->db->select()
			->columns( array( 'matched_os_soft_id' ) )
			->from( 'import_products' )
			->where( array( 'account_id' => $this->accountId
							,'selected' => 1 )  )
			->exec();
		$productIds = array();
		foreach( $rows as $row ) {
			$productIds[] = (int) $row['matched_os_soft_id'];
		}
		return $productIds;
	}

	public function getSelectedMatchesCount() {
		$count = $this->db->select()
			->from( 'import_products' )
			->where( array( 'account_id' => $this->accountId
							,'selected' => 1 )  )
			->rowCountIgnoreLimit();
		return $count;
	}

	/*
	 * Clean the import_products table
	 */
	public function deleteMatches() {
		$this->db->deleteAll()
			->from( 'import_products' )
			->where( array( 'account_id' => $this->accountId ) )
			->exec();
	}

	function handleRequest( $which ) {

		$response = array( 'success' => true, 'error_code' => self::UNKNOWN, 'data' => array() );

		/*
		 * Write Operations
		 */
		if ( !$GLOBALS['write_permission'] ) {
			$GLOBALS['debug']->error( "Unable to perform operation ( no write permission )" );
			die();
		}

		switch( $which ) {
		case 'preview':
			// @todo: use the data array
			$response = $this->previewFile( $GLOBALS['input']->MPOST('line_count') );
			break;
		case 'import':
			$success = $this->importFile();
			$response['error_code'] = $success ? self::SUCCESS : self::INVALID_INPUT;
			if ( !$success ) {
				$GLOBALS['debug']->error( "File parsing error - see parseCSVFile() function" );
			} else {
				$GLOBALS['debug']->notice( "Successfully parsed the CSV file" );
			}
			break;
		case 'read_imported':
			$userModifiers = Grid::getUserModifiers( self::$whitelist );
			$response['data'] = $this->getMatchedProducts( $userModifiers, true );
			$response['data']['selected_count'] = $this->getSelectedMatchesCount();
			$response['error_code'] = self::SUCCESS;
			break;
		case 'update_imported':
			$numAffected = false;
			if ( isset( $_POST['distance'] ) ) {
				$distance = (int) INPUT::RPOST('distance');
				$numAffected = $this->selectMatches( $distance );
			} else {
				$data = INPUT::getPayload( INPUT::PAYLOAD_RESPONSE_OBJECT );
				$numAffected = $this->updateSavedMatches( $data );
			}
			if ( $numAffected !== false ) {
				$response['error_code'] = self::SUCCESS;
				$response['data']['updated'] = $numAffected;
			}
			break;
		case 'num_of_matches':
			$response['error_code'] = self::SUCCESS;
			$response['data']['num_of_matches'] = $this->getNumberOfMatches();
		}

		echo $GLOBALS['json']->json_encode( $response );
	}
}
