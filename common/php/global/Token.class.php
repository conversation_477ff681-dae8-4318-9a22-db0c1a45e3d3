<?php

/**
 * Class Token
 *
 * Handles the creation of DB persistent Tokens
 *
 * The php time and MySql time should be exactly the same for this to work
 */
class Token {

	/**
	 * Constants
	 */
	const RANDOM = 0;

	const CSRF = 1;

	/**
	 * The Token is used for authentication. Set the auth type in the {@link #name} field.
	 * @const int
	 */
	const TEMP_AUTH = 2;

	const DOWNLOAD = 3;

	/**
	 * The table name in which the tokens are stored
	 * @const string
	 */
	const TABLENAME = 'token';

	/**
	 * Connection to the common database
	 * @var DB
	 */
	protected $db;

	/**
	 * Whether the Token is new or not
	 * @var bool
	 */
	protected $isNew = true;

	/**
	 * Account ID this Token is bound to
	 * @var int
	 */
	protected $account_id;

	/**
	 * Type of token. See class constants
	 * @var int
	 */
	protected $type;

	/**
	 * Name of token
	 * @var string
	 */
	protected $name;

	/**
	 * Token value (hash)
	 * @var string (255 varchar)
	 */
	protected $value;

	/**
	 * Start datetime
	 * @var string
	 */
	protected $start_dtm;

	/**
	 * Number of seconds from $start_dm that this Token expires
	 * @var int
	 */
	protected $expiry;

	/**
	 * Max Usage
	 * @var int
	 */
	protected $max_usage;

	/**
	 * Usage Count
	 * @var int
	 */
	protected $usage_count = 0;

	/**
	 * Create a token that can be used by a single Account
	 *
	 * @param int $accountId The Account ID the Token is created for
	 */
	function __construct( $accountId )
	{
		if ( !is_numeric( $accountId ) || $accountId <= 0 ) {
			throw new Exception( 'Account Id not provided for the account' );
		}

		$this->account_id = $accountId;

		$this->db = DB::getInstanceByName( 'common' );
	}

	/**
	 * Returns the accountId of the token
	 *
	 * @return string
	 */
	public function getAccountId()
	{
		return $this->account_id;
	}

	/**
	 * Sets the accountId of the token
	 * This function is only for internal use. The account_id is set via the
	 * constructor
	 *
	 * @return string
	 */
	protected function setAccountId( $accountId )
	{
		// @todo: validate hash?
		$this->account_id = $accountId;
	}

	/**
	 * Returns the type of the token
	 *
	 * @return string
	 */
	public function getType()
	{
		return $this->type;
	}

	/**
	 * Sets the type of the token
	 *
	 * @return string
	 */
	public function setType( $type )
	{
		// @todo: whitelist types?
		$this->type = $type;
	}


	/**
	 * Returns the name of the token
	 *
	 * @return string
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * Sets the name of the token
	 *
	 * @return string
	 */
	public function setName( $name )
	{
		$this->name = $name;
	}

	/**
	 * Returns the value of the token
	 *
	 * @return string
	 */
	public function getValue()
	{
		return $this->value;
	}

	/**
	 * Sets the value of the token
	 *
	 * @return string
	 */
	public function setValue( $value )
	{
		// @todo: validate hash?
		$this->value = $value;
	}

	/**
	 * Returns the startDtm of the token
	 *
	 * @return string
	 */
	public function getStartDtm()
	{
		return $this->start_dtm;
	}

	/**
	 * Sets the startDtm of the token
	 *
	 * @return string
	 */
	public function setStartDtm( $startDtm )
	{
		if ( !Util::validateDateTime( $startDtm ) ) {
			throw new Exception( 'Invalid date time specified' );
		}
		$this->start_dtm = $startDtm;
	}

	/**
	 * Returns the expiry of the token
	 *
	 * @return string
	 */
	public function getExpiry()
	{
		return $this->expiry;
	}

	/**
	 * Sets the expiry of the token
	 *
	 * @param int Number of seconds during which the token is valid
	 * @return string
	 */
	public function setExpiry( $expiry )
	{
		$this->expiry = (int) $expiry;
	}

	/**
	 * Sets the Max Usage of the token
	 *
	 * @return string
	 */
	public function setMaxUsage( $maxUsage )
	{
		$this->max_usage = (int) $maxUsage;
	}

	/**
	 * Sets the Usage Count of the token
	 *
	 * @see incrementUsage()
	 * This is only for private use, for updating the usage count use incrementUsage
	 *
	 * @return string
	 */
	protected function setUsageCount( $usageCount )
	{
		$this->usage_count = (int) $usageCount;
	}

	/**
	 * Increments the usage by 1. The token needs to be saved after calling this
	 * function to make the changes persistent.
	 *
	 * @return string
	 */
	public function incrementUsage()
	{
		$this->usage_count++;
	}

	/**
	 * Checks whether the Token is new and has not been saved to the DB yet
	 *
	 * @return bool True if Token is new otherwise false
	 */
	public function isNew( $val = null )
	{
		if ( !is_null( $val ) ) {
			$this->isNew = (bool) $val;
		}
		return $this->isNew;
	}

	/**
	 * Generates a 296-bit strong token that isn't susceptible to brute-forcing
	 * in any realistic time frame
	 *
	 * @return string Returns a 64 char token matching ^[A-z0-9]{64}$
	 *	Example: "BWFaNbX1anEGi3xyFd4BXOtWBUDYiCUlZYLrAKbDMLjUAvG7jaVYDdwX0IE0m5Fk"
	 */
	public static function generateHash()
	{
		// Generate a 296 bit hash.
		$entropy = openssl_random_pseudo_bytes( 48 );
		// Compress it a 64 characters
		$hash = base64_encode( $entropy );
		// Make it alpha numeric
		$validChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		$token = str_replace( array( '/', '+' ), array( $validChars[mt_rand(0, 61)], $validChars[mt_rand(0, 61)] ), $hash );

		return $token;
	}

	/**
	 * Regenerates the Token's hash.
	 *
	 * @note The Token will need to be saved for this change to be persistent
	 */
	public function regenerate()
	{
		$this->setValue( self::generateHash() );
	}

	/**
	 * Create a token
	 *
	 * @param string Name of the token
	 * @param int Type of the token
	 *
	 */
	public static function create( $accountId, $name, $type = self::RANDOM, $expiresInSeconds = null )
	{
		$token = new static( $accountId );
		$token->setName( $name );
		$token->setType( $type );
		$token->setValue( self::generateHash() );
		$token->setStartDtm( date('Y-m-d H:i:s') );
		$expiry = false;
		$maxUsage = false;
		switch( $type ) {
			case self::TEMP_AUTH:
				$expiry = 60;
				$maxUsage = 100;
				break;
			case self::DOWNLOAD: /* Maximum of 50 times in the next 15 minutes.
									15 minutes should be enough to download all the
									packages */
				$expiry = 60*15;
				$maxUsage = 50;
				break;
			case self::RANDOM:
			case self::CSRF:
			default:
				$expiry = 60;
				$maxUsage = 1;
				break;
		}
		$expiry = isset( $expiresInSeconds ) ? $expiresInSeconds : $expiry;
		$token->setExpiry( $expiry );
		$token->setMaxUsage( $maxUsage );
		$token->setUsageCount( 0 );
		return $token;
	}

	/**
	 * Saves the token to the database
	 *
	 * @return int The last inserted id
	 */
	public function save()
	{
		$this->isNew = false;
		$stmt = $this->db
			->replace()
			->into( self::TABLENAME )
			->set( array( 'account_id' => $this->account_id
						  ,'name' => $this->name
						  ,'type' => $this->type
						  ,'value' => $this->value
						  ,'start_dtm' => $this->start_dtm
						  ,'expiry' => $this->expiry
						  ,'max_usage' => $this->max_usage
						  ,'usage_count' => $this->usage_count
						  ) )
			->setOption(Select::RETURN_PDO_STATEMENT, 1)->exec();

		if (0 !== (int) $stmt->errorCode()) {
			return false;
		}
		return true;
	}

	/**
	 * Deletes the token
	 *
	 * @return int Number of rows deleted
	 */
	public function delete()
	{
		if ( !$this->account_id || !$this->value ) {
			throw new Exception( 'Empty token can not be deleted' );
		}
		$this->isNew = true;
		return $this->db
			->delete()
			->from( self::TABLENAME )
			->where( array( 'account_id' => $this->account_id
							,'value' => $this->value
							) )
			->limit(1)
			->exec();
	}

	/**
	 * Deletes the token from the database
	 *
	 * @return int Number of rows deleted
	 */
	public static function deleteBy( $accountId, $value )
	{
		return DB::getInstanceByName( 'common' )
			->delete()
			->from( self::TABLENAME )
			->where( array( 'account_id' => $accountId
							,'value' => $value ) )
			->exec();
	}

	/**
	 * Fetch the Token from the database and return as an object
	 *
	 * @param int Account Id
	 * @param int Token value
	 * @return mixed Either the token object or false if not found or more than one Token matched
	 */
	public static function fetch( $value, $type )
	{
		$query = 'SELECT account_id, name, type, value, start_dtm, expiry, max_usage, usage_count'
			. ' FROM ' . self::TABLENAME
			. ' WHERE value = :value'
			. ' AND type = :type'
			. ' AND start_dtm > DATE_SUB( :now, INTERVAL expiry SECOND )'
			. ' AND usage_count < max_usage'
			;
		$rows = DB::getInstanceByName( 'common' )
			->execRaw( $query, array(
				 ':value' => $value
				 ,':type' => $type
				 ,':now' => date('Y-m-d H:i:s')
			) )
			->fetchAll();

		if ( count( $rows ) === 1 ) {
			$r = $rows[0];
			$token = new Token( $r['account_id'] );
			$token->setName( $r['name'] );
			$token->setType( $r['type'] );
			$token->setValue( $r['value'] );
			$token->setStartDtm( $r['start_dtm'] );
			$token->setExpiry( $r['expiry'] );
			$token->setMaxUsage( $r['max_usage'] );
			$token->setUsageCount( $r['usage_count'] );
			$token->isNew( false );
			return $token;
		}
		return false;
	}

}
