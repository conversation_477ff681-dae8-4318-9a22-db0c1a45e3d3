<?php
/**
 * Created by PhpStorm.
 * User: mkorff
 * Date: 3/5/15
 * Time: 4:48 PM
 */

class ReportGenerator {

    /**
     * Report Formats
     */
    const XML = 1;
    const RSS = 2;
    const PDF = 3;
    const CSV = 4;
    const PNG = 5;
    const BIN = 6;				/* Binary files e.g. exe etc */

    private $format = self::PDF;

    /**
     *
     */
    public function generateReport( $accountId, $reportId, $timeNow = false, $cstId = false )
    {
        switch ($this->format)
        {
            case self::CSV:
                $repoting = new CSVReporting();
                break;
            case self::PDF:
            default:
                $repoting = new PDFReporting();
                break;


        }
        return $repoting->generateReport($accountId, $reportId, $timeNow, $cstId );
    }

    /**
     *
     */
    public function setFormat( $_f )
    {
        $this->format = $_f;
    }

} 