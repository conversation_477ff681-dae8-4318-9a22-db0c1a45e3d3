<?php
/**
 * @file options.class.php
 * Provides account options functionality.
*/

/**
 * Existing options so far, and their hex values:
 * OPT_NONE 0x0000
 * OPT_ADVISORY_DEEP_LINKS 0x0001
 * OPT_ADVISORY_POC_DATA 0x0002
 * OPT_ADVISORY_EXTENDED_DATA 0x0004
 * OPT_ADVISORY_EXTENDED_DESCRIPTION 0x0008
 * OPT_ASSET_RECEIVE_ALL 0x0010
 *
 * Synopsis:
 * Based on the user license, the VIM requires certain modules or options enabled/disabled.
 * To allow for granularity and avoid database redundancy, the option/module mechanism is
 * using an approach similar to the UNIX chmod:
 * Setting a bit to 0 or 1 for every module disabled or enabled, storing it as base 10 (in the database), and configuring it in base 16.
 *
 * For example, if the user has options OPT_ADVISORY_DEEP_LINKS and OPT_ASSET_RECEIVE_ALL the binary representation would be:
 * 1000010
 *
 * The configuration option is stored as HEX, so that OPT_ADVISORY_DEEP_LINKS, is the equivalent of 1 -> 0x1 ( 0x0001 ), OPT_ADVISORY_POC_DATA -> 10 -> 0x0002, OPT_ADVISORY_EXTENDED_DATA -> 100 -> 0x0004 and so on.
 *
 * To build a new set of options, you do a bitwise OR. For example, to have an account with options OPT_ADVISORY_DEEP_LINKS and OPT_ASSET_RECEIVE_ALL: modules = OPT_ADVISORY_DEEP_LINKS | OPT_ASSET_RECEIVE_ALL
 *
 * To remove an option, use the bitwise VALUE AND NOT OPTION_MODULE. For example: 63 ( all options enabled ), having OPT_ADVISORY_DEEP_LINKS disabled, would result in 62.
*/
class OPTIONS {

	/**
	 * Function for returning enabled modules.
	 * @param opt
	 *	Integer RAW modules integer.
	 * @return
	 *	Object having ->OPT_NAME set to true or false, if enabled or not.
	*/
	public static function getOptions( $opt ) {
		$options = new stdClass();
		$options->OPT_NONE = true;
		$options->OPT_ADVISORY_DEEP_LINKS = false;
		if ( $opt & OPT_ADVISORY_DEEP_LINKS ) {
			$options->OPT_ADVISORY_DEEP_LINKS = true;
			$options->OPT_NONE = false;
		}
		$options->OPT_ADVISORY_POC_DATA = false;
		if ( $opt & OPT_ADVISORY_POC_DATA ) {
			$options->OPT_ADVISORY_POC_DATA = true;
			$options->OPT_NONE = false;
		}
		$options->OPT_ADVISORY_EXTENDED_DATA = false;
		if ( $opt & OPT_ADVISORY_EXTENDED_DATA ) {
			$options->OPT_ADVISORY_EXTENDED_DATA = true;
			$options->OPT_NONE = false;
		}
		$options->OPT_ADVISORY_EXTENDED_DESCRIPTION = false;
		if ( $opt & OPT_ADVISORY_EXTENDED_DESCRIPTION ) {
			$options->OPT_ADVISORY_EXTENDED_DESCRIPTION = true;
			$options->OPT_NONE = false;
		}
		$options->OPT_ASSET_RECEIVE_ALL = false;
		if ( $opt & OPT_ASSET_RECEIVE_ALL ) {
			$options->OPT_ASSET_RECEIVE_ALL = true;
			$options->OPT_NONE = false;
		}

		return $options;
	}
}
?>