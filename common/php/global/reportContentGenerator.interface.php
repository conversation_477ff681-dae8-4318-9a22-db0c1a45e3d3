<?php

interface reportContentGenerator {

	// This function must be implemented in any module that generates PDF content for a report
	//
	// Start and end date - assumme already in Y-M-D format, no time data.
	function generatePdfOutput( $pdfInput, $accountId, $generationDate, $options, $oneTimeGen, $startDate, $endDate, $recurrenceScheduleArray, $timeFrameArray, $recipients, $requiredConfigurationsParameters );

	// Any module that generates content should generate some data for the table of contents.
	// If you don't want something included in the TOC, just return an empty array from this.
	function generateTocContent( $optionsArray, $requiredConfigurationsParameters );

	// Some modules require not just the options selected for content, but rather, things within
	// the content are based on a more global configurable setting. For example, the users and
	// asset lists selected in one module which apply to others.  In these cases, we get the
	// required data headers with this function so we can pass in the correct parameters.
	//
	// If a module does not require any such configuration, just return an empty array from
	// this function
	function getConfigurationRequirements();

	// This function must be implemented if we want the 'what this report contains' to hold data about a given
	// module
	function generateMiscContent( $pdfInput, $accountId, $moduleOptions, $startDate, $endDate, $miscParsedOptions );
}

?>