<?php

  /*
   * Grid Class
   * Contains utility functions to generate:
   * - User specified conditions, WHERE clause
   * - Paging
   * - Grouping
   * - Sorting..
   *
   * Designed to be used with 'db.class.php'
   *
   * NOTE: Should not be used with the old 'database.class.php'
   */

class Grid {

	/*
	 * Filter [ and Qualify ] the column names.
	 *
	 * Translating the $columnName to the qualified column name ( if it exists ).
	 * i.e.
	 * -     If the value for that columnName is set in the searchableColumns, use the
	 *       value,
	 * - ELSE
	 *       If the columnName 'is' a value in the searchableColumns, no translation is
	 *       required, so the columnName itself can be used as the columns name
	 * - ELSE
	 *       The columnName couldn't be found in the allowed array so continue..
	 *
	 * @param $columnName
	 *   The name of the column that needs to be qualified
	 * @param $translations
	 *   A static array that contains the mappings of the column names to the qualified
	 *   column names.
	 *
	 * @return Boolean | String
	 *   Returns False, if not allowed
	 *   Else, the qualified column name.
	 */
	public static function qualifyColumn( $columnName, array $translations ) {
		$qualifiedColumnName = false;

		if ( isset( $translations[$columnName] ) ) {
			$qualifiedColumnName = $translations[$columnName];
		} else if ( in_array( $columnName, $translations ) ) {
			// No translation is required.
			$qualifiedColumnName = $columnName;
		}

		return $qualifiedColumnName;
	}

	/*
	 * Get the list of allowed columns ( aliases and qualified names )
	 *
	 * @param $allowedColumns
	 *   @todo: write the format of the array
	 * @param $paramName
	 *   The parameter name that has the column name.
	 *
	 * @return Boolean|Array
	 *   False, for Error
	 *   Array, Allowed list of Qualified Columns indexed with the Aliases
	 */
	protected static function getColumns( array $allowedColumns ) {
		$paramName = 'columns';
		$paramNameHeaders = 'columnHeaders';

		$columns = array();
		$columnHeaders = array();

		if ( empty( $allowedColumns ) ) {
			return $columns;
		}

		$encodedColumns = '';
		if ( isset( $_GET[ $paramName ] ) ) {
			$encodedColumns = INPUT::RGET( $paramName );
		} else if ( isset( $_POST[ $paramName ] ) ) { // for compatibility with ext js 3 dependent code.
			$encodedColumns = INPUT::RPOST( $paramName );
		} else {
			return $columns;
		}

		$decodedColumns = json_decode( $encodedColumns, true );
		if ( json_last_error() !== JSON_ERROR_NONE ) {
			return false;
		}

		$encodedColumnHeaders = '';
		$decodedColumnHeaders = array();
		if ( isset( $_GET[ $paramNameHeaders ] ) ) {
			$encodedColumnHeaders = INPUT::RGET( $paramNameHeaders );
		} else if ( isset( $_POST[ $paramNameHeaders ] ) ) { // for compatibility with ext js 3 dependent code.
			$encodedColumnHeaders = INPUT::RPOST( $paramNameHeaders );
		}
		if ( !empty( $encodedColumnHeaders ) ) {
			$decodedColumnHeaders = json_decode( $encodedColumnHeaders, true );
			if ( json_last_error() !== JSON_ERROR_NONE ) {
				return false;
			}
		}

		for ( $i = 0, $size = count( $decodedColumns ); $i < $size; $i++ ) {
			$alias = $decodedColumns[ $i ];
			$qualifiedColumnName = self::qualifyColumn( $alias, $allowedColumns );
			if ( $qualifiedColumnName !== false ) {
				$columns[$alias] = $qualifiedColumnName;
				if ( isset( $decodedColumnHeaders[ $i ] ) ) {
					$columnHeaders[$i] = $decodedColumnHeaders[ $i ];
				}
			}
		}
		return array( 'columns' => $columns, 'column_headers' => $columnHeaders );
	}

	/*
	 * Function to fetch the qualified and sanitized parameters from the filter string:
	 * It is a compatibility function (for supporting old ext 3.x code).
	 *
	 * The function does exactly what the getSanitizedAndQualifiedParams() function does,
	 * except for searching individual parameters from the _GET array. Instead it uses the
	 * the comma separated 'filter' string that comes in as a POST parameter.
	 *
	 * The approach is discouraged as the comma is not encoded. Instead, we have the
	 * following two options:
	 *
	 * 1. Json encode the filters
	 * 2. Send params separately.
	 *
	 * @see getSanitizedAndQualifiedParams() function
	 *
	 * @param $searchableColumns Array
	 * @return Array
	 */
	protected static function getConditionsFromFilterString( array $searchableColumns ) {
		$conditions = array();
		if ( !isset( $_POST['filter'] ) ) {
			return $conditions;
		}

		// prop=value,prop2=value2..
		$filterString = INPUT::RPOST('filter');
		$filters = explode( ',', $filterString );
		if ( !is_array( $filters ) ) {
			return $conditions;
		}
		foreach ( $filters as $filter ) {
			if ( empty( $filter ) ) {
				continue;
			}
			$filter = explode( '=', $filter );
			$property = $filter[0];
			$value = $filter[1];
			$qualifiedColumnName = self::qualifyColumn( $property, $searchableColumns );
			if ( $qualifiedColumnName !== false ) {
				/*
				 * Fix to an issue with the filter string
				 * The properties are always there with empty values so we have to check
				 * if the value exists before applying the condition.
				 *
				 */
				if ( $value !== "" ) {
					$conditions[$qualifiedColumnName] = $value;
				}
			}
		}
		return $conditions;
	}

	/*
	 * Function for getting parameters from the _GET array using the $whitelist
	 *
	 * @param $whitelist Array
	 * The whitelist is a mapping of the inputs provided by the client ( browser / user )
	 * to the actual name used at the server side ( e.g. actual column name in the
	 * database table. ). To summarize, the whitelist array is used to:
	 *
	 * 1.  Whitelist the column names.. only the columns present in the whitelist will be
	 *     searched. The rest will be ignored.
	 * 2.  Qualifiy the parameters e.g. rename the user names parameters to the names used
	 *     at the server ( e.g. column name in the table. )
	 *
	 * Format:
	 * $whitelist => array( 'input_name' => 'column_name'
	 *                       , ...  )
	 *
	 * If no key exists, the name is taken as it is and no qualification happens.
	 *
	 * @return Array
	 * Sanitized and Qualified Parameters
	 * i.e.
	 *       array( column_name => value
	 *              , ... )
	 *
	 */
	protected static function getSanitizedAndQualifiedParams( array $whitelist ) {
		$sanitizedParams = array();

		/*
		 * Get the names of the columns to be used as parameters for searching in the
		 * _GET array
		 */
		$columns = array();
		foreach( $whitelist as $key => $value ) {
			if ( is_numeric( $key ) ) {
				$columns[] = $value;
			} else if ( is_string( $key ) ) {
				$columns[] = $key;
			}
		}

		$params = INPUT::getRawParams( $columns );

		if ( empty( $params ) ) {
			return $sanitizedParams;
		}

		foreach ( $params as $columnName => $value ) {

			$qualifiedColumnName = self::qualifyColumn( $columnName, $whitelist );
			if ( $qualifiedColumnName !== false ) {
				$sanitizedParams[$qualifiedColumnName] = $value;
			}
		}

		return $sanitizedParams;
	}


	/*
	 * Verify the raw sorters and qualify them.
	 *
	 * @param $rawSorters
	 *   An array with the sorters which need to be verified and qualified
	 * @param $allowedColumns
	 *   Columns that can be ordered by.
	 *   @todo: write the format of the array
	 *
	 * @return Array
	 *   An array of the sort options
	 */
	protected static function getSorters( $rawSorters, array $allowedColumns ) {
		$sorters = array();

		foreach( $rawSorters as $sorter ) {
			$columnName = isset( $sorter['property'] ) ? $sorter['property'] : false;
			$direction = isset( $sorter['direction'] ) && $sorter['direction']  == 'DESC' ? 'DESC' : 'ASC';
			if ( !is_string( $columnName ) || !is_string( $direction ) ) {
				continue;
			}

			$qualifiedColumnName = self::qualifyColumn( $columnName, $allowedColumns );
			if ( $qualifiedColumnName !== false ) {
				$sorters[$qualifiedColumnName] = $direction;
			}
		}
		return $sorters;
	}

	const SORT = 1;
	const GROUP = 2;
	/*
	 * Function that checks the inputs and based on them, creates a sorters/groupers array
	 * that is ready for verification. The output needs to be passed to getSorters() where
	 * it is verified and qualified.
	 *
	 * @return Boolean|Array
	 *   False, for Error
	 *   Array, with the raw sort/group parameters that need to be verified and qualified.
	 */
	protected static function makeRawSorters( $type ) {
		$sorters = false;

		$property = false;
		$direction = false;
		switch( $type ) {
		case self::SORT:
			$property = 'sort';
			$direction = 'dir';
			break;
		case self::GROUP:
			/*
			 * Fix for Ext JS 3 / Ext JS 4 compatibility
			 * If 'group' is set, we use it as Ext JS 4 uses it to send a json encoded groupers.
			 */
			$property = isset( $_GET['group'] ) ? 'group' : 'groupBy';
			$direction = 'groupDir';
			break;
		default:
			return $sorters;
		}

		/*
		 * First we need to decide how the sort/group data is coming in. Lets take sort
		 * as an example:
		 * - Ext JS 4 json encodes the sorters in the 'sort' param, using HTTP 'GET'
		 * - Ext JS 3 sends two params 'sort' and 'dir'           , using HTTP 'POST'
		 *   ( this is true for alteast the way we are currently using it
		 *     i.e. for single param sorting)
		 *
		 * In both the cases, the sort parameter needs to be set.
		 */

		if ( isset( $_POST[$property] ) && isset( $_POST[$direction] ) ) {
			$sorters[] = array( 'property' => INPUT::RPOST($property)
								,'direction' => INPUT::RPOST($direction) );
		} else if ( isset( $_GET[$property] ) ) {
			$encodedSorters = INPUT::RGET($property);
			$decodedSorters = json_decode( $encodedSorters, true );

			// Make sure the format of the json encoded sort options is correct
			// @todo: use json_last_error() !== JSON_ERROR_NONE instead of checking on the
			// return type
			if ( !is_array( $decodedSorters ) ) {
				// Error, Invalid json endoding?
				return false;
			}

			if ( !empty( $decodedSorters ) ) {
				$sorters = $decodedSorters;
			}
		}

		return $sorters;
	}

	/*
	 * Get the filters using the the whitelisted columns i.e. $searchableColumns
	 *
	 * @param $searchableColumns Array
	 * @return Array
	 */
	protected static function getFilters( $searchableColumns ) {
		$sanitizedFilters = array();
		if ( isset( $_GET['filter'] ) ) {
			$encodedFilters = INPUT::RGET('filter');
			$decodedFilters = json_decode( $encodedFilters, true );

			if ( json_last_error() !== JSON_ERROR_NONE ) {
				return false;
			}

			foreach( $decodedFilters as $filter ) {
				$columnName = isset( $filter['property'] ) ? $filter['property'] : false;
				$value = isset( $filter['value'] ) ? $filter['value'] : null;
				if ( !is_string( $columnName ) ) {
					continue;
				}
				if ( !is_numeric( $value) && !is_bool( $value ) && !is_string( $value ) ) {
					continue;
				}
				$qualifiedColumnName = self::qualifyColumn( $columnName, $searchableColumns );
				if ( $qualifiedColumnName !== false ) {
					$sanitizedFilters[$qualifiedColumnName] = $value;
				}
			}
		}
		return $sanitizedFilters;
	}

	protected static function getLimit() {
		$limit = array();
		if ( isset( $_GET['start'] ) && isset( $_GET['limit'] ) ) {
			$limit = array( 'start' => (int)INPUT::RGET( 'start' )
							,'limit' => (int)INPUT::RGET( 'limit' ) );
		} else if ( isset( $_POST['start'] ) && isset( $_POST['limit'] ) ) {
			$limit = array( 'start' => (int)INPUT::RPOST( 'start' )
							,'limit' => (int)INPUT::RPOST( 'limit' ) );
		} else if ( !INPUT::isCsvRequired() ) {
			// If csv isn't required then we shouldn't respond with the complete
			// result set
			$limit = array( 'start' => 0
							,'limit' => 25 );
		}
		return $limit;
	}
	/*
	 * @param $allowed Array // @todo
	 * @param $searchable Array
	 * @param $orderable Array
	 * @param $groupable Array
	 *    We don't actually want to group the rows, just order them so that the UI is
	 *    able to render the rows in a way so that it looks grouped.
	 *    @todo: need a flag that would trigger actual grouping on if required.
	 *
	 * @return Array
	 */
	public static function getUserModifiers( array $whitelists = array() ) {

		//  array $allowed = array(), array $searchable = array(), array $orderable = array(), array $groupable = array()

		$modifiers = array();

		$allowed = isset( $whitelists['allowed'] ) && is_array( $whitelists['allowed'] )  ? $whitelists['allowed'] : array();
		$searchable = isset( $whitelists['searchable'] ) && is_array( $whitelists['searchable'] )  ? $whitelists['searchable'] : array();
		$orderable = isset( $whitelists['orderable'] ) && is_array( $whitelists['orderable'] )  ? $whitelists['orderable'] : array();
		$groupable = isset( $whitelists['groupable'] ) && is_array( $whitelists['groupable'] )  ? $whitelists['groupable'] : array();


		/*
		 * Columns i.e. the column dataindex and column headers (titles)
		 */
		$modifiers['columns'] = array();
		if ( !empty( $allowed ) ) {
			$filteredColumns = self::getColumns( $allowed );
			if ( !empty( $filteredColumns ) ) {
				$modifiers['columns'] = $filteredColumns['columns'];
				$modifiers['column_headers'] = $filteredColumns['column_headers'];
			}
		}

		/*
		 * Conditions
		 */
		if ( !empty( $searchable ) ) {

			// Get any parameters that would change the 'where' clause of the query
			$conditions = self::getSanitizedAndQualifiedParams( $searchable );

			// Fetch the filters and append to the $conditions.
			$conditions = array_merge( $conditions, self::getFilters( $searchable ) );

			// Compatibility with the old way of supplying a filter
			// i.e. comma separated and NOT encoded
			// The $conditions variable should overwrite the keys in case of conflict
			$conditions = array_merge( self::getConditionsFromFilterString( $searchable ), $conditions );

			// The $conditions variable should overwrite the keys of $conditions_ in case of conflict
			$modifiers['where'] = $conditions;
		} else {
			$modifiers['where'] = array();
		}

		/*
		 * Order
		 */

		// default value of the 'order'
		$modifiers['order'] = array();
		if ( !empty( $orderable ) ) {
			$rawSorters = static::makeRawSorters( self::SORT );
			if ( $rawSorters !== false ) {
				$modifiers['order'] = static::getSorters( $rawSorters, $orderable );
			}
		}

		/*
		 * Group
		 */
		if ( !empty( $groupable ) ) {
			$rawSorters = static::makeRawSorters( self::GROUP );
			if ( $rawSorters !== false ) {
				$modifiers['order'] = array_merge( static::getSorters( $rawSorters, $groupable ), $modifiers['order'] );
			}
		}

		/*
		 * Limit
		 */
		$modifiers['limit'] = self::getLimit();

		return $modifiers;
	}

}