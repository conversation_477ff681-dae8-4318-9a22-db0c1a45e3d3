<?php
/**
 * @file ipAccessRules.class.php
 * Provides IP Access Management functionality.
 */

/**
 * This class provides generic functionality for IP access management
 * It should be inherited from by projects that will use it.
 * It has very weak dependencies as it does not use any GLOBALS or REQUEST variables
 * It expects all parameters to be unescaped(original form) as all the escaping is done at query level
 */
class ipAccessRules {
	/**
	 * The database must be an implementation of the standard DATABASE class
	 * It can however connect to any database that has the 2 tables structure required by the class
	 */
	protected $database;

	/**
	 * For escaping array of ints a function of the UTIL class is used
	 */
	private $util;

	/**
	 * Constants
	 */
	const UNKNOWN_ERROR = -1;
	const SUCCESS = 0;
	const WHITELISTED = 'Allow List';
	const BLACKLISTED = 'Block List';
	const INVALID_IP_FORMAT = 1;
	const SELF_BLOCKING_RULE = 2;
	const INVALID_RANGE_ORDER = 3;
	const NO_RULE_LABEL = 4;
	const DB_WHITELISTED = 'Allow List';
	const DB_BLACKLISTED = 'Block List';

	public static $whitelist = array(
		'allowed' => array(
			'id'
			,'label'
			,'type'
			,'ip'
			,'enabled'
			,'last_update'
			,'users' => 'GROUP_CONCAT(ip_rules_accounts.account_id)'
		)
		,'searchable' => array()
		,'orderable' => array(
			'label'
			,'ip'
			,'type'
			,'enabled'
			,'last_update'
		)
		,'groupable' => array()
	);

	public function __construct( $database, $privateDb = false ) {
		//This is creating a hard dependency
		//This class requires UTIL so unsure it's loaded except when this is a
		//an agent_check request because it doesn't use UTIL and won't load it
		// TODO Remote this util definition once we have an autoloader implemented
		if ( false === isset($GLOBALS['util']) && true === empty($GLOBALS['isAgentCheck']) ) {
			$GLOBALS['util'] = new UTIL();
		}

		$this->privateDb = $privateDb;

		$this->database = $database;
	}

	/**
	 * Adds a new IP to the list for a customer
	 * @param string $ip the IPv4 of the current user adding the rule
	 * @param int $user this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param string $rule represents the actual rule to be added
	 * @param string $type either WHITELISTED or BLACKLISTED
	 * @param string $label the name of the rule
	 * @param array $users int[] representing the ids of the user for which the whitelist is created
	 * @return boolean whether the ip was succesfully added to the db
	 */
	public function addIpRule( $ip, $user, $rule, $type, $label, $users ) {
		//$type = strtoupper( $type );
		//dbType is the actual SET item inserted in the database, it cannot be altered
		$dbType = '';
		switch ( $type ) {
			case self::WHITELISTED:
				$dbType = self::DB_WHITELISTED;
				break;
			case self::BLACKLISTED:
				$dbType = self::DB_BLACKLISTED;
				break;
			default:
				return false;
				break;
		}
		$checkOptions = array(
			'currentIp' => $ip
			,'currentUser' => $user
			,'ruleToBeAdded' => $rule
			,'typeOfRule' => $type
			,'users' => $users
		);
		//Check to see if the rule can be added without
		//affecting the current configuration in an undesired way
		$ruleCheck = $this->isValidIpRule( $rule );
		if ( $ruleCheck !== self::SUCCESS ) {
			return $ruleCheck;
		}
		if ( !$this->isNonBlockingIp( $checkOptions ) ) {
			return self::SELF_BLOCKING_RULE;
		}
		$query = "
			INSERT INTO
				ip_rules (
					 ip
					,type
					,label
					,last_update
				) VALUES (
					 '" . $this->database['ca']->escapeString( $rule ) . "'
					,'" . $this->database['ca']->escapeString( $dbType ) . "'
					,'" . $this->database['ca']->escapeString( $label ) . "'
					,NOW()
				)
		";
		//Finally insert the rule
		if ( $this->database['ca']->query( $query ) !== false ) {
			$lastId = $this->database['ca']->getInsertId();
			//Only whitelists can have user customizations( if they have )
			if ( !empty( $users ) && $type === self::WHITELISTED ) {
				$query = "
					INSERT INTO
						ip_rules_accounts (
							account_id
							,rule_id
						)VALUES
				";
				$userRecords = array();
				foreach ($users as $user) {
					$userRecords[] = "(" . (int) $user . ", " . (int)$lastId . ")";
				}
				$query .= implode(", ", $userRecords );
				if ( $this->database['ca']->query( $query ) === false ) {
					return self::UNKNOWN_ERROR;
				}
			}
			return self::SUCCESS;
		} else {
			return self::UNKNOWN_ERROR;
		}
	}

	/**
	 * Updates an existing rule
	 * @param string $ip the IPv4 of the current user adding the rule
	 * @param int $user this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param string $rule represents the actual rule to be added
	 * @param string $type either WHITELISTED or BLACKLISTED
	 * @param string $label the name of the rule
	 * @param array $users int[] representing the ids of the user for which the whitelist is created
	 * @param int $id the id of the rule to be updated
	 * @return string constant whether the ip was succesfully updated to the db
	 */
	public function updateIpRule( $ip, $user, $rule, $type, $label, $users, $id ) {
		if ( !( strlen( $label ) > 0 ) ) {
			return self::NO_RULE_LABEL;
		}
		//$type = strtoupper( $type );
		//dbType is the actual SET item inserted in the databas;, it cannot be altered
		$dbType = '';
		switch ( $type ) {
			case self::WHITELISTED:
				$dbType = self::DB_WHITELISTED;
				break;
			case self::BLACKLISTED:
				$dbType = self::DB_BLACKLISTED;
				break;
			default:
				return false;
				break;
		}
		$existingRule = $this->getRule( $id );
		$checkOptions = array(
			'currentIp' => $ip
			,'currentUser' => $user
			,'ruleToBeAdded' => $rule
			,'typeOfRule' => $type
			,'users' => $users
			,'ignoreRuleId' => $id
		);
		//Check to see if the rule can be updated without
		//affecting the current configuration in an undesired way
		$ruleCheck = $this->isValidIpRule( $rule );
		if ( $ruleCheck !== self::SUCCESS ) {
			return $ruleCheck;
		}
		if ( $existingRule['enabled'] == 1 && !$this->isNonBlockingIp( $checkOptions ) ) {
			return self::SELF_BLOCKING_RULE;
		}
		$query = "
			UPDATE
				ip_rules
			SET
				ip = '" . $this->database['ca']->escapeString( $rule ) . "'
				,type = '" . $this->database['ca']->escapeString( $dbType ) . "'
				,label = '" . $this->database['ca']->escapeString( $label ) . "'
				,last_update = NOW()
			WHERE
				id = '" . (int)$id . "'
		";
		//Finally update the rule
		if ( $this->database['ca']->query( $query ) !== false ) {
			$this->database['ca']->query( "DELETE FROM ip_rules_accounts WHERE rule_id = '" . (int)$id . "'" );
			if ( !empty( $users ) ) {
				$query = "
					INSERT INTO
						ip_rules_accounts (
							account_id
							,rule_id
						)VALUES
				";
				$userRecords = array();
				foreach ($users as $user) {
					$userRecords[] = "(" . (int) $user . ", " . (int)$id . ")";
				}
				$query .= implode(", ", $userRecords );
				if ( $this->database['ca']->query( $query ) === false ) {
					return self::UNKNOWN_ERROR;
				}
			}
			return self::SUCCESS;
		} else {
			return self::UNKNOWN_ERROR;
		}
	}

	/**
	 * Enables an existing rule
	 * @param string $ip the IPv4 of the current user adding the rule
	 * @param int $user this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param int $id the rule to be enabled
	 * @return constant string
	 */
	public function enableRule( $ip, $user, $id ) {
		$rule = $this->getRule( $id );
		//Check to see if the rule can be safely altered
		$checkOptions = array(
			'currentIp' => $ip
			,'currentUser' => $user
			,'ruleToBeAdded' => $rule['ip']
			,'typeOfRule' => $rule['type']
			,'users' => $rule['users']
		);
		if ( $this->isNonBlockingIp( $checkOptions ) ) {
			$query = "
				UPDATE
					ip_rules
				SET
					enabled = '1'
					,last_update = NOW()
				WHERE
					id = '" . (int)$id . "'
			";
			if ( $this->database['ca']->query( $query ) ) {
				return self::SUCCESS;
			} else {
				return self::UNKNOWN_ERROR;
			}
		} else {
			return self::SELF_BLOCKING_RULE;
		}
	}

	/**
	 * Disables an existing rule
	 * @param string $ip the IPv4 of the current user adding the rule
	 * @param int $user this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param int $id the rule to be disabled
	 * @return constant string
	 */
	public function disableRule( $ip, $user, $id ) {
		return $this->deactivateRule( $ip, $user, $id, false );
	}

	/**
	 * Deletes an existing rule
	 * @param $ip string the IPv4 of the current user adding the rule
	 * @param $user int this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param $id int the rule to be deleted
	 * @return constant string
	 */
	public function deleteRule( $ip, $user, $id ) {
		return $this->deactivateRule( $ip, $user, $id, true );
	}

	/**
	 * Wrapper function that is used for disabling and deleting rules
	 * @param string $ip the IPv4 of the current user adding the rule
	 * @param int $user this is the current user making the modifications, it is needed
	 *			so that he does not acidentally block himself
	 * @param int $id the id of the rule to be disabled/deleted
	 * @param boolean $delete flag that determines the deletion of the rule
	 * @return string constant
	 */
	private function deactivateRule( $ip, $user, $id, $delete = false ) {
		$rule = $this->getRule( $id );
		//Check to see if the rule can be safely altered
		$checkOptions = array(
			'currentIp' => $ip
			,'currentUser' => $user
			,'ignoreRuleId' => $id
		);
		if ( $this->isNonBlockingIp( $checkOptions ) ) {
			if ( $delete === true ) {
				//ENHANCEMENT: A transaction would be nice here
				$query = "
					DELETE
						ip_rules_accounts
					FROM
						ip_rules_accounts
					INNER JOIN ip_rules
						ON ip_rules.id = ip_rules_accounts.rule_id
					WHERE
						ip_rules_accounts.rule_id = '" . (int)$id . "'
				";
				$this->database['ca']->query( $query );

				$query = "
					DELETE FROM
						ip_rules
					WHERE
						id = '" . (int)$id . "'
				";
			} else {
				$query = "
					UPDATE
						ip_rules
					SET
						enabled = '0'
						,last_update = NOW()
					WHERE
						id = '" . (int)$id . "'
				";
			}
			if ( $this->database['ca']->query( $query ) ) {
				return self::SUCCESS;
			} else {
				return self::UNKNOWN_ERROR;
			}
		} else {
			return self::SELF_BLOCKING_RULE;
		}
	}

	/**
	 * Check whether an IP rule is in one of the allowed formats
	 * @param string $rule a rule string
	 * @return string constant
	 */
	private function isValidIpRule( $rule ) {
		//the pipe must be present once at a maximum
		$pipeCount = substr_count( $rule, '|' );
		if ( $pipeCount > 1 ) {
			return self::INVALID_IP_FORMAT;
		} elseif ( $pipeCount === 1 ) {
			//this should be a range
			list( $startIp, $endIp ) = explode( '|', $rule, 2 );
			if ( !self::isValidIp( $startIp ) ) {
				return self::INVALID_IP_FORMAT;
			} elseif ( self::isValidIp( $endIp ) ) {
				$unsignedStart = (float)sprintf( "%u", ip2long( $startIp ) );
				$unsignedEnd = (float)sprintf( "%u", ip2long( $endIp ) );
				if ( $unsignedStart >= $unsignedEnd ) {
					return self::INVALID_RANGE_ORDER;
				}
				return self::SUCCESS;
			}
			return self::INVALID_IP_FORMAT;
		} else {
			//this should be a simple ip
			if ( self::isValidIp( $rule ) ) {
				return self::SUCCESS;
			} else {
				return self::INVALID_IP_FORMAT;
			}
		}
	}

	/**
	 * Check whether an IP has the correct format
	 * @param string $ip a presumed IPv4 to be validated from the point of view of the format
	 * @return boolean
	 */
	private static function isValidIp( $ip ) {
		$unsignedIp = (float)sprintf( "%u", ip2long( $ip ) );
		if ( $ip === long2ip( $unsignedIp ) ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Checks whether altering a rule will result in blocking a specific IP-user
	 * @param array $options:
	 * 		- currentIp: string, the IPv4 to check against(mandatory)
	 *		- currentUser: int, the User to check against
	 *		- ruleToBeAdded: string, compute the result by also adding this rule
	 *		- typeOfRule: [whitelisted,blacklisted], is tied to the above specified option
	 *		- users: int[], the users to which the above rule applies
	 * 		- ignoreRuleId: int, compute the result by ognoring this rule
	 * @return boolean true if the rule collection does not block the specified IP
	 */
	private function isNonBlockingIp( $options ) {
		if ( !isset( $options['currentUser'] ) ) {
			$user = 0;
		} else {
			$user = (int)$options['currentUser'];
		}

		//Flag to mark if we are about to add a rule. It is neeeded for the case when the first rule that is added
		//is on the non-blocking IP but doesn't use the master account
		$activeRules = false;

		//Get all the IDs of the rules currently active, this is needed so that
		$allRulesIds = $this->getActiveRulesIds();

		//Get the blacklist first so that if we find an IP match we can exist immediatly
		//Optimization by convention
		$rules = $this->getIpRules( array(
			'enabledIps' => true
			,'sort' => 'type'
			,'dir' => 'DESC'
			,'user' => $user
		));

		//Ignore a specific rule when computing
		if ( isset( $options['ignoreRuleId'] ) && is_numeric( $options['ignoreRuleId'] ) ) {
			//Remove the rule from the list of rules that will be provided to the check function
			foreach ( $rules as $key => $rule ) {
				if ( $rule['id'] == $options['ignoreRuleId'] ) {
					unset( $rules[$key] );
					break;
				}
			}
			//Remove the rule id from the list of all the active rules
			foreach ( $allRulesIds as $key => $rule ) {
				if ( $rule === (int)$options['ignoreRuleId'] ) {
					unset( $allRulesIds[$key] );
					break;
				}
			}
		}

		//Check to see if we still have active rules
		if ( count( $allRulesIds ) > 0 ) {
			$activeRules = true;
		}

		//Add to the rules array a new rule (this is used when we're about to create a new rule)
		if ( isset( $options['ruleToBeAdded'] ) && isset( $options['typeOfRule'] ) ) {
			//This rule will be added nevertheless, so even if it is not added to this user's specific
			//rule list, it will still be added to other users, so the system will be active
			$activeRules = true;
			if ( $options['typeOfRule'] === self::WHITELISTED ) {
				if (
					!isset( $options['users'] )
					|| ( isset( $options['users'] ) && empty( $options['users'] ) )
					|| ( isset( $options['users'] ) && is_array( $options['users'] ) && in_array( $options['currentUser'], $options['users'] ) )
				) {
					array_push( $rules, array(
						'ip' => $options['ruleToBeAdded']
						,'type' => self::DB_WHITELISTED
					));
				}
			} else {
				$rules = array_merge( array(array(
					'ip' => $options['ruleToBeAdded']
					,'type' => self::DB_BLACKLISTED
				)), $rules );
			}
		}

		if ( count( $rules ) > 0 ) {
			return $this->checkIpAgainstRules( $options['currentIp'], $rules);
		} else if ( $activeRules ) {
			//Some rules were not fetched for the specific user
			return false;
		} else {
			//If there are no active rules, then the system is deactivated
			return true;
		}
	}

	/**
	 * Checks whether an IP is allowed for a customer
	 * @param string $ip the ip to check
	 * @param int $user the user to check
	 * @return boolean
	 */
	public function checkIp( $ip, $user ) {
		// first check if IP is one of allowed ones from configuration
		if ( in_array( $ip , explode(',', ALLOWED_IPS ) ) ) {
			return true;
		}

		//Get the blacklist first so that if we find an IP we can exit immediatly
		$rules = $this->getIpRules( array(
			'enabledIps' => true
			,'sort' => 'type'
			,'dir' => 'DESC'
			,'user' => $user
		));
		return $this->checkIpAgainstRules( $ip, $rules );
	}

	/**
	 * Wrapper function to see if an IP passes the provided rules
	 * THE rules ARRAY MUST HAVE THE BLACKLISTED RULES FIRST and must already be filtered BY USER
	 * @param string $ip the IPv4 to check
	 * @param array $rules the rules against which the ip checked, this are custom tailord for the user
	 * @return boolean true if it passes, false otherwise
	 */
	private function checkIpAgainstRules( $ip, $rules ) {
		/**
		 * If there are no rules, all IPs are llowed
		 */
		if ( count( $rules ) === 0 ) {
			return true;
		}

		foreach ( $rules as $rule ) {
			if ( strpos( $rule['ip'], '|' ) === false ) {
				//This is a standard check
				if ( $ip === $rule['ip'] ) {
					if ( $rule['type'] === self::DB_BLACKLISTED ) {
						return false;
					} else {
						return true;
					}
				}
			} else {
				//This is a ranged check
				list( $startIp, $endIp ) = explode( '|', $rule['ip'], 2 );
				if ( $this->isInRange( $ip, $startIp, $endIp ) ) {
					if ( $rule['type'] === self::DB_BLACKLISTED ) {
						return false;
					} else {
						return true;
					}
				}
			}
		}
		//No rule matched the provided IP so it is out of scope, that is not allowed
		return false;
	}

	/**
	 * This function check wheather an Ip is within a specific range
	 * @param string $ip an IP in the IPv4 format
	 * @param string $startIp the lower boundry IP
	 * @param string $endIp the higher boundry IP
	 * @return boolean true if the checked Ip is within range, false otherwise
	 */
	private function isInRange( $ip, $startIp, $endIp ) {
		$startIp = (float)sprintf( "%u", ip2long( $startIp ) );
		$endIp = (float)sprintf( "%u", ip2long( $endIp ) );
		$ip = (float)sprintf( "%u", ip2long( $ip ) );
		return ( ( $ip >= $startIp ) && ( $ip <= $endIp ) );
	}

	/**
	 * Checks to see if the customer has IP rules set-up and active
	 * @return boolean true if the customer has IP Access Management active, false otherwise
	 */
	public function hasIpRules() {
		$cnt = (int)$this->database['ca']->numRows( 'ip_rules', "enabled = '1'");
		if ( $cnt > 0 ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Gets the details of a single rule
	 * @param int $id the id of the rule
	 * @return array string[] containing the details of a rule, if one is found, otherwise empty array
	 */
	private function getRule( $id ) {
		$query = "
			SELECT
				id
				,ip
				,enabled
				,type
				,label
				,last_update
				,ip_rules_accounts.account_id
			FROM
				ip_rules
			LEFT JOIN ip_rules_accounts
				ON ip_rules_accounts.rule_id = ip_rules.id
			WHERE
				id = '" . (int)$id . "'
		";

		$arr = (array)$this->database['ca']->queryGetRows( $query );

		$rule = array();

		foreach ( $arr as $value ) {
			if ( empty( $rule ) ) {
				$rule = array(
					'id' => (int)$value['id']
					,'ip' => $value['ip']
					,'enabled' => $value['enabled']
					,'type' => $value['type']
					,'label' => $value['label']
					,'last_update' => $value['last_update']
					,'users' => ''
				);
			}
			if ( strlen( $rule['users'] ) > 0 ) {
				$rule['users'] .= ',' . (int)$value['account_id'];
			} else {
				$rule['users'] .= (int)$value['account_id'];
			}
		}
		return $rule;
	}

	/**
	 * This is an utility function used by the isNonBlockingIp function,
	 * it just returns a array with the IDs of the active rules
	 * @return array an int[] containing the ID of the rules
	 */
	private function getActiveRulesIds() {
		$rules = $this->getIpRules(array(
			'enabledIps' => true
		));
		$arr = array();
		foreach ( $rules as $rule ) {
			$arr[] = (int)$rule['id'];
		}
		return $arr;
	}

	/**
	 * Utility function to constrcut the where part of the query for general searches of the rules
	 * @param array $options:
	 * 		- enabledIps: [true, false], empty( true + false )
	 *		- ipType: [whitelisted, blacklisted, empty( both )]
	 *		- user: int, optional user for which the rules will be applied, this
	 *			is used to fetch the rules filtered by user or NULL
	 * @return string
	 */
	private function getIpWhereQuery( $options ) {

		$enabledIps = '';
		if ( isset( $options['enabledIps'] ) ) {
			if ( $options['enabledIps'] === true ) {
				$enabledIps = ' AND enabled = 1 ';
			} elseif ( $options['enabledIps'] === false ) {
				$enabledIps = ' AND enabled = 0 ';
			}
		}

		$ipType = '';
		if ( isset( $options['ipType'] ) ) {
			if ( $options['ipType'] === self::WHITELISTED ) {
				$ipType = " AND type = '" . $this->database['ca']->escapeString( self::DB_WHITELISTED ) . "' ";
			} elseif ( $options['ipType'] === self::BLACKLISTED ) {
				$ipType ="' AND type = '" . $this->database['ca']->escapeString( self::DB_BLACKLISTED ) . "' ";
			}
		}

		$user = '';
		if ( isset( $options['user'] ) ) {
			$user = " AND ( ip_rules_accounts.account_id IS NULL OR ip_rules_accounts.account_id = '" . (int)$options['user'] . "' ) ";
		}

		return $enabledIps . $ipType . $user;
	}

	/**
	 * Gets the Ip rules ordered and fileterd and paginated
	 * @param array $options:
	 * 		- enabledIps: [true, false], empty( true + false )
	 *		- ipType: [whitelisted, blacklisted, empty( both )]
	 * 		- orderBy: string, column to order by and direction
	 *		- user: int, optional user for which the rules will be applied, this
	 *			is used to fetch the rules prioritized by user
	 * @return array
	 */
	public function getIpRules( $options = array(), array $modifiers = array() ) {
		$userPrioritySql = '';
		if ( !isset( $options['user'] ) ) {
			$user = 0;
		} else {
			$user = (int)$options['user'];
		}

		$columns = !empty( $modifiers['columns'] ) ? $modifiers['columns'] : self::$whitelist['allowed'];
		array_walk( $columns, function( &$val, $key ){
			if ( $val != $key && !is_int( $key ) ) {
				$val = $val . ' AS ' . $key;
			}
		} );
		$columnsSQL = implode( ',', $columns );

		$orderBy = '';
		// Sorters are used by secuningSorting for multi-column sorting
		if ( !empty( $options['sorters'] ) ) {
			// Process sorters and combine them in to an ORDER BY clause
			foreach ( $options['sorters'] as $column => &$direction ) {
				$direction = '`' . addcslashes( $this->database['ca']->escapeString( $column ), '`' ) . '` ' . ( $direction === 'ASC' ? 'ASC' : 'DESC' );
			}
			$orderBy = ' ORDER BY ' . implode(', ', $options['sorters']);
		} elseif ( isset( $options['sort'] ) && strlen( $options['sort'] ) > 0 ) {
			$orderBy = ' ORDER BY `' . addcslashes( $this->database['ca']->escapeString( $options['sort'] ), '`' ) . '` ';
			if ( isset( $options['dir'] ) ) {
				$orderBy .= strtoupper( $options['dir'] ) === 'ASC' ? ' ASC ' : ' DESC ';
			}
			if ( !empty( $user ) ) {
				$orderBy .= ", IF( '" . (int)$user . "' = ip_rules_accounts.account_id, 1, 0 ) DESC";
			}
		} else if ( !empty( $user ) ) {
			$orderBy = " ORDER BY IF( '" . (int)$user . "' = ip_rules_accounts.account_id, 1, 0 ) DESC";
		}

		$limitBy = '';
		if ( isset( $options['limit'] ) ) {
			$limitBy = " LIMIT " . (int)$options['limit'];
			if ( isset( $options['start'] ) ) {
				$limitBy = " LIMIT " . (int)$options['start'] . ", " . (int)$options['limit'];
			}
		}

		$query = "SELECT " . $columnsSQL . "
			FROM ip_rules
			LEFT JOIN ip_rules_accounts
				ON ip_rules_accounts.rule_id = ip_rules.id
			WHERE
				1 = 1 " . $this->getIpWhereQuery( $options ) . "
			GROUP BY id
			" . $orderBy . "
			" . $limitBy . "
		";

		if ( isset( $options['useResponseObject'] ) && $options['useResponseObject'] === true ) {
			return $this->privateDb->execRaw( $query );
		}

		$rules = (array)$this->database['ca']->queryGetRows( $query );

		$exportedRules = array();

		foreach ( $rules as $rule ) {
			if ( !isset( $exportedRules[ $rule['id'] ] ) ) {
				$exportedRules[ $rule['id'] ] = array(
					'id' => (int)$rule['id']
					,'ip' => $rule['ip']
					,'enabled' => (int)$rule['enabled']
					,'type' => $rule['type']
					,'label' => $rule['label']
					,'last_update' => $rule['last_update']
					,'users' => $rule['users']
				);
			}
		}

		//the framework's json_encode can't handle non-consecutive numerical ids
		return array_merge( array(), $exportedRules );
	}

	/**
	 * Gets the Ip rules total count
	 * @param array $options:
	 * 		- enabledIps: [true, false, empty( true + false )]
	 *		- ipType: [whitelisted, blacklisted, empty( both )]
	 *		- user: int, optional user for which the rules will be applied, this
	 *			is used to fetch the rules prioritized by user
	 */
	public function getIpRulesCount( $options = array() ) {
		if ( isset( $options['useResponseObject'] ) && $options['useResponseObject'] === true ) {
			return $this->privateDb->execRaw( 'SELECT COUNT(1) AS total FROM ip_rules WHERE 1 = 1 ' . $this->getIpWhereQuery( $options ) );
		}

		return (int)$this->database['ca']->numRows(
			'ip_rules'
			,"1 = 1 " . $this->getIpWhereQuery( $options )
		);
	}
}
