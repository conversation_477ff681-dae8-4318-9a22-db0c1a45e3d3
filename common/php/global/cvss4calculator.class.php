<?php

class cvss4calculator
{
    private function cvssConfig()
    {
        //Instead of building CVSS4 vector everytime from CVSSConfig, return vector with default values
        $cvssConfig['default_vector'] = 'CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:N/VI:N/VA:N/SC:N/SI:N/SA:N/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X';
        //'BaseScore' => $baseScore, 'BaseThreatScore' => $baseThreatScore, 'BaseEnvironmentalScore' => '', 'BaseThreatEnvironmentalScore'
        $cvssConfig['Base'] = array("AV", "AC", "AT", "PR", "UI", "VC", "VI", "VA", "SC", "SI", "SA");
        $cvssConfig['BaseThreat'] = array("AV", "AC", "AT", "PR", "UI", "VC", "VI", "VA", "SC", "SI", "SA", "E");
        $cvssConfig['BaseEnvironmental'] = array("AV", "AC", "AT", "PR", "UI", "VC", "VI", "VA", "SC", "SI", "SA", "CR",
            "IR", "AR", "MAV", "MAC", "MAT", "MPR", "MUI", "MVC",  "MVI", "MVA", "MSC", "MSI", "MSA");
        $cvssConfig['BaseThreatEnvironmental'] = array("AV", "AC", "AT", "PR", "UI", "VC", "VI", "VA", "SC", "SI", "SA", "E", "CR",
            "IR", "AR", "MAV", "MAC", "MAT", "MPR", "MUI", "MVC",  "MVI", "MVA", "MSC", "MSI", "MSA");

        return $cvssConfig;
    }

    private function cvssLookupGlobal()
    {
        return array(
            "000000" => 10,
            "000001" => 9.9,
            "000010" => 9.8,
            "000011" => 9.5,
            "000020" => 9.5,
            "000021" => 9.2,
            "000100" => 10,
            "000101" => 9.6,
            "000110" => 9.3,
            "000111" => 8.7,
            "000120" => 9.1,
            "000121" => 8.1,
            "000200" => 9.3,
            "000201" => 9,
            "000210" => 8.9,
            "000211" => 8,
            "000220" => 8.1,
            "000221" => 6.8,
            "001000" => 9.8,
            "001001" => 9.5,
            "001010" => 9.5,
            "001011" => 9.2,
            "001020" => 9,
            "001021" => 8.4,
            "001100" => 9.3,
            "001101" => 9.2,
            "001110" => 8.9,
            "001111" => 8.1,
            "001120" => 8.1,
            "001121" => 6.5,
            "001200" => 8.8,
            "001201" => 8,
            "001210" => 7.8,
            "001211" => 7,
            "001220" => 6.9,
            "001221" => 4.8,
            "002001" => 9.2,
            "002011" => 8.2,
            "002021" => 7.2,
            "002101" => 7.9,
            "002111" => 6.9,
            "002121" => 5,
            "002201" => 6.9,
            "002211" => 5.5,
            "002221" => 2.7,
            "010000" => 9.9,
            "010001" => 9.7,
            "010010" => 9.5,
            "010011" => 9.2,
            "010020" => 9.2,
            "010021" => 8.5,
            "010100" => 9.5,
            "010101" => 9.1,
            "010110" => 9,
            "010111" => 8.3,
            "010120" => 8.4,
            "010121" => 7.1,
            "010200" => 9.2,
            "010201" => 8.1,
            "010210" => 8.2,
            "010211" => 7.1,
            "010220" => 7.2,
            "010221" => 5.3,
            "011000" => 9.5,
            "011001" => 9.3,
            "011010" => 9.2,
            "011011" => 8.5,
            "011020" => 8.5,
            "011021" => 7.3,
            "011100" => 9.2,
            "011101" => 8.2,
            "011110" => 8,
            "011111" => 7.2,
            "011120" => 7,
            "011121" => 5.9,
            "011200" => 8.4,
            "011201" => 7,
            "011210" => 7.1,
            "011211" => 5.2,
            "011220" => 5,
            "011221" => 3,
            "012001" => 8.6,
            "012011" => 7.5,
            "012021" => 5.2,
            "012101" => 7.1,
            "012111" => 5.2,
            "012121" => 2.9,
            "012201" => 6.3,
            "012211" => 2.9,
            "012221" => 1.7,
            "100000" => 9.8,
            "100001" => 9.5,
            "100010" => 9.4,
            "100011" => 8.7,
            "100020" => 9.1,
            "100021" => 8.1,
            "100100" => 9.4,
            "100101" => 8.9,
            "100110" => 8.6,
            "100111" => 7.4,
            "100120" => 7.7,
            "100121" => 6.4,
            "100200" => 8.7,
            "100201" => 7.5,
            "100210" => 7.4,
            "100211" => 6.3,
            "100220" => 6.3,
            "100221" => 4.9,
            "101000" => 9.4,
            "101001" => 8.9,
            "101010" => 8.8,
            "101011" => 7.7,
            "101020" => 7.6,
            "101021" => 6.7,
            "101100" => 8.6,
            "101101" => 7.6,
            "101110" => 7.4,
            "101111" => 5.8,
            "101120" => 5.9,
            "101121" => 5,
            "101200" => 7.2,
            "101201" => 5.7,
            "101210" => 5.7,
            "101211" => 5.2,
            "101220" => 5.2,
            "101221" => 2.5,
            "102001" => 8.3,
            "102011" => 7,
            "102021" => 5.4,
            "102101" => 6.5,
            "102111" => 5.8,
            "102121" => 2.6,
            "102201" => 5.3,
            "102211" => 2.1,
            "102221" => 1.3,
            "110000" => 9.5,
            "110001" => 9,
            "110010" => 8.8,
            "110011" => 7.6,
            "110020" => 7.6,
            "110021" => 7,
            "110100" => 9,
            "110101" => 7.7,
            "110110" => 7.5,
            "110111" => 6.2,
            "110120" => 6.1,
            "110121" => 5.3,
            "110200" => 7.7,
            "110201" => 6.6,
            "110210" => 6.8,
            "110211" => 5.9,
            "110220" => 5.2,
            "110221" => 3,
            "111000" => 8.9,
            "111001" => 7.8,
            "111010" => 7.6,
            "111011" => 6.7,
            "111020" => 6.2,
            "111021" => 5.8,
            "111100" => 7.4,
            "111101" => 5.9,
            "111110" => 5.7,
            "111111" => 5.7,
            "111120" => 4.7,
            "111121" => 2.3,
            "111200" => 6.1,
            "111201" => 5.2,
            "111210" => 5.7,
            "111211" => 2.9,
            "111220" => 2.4,
            "111221" => 1.6,
            "112001" => 7.1,
            "112011" => 5.9,
            "112021" => 3,
            "112101" => 5.8,
            "112111" => 2.6,
            "112121" => 1.5,
            "112201" => 2.3,
            "112211" => 1.3,
            "112221" => 0.6,
            "200000" => 9.3,
            "200001" => 8.7,
            "200010" => 8.6,
            "200011" => 7.2,
            "200020" => 7.5,
            "200021" => 5.8,
            "200100" => 8.6,
            "200101" => 7.4,
            "200110" => 7.4,
            "200111" => 6.1,
            "200120" => 5.6,
            "200121" => 3.4,
            "200200" => 7,
            "200201" => 5.4,
            "200210" => 5.2,
            "200211" => 4,
            "200220" => 4,
            "200221" => 2.2,
            "201000" => 8.5,
            "201001" => 7.5,
            "201010" => 7.4,
            "201011" => 5.5,
            "201020" => 6.2,
            "201021" => 5.1,
            "201100" => 7.2,
            "201101" => 5.7,
            "201110" => 5.5,
            "201111" => 4.1,
            "201120" => 4.6,
            "201121" => 1.9,
            "201200" => 5.3,
            "201201" => 3.6,
            "201210" => 3.4,
            "201211" => 1.9,
            "201220" => 1.9,
            "201221" => 0.8,
            "202001" => 6.4,
            "202011" => 5.1,
            "202021" => 2,
            "202101" => 4.7,
            "202111" => 2.1,
            "202121" => 1.1,
            "202201" => 2.4,
            "202211" => 0.9,
            "202221" => 0.4,
            "210000" => 8.8,
            "210001" => 7.5,
            "210010" => 7.3,
            "210011" => 5.3,
            "210020" => 6,
            "210021" => 5,
            "210100" => 7.3,
            "210101" => 5.5,
            "210110" => 5.9,
            "210111" => 4,
            "210120" => 4.1,
            "210121" => 2,
            "210200" => 5.4,
            "210201" => 4.3,
            "210210" => 4.5,
            "210211" => 2.2,
            "210220" => 2,
            "210221" => 1.1,
            "211000" => 7.5,
            "211001" => 5.5,
            "211010" => 5.8,
            "211011" => 4.5,
            "211020" => 4,
            "211021" => 2.1,
            "211100" => 6.1,
            "211101" => 5.1,
            "211110" => 4.8,
            "211111" => 1.8,
            "211120" => 2,
            "211121" => 0.9,
            "211200" => 4.6,
            "211201" => 1.8,
            "211210" => 1.7,
            "211211" => 0.7,
            "211220" => 0.8,
            "211221" => 0.2,
            "212001" => 5.3,
            "212011" => 2.4,
            "212021" => 1.4,
            "212101" => 2.4,
            "212111" => 1.2,
            "212121" => 0.5,
            "212201" => 1,
            "212211" => 0.3,
            "212221" => 0.1
        );
    }

// CVSS v4.0 metrics ordering and valid values

    private function expectedMetricOrder()
    {
        return array(
            // Base (11 metrics)
            "AV" => array("N", "A", "L", "P"),
            "AC" => array("L", "H"),
            "AT" => array("N", "P"),
            "PR" => array("N", "L", "H"),
            "UI" => array("N", "P", "A"),
            "VC" => array("H", "L", "N"),
            "VI" => array("H", "L", "N"),
            "VA" => array("H", "L", "N"),
            "SC" => array("H", "L", "N"),
            "SI" => array("H", "L", "N"),
            "SA" => array("H", "L", "N"),
            // Threat (1 metric)
            "E" => array("X", "A", "P", "U"),
            // Environmental (14 metrics)
            "CR" => array("X", "H", "M", "L"),
            "IR" => array("X", "H", "M", "L"),
            "AR" => array("X", "H", "M", "L"),
            "MAV" => array("X", "N", "A", "L", "P"),
            "MAC" => array("X", "L", "H"),
            "MAT" => array("X", "N", "P"),
            "MPR" => array("X", "N", "L", "H"),
            "MUI" => array("X", "N", "P", "A"),
            "MVC" => array("X", "H", "L", "N"),
            "MVI" => array("X", "H", "L", "N"),
            "MVA" => array("X", "H", "L", "N"),
            "MSC" => array("X", "H", "L", "N"),
            "MSI" => array("X", "S", "H", "L", "N"),
            "MSA" => array("X", "S", "H", "L", "N"),
            // Supplemental (6 metrics)
            "S" => array("X", "N", "P"),
            "AU" => array("X", "N", "Y"),
            "R" => array("X", "A", "U", "I"),
            "V" => array("X", "D", "C"),
            "RE" => array("X", "L", "M", "H"),
            "U" => array("X", "Clear", "Green", "Amber", "Red"),
        );
    }

// max severity distances in EQs MacroVectors (+1)
    private function maxSeverity()
    {
        return array(
            "eq1" => array(
                0 => 1,
                1 => 4,
                2 => 5
            ),
            "eq2" => array(
                0 => 1,
                1 => 2
            ),
            "eq3eq6" => array(
                0 => array(0 => 7, 1 => 6),
                1 => array(0 => 8, 1 => 8),
                2 => array(1 => 10)
            ),
            "eq4" => array(
                0 => 6,
                1 => 5,
                2 => 4
            ),
            "eq5" => array(
                0 => 1,
                1 => 1,
                2 => 1
            ),
        );
    }

    private function maxComposed()
    {
        return array(
            // EQ1
            "eq1" => array(
                0 => array("AV:N/PR:N/UI:N/"),
                1 => array("AV:A/PR:N/UI:N/", "AV:N/PR:L/UI:N/", "AV:N/PR:N/UI:P/"),
                2 => array("AV:P/PR:N/UI:N/", "AV:A/PR:L/UI:P/")
            ),
            // EQ2
            "eq2" => array(
                0 => array("AC:L/AT:N/"),
                1 => array("AC:H/AT:N/", "AC:L/AT:P/")
            ),
            // EQ3+EQ6
            "eq3" => array(
                0 => array(
                    "0" => array("VC:H/VI:H/VA:H/CR:H/IR:H/AR:H/"),
                    "1" => array("VC:H/VI:H/VA:L/CR:M/IR:M/AR:H/", "VC:H/VI:H/VA:H/CR:M/IR:M/AR:M/")
                ),
                1 => array(
                    "0" => array("VC:L/VI:H/VA:H/CR:H/IR:H/AR:H/", "VC:H/VI:L/VA:H/CR:H/IR:H/AR:H/"),
                    "1" => array(
                        "VC:L/VI:H/VA:L/CR:H/IR:M/AR:H/",
                        "VC:L/VI:H/VA:H/CR:H/IR:M/AR:M/",
                        "VC:H/VI:L/VA:H/CR:M/IR:H/AR:M/",
                        "VC:H/VI:L/VA:L/CR:M/IR:H/AR:H/",
                        "VC:L/VI:L/VA:H/CR:H/IR:H/AR:M/"
                    )
                ),
                2 => array("1" => array("VC:L/VI:L/VA:L/CR:H/IR:H/AR:H/"))
            ),
            // EQ4
            "eq4" => array(
                0 => array("SC:H/SI:S/SA:S/"),
                1 => array("SC:H/SI:H/SA:H/"),
                2 => array("SC:L/SI:L/SA:L/")
            ),
            // EQ5
            "eq5" => array(
                0 => array("E:A/"),
                1 => array("E:P/"),
                2 => array("E:U/"),
            )
        );
    }

    private function getEQMaxes($lookup, $eq)
    {
        $maxComposed = $this->maxComposed();
        return $maxComposed["eq" . $eq][$lookup[$eq - 1]];
    }

    private function extractValueMetric($metric, $str)
    {
        // indexOf gives first index of the metric, we then need to go over its size
        $extracted = substr($str, strpos($str, $metric) + strlen($metric) + 1);

        // remove what follows
        if (strpos($extracted, '/') > 0) {
            $metric_val = substr($extracted, 0, strpos($extracted, '/'));
        } else {
            // case where it is the last metric so no ending /
            $metric_val = $extracted;
        }
        return $metric_val;
    }

    private function m($cvssSelected, $metric)
    {
        //All metrics may not be available in vector
        if (!isset($cvssSelected[$metric])) {
            return '';
        }
        $selected = $cvssSelected[$metric];

        // If E=X it will default to the worst case i.e. E=A
        if ($metric == "E" && $selected == "X") {
            return "A";
        }
        // If CR=X, IR=X or AR=X they will default to the worst case i.e. CR=H, IR=H and AR=H
        if (($metric == "CR" || $metric == "IR" || $metric == "AR") && $selected == "X") {
            return "H";
        }
        // IR:X is the same as IR:H
        if ($metric == "IR" && $selected == "X") {
            return "H";
        }
        // AR:X is the same as AR:H
        if ($metric == "AR" && $selected == "X") {
            return "H";
        }

        // All other environmental metrics just overwrite base score values,
        // so if they’re not defined just use the base score value.
        if (array_key_exists("M" . $metric, $cvssSelected)) {
            $modified_selected = $cvssSelected["M" . $metric];
            if ($modified_selected != "X") {
                return $modified_selected;
            }
        }

        return $selected;
    }

    private function macroVector($cvssSelected)
    {
        $eq1 = 0;
        $eq2 = 0;
        $eq3 = 0;
        $eq4 = 0;
        $eq5 = 0;
        $eq6 = 0;

        if ($this->m($cvssSelected, "AV") == "N" && $this->m($cvssSelected, "PR") == "N" && $this->m($cvssSelected,"UI") == "N") {
            $eq1 = 0;
        } elseif (($this->m($cvssSelected, "AV") == "N" || $this->m($cvssSelected, "PR") == "N" || $this->m($cvssSelected,"UI") == "N")
            && !($this->m($cvssSelected, "AV") == "N" && $this->m($cvssSelected, "PR") == "N" && $this->m($cvssSelected,"UI") == "N")
            && !($this->m($cvssSelected, "AV") == "P")) {
            $eq1 = 1;
        } elseif ($this->m($cvssSelected, "AV") == "P" || !($this->m($cvssSelected, "AV") == "N" || $this->m($cvssSelected, "PR") == "N"
                || $this->m($cvssSelected,"UI") == "N")) {
            $eq1 = 2;
        }

        if ($this->m($cvssSelected, "AC") == "L" && $this->m($cvssSelected, "AT") == "N") {
            $eq2 = 0;
        } elseif (!($this->m($cvssSelected, "AC") == "L" && $this->m($cvssSelected, "AT") == "N")) {
            $eq2 = 1;
        }

        if ($this->m($cvssSelected, "VC") == "H" && $this->m($cvssSelected, "VI") == "H") {
            $eq3 = 0;
        } elseif (!($this->m($cvssSelected, "VC") == "H" && $this->m($cvssSelected, "VI") == "H")
            && ($this->m($cvssSelected, "VC") == "H" || $this->m($cvssSelected, "VI") == "H" || $this->m($cvssSelected,"VA" ) == "H")) {
            $eq3 = 1;
        } elseif (!($this->m($cvssSelected, "VC") == "H" || $this->m($cvssSelected, "VI") == "H" || $this->m($cvssSelected,"VA") == "H")) {
            $eq3 = 2;
        }

        if ($this->m($cvssSelected, "MSI") == "S" || $this->m($cvssSelected, "MSA") == "S") {
            $eq4 = 0;
        } elseif (!($this->m($cvssSelected, "MSI") == "S" || $this->m($cvssSelected, "MSA") == "S") &&
            ($this->m($cvssSelected, "SC") == "H" || $this->m($cvssSelected, "SI") == "H" || $this->m($cvssSelected,"SA") == "H")) {
            $eq4 = 1;
        } elseif (!($this->m($cvssSelected, "MSI") == "S" || $this->m($cvssSelected, "MSA") == "S") &&
            !(($this->m($cvssSelected, "SC") == "H" || $this->m($cvssSelected, "SI") == "H" || $this->m($cvssSelected,"SA") == "H"))) {
            $eq4 = 2;
        }

        if ($this->m($cvssSelected, "E") == "A") {
            $eq5 = 0;
        } elseif ($this->m($cvssSelected, "E") == "P") {
            $eq5 = 1;
        } elseif ($this->m($cvssSelected, "E") == "U") {
            $eq5 = 2;
        }

        if (($this->m($cvssSelected, "CR") == "H" && $this->m($cvssSelected, "VC") == "H")
            || ($this->m($cvssSelected, "IR") == "H" && $this->m($cvssSelected, "VI") == "H")
            || ($this->m($cvssSelected, "AR") == "H" && $this->m($cvssSelected, "VA") == "H")) {
            $eq6 = 0;
        } elseif (!(($this->m($cvssSelected, "CR") == "H" && $this->m($cvssSelected, "VC") == "H")
            || ($this->m($cvssSelected, "IR") == "H" && $this->m($cvssSelected, "VI") == "H")
            || ($this->m($cvssSelected, "AR") == "H" && $this->m($cvssSelected, "VA") == "H"))) {
            $eq6 = 1;
        }

        return $eq1 . $eq2 . $eq3 . $eq4 . $eq5 . $eq6;
    }

    private function cvssScore($cvssSelected, $lookup, $maxSeverityData, $macroVectorResult)
    {
        // The following defines the index of each metric's values.
        // It is used when looking for the highest vector part of the
        // combinations produced by the MacroVector respective highest vectors.
        $AV_levels = ["N" => 0.0, "A" => 0.1, "L" => 0.2, "P" => 0.3];
        $PR_levels = ["N" => 0.0, "L" => 0.1, "H" => 0.2];
        $UI_levels = ["N" => 0.0, "P" => 0.1, "A" => 0.2];

        $AC_levels = ['L' => 0.0, 'H' => 0.1];
        $AT_levels = ['N' => 0.0, 'P' => 0.1];

        $VC_levels = ['H' => 0.0, 'L' => 0.1, 'N' => 0.2];
        $VI_levels = ['H' => 0.0, 'L' => 0.1, 'N' => 0.2];
        $VA_levels = ['H' => 0.0, 'L' => 0.1, 'N' => 0.2];

        $SC_levels = ['H' => 0.1, 'L' => 0.2, 'N' => 0.3];
        $SI_levels = ['S' => 0.0, 'H' => 0.1, 'L' => 0.2, 'N' => 0.3];
        $SA_levels = ['S' => 0.0, 'H' => 0.1, 'L' => 0.2, 'N' => 0.3];

        $CR_levels = ['H' => 0.0, 'M' => 0.1, 'L' => 0.2];
        $IR_levels = ['H' => 0.0, 'M' => 0.1, 'L' => 0.2];
        $AR_levels = ['H' => 0.0, 'M' => 0.1, 'L' => 0.2];

        $E_levels = ['U' => 0.2, 'P' => 0.1, 'A' => 0];


        // Exception for no impact on system (shortcut)
        if ($this->m($cvssSelected, "VC") == "N" && $this->m($cvssSelected, "VI") == "N" && $this->m($cvssSelected,"VA") == "N"
            && $this->m($cvssSelected, "SC") == "N" && $this->m($cvssSelected, "SI") == "N" && $this->m($cvssSelected,"SA") == "N") {
            return 0.0;
        }

        $value = $lookup[$macroVectorResult];

        // 1. For each of the EQs:
        //   a. The maximal scoring difference is determined as the difference
        //      between the current MacroVector and the lower MacroVector.
        //     i. If there is no lower MacroVector the available distance is
        //        set to NaN and then ignored in the further calculations.
        $eq1_val = intval($macroVectorResult[0]);
        $eq2_val = intval($macroVectorResult[1]);
        $eq3_val = intval($macroVectorResult[2]);
        $eq4_val = intval($macroVectorResult[3]);
        $eq5_val = intval($macroVectorResult[4]);
        $eq6_val = intval($macroVectorResult[5]);

        // compute next lower macro, it can also not exist
        $eq1_next_lower_macro = "" . ($eq1_val + 1) . $eq2_val . $eq3_val . $eq4_val . $eq5_val . $eq6_val;
        $eq2_next_lower_macro = "" . $eq1_val . ($eq2_val + 1) . $eq3_val . $eq4_val . $eq5_val . $eq6_val;

        // eq3 and eq6 are related
        if ($eq3_val == 1 && $eq6_val == 1) {
            // 11 --> 21
            $eq3eq6_next_lower_macro = "" . ($eq1_val . $eq2_val . ($eq3_val + 1) . $eq4_val . $eq5_val . $eq6_val);
        } elseif ($eq3_val == 0 && $eq6_val == 1) {
            // 01 --> 11
            $eq3eq6_next_lower_macro = "" . ($eq1_val . $eq2_val . ($eq3_val + 1) . $eq4_val . $eq5_val . $eq6_val);
        } elseif ($eq3_val == 1 && $eq6_val == 0) {
            // 10 --> 11
            $eq3eq6_next_lower_macro = "" . ($eq1_val . $eq2_val . $eq3_val . $eq4_val . $eq5_val . ($eq6_val + 1));
        } elseif ($eq3_val == 0 && $eq6_val == 0) {
            // 00 --> 01
            // 00 --> 10
            $eq3eq6_next_lower_macro_left = "" . ($eq1_val . $eq2_val . $eq3_val . $eq4_val . $eq5_val . ($eq6_val + 1));
            $eq3eq6_next_lower_macro_right = "" . ($eq1_val . $eq2_val . ($eq3_val + 1) . $eq4_val . $eq5_val . $eq6_val);
        } else {
            // 21 --> 32 (do not exist)
            $eq3eq6_next_lower_macro = "" . ($eq1_val . $eq2_val . ($eq3_val + 1) . $eq4_val . $eq5_val . ($eq6_val + 1));
        }

        $eq4_next_lower_macro = "" . ($eq1_val . $eq2_val . $eq3_val . ($eq4_val + 1) . $eq5_val . $eq6_val);
        $eq5_next_lower_macro = "" . ($eq1_val . $eq2_val . $eq3_val . $eq4_val . ($eq5_val + 1) . $eq6_val);

        // get their score, if the next lower macro score do not exist the result is NaN
        $score_eq1_next_lower_macro = isset($lookup[$eq1_next_lower_macro]) ? $lookup[$eq1_next_lower_macro] : NAN;
        $score_eq2_next_lower_macro = isset($lookup[$eq2_next_lower_macro]) ? $lookup[$eq2_next_lower_macro] : NAN;

        if ($eq3_val == 0 && $eq6_val == 0) {
            // multiple path take the one with higher score
            $score_eq3eq6_next_lower_macro_left = isset($lookup[$eq3eq6_next_lower_macro_left]) ? $lookup[$eq3eq6_next_lower_macro_left] : NAN;
            $score_eq3eq6_next_lower_macro_right = isset($lookup[$eq3eq6_next_lower_macro_right]) ? $lookup[$eq3eq6_next_lower_macro_right] : NAN;

            if ($score_eq3eq6_next_lower_macro_left > $score_eq3eq6_next_lower_macro_right) {
                $score_eq3eq6_next_lower_macro = $score_eq3eq6_next_lower_macro_left;
            } else {
                $score_eq3eq6_next_lower_macro = $score_eq3eq6_next_lower_macro_right;
            }
        } else {
            $score_eq3eq6_next_lower_macro = isset($lookup[$eq3eq6_next_lower_macro]) ? $lookup[$eq3eq6_next_lower_macro] : NAN;
        }

        $score_eq4_next_lower_macro = isset($lookup[$eq4_next_lower_macro]) ? $lookup[$eq4_next_lower_macro] : NAN;
        $score_eq5_next_lower_macro = isset($lookup[$eq5_next_lower_macro]) ? $lookup[$eq5_next_lower_macro] : NAN;

        //   b. The severity distance of the to-be scored vector from a
        //      highest severity vector in the same MacroVector is determined.
        $eq1_maxes = $this->getEQMaxes($macroVectorResult, 1);
        $eq2_maxes = $this->getEQMaxes($macroVectorResult, 2);
        $eq3_eq6_maxes = $this->getEQMaxes($macroVectorResult, 3)[$macroVectorResult[5]];
        $eq4_maxes = $this->getEQMaxes($macroVectorResult, 4);
        $eq5_maxes = $this->getEQMaxes($macroVectorResult, 5);

        // compose them
        $max_vectors = [];
        foreach ($eq1_maxes as $eq1_max) {
            foreach ($eq2_maxes as $eq2_max) {
                foreach ($eq3_eq6_maxes as $eq3_eq6_max) {
                    foreach ($eq4_maxes as $eq4_max) {
                        foreach ($eq5_maxes as $eq5max) {
                            $max_vectors[] = $eq1_max . $eq2_max . $eq3_eq6_max . $eq4_max . $eq5max;
                        }
                    }
                }
            }
        }

        // Find the max vector to use i.e. one in the combination of all the highests
        // that is greater or equal (severity distance) than the to-be scored vector.
        foreach ($max_vectors as $max_vector) {
            $severity_distance_AV = $AV_levels[$this->m($cvssSelected, "AV")] - $AV_levels[$this->extractValueMetric("AV", $max_vector)];
            $severity_distance_PR = $PR_levels[$this->m($cvssSelected, "PR")] - $PR_levels[$this->extractValueMetric("PR", $max_vector)];
            $severity_distance_UI = $UI_levels[$this->m($cvssSelected, "UI")] - $UI_levels[$this->extractValueMetric("UI", $max_vector)];

            $severity_distance_AC = $AC_levels[$this->m($cvssSelected, "AC")] - $AC_levels[$this->extractValueMetric("AC", $max_vector)];
            $severity_distance_AT = $AT_levels[$this->m($cvssSelected, "AT")] - $AT_levels[$this->extractValueMetric("AT", $max_vector)];

            $severity_distance_VC = $VC_levels[$this->m($cvssSelected, "VC")] - $VC_levels[$this->extractValueMetric("VC", $max_vector)];
            $severity_distance_VI = $VI_levels[$this->m($cvssSelected, "VI")] - $VI_levels[$this->extractValueMetric("VI", $max_vector)];
            $severity_distance_VA = $VA_levels[$this->m($cvssSelected, "VA")] - $VA_levels[$this->extractValueMetric("VA", $max_vector)];

            $severity_distance_SC = $SC_levels[$this->m($cvssSelected, "SC")] - $SC_levels[$this->extractValueMetric("SC", $max_vector)];
            $severity_distance_SI = $SI_levels[$this->m($cvssSelected, "SI")] - $SI_levels[$this->extractValueMetric("SI", $max_vector)];
            $severity_distance_SA = $SA_levels[$this->m($cvssSelected, "SA")] - $SA_levels[$this->extractValueMetric("SA", $max_vector)];

            $severity_distance_CR = $CR_levels[$this->m($cvssSelected, "CR")] - $CR_levels[$this->extractValueMetric("CR", $max_vector)];
            $severity_distance_IR = $IR_levels[$this->m($cvssSelected, "IR")] - $IR_levels[$this->extractValueMetric("IR", $max_vector)];
            $severity_distance_AR = $AR_levels[$this->m($cvssSelected, "AR")] - $AR_levels[$this->extractValueMetric("AR", $max_vector)];

            // if any is less than zero this is not the right max
            if (min(
                    [
                        $severity_distance_AV,
                        $severity_distance_PR,
                        $severity_distance_UI,
                        $severity_distance_AC,
                        $severity_distance_AT,
                        $severity_distance_VC,
                        $severity_distance_VI,
                        $severity_distance_VA,
                        $severity_distance_SC,
                        $severity_distance_SI,
                        $severity_distance_SA,
                        $severity_distance_CR,
                        $severity_distance_IR,
                        $severity_distance_AR
                    ]
                ) < 0) {
                continue;
            }
            // if multiple maxes exist to reach it is enough the first one
            break;
        }

        $current_severity_distance_eq1 = $severity_distance_AV + $severity_distance_PR + $severity_distance_UI;
        $current_severity_distance_eq2 = $severity_distance_AC + $severity_distance_AT;
        $current_severity_distance_eq3eq6 = $severity_distance_VC + $severity_distance_VI + $severity_distance_VA + $severity_distance_CR + $severity_distance_IR + $severity_distance_AR;
        $current_severity_distance_eq4 = $severity_distance_SC + $severity_distance_SI + $severity_distance_SA;
        $current_severity_distance_eq5 = 0;

        $step = 0.1;

        // if the next lower macro score do not exist the result is Nan
        // Rename to maximal scoring difference (aka MSD)
        $available_distance_eq1 = $value - $score_eq1_next_lower_macro;
        $available_distance_eq2 = $value - $score_eq2_next_lower_macro;
        $available_distance_eq3eq6 = $value - $score_eq3eq6_next_lower_macro;
        $available_distance_eq4 = $value - $score_eq4_next_lower_macro;
        $available_distance_eq5 = $value - $score_eq5_next_lower_macro;

        $percent_to_next_eq1_severity = 0;
        $percent_to_next_eq2_severity = 0;
        $percent_to_next_eq3eq6_severity = 0;
        $percent_to_next_eq4_severity = 0;
        $percent_to_next_eq5_severity = 0;

        // some of them do not exist, we will find them by retrieving the score. If score null then do not exist
        $n_existing_lower = 0;

        $normalized_severity_eq1 = 0;
        $normalized_severity_eq2 = 0;
        $normalized_severity_eq3eq6 = 0;
        $normalized_severity_eq4 = 0;
        $normalized_severity_eq5 = 0;

        // multiply by step because distance is pure
        $maxSeverity_eq1 = $maxSeverityData["eq1"][$eq1_val] * $step;
        $maxSeverity_eq2 = $maxSeverityData["eq2"][$eq2_val] * $step;
        $maxSeverity_eq3eq6 = $maxSeverityData["eq3eq6"][$eq3_val][$eq6_val] * $step;
        $maxSeverity_eq4 = $maxSeverityData["eq4"][$eq4_val] * $step;

        //   c. The proportion of the distance is determined by dividing
        //      the severity distance of the to-be-scored vector by the depth
        //      of the MacroVector.
        //   d. The maximal scoring difference is multiplied by the proportion of
        //      distance.
        if (!is_nan($available_distance_eq1)) {
            $n_existing_lower = $n_existing_lower + 1;
            $percent_to_next_eq1_severity = ($current_severity_distance_eq1) / $maxSeverity_eq1;
            $normalized_severity_eq1 = $available_distance_eq1 * $percent_to_next_eq1_severity;
        }

        if (!is_nan($available_distance_eq2)) {
            $n_existing_lower = $n_existing_lower + 1;
            $percent_to_next_eq2_severity = ($current_severity_distance_eq2) / $maxSeverity_eq2;
            $normalized_severity_eq2 = $available_distance_eq2 * $percent_to_next_eq2_severity;
        }

        if (!is_nan($available_distance_eq3eq6)) {
            $n_existing_lower = $n_existing_lower + 1;
            $percent_to_next_eq3eq6_severity = ($current_severity_distance_eq3eq6) / $maxSeverity_eq3eq6;
            $normalized_severity_eq3eq6 = $available_distance_eq3eq6 * $percent_to_next_eq3eq6_severity;
        }

        if (!is_nan($available_distance_eq4)) {
            $n_existing_lower = $n_existing_lower + 1;
            $percent_to_next_eq4_severity = ($current_severity_distance_eq4) / $maxSeverity_eq4;
            $normalized_severity_eq4 = $available_distance_eq4 * $percent_to_next_eq4_severity;
        }

        if (!is_nan($available_distance_eq5)) {
            // for eq5 is always 0 the percentage
            $n_existing_lower = $n_existing_lower + 1;
            $percent_to_next_eq5_severity = 0;
            $normalized_severity_eq5 = $available_distance_eq5 * $percent_to_next_eq5_severity;
        }

        // 2. The mean of the above computed proportional distances is computed.
        if ($n_existing_lower == 0) {
            $mean_distance = 0;
        } else { // sometimes we need to go up but there is nothing there, or down but there is nothing there so it's a change of 0.
            $mean_distance = ($normalized_severity_eq1 + $normalized_severity_eq2 + $normalized_severity_eq3eq6 + $normalized_severity_eq4 + $normalized_severity_eq5) / $n_existing_lower;
        }

        // 3. The score of the vector is the score of the MacroVector
        //    (i.e. the score of the highest severity vector) minus the mean
        //    distance so computed. This score is rounded to one decimal place.
        $value -= $mean_distance;
        if ($value < 0) {
            $value = 0.0;
        }
        if ($value > 10) {
            $value = 10.0;
        }
        return round($value * 10) / 10;
    }

    /**
     * @param $vector
     * @return array
     */
    private function transformVectorToArray($vector)
    {
        $vectorArray = array();

        $metrics = explode('/', $vector);

        // Ignore if first values is CVSS4.0
        if ($metrics[0] === 'CVSS:4.0') {
            array_shift($metrics);
        }

        foreach ($metrics as $metric) {
            $key_value_pair = explode(':', $metric);

            if (count($key_value_pair) === 2) {
                // Add the pair to the result array
                $vectorArray[$key_value_pair[0]] = $key_value_pair[1];
            }
        }
        return $vectorArray;
    }

    private function transformArravToVector($vectorArray)
    {
        $metrics = array();

        foreach ($vectorArray as $key => $value) {
            if ($key != '' and $value != '') {
                $metrics[] = $key . ':' . $value;
            }
        }
        $metricSting = implode('/', $metrics);
        return 'CVSS:4.0/' . $metricSting;
    }

    private function buildCvssSelected(array $default_vector, array $toSelect, $filter = '')
    {
        $cvssSelected = array();
        if ($filter != '') {
            $cvssConfig = $this->cvssConfig();
            $toSelect = array_intersect_key($toSelect, array_flip($cvssConfig[$filter]));
        }

        foreach ($default_vector as $key => $value) {
            if (isset($toSelect[$key])) {
                $cvssSelected[$key] = $toSelect[$key];
            } else {
                $cvssSelected[$key] = $value;
            }
        }
        return $cvssSelected;
    }


    /**
     *
     * Process
     * Check $vector have mandatory 11 Base metrics. Compare against $cvssConfig['Base']
     * Build CVSS4 vector with default values when not in $vector. This newly build vector is used only for calculation
     * To get Base score, from $vector keep Base metrics and replace others with default. Calculate score.
     * To get BaseThreat score, from $vector keep Base and Threat metrics and replace others with default. Calculate score.
     * To get BaseEnvironmental score, from $vector keep Base and Environmental metrics and replace others with default Calculate score.
     * To get BaseThreatEnvironmental score, from $vector keep Base, Threat and Environmental metrics and replace others with default. Calculate score.
     *
     */
    public function fCVSS4Calc($vector)
    {
        $cvssSelected = array(); //feed vector in array format to get required score
        $cvssConfig = $this->cvssConfig();

        //Transform vector received to array
        $toSelect = $this->transformVectorToArray($vector);

        //Check mandatory base metrics available
        if (count($toSelect) >= 11) {
            foreach ($cvssConfig['Base'] as $base_metric) {
                if (!isset($toSelect[$base_metric])) {
                    return "Error invalid vector, missing mandatory base metric " . $base_metric;
                }
            }
        }

        //Transform vector received to array
        $default_vector = $this->transformVectorToArray($cvssConfig['default_vector']);

        //Build $cvssSelected. Loop through default values & set values from vector where available
        //$cvssSelects['OverAll'] = $this->buildCvssSelected($default_vector, $toSelect, '');//As in webpage
        $cvssSelects['Base'] = $this->buildCvssSelected($default_vector, $toSelect,'Base');//By default, considers Exploit Maturity(E) as Attacked(A)
        $cvssSelects['BaseThreat'] = $this->buildCvssSelected($default_vector, $toSelect, 'BaseThreat');
        //$cvssSelects['BaseEnvironmental'] = $this->buildCvssSelected($default_vector, $toSelect, 'BaseEnvironmental');
        //$cvssSelects['BaseThreatEnvironmental'] = $this->buildCvssSelected($default_vector, $toSelect,'BaseThreatEnvironmental');//Supplemental not added, matches with OverAll

        $result = array();
        $lookup = $this->cvssLookupGlobal();
        $maxSeverityData = $this->maxSeverity();

        foreach ($cvssSelects as $key => $cvssSelected) {
            $macroVectorResult = '';
            if (count($cvssSelected) >= 11) {
                $macroVectorResult = $this->macroVector($cvssSelected);
                $result[$key . 'Score'] = $this->cvssScore($cvssSelected, $lookup, $maxSeverityData, $macroVectorResult);
            } else {
                return "Error invalid vector, missing mandatory metrics";
            }
        }

        return array(
            'BaseScore' => $result['BaseScore'],
            'BaseThreatScore' => $result['BaseThreatScore']
        );

        /*return array(
            'OverAllScore' => $result['OverAllScore'],
            'BaseScore' => $result['BaseScore'],
            'BaseThreatScore' => $result['BaseThreatScore'],
            'BaseEnvironmentalScore' => $result['BaseEnvironmentalScore'],
            'BaseThreatEnvironmentalScore' => $result['BaseThreatEnvironmentalScore']
        );*/
    }
}