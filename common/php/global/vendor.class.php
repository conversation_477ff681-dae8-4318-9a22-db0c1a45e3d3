<?php
/**
 * @file vendor.class.php
*/

/**
 * Provides vendor specific functionality.
*/
class VENDOR {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for fetching a vendor link.
	 * @param vendorId
	 *	Integer vendor id
	 * @return
	 *	String vendor link
	*/
	function getVendorLink( $vendorId ) {
		return $this->database['ca']->getRowValue("vuln_track.vendor", "vendor_hp", "vendor.vendor_id = '".(int)$vendorId."'");
	}

	/**
	 * Function for fetching a vendor name.
	 * @param vendorId
	 *	Integer vendor id
	 * @return
	 *	String vendor name
	*/
	function getVendorName( $vendorId ) {
		return $this->database['ca']->getRowValue("vuln_track.vendor", "vendor_name", "vendor.vendor_id = '".(int)$vendorId."'");
	}
	/**
	 * Function for fetching vendor products that can be displayed.
	 * @param vendorId
	 *	Integer vendor id
	 * @return
	 *	Mixed array of product id's
	*/
	function fetchProducts( $vendorId ) {
		$query = "SELECT
				os_soft_id
			FROM
				vuln_track.os_soft
			WHERE
				vendor_id = '".(int)$vendorId."'
		";

		return $this->database['ca']->queryGetRows( $query );
	}
}
?>