<?php
/**
 * @file websiteSlideshow.class.php
 * Provides CMS functionality for static page items
*/

class websiteStaticPageItems {

	/**
	 * Function for fetching page items data
	 * @param path
	 *	String path of the fetched item
	 * @return
	 *	Resource containing the row(s) for Static Page Items
	*/
	function fetchData( $path = '' ) {
		$result = $GLOBALS['database']->getRows( "website.static_page_items",( ( $path != "" ) ? "path = '".$path."'" : "" ), "path ASC, align ASC, position ASC", "" );

		if ( count( $result ) == 0 ) {
			$GLOBALS['debug']->error( "Cannot fetch static page items data" );
			return false;
		} else {
			$GLOBALS['debug']->notice( "Static page items data fetched" );
			return $result;
		}
	}

	/**
	 * Function for constructing the static page items array.
	 * @param path
	 *	String current path, no leading /, ending /. Eg: current/path/
	 * @return
	 *	Array consisting of page items. Format: array( array( (int)Type, (int)ItemId, (int)Position ) ...). To be consumed by website: global/functions.php 
	*/
	function constructItems( $path ) {
		$results = $this->fetchData( $path );
		$array = array();
		for ( $i = 0; $i < count( $results ); $i++ ) {
			array_push( $array, array( $results[$i]['itemtype'], $results[$i]['item'], $results[$i]['align'] ) );
		}

		return $array;
	}
}
?>