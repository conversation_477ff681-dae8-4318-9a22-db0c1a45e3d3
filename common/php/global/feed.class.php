<?php

  /*
   * Convenience class for writing xml / rss feeds
   * @todo: functionality for reading
   */

class Feed {

	protected $writer;

	/*
	 * Private constructor. For creating objects, the factory functions should be used.
	 * e.g. Feed::writer();
	 */
	private function __construct( $element ) {
		switch( true ) {
		case is_string( $element ):
			$this->writer = new SimpleXMLElement( '<?xml version="1.0" encoding="ISO-8859-1" standalone="yes"?><' . $element . '/>' );
			break;
		case $element instanceof SimpleXMLElement:
			$this->writer = $element;
			break;
		}
	}

	public function child( $name, $content = null ) {
		$content = self::format( $content );
		$child = $this->writer->addChild( $name, $content );

		$f = new Feed( $child );
		return $f;
	}

	// Add attribute to the last element added
	public function attribute( $name, $value ) {
		$this->writer->addAttribute( $name, $value );
		return $this;
	}

	public function asXML() {
		return $this->writer->asXML();
	}


	/*
	 * Make the content is safe for adding as a child.
	 *
	 * @param $content Integer | String
	 * The function doesn't validate the input since it is private.
	 */
	public static function format( $content ) {

		switch( true ) {
		case is_string( $content ):
			/*
			 * Since we have set the encoding to ISO-8859-1 in the root element of the xml,
			 * SimpleXml will try to convert the strings to ISO-8859-1. I believe that the
			 * library assumes that the strings are already in UTF and then tries to convert
			 * them into ISO-8859-1. This assumption is based on empirical analysis.
			 * Therefore, if we try to convert the strings directly to ISO-8859-1 here, the
			 * SimpleXMLElement::asXML() crashes as it tries to call iconv and doesn't find
			 * valid UTF-8 chars to be converted.
			 *
			 * I don't know why the library assumes that the characters are already in UTF-8.
			 * But tests have shown that by keeping all strings in UTF-8 and relying on
			 * SimpleXml to do our conversion to the encoding (specified in the root element
			 * of the xml) works !
			 */
			$content = JSON::validUTF( $content );

			/*
			 * Handling Ampersands:
			 * -------------------
			 * & is not handled by the addChild() function. Other chars such as < and >
			 * are converted to the html entities. For the &, we can use htmlspecial chars
			 * for encoding. This is okay as the addChild() function will not replace the
			 * special chars in the html entities.
			 * e.g.
			 * &    : Not converted
			 * <    : &lt;
			 * &lt; : &lt;  (No further conversion of the special chars)
			 *
			 *
			 * Encoding:
			 * --------
			 * According to the documentation htmlspecialchars() has the same effect for UTF-8
			 * and ISO-8859-1 strings, therefor there is not need for specifying the encoding.
			 *
			 * Flags:
			 * -----
			 * Ideally we should use the ENT_XML1 flag but that is not available in php 5.3.
			 *
			 */
			$content = UTIL::htmlspecialchars( $content );

			// @todo: do we need the nl2br() ?? Doesn't make any sense for having this in
			// an xml
			// $content = nl2br( $content );

			break;
		case is_numeric( $content ):
			break;
		default:
			return false;
		}

		/*
		 * We don't need CDATA if the content is numeric but this is the current
		 * schema for all the feeds.
		 *
		 * @todo: change later, maybe have a static flag to control this..
		 * However, CDATA can not be provided like this.. as the special chars will be
		 * encoded by the addChild() function.
		 */
		// $content = '<![CDATA[' . $content	. ']]>';

		return $content;
	}

	/*
	 * Factory Functions
	 */

	public static function writer( $rootElementName, array $attributes = NULL ) {
		$f = new Feed( $rootElementName );
		if ( !empty( $attributes ) ) {
			foreach( $attributes as $name => $value ) {
				$f->attribute( $name, $value );
			}
		}
		return $f;
	}

}