<?php
final class UnknownException {
	public function handleException() {
		var_dump( $this );
	}
}

final class MySQLi_Exception extends Exception {
	public function handleException() {
		var_dump( $this );
	}
}

class DATABASEi extends mysqli {
	public function __construct( $hostname, $username, $password, $database = "" ) {
		try {
			// Summon chronos
			$this->time = microtime( true );
			// Prepare connection
			@parent::__construct( $hostname, $username, $password, $database );
			// Create statement
			if ( mysqli_connect_error() ) {
				throw new MySQLi_Exception( "Database connection error: " . mysqli_connect_error() );
			}
		} catch ( MySQLi_Exception $e ) {
			$e->handleException();
		} catch  ( UnknownException  $e ) {
			$e->handleException();
		}
	}

	public function prepare( $query ) {
		try {
			if ( !$this->statement = parent::prepare( $query ) ) {
				throw new MySQLi_Exception( "Statement prepare error: " . $this->error );
			}
			return true;
		} catch ( MySQLi_Exception $e ) {
			$e->handleException();
			return false;
		}
	}

	public function in_query( $parameters ) {
		try {
			// Construct bind parameters
			$types = "";
			foreach ( $parameters as $key => $parameter ) {
				if ( is_double( $parameter ) ) {
					$types .= "d";
				} elseif( is_integer( $parameter ) ) {
					$types .= "i";
				} else {
					$types .= "s";
				}
			}

			$bind_function_parameters = array( $types );
			$bind_function_parameters = array_merge( $bind_function_parameters, $parameters );

			if ( !call_user_func_array( array(
				$this->statement
				,"bind_param"
			), $bind_function_parameters ) ) {
				throw new MySQLi_Exception( "Parameter binding error: " . $this->error );
			}

			if ( !$this->statement->execute() ) {
				throw new MySQLi_Exception( "Statement execution error: " . $this->error );
			}

			return true;
		} catch ( MySQLi_Exception $e ) {
			$e->handleException();
			return false;
		}
	}

	public function get_query( $parameters, &$results ) {
		try {
			if ( $this->in_query( $parameters ) ) {
				// Construct Fields
				$result = $this->statement->result_metadata();
				$_result = array();
				$fields = array();
				while ( $field = $result->fetch_field() ) {
					$fields[] = $field->name;
					$_result[] = &${$field->name};
				}

				if ( !call_user_func_array( array(
					$this->statement
					,"bind_result"
				), $_result ) ) {
					throw new MySQLi_Exception( "Result binding error: " . $this->error );
				}

				$results = array();
				$i = 0;
				while ( $this->statement->fetch() ) {
					$results[$i] = array();
					foreach ( $fields as $key => $name ) {
						$results[$i][$name] = $$name;
					}
					$i++;
				}
			}
		} catch ( MySQLi_Exception $e ) {
			$e->handleException();
			return false;
		}
	}

	public function __destruct() {
		@$this->statement->close();
		@$this->close();
		$lapsed = microtime( true ) - $this->time;
		echo  "Execution time: ".round( $lapsed, 2 ). " seconds\n";
	}
}

$db = new DATABASEi("localhost", "root", "", "mysqli");

$db->prepare("SELECT DISTINCT vuln_title FROM vuln_track.vuln WHERE vuln_title LIKE ?");
$db->get_query( array(
		"%".$argv[1]."%"
	), $result
);

$db->prepare("INSERT INTO ca.accounts SET account_username = ?");
$db->in_query( array( "test_" ) );

/*
echo count( $result )."\n";
unset( $db );
mysql_connect("localhost", "root", "");
$time = microtime( true );
$result = mysql_query("SELECT DISTINCT vuln_title FROM vuln_track.vuln WHERE vuln_title LIKE '%".$argv[1]."%'");
$arr = array();
for ( $i = 0; $i < mysql_num_rows( $result ); $i++ ) {
	$arr[] = mysql_fetch_assoc( $result );
}
echo count( $arr )."\n";
$lapsed = microtime( true ) - $time;
echo  "Execution time (classic): ".round( $lapsed, 2 ). " seconds\n";
*/
?>