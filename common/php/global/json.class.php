<?php
/**
 * @file json.class.php
 * Provides global json functionality.
 *
*/
class JSON {
	/**
	 * Log the class being loaded
	*/
	function __construct() {
	}

	/*
	 * Function for checking if a string is encoded correctly in UTF-8
	 * This is more accurate regarding RFC3629 than PHP's mb_check_encoding()
	 */
	private static function checkUTF8( $str ) {
		$len = strlen( $str );
		for ( $i = 0; $i < $len; $i++ ) {
			$c = ord( $str[$i] );
			if ( $c > 128 ) {
				if ( $c > 247) {
					return false;
				} elseif ( $c > 239 ) {
					$bytes = 4;
				} elseif ( $c > 223 ) {
					$bytes = 3;
				} elseif ( $c > 191 ) {
					$bytes = 2;
				} else {
					return false;
				}
				if ( ( $i + $bytes ) > $len ) {
					return false;
				}
				while ( $bytes > 1 ) {
				        $i++;
				        $b = ord( $str[$i] );
				        if ( $b < 128 || $b > 191 ) {
				        	return false;
				        }
				        $bytes--;
				}
			}
		}
		return true;
	}

	/**
	 * Function for removing 'invalid' UTF characters. ( Fixing a IE bug. )
	 *
	 * @todo: the function should go to a central place e.g. OUTPUT
	 */
	public static function validUTF( $string ) {
		// Note: preg_match() here crashes the PHP httpd thread given a long enough string on some PHP 5.3 configurations such as RHEL 5 official packages
		// Regexp according to: http://www.w3.org/International/questions/qa-forms-utf-8
		//if ( preg_match('%^(?:[\x09\x0A\x0D\x20-\x7E]|[\xC2-\xDF][\x80-\xBF]|\xE0[\xA0-\xBF][\x80-\xBF]|[\xE1-\xEC\xEE\xEF][\x80-\xBF]{2}|\xED[\x80-\x9F][\x80-\xBF]|\xF0[\x90-\xBF][\x80-\xBF]{2}|[\xF1-\xF3][\x80-\xBF]{3}|\xF4[\x80-\x8F][\x80-\xBF]{2})*$%xs', $string ) ) { // If valid UTF-8
		if ( self::checkUTF8( $string ) ) {
			return $string;
		} else { // If invalid UTF-8, convert
			return iconv('ISO-8859-1', 'UTF-8', $string);
		}
	}

	/**
	 * Format a values in the json response:
	 *
	 * The values could be boolean, integers or strings.
	 * In case of strings, we need to take care of the following:
	 * - Backslashes should be escaped.
	 * - The value should be valid UTF8
	 * - The value should be html encoded
	 * - We are also converting the new lines to html equivalents i.e. <br>
	 *
	 * @param $value
	 *	String subject
	 * @param $stringEnclosure String
	 *  The string enclosed by character should always be a double quote if the value
	 *  will end up in a JSON response. However, for other types of outputs e.g. csv, xml
	 *  the string enclosure can be changed to e.g. no character.
	 *
	 * @return String
	 *   Formatted value, safe to echo to the client's browser.
	 */
	public static function format( $value, $stringEnclosure = '"' ) {
		if ( is_bool( $value ) ) {
			return ( $value ? 'true' : 'false' );
		} else if ( is_int( $value ) || is_float( $value ) ) {
			return strval( $value );
		} else if ( is_string( $value ) ) {
			$string = str_replace( "\\", "\\\\", htmlspecialchars( self::validUTF( $value ) ) );
			$string = nl2br( $string );
			$string = str_replace( array( "\r", "\n" ), "", $string );
			return $stringEnclosure . $string . $stringEnclosure; // "_value_"
		} else {
			// Don't know how to format
			return $stringEnclosure . $stringEnclosure; // ""
		}
	}

	/**
	 * Returns the JSON representation of an array, having htmlspecialchars applied on each entry. Similar to PHP json_encode, without the second paramater.
	 *
	 * NOTE: Fields and columns defined in the ExtJS Object making use of the result should be the same as those in the database ( when using data from db ).
	 * NOTE: If an array has numeric keys without any holes ( i.e. list of consecutive positive integers starting from 0), the result would be an Array, otherwise an Object.
	 * Example of a JSON array:
	 *
	 * INPUT:
	 * $json['total'] = 1;
	 * $json['data'][0] = array(
	 *	"id" => 1
	 *	,"field1" => "text1"
	 *	,"field2" => "text2"
	 * );
	 *
	 * OUTPUT:
	 * {
	 *	"total": 1
	 *	"data": {
	 *		"id": 1
	 *		,"field1": "text1"
	 *		,"field2": "text2"
	 *	}
	 * }
	 *
	 * @param Array|Object $input    Array of strings consisting of json items to be displayed
	 * @param Boolean      $internal If set to true, it will write a debug log
	 *
	 * @return String JSON representation of the array.
	 *
	 * @todo: this function should be static e.g. JSON::encode()
	 */
	public function json_encode( $input, $internal = false ) {
		$output = '';
		if ( is_object( $input ) ) {
			$input = get_object_vars( $input );
		}
		if ( !is_array( $input ) ) {
			return $output;
		}
		if ( $GLOBALS['util']->numericKeys( array_keys( $input ), true ) ) {
			$output = '[';
			for ( $i = 0, $size = count( $input ); $i < $size; ++$i ) {
				if ( $i !== 0 ) {
					$output .= ",";
				}
				// Only recursive json if '$input[$i]' is an array
				if ( is_array($input[$i]) ) {
					$output .= $this->json_encode( $input[$i], true );
				} elseif ( is_object( $input[$i] ) ) {
					$output .= $this->json_encode( get_object_vars( $input[$i] ), true );
				} else {
					$output .= self::format( $input[$i] );
				}
			}
			$output .= "]";
		} else {
			$output = "{";
			$first = true;
			foreach ( $input as $key => $value ) {
				if ( $first ) {
					$first = false;
				} else {
					$output .= ",";
				}
				$output .= self::format( $key ).":";
				if ( is_array($value) ) {
					$output .= $this->json_encode( $value, true );
				} elseif ( is_object( $value ) ) {
					$output .= $this->json_encode( get_object_vars( $value ), true );
				} else {
					$output .= self::format( $value );
				}
			}
			$output .= "}";
		}
		if ( $internal !== true ) {
			$GLOBALS['debug']->debug( "Json encode: " . $output );
		}
		return $output;
	}

	/**
	 * Function for generating an ExtJS form answer.
	 *
	 * @param Boolean $success True or false if the form submission was successful or not.
	 * @param String  $message Message for the client side, to be displayed in case of an error, OPTIONAL
	 *
	 * @return String json object
	*/
	public function formAnswer( $success, $message = "" ) {
		$result['success'] = $success;
 		if ( $message !== "" ) {
			$result['msg'] = $message;
		}

		return $this->json_encode( $result );
	}

	/**
	 * Encodes an array as JSON while doing extra processing on the string values.
	 * String values have htmlspecialchars applied to the them, have any line endings
	 * converting to html <br> tags then are converted to UTF-8.
	 *
	 * @param mixed $data
	 * @return string JSON string with UTF-8 encoded string values
	 */
	public static function encodeArray( $data ) {
		if ( !is_array($data) ) {
			return;
		}
		// Prepare the object - Requires PHP 5.3+
		array_walk_recursive( $data, function ( &$v, $k ) {
			if ( is_string( $v ) ) {
				$v = htmlspecialchars( JSON::validUTF( $v ) , ENT_COMPAT );
				$v = str_replace( array( "\r", "\n" ), '', nl2br( $v ) );
			} elseif ( is_null( $v ) ) {
				$v = strval($v);
			} // elseif is_object
		} );
		return (string) json_encode( $data );
	}
}
