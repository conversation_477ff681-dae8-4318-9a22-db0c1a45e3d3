<?php
/**
 * @file users.class.php
*/

/**
 * Provides user/shadow user functionality.
*/
class USERS {


	/**
	 * Cache for holding subaccounts of account so that we don't have
	 * to calculate them over and over again. The cache is an array
	 * indexed by the customer_id and the account_id
	 */
	private $subAccountsCache;

	function __construct() {
		// TODO - set up DB connection properly - all the accounts table queries here should use 'common'
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

 	/**
	 * Function for fetching all the levels of accounts above a given account id in the parental chain
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Array  list of accout ids in the parental chain above the input id - does NOT include the original input ID.
	*/
	function getParentalChain( $accountId, $chainSoFar = 0 ) {

		if ( !$chainSoFar ) {
			$chainSoFar = array();
		}

		// Note - an account can only have one parent so this is simple
		$parentalId = $this->database['ca']->getRowValue( "ca.accounts", "account_esm", "account_id = '" . (int) $accountId . "'" );
		if ( !$parentalId ) {
			return $chainSoFar;
		} else {
			$chainSoFar[] = (int) $parentalId;
			return $this->getParentalChain( $parentalId, $chainSoFar );
		}
	}

 	/**
	 * Function for fetching all the non-shadow children accounts below a given account id in the parental chain
	 * @param accountId
	 *	Integer account id
	 *
	 * @return
	 *	Array  list of accout ids in the parental chain below the input id - does NOT include the original input ID.
	 *
	 * @todo
	 *   Execute a single query that filters the accounts based on the customer id
	 *   and iterate the rows to find the subaccounts. @see getSubAccounts()
	 *
	 */
	// ENHANCEMENT - these 'family' functions do not currently include cst_id or modules.  Should they?  See csiUsers.class.php->accountOwner for example.
	function getChildChain( $accountId, $chainSoFar = 0, $counter = 0 ) {

		if ( $counter > 30 ) {
			return $chainSoFar;
		}

		if ( !$chainSoFar ) {
			$chainSoFar = array();
		}

		// Note - an account can have multiple children
		$childIds = $this->database['ca']->getRows( "ca.accounts", "account_esm = '" . (int) $accountId . "' AND shadow = 0 " );
		$counter++;

		if ( $childIds ) {
			for ( $i = 0; $i < count( $childIds ); $i++ ) {
				$thisChildId = (int) $childIds[$i]['account_id'];
				$chainSoFar[] = $thisChildId;
				$chainSoFar = $this->getChildChain( $thisChildId, $chainSoFar );
			}
		}
		return $chainSoFar;
	}

 	/**
	 * Helper function to get the parental and child chains to give them whole family of accounts associated with any one account
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Array  list of accout ids in the family chain of the input id.  I.e., parents, itself, and children
	*/
	function getAccountFamily( $accountId ) {

		$parentArray = $this->getParentalChain( $accountId );
		$childArray = $this->getChildChain( $accountId );
		$familyArray = array( (int) $accountId );

		if ( 0 < count($parentArray) ) {
			$familyArray = array_merge( $familyArray, $parentArray );
		}
		if ( 0 < count($childArray) ) {
			$familyArray = array_merge( $familyArray, $childArray );
		}
		return $familyArray;
	}



	/**
	 * Function for fetching the root account id
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Integer root accout id
	*/
	function getRoot( $accountId ) {
		$account_esm = $this->database['ca']->getRowValue( "ca.accounts", "account_esm", "account_id = '" . (int) $accountId . "'" );
		if ( !$account_esm ) {
			$result = $accountId; // The root is the root...
		} else {
			$result = $this->getRoot( $account_esm ); // Keep searching until we find an account, above this one, that does not have a parent. (root)
		}
		return $result;
	}

	/**
	 * Function for fetching XML permissions.
	 *
	 * NOTE: The xml_access column is only applicable to the Intelligence Feeds
	 *
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Boolean true or false
	*/
	function getXMLInfo( $accountId ) {
		$xmlAccess = $this->database['ca']->getRowValue( "ca.accounts", "xml_access", "account_id = '".(int) $accountId ."'" );
		if ( $xmlAccess == 1 ) {
			return true;
		}
		return false;
	}

	/**
	 * Special function for creating a new user account.
	 * @param username
	 *	String account username
	 * @param name
	 *	String account owner name
	 * @param customerId
	 *	Integer customer id
	 * @param contacts
	 *	Integer number of assigned contacts
	 * @param starts
	 *	Integer starting date
	 * @param expires
	 *	Integer expiration date
	 * @param special
	 *	String special, special limits such as beta
	 * @param quantity
	 *	String quantity
	 * @param password
	 *	String password, optional
	 * @param isEscaped
	 *	String data is escaped or not for mysql query
	 * @return
	 *	Mixed false or array, having the username and password
	*/
	function newVimUser( $username, $name, $customerId, $contacts, $starts, $expires, $special, $quantity, $password = "", $isEscaped = false  ) {
		// Escape strings
		if ( $isEscaped !== true ) {
			$starts = DATABASE::escapeString( $starts );
			$expires = DATABASE::escapeString( $expires );
			$name = DATABASE::escapeString( $name );
			$username = DATABASE::escapeString( $username );
			$special = DATABASE::escapeString( $special );
			$password = ( $password != "" ? DATABASE::escapeString( $password ) : rand(1000,9999) );
			$contacts = (int)$contacts;
			$customerId = (int)$customerId;
		}

		// Begin add account
		if ( $this->database['ca']->getRow('ca.accounts', "account_username = '".$username."'") ) {
			return false; // Account already in database.
		}

		$sQuery = "INSERT INTO ca.accounts SET " .
			"account_name = '" . $name . "', " .
			"account_username = '" . $username . "', " .
			"account_password = password('" . $password . "'), " .
			"account_gen_pwd = '1', " .
			"account_login_type = '1', " .
			"modules = 3743, " .
			"show_modules = 3743, " .
			"assigned_contacts = ".$contacts.", " .
			"account_recv_all = '1', " . // it seems like this option is deprecated. The modules OPT_ASSET_RECV_ALL is used instead.
			"cst_id = '" . $customerId . "', " .
			"account_expires = '" . $expires . "', " .
			"lang_id = '1', " .
			"special_limits = '".$special."'," .
			"shadow = 0 ";

		$this->database['ca']->query( $sQuery );
		$accountId = $this->database['ca']->getInsertId();

		// Begin add license
		$key = $GLOBALS['license']->generateKey( "BCDFGHJKLMNPQRSTVWXYZ" );
		$values = array(
			"license" => $key
			,"account_id" => $accountId
			,"created" => $starts . " 00:00:00"
			,"valid_from" => $starts . " 00:00:00"
			,"valid_to" => $expires . " 23:59:59"
			,"activated" => $starts . " 00:00:00"
			,"quantity" => (int)$quantity
			,"type" => "132"
		);
		$GLOBALS['license']->addLicense( $values, $isEscaped = false );

		// Done.
		if ( $accountId != 0 ) {
			return array(
				"password" => $password
				,"account_id" => $accountId
			);
		} else {
			return false;
		}
	}

	/**
	 * Function for checking if the user, is sub user for the current account.
	 * @param accountId
	 *	Integer current user account id
	 * @param childId
	 *	Integer child account id
	 * @return
	 *	Boolean true or false if child or not
	 *
	 * // Enhancement - getChildChain should be more efficient than getSubAccounts
	*/
	function isChild( $accountId, $childId ) {
		if ( $childId == $accountId ) {
			return true; // It is itself...
		}
		$users = $this->getSubAccounts( $accountId, true );
		for ( $i = 0; $i < count( $users ); $i++ ) {
			if ( $users[$i]['account_id'] == $childId ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Function for validating a list of users as being valid child accounts of a parent user.
	 * This is an efficiency enhancement to avoid calling isChild for every element in an array
	 * and doing a lot of redundant processing.
	 *
	 * @param accountId
	 *	Integer : the parent user account id we validate against
	 *
	 * @param idListToValidate
	 *  Mixed : either an array or a comma separated string of potential child ids to validate
	 *
	 * @return
	 *	Array : will be equal to or a subset of idListToValidate, containing only validated ids.
	 *
	*/
	function validateAccountList( $accountId, $idListToValidate ) {

		$childList = $this->getChildChain( $accountId );
		$validatedList = array();

		// If $idListToValidate is not an array, it is a csv string - make it an array
		if ( !is_array($idListToValidate) ) {
			$idListToValidate = explode( ",", $idListToValidate );
		}

		foreach ( $idListToValidate as $thisId ) {
			if ( ($thisId == $accountId) || in_array( $thisId,  $childList ) ) {
				$validatedList[] = (int) $thisId;
			}
		}

		return $validatedList;
	}

	/**
	 * Function for fetching sub account ids in array form.
	 * @param accountId
	 *	Integer account id
	 * @param skipShadows
	 *	Boolean skip the shadow accounts, default to false.
	 * @return
	 *	Array of sub account ids with metadata (i.e., shadow property, username, esm)
	 *
	 * @deprecated
	 * @use getSubAccs()
	 */
	function getSubAccounts( $accountId, $skipShadows = false, $recursive = false ) {
		$accountId = (int) $accountId;
		if ( !$recursive ) {
			$this->subAccounts = array();
		}

		$results = $this->database['ca']->getRows( "ca.accounts"
							   ,"account_esm = '" . (int) $accountId . "' " .
							   ( $skipShadows ? " AND shadow = 0 " : "" ) );

		for ( $i = 0; $i < count( $results ); $i++ ) {
			array_push( $this->subAccounts
					,array( "account_id" => (int) $results[$i]['account_id']
						,"account_name" => $results[$i]['account_name']
						,"account_username" => $results[$i]['account_username']
						,"account_esm" => (int) $results[$i]['account_esm']
						,"agent_uid" => $results[$i]['agent_uid']
						,"modules" => $results[$i]['modules']
						,"shadow" => (int) $results[$i]['shadow']
						// auth_type is used in VIM's csiUserManagement::createShadowLoginList()
						,"auth_type" => (int) $results[$i]['auth_type']
					)
			);
			$this->getSubAccounts( (int) $results[$i]['account_id'], $skipShadows, true );
		}

		return $this->subAccounts;
	}


	/**
	 * Returns all the subaccount including the root account
	 */
	public function getSubAccs( $customerId, $accountId ) {

		if ( isset( $this->subAccountsCache[ $customerId ][ $accountId ] ) ) {
			return $this->subAccountsCache[ $customerId ][ $accountId ];
		}

		$this->fetchSubAccs( $customerId, $accountId );

		return $this->subAccountsCache[ $customerId ][ $accountId ];
	}

	/**
	 * Function for getting the sub account ids
	 */
	public function getSubAccIds( $customerId, $accountId, $excludeRoot = false, $excludeShadow = false ) {
		$accounts = $this->getSubAccs( $customerId, $accountId );


		// Filter out shadow accounts
		if ( $excludeShadow ) {
			$accounts = array_filter( $accounts, function( $row ) { return ( $row['shadow'] == '0' ); } );
		}

		// Get only the ids
		$ids = UTIL::array_column( $accounts, 'account_id' );

		// Remove the current account id
		if ( $excludeRoot ) {
			$index = array_search( $accountId, $ids );
			array_splice( $ids, $index, 1 );
		}


		return $ids;

	}

	private function fetchSubAccs( $customerId, $accountId ) {
		if ( !is_numeric( $customerId ) || $customerId < 1 ) {
			throw new Exception( 'Invalid customer Id' );
		}
		if ( !is_numeric( $accountId ) || $accountId < 1 ) {
			throw new Exception( 'Invalid account Id' );
		}

		$condition = "cst_id = " . (int) $customerId;
		$rows = $this->database['ca']->getRows("ca.accounts", $condition );

		// Conver the data into a tree format
		$tree = new Tree();
		$tree->import( $rows, 'account_id', 'account_esm' );

		// Get all the subaccounts
		$subAccounts = $tree->getChildren( $accountId, 'array' );

		$this->subAccountsCache[ $customerId ][ $accountId ] = $subAccounts;
	}

	/**
	 * Function for creating a comma separated list of an accountId and all its sub-account IDs.
	 * @param accountId
	 *	Integer account id
	 * @param skipShadows
	 *	Boolean skip the shadow accounts, default to false.
	 * @return
	 *	CSV list of account ids, made up of the passed in ID and all related sub-account IDs
	 *	
	*/
	function getCommaSeparatedSubAccountIds( $accountId, $skipShadows = false ) {

		$accountIdList = (int) $accountId;
		$subAccountIds = $GLOBALS['users']->getSubAccounts( $accountId, $skipShadows );
		for ( $i = 0; $i < count( $subAccountIds ); $i++ ) {
			$accountIdList .= "," . (int) $subAccountIds[$i]['account_id'];
		}
		return $accountIdList;
	}


	/**
	 * Function for building a user's module string.
	 * @param modules
	 *	Array of enabled modules, having key => true or false
	 * @param parentModules
	 *	Integer parent modules
	 * @return
	 *	Integer number
	*/
	function buildModules( $modules, $parentInfo ) {
		$mod = MOD_NONE | MOD_ACCOUNT | MOD_BA | MOD_ESM | MOD_NSI | MOD_SM | MOD_SUPPORT | MOD_UM | MOD_VDB | MOD_VSS | MOD_VTS | MOD_VTSE;
		if ( !( isset( $modules['mod_sm'] ) && $modules['mod_sm'] == "on" ) || $parentInfo->modules->MOD_SM !== true ) {
			$mod = $mod & ~MOD_SM;
		}
		if ( !( isset( $modules['mod_esm'] ) && $modules['mod_esm'] == "on" ) || $parentInfo->modules->MOD_ESM !== true ) {
			$mod = $mod & ~MOD_ESM;
		}
		if ( !( isset( $modules['mod_vm'] ) && $modules['mod_vm'] == "on" ) || $parentInfo->modules->MOD_VTS !== true ) {
			$mod = $mod & ~MOD_VTS;
		}
		if ( !( isset( $modules['mod_vtse'] ) && $modules['mod_vtse'] == "on" ) || $parentInfo->modules->MOD_VTSE !== true ) {
			$mod = $mod & ~MOD_VTSE;
		}
		if ( !( isset( $modules['mod_vss'] ) && $modules['mod_vss'] == "on" ) || $parentInfo->modules->MOD_VSS !== true ) {
			$mod = $mod & ~MOD_VSS;
		}
		if ( !( isset( $modules['mod_nsi'] ) && $modules['mod_nsi'] == "on" ) || $parentInfo->modules->MOD_NSI !== true ) {
			$mod = $mod & ~MOD_NSI;
		}
		if ( !( isset( $modules['mod_ba'] ) && $modules['mod_ba'] == "on" ) || $parentInfo->modules->MOD_BA !== true ) {
			$mod = $mod & ~MOD_BA;
		}
		if ( !( isset( $modules['mod_vdb'] ) && $modules['mod_vdb'] == "on" ) || $parentInfo->modules->MOD_VDB !== true ) {
			$mod = $mod & ~MOD_VDB;
		}
		if ( !( isset( $modules['mod_support'] ) && $modules['mod_support'] == "on" ) || $parentInfo->modules->MOD_SUPPORT !== true ) {
			$mod = $mod & ~MOD_SUPPORT;
		}
		if ( !( isset( $modules['mod_account'] ) && $modules['mod_account'] == "on" ) || $parentInfo->modules->MOD_ACCOUNT !== true ) {
			$mod = $mod & ~MOD_ACCOUNT;
		}
		if ( !( isset( $modules['mod_um'] ) && $modules['mod_um'] == "on"  ) || $parentInfo->modules->MOD_UM !== true ) {
			$mod = $mod & ~MOD_UM;
		}

		return  $mod;
	}

	/**
	 * Function for fetching the user's modules.
	 * NOTE: STUB Refactor the naming and figure a conversion method for that. Keep as it is for compatibility.
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Object having enabled modules, having the key as module name and the value set to true if enabled
	*/
	function getUserModules( $accountId ) {
		$modules = $this->database['ca']->getRowValue( "ca.accounts", "modules", "account_id = '".(int)$accountId."'" );
		$result = new stdClass();
		$result->modules = 0; // Count modules
		$result->enabled_modules = $modules;
		$result->MOD_NONE = false; // Set this bit, to prevent any other checks, where needed
		$result->MOD_NSI = false; // Deprecated naming, NSI -> CSI
		$result->MOD_SM = false;
		$result->MOD_VTS = false; // Deprecated naming, VTS -> VM
		$result->MOD_VTSE = false;
		$result->MOD_VSS = false;
		$result->MOD_ESM = false; // Deprecated naming, ESM -> Management and Administration
		$result->MOD_BA = false;
		$result->MOD_VDB = false;
		$result->MOD_SUPPORT = false;
		$result->MOD_ACCOUNT = false;
		$result->MOD_UM = false;

		if ( MOD_NONE & $modules ) {
			$result->MOD_NONE = true;
		}
		if ( MOD_NSI & $modules ) {
			$result->MOD_NSI = true;
			$result->modules++;
		}
		if ( MOD_SM & $modules ) {
			$result->MOD_SM = true;
			$result->modules++;
		}
		if ( MOD_VTS & $modules ) {
			$result->MOD_VTS = true;
			$result->modules++;
		}
		if ( MOD_VTSE & $modules ) {
			$result->MOD_VTSE = true;
			$result->modules++;
		}
		if ( MOD_VSS & $modules ) {
			$result->MOD_VSS = true;
			$result->modules++;
		}
		if ( MOD_ESM & $modules ) {
			$result->MOD_ESM = true;
			$result->modules++;
		}
		if ( MOD_BA & $modules ) {
			$result->MOD_BA = true;
			$result->modules++;
		}
		if ( MOD_VDB & $modules ) {
			$result->MOD_VDB = true;
			$result->modules++;
		}
		if ( MOD_SUPPORT & $modules ) {
			$result->MOD_SUPPORT = true;
			$result->modules++;
		}
		if ( MOD_ACCOUNT & $modules ) {
			$result->MOD_ACCOUNT = true;
			$result->modules++;
		}

		if ( MOD_UM & $modules ) {
			$result->MOD_UM = true;
			$result->modules++;
		}

		return $result;
	}

	/**
	 * Function for fetching username.
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	String user name
	*/
	function getUserName( $accountId ) {
		return $this->database['ca']->getRowValue( "ca.accounts", "account_username", "account_id = '".(int)$accountId."'" );
	}


	/**
	 * Get the list of permissions for a shadow account.
	 * NOTE: The database has the columns switched. shadow_id being the id of the account that's being shadowd, while the account_id is the account of the shadow itself.
	 * @param shadowId
	 *	Integer shadow account id
	 * @return
	 *	Object having an array of permissions
	*/
	function getShadowPermissions( $shadowId ) {
		$shadowId = (int)$shadowId;

		$userPermissions = new stdClass();
		$userPermissions->users = array();
		$results = $this->database['ca']->getRows( "ca.shadow_accounts", "account_id = '".$shadowId."'" );
		for ( $i = 0; $i < count( $results ); $i++ ) {
			$userPermissions->users[$i] = new stdClass();
			$userPermissions->users[$i]->accountId = $results[$i]['shadow_id']; // The account that's being shadowed...
			$userPermissions->users[$i]->rights = $results[$i]['shadow_rights']; // Rights
			$userPermissions->users[$i]->shadow_advisories = $results[$i]['shadow_advisories']; // Hard coded permission entry, for the advisories
		}

		return $userPermissions;
	}

	/**
	 * Function for cecking if account is expired or not.
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Boolean true if it is NOT expired, false if it is expired.
	*/
	function isExpired( $accountId ) {
		$result = $this->database['ca']->numRows("ca.accounts", "account_id = '".(int)$accountId."' AND account_expires >= NOW()");
		if ( $result == 0 ) {
			return false;
		}
		return true;
	}

	/**
	 * Function for fetching customer account id's
	 * @param cstId
	 *	Integer account id
	 * @return
	 *	Array Array of account id's.
	*/
	function getCstAccountIDs( $cst_id ) {
		$result = $this->database['ca']->getRows("ca.accounts", "cst_id = " . (int) $cst_id );
		$ids = array(); // This is an empty array
		foreach ( $result as $account ) {
			array_push( $ids, $account['account_id'] );
		}
		return $ids;
	}

	/**
	 * Function for fetching user details.
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Object having the details
	*/
	function getDetails( $accountId ) {
		$accountId = (int)$accountId;
		$details = new stdClass();

		$details->contact = new stdClass();
		$details->contact->email = $this->database['ca']->getRowValue( "ca.contact_method", "contact_method_value", "account_id = '".$accountId."' AND pos = 1 AND contact_method_type = 1" );
		$details->contact->mobile = $this->database['ca']->getRowValue( "ca.contact_method", "contact_method_value", "account_id = '".$accountId."' AND pos = 1 AND contact_method_type = 2" );
		$details->contact->name = $this->database['ca']->getRowValue("ca.accounts", "account_name", "account_id = '".$accountId."'");
		$details->cst_id = $this->database['ca']->getRowValue("ca.accounts", "cst_id", "account_id = '".$accountId."'");

		return $details;
	}

	/**
	 * Function for fetching user account info.
	 * @note VIM Only
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Object having user details, such as: accounts ( total, used, shadow, available )
	*/
	function userInfo( $accountId, $root = true ) {
		$accountId = (int)$accountId;

		$info = new stdClass();
		if ( $root == true ) {
			$info->parent = (int)$this->database['ca']->getRowValue("ca.accounts", "account_esm", "account_id = '".$accountId."'");
		}
		$info->username = $this->getUserName( $accountId );
		$info->accounts = new stdClass();
		$info->rootAccount = $this->getRoot( $accountId );
		$info->accountId = $accountId;
		$info->expires = $this->database['ca']->getRowValue("ca.accounts", "account_expires", "account_id = '".$accountId."'");
		// We use the product type to make the difference between Small Business Edition or the Enterprise Edition
		// The database has a account_product_type which makes the difference by using the shown modules,
		// however in VIM 3.0 we need some extra difference, so we will use the special limits for that purpose.
		$info->productType = $this->database['ca']->getRowValue("ca.accounts", "special_limits", "account_id = '".$accountId."'");
		$info->accounts->total = (int)$this->database['ca']->getRowValue("ca.esm", "no_users", "master_account_id = '".$accountId."'");
		$info->accounts->used = $this->database['ca']->numRows("ca.accounts", "account_esm = '".$accountId."' AND shadow = 0");
		$info->accounts->shadows = $this->database['ca']->numRows("ca.accounts", "account_esm = '".$accountId."' AND shadow > 0");
		$info->details = $this->getDetails( $accountId );
		$info->shadow = (int)$this->database['ca']->getRowValue("ca.accounts", "shadow", "account_id = '".$accountId."'");
		if ( $info->shadow == 0 ) {
			$info->assigned_contacts = $this->database['ca']->getRowValue("ca.accounts", "assigned_contacts", "account_id = '".$accountId."'");
			$info->used_contacts = $this->database['ca']->numRows("ca.contact_method", "account_id = '".$accountId."' AND contact_method_type = 1"); // Count self...

			// Fetch contacts that have been assigned to sub users
			$subAccounts = $this->getSubAccounts( $accountId );
			$sqlIn = "";
			for ( $i = 0; $i < count( $subAccounts ); $i++ ) {
				$sqlIn .= ( $sqlIn != "" ? "," : "" ) . (int)$subAccounts[$i]['account_id'];
			}
			if ( $sqlIn != "" ) {
				$info->used_subuser_contacts = (int) $this->database['ca']->getRowValue( "ca.accounts", "SUM( assigned_contacts )", "account_id IN (".$sqlIn.")" );
			} else { // User does not have any sub users. Do not bother the database.
				$info->used_subuser_contacts = 0;
			}
		} else {
			$info->assigned_contacts = 1;
			$info->used_contacts = 1;
			$info->used_subuser_contacts = 0;
			$info->available_contacts = 1;
		}
		$info->modules = $this->getUserModules( $accountId );
		if ( $info->shadow != 0 ) {
			$info->permissions = $this->getShadowPermissions( $accountId );
		} else {
			$info->administrator = ( $info->accounts->total != 0 ) ? 1 : 0;
		}
		$info->master = (int)$this->database['ca']->getRowValue("ca.accounts", "account_esm", "account_id = '".$accountId."'");
		$info->accounts->administrator = 0;

		$subAccounts = $this->getSubAccounts( $accountId );
		if ( count( $subAccounts ) != 0 ) {
			$info->subaccounts = new stdClass();
			$info->shadowaccounts = new stdClass();
			$info->subaccounts->account = array();
			$info->shadowaccounts->account = array();
			for ( $i = 0; $i < count( $subAccounts ); $i++ ) {
				if ( (int)$subAccounts[$i]['shadow'] == 0 ) {
					$count = count( $info->subaccounts->account );
					$info->subaccounts->account[$count] = new stdClass();
					$info->subaccounts->account[$count]->accountId = (int)$subAccounts[$i]['account_id'];
					$info->subaccounts->account[$count]->info = $this->userInfo( (int)$subAccounts[$i]['account_id'], false );
					$info->accounts->administrator += $info->subaccounts->account[$count]->info->accounts->total;
				} else {
					$count = count( $info->shadowaccounts->account );
					$info->shadowaccounts->account[$count] = new stdClass();
					$info->shadowaccounts->account[$count]->accountId = (int)$subAccounts[$i]['account_id'];
					$info->shadowaccounts->account[$count]->info = $this->userInfo( (int)$subAccounts[$i]['account_id'], false );
					$info->shadowaccounts->account[$count]->permissions = $this->getShadowPermissions( (int)$subAccounts[$i]['account_id'] );
				}
			}
		}

		if ( $info->shadow == 0 ) {
			$info->available_contacts = $info->assigned_contacts - $info->used_subuser_contacts - $info->used_contacts - $info->accounts->shadows;
		}
		$info->accounts->available = $info->accounts->total - $info->accounts->shadows - $info->accounts->administrator - $info->used_contacts;
		return $info;
	}

}