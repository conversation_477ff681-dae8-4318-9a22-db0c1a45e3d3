<?php
/**
 * @file database.class.php
 * Provides database functionality
*/

/**
 * Secunia global database class.
 *
 * You should always use this class for interacting with a underlying MySQL database.
 *
 * Generally, you should never reference PHP's native mysql_* functions directly.
 *
 * Open a database connection:
 * @code
 * $db = new DATABASE( 'server', 'user', 'pass' );
 * @endcode
 *
 */
class DATABASE {
	const INSERT = 0;
	const UPDATE = 1;
	const REPLACE = 2;

	public static $connection;

	/**
	 * @var array
	 * Instances of the DATABASE class i.e. common, private etc
	 */
	private static $instances = array();

	/*
	 * @var Psr\Log\LoggerInterface $logger
	 */
	private $logger = null;


	private function cleanSyntax( $string ) {
		return str_replace( array( "\t", "\r\n", "\n" ), " ", $string);
	}

	/**
	 * DATABASE constructor. For each database connection, a new instance of DATABASE should be created
	 *
	 * @param host
	 *	String: Host
	 * @param user
	 *	String: Username
	 * @param password
	 *	String: Password
	 */
	function __construct( $host, $user, $password, $utcMode = false ) {
		$this->logger = isset( $GLOBALS['debug'] ) ? $GLOBALS['debug'] : new Psr\Log\NullLogger();
		$this->db_selected = "";
		$this->openLink( $host, $user, $password, $utcMode );
	}

	/*
	 * Setter for the logger object
	 * @param Psr\Log\LoggerInterface $logger Use the logger to debug
	 */
	public function setLogger( Psr\Log\LoggerInterface $logger ) {
		$this->logger = $logger;
	}

	/**
	 * This does the same thing as the constructor for an existing
	 * object. You probably want to create a new database object for
	 * each connection instead of using this method, but it is
	 * sometimes useful together with the closeLink method.
	 *
	 * This method does not close the previous connection.
	 *
	 * @param host
	 *	String: Host
	 * @param user
	 *	String: Username
	 * @param password
	 *	String: Password
	 */
	function openLink( $host, $user, $password, $utcMode = false ) {
		if ( isset( self::$connection[$host][$user] ) ) {
			$this->link = self::$connection[$host][$user];
			$this->logger->notice( "Using cached database connection" );
		} else {
			$this->link = mysqli_connect( $host, $user, $password );
			self::$connection[$host][$user] = $this->link;
			if ( $this->link == null ) {
				$this->logger->notice( "Cannot connect to database as $user@$host" );
				// Don't just exit here - throw an exception instead
				// exit(1);
				throw new Exception( "Cannot connect to database as $user@$host" );
			} else {
				if ( $utcMode ) {
					$setUTCResult = mysqli_query( $this->link, "set time_zone='+0:00'");
					if ( ! $setUTCResult ) {
						$this->logger->notice( "Failed to set database connection in UTC timezone" );
					}
				}
				$this->logger->notice( "Connected to database" );
			}
		}
		$this->host = $host;
		$this->user = $user;
	}

	/**
	 * Close a specific database link ( hostname / user pair ).
	 * Default parameters makes it close the current link which may be
	 * shared with other objects!
	 *
	*/
	function closeLink( $host = NULL, $user = NULL) {
		if( $host == NULL ) {
			$host = $this->host;
		}
		if( $user == NULL ) {
			$user = $this->user;
		}
		if ( isset( self::$connection[$host][$user] ) ) {
			if ( mysqli_close( self::$connection[$host][$user] ) ) {
				unset( self::$connection[$host][$user] );
				$this->logger->notice( "Closed database connection to host '" . $host . "' with user '" . $user . "'" );
			} else {
				$this->logger->notice( "Failed to close database connection to host '" . $host . "' with user '" . $user . "'" );
			}
		} else {
			$this->logger->notice( "Attempted to close non-existing database connection to host '" . $host . "' with user '" . $user . "'" );
		}
	}

	/**
	 * Return the internal database object
	 * Try not to use this method
	 */
	public function getLink() {
		return $this->link;
	}

	/**
	 * Select a default database for this database connection
	 *
	 * @param dbname
	 *  String: The name of the default database
	 */
	public function selectDB( $dbname ) {
		$this->db_selected = $dbname;
		mysqli_select_db( $this->link, $this->db_selected );
		$error = mysqli_error($this->link);
		if ( $error != "" ) {
			$this->logger->notice( "Select database error: " . $error );
			return false;
		}
		return true;
	}

	/**
	 * Function for returning a MySQL PASSWORD hash.
	 *
	 * @param password
	 *	String: Input password to be hashed.
	 * @param isEscaped
	 *	Boolean: FALSE to apply escapeString() on the $password passed. TRUE to use data as is.
	 *
	 * @return
	 *	String: Hash from MySQL PASSWORD
	*/
	public function makePassword( $password, $isEscaped = false ) {
		if ( $isEscaped !== true ) {
			$password = $this->escapeString( $password );
		}
		$result = $this->queryGetRow( "SELECT PASSWORD('".$password."') AS password" );
		return $result['password'];
	}

	// Get last mysql error
	public function get_last_mysql_error() {
		return mysqli_error($this->link);
	}

	// Get number of affected rows from last query
	public function getLastAffectedRows() {
		return mysqli_affected_rows($this->link);
	}

	/**
	 * Build where string for use in MySQL query.
	 *
	 * Internal function: Can't be called from outside the DATABASE class.
	 *
	 * @param where_data
	 *  Mixed: May be either a string not prefixed with WHERE or an array ('column_name' => 'unescaped_value' )
	 *
	 * @return
	 *  String: WHERE part for use in a MySQL query.
	 *
	 * TODO:
	 *		escape the string by default, like we do with the array of values.
	 *		For already escaped $where_data, there should be an $isEscaped boolean value set to true.
	 */
	protected function buildWhere( $where_data ) {
		$where = '';

		// Build up where if necessary
		if( $where_data ) {
			$where = ' WHERE ';

			if ( is_string( $where_data ) ) {
				$where .= $where_data;

			} else if( is_array( $where_data ) ) {
				foreach( $where_data as $name => $value ) {
					$where .= $name . " = '" . $this->escapeString($value) . "' AND ";
				}

				// Remove extra " AND "
				$where = preg_replace('/ AND $/', '', $where);
			}
		}

		return $where;
	}

	/**
	 * Builds a comma separated string based on input array
	 *
	 * Internal function: Can't be called from outside the DATABASE class.
	 *
	 * @param values
	 *	Array: Values to be joined
	 *
	 * @return
	 *	String: Comma separated string
	*/
	protected function buildValues( $values ) {
		return implode( ",", $values );
	}

	/**
	 * Query database and return resource.
	 *
	 * @param buffered
	 *  Boolean: true to used buffered queries, false to use unbuffered
	 *
	 * @param sQuery
	 *	String: Valid MySQL query.
	 *
	 * @param force
	 *	Boolean: Flag to override the canExecute() check inside the function
	 *
	 * @return
	 *	Resource: MySQL resource consisting of query result, false on failure
	*/
	private function query_base( $buffered, $sQuery, $force = false ) {
		$rQuery = false;
		if ( $force || $this->canExecute( $sQuery ) ) {
			$time = microtime( true );
			if ( !defined( 'IGNORE_MYSQL_LOG' ) || IGNORE_MYSQL_LOG == false ) {
				$this->logger->notice( "Query: {sQuery}", array( "sQuery" => $this->cleanSyntax( $sQuery ) ) );
			}
			$resourcetype = get_class( $this->link );
			if ( $this->link instanceof mysqli) {
				if( $buffered ) {
					$rQuery = mysqli_query( $this->link, $sQuery );
				} else {
					$rQuery = mysqli_query( $this->link, $sQuery, MYSQLI_USE_RESULT );
				}
				$error = mysqli_error($this->link);
				if (is_object($rQuery)) {
					$resourcetype = get_class( $rQuery );
					
					if( $error == "" && !($rQuery  instanceof mysqli_result)) {
						$error = "Query gave something that was not a mysql result";
					}
				} else {
					//
				}
				
			} else {
				$error = "Not a resource";
			}
			if ( $error != "" ) {
				$this->logger->notice( "Database error: " . $error );
			}
			$lapsed = microtime( true ) - $time;
			if ( !defined( 'IGNORE_MYSQL_LOG' ) || IGNORE_MYSQL_LOG == false ) {
				$stats = "Return resource type: " . ( $resourcetype? $resourcetype : "None" ) . ". ";
				if( $buffered ) {
					$stats .= "Affected rows: " . mysqli_affected_rows($this->link) . ". ";
					if( $rQuery instanceof mysqli_result) {
						$stats .= "Modified rows: " . mysqli_num_rows($rQuery);
					}
				}
				$this->logger->notice( "#######MySQL Query ended ( Execution time: ".round( $lapsed, 2 ). " seconds ). " . $stats );
			}
			return $rQuery;
		} else {
			// query was not allowed for the user
			$this->logger->notice( 'Query was not allowed by "canExecute": {query}', array( 'query' => $sQuery ) );
			return false;
		}
	}

	/**
	 * Query database (using buffered queries) and return resource.
	 *
	 * @param sQuery
	 *	String: Valid MySQL query.
	 *
	 * @param force
	 *	Boolean: Flag to override the canExecute() check inside the function
	 *
	 * @return
	 *	Resource: MySQL resource consisting of query result, false on failure
	*/
	public function query( $sQuery, $force = false ) {
		return $this->query_base( true, $sQuery, $force );
	}

	/**
	 * Query database using unbuffered queries and return resource.
	 * This is slightly faster and uses less memory than unbuffered queries.
	 * NOTE: You must retrieve all results and you cannot use the
	 * queryResourceNumRows and queryNumRows methods for this query
	 *
	 * @param sQuery
	 *	String: Valid MySQL query.
	 *
	 * @return
	 *	Resource: MySQL resource consisting of query result, false on failure
	*/
	public function unbuffered_query( $sQuery ) {
		return $this->query_base( false, $sQuery );
	}

	/**
	 * Validates if a query can be executed or not.
	 * By default it returns true and checks whether a query should be executed or not by
	 * looking into the global variable: $write_permission.
	 * If the variable is set and its value is false, then false is returned.
	 *
	 * @param query
	 *	String: Valid MySQL query.
	 *
	 * @return
	 *	Boolean: True if query can be executed, false if it is now allowed
	*/
	private function canExecute( $query ) {
		if ( preg_match("/^\s*(select)|(create temporary table)|(create index)/i", $query) ) {
			// Allows allow select queries, regardless of account type/modules
			return true;

		}

		if ( isset( $GLOBALS['write_permission'] ) && $GLOBALS['write_permission'] === false ) {
			return false;
		}

		return true;
	}

	/**
	 * Takes a valid MySQL query resource and return the number of rows in result set. This is basically a clean wrapper for 'mysql_num_rows'.
	 *
	 * @param query_resource
	 *	String: Valid MySQL query resource.
	 *
	 * @return
	 *	Integer: Number of affected rows in result set, return -1 on error.
	*/
	public function queryResourceNumRows( $query_resource ) {
		$ret = mysqli_num_rows( $query_resource);
		if ( $ret === false ) {
			$ret = -1;
		}
		return (int) $ret;
	}

	/**
	 * Takes a valid MySQL query resource and returns an assosiative array. This is basically a clean wrapper for 'mysql_fetch_assoc'
	 *
	 * @param resource_result
	 *	String: Valid MySQL query resource.
	 *
	 * @return
	 *	Array: Data from current row in result set.
	*/
	public function queryFetchAssoc( $resource_result ) {
		$res = false;

		if ( !($this->link instanceof mysqli)) {
			$error = "DB link is not a resource";
		} else {
			if ( !($resource_result instanceof mysqli_result) ) {
				$error = "Query is not a resource";
			} else {
				$res = mysqli_fetch_assoc( $resource_result );
				$error = mysqli_error($this->link);
				if ( $error != "" ) {
					$this->logger->notice( "Database error: " . $error );
				}
			}
		}

		if ( $error != "" ) {
			$this->logger->notice( "Database error: " . $error );
		}

		return $res;
	}

	/**
	 * Query database and return array containing the first row in result set.
	 *
	 * @param sQuery
	 *	String: Valid MySQL query.
	 *
	 * @return
	 *	Array: The first row in result set.
	*/
	public function queryGetRow( $sQuery ) {
		return mysqli_fetch_assoc( $this->query_base(true, $sQuery, true) );
	}

	/**
	 * Get number of affected/matching rows.
	 *
	 * @param resource
	 *	Resource: Valid MySQL resource.
	 *
	 * @return
	 *	Integer: Number of affected/matching rows.
	*/
	public function queryNumRows( $resource ) {
		return mysqli_num_rows( $resource );
	}

	/**
	 * Get total number of rows for the last query.
	 * The last query must be run using SELECT SQL_CALC_FOUND_ROWS..
	 * The function disregards the LIMIT used in the last query.
	 * This function should be used for getting the total number of matching rows as
	 * it is way better than executing the query twice.
	 *
	 * @return
	 *	Integer: Total number of rows for the last query ran (disregarding the limit information)
	*/
	public function queryMysqlNumRows() {
		$numRowsQuery = "SELECT FOUND_ROWS() AS count";
		$numRows = $this->queryGetRow( $numRowsQuery );
		return (int) $numRows['count'];
	}

	/**
	 * Query database and return array containing all rows in the result set.
	 *
	 * @param sQuery
	 *	String: Valid MySQL query.
	 *
	 * @return
	 *	Array: All rows in result set. False, if query failed
	*/
	public function queryGetRows( $query ) {
		$res = $this->query_base( true, $query, true );
		if( $res ) {
			$rows = array();
			while( $row = mysqli_fetch_assoc($res) ) {
				array_push( $rows, $row );
			}
			return $rows;
		} else {
			return false;
		}
	}

	/**
	 * Simplified method for selecting multiple rows from one or more tables.
	 *
	 * @param table
	 *	String: Table, or tables comma separated.
	 * @param where
	 *	Mixed: Optional. Parameters for buildWhere().
	 * @param order
	 *	String: Optional. Column name, or names comma separated. 'ORDER BY' is prepended this argument.
	 * @param limit
	 *	String: Optional. Limit to apply to query. 'LIMIT' is prepended this argument.
	 * @param values
	 *	Array: Columns to be selected.
	 *
	 * @return
	 *	Array: Rows as returned by the database server.
	*/
	public function getRows( $table, $where='', $order='', $limit='', $values = '' ) {
		$query = "SELECT ".( is_array( $values ) ? $this->buildValues( $values ) : "*" )." FROM " . $table . $this->buildWhere( $where );

		if( $order ) {
			$query .= " ORDER BY $order";
		}

		if ( $limit ) {
			$query .= " LIMIT $limit";
		}

		$res = $this->query_base( true, $query, true );
		$rows = array();

		while( $row = mysqli_fetch_assoc($res) ) {
			array_push( $rows, $row );
		}

		return $rows;
	}

	/**
	 * Select a single row from a table.
	 *
	 * @param table
	 *	String: Table name
	 * @param where
	 *	Mixed: Optional. Parameters for buildWhere()
	 * @param order
	 *	String: Optional. Column name, or names comma separated. 'ORDER BY' is prepended this argument.
	 *
	 * @return
	 *	Array: Row as returned by the database server.
	*/
	public function getRow( $table, $where='', $order='' ) {
		$query = "SELECT * FROM " . $table . $this->buildWhere($where) . ( $order ? ' ORDER BY ' . $order : '' ) . " LIMIT 1";
		$resource = $this->query_base(true, $query, true);
		if ( false !== $resource) {
    		return mysqli_fetch_assoc( $resource );
    	}
	}

	/**
	 * Simplified way of selecting a single column from a single row.
	 *
	 * @param table
	 *	String: Table name
	 * @param value
	 *	String: Column name
	 * @param where
	 *	Mixed: Optional. Parameters for buildWhere()
	 *
	 * @return
	 *	String: Column value
	*/
	public function getRowValue( $table, $value, $where='' ) {
		$query = "SELECT " . $value . " FROM " . $table . $this->buildWhere($where) . " LIMIT 1";
		$res = $this->query_base(true, $query, true);
		if ( $res === false ) {
			$GLOBALS[ "debug" ]->error( "A query failed to return a resource: " . $query );
			return "";
		}
		$row = mysqli_fetch_assoc( $res ); //$row = NULL for no result
		return $row ? $row[$value] : "";
	}

	/**
	 * Simplified way of selecting multiple columns from a single row.
	 *
	 * @param table
	 *	String: Table name
	 * @param values
	 *	Array: Array of string Column names
	 * @param where
	 *	String: Optional. Parameter for buildWhere()
	 *
	 * @return
	 *	Array: Column values
	*/
	public function getRowValues( $table, $values, $where='' ) {
		$query = "SELECT ";
		if ( is_array($values) ) {
			$query .= $this->buildValues( $values );
			$query .= " FROM " . $table . $this->buildWhere($where) . " LIMIT 1";
			$row = mysqli_fetch_assoc($this->query_base(true, $query, true));
			if ( !$row ) {
				return array();
			} else {
				return $row;
			}
		} else {
			return $this->getRowValue($table,$values,$where);
		}
	}

	/**
	 * Count the number of rows in a table.
	 *
	 * @param table
	 *	String: Table name.
	 * @param where
	 *	String: Optional. Parameters for buildWhere().
	 *
	 * @return
	 *	Integer: Number of rows.
	*/
	public function numRows( $table, $where='' ) {
		$query = "SELECT COUNT(*) AS COUNT FROM " . $table . $this->buildWhere($where);
		//return (int) mysql_result($this->query_base(true, $query, true), 0, "COUNT(*)");
		$rQuery = $this->query_base(true, $query, true);
		if ($rQuery && $rQuery instanceof mysqli_result) {
			while ( $finfo = mysqli_fetch_field($rQuery) ) {
				if ('COUNT' == $finfo->name) {
					$f = mysqli_fetch_assoc($rQuery);
					return (int)$f['COUNT'];
				}
			}
		}
		return 0;
	}

	/**
	 * Generic method for performing an atomic "INSERT IF NOT EXIST" query.
	 * Will insert an element if it does not exist in the database, otherwise does nothing.
	 * Relies on a mutex table (named mutex) that has been set up correctly
	 *
	 * @note Used by CSI only
	 * @note Uses the `vuln_track`.`mutex` in the Hosted Edition.
	 *
	 * @param table
	 *  String: The name of the table to conditionally insert into
	 *
	 * @param values
	 *  Array: { column names => values } pair with the values to conditionally insert
	 *
	 * @return
	 *  Mixed: The MySQL insert id (an integer) if a row was inserted, otherwise false.
	 */
	public function insertIfNotExist( $table, $values ) {
		foreach( $values as $column => $value ) {
			$values[ $column ] = $this->escapeString( $value );
		}
		$columnsep = '';
		$valuesep = '';
		$joinsep = '';
		$wheresep = '';
		$columnlist = '';
		$valuelist = '';
		$joinlist = '';
		$wherelist = '';
		foreach ( $values as $column => $value ) {
			$columnlist .= $columnsep . $column;
			$columnsep = ', ';
			$valuelist .= $valuesep . "'" . $value . "'";
			$valuesep = ', ';
			$joinlist .= $joinsep . ' ' . $column . " = '" . $value . "'";
			$joinsep = ' AND ';
			$wherelist .= $wheresep . $column . ' IS NULL';
			$wheresep = ' AND ';
		}
		// The mutex table exists in `VPDB` DB in the Server Edition.
		$schemaSql = EDITION == SERVER_EDITION ? '' : 'vuln_track.';
		$query =
			'INSERT INTO ' . $table . '(' . $columnlist . ")\n" .
			'SELECT ' . $valuelist . "\n" .
			'FROM ' . $schemaSql . 'mutex LEFT OUTER JOIN ' . $table . "\n" .
			'  ON ' . $joinlist . "\n" .
			'WHERE ' . $schemaSql . 'mutex.i = 1 AND ' . $wherelist ;
		$this->query( $query );
		if ( mysqli_affected_rows( $this->link ) !== 0 ) {
			return $this->getInsertId();
		} else {
			return false;
		}
	}

	/**
	 * Build where string for use in MySQL "UPDATE" or "INSERT INTO" query.
	 *
	 * Internal function: Can't be called from outside the DATABASE class.
	 *
	 * @param values
	 *  Array: Array ( 'column_name' => 'value' ) that will be used to construct the output string
	 * @param isEscaped
	 *  Boolean: FALSE to apply escapeString() on the values passed. TRUE to use data as is.
	 *
	 * @return
	 *  String: Comma separated string for use in "UPDATE" or "INSERT INTO" statement.
	 */
	protected function buildEdit( $values, $isEscaped = false ) {
		$result = "";
		$i = 0;
		foreach ( $values as $key => $value ) {

			if ( $i != 0 ) {
				$result .= ",";
			}
			if ( $isEscaped !== true ) {
				$value = $this->escapeString( $value );
			}
			// Note - this '===' is critical if $value can be 0, which it can.
			// PHP will often evaluate 'random_string' == 0 as true
			if ( !empty($value) && (strtoupper( $value ) === "NOW()" || strtoupper( $value ) === "UTC_TIMESTAMP()" )) {
				$result .= $key." = ".$value." ";
			} else {
				$result .= $key." = '".$value."' ";
			}
			$i++;
		}

		return $result;
	}

	/**
	 * Generic method for performing "UPDATE" / "INSERT INTO" / "REPLACE INTO"queries.
	 *
	 * @param table
	 *	String: Table name
	 * @param values
	 *	Array: Parameter for buildEdit().
	 * @param type
	 *	Integer: Determines query type. 0 = INSERT, 1 = UPDATE, 2 = REPLACE
	 * @param where
	 *	Mixed: Optional. Parameter for buildWhere().
	 * @param isEscaped
	 *  Boolean: FALSE to apply escapeString() on the $password passed. TRUE to use data as is.
	 *
	 * @return
	 *	Mixed: Integer or query result. If performing an UPDATE the result is the query result.
	 *         If INSERT the result is the MySQL insert id.
	*/
	public function edit( $table, $values, $type, $where = '', $isEscaped = false ) {
		$query = "";

		switch( $type ) {
		case self::INSERT:
			$query = "INSERT INTO ";
			break;
		case self::UPDATE:
			$query = "UPDATE ";
			break;
		case self::REPLACE:
			$query = "REPLACE INTO ";
			break;
		default:
			return false;
		}

		$query .= $table .
			" SET " . $this->buildEdit( $values, $isEscaped ) .
			( ( $type === self::UPDATE && $where != '' ) ? $this->buildWhere( $where ) : "" );

		$result = $this->query( $query );

		if ( $result === false ) {
			return false;
		}

		if ( $type === self::INSERT ) {
			return $this->getInsertId();
		}

		return $result;
	}

	/**
	 * Function for escaping a value before it is used in a database query as part of an enclosed value.
	 *
	 * @param value
	 *  String: Value to escape.
	 *
	 * @return
	 *  String: Escaped string for use as part of an enclosed value.
	*/
	public function escapeString( $value ) {
              if (!empty($value)) {
                  return mysqli_real_escape_string($this->link, $value);
              } elseif (is_null($value)) {
                  return null;
              } else {
                  return '';
              }
	}

	/**
	 * Function for returning the ID of the last inserted row
	 *
	 * @return
	 *  Integer: The ID of the last inserted row.
	*/
	public function getInsertId() {
		return mysqli_insert_id($this->link);
	}

	/**
	 * Wrapper function for getting the current local date/time in the MySQL database.
	 * TODO - could introduce a 'format' parameter if this is ever needed.  For now, default format is fine.
	 *
	 * @return
	 *	String: date value
	*/
	public function getCurrentDateTime() {
		$query = "SELECT NOW() AS time_now";
		$row = mysqli_fetch_assoc( $this->query_base(true, $query, true) );
		return $row['time_now'];
	}

	/**
	 * Wrapper function for getting the current UTC date/time in the MySQL database
	 * TODO - could introduce a 'format' parameter if this is ever needed.  For now, default format is fine.
	 *
	 * @return
	 *	String: date value
	*/
	public function getCurrentUTCDateTime() {
		$query = "SELECT UTC_TIMESTAMP() AS time_now";
		$row = mysqli_fetch_assoc( $this->query_base(true, $query, true) );
		return $row['time_now'];
	}

	/**
	* Disables triggers for the session.
	*/
	public function disableTrigger() {
		$this->query( 'SET @TRIGGER_CHECKS = FALSE' );
	}

	/**
	 * Returns the DATABASE instance with the specified name
	 *
	 * @param string $name
	 * @return DATABASE
	 *	Returns null if the DATABASE instance isn't found
	 */
	public static function getInstanceByName( $name ) {
		if ( isset( self::$instances[$name] ) ) {
			return self::$instances[$name];
		}
	}

	/**
	 * Sets a DATABASE instance that can be later fetched with {@link DATABASE::getInstanceByName()}
	 * using the specified $name.
	 *
	 * @param string $name
	 * @param DATABASE $db
	 */
	public static function setInstance( $name, $db ) {
		self::$instances[$name] = $db;
	}
}
