<?php
/**
 * @file debug.class.php
 * Provides global debugging functionality.
 *
 * You should always use this file for debugging purposes.
 *
 * Offers the mechanisms for logging user actions (e.g. login) or non-critical PHP errors
*/

/**
 * Provides debugging functionality. Any instance of the DEBUG class should be named $debug.
 * Global configuration options:
 *
 * Error debugging:
 *
 * DEBUG_LEVEL: 0 - none, 1 - information, 2 - verbose, 3 - everything
 *
 * DEBUG_LOG_FILE: file to write the log, if not writable then errors will be displayed on screen
 *
 * DEBUG_FORCE_SCREEN: force errors to be displayed on screen
 *
 * ERROR_REPORTING: PHP error reporting level
 *
 * Action logging:
 *
 * ACTION_LEVEL: 0 - none, 1 - action message, 2 - action message + scope variables
 *
 * ACTION_LOG_FILE: file to save the action log to
 *
 * ACTION_LOG_CLASS: set to true if logging class constructors
 *
*/
class DEBUG {
	private $time = "";

	private static $actionLogFile = null;

	function __construct() {
		switch ( DEBUG_LEVEL ) {
		case 0:
			break;
		case 1:
		case 2:
		case 3:
			set_error_handler( "DEBUG::_errorHandler", ERROR_REPORTING );
		}
		$this->time = microtime( true );
		$this->storeActionLog( "--------DEBUG FILE OPENED--------", "" );
	}

	function __destruct() {
		$lapsed = microtime( true ) - $this->time;
		$this->storeActionLog( "Execution time: ".round( $lapsed, 2 ). " seconds", "" );
		$this->storeActionLog( "--------DEBUG FILE CLOSED--------", "" );
	}

	/**
	* Function to be called by PHP's set_error_handler()
	* @param set_error_handler()
	*	Collection, see set_error_handler()
	*/
	final static function _errorHandler( $errorNumber, $errorMessage, $errorFile, $errorLine, $errorContext ) {
		switch ( DEBUG_LEVEL ) {
		case 1:
			$logContent = "-------\r\n" . "[" . gmdate( "d-m-y H:i:s" ) . "] " . "\r\nError number: " . $errorNumber . "\r\nError message: " . $errorMessage . "\r\nError file: " . $errorFile . "\r\n-------\r\n";
			break;
		case 2:
			$logContent = "-------\r\n" . "[" . gmdate( "d-m-y H:i:s" ) . "] " . "\r\nError number: " . $errorNumber . "\r\nError message: " . $errorMessage . "\r\nError file: " . $errorFile . "\r\nError line: " . $errorLine . "\r\n-------\r\n";
			break;
		case 3:
			$logContent = "-------\r\n" . "[" . gmdate( "d-m-y H:i:s" ) . "] " . "\r\nError number: " . $errorNumber . "\r\nError message: " . $errorMessage . "\r\nError file: " . $errorFile . "\r\nError line: " . $errorLine . "\r\nError context: " . print_r( $errorContext, true ) . "\r\n-------\r\n";
			break;
		}

		if ( DEBUG_FORCE_SCREEN == true ) {
			DEBUG::displayErrorLog( $logContent );
			return;
		}

		DEBUG::storeErrorLogStatic( $logContent );
	}

	/**
	 * Display error on screen
	 * @param logContent
	 *	String log message
	*/
	static function displayErrorLog( $logContent ) {
		// On the live side, we don't actually want to do anything here - the error log is essentially disabled
		// so we will always come here - just ignore...
		// error_log( $logContent );
	}

	/**
	 * Save error in the log file or if the log file cannot be openned display error on screen
	 * Note - cannot be static as currently called in the psi_20 project
	 * @param logContent
	 *	String log message
	*/
	function storeErrorLog( $logContent ) {
		$logFile = fopen( DEBUG_LOG_FILE, "a" );
		if ( $logFile != null ){
			fwrite( $logFile, $logContent );
			fclose( $logFile );
		} else {
			$logContent = "<pre>Cannot open log file: ".DEBUG_LOG_FILE."\r\n".$logContent."<pre>";
			DEBUG::displayErrorLog( $logContent );
		}
	}

	/**
	 * Static copy of the above so we can call the function in the static errorHandler function
	*/
	static function storeErrorLogStatic( $logContent ) {
		$logFile = fopen( DEBUG_LOG_FILE, "a" );
		if ( $logFile != null ){
			fwrite( $logFile, $logContent );
			fclose( $logFile );
		} else {
			$logContent = "<pre>Cannot open log file: ".DEBUG_LOG_FILE."\r\n".$logContent."<pre>";
			DEBUG::displayErrorLog( $logContent );
		}
	}

	/**
	 * Write in the action log file
	 * @param logContent
	 *	String log message
	 */
	public static function writeActionLog( $logContent ) {
		if ( $logContent != "" ) {
			if ( self::$actionLogFile == null ){ // Open the log file ONCE
				self::$actionLogFile = fOpen( ACTION_LOG_FILE, 'a' );
			}
			if ( self::$actionLogFile != null ){ // Store data only if file was opened
				fwrite( self::$actionLogFile, $logContent );
			}
		}
	}

	/**
	 * Store a regular action log ( function, etc )
	 * @param actionIdentifier
	 *	String action identifier text
	 * @param definedVariables
	 *	Array of scope variables
	*/
	public function storeActionLog( $actionIdentifier, $definedVariables = '' ) {
		$logContent = "";
		switch ( ACTION_LEVEL ) {
		case 0:
			return;
			break;
		case 1:
			$logContent = $actionIdentifier;
			break;
		case 2:
			$logContent = $actionIdentifier;
			if ( $definedVariables ) {
				$logContent .= "\r\n" . print_r( $definedVariables, true );
			}
			break;
		}

		if ( $logContent != "" ) {
			$trace = debug_backtrace();
			$caller = $trace[1];
			$source = '';
			if ( isset( $caller['file'] ) ) {
				$source .= basename( $caller['file'] ) . ' - ';
			}
			if ( isset( $caller['class'] ) ) {
				$source .= $caller['class'] . $caller['type'];
			}
			if ( isset( $caller['function'] ) ) {
				$source .= $caller['function'];
			}
			$logContent = str_replace( array("\n", "\r", "\r\n"), '\\n', $logContent );
			$logContent = "[" . gmdate( "d-m-y H:i:s" ) . "] [" . $source . "] " . $logContent . "\n";
		}

		self::writeActionLog( $logContent );
	}

	/**
	 * This is a proxy for calling storeActionLog function.
	 * The reason for this function is that projects that use the PSR-3 standard
	 * can keep on calling the debug() function as well as projects that don't
	 * use it ( e.g. the projects that use this Debug class.) This way nothing will
	 * break. This will enable us to migrate to the PSR-3 standard without breaking
	 * anything.
	 */
	public function debug( $message, $values = array() ) {
		return $this->storeActionLog( $message, $values );
	}

	/**
	 * This is a proxy for calling storeActionLog function.
	 * The reason for this function is that projects that use the PSR-3 standard
	 * can keep on calling the notice() function as well as projects that don't
	 * use it ( e.g. the projects that use this Debug class.) This way nothing will
	 * break. This will enable us to migrate to the PSR-3 standard without breaking
	 * anything.
	 */
	public function notice( $message, $values = array() ) {
		return $this->storeActionLog( $message, $values );
	}

	/**
	 * This is a proxy for calling storeActionLog function.
	 * The reason for this function is that projects that use the PSR-3 standard
	 * can keep on calling the error() function as well as projects that don't
	 * use it ( e.g. the projects that use this Debug class.) This way nothing will
	 * break. This will enable us to migrate to the PSR-3 standard without breaking
	 * anything.
	 */
	public function error( $message, $values = array() ) {
		return $this->storeActionLog( $message, $values );
	}

	/**
	 * This is a proxy for calling storeActionLog function.
	 * The reason for this function is that projects that use the PSR-3 standard
	 * can keep on calling the critical() function as well as projects that don't
	 * use it ( e.g. the projects that use this Debug class.) This way nothing will
	 * break. This will enable us to migrate to the PSR-3 standard without breaking
	 * anything.
	 */
	public function critical( $message, $values = array() ) {
		return $this->storeActionLog( $message, $values );
	}

	/**
	 * Function for logging a class constructor beying called
	 *
	 * @deprecated
	 * @todo TODO remove all references to this method
	 * @param className
	 *	String name of the class
	*/
	public function storeClassInitialized( $className ) {
		if ( ( ACTION_LOG_CLASS == true ) && ( ACTION_LEVEL != 0 ) ) {
			$logContent = "Loaded class: ".$className;
			$logContent = "[" . gmdate( "d-m-y H:i:s" ) . "] " . $logContent . "\r\n";
			self::writeActionLog( $logContent );
		}
	}

	/**
	 * Logs an action.
	 * Actions are currently written to a file specified by the ACTION_LOG_FILE constant
	 *
	 * @param string $action
	 * @param mixed $definedVariables
	 */
	public static function logAction( $action, $definedVariables = '' ) {
		if ( 0 === ACTION_LEVEL || '' === $action ) {
			return;
		}
		if ( 2 === ACTION_LEVEL ) {
			if ( $definedVariables ) {
				$action .= "\r\n" . print_r( $definedVariables, true );
			}
		}
		return self::writeActionLog( '[' . gmdate( 'd-m-y H:i:s' ) . '] ' . $action . "\r\n" );
	}

	/**
	 * Logs an action if the $condition is true.
	 *
	 * @param bool $condition If true then the condition is logged
	 * @param string $action
	 * @param mixed $definedVariables
	 * @return bool
	 *	Returns $condition
	 */
	public static function logActionIf( $condition, $action, $definedVariables = '' ) {
		if ( true === $condition ) {
			self::logAction( $action, $definedVariables );
		}
		return $condition;
	}
}
