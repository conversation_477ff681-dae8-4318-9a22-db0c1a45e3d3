<?php

  /**
   * This class should not be static.
   *
   * @todo: REFACTOR:
   * - Break this class into two classes: AccountManager and Account
   * - AccountManager will contain the functionality: read, delete, create etc
   * - Account class will contain functionality specific to one instance of the account
   *
   * @todo: move csiUserManagement::getSubAccounts() here
   */

class Account {

	/*
	 * STATICS
	 */

	private static $db ;

	public static function init( DB $db ) {
		self::$db = $db;
	}

	public static function getAccountIdsBy( $where ) {
		return self::$db->select()
			->columns( array( 'account_id' ) )
			->from( 'accounts' )
			->where( $where )
			->setOption( Select::RETURN_PDO_STATEMENT, true )
			->exec()
			->fetchAll( PDO::FETCH_COLUMN, 0 );
	}

	/*
	 * @param $where Array
	 *  Condition
	 * @param $columns Array [Optional]
	 *  The columns to be fetched.
	 *
	 * @return Array
	 *  Array of rows.
	 */
	public static function getBy( Array $where, Array $columns = array() ) {
		$select = self::$db->select()
			->from( 'accounts' )
			->where( $where );
		if ( !empty( $columns ) ) {
			$select->columns( $columns );
		}
		return $select->exec();
	}

	/*
	 * @param $accountId Int
	 * @param $customerAccounts Array
	 *  All accounts that belong to the customer. These accounts need to be filtered to
	 *  created the sub accounts tree.
	 *
	 * @return Array
	 *  Nested arrays in a tree structure representing the account hierarchy.
	 *
	 */
	private static function _getSubAccounts( $account, $customerAccounts, $nested = false) {

		// Find children
		$subAccounts = array();
		foreach( $customerAccounts as $key => $possibleSubAccount ) {
			if ( $possibleSubAccount[ 'account_esm' ] == $account[ 'account_id' ] ) {
				// $possibleSubAccount[ 'expanded' ] = 'true';
				$subAccountId = $possibleSubAccount[ 'account_id' ];
				$subAccounts[ /*$subAccountId*/ ] = $possibleSubAccount;
				unset( $customerAccounts[ $key ] );
			}
		}

		if ( empty( $subAccounts ) ) {
			return array();
		}

		foreach ( $subAccounts as $key => $subAccount ) {
			$children = self::_getSubAccounts( $subAccount, $customerAccounts, $nested );
			if ( $nested ) {
				$subAccounts[ $key ][ 'children' ] = $children;
			} else {
				$subAccounts = array_merge( $subAccounts, $children );
			}
		}

		return $subAccounts;
	}

	/*
	 * @param $account Account
	 *  Either an account id, or an account object
	 * @param $columns Array [Optional]
	 *  Only get selected columns from the database. However, they must contain the
	 *  account_id and account_esm.
	 * @param $nested Boolean
	 *  Return the accounts in the nested form.
	 * @param $condition Array [Optional] @todo: not implemented yet
	 *  Conditions for the sub-accounts
	 *
	 * @return Array
	 *  A tree of the account hierarchy
	 */
	public static function getSubAccounts( Account $account
										   ,Array $columns = array( 'account_id', 'account_esm' )
										   ,$includeRoot = false
										   ,$nested = false
										   // , Array $condition = array()
										   ) {

		if ( !in_array( 'account_id', $columns ) || !in_array( 'account_esm', $columns ) ) {
			throw new Exception( 'The columns account_id and account_esm must be included.' );
		}

		// Get all accounts in the family
		$condition = array( 'cst_id' => $account->getCstId()
							);
		$customerAccounts = self::getBy( $condition, $columns );

		// Find the account whose hierarchy needs to be found
		$_account = array();
		foreach( $customerAccounts as $__account ) {
			if ( $__account[ 'account_id' ] == $account->getAccountId()  ) {
				$_account = $__account;
				break;
			}
		}

		if ( empty( $_account ) ) {
			return false;
		}

		$subAccountsTree = self::_getSubAccounts( $_account, $customerAccounts, $nested );

		$tree = array();

		if ( $nested ) {
			$tree['text'] = '.'; // @todo: document why we have to do this
			if ( $includeRoot ) {
				$_account['children'] = $subAccountsTree;
				$tree['children'] = $_account;
			} else {
				$tree['children'] = $subAccountsTree;
			}
		} else {
			$tree = array_merge( $tree, $subAccountsTree );
		}

		return $tree;
	}

	/**
	 * Gets the Account IDs of sub-Accounts.
	 *
	 * @note Used by VIM only.
	 *
	 * @param Account $account
	 * @param bool $includeRoot
	 * @param bool $nested
	 * @return bool|array
	 */
	public static function getSubAccountIds( Account $account, $includeRoot = false, $nested = false ) {
		$subAccounts = self::getSubAccounts( $account, array( 'account_id', 'account_esm', 'account_username' ), $includeRoot, $nested );

		return $subAccounts;
	}

	/*
	 * NON-STATICS
	 */

	/*
	 * Account Properties i.e. Column values
	 */
	private $properties;

	public function __construct( $accountId ) {
		$rows = self::$db->select()
			->from( 'accounts' )
			->where( array( 'account_id' => $accountId ) )
			->exec();

		$this->properties = array();
		if ( isset( $rows[0] ) ) {
			$this->properties = $rows[0];
		}
	}

	public function getAccountId() {
		return (int) $this->properties[ 'account_id' ];
	}

	public function getCstId() {
		return $this->properties[ 'cst_id' ];
	}

	public function getAccountOptions() {
		return $this->properties[ 'account_options' ];
	}

	/*
	 * Get parent account
	 */
	public function getAccountEsm() {
		return $this->properties[ 'account_esm' ];
	}

	/*
	 * Get email address
	 */
	public function getAccountEmail() {
		return $this->properties[ 'account_email' ];
	}

  }