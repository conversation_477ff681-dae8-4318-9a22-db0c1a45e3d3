<?php
/**
 * @file utcDateTime.class.php
*/

/**
 * Provides timestamp/date functionality.
 * Should be the first class to be instantiated to set the default timezone
 * required by the date handling functions to work without warnings
*/
class UTCDATETIME {
	function __construct() {
		// set required default backend timezone for date handling functions
		// CSI backend operates in UTC across the board
		date_default_timezone_set('UTC');
	}

	function getUtcTimestamp( $str ) {
		// YYYY-MM-DD HH:MM:SS
		// hour, minute, second, month, day, year
		$datetime = preg_split('/[ \-:]/', $str);
        return gmmktime( $datetime[3], $datetime[4], $datetime[5], $datetime[1], $datetime[2], $datetime[0] );
	}

	function mySQLDatetimeToTimestamp( $sDatetime ) {
		list( $sDate, $sTime ) = explode( " ", $sDatetime );
		$d = explode( "-", $sDate );
		$t = explode( ":", $sTime );
		return gmmktime( $t[0], $t[1], $t[2], $d[1], $d[2], $d[0] );
	}
}
