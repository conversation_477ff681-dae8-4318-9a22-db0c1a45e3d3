<?php
/**
 * @file prng.class.php
*/

/**
 * Provides strong random number generation
 *
 */
class PRNG {
	protected $max_random;

	const NUMBER_CHARS = 62;
	private static $chars = array( '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
								  ,'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'
								  ,'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
								  ,'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm'
								  ,'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' );

	private static $err_open_string = "Error opening or reading /dev/urandom. Something is terribly wrong. Continuing with pseudo-random ids.";

	private function fallback($id, $len) {
		error_log(self::$err_open_string);
		while( strlen($id) < $len ) {
			$id .= self::$chars[mt_rand(0,self::NUMBER_CHARS-1)];
		}
		return $id;
	}

	// Function for generating random IDs
	// TODO: If this is to be used on Windows, it needs to call CryptGenRandom() instead of using /dev/urandom
	function generateRandomID( $len ) {
		$id = '';
		$urandom = fopen("/dev/urandom", "r");
		if( !$urandom ) {
			return $this->fallback($id,$len);
		}
		while( strlen($id) < $len ) {
			$randomData = fread($urandom,1);
			if( false === $randomData ) {
				fclose($urandom);
				return $this->fallback($id,$len);
			}
			$pos = ord($randomData) >> 2;
			if( $pos < self::NUMBER_CHARS ) {
				$id .= self::$chars[$pos];
			}
		}
		fclose($urandom);
		return $id;
	}

	/**
	 * Returns the specified numbers of bytes from the entropy pool. If no
	 * entropy pool is available then bytes will be generated using mt_rand().
	 *
	 * @param int $length Number of bytes to return
	 * @return string
	 *	Returns a binary string containing the data
	 */
	public function getEntropy( $length ) {
		$binaryString = false;
		if ( function_exists( 'mcrypt_create_iv' ) && !defined( 'PHALANGER' ) ) {
			$binaryString = mcrypt_create_iv( $length, MCRYPT_DEV_URANDOM );
		}
		if ( false === $binaryString && function_exists( 'openssl_random_pseudo_bytes' ) ) {
			$binaryString = openssl_random_pseudo_bytes( $length );
		}
		if ( false === $binaryString && is_readable( '/dev/urandom' ) ) {
			$fh = fopen( '/dev/urandom', 'r' );
			$bytesRead = strlen( $binaryString );
			while ( $bytesRead < $length ) {
				$binaryString .= fread( $fh, $length - $bytesRead );
				$bytesRead = strlen( $binaryString );
			}
			fclose( $fh );
		}
		if ( false === $binaryString || strlen( $binaryString ) < $length ) {
			$currentLength = strlen( $binaryString );
			for ( $i = 0; $i < $length; $i++ ) {
				if ( $i < $currentLength ) {
					$binaryString[$i] = $binaryString[$i] ^ chr( mt_rand( 0, 255 ) );
				} else {
					$binaryString .= chr( mt_rand( 0, 255 ) );
				}
			}
		}
		return $binaryString;
	}
}

/*$prng = new PRNG();
  echo $prng->generateRandomID( 20 ) . "\n";*/
