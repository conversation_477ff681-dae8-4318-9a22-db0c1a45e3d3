<?php
/**
 * @file websitePageContent.class.php
*/

/**
 * Provides main functionality for generating website pages.
*/
class websitePageContent {
	/**
	 * Log the class beying loaded
	*/
	function __construct() {
		$this->database['website'] = new DATABASE( DB_HOST_WEBSITE, DB_USER_WEBSITE, DB_PASS_WEBSITE );
	}

	/**
	 * Function for fetching a CMS page.
	 * @param path
	 *	String page path ( E.g.: community/advisories/terminology )
	 * @param isEscaped
	 *	Boolean if the string is escaped or not for the SQL query. Default to false.
	 * @return
	 *	String page content
	*/
	function getCMSPage( $path, $isEscaped = false ) {
		if ( $isEscaped !== true ) {
			$path = $this->database['website']->escapeString( $path );
		}
		$content = $this->database['website']->getRowValue( "website.web_pages_new", "content", "path = '".$path."'" );
		$content = str_replace( '##LATEST_SECUNIA_ADVISORIES##', '', $content );
		$GLOBALS['debug']->notice( "Website CMS page content fetched" );
		return $content;
	}
}
?>