<?php
/**
 * @file util.class.php
 * Provides basic Utility functions
 *
*/
class UTIL {
	private static $timeStart = '';
	private static $timeEnd = '';
	private  static $csidlPathMap = array(
		'%APPDATA%' => 26,
		'%LOCALAPPDATA%' => 28,
		'%SYSTEMROOT%' => 36,
		'%PROGRAMFILES%' => 38,
		'%USERPROFILE%' => 40,
		'%PROGRAMFILES(X86)%' => 42
	);
	const MAX_ROW_COUNT_PER_REQUEST_CLOUD = 5000;
	//CSIL-8232
	const MAX_ROW_COUNT_PER_REQUEST_RPM = 50000;
	//CSIL-10733
	const MAX_DEVICES_COUNT_PER_REQUEST_SG_PRODUCTS = 25000;

	public static function url_join($glue, $pieces){
		$s = '';
		foreach ( $pieces as $piece ) {
			$s .= (strlen($s) ? $glue : "") . urlencode($piece);
		}
		return $s;
	}

	/**
	 * Function for searching in a directory
	 * @param path
	 *	String path to search in ( E.g. "./" )
	 * @param extension
	 *	String extension we are looking for ( E.g. "php" )
	*/
	function find( $path, $extension ) {
		$fileArray = Array();
		if ( is_dir( $path ) ) {
			$rDirectory = @opendir( $path );
			if ( $rDirectory != false ) {
				while ( ( $rItem = readdir( $rDirectory ) ) !== false ) {
					if ( is_file( $path.$rItem ) ) {
						$tmp = explode( ".", $rItem);
						if ( $tmp[count($tmp)-1] == $extension ) {
							$fileArray[count($fileArray)] = $path.$rItem;
						}
						unset( $tmp );
					}  elseif ( is_dir( $path.$rItem ) && ( $rItem != "." ) && ( $rItem != ".." ) && ( $rItem != ".svn" ) ) {
						$fileArray = array_merge( $fileArray, $this->find( $path.$rItem."/", $extension ) );
						// TODO - use DIRECTORY_SEPARATOR above as in CSIS stuff
					}
				}
			}
		}
		@closedir( $rDirectory );
		return $fileArray;
	}

	/**
	 * Determine if an array has only numberic keys ( used by JSON::json_decode )
	 * @param
	 *	Array keys
	 * @return
	 *	Boolean true if they are all numeric, false if atleast one is not numeric
	*/
	function numericKeys( $keys, $noHoles = false ) {
		for ( $i = 0, $size = count( $keys ); $i < $size; $i++ ) {
			if ( $noHoles ) {
				if ( $i !== $keys[$i] ) {
					return false;
				}
			} else {
				if ( !is_numeric( $keys[$i] ) ) {
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * Validate a PDF filename, required for the scheduled reports.
	*/
	function validatePdfFilename( $filename ) {
		$pattern = '/^(\w+)([\-][\w]+)*[\.][Pp][Dd][Ff]$/';
		if ( preg_match( $pattern, $filename ) ) {
			return true;
		}
		return false;
	}

	/**
	 * Function for rendering a base64 file ( and allow download from browser ). Will print a HTTP file, and STOP the current script.
	 * @param fileName
	 *	String file name to be sent to browser
	 * @param fileType
	 *	String file type  ( E.g.: image/jpg )
	 * @param fileContent
	 *	String file content ( encoded in base 64 )
	*/
	function render64( $fileName, $fileType, $fileContent ) {
		if ( $fileContent == "" && $fileName == "" ) {
			header("HTTP/1.0 404 Not Found");
			echo "<h1>The requested file could not be found.</h1>
			Return to <a href='http://secunia.com/'>secunia.com</a>";
			$GLOBALS['debug']->notice( "File not found. " );
			die();
		}
		// @todo: The file type and filename should be whitelisted
		// @see use the Output::output() function instead.
		header("Content-type: ".$fileType);
		header("Content-disposition: filename=\"".$fileName."\"");
		print base64_decode( $fileContent );
		$GLOBALS['debug']->notice( "Rendered file: ".$fileName );
		die();
	}

	/**
	 * Function used by mergeSql.
	 * @param key
	 *	String key the search is based on
	 * @param value
	 *	String value of the key
	 * @param array
	 *	Array we are searching in
	*/
	function searchSqlArray( $key, $value, $array ) {
		for ( $i = 0; $i < count( $array['data'] ); $i++ ) {
			foreach ( $array['data'][$i] as $arrayKey => $arrayValue ) {
				if ( $arrayKey == $key && $value == $arrayValue ) {
					return $array['data'][$i];
				}
			}
		}

		return null;
	}

	/**
	 * Function for merging two or more SQL JSON ready row arrays, based on a key.
	 * Similar behavior to an SQL joint selection.
	 * Useful for merging rows from different databases ( with different access rights )
	 * @param key
	 *	String name of the key
	 * @param parentArray
	 *	Array containing the key
	 * @param childArray
	 *	Array of arrays to be merged
	*/
	function mergeSql( $key, $parentArray, $childArrays ) {
		for ( $i = 0; $i < count( $parentArray['data'] ); $i++ ) {
			$value = $parentArray['data'][$i][$key];
			for ( $j = 0; $j < count( $childArrays ); $j++ ) {
				$row = $this->searchSqlArray( $key, $value, $childArrays[$j] );
				if ( $row != null ) {
					$parentArray['data'][$i] = array_merge( $parentArray['data'][$i], $row );
				}
			}
		}
		return $parentArray;
	}

	/**
	 * Function for checking if an array has empty items. Will NOT iterate if there are more arrays within.
	 * @param array
	 *	Array subject
	*/
	function arrayIsEmpty( $array ) {
		foreach ( $array as $key => $value ) {
			if ( $value != "" ) {
				return false;
			}
		}
		return true;
	}

	/**
	 *
	 *	DEPRECATED
	 *
	 * Multi-purpose function for generating a random id.
	 * @param length
	 *	Integer length of the key
	 * @deprecated
	 *	Use PRNG class generateRandomID() for
	 *	stronger multi-platform randomness
	 *
	*/
	function generateRandomId( $length ) {
		// Numbers (48-57)
		for ( $i=48 ; $i<=57 ; $i++ ) {
			$chars[] = chr($i);
		}

		// UC Letters (65-90)
		for ( $i=65; $i<=90 ; $i++ ) {
			$chars[] = chr($i);
		}

		// LC Letters (97-122)
		for ( $i=97 ; $i<=122 ; $i++ ) {
			$chars[] = chr($i);
		}

		// Make id
		$id = '';
		for ( $i=0 ; $i<$length ; $i++ ) {
			srand ((double) microtime() * 948625);
			$id .= $chars[rand(0, count($chars)-1)];
		}

		// Return ID
		return $id;
	}

	/**
	 * Function for generating a MySQL Timestamp based on Datetime
	 * @param datetime
	 *	String date and time
	 * @return
	 *	String timestamp
	*/
	function MySQLDatetimeToTimestamp( $datetime, $utc = false ) {
		list( $date, $time ) = explode( " ", $datetime );
		$d = explode( "-", $date );
		$t = explode( ":", $time );

		if ( $utc ) {
			$time = gmmktime( $t[0], $t[1], $t[2], $d[1], $d[2], $d[0] );
		} else {
			$time = mktime( $t[0], $t[1], $t[2], $d[1], $d[2], $d[0] );
		}
		return $time;
	}

	/**
	 * Function for checking if the default timezone is UTC or not
	 *
	 * This is used if some code relying on system time is being shared between different
	 * projects (e.g. CSI and the VIM)
	 *
	 */
	function isUtcTime() {
		if ( date_default_timezone_get() === "UTC" ) {
			return true;
		}
		return false;
	}

	/**
	 * Function for returning the content of a SINGLE uploaded file.
	 * @param fieldName
	 *	String HTML upload field name ( to determine file position in the $_FILE array )
	 * @return
	 *	Mixed array RAW file content OR false if file can't be found. E.g. php is misconfigured or false if the file size has exceeded
	 *
	 *	TODO: add more tests e.g. file exists, is empty etc
	 *
	 */
	function uploadFileToText( $fieldName ) {
		if ( file_exists( $_FILES[$fieldName]['tmp_name'] ) ) {
			if ( $_FILES[$fieldName]["size"] / 1024 / 1024 > 5 ) {
				return false;
			}
			return file( $_FILES[$fieldName]['tmp_name'], FILE_IGNORE_NEW_LINES );
		}
		return false;
	}

    /**
     * Validate a PDF filename, required for the scheduled reports.
     */
    function validateCsvFilename( $filename ) {
        $pattern = '/^(\w+)([\-][\w]+)*[\.][Cc][Ss][Vv]$/';
        if ( preg_match( $pattern, $filename ) ) {
            return true;
        }
        return false;
    }

	/**
	 * Function for parsing a CSV file.
	 * @param fileContent
	 *	Array of each line within the file.
	 * @param delimiter
	 *	String delimiter, default to ','
	 * @param column
	 *	Integer column number, default 0
	 * @return
	 *	Array of values within the columns
	 *
	 * @todo:
	 *   This function should use str_getcsv() to parse the file
	 *
	 * @deprecated
	 *   This function doesn't take into account that the CSV items might have a " or a ,
	 *   Also, the function takes in an array as the argument but says that it is parsing
	 *   a file.
	 *   Use parseCsv() instead.
	 */
	function parseCSVFile( $fileContent, $delimiter = ",", $column = 0 ) {
		$tmp_list = array();

		foreach ( $fileContent as $line ) {
			$fields = explode( $delimiter, $line );
			if ( $fields[$column] ) {
				array_push( $tmp_list, $fields[$column] );
			}
		}

		return $tmp_list;
	}

	static function parseCsv( array $content, $delimiter = ",", $column = 0 ) {
		// stub
	}

	/**
	 * Function for determining delimiter in a CSV file.
	 * @param fileContent
	 *	Array of each line within the file
	 * @return
	 *	String or Array The detected delimiter, or an array of delimiters for the user to choose from
	 *
	*/
	function getCVSDelimiter( $fileContent ) {
		$delimiters = array();
		$line_num = 0;

		for ( $i = 0; $i < count( $fileContent ); $i++ ){
			if( empty( $fileContent[$i] ) ) {
				continue;
			}
			// Keep track of non-alpha numeric characters appearing on every line
			$stripped = preg_replace( "/[0-9A-Za-z ]*/", "", $fileContent[$i] );
			// A list of the characters appearing in this line
			$line_chars = array();

			// Loop over all non alpha-numeric characters
			for ( $j = 0; $j < strlen( $stripped ); $j++ ) {
				$char = $stripped[$j];
				// Track characters that occur on this line
				array_push( $line_chars, $char );
				// Character must have occurred on all lines previously to be included
				if ( !isset( $delimiters[$char] ) ) {
					$delimiters[$char] = false;
				}
				if ( $line_num == 0 || $delimiters[$char] ) {
					$delimiters[$char]++;
				}
			}

			// Unset any previous possible delimiters if they didn't occur on this line
			foreach ( $delimiters as $char => $occurrences ) {
				// If the character didn't occur on the line, it's not a delimiter
				if ( !in_array($char, $line_chars) )
					unset( $delimiters[$char] );
			}

			$line_num++;
		}

		// Check to see if a suitable delimiter has been found
		if ( empty( $delimiters ) ) {
			// Unable to determine delimiter, give the user some to choose from
			$delimiters = array (
				','		=> 1,
				';'		=> 1,
				'\t'	=> 1,
			);
			return $delimiters;
		} else if ( count($delimiters) == 1 ) {
			// Only one one possible delimiter, don't prompt the user, just use it
			$keys = array_keys( $delimiters );
			$delimiter = $keys[0];
			return $delimiter;
		}
		return $delimiters;
	}

	static function validateEmail( $mail ) {
		$pattern = '/^(\w+)([\-+.][\w]+)*@(\w[\-\w]*\.){1,5}([A-Za-z]){2,9}$/';
		if ( preg_match( $pattern, $mail ) ) {
			return true;
		}
		return false;
	}

	// An international number should be at least 8 digits and not more than 15
	static function validateMobile( $sms ) {
		// each international number should be atleast 8 digits
		if ( ctype_digit( $sms ) && strlen( $sms ) >= 8 && strlen( $sms ) <= 15 ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * For for applying UTF8 encode against a string or array.
	 * @param item
	 *	Mixed array or string
	 * @return
	 *	UTF8 Encoded string or array. NOTE: If array, both the keys and values will be encoded.
	*/
	function utf8_encode( $item ) {
		if ( is_object( $item ) ) {
			$item = get_object_vars( $item );
		}
		if ( is_array( $item ) ) { // Encode an array AND it's keys
			foreach ( $item as $key => $value ) {
				$item[utf8_encode( $key )] = $this->utf8_encode( $value );
			}
			return $item;
		} else {
			return utf8_encode( $item ); // Only a string.
		}
	}

	/**
	 * Function for converting an array to a comma separated string that contains only numbers.
	 * Will convert all the number types to integers i.e. float etc
	 *
	 * Useful when we need to use the array contents as comma separated values in a query.
	 *
	 * @deprecated Use the static function toCommaSeparatedInts() instead
	 *
	 */
	function toCommaSeparatedIntegers( $array ) {
		$response = "";
		foreach( $array as $value ) {
			if ( is_numeric( $value ) ) {
				if ( $response === "" ) {
					$response = (int) $value;
				} else {
					$response .= ( ",". (int) $value );
				}
			}
		}
		return $response;
	}

	/**
	 * Function for converting an array to a comma separated string that contains only numbers.
	 * Will convert all the number types to integers i.e. float etc
	 *
	 * Useful when we need to use the array contents as comma separated values in a query.
	 *
	 */
	public static function toCommaSeparatedInts( $array ) {
		$response = "";
		foreach( $array as $value ) {
			if ( is_numeric( $value ) ) {
				if ( $response === "" ) {
					$response = (int) $value;
				} else {
					$response .= ( ",". (int) $value );
				}
			}
		}
		return $response;
	}

	static function tStart() {
		self::$timeStart = microtime(true);
	}

	static function tShow( $round = false ) {
		self::$timeEnd = microtime(true);
		$timeDiff = self::$timeEnd - self::$timeStart;
		self::$timeStart = self::$timeEnd;
		if ( $round && is_numeric($round) ) {
			$timeDiff = round( $timeDiff, (int) $round );
		}
		return $timeDiff;
	}

	/**
	 * Removes an item with a specified value from an array
	 * @param mixed $value what to look for
	 * @param array $arr where to look
	 * @param boolean $removeAll if set to true, it will remove all matching items
	 * @return array the result of a possible removal
	 */
	static function removeValueFromArray( $value, $arr, $removeAll = false ) {
		$arr = (array)$arr;
		$keys = array_keys( $arr, $value );
		foreach ( $keys as $key ) {
			unset( $arr[$key] );
			if ( !$removeAll ) {
				return $arr;
			}
		}
		return $arr;
	}

	/**
	 * Compresses a file
	 * @param string $fileName the name of the file to compress
	 * @param string $filePath the absolute file path to the file
	 * 		that needs to be compressed. The $filePath must not
	 * 		contain trailing slashes
	 * @return mixed boolean false in case it fails, name of
	 * 		the compressed file if succeeds,
	 * 		the compressed file will be in the same folder
	 */
	static function compressFile( $filePath, $fileName ) {
		$inFile = $filePath . "/" . $fileName;
		if ( !file_exists( $inFile ) ) {
			return false;
		}

		//Compute a unique name for the file
		//by making sure we don't overwrite some
		//other file
		$outFile = $inFile . ".z";
		$tmpName = $outFile;
		$outFileName = $fileName . ".z";
		$i = 0;
		while ( file_exists( $tmpName ) ) {
			$tmpName = $outFile . $i;
			$outFileName = $fileName . ".z" . $i;
			$i++;
		}
		$outFile = $tmpName;

		$perlSrc = 'use Compress::Zlib;use IO::File;my ($d,$status)=deflateInit();exit 1 unless Z_OK==$status;my $infile=new IO::File("' . $inFile . '","r");exit 2 unless defined $infile;my $outfile=new IO::File("' . $outFile . '","w" );exit 3 unless defined $outfile;my ($res,$readbuf,$out,$len);my $readsize=1024*1024;while($res=$infile->read($readbuf,$readsize)){($out,$status)=$d->deflate($readbuf);exit 4 unless Z_OK==$status;$len=length($out);$writesize=$outfile->syswrite($out,$len);exit 5 unless $writesize==$len;}($out,$status)=$d->flush();exit 6 unless Z_OK==$status;$len=length($out);$writesize=$outfile->syswrite($out,$len);exit 7 unless $writesize == $len;exit 0;';

		$retVal = 0;
		if ( false === system( "/usr/bin/perl -e '$perlSrc'", $retVal ) || 0 !== $retVal ) {
			return false;
		} else {
			return $outFileName;
		}
	}

	/**
	* Checks validity of the date.
	*
	* Also checks if the date is posibble, so 2012-12-35 is not valid.
	*
	* @param string Date in format YYYY-MM-DD
	* @return boolean true if date is valid, false if it is not
	*/
	static function validateDate( $date ) {
		if ( !preg_match( '/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/', $date, $parts ) ) {
			return false;
		}

		if ( !checkdate( $parts[2], $parts[3], $parts[1] ) ) {
			return false;
		}

		return true;
	}

	/**
	* Checks validity of a datetime string, i.e. 2012-05-03 11:06:49
	*
	* Uses our custom validateDate for the date part (thus this also checks if the date is possible - see above).
	*
	* @param string Date in format "YYYY-MM-DD HH:MM:SS" (note, space is also required)
	* @return boolean true if datetime is valid, false if it is not
	*/
	static function validateDateTime( $dateTime ) {
		// Explode on the space (' ') delimiter to separate date from time
		$dataArray = explode( ' ', $dateTime );
		if ( 2 !== count( $dataArray ) ) {
			return false;
		}

		$date = $dataArray[0];
		$time = $dataArray[1];
		if ( !self::validateDate( $date ) ) {
			return false;
		}

		// Now validate time
		if ( !preg_match( '/^(([0-1][0-9])|([2][0-3])):([0-5][0-9]):([0-5][0-9])$/', $time ) ) {
			return false;
		}

		return true;
	}

	/**
	 * Validates if a supplied string has only alpha-numeric values [a-zA-Z0-9] and allowes additional specified characters.
	 * @param  string $str     String to validate
	 * @param  array  $allowed Array of allowed characters
	 * @return boolean         Is valid or not
	 */
	static function validateString( $str, $allowed = '' ) {
		if ( !empty( $allowed ) ) {
			// Removing allowed characters from supplied string
			$str = str_replace( $allowed, '', $str );
		}

		return ctype_alnum( $str );
	}

	/**
	 * Converts string with G, M, K as last char to byte value. Used in php.ini file where we define memory limit as '512M'.
	 * @param  string $value Value to convert to bytes
	 * @return boolean|int   False or size in bytes
	 */
	static function convertGMK( $value ) {
		$extension = substr( $value, -1 );
		$number = intval(substr( $value, 0, -1 ));
		switch( $extension ) {
		case 'G':
			$return = $number * pow( 1024, 3 );
			break;
		case 'M':
			$return = $number * pow( 1024, 2 );
			break;
		case 'K':
			$return = $number * 1024;
			break;
		default:
			$return = false;
			break;
		}
		return $return;
	}

	static function isMemLimitReached( $memoryUsagePercentage ) {
		// The Server Edition has no memory limit so we would get a #DIV/0 error
		if ( defined('EDITION') && EDITION == SERVER_EDITION ) { /* EDITION */
			return false;
		}  /* EDITION */

		$iniMemory = UTIL::convertGMK( ini_get( 'memory_limit' ));
		if ( $iniMemory !== false && (( memory_get_usage() /  $iniMemory * 100 ) > $memoryUsagePercentage) ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Sort an array of arrays by the value of the column specified
	 *
	 * This function acts on the array instead of returning one
	 */
	static function sortByColumn( Array &$rows, $column ) {
		$columnValues = array();
		foreach( $rows as $index => $row ) {
			$columnValues[ $index ] = $row[ $column ];
		}
		array_multisort( $columnValues, SORT_ASC, $rows );
	}

	/**
	 * The following function is avaiable in php >= 5.5.0
	 */
	static function array_column( Array $rows, $column ) {
		$values = array();
		foreach( $rows as $row ) {
			$values[] = $row[ $column ];
		}
		return $values;
	}
	
	/**
	 * Substitute of array_column function , works for all PHP verisons including < 5.5.0
	 * @param array $array
	 * @param string $columnKey
	 * @param string $indexKey
	 * @return array $return
	 */
	static function array_column_substitute($array, $columnKey, $indexKey = null)
	{
		$result = array();
		foreach ($array as $subArray) {
			if (is_null($indexKey) && array_key_exists($columnKey, $subArray)) {
				$result[] = is_object($subArray)?$subArray->$columnKey: $subArray[$columnKey];
			} elseif (array_key_exists($indexKey, $subArray)) {
				if (is_null($columnKey)) {
					$index = is_object($subArray)?$subArray->$indexKey: $subArray[$indexKey];
					$result[$index] = $subArray;
				} elseif (array_key_exists($columnKey, $subArray)) {
					$index = is_object($subArray)?$subArray->$indexKey: $subArray[$indexKey];
					$result[$index] = is_object($subArray)?$subArray->$columnKey: $subArray[$columnKey];
				}
			}
		}
		return $result;
	}

	public static function decodeStandardJSON( $rawData, $levels = 255 ) {
		// Convert the JSON data to a associative array
		$data = json_decode( $rawData, true, $levels );

		/**
		 * Handle any JSON errors
		 */
		$lastError = json_last_error();
		if ( $lastError !== JSON_ERROR_NONE ) {
			throw new SfwEx( "There was an error while trying to process JSON data provided.", $lastError );
		}

		return $data;
	}

	/**
	 * Checks if the values of the array are positive integers ( > 0 ).
	 *
	 * @param $array Array of elements
	 *
	 * @return bool True if array holds only positive integers, false otherwise
	 */
	public static function validateArrayPositiveInt( $array ) {
		foreach( $array as $val ) {
			if ( filter_var( $val, FILTER_VALIDATE_INT, array( 'options' => array( 'min_range' => 1 ) ) ) === false ) {
				return false;
			}
		}

		return true;
	}

    /**
     * Function to format the xml string
     *
     * @see http://stackoverflow.com/questions/3616540/format-xml-string
     */
    public static function formatXmlString($xml){
        $xml = preg_replace('/(>)(<)(\/*)/', "$1\n$2$3", $xml);
        $token = strtok($xml, "\n");
        $result = '';
        $pad = 0;
        $matches = array();
        while ($token !== false) :
            if (preg_match('/.+<\/\w[^>]*>$/', $token, $matches)) :
                $indent=0;
            elseif (preg_match('/^<\/\w/', $token, $matches)) :
                $pad--;
                $indent = 0;
            elseif (preg_match('/^<\w[^>]*[^\/]>.*$/', $token, $matches)) :
                $indent=1;
            else :
                $indent = 0;
            endif;
            $line = str_pad($token, strlen($token)+$pad, ' ', STR_PAD_LEFT);
            $result .= $line . "\n";
            $token = strtok("\n");
            $pad += $indent;
        endwhile;
        return $result;
    }

	/**
	 * Checks if the values of the array are integers.
	 *
	 * @param $array Array of elements
	 *
	 * @return bool True if array holds only integers, false otherwise
	 */
	public static function validateArrayInt( $array ) {
		foreach( $array as $val ) {
			if ( filter_var( $val, FILTER_VALIDATE_INT ) === false ) {
				return false;
			}
		}

		return true;
	}


	/**
	 * password used to encrypt for server edition in the bash script
	 */
	public static $bashAESPassword = "qwefm3452k34mkek234mr";

	public static function decryptConfigConstant($value)
	{
		if (defined('PASSWORD_ENC') && strlen(PASSWORD_ENC)) {
			$fullDecryptPassword = PASSWORD_ENC . UTIL::$bashAESPassword;
			return self::decryptAES256($fullDecryptPassword, $value);
		}
		return $value;
	}

	public static function encryptConfigConstant($value)
	{
		if (defined('PASSWORD_ENC') && strlen(PASSWORD_ENC)) {
			$fullEncryptPassword = PASSWORD_ENC . UTIL::$bashAESPassword;
			return self::cryptAES256($fullEncryptPassword, $value);
		}
		return $value;
	}

	/**
	 * decrypt AES 256
	 *
	 * @param string $password
	 * @param data $edata
	 * @return dencrypted data
	 */
	public static function decryptAES256($password, $edata) {
		$data = base64_decode($edata);
		$salt = substr($data, 8, 8);
		$ct = substr($data, 16);

		$rounds = 3;
		$data00 = $password.$salt;
		$md5_hash = array();
		$md5_hash[0] = md5($data00, true);
		$result = $md5_hash[0];
		for ($i = 1; $i < $rounds; $i++) {
			$md5_hash[$i] = md5($md5_hash[$i - 1].$data00, true);
			$result .= $md5_hash[$i];
		}
		$key = substr($result, 0, 32);
		$iv  = substr($result, 32,16);

		return openssl_decrypt($ct, 'aes-256-cbc', $key, true, $iv);
	}

	/**
	 * crypt AES 256
	 *
	 * @param string $password
	 * @param data $data
	 * @return base64 encrypted data
	 */
	public static function cryptAES256($password, $data) {
		// Set a random salt
		$salt = openssl_random_pseudo_bytes(8);

		$salted = '';
		$dx = '';
		// Salt the key(32) and iv(16) = 48
		while (strlen($salted) < 48) {
			$dx = md5($dx.$password.$salt, true);
			$salted .= $dx;
		}

		$key = substr($salted, 0, 32);
		$iv  = substr($salted, 32,16);

		$encrypted_data = openssl_encrypt($data, 'aes-256-cbc', $key, true, $iv);
		return base64_encode('Salted__' . $salt . $encrypted_data);
	}

	public static function setFlagOn(&$var,$flag){
		$var = ($var|$flag);
	}
	public static function setFlagOff(&$var,$flag){
		$var = ($var&~$flag);
	}
	public static function checkFlagOn($var,$flag){
		return $var & $flag? true : false;
	}

	/**
	 * Wrapper for htmlentities() that sets the default encoding to ISO-8859-1.
	 *
	 * In PHP versions less than 5.4 the default encoding for htmlentities is ISO-8859-1.
	 * In PHP version 5.4 and greater the default encoding for htmlentities is UTF-8.
	 *
	 * @todo Make this an instance method if refactoring the CSI in to a framework design
	 * @issue CSI-6342
	 * @See http://www.php.net/manual/en/function.htmlentities.php
	 * @see UTIL::htmlspecialchars()
	 *
	 * @param string $string The input string
	 * @param int $flags Bitmask of encoding flags. See the PHP documentation
	 * @param string $encoding
	 *	Defines the encoding to use while converting characters - defaults to ISO-8859-1
	 * @return string Html encoded string
	 */
	public static function htmlentities($string, $flags = ENT_COMPAT, $encoding = 'ISO-8859-1')
	{
		return htmlentities($string, $flags, $encoding);
	}

	/**
	 * Wrapper for htmlspecialchars() that sets the default encoding to ISO-8859-1.
	 *
	 * In PHP versions less than 5.4 the default encoding for htmlspecialchars is ISO-8859-1.
	 * In PHP version 5.4 and greater the default encoding for htmlspecialchars is UTF-8.
	 *
	 * @todo Make this an instance method if refactoring the CSI in to a framework design
	 * @issue CSI-6342
	 * @See http://www.php.net/manual/en/function.htmlspecialchars.php
	 * @see UTIL::htmlentities()
	 *
	 * @param string $string The input string
	 * @param int $flags Bitmask of encoding flags. See the PHP documentation
	 * @param string $encoding
	 *	Defines the encoding to use while converting characters - defaults to ISO-8859-1
	 * @return string Html encoded string
	 */
	public static function htmlspecialchars($string, $flags = ENT_COMPAT, $encoding = 'ISO-8859-1')
	{
               if(empty($string)){
                   return '';
               }
		return htmlspecialchars($string, $flags, $encoding);
	}

    /**
     *
     * Convert CSIDL agent path *XY to %PATH%
     *
     * @param $path
     * @return mixed
     */
	public static function csidlPath($path) {

        if ($path != '' && $path[0] == '*') {

            $data = array ('*26' => '%APPDATA%', '*28' => '%LOCALAPPDATA%',
                '*36' => '%SYSTEMROOT%', '*38' => '%PROGRAMFILES%',
                '*40' => '%USERPROFILE%', '*42' => '%PROGRAMFILES(X86)%');

            $search = array_keys($data);
            $replace = array_values($data);

            return str_ireplace($search, $replace, $path);
        }

        return $path;
    }

    /**
     *
     * Don't use mysql password() when password policy enabled
     * MySQL >= 5.6.6 - validate_password_policy
     * MySQL 8        - validate_password.policy - password() deprecated
     *
     * @param $conn
     * @return bool
     */
    public static function isPasswordPolicyEnabled($conn) {
        $result = null;
        $query = "SHOW VARIABLES LIKE '%validate_password%'";

        if ($conn instanceof DB) {
            $result = $conn->execRaw($query)->fetchAll();
        } else if ($conn instanceof DATABASE) {
            $result = $conn->queryGetRows($query);
        }

        if (count($result) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $version 5.5.52-MariaDB,
     * 10.3.8-MariaDB,
     * 5.1.73, 5.7.22-log, 5.7.22-ndb-7.6.6-cluster-commercial-advanced-log
     * @return bool
     */
    public static function dbServerSupportAlter($version) {

        $isSupportAlter = false;
        list ($numeric_version, $server_name) = explode('-', $version);

        //MariaDB >= 10.2.0 Supports ALTER
        if (stristr($version, 'maria')) {
            if (version_compare('10.2.0', $numeric_version, '<=')) {
                $isSupportAlter = true;
            }
        } else { //MySQL
            if (version_compare('5.7.0', $numeric_version, '<=')) {
                $isSupportAlter = true;
            }
        }

        return $isSupportAlter;

    }

	/**
	 * @param $rows from csi_device_software table
	 * @return array
	 */
	public static function transformCsidlPaths($rows) {
		$rowsWithUniquePath = array();
		foreach ($rows as $row) {
			//Check path value which start with % (csidl value)
			if (strpos($row[1],'%') === 0) {
				//Separate csidl value from path and check key  exist in our $csidlPathMap array
				$csidlPath = substr($row[1], 0, strpos($row[1],'%',  1) + 1);
				if (array_key_exists($csidlPath, self::$csidlPathMap)) {
					//Add csidl value to the column 3 (Advanced Options column) and remove csidl value from column 1 (Path column)
					//Check in csi_sps_wizard.js and  csi_vpm_wizard.js before changing column order
					$row[3] = self::$csidlPathMap[$csidlPath];
					$row[1] = substr($row[1],strpos($row[1],'%',  1) + 1);;
				}
			}
			//Remove duplicate paths from array and increase count in column 2 (Information column)
			//Add duplicate path csidl values as comma separated to column 3 (Advanced Options column)
			if (array_key_exists($row[1], $rowsWithUniquePath)) {
				$rowsWithUniquePath[$row[1]][2] = (string)(((int) $rowsWithUniquePath[$row[1]][2]) + 1);
				$rowsWithUniquePath[$row[1]][3] = $rowsWithUniquePath[$row[1]][3] .',' . $row[3];
			}else {
				$rowsWithUniquePath[$row[1]] = $row;
			}
		}

		return array_values($rowsWithUniquePath);

	}

	public static function transformPaths ($paths)	{
		$fPaths = array(); //Final formatted path list
		$cPaths = array(); //CSIDL path for loop life time
		$aPaths = array(); //Absolute path
		foreach ($paths as $path) {
			if (stripos($path, '%') === 0) {
				if (preg_match('/^%(.*?)%/i', $path, $matches)) {
					if (array_key_exists($matches[0], self::$csidlPathMap)) {
						$cPaths['csidls'] = array(self::$csidlPathMap[$matches[0]]);
						$cPaths['path_data'] = str_ireplace($matches[0], '', $path);
						$fPaths[] = $cPaths;
					}
				}
			} else {
				$aPaths['csidls'] = array();
				$aPaths['path_data'] = $path;
				$fPaths[] = $aPaths;
			}
		}
		$fPaths[] = $aPaths;
		return array_values($fPaths);
	}

	/**
	 * major.minor.build.update
	 * ProductVersion [0-255].[0-255].[0-65535].[0-65535]
	 * FileVersion [0-65535].[0-65535].[0-65535].[0-65535] Unsigned 16 bit
	 * @param $version
	 * @return string
	 */
	public static function sanitizeVersion($version, $source = null) {
		$max_value = $source == 'ws1' ? 32767 : 65535;
		if (strlen($version) >= 7 && stripos($version, '.') > 0) { //skip for min length <= 7 (version: 1.65535)
			$component = explode('.', $version);
			if (is_array($component)) {
				foreach ($component as $k => $c) {
					if (is_numeric($c) && $c >= $max_value && $k >= 1) { //$k >= 1, msu package have single component, return
						$v[] = $max_value;//max value
					} else {
						$v[] = $c;
					}
				}
				return implode('.', $v);
			}
		}

		return (string)$version;
	}

	/**
	 * Get max row count per request for Report
	 * @return Number (limit)
	 */
	public static function getReportMaxRowCountPerRequest() {
		$amount = self::MAX_ROW_COUNT_PER_REQUEST_CLOUD;
		//CSIL-8232 fetch 50K records for RPM installations
		if ( EDITION == SERVER_EDITION ) {
			$amount = self::MAX_ROW_COUNT_PER_REQUEST_RPM;
		}

		$privateDb = DB::getInstanceByName( 'private' );
		$optionValue = $privateDb->select()
			->columns(array('option_value'))
			->from( 'csi_configuration' )
			->where( array(
				'option_name' => 'REPORT_MAX_ROW_COUNT_PER_REQUEST'
			) )
			->limit(1)
			->setOption(Select::RETURN_PDO_STATEMENT, 1)
			->exec()
			->fetchColumn();

		if (!empty($optionValue)) {
			$amount = (int) $optionValue;
		}

		return $amount;
	}

    /**
     * Get smart group name
     * @return String (name)
     */
    public static function getSmartGroupNameForReport($options, $smartGroupToken) {
        $smartGroupId = 0;
        $smartGroupName = "";
        $keyLength = strlen($smartGroupToken);
        foreach ($options as $key => $val) {
            if ( $smartGroupToken == substr( $val, 0, $keyLength ) ) {
                $extraOptions = substr( $val, $keyLength + 1 );
                $smartGroupId = (int) $extraOptions;
                break;
            }
        }
        if ($smartGroupId === 0) {
            return "";
        }
        $smartGroupName = DB::getInstanceByName( 'private' )->select()
            ->columns( array( 'name' ) )
            ->from( 'csi_smartgroups' )
            ->where( array( 'id' => $smartGroupId ) )
            ->limit(1)
            ->setOption(Select::RETURN_PDO_STATEMENT, 1)
            ->exec()
            ->fetchColumn();
        if ($smartGroupName !== false) {
            $smartGroupName = preg_replace('/[^a-z0-9]/i', '_', $smartGroupName);
            return $smartGroupName;
        }

        return "";
    }

    /**
     * Check cst id is large customer for report
     * @return Boolean
     */
    public static function isLargeCstReport($cstId) {

        $largeCst = DB::getInstanceByName( 'common' )->select()
            ->columns( array( 'accounts.cst_id') )
            ->from( 'accounts')
            ->join( 'csi_large_customers' )
            ->on( array( 'csi_large_customers.cst_id' => 'accounts.cst_id') )
            ->where(array("csi_large_customers.type" => 0, 'accounts.cst_id' => $cstId))
            ->limit(1)
            ->setOption(Select::RETURN_PDO_STATEMENT, 1)
            ->exec()
            ->fetchColumn();
        if ($largeCst !== false) {
            return true;
        }

        return false;
    }
    
    
    /**
     * CSIL-10733  Check cst id is large customer for SG, this function could be used for new flag types as well
     * @return Boolean
     */
    public static function isLargeCst($cstId, $type) {
    	
    	$largeCst = DB::getInstanceByName( 'common' )->select()
    	->columns( array( 'accounts.cst_id') )
    	->from( 'accounts')
    	->join( 'csi_large_customers' )
    	->on( array( 'csi_large_customers.cst_id' => 'accounts.cst_id') )
    	->where(array("csi_large_customers.type" => $type, 'accounts.cst_id' => $cstId))
    	->limit(1)
    	->setOption(Select::RETURN_PDO_STATEMENT, 1)
    	->exec()
    	->fetchColumn();
    	if ($largeCst !== false) {
    		return true;
    	}
    	
    	return false;
    }
    
    /**
     * CSIL-10733 Get devices per request for SG product temp_smartgroup_product
     * @return INT (limit)
     */
    public static function getSGMaxDevicesCountPerRequest() {
    	
    	$privateDb = DB::getInstanceByName( 'private' );
    	$optionValue = $privateDb->select()
    	->columns(array('option_value'))
    	->from( 'csi_configuration' )
    	->where( array(
    			'option_name' => 'PRODUCT_SG_DEVICES_FETCH_PER_LOOP'
    	) )
    	->limit(1)
    	->setOption(Select::RETURN_PDO_STATEMENT, 1)
    	->exec()
    	->fetchColumn();
    	
    	if (!empty($optionValue)) {
    		$amount = (int) $optionValue;
    	} else {
    		$amount = (int) self::MAX_DEVICES_COUNT_PER_REQUEST_SG_PRODUCTS;
    	}
    	
    	return $amount;
    }

	public static function generateGuid() {
		return (string)\Uuid\Uuid::generate(4);
	}

	public static function transformUninstallString( $path )	{
		if (empty($path)) {
			return null;
		}
		$csidlMap = array(
			'{AppData}' => '%AppData%',
			'{LocalAppData}' => '%LocalAppData%',
			'{SystemRoot}' => '%SystemRoot%',
			'{ProgramFiles}' => '%ProgramFiles%',
			'{UserProfile}' => '%UserProfile%',
			'{ProgramFiles(x86)}' => '%ProgramFiles(x86)%',
			'{ProgramData}' => '%ProgramData%'
		);
		$fPath = $path; //Final formatted path

		foreach ($csidlMap as $key => $val) {
			$fPath = str_replace($key, $val, $fPath);
		}

		return $fPath;
	}

	public static function parseURL($sURL)
	{
		$aURL = parse_url($sURL);
		if (!isset($aURL['port'])) {
			$aURL['port'] = ($aURL['scheme'] == "https" ? 443 : 80);
		}
		return $aURL;
	}

    public static function pathExpansion($path)
    {
        if (preg_match('/\%([^)]+)\%/', $path, $match)) {
            if (!empty($match)) {
                $abs_path = preg_match_all('/%/', $path, $matches, PREG_OFFSET_CAPTURE);
                if ($match[1] == 'USERPROFILE') {
                    $expansionPath = ':\\\\Users\\\\%' . substr($path, $matches[0][1][1] + 1);
                } else if ($match[1] == 'LOCALAPPDATA') {
                    $expansionPath = ':\\\\Users\\\\%\\\\AppData\\\\Local' . substr($path, $matches[0][1][1] + 1);
                } else if ($match[1] == 'APPDATA') {
                    $expansionPath = ':\\\\Users\\\\%\\\\AppData\\\\Roaming' . substr($path, $matches[0][1][1] + 1);
                } else if ($match[1] == 'SYSTEMROOT') {
                    $expansionPath = ':\\\\Windows' . substr($path, $matches[0][1][1] + 1);
                } else if ($match[1] == 'PROGRAMFILES') {
                    $expansionPath = ':\\\\Program Files';
                    $subString = substr($path, $matches[0][1][1] + 1);
                    if (empty($subString)) {
                        $expansionPath .= '\\\\';
                    } else {
                        $expansionPath .= substr($path, $matches[0][1][1] + 1);
                    }
                }
                return $expansionPath;
            } else {
                return false;
            }
        } else {
            $subString = explode('%', $path);
            if ($subString[1] == 'PROGRAMFILES(X86)') {
                $expansionPath = ':\\\\Program Files (x86)' . $subString[2];
                return $expansionPath;
            } else {
                return false;
            }
        }
        return false;
    }

	/**
	 * @param $paths
	 * @return array
	 */
	public function getPathsForPublisher($paths)
	{
		//$replace_key = array(0 => 'checked', 1= > 'path', 2 => 'count', 3 => 'csidl');
		$cPaths = array();
		$fPaths = array();
		foreach ($paths as $path) {
			if ($path[1] != '') {
				$cPaths['checked'] = $path[0];
				$cPaths['count'] = $path[2];
				$path3 = trim($path[3]);
				if ($path3 == '') { //No CSIDL
					$cPaths['csidl'] = array();
				} elseif (stripos($path3, ',') === false) { //One CSIDL
					$cPaths['csidl'] = array($path3);
				} else { //Multiple CSIDL
					$cPaths['csidl'] = explode(',', $path3);
				}
				$cPaths['path'] = $path[1];
				$fPaths[] = $cPaths;
			}
		}
		return $fPaths;
	}

    /**
     * @return mixed
     */
    public static function gzJsonData()
    {
        $payload = '';
        if (isset($_SERVER['HTTP_CONTENT_ENCODING']) && stripos($_SERVER['HTTP_CONTENT_ENCODING'], 'gzip') !== false) {
            $payload = gzdecode(file_get_contents('php://input'));
        }
        $data = json_decode($payload, true);
        return $data;
    }

    public static function isSGMigrated($smartGroupId) {

        $privateDb = DB::getInstanceByName( 'private' );
        $isMigrated = $privateDb->select()
            ->columns( array('is_migrated' ))
            ->from( 'csi_smartgroups' )
            ->where(array('id' => $smartGroupId))
            ->setOption( Select::RETURN_PDO_STATEMENT, 1 )
            ->exec()
            ->fetch( PDO::FETCH_COLUMN );

        if($isMigrated){
            return true;
        }

        return false;
    }

    public static function getAssignmentsForSubscription ($subscriptionId, $connectionId, $accountId, $taskGuid )	{

        $privateDb = DB::getInstanceByName( 'private' );

        $values[':subscription_id'] = $subscriptionId;
        $values[':account_id'] = $accountId;
        $values[':connection_id'] = $connectionId;
        $values[':task_guid'] = $taskGuid;

        $sql = "SELECT cpsa.connection_id, cpsa.group_type AS intent, cpsa.group_mode, cpsa.group_guid , cpsa.group_all, cig.name as group_name
				FROM csi_assignments cpsa LEFT JOIN csi_intune_groups cig
				ON cpsa.csi_intune_groups_id = cig.id
				WHERE subscription_id = :subscription_id  AND cpsa.connection_id = :connection_id AND cpsa.account_id = :account_id AND cpsa.task_guid = :task_guid";

        $rows = $privateDb->execRaw($sql,$values)->fetchAll();

        if(empty($rows)){
            return array();
        }else{
            return $rows;
        }

    }

	public static function copySubscriptionAssignments ($subscriptionId, $connectionId, $accountId, $taskGuid )	{

		$privateDb = DB::getInstanceByName( 'private' );

		$values[':subscription_id'] = $subscriptionId;
		$values[':account_id'] = $accountId;
		$values[':connection_id'] = $connectionId;

        $checkSubscription =  $privateDb->select()
            ->columns(array('assignment_id'))
            ->from('csi_assignments')
            ->where(array('subscription_id' => $subscriptionId, 'account_id' => $accountId, 'connection_id' => $connectionId, 'task_guid' => $taskGuid))
            ->setOption( Select::RETURN_PDO_STATEMENT, true )
            ->exec()
            ->fetch( PDO::FETCH_ASSOC );

        if(empty($checkSubscription)){

            $sql = "INSERT INTO csi_assignments (connection_id,connection_type, csi_intune_groups_id,group_type,group_guid,group_mode,subscription_id,account_id,local_time,group_all,task_guid)
				SELECT connection_id,connection_type,csi_intune_groups_id,group_type,group_guid,group_mode,subscription_id,account_id,local_time,group_all,'".$taskGuid."'
				FROM csi_patch_subscription_assignments
				WHERE subscription_id= :subscription_id AND connection_id = :connection_id AND account_id = :account_id;";

            $rows = $privateDb->execRaw($sql,$values);
        }


        return true;

	}

    public static function getAssignmentsForWizard ($wizardId, $connectionId, $accountId, $taskGuid )	{

        $privateDb = DB::getInstanceByName( 'private' );

        $values[':account_id'] = $accountId;
        $values[':connection_id'] = $connectionId;
        $values[':task_guid'] = $taskGuid;

        $sql = "SELECT cs.connection_id, cs.group_type AS intent, cs.group_mode, cs.group_guid , cs.group_all, cig.name as group_name
                FROM csi_assignments cs LEFT JOIN csi_intune_groups cig
                ON cs.csi_intune_groups_id = cig.id
                WHERE cs.task_guid = :task_guid AND cs.connection_id = :connection_id AND cs.account_id = :account_id";

        $rows = $privateDb->execRaw($sql,$values)->fetchAll();

        if(empty($rows)){
            return array();
        }else{
            return $rows;
        }
    }

	public static function getSuffixByIntent($intent) {
		switch ($intent) {
			case 0:
				return ' (Available)';
			case 2:
				return ' (Uninstall)';
			default:
				return ' (Required)';
		}
	}

    public static function insertIntuneGroups ($groupName, $groupGuid, $connectionId, $accountId )	{

        $privateDb = DB::getInstanceByName( 'private' );

        $intuneGroupsColumns['guid'] = $groupGuid;
        $intuneGroupsColumns['name'] = $groupName;
        $intuneGroupsColumns['connection_id'] = $connectionId;
        $intuneGroupsColumns['account_id'] = $accountId;
        $intuneGroupsColumns['sync_date'] = SqlFn::UTC_TIMESTAMP();

        $lastInsertId = $privateDb->insert()
            ->into('csi_intune_groups')
            ->set($intuneGroupsColumns)
            ->exec();

        return $lastInsertId;
    }

    public static function getAssignmentsForSinglePatch($singlePatchId, $connectionId, $accountId, $taskGuid )	{

        $privateDb = DB::getInstanceByName( 'private' );

        $values[':account_id'] = $accountId;
        $values[':connection_id'] = $connectionId;
        $values[':task_guid'] = $taskGuid;
        $values[':single_patch_id'] = $singlePatchId;

        $sql = "SELECT cs.connection_id, cs.group_type AS intent, cs.group_mode, cs.group_guid , cs.group_all, cig.name as group_name
                FROM csi_assignments cs LEFT JOIN csi_intune_groups cig
                ON cs.csi_intune_groups_id = cig.id
                WHERE cs.task_guid = :task_guid AND cs.connection_id = :connection_id AND cs.account_id = :account_id AND cs.single_patch_id = :single_patch_id";

        $rows = $privateDb->execRaw($sql,$values)->fetchAll();

        if(empty($rows)){
            return array();
        }else{
            return $rows;
        }
    }

    public static function getDisplayNameAndRegistryPath($registryName)	{

        $lastBackslashPos = strrpos($registryName, '\\');

        if ($lastBackslashPos === false) {
            $registryPath = '';
            $displayName = $registryName;
        } else {
            $registryPath = substr($registryName, 0, $lastBackslashPos);
            $displayName = substr($registryName, $lastBackslashPos + 1);
        }

        // If version is not provided, try to extract it from displayName
        if (empty($version)) {
            if (preg_match('/\d+\.\d+(?:\.\d+){0,2}/', $displayName, $matches)) {
                $version = $matches[0];
            }

            if (preg_match('/\d+\.\d+(?:\.\d+){0,2}/', $registryPath, $matches)) {
                $patchVersion = $matches[0];
            }
        }

        if (!empty($version)) {
            $displayName = str_replace($version, '*', $displayName);
            $displayName = trim($displayName, "-_\\ "); // clean up separators
        }

        // Step 4: Remove version from path if present
        if (!empty($patchVersion)) {
            // Step 2: Split into parts
            $parts = explode('\\', $registryPath);
            // Step 3: Pattern to match version (e.g., 1.2.3, 2.0, etc.)
            $versionPattern = '/\d+\.\d+(?:\.\d+){0,2}/';
            // Step 4: Build new path
            $newParts = [];

            foreach ($parts as $part) {
                if (preg_match($versionPattern, $part)) {
                    // Replace only the matching part with *
                    $newParts[] = preg_replace($versionPattern, '*', $part);
                } else {
                    $newParts[] = $part;
                }
            }
            // Step 5: Rebuild registry path
            $registryPath = implode('\\', $newParts);
        }

        return [
            'registry_path' => $registryPath,
            'display_name' => $displayName
        ];

    }

    public static function replaceregistryKey($registryKey)	{

        $registryKey = trim($registryKey);

        if (strpos($registryKey, 'HL64') === 0) {
            $registryKey = str_replace('HL64', 'HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall', $registryKey);
        } elseif (strpos($registryKey, 'HL32') === 0) {
            $registryKey = str_replace('HL32', 'HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall', $registryKey);
        }elseif (strpos($registryKey, 'HC64') === 0) {
            $registryKey = str_replace('HC64', 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall', $registryKey);
        }

        return $registryKey;
    }

    public static function normalizeVersion($version) {
        $parts = explode('.', (string)$version);

        if (count($parts) < 3) {
            $parts = array_pad($parts, 3, '0');
        }

        return implode('.', $parts);
    }

    public static function getMultiSetupPackageData($vmpId, $cstId) {

        $privateDb = DB::getInstanceByName( 'private' );

        $multiSetupPackageData = array();

        $multiSetupPackages = $privateDb->select()
            ->columns(array('id', 'vpm_id', 'filename','md5'))
            ->from('vuln_track.vpm_multi_setup_packages')
            ->where(array('vpm_id' => $vmpId))
            ->setOption( Select::RETURN_PDO_STATEMENT, true )
            ->exec()
            ->fetchAll( PDO::FETCH_ASSOC );

        if(!empty($multiSetupPackages)) {
            foreach($multiSetupPackages as $multi) {
                $multiPackgedata = array();
                $multiPackgedata['download_url'] = "https://dl.csi7.secunia.com/?action=vpm_download_svm&src=vpm&token=F8859A92-C9C5-43F2-95D7-9A3E19FEC0B6&vpm_id={$vmpId}&cstid={$cstId}&multi_package_id={$multi['id']}";
                $multiPackgedata['filename'] = $multi['filename'];
                $multiPackgedata['md5'] = $multi['md5'];

                $multiSetupPackageData[]  = $multiPackgedata;
            }
        }

        return $multiSetupPackageData;

    }


}
