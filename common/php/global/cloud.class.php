<?php
/*
* @file cloud.class.php
* Manage the private databases.
*
* TODO: This file is in globals, but it is CSI specific and should be in local.
*
*/
final class CLOUD  implements Psr\Log\LoggerAwareInterface {
	private $cst_id;
	private $partitionId;
	private $logger;
    /**
     * @var DB
     */
    private $commonDb;

	/**
	 * @var string
	 * Cache of the dbClientHostCache, used for cheap repeat lookups
	 */
	private $dbClientHostCache = '';

	protected $cache;

	protected function setupCacheCloudClass() {
        // Set the CACHE object
		if ( class_exists( 'CACHE' ) ) {
			$this->cache = CACHE::getSelfInstance();
		}
	}

	/**
	 * @param $customerId Integer (Optional)
	 *    The customer id is kept optional as we sometimes need to init the CLOUD object
	 *    without binding it to a customer. This is required for admin purposes. However,
	 *    when we need to work with a single customer, the customerId should be provided
	 *    in the constructor.
	 */
	function __construct( $customerId = 0, $partitionId = 0 ) {
		$customerId = (int) $customerId;

		if ( $customerId > 0 ) {
			$this->cst_id = $customerId;
		}

		$this->partitionId = $partitionId;

		if ( isset( $GLOBALS[ "debug" ] ) ) {
			$this->logger = $GLOBALS[ "debug" ];
		}

		$this->setupCacheCloudClass();
	}

	/*
	 * Dependency injection setter for the logger object
	 * @param LoggerInterface $logger Use the logger to debug
	 */
	public function setLogger( Psr\Log\LoggerInterface $logger ) {
		$this->logger = $logger;
	}

	public function setCommonDb( DB $commonDb ) {
		$this->commonDb = $commonDb;
	}

	// Get the DB hostname the user connects to
	private function get_db_client_host() {
		// fetch from cache if already cached
		if ( '' !== $this->dbClientHostCache ) {
			return $this->dbClientHostCache;
		}

		if($this->cache && $dbClientHost  = $this->cache->get('db_client_host_'.$this->cst_id.'_'.$this->partitionId)) {
			$dbClientHost = $dbClientHost;

		} else if ( isset( $this->commonDb ) ) {

			$hosts = $this->commonDb->select()
				->columns( array(
					 "db_client_host"
				) )
				->from( "csi_pdb_info" )
				->where( array(
					 "cst_id" => $this->cst_id
					,"partition_id" => $this->partitionId
				) )
				->limit( 2 )
				->exec();
			if ( count( $hosts ) !== 1 ) {
				throw new SfwEx( "An unexpected number of hosts was found in the csi_pdb_info for CST_ID " . $this->cst_id . " and partition ID " . $this->partitionId . "." );
			}
			$dbClientHost = $hosts[ 0 ][ "db_client_host" ];
		}  else if ( isset( $GLOBALS[ "database" ] ) ) {

			$dbClientHost = $GLOBALS["database"]["common"]->getRowValue("csi_pdb_info", "db_client_host", "cst_id = '" . (int)$this->cst_id . "' AND partition_id = '" . (int)$this->partitionId . "'");

		}
		else {
			throw new SfwEx( "Could not find a common db instance to use to fetch partition related metadata." );
		}

		// cache $dbClientHost for next lookups
		$this->dbClientHostCache = $dbClientHost;

		return $dbClientHost;
	}

	// static wrapper around parent function
	public static function getDatabaseName( $cstId, $partitionId ) {
		$dbName = DB_PREFIX . (int) $cstId;
		if ( (int) $partitionId > 0 ) {
			$dbName .= '_' . (int) $partitionId;
		}

		return $dbName;
	}

	public static function getDatabaseUsername( $cstId, $partitionId ) {
		$dbUsername = (int) $cstId;
		if ( (int) $partitionId > 0 ) {
			$dbUsername .= '_' . (int) $partitionId;
		}

		return $dbUsername;
	}

	// Generate a new database/partition name
	public function get_database_name() {
		return self::getDatabaseName( $this->cst_id, $this->partitionId );
	}

	// Generate a new database/partition username
	private function get_database_username() {
		return self::getDatabaseUsername( $this->cst_id, $this->partitionId );
	}

	// Generate a new database/partition password
	private function get_database_password() {
		// CSIL-8694 Pvt DB password from configuration.php
		return DB_PASS_PVTDB;
	}

	/**
	 * Gets all the partitions grouped by customer
	 * @param  DB     $commonDb An instance of a database which contains the
	 *                          accounts table
	 * @return array           An array with partitions and their corresponing customer id.
	 */
	public static function getClouds( DB $commonDb ) {
		// Use the accounts table instead of csi_pdb_info becuase
		// the information can be found in the accounts table as well and
		// there's no need to join to verify the account limit. In the future,
		// when the accounts won't be differentiated by version, some other method
		// of fething the partitions might be better.
		return $commonDb->select()
			->columns( array(
				 "cst_id"
				,"partition_id"
			) )
			->from( "accounts" )
			->where( array(
				 "special_limits" => CSI_VERSION_SPECIAL_LIMITS
				,"account_expires" => Exp::gt( SqlFn::UTC_TIMESTAMP() )
			) )
			->groupBy( array(
				 "cst_id"
				,"partition_id"
			) )
			->exec();
	}
	 
	/**
	 * CSIL-9799 - Gets all the partitions grouped by customer
	 * @param  	DB     $commonDb An instance of a database 
	*  @param isLargeCusts
	 *	Boolean flag  ( True for large customers )
	 * @param type
	 *	Boolean type ( Large customer for what type for event i.e. 0 for report  )                          
	 * @return array        An array with partitions and their corresponing customer id.
	 */
	public static function getCloudPdbs( DB $commonDb, $isLargeCusts = false, $type = 0 ) {
		//Not consider the large customers for normal customer execution 
		$joinType = 'LEFT JOIN' ;
		$whereCondition = ' AND c.cst_id IS NULL ';
		$joinText = '( SELECT cst_id FROM csi_large_customers WHERE type = '.(int) $type.' ) c  ON a.cst_id = c.cst_id ' ;
		
		if ( $isLargeCusts == true ) {
			//Consider only large customers for large customer execution for same type
			$joinType = 'INNER JOIN' ;
			$whereCondition = '' ;
		}
		
		$sql = "SELECT
					a.cst_id,
					a.partition_id
				FROM
					accounts  a ".$joinType." ".$joinText."
				WHERE
					special_limits = :special_limits AND
					account_expires > UTC_TIMESTAMP()
					".$whereCondition."
				GROUP BY a.cst_id,a.partition_id";
		$bindValues = array(
				':special_limits' => CSI_VERSION_SPECIAL_LIMITS
		);
		
		return $commonDb->execRaw($sql, $bindValues)->fetchAll();		
	}				

	// This is like the old getPartitions, but we join on csi_pdb_info so
	// we can also pull out the version and patch version which we
	// sometimes need
	// The idea is to get all the VPDBs in a given special limits
	// which is equivalent to getting all the individual partitions using
	// this CSI version.
	public static function getVpdbs( DB $commonDb ) {

		return $commonDb->select()
			->columns( array(
				'cst_id'
				,'partition_id'
				,'patch_version'
				, 'csi_version' ) )
			->from( 'accounts' )
				->join( 'csi_pdb_info' )
				->using( array( 'cst_id', 'partition_id' ) )
			->where( array(
				 'special_limits' => CSI_VERSION_SPECIAL_LIMITS
				,'account_expires' => Exp::gt(SqlFn::UTC_TIMESTAMP()) ) )
			->groupBy( array(
				 'cst_id'
				,'partition_id' ) )
			->exec();
	}

	/**
	 * CHeks wether a partition databse is locked in an upgrade state
	 * @return boolean True if the database is locked.
	 */
	public function isLocked() {
		if ( !$this->commonDb instanceof DB ) {
			throw new SfwEx( "Using CLOUD::isLocked requires a DB instance to be set for the CLOUD instance." );
		}
		if ( !( $this->cst_id > 0 ) ) {
			$this->logger->notice( "CLOUD::isLocked is called with a CST_ID value that does not seem to be valid: " . $this->cst_id );
		}
		if ( !( $this->cst_id >= 0 ) ) {
			$this->logger->notice( "CLOUD::isLocked is called with a partitionId value that does not seem to be valid: " . $this->partitionId );
		}
		$rows = $this->commonDb->select()
			->columns( array(
				 "locked"
				,"csi_version"
			) )
			->from( "csi_pdb_info" )
			->where( array(
				 "cst_id" => $this->cst_id
				,"partition_id" => $this->partitionId
			) )
			->limit( 2 )
			->exec();

		if ( count( $rows ) !== 1 ) {
			throw new SfwEx( "An unexpected number of records was found in csi_pdb_info for CST_ID " . $this->cst_id ." and partition ID " . $this->partitionId . "." );
		}

		if ( (int) $rows[ 0 ][ "locked" ] === 0 ) {
			return false;
		}

		if ( empty( $rows[ 0 ][ "csi_version" ] ) || $rows[ 0 ][ "csi_version" ] === "4.0" ) {
			return false;
		}
		return true;
	}

    /**
     * Fetches patch version for current user.
     *
     * @return int Patch version for current user.
     */
    public function getPatchVersion() {
        $return = $this->commonDb->select()
            ->columns(array('patch_version'))
            ->from('csi_pdb_info')
            ->where(array(
                'cst_id' => $this->cst_id,
                'partition_id' => $this->partitionId) )
            ->limit( 1 )
            ->setOption( Select::RETURN_PDO_STATEMENT, true )
            ->exec()
            ->fetch( PDO::FETCH_ASSOC );
        return $return['patch_version'];
    }

	/*
	 * Initialize a link to the private database for the customer id stored and return it.
	 *
	 * @pdo boolean
	 *   The flag is false by default so that the existing code doesn't break while moving
	 *   towards prepared statements. Once we move to prepared statements, we'll remove
	 *   this option.
	 */
	public function initPrivateDatabase( $pdo = false ) {
		if ( $this->cst_id <= 0 ) {
			return false;
		}

		// Create our new database object, used both for ca_(CST_ID).* and vuln_track.*.
		// Note, vuln_track can only be read.

		$privateDatabase = NULL;

		if ( $pdo ) {
			$privateDatabase = new DB(
				$this->get_db_client_host()
				,$this->get_database_name()
				,$this->get_database_username()
				,$this->get_database_password()
				,DB_UTC_TIMEZONE
				,DB_PORT
			);
		} else {
			$privateDatabase = new DATABASE(
				$this->get_db_client_host()
				,$this->get_database_username()
				,$this->get_database_password()
				,DB_UTC_TIMEZONE
			);

			// Switch to the customer's database as primary source of data.
			$privateDatabase->selectDB( $this->get_database_name() );
		}

		$this->logger->notice( "Privilege separation loaded. " );

		return $privateDatabase;
	}
}
