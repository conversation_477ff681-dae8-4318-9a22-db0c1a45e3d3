<?php
/**
 * @file crmPartners.class.php
 * Provides functionality for CRM partner handling.
*/
class crmPartners {
	protected $database = array();

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for fetching data.
	 * @param partner_id
	 *	Integer partner id
	 * @return
	 *	String array cotaining the resulting rows
	*/
	function fetchData( $partner_id ) {
		$result = null;
		$result = $this->database['ca']->getRow("ca.partner_profile", "partner_id = '".(int)$partner_id."'");
		$GLOBALS['debug']->notice( "Partner profile data fetched" );
		return $result;
	}

	/**
	 * Function for updating a profile.
	 * @param partner_id
	 *	Integer partner id
	 * @param values
	 *	Array of column values
	 * @param isEscaped
	 *	Boolean data is escaped or not
	*/
	function updateProfile( $partner_id, $values, $isEscaped = false ) {
		$this->database['ca']->edit( "ca.partner_profile", $values, 1, "partner_id = '".(int)$partner_id."'", $isEscaped );
		$GLOBALS['debug']->notice( "Partner profile data updated" );
	}

	/**
	 * Function for updating the partner password. Any input validation should be done before this stage.
	 * @param partnerId
	 *	Integer partner it
	 * @param password
	 *	String password
	 * @param isEscaped
	 *	Boolean data is escaped or not
	*/
	function updatePassword( $partnerId, $password, $isEscaped = false ) {
		$partnerId = (int)$partnerId;
		if ( $isEscaped == false  ) {
			$password = $this->database['ca']->escapeString( $password );
		}
		$password = $this->database['ca']->makePassword( $password );
		$accountId = $this->database['ca']->getRowValue("ca.partner_profile", "account_id", "partner_id = ".(int)$partnerId );
		$this->database['ca']->edit("ca.accounts", array( "account_password" => $password ), 1, "account_id = '".(int)$accountId."'", $isEscaped );
	}
}
?>