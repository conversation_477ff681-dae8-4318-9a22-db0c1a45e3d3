<?php
/**
 * @file reporting.class.php
 */
class REPORTING extends DATABOUNDGRID {

	const SUCCESS = 0;
	const GENERIC_ERROR = 1;
	const UNEXPECTED_ERROR = -1;

	// The following caps the number of elements for which we will construct a IN (...) addendum to
	// pre-filter the results. This is an optimization enhancement as well as a safety, as we don't
	// want to construct arbitrarily long queries which might get truncated and thus fail.
	const MAX_REASONABLE_LIST_SIZE = 50; // TODO - benchmark tests to determine optimal / reasonable value

	const MAX_MEMORY_USAGE_PERCENTAGE = 80; // meaning if memory usage reaches 80% of max memory assigned to PHP, report generation will fail gracefully.
	
	const REPORTING_CRON_FLAGTYPE = 0;  // Flag to identify type of large customers  

	/**
	 * The types of reporting elements are:
	 *
	 *	1.	Configuration Elements:
	 *	  	----------------------
	 *			- They are used for configuration e.g. Time Schedule, UAL etc.
	 *			- They can be either:
	 *				1.	Global Configuration Elements ( implement reportGlobalConfigurator interface )
	 *				2.	Local Configuration Elements ( implement reportLocalConfigurator interface )
	 *
	 *	2.	Content Generating Elements:
	 *		---------------------------
	 *			- They generate some output to be included in the report.
	 *			- They need to implement the 'reportContentGenerator' interface.
	 *			- Since the type of content to be generated should be configurable by the user,
	 *			  content generating report can also be 'configuration' elements. (which is always the case in our setup)
	 *
	 *	3.	Data Providers:
	 *		--------------
	 *			- The report elements might provide an additional feature i.e. provide some data to be used at the UI.
	 *			- They need to implement the 'reportDataProvider' interface.
	 *			- e.g.	The report element for 'Users and Asset Lists' should provide the list of Users and Asset Lists
	 *					so that the user can select from them.
	 *
	 */

	private $reportElements = array();

	/**
	 * @var Crypt
	 */
	protected $crypt;

	protected static $whitelist = array(
		'allowed' => array(
			'id' => 'enhanced_reporting_schedule.id'
			,'one_time_gen'
			,'report_title'
			,'filename'
			,'end_date'
			,'recurrence_schedule'
			,'recipients'
			,'time_frame'
			,'last_gen_date'
			,'modified_date'
			,'process_status'
			,'configuration' => "configuration"
			,'no_email'
			,'source'
            ,'report_format'
			,'debug_enable'
                        ,'time_elapsed'
                        ,'file_size'
		)
		,'searchable' => array(
			'source'
		)
		,'orderable' => array(
			'report_title'
			,'last_gen_date'
			,'modified_date'
			,'one_time_gen'
			,'end_date'
			,'recurrence_schedule'
			,'time_frame'
			,'recipients'
			,'filename'
			,'process_status'
            ,'report_format'
                        ,'time_elapsed'
                        ,'file_size'
		)
	);

	/**
	 * Requests that are permitted for MSP Users
	 * @msp
	 * @var array
	 */
	protected $permittedRequestsForMspUsers = array(
		'generate_report',
		'prepare_report_download',
		'overview'
	);

	public function __construct() {

		if ( isset( $GLOBALS['database'] ) ) {
			$this->database = $GLOBALS['database'];
			$this->commonDb = DB::getInstanceByName( 'common' );
			$this->privateDb = DB::getInstanceByName( 'private' );
		} else {
			$database = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
			$database->selectDB( DB_NAME_CA );
			$this->database['common'] = $database;
			$this->database['ca'] = $database;
		}
	}

	/**
	 * @param Crypt $crypt
	 */
	public function setCrypt(Crypt $crypt)
	{
		$this->crypt = $crypt;
	}

	/**
	 * @msp
	 * @param string $which The same $which used in handleRequest() methods
	 * @return bool
	 */
	public function isRequestPermittedForMspUsers($which)
	{
		$isPermitted = isset($this->permittedRequestsForMspUsers)
			&& in_array($which, $this->permittedRequestsForMspUsers);

		return $isPermitted;
	}

	function registerReportElement( $key, &$reportElement, $externallyAvailable = false ) {

		// Tracks which entries in reportElements are flagged as having certain config
		// data externally available
		// Note - it is assumed that no non-config element will have a variable
		// named 'externalConfigData'
		if ( $externallyAvailable ) {
			$reportElement->externalConfigData = array();
		}

		$this->reportElements[$key] = $reportElement;
	}

	function getReportElement( $name ) {
		if ( isset( $this->reportElements[$name] ) ) {
			return $this->reportElements[$name];
		} else {
			return false;
		}
	}

	function fetchRecipientData( $accountId, $recipientId ) {
		$whereClause = "account_id = '" . (int) $accountId .
			"' AND contact_method_id = '" . (int) $recipientId . "'";

		return $this->database['ca']->getRow( "contact_method", $whereClause );
	}

	protected function saveAllConfiguration( $accountId, $sourceType, $reportId = 0 ) {
		$response = array( "success" => true, "status" => 0 );

		// @todo: why is the POST array being accessed?? Need to fix.
		if ( $sourceType == "vim" ) {
			if ( isset( $_POST['VIM_MISC_report_title'] ) ) {
				$response["log_info"][] = array($_POST['VIM_MISC_report_title']);
			} else {
				$response["log_info"][] = array("Flexera Custom Report");
			}
		}


		if ( !is_numeric( $reportId ) ) {
			$response['status'] = 2;
			$GLOBALS['debug']->error( "Report Id is incorrect" );
			return $GLOBALS['json']->json_encode( $response );
		}

		$validConfiguration = false; // Check that we selected enough options to generate a non-empty report

		// Get configuration strings from the modules to save in the database. The
		// configuration elements must be processed separately first in case their
		// externally available data is required by other reporting elements.

		$reportElementNames = array_keys( $this->reportElements );
		$numOfReportElements = count( $reportElementNames );

		for ( $i = 0; $i < $numOfReportElements; $i++ ) {

			$element = $this->getReportElement( $reportElementNames[$i] );
			if ( $element && isset($element->externalConfigData) ) {
				// Then this is a config element - call the 'generateExternalConfigData'
				// method to generate the data which will be available externally to other modules
				if ( method_exists( $element, "generateExternalConfigData" ) ) {
 					$element->externalConfigData = $element->generateExternalConfigData();
				}
			}
		}

		$configuration = "";
		for ( $i = 0; $i < $numOfReportElements; $i++ ) {

			$element = $this->getReportElement( $reportElementNames[$i] );
			if ( $element ) {
				if ( method_exists( $element, "generateConfigurationString" ) ) {

					$newConfString = $element->generateConfigurationString();

					// Only add the bar if we are not the first one, and we actually have
					// something here.
					// Note - we NEED to have introduced report options other than just CSI_USERS,
					// so if we don't add something else here, we don't have a valid report
					if ( $newConfString ) {
						if ( $configuration != "" ) {
							$configuration .= "|";
						}
						$validConfiguration = true;
					}

					$configuration .= $newConfString;

					if ( $sourceType != "csi" ) {
						$validConfiguration = true;
					}
				}
			}
		}

		if ( !$validConfiguration || $configuration == "" ) {
			$response['status'] = 1;
			$GLOBALS['debug']->error( "Scheduled report would be empty - cancelling report schedule creations." );
			return $GLOBALS['json']->json_encode( $response );
		}

		// If we want to edit the report configuration, we need to delete the existing
		// configuration and create a new one
		if ( (int) $reportId > 0 ) {

			$configurationId = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'configuration_options_id', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" ) );
			
			//CSIL-9770 Getting and setting the report debug_flag 
			$debugStatus = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'debug_enable', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" ) );
			
			// Delete the configuration row
			$delQuery = "DELETE FROM reporting_configuration_options
						WHERE account_id = '" . (int) $accountId . "'
						AND	id = '" . (int) $configurationId . "'";
			$this->database['ca']->query( $delQuery );

			// Delete the report schedule entry
			$delQuery = "DELETE FROM ca.enhanced_reporting_schedule
						WHERE account_id = '" . (int) $accountId . "'
						AND	id = '" . (int) $reportId . "'";
			$this->database['common']->query( $delQuery );

			// Insert a new row with the account id and report id in the CONFIGURATION table
			$values = array();
			$values["account_id"] = (int) $accountId;
			$values["id"] = (int) $configurationId;
			$this->database['ca']->edit( "reporting_configuration_options", $values, DATABASE::INSERT );

			// set up new values for the REPORTING SCHEDULE table
			$insQuery = "INSERT INTO
							ca.enhanced_reporting_schedule
						SET
							id = '" . (int) $reportId . "'
							,account_id = '" . (int) $accountId . "'
							,configuration_options_id = '" . (int) $configurationId . "'
							,modified_date = UTC_TIMESTAMP()
							,process_status = 0
							,debug_enable = '" . (int) $debugStatus . "'
							,source = '" . $sourceType . "'";
			$this->database['common']->query( $insQuery );

			$activityType = 4011;

		} else { // Then reportId == 0 (default), so this is a NEW REPORT

			// Insert a new row with the account id and save the new report id
			$values["account_id"] = (int) $accountId;
			$configurationId = $this->database['ca']->edit( "reporting_configuration_options", $values, DATABASE::INSERT );
			$insQuery = "INSERT INTO
							ca.enhanced_reporting_schedule
						SET
							account_id = '" . (int) $accountId . "'
							,configuration_options_id = '" . (int) $configurationId . "'
							,modified_date = UTC_TIMESTAMP()
							,process_status = 0
							,source = '" . $sourceType . "'";
			$this->database['common']->query( $insQuery );
			$reportId = $this->database['common']->getInsertId();

			$activityType = 4010;
		}

		for ( $i = 0; $i < $numOfReportElements; $i++ ) {
			$element = $this->getReportElement( $reportElementNames[$i] );
			if ( ( $element !== false ) && method_exists( $element, "saveConfiguration" ) ) {
				$element->saveConfiguration( $accountId, $reportId );
				if ( get_class( $element ) == "csiReMiscContent" && method_exists( $element, "getName" ) ) {
					$reportTitle = $GLOBALS['input']->POST( $element->getName() . "_report_title" );
				}
			}
		}

		// Save the configuration string containing configurations of all the content generating elements
		$saveOptionsStringQuery = "UPDATE
						reporting_configuration_options
					SET
						configuration = '" . $this->database['ca']->escapeString( $configuration ) .  "'
					WHERE
						account_id = '" . (int) $accountId . "'
					AND
						id = '" . (int) $configurationId . "'";

		$this->database['ca']->query( $saveOptionsStringQuery );
		if ( $sourceType == "csi" ) {
			$GLOBALS['activityLog']->logActivity( $accountId, $activityType, ACTIVITYLOG::SUCCESS, "Report: " . $reportTitle . "", null, 18 );
		}

		return $GLOBALS['json']->json_encode( $response );
	}

	protected function deleteConfiguration( $accountId, $reportIdList = 0 ) {
		$response = array( "success" => true, "status" => 0 );
		$reportIdArray = explode( ',', $reportIdList );

		if ( !$reportIdList || 0 == count($reportIdArray) ) {
			$response['error'] = 1;
			$GLOBALS['debug']->error( "Error - incorrect input for report schedule..." );
			return $GLOBALS['json']->json_encode( $response );
		}

		foreach ( $reportIdArray as $thisReportId ) {

			$configurationId = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'configuration_options_id', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $thisReportId . "'" ) );
			$reportTitle = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'report_title', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $thisReportId . "'" ) );

			if ( $thisReportId <= 0 || !$configurationId || $configurationId <= 0 ) {
				$response['error'] = 1;
				$response['success'] = false;
				$warningString = "Unable to delete selected configuration" .
					( 1 < count($reportIdArray) ? 's' : '' );
				$GLOBALS['debug']->error( $warningString );
				return $GLOBALS['json']->json_encode( $response );
			}

			// Delete the configuration row
			$delQuery = "DELETE FROM reporting_configuration_options
					WHERE account_id = '" . (int) $accountId . "'
					AND	id = '" . (int) $configurationId . "'";
			$this->database['ca']->query( $delQuery );

			// Delete the report schedule entry
			$delQuery = "DELETE FROM ca.enhanced_reporting_schedule
					WHERE account_id = '" . (int) $accountId . "'
					AND	id = '" . (int) $thisReportId . "'";
			$this->database['common']->query( $delQuery );

			$response['log_info'][] = array( $reportTitle );
		}

		$response['error'] = 0;
		$GLOBALS['debug']->notice( "Report configuration(s) deleted" );
		return $GLOBALS['json']->json_encode( $response );
	}

/**
 * Generates the type of report called for by the scheduled-generation module, and sends it out to the supplied
 * email list.  If list is not specified, send report to the default list set for this account.
 *
 * @param accountID
 *   INT - taken from the DB Table and corresponding to a specific user and account.
 *
 * @param reportID
 *   INT - Taken from the DB Table. This is the ID of the DB row (primary key of table)
 *
*/
	const NO_REPORT_ID = 1;
	const NO_CONFIGURATION_ID = 2;
	const CURRENTLY_BUILDING = 3;

	private function scheduleAsapReportGeneration( $accountId, $reportId ) {
		$response = array( "error" => self::UNEXPECTED_ERROR );
		$accountId = (int) $accountId;
		$reportId = (int) $reportId;

		if ( !$reportId || $reportId <= 0) {
			$response['error'] = self::NO_REPORT_ID;
			$GLOBALS['debug']->error( "Reporting - Error - incorrect reportId for generating report ($reportId)" );
			return $response;
		}

		if ( !$accountId || $accountId <= 0) {
			$GLOBALS['debug']->error( "Reporting - Error - incorrect accountId for generating report ($accountId)" );
			return $response;
		}

		// check if configuration_options_id exists
		$configurationId = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'configuration_options_id', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" ) );

		if ( !$configurationId || $configurationId <= 0 ) {
			$response['error'] = self::NO_CONFIGURATION_ID;
			$GLOBALS['debug']->error( "Reporting - Error - configuration ID not found" );
			return $response;
		}

		$GLOBALS['debug']->notice( "On demand cron has been flagged for ASAP execution with the following configuration " . $configurationId );

		$this->database['common']->query( "
			UPDATE
				ca.enhanced_reporting_schedule
			SET
				generate_asap = 1,
				process_status = 0,
				file_size = 0
			WHERE
					account_id = " . (int) $accountId . "
				AND id = " . (int) $reportId . "
		", true );

		$response['error'] = self::CURRENTLY_BUILDING;
		return $response;
	}

	// This function returns data specific to the CSI as it relies on vpdb-stuff
	// todo: need to make this function local to the projects so that the VIM and the CSI
	// both will have their own version of the function.
	// At the moment the VIM can not use this function and the issue needs to be fixed.
	protected function getReportsOverview( array $modifiers, $accountId, $reportTitle, $reportType ) {
		$columns = !empty( $modifiers['columns'] ) ? $modifiers['columns'] : self::$whitelist['allowed'];
		$where = $modifiers['where'];
		$order = $modifiers['order'];
		$limit = $modifiers['limit'];

		unset( $columns[ "configuration" ] );
		array_push( $columns, "configuration_options_id" );
		$where[ "account_id" ] = $accountId;

                if(!empty($reportTitle)){
                    $where["report_title"] = Exp::LIKE('%'.$reportTitle.'%');
                }

                if($reportType != ''){
                    if($reportType == 0 || $reportType == 1){
                        $where["one_time_gen"] = $reportType;
                    }

                    if($reportType == 3 || $reportType == 4){
                        $where["report_format"] = $reportType;
                    }
                }

		$statement = $this->commonDb->select()
			->columns( $columns )
			->from( "enhanced_reporting_schedule" )
			->where( $where )
			->orderBy( $order )
			->limit( $limit['start'], $limit['limit'] );

		$reports = $statement->exec();

		$count = $statement->rowCountIgnoreLimit();

		foreach ( $reports as &$report ) {
			$configurations = $this->privateDb->select()
				->columns( array(
					 "configuration"
				) )
				->from( "reporting_configuration_options" )
				->where( array(
					 "id" => $report[ "configuration_options_id" ]
				) )
				->limit( 2 )
				->exec();
			if ( count( $configurations ) !== 1 ) {
				$GLOBALS[ "debug" ]->error( "Un unexpected number of configurations was found for account " . $accountId . " and configuration id " . $report[ "id" ] );
				continue;
			}
			$report[ "configuration" ] = $configurations[ 0 ][ "configuration" ];

			if ( $report[ "source" ] === "csi" ) {
				$recipients = $this->privateDb->select()
					->columns( array(
						"recipient_account_id"
					) )
					->from( "csi_recipients" )
					->where( array(
						 "instance_id" => $report[ "id" ]
					) )
					->exec();

				if ( count( $recipients ) > 0 ) {
					$report[ "configuration" ] .= "|" . csiReRecipients::NAME . ":";
					$addedFlag = false;
					foreach ( $recipients as $recipient ) {
						if ( $addedFlag ) {
							$report[ "configuration" ] .= ",";
						}
						$addedFlag = true;
						$report[ "configuration" ] .= $recipient[ "recipient_account_id" ];
					}
				}
			}
		}

		$response[ "data" ] = array(
			 "rows" => $reports
			,"total" => $count
		);
		$response[ "success" ] = true;
		return $response;
	}


	// Function to calculate the date buckets for a bucketArray of timestamps.
	// Input is the bucket array, and the date we need the bucket number for.
	// Input can be either a timestamp or a date - we specify when we call it.
	function calculateDateBucket( $bucketArray, $inputDate, $format = 'timestamp' ) {

		if ( 'date' == $format ) {
			list($y, $m, $d) = explode( "-", $inputDate );
			$inputStamp = mktime(0, 0, 0, $m, $d, $y);
		} else {
			// assume then that it is a timestamp (also the default)
			$inputStamp = $inputDate;
		}

		$bucket = 0;
		$size = count($bucketArray);
		for ( $i = 0; $i < $size; $i++ ) {
			if ( $inputStamp >= $bucketArray[$i] ) {
				$bucket = $i;
			} else {
				break;
			}
		}
		return $bucket;
	}

	function prepareReportDownload( $accountId, $reportId = 0 ) {

		$response = array( "error" => -1, "token" => "" );

		if ( !is_numeric( $reportId ) || $reportId <= 0 ) {
			$response['error'] = 1;
			$GLOBALS['debug']->error( "Report Id is incorrect" );
			return $GLOBALS['json']->json_encode( $response );
		}

		if ( !defined( 'REPORTS_INTERNAL_URL' ) ) {
			$response['error'] = 2;
			$GLOBALS['debug']->critical( "REPORTS_INTERNAL_URL is not defined" );
			return $GLOBALS['json']->json_encode( $response );
		}

        $report_format = $this->database['common']->getRowValue( "enhanced_reporting_schedule", "report_format", "account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" );
        $ext = ( $report_format == 3 ) ? 'pdf' : 'zip'; // this should be done differently when we have more then just the PDF and CSV files. CSV files are zipped into one file.
        $largeCst = UTIL::isLargeCstReport((int) $GLOBALS['cst_id']);
        if ($largeCst) {
            $ext = 'zip';
        }

        $file = (int) $accountId . "-" . (int) $reportId . ".$ext";

		$fh = @fopen( REPORTS_INTERNAL_URL . $file, 'r' );
		if ( $fh === false ) {
			$response['error'] = 3;
			$GLOBALS['debug']->critical( "File does not exists {file}", array( 'file' => REPORTS_INTERNAL_URL . $file ) );
			return $GLOBALS['json']->json_encode( $response );
		}
		fclose( $fh );

		$token = $GLOBALS['prng']->generateRandomID(32);
		$reportName = $this->database['common']->getRowValue( "enhanced_reporting_schedule", "filename", "account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" );
		$reportGenDate = $this->database['common']->getRowValue( "enhanced_reporting_schedule", "last_gen_date", "account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" );

		$values = array(
						"token" => $token
						,"file" => $file
						,"filename" => $reportName
						,"last_gen_date" => $reportGenDate
						);
		$this->database['common']->edit( "nsi_report_tokens", $values, DATABASE::INSERT );

		$response['error'] = 0;
		$response['token'] = $token;
		return $GLOBALS['json']->json_encode( $response );
	}

	/*
	 *	The download report is called from the main index file and doesn't need a GUID.
	 *	If a correct token is passed, the pdf report is returned. Therefore, in case of errors
	 *	we do not return any error handling messages.
	 *
	 *	todo: write more about the execution flow
	 */
	public function downloadReport( $token = "") {

		if ( !$token ) {
			$GLOBALS['debug']->error( "downloadReport() - token is empty" );
			return; // No response since the user is not authenticated
		}

		$columns = array( "file", "filename, last_gen_date" );
		$fileData = $this->database['common']->getRowValues( "nsi_report_tokens", $columns, "token = '" . $this->database['common']->escapeString( $token ) . "' AND used = 0" );

		$file = $fileData['file'];
        // get the file extension so we can send the file in the right format.
        $ext = pathinfo($file, PATHINFO_EXTENSION);
		$filename =  "report.$ext"; // default name
		$generationDate = $fileData['last_gen_date'];
		if ( isset( $fileData['filename'] ) && $fileData['filename'] ) {

			// only use the stored filename if it has a valid format
			if ( preg_match( "/^[0-9a-z-_]+.". $ext ."$/i", $fileData['filename'] ) ) {
				$filename = $fileData['filename'];
			} else {
				$GLOBALS['debug']->error( "downloadReport() - filename format is incorrect, using standard name i.e. report.$ext" );
			}
		}
		// prepare filename the way we do when emailing reports - i.e., attach generation timestamp
		$filename = $this->getFileName( $generationDate, $filename, $ext );

		// sanitize file
		$matches = array();
		preg_match( "/^(?P<account_id>\d+)-(?P<report_id>\d+).". $ext ."$/", $file, $matches );

		if ( !isset( $matches['account_id'] ) || !$matches['account_id'] || !isset( $matches['report_id'] ) || !$matches['report_id'] ) {
			$GLOBALS['debug']->error( "downloadReport() - file format is incorrect, it should be accountId-reportId.$ext" );
			return;  // No response since the user is not authenticated
		}

		$filePath = REPORTS_INTERNAL_URL . $file;

		$fh = @fopen( $filePath, 'r' );
		if ( $fh === false ) {
			$GLOBALS['debug']->error( "downloadReport() - file does not exist" );
			return;  // No response since the user is not authenticated
		}
		fclose( $fh );

		// Mark the token as used
		$values = array( "used" => 1 );
		$this->database['common']->edit( "nsi_report_tokens", $values, DATABASE::UPDATE, "token = '" . $this->database['common']->escapeString( $token ) . "'" );

		header("Content-disposition: attachment; filename=" . $filename . ";");
		header("Content-type: application/$ext");
		readfile( $filePath );

		$GLOBALS['debug']->error( "downloadReport() - success" );
		return;
	}

	/**
	 * Get the configuration parameters required for a given reporting module
	 *
	 * @param parsedOptions
	 *   Array - The array of parsed options for the report schedule
	 * @param reportingObject
	 *   Object - This is one of our reporting elements
	 *
	 * @return
	 *   Array - The array we need for the $requiredConfigurationsParameters variable used in various places
	 *
	 */
	function parseConfigurationData( $parsedOptions, $reportingObject ) {
		// Certain modules have parameters that need to be fetched from the main configuration string.
		// Pull those specific ones out from the parsed options.
		$requiredConfigurationsParameters = array();
		if ( method_exists($reportingObject, 'getConfigurationRequirements') ) {
			$configurationRequirements = $reportingObject->getConfigurationRequirements();
			if ( 0 < count($configurationRequirements) ){
				foreach ( $configurationRequirements as $confKey ) {
					if ( isset($parsedOptions[$confKey]) ) {
						$requiredConfigurationsParameters[$confKey] = $parsedOptions[$confKey];
					}
				}
			}
		}
		return $requiredConfigurationsParameters;
	}


	/*
	 * Helper function to populate a lookup table of vendor data. Used in various reporting elements.
	 *
	 * Note - make use of our method of avoiding huge IN clauses in queries
	 *
	 * @param vendorsSeen
	 *   Array - array of vendor ids seen.  Each key in array is the vendor_id, and the value is
	 *           simply 1 to show existence.  Value will be replaced with the vendor name.
	 *
	 * @return Array - copy the input array and modify it as needed before returning it
	 *
	 */
	function populateVendorLookupTable( $vendorsSeenInput ) {

		$vendorsSeen = array();

		if ( !count($vendorsSeenInput) ) {
			return $vendorsSeen;
		}

		$vendorsSeen = $vendorsSeenInput;

		$query = "SELECT vendor_id,vendor_name
				FROM
					vuln_track.vendor
				WHERE
					vendor_id > 0  AND vendor_id IN (";
		$offset = 0;
		$whileMustGoOn = true;
		$vendorSeenKeys = array_keys($vendorsSeen);
		$maximum = self::MAX_REASONABLE_LIST_SIZE;

		while($whileMustGoOn){
			$currentIds = array_slice($vendorSeenKeys,$offset,$maximum);
			$nrOfCurrentIds = count($currentIds);
			$whileMustGoOn = ($maximum == $nrOfCurrentIds);

			//CSIL-10725 Fixed Kibana error
			$ins = ( $nrOfCurrentIds > 0 ) ? implode (",", $currentIds) : 0 ;
			if(!Query::isSafe($ins,array(Query::INC_COMMA))){
				return $vendorsSeen;
			}
			$currentQuery = $query . $ins . ')';

			$resultRows = $this->privateDb->execRaw($currentQuery)->fetchAll();
			if (is_array($resultRows)) {
				foreach ($resultRows as $thisRow) {
					$vendorId = $thisRow['vendor_id'];
					if (isset($vendorsSeen[$vendorId])) {
						$vendorsSeen[$vendorId] = $thisRow['vendor_name'];;
					}
				}
			}
			$offset += $nrOfCurrentIds;
		}
		return $vendorsSeen;
	}

	/*
	 * Helper function to populate a lookup table of vulnerability data. Used in various reporting elements.
	 *
	 * Note - make use of our method of avoiding huge IN clauses in queries
	 *
	 * @param vulnsSeenInput
	 *   Array - array of vuln ids seen.  Each key in array is the vuln_id, and the value is
	 *           simply 1 to show existence.
	 *           Array will be modified with 2 fields:
	 *                                          - 'solutionText' (string)
	 *                                          - 'criticality'  (int)
	 *
	 * @return Array - copy the input array and modify it as needed before returning it
	 *
	 */
	function populateVulnDataLookupTable( $vulnsSeenInput ) {

		$vulnsSeen = array();

		// For vulns we will get the solution text, then criticallity, which we must do separately,
		// as there is not an injective correspondence between the solution text and all vulns
		if ( !count($vulnsSeenInput) ) {
			return $vulnsSeen;
		}

		$vulnsSeen = $vulnsSeenInput;

		$queryText = "SELECT
					text_text
					,vuln_id
				FROM
					vuln_track.text
				WHERE
					vuln_id > 0
					AND text_type_id = 2  AND vuln_id IN (";

		// get the vuln criticality data
		$queryCrit = "SELECT
					vuln_critical_boolean
					,vuln_id
				FROM
					vuln_track.vuln
				WHERE
					vuln_id > 0  AND vuln_id IN (";

		$offset = 0;
		$whileMustGoOn = true;
		$vulnSeenKeys = array_keys($vulnsSeen);
		$maximum = self::MAX_REASONABLE_LIST_SIZE;

		while ($whileMustGoOn) {
			$currentIds = array_slice($vulnSeenKeys,$offset,$maximum);
			$nrOfCurrentIds = count($currentIds);
			$whileMustGoOn = ($maximum == $nrOfCurrentIds);
			
			//CSIL-10725 Fixed Kibana error
			$ins = ( $nrOfCurrentIds > 0 ) ? implode (",", $currentIds) : 0 ;
			if(!Query::isSafe($ins,array(Query::INC_COMMA))){
				return $vulnsSeen;
			}

			$currentQueryText = $queryText . $ins . ')';

			$resultRows = $this->privateDb->execRaw($currentQueryText)->fetchAll();
			if (is_array($resultRows)) {
				foreach ($resultRows as $thisRow) {
					$vulnId = $thisRow['vuln_id'];
					if (isset($vulnsSeen[$vulnId])) {

						// Decrypt the Advisory text if required
						if (EDITION == SERVER_EDITION && !empty($thisRow['text_text'])) {
							$thisRow['text_text'] = $this->crypt->decrypt($thisRow['text_text']);
						}

						$solutionText = htmlspecialchars($thisRow['text_text'], ENT_COMPAT, 'ISO-8859-1');
						// Format Solution: Replace Line Feeds with XHTML break tags and remove carriage returns
						$solutionText = str_replace(array("\n", "\r"), array('<br />', ''), $solutionText);
						$vulnsSeen[$vulnId] = array('solutionText' => $solutionText);
					}
				}
			}

			$currentQueryCrit = $queryCrit . $ins . ')';

			$resultRows = $this->privateDb->execRaw($currentQueryCrit)->fetchAll();
			if (is_array($resultRows)) {
				foreach ($resultRows as $thisRow) {
					$vulnId = $thisRow['vuln_id'];
					if (isset($vulnsSeen[$vulnId])) {
						$criticality = (int)$thisRow['vuln_critical_boolean'];
						// this value is (int) 1 when not set in the first foreach loop
						if (!is_array($vulnsSeen[$vulnId])) {
							$vulnsSeen[$vulnId] = array();
						}
						$vulnsSeen[$vulnId]['criticality'] = $criticality;
					}
				}
			}
			$offset += $nrOfCurrentIds;
		}

		return $vulnsSeen;
	}

	/**
	 * Generates a file name for the report if one was not provided specifically by a user.
	 *
	 * @param genDate
	 *   String - This is a timestamp generated by the cron job for when it is actually generating the report.
	 *
	 * @param fileName
	 *   String - One can optionally pass in a filename in which case we use it and just append our generation time stamp.
	 *
	 */
	public function getFileName( $genDate, $fileName = '', $ext = 'pdf' ) {

		$nameToReturn = 'secuniaReport';

		// Format the passed in genDate to have underscores instead of spaces, but first validate the value passed in
		$dateFormatted = 'unknownDate'; // default in the unlikely event we have a bad date
		if ( UTIL::validateDateTime( $genDate ) ) {
			$dateFormatted = preg_replace('/ /', '_', $genDate);
			$dateFormatted = preg_replace('/:/', '', $dateFormatted);
		}

		if ( $fileName ) {
			// we have something like filename.pdf - modify it to be filename_genDate.pdf
			$nameToReturn = preg_replace('/.'.$ext.'/', "_" . $dateFormatted . ".$ext", $fileName);
		} else {
			$nameToReturn .= "_generated_" . $dateFormatted . ".$ext";
		}

		return $nameToReturn;
	}

	// The handleRequest function is now used by the CSI only.
	// The VIM extends this class and provides its own handleRequest function (see report.class.php in the VIM php/local folder)
	public function handleRequest( $which ) {
		switch ( $which ) {
		case "save":
			echo $this->saveAllConfiguration( $GLOBALS['account_id']
											  ,$GLOBALS['input']->MPOST('source_type') );
			break;
		case "edit":
			echo $this->saveAllConfiguration( $GLOBALS['account_id']
											  ,$GLOBALS['input']->MPOST('source_type')
											  ,$GLOBALS['input']->MPOST('report_id') );
			break;
		case "delete":
			echo $this->deleteConfiguration( $GLOBALS['account_id'], $GLOBALS['input']->MPOST('delete_id_list') );
			break;
		case "generate_report":
			echo $GLOBALS['json']->json_encode( $this->scheduleAsapReportGeneration( $GLOBALS['account_id'], $GLOBALS['input']->MPOST('generate_id') ) );
			break;
		case "overview":
			$userModifiers = csiGrid::getUserModifiers( self::$whitelist );
			$response = $this->getReportsOverview( $userModifiers, $GLOBALS['account_id'], $GLOBALS['input']->MGET('report_title'), $GLOBALS['input']->MGET('report_type') );
			break;
		case "prepare_report_download":
			echo $this->prepareReportDownload( $GLOBALS['account_id'], $GLOBALS['input']->MGET('report_id') );
			break;
		default:
			$reportElement = $this->getReportElement( $which );
			if ( $reportElement !== false ) {
				$reportElement->handleRequest( $GLOBALS['input']->MGET('type') );
			}
			break;
		}

		// code taken from index.php. Since this class cannot extend action and this is for CSI only, this is hackish way of doing it
		if ( isset( $response ) ) {
			header( 'Content-type: application/json' );
			if ( is_object( $response ) ) {
				$response = $response->getResponseArray();
			}
			if ( is_array( $response ) && count( $response ) > 0 ) {
				echo JSON::encodeArray( $response );
			}
		}
	}
	
	/* CSIL-9822 method to build SQL for products secure status
	 * 
	 * @return String
 	 */

	public static function buildSecureStatusQuery ($optionsSelected, $prefix = 'cds', $className = 'csiReHostLevelData') {
	
		
		if ( isset($optionsSelected[$className::OVERALL_SUMMARY_STATS_TOKEN]) ) {
			return '';
		}
		if ( isset($optionsSelected[$className::INSECURE_STATS_TOKEN]) &&  isset($optionsSelected[$className::EOL_STATS_TOKEN]) && isset($optionsSelected[$className::PATCHED_STATS_TOKEN]) ) {
			return '';
		}
		if ( isset($optionsSelected[$className::INSECURE_STATS_TOKEN]) ) {
			$sqlExtraxCond[] = "0";
		}
		if ( isset($optionsSelected[$className::EOL_STATS_TOKEN]) ) {
			$sqlExtraxCond[] = "-1";
		}
		if ( isset($optionsSelected[$className::PATCHED_STATS_TOKEN]) ) {
			$sqlExtraxCond[] = "1,2,3,4";
		}
		
		$getSecureStatus = implode(",", $sqlExtraxCond) ;
		
		return " AND secure_status IN (".$getSecureStatus.") " ;
		
	}
	
	/* CSIL-10721 Update the report generation starting datetime 
	 *
	 * @return NONE
	 */
	
	public static function updateReportGenDateTime ( $accountId, $reportId, $dbConn ) {
			
		$updateQuery = "UPDATE enhanced_reporting_schedule
					SET generation_started_at = UTC_TIMESTAMP()
					WHERE account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'";
		$GLOBALS['db']['common']->execRaw( $updateQuery );
	}
	
	/* CSIL-10721 Update the report generation starting datetime
	 *
	 * @return NONE
	 */
	
	public static function updateReportelapsedTime ( $accountId, $reportId, $dbConn, $elapsedTime ) {
		$updateQuery = "UPDATE enhanced_reporting_schedule
					SET time_elapsed = '". $elapsedTime. "'
					WHERE account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'";
		$GLOBALS['db']['common']->execRaw( $updateQuery );
	}
	
}
