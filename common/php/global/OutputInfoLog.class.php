<?php

/**
 * A PSR-3 implementation of a Logger that logs to Syslogger and outputs if log level is INFO
 */
class OutputInfoLog extends Syslog {
	/**
	 * Logs with an arbitrary level and outputs if the level is INFO
	 *
	 * @param mixed  $level
	 * @param string $message
	 * @param array  $values
	 *
	 * @return null
	 */
	public function log( $level, $message, array $values = array() ) {
		if ( empty( $message ) ) {
			return;
		}

		parent::log( $level, $message, $values );

		if ( $level === Psr\Log\LogLevel::INFO || $level === Psr\Log\LogLevel::ERROR ) {
			echo trim( $this->logMessagePrefix . str_repeat( '  ', $this->depth ) . ' ' . ( isset( $values ) ? $this->format( $message, $values ) : $message ) ) . "\n";
		}
	}
}