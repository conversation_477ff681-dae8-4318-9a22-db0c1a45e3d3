<?php
/**
 * @file websiteBanner.class.php
 * Provides website banner control, both for Website and CRM.
 *
 * Configuration options:
 *
 * Optional: FILE_RENDER_PATH: path to the file rendering script
 *
*/
class websiteBanner {

	/**
	 * Fetch existing banner data
	 * @return
	 *	Array containing banner data: url, alt, file, file_type
	*/
	function fetchData() {
		$result = null;
		$result = $GLOBALS['database']->getRow("website.banner");
		$GLOBALS['debug']->notice( "Website banner data fetched" );
		return $result;
	}

	/**
	 * Function for fetching page items data
	 * @param path
	 *	String path of the fetched item
	 * @return
	 *	Resource containing the row(s) for Static Page Items
	*/
	function fetchGridData( $path = '' ) {
		$result = $GLOBALS['database']->getRows( "website.banner",( ( $path != "" ) ? "file_name = '".$path."'" : "" ), "file_name ASC, link ASC, alt ASC", "" );

		if ( count( $result ) == 0 ) {
			$GLOBALS['debug']->notice( "Cannot fetch website banner items data" );
			return false;
		} else {
			$GLOBALS['debug']->notice( "Website banner items data fetched" );
			return $result;
		}
	}

	/**
	 * Render banner, if there is an image defined
	 * @return
	 *	String containing banner image
	*/
	function renderBanner() {
		$result = $this->fetchData();
		$output = "";
		if ( $result['file_name'] != "" ) {
			$output = "<div id=\"divTopbanner\">
				<img onclick='document.location=\"".UTIL::htmlspecialchars( $result['link'] )."\"'
				style=\"".( ( $result['link'] != "" ) ? "cursor: pointer;" : "" )."\" src=\"".FILE_RENDER_PATH.UTIL::htmlspecialchars( $result['file_name'] )."\"
				width=\"952\" height=\"121\"
				alt=\"".UTIL::htmlspecialchars( $result['alt'] )."\">
			</div>";
		}
		return $output;
	}
}