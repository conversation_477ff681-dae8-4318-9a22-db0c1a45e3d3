
<?php
/**
 * @file advisory.class.php
*/

/**
 * Provides advisory object.
*/
class ADVISORY {

	/*
	 * Languages supported
	 */
	const LANG_ENGLISH = 1;
	const LANG_DANISH = 2;
	const LANG_GERMAN = 5;

	/*
	 * Advisory Content
	 */
	const DESCRIPTION = 1;
	const SOLUTION = 2;
	const CREDITS = 3;
	const CHANGELOG = 4;
	const ORIGINAL_ADVISORY = 5;
	const OTHER_REFERENCES = 6;
	const REASON = 7;
	const EXTENDED_DESCRIPTION = 8;
	const EXTENDED_SOLUTION = 9;

	/*
	 * Advisory detail levels
	 */
	const TYPE_SIMPLE = 0;
	const TYPE_EXTENDED = 1;

	/*
	 * Advisory Criticality
	 */
	const CRIT_EXTREME = 1;
	const CRIT_HIGH = 2;
	const CRIT_MODERATE = 3;
	const CRIT_LOW = 4;
	const CRIT_NOT = 5;

	protected $db;

	function __construct() {

		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA ); // @deprecated
		$this->db = new DB( DB_HOST_CA, "ca", DB_USER_CA, DB_PASS_CA, false, DB_PORT  );

		Cpe::init( $this->db );
	}

	/**
	 * Get advisory status.
	 * @param vulnId
	 *	Integer vulnerability id
	 * @return
	 *	Integer vulnerability status
	*/
	function fetchRawDetails( $vulnId, $lang_id = self::LANG_ENGLISH ) {

		// Get all the regular data associated with a vulnId
		$row = $this->database['ca']->getRow( "vuln_track.vuln", "vuln_id = '".(int)$vulnId."'" );

		// Now deal with getting the title, in case we are asking for a different language
		$languageSpecificTitle = "";
		$languageSpecificTitle = $this->database['ca']->getRowValue("vuln_track.vuln_reference", "vuln_title", "m_vuln_id = '" . (int)$vulnId . "' && lang_id = '" . (int)$lang_id . "'" );
		if ( "" != $languageSpecificTitle ) {
			$row['vuln_title'] = $languageSpecificTitle;
		}

		// Add the calculated cvss date here too, including overall score
		if ( $row['vuln_cvss_vector'] ) {
			$scoreDataArray = $this->CVSS2Calc( $row['vuln_cvss_vector'] );
			$row['cvss_data'] = $scoreDataArray;
		}

		//CVSS3 data
		$row['vuln_cvss3_score'] = null;
		$row['vuln_cvss3_vector'] = null;
		$vuln_cvss3_vector = null;
		
	    $cvss3A = $this->database['ca']->getRowValues( "vuln_track.cvss3", array('vuln_id', 'cvss3_vector', 'cvss3_score'), "vuln_id = '".(int)$vulnId."'" );
	    if (!empty($cvss3A)) {
	        $row['vuln_cvss3_score'] = $cvss3A['cvss3_score'];
	        $vuln_cvss3_vector = $cvss3A['cvss3_vector'];
	        $row['vuln_cvss3_vector'] = $cvss3A['cvss3_vector'];

	        if ($vuln_cvss3_vector) {
	            $scoreDataArray = $this->CVSS3Calc( $vuln_cvss3_vector );
	            $row['cvss3_data'] = $scoreDataArray;
	        }
	        //Get vector enclosed in () and format.
	        preg_match ('/^\((.*?)\)$/i', $vuln_cvss3_vector, $matches);
	        if (isset($matches[1]) && !empty($matches[1])) {
	            $row['vuln_cvss3_vector'] = 'CVSS:3.1/' . $matches[1];
	        }
	    }
	    
	    //CVSS4 data
	    $row['vuln_cvss4_score'] = null;
	    $row['vuln_cvss4_vector'] = null;
	    $vuln_cvss4_vector = null;
	    $cvss4Cal = new cvss4calculator();

	    $cvss4A = $this->database['ca']->getRowValues( "vuln_track.cvss4", array('vuln_id', 'cvss4_vector', 'cvss4_score'), "vuln_id = '".(int)$vulnId."'" );
	    if (!empty($cvss4A)) {
	        $row['vuln_cvss4_score'] = $cvss4A['cvss4_score'];
	        $vuln_cvss4_vector = $cvss4A['cvss4_vector'];
	        $row['vuln_cvss4_vector'] = $cvss4A['cvss4_vector'];

	        preg_match ('/^\((.*?)\)$/i', $vuln_cvss4_vector, $matches);
	        if (isset($matches[1]) && !empty($matches[1])) {
	            $vuln_cvss4_vector = $row['vuln_cvss4_vector'] = 'CVSS:4.0/' . $matches[1];
	        }

	        if ($vuln_cvss4_vector) {
	            $scoreDataArray = $cvss4Cal->fCVSS4Calc( $vuln_cvss4_vector );
	            $row['cvss4_data'] = $scoreDataArray;
	        }
	    }
	    
		return $row;
	}

	/**
	 * Function for fetching affected vendors
	 * @param vulnId
	 *	Mixed integer vulnerability id or Array of vulnerability id's
	 * @return
	 *	Array of vendor ids, vendor names and vuln ids.
	*/
	public function fetchVendors( $vulnId ) {
		$vendors = array();

		$columns = array( 'vendor.vendor_id', 'vendor.vendor_name', 'vuln_id');

		// CASE : Operating System
		$rows1 = $this->db->select()
			->columns( $columns )
			->from( 'vuln_track.os_soft_rel' )
			->join( 'vuln_track.os_soft' )
			->on( array( 'os_soft_rel.os_id' => 'os_soft.os_soft_id' ) )
			->join( 'vuln_track.vendor' )
			->using( array( 'vendor_id' ) )
			->where( array( 'vuln_id' => $vulnId
							,'os_soft_rel.soft_id' => array( 0, Exp::IS_NULL() ) ) )
			->exec();

		// CASE : Software
		$rows2 = $this->db->select()
			->columns( $columns )
			->from( 'vuln_track.os_soft_rel' )
			->join( 'vuln_track.os_soft' )
			->on( array( 'os_soft_rel.soft_id' => 'os_soft.os_soft_id' ) )
			->join( 'vuln_track.vendor' )
			->using( array( 'vendor_id' ) )
			->where( array( 'vuln_id' => $vulnId ) )
			->exec();

		// Merge the data into a single array and remove duplicates
		$rows = array_merge( $rows1, $rows2 );

		$vendorIds = array(); // Keep track of unique vendors

		// Remove duplicates
		foreach ( $rows as $key => $row ) {
			if ( in_array( $row['vuln_id'].$row['vendor_id'], $vendorIds, true ) ) {
				unset( $vendors[ $key ] );
			} else {
				$vendorIds[] = $row['vuln_id'].$row['vendor_id'];
				$vendors[] = $row;
			}
		}

		return $vendors;
	}

	/**
	 * Fetch CVE references. Not part of the CVE object, since this is supposed to fetch information
	 * about the Advisory CVE, not a particular CVE.
	 *
	 * dsalatti: made public since it is called from csiAdvisory
	 *
	 * @param vulnId
	 *	Integer vulnerability id
	 *
	 * @return
	 *	Array - packed with the appropriate CVE Data
	*/
	public function fetchCVE( $vulnId ) {

		$output = array();

		// First get the space separated cve list from the DB, and convert it into a comma separated string
		$cveList = $this->database['ca']->getRowValue( "vuln_track.ref", "ref_value", "length(ref_value) > 0 AND ref_type_id = 1 AND vuln_id = '" . (int) $vulnId . "'" );
		if ( !$cveList ) {
			return $output;
		}

		// Else...
		$verifiedCveArray = array();

		// Even though data comes from vuln_track, i.e., not user input, we escape data in case of bad data, entries
		// containing a ' character, etc. which could mess up our query below
		$cveList = $this->database['ca']->escapeString( $cveList );

		$cveList = trim( $cveList ); // in case of extra spaces - might not be required

		$cveArray = explode( ' ', $cveList );
		// Go through the array and make sure every entry is a valid CVE identifier.  They must start
		// with either 'CVE' or the old version 'CAN', and have a '-' then a 4-digit year part and '-' and another minimum of 4 digits and more
		foreach ( $cveArray as $potentialCve ) {
			$extractedCVE = CVE::extractCVE($potentialCve);
			if($extractedCVE!=null){
				$verifiedCveArray[] = $extractedCVE[1].'-'.$extractedCVE[2]; // just the XXXX-YYYY part
			}
		}
		$cveList = implode( "','", $verifiedCveArray ); // need list to be in form:  a','b',...,'n

		$query = "SELECT reference
					,cvss_vector
				FROM
					vuln_track.refsys_entries
				WHERE
					source = 'CVE'
					AND reference IN ('" . $cveList . "')";  // add front and end quotes

		$result = $this->database['ca']->queryGetRows( $query );

		for ( $i = 0; $i < count( $verifiedCveArray ); $i++ ) {
			// For this CVE, we don't know if it had a matching entry in the refsys_entries table.
			$thisCveReference = $verifiedCveArray[$i];

			// Search for it in our query results - if it exists, add the vector/score data.
			for ( $j = 0; $j < count( $result ); $j++ ) {
				if ( $result[$j]['reference'] == $thisCveReference ) {
					$cvssVector = $result[$j]['cvss_vector'];
					if ( $cvssVector ) {
						$scoreDataArray = $this->CVSS2Calc( $cvssVector );
						$output[$i] = $scoreDataArray;
						$output[$i]['cvss_vector'] = $cvssVector;
						// Check for beta!
						if ( BETA == true ) {
							unset( $output[$i]['cvss_vector'] );
						}
					}
					// delete this one to make the search shorter next time
					array_splice( $result, $j, 1 );
					break;
				}
			}
			$output[$i]['cve_reference'] = "CVE-" . $thisCveReference;
		}

		return $output;
	}

	/**
	 * Fetch various infomation about the current vuln. See the constant values.
	 *
	 * VIM Only
	 *
	 * @param vulnId
	 *	Integer vulnerability id.
	 * @param type
	 *	Integer, see the above constant values
	 * @return
	 *	Array requested info
	*/
	public function getInfo( $vulnId, $type, $lang_id = self::LANG_ENGLISH ) {
		// Select Extended Solution
		$info = $this->database['ca']->getRow( "vuln_track.text, vuln_track.text_type", "vuln_id = '" . (int)$vulnId . "' && text.text_type_id = text_type.text_type_id && text_type.text_type_value = '".(int)$type."' AND text_type.lang_id = '".(int)$lang_id."'" );

		return $info;
	}

	function advisorySolutionStatusText( $status ) {
		switch( $status ) {
			case 1:
				return 'Unpatched';
			case 2:
				return 'Vendor Patched';
			case 3:
				return 'Vendor Workaround';
			case 4:
				return 'Partial Fix';
		}
	}

	/**
	 * Fetch criticality text.
	 * @param criticality
	 *	Integer criticality id
	 * @return
	 *	String criticality text
	*/
	function advisoryCriticalityText( $criticality, $lang_id = self::LANG_ENGLISH ) {
		// Select criticality
		$criticality = 6 - $criticality; // Normalize.
		$row = $this->database['ca']->getRow( "vuln_track.critical_type", "critical_type_value = '" . (int)$criticality . "' AND lang_id = '".(int)$lang_id."'" );
		// Return criticality
		return $row['critical_type_name'];
	}

	/**
	* Fetch threat score text.
	* @param vulnid
	* @return
	*	int threat score
	*/
	function advisoryThreatScore( $vuln_id) {

		$row = $this->database['ca']->getRow( "vuln_track.newengland_vulnscore", "vuln_id = " . (int)$vuln_id );
		// Return exploit_score
                if(!empty($row['exploit_score'])){
                    return $row['exploit_score'];
                }else{
                    return null;
                }
	}

	// Return Where
	function advisoryWhere( $vuln_id, $lang_id = self::LANG_ENGLISH ) {
		// Select 'from where' - we only get one row - a given advisory only has one 'from...' entry
		$row = $this->database['ca']->getRow( "vuln_track.where_type, vuln_track.where_type_info", "vuln_id = '" . (int)$vuln_id . "' && where_type.where_type_value = where_type_info.where_type_id AND where_type.lang_id = '".(int)$lang_id."'" );
		// Return content
		return $row;
	}

	function advisoryImpact( $vuln_id, $lang_id = self::LANG_ENGLISH ) {
		// Select impact - there can be multiple impacts for a given vuln_id
		$row = $this->database['ca']->getRows( "vuln_track.impact_type, vuln_track.impact_type_info", "vuln_id = '" . (int)$vuln_id . "' && impact_type.impact_type_value = impact_type_info.impact_type_id AND lang_id = '".(int)$lang_id."'", "impact_type.impact_type_value DESC" );
		// Return content
		return $row;
	}

	/**
	 * Fetch affected OS ( type 0 ) or Software ( type 1 )
	 * @param vuln_id
	 *	Integer vulnerability id
	 * @param type
	 *	Integer product type, 0 - OS, 1 - Soft, default: OS
	*/
	function vulnGetAffectedProducts( $vuln_id, $type = 0 ) {
		$products = array();
		$where = "os_soft_rel.os_id = os_soft.os_soft_id";
		if ( 1 == $type ) {
			$where = "os_soft_rel.soft_id = os_soft.os_soft_id";
		}
		$rows = $this->database['ca']->getRows( "vuln_track.os_soft, vuln_track.os_soft_rel", "os_soft_rel.vuln_id = '" . (int)$vuln_id . "' && ".$where, "os_soft_name", "", array( "os_soft_name", "os_soft_id" ) );

		for ( $i = 0; $i < count( $rows ); $i++ ) {
			$cpeExists = Cpe::existsMatch( $rows[$i]['os_soft_id'] );
			array_push($products, array( 0 => $rows[$i]['os_soft_id'], 1 => $rows[$i]['os_soft_name'], 2 => $cpeExists ) );
		}

		return $products;
	}

	/**
	 * Function for fetching advisory pocs ( id and file name).
	 * @param accountId
	 *	Integer account id, to check if the user has access or not to the PoC info.
	 * @param vulnId
	 *	Integer vulnId
	 * @return
	 *	Mixed boolean or array, false if there is no PoC or array of poc data, having id and file_name set
	*/
	function fetchPoCData( $accountId, $vulnId ) {
		// PoC can only be seen by EVM users!
		if ( $GLOBALS['options']->OPT_ADVISORY_POC_DATA == false ) {
			return false;
		}
		$results = $this->database['ca']->getRows( "vuln_track.vuln_files_approved", "vuln_id = '".(int)$vulnId."'", "", "", array( "id", "file_name" ) );
		$GLOBALS['debug']->notice( "Advisory poc data fetched" );
		if ( count( $results ) == 0 ) {
			return false;
		} else {
			return $results;
		}
	}

	/**
	 * Function for fetching PoC file contents.
	 * @param id
	 *	Integer id of the selected exploit
	*/
	function fetchExploitFileData( $exploitId ) {
		// PoC can only be seen by EVM users!
		if ( $GLOBALS['options']->OPT_ADVISORY_POC_DATA == false ) {
			return false;
		}
		$results = $this->database['ca']->getRows( "vuln_track.vuln_files_approved", "id = '".(int)$exploitId."'", "", "", array( "file_name", "file_data" ) );
		$GLOBALS['debug']->notice( "Advisory poc content fetched" );

		if ( count( $results ) == 0 ) {
			header("HTTP/1.0 404 Not Found");
			die();
		} else {
			// Set header information
			header("Content-Type: application/zip");
			header('Content-Disposition: attachment; filename="'.$results[0]['file_name'].'"');
			header("Content-Length: " . strlen( $results[0]['file_data'] ) );
			echo base64_decode( $results[0]['file_data'] );
			die();
		}
	}

	/**
	 * Get available languages for selected advisory.
	 * @param vuln_id
	 *	Integer vulnerability id
	 * @return
	 *	Array having the sepcified languages set to true or false
	*/
	function getLanguages( $vuln_id ) {
		$languages = array(
			"english" => true
			,"danish" => false
			,"german" => false
		);
		// Select Available Languages
		$result = $this->database['ca']->queryGetRows("SELECT DISTINCT
				lang_id
			FROM
				vuln_track.vuln_reference
			WHERE
				m_vuln_id = '" . (int)$vuln_id . "' && vuln_status != 4
			ORDER BY
				lang_id
		");

		for ( $i = 0; $i < count( $result ); $i++ ) {
			// Register language as being available
			if ( $result[$i]['lang_id'] == self::LANG_ENGLISH ) {
				$languages["english"] = true;
			}
			if ( $result[$i]['lang_id'] == self::LANG_DANISH ) {
				$languages["danish"] = true;
			}
			if ( $result[$i]['lang_id'] == self::LANG_GERMAN ) {
				$languages["german"] = true;
			}
		}

		return $languages;
	}

	/**
	 * Fetch advisory details.
	 *
	 * VIM Only
	 *
	 * @param vulnId
	 *	Integer vulnerability id
	 *
	 * @param getExploitData
	 *	Boolean Do we also allow for retreiving exploit data? (i.e. VIM only, not CSI)
	 *
	 * @return
	 *	Array containing advisory details
	 *
	 *
	 *	TODO:	If $formatForDisplay is true, the function formats the text with VIM specific code.
	 *			Such functionality shouldn't reside in the global space. Until the fix is implemented,
	 *			the function should be used with the $formatForDisplay value as false.
	 */
	function getDetails( $vulnId, $getExploitData = false, $formatForDisplay = false, $lang_id = self::LANG_ENGLISH, $options = null ) {
		$lang_id = ( $lang_id ? $lang_id : self::LANG_ENGLISH );

		$result['details'] = $this->fetchRawDetails( $vulnId, $lang_id );

		if ( $result['details']['vuln_status'] == 4 ) {
			return false;
		}

		$vendorArray = $this->fetchVendors( $vulnId );
		$result['vendors'] = "";
		for ( $i = 0, $size = count( $vendorArray ); $i < $size; $i++ ) {
			$result['vendors'] .= $vendorArray[$i]['vendor_name']."\n";
		}

		$result['cve'] = $this->fetchCVE( $vulnId );

		$rows = $this->db->select()
			->columns( array( 'text.text_text'
							  ,'text_type.text_type_value'
							  ,'text_type.text_type_name') )
			->from( 'vuln_track.text' )
			->join( 'vuln_track.text_type' )
			->using( array( 'text_type_id' ) )
			->where( array( 'text.vuln_id' => $vulnId
							,'text_type.lang_id' => $lang_id ) )
			->orderBy( array( 'text_type_value' => 'ASC' ) )
			->exec();

		foreach ( $rows as $aRow ) {
			$typeValue = $aRow['text_type_value'];
			switch ( $typeValue ) {
			case self::DESCRIPTION:
				$result['description'] = $aRow;
				break;
			case self::SOLUTION:
				$result['solution'] = $aRow;
				break;
			case self::CREDITS:
				$result['credits'] = $aRow;
				break;
			case self::CHANGELOG:
				$result['changelog'] = $aRow;
				break;
			case self::ORIGINAL_ADVISORY:
				$result['originalAdvisory'] = $aRow;
				break;
			case self::OTHER_REFERENCES:
				$result['otherReferences'] = $aRow;
				break;
			/*
			 * @todo:
			 * The REASON, EXTENDED_DESCRIPTION and EXTENDED_SOLUTION shouldn't be fetched
			 * from the database if the appropriate options are not set.
			 */
			case self::REASON:
				$result['ratingReason'] = ( !empty( $options ) && $options->OPT_ADVISORY_EXTENDED_DESCRIPTION ) ? $aRow : null;
				break;
			case self::EXTENDED_DESCRIPTION:
				$result['extendedDescription'] = ( !empty( $options ) && $options->OPT_ADVISORY_EXTENDED_DESCRIPTION ) ? $aRow : null;
				break;
			case self::EXTENDED_SOLUTION:
				$result['extendedSolution'] = ( !empty( $options ) && $options->OPT_ADVISORY_EXTENDED_DATA ) ? $aRow : null;
				break;
			}
		}

		$result['advisorySolutionStatusText'] = $this->advisorySolutionStatusText( $result['details']['vuln_solution_status'] );
		$result['advisoryCriticalityText'] = $this->advisoryCriticalityText( $result['details']['vuln_critical_boolean'], $lang_id );
		$result['advisoryImpact'] = $this->advisoryImpact( $vulnId, $lang_id );
		$result['advisoryWhere'] = $this->advisoryWhere( $vulnId, $lang_id );
		$result['affectedOs'] = $this->vulnGetAffectedProducts( $vulnId );
		$result['affectedSoftware'] = $this->vulnGetAffectedProducts( $vulnId, 1 );
		$result['shortDescription'] = "";

		// Get the Exploit data
		if ( $getExploitData && isset( $GLOBALS['exploit'] ) ) {
			$result['exploit'] = ( !empty( $options ) && $options->OPT_ADVISORY_POC_DATA ) ? $GLOBALS['exploit']->getExploits( $vulnId ) : "";
		}

		$result['languages'] = $this->getLanguages( $vulnId );
		if ( array_key_exists( 'advisoryRelations', $GLOBALS ) && is_object( $GLOBALS['advisoryRelations'] ) && $GLOBALS['advisoryRelations']->locateRelations( $vulnId, 'SAID', 0 ) == true ) {
			$result['deepLinks'] = $GLOBALS['advisoryRelations']->refs;
		}

		$pos = strpos( $result['description']['text_text'], "\n" );
		if ( $pos ) {
			$result['shortDescription'] = trim( substr( $result['description']['text_text'], 0, $pos) );
		}

		// @todo: this should be in the viAdvisory class.
		if ( $formatForDisplay == true ) {
			$result['originalAdvisory']['text_text'] = MISC::advisoryGenerateLinks( $result['originalAdvisory']['text_text'], true );
			$result['otherReferences']['text_text'] = MISC::advisoryGenerateLinks( $result['otherReferences']['text_text'], true );
			$result['changelog']['text_text'] = MISC::advisoryGenerateLinks( $result['changelog']['text_text'], true );
			$result['credits']['text_text'] = MISC::advisoryGenerateLinks( $result['credits']['text_text'], true );
			$result['solution']['text_text'] = MISC::advisoryGenerateLinks( $result['solution']['text_text'], true );
			$result['description']['text_text'] = MISC::advisoryGenerateLinks( $result['description']['text_text'], false );
		}

		// Check for beta!
		if ( BETA == true ) {
			$result['extendedDescription']['text_text'] = "Not available during Public Beta.";
			$result['extendedSolution']['text_text'] = "Not available during Public Beta.";
			$result['ratingReason']['text_text'] = "Not available during Public Beta.";
			$result['exploit'] = "";
		}

		$GLOBALS['debug']->notice( "Advisory data fetched" );
		return $result;
	}

	private function impact( $impact ) {
		return ( 0 == $impact ? 0 : 1.176 );
	}

	/**
	 * Function for pull the base and temportal vectors out of a CVSS Vector.  Only need these two now
	 * as we only pass these secunia cvss scores which have no environmental metrics set
	 * @param vector
	 *	String cvss vector
	 * @return
	 *	String base vector piece
	 */
	protected function getCvssVectorComponents( $vector ) {

		// First get the base vector out
		if ( !preg_match("@AV:(L|A|N)/AC:(H|M|L)/Au:(M|S|N)/C:(N|P|C)/I:(N|P|C)/A:(N|P|C)@", $vector, $matches) ) {
			return;
		}

		$CVSSValues = $GLOBALS['misc']->returnNISTCVSSArray();

   		$AV = $matches[1];
		$AC = $matches[2];
		$Au = $matches[3];
		$C = $matches[4];
		$I = $matches[5];
		$A = $matches[6];

		// construct base vector
		$baseVector = 'AV:' . $AV . '/AC:' . $AC . '/Au:' . $Au . '/C:' . $C . '/I:' . $I . '/A:' . $A;


		// Now get the temporal data
		$temporalVector = '';
		if ( preg_match("@E:(U|P|POC|F|H|ND)/RL:(O|OF|T|TF|W|U|ND)/RC:(UC|UR|C|ND)@", $vector, $matches) ) {
			// Calculate temporal score
			$E = $matches[1];
			$RL = $matches[2];
			$RC = $matches[3];

			// construct temporal vector
			if ( $E ) {
				$temporalVector .= 'E:' . $E;
			} else {
				$temporalVector .= 'E:ND';
			}
			if ( $RL ) {
				$temporalVector .= '/RL:' . $RL;
			} else {
				$temporalVector .= '/RL:ND';
			}
			if ( $RC ) {
				$temporalVector .= '/RC:' . $RC;
			} else {
				$temporalVector .= '/RC:ND';
			}
		}

		$vectorArray = array( $baseVector, $temporalVector );
		return $vectorArray;
	}

	/**
	 * Function for calculating the CVSS2 Score
	 * @param vector
	 *	String attack vector
	 *
	 * @return
	 *	String score
	*/
	protected function CVSS2Calc( $vector ) {
		$baseScore = $temporalScore = $envScore = 0.0;

		if ( !preg_match("@AV:(L|A|N)/AC:(H|M|L)/Au:(M|S|N)/C:(N|P|C)/I:(N|P|C)/A:(N|P|C)@", $vector, $matches) ) {
			return;
		}

		$CVSSValues = $GLOBALS['misc']->returnNISTCVSSArray();

		$AV = $CVSSValues['AV'][$matches[1]][1];
		$AC = $CVSSValues['AC'][$matches[2]][1];
		$Au = $CVSSValues['Au'][$matches[3]][1];
		$C = $CVSSValues['C'][$matches[4]][1];
		$I = $CVSSValues['I'][$matches[5]][1];
		$A = $CVSSValues['A'][$matches[6]][1];

		// Calculate CVSS2 Base Score
		$impact = 10.41*(1-(1-$C)*(1-$I)*(1-$A));
		$exploitability = 20*$AC*$Au*$AV;
		$baseScore = round( (.6*$impact+.4*$exploitability-1.5)*$this->impact($impact), 1);

		$Score = array();
		$Score['BaseScore'] = $baseScore;
		$Score['ImpactSubscore'] = $impact;
		$Score['ExploitabilitySubscore'] = $exploitability;
		$Score['OverallScore'] = $baseScore;

		if ( preg_match("@E:(U|P|POC|F|H|ND)/RL:(O|OF|T|TF|W|U|ND)/RC:(UC|UR|C|ND)@", $vector, $matches) ) {
			// Calculate temporal score
			$E = $CVSSValues['E'][$matches[1]][1];
			$RL = $CVSSValues['RL'][$matches[2]][1];
			$RC = $CVSSValues['RC'][$matches[3]][1];
			$temporalScore = round( ($baseScore*$E*$RL*$RC), 1);

			// Show temporal score only if one or more temporal metrics have been supplied
			foreach ( $matches as $match => $value ) {
				if ( $match > 0 && $value != "ND") {
					$Score['TemporalScore'] = $temporalScore;
					$Score['OverallScore'] = $temporalScore;
					break;
				}
			}

			if ( preg_match("@CDP:(N|L|LM|MH|H|ND)/TD:(N|L|M|H|ND)/CR:(L|M|H|ND)/IR:(L|M|H|ND)/AR:(L|M|H|ND)@", $vector, $matches) ) {
				// Calculate environmental score
				$CDP = $CVSSValues['CDP'][$matches[1]][1];
				$TD = $CVSSValues['TD'][$matches[2]][1];
				$CR = $CVSSValues['CR'][$matches[3]][1];
				$IR = $CVSSValues['IR'][$matches[4]][1];
				$AR = $CVSSValues['AR'][$matches[5]][1];

				$adjustedImpact = min(10,10.41*(1-(1-$C*$CR)*(1-$I*$IR)*(1-$A*$AR)));
				$adjustedBase = round( ((0.6*$adjustedImpact)+(0.4*$exploitability)-1.5)*$this->impact($adjustedImpact), 1);
				$adjustedTemporal = round($adjustedBase*$E*$RL*$RC, 1);
				$envScore = round(($adjustedTemporal+(10-$adjustedTemporal)*$CDP)*$TD, 1);

				// Show environmental score only if one or more environmental metrics have been supplied
				foreach ( $matches as $match => $value ) {
					if ( $match > 0 && $value != "ND") {
						$Score['EnvScore'] = $envScore;
						$Score['ModImpactSubscore'] = $adjustedImpact;
						$Score['OverallScore'] = $envScore;
						break;
					}
				}


			}
		}

		return $Score;
	}

	protected function CVSS3Calc($sVector) {

        $nBaseScore = $nTemporalScore = $nEnvScore = 0.0;
        $scopeCoefficient = 1.08;
        $exploitabilityCoefficient = 8.22;
        $nImpact = 0;
        if ( !preg_match("@AV:(L|A|N|P)/AC:(H|L)/PR:(N|L|H)/UI:(N|R)/S:(U|C)/C:(N|L|H)/I:(N|L|H)/A:(N|L|H)@", $sVector, $aMatches) ) {
            return;
        }
        $aCVSS3Values = $GLOBALS['misc']->returnNISTCVSS3Array();
        $AV = $aCVSS3Values['AV'][$aMatches[1]][1];
        $AC = $aCVSS3Values['AC'][$aMatches[2]][1];
        $PR = $aCVSS3Values['PR'][$aMatches[3]][1];
        $UI = $aCVSS3Values['UI'][$aMatches[4]][1];
        $S = $aCVSS3Values['S'][$aMatches[5]][1];
        $C = $aCVSS3Values['C'][$aMatches[6]][1];
        $I = $aCVSS3Values['I'][$aMatches[7]][1];
        $A = $aCVSS3Values['A'][$aMatches[8]][1];
        // Calculate CVSS3 Base Score
        $impactSubScoreMultiplier = (1 - ((1 - $C) * (1 - $I) * (1 - $A)));
        if($S == 7.52) {//C
            if($PR == 0.62) {
                $PR = 0.68;
            } else if($PR == 0.27) {
                $PR = 0.5;
            }
            $nImpact = $S * ($impactSubScoreMultiplier - 0.029) - 3.25 * pow($impactSubScoreMultiplier - 0.02, 15);
        } else { //U
            $nImpact = $S * $impactSubScoreMultiplier;
        }
        $nExploitability = $exploitabilityCoefficient * $AV * $AC * $PR * $UI;
        if ($nImpact <= 0) {
            $nBaseScore = 0;
        } else {
            if ($S == 7.52) {
                //$nBaseScore = round(min(($nExploitability + $nImpact) * $scopeCoefficient, 10),1);
                $nBaseScore = $this->roundUp1(min(($nExploitability + $nImpact) * $scopeCoefficient, 10));
            } else {
                //$nBaseScore = round(min(($nExploitability + $nImpact), 10),1);
                $nBaseScore = $this->roundUp1(min(($nExploitability + $nImpact), 10));
            }
        }
        if ( preg_match("@E:(U|P|F|H|X)/RL:(O|T|W|U|X)/RC:(U|R|C|X)@", $sVector, $aMatches) &&
            $aMatches[1] != "X" || $aMatches[2] != "X" || $aMatches[3] != "X" ) {
            // Calculate temporal score
            $E = $aCVSS3Values['E'][$aMatches[1]][1];
            $RL = $aCVSS3Values['RL'][$aMatches[2]][1];
            $RC = $aCVSS3Values['RC'][$aMatches[3]][1];
            //$nTemporalScore = round( ($nBaseScore*$E*$RL*$RC), 1);
            $nTemporalScore = $this->roundUp1( ($nBaseScore*$E*$RL*$RC) );
            if ( preg_match("@CR:(L|M|H|X)/IR:(L|M|H|X)/AR:(L|M|H|X)/MAV:(X|L|H)/MAC:(X|N|L|H)/MPR:(X|N|L|H)/MUI:(X|N|R)/MS:(X|U|C)/MC:(X|N|L|H)/MI:(X|N|L|H)/MA:(X|N|L|H)@", $sVector, $aMatches) ) {
                // Calculate environmental score
                $CR = $aCVSS3Values['CR'][$aMatches[1]][1];
                $IR = $aCVSS3Values['IR'][$aMatches[2]][1];
                $AR = $aCVSS3Values['AR'][$aMatches[3]][1];
                $MAV = ($aMatches[4] == 'X' ? $aCVSS3Values['AV'][$aMatches[4]][1] : $aCVSS3Values['MAV'][$aMatches[4]][1]);
                $MAC = ($aMatches[5] == 'X' ? $aCVSS3Values['AC'][$aMatches[5]][1] : $aCVSS3Values['MAC'][$aMatches[5]][1]);
                $MPR = ($aMatches[6] == 'X' ? $aCVSS3Values['PR'][$aMatches[6]][1] : $aCVSS3Values['MPR'][$aMatches[6]][1]);
                $MUI = ($aMatches[7] == 'X' ? $aCVSS3Values['UI'][$aMatches[7]][1] : $aCVSS3Values['MUI'][$aMatches[7]][1]);
                $MS = ($aMatches[8]  == 'X' ? $aCVSS3Values['S'][$aMatches[8]][1]  : $aCVSS3Values['MS'][$aMatches[8]][1]);
                $MC = ($aMatches[9]  == 'X' ? $aCVSS3Values['C'][$aMatches[9]][1]  : $aCVSS3Values['MC'][$aMatches[9]][1]);
                $MI = ($aMatches[10] == 'X' ? $aCVSS3Values['I'][$aMatches[10]][1] : $aCVSS3Values['MI'][$aMatches[10]][1]);
                $MA = ($aMatches[11] == 'X' ? $aCVSS3Values['A'][$aMatches[11]][1] : $aCVSS3Values['MA'][$aMatches[11]][1]);

                $nEnvScore = 0;
                $envModifiedImpactSubScore = 0;
                $envModifiedExploitabalitySubScore = $exploitabilityCoefficient * $MAV * $MAC * $MPR * $MUI;
                $envImpactSubScoreMultiplier = min (1 - (
                        (1 - $MC * $CR) *
                        (1 - $MI * $IR) *
                        (1 - $MA * $AR)), 0.915);
                if ($MS == "U" || ($MS == "X" && $S == "U")) {
                    $envModifiedImpactSubScore = $MS * $envImpactSubScoreMultiplier;
                    $nEnvScore = $this->roundUp1($this->roundUp1(min($envModifiedImpactSubScore + $envModifiedExploitabalitySubScore), 10) * $E * $RL * $RC);
                } else {
                    $envModifiedImpactSubScore = $MS * ($envImpactSubScoreMultiplier - 0.029) - 3.25 * pow($envImpactSubScoreMultiplier - 0.02, 15);
                    $nEnvScore = $this->roundUp1($this->roundUp1(min($scopeCoefficient * ($envModifiedImpactSubScore + $envModifiedExploitabalitySubScore), 10)) *
                        $E * $RL * $RC);
                }
                if ($envModifiedImpactSubScore <= 0) {
                    $nEnvScore = 0;
                }
            }
        }
        return array(
            'BaseScore' => $nBaseScore,
            'TemporalScore' => $nTemporalScore ? $nTemporalScore : "Undefined",
            'OverallScore' => $nTemporalScore ? $nTemporalScore : "Undefined",
            'EnvScore' => $nEnvScore ? $nEnvScore : "Undefined"
        );//OverallScore not available in vt14_ca/ca/public_html/functions.php
    }

    function roundUp1($d) {
        return ceil($d*10)/10;
    }

	function parseNISTCVSS( $vector ) {
		$result = array();
		$CVSSValues = $GLOBALS['misc']->returnNISTCVSSArray();

		// Remove leading and trailing ()
		$vector = trim( $vector, '()' );

		// Split input string
		$entries = explode( '/', $vector );

		// Loop trough each entry
		//while ( list($k, $value) = each($entries) ) { //PHP72
		foreach ( $entries as $k => $value) {
			// Split each value
			list( $ident, $value ) = explode( ':', $value );
			// Result
			$result[$ident] = array( "name" => $CVSSValues[$ident]['outputName'], "value" => $value, "value_description" => $CVSSValues[$ident][$value][0] );
		}

		foreach ( array_keys( $CVSSValues ) as $ident ) {
			if ( !isset( $result[$ident] ) ) {
				$result[$ident] = array( "name" => $CVSSValues[$ident]['outputName'], "value" => 'ND', "value_description" => 'Not Defined' );
			}
		}

		// Return output
		return $result;
	}

	/**
	 * Function for checking if the environmental metrics vector is valid or not
	 *
	 * @param $vector String
	 *  The environmental metrics vector
	 *
	 * @return bool
	 */
	public static function isValidEnvironmentalMetricsVector( $vector ) {
		$matches = array();
		if ( preg_match("@CDP:(N|L|LM|MH|H|ND)/TD:(N|L|M|H|ND)/CR:(L|M|H|ND)/IR:(L|M|H|ND)/AR:(L|M|H|ND)@", $vector, $matches) ) {
			return true;
		}
		return false;
	}

	/**
	 * Function for checking if the temporal metrics vector is valid or not
	 *
	 * @param $vector String
	 *  The temporal metrics vector
	 *
	 * @return bool
	 */
	public static function isValidTemporalMetricsVector( $vector ) {
		$matches = array();
		if ( preg_match("@E:(U|P|POC|F|H|ND)/RL:(O|OF|T|TF|W|U|ND)/RC:(UC|UR|C|ND)@", $vector, $matches) ) {
			return true;
		}
		return false;
	}

}
