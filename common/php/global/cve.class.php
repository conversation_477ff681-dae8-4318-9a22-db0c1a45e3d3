<?php
/**
 * @file cve.class.php
 */

/**
 * The CVE provides CVE functionality.
 */
class CVE {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );

	}

	/**
	 * Fetch CVE data.
	 * @param cveId
	 *	String CVE id
	 * @param isEscaped
	 *	Boolean true or false if cveId is escaped or not for the SQL queries.
	 * @return
	 *	Array of CVE data
	 */
	function fetchCVEData( $cveId, $isEscaped = false ) {
		$result = array();
		$cveArray = CVE::extractCVE($cveId);
		if($cveArray != null){
			$cveId = $cveArray[1].'-'.$cveArray[2];
		}else{
			return $result;
		}
		if ( $isEscaped !== true ) {
			$cveId = $this->database['ca']->escapeString( $cveId );
		}

		$result = $this->database['ca']->getRow("vuln_track.refsys_entries", "source = 'cve' && reference = '" . $cveId . "'");
		$ref = $this->database['ca']->getRows( "vuln_track.refsys_entry_refs", "entry_id = '".(int)$result['entry_id']."'", "source DESC" );
		for ( $i = 0; $i < count( $ref ); $i++ ) {
			if ( !$GLOBALS['misc']->fetchDisallowedSource( $ref[$i]['source'] ) && !stristr( $ref[$i]['reference'], 'frsirt' ) && !stristr( $ref[$i]['reference'], 'k-otik' ) ) {

				// Only create the array if we have valid data for it - reserved CVEs (candidates) do not have sources, and
				// the logic on the UI side requires that we either have a valid array or nothing for the 'sources' field.
				if ( !isset( $result['sources'] ) ) {
					$result['sources'] = array();
				}
				if ( !isset( $result['sources'][$ref[$i]['source']] ) ) {
					$result['sources'][$ref[$i]['source']] = array();
				}
				array_push( $result['sources'][$ref[$i]['source']], array( "reference" => $ref[$i]['reference'] ) );
			}
		}

		return $result;
	}

	public static function isCVEValid($potentialCve){
		if (CVE::extractCVE($potentialCve) != null){
			return true;
		}
		return false;
	}

	/**
	 * Extract CVE
	 * @param potentialCve
	 *	a string like: CVE-YYYY-XXXX...X where Y = YEAR and X = 4 or more digits (if more then 4, cannot start with 0)
	 * @return
	 *	null if no match or array[0] = full CVE, array[1] = year part and array[2] = the last digits part
	 */
	public static function extractCVE($potentialCve){
		$extractedCVE = array();
		if(preg_match('/^CVE-(\d{4})-(0\d{3}|[1-9]\d{3,10})$/', $potentialCve,$extractedCVE)){
			return $extractedCVE;
		}
		return null;
	}
}

?>