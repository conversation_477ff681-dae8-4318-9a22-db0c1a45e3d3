<?php
/**
 * @file sms.class.php
*/

/**
 * Provides SMS functionality.
 *
 * Configuration options:
 * ----------------------
 *
 * SMS_USERNAME: SMS Username for service provider
 *
 * SMS_PIN: SMS Pin for the service provider
 */
class SMS {

	/**
	 * Format a phone number.
	 * @param number
	 *	String phone number
	 * @return
	 *	String formatted phone number
	*/
	function formatNumber( $number ) {
		// Remove all whitespaces
		$number = preg_replace( '/ /', '', $number );

		// Remove all '+'
		$number = preg_replace( '/\+/', '', $number );

		// Remove leading '0' after country code
		// $number = preg_replace('/^440/', '44', $number);			// old functionality for removing leading 0's, seems to be incorrect.
																	// Fails for numbers starting with leading 0's e.g. 0044 etc.
																	// Also, it only searches for 44 as Country code which seems to be incorrect.
		$number = ltrim( $number, "0" );

		//SVMDEVOPS-4337
		if (strlen($number) <= 10) {//Norway country-code+mobile (2+8 digits)
			$number = '00'. $number; //system will see them as too short and assume you have missed the country code off and add in the 44.
		}

		// Test if number only consist of numbers
		if ( !is_numeric( $number) ) {
			return false;
		}

		// Return prepared number
		return $number;
	}

	/**
	 * Format a SMS text message.
	 * @param textmessage
	 *	String text message
	 * @return
	 *	String 160 chars, url encoded text message.
	*/
	function formatTextMessage( $textmessage ) {
		// Cut textmessage to 160 chars
		$textmessage = substr( $textmessage, 0, 160 );

		// Format it for url
		$textmessage = urlencode( $textmessage );

		// Replace '+' with '%20'
		$textmessage = preg_replace( "/\+/", "%20", $textmessage );

		// Return
		return $textmessage;
	}

	/**
	 * Function for sending an SMS message.
	 * @param numberList
	 *	mixed, Comma separated list of numbers OR
	 *         Array of strings containing phone numbers
	 * @param text
	 *	String, message content
	 * @return
	 *	Mixed true if success, false or reason string if failed.
	*/
	function send( $numberList, $text, $log = '' ) {

		if ( !defined ( 'SMS_USERNAME' ) || !defined( 'SMS_PIN' ) ) {
			$GLOBALS['debug']->critical( "SMS Send failed (Please define csoft auth credentials SMS_USERNAME and SMS_PIN in api/configuration.php)" );
			return false;
		}

		if ( !is_array($numberList) ) {
			$numbersArray = explode(",", $numberList );
		} else {
			$numbersArray = $numberList;
		}

		// Sanitize and prepare the phone numbers. Collect US/Canada numbers separately
		$sanitizedNumbers = "";
		$sanitizedUsNumbers = "";
		$size = count( $numbersArray );
		for ( $i = 0; $i < $size; $i++ ) {
			$prepared = $this->formatNumber( $numbersArray[$i] );
			if ( $prepared !== false &&  strlen( $prepared ) > 3  && is_numeric( $prepared ) && $prepared[0] != '1') {
				if ( !( $sanitizedNumbers == "" ) ) {
					$sanitizedNumbers .= ",";
				}
				$sanitizedNumbers .= $prepared;
			} elseif ( $prepared !== false &&  strlen( $prepared ) > 3  && is_numeric( $prepared ) && $prepared[0] == '1' ){
				if ( !( $sanitizedUsNumbers == "" ) ) {
					$sanitizedUsNumbers .= ",";
				}
				$sanitizedUsNumbers .= $prepared;
			}
		}

		$urlText = $this->formatTextMessage( $text );

		// (#csoft) The format of the error code is: "Code - Reason"
		// According to the CSOFT website some of the error codes are
		// deprecated and that can be seen by manually testing the service.
		// Hence the safest option is check on all the success codes i.e 0,
		// 158 (deprecated) and 99 (test message success)

		if ( ( $sanitizedNumbers != "" ) && ( $urlText != "" ) ) {
			$requestURL = "https://www.csoft.co.uk/sendsms?UserName=" . urlencode( SMS_USERNAME ) . "&PIN=" . urlencode( SMS_PIN ) ."&SendTo=" . $sanitizedNumbers . "&Message=" . $urlText;
			$httpStatus = '';
			$smsStatus = $GLOBALS['misc']->curlLoadPage( $requestURL, $httpStatus );

			if ( preg_match ( '/^([0-9]+) - (.*)$/', $smsStatus, $matches ) ) {
				switch ( $matches[1] ) {
					case 0:
					case 158:
					case 99:
						$GLOBALS['debug']->notice( "Sent SMS to number(s) " . $sanitizedNumbers . ": '" . $text . "'" . ( $log ? ". Log: " . $log : '' ) );
						return true;
						break;
					default:
						$GLOBALS['debug']->error( "Failed to send SMS to number(s) " . $sanitizedNumbers . " with server-side response: '" . $matches[2] . "'" . ( $log ? ". Log: " . $log : '' ) );
						return $matches[2];
				}
			} else {
				$GLOBALS['debug']->error( "Failed to send SMS to number(s) " . $sanitizedNumbers . ". Invalid server-side response: '" . $smsStatus . "'" . ( $log ? ". Log: " . $log : '' ) );
				return false;
			}
		}
		//Send SMS to US numbers
		if ($sanitizedUsNumbers != '' && $urlText != '') {
			$usNumberList = explode(',', $sanitizedUsNumbers);
			$sendSuccess = true;
			foreach ($usNumberList as $phoneNumber) {
				$httpCode = '';
				$usNumber = preg_replace('/1/', '+1', $phoneNumber, 1);
				$data = $this->buildDataForUs($usNumber, $urlText);

				$usSmsStatus = $GLOBALS['misc']->curlLoadPage( $data['url'], $httpCode, $data['urlText'], $data['headers'] );
				//$usSmsStatus {"id":"226891df-0952-46a1-928f-baed1df7ea51"}
				//$usStatus 200

				if ($httpCode >= 200 && $httpCode < 300) {
					$GLOBALS['debug']->notice( "Sent SMS to number $phoneNumber. HTTP Status: $httpCode. Message: $usSmsStatus" );
				} elseif ($httpCode >= 400 && $httpCode < 600) {
					$GLOBALS['debug']->error("Failed to send SMS to number $phoneNumber with server-side response: $httpCode. Message: $usSmsStatus");
					$sendSuccess = false;
				} else {
					$GLOBALS['debug']->error( "Failed to send SMS to number $phoneNumber. Invalid server-side response. Message: $usSmsStatus");
					$sendSuccess = false;
				}
			}
			return $sendSuccess; //If at least one message failed to send, return false. Message will be sent successfully for other numbers

		}

		if (($sanitizedUsNumbers == '' || $sanitizedNumbers == '') && $urlText == '') {
			$GLOBALS['debug']->error( "SMS Send failed (invalid sms data)" );
			return false;
		}
	}

	/**
	 * @param $number
	 * @param $textMessage
	 * @return array
	 *
	 * Doc: https://developers-portal.esendex.com/default/documentation/conversations
	 */
	private function buildDataForUs($number, $textMessage) {
		$url = 'http://conversations.esendex.com/v1/messages';
		$headers = array(
			'Content-Type: application/json',
			"Api-Key: " . SMS_API_KEY,
			'Account-Reference: EX0355234'
		);
		$text = $this->formatTextMessage($textMessage);
		$message = array(
			'channel' => 'sms',
			'metadata' => array(
				'key' => 'value'
			),
			'recepientVariable' => array(
				'name' => 'SVM'
			),
			'to' => $number,
			'body' => array(
				'text' => array(
					'value' => $text
				)
			),
			'from' => '***********',
			'characterSet' => 'GSM',
			'expiry' => 30
		);
		$urlText = json_encode($message);

		return array (
			'url' => $url,
			'headers' => $headers,
			'urlText' => $urlText
		);

	}
}
