<?php
namespace IntuneGraphAPI;
class GraphAuth {
    private $tenantId;
    private $clientId;
    private $clientSecret;
    private $tokenEndpoint;
    private $accessToken;

    public function __construct($tenantId, $clientId, $clientSecret) {
        $this->tenantId = $tenantId;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->tokenEndpoint = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token";
    }

    public function getAccessToken() {

        if ($this->accessToken) {
            return $this->accessToken; // Return cached token if available
        }

        $postFields = array(
            'client_id' =>  $this->clientId,
            'client_secret' => $this->clientSecret,
            'grant_type' => 'client_credentials',
            'scope' => 'https://graph.microsoft.com/.default'
        );

        $curlHandle = curl_init($this->tokenEndpoint);
        curl_setopt($curlHandle, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($curlHandle, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($curlHandle);

        curl_close($curlHandle);

        $data = json_decode($response,true);

        if (isset($data['access_token'])) {
            
            $this->accessToken = $data['access_token'];

            $response = array();
            $response['access_token'] = $this->accessToken;

            return $response;
        } else {

            return false;
            //throw new \Exception("Error retrieving access token: " . json_encode($data));
        }
    }
}
?>

