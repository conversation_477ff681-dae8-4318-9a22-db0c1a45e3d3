<?php
namespace IntuneGraphAPI;

class GraphClient
{
    private $accessToken;
    private $graphEndpoint = 'https://graph.microsoft.com/v1.0';

    public function __construct($accessToken, $serachText)
    {
        $this->accessToken = $accessToken;
        $this->searchText = $serachText;
    }

    public function fetchIntuneGroups($nextPageLink)
    {

        if (!empty($nextPageLink)) {
            $url = rawurldecode($nextPageLink);
        }else{
            $url = $this->graphEndpoint . '/groups?&$top=100&$orderby=displayName&$count=true&$select=displayName,id';
            if (!empty($this->searchText)) {
                $url .= '&$search="displayName:' . $this->searchText . '"';
            }
        }

        $response = $this->makeRequest($url);

        return $response;
    }

    private function makeRequest($url)
    {
        $headers = [
            "Authorization: Bearer " . $this->accessToken,
            "Accept: application/json",
            "ConsistencyLevel:eventual"
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);

    }
}

?>
