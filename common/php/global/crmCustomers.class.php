<?php
/**
 * @file crmCustomers.class.php
 * Provides CRM customer functionality.
*/
class crmCustomers {
	protected $database = array();

	function __construct() {
		$this->database['crm'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
	}

	/**
	 * Function for checking if customer name is unique ( AND for the current partner_id, if any ).
	 * @param name
	 *	String customer name
	 * @param cst_id
	 *	Integer customer id ( used in case of an update, to skip current customer )
	 * @param isEscaped
	 *	Boolean see database->edit()
	 * @param partner_id
	 *	Integer partner id, optional
	 * @return
	 *	Boolean true if it is ok for use, false if it is already used or data is not valid
	*/
	protected function checkCustomerName( $name, $cst_id = "", $isEscaped = false, $partner_id = "" ) {
		$partnerConstraint = "";
		if ( $partner_id != "" ) {
			if ( !is_numeric( $partner_id ) ) {
				return false;
			}
			$partnerConstraint = " AND partner_id = '".(int)$partner_id."'";
		}
		if ( $isEscaped == false ) {
			$name = $this->database['crm']->escapeString( $name );
		}

		$cstConstraint = "";
		if ( $cst_id != "" ) {
			if ( !is_numeric( $cst_id ) ) {
				return false;
			}
			$cstConstraint = " AND cst_id != '".(int)$cst_id."'";
		}
		if ( $this->database['crm']->numRows('crm.cst', "name = '".$name."'".$partnerConstraint.$cstConstraint ) != 0 ) {
			return false;
		}
		return true;
	}

	/**
	 * Function for adding a new customer in the CRM database
	 * @param values
	 *	Array of values, see the above function
	 * @param isEscaped
	 *	Boolean true or false to secure the data or not. See database->edit()
	 * @return
	 *	Integer new customer id
	*/
	function newCustomer( $values, $isEscaped = false ) {
		if ( $this->checkCustomerName( $values['name'], "", $isEscaped, $values['partner_id'] ) == false ) {
			return false;
		}

		$customerId = $this->database['crm']->edit( "crm.cst", $values, 0, $isEscaped );
		$GLOBALS['debug']->notice( "CRM customer added" );
		return $customerId;
	}

	/**
	 * Function for building case type, only to be used localy.
	 * @param type
	 *	String case type: csi, vi, partner, shadow
	*/
	protected function caseType( $type ) {
		$caseName = "";
		switch ( $type ) {
			case "csi":
				$caseName = "card_nsi";
				break;
			case "vi":
				$caseName = "card_vi";
				break;
			case "partner":
				$caseName = "card_partner";
				break;
			case "shadow":
				$caseName = "card_shadow";
				break;
		}
		return $caseName;
	}

	/**
	 * Function for creating a new customer case ( not data but, the empty case )
	 * @param type
	 *	String case type
	 * @param id
	 *	Integer customer id
	 * @param partner_id
	 *	Integer partner id, optional
	 * @param isEscaped
	 *	Boolean see database->edit()
	 * @return
	 *	Integer new case id or false if invalid case type
	*/
	function newCase( $type, $id, $partner_id = "", $isEscaped = false ) {
		$caseName = $this->caseType( $type );

		if ( $caseName != "" ) {
			$newId = $this->database['crm']->edit("crm.cst", array(
				"master_id" => $id
				,"case_name" => $caseName
				,"partner_id" => $partner_id
			), 0, $isEscaped );
			if ( (int)$newId == 0 ) {
				return false;
			}
			return $newId;
		} else {
			return false;
		}
	}

	/**
	 * Function for fetching the customer child id, based on it's id and case name.
	 * @param cstId
	 *	Integer cutsomer id
	 * @param type
	 *	String case type
	*/
	function getChildId( $cstId, $type ) {
		$caseName = $this->caseType( $type );

		if ( $caseName != "" ) {
			$childId = $this->database['crm']->getRowValue('crm.cst', "cst_id", "master_id = '".(int)$cstId."' AND case_name = '".$caseName."'");
			if ( $childId == "" ) {
				return false;
			} else {
				return $childId;
			}
		} else {
			return false;
		}
	}

	/**
	 * Function for updating a customer entry in the CRM database
	 * @param values
	 *	Array of values, see the above function
	 * @param id
	 *	Integer customer id
	 * @param isEscaped
	 *	Boolean true or false to secure the data or not. See database->edit()
	 * @param partner_id
	 *	Integer partner id ( OPTIONAL )
	*/
	function updateCustomer( $values, $id, $isEscaped = false, $partner_id = "" ) {
		if ( $this->checkCustomerName( $values['name'], $id, $isEscaped, $partner_id ) == false ) {
			return false;
		}
		$this->database['crm']->edit( "crm.cst", $values, 1, "cst_id = '".(int)$id."'".( $partner_id != "" ? " AND partner_id = '".(int)$partner_id."'" : "" ), $isEscaped );
		$GLOBALS['debug']->notice( "CRM customer updated" );
	}

	/**
	 * Function for attaching a contact
	 * @param cumsterId
	 *	Integer customer id, usually the company card cst_id
	 * @param values
	 *	Array of values, see database->edit()
	 * @param primary_contact
	 *	Integer 1 - Primary contact, 0 - Secondary contact
	 * @param partnerId
	 *	Integer partner id
	 * @param isEscaped
	 *	Boolean true or false to secure the data or not. See database->edit()
	*/
	function attachContact( $customerId, $values, $primary_contact = 0, $partner_id = "", $isEscaped = false ) {
		$values['cst_id'] = $customerId;
		$values['partner_id'] = $partner_id;
		$values['primary_contact'] = $primary_contact;
		$this->database['crm']->edit( "crm.contacts", $values, 0, "", $isEscaped );
	}

	/**
	 * Function for fetching the sales representative id, attached to an account id
	 * @param cstId
	 *	Integer account id
	 * @return
	 *	Mixed sales person's id or false if something went wrong
	*/
	function fetchSalesPersonID( $cstId ) {
		$personId = $this->database['crm']->getRowValue( "crm.cst", "person_id", "cst_id = '".(int)$cstId."'" );
		$GLOBALS['debug']->error( "Sales person id could not be found!" );
		return $personId;
	}
}