<?php

/**
 * @file cache.class.php
 * Memcache Abstraction Layer
 *
 */

class CACHE implements Psr\Log\LoggerAwareInterface {

	/*
	 * Instance of the Memcached class
	 */
	private static $instance = null;

	/*
	 * Instance of the CACHE class
	 */
	private static $selfInstance = null;

	/**
	 * @var Psr\Log\LoggerInterface $logger
	 */
	private $logger = null;

	/**
	 * Memcached constructor.
	 *
	 * @param String $host
	 */
	function __construct( $cache = null ) {
		if ($cache instanceof Memcached) {
			self::$instance = $cache;
		}
	}

	/*
	 * Setter for the logger object
	 * @param Psr\Log\LoggerInterface $logger Use the logger to debug
	 */
	public function setLogger( Psr\Log\LoggerInterface $logger ) {
		$this->logger = $logger;
	}

	/**
	 * Logs an entry to the action log file
	 * @param string $msg
	 *   Message to log
	 * @param int $errorType
	 */
	public function log( $msg = "", $errorType = "info" ) {
		if (is_null($this->logger)) {
			return false;
		}
		$logConstant = "MEMCACHE: ";
		switch ($errorType) {
			case  "info":
				$this->logger->info( $logConstant . $msg . "." );
				break;
			case "notice":
				$this->logger->notice($logConstant . $msg . "." );
				break;
			case "error":
				$this->logger->error($logConstant . $msg . "." );
				break;
			default:
				break;
		}
	}

	/**
	 * Returns the Memcached instance
	 *	Returns false if the Memcached instance isn't found
	 */
	public static function getInstance( ) {
		if ( isset( self::$instance ) ) {
			return self::$instance;
		}
		return false;
	}

	/**
	 * Sets a Memcached instance that can be later fetched with {@link CACHE::getInstance()}
	 * @param Memcached $cache
	 */
	public static function setInstance( Memcached $cache ) {
		self::$instance = $cache;
	}

	/**
	 * Returns the CACHE instance
	 */
	public static function getSelfInstance( ) {
		if (! isset( self::$selfInstance ) ) {
			self::$selfInstance = new self();
		}
		return self::$selfInstance;
	}

	/**
	 * Sets a Memcached instance that can be later fetched with {@link CACHE::getSelfInstance()}
	 * @param CACHE $cache
	 */
	public static function setSelfInstance( CACHE $cache ) {
		self::$selfInstance = $cache;
	}

	public function addServer ($host, $port, $weight = 0) {
		if (self::$instance instanceof Memcached) {
			$cache = self::$instance;
			return $cache->addServer($host, $port, $weight);
		}

		return false;
	}

	public function addServers (array $servers) {
		if (self::$instance instanceof Memcached) {
			$cache = self::$instance;
			return $cache->addServers($servers);
		}

		return false;
	}

	public function set ($key, $value, $expiration = 0) {
		if (self::$instance instanceof Memcached) {
			$cache = self::$instance;
			return $cache->set($key, $value, $expiration);
		}

		return false;
	}


	public function get ($key, callable $cache_cb = null, $flags = 0) {
		if (self::$instance instanceof Memcached) {
			$cache = self::$instance;
			return $cache->get($key, $cache_cb, $flags);
		}

		return false;
	}

	public function delete ($key, $time = 0) {
		if (self::$instance instanceof Memcached) {
			$cache = self::$instance;
			return $cache->delete($key, $time);
		}

		return false;
	}

}

