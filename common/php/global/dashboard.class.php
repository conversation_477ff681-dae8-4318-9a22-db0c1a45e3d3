<?php
/**
 * @file dashboard.class.php
*/

  /**
   * - Not intended to provide portlet functionality as their data is part of their respective objects.
   * - Requires the global variable write_permisson to be set to TRUE!
   *
   * Legacy:
   * - The schema contains a type column to distinguish between vi and evm dashboards. These have been
   *   merged into a single dashboard so we don't use it anymore.
   */

class DASHBOARD {

	private $accountId;

	function __construct( $accountId ) {
		$this->accountId = $accountId;
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for saving a profile.
	 * @param profileId
	 *	Integer profile id
	 * @param accountId
	 *	Integer account id
	 * @param properties
	 *	Array of profile data values ( E.g.: name, default_profile, data )
	*/
	protected function saveProfile( $profileId, $accountId, $properties, $isEscaped = false ) {
		if ( isset( $properties['name'] ) ) {
			if ( !$this->profileExists( $properties['name'], $accountId, $isEscaped, $profileId ) ) {
				echo $GLOBALS['json']->formAnswer( false, "Profile already exists!" );
				return;
			}
		}
		$this->database['ca']->edit("ca.dashboard_profiles", $properties, 1, "account_id = '".(int)$accountId."' AND id = '".(int)$profileId."'", $isEscaped );
		$GLOBALS['debug']->notice( "Dashboard profile updated" );
		echo $GLOBALS['json']->formAnswer( true );
	}

	/**
	 * Check if profile already exists
	 * @param profileName
	 *	String profile name
	 * @param accountId
	 *	Integer account id
	 * @param isEscaped
	 *	Boolean true or false if data is escaped or not
	 * @param profileId
	 *	Integer profile id, optional
	*/
	protected function profileExists( $profileName, $accountId, $isEscaped = false, $profileId = null ) {
		if ( $isEscaped !== true ) {
			$profileName = $this->database['ca']->escapeString( $profileName );
		}
		$profileSQL = "";
		if ( $profileId != null ) {
			$profileSQL = " AND id != ".(int)$profileId;
		}
		$count = $this->database['ca']->numRows('ca.dashboard_profiles', "name = '".$profileName."' AND account_id = '".(int)$accountId."'".$profileSQL);
		if ( $count == 0 ) {
			return true;
		} else {
			return false;
		}
	}

	/*
	 * Function for determining if the default profile exists for the specified account
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Boolean
	 */
	protected function defaultProfileExists( $accountId ) {
		$count = $this->database['ca']->numRows('ca.dashboard_profiles', "default_profile = 1  AND account_id = '".(int)$accountId."'");
		return ( $count > 0 );
	}

	/**
	 * Function for creating a new profile.
	 * @param accountId
	 *	Integer account id
	 * @param profileName
	 *	String profile name
	 * @return
	 *	Integer new profile id
	*/
	protected function createProfile( $accountId, $profileName, $isEscaped = false ) {
		if ( !$this->profileExists( $profileName, $accountId, $isEscaped ) ) {
			return false;
		}

		$makeDefault = false;
		if ( !$this->defaultProfileExists( $accountId ) ) {
			$makeDefault = true;
		}
		// Call our edit function with 3rd parameter type=0 for a mysql 'insert'
		$profileId = $this->database['ca']->edit( "ca.dashboard_profiles"
												  ,array( "account_id" => (int)$accountId
														  , "name" => $profileName
														  , "default_profile" => $makeDefault ? 1 : 0
														  )
												  ,0, "", $isEscaped );

		$GLOBALS['debug']->notice( "Dashboard profile created" );
		return $profileId;
	}

	/**
	 * Function for fetching profile drop down data.
	 * @param accountId
	 *	Integer account id
	*/
	function fetchProfiles( $accountId ) {
		$result = $this->database['ca']->getRows( "ca.dashboard_profiles"
			,"account_id = '".(int)$accountId."'"
			,"name ASC"
			,""
			,array( "id"
				,"name"
				,"default_profile"
				,"data AS items"
				,"type"
			)
		);

		$gridData['data'] = $result;

		/*
		 * The first element marked as default is the one we want to use
		 */
		$defaultFound = false;

		for ( $i = 0, $size = count( $gridData['data'] ); $i < $size; $i++ ) {

			if ( $gridData['data'][$i]['default_profile'] == 1 && !$defaultFound ) {

					$gridData['data'][$i]['default'] = "(Default)";
					$defaultFound = true;

			} else {

				$gridData['data'][$i]['default'] = "";

			}

		}


		$GLOBALS['debug']->notice( "Dashboard profiles data fetched" );
		echo $GLOBALS['json']->json_encode(  $gridData );
	}

	/**
	 * Function for deleting a profile.
	 * @param profileId
	 *	Integer profile id
	 * @param accountId
	 *	Integer account id
	*/
	protected function deleteProfile( $profileId, $accountId ) {
		// If we delete the default profile, we must arbitrarily set one of the other profiles as the default.
		// So, first check if this one is the default profile.
		$isDefault = $this->database['ca']->query( "SELECT * FROM ca.dashboard_profiles WHERE id = '".(int)$profileId."' AND account_id = '".(int)$accountId."' AND default_profile=1 LIMIT 1'" );
		$wasDefault = count($isDefault);

		// Now we can go ahead and delete this one
		$this->database['ca']->query("DELETE FROM ca.dashboard_profiles WHERE id = '".(int)$profileId."' AND account_id = '".(int)$accountId."' LIMIT 1");

		// If it was the default, select the first profile we get as the new default.
		if ( $wasDefault ) {
			$this->database['ca']->query( "UPDATE ca.dashboard_profiles SET default_profile=1 WHERE account_id = '".(int)$accountId."' LIMIT 1" );
		}

		$GLOBALS['debug']->notice( "Dashboard profile deleted" );
		echo $GLOBALS['json']->formAnswer( true );
	}

	/**
	 * Set profile as default profile.
	 * @param accountId
	 *	Integer account id
	 * @param profileId
	 *	Integer profile id
	*/
	protected function makeDefault( $accountId, $profileId, $isEscaped = false ) {
		// Set all others as not-default
		$this->database['ca']->edit("ca.dashboard_profiles", array( "default_profile" => 0 ), 1, "account_id = '".(int)$accountId."'", $isEscaped );
		// Set current as default
		$this->database['ca']->edit("ca.dashboard_profiles", array( "default_profile" => 1 ), 1, "account_id = '".(int)$accountId."' AND id = '".(int)$profileId."'", $isEscaped );
		$GLOBALS['debug']->notice( "Dashboard profile set as default" );
		echo $GLOBALS['json']->formAnswer( true );
	}

	function handleRequest( $where ) {

		/*
		 * Read Operations
		 */

		switch( $where ) {
			case "profiledd":
				$this->fetchProfiles( $this->accountId );
				break;
		}

		if ( $GLOBALS['write_permission'] !== true ) {
			$GLOBALS['debug']->error( "Cannot perform action: {where} as it requires WRITE permissions.", array( 'where' => $where ) );
			return;
		}

		/*
		 * Write Operations
		 */
		switch ( $where ) {
			case "save":
				$this->saveProfile( $GLOBALS['input']->MGET('profile_id')
						,$this->accountId
						,array(
							"data" => $GLOBALS['input']->MGET('data')
						)
						,true
				);
				break;
			case "new":
				$profileId = $this->createProfile( $this->accountId, $GLOBALS['input']->MPOST('profile_name'), true );
				if ( $profileId === false ) {
					echo $GLOBALS['json']->formAnswer( false, "Profile already exists!" );
				} else {
					echo $GLOBALS['json']->formAnswer( true, $profileId );
				}
				break;
			case "rename":
				$this->saveProfile( $GLOBALS['input']->MGET('profile_id')
						,$this->accountId
						,array(
							"name" => $GLOBALS['input']->MGET('name')
						)
						,true
				);
				break;
			case "default":
				$this->makeDefault( $this->accountId, $GLOBALS['input']->MGET('profile_id'), true );
				break;
			case "delete":
				$this->deleteProfile( $GLOBALS['input']->MGET('profile_id'), $this->accountId );
				break;
		}
	}
}

?>