<?php
/**
 * @file syncClient.class.php
 */

/**
 * This is the class that is responsable for
 * retriving and applying updates to the
 * local database
 */
class syncClient {

	public $verbose = false;
	public $upToDate = true;

	/**
	 * An implmentation of databaseImporter interface
	 */
	private $importer;
	private $db;

	private $updateId;
	private $updateType;
	private $fileChecksum;
	private $fileName;
	private $renameToFileName;

	private $updateTypes = array(
		'BINLOG'
		,'DUMP'
	);
	private $storageFolder;

	private $storageEngine = ' ENGINE MyISAM';

	private $checksums = array();

	/*

	// @todo: use the class constants instead of global variables.

	const ERR_CODE_FIRST_RUN = 110845;
	const ERR_CODE_DB_OUTAGE = 190536;
	const ERR_CODE_UPDATE_CHECKSUM = 163545;
	const ERR_CODE_UNKOWN_FORMAT = 165736;
	const ERR_CODE_WRONG_IMPLEMENTATION = 134566;
	const ERR_CODE_FILE_ACCESS = 194378;
	const ERR_CODE_NO_IMPORTER = 189583;
	const ERR_CODE_CURL_INIT = 175439;
	const ERR_CODE_CURL_REQUEST = 175440;
	const ERR_CODE_WRONG_FILE = 175734;
	const ERROR_THRESHOLD = 1;
	const BINLOG_FREE_SPACE_WARNING = 100;
	const DUMP_FREE_SPACE_WARNING = 800;
	const UPDATES_STORAGE_FOLDER = '/var/tmp/';
	const SYNC_SSL_VERIFY_HOST = 2;
	const SEND_SYNC_WARNINGS = false;
	// The update handler on Secunia's side. This should not be altered unless specified.
	// @todo: will this work for the vim once we have the vim replication fileserver online.
	const REQUEST_LOCATION = 'https://sync.secunia.com/vim/4/';
	*/

	/**
	 * Constructor
	 */
	function __construct() {
		//Provide defaults for constant used in this class to make it fully independent
		//This also serves as reference
		if ( !defined( 'ERR_CODE_FIRST_RUN' ) ) {
			define( 'ERR_CODE_FIRST_RUN', 110845 );
		}
		if ( !defined( 'ERR_CODE_DB_OUTAGE' ) ) {
			define( 'ERR_CODE_DB_OUTAGE', 190536 );
		}
		if ( !defined( 'ERR_CODE_UPDATE_CHECKSUM' ) ) {
			define( 'ERR_CODE_UPDATE_CHECKSUM', 163545 );
		}
		if ( !defined( 'ERR_CODE_UNKOWN_FORMAT' ) ) {
			define( 'ERR_CODE_UNKOWN_FORMAT', 165736 );
		}
		if ( !defined( 'ERR_CODE_WRONG_IMPLEMENTATION' ) ) {
			define( 'ERR_CODE_WRONG_IMPLEMENTATION', 134566 );
		}
		if ( !defined( 'ERR_CODE_FILE_ACCESS' ) ) {
			define( 'ERR_CODE_FILE_ACCESS', 194378 );
		}
		if ( !defined( 'ERR_CODE_NO_IMPORTER' ) ) {
			define( 'ERR_CODE_NO_IMPORTER', 189583 );
		}
		if ( !defined( 'ERR_CODE_CURL_INIT' ) ) {
			define( 'ERR_CODE_CURL_INIT', 175439 );
		}
		if ( !defined( 'ERR_CODE_CURL_REQUEST' ) ) {
			define( 'ERR_CODE_CURL_REQUEST', 175440 );
		}
		if ( !defined( 'ERR_CODE_WRONG_FILE' ) ) {
			define( 'ERR_CODE_WRONG_FILE', 175734 );
		}
		if ( !defined( 'ERR_CODE_BUSY_PROCESS' ) ) {
			define( 'ERR_CODE_BUSY_PROCESS', 1534534 );
		}
		if ( !defined( 'ERROR_THRESHOLD' ) ) {
			define( 'ERROR_THRESHOLD', 1 );
		}
		if ( !defined( 'BINLOG_FREE_SPACE_WARNING' ) ) {
			define( 'BINLOG_FREE_SPACE_WARNING', 100 );
		}
		if ( !defined( 'DUMP_FREE_SPACE_WARNING' ) ) {
			define( 'DUMP_FREE_SPACE_WARNING', 800 );
		}
		if ( !defined( 'UPDATES_STORAGE_FOLDER' ) ) {
			define( 'UPDATES_STORAGE_FOLDER', '/var/tmp/' );
		}
		if ( !defined( 'SYNC_SSL_VERIFY_HOST' ) ) {
			define( 'SYNC_SSL_VERIFY_HOST', 2 );
		}
		if ( !defined( 'SEND_SYNC_WARNINGS' ) ) {
			define( 'SEND_SYNC_WARNINGS', false );
		}
		// The update handler on Secunia's side. This should not be altered unless specified.
		if ( !defined( 'REQUEST_LOCATION' ) ) {
			define( 'REQUEST_LOCATION', 'https://sync.secunia.com/csi/7/' );
		}
		if ( defined('DB_PAAS') && DB_PAAS == 1) {
			$this->storageEngine = '';
		}

		$this->storageFolder = UPDATES_STORAGE_FOLDER;
		if ( !is_dir( $this->storageFolder ) ) {
			$this->storageFolder = dirname( __FILE__ ) . '/';
		}
	}

	/**
	 * Dependency Injection setter
	 * Sets the object to be used to issue commands
	 * to the database importing the updates
	 * @param databaseImport $importer the object must implement the databaseImport interface
	 */
	public function setDatabaseImporter( databaseImporter $importer ) {
		if ( !( $importer instanceof databaseImporter ) ) {
			throw new Exception( 'An implementation of the databaseImporter interface must be provided.', ERR_CODE_WRONG_IMPLEMENTATION );
		} else {
			$this->importer = $importer;
		}
	}

	/**
	 * Dependency Injection setter
	 * Sets the object to be used to query the database.
	 * It is not dependent of an interface implementation
	 * @param object $db the object must provide the methods to query the db
	 */
	public function setDatabaseHandler( $db ) {
		$this->db = $db;
	}

	/**
	 * Sets the connection parameters for the database where we insert data
	 * @param string $user
	 * @param string $password
	 * @param string $database
	 * @param int $port
	 */
	public function setConnection( $host, $user, $password, $database, $port) {
		if ( $this->importer instanceof databaseImporter ) {
			$this->importer->host = $host;
			$this->importer->user = $user;
			$this->importer->password = $password;
			$this->importer->database = $database;
			$this->importer->port = $port;
		} else {
			throw new Exception( 'No importer has been set up yet.', ERR_CODE_NO_IMPORTER );
		}
	}

	/**
	 * Because the secunia packages are big, use this setting
	 * to alter the mysql setting
	 * @param int $val size in bytes
	 */
	private function setMaxAllowedPacket( $val ) {
		$query = "SET max_allowed_packet = " . (int) $val. " ";
	}

	/**
	 * Make a request to Secunia for a binlog and
	 * if there is a new update, try to import it
	 */
	public function importBinlog() {
		$this->log( "Trying to import binlog...\n" );
		$df = (int) ( disk_free_space( $this->storageFolder ) / ( 1024 * 1024 ) );
		$this->log( "\tFree space: " . $df . " Mb\n" );
		if ( $df < BINLOG_FREE_SPACE_WARNING && defined( 'SYNC_MAIL' ) && $GLOBALS['util']->validateEmail( SYNC_MAIL ) ) {
			mail( SYNC_MAIL, '[SVM ' . CST_ID . ']Client Side warning - Disk space warning', 'There are less then ' . BINLOG_FREE_SPACE_WARNING . ' Mb free disk space available for the binlogs to import', 'From: ' . NO_REPLY_EMAIL );
		}
		$this->upToDate = true;
		//Get the current status of the sync from the database
		$lastUpdate = $this->getLastUpdate();
		//if we do not find the expected data just force a dump update
		if ( $lastUpdate === '' ) {
			throw new Exception( 'There is no registered last update in the replication_metadata database. This might happen because this is the first run of the cron or the data in the `update_status` has been altered. A new dump import will fix this problem.', ERR_CODE_FIRST_RUN );
		} else if ( !in_array( $lastUpdate->lastUpdateType, $this->updateTypes ) ) {
			throw new Exception( 'The data in the database is in an unknown format, the values for the updates types are in an unrecognized format', ERR_CODE_UNKOWN_FORMAT );
		} else if ( $lastUpdate === false ) {
			throw new Exception( 'There was a problem while trying to fetch update information from the database. The database does not seem to be working correctly.', ERR_CODE_DB_OUTAGE );
		}
		//Make the request sending the last succesful update
		$postdata = array(
			'actionType' => 'getLatestBinlog'
			,'lastUpdateId' =>(int) $lastUpdate->lastUpdateId
			,'lastUpdateType' => $lastUpdate->lastUpdateType
		);
		$this->makeFileRequest( $postdata );
		//If a file was succesfuly received then it must be imported
		if ( strlen( $this->fileName ) > 0 ) {
			if ( $this->updateType === 'BINLOG' ) {
				$this->log( "\tExecuting binlog import\n" );
				$this->importer->executeBinlog( $this->storageFolder . $this->fileName );
				$this->setLastUpdate( 'BINLOG', $this->updateId );
				$this->removeUpdate( $this->storageFolder . $this->fileName );
				$this->checkImportedData();
				$this->log( "\tImport finished\n" );
			} else if ( $this->updateType === 'DUMP' ) {
				//A server request for a clean update
				$this->log( "\tExecuting dump import\n" );
				$this->importer->executeDump( $this->storageFolder . $this->fileName );
				$this->setLastUpdate( 'DUMP', $this->updateId );
				$this->removeUpdate( $this->storageFolder . $this->fileName );
				$this->log( "\tImport finished\n" );
			} else {
				throw new Exception( 'The update type of the file is unexpected.', ERR_CODE_WRONG_FILE );
			}
		}
	}

	/**
	 * This function will request the latest dump
	 * and after an succesful retrive event
	 * will try to process it
	 */
	public function importDump() {
		$this->log( "Trying to import dump...\n" );
		$df = (int) ( disk_free_space( $this->storageFolder ) / ( 1024 * 1024 ) );
		$this->log( "\tFree space: " . $df . " Mb\n" );
		if ( $df < DUMP_FREE_SPACE_WARNING && defined( 'SYNC_MAIL' ) && $GLOBALS['util']->validateEmail( SYNC_MAIL ) ) {
			mail( SYNC_MAIL, '[SVM ' . CST_ID . ']Client Side warning - Disk space warning', 'There are less then ' . DUMP_FREE_SPACE_WARNING . ' Mb free disk space available for the dumps to import', 'From: ' . NO_REPLY_EMAIL );
		}
		$this->upToDate = true;
		//Don't need to know which dump to ask for, it will always be the last available one
		//Make the request sending the last succesful update
		$postdata = array(
			'actionType' => 'getLatestDump'
		);
		$this->makeFileRequest( $postdata );
		//If a file was succesfuly received then it must be imported
		if ( strlen( $this->fileName ) > 0) {
			if ( $this->updateType === 'DUMP' ) {
				$this->log( "\tExecuting dump import\n" );
				$this->importer->executeDump( $this->storageFolder . $this->fileName );
				$this->setLastUpdate( 'DUMP', $this->updateId );
				$this->removeUpdate( $this->storageFolder . $this->fileName );
				$this->log( "\tImport finished\n" );
			} else {
				throw new Exception( 'Was expecting a dump file but received a binlog.', ERR_CODE_WRONG_FILE );
			}
		} else {
			return false;
		}
	}

	/**
	 * This function check weather the db needs to import a dump
	 * or it is synchronised and needs the next binlog
	 * @return {boolean} true if it needs a dump update
	 */
	public function needsDump() {
		$query = "
			SELECT
				`value` AS nextUpdate
			FROM
				update_status
			WHERE
				`name` = 'NEXT_UPDATE'
			LIMIT 1
		";
		$needsDump = $this->db->queryGetRow( $query );
		if (
			is_array ( $needsDump )
			&& $needsDump['nextUpdate'] === 'BINLOG'
		) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * This will mark what type of update does the cron have on next run
	 * @param string a valid update type
	 */
	public function setNextUpdateType( $type ) {
		if ( !in_array( $type, $this->updateTypes) ) {
			return false;
		}
		$this->log( "\tSetting next update to " . $type . "\n" );
		$nextUpdate = $this->db->getRow( "update_status", "`name`='NEXT_UPDATE'", 'id ASC' );
		if ( !empty($nextUpdate) && count( $nextUpdate ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = '" . $this->db->escapeString( $type ) . "'
				WHERE
					`name` = 'NEXT_UPDATE'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'NEXT_UPDATE'
						,'" . $this->db->escapeString( $type ) . "'
					)
			";
		}
		$this->db->query( $query ) ;
	}

	/**
	 * This function will return a reference that when sent to Secunia
	 * will be used to determine the next update to be applied
	 * @return {object} with two properties: lastUpdateId and lastupdateType
	 */
	private function setLastUpdate( $type, $id) {
		if ( !in_array( $type, $this->updateTypes ) ) {
			return false;
		}
		$lastUpdateId = $this->db->getRow( "update_status", "`name`='LAST_UPDATE_ID'", 'id ASC' );
		if ( !empty($lastUpdateId) && count( $lastUpdateId ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = '" . (int)$id . "'
				WHERE
					`name` = 'LAST_UPDATE_ID'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'LAST_UPDATE_ID'
						,'" . (int)$id . "'
					)
			";
		}
		$this->db->query( $query ) ;
		$lastUpdateTye = $this->db->getRow( "update_status", "`name`='LAST_UPDATE_TYPE'", 'id ASC' );
		if ( !empty($lastUpdateTye) && count( $lastUpdateTye ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = '" . $this->db->escapeString( $type ) . "'
				WHERE
					`name` = 'LAST_UPDATE_TYPE'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'LAST_UPDATE_TYPE'
						,'" . $this->db->escapeString( $type ) . "'
					)
			";
		}
		$this->db->query( $query ) ;
	}

	/**
	 * This function will return a reference that when sent to Secunia
	 * will be used to determine the next update to be applied
	 * @return {object} with two properties: lastUpdateId and lastupdateType
	 */
	private function getLastUpdate() {
		$query = "
			SELECT
				update_status.`value` AS lastUpdateId
				,update_status1.`value` AS lastUpdateType
			FROM
				update_status
			INNER JOIN update_status as update_status1
				ON update_status1.`name` = 'LAST_UPDATE_TYPE'
			WHERE
				update_status.`name` = 'LAST_UPDATE_ID'
			LIMIT 1
		";
		$lastUpdate = $this->db->queryGetRow( $query );
		if (
			is_array ( $lastUpdate )
			&& is_numeric( $lastUpdate['lastUpdateId'] )
			&& in_array( $lastUpdate['lastUpdateType'], $this->updateTypes )
		) {
			return (object) array(
				'lastUpdateId' => (int)$lastUpdate['lastUpdateId']
				,'lastUpdateType' => $lastUpdate['lastUpdateType']
			);
		} else {
			return '';
		}
	}

	/**
	 * This function checks the counts on the tables and reports back if
	 * the difference is bigger then a specified threshold
	 */
	private function checkImportedData() {
		$msg = "";
		foreach ( (array)$this->checksums as $tableInfo ) {
			$query = "SHOW TABLES IN vuln_track LIKE '" . $this->db->escapeString( $tableInfo['table'] ) . "'";
			$_temp = $this->db->queryGetRows( $query );
			if ( count( $_temp ) > 0 ) {
				$query = "SHOW COLUMNS FROM `vuln_track`.`" . $this->db->escapeString( $tableInfo['table'] ) . "`";
				$_temp = $this->db->queryGetRows( $query );
				foreach ( $_temp as $columnDetails ) {
					if ( $columnDetails['Field'] === $tableInfo['pk'] ) {
						$num = $this->db->numRows( "`vuln_track`.`" . $tableInfo['table'] . "`", "`" . $this->db->escapeString( $tableInfo['pk'] ) . "` <= " . (int)$tableInfo['last_id'] );
						if (
							!( (int)$tableInfo['check'] === (int)$num ) && (
								(int)$tableInfo['check'] === 0
								|| $num > (int)$tableInfo['check']
								|| ( (int)$tableInfo['check'] - $num ) / (int)$tableInfo['check'] * 100 > ERROR_THRESHOLD
							)
						) {
							$msg .= "The table " . $tableInfo['table'] . " has " . (int)$num. " as oposed to the expected " . (int)$tableInfo['check'] . ".\n";
						}
						continue 2;
					}
				}
				$msg .= "The column " . $tableInfo['pk'] . " in the table " . $tableInfo['table'] . " does not exist although it is expected.\n";
			} else {
				$msg .= "The table " . $tableInfo['table'] . " does not exist on the client side although it was expected.\n";
				continue;
			}
		}
		if ( strlen( $msg ) > 0 && defined( 'SYNC_MAIL' ) && $GLOBALS['util']->validateEmail( SYNC_MAIL ) && SEND_SYNC_WARNINGS === true ) {
			mail( SYNC_MAIL, '[SVM ' . CST_ID . ']Client Side exception - Validity inconsistency', $msg, 'From: ' . NO_REPLY_EMAIL );
		}
	}

	/**
	 * This function is used to read the posted data in the headers
	 * @param object $ch curl object
	 * @param string $headerLine the individual line parsed
	 */
	private function headerCallback( $ch, $headerLine ) {
		$headerPair = explode( ':', $headerLine, 3 );
		$patternCustom = '/^<(.*)>$/';
		$patternFilename = '/^attachment; filename=(.*)/i';
		$matches = array();
		if (
			strtolower( trim( $headerPair[0] ) ) === 'update-id'
			&& preg_match( $patternCustom, trim( $headerPair[1] ), $matches )
		) {
			$this->updateId = $matches[1];
		} else if (
			strtolower( trim( $headerPair[0] ) ) === 'file-checksum'
			&& preg_match( $patternCustom, trim( $headerPair[1] ), $matches )
		) {
			$this->fileChecksum = $matches[1];
		} else if (
			strtolower( trim( $headerPair[0] ) ) === 'update-type'
			&& preg_match( $patternCustom, trim( $headerPair[1] ), $matches )
			&& in_array( $matches[1], $this->updateTypes )
		) {
			$this->updateType = $matches[1];
		} else if (
			strtolower( trim( $headerPair[0] ) ) === 'db-checksums'
			&& preg_match( $patternCustom, trim( $headerPair[1] ), $matches )
		) {
			$arr = explode( '#', $matches[1] );
			if ( is_array( $arr ) ) {
				foreach ( $arr as $tableData ) {
					$tableDetails = explode( "|", $tableData );
					if ( is_array( $tableDetails ) && count( $tableDetails ) === 4 ) {
						$this->checksums[] = array(
							'table' => $tableDetails[0]
							,'pk' => $tableDetails[1]
							,'check' => $tableDetails[2]
							,'last_id' => $tableDetails[3]
						);
					}
				}
			}
		} else if (
			strtolower( trim( $headerPair[0] ) ) === 'last-available-update'
			&& preg_match( $patternCustom, trim( $headerPair[1] ), $matches )
		) {
			if ( $matches[1] === 'FALSE' ) {
				$this->upToDate = false;
			} else {
				$this->upToDate = true;
			}
		} else if (
			strtolower( trim( $headerPair[0] ) ) === 'content-disposition'
			&& preg_match( $patternFilename, trim( $headerPair[1] ), $matches )
		) {
			$this->renameToFileName = basename( $matches[1] );
		}
		return strlen( $headerLine );
	}

	/**
	 * This is used specifically for retriving streams of updates from secunia
	 * @param array $postdata key value pairs with the POST params
	 */
	private function makeFileRequest( $postdata = array() ) {
		$this->log( "\tMaking file request\n" );
		//Reset
		$this->updateId = '';
		$this->updateType = '';
		$this->fileChecksum = '';
		$this->renameToFileName = '';
		$this->checksums = array();

		$ch = curl_init();
		if ( $ch === false ) {
			//This is fatal
			throw new Exception( 'There was a problem creating the curl object Importing will not continue', ERR_CODE_CURL_INIT );
		}

		$url = REQUEST_LOCATION . '?CST_ID=' . urlencode( CST_ID ) .'&action=server_sync';

		//A file handler is needed to accept the
		//possible file. The file is treated like a stream
		//so it does not take memory

		$this->fileName = 'secunia_' . time() . '.update';
		$fh = fopen( $this->storageFolder . $this->fileName, 'w+' );

		if ( $fh === false ) {
			//This is fatal
			throw new Exception( 'There was a problem creating the file receiving the update. Importing will not continue', ERR_CODE_FILE_ACCESS );
		}

		curl_setopt( $ch, CURLOPT_URL, $url );
		curl_setopt( $ch, CURLOPT_FILE, $fh );
		curl_setopt( $ch, CURLOPT_HEADER, false );
		curl_setopt( $ch, CURLOPT_HEADERFUNCTION, array( $this, 'headerCallback' ) );
		$sslVerifyHost = 2;
		switch ( SYNC_SSL_VERIFY_HOST ) {
			case 0:
			case 1;
				$sslVerifyHost = (int)SYNC_SSL_VERIFY_HOST;
				break;
			default:
				$sslVerifyHost = 2;
				break;
		}
		curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, $sslVerifyHost );

		// Proxy host/port
		if ( defined( 'PROXY_HOST' ) && strlen( PROXY_HOST ) && defined( 'PROXY_PORT' ) && strlen( PROXY_PORT ) ) {
			curl_setopt( $ch, CURLOPT_PROXY, PROXY_HOST . ':' . PROXY_PORT );
		}

		// Proxy credentials
		if( defined( 'PROXY_USERNAME' ) && strlen( PROXY_USERNAME ) && defined( 'PROXY_PASSWORD' ) && strlen( PROXY_PASSWORD ) ) {
			curl_setopt( $ch, CURLOPT_PROXYUSERPWD, PROXY_USERNAME . ":" . UTIL::decryptConfigConstant(constant('PROXY_PASSWORD')) );
		}

		// Post data?
		if( !empty( $postdata ) && is_array( $postdata ) ) {
			if ( !isset( $postdata[ 'version' ] ) ) {
				$postdata[ 'version' ] = CSI_VERSION;
			}
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
		}

		$content = curl_exec( $ch );

		$exception = '';
		if ( $content === false ) {
			$exception = 'There was a problem with the curl request. Error no ' . curl_errno( $ch ) . ' : ' . curl_error( $ch );
		}

		curl_close($ch);
		fclose( $fh );

		if ( $content === false ) {
			throw new Exception( $exception, ERR_CODE_CURL_REQUEST );
		}

		//If we have a file checksum than we must check if it corresponds to the file
		if ( strlen ( $this->fileChecksum ) > 0 ) {
			if ( sha1_file( $this->storageFolder . $this->fileName ) !== $this->fileChecksum ) {
				$this->log( "\tReceived file is corrupt\n" );
				//The file is removed if it is a dump as it takes too much space
				if ( $this->updateType === 'DUMP' ) {
					unlink( $this->storageFolder . $this->fileName );
				}
				//The file is corrupt and import will not continue
				throw new Exception( 'The file update was received but did not pass the checksum validation. Refrence id is ' . $this->updateId . ', filename is ' . $this->fileName . '.', ERR_CODE_UPDATE_CHECKSUM );
			}
			$matches = array();
			preg_match( '/^(.*)\.zip$/', $this->renameToFileName, $matches );

			//This is the name of the file that should be in the zip
			$updateFile = $matches[1];

			chdir( $this->storageFolder );
			exec( "/usr/bin/unzip -o ".escapeshellarg( $this->fileName ). " ". escapeshellarg( $updateFile ) );
			chdir( dirname( __FILE__ ) );

			if ( file_exists( $this->storageFolder . $this->fileName ) ) {
				unlink( $this->storageFolder . $this->fileName );
			}

			$this->fileName = $updateFile;

			$this->log( "\tReceived file is ready for import\n" );
		} else {
			$this->log( "\tNo file received\n" );

			unlink( $this->storageFolder . $this->fileName );

			$this->updateId = '';
			$this->updateType = '';
			$this->fileChecksum = '';
			$this->fileName = '';
			$this->renameToFileName = '';
			$this->upToDate = true;
			$this->checksums = array();
		}
	}

	/**
	 * Method used for removing successful updates
	 * @param string $path the full path of the update to be deleted
	 */
	private function removeUpdate( $path ) {
		if ( file_exists( $path ) ) {
			unlink( $path );
		}
	}

	/**
	 * just a simple function to log behaviour for debugging
	 * must be enabled from the verbose flag
	 * @param string $str string to write to the error log
	 */
	public function log( $str ) {
		if ( $this->verbose ) {
			print "[" . date("Y-m-d H:i:s"). "]" . $str ;
		}
	}

	/**
	 * Method that is used to mark that an import has started
	 */
	public function makeBusyProcess() {
		$query = "
			INSERT INTO
				sync (
					id
				) VALUES (
					1
				)
		";
		if ( $this->db->query( $query ) ) {
			$process = $this->db->getRow( "update_status", "`name`='RUNNING_PROCESS'", 'id ASC' );
			if ( !empty($process) && count( $process ) > 0 ) {
				$query = "
					UPDATE
						update_status
					SET
						`value` = NOW()
					WHERE
						`name` = 'RUNNING_PROCESS'
				";
			} else {
				$query = "
					INSERT INTO
						update_status (
							`name`
							,`value`
						) VALUES (
							'RUNNING_PROCESS'
							,NOW()
						)
				";
			}
			$this->db->query( $query ) ;
			return true;
		} else {
			return false;
		}
	}

	public function releaseLock() {
		$query = "
			DELETE FROM
				sync
			WHERE
				id = 1
		";
		$this->db->query( $query ) ;
	}

	/**
	 * Method that is used to mark that an import has finished
	 */
	public function freeProcess() {
		$process = $this->db->getRow( "update_status", "`name`='RUNNING_PROCESS'", 'id ASC' );
		if ( !empty($process) && count( $process ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = 0
				WHERE
					`name` = 'RUNNING_PROCESS'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'RUNNING_PROCESS'
						,0
					)
			";
		}
		$this->db->query( $query ) ;
	}

	/**
	 * Method that is used to find out if there is a running process
	 * @return boolean true is there's a running process, false otherwise
	 */
	public function isBusyProcess() {
		$process = $this->db->getRow( "update_status", "`name`='RUNNING_PROCESS'", 'id ASC' );
		if ( !empty($process) && count( $process ) > 0 && $process['value'] !== '0') {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Method that is used to find out since when a process has been running
	 */
	public function busyProcess() {
		if ( $this->isBusyProcess() ) {
			$process = $this->db->getRow( "update_status", "`name`='RUNNING_PROCESS'", 'id ASC' );
			return strtotime( $process['value'] );
		} else {
			return false;
		}
	}

	/**
	 * After the first run this function should only select the right db
	 * On the first run it prepares the environment
	 */
	public function prepareDb() {
		$query = "CREATE DATABASE IF NOT EXISTS vuln_track;";
		$this->db->query( $query );
		$query = "CREATE DATABASE IF NOT EXISTS replication_metadata;";
		$this->db->query( $query );
		$this->db->selectDB( 'replication_metadata' );
		$query = "CREATE TABLE IF NOT EXISTS replication_metadata.update_status (id int(11) NOT NULL AUTO_INCREMENT, `name` varchar(255) NOT NULL, `value` varchar(255) NOT NULL, PRIMARY KEY (id))";
		$this->db->query( $query );
		$query = "CREATE TABLE IF NOT EXISTS replication_metadata.sync (id int(11) NOT NULL, PRIMARY KEY (id))" . $this->storageEngine;
		$this->db->query( $query );
	}

	/**
	 * Get MySQL time
	 * Because of possible differences between the mysql and the system clock
	 * @return time
	 */
	public function getTime() {
		$query = "SELECT NOW() AS `date`";
		$date = $this->db->queryGetRow( $query );
		return strtotime( $date['date'] );
	}

	public function getErrors(){
		$query = "
			SELECT
				`value` AS errors
			FROM
				update_status
			WHERE
				`name` = 'ERROR_FLAGS'
			LIMIT 1
		";
		$errorRow = $this->db->queryGetRow( $query );
		if (
			is_array ( $errorRow )
			&& is_numeric($errorRow['errors'])
		) {
			return (int)$errorRow['errors'];
		} else {
			return 0;
		}
	}

	public function clearErrors(){
		$this->insertErrors(0);
	}

	public function insertErrors($errors){
		$errorRow = $this->db->getRow( "update_status", "`name`='ERROR_FLAGS'", 'id ASC' );
		if ( !empty($errorRow) && count( $errorRow ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = '" . (int)$errors . "'
				WHERE
					`name` = 'ERROR_FLAGS'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'ERROR_FLAGS'
						,'" . (int)$errors . "'
					)
			";
		}
		$this->db->query( $query ) ;
	}

	public function updateVTSyncTimestamp() {
		$process = $this->db->getRow( "update_status", "`name`='LAST_SYNC'", 'id ASC' );
		if ( !empty($process) && count( $process ) > 0 ) {
			$query = "
				UPDATE
					update_status
				SET
					`value` = NOW()
				WHERE
					`name` = 'LAST_SYNC'
			";
		} else {
			$query = "
				INSERT INTO
					update_status (
						`name`
						,`value`
					) VALUES (
						'LAST_SYNC'
						,NOW()
					)
			";
		}
		$this->db->query( $query ) ;
	}
}

?>
