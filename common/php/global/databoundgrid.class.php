<?php
/**
 * @deprecated
 *   Use Grid class instead that works with the new db layer
 *   The Grid class also provides some compatibility functions (the UI doesn't need to
 *   change in most cases to use it.)
 *
 * @file databoundgrid.class.php
 * Provides data for the databoundgrid.js object.
*/
class DATABOUNDGRID {

	protected $db;
	protected $database = array();
	protected $sortColumn = "";
	protected $sortDirection = "";
	protected $groupColumn = "";
	protected $groupDirection = "";

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
		$this->database['crm'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
		$this->db = new DB( DB_HOST_CA, 'ca', DB_USER_CA, DB_PASS_CA, false. DB_PORT );
	}

	/**
	 * Public function for constructing the search filter.
	 * @param filter
	 *	String filter to apply against the advisory list, having comma separated values of type=value
	 * @return
	 *	Array of filter value, ready to be inserted in the SQL query
	*/
	public function constructFilter( $filter, $allowed, $isEscaped = false ) {
		$final = array();
		$filterArray = explode( ",", $filter );
		for ( $i = 0; $i < count( $filterArray ); $i++ ) {
			$filterKey = explode( "=", $filterArray[$i] );
			if ( isset( $allowed[$filterKey[0]] ) && $allowed[$filterKey[0]] === true ) {
				if ( $filterKey[1] != "" ) {
					if ( $isEscaped !== true ) {
						$final[$filterKey[0]] = $this->database['ca']->escapeString( $filterKey[1] );
					} else {
						$final[$filterKey[0]] = $filterKey[1];
					}
				}
			}
		}

		return $final;
	}

	/**
	 * Function for verifying the sort parameters.
	 * @param direction
	 *	String direction
	 * @param column
	 *	String column
	 * @return
	 *	Boolean. If not legit, return false else true or alias column name.
	 */
	function verifySort( $direction, $column ) {
		$ok = false;
		$allowed = array(
						 "ignored_programs"=>true
						 ,"no_zombie"=>true
						 ,"no_total"=>true
						 ,"no_patched"=>true
						 ,"no_eol"=>true
						 ,"no_insecure"=>true
						 ,"status_date"=>true
						 ,"scan_type"=>true
						 ,"short_msg"=>true
						 ,"vuln_title" => true
						 ,"critical_type_name" => true
						 ,"policy_text" => true
						 ,"prio_text" => true
						 ,"vuln_critical_boolean" => true
						 ,"vuln_solution_status" => true
						 ,"vuln_id" => true
						 ,"asset_name" => true
						 ,"last_status" => true
						 ,"changed" => true
						 ,"where_type_name" => true
						 ,"vuln_create_date" => true
						 ,"affected" => true
						 ,"no_total" => true
						 ,"host" => true
						 ,"name" => true
						 ,"os_soft_id" => true
						 ,"os_soft_name" => true
						 ,"vendor_name" => true
						 ,"value" => true
						 ,"os_soft_version" => true
						 ,"os_soft_type" => true
						 ,"vuln_modified_date" => true
						 ,"exploit_count" => true
						 ,"policy" => true
						 ,"added" => true
						 ,"username" => true
						 ,"inp" => true
						 ,"status" => true
						 ,"account_username" => true
						 ,"sss" => true
						 ,"dcss" => true
						 ,"lss" => true
						 ,"prio" => true
						 ,"process_started" => true
						 ,"queue_at" => true
						 ,"target" => true
						 ,"script_name" => true
						 ,"port" => true
						 ,"port_type" => true
						 ,"status" => true
						 ,"changed" => true
						 ,"previous_status" => true
						 ,"comment" => true
						 ,"added_text" => true
						 ,"vuln_cvss_score" => true
						 ,"modified_date" => true
						 ,"last_gen_date" => true
						 ,"report_title" => true
						 ,"topic" => true
						 ,"reftype" => true
						 ,"authorName" => "author"
						 ,"lastAuthorName" => "lastauthor"
						 ,"posts" => true
						 ,"views" => true
						 ,"votes" => true
						 ,"posted" => true
						 ,"lastupdate" => true
						 ,"fail_compliance" => true
						 ,"rule_name" => true
						 ,"created_or_modified" => true
						 ,'next_scheduled' => true
						 ,'email_recipients' => true
						 ,'sms_recipients' => true
						 ,'notify_always' => true
						 ,'frequency' => true
						 ,'smartgroup_name' => 'smartgroup_id' // Note - means we sort by id, not alphabetically, but this is better than nothing.
		);

		if ( strtolower( $direction ) == "asc" || strtolower( $direction ) == "desc" ) {
			$ok = true;
		}

		if ( $ok !== true ) {
			return false;
		}

		if ( isset($allowed[$column]) ) {
			return $allowed[$column];
		}

		return false;
	}


	/**
	 * Gets appropiate sorting strings from RPOST input. It uses the following
	 * priority if multiple sort information is set.
	 *
	 *  RPOST('sorters')
	 *  RPOST('sort') && RPOST('dir')
	 *
	 * @todo add sorting logic for RPOST('groupby') ie. constructSort( true )
	 *  functionality.
	 *
	 * @param array $allowedSortFields must contain an array of allowed sorting fields.
	 *  where the key must be the allowed sort field name and the value eihter 'true' or
	 *  an alias fieldname. fx array( 'fieldname'=>true, 'fieldname2'=>'aliasfieldname' )
	 *  if an aliasfieldname is specified as in example above and fieldname2 is
     *  recieved from server then alieasfieldname will be returned instead of fieldname2
	 *
	 * @return array sorters
	 *  ie ( fieldname => direction, aliasfieldname => direction2 )
	 */

	public static function constructSorters( array $allowedSortFields = array() ){
		$sorters = array();

		// we have to check here whether $_POST['parameter'] isset as
		// INPUT::RPOST('parameter') will generate php error message
		// if trying to acces a unset key.

		if ( isset($_POST['sorters']) ){

			$encodedSorters = INPUT::RPOST('sorters');

			// $encodedSorters should contain string like:
			// "fieldname=direction,fieldname2=direction"
			$sorterStrings = explode( "," , $encodedSorters );

			foreach( $sorterStrings as $string ){
				list( $field , $direction ) = explode('=', $string, 2);

				//must contain true or alias field name to sort by.
				$fieldAllowed = $allowedSortFields[ $field ];

				if( $fieldAllowed && ($direction ==='ASC' || $direction === 'DESC') ){

				// Then we either have 'true' or we have the alias field to actually sort by
					if ( $fieldAllowed !== true ) {
						$field = $fieldAllowed;
					}

					$sorters[ $field ] = $direction;
				}

			}

			return $sorters;

		} elseif ( isset($_POST['sort']) && isset($_POST['dir']) ){//single sorting default ext params
			$sort = INPUT::RPOST('sort');
			$dir = INPUT::RPOST('dir');

			$fieldAllowed = $allowedSortFields[ $sort ];

			if ( $fieldAllowed && ( $dir === 'ASC' || $dir === 'DESC' ) ){
				// Then we either have 'true' or we have the alias column to actually sort by
				if ( $fieldAllowed !== true ) {
					$sort = $fieldAllowed;
				}

				$sorters[ $sort ] = $dir;
			}

			return $sorters;
		}

		return $sorters;
	}

	/**
	 * @deprecated constructs the order by clause.
	 *  should only be used in conjuction with old database class
	 *  use DATABOUNDGRID->constructSorters() and query->buildOrder
	 *  with new database class instead.
	 *
	 * @return string If no sorters available it will return 'ORDER BY NULL' and
     *  otherwise 'ORDER BY field1 dir1, field2 dir2' etc.
	 *
	 */

	public static function constructOrderByClause( array $allowedSortFields = array() ){
		$sorters = array();
		$sorters = DATABOUNDGRID::constructSorters( $allowedSortFields );
		$orderBy = '';

		foreach( $sorters as $field => $direction ){

			if( $orderBy !== ''){
				$orderBy .= ', ';
			}
			$orderBy .= $field . ' ' . $direction;
		}

		return ( $orderBy ==='' ?  ' ORDER BY NULL ' : ' ORDER BY ' . $orderBy . ' ' );
	}


	/**
	 * Function for building the proper SQL in sintax
	 * @param array
	 *	Array of items to be added in the SQL IN sintax
	 * @param key
	 *	String name of the key from within the array that will be added
	 * @return
	 *	String SQL IN sintax E.g.: 'a', 'b'. No brackets!
	*/
	protected function buildSqlIn( $array, $key ) {
		$sqlIn = "";
		for ( $i = 0; $i < count( $array ); $i++ ) {
			if ( $i != 0 ) {
				$sqlIn .= ",";
			}
			$sqlIn .= "'".$array[$i][$key]."'";
		}
		return $sqlIn;
	}

	/**
	 * @deprecated not compatible with secuniaSorting and new database class use
	 * constructSorters() with new database class and constructOrderByClause
	 * with old databse class.
	 *
	 * Function for building sort direction, if any.
	 * @param grouping
	 *	Boolean GridGrouping sort, default to false
	 * @return
	 *	Sort direction and field
	*/
	function constructSort( $grouping = false ) {
		$sort = "";
		$column = $grouping == false ? $GLOBALS['input']->MPOST('sort') : $GLOBALS['input']->MPOST('groupBy');
		$direction = $grouping == false ? $GLOBALS['input']->MPOST('dir') : $GLOBALS['input']->MPOST('groupDir');

		if ( $column != "" && $direction != "" ) {

			$verification = $this->verifySort( $direction, $column );

			if ( $verification  ) {
				// Then we either have 'true' or we have the alias column to actually sort by
				if ( true === $verification ) {
					$sortingBy = $column;
				} else {
					$sortingBy = $verification;
				}

				$sort = $sortingBy . " " . $direction;
				if ( $grouping == false ) {
					$this->sortDirection = $direction;
					$this->sortColumn = $sortingBy;
				} else {
					if ( $this->sortColumn != "" && $this->sortColumn == $this->groupColumn &&
						 $this->sortDirection != "" && $this->sortDirection == $this->groupDir ) {
						return ""; // Sorting and grouping are identical
					}
				}
			}
		}
		return $sort;
	}

	/**
	 * Function for constructing the paging limits, if any
	 * @return
	 *	String limit for SQL query
	*/
	function constructLimit() {
		$limit = "";

		if ( is_numeric( $GLOBALS['input']->MGET('pageSize') ) && ( $GLOBALS['input']->MGET('paging') == "true" ) || ( is_numeric( $GLOBALS['input']->MPOST('start') ) && is_numeric( $GLOBALS['input']->MPOST('limit') ) ) ) {
			if ( is_numeric( $GLOBALS['input']->MPOST('start') ) && is_numeric( $GLOBALS['input']->MPOST('limit') ) ) {
				$limit = $GLOBALS['input']->MPOST('start').",".$GLOBALS['input']->MPOST('limit');
			} else if ( !INPUT::isCsvRequired() ) {
				$limit = '0,' . $GLOBALS['input']->MGET('pageSize');
			}
		}
		/*
		 * Ext JS 4 Compatibility
		 */
		else if ( is_numeric( $GLOBALS['input']->MGET('start') ) && is_numeric( $GLOBALS['input']->MGET('limit') ) ) {
			$limit = $GLOBALS['input']->MGET('start').",".$GLOBALS['input']->MGET('limit');
			// @todo: implementation for the isCsvRequired() case.
		}

		return $limit;
	}

	/**
	 * Fetch row data, when there is only one table.
	 * @return
	 *	Array grid data, containing object for json encode function
	*/
	private function fetchRawData( $table, $where, $order, $columns, $databaseObject ) {
		$gridData = "";
		$limit = $this->constructLimit();
		if ( $limit != "" ) {
			$gridData['total'] = $databaseObject->numRows( $table, $where );
		}

		$GLOBALS['debug']->notice( "fetchData" );
		$result = $databaseObject->getRows( $table, $where, $order, $limit, $columns );
		$gridData['data'] = $result;
		return $gridData;
	}

	/**
	 * Function for fetching,formatting and displaying grid data.
	 * If _GET['pageSize'] and 'paging' are set, it means we have a paging rid.
	 * @param columns
	 *	Array of columns to be selected from database
	 * @return
	 *	String json encoded data
	*/
	function fetchData( $table, $where, $order, $columns, $databaseObject ) {
		$gridData = $this->fetchRawData( $table, $where, $order, $columns, $databaseObject );
		return $GLOBALS['json']->json_encode(  $gridData );
	}
}
?>