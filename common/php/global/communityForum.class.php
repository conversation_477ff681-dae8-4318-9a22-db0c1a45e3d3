<?php
/**
 * @file reporting.class.php
 */
class communityForum extends DATABOUNDGRID {

	function __construct() {

		$this->database['community'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
		$this->database['psi'] = new DATABASE( PSI_DB_HOST, PSI_DB_USER, PSI_DB_PASS );

		$this->imagePath = 'gfx/community/';

		$this->userRankingThresholds = array(
			 array( 100, "Member", 1, "<img src='" . $this->imagePath . "secuniamember.gif' border='0' alt='Member'>" )
			 ,array( 500, "Contributor", 3, "<img src='" . $this->imagePath . "secuniacontributor.gif' border='0' alt='Contributor'>" )
			 ,array( 1500, "Dedicated Contributor", 5, "<img src='" . $this->imagePath . "secuniadedicatedcontributor.gif' border='0' alt='Dedicated Contributor'>" )
			 ,array( 1501, "Expert Contributor", 10, "<img src='" . $this->imagePath . "secuniaexpertcontributor.gif' border='0' alt='Expert Contributor'>" )
			 );
	}

	// return an array with two entries - a users profileId and email from the
	// community forum psi.profile DB table.
	private function getUserProfileData( $accountId ) {

		$result = $this->checkForumRegistration( $accountId, false );
		if ( count($result) ) {
			$email = $this->database['community']->escapeString( $result['email'] );
			$returnArray = array( 'id' => (int) $result['user_id']
								  ,'email' => $email );
			return $returnArray;
		}
		return false;
	}

	private function checkForumRegistration( $accountId, $returnJson = false ) {

		// Check if user has a community profile
		$userProfileData = array();

		$query = "SELECT community_profile_id FROM ca.nsi_base_settings WHERE community_profile_id IS NOT NULL AND account_id = '" . (int) $accountId . "'";

		$userInfo = $this->database['community']->queryGetRow( $query );
		if ( $userInfo && $userInfo['community_profile_id'] ) {
			$profileId = $userInfo['community_profile_id'];
			$query = "SELECT user_id, username, email, name, confirmed, confirm_string FROM psi.profile WHERE user_id = '" . (int) $profileId . "'";
			$userProfileData = $this->database['psi']->queryGetRow( $query );
		}

		if ( $returnJson ) {
			return $GLOBALS['json']->json_encode( $userProfileData );
		} else {
			return $userProfileData;
		}
	}


	// Calling with a subscribe value of 0 will unsubscribe, 1 means subscribe
	private function subscribeToThread( $accountId, $threadId = 0, $subscribe ) {

		// Find out if this user is already subscribing to this thread
		$userInfo = $this->getUserProfileData( $accountId );

		if ( !$userInfo || !isset( $userInfo['email'] ) || !$userInfo['email'] ) {
			$GLOBALS['debug']->error( "userInfo does not exist" );
			echo "{success: false, response: 1 }";
			return;
		}

		$userEmail = $userInfo['email'];

		if ( !$subscribe ) {
			// remove from DB
			$query = "DELETE FROM community.forum_subscriptions WHERE  email = '" . $this->database['community']->escapeString( $userEmail ) . "' AND threadid = '" . (int) $threadId . "'";
			$this->database['community']->query( $query );
		} else {
			$uniquestring = $GLOBALS[ 'prng' ]->generateRandomID( 30 );
			$query = "INSERT INTO
						community.forum_subscriptions
					SET
						threadid = '" . (int) $threadId . "'
						,email = '" . $this->database['community']->escapeString( $userEmail ) . "'
						,uniquestring = '" . $this->database['community']->escapeString( $uniquestring ) . "'";
			$this->database['community']->query( $query );
		}

		echo "{success: true, response: 0 }";
	}

	private function getForumThreadPosts( $accountId, $threadId = 0, $threadAuthorId ) {

		$response = array();
		$tableToUse = " community.forum_posts ";
		$whereClause = " threadid = '" . (int) $threadId . "' AND deleted = 0 ";
		$fields = " postid as id
				,topic
				,content
				,timestamp as posted
				,lastedit
				,author
				,post_negative
				,post_positive
				,attachment_id";

		$query = "SELECT " . $fields . " FROM " . $tableToUse . " WHERE " . $whereClause;
		$rows = $this->database['community']->queryGetRows( $query );
		$totalPosts = count( $rows );

		$response["metaData"] = array();
		$response["metaData"]['total'] = $totalPosts;
		$response["userData"] = array();

		// Get all the user data we will need for these posts
		$queryAuthors = "SELECT distinct author FROM " . $tableToUse . " WHERE " . $whereClause;
		$authorResultRows = $this->database['community']->queryGetRows( $queryAuthors );
		// Make an array out of these - add the thread author as well
		$userRows = array( (int) $threadAuthorId );
		foreach ( $authorResultRows as $thisRow ) {
			array_push( $userRows, (int) $thisRow['author'] );
		}

		// For each author in our list, get their metadata
		foreach ( $userRows as $thisAuthor ) {
			$authorString = 'authorId_' . $thisAuthor;
			$userInfo = $this->getUserInfo( $thisAuthor );
			if ( !isset($response["userData"][$authorString]) ) {
				$response["userData"][$authorString] = $userInfo;
			}
		}

		$userInfo = $this->getUserProfileData( $accountId );
		if ( !$userInfo ) {
			$userEmail = 'noData';
			$userId = 0;
		} else {
			$userEmail = $userInfo['email'];
			$userId = $userInfo['id'];
		}

		$exists = $this->database['community']->numRows( "community.forum_subscriptions", " email = '" . $this->database['community']->escapeString( $userEmail ) . "' AND threadid = '" . (int) $threadId . "'" );

		$response["metaData"]['subscribed'] = 0;
		if ( 0 < $exists ) {
			$response["metaData"]['subscribed'] = 1;
		}

		// We also want to flag each given post as 'editable by this user', i.e., if
		// this user wrote it, he can edit it.
		$modifiedRows = array();
		foreach ( $rows as $thisRow ) {
			$authorId = $thisRow['author'];
			$canEdit = false;
			if ( 0 < $userId && $authorId == $userId ) {
				$canEdit = true;
			}
			$thisRow['canEdit'] = $canEdit;
			// Add the modifed row to the result array which we will later return
			$modifiedRows[] = $thisRow;
		}

		// This will be our final returned structure
		$response["data"] = $modifiedRows;


		// Housekeeping:
		// Since we are opening up a thread view window, increment the number
		// of views this thread has. Note, this will also increment it on a refresh
		// like for example when you post a reply to the thread.
		//
		// Note: Can't use our database->edit call as it will put quotes around
		// the 'views+1' which evaluates to 0
		$query = "UPDATE community.forum_threads SET views = views+1 WHERE threadid='" . (int) $threadId . "'";
		$this->database['community']->query( $query );

		return $GLOBALS['json']->json_encode( $response );
	}


	// This gets the community forum overview grid - if we call it with a threadId specified, it only gets the one row - this is for the case where we just need an updated info for a single thread when updating/refreshing a view.
	private function getForumOverview( $accountId, $sourceType, $threadId = 0, $freeTextSearch = '', $startProductId = 0 ) {

		$limit = '';
		$joins = '';
		$group = '';
		$queryOrder = '';
		$productIdSearch = "";
		$freeTextSearch = trim( $freeTextSearch );
		$allowedSortFields = array(
								   'id'=>true
								   ,'reftype'=>true
								   ,'topic'=>true
								   ,'posted'=>true
								   ,'lastupdate'=>true
								   ,'authorName'=>true
								   ,'lastAuthorName'=>true
								   ,'views'=>true
								   ,'posts'=>true
								   ,'threadstatus'=>true
								   ,'votes'=>true
								   ,'content'=>true
								   ,'author'=>true
								   ,'accepted_answer'=>true
								   ,'lastedit'=>true
								   ,'canEdit'=>true
								   ,'attachment_id'=>true
								   );

		if ( !$threadId ) { // default case
			$limit = $this->constructLimit();
			$limit = ( $limit != "" ) ? " LIMIT " . $limit : "";
			$queryOrder = parent::constructOrderByClause($allowedSortFields);
		}

		$response = array();

		$tableToUse = " community.forum_threads ";
		$whereClause = " community.forum_threads.deleted = 0 ";
		if ( 0 < $sourceType ) {
			$whereClause .= " AND community.forum_threads.reftype = '" . $sourceType . "' " ;
		}
		if ( $threadId ) {
			$whereClause .= " AND  community.forum_threads.threadid = '" . (int) $threadId . "' " ;
		}

		if ( !empty( $startProductId ) ) {
			//Use the products name to look for threads containing it
			$freeTextSearch = $this->database['community']->getRowValue(
				"vuln_track.os_soft"
				,"os_soft_name"
				,"vuln_track.os_soft.os_soft_id = '" . (int) $startProductId . "'"
			);
			$productIdSearch = " community.forum_threads.product_id = " . (int) $startProductId . " OR ";
			$freeTextSearch = $this->database['community']->escapeString( $freeTextSearch );
		}

		//This section adds the logic of searching for a piece of text the forum
		if ( !empty( $freeTextSearch ) ) {
			$joins .= "
				INNER JOIN community.forum_posts
					ON community.forum_posts.threadid = community.forum_threads.threadid
			";
			//mysql_real_escape_string and gpc_magic_quotes does not protect us from injection via LIKE statements
			//we need to escape the wildcard characters here
			$freeTextSearch = addcslashes( $freeTextSearch, '%_' );
			$whereClause .= "
				AND (
					" . $productIdSearch . "
					community.forum_threads.topic LIKE '%" . $freeTextSearch . "%'
					OR community.forum_posts.content LIKE '%" . $freeTextSearch . "%'
					OR community.forum_posts.topic LIKE '%" . $freeTextSearch . "%'
				)
			";
			$group = " GROUP BY community.forum_threads.threadid ";
		}

		$fields = "community.forum_threads.threadid as id
				,community.forum_threads.topic
				,community.forum_threads.author
				,community.forum_threads.content
				,community.forum_threads.timestamp as posted
				,community.forum_threads.lastupdate
				,community.forum_threads.lastauthor
				,community.forum_threads.views
				,community.forum_threads.posts
				,community.forum_threads.reftype
				,community.forum_threads.threadstatus
				,community.forum_threads.votes
				,community.forum_threads.lastedit
				,community.forum_threads.accepted_answer
				,community.forum_threads.attachment_id";
		$query = "SELECT " . $fields . " FROM " . $tableToUse . $joins . " WHERE " . $whereClause . $group;

		if ( $limit != "" ) {
			$response['total'] = $this->database['community']->queryResourceNumRows(
				$this->database['community']->query( $query )
			);
		}


		$query = $query . $queryOrder . $limit;

		$rows = $this->database['community']->queryGetRows( $query );

		// NOTE - if we want to get the usernames in the same query so we can immediately sort by them, use something like:
		// select * from (select threadid as tt1, author, username as aName from forum_threads, psi.profile where author = user_id) as x LEFT JOIN (select threadid as tt2, lastauthor, username as bName from forum_threads, psi.profile where lastauthor=user_id) as y on tt1=tt2;
		// Issue - we will not get threads with userid which has no associated name in psi.profile, which maybe is fine if we assume all users have a valid id in one table and name in the other.
		// we also have to deal with people wanting to post anonymously - they should be able to set a flag in their profile so that all their posts are anonymous, as well as have a check when creating to 'post anonymously'.  BUT - we need to still know who posts what, so for each user when we get the name we check if their default setting is to post anonymously, then make their name 'anonymous' if set, and for each post we have a flag to check for 'post_anon', and if set, we only display 'anon' instead of the actual username.

		// ANYWAY - LOTS OF TODO ISSUES HERE!

		// For each row we also need to get the user names for both the author and the lastAuthor
		// For efficiency, get the userIds out first and create a lookup table so we don't have to do
		// a query insidea  loop
		$usersSeen = array();
		foreach ( $rows as $thisRow ) {
			$author = (int) $thisRow['author'];
			$lastAuthor = (int) $thisRow['lastauthor'];
			if ( !isset($usersSeen[$author]) ) {
				$usersSeen[$author] = 1;
			}
			if ( !isset($usersSeen[$lastAuthor]) ) {
				$usersSeen[$lastAuthor] = 1;
			}
		}

		$usersSeenString = '';
		foreach ( array_keys($usersSeen) as $userId ) {
			if ( '' != $usersSeenString ) {
				$usersSeenString .= ', ';
			}
			$usersSeenString .= (int) $userId;
		}

		$resultRows = 0;
		if ( strlen( $usersSeenString ) > 0 ) {
			$query = "SELECT user_id, username FROM psi.profile WHERE user_id IN (" . $usersSeenString . ")";
			$resultRows = $this->database['psi']->queryGetRows( $query );
		}
		$usersLookupTable = array();
		for ( $i=0; $i < count($resultRows); $i++ ) {
			$userId = $resultRows[$i]['user_id'];
			$userName = $resultRows[$i]['username'];
			$usersLookupTable[$userId] = $userName;
		}

		$userInfo = $this->getUserProfileData( $accountId );
		if ( !$userInfo ) {
			$thisUserId = 0;
		} else {
			$thisUserId = (int) $userInfo['id'];
		}

		$modifiedRows = array();
		foreach ( $rows as $thisRow ) {
			$authorId = $thisRow['author'];
			$authorName = '';
			if ( isset($usersLookupTable[$authorId]) ) {
				$authorName = $usersLookupTable[$authorId];
			}

			$lastAuthorId = $thisRow['lastauthor'];
			$lastAuthorName = '';
			if ( isset($usersLookupTable[$lastAuthorId]) ) {
				$lastAuthorName = $usersLookupTable[$lastAuthorId];
			}

			$thisRow['authorName'] = $authorName;
			$thisRow['lastAuthorName'] = $lastAuthorName;

			// Flag if the current user is the owner of this a given thread
			$canEdit = false;
			if ( $authorId == $thisUserId ) {
				$canEdit = true;
			}
			$thisRow['canEdit'] = $canEdit;

			// Add the modifed row to the result array which we will later return
			$modifiedRows[] = $thisRow;
		}

		// NOTE ON SORTING - right now author and lastauthor are sorted by their numeric ID, but of course,
		// this will not correspond to them being alphabetically sorted, but they will at least be grouped.
		// TODO - decide later if this is good enough, or (more likely) we fix it, in which case we do everything
		// within the query as discussed above.

		$response["data"] = $modifiedRows;
		return $GLOBALS['json']->json_encode( $response );
	}

	// Submitting a post can mean one of three things: Editing your own post (a reply),
	// editing your own thread (i.e., the first post), or replying to another
	// thread.  $postType is (resp.): 'editPost', 'editThread', 'reply'
	//
	// threadId must also be passed in so we known which thread we are posting to.
 	private function submitPost( $accountId, $topicText, $postText, $subForum, $subscribe, $postType, $threadId, $postId, $attachmentName = '' ) {

		$userInfo = $this->getUserProfileData( $accountId );

		if ( !$userInfo || !isset( $userInfo['email'] ) || !$userInfo['email'] ) {
			$GLOBALS['debug']->error( "userInfo does not exist" );
			echo "{success: false, response: 1 }";
			return;
		}

		$thisUserId = (int) $userInfo['id'];

		// Set up common values for all types of submission
		$values = array();
		$values["topic"] = $topicText;
		$values["content"] = $postText;
		$values["timestamp"] = 'NOW()'; // TODO - change to new UTC stuff - this is true throughout this file!
 		$values["lastedit"] = 'NOW()';
		$values["author"] = $thisUserId;


		// Handle Attachment

		if ( $attachmentName ) {

			$encodedContents = $this->getEncodedAttachment();

			// We find the attachment ids corresponding to the threads / posts. If we find a valid attachment_id (i.e. > 0)
			// we edit the attachments table by replacing the contents of the attachment, else we create a new entry
			// In both cases, we need the attachment_id of the new/edited entry ( though it should remain the same for edits )
			//  which will be used for editing the actual threads/posts table.
			$attachmentId = 0;

			switch ( $postType ) {
			case 'editThread':
				$attachmentId = $this->database['community']->getRowValue( "community.forum_threads", "attachment_id", "threadid='" . (int) $threadId . "'" );
				break;
			case 'editPost':
				$attachmentId = $this->database['community']->getRowValue( "community.forum_posts", "attachment_id", "postid='" . (int) $postId . "'" );
				break;
			case 'reply':
				$attachmentId = 0; // The attachment id should always be 0 for a new post
				break;
			}

			if ( !$attachmentId ) {
				$values['attachment_id'] = (int) $this->saveAttachment( $encodedContents, $attachmentName );
			} else {
				$values['attachment_id'] = (int) $this->editAttachment( $encodedContents, $attachmentId, $attachmentName );
			}

		}

		// Now call each specific helper sub-function based on type
		if ( 'editThread' == $postType ) {
			$this->submitEditedThread( $values, $subForum, $threadId );
		} else if ( 'editPost' == $postType ) {
			$this->submitEditedPost( $values, $threadId, $postId );
		} else if ( 'reply' == $postType ) {
			$this->submitNewReply( $values, $threadId );
		}

		// Deal with thread subscription
		$this->subscribeToThread( $accountId, $threadId, $subscribe );
	}

	// Just need to update the thread post with the new data
 	private function submitEditedThread( $values, $subForum, $threadId ) {
		$values['reftype'] = (int) $subForum;
 		$this->database['community']->edit( "community.forum_threads"
											,$values
											,DATABASE::UPDATE
											,"threadid='" . (int) $threadId . "' AND author='" . (int) $values['author'] . "'"
											,true );
	}

 	private function submitEditedPost( $values, $threadId, $postId ) {
		$this->database['community']->edit( "community.forum_posts"
											,$values
											,DATABASE::UPDATE
											,"threadid='" . (int) $threadId . "' AND author='" . (int) $values['author'] . "' AND postid='" . (int) $postId . "'"
											,true );
	}

 	private function submitNewReply( $values, $threadId ) {
		$values['threadid'] = $threadId;
		$this->database['community']->edit( "community.forum_posts"
											,$values
											,DATABASE::INSERT
											,true );

		// Remember to change last author on thread, increment posts, etc.
		// Can't use DB->edit function due to the 'posts+1' call in the update.
		$query = "UPDATE community.forum_threads
				SET lastupdate = NOW()
					,lastauthor = '" . (int) $values['author'] . "'
					,posts = posts+1
				WHERE
					threadid='" . (int) $threadId . "'";
		$this->database['community']->query( $query );

		$this->updateUserStats( $values['author'], 'post' );
	}

	// enhancement:	Implement 'remove attachment' feature


	private function saveAttachment( $contents, $attachmentName ) {

		$values = array();
		$values['name'] = $attachmentName;
		$values['encoded_attachment'] = $contents;

		$attachmentId = $this->database['community']->edit( "community.forum_attachments", $values, DATABASE::INSERT );

		return $attachmentId;
	}

	private function editAttachment( $contents, $attachmentId, $attachmentName ) {

		if ( !$attachmentId ) {
			$GLOBALS['debug']->error( "editAttachment() - incorrect arguments" );
			return false;
		}

		$values = array();
		$values['name'] = $attachmentName;
		$values['encoded_attachment'] = $contents;

		$whereClause = " id = " . ( (int) $attachmentId );

		$this->database['community']->edit( "community.forum_attachments", $values, DATABASE::UPDATE, $whereClause );

		return (int) $attachmentId;
	}

	// The function takes the raw attachment and encodes it using base64 encoding
	private function getEncodedAttachment() {

		// todo:	once magic gpc is turned off, we will fetch the raw contents using _POST.
		//			for now, we use MPOST and strip the slashes.
		// $rawAttachment = $_POST['attachment_contents'];
		$escapedAttachment = $GLOBALS['input']->MPOST('attachment_contents');
		$rawAttachment = stripslashes( $escapedAttachment );

		$encodedContents = base64_encode( $rawAttachment );
		return $encodedContents;
	}
	// TODO - if incorporating product id into 'prorams sub forum as it is now, need to add refid stuff and product id stuff above


	private function submitNewThread( $accountId, $topicText, $postText, $subForum, $subscribe, $attachmentName = '' ) {
		// Find out if this user is already subscribing to this thread
		$userInfo = $this->getUserProfileData( $accountId );
		if ( !$userInfo || !isset( $userInfo['email'] ) || !$userInfo['email'] ) {
			$GLOBALS['debug']->error( "userInfo does not exist" );
			echo "{success: false, response: 1 }";
			return;
		}
		$thisUserId = (int) $userInfo['id'];

		$values = array();
		$values["topic"] = $topicText;
		$values["content"] = $postText;
		$values["reftype"] = (int) $subForum;
		$values["timestamp"] = 'NOW()';
		$values["lastupdate"] = 'NOW()';

		$values["author"] = $thisUserId; // TODO - link with profile management mentioned above
		$values["lastauthor"] = $thisUserId; // same int as author above

		$values["refid"] = (int) 0; // TODO - if incorporating product id into 'prorams sub forum as it is now


		// Handle Attachment
		if ( $attachmentName ) {
			$encodedContents = $this->getEncodedAttachment();
			$attachmentId = $this->saveAttachment( $encodedContents, $attachmentName );
			$values['attachment_id'] = (int) $attachmentId;
		}

		$newThreadId = $this->database['community']->edit( "community.forum_threads", $values, DATABASE::INSERT, true );

		// Now do the required housekeeping
		// Update users stats and manage subscription
		$this->updateUserStats( $thisUserId, 'thread' );
		// todo: need to rewrite subscribeToThread to avoid confusion such as follows:
		if ( $subscribe ) {
			$this->subscribeToThread( $accountId, $newThreadId, $subscribe );
		} else {
			echo "{ success: true }";
		}
	}

	// =========================================================================


	/**
	 * Update the number of threads or posts the user has posted - increment by 1
	 *
	 * @param integer userId
	 * @param string type - 'thread' or 'post'
	 *
	 * @return boolean MySQL update status, true on success - false on failure
	 */
	private function updateUserStats( $userId, $type ) {

		$probeUserRow = $this->database['community']->getRow( "community.forum_userstats", "userid='" . (int) $userId . "'" );

		$values = array();
		if ( 'thread' == $type ) {
			$values['threads'] = 1;
			$values['posts'] = 0;
		} else if ( 'post' == $type ) {
			$values['posts'] = 1;
			$values['threads'] = 0;
		}

		if ( !$probeUserRow ) {
			// insert into table - must be user's first post
			$values['userid'] = (int) $userId;
			$this->database['community']->edit( "community.forum_userstats", $values, DATABASE::INSERT );

		} else {
			// then we are updating this user's threads or posts to add 1
			$currentThreads = (int) $probeUserRow['threads'];
			$currentPosts = (int) $probeUserRow['posts'];
			$values['threads'] += $currentThreads;
			$values['posts'] += $currentPosts;

			$this->database['community']->edit( "community.forum_userstats", $values, DATABASE::UPDATE, "userid='" . (int) $userId . "'" );
		}
	}

	// User Metadata (ratings, info, etc.)
	// Taken from website code
	// Modified to return structure with all user info potentially required
	// ---------------------------------------------------------------------

	// Check if a user has admin rights based on user ID
	private function isUserAdmin( $userId ) {

		$userSettings = $this->database['community']->getRow( "community.forum_usersettings", "userid='" . (int) $userId . "'" );
		if ( !$userSettings ) {
			// put entry into table, which will get the default value of 'user'
			$values = array( "userid" => (int) $userId );
			$this->database['community']->edit( "community.forum_usersettings", $values, DATABASE::INSERT );
			return false;

		} else {
			if ( "admin" == $userSettings['usertype'] ) {
				return true;
			}
		}
		return false;
	}

	// Get the location of the user, and format it correctly
	private function getUserLocation( $userId, $userInfo ) {

		if ( $this->isUserAdmin( $userId ) ) {
			return "Copenhagen, DK";
		}

		if ( !$userInfo ) {
			return "N/A";
		} else {
			$city = '';
			$country = '';
			if ( trim($userInfo['city']) ) {
				$city = $userInfo['city'];
			}
			if ( trim($userInfo['country']) ) {
				$country = $userInfo['country'];
			}
			if ( $city && $country ) {
				return $city . ", " . $country;
			} else if ( ($city && !$country) || (!$city && $country) ) {
				// only one is non-empty
				return $city . $country;
			} else {
				// both are empty
				return "N/A";
			}
		}
	}

  	// This will get all the user info associated with an ID and return
	// it in a structure
	private function getUserInfo( $userId ) {

		// The fields assocaited with a given user are as follows:
		$userImage = '';
		$rank = '';

		$userName = '';
		$signature = '';
		$userSince = '';
		$location = '';
		$posts = 0;

		// NOTE - We don't include system score as this is PSI specific, and might
		// be misleading to CSI users, who might think their CSI info is being
		// publicly displayed.  TODO - change if we want to make this dependent on
		// sourceType (i.e. PSI, VIM, etc.)

		// First deal with the generic ones that apply to everyone
		$userInfo = $this->database['psi']->getRow( "psi.profile", "user_id='" . (int) $userId . "'" );
		if ( $userInfo ) {
			$signature = $userInfo['signature'];
			$userSince = $userInfo['created'];
			$userName = $userInfo['username'];
		}
		$location = $this->getUserLocation( $userId, $userInfo );

 		// Posts (includes threads started and replies
		$userStats = $this->database['community']->getRow( "community.forum_userstats", "userid='" . (int) $userId . "'" );
		if ( $userStats ) {
			$posts = (int) $userStats['posts'] + (int) $userStats['threads'];
		}

		// Now get the rank and associated image
		// -------------------------------------

		// Special hard coded value of a "-1" id means: "Secunia" user
		if ( -1 == $userId ){
			$rank = "Flexera Official";
			$userImage = "<img src='" . $this->imagePath . "secuniaofficial.gif' border='0' alt='Flexera Official'>";
		}

		$userSettings = $this->database['community']->getRow( "community.forum_usersettings", "userid='" . (int) $userId . "'" );

		$userRating = $userSettings['userrating'];
		$userType = $userSettings['usertype'];

		// We also have a special designation for new members, i.e., account is less
		// than 1 month old
		if( $userSince ) {
			$createdTimestamp = strtotime( $userSince );
			$lastMonth = strtotime( "-1 month" );
			if ( $createdTimestamp < $lastMonth ) {
				$rank = "New Member";
				$userImage = "<img src='" . $this->imagePath . "secunianewmember.gif' border='0' alt='New Member'>";
			}
		}

		// Check for hardcoded user types - this will overwrite newMember if valid
		switch ( $userType ){
		case "researcher":
			$rank = "Secunia Researcher";
			$userImage = "<img src='" . $this->imagePath . "secuniaresearcher.gif' border='0' alt='Secunia Researcher'>";
			break;
		case "vendor":
			$rank = "Vendor";
			$userImage = "<img src='" . $this->imagePath . "secuniavendor.gif' border='0' alt='Flexera Vendor'>";
			break;
		case "admin":
			$rank = "Flexera Official";
			$userImage = "<img src='" . $this->imagePath . "secuniaofficial.gif' border='0' alt='Flexera Official'>";
			break;
		case "handler":
			$rank = "Handling Contributor";
			$userImage = "<img src='" . $this->imagePath . "secuniahandlingcontributor.gif' border='0' alt='Handling Contributor'>";
			break;
		}

		if ( !$userImage ) {
			//Begin with a maximum type of user and move down based on $userRating
			$rankTypes = count ( $this->userRankingThresholds );
			$rank = $this->userRankingThresholds[ $rankTypes -1 ][1];
			$userImage = $this->userRankingThresholds[ $rankTypes -1 ][3];
			for ( $i = 0; $i < $rankTypes; $i++ ){
				if ( $userRating <= $this->userRankingThresholds[$i][0] ){
					$rank = $this->userRankingThresholds[$i][1];
					$userImage = $this->userRankingThresholds[$i][3];
					break;
				}
			}
		}

		// Construct the returned object
		$resultArray = array( 'userName' => $userName
							  ,'userRank' => $rank
							  ,'userImage' => $userImage
							  ,'numPosts' => $posts
							  ,'location'  => $location
							  ,'joinDate'  => $userSince
							  ,'signature'  => $signature );
		return $resultArray;
	}
	// ------------------- End of user metadata  -------------------


	private function updateForumProfileName( $accountId, $username, $newProfile ) {

		$returnSuccess = $GLOBALS['json']->json_encode( array("result" => 0) );
		$returnNameConflict = $GLOBALS['json']->json_encode( array("result" => 1) );
		$returnNoUserData = $GLOBALS['json']->json_encode( array("result" => 2) );

		// In either case, new or update, we must check that the requested
		// username does not already exist.  Though, if we are trying to update
		// our own name to the same name, we allow it.  We don't so much allow it
		// exactly, we just don't do anything and return success.

		// Note: username is passed in via post thus is safe to use as is.

		$existanceWhere = " username = '" . $username . "'";
		if ( !$newProfile ) {
			$userInfo = $this->getUserProfileData( $accountId );
			if ( !$userInfo ) {
				// We cannot look up the user's data - return error
				return $returnNoUserData;
			} else {
				$userId = $userInfo['id'];
				$existanceWhere .= " AND user_id !='" . (int) $userId . "'";
			}
		}

		$exists = $this->database['psi']->numRows( "psi.profile", $existanceWhere );
		if ( $exists ) {
			return $returnNameConflict;
		}

		if ( $newProfile ) {
			// Insert the profile data into the DB and associate the CSI table with
			// the new profile ID.  Get the csi password to use as forum password.
			$caAccount = $this->database['community']->getRowValues(
				 'ca.accounts'
				,array(
					 'account_password'
					,'account_email'
				 )
				,"account_id='" . (int) $accountId . "'"
			);

			// Get the next userId to use for the table
			$query = "SELECT user_id FROM psi.profile WHERE user_id >= ********* ORDER BY user_id DESC LIMIT 1";
			$topUidRow = $this->database['psi']->queryGetRow( $query );
			if ( !$topUidRow ) {
				$newUid = *********;
			} else {
				$newUid = (int) $topUidRow['user_id'] + 1;
			}
			// Shouldn't need to really escape password as it should be in the DB in an expected form, but it doesn't hurt, and is consistent with our general methodology of escaping data coming from the DB then going to the DB.
			$values = array( 'user_id' => $newUid
							 ,'username' => $username
							 ,'password' => $this->database['community']->escapeString( $caAccount[ 'account_password' ] )
							 ,'email' => $this->database['community']->escapeString( $caAccount[ 'account_email' ] )
							 ,'confirm_string' => $GLOBALS[ 'prng' ]->generateRandomID( 30 )
							 ,'confirmed' => 1 );

			$this->database['psi']->edit( "psi.profile"
												,$values
												,DATABASE::INSERT
												,''
												,true );
			// Note - data already escaped, so last param is true
			// Also note, can't get the insertId returned from here since the table
			// is not an auto increment on the primary key, so it always returns 0.
			// Will need to trust that the $newUid was inserted properly.
			$values = array( 'community_profile_id' => (int) $newUid );
			$this->database['community']->edit( "ca.nsi_base_settings"
												,$values
												,DATABASE::UPDATE
												,"account_id='" . (int) $accountId . "'"
												,true );
			return $returnSuccess;

		} else {
			$values = array( 'username' => $username );
			$ret = $this->database['psi']->edit( "psi.profile"
													   ,$values
													   ,DATABASE::UPDATE
													   ,"user_id='" . (int) $userId . "'"
													   ,true );
			// Note - data already escaped, so last param is true
			return $returnSuccess;
		}
	}

	private function getAttachmentContents( $attachmentId ) {
		$response = array();

		if ( !is_numeric( $attachmentId ) || !( (int) $attachmentId > 0 ) ) {
			$GLOBALS['debug']->error( "getAttachmentContents() - invalid attachmentId" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		// Fetch the attachment contents (stored in encoded form)

		$encodedContents = $this->database['community']->getRowValue( "community.forum_attachments", "encoded_attachment", "id='" . (int) $attachmentId . "'" );

		$response['contents'] = base64_decode( $encodedContents );
		$response['success'] = true;

		return $GLOBALS['json']->json_encode( $response );
	}

	private function deleteAttachment( $attachmentId ) {
		$query = "DELETE FROM community.forum_attachments WHERE id = '" . (int) $attachmentId . "'";
		$this->database['community']->query( $query );
	}

	private function removeThreadAttachment( $accountId, $threadId ) {
		$response = array();

		if ( !is_numeric( $threadId ) || ( $threadId <= 0 ) ) {
			$GLOBALS['debug']->error( "removeThreadAttachments() - invalid args" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		$userInfo = $this->getUserProfileData( $accountId );
		if ( !$userInfo ) {
			$GLOBALS['debug']->error( "removeThreadAttachments() - can't fetch user info" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		$userId = (int) $userInfo['id'];
		$whereClause = " author = '" . (int) $userId . "' AND  threadid = '" . (int) $threadId . "'";

		// Get the attachment id to be deleted
		$attachmentId = $this->database['community']->getRowValue( "community.forum_threads", "attachment_id", $whereClause );

		// Delete the attachment
		$this->deleteAttachment( $attachmentId );

		// Update the thread row with attachment_id = 0
		$values['attachment_id'] = 0;
		$this->database['community']->edit( "community.forum_threads", $values, DATABASE::UPDATE, $whereClause );

		$response['success'] = true;
		return $GLOBALS['json']->json_encode( $response );
	}

	private function removePostAttachment( $accountId, $postId ) {
		$response = array();

		if ( !is_numeric( $postId ) || ( $postId <= 0 ) ) {
			$GLOBALS['debug']->error( "removePostAttachment() - invalid args" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		$userInfo = $this->getUserProfileData( $accountId );
		if ( !$userInfo ) {
			$GLOBALS['debug']->error( "removePostAttachment() - can't fetch user info" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		$userId = (int) $userInfo['id'];
		$whereClause = " author = '" . (int) $userId . "' AND  postid = '" . (int) $postId . "'";

		// Get the attachment id to be deleted
		$attachmentId = $this->database['community']->getRowValue( "community.forum_posts", "attachment_id", $whereClause );

		// Delete the attachment
		$this->deleteAttachment( $attachmentId );

		// Update the post row with attachment_id = 0
		$values['attachment_id'] = 0;
		$this->database['community']->edit( "community.forum_posts", $values, DATABASE::UPDATE, $whereClause );

		$response['success'] = true;
		return $GLOBALS['json']->json_encode( $response );
	}

	// The function is used to return the metadata
	// At the moment we only have the name of the file stored at the server
	private function getAttachmentMetadata( $attachmentId ) {
		$response = array();

		if ( !is_numeric( $attachmentId ) || !( (int) $attachmentId > 0 ) ) {
			$GLOBALS['debug']->error( "getAttachmentMetadata() - invalid attachmentId" );
		    $response['success'] = false;
			return $GLOBALS['json']->json_encode( $response );
		}

		// Fetch the attachment contents (stored in encoded form)
		$fields = "name";

		$name = $this->database['community']->getRowValue( "community.forum_attachments", $fields, "id='" . (int) $attachmentId . "'" );

		$response['data']['name'] = $name;
		$response['success'] = true;

		return $GLOBALS['json']->json_encode( $response );
	}

	// =====================================================

	/**
	 * Returns the last forum activity datetime. Will throw an exception if it fails
	 * @param integer - Account Id (not to be confused with profile id)
	 * @return array with 2 keys: hasUnreadContent and lastCheck
	 */
	private function hasUnreadContent( $accountId ) {
		$communityProfile = $this->getUserProfileData( $accountId );
		//If we do not have a profile we signal accordingly
		if ( $communityProfile === false ) {
			return false;
		}

		$lastActivity = $this->getLastActivity();
		if ( $lastActivity === false ) {
			return false;
		}

		//ENHANCEMENT: replace with DateTime Object when upgraded to PHP > 5.2
		$lastActivity = strtotime( $lastActivity );
		if ( $lastActivity === false ) {
			return false;
		}

		$lastUserCheck = $this->getLastCheck( $accountId );
		if ( $lastUserCheck === false ) {
			return false;
		} else if ( $lastUserCheck === '0000-00-00 00:00:00' ) {
			//This is the default value
			//No point in marking the whole forum as unread
			return array(
				'hasUnreadContent' => false
				,'lastCheck' => null
			);
		}
		$lastUserCheck = strtotime( $lastUserCheck );
		if ( $lastUserCheck === false ) {
			return false;
		}
		return array(
			'hasUnreadContent' => ( ( $lastUserCheck - $lastActivity ) >= 0 ? false : true )
			,'lastCheck' => date( 'Y-m-d H:i:s', $lastUserCheck )
		);
	}

	/**
	 * Returns the date of the last activity on the forum, regardless of who did it
	 * @return string in date format
	 */
	private function getLastActivity() {
		$query = "
			SELECT
				MAX( community.forum_threads.lastupdate )
			FROM
				community.forum_threads
			WHERE
				community.forum_threads.deleted = 0
		";
		$resourceArr = (array) $this->database['community']->queryGetRow( $query );
		//Check to see if we have retrived something from the database
		$lastActivity = reset( $resourceArr );
		return $lastActivity;
	}

	/**
	 * Returns the date when the user last checked the forum
	 * @param integer - Account Id (not to be confused with profile id)
	 * @return string in date format
	 */
	private function getLastCheck( $accountId ) {
		$communityProfile = $this->getUserProfileData( $accountId );
		//If we do not have a profile we signal accordingly
		if ( $communityProfile === false ) {
			return false;
		}

		$probeUserRow = $this->database['community']->getRow( "community.forum_userstats", "userid='" . (int) $communityProfile['id'] . "'" );
		if ( !$probeUserRow ) {
			$values['userid'] = (int) $communityProfile['id'];
			$values['posts'] = 0;
			$values['threads'] = 0;
			$this->database['community']->edit( "community.forum_userstats", $values, DATABASE::INSERT );
		}

		$lastUserCheck = $this->database['community']->getRowValue(
			'community.forum_userstats'
			,'lastcheck'
			,"userid = '" . (int) $communityProfile['id'] . "'"
		);
		return $lastUserCheck;
	}

	/**
	 * Sets the lastcheck date for an account
	 * @param integer - Account Id (not to be confused with profile id)
	 */
	private function setLastCheck( $accountId ) {
		$communityProfile = $this->getUserProfileData( $accountId );
		//If we do not have a profile we signal accordingly
		if ( $communityProfile === false ) {
			return false;
		}

		$probeUserRow = $this->database['community']->getRow( "community.forum_userstats", "userid='" . (int) $communityProfile['id'] . "'" );
		if ( !$probeUserRow ) {
			$values['userid'] = (int) $communityProfile['id'];
			$values['posts'] = 0;
			$values['threads'] = 0;
			$this->database['community']->edit( "community.forum_userstats", $values, DATABASE::INSERT );
		}

		$query = "
			UPDATE
				community.forum_userstats
			SET
				community.forum_userstats.lastcheck = NOW()
			WHERE
				community.forum_userstats.userid = " . (int) $communityProfile['id'] . "
		";
		if ( !$this->database['community']->query( $query ) ) {
			return false;
		}
		return true;
	}

	function handleRequest( $which ) {

		switch ( $which ) {
		case "overview":
			echo $this->getForumOverview( $GLOBALS['account_id']
										  ,$GLOBALS['input']->MPOST('source_type')
										  ,0
										  ,$GLOBALS['input']->MPOST('free_text_search')
										  ,$GLOBALS['input']->MPOST('start_product_id') );
			break;
		case "getOneThread":
			echo $this->getForumOverview( $GLOBALS['account_id']
										  ,0
										  ,$GLOBALS['input']->MPOST('threadId') );
			break;
		case "submitThread":
			echo $this->submitNewThread( $GLOBALS['account_id']
										 ,$GLOBALS['input']->MPOST('topic')
										 ,$GLOBALS['input']->MPOST('contents')
										 ,$GLOBALS['input']->MPOST('subForum')
										 ,$GLOBALS['input']->MPOST('subscribe')
										 ,$GLOBALS['input']->MPOST('attachment_name') );
			break;
		case "submitPost":
			echo $this->submitPost( $GLOBALS['account_id']
									,$GLOBALS['input']->MPOST('topic')
									,$GLOBALS['input']->MPOST('contents')
									,$GLOBALS['input']->MPOST('subForum')
									,$GLOBALS['input']->MPOST('subscribe')
									,$GLOBALS['input']->MPOST('postType')
									,$GLOBALS['input']->MPOST('threadId')
									,$GLOBALS['input']->MPOST('postId')
									,$GLOBALS['input']->MPOST('attachment_name'));

			break;
		case "getPosts":
			echo $this->getForumThreadPosts( $GLOBALS['account_id']
											 ,$GLOBALS['input']->MPOST('threadId')
											 ,$GLOBALS['input']->MPOST('threadAuthorId') );
			break;
		case "threadSubscribe":
			$this->subscribeToThread( $GLOBALS['account_id']
									  ,$GLOBALS['input']->MPOST('threadId')
									  ,1 );
			break;
		case "threadUnSubscribe":
			$this->subscribeToThread( $GLOBALS['account_id']
									  ,$GLOBALS['input']->MPOST('threadId')
									  ,0 );
			break;
		case "checkForumRegistration":
			echo $this->checkForumRegistration( $GLOBALS['account_id'], true );
			break;
		case "updateProfile":
			echo $this->updateForumProfileName( $GLOBALS['account_id']
												,$GLOBALS['input']->MPOST('username')
												,false );
			break;
		case "newProfile":
			echo $this->updateForumProfileName( $GLOBALS['account_id']
												,$GLOBALS['input']->MPOST('username')
												,true );
			break;
		case "attachment":
			echo $this->getAttachmentContents( $GLOBALS['input']->MPOST('attachment_id') );
			break;
		case "remove_thread_attachment":
			echo $this->removeThreadAttachment( $GLOBALS['account_id']
												,$GLOBALS['input']->MGET('thread_id') );
			break;
		case "remove_post_attachment":
			echo $this->removePostAttachment( $GLOBALS['account_id']
											  ,$GLOBALS['input']->MGET('post_id') );
			break;
		case "metadata":
			echo $this->getAttachmentMetadata( $GLOBALS['input']->MPOST('attachment_id') );
			break;
		case "hasUnreadContent":
			$data = $this->hasUnreadContent( $GLOBALS['account_id'] );
			if ( $data === false ) {
				$data = array();
				$success = false;
			} else {
				$data = $data;
				$success = true;
			}
			echo $GLOBALS['json']->json_encode(array(
				'data' => $data
				,'success' => $success
			));
			break;
		case "setLastCheck":
			if ( $this->setLastCheck( $GLOBALS['account_id'] ) ) {
				$data = array(
					'lastCheck' => $this->getLastCheck( $GLOBALS['account_id'] )
				);
				$success = true;
			} else {
				$data = array();
				$success = false;
			}
			echo $GLOBALS['json']->json_encode(array(
				'data' => $data
				,'success' => $success
			));
			break;
		}

	}
}