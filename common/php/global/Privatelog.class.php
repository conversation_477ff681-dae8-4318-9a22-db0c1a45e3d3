<?php

  /*
   * A PSR-3 implementation of a Logger that uses a file for logging purposes
   */

class Privatelog extends Syslog {

	private $logFileHandler;
	private $maxLevels;
	private $timeLimit;

	public function __construct( $filePath = "", $maxLevels = 10, $timeLimit = null ) {
		if ( $filePath === "" ) {
			if ( defined( "DEBUG_LOG_FILE" ) ) {
				$filePath = DEBUG_LOG_FILE;
			} else {
				throw new Exception( "Please specify a file to log to be either providing a filepath to the constructor or defining the DEBUG_LOG_FILE constant" );
			}
		}
		$this->logFileHandler = @fopen( $filePath, "a" );
		if ( $this->logFileHandler === false ) {
			throw new Exception( "The specified filepath could not be opened for writing: " . $filePath );
		}
		$this->maxLevels = $maxLevels;
		$this->timeLimit = $timeLimit;
	}

	function __destruct() {
		fclose( $this->logFileHandler );
	}

	private function dp( $obj, $timeLimit = null, $levels = 0 ) {
		if ( is_numeric( $timeLimit ) ) {
			set_time_limit( $timeLimit );
		}
		if ( $levels > $this->maxLevels ) {
			return "";
		}
		$tabs = "";
		$output = "";
		for ( $i = 0; $i < $levels; $i++ ) {
			$tabs .= "\t";
		}
		//Will loop through the elements
		if ( is_array( $obj ) || is_object( $obj ) ) {
			if ( is_array( $obj ) ) {
				$output .= "array (";
			} else {
				$output .= get_class( $obj ) . " (";
			}
			foreach ( $obj as $key => $val ) {
				$output .= "\n" . $tabs . "\t" . $key . " => " . dp( $val, null, $levels + 1 );
			}
			//get the methods
			if ( is_object( $obj ) ) {
				$reflection = new ReflectionClass( get_class( $obj ) );
				foreach ( $reflection->getProperties() as $property ) {
					$output .= "\n" . $tabs . "	" . $property->getName();
				}
			}
			$output .= "\n" . $tabs . ")";
		} else if ( is_numeric( $obj ) ) {
			$output .= $obj;
		} else if ( is_string( $obj ) ) {
			$output .= $obj;
		} else if ( is_null( $obj ) ) {
			$output .= "NULL";
		} else if ( is_bool( $obj ) ) {
			if ( $obj ) {
				$output .= "TRUE";
			} else {
				$output .= "FALSE";
			}
		} else if ( empty( $obj ) ) {
			$output .= "" ;
		} else {
			$output .= "<<UNRECOGNIZED DATA TYPE>>";
		}
		fwrite( $this->logFileHandler, "\n" . $output . "\n" );
	}

    /**
     * Logs with an arbitrary level.
     *
     * @param mixed $level
     * @param object $message
     * @param array $context
     * @return null
     */
    public function log($level, $message, array $values = array()) {
    	$this->dp( $message, $this->timeLimit );
	}

}