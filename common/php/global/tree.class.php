<?php

  /**
   * The tree data structure. It is primarily used for importing an array into a tree.
   *
   * Some basic functionality hasn't been implemented yet e.g. insert, delete etc.
   */
class Tree {

	private $tree = null;

	/**
	 * Import an array of arrays to a tree
	 */
	public function import( $rows, $idName = 'id', $parentIdName = 'parent_id' ) {

		// Check if we have a cached version
		if ( !is_null( $this->tree ) ) {
			return $this->tree;
		}

		// Index the rows:
		// - Using the element ids
		// - In the form of objects to that references are used implicitly
		$elements = array();
		foreach( $rows as $row ) {
			if ( !is_object( $row ) ) {
				$element = (Object) $row;
			}
			$element->_id = $element->{$idName};
			$element->_parentId = $element->{$parentIdName};
			$element->children = array();
			$elements[ $element->_id ] = $element;
		}
		ksort( $elements );

		// Make the tree
		foreach( $elements as $elementId => $element ) {
			if ( isset( $elements[ $element->_parentId ] ) ) {
				$parentElement = $elements[ $element->_parentId ];
				$parentElement->children[] = $element;
			}
		}

		$this->tree = $elements;
		return $this->tree;
	}

	/**
	 * Traverse the tree and apply a callback function for each element
	 */
	private function traverse( $element = null, $callback = null ) {
		if ( empty( $element ) ) {
			return;
		}

		if ( is_callable( $callback ) ) {
			$callback( $element );
		}

		$children = array();
		foreach( $element->children as $i => $child ) {
			$this->traverse( $child, $callback );
		}
		return;
	}

	/**
	 * Function that extracts the children of the element. The
	 * returned data also includes the root element.
	 *
	 * @param Integer $elementId
	 * @param String $format
	 *  Can be: 'integer' | 'object' | 'array'
	 *
	 * @return Array
	 *  An array of data types specified in $format
	 */
	public function getChildren( $elementId, $type = 'integer' ) {

		$children = array();
		$callback = function( $element ) use ( &$children, $type ) {
			$child = null;
			switch( $type ) {
			case 'integer':
				$child = $element->_id;
				break;
			case 'object':
				$child = $element;
				break;
			case 'array':
				$child = (array) $element;
				break;
			default:
				return;
			}
			$children[] = $child;
		};
		$this->traverse( $this->tree[ $elementId ], $callback );
		return $children;
	}


	/**
	 * @todo: can we use the traverse function instead of toString
	 */
	public function toString( $element = null, $callback = null ) {
		if ( empty( $element ) ) {
			return;
		}

		$children = array();
		foreach( $element->children as $i => $child ) {
			$children[] = $this->toString( $child );
		}

		return $element->_id . ' :: [ ' . implode( ' , ', $children ) . '] ';
	}

	public function __toString() {
		reset( $this->tree );
		return $this->toString( current( $this->tree ) );
	}
}