<?php
/**
 * @file exploit.class.php
*/

/**
 * Provides exploit object.
*/
class EXPLOIT {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for fetching advisory exploits.
	 * @param vulnId
	 *	Integer vulnerability/advisory id
	 * @return
	 *	Array of exploit data
	*/
	function getExploits( $vulnId ) {
		// Check for beta!
		if ( BETA == true ) {
			return array();
		}
		$query = "SELECT
				title
				,codelang
				,exploit_type_name
				,released
				,revision
				,updated
				,exploit.exploit_id
				,display_inline
			FROM
				vuln_track.exploit_rel
				,vuln_track.exploit
			LEFT JOIN
				vuln_track.exploit_codelang
			ON
				exploit.codelang_id = exploit_codelang.exploit_codelang_id
			LEFT JOIN
				vuln_track.exploit_type
			ON
				exploit.type_id = exploit_type.exploit_type_id
			WHERE
				exploit_rel.vuln_id = '" . (int)$vulnId . "'
				AND exploit_rel.exploit_id = exploit.exploit_id
				AND exploit.public != 0
		";

		$GLOBALS['debug']->notice( "Exploit list fetched" );
		return $this->database['ca']->queryGetRows( $query );
	}

	/**
	 * Function for checking fetching the number of exploits
	 * @param vulnId
	 *	Integer vulnerability id
	 * @return
	 *	Integer number of exploits
	*/
	function countExploits( $vulnId ) {
		return $this->database['ca']->numRows( "vuln_track.exploit_rel, vuln_track.exploit", "exploit_rel.vuln_id = '".(int)$vulnId."' AND exploit_rel.exploit_id = exploit.exploit_id AND exploit.public != 0" );
	}

	/**
	 * Function for fetching exploit data.
	 * @param exploitId
	 *	Integer exploit id
	 * @return
	 *	String exploit code/data
	*/
	function getExploitData( $exploitId ) {
		// Check for beta!
		if ( BETA == true || $GLOBALS['options']->OPT_ADVISORY_POC_DATA == false ) {
			return "";
		}
		$result = $this->database['ca']->queryGetRows( "SELECT
				exploit.data AS data, vuln.vuln_title AS vuln_title
			FROM
				vuln_track.exploit, vuln_track.exploit_rel, vuln_track.vuln
			WHERE
				exploit.exploit_id = '".(int)$exploitId."' AND exploit.public != 0 AND exploit_rel.exploit_id = exploit.exploit_id AND exploit_rel.vuln_id = vuln.vuln_id
			LIMIT 1
		");

		// Log exploit id only, no data!
		$GLOBALS['debug']->notice( "Exploit data fetched, Exploit Id: {exploitId}, Data Length: {datalen}"
								   , array( "exploitId" => $exploitId, "datalen" => strlen( $result[0]['data'] ) ) );
		return $result;
	}
}
