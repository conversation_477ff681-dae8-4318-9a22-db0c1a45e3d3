<?php
/**
 * @file assetSync.class.php
 * VIM Only
 * This file provides asset sync functionality. In other words, it sends new notifications to customers, and saves data to their process lists.
*/
class assetSync {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	public function buildAdvisoryRating( $advisoryInfo ) { // Build ratings for the mail subject
		$rating = "";
		if ( $advisoryInfo['details']['vuln_create_date'] != $advisoryInfo['details']['vuln_modified_date'] ) {
			$rating = "N";
		} else {
			$rating = "U";
		}
		$rating .= $advisoryInfo['details']['vuln_critical_boolean'];
		switch ( $advisoryInfo['details']['vuln_solution_status'] ) {
			case 1:
				$rating .= "U";
				break;
			case 2:
				$rating .= "P";
				break;
			case 3:
				$rating .= "W";
				break;
			case 4:
				$rating .= "½";
				break;
		}

		return $rating;
	}

	private function fetchValues( $vulnId, $xml = true ) {
		// Fetch advisory values
		// Note - Don't need exploit data here - don't need 2nd param set to true
		$advisoryInfo = $GLOBALS['advisory']->getDetails( $vulnId );

			// Prepare impacts
			$impact = "";
			for ( $i = 0; $i < count( $advisoryInfo['advisoryImpact'] ); $i++ ) {
				$impact .= $advisoryInfo['advisoryImpact'][$i]['impact_type_name']."\r\n";
			}

			// Prepare OS
			$os = "";
			if ( count( $advisoryInfo['affectedOs'] ) != 0 ) {
				for ( $i = 0; $i < count( $advisoryInfo['affectedOs'] ); $i++ ) {
					$os .= $advisoryInfo['affectedOs'][$i][1]."\r\n";
				}
				$os = ( $xml == true ? "<os>" : "OS: \r\n" ) . $os . ( $xml == true ? "</os>\n" : "" );
			}

			// Prepare Software
			$software = "";
			if ( count( $advisoryInfo['affectedSoftware'] ) != 0 ) {
				for ( $i = 0; $i < count( $advisoryInfo['affectedSoftware'] ); $i++ ) {
					$software .= $advisoryInfo['affectedSoftware'][$i][1]."\r\n";
				}
				$software = ( $xml == true ? "<software>" :  "Software: \r\n" ) . $software . ( $xml == true ? "</software>\n" :  "" );
			}

			// Prepare CVSS
			$cvss = "";
			if ( $advisoryInfo['details']['vuln_cvss_score'] ) {
				$cvss .= ( $xml == true ? "<secunia_cvss_score>" : "Secunia CVSS Score: \r\n" ).$advisoryInfo['details']['vuln_cvss_score'] . " " . $advisoryInfo['details']['vuln_cvss_vector'] . ( $xml == true ? "</secunia_cvss_score>" : "\n\n" );
			}

			// Prepare text ( DESCRIPTION, SOLUTION, CREDITS, CHANGELOG, ORIGINAL_ADVISORY, OTHER_REFERENCES, REASON, EXTENDED_DESCRIPTION, EXTENDED_SOLUTION )
			$text = "";

			// Description
			if ( $advisoryInfo['description']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<description>" : "DESCRIPTION:\n" ).$advisoryInfo['description']['text_text'].( $xml == true ? "\n\n" : "" );
			}

			// Solution
			if ( $advisoryInfo['solution']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<solution>" : "SOLUTION:\n" ).$advisoryInfo['solution']['text_text']. ( $xml == true ? "</solution>\n" : "\n\n" );
			}

			// Credits
			if ( $advisoryInfo['credits']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<reported_by_credits>" : "CREDITS:\n" ).$advisoryInfo['credits']['text_text'].( $xml == true ? "</reported_by_credits>\n" : "\n\n" );
			}

			// Changelog
			if ( $advisoryInfo['changelog']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<changelog>" : "CHANGELOG:\n" ). $advisoryInfo['changelog']['text_text'].( $xml == true ? "</changelog>\n" : "\n\n" );
			}

			// Original Advisory
			if ( $advisoryInfo['originalAdvisory']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<original_advisory>" : "ORIGINAL ADVISORY:\n" ).$advisoryInfo['originalAdvisory']['text_text'].( $xml == true ? "</original_advisory>\n" : "\n\n" );
			}

			// Other references
			if ( $advisoryInfo['otherReferences']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<other_references>" : "OTHER REFERENCES:\n" ).$advisoryInfo['otherReferences']['text_text'].( $xml == true ? "</other_references>\n" : "\n\n" );
			}

			// Reason
			if ( $advisoryInfo['ratingReason']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<reason_rating>" : "RATING REASON:\n" ).$advisoryInfo['ratingReason']['text_text'].( $xml == true ? "</reason_rating>\n" : "\n\n" );
			}

			// Extended description
			if ( $advisoryInfo['extendedDescription']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<extended_description>" : "EXTENDED DESCRIPTION:\n" ).$advisoryInfo['extendedDescription']['text_text'].( $xml == true ? "</extended_description>\n" : "\n\n" );
			}

			// Extended solution
			if ( $advisoryInfo['extendedSolution']['text_text'] != "" ) {
				$text .= ( $xml == true ? "<extended_solution>" : "EXTENDED SOLUTION:\n" ).$advisoryInfo['extendedSolution']['text_text'].( $xml == true ? "</extended_solution>\n" : "\n\n" );
			}

			// Prepare revision
			$revision = "";
			if ( $advisoryInfo['details']['vuln_revision'] > 1 ) {
				$revision =  ( $xml == true ? "<revision>" : "REVISION:\n" ).( strlen( $advisoryInfo['details']['vuln_revision'] ) == 1 ? $advisoryInfo['details']['vuln_revision']  . '.0' : $advisoryInfo['details']['vuln_revision']  ) . " originally created " . $advisoryInfo['details']['vuln_create_date']  .( $xml == true ? "</revision>\n" : "\n\n" );
			}

			// Prepare advisory values
			$values = array(
				"vuln_id" => $advisoryInfo['details']['vuln_id']
				,"vuln_title" => $advisoryInfo['details']['vuln_title']
				,"ca_domain" => CA_DOMAIN
				,"vuln_create_date" => $advisoryInfo['details']['vuln_create_date']
				,"vuln_modified_date" => $advisoryInfo['details']['vuln_modified_date']
				,"vuln_revision" => $advisoryInfo['details']['vuln_revision']
				,"vuln_critical_boolean" => $advisoryInfo['details']['vuln_critical_boolean']
				,"critical" => $GLOBALS['misc']->fetchCriticalityText( $advisoryInfo['details']['vuln_critical_boolean'] )
				,"vuln_critical_boolean" => $advisoryInfo['details']['vuln_critical_boolean']
				,"impact" => $impact
				,"where" => $advisoryInfo['advisoryWhere']['where_type_name']
				,"revision" => $revision
				,"cvss" => $cvss
				,"os" => $os
				,"software" => $software
				,"text" => $text
				,"ca_about_advisories" => CA_DOMAIN."?action=vdbAboutSecuniaAdvisories"
				,"rating" => $this->buildAdvisoryRating( $advisoryInfo )
			);

			return $values;
	}

	/**
	 * Function for getting the asset lists that have not been updated yet.
	 * @return
	 *	Array having pairs of asset list id's and owner accounts.
	*/
	function fetchAssetLists() {
		$assets = $this->database['ca']->queryGetRows( "SELECT last_sync, asset_id, account_id, approval_method, xml_receive_all FROM ca.vi_assets WHERE last_sync <= NOW() ");
		$GLOBALS['debug']->notice( "Asset lists fetched" );
		return $assets;
	}

	/**
	 * Function for marking the asset list as syncronized.
	*/
	function markSynchronized( $assetId ) {
		$this->database['ca']->query("UPDATE ca.vi_assets SET last_sync = NOW() WHERE asset_id = '".(int)$assetId."' LIMIT 1");
	}

	/**
	 * Function for fetching all historic advisories, starting from last sync.
	 * @param asset
	 *	Array having asset id and account id
	 * @param lang
	 *	Integer language id ( 1- English, 2 - German )
	 * @return
	 *	Array of vulnerability id's
	*/
	function fetchAdvisories( $asset, $lang = 1 ) {
		$advisories = $GLOBALS['databoundgrid']->fetchAssetAdvisories( $asset['account_id'], $asset['asset_id'], 0, $ignoreJson = true, 'from=' . $asset['last_sync'].",lang=".(int)$lang.( $asset['xml_receive_all'] == 0 ? "" : ",skipid=true" ) );
		$GLOBALS['debug']->notice( "Asset advisories fetched" );

		return $advisories['data'];
	}

	/**
	 * Function for notifying contacts, except position 1, which is the main account details.
	 * @param accountId
	 *	Integer account id
	 * @param criticality
	 *	Integer criticality, used to determine which user(s) should receive an sms about this advisory.
	 * @param lang
	 *	Integer language id, default 1 - English ( NOTE in the database, it might also be 2 if the user wants both german and english ), 5 - German
	 * @return
	 *	Array having the "rcpt" set, for the email recipients; and "nb" for the mobile recipients
	*/
	function notifyContacts( $accountId, $criticality, $lang = 1 ) {
		$emails = $this->database['ca']->queryGetRows("SELECT contact_method_value, email_xml FROM ca.contact_method WHERE pos != 1 AND account_id = '".(int)$accountId."' AND contact_method_type = 1 AND ".( $lang == 1 ? " ( lang_eng = 2 OR lang_eng = 1 ) " : " AND alt_lang_id = '".(int)$lang."'" ) ); // Email info
		$rcpt = "";
		$xrcpt = "";
		for ( $i = 0; $i < count( $emails ); $i++ ) {
			if ( $emails[$i]['email_xml'] == 1 ) {
				$xrcpt .= ( $xrcpt != "" ? "," : "" ) . $emails[$i]['contact_method_value'];
			} else {
				$rcpt .= ( $rcpt != "" ? "," : "" ) . $emails[$i]['contact_method_value'];
			}
		}

		$mobiles = $this->database['ca']->queryGetRows("SELECT contact_method_value, email_xml FROM ca.contact_method WHERE pos != 1 AND account_id = '".(int)$accountId."' AND contact_method_type = 2 AND contact_method_critical <= '".(int)$criticality."'"); // Email info
		$nb = array();
		for ( $i = 0; $i < count( $mobiles ); $i++ ) {
			$nb = array_push( $mobiles[$i]['contact_method_value'] );
		}

		return array(
			"rcpt" => $rcpt
			,"xrcpt" => $xrcpt
			,"nb" => $nb
		);
	}

	/**
	 * Function for deploying advisories to users
	 * @param advisories
	 *	Array of advisory data, each key having at least they vuln_id set.
	 * @param asset
	 *	Array same as for fetchAdvisories
	 * @param xml
	 *	Boolean send xml or not
	 * @param lang
	 *	Inttger language ( 1 - english, 5 - german )
	*/
	private function deployAdvisories( $advisories, $asset, $lang = 1, $xml = false ) {
		// Send each and every advisory to the selected user
		$user = $GLOBALS['users']->getDetails( $asset['account_id'] );
		$isSMSEnabled = (EDITION == HOSTED_EDITION || (defined('SMS_ENABLED') && SMS_ENABLED));
		$email = $user->contact->email;
		$mobile = $user->contact->mobile;
		if ( $email == "" ) {
			return false; // Cannot send, invalid email. How did this happen?
		}

		for ( $i = 0; $i < count( $advisories ); $i++ ) {
			// Prepare mail
			$values = $this->fetchValues( $advisories[$i]['vuln_id'], $xml );
			if ( $lang == 1 ) {
				$template = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->newAssetAdvisoryEmail['content'], $values, true );
				$subject = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->newAssetAdvisoryEmail['subject'], $values, true );
				$smsText = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->smsText['content'], $values );
				$from = $GLOBALS['misc']->newAssetAdvisoryEmail['from'];
				// Push mail to main account
				$GLOBALS['misc']->sendMail( $from, $email, $subject, $template);
				// Send SMS to main account
				if ( $mobile != "" && $isSMSEnabled) {
					$GLOBALS['sms']->send( array( $mobile ), $smsText, $advisories[$i]['vuln_id'] );
				}
			} elseif ( $lang == 5 ) {
				$template = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->newAssetAdvisoryEmailGerman['content'], $values, true );
				$subject = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->newAssetAdvisoryEmailGerman['subject'], $values, true );
				$smsText = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->smsText['content'], $values );
				$from = $GLOBALS['misc']->newAssetAdvisoryEmail['from'];
			}
			// Add to processlist
			if ( $asset['approval_method'] == 1 ) {
				// Auto approve
				$GLOBALS['assetProcesslist']->addToProcesslist( $user->cst_id, $asset['account_id'], $advisories[$i]['vuln_id'], $asset['asset_id'], true, true );
				// Notify all sub contacts
				$contacts = $this->notifyContacts( $asset['account_id'], $values['vuln_critical_boolean'], $lang );
				if ( $contacts['rcpt'] != "" ) {
					$GLOBALS['misc']->sendMail( $from, $contacts['rcpt'], $subject, $template );
				}
				// XML if any
				if ( $contacts['xrcpt'] != "" ) {
					$xmlValues = $this->fetchValues( $advisories[$i]['vuln_id'], true );
					$xmlTemplate = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->newAdvisoryXMLEmail['content'], $xmlValues, true );
					$GLOBALS['misc']->sendMail( $from, $contacts['xrcpt'], $subject, $xmlTemplate );
				}
				// Send SMS to matching contacts ( vuln_critical_boolean >= the selected minimum criticality )
				if ( count( $contacts['nb'] ) != 0  && $isSMSEnabled) {
					$GLOBALS['sms']->send( array( $contacts['nb'] ), $smsText, $advisories[$i]['vuln_id'] );
				}
			} else {
				// Manual approve.
				$GLOBALS['assetProcesslist']->addToProcesslist( $user->cst_id, $asset['account_id'], $advisories[$i]['vuln_id'], $asset['asset_id'], false, true  );
			}
			$GLOBALS['debug']->notice( "User notified, advisory pushed to processlist" );
		}

		// Mark as synchronized
		$GLOBALS['debug']->notice( "Asset List synchronized!" );
		$this->markSynchronized( $asset['asset_id'] );
	}

	/**
	 * Main deploy function.
	*/
	public function deploy() {
		$assets = $this->fetchAssetLists();
		for ( $i = 0; $i < count( $assets ); $i++ ) {
			$advisories = $this->fetchAdvisories( $assets[$i] ); // English
			$advisoriesGerman = $this->fetchAdvisories( $assets[$i], 5 ); // German
			if ( $GLOBALS['users']->isExpired( $assets[$i]['account_id'] ) ) {
				$this->deployAdvisories( $advisories, $assets[$i] );
				if ( count( $advisoriesGerman ) != 0 ) {
					$this->deployAdvisories( $advisoriesGerman, $assets[$i], 5 );
				}
			}
		}
		$GLOBALS['debug']->notice( "Advisories have been deployed to users and pushed to processlists" );
	}
}