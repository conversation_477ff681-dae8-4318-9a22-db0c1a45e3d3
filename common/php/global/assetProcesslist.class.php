<?php
/**
 * @file assetProcesslist.class.php
*/

/**
 * Provides VI asset list functionality.
*/
class assetProcesslist {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
		$this->db = new DB( DB_HOST_CA, "ca", DB_USER_CA, DB_PASS_CA );

	}

	/**
	 * Function for changing advisory status. Used for VI Intelligence Feed.
	 * @param accountId
	 *	Integer or array of integers account id
	 * @param vulnId
	 *	Integer vulnerability id
	 * @param status
	 *	Integer status. Values: 0 - Open, 3 - Waiting, 2 - Handled, 4 - Irrelevant
	 * @param customerId
	 *	Integer customer id ( for the active account )
	 * @param comment
	 *	String comment
	 * @param assets
	 *	String comma separated list of assets to change status for
	 * @param priority
	 *	Integer priority
	 * @param isEscaped
	 *	Boolean true or false if escaped or not for SQL
	*/
	function changeViAdvisoryStatus( $accountId, $vulnId, $status, $customerId, $comment, $assets, $priority, $isEscaped = false ) {
		if ( $isEscaped !== true ) {
			$comment = $this->database['ca']->escapeString( $comment );
		}

		$username = "";
		if ( $GLOBALS['login']->getSessionValue('shadowing') === true ) {
			$auth = $GLOBALS['login']->getSessionValue('auth'); // Get the REAL user's info
			$username = $this->database['ca']->escapeString( $auth['account_username'] );
		} else {
			// Get the logged in user's info
			$username = $this->database['ca']->escapeString( $GLOBALS['account_username'] );
		}

		$assets = $GLOBALS['wizard']->buildSQLIn( $assets );

		$accountIds = "";
		if ( is_array( $accountId ) ) {
			for ( $i = 0; $i < count( $accountId ); $i++ ) {
				$accountIds .= ( $i != 0 ? "," : "" ).(int)$accountId[$i];
			}
		}

		// Loop over all processlist entries with 'vuln_id' associated
		$result = $this->database['ca']->getRows( "ca.processlist", "account_id ".( is_array( $accountId ) ? " IN (".$accountIds.")": "= '".(int)$accountId."'" )." && vuln_id = '" . (int)$vulnId . "' AND asset_id IN ( ".$assets." )" );

		for ( $i = 0; $i < count( $result ); $i++ ) {

			if ( $status == "" ) {
				// Get current status
				$curStatus = $this->database['ca']->getRow( "ca.processlist", "asset_id = '".(int)$result[$i]['asset_id']."' && cst_id = '" . (int)$customerId . "' && vuln_id = '" . (int)$vulnId . "'" );
				$status = (int)$curStatus['processlist_status'];
			}

			// Why do we always update? we should only update when the status arg has a value..

			// Update processlist status
			$this->database['ca']->edit( "ca.processlist", array(
				"processlist_status" => (int)$status
			), 1, "asset_id = '".(int)$result[$i]['asset_id']."' && cst_id = '" . (int)$customerId . "' && vuln_id = '" . (int)$vulnId . "'", true );


			// Insert into adv_comment_log
			$query = "INSERT INTO ca.adv_comment_log SET
				asset_id = '".(int)$result[$i]['asset_id']."'
				,vuln_id = '".(int)$vulnId."'
				,last_comment = '".$comment."'
				,last_status = '".(int)$status."'
				,changed = NOW()
				,username = '".$username."'
			";
			$this->database['ca']->query( $query );

			// Comment
			$this->database['ca']->query("DELETE FROM ca.adv_comment WHERE asset_id = '" . (int)$result[$i]['asset_id'] . "' && vuln_id = '" . (int)$vulnId . "'");

			// Insert into adv_comment_log
			$this->database['ca']->edit( "ca.adv_comment", array(
				"asset_id" => (int)$result[$i]['asset_id']
				,"vuln_id" => (int)$vulnId
				,"comment" =>  $this->database['ca']->escapeString( $comment )
			), 0, "", true );

			if ( $priority != "" ) {
				// Update processlist priority
				$this->database['ca']->edit( "ca.processlist", array(
					"internal_priority" => (int)$priority
				), 1, "asset_id = '".(int)$result[$i]['asset_id']."' && cst_id = '" . (int)$customerId . "' && vuln_id = '" . (int)$vulnId . "'", true );
			}
		}
		$GLOBALS['debug']->notice( "Advisory status changed" );
	}

	private function getLastPriorityComment( $vuln_id, $priority ) {
		// STUB
	}

	/**
	 * Function for adding item to process list.
	 * @param customerId
	 *	Integer customer id
	 * @param accountId
	 *	Integer account id
	 * @param vulnId
	 *	Integer vuln id
	 * @param assetId
	 *	Integer asset id
	 * @param approved
	 *	Boolean approved, default to true
	 * @param reopen
	 *	Boolean reopen or not, false by default
	 * @return
	 *	Boolean true if success, false if vuln already in asset list.
	*/
	function addToProcesslist( $customerId, $accountId, $vulnId, $assetId, $approved = true, $reopen = false ) {
		$result = $this->database['ca']->numRows( "ca.processlist", "account_id = '".(int)$accountId."' AND asset_id = '".(int)$assetId."' AND vuln_id = '".(int)$vulnId."'" );
		if ( $result != 0 ) {
			if ( $reopen == true ) {
				// Open it again.
				$this->database['ca']->query("update ca.processlist set processlist_status = 0 where cst_id = '" . (int)$customerId . "' && vuln_id = '" . (int)$vulnId . "' && asset_id = '" . (int)$assetId . "' && account_id = '" . (int)$accountId . "' limit 1");
				// Fetch existing data.
				$info = $this->database['ca']->queryGetRows("SELECT * FROM ca.processlist WHERE account_id = '".(int)$accountId."' AND vuln_id = '".(int)$vulnId."' AND asset_id = '".(int)$assetId."' LIMIT 1");
				// If the advisory is marked as HANDLED or IRRELEVANT
				if ( $info[0]['processlist_status'] == 2 || $info[0]['processlist_status'] == 4 ) {
					// Advisory was marked as "Handled" therefore reset 'ADDED' timestamp
					$this->database['ca']->query("update ca.processlist set added = now() where cst_id = '" . (int)$customerId . "' && vuln_id = '" . (int)$vulnId . "' && asset_id = '" . (int)$assetId . "' && account_id = '" . (int)$accountId . "' limit 1");
				}
				// Insert entry into adv_comment_log that advisory was reset
				$this->database['ca']->query("INSERT INTO ca.adv_comment_log SET asset_id = '" . (int)$assetId . "', vuln_id = '" . (int)$vulnId . "', last_status = 0, changed = NOW(), username = 'Secunia System'");
				$GLOBALS['debug']->notice( "Advisory reopened!" );
				return true;
			}
			$GLOBALS['debug']->notice( "Cannot add advisory to processlist, already there!" );
			return false;
		} else {
			$query = "INSERT INTO
					ca.processlist
				SET
					processlist_status = 0
					,cst_id = '".(int)$customerId."'
					,vuln_id = '".(int)$vulnId."'
					,account_id = '".(int)$accountId."'
					,added = NOW()
					,admin_approved = '".( $approved == true ? '1' : '0' )."'
					,asset_id = '".(int)$assetId."'
			";
			$this->database['ca']->query( $query );
			$GLOBALS['debug']->notice( "Advisory added to processlist" );
			return true;
		}
	}

	/**
	 * Function for approving or rejecting a pending asset item.
	 * @param processlistId
	 *	Integer entry id, in the processlist
	 * @param type
	 *	Integer type of action ( 0 - delete, 1 - approve )
	 * @param comment
	 *	String comment, @deprecated
	*/
	function approve( $processlistId, $type, $comment = "" ) {


		/*
		 * @todo:
		 * Delete this ticket and re issue the advisory for the account, since the asset list might have
		 * changed.
		 * For now we are updating the admin approved field so the ticket will show up on the customers end
		 * even if the asset list changed. But we are reissuing the advisory which means that the alerts will
		 * not be generated if the vuln doesn't affect the asset list anymore.
		 */

		if ( $type == 0 ) {
			//$this->database['ca']->query("DELETE FROM ca.processlist WHERE processlist_id = '".(int)$processlistId."' LIMIT 1");
            $this->database['ca']->query("UPDATE ca.processlist SET admin_approved = 2 WHERE processlist_id = '".(int)$processlistId."' LIMIT 1");
		} else {
			$this->database['ca']->query("UPDATE ca.processlist SET admin_approved = 1 WHERE processlist_id = '".(int)$processlistId."' LIMIT 1");

			// Get the ticket details
			$rows = $this->db->select()
				->columns( array( 'vuln_id', 'account_id', 'asset_id', 'internal_priority' ) )
				->from( 'processlist' )
				->where( array( 'processlist_id' => $processlistId  ) )
				->exec();
			$row = $rows[0];

			$manualApproveOptions = array(
										  'filter' => array( 'account_id' => $row['account_id']
															 ,'asset_id' => $row['asset_id'] )
										  ,'priority' => $row['internal_priority'] // Ticket Priority, internal_priority
										  ,'comment' => $comment // The adv_comment_log
										  );

			AdvisorySender::processAdvisory( (int) $row['vuln_id'], Advisory::LANG_ENGLISH, $manualApproveOptions );



		}
	}

    /**
     * Function for re-approving or deleting a rejected asset item.
     * @param processlistId
     *	Integer entry id, in the processlist
     * @param type
     *	Integer type of action ( 0 - delete, 1 - approve )
     * @param comment
     *	String comment, @deprecated
     */
    function handleRejected( $processlistId, $type, $comment = "" ) {


        /*
         * @todo:
         * Delete this ticket and re issue the advisory for the account, since the asset list might have
         * changed.
         * For now we are updating the admin approved field so the ticket will show up on the customers end
         * even if the asset list changed. But we are reissuing the advisory which means that the alerts will
         * not be generated if the vuln doesn't affect the asset list anymore.
         */

        if ( $type == 0 ) {
            $this->database['ca']->query("DELETE FROM ca.processlist WHERE processlist_id = '".(int)$processlistId."' LIMIT 1");
        } else {
            $this->database['ca']->query("UPDATE ca.processlist SET admin_approved = 0 WHERE processlist_id = '".(int)$processlistId."' LIMIT 1");
        }
    }
}

?>