<?php

/**
 * Factory class for instantiating different logger classes.
 */
final class Logger {
	/**
	 * Available logger types
	 */
	const VOID = 0;
	const SYSLOG = 1;
	const PRIVATELOG = 2;
	const OUTPUTINFOLOG = 3;

	/**
	 * Prevent instantiation by making the constructor private
	 * We could have used an abstract class but it is still possible to extend
	 * it as it can't be 'final'
	 */
	private function __construct() {}

	/*
	 * Instantiated Logger's identifier and the logger itself
	 */
	private static $identifier;
	private static $logger;

	private static $defaultLoggerType = Logger::SYSLOG;

	/**
	 * Use this function to change the default logger type initialized when
	 * using the factory method Logger::create()
	 *
	 * @param int $loggerType Should be one of the defined Logger Types
	 * @return boolean
	 */
	public static function setDefaultLoggerType( $loggerType ) {
		switch( $loggerType ) {
		case Logger::VOID:
			self::$defaultLoggerType = Logger::VOID;
			break;
		case Logger::SYSLOG:
			self::$defaultLoggerType = Logger::SYSLOG;
			break;
		case Logger::PRIVATELOG:
			self::$defaultLoggerType = Logger::PRIVATELOG;
			break;
		case Logger::OUTPUTINFOLOG:
			self::$defaultLoggerType = Logger::OUTPUTINFOLOG;
			break;
		default:
			return false;
		}
		return true;
	}

	/**
	 * Get the default logger type
	 *
	 * @return int
	 */
	public static function getDefaultLoggerType() {
		return self::$defaultLoggerType;
	}

	/**
	 * Factory method for instantiating a specific implementation of PSR-3
	 * LoggerInterface. If no logger type is specified, the default logger type
	 * will be instantiated and returned.
	 * However, if the global configuration parameter is set for filtering for
	 * only a specific customer id, then a NullLogger is returned
	 *
	 * @param int      $identifier Identifier for the log object
	 * @param int|null $loggerType Specifying the type of logger to be created. If none is specified, SYSLOG is used.
	 * @param array    $cfg        Configuration for the logger specified.
	 *
	 * @return Psr\Log\LoggerInterface Logger object
	 * @throws Exception Throws exception if wrong logger type is specified
	 */
	public static function create( $identifier, $loggerType = null, array $cfg = array() ) {
		$identifier = (int) $identifier;

		// Filtering on the customer id
		$logLevel = LOG_LEVEL_GENERAL;
		if ( defined('LOG_FILTER_CST_ID') ) {
			$cstIds = explode( ',', LOG_FILTER_CST_ID );
			if ( in_array( $identifier, $cstIds ) ) {
				$logLevel = LOG_LEVEL_CST;
			}
		}

		$options = isset( $cfg['options'] ) ? $cfg['options'] : LOG_PID;
		$facility = isset( $cfg['facility'] ) ? $cfg['facility'] : LOG_LOCAL2;

		$logMessagePrefix = '';
		if ( defined('LOG_MESSAGE_PREFIX') && ( is_numeric( LOG_MESSAGE_PREFIX ) || is_string( LOG_MESSAGE_PREFIX ) ) ) {
			$logMessagePrefix = LOG_MESSAGE_PREFIX;
		}

		if ( self::$logger === null ) {
			self::$identifier = $identifier;

			if ( is_null( $loggerType ) ) {
				$loggerType = self::$defaultLoggerType;
			}

			$logger = null;
			switch( $loggerType ) {
				case Logger::VOID:
					$logger = new Psr\Log\NullLogger();
					break;

				case Logger::SYSLOG:
					$logger = new Syslog();
					break;

				case Logger::PRIVATELOG:
					$logger = new Privatelog();
					break;

				case Logger::OUTPUTINFOLOG:
					$logger = new OutputInfoLog( $identifier, $options, $facility, $logMessagePrefix );
					break;

				default:
					throw new Exception( 'Invalid Logger Type specified' );
					break;
			}

			self::$logger = $logger;
		}

		self::$logger->setLogLevel( $logLevel );
		self::$logger->setIdentifier( $identifier );
		self::$logger->setOptions( $options );
		self::$logger->setFacility( $facility );
		self::$logger->setMessagePrefix( $logMessagePrefix );

		return self::$logger;
	}

	/**
	 * Method to call for logging enablement irrespective of config setings (CSIL-9770) 
	 * @param int      $identifier Identifier for the log object
	 * @param int|null $loggerType Specifying the type of logger to be created. If none is specified, SYSLOG is used.
	 * @param array    $cfg        Configuration for the logger specified.
	 *
	 * @return Psr\Log\LoggerInterface Logger object
	 * @throws Exception Throws exception if wrong logger type is specified
	 */
	public static function createEnable( $identifier, $loggerType = null, array $cfg = array() ) {
		$identifier = (int) $identifier;
		
		$logLevel = LOG_LEVEL_CST;
		
		$options = isset( $cfg['options'] ) ? $cfg['options'] : LOG_PID;
		$facility = isset( $cfg['facility'] ) ? $cfg['facility'] : LOG_LOCAL2;
		
		$logMessagePrefix = '';
		if ( defined('LOG_MESSAGE_PREFIX') && ( is_numeric( LOG_MESSAGE_PREFIX ) || is_string( LOG_MESSAGE_PREFIX ) ) ) {
			$logMessagePrefix = LOG_MESSAGE_PREFIX;
		}
		
		if ( self::$logger === null ) {
			self::$identifier = $identifier;
			
			if ( is_null( $loggerType ) ) {
				$loggerType = self::$defaultLoggerType;
			}
			
			$logger = null;
			switch( $loggerType ) {
				case Logger::VOID:
					$logger = new Psr\Log\NullLogger();
					break;
					
				case Logger::SYSLOG:
					$logger = new Syslog();
					break;
					
				case Logger::PRIVATELOG:
					$logger = new Privatelog();
					break;
					
				case Logger::OUTPUTINFOLOG:
					$logger = new OutputInfoLog( $identifier, $options, $facility, $logMessagePrefix );
					break;
					
				default:
					throw new Exception( 'Invalid Logger Type specified' );
					break;
			}
			
			self::$logger = $logger;
		}
		
		self::$logger->setLogLevel( $logLevel );
		self::$logger->setIdentifier( $identifier );
		self::$logger->setOptions( $options );
		self::$logger->setFacility( $facility );
		self::$logger->setMessagePrefix( $logMessagePrefix );
		
		return self::$logger;
	}
	
	/**
	 * Factory method for creating a PSR-3 Null Logger
	 */
	public static function createNullLogger() {
		return new Psr\Log\NullLogger();
	}
}
