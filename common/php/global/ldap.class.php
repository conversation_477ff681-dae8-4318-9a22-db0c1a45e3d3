<?php

  /*
   * Class that provides LDAP related functionality
   *
   * Usage:
   * -----
   *
   * 1. Connect:
   *    -------
   *	Connect to the LDAP server using the LDAP_HOST_URL
   *	e.g.
   *		ldap://***********:389
   *		ldaps://***********:636
   *
   * 2. Bind:
   *    ----
   *	Bind to the server to allow for querying the directory.
   *	Set the credentials using the following params:
   *	- LDAP_BIND_DN
   *	- LDAP_BIND_PASS
   *
   * 3. User Bind:
   *    ---------
   *	To authenticate a specific user, bind that user to the server with its credentials
   *
   *
   * @todo:
   * 	The constructor should take LDAP parameters instead of relying on globally set
   *	params. The globally set params should be passed to the constructor of the class
   *	when required. This allows for unit testing.
   *
   */

class LDAP {

	public function connect() {
		if( !defined('LDAP_HOST_URL') ) {
			$GLOBALS['debug']->notice( "Unable to connect to the LDAP server as LDAP_HOST_URL are not configured" );
			return FALSE;
		}

		$GLOBALS['debug']->notice( "Connecting to remote LDAP server (" . LDAP_HOST_URL . ")" );
		$linkId = ldap_connect( LDAP_HOST_URL );

		if ( !$linkId ) {
			return FALSE;
		}

		// Require LDAPv3
		ldap_set_option( $linkId, LDAP_OPT_PROTOCOL_VERSION, 3 );
		if ( defined( 'LDAP_REFERALS_FLAG' ) ) {
			ldap_set_option( $linkId, LDAP_OPT_REFERRALS, LDAP_REFERALS_FLAG );
		}
		return $linkId;
	}

	public function bind( $linkId ) {

		if ( empty( $linkId ) ){
			return false;
		}

		if ( !defined('LDAP_BIND_USER') && !defined('LDAP_BIND_PASS') ) {

			// Bind anonymously to retrieve the user's dn
			// Note: Anonymous binds might restrict the user the query the LDAP server
			$GLOBALS['debug']->notice( "Binding anonymously to remote LDAP directory" );
			$bind = @ldap_bind( $linkId );

		} else {

			$GLOBALS['debug']->notice( "Binding to LDAP directory as '" . LDAP_BIND_DN . "'" );
			$bind = @ldap_bind( $linkId, LDAP_BIND_DN, UTIL::decryptConfigConstant(constant('LDAP_BIND_PASS')) );
		}

		if( $bind ) {
			return TRUE;
		}

		return FALSE;
	}

	public function userBind( $linkId, $dn, $password ) {

		if ( empty( $linkId ) || empty( $dn ) || empty( $password ) ){
			return false;
		}

		$GLOBALS['debug']->notice( "Binding to LDAP Directory" );
		return @ldap_bind($linkId, $dn, $password);
	}

	/**
	 *
	 * The function takes in the username and the password, searches for the corresponding DN using the username
	 * and authenticates the user using the DN and the password.
	 *
	 * TRUE is returned in case of success i.e. user authenticated, otherwise FALSE is returned.
	 *
	 * The function only authenticates the user using the supplied credentials and the connection is closed
	 * afterwards.
	 *
	 */
	public function authenticate( $linkId, $username, $password ) {

		if ( empty( $linkId )  || empty( $username ) || empty( $password ) ){
			$GLOBALS['debug']->error( "LDAP authenticate() - Values can not be empty." );
			return false;
		}

		$dn = $this->findDistinguishedName( $linkId, $username );

		if ( !@$this->userBind( $linkId, $dn, $password ) ){
			$GLOBALS['debug']->error( "LDAP authenticate() - Error during userbind." );
			return false;
		} else {
			return true;
		}

		return false;
	}

	/**
	 * Search for the user in the ldap tree.
	 * The function returns true if the user exists
	 */
	public function findDistinguishedName( $linkId, $username ) {

		if ( empty( $linkId ) ){
			return false;
		}

		if (empty($username)) {
			$GLOBALS['debug']->error("LDAP findDistinguishedName() - Username is invalid." );
			return false;
		}

		// Bind to LDAP - bind() will decide whether to bind with DN and password, or anonymously
		$ret = $this->bind( $linkId );
		if ( !$ret ){
			$GLOBALS['debug']->error( "LDAP findDistinguishedName() - Username is invalid." );
			return false;
		}

		// Search uid entry for supplied uid and retrieve the user's dn
		$escapedAttrib = ldap_escape(LDAP_UID_ATTR, LDAP_NON_ESCAPED_CHARS, LDAP_ESCAPE_FILTER);
		$escapedUsername = ldap_escape($username, LDAP_NON_ESCAPED_CHARS, LDAP_ESCAPE_FILTER);

		$filter = "({$escapedAttrib}={$escapedUsername})";
		$fields = array( "dn" );
		$res = ldap_search( $linkId, LDAP_BASE_DN, $filter, $fields );
		if ( !$res ) {
			$GLOBALS['debug']->error( "LDAP findDistinguishedName() - Error during ldap_search." );
			return false;
		}

		// Ensure one and only one entry returned by the LDAP server
		$nResults = ldap_count_entries( $linkId, $res );
		if ( $nResults > 1 || $nResults == 0 ) {
			$GLOBALS['debug']->error( "LDAP findDistinguishedName() - Couldn't find a unique dn." );
			return false;
		}

		$info = ldap_get_entries( $linkId, $res );
		$dn = $info[0]['dn'];

		if ( !empty( $dn ) ) {
			$GLOBALS['debug']->error( "LDAP findDistinguishedName() - dn found." );
			return $dn;
		}

		return false;

	}

	public function close( $linkId ) {
		ldap_close( $linkId );
		$GLOBALS['debug']->notice("Link closed");
	}

}
