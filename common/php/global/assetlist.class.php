<?php

  /**
   * @file assetlist.class.php
   *
   * Provides basic Asset Lists handling functionality.
   * Requires:
   *	- debug.class.php
   *	- users.class.php
   * 	- util.class.php
   */

class ASSETLIST {

	protected $database = array();

	const UNKNOWN_ERROR = -1;
	const SUCCESS = 0;
	const INVALID_ARGS = 1;
	const NAME_EXISTS = 2;
	const NOT_ALLOWED = 3;
	const ASSETLIST_IS_RECEIVE_ALL = 4;

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA ); // deprecated, use $this->db instead
		$this->db = new DB( DB_HOST_CA, DB_NAME_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for cloning an asset.
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param assetName
	 *	String The name of the newly copied asset list.
	 * @return
	 *	Boolean true or false if success or not
	 *
	 * Security Note:
	 * -------------
	 * - For integers we can use quote() after casting, without having to think about
	 * the character set.
	 * - However, for strings, we should always use named parameters.
	 *
	 *
     * @todo: why not specify the accountId, newId etc as part of the where instead of
     * hardcoding them as column values?
	 *
	 */
	public function cloneAsset( $assetId, $accountId, $assetName ) {
		if ( empty( $assetId ) || empty( $accountId ) || ( strlen( $assetName ) < 1 )  ) {
			return false;
		}

		if ( !is_numeric( $assetId ) || $assetId <= 0
			 || !is_numeric( $accountId ) || $accountId <= 0 ) {
			return false;
		}

		/*
		 * @todo:
		 * are we using this group anywhere?
		 * The following query is part of ported code but maybe it isn't required anymore.
		 */
		$groupId = $this->db->insert()
			->into( "vtse_asset_groups" )
			->columns( array( "name", "account_id" ) )
			->select(
					 $this->db->select()
					 ->columns( array( "name"
									   , $this->db->quote( (int) $accountId ) ) )
					 ->from( "vtse_asset_groups" )
					 ->where( array( "group_id" => $this->getAssetGroupId( $assetId ) ) )
					 )
			->exec();

		$insertQuery = "INSERT INTO vi_assets
			(account_id
			,asset_name
			,xml_receive_all
			,xml_auth_key
			,xml_last_request
			,group_id
			,general_notification_threshold
			,security_requirements)
		SELECT
			:account_id
		    ,:asset_name
		    ,xml_receive_all
		    ,:auth_key
			,xml_last_request
			,group_id
			,general_notification_threshold
			,security_requirements
		FROM vi_assets
		WHERE asset_id = :asset_id";

		$this->db->execRaw( $insertQuery
							,array( ':account_id' => $accountId
									,':asset_name' => $assetName
									,':auth_key' => $GLOBALS['util']->generateRandomId( 32 )
									,':asset_id' => $assetId ) );


		$newAssetId = $this->db->lastInsertId();
		if ( !$newAssetId ) {
			return false;
		}

		// Copy the process list

		$this->db->insert()
			->into( "processlist" )
			->columns( array( "processlist_status"
							  ,"cst_id"
							  ,"vuln_id"
							  ,"account_id"
							  ,"added"
							  ,"admin_approved"
							  ,"internal_priority"
							  ,"asset_id" ) )
			->select(
					 $this->db->select()
					 ->columns( array( "processlist_status"
									   ,"cst_id"
									   ,"vuln_id"
									   , $this->db->quote( (int) $accountId )
									   ,"NOW()"
									   ,"admin_approved"
									   ,"internal_priority"
									   , $this->db->quote( (int) $newAssetId )
									   ) )
					 ->from( "processlist" )
					 ->where( array( "asset_id" => $assetId ) )
					 )
			->exec();

		// Copy the products

		$this->db->insert()
			->into( "vi_asset_products" )
			->columns( array( "asset_id"
							  ,"os_soft_id" ) )
			->select(
					 $this->db->select()
					 ->columns( array( $this->db->quote( (int) $newAssetId )
									   ,"os_soft_id" ) )
					 ->from( "vi_asset_products" )
					 ->where( array( "asset_id" => $assetId ) )
					 )
			->exec();

		// Copy the vendors

		$this->db->insert()
			->into( "vi_asset_vendors" )
			->columns( array( "asset_id"
							  ,"vendor_id" ) )
			->select(
					 $this->db->select()
					 ->columns( array( $this->db->quote( (int) $newAssetId )
									   ,"vendor_id" ) )
					 ->from( "vi_asset_vendors" )
					 ->where( array( "asset_id" => $assetId ) )
					 )
			->exec();

		return $newAssetId;
	}

	/**
	 * Function for setting a new asset list owner.
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
 	 */
	public function changeOwner( $assetId, $accountId ) {
		if ( !is_numeric( $assetId ) || $assetId <= 0
			 || !is_numeric( $accountId ) || $accountId <= 0 ) {
			return false;
		}

		$numAffected = $this->db->update()
			->table( "vi_assets" )
			->set( array( "account_id" => $accountId ) )
			->where( array( "asset_id" =>  $assetId
							,"external" => 0 ) )
			->exec();

		$success = ($numAffected > 0 );

		if ( $success ) {
			$this->db->update()
				->table( "vtse_asset_groups" )
				->set( array( "account_id" => $accountId ) )
				->where( array( "group_id" => $this->getAssetGroupId( $assetId ) ) )
				->exec();

			$this->db->update()
				->table( "processlist" )
				->set( array( "account_id" => $accountId ) )
				->where( array( "asset_id" =>  $assetId ) )
				->exec();
		}
		return $success;
	}

	/**
	 * Check for asset name duplicates
	 *
	 * @param accountId
	 *	Integer account id
	 * @param assetName
	 *	String asset name
	 * @param exceptionAssetId
	 *	Integer Check for names except for the name of this asset id. Used when the asset
	 *  is being modified and the name doesn't change.
	 *
	 * @return
	 *	Boolean true or false if name if valid or not
	*/
	public function checkName( $accountId, $assetName, $exceptionAssetId = false ) {

		$condition = array( "account_id" => $accountId
							 ,"asset_name" => $assetName );

		if ( $exceptionAssetId !== false ) {
			$condition['asset_id'] = Exp::ne( $exceptionAssetId );
		}

		$rows = $this->db->select()
			->from( "vi_assets" )
			->where( $condition )
			->exec();

		if ( !empty( $rows ) ) {
			return false;
		}

		return true;
	}

    /**
     * Check for asset name duplicates
     *
     * @param accountId
     *	Integer account id
     * @param assetName
     *	String asset name
     * @param exceptionAssetId
     *	Integer Check for names except for the name of this asset id. Used when the asset
     *  is being modified and the name doesn't change.
     *
     * @return
     *	Boolean true or false if name if valid or not
     */
    public function checkNameForAsset( $assetId, $assetName, $exceptionAssetId = false ) {
        $ownerId = $this->getAssetAccountId( $assetId );

        $condition = array( "account_id" => $ownerId
        ,"asset_name" => $assetName );

        if ( $exceptionAssetId !== false ) {
            $condition['asset_id'] = Exp::ne( $exceptionAssetId );
        }

        $rows = $this->db->select()
            ->from( "vi_assets" )
            ->where( $condition )
            ->exec();

        if ( !empty( $rows ) ) {
            return false;
        }

        return true;
    }

	/**
	 * Make sure the asset belongs to selected account id.
	 * @param accountId
	 *	Integer account id
	 * @param assetId
	 *	Integer asset id
	 * @return
	 *	Boolean true or false if it belongs or not.
	*/
	function checkAsset( $accountId, $assetId, $notExternal = false ) {

		if ( !is_numeric( $accountId )
			 || $accountId <= 0
			 || !is_numeric( $assetId )
			 || $assetId <= 0 ) {
			return false;
		}

		$condition = "asset_id = '".(int)$assetId."'";
		if ( $notExternal ) {
			$condition .= "  AND external = 0";
		}
		$ownerAccountId = $this->database['ca']->getRowValue("ca.vi_assets", "account_id", $condition );

		if ( $accountId == $ownerAccountId ) {
			return true;
		} else {
			$GLOBALS['debug']->error( "Do not have write permissions for the Vulnerability Intelligence Manager Asset" );
			return false;
		}
	}

    /**
     * Function for fetching all sub users.
     */
    protected function fetchAllUsers( $accountId, $includeAllUsersOption = false) {

        $subAccountIds = $GLOBALS['users']->getSubAccounts( $accountId, true );

        $results = array();
        $usernames = array();

        for ( $i = 0; $i < count( $subAccountIds ); $i++ ) {
            array_push($results, array( "account_id" => (int)$subAccountIds[$i]['account_id'], "account_username" => $subAccountIds[$i]['account_username']));
            $usernames[$i] = $subAccountIds[$i]['account_username'];
        }

        array_multisort($usernames, SORT_STRING, $results);

        if ( $includeAllUsersOption ){
            $allUserAccounts = array(
                "account_id" => 0,
                "account_username" => "All User Accounts"
            );

            array_unshift($results, $allUserAccounts);
        }

        return $results;
    }

    /**
     * Make sure the asset belongs to selected account id, or to a subaccount from the current account
     * @param accountId
     *	Integer account id
     * @param assetId
     *	Integer asset id
     * @return
     *	Boolean true or false if it belongs or not.
     */
    function checkAssetWithSubaccounts( $accountId, $assetId, $notExternal = false ) {

        if ( !is_numeric( $accountId )
            || $accountId <= 0
            || !is_numeric( $assetId )
            || $assetId <= 0 ) {
            return false;
        }

        $condition = "asset_id = '".(int)$assetId."'";
        if ( $notExternal ) {
            $condition .= "  AND external = 0";
        }
        $ownerAccountId = $this->database['ca']->getRowValue("ca.vi_assets", "account_id", $condition );

        if ( $accountId == $ownerAccountId ) {
            return true;
        }
        //if it's not the base account, we're checking if it's a subaccount
        $childrenAccounts = $this->fetchAllUsers( $accountId );
        foreach ($childrenAccounts as $childAccount){
            if ( $childAccount['account_id'] == $ownerAccountId ){
                return true;
            }
        }

        $GLOBALS['debug']->storeActionLog( "Do not have write permissions for the Vulnerability Intelligence Manager Asset", get_defined_vars() );
        return false;

    }

	/**
	 * Add a new asset.
	 *
	 * @param accountId
	 *  Integer
	 * @param assetName
	 * String
	 * @param values
	 *	Array Items to be added using DATABASE::edit.
	 *		Optional items: xml_receive_all, external
	 *
	 * @return
	 *	Integer or Boolean if success return the asset_id, else false
	*/
	public function newAsset( $accountId, $assetName, $params = array() ) {

		$response = array( "error_code" => self::UNKNOWN_ERROR, "asset_id" => 0 );

		/*
		 * Input Sanitization
		 */

		if ( !$accountId || !$assetName ) {
			$response['error_code'] = self::INVALID_ARGS;
			return $response;
		}

		$xmlReceiveAll = isset( $params['xml_receive_all'] ) ? (int) $params['xml_receive_all'] : 0;
		$external = isset( $params['external'] ) ? (int) $params['external'] : 0;


		/*
		 * Validation Checks before creating an Asset
		 */

		if ( $this->checkName( $accountId, $assetName ) === false ) {
			$response['error_code'] = self::NAME_EXISTS;
			return $response;
		}

		/*
		 * Get other required params for asset creation
		 */

		// Mark the asset as syncronised
		// todo: no need for a separate db call if we don't use the database edit()
		// function

		$row = $this->database['ca']->queryGetRow("SELECT " . ( $GLOBALS['util']->isUtcTime() ?  "UTC_TIMESTAMP()" : "NOW()" ) . " AS date");
		$lastSync = $row['date'];

		$xmlAuthKey = $GLOBALS['util']->generateRandomId( 32 );

		/*
		 * DB insertion
		 */

		$assetId = $this->db->insert()
			->into( "vi_assets" )
			->set( array( "account_id" => $accountId
						  ,"asset_name" => $assetName
						  ,"xml_receive_all" => $xmlReceiveAll
						  ,"last_sync" => $lastSync
						  ,"xml_auth_key" => $xmlAuthKey
						  ,"external" => $external
						  ) )
			->exec();

		if ( $assetId > 0 ) {
			$response['error_code'] = self::SUCCESS;
			$response['asset_id'] = $assetId;
			$GLOBALS['debug']->notice( "Vulnerability Intelligence Manager Asset added" );
		} else {
			$response['error_code'] = self::UNKNOWN_ERROR;
			$GLOBALS['debug']->error( "Vulnerability Intelligence Manager Asset Add failed" );
		}

		return $response;
	}

	/**
	 * Update an asset for the select account and asset id.
	 * @param accountId
	 *	Integer account id
	 * @param assetId
	 *	Integer asset id
	 * @param values
	 *	Array of values to be added.
	 *
	 * Note: This function only updates internal asset lists. Should be moved to the vim
	 * directory.
	*/
	function updateAsset( $accountId, $assetId, $values ) {
		if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId, true ) ) {
			return self::NOT_ALLOWED;
		}

		if ( isset( $values['asset_name'] ) ) {
			if ( $this->checkName( $accountId, $values['asset_name'], $assetId ) === false ) {
				return self::NAME_EXISTS;
			}
		}

		if ( $values['xml_receive_all'] == 1 ) { // Remove all selected products and vendors...
			$this->deleteAssetProducts( $assetId );
			$this->deleteAssetVendors( $assetId );
		}

		$this->db->update()
			->table( "vi_assets" )
			->set( $values )
			->where( array( /*"account_id" => $accountId,*/
							"asset_id" => $assetId ) )
			->exec();


		$GLOBALS['debug']->notice( "VIM Asset updated" );
		return self::SUCCESS;
	}

	/**
	 * Choose the right table for updating asset items ( vi_asset_vendors for vendors and vi_asset_products for products ).
	 * @param type
	 *	Integer type of table required
	 * @return
	 *	Array containing table and column names
	*/
	protected function constructTable( $type ) {
		switch ( $type ) {
			case 1:
				$table = "vi_asset_vendors";
				$column = "vendor_id";
				break;
			case 0:
				$table = "vi_asset_products";
				$column = "os_soft_id";
				break;
			default:
				return false;
				break;
		}
		return array( "table" => $table, "column" => $column );
	}

	/**
	 * Function for adding/deleting an item to/from the asset list ( product or vendor ). Only to be used by other functions in this class.
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param itemId
	 *	Integer item id ( product or vendor )
	 * @param action
	 *	Integer action type ( delete 0 or add 1 - default )
	 * @param type
	 *	Integer item type ( 0 - product ( default ), 1 - vendor )
	*/
	protected function handleItem( $assetId, $accountId, $itemId, $action = 1, $type = 0 ) {
		if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId, true ) ) {
			return false;
		}

		$data = $this->constructTable( $type );
		if ( $data === false ) {
			// Invalid type?
			return false;
		}
		$table = $data['table'];
		$column = $data['column'];

		if ( $action == 1 ) {
			$count = $this->database['ca']->numRows( "ca." . $table, "asset_id = '" . intval( $assetId ) . "' && " .
					$column ." = '" . intval( $itemId ) . "'");
			if ( $count != 0 ) {
				$GLOBALS['debug']->error( "VIM Asset delete failed" );
				return false;
			}
			$newItemId = $this->database['ca']->edit("ca.".$table, array(
				$column => (int)$itemId
				,"asset_id" => (int)$assetId
			) , DATABASE::INSERT, "", true );
			$GLOBALS['debug']->notice( "VIM Asset item added" );

			return $newItemId;
		} elseif ( $action == 0 ) {
			$query = "DELETE FROM ca.".$table." WHERE asset_id = '".(int)$assetId."' AND ".$column." = '".(int)$itemId."' LIMIT 1";
			$this->database['ca']->query( $query );
			$GLOBALS['debug']->notice( "VIM Asset item deleted" );
		}
	}


	private function deleteAssetProducts( $assetId ) {
		if ( !is_numeric( $assetId ) || $assetId <= 0 ) {
			return false;
		}
		// Delete products
		$status = $this->database['ca']->query("DELETE FROM ca.vi_asset_products WHERE asset_id = '" . (int)$assetId . "'");
		return $status;
	}

	private function deleteAssetVendors( $assetId ) {
		if ( !is_numeric( $assetId ) || $assetId <= 0 ) {
			return false;
		}
		// Delete vendors
		$status = $this->database['ca']->query("DELETE FROM ca.vi_asset_vendors WHERE asset_id = '" . (int)$assetId . "'");
		return $status;
	}

	/**
	 * Function for deleting an asset's products and vendors, but not the asset itself
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param notExternal
	 *	Boolean Flag to specify that the asset list should not be external.
	 */
	function deleteAssetProductsAndVendors( $assetId, $accountId, $notExternal = false ) {
		if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId, $notExternal ) ) {
			return false;
		}

		$this->deleteAssetProducts( $assetId );
		$this->deleteAssetVendors( $assetId );

		$GLOBALS['debug']->notice( "Products and Vendors for the VIM Asset List Deleted" );
	}

	private function deleteExternalAssetList( $assetListId, $accountId ) {
		if ( !is_numeric( $accountId ) || $accountId <= 0
			 || !is_numeric( $assetListId ) || $assetListId <= 0 ) {
			return false;
		}

		$this->database['ca']->query("DELETE FROM external_assetlists WHERE vim_account_id = '" . (int) $accountId . "' AND asset_id = '" . (int) $assetListId . "'");
		return true;
	}


	/**
	 * Function for deleting an asset ( and all it's items ).
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param external
	 *	Boolean Delete only the external asset list
	 *
	 * @todo:
	 * Modify the function so that it can take multiple asset list ids. For deleting
	 * multiple asset lists, this using this function can be costly.
	 */
	function deleteAsset( $assetId, $accountId, $external = false ) {

		if ( !is_numeric( $assetId ) || $assetId <= 0
			 || !is_numeric( $accountId ) || $accountId <= 0 ) {
			return false;
		}

        if ( !$this->checkAsset( $accountId, $assetId, !($external) ) ) {
            return false;
        }

		$condition = " AND ( external = 0 OR external IS NULL )";
		if ( $external ) {
			$condition = " AND external = 1 ";
		}

		$count = $this->database['ca']->numRows( "ca.vi_assets", "asset_id = '" . (int) $assetId . "' AND account_id ='". (int)$accountId. "'" . $condition );

		if ( $count <= 0 ) {
			return false;
		}

		// Delete asset
		$this->database['ca']->query("DELETE FROM ca.vi_assets WHERE asset_id = '" . (int)$assetId . "' AND account_id ='". (int)$accountId. "'");

		if ( $external ) {
			$this->deleteExternalAssetList( $assetId, $accountId );
		}

		// Delete products and vendors
		$this->deleteAssetProducts( $assetId );
		$this->deleteAssetVendors( $assetId );

		// Delete from process list
		$this->database['ca']->query("DELETE FROM ca.processlist WHERE asset_id = '". (int)$assetId ."' AND account_id ='". (int)$accountId. "'");

		// Delete Asset List Recipients
		$this->deleteAssetListRecipients( $accountId, $assetId );

		// Get all asset rules
		$rules = $this->database['ca']->queryGetRows("SELECT rule_id FROM ca.policy_compliance WHERE asset_id = '".(int)$assetId."' AND account_id = '".(int)$accountId."'");
		for ( $i = 0; $i < count( $rules ); $i++ ) {
			$this->database['ca']->query("DELETE FROM ca.policy_rules_criticality WHERE rule_id = '".(int)$rules[$i]['rule_id']."'");
			$this->database['ca']->query("DELETE FROM ca.policy_rules_cvss WHERE rule_id = '".(int)$rules[$i]['rule_id']."'");
			$this->database['ca']->query("DELETE FROM ca.policy_rules_solution WHERE rule_id = '".(int)$rules[$i]['rule_id']."'");
			$this->database['ca']->query("DELETE FROM ca.policy_compliance WHERE rule_id = '".(int)$rules[$i]['rule_id']."'");
		}

		$GLOBALS['debug']->notice( "VIM Asset list deleted" );
		return true;
	}

	/**
	 * Human usable function for adding a new item.
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param itemId
	 *	Integer item id ( product or vendor )
	 * @param type
	 *	Integer item type ( 0 - product ( default ), 1 - vendor )
	*/
	function addItem( $assetId, $accountId, $itemId, $type = 0 ) {
		return $this->handleItem( $assetId, $accountId, $itemId, 1, $type );
	}

	/**
	 * Human usable function for deleting an item.
	 * @param assetId
	 *	Integer asset id
	 * @param accountId
	 *	Integer account id
	 * @param itemId
	 *	String item id ( product or vendor ), comma separated values
	 * @param type
	 *	Integer item type ( 0 - product ( default ), 1 - vendor )
	*/
	function deleteItem( $assetId, $accountId, $itemId, $type = 0 ) {
		$itemArray = explode( ",", $itemId );
		for ( $i = 0; $i < count( $itemArray ); $i++ ) {
			$this->handleItem( $assetId, $accountId, (int)$itemArray[$i], 0, $type );
		}
		return true;
	}

	/**
	 * Check if group exists, and belongs to selected account.
	 * @param groupId
	 *	String group name
	 * @param accountId
	 *	Integer account id
	 * @return
	 *	Boolean true or false if it does or not.
	*/
	function checkGroup( $groupId, $accountId ) {
		$account_id = $this->database['ca']->getRowValue("ca.vtse_asset_groups", "account_id", "group_id = '".(int)$groupId."'");

		if ( $accountId == $account_id ) {
			return true;
		} else {
			$GLOBALS['debug']->error( "CA Group owner invalid" );
			return false;
		}
	}

	/**
	 * Function for deleting empty groups.
	 * @param accountId
	 *	Integer account id
	*/
	function flushGroups( $accountId ) {
		$emptyGroups = $this->database['ca']->queryGetRows( "SELECT group_id
				,(SELECT
					COUNT(asset_id)
				FROM
					ca.vi_assets
				WHERE
					ca.vi_assets.group_id = ca.vtse_asset_groups.group_id
				) AS items
			FROM
				ca.vtse_asset_groups
			WHERE
				account_id = '".(int)$accountId."'" );

		$delete = "";

		$emptyGroupCount = 0;

		$size = count( $emptyGroups );
		for ( $i = 0; $i < $size; $i++ ) {
			if ( $emptyGroups[$i]['items'] == 0 ) {
				if ( $emptyGroupCount !== 0 ) {
					$delete .= ",";
				}
				$delete .= (int) $emptyGroups[$i]['group_id'];
				$emptyGroupCount++;
			}
		}

		if ( $delete ) {

			$query = "DELETE FROM ca.vtse_asset_groups WHERE group_id IN (" . $delete.") AND account_id = '".(int)$accountId."'";
			$status = $this->database['ca']->query( $query );

			if ( $status === false ) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Set asset group.
	 * @param assetId
	 *	Integer asset id to set the group for.
	 * @param groupId
	 *	Integer group id
	 * @param accountId
	 *	Integer account id
	*/
	function setGroup( $assetId, $groupId, $accountId ) {

		if ( (int)$groupId === 0 ) {
			$groupId = "NULL";
		}

		if ( ( $groupId != "NULL" ) && ( !$this->checkGroup( $groupId, $accountId ) ) ) {
			return false;
		}

		if ( $this->checkAssetWithSubaccounts( $accountId, $assetId ) ) {
			$this->database['ca']->edit( "ca.vi_assets", array( "group_id" => (int) $groupId ), 1, "asset_id = '".(int)$assetId."'", true );
		} else {
			return false;
		}

		$GLOBALS['debug']->notice( "VIM Asset group set" );
		return true;
	}

	/**
	 * Add a new group.
	 *
	 * @param groupName
	 *	String group name.
	 * @param accountId
	 *	Integer account id.
	 *
	 * @return
	 *	Integer group id.
	*/
	function addGroup( $groupName, $accountId ) {

		$groupId = $this->db->insert()
			->into( "vtse_asset_groups" )
			->set( array( "name" => $groupName
						  ,"account_id" => $accountId ) )
			->exec();

		$GLOBALS['debug']->notice( "VIM Asset group created" );
		return $groupId;
	}

	/**
	 * Fetch group id, based on group name.
	 *
	 * @param groupName
	 *	String group name
	 * @param accountId
	 *	Integer account id
	 *
	 * @return
	 *	Integer group id
	*/
	function getGroupId( $groupName, $accountId ) {

		$groupId = false;

		$rows = $this->db->select()
			->columns( array( "group_id" ) )
			->from( "vtse_asset_groups" )
			->where( array( "account_id" => $accountId
							,"name" => $groupName ) )
			->exec();

		if ( !empty( $rows ) ) {
			$groupId = $rows[0]['group_id'];
		}

		return (int) $groupId;
	}

	/**
	 * Fetch asset user id, based on asset id.
	 * @param assetId
	 *	String asset name
	 * @return
	 *	Integer account_id
	*/
	function getAssetAccountId( $assetId ) {
		return $this->database['ca']->getRowValue("ca.vi_assets", "account_id", "asset_id = '".(int)$assetId."'");
	}

	private function getAssetGroupId( $assetId ) {
		$assetGroupId = $this->database['ca']->getRowValue("ca.vi_assets", "group_id", "asset_id = '".(int)$assetId."'");
		return (int) $assetGroupId;
	}

	/**
	 * Fetch asset id, based on asset name.
	 * @param assetName
	 *	String asset name
	 * @param accountId
	 *	Integer account id
	 *
	 * @return
	 *	Integer group id
	*/
	function getAssetId( $assetName, $accountId ) {

		$assetId = false;

		$rows = $this->db->select()
			->columns( array( "asset_id" ) )
			->from( "vi_assets" )
			->where( array( "account_id" => $accountId
							,"asset_name" => $assetName ) )
			->exec();

		if ( !empty( $rows ) ) {
			$assetId = $rows[0]['asset_id'];
		}

		return $assetId;
	}


	/**
	 * Given an asset id and account id, confirm that this is a valid asset_id for the account.  If the global
	 * flag is set, it may also be a valid asset id for a sub-account of the passed in account_id.
	 * @param assetId
	 *	Integer - asset id
	 * @param accountId
	 *	Integer account id
	 * @param globalFlag
 	 *	Boolean - true if we want to look at all accounts underneath the passed in accountID, false otherwise
	 * @return
	 *	Boolean - true if asset_id is valid, false otherwise
	*/
	function confirmAssetId( $assetId, $accountId, $globalFlag = false ) {

		// If we want to look at all sub-accounts as well, build the csv string of all sub-accounts
		if ( $globalFlag ) {
			$accountIdList = $GLOBALS['users']->getCommaSeparatedSubAccountIds( (int) $accountId, true );
			$querySegment = " account_id IN (" . $accountIdList .") ";
		} else {
			$querySegment = " account_id = '" . (int) $accountId . "' ";
		}

		$queryWhere = "asset_id = '" . (int) $assetId . "' AND " . $querySegment;
	 	$numRows = $this->database['ca']->numRows( 'ca.vi_assets', $queryWhere );
		if ( $numRows > 0 ) {
			return true;
		} else {
			return false;
		}
	}


	/**
	 * Check XML feed authentication key.
	 * @param key
	 *	String key hash
	 * @param accountId
	 *	Boolean asset data
	 */
	function checkAuthKey( $key ) {

		if ( !$key ) {
			return false;
		}

		$rows = $this->db->select()
			->from( "vi_assets" )
			->where( array( "xml_auth_key" => $key ) )
			->exec();

		if( empty( $rows ) ) {
			return false;
		}

		$asset = $rows[0];

		// todo: #db
		// Modify the following query once we can specify mysql functions as values

		// Verify account is active
		$account = $this->database['ca']->getRow("ca.accounts", "account_id = '" . (int)$asset['account_id'] . "' && account_expires >= NOW()");
		if ( !( $account['modules'] & MOD_VTS ) ) {
			return false;// Account has no access to this sort of items.
		}
		if ( $account == "" ) {
			return false; // Account inactive
		}

		return $asset;
	}

	/**
	 * Rename group.
	 * @param accountId
	 *	Integer account id
	 * @param groupId
	 *	Integer group id
	 * @param name
	 * 	String group name
	 *
	 * @return
	 *	Boolean false if invalid owner or group name already in use
	*/
	function renameGroup( $accountId, $groupId, $name ) {
		if ( $this->checkGroup( $groupId, $accountId ) === false ) {
			return false;
		}

		$this->db->update()
			->table( "vtse_asset_groups" )
			->set( array( "name" => $name ) )
			->where( array( "group_id" => $groupId ) )
			->exec();

		return true;
	}

	/**
	 * Delete group.
	 * @param accountId
	 *	Integer account id
	 * @param groupId
	 *	Integer group id
	 *
	 * @return
	 *	Boolean false if invalid owner
	*/
	function deleteGroup( $accountId, $groupId ) {
		if ( $this->checkGroup( $groupId, $accountId ) === false ) {
			return false;
		}
		$this->database['ca']->query("DELETE FROM ca.vtse_asset_groups WHERE group_id = '".(int)$groupId."' LIMIT 1");
		$this->database['ca']->query("UPDATE ca.vi_assets SET group_id = NULL WHERE group_id = '".(int)$groupId."' AND account_id = '".(int)$accountId."'");
	}

	/**
	 * Function for fetching asset list name.
	 * @param accountId
	 *	Integer owner account id
	 * @param assetId
	 *	Integer asset id
	 * @return
	 *	String asset name ( NOT secured for further SQL usage )
	*/
	public function fetchAssetName( $accountId, $assetId ) {
		return $this->database['ca']->getRowValue("ca.vi_assets", "asset_name", "asset_id = '".(int)$assetId."' AND account_id = '".(int)$accountId."'");
	}

	/**
	 * Function for fetching similar asset list name.
	 * @param accountId
	 *	Integer owner account id
	 * @param assetId
	 *	Integer asset id
	 * @return
	 *	String asset name ( NOT secured for further SQL usage )
	*/
	public function fetchSimilarAssetNames( $accountId, $assetId ) {
		$query = "SELECT asset_name
			FROM ca.vi_assets
			WHERE account_id = :account_id
			AND asset_name LIKE CONCAT( ( SELECT asset_name FROM ca.vi_assets WHERE account_id = :account_id AND asset_id = :asset_id ) , '%' )";
		return $this->db
			->execRaw( $query, array( ':account_id' => $accountId, ':asset_id' => $assetId ) )
			->fetchAll();
	}

	/**
	 * Fetch an array of vendor ids, based on selected vendors.
	 * @param accountId
	 *	Integer account id
	 * @param assetId
	 *	Integer asset id
	 * @param vendorId
	 *	Mixed boolean|integer|Array
	 *  False, to disable filtering on vendorId
	 *  Integer or Array or integers to filter on the vendor ids.
	 * @return
	 *	Array of integers, having the vendor ids
	*/
	function getVendors( $accountId, $assetId, $vendorId = false ) {

        $condition = array( 'account_id' => $accountId, 'asset_id' => $assetId );
        if ( $vendorId !== false ) {
            $condition['vendor_id'] = $vendorId;
        }
        $rows = $this->db->select()
			->columns( array( 'vendor_id' ) )
			->from( 'ca.vi_assets' )
            ->join( 'ca.vi_asset_vendors' )
			->using( array( 'asset_id' ) )
            ->where( $condition )
            ->exec();

		return UTIL::array_column( $rows, 'vendor_id' );
	}

	/**
	 * Get a list of all the products that are covered by the Asset List
	 * This includes:
	 * - Products explicitly specified by the user
	 * - All the products belonging to the vendors specified by the user
	 *
	 * Fetch an array of product ids, based on selected products.
	 * @param accountId
	 *	Integer user account id
	 * @param assetId
	 *	Integer asset id
	 * @param vendorId
	 *	Mixed boolean or integer to limit the search to a vendor or not
	 * @return
	 *	Array of integers, having the product ids
	*/
	public function getProducts( $accountId, $assetId, $vendorId = false ) {

		/*
		 * Fetch all the products specified by the user
		 */
		$productIds = array();
		$where = array(
					   'account_id' => $accountId
					   ,'asset_id' => $assetId
					   );
		$select = $this->db->select()
			->columns( array( 'os_soft_id' ) )
			->from( 'ca.vi_asset_products' )
			->join( 'ca.vi_assets' )
			->using( array( 'asset_id' ) )
			->join( 'ca.accounts' )
			->using( array( 'account_id' ) );

		if ( $vendorId ) {
			$select
				->join( 'vuln_track.os_soft' )
				->using( array( 'os_soft_id' ) )
				->join( 'vuln_track.vendor' )
				->using( array( 'vendor_id' ) );
			$where[ 'vendor_id' ] = $vendorId;
		}

		$rows = $select
			->where( $where )
			->exec();

		foreach ( $rows as $row ) {
			$productIds[] = (int) $row[ 'os_soft_id' ];
		}

		/*
		 * Go through all the Vendors specified by the user and get their products
		 *
		 * @todo: why do we have a foreach here? We should extract the
		 * vendor_ids and query once.
		 */
		$vendors = $this->getVendors( $accountId, $assetId, $vendorId );

		foreach ( $vendors as $vendor ) {
			$_rows = $this->db->select()
				->columns( array( 'os_soft_id' ) )
				->from( 'vuln_track.os_soft' )
				->where( array( 'vendor_id' => $vendor ) )
				->exec();

			foreach ( $_rows as $_row ) {
				array_push( $productIds, (int) $_row['os_soft_id'] );
			}
		}

		return array_unique( $productIds );
	}

    /**
     * Get a list of all the products that are covered by the Asset List
     * This includes:
     * - Products explicitly specified by the user
     *
     * Fetch an array of products
     * @param accountId
     *	Integer user account id
     * @param assetId
     *	Integer asset id
     * @return
     *	Array with all the product details
     */
    public function getProductsForAssetList( $accountId, $assetId ) {

        /*
         * Fetch all the products specified by the user
         */

        if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId ) ) {
            return null;
        }
        $where = array(
            'asset_id' => $assetId
        );

        $select = $this->db->select()
            ->from( 'ca.vi_asset_products' )
            ->join( 'ca.vi_assets' )
            ->using( array( 'asset_id' ) )
            ->join( 'ca.accounts' )
            ->using( array( 'account_id' ) )
            ->join( 'vuln_track.os_soft' )
            ->using( array( 'os_soft_id' ) )
            ->leftJoin( 'vuln_track.vendor' )
            ->using( array( 'vendor_id' ) );

        $rows = $select
            ->where( $where )
            ->orderBy( array( 'account_id' => 'ASC', 'asset_id' => 'ASC' ) )
            ->exec();

        return $rows;
    }

    /**
     * Get a list of all the vendors that are covered by the Asset List
     * This includes:
     * - Vendors explicitly specified by the user
     *
     * Fetch an array of vendors
     * @param accountId
     *	Integer user account id
     * @param assetId
     *	Integer asset id
     * @param includeProducts
     *	Boolean that determines if the return list contains the vendors products as well
     * @return
     *	Array of vendors
     */
    public function getVendorsForAssetList( $accountId, $assetId, $includeProducts = false ) {

        if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId ) ) {
            return null;
        }

        $where = array(
            'asset_id' => $assetId
        );

        $select = $this->db->select()
            ->from( 'ca.vi_asset_vendors' )
            ->join( 'ca.vi_assets' )
            ->using( array( 'asset_id' ) )
            ->join( 'ca.accounts' )
            ->using( array( 'account_id' ) )
            ->join( 'vuln_track.vendor' )
            ->using( array( 'vendor_id' ) );

        if ( $includeProducts ){
            $select
                ->leftJoin( 'vuln_track.os_soft' )
                ->using( array( 'vendor_id' ) );
        }

        $rows = $select
            ->where( $where )
            ->orderBy( array( 'account_id' => 'ASC', 'asset_id' => 'ASC' ) )
            ->exec();

        return $rows;
    }


    /**
     * Get a list of all the products that are covered by the Asset List
     * This includes:
     * - Products explicitly specified by the user
     * - Products from the vendors list
     *
     * Fetch an array of products
     * @param accountId
     *	Integer user account id
     * @return
     *	Array with the following product details: Asset List Id, Asset List Name, Product Id, Product Name, Vendor Id, Vendor Name, Account Id, Account Name
     */
    public function getProductsForAccount( $accountId ) {

        //check if account(s) are related with the logged in account
        $accountIds = array();
        if ( is_array( $accountId ) ) { //$accountId is an array of arrays
            $accountIds = $accountId;
        }
        else {
            array_push( $accountIds, $accountId );
        }

        //if it's not the base account, we're checking if it's a subaccount
        $allowedAccounts = $this->fetchAllUsers( $GLOBALS['account_id'] );
        array_push( $allowedAccounts, array ( 'account_id' => $GLOBALS['account_id'], 'account_username' => '' ) ); //account username doesn't matter in this case

        foreach ( $accountIds as $acId ) {
            $isChild = false;
            foreach ( $allowedAccounts as $allowed ) {
                if ( $allowed['account_id'] == $acId ) {
                    $isChild = true;
                    break;
                }
            }

            if ( !$isChild ) {
                return null;
            }
        }

        /*
         * Fetch all the products specified by the user
         */
        $where = array(
            'account_id' => $accountId
        );

        $select = $this->db->select()
            ->columns(array( 'ca.accounts.account_id', 'ca.accounts.account_username'
                , 'ca.vi_assets.asset_id', 'asset_name'
                , 'os_soft_id', 'vuln_track.os_soft.os_soft_name'
                , 'vuln_track.vendor.vendor_id', 'vuln_track.vendor.vendor_name'))
            ->from( 'ca.vi_asset_products' )
            ->join( 'ca.vi_assets' )
            ->using( array( 'asset_id' ) )
            ->join( 'ca.accounts' )
            ->using( array( 'account_id' ) )
            ->join( 'vuln_track.os_soft' )
            ->using( array( 'os_soft_id' ) )
            ->leftJoin( 'vuln_track.vendor' )
            ->using( array( 'vendor_id' ) );

        $rows = $select
            ->where( $where )
            ->orderBy( array( 'account_id' => 'ASC', 'asset_id' => 'ASC' ) )
            ->exec();

        /*
         * Fetch the products from the vendor list
         */

        $select = $this->db->select()
            ->columns(array( 'ca.accounts.account_id', 'ca.accounts.account_username'
                , 'ca.vi_assets.asset_id', 'asset_name'
                , 'os_soft_id', 'vuln_track.os_soft.os_soft_name'
                , 'vuln_track.vendor.vendor_id', 'vuln_track.vendor.vendor_name'))
            ->from( 'ca.vi_asset_vendors' )
            ->join( 'ca.vi_assets' )
            ->using( array( 'asset_id' ) )
            ->join( 'ca.accounts' )
            ->using( array( 'account_id' ) )
            ->join( 'vuln_track.vendor' )
            ->using( array( 'vendor_id' ) )
            ->leftJoin( 'vuln_track.os_soft' )
            ->using( array( 'vendor_id' ) );


        $vendorProducts = $select
            ->where( $where )
            ->orderBy( array( 'account_id' => 'ASC', 'asset_id' => 'ASC' ) )
            ->exec();

        $rows = array_merge($rows, $vendorProducts);

        usort($rows, function ( $a, $b ) {

            $accountIdA = $a['account_id'];
            $accountIdB = $b['account_id'];
            if ( $accountIdA == $accountIdB ){
                $assetIdA = $a['asset_id'];
                $assetIdB = $b['asset_id'];

                return ( $assetIdA - $assetIdB );
            }
            return ( $accountIdA - $accountIdB );
        });

        return $rows;
    }

	public function read( $accountId, $assetId, Array $where = array() ) {

		if ( !is_numeric( $accountId ) && !is_array( $accountId ) ) {
			return false;
		}
		if ( !is_numeric( $assetId ) && !is_array( $assetId ) ) {
			return false;
		}

		$where['account_id'] = $accountId;
		$where['asset_id'] = $assetId;

		$rows = $this->db->select()
			->from( 'vi_assets' )
			->where( $where )
			->exec();
		return $rows;
	}

	public function getVulnerabilitiesResource( $accountId, $assetId ) {

		$response = array( 'error_code' => self::UNKNOWN_ERROR, 'data' => null );

		$rows = $this->read( $accountId, $assetId, array( 'xml_receive_all' => 1 ) );

		if ( count( $rows ) > 0 ) {
			$response[ 'error_code' ] = self::ASSETLIST_IS_RECEIVE_ALL;
			return $response;
		}

		$productIds = $this->getProducts( $accountId, $assetId );

		/*
		 * Store the products in a temprary table
		 */
		$relevantProductIdsTable = 'temp_relevant_product_id';
		$query = 'CREATE TEMPORARY TABLE IF NOT EXISTS ' . $relevantProductIdsTable . ' ( product_id int(11), PRIMARY KEY (product_id) )';
		$this->db->execRaw( $query );
		if ( !empty( $productIds ) ) {
			$query= '';
			foreach( $productIds as $productId ) {
				if ( $query !== '' ) {
					$query .= ',';
				}
				$query .= '(' . (int) $productId . ')';
			}
            $query = 'INSERT IGNORE INTO ' . $relevantProductIdsTable . ' VALUES ' . $query;
            $this->db->execRaw( $query );
		}

		/*
		 * Get the relevant 'vuln_id's
		 */
		$relevantVulnIdsTable = 'temp_relevant_vuln_id';
		$query = 'CREATE TEMPORARY TABLE IF NOT EXISTS ' . $relevantVulnIdsTable
			. ' ( vuln_id int(11)'
			. ' , PRIMARY KEY (vuln_id)'
			. ' )';
		$this->db->execRaw( $query );

		$query = 'INSERT IGNORE INTO ' . $relevantVulnIdsTable . '
				 SELECT DISTINCT(vuln_id)
				 FROM vuln_track.os_soft_rel
				 LEFT JOIN vuln_track.vuln
				 USING ( vuln_id )
				 JOIN ' . $relevantProductIdsTable . '
				 WHERE ( os_id = product_id AND ( soft_id = 0 OR soft_id IS NULL ) )'
			;
		$this->db->execRaw( $query );

		$query = 'INSERT IGNORE INTO ' . $relevantVulnIdsTable . '
				 SELECT DISTINCT(vuln_id)
				 FROM vuln_track.os_soft_rel
				 LEFT JOIN vuln_track.vuln
				 USING ( vuln_id )
				 JOIN ' . $relevantProductIdsTable . '
				 WHERE ( soft_id = product_id )'
			;
		$this->db->execRaw( $query );

		/* $query = 'ALTER TABLE ' . $relevantVulnIdsTable . ' ADD INDEX (vuln_id)'; */
		$this->db->execRaw( $query );

		$select = $this->db->select()
			->from( $relevantVulnIdsTable );

		$response['error_code'] = self::SUCCESS;
		$response['data'] = $select;
		return $response;
	}

	/**
	 * Update an asset with the products specified.
	 * @param accountId
	 *	Integer account_id
	 * @param assetId
	 *	Integer asset_id
	 * @param productId
	 *	Integer os_soft_id
	 */
	public function updateProducts( $accountId, $assetId, $productIds ) {

		if ( !is_numeric( $accountId )
			 || $accountId <= 0
			 || !is_numeric( $assetId )
			 || $assetId <= 0 ) {
			return false;
		}
		if ( !is_array( $productIds ) ) {
			return false;
		}

		if ( !$this->checkAssetWithSubaccounts( $accountId, $assetId ) ) {
			return false;
		}

		$status = $this->deleteAssetProducts( $assetId );
		if ( !$status ) {
			return false;
		}

		$query = "INSERT INTO ca.vi_asset_products( asset_id, os_soft_id ) VALUES ";
		$values = "";
		foreach( $productIds as $productId ) {
			if ( $productId <= 0 ) {
				continue;
			}
			if ( $values ) {
				$values .= ",";
			}
			$values .= "( " . (int) $assetId . ", " . (int) $productId . " )";
		}

		$query .= $values;

		$status = $this->database['ca']->query( $query );
		return $status;
	}

	/**
	 * Associate contacts with an Asset List of a VIM Account
	 */
	public function saveAssetListRecipients( $accountId, $assetId, $contactPositions ) {

		if ( !is_numeric( $accountId )
			 || $accountId <= 0
			 || !is_numeric( $assetId )
			 || $assetId <= 0
			 || !is_array( $contactPositions )
			 || empty( $contactPositions ) ) {
			return false;
		}

		$conditionQuery = " asset_id = '" . (int) $assetId . "' AND account_id = '" . (int) $accountId . "'";

		$insertQuery = "INSERT INTO
							ca.processlist_contact_rel
						SET
							asset_id = '" . (int) $assetId . "'
							,account_id = '" . (int) $accountId . "'";

		$size = count( $contactPositions );
		for ( $i = 0; $i < $size; $i++ ) {

			$posQuery = "pos = '" . (int) $contactPositions[$i] . "'";

			// Do we need to check if the row already exists in the database?
			// Shouldn't we delete the existing entries and insert new ones?
			$result = $this->database['ca']->numRows( "ca.processlist_contact_rel", $conditionQuery . " AND " . $posQuery );
			if ( $result === 0 ) {
				$this->database['ca']->query( $insertQuery . "," . $posQuery );
			}
		}

		return true;
	}

	public function deleteAssetListRecipients( $accountId, $assetId ) {
		if ( !is_numeric( $accountId )
			 || $accountId <= 0
			 || !is_numeric( $assetId )
			 || $assetId <= 0 ) {
			return false;
		}
		$status = $this->database['ca']->query("DELETE FROM ca.processlist_contact_rel WHERE asset_id = '". (int) $assetId . "' AND account_id = '" . (int) $accountId . "'" );
		if ( $status !== false ) {
			return true;
		}
		return false;
	}

	public function getAssetListRecipientPositions( $accountId, $assetId ) {
		if ( !is_numeric( $accountId )
			 || $accountId <= 0
			 || !is_numeric( $assetId )
			 || $assetId <= 0 ) {
			return false;
		}
		$recipients = $this->database['ca']->getRows( "ca.processlist_contact_rel", "account_id = '" . (int) $accountId . "' AND asset_id = '" . (int) $assetId . "'" );
		if ( $recipients !== false) {
			return $recipients;
		}
		return false;
	}


}