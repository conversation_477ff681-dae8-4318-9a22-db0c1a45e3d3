<?php
/**
 * @file caAccounts.class.php
 * Provides ca accounts functionality.
*/

/**
 * Secunia CA accounts functionality.
 *
 * Requires database.class.php, misc.class.php, template.class.php and crmCustomers.class.php
*/
class caAccounts {
	protected $database = array();

	/**
	 * Connect to the database.
	*/
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
		$this->database['crm'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
	}

	/**
	 * Verify if account username is available, used by addAccount.
	 * @param username
	 *	String username
	 * @return
	 *	Boolean true if the account already exists, false if it doesn't exist
	*/
	private function verifyUsername( $username ) {
		$count = $this->database['ca']->numRows('ca.accounts', "account_username = '".$username."'");
		if ( $count == 0 ) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * Verify if customer has an existing account underneath
	 * @param cst_id
	 *	Integer customer id
	 * @param
	 *	Boolean true if it has, false if it doesn't
	*/
	private function verifyAccount( $cst_id ) {
		if ( $this->database['ca']->getRow('ca.accounts', "accounts.cst_id = '".$cst_id."'") ) {
			return true;
		} else {
			return false;
		}
	}
	/**
	 * Function for generating an account PIN code ( password ).
	 * @return
	 *	Integer the new pin number
	*/
	private function generatePin() {
		return rand( 1000, 9999 );
	}

	/**
	 * Function for verifying if the customer id is attached to the current partner_id.
	 * @param cstId
	 *	Integer customer id
	 * @param partnerId
	 *	Integer partner id
	 * @return
	 *	Boolean false if it is not attached or it is not numeric, true if it's attached and 'good' to use
	*/
	protected function verifyCst( $cstId, $partnerId ) {
		if ( !is_numeric( $cstId ) ) {
			return false;
		}
		$isOk = "";

		if ( $this->database['crm']->numRows( "crm.cst", "cst_id = '".(int)$cstId."' AND partner_id = '".(int)$partnerId."'" ) != 0 ) {
			$isOk = true;
		} else {
			$GLOBALS['debug']->error( "Invalid customer id" );
			$isOk = false;
		}
		return $isOk;
	}

	/**
	 * Function for creating a new CA account.
	 * @param password
	 *	String password, optional, if empty, it will generate a random password ( aka pin code )
	 * @param values
	 *	Array of values to be added for the current account, where that is applicable
	 *	NOTE: Paramater ignore_partners will skip any partner data, if set to false.
	 * @param isEscaped
	 *	Boolean see database->edit();
	 * @return
	 *	False if something wrong happened, or an array with new pin/password and username + new account id
	*/
	function addAccount( $password = "", $values, $isEscaped = false ) {
		$username = $values['account_username'];
		$cst_id = $values['cst_id'];
		if ( $isEscaped == false ) {
			$password = $this->database['ca']->escapeString( $password );
			$username = $this->database['ca']->escapeString( $values['account_username'] );
			$cst_id = $this->database['ca']->escapeString( $values['cst_id'] );
		}

		// Make sure the current partner ( if any! ) is allowed to add a new account for this customer
		if ( !isset( $values['ignore_partners'] ) || $values['ignore_partners'] == false ) {
			if ( isset( $values['partner_id'] ) ) {
				if ( $this->verifyCst( $cst_id, $values['partner_id'] ) == false ) {
					return false;
				}
			}
		}

		if ( $this->verifyUsername( $username ) ) {
			unset( $password ); // Make sure the password doesn't get logged
			$GLOBALS['debug']->error( "CA account username already used" );
			return false;
		}

		if ( $password == "" ) {
			$password = $this->generatePin();
		}

		$values['account_password'] = $this->database['ca']->makePassword( $password );
		if ( !isset( $values['ignore_partners'] ) || $values['ignore_partners'] == false ) {
			$values['cst_id'] = $GLOBALS['crmCustomers']->newCase( "shadow", $cst_id, $values['partner_id'], $isEscaped );
		}
		unset( $values['ignore_partners'] );
		$accountId = $this->database['ca']->edit("ca.accounts", $values, 0, "", $isEscaped );

		$resultValues['account_password'] = $password;
		$resultValues['account_username'] = $username;
		$resultValues['account_id'] = $accountId;
		unset( $password ); // Make sure the password doesn't get logged
		unset( $values['account_password'] );
		$GLOBALS['debug']->notice( "CA account created" );
		return $resultValues;
	}

	/**
	 * Function for updating an existing account.
	 * @param accountId
	 *	Integer account id
	 * @param values
	 *	Array see database->edit()
	 * @param isEscaped
	 *	Boolean see database->edit()
	*/
	function updateAccount( $accountId, $values, $isEscaped = false ) {
		$this->database['ca']->edit( "ca.accounts", $values, 1, "account_id = '".(int)$accountId."'", $isEscaped );
		$GLOBALS['debug']->notice( "CA account updated" );
	}

	/**
	 * Verify if account belongs to partner.
	 * @param accountId
	 *	Integer account id
	 * @param partnerId
	 *	Integer partner id
	 * @return
	 *	Boolean true or false
	*/
	private function verifyPartner( $accountId, $partner_id ) {
		if ( $this->getCustomerName( $accountId, $partner_id ) === false || $this->getCustomerName( $accountId, $partner_id ) == "" ) {
			return false;
		}
		return true;
	}

	/**
	 * Function for resetting account password
	 * @param accountId
	 *	Integer account id
	 * @param partnerId
	 *	Integer partnerId, optional
	 * @param isEscaped
	 *	See previous function
	 * @return
	 *	Array new 'pin'/password + username, to be used when sending notification email OR false if there is an error
	*/
	function resetPassword( $accountId, $partnerId = 0, $isEscaped = false ) {
		$accountId = (int)$accountId;
		$partnerId = (int)$partnerId;
		if ( $partnerId != 0 ) {
			if ( !$this->verifyPartner( $accountId, $partnerId ) ) {
				return false;
			}
		}
		$pin = $this->generatePin();
		$values['account_password'] = $pin;
		$values['account_username'] = $this->getUsername( $accountId, $partnerId );
		$this->updateAccount( $accountId, array(
			"account_password" => $this->database['ca']->makePassword( $pin )
			,"account_gen_pwd" => 1
		), $isEscaped );
		return $values;
	}

	/**
	 * Function for building SQL Where based on the partner_id
	 * @param partnerId
	 *	Integer partner id
	 * @return
	 *	String SQL where constuct
	*/
	private function buildPartnerWhere( $partnerId ) {
		$partnerWhere = "";
		if ( $partnerId != 0 ) {
			$partnerWhere = " AND partner_id = '".$partnerId."'";
		}
		return $partnerWhere;
	}

	/**
	 * Function for getting an account username
	 * @param accountId
	 *	Integer account id
	 * @param partnerId
	 *	Integer partner id, optional
	 * @return
	 *	String or boolean. Username or false if something went wrong.
	*/
	function getUsername( $accountId, $partnerId = 0 ) {
		$accountId = (int)$accountId;
		$partnerId = (int)$partnerId;
		$partnerWhere = $this->buildPartnerWhere( $partnerId );

		if ( $accountId == 0 ) {
			return false;
		}
		$accountUsername = $this->database['ca']->getRowValue('ca.accounts', "account_username", "account_id = '".$accountId."'".$partnerWhere);

		return $accountUsername;
	}

	/**
	 * Function for returning the customer username that owns a CA account.
	 * @param accountId
	 *	Integer account id
	 * @param partnerId
	 *	Integer partner id, optional
	*/
	function getCustomerName( $accountId, $partnerId = 0 ) {
		$accountId = (int)$accountId;
		$partnerId = (int)$partnerId;
		if ( $accountId == 0 ) {
			return false;
		}
		$partnerWhere = $this->buildPartnerWhere( $partnerId );
		$masterId = $this->database['ca']->getRowValue('ca.accounts', "cst_id", "account_id = '".$accountId."'".$partnerWhere);
		$cstId = $this->database['crm']->getRowValue('crm.cst', "master_id", "cst_id = '".(int)$masterId."'");
		$customerName = $this->database['crm']->getRowValue('crm.cst', "name", "cst_id = '".(int)$cstId."'");

		return $customerName;
	}

}
?>