<?php

/**
 * @file advisoryRelations.class.php
 * VIM Only
 */



class AdvisoryRelations {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for checking if the advisory information is only an informative one ( 'For more...' )
	 * @param said
	 *	String secunia advisory id
	 * @return
	 *	Boolean true or false if informative or not
	*/
	private function checkUpdateFor( $said ) {
		$result = $this->database['ca']->queryGetRows("SELECT
									vuln_id
								FROM
									vuln_track.text
								WHERE
									( text_text LIKE '%for more information%' || text_text LIKE '%For further information%' )
									AND vuln_id = '" . intval( $said ) . "'
								LIMIT 1");

		if ( count( $result ) == 1 ) {
			return true;
		}

		return false;
	}

	/**
	 * Function for locating advisory relations ( Deep Links ).
	 * @param id
	 *	String secunia advisory id
	 * @param type
	 *	String type of check ( Valid types: 'SAID', 'NESSUS', 'BID', 'CVE', 'CERT-VN', 'OSVDB', 'ST' )
	 * @param level
	 *	Integer how deep are we searching ( for initial call, level is 0 )
	 * @param init
	 *	Boolean true if we should initialise/reset the object
	 * @return
	 *	Boolean true or false if it has or not Deep Links
	 * NOTE: To access the data use AdvisoryRelations::refs
	*/
	function locateRelations ( $id, $type, $level, $init = true ) {
		// Check if allowed in the SMB
		if ( $GLOBALS['options']->OPT_ADVISORY_DEEP_LINKS == false ) {
			return false;
		}
		if ( $init == true ) {
			$this->refs = array();
			$this->refs_2 = array();
			$this->d = array();
			$this->lrcalls = array();
			$this->counter = 0;
		}

		// Have we already been down this path?
		if ( isset( $this->lrcalls[$type][$id][$level] ) && $this->lrcalls[$type][$id][$level] ) {
			return -1;
		} else {
			$this->lrcalls[$type][$id][$level] = 1;
		}

		// Increase depth counter
		$level++;

		// Check if depth exceeds our limit
		if ( $level > 3 ) {
			// Go back
			return -1;
		}

		// Don't work with 'update for'// Check - word 'update' not allowed in title!
		if ( $type == 'SAID' ) {
			if ( $this->checkUpdateFor( $id ) ) {
				return -1;
			}
		}

		// Safety exit
		$this->counter++;
		if ( $this->counter > 100 ) {
			return -1;
			exit();
		}

		// Add current to array
		$this->refs[$type][$id] = $id;

		// First: Check if this entry is being linked to by other of our references
		if ( isset( $first[$type] ) && $first[$type] != $id ) {
			$first[$type] = $id;
			$result = $this->database['ca']->queryGetRows("SELECT
										*
									FROM
										vuln_track.refsys_entry_refs
									WHERE
										source = '" . $this->database['ca']->escapeString( $type ) . "'
										AND reference = '" . $this->database['ca']->escapeString( $id ) . "'
			");
			$this->d++;
			for ( $i = 0; $i < count( $result ); $i++ ) {
				// Select data
				$r_res = $this->database['ca']->queryGetRows( "SELECT
											*
										FROM
											vuln_track.refsys_entries
										WHERE
											entry_id = '" . $result[$i]['entry_id'] . "'
											AND source not in('" . $this->database['ca']->escapeString( $type ) . "')
										LIMIT 1
				" );

				$this->d++;

				// If result
				if ( $r_res[0]['reference'] ) {
					if ( $r_res[0]['source'] != 'SAID' ) {
						// Dig deeper
						$this->refs[$r_row['source']][$r_row['reference']] = $r_res[0]['reference'];
						$this->locateRelations( $r_res[0]['reference'], $r_res[0]['source'], $level, false );
					} else {
						if ( $this->checkUpdateFor( $r_res[0]['reference'] ) ) {
							// Update for
							$this->refs_2[$r_res[0]['source']][$r_res[0]['reference']] = $r_res[0]['reference'];
						} else {
							// Dig deeper
							$this->refs[$r_res[0]['source']][$r_res[0]['reference']] = $r_res[0]['reference'];
							$this->locateRelations($r_res[0]['reference'], $r_res[0]['source'], $level, false );
						}
					}
				}
			}
		}

		// Second: Find all references which this links to, but only if this is 'level 1'
		if ( $level == 1 ) {
			// Select main data
			$res = $this->database['ca']->queryGetRows("SELECT
										*
									FROM
										vuln_track.refsys_entries
									WHERE
										source = '" . $this->database['ca']->escapeString($type) . "'
										AND reference = '" . $this->database['ca']->escapeString($id) . "'
									LIMIT 1
			");
			$this->d++;

			if ( count( $res ) > 0 ) {
				// Select all outbound references
				$res = $this->database['ca']->queryGetRows("SELECT
											*
										FROM
											vuln_track.refsys_entry_refs
										WHERE
											entry_id = '" . $res[0]['entry_id'] . "'
											AND source in ('SAID', 'CVE', 'CERT-VN', 'OSVDB', 'ST', 'BID')
											AND source != '" . $this->database['ca']->escapeString( $type ) . "'
				");
			} else {
				$res = array();
			}
			$this->d++;
			for ( $i = 0; $i < count( $res ); $i++ ) {
				$this->locateRelations( $res[$i]['reference'], $res[$i]['source'], $level, false );
			}
		}

		// Third: Check to see if this has - manual selected (Secunia staff) references
		if ( $type == 'BID' || $type == 'NESSUS' ) {
			// select all
			$res = $this->database['ca']->queryGetRows( "SELECT
										*
									FROM
										vuln_track.refsys_verify
									WHERE
										source = '" . $type . "'
										AND reference = '" . $this->database['ca']->escapeString($id) . "'
									LIMIT 1
			" );

			// Explode
			if ( $res && isset($res[0]) && isset($res[0]['saids']) && $res[0]['saids'] ) {
				$saids = explode(',', $res[0]['saids']);
				while ( list( $id, $said ) = each( $saids ) ) {
					$this->locateRelations( $said, 'SAID', $level, false );
				}
			}
		} elseif( $type == 'SAID' ) {
			// select all
			$res = $this->database['ca']->queryGetRows( "SELECT
										*
									FROM
										vuln_track.refsys_verify
									WHERE
										saids like '%" .  $this->database['ca']->escapeString($id) . "%'
			" );
			for ( $i = 0; $i < count( $res ); $i++ ) {
				$this->locateRelations( $res[$i]['reference'], $res[$i]['source'], $level, false );
			}
		}

		// Fourth: Find all references, which this links to
		// Select main data
		$res = $this->database['ca']->queryGetRows( "SELECT
									*
								FROM
									vuln_track.refsys_entries
								WHERE
									source = '" . $this->database['ca']->escapeString( $type ) . "'
									AND reference = '" . $this->database['ca']->escapeString( $id ) . "'
								LIMIT 1
		" );
		$this->d++;

		// Select all outbound references
		if ( count( $res ) > 0 ) {
			$res = $this->database['ca']->queryGetRows( "SELECT
										*
									FROM
										vuln_track.refsys_entry_refs
									WHERE
										entry_id = '" . $res[0]['entry_id'] . "'
										AND source != '" . $this->database['ca']->escapeString( $type ) . "'
			");
		} else {
			$res = array();
		}

		$this->d++;
		for ( $i = 0; $i < count( $res ); $i++ ) {
			if ( ($type != 'NESSUS') && ($type != 'SAID' || $res[$i]['source'] != 'CVE') ) {
				if ( !isset( $this->refs[$res[$i]['source']][$res[$i]['reference']] ) || !$this->refs[$res[$i]['source']][$res[$i]['reference']] ) {
					// Source rules
					if ( $res[$i]['source'] == 'MISC' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif( $res[$i]['source'] == 'ISS X-Force ID' )
					{
						$res[$i]['reference'] = 'http://xforce.iss.net/xforce/xfdb/' . $res[$i]['reference'];
					}
					elseif ( $res[$i]['source'] == 'XF' )
					{
						$res[$i]['source'] = 'ISS X-Force ID';
					}
					elseif ( $res[$i]['source'] == 'CONFIRM' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Other Solution URL' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'BUGTRAQ' )
					{
						$res[$i]['source'] = 'Bugtraq';
					}
					elseif ( $res[$i]['source'] == 'Other Advisory URL' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Generic Informational URL' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Vendor Specific Solution' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Vendor Specific Advisory' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Vendor Specific News/Chan' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'FEDORA' )
					{
						$res[$i]['source'] = 'Fedora';
					}
					elseif ( $res[$i]['source'] == 'CIAC Advisory' )
					{
						$res[$i]['source'] = 'CIAC';
					}
					elseif ( $res[$i]['source'] == 'Microsoft Security Bullet' )
					{
						continue;
					}
					elseif ( $res[$i]['source'] == 'MLIST' )
					{
						$res[$i]['source'] = 'Other Reference';
					}
					elseif ( $res[$i]['source'] == 'Generic Exploit URL' )
					{
						$res[$i]['source'] = 'Exploit Example';
					}

					// Reference rules
					if ( stristr($res[$i]['reference'], 'xforce.iss.net') )
					{
						$res[$i]['source'] = 'ISS X-Force ID';
					}
					elseif ( stristr($res[$i]['reference'], 'bugtraq') )
					{
						$row['source'] = 'Bugtraq';
					}
					elseif ( stristr($res[$i]['reference'], 'gentoo.org') )
					{
						$res[$i]['source'] = 'Gentoo';
					}
					elseif ( stristr($res[$i]['reference'], 'http://isc') )
					{
						$res[$i]['source'] = 'Internet Storm Center';
					}
					elseif ( stristr($res[$i]['reference'], 'securiteam.com') )
					{
						$res[$i]['source'] = 'SecuriTeam';
					}
					elseif ( stristr($res[$i]['reference'], 'microsoft.com/kb/') )
					{
						$res[$i]['source'] = 'Microsoft Knowledge Base';
					}
					elseif ( stristr($res[$i]['reference'], 'debian.org') )
					{
						$res[$i]['source'] = 'Debian';
					}
					elseif ( stristr($res[$i]['reference'], 'idefense.com') )
					{
						$res[$i]['source'] = 'iDefense';
					}
					elseif ( stristr($res[$i]['reference'], 'redhat.com') )
					{
						$res[$i]['source'] = 'RedHat';
					}
					elseif ( stristr($res[$i]['reference'], 'freebsd.org') )
					{
						$res[$i]['source'] = 'FreeBSD';
					}
					elseif ( stristr($res[$i]['reference'], 'sco.com') )
					{
						$res[$i]['source'] = 'SCO';
					}
					elseif ( stristr($res[$i]['reference'], 'trustix') )
					{
						$res[$i]['source'] = 'Trustix';
					}
					elseif ( stristr($res[$i]['reference'], 'mandrakesoft.com') )
					{
						$res[$i]['source'] = 'Mandrake';
					}
					elseif ( stristr($res[$i]['reference'], 'www.suse.de') )
					{
						$res[$i]['source'] = 'SUSE';
					}
					elseif ( stristr($res[$i]['reference'], '.cisco.com') )
					{
						$res[$i]['source'] = 'Cisco';
					}
					elseif ( stristr($res[$i]['reference'], 'conectiva.com') )
					{
						$res[$i]['source'] = 'Conectiva';
					}
					elseif ( stristr($res[$i]['reference'], 'fedora') )
					{
						$res[$i]['source'] = 'Fedora';
					}
					elseif ( stristr($res[$i]['reference'], 'ubuntu') )
					{
						$res[$i]['source'] = 'Ubuntu';
					}
					elseif ( stristr($res[$i]['reference'], 'slackware.com') )
					{
						$res[$i]['source'] = 'Slackware';
					}
					elseif ( stristr($res[$i]['reference'], 'kde.org') )
					{
						$res[$i]['source'] = 'KDE';
					}
					elseif ( stristr($res[$i]['reference'], 'apple.com') )
					{
						$res[$i]['source'] = 'Apple';
					}
					elseif ( stristr($res[$i]['reference'], 'secunia.com/advisories') )
					{
						continue;
					}
					elseif ( stristr($res[$i]['reference'], 'microsoft.com/') && stristr($res[$i]['reference'], 'bulletin') )
					{
						$res[$i]['source'] = 'Microsoft Security Bulletin';
					}
					elseif ( stristr($res[$i]['reference'], 'kb.cert.org') || stristr($res[$i]['reference'], 'securityfocus') )
					{
						continue;
					}
					elseif ( stristr($res[$i]['reference'], 'full') && stristr($res[$i]['reference'], 'disclosure') )
					{
						$res[$i]['source'] = 'Full-Disclosure';
					}

					if ( $res[$i]['source'] != 'SAID' )
					{
						$this->refs[$res[$i]['source']][$res[$i]['reference']] = $res[$i]['reference'];
					}
					elseif ( $res[$i]['source'] == 'SAID' )
					{
						if ( $this->checkUpdateFor($res[$i]['reference']) )
						{
							$this->refs_2[$res[$i]['source']][$res[$i]['reference']] = $res[$i]['reference'];
						}
						else
						{
							$this->refs[$res[$i]['source']][$res[$i]['reference']] = $res[$i]['reference'];
						}
					}
				}
			}
		}

		return true;
	}
}
?>