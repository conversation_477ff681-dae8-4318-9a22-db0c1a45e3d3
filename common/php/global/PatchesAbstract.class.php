<?php

abstract class PatchesAbstract {
	protected $engineMyIsam;
	protected $engineInno;
	protected $cstId;
	protected $privateDb;
	protected $commonDb;

	final public function __construct( $cstId, DB $privateDb, DB $commonDb, DB $psiDb = null) {
		$this->engineMyIsam = " ENGINE=MyISAM DEFAULT CHARSET=latin1 ";
		$this->engineInno = " ENGINE=InnoDB DEFAULT CHARSET=latin1 ";
		$this->cstId = $cstId;
		$this->privateDb = $privateDb;
		$this->commonDb = $commonDb;
		$this->psiDb = $psiDb;
	}

	/**
	 * Return an SQL statement for index creation if and index doesn't already exists.
	 *
	 * @param  string $table     name of the table
	 * @param  string $index     name of the index
	 * @param  array  $fieldList list of columns for the index
	 * @param  string $unique    UNIQUE
	 * @return query             MySQL query to be run
	 */
	final public function createIndexIfNotExists( $table, $index, array $fieldList, $unique = '' ) {
		// only checking the name of the index, not the columns
		$query = 'SHOW TABLES LIKE "' . $table . '"';
		$tableExists = $this->privateDb->execRaw( $query )->fetchAll();
		if ( count( $tableExists) ) {
			$query = 'SHOW INDEXES FROM ' . $table . ' WHERE Key_name = "' . $index . '"';
			$indexExists = $this->privateDb->execRaw( $query )->fetchAll();
			if ( count( $indexExists ) == 0 ) {
				return 'ALTER TABLE ' . $table . ' ADD ' . $unique . ' INDEX ' . $index . ' ( ' . implode(',', $fieldList) . ' )';
			}
		}
		return 'SELECT 1';
	}

	/**
	 * Return an SQL statement for drop index if index exists.
	 *
	 * @param  string $table     name of the table
	 * @param  string $index     name of the index
	 * @return query             MySQL query to be run
	 */
	final public function dropIndexIfExists( $table, $index ) {
		// only checking the name of the index, not the columns
		$query = 'SHOW TABLES LIKE "' . $table . '"';
		$tableExists = $this->privateDb->execRaw( $query )->fetchAll();
		if ( count( $tableExists) ) {
			$query = 'SHOW INDEXES FROM ' . $table . ' WHERE Key_name = "' . $index . '"';
			$indexExists = $this->privateDb->execRaw( $query )->fetchAll();
			if ( count( $indexExists ) > 0 ) {
				return "DROP INDEX `$index` ON $table";
			}
		}
		return 'SELECT 1';
	}

	/**
	 * Return an SQL statement for checking if index exists.
	 *
	 * @param  string $table     name of the table
	 * @param  string $index     name of the index
	 *
	 */
	final public function checkIndexExists( $table, $index ) {
		// only checking the name of the index, not the columns
		$count = 0;
		$query = 'SHOW TABLES LIKE "' . $table . '"';
		$tableExists = $this->privateDb->execRaw( $query )->fetchAll();
		if ( count( $tableExists) ) {
			$query = 'SHOW INDEXES FROM ' . $table . ' WHERE Key_name = "' . $index . '"';
			$indexExists = $this->privateDb->execRaw( $query )->fetchAll();
			if ( count( $indexExists ) > 0 ) {
				$count = $indexExists;
			}
		}
		return $count;
	}


	/**
	 * Common function to check column exists
	 * Another option is to use DESC $tableName command if checking currently connected DB
	 *
	 * @param $tableName
	 * @param $columnName
	 * @param null $dbName
	 * @return int|string
	 */
	final public function checkColumnExists ($tableName, $columnName, $dbName = null) {

		if (is_null($dbName)) {
			$params = $this->privateDb->getConnectionParams();
			$dbName = $params['dbname'];
		}

		$query = "SELECT COUNT(*) AS count FROM INFORMATION_SCHEMA.COLUMNS
				  WHERE TABLE_SCHEMA = '". $dbName ."'
				  AND COLUMN_NAME = '". $columnName ."'
				  AND TABLE_NAME = '". $tableName . "'";

		$count = $this->privateDb->execRaw($query)->fetchColumn(0);
		$value = $count ? $count : 0;

		return $value;
	}

	abstract public function getBaseline();

	abstract public function getBaselinePatchVersion();

	abstract public function getPatches();
}