<?php
/**
 * @file products.class.php
*/

/**
 * Provides product functionality.
 * Requires: database.class.php
*/
class PRODUCTS {

	private $db;

	function __construct( DB $db ) {
		$this->db = $db;
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/*
	 * Create an associative array that holds all the the secuia products.
	 *
	 * Performance Note:
	 * We have about 42 K products as of now in the database  (9th June 2012).
	 * So, if we want to store the following values in the array: os_soft_id, os_soft_name,
	 * os_soft_version, then it would take following amount of memory (this is the upper
	 * limit):
	 *
	 *   42 K *   4   ( os_soft_id      : INT )
	 * + 42 K * 100   ( os_soft_name    : varchar(100) )
	 * + 42 K *  20   ( os_soft_version : varchar(20) )
	 * + 42 K * 100   ( vendor_name     : varchar(100) )
	 * ----------
	 *       ~ 9  MB
	 *
	 * Real Scenario:
	 * - with only id and name        : ~ 26 MB
	 * - also with version and vendor : ~ 38 MB
	 *
	 * Currently: the memory_limit for php scripts is 128 M
	 */
	private function createSecuniaProductsLookup() {

		$secuniaProducts = array();

		/*
		$m_ = memory_get_usage();
		error_log( 'araja: ' . $m_ );
		*/

		$rows = $this->db->select()
			->columns( array( 'id' => 'os_soft_id'
							  ,'name' => 'os_soft_name'
							  // ,'version' => 'os_soft_version'
							  // ,'vendor' => 'vendor_name'
							  ) )
			->from( 'vuln_track.os_soft' )
			// ->leftJoin( 'vuln_track.vendor' )
			// ->using( array( 'vendor_id' ) )
			->exec();

		/*
		$m_ = memory_get_usage();
		error_log( 'araja: ' . $m_ );
		*/

		foreach ( $rows as $key => $row ) {

			$row['components'] = self::extractProductComponents( $row['name']
														  // , array( 'vendor' => $row['vendor'], 'version' => $row['version'] )
														  );
			/*
			 * Index the products using the soundexes of the hints
			 */
			foreach ( $row['components']['hints'] as $_key => $hint ) {
				$secuniaProducts[ soundex( $hint ) ][] = $row;
			}

			// Free up memory
			unset( $rows[$key] );
		}

		return $secuniaProducts;
	}

	private static function ceilToAny( $n, $x = 5 ) {
		return ceil( $n / $x ) * $x;
	}

	/*
	 * Generate possible matches to the Secunia Product Names from the input.
	 *
	 * From the input, we can detect if it carries a version. Also, if the
	 * word count is more than 1 and no version is detected, we can say that
	 * the name could also carry the Vendor name. Based on these observations,
	 * we can more accurately match the $name with the secunia product names.
	 *
	 */
	public static function calculateDistance( array $product1, array $product2 ) {
		$result = false;
		/*
		 * @todo: also use the vendor information?
		 */

		$distance = levenshtein( $product1['name'], $product2['name'], self::INSERT_COST, self::REPLACE_COST, self::DELETE_COST );

		$length = self::ceilToAny( strlen( $product1[ 'name' ] ) );

		$threshold = isset( self::$matchTreshold[ $length ] ) ? self::$matchTreshold[ $length ] : self::MAX_THRESHOLD;

		if ( $distance <= $threshold ) {
			if ( empty( $product1['version'] ) xor  empty( $product2['version'] ) ) {
				// If version is missing for one of the products, it is still a really good match.
				// We just add 1 to the distance.
				$distance += 1;
			} else if ( empty( $product1['version'] ) ||  empty( $product2['version'] ) ) {
				$distance += strlen( $product1['version'] ) + strlen( $product2['version'] );
			} else {
				// Also adjust for the version
				$distance += levenshtein( $product1['version'], $product2['version'] );
			}
			$result = $distance;
		}

		return $result;
	}

	private static function extractVersion( $word ) {
		$match = array();
		/*
		 * Determine if this word contains version info or not e.g. 1.0, 1.0.0, 1.x etc
		 * We need to find major and minor parts of the version i.e.
		 * e.g. '4.4.5', major: '4.4.' , minor: '5'
		 *      '4.x'  , major: '4.'   , minor: 'x'
		 */
		if ( preg_match("/(?P<major>(\d+\.)+)(?P<minor>\d+|[x])\b/", $word, $match ) !== 0 ) {
			$version = $match["major"] . $match["minor"];
			return $version;
		} else if ( is_numeric( $word ) ) {
			return $word;
		}

		return false;
	}

	/*
	 * Extract the product components from the productName
	 * The better this extraction function gets, the better we can implement the
	 * distance calculation functionality. At the moment, the components are name and version.
	 *
	 * Later, if we need more components we will enhance the functionality here ( e.g. by
	 * using the helpers array. )
	 *
	 * @todo: use helpers
	 *
	 * @retrun array
	 *   Array of product components, with keys: name, version
	 */
	private static function extractProductComponents( $productName, array $helpers = NULL ) {
		$components =
			array(
				  // Lower cased, space stripped, version stripped product name
				  // Used for distance calculation
				  'name' => ''
				  // The version extracted from the product name
				  ,'version' => ''
				  // Hints for generating hashes for the product
				  ,'hints' => array()
				  );

		$words = explode( ' ', $productName );

		/*
		 * Generate the hints
		 * Strip the version ( @todo: and later maybe other components )
		 */
		for ( $i = 0, $size = count( $words ); $i < $size; $i++ ) {
			if ( $i === 0 ) {
				// todo: can this be vendor? can we use this somehow?
				$components['hints'][] = $words[$i];
			} else if ( $i > 0 ) {
				// Detect version, If there are more than two words/ and the version hasn't
				// been detected
				if ( empty( $components['version'] ) ) {
					$version = self::extractVersion( $words[$i] );
					if ( $version !== false ) {
						$components['version'] = trim( $version );
						unset( $words[$i] );
					} else if ( $i < 2 ) {
						$components['hints'][] = $words[$i];
					}
				} else if ( $i < 2 ) {
					$components['hints'][] = $words[$i];
				}
			}
		}

		// Product name ready for distance calculation
		$components['name'] = strtolower( trim( implode( $words ) ) );

		return $components;
	}

	/*
	 * Costs, only applicable to the names (not versions etc)
	 */
	const INSERT_COST = 1;
	const REPLACE_COST = 20;
	const DELETE_COST = 20;

	/*
	 * Match threshold
	 * e.g. with Insert, Replace, Delete costs as 10,20,20 resp threshold will be 70
	 *
	 * @todo: Init the following in the constructor
	 * // ( 5 * self::INSERT_COST ) + max( self::REPLACE_COST, self::DELETE_COST );
	 */
	const MAX_THRESHOLD = 65;

	private static $matchTreshold = array(
				  0 => 15
				, 5 => 25
				,10 => 50
				,15 => 65
			 );


	const NUM_OF_MATCHES = 5;


	/*
	 * Search the Secunia Products to find close matches to the inputs.
	 *
	 * Pseudo code:
	 * -----------
	 *
	 * p = all secunia products
	 * p' = hashed secunia products // using soundex
	 *
	 * for each input product i {
	 *   s = getRelevantSecuniaProducts( i, p' )
	 *   for each relevant product s {
	 *      c_i = extractComponents( i );
	 *      c_s = extractComponents( s );
	 *      distance = calculateDistance( c_i, c_s );
	 *      result[ i ][ s ] = distance;
	 *   }
	 * }
	 *
	 * Performance:
	 * -----------
	 * O( m * n ) ,  where m : Num of input products
	 *                     n : Num of Secunia Products
	 *
	 * If m = 2K and n = 50 K,
	 * then, m*n = 100,000,000
	 *	 *
	 * Improvement:
	 * O( m * n' ) , where m : Num of input products
	 *                     n': Num of Relevant Secunia Products
	 *
	 * If m = 2K and n = ~20,
	 * then, m*n = 40 K
	 *
	 * @param Array $inputProductNames
	 *   The product names to be matched with the Secunia Prodct Database
	 * @return boolean
	 */
	function productsSearch( array $inputProductNames = array() ) {
		$products = $this->createSecuniaProductsLookup();

		/*
		$m_ = memory_get_usage();
		error_log( 'araja: ' . $m_ );
		*/

		$results = array();

		foreach( $inputProductNames as $_key => $inputProductName ) {

			if ( isset( $results[$inputProductName] ) ) {
				continue;
			}

			// Init the results
			$results[$inputProductName] = array();

			$inputProductComponents = self::extractProductComponents( $inputProductName );

			$selectedProducts = array();
			foreach ( $inputProductComponents['hints'] as $_key => $hint ) {
				$index = soundex( $hint );
				if ( isset( $products[ $index ] ) ) {
					$selectedProducts = array_merge( $selectedProducts, $products[ $index ]);
				}
			}

			/*
			 * If no close matches are found, we continue with the next input
			 */
			if ( empty( $selectedProducts ) ) {
				continue;
			}

			foreach( $selectedProducts as $_key => $product ) {
				$distance = self::calculateDistance( $product['components'], $inputProductComponents );

				if ( $distance === false ) {
					$distance = self::calculateDistance( $inputProductComponents, $product['components'] );
				}

				if ( $distance !== false ) {
					/*
					 * Potential Performance Bottleneck?
					 * Find a better way to insert the value i.e. need to insert by
					 * keeping the array sorted.. shouldn't be two separate operations.
					 * Anyway, if the NUM_OF_MATCHES is small, this shouldn't be a major
					 * problem.
					 *
					 * @todo: benchmark
					 */
					if ( isset( $results[$inputProductName] ) ) {
						asort( $results[$inputProductName] );
						if ( count( $results[$inputProductName] ) > self::NUM_OF_MATCHES ) {
							array_pop( $results[$inputProductName] );
						}
					}
					$results[$inputProductName][ $product['id'] ] = $distance;
				}
			}

			// error_log( 'araja: Product : ' . $inputProductName . ', Counter: ' . $_counter++  );
		}

		// error_log( 'araja: ' . print_r( $results, true ) );

		return $results;

	}

	function productIdSearch( array $raw_list = array() ) {
		$query = "CREATE TEMPORARY TABLE IF NOT EXISTS ca.temp_product_ids_to_match ( os_soft_id int(10) )";
		$this->database['ca']->query( $query );

		// Insert the product ids into the temporary table so that we can compare it later.
		// We can not just use the mysql IN() comparision function because the 'max_allowed_packet' might limit the input
		$size = count( $raw_list );
		$valuesQuery = "";
		for ( $i = 0; $i < $size; $i++ ) {
			if ( !is_numeric( $raw_list[$i] ) ) {
				$unmatched[] = $raw_list[$i];
				continue;
			}
			if ( $valuesQuery ) {
				$valuesQuery .= ",";
			}
			$valuesQuery .= "(" . (int) $raw_list[$i] . ")";
		}

		if ( !$valuesQuery ) {
			$GLOBALS['debug']->error( "productIdSearch() - Unable to generate the valuesQuery()." );
			return false;
		}
		$query = "INSERT INTO ca.temp_product_ids_to_match VALUES " . $valuesQuery;
		$this->database['ca']->query( $query );

		$matchQuery = "SELECT temp_product_ids_to_match.os_soft_id AS input, os_soft.os_soft_id AS id
					FROM
						ca.temp_product_ids_to_match
					LEFT OUTER JOIN
						vuln_track.os_soft
					USING ( os_soft_id )";

		$results = array();
		$rows = $this->database['ca']->queryGetRows( $matchQuery );

		for ( $i = 0, $size = count( $rows ); $i < $size; $i++ ) {
			$row = $rows[$i];
			$id = $row['id'];
			$input = $row['input'];
			$results[ $input ] = array();
			if ( is_numeric( $id ) && $id > 0  ) {
				$results[ $input ][ $id ] = 0;
			}
		}

		return $results;
	}

	/**
	 * Function for fetching a product name.
	 * @param productId
	 *	Integer product id
	 * @return
	 *	String product name. NOTE: Unsecured for output!
	*/
	function getProductName( $productId ) {
		return $this->database['ca']->getRowValue("vuln_track.os_soft", "os_soft_name", "os_soft_id = '".(int)$productId."'");
	}

	/**
	 * Function for fetching the product vendor.
	 * @param productId
	 *	Integer product id
	 * @return
	 *	String vendor name
	*/
	function getProductVendor( $productId ) {
		return $this->database['ca']->getRowValue("vuln_track.os_soft, vuln_track.vendor", "vendor_name", "os_soft.os_soft_id = '".(int)$productId."' AND os_soft.vendor_id = vendor.vendor_id");
	}

	/**
	 * Function for fetching the product id.
	 * @param productId
	 *	Integer product id
	 * @return
	 *	String vendor name
	*/
	function getProductVendorId( $productId ) {
		return $this->database['ca']->getRowValue("vuln_track.os_soft", "vendor_id", "os_soft.os_soft_id = '".(int)$productId."'");
	}

	/**
	 * Function for fetching a product link
	 * @param productId
	 *	Integer product id
	 * @return
	 *	String product link
	*/
	function getProductLink( $productId ) {
		return $this->database['ca']->getRowValue("vuln_track.os_soft", "os_soft_prod_page", "os_soft_id = '".(int)$productId."'");
	}

	/**
	 * Function for determining if a product should be displayed or not.
	 * @param productId
	 *	Integer product id
	 * @return
	 *	Boolean true or false if to be displayed or not
	*/
	function canDisplay( $productId ) {
		$display = $this->database['ca']->getRowValue("vuln_track.os_soft", "os_soft_id", "os_soft_id = '".(int)$productId."' AND ( !dont_display OR dont_display is null ) ");
		if ( $display != "" ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Function for fetching products that CAN be displayed, out of a list of products.
	*/
	private function canBeDisplayed( $products ) {
		$display = $this->database['ca']->queryGetRows(
			"SELECT
				os_soft_id
			FROM
				vuln_track.os_soft
			WHERE
				os_soft_id IN ( ".$this->buildSqlIn( $products )." ) AND ( !dont_display OR dont_display is null )
			"
		);
		$results = array();
		for ( $i = 0; $i < count( $display ); $i++ ) {
			$results[$i] = $display[$i]['os_soft_id'];
		}
		return $results;
	}

	private function buildSqlIn( $array ) {
		$sqlIn = "";
		for ( $i = 0; $i < count( $array ); $i++ ) {
			$sqlIn .= ( $i != 0 ? "," : "" ).(int)$array[$i];
		}
		return $sqlIn;
	}

	/**
	 * Function for fetching product advisories.
	 * @param productId
	 *	Mixed integer or array of product id's
	 * @return
	 *	Mixed array of vulnerability id's, for selected product, or false if product is not to be dislpayed. NOTE: vulnerability display or not is NOT beying checked.
	*/
	function fetchAdvisories( $productId ) {
		if ( is_array( $productId ) ) {
			$products = $this->buildSqlIn( $this->canBeDisplayed( $productId ) );
		} else {
			$products = (int)$productId;
		}

		if ( !is_array( $productId ) && $this->canDisplay( $products ) !== true ) {
			return false;
		}

		$query = "SELECT
				DISTINCT( os_soft_rel.vuln_id )
			FROM
				vuln_track.os_soft_rel
			WHERE
				( os_id IN ( ".$products." ) AND ( soft_id is NULL OR soft_id = 0) )
				OR ( soft_id IN ( ".$products." ) )
		";

		$GLOBALS['debug']->notice( "(PRODUCTS) Vulnerabilities list fetched" );
		return $this->database['ca']->queryGetRows( $query );
	}

	/*
	 * @param $filter
	 * array(
	 *   ,filter => array(
	 *                from_dtm => 'xxx' // OR
	 *                days => x
	 *              )
	 * )
	 *
	 * filters are mutually exclusive. If both are given, from_dtm takes precedence.
	 *
	 * @return Array of rows
	 */
	public function fetch( $filter ) {

		$where = array();

		if ( isset( $filter[ 'from_dtm' ] ) ) {
			$where[ 'sync_updated' ] = Exp::ge ( $filter[ 'from_dtm' ] );
		} else if ( isset( $filter[ 'days' ] ) ) {
			$where[ 'sync_updated' ] = Exp::ge( SqlFn::DATE_SUB( SqlFn::NOW(), $filter[ 'days' ], 'DAY' ) );
		}

		$rows = $this->db->select()
			->columns( array( 'vendor_id'
							  ,'vendor_name'
							  ,'os_soft_id'
							  ,'os_soft_name'
							  ,'os_soft_version'
							  ,'os_soft_type'
							  ,'sync_updated'
							  ) )
			->from( 'vuln_track.os_soft' )
			->leftJoin( 'vuln_track.vendor' )
			->using( array( 'vendor_id' ) )
			->where( $where )
			->exec();

		return $rows;

	}

	/*
	 * @param options
	 * array(
	 *   ,filter => array(
	 *                from_dtm => 'xxx' // OR
	 *                days => x
	 *              )
	 * )
	 *
	 * filters are mutually exclusive. If both are given, 'from_dtm' takes precedence.
	 *
	 * @return feed
	 */
	public function generateFeed( $options ) {

		$filter = isset( $options['filter'] ) ? $options['filter'] : array();

		/*
		 * Create Feed
		 */
		$writer = Feed::writer( 'secunia_products' );

		// Asset List Name
		$writer->child( 'title', 'Flexera Products' );

		$products = self::fetch( $filter );

		foreach ( $products as $product ) {
			$elementWriter = $writer->child( 'product' );
			$elementWriter->child( 'id', $product['os_soft_id'] );
			$elementWriter->child( 'name', $product['os_soft_name'] );
			$elementWriter->child( 'version', $product['os_soft_version'] );
			$elementWriter->child( 'type', $product['os_soft_type'] );
			$elementWriter->child( 'added', $product['sync_updated'] );
			$elementWriter->child( 'vendor_id', $product['vendor_id'] );
			$elementWriter->child( 'vendor_name', $product['vendor_name'] );
		}

		return $writer->asXML();
	}

}
