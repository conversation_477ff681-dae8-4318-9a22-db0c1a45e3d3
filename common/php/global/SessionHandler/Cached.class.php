<?php

/**
 * @file Cached.class.php
 * This file contains a class for working with Memcached MySQL sessions.
 */

namespace SessionHandler;

/**
 * Class Cached
 *
 * @note To use Memcached Sessions with this Class the SESSION_USE_CACHED_HANDLER constant must be defined and set to true
 *
 * Session Handling using two Session Handlers, one for Caching and one for Persistence.
 *
 * @todo TODO When we have PHP 5.4 use the SessionHandlerInterface and change how we set the handler
 * @todo TODO Create Test for this Class
 * @todo TODO Move this into the libs directory
 *
 * @example
 *	// Don't forget to setup configuration.php for the custom session handlers
 *	$memcached = new \Memcached;
 *	foreach ( explode( ',', MEMCACHED_SERVERS ) as $server ) {
 *		list( $host, $post, $weight ) = explode( ':', $server );
 *		$memcached->addServer( $host, $post, $weight );
 *	}
 *	$sessionHandler = new \SessionHandler\Cached(
 * 		new \SessionHandler\Mysql()
 * 		,new \SessionHandler\Memcached( $memcached )
 * 	);
 * 	session_set_save_handler(
 * 		array( $sessionHandler, 'open' )
 * 		,array( $sessionHandler, 'close' )
 * 		,array( $sessionHandler, 'read' )
 * 		,array( $sessionHandler, 'write' )
 * 		,array( $sessionHandler, 'destroy' )
 * 		,array( $sessionHandler, 'gc' )
 * 	);
 * 	register_shutdown_function('session_write_close');
 * @endexample
 *
 * <h3>Memcached will take care to not timeout on a failed server on repeat calls when the first call has failed (see retry time.)</h3>
 * In the situation where we want to check if read() call to Memcached failed we have to hook the error handler as read()'s return
 * value doesn't differentiate between a server error and a missing key.
 * @verbatim
 *		$serverError = false;
 *		$oldErrorHandler = set_error_handler( function( $errno ) use ( &$serverError ) {
 *			if ( 8 === $errno ) {
 *				// Memcached server has gone away
 *				$serverError = false;
 *			}
 *		}, E_NOTICE );
 *		$data = $this->cacheSessionHandler->read( $sessionId );
 *		restore_error_handler();
 * @endverbatim
 *
 * @note The sessions are not encrypted so don't store anything sensitive in them.
 * @note Don't whitelist the session table anywhere in the API.
 * @note Do not store Session IDs in logs and do not send them in the response body.
 * @note If requests are received from the CSI Binary's request implementation (not the web browser's)
 * 	and we respond with a Set-Cookie - it may not be seen by the web browser.
 *
 * @package CSI
 * @subpackage Session
 * <AUTHOR>
 * @since 29.10.2013
 */
class Cached implements SessionHandlerInterface {

	/**
	 * @const string
	 * The Table name where Session data is stored
	 */
	const TABLE_NAME =  'session';

	/**
	 * @var SessionHandlerInterface
	 * Database where the Session is held
	 */
	private $sessionHandler = false;

	/**
	 * @var SessionHandlerInterface
	 * Memcached instance
	 */
	private $cacheSessionHandler;

	/**
	 * @var bool
	 * Whether to use Memcached or not.
	 * This is set to the SESSION_USE_CACHED_HANDLER configuration.php constant. If the
	 * constant isn't defined then it defaults to false. The reason this varexists is
	 * so we can disable the cache handler at runtime if warranted.
	 */
	private $useCacheHandler = false;

	/**
	 * Constructor
	 *
	 * @param DB $db The DB connection where Sessions are stored
	 * @param [Memcached|Memcached] $memcached Memcached instance.
	 */
	public function __construct( SessionHandlerInterface $sessionHandler, SessionHandlerInterface $cacheSessionHandler = null ) {
		$this->sessionHandler = $sessionHandler;
		$this->cacheSessionHandler = $cacheSessionHandler;
		$this->useCacheHandler = defined('SESSION_USE_CACHED_HANDLER') ? SESSION_USE_CACHED_HANDLER : false;
	}

	/**
	 * Open the Session
	 *
	 * @param string $savePath
	 * @param string $sessionName
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function open( $savePath, $sessionName ) {
		$this->sessionHandler->open( $savePath, $sessionName );
		if ( $this->useCacheHandler ) {
			$this->cacheSessionHandler->open( $savePath, $sessionName );
		}
		return true;
	}

	/**
	 * Close the Session
	 *
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function close() {
		if ( $this->useCacheHandler ) {
			$this->cacheSessionHandler->close();
		}
		$this->sessionHandler->close();
		return true;
	}

	/**
	 * Reads the Session data
	 *
	 * @param string $sessionId <p>
	 * The session ID to read the data of
	 * </p>
	 * @return string <p>
	 * 	Returns  session data or an empty string if no data exists
	 * </p>
	 */
	public function read( $sessionId ) {
		$data = false;
		if ( $this->useCacheHandler ) {
			$data = $this->cacheSessionHandler->read( $sessionId );
		}
		if ( false === $data ) {
			$data = $this->sessionHandler->read( $sessionId );
		}
		return $data ? $data : '';
	}

	/**
	 * Write the Session data
	 *
	 * @enhancement ENHANCEMENT If the Session is being written often in many
	 * requests in a short period of time and {@link #useCacheHandler} is True then the
	 * second write to the persistent storage that takes place in this method can be
	 * omitted for some calls that way the high speed cache is used more often.
	 *
	 * @param string $sessionId <p>
	 * The session ID to write data to
	 * </p>
	 * @param string $data <p>
	 *	The Session Data to write
	 * </p>
	 * @return bool
	 */
	public function write( $sessionId, $data ) {
		if ( $this->useCacheHandler ) {
			$this->cacheSessionHandler->write( $sessionId, $data );
		}
		return $this->sessionHandler->write( $sessionId, $data );
	}

	/**
	 * Destroy the current session
	 *
	 * @param int $sessionId <p>
	 * The ID of the Session to destroy
	 * </p>
	 */
	public function destroy( $sessionId ) {
		if ( $this->useCacheHandler ) {
			$this->cacheSessionHandler->destroy( $sessionId );
		}
		return $this->sessionHandler->destroy( $sessionId );
	}

	/**
	 * Garbage Collect Sessions
	 *
	 * @param int $maxLifetime <p>
	 * Sessions that have not updated for the last $maxLifetime seconds will be removed
	 * </p>
	 * @return bool
	 */
	public function gc( $maxLifetime ) {
		if ( $this->useCacheHandler ) {
			$this->cacheSessionHandler->gc( $maxLifetime );
		}
		return $this->sessionHandler->gc( $maxLifetime );
	}

}