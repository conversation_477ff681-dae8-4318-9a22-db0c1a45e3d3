<?php

/**
 * @file Mysql.class.php
 * This file contains a class for working with MySQL sessions.
 */

namespace SessionHandler;

/**
 * Class Mysql
 *
 * Session Handling using MySQL
 *
 * @verbatim
 *
 * Required configuration.php constants:
 *
 * 	define('SESSION_MYSQL_HOST', '*************');
 * 	define('SESSION_MYSQL_USER', 'development');
 * 	define('SESSION_MYSQL_PASSWORD', '-------');
 * 	define('SESSION_MYSQL_SCHEMA', 'ca_666999');
 * 	define('SESSION_MYSQL_TABLE', 'session');
 *
 * The MySQL Session Handler requires a table be created to store session data,
 * the database this table is created in is set in the configuration.php constants.
 *
 * CREATE TABLE `session` (
 *     `id` int(11) unsigned NOT NULL auto_increment
 *     ,`session_id` varchar(64) NOT NULL COMMENT 'session id'
 *     ,`data` blob NULL
 *     ,`created_at` TIMESTAMP NOT NULL COMMENT 'time Session was created'
 *     ,`updated_at` TIMESTAMP NULL COMMENT 'time Session was updated'
 *     ,PRIMARY KEY  (`id`)
 *     ,UNIQUE (`session_id`)
 * ) ENGINE=InnoDB CHARSET=latin1 AUTO_INCREMENT=1 COMMENT='Contains User Session Data';
 *
 * @endverbatim
 *
 * @example
 *	// Don't forget to setup configuration.php for the custom session handlers
 *	$sessionHandler = new \SessionHandler\Mysql();
 * 	session_set_save_handler(
 * 		array( $sessionHandler, 'open' )
 * 		,array( $sessionHandler, 'close' )
 * 		,array( $sessionHandler, 'read' )
 * 		,array( $sessionHandler, 'write' )
 * 		,array( $sessionHandler, 'destroy' )
 * 		,array( $sessionHandler, 'gc' )
 * 	);
 * 	register_shutdown_function('session_write_close');
 * @endexample
 *
 * @todo TODO When we have PHP 5.4 load this using the interface
 * @todo TODO Create Test for this Class
 * @todo TODO Move this into the libs directory
 *
 * @note The sessions are not encrypted so don't store anything sensitive in them.
 * @note Don't whitelist the session table anywhere in the API.
 * @note Do not store Session IDs in logs and do not send them in the response body.
 * @note If requests are received from the CSI Binary's request implementation (not the web browser's)
 * 	and we respond with a Set-Cookie - it may not be seen by the web browser.
 *
 * @package CSI
 * @subpackage Session
 * <AUTHOR>
 * @since 29.10.2013
 */
class Mysql implements SessionHandlerInterface {

	/**
	 * @var string
	 * The Table name where Session data is stored. This can also reference the Database name.
	 */
	private $tableName =  'ca.session';

	/**
	 * @var DB
	 * Database where the Session is held
	 */
	private $db = false;

	/**
	 * Constructor
	 */
	public function __construct() {
		if ( !defined( 'SESSION_MYSQL_TABLE' ) || !defined( 'SESSION_MYSQL_SCHEMA' ) ) {
			throw new \Exception('The configuration.php is not setup for the Mysql Session Handler.');
		}
		$this->tableName = SESSION_MYSQL_TABLE;
	}

	/**
	 * Open the Session
	 *
	 * @param string $savePath
	 * @param string $sessionName
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function open( $savePath, $sessionName ) {
		if ( !$this->db ) {
			try {
				$this->db = new \DB( SESSION_MYSQL_HOST, SESSION_MYSQL_SCHEMA, SESSION_MYSQL_USER, SESSION_MYSQL_PASSWORD, DB_UTC_TIMEZONE );
			} catch ( DbEx $ex ) {
				$this->db = false;
			}
		}
		return ( $this->db ? true : false );
	}

	/**
	 * Close the Session
	 *
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function close() {
		if ( $this->db ) {
			$this->db->close();
			$this->db = false;
		}
		return true;
	}

	/**
	 * Reads the Session data
	 *
	 * @param string $sessionId <p>
	 * The session ID to read the data of
	 * </p>
	 * @return string <p>
	 * 	Returns serialized session data or an empty string if no data exists
	 * </p>
	 */
	public function read( $sessionId ) {
		return (string) $this->db->select()
			->columns( array( 'data' ) )
			->from( $this->tableName )
			->where( array( 'session_id' => $sessionId ) )
			->setOption( \Select::RETURN_PDO_STATEMENT, 1 )
			->exec()
			->fetch( \PDO::FETCH_COLUMN, 0 );
	}

	/**
	 * Write the Session data
	 *
	 * @param string $sessionId <p>
	 * The session ID to write data to
	 * </p>
	 * @param string $data <p>
	 *	The Session Data to write
	 * </p>
	 * @return bool <p>
	 * 	Returns serialized session data or an empty string if no data exists
	 * </p>
	 */
	public function write( $sessionId, $data ) {
		$stmt = $this->db->insert()
			->into( $this->tableName )
			->set( array(
				'session_id' => $sessionId
				,'data' => $data
				,'created_at' => \SqlFn::UTC_TIMESTAMP()
			) )
			->onDuplicateKeyUpdate( array(
				'data' => $data
				,'updated_at' => \SqlFn::UTC_TIMESTAMP()
			) )
			->setOption( \Select::RETURN_PDO_STATEMENT, 1 )->exec();
		return ( 0 === (int) $stmt->errorCode() );
	}

	/**
	 * Destroy the current session
	 *
	 * @param int $sessionId <p>
	 * The session ID being destroyed
	 * </p>
	 */
	public function destroy( $sessionId ) {
		$stmt = $this->db->delete()
			->from( $this->tableName )
			->where( array( 'session_id' => $sessionId ) )
			->setOption( \Select::RETURN_PDO_STATEMENT, 1 )->exec();

		return ( 0 === (int) $stmt->errorCode() );
	}

	/**
	 * Garbage Collect Sessions
	 *
	 * @param int $maxLifetime <p>
	 * Sessions that have not updated for the last $maxLifetime seconds will be removed
	 * </p>
	 * @return bool
	 */
	public function gc( $maxLifetime ) {
		$stmt = $this->db->delete()
			->from( $this->tableName )
			->where( array( 'created_at' => Exp::lt( \SqlFn::DATE_SUB( \SqlFn::UTC_TIMESTAMP(), $maxLifetime, 'SECOND' ) ) ) )
			->setOption( \Select::RETURN_PDO_STATEMENT, 1 )->exec();

		return ( 0 === (int) $stmt->errorCode() );
	}

}