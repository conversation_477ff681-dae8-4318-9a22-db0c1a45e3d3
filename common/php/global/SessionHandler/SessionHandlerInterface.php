<?php

/**
 * @file SessionHandlerInterface.php
 * This file contains the SessionHandlerInterface
 * @todo TODO Remove this once we upgrade to PHP 5.4
 */

namespace SessionHandler;


/**
 * Interface SessionHandlerInterface
 *
 */
interface SessionHandlerInterface  {

	/**
	 * Initialize session
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.open.php
	 * @param save_path string <p>
	 * The path where to store/retrieve the session.
	 * </p>
	 * @param sessionid string <p>
	 * The session id.
	 * </p>
	 * @return void &returns.session.storage.retval;
	 */
	public function open( $savePath, $sessionId );

	/**
	 * Close the session
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.close.php
	 * @return void &returns.session.storage.retval;
	 */
	public function close();

	/**
	 * Read session data
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.read.php
	 * @param sessionid string <p>
	 * The session id to read data for.
	 * </p>
	 * @return void the read data.
	 */
	public function read( $sessionId );

	/**
	 * Write session data
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.write.php
	 * @param sessionid string <p>
	 * The session id.
	 * </p>
	 * @param sessiondata string <p>
	 * The ( session_encoded ) session data.
	 * </p>
	 * @return void &returns.session.storage.retval;
	 */
	public function write( $sessionId, $data );

	/**
	 * Destroy a session
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.destroy.php
	 * @param sessionid string <p>
	 * The session ID being destroyed.
	 * </p>
	 * @return void &returns.session.storage.retval;
	 */
	public function destroy( $sessionId );

	/**
	 * Cleanup old sessions
	 * @link http://www.php.net/manual/en/sessionhandlerinterface.gc.php
	 * @param maxlifetime string <p>
	 * Sessions that have not updated for the last maxlifetime seconds will be removed.
	 * </p>
	 * @return void &returns.session.storage.retval;
	 */
	public function gc( $maxlifetime );

}
