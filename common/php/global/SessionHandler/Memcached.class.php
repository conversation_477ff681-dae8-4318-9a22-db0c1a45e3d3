<?php

/**
 * @file Memcached.class.php
 * This file contains a class for working with Memcached sessions.
 */

namespace SessionHandler;

/**
 * Class Memcached
 *
 * Session Handling using Memcached
 *
 * @example
 *	// Don't forget to setup configuration.php for the custom session handlers
 *	$memcached = new \Memcached;
 *	foreach ( explode( ',', MEMCACHED_SERVERS ) as $server ) {
 *		list( $host, $post, $weight ) = explode( ':', $server );
 *		$memcached->addServer( $host, $post, $weight );
 *	}
 *	$sessionHandler = \SessionHandler\Memcached( $memcached );
 * 	session_set_save_handler(
 * 		array( $sessionHandler, 'open' )
 * 		,array( $sessionHandler, 'close' )
 * 		,array( $sessionHandler, 'read' )
 * 		,array( $sessionHandler, 'write' )
 * 		,array( $sessionHandler, 'destroy' )
 * 		,array( $sessionHandler, 'gc' )
 * 	);
 * 	register_shutdown_function('session_write_close');
 * @endexample
 *
 * @todo TODO Update the State Provider to limit the sizes and number of data items it allows to be saved. This is to keep Memcached sizes low.
 * @todo TODO When we have PHP 5.4 load this using the interface
 * @todo TODO Create Test for this Class
 * @todo TODO Create Dummy Memcached Session Handler
 * @todo TODO Move this into the libs directory
 *
 * @note The sessions are not encrypted so don't store anything sensitive in them.
 * @note Don't whitelist the session table anywhere in the API.
 * @note Do not store Session IDs in logs and do not send them in the response body.
 * @note If requests are received from the CSI Binary's request implementation (not the web browser's)
 * 	and we respond with a Set-Cookie - it may not be seen by the web browser.
 *
 * @package CSI
 * @subpackage Session
 * <AUTHOR>
 * @since 29.10.2013
 */
class Memcached implements SessionHandlerInterface {

	/**
	 * @var string
	 * The string prefixed to memcached session keys to keep them separated from other keys
	 */
	private $keyPrefix = '_mcs.';

	/**
	 * @var Memcached
	 * Database where the Session is held
	 */
	private $memcached;

	/**
	 * Constructor
	 *
	 * @param Memcached $memcached The Memcached instance.
	 */
	public function __construct( \Memcached $memcached ) {
		if ( !defined( 'MEMCACHED_EXPIRY_SECONDS' ) ) {
			throw new \Exception('The configuration.php is not setup for the Memcached Session Handler.');
		}
		$this->memcached = $memcached;
	}

	/**
	 * Open the Session
	 *
	 * @param string $savePath
	 * @param string $sessionName
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function open( $savePath, $sessionName ) {
		/* intentionally empty */
		return true;
	}

	/**
	 * Close the Session
	 *
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function close() {
		/* intentionally empty */
		return true;
	}

	/**
	 * Reads the Session data
	 *
	 * @param string $sessionId <p>
	 * The session ID to read the data of
	 * </p>
	 * @return string <p>
	 * 	Returns serialized session data or an empty string if no data exists
	 * </p>
	 */
	public function read( $sessionId ) {
		return $this->memcached->get( $this->keyPrefix . $sessionId );
	}

	/**
	 * Write the Session data
	 *
	 * @param string $sessionId <p>
	 * The session ID to write data to
	 * </p>
	 * @param string $data <p>
	 *	The Session Data to write
	 * </p>
	 * @return bool <p>
	 * 	Returns serialized session data or an empty string if no data exists
	 * </p>
	 */
	public function write( $sessionId, $data ) {
		return $this->memcached->set( $this->keyPrefix . $sessionId, $data, \MEMCACHED_EXPIRY_SECONDS );
	}

	/**
	 * Destroy the current session
	 *
	 * @param int $sessionId <p>
	 * The session ID being destroyed
	 * </p>
	 */
	public function destroy( $sessionId ) {
		return $this->memcached->delete( $this->keyPrefix . $sessionId );
	}

	/**
	 * Garbage Collect Sessions
	 *
	 * @param int $maxLifetime <p>
	 * Not used. Memcached has it's own gc.
	 * </p>
	 * @return bool <p>
	 * Always returns true
	 * </p>
	 */
	public function gc( $maxLifetime ) {
		return true;
	}

}