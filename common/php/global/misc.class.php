<?php
/**
 * @file misc.class.php
 * Provides various hard coded data.
*/
class MISC {

	protected $commonDb = null;

	function __construct($caDb = false) {
		//TODO: Switch to private database. One of the query is fetching data from CRM
		$this->commonDb = DB::getInstanceByName('common');
		if (is_null($this->commonDb)) {
			$this->commonDb = new DB( DB_HOST_CA, DB_NAME_CA, DB_USER_CA, DB_PASS_CA, DB_UTC_TIMEZONE, DB_PORT );
		}
	}

	private $contactTitle = array(
					1 => "Chief Executive Officer"
					,2 => "Chief Information Officer"
					,3 => "Chief Security Officer"
					,4 => "Chief Technology Officer"
					,5 => "Chief Information Security Officer"
					,6 => "Director"
					,7 => "Vice President"
					,8 => "IT Director"
					,9 => "IT Manager"
					,11 => "Senior IT Consultant"
					,11 => "IT Consultant"
					,12 => "IT Security Consultant"
					,13 => "IT Security Officer"
					,14 => "IT Security Manager"
					,15 => "IT Security Administrator"
					,16 => "IT Audit Manager"
					,17 => "Manager"
					,18 => "Network Security Manager"
					,19 => "Network Systems Administrator"
					,20 => "Network Systems Engineer"
					,21 => "Project Manager"
					,22 => "Project Director"
					,23 => "Systems Engineer"
					,24 => "Compliance Officer"
					,25 => "Auditor"
					,26 => "Finance Manager"
					,27 => "Other"
					,28 => "IT System Administrator"
					,29 => "IT Security Analyst"
					,30 => "Information Security Analyst"
					,31 => "Analyst"
					,32 => "Security Manager"
					,33 => "President"
	);

	// Common text string for multiple reporting elements
	public $eolSolutionText = "End-of-Life means that the vendor no longer supports the product.  When a product is EOL, BETA, Alpha, or similar, Flexera will not be issuing advisories regarding it, as the product is not intended to be installed on production systems.  As a general rule you should consider the following actions:<br>    * Update to a newer supported version<br>    * Uninstall the product from production environments<br>    * Use a similar supported product from another vendor";

	public $viLanguages = array(
		5 => "German"
		,1 => "English"
		,2 => "English" // Duplicate, since number 2 is English and Something
	);

	public $viAlertLevels = array(
		5 => "Extremely Critical"
		,4 => "Highly Critical and Above"
		,3 => "Moderately Critical and Above"
		,2 => "Less Critical and Above"
		,1 => "Not Critical and Above"
	);

	public $cvssInterval = array(
		0 => "[ 0 , 1 ]"
		,1 => "( 1 , 2 ]"
		,2 => "( 2 , 3 ]"
		,3 => "( 3 , 4 ]"
		,4 => "( 4 , 5 ]"
		,5 => "( 5 , 6 ]"
		,6 => "( 6 , 7 ]"
		,7 => "( 7 , 8 ]"
		,8 => "( 8 , 9 ]"
		,9 => "( 9 , 10 ]"
	);

	private $productTypes = array( "CSI-SH-12" => "Corporate Software Inspector v4 (1-100 hosts), 12 months"
					,"CSI-SH-36" => "Corporate Software Inspector v4 (1-100 hosts), 36 months"
					,"CSI-SH-60" => "Corporate Software Inspector v4 (1-100 hosts), 60 months"
					,"CSI-A-12" => "Corporate Software Inspector v4 (101-400 hosts), 12 months"
					,"CSI-A-36" => "Corporate Software Inspector v4 (101-400 hosts), 36 months"
					,"CSI-A-60" => "Corporate Software Inspector v4 (101-400 hosts), 60 months"
					,"CSI-B-12" => "Corporate Software Inspector v4 (401-1000 hosts), 12 months"
					,"CSI-B-36" => "Corporate Software Inspector v4 (401-1000 hosts), 36 months"
					,"CSI-B-60" => "Corporate Software Inspector v4 (401-1000 hosts), 60 months"
					,"CSI-C-12" => "Corporate Software Inspector v4 (1001-2500 hosts), 12 months"
					,"CSI-C-36" => "Corporate Software Inspector v4 (1001-2500 hosts), 36 months"
					,"CSI-C-60" =>  "Corporate Software Inspector v4 (1001-2500 hosts), 60 months"
					,"CSI-TRIAL10-1" => "Corporate Software Inspector v4 (10 hosts), 1 month TRIAL"
	);

	private $industryList = array( 1 => "Aerospace & Defense"
					,2 => "Agriculture"
					,3 => "Automotive & Transport"
					,4 => "Banking"
					,5 => "Banking & Finance"
					,6 => "Beverages"
					,7 => "Business Services"
					,8 => "Charitable Organizations"
					,9 => "Chemical & Pharmaceutical"
					,10 => "Chemicals"
					,11 => "Computer Hardware"
					,12 => "Computer Services"
					,13 => "Computer Software"
					,14 => "Construction"
					,15 => "Construction & Contractors"
					,16 => "Consumer Products Manufacturers"
					,17 => "Consumer Services"
					,18 => "Cultural Institutions"
					,19 => "Education"
					,20 => "Electronical & Electrical"
					,21 => "Electronics"
					,22 => "Energy & Utilities"
					,23 => "Environmental Services & Equipment"
					,24 => "Financial Services"
					,25 => "Food"
					,26 => "Food & Beverage"
					,27 => "Foundations"
					,28 => "Government"
					,29 => "Graphical & Publishing"
					,30 => "Health Care"
					,31 => "Healthcare, Hospital"
					,32 => "IT"
					,33 => "IT Security"
					,34 => "IT Services Provider"
					,35 => "Industrial Manufacturing"
					,36 => "Insurance"
					,37 => "Legal"
					,38 => "Leisure"
					,39 => "Mechanical"
					,40 => "Media"
					,41 => "Membership Organizations"
					,42 => "Metals & Mining"
					,43 => "Military"
					,44 => "Mineral"
					,45 => "Miscellaneous"
					,46 => "Pharmaceuticals"
					,47 => "Real Estate"
					,48 => "Retail"
					,49 => "Road, Air, Sea transportation"
					,50 => "Security Products & Services"
					,51 => "Services"
					,52 => "Telecommunication"
					,53 => "Telecommunications Equipment"
					,54 => "Telecommunications Services"
					,55 => "Textile"
					,56 => "Transportation Services"
					,57 => "Utilities"
					,58 => "Wholesale Durables and Non-Durables"
					,59 => "Wood, Paper, Furniture"
					,60 => "Other"
	);

	private $partnerList = array( 1 => "Consultant"
					,2 => "MSSP"
					,3 => "System Integrator"
					,4 => "Reseller"
	);

	private $deploymentTools = array( 1 => "Alteris"
					,2 => "Capa"
					,3 => "ZENworks"
					,4 => "Shavlik"
					,5 => "Gfi"
					,6 => "Lumension"
					,7 => "Foundstone"
					,8 => "McAffee"
					,9 => "LANDesk"
	);

	private $priceList = array(
		0 => array( 0 => "CSI-SH-12", 1 => "<100", 2 => "12 Months", 3 => "2000", 4 => "2440", 5 => "P", 6 => "30.00", 7=> "1400", 8 => "1708" )
		,1 => array( 0 => "CSI-SH-36", 1 => "<100", 2 => "36 Months", 3 => "5000", 4 => "6100", 5 => "P", 6 => "30.00", 7=> "3500", 8 => "4270" )
		,2 => array( 0 => "CSI-SH-60", 1 => "<100", 2 => "60 Months", 3 => "7000", 4 => "8540", 5 => "P", 6 => "30.00", 7=> "4900", 8 => "5978" )
		,3 => array( 0 => "CSI-A-12", 1 => "<400", 2 => "12 Months", 3 => "6000", 4 => "7320", 5 => "P", 6 => "30.00", 7=> "4200", 8 => "5124" )
		,4 => array( 0 => "CSI-A-36", 1 => "<400", 2 => "36 Months", 3 => "15000", 4 => "18300", 5 => "P", 6 => "30.00", 7=> "10500", 8 => "12810" )
		,5 => array( 0 => "CSI-A-60", 1 => "<400", 2 => "60 Months", 3 => "21000", 4 => "25620", 5 => "P", 6 => "30.00", 7=> "14700", 8 => "17934" )
		,6 => array( 0 => "CSI-B-12", 1 => "<1000", 2 => "36 Months", 3 => "12000", 4 => "14640", 5 => "P", 6 => "30.00", 7=> "8400", 8 => "10248" )
		,7 => array( 0 => "CSI-B-36", 1 => "<1000", 2 => "60 Months", 3 => "30000", 4 => "36600", 5 => "P", 6 => "30.00", 7=> "21000", 8 => "25620" )
		,8 => array( 0 => "CSI-B-60", 1 => "<1000", 2 => "36 Months", 3 => "42000", 4 => "51240", 5 => "P", 6 => "30.00", 7=> "29400", 8 => "35868" )
		,9 => array( 0 => "CSI-C-12", 1 => "<2500", 2 => "12 Months", 3 => "24000", 4 => "29280", 5 => "P", 6 => "30.00", 7=> "16800", 8 => "20496" )
		,10 => array( 0 => "CSI-C-36", 1 => "<2500", 2 => "36 Months", 3 => "60000", 4 => "73200", 5 => "P", 6 => "30.00", 7=> "42000", 8 => "51240" )
		,11 => array( 0 => "CSI-C-60", 1 => "<2500", 2 => "60 Months", 3 => "84000", 4 => "102480", 5 => "P", 6 => "30.00", 7=> "58800", 8 => "71736" )
	);

	private $NISTCVSSA = array(
			// Base metrics
			'AV' => array(
				'outputName' => 'Access Vector'
				,'L' => array('Local', 0.395)
				,'A' => array('Adjacent Network', 0.646)
				,'N' => array('Network', 1.0)
			)
			,'AC' => array(
				'outputName' => 'Access Complexity'
				,'H' => array('High', 0.35)
				,'M' => array('Medium', 0.61)
				,'L' => array('Low', 0.71)
			)
			,'Au' => array(
				'outputName' => 'Authentication'
				,'M' => array('Multiple', 0.45)
				,'S' => array('Single', 0.56)
				,'N' => array('None', 0.704)
			)
			,'C' => array(
				'outputName' => 'Confidentiality Impact'
				,'N' => array('None', 0)
				,'P' => array('Partial', 0.275)
				,'C' => array('Complete', 0.660)
			)
			,'I' => array(
				'outputName' => 'Integrity Impact'
				,'N' => array('None', 0)
				,'P' => array('Partial', 0.275)
				,'C' => array('Complete', 0.660)
			)
			,'A' => array(
				'outputName' => 'Availability Impact'
				,'N' => array('None', 0)
				,'P' => array('Partial', 0.275)
				,'C' => array('Complete', 0.660)
			)
			// Temporal metrics
			,'E' => array(
				'outputName' => 'Exploitability'
				,'ND' => array('Not Defined', 1.0)
				,'U' => array('Unproven', 0.85)
				,'POC' => array('Proof of concept', 0.9)
				,'F' => array('Functional', 0.95)
				,'H' => array('High', 1.0)
			)
			,'RL' => array(
				'outputName' => 'Remediation Level'
				,'ND' => array('Not Defined', 1.0)
				,'OF' => array('Official-fix', 0.87)
				,'TF' => array('Temporary-fix', 0.90)
				,'W' => array('Workaround', 0.95)
				,'U' => array('Unavailable', 1.0)
			)
			,'RC' => array(
				'outputName' => 'Report Confidence'
				,'ND' => array('Not Defined', 1.0)
				,'UC' => array('Unconfirmed', 0.90)
				,'UR' => array('Uncorroborated', 0.95)
				,'C' => array('Confirmed', 1.0)
			)
			// Environmental metrics
			,'CDP' => array(
				'outputName' => 'Collateral Damage Potential'
				,'ND' => array('Not Defined', 0)
				,'N' => array('None', 0.0)
				,'L' => array('Low', 0.1)
				,'LM' => array('Low-Medium', 0.3)
				,'MH' => array('Medium-High', 0.4)
				,'H' => array('High', 0.5)
			)
			,'TD' => array(
				'outputName' => 'Target Distribution'
				,'ND' => array('Not Defined', 1.0)
				,'N' => array('None', 0)
				,'L' => array('Low', 0.25)
				,'M' => array('Medium', 0.75)
				,'H' => array('High', 1.0)
			)
			,'CR' => array(
				'outputName' => 'Confidentiality Requirement'
				,'ND' => array('Not Defined', 1.0)
				,'L' => array('Low', 0.5)
				,'M' => array('Medium', 1.0)
				,'H' => array('High', 1.51)
			)
			,'IR' => array(
				'outputName' => 'Integrity Requirement'
				,'ND' => array('Not Defined', 1.0)
				,'L' => array('Low', 0.5)
				,'M' => array('Medium', 1.0)
				,'H' => array('High', 1.51)
			)
			,'AR' => array(
				'outputName' => 'Availability Requirement'
				,'ND' => array('Not Defined', 1.0)
				,'L' => array('Low', 0.5)
				,'M' => array('Medium', 1.0)
				,'H' => array('High', 1.51)
			)
	);

	function returnNISTCVSSArray() {
		// Array with NIST CVSS data
		return $this->NISTCVSSA;
	}

	private $NISTCVSS3A = array (
		// Base metrics
		'AV' => array
		(
		    'outputName' => 'Attack Vector',
		    'N' => array('Network', 0.85),
		    'A' => array('Adjacent Network', 0.62),
		    'L' => array('Local', 0.55),
		    'P' => array('Physical', 0.2),
		),
		'AC' => array
		(
		    'outputName' => 'Attack Complexity',
		    'H' => array('High', 0.44),
		    'L' => array('Low', 0.77),
		),
		'PR' => array
		(
		    'outputName' => 'Privileges Required',
		    'N' => array('None', 0.85),
		    'L' => array('Low', 0.62),
		    'H' => array('High', 0.27),
		),
		'UI' => array
		(
		    'outputName' => 'User Interaction',
		    'N' => array('None', 0.85),
		    'R' => array('Required', 0.62),
		),
		'S' => array
		(
		    'outputName' => 'Scope',
		    'U' => array('Unchanged', 6.42),
		    'C' => array('Changed', 7.52),
		),
		'C' => array
		(
		    'outputName' => 'Confidentiality',
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		),
		'I' => array
		(
		    'outputName' => 'Integrity',
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		),
		'A' => array
		(
		    'outputName' => 'Availability',
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		),
		// Temporal metrics
		'E' => array
		(
		    'outputName' => 'Exploitability',
		    'X' => array('Not Defined', 1.0),
		    'U' => array('Unproven', 0.91),
		    'P' => array('Proof-of-Concept', 0.94),
		    'F' => array('Functional', 0.97),
		    'H' => array('High', 1.0),
		),
		'RL' => array
		(
		    'outputName' => 'Remediation Level',
		    'X' => array('Not Defined', 1.0),
		    'O' => array('Official Fix', 0.95),
		    'T' => array('Temporary Fix', 0.96),
		    'W' => array('Workaround', 0.97),
		    'U' => array('Unavailable', 1.0),
		),
		'RC' => array
		(
		    'outputName' => 'Report Confidence',
		    'X' => array('Not Defined', 1.0),
		    'U' => array('Unknown', 0.92),
		    'R' => array('Reasonable', 0.96),
		    'C' => array('Confirmed', 1.0),
		),
		// Environmental metrics
		'CR' => array
		(
		    'outputName' => 'Confidentiality Requirement',
		    'X' => array('Not Defined', 0),
		    'L' => array('Low', 0.5),
		    'M' => array('Medium', 1.0),
		    'H' => array('High', 1.5),
		),
		'IR' => array
		(
		    'outputName' => 'Integrity Requirement',
		    'X' => array('Not Defined', 1.0),
		    'L' => array('Low', 0.5),
		    'M' => array('Medium', 1.0),
		    'H' => array('High', 1.5),
		),
		'AR' => array
		(
		    'outputName' => 'Availability Requirement',
		    'X' => array('Not Defined', 1.0),
		    'L' => array('Low', 0.5),
		    'M' => array('Medium', 1.0),
		    'H' => array('High', 1.5),
		),
		'MAV' => array
		(
		    'outputName' => 'Modified Attack Vector',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('Network', 0.85),
		    'A' => array('Adjacent Network', 0.62),
		    'L' => array('Local', 0.55),
		    'P' => array('Physical', 0.2),
		),
		'MAC' => array
		(
		    'outputName' => 'Modified Attack Complexity',
		    'X' => array('Not Defined', 1.0),
		    'H' => array('High', 0.44),
		    'L' => array('Low', 0.77),
		),
		'MPR' => array
		(
		    'outputName' => 'Modified Privileges Required',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('None', 0.85),
		    'L' => array('Low', 0.62),
		    'H' => array('High', 0.27),
		),
		'MUI' => array
		(
		    'outputName' => 'Modified User Interaction',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('None', 0.85),
		    'R' => array('Required', 0.62),
		),
		'MS' => array
		(
		    'outputName' => 'Modified Scope',
		    'X' => array('Not Defined', 1.0),
		    'U' => array('Unchanged', 6.42),
		    'C' => array('Changed', 7.52),
		),
		'MC' => array
		(
		    'outputName' => 'Modified Confidentiality',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		),
		'MI' => array
		(
		    'outputName' => 'Modified Integrity',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		),
		'MA' => array
		(
		    'outputName' => 'Modified Availability',
		    'X' => array('Not Defined', 1.0),
		    'N' => array('None', 0),
		    'L' => array('Low', 0.22),
		    'H' => array('High', 0.56),
		)
	);

    	function returnNISTCVSS3Array() {
		// Array with NIST CVSS3 data
		return $this->NISTCVSS3A;
    	}

	/**
	 * VIM Only
	 * VIM's csiUserManagement
	 */
	public $newVIMAccount = array(
		'from' => EMAIL_NEW_VIM_ACCOUNT_FROM
		,'subject' => 'Flexera - Login Credentials'
		,'content' => "Dear {account_name},\r\nYour login credentials to the {product} are as follows:\r\n\r\nUsername: {account_username}\r\nPassword: {account_password}\r\n\r\nPlease visit this URL to login:\r\n{ca_domain} \r\n\r\nKind regards,\r\n\r\nFlexera"
		);

	/**
	 * VIM Only
	 * VIM's index.php
	 */
	public $suggestSoftwareMail = array(
		'from' => EMAIL_SUGGEST_SOFTWARE_FROM
		,'to' => EMAIL_SUGGEST_SOFTWARE_TO
	);

	/**
	 * VIM Only
	 * VIM's viContacts
	 */
	public $emailTest = array(
		'from' => EMAIL_TEST_FROM
		,'subject' => 'Secunia Advisory Email Message Test'
		,'content' => "Hi,\r\n\r\nFlexera email message test. Reading this message means that your are able to receive emails from Flexera.\r\n\r\nKind regards,\r\n\r\nFlexera" );

	/**
	 * VIM Only
	 * VIM's viContacts
	 */
	public $smsTest = array(
		'content' => "Secunia advisory SMS message test. Reading this message means that you are able to receive SMS alerts from Flexera.\n"
	);

	/**
	 * VIM Only
	 * VIM's viContacts and assetSync
	 */
	public $smsText = array(
		'content' => "Flexera\n{critical}\n{vuln_title}\n"
	);

	/**
	 * VIM Only
	 */
	public $solutionStatus = array(
		1 => "Unpatched",
		2 => "Vendor Patch",
		3 => "Vendor Workaround",
		4 => "Partial Fix"
	);

	/**
	 * VIM Only
	 * VIM's assetSync
	 */
	public $newAssetAdvisoryEmailGerman = array(
		'from' => EMAIL_NEW_ASSET_ADVISORY_GERMAN_FROM
		,'subject' => "[SECUNIA] [SA{vuln_id}] [{rating}] {vuln_title}"
		,'content' => "Titel:
{vuln_title}

SECUNIA ADVISORY ID:
SA{vuln_id}

ADVISORY VERIFIZIEREN:
{ca_domain}?action=viewadvisory&vulnid={vuln_id}

GEFAHRENSTUFE:
{critical}

AUSWIRKUNG:
{impact}

VON WO:
{where}
{revision}
{cvss}
{os}
{software}
{text}
----------------------------------------------------------------------
Flexera empfiehlt, alle erhaltenen Advisories zu verifizieren, indem
Sie auf den Link klicken.
Flexera versendet NIEMALS Advisories mit Anh채ngen.
Flexera r채t nicht zur Installation von Patches aus dritter Hand.
Nutzen Sie nur Patches des Herstellers.

Kontakt :
Web     : http://secunia.com/
Support : https://secunia.com/support/
Tel     : +45 7020 5144
Fax     : +45 7020 5145"
		);

	/**
	 * VIM Only
	 * VIM's assetSync
	 */
	public $newAssetAdvisoryEmail = array(
		'from' => EMAIL_NEW_ASSET_ADVISORY_FROM
		,'subject' => "[SECUNIA] [SA{vuln_id}] [{rating}] {vuln_title}"
		,'content' => "
=======================================================================

This Secunia Advisory is licensed to:

Licensed to: {company}
Department: {department}
Recipient: {name} / {email}

This advisory may not be distributed to any other person, department, or company, other than as set out and specified in the order confirmation or contract with Flexera. Please contact your Flexera account manager for details regarding licensing for further distribution of Secunia Advisories.

=======================================================================

PRIORITY:
{priority}

COMMENT:
{comment}

TITLE:
{vuln_title}

SECUNIA ADVISORY ID:
SA{vuln_id}

VERIFY ADVISORY:
{ca_domain}?action=viewadvisory&vulnid={vuln_id}

CRITICAL:
{critical}

IMPACT:
{impact}

WHERE:
{where}
{revision}
{cvss}
{os}
{software}
{text}

=======================================================================

This Secunia Advisory is licensed to:

Licensed to: {company}
Department: {department}
Recipient: {name} / {email}

This advisory may not be distributed to any other person, department, or company, other than as set out and specified in the order confirmation or contract with Flexera. Please contact your Flexera account manager for details regarding licensing for further distribution of Secunia Advisories.

=======================================================================

Flexera recommends that you verify all advisories you receive by clicking the link.
Flexera NEVER sends attached files with advisories.
Flexera does not advise people to install third party patches, only use those supplied by the vendor.

Definitions: (Criticality, Where etc.)
http://secunia.com/about_secunia_advisories/

Contact details:
Web		: http://secunia.com/
Support	: https://secunia.com/support/
Tel		: +45 7020 5144
Fax		: +45 7020 5145"
		);

	/**
	 * VIM Only
	 * VIM's assetSync
	 */
	public $newAdvisoryXMLEmail = array(
		'content' => '<?xml version="1.0" encoding="iso-8859-1" standalone="yes"?>
<advisories>
<advisory id="{vuln_id}">
<advisory_id><![CDATA[ {vuln_id} ]]></advisory_id>
<secunia_advisory_link><![CDATA[ {ca_domain}?action=viewadvisory&vulnid={vuln_id} ]]></secunia_advisory_link>
<date><![CDATA[ {vuln_create_date} ]]</date>
<last_modified_datetime><![CDATA[ {vuln_modified_date} ]]></last_modified_datetime>
<advisory_revision><![CDATA[ {vuln_revision} ]]></advisory_revision>
<title><![CDATA[ {vuln_title} ]]>
<critical><![CDATA[ {vuln_critical_boolean} ]]></critical>
<impact><![CDATA[ {impact} ]]></impact>
<where><![CDATA[ {where} ]]></where>
{cvss}
{os}
{software}
{text}
</advisory>
</advisories>'
		);

	private function fetchArray( $array, $keyName, $valueName ) {
		$i = 0;
		foreach ( $array as $key => $value ) {
			$data[$i][$keyName] = $key;
			$data[$i][$valueName] = $value;
			$i++;
		}
		return $data;
	}

	function fetchIndustryData() {
		return $this->fetchArray( $this->industryList, "company_industry", "industry" );
	}

	function fetchPartnerData() {
		return $this->fetchArray( $this->partnerList, "category_id", "category" );
	}

	function fetchDeploymentToolsData() {
		return $this->fetchArray( $this->deploymentTools, "deployment_id", "deployment_name" );
	}

	function fetchProductName( $productId ) {
		return $this->productTypes[$productId];
	}

	function fetchProductData() {
		return $this->fetchArray( $this->productTypes, "product_type", "product_name" );
	}

	function fetchPriceList() {
		return $this->priceList;
	}

	function fetchContactTitleList() {
		return $this->fetchArray( $this->contactTitle, "title_id", "title_name" );
	}

	function fetchContactTitleName( $titleId ){
		return $this->contactTitle[$titleId];
	}

	// todo:	The function does not validate the $to parameter and sends an email to any address passed in.
	// 			Also, the $subject and $message parameters should also be sanitized.
	function sendMail( $from, $to, $subject, $message ) {
		if ( preg_match('/[\r]/', $from) || preg_match('/[\n]/', $from) ) {
			return false;
		}

		$headers = "From: ".$from. "\r\n" . "Reply-To: ".$from."\r\n". "X-Mailer: SECUNIA mail system\r\n";
		return mail( $to, $subject, $message, $headers );
	}

	function returnRegionNameFromCountryID ( $countryID ) {
		$country = $this->commonDb->select()
			->from('crm.countries')
			->where(array('id' => (int)$countryID))
			->setOption(Select::RETURN_PDO_STATEMENT, 1)
			->exec()
			->fetch();
		return $this->returnRegionName( $country['region'] );
	}

	function returnRegionName( $region ) {
		switch ( $region ) {
			case 1:
				return 'Europe';
			case 2:
				return 'America';
			case 3:
				return 'Asia';
			default:
				return 'n/a';
		}
	}

	private $disallowedCveSources = array(
		'FRSIRT' => true
		,'K-OTIK' => true
		,'VUPEN' => true
	);

	function fetchDisallowedSource( $name ) {
		if ( isset( $this->disallowedCveSources[$name] ) ) {
			return $this->disallowedCveSources[$name];
		} else {
			return false;
		}
	}

	function fetchCriticalityImage( $level ) {
		if ( $level <= 0 || $level > 5 ) {
			$level = 1; // Default is the maximum criticality image i.e. 6-1 = 5
		}
		return "gfx/pdf/crit_" . ( 6 - $level ) . ".png";
	}

	function fetchCriticalityText( $level ) { // For certain speed optimizations
		switch ( $level ) {
			case "1":
				return 'Extremely critical';
			case "2":
				return 'Highly critical';
			case "3":
				return 'Moderately critical';
			case "4":
				return 'Less critical';
			case "5":
				return 'Not critical';
		}
	}


	function returnCriticalityColor( $iType ) {
		switch ( $iType ) {
		case 1: // Red
			return '#CC0000';
		case 2: // Orange
			return '#FF9400';
		case 3: // Yellow
			return '#FFDB00';
		case 4: // Green 1
			return '#CCFF33';
		case 5: // Green 2
			return '#00BF12';
			// then the next 5 are the light versions of these for the last week data
		case 6:
			return '#FFCCCC';
		case 7:
			return '#FFCC99';
		case 8:
			return '#FFFFCC';
		case 9:
			return '#CCFFCC';
		case 10:
			return '#CCFF99';
		}
		return '#000000';
	}

	/**
	 * Return an array of criticality data, associating the vuln_critical_boolean value with
	 * its correct text string.  Note, the vuln_critical_boolean that is used for a given
	 * vuln uses the critical_type_id, not the _value_ as it should, so account for that here.
	 */
	function getCriticalTypesArray() {

		$critTypes = array();
		$aCritRows = $this->commonDb->select()
			->from('vuln_track.critical_type')
			->where(array('lang_id' => 1))
			->exec();

		foreach ( $aCritRows as $aCritRow ) {
			$critTypes[$aCritRow['critical_type_id']] = $aCritRow['critical_type_name'];
		}

		return $critTypes;
	}

	/*
	 * Return an array of the impact types with the type name.
	 * Account for the irregularity in the DB with type_values of 11
	 * or 12 and map them to 21, 34 respectively
	 *
	 */
	function getImpactTypesArray() {

		$impactTypes = array();
		$aImpactRows = $this->commonDb->select()
			->from('vuln_track.impact_type')
			->where(array('lang_id' => 1))
			->exec();
		foreach ( $aImpactRows as $aImpactRow ) {
			// No impact_type_info entries exist for impact_type_value of 11 or 12, instead they
			// map to impact_type_values of 21 and 34.  Here we will correct the problem.
			if ( 11 == $aImpactRow['impact_type_value'] ) {
				$aImpactRow['impact_type_value'] = 21;
			}
			if ( 12 == $aImpactRow['impact_type_value'] ) {
				$aImpactRow['impact_type_value'] = 34;
			}

			$impactTypes[$aImpactRow['impact_type_value']] = $aImpactRow['impact_type_name'];
		}

		return $impactTypes;
	}

	/*
	 * Return an array of the where (attack vector) types with the type name.
	 *
	 */
	function getWhereTypesArray() {

		$whereTypes = array();
		$aWhereRows = $this->commonDb->select()
			->from('vuln_track.where_type')
			->where(array('lang_id' => 1))
			->exec();
		foreach ( $aWhereRows as $aWhereRow ) {
			$whereTypes[$aWhereRow['where_type_value']] = $aWhereRow['where_type_name'];
		}
		return $whereTypes;
	}

	/**
	 * Function for fetching a remote page, using curl.
	 * NOTE: This function is proxy aware and will use the PROXY settings in the CA configuration file.
	 *
	 * Configuration options (OPTIONAL): PROXY_HOST, PROXY_USERNAME, PROXY_PASSWORD, PROXY_PORT -> self explanatory proxy configuration options
	 *
	 * @param url
	 *	String URL to fetch data from
	 * @param status
	 *	String status reference
	 * @param postdata
	 *	String optional post data
	 * @return
	 *	String fetched content
	*/
	function curlLoadPage($url, &$status, $postdata ='', $headers = '') {
		// Initialize curl
		$ch = curl_init();

		// Set some curl parameters [ see http://dk2.php.net/curl_setopt ]
		curl_setopt($ch, CURLOPT_URL, $url);		// The URL
		curl_setopt($ch, CURLOPT_HEADER, 0);		// Supress output of headers
		curl_setopt($ch, CURLOPT_TIMEOUT, 0);		// Don't time out
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);	// Follow redirects
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);	// Return output from exec
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 120);	// Increase default timeout
		curl_setopt($ch, CURLOPT_MAXREDIRS, 50);	// Max redirects to follow

		// Proxy host/port
		if( defined('PROXY_HOST') && strlen( PROXY_HOST ) && defined('PROXY_PORT') && strlen( PROXY_PORT ) ) {
			curl_setopt( $ch, CURLOPT_PROXY, PROXY_HOST . ":" . PROXY_PORT );
		}

		// Proxy credentials
		if( defined('PROXY_USERNAME') && strlen( PROXY_USERNAME ) && defined('PROXY_PASSWORD') && strlen( PROXY_PASSWORD ) ) {
			curl_setopt( $ch, CURLOPT_PROXYUSERPWD, PROXY_USERNAME . ":" . UTIL::decryptConfigConstant(constant('PROXY_PASSWORD')) );
		}

		// Post data?
		if ( $postdata != '' ) {
			curl_setopt( $ch, CURLOPT_POST, 1 );
			curl_setopt( $ch, CURLOPT_POSTFIELDS, $postdata );
		}

		//Headers
		if ( $headers != '' ) {
			curl_setopt( $ch, CURLOPT_HTTPHEADER, $headers );
		}

		// If requested, ignore option to verify remote certificate.
		if( !SSL_VERIFY_HOST ) {
			curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );
		}

		// curl_exec returns 0 on error and curl_error($ch) returns the error string
		$content = curl_exec( $ch );
		if( $content === false ) {
			die( curl_error( $ch ) . "\n" );
		}

		// Retrieve the status code of the operation and store it for the caller
		$status = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

		// Retrieve the last effective URL to see if redirection took place
		$last_url = curl_getinfo( $ch, CURLINFO_EFFECTIVE_URL );

		// All done with the curl handle for the moment
		curl_close( $ch );

		// Were we redirected?
		$redirected = ( $last_url != $url );

		//
		// There is a bug in curl that results in redirects not working properly
		// when proxy credentials are supplied.  In the subsequent request the
		// credentials will be missing.  Bug fixed on 19 February, 2005.
		//
		//   [ http://curl.haxx.se/docs/verdiff.cgi?r1=7.13.0&r2=7.13.1 ]
		//
		// Workaround:
		// If status is 407 ("Proxy Authentication Required") and a redirect took
		// place at some point, call ourselves with the last effective URL.
		//
		if ( $redirected && $status == 407 ) {
			return $this->curlLoadPage( $last_url, $status );
		}

		return $content;
	}

	// Function for generating links
	//
	// todo:	The function generates project specific links and hence should not be part of the
	//			global space
	//				i.e. in case $pdf = true
	//			Need to either modify the function so that it doesn't format the text
	//			differently or move this function to the local space.
	//			Until the changes take place, this function should not be used.
	function advisoryGenerateLinks( $text, $links = false, $pdf = false ) {
		if ( !$pdf ) {
			$text = UTIL::htmlspecialchars( $text );
		}
		$text = trim( $text ).( $text != "" ? "\n" : "" );

		if ( $links ) {
			preg_match_all( "((https)?(ftp)?(http)?://[^\n]+)", $text, $regs );

			$regs[0] = array_unique($regs[0]);

			while ( list($key, $value) = each($regs[0]) ) {
				if ( strlen($value) > 70 ) {
					$display = substr($value, 0, 32) . '...' . substr($value, -32);
				} else {
					$display = $value;
				}

				$text = str_replace( $value . "\n", '<a href="' . $value . '" class="linkInText" target="_blank">' . $display . '</a>'."\n", $text );
			}

		} else {
			if ( !$pdf ) {
				$text = preg_replace( "(http://(www\.)?(secunia[^\n]+))", '<a href="http://\\2" class="linkInText" target="_blank">http://\\2</a>'."\n", $text );
			} else {
				$text = preg_replace( "(http://(www\.)?(secunia[^\n]+))", 'http://\\2'."\n", $text );
			}
		}

		$temp = explode( "\n", $text );
		$text = ""; // Destroy the text
		$size = count( $temp );
		for ( $i = 0; $i < $size; $i++ ) {
			if ( !strstr( $temp[$i], "http://secunia.com/advisories/" ) ) {
				$text .= $temp[$i]."\n"; // Rebuild without the above link
			}
		}

		// SAID reference links
		preg_match_all( "(SA[0-9]{4,5})", $text, $regs );

		// Begin replacing the values found above
		$size = count( $regs[0] );
		for ( $i = 0; $i < $size; $i++ ) {
			$temp = str_replace( "SA", "", $regs[0][$i] );
			$temp = str_replace( ":", "", $temp );

			//	Todo:
			//	The CA_DOMAIN appended link will work for the VIM but not for the CSI since it also needs to UID
			//	and we can't append the UID there. The pdf generation functionality should go into the local advisory class.

			if ( $pdf == false ) {
				$text = str_replace( "SA".$temp.":", "<a href='javascript:sfw.advisory.view(\"" .$temp. "\")'>SA" .$temp. "</a>", $text );
				$text = str_replace( "SA".$temp, "<a href='javascript:sfw.advisory.view(\"" .$temp. "\")'>SA" .$temp. "</a>", $text );
			} else {
				$text = str_replace( "SA".$temp.":", CA_DOMAIN."?action=viewadvisory&vulnid=" .$temp, $text );
				$text = str_replace( "SA".$temp, CA_DOMAIN."?action=viewadvisory&vulnid=" .$temp, $text );
			}
		}

		// Normalize
		$text = str_replace( "\r", "", $text ); // \r was lost in transit

		// Return content and remove any additional newlines
		return trim($text);
	}

	function formatExploitCode( $text ) {
		$text = UTIL::htmlspecialchars($text);

		while ( preg_match("#\[code\][\n]*(.*)\[/code\]#Us", $text, $regs) ) {
			// Remove any leading newlines and <br> tags from the code block
			$regs[1] = preg_replace("/^(<br>)*/", "", trim($regs[1]));
			$regs[1] = str_replace("\t", "&nbsp;&nbsp;&nbsp;", $regs[1]);

			$lines = count( explode( "\n", $regs[1] ) );
			$lines = ( $lines > 30 ? 30 : $lines );

			$code = '<pre>' . $regs[1] . '</pre>';
			$text = str_replace( $regs[0], $code, $text );
		}

		while ( preg_match("#\[(x?)url=(.*)\](.*)\[/x?url\]#Us", $text, $regs) ) {
			// Is it an [xurl] (external URL) tag?
			if( $regs[1] ) {
				// [xurl]
				$link = '<a class="linkInText" href="' . $regs[2] . '" onClick="return confirm(\'' .
					"WARNING: You are about to exit the Flexera customer area to a site that may be untrustworty and potentially malicious in nature.Do you wish to proceed?" . '\');">';
			} else {
				// just [url]
				$link = '<a class="linkInText" href="' . $regs[2] . '">';
			}

			$link .= $regs[3] . '</a>';

			$text = str_replace($regs[0], $link, $text);
		}
		return $text;
	}


	/**
	 * Apply a recurrence schedule and (optionally) a timeFrame to an input date.
	 * Can be used for reporting, notifications, etc. Anywhere a time interval is
	 * applied to a date.
	 *
	 * @param inputDate
	 *  Date - the date you want to calculate the next date or next interval from
	 *
	 * @param recurrenceArray
	 *   Array - the values representing the amount of time to move forward.
	 *        Note: to be compatible with existing data, this can be a 3-tuple,
	 *        representing (days,weeks,months), or a 4-tuple with 'hours' being
	 *        the first value.  i.e. [0,1,0] OR [0,0,1,0]
	 *
	 * @param timeFrameArray (optional)
	 *   Array - the values representing the amount of time to take for the time
	 *        interval used. The recurrence array above moves the inputDate forward
	 *        by the prescribed amount, and the timeInterval, if defined, will
	 *        determine a 'startDate'
	 *
	 * @return
	 *   Array - [startDate, endDate]
	 *        Here the endDate is the inputDate moved forward by the recurrence, and
	 *        the startDate is only non-empty if a time interval is defined, in which
	 *        case the startDate will be the endDate minus the specified interval.
	 *
	 */
	function getScheduledDates( $inputDate, $recurrenceArray, $timeFrameArray = NULL ) {

		// First validate input
		if ( !$inputDate
			 || !$recurrenceArray
			 || ( 3 != count($recurrenceArray) && 4 != count($recurrenceArray) )
			 || ( $timeFrameArray
				  && (3 != count($timeFrameArray) && 4 != count($timeFrameArray) )
				  )
			 ) {
			return false;
		}

		// Specify the order for the recurrence array using the right mysql keywords.
		$timeUnitArray = array('HOUR', 'DAY', 'WEEK', 'MONTH');

		// Determine if we are using 3-tuples or 4-tuples, and normalize
		if ( 3 == count($recurrenceArray) ) {
			// If using 3-tuples, append a 0 for the 'hours', for both recurrence
			// and timeFrame (if applicable)
			array_unshift( $recurrenceArray, 0  );
			if ( $timeFrameArray && 3 == count($timeFrameArray) ) {
				array_unshift( $timeFrameArray, 0  );
			}
		}

		// First get the new end date using the recurrence schedule
		$query = "DATE_ADD('" . $inputDate . "', INTERVAL " . (int) $recurrenceArray[0] . " " . $timeUnitArray[0] . ")";
		$query = "DATE_ADD(" . $query . ", INTERVAL " . (int) $recurrenceArray[1] . " " . $timeUnitArray[1] . ")";
		$query = "DATE_ADD(" . $query . ", INTERVAL " . (int) $recurrenceArray[2] . " " .  $timeUnitArray[2] . ")";
		$query = "SELECT DATE_ADD(" . $query . ", INTERVAL " . (int) $recurrenceArray[3] . " " . $timeUnitArray[3] . ") AS result";

		$result = $this->commonDb->execRaw( $query )->fetch();
		$endDate = $result[ 'result' ];

		// We only get the start date if the timeFrameArray exists
		if ( $timeFrameArray && 4 == count($timeFrameArray) ) {
			// Get the new start date, going back from the new end date
			$query = "DATE_ADD('" . $endDate . "', INTERVAL -" . (int) $timeFrameArray[0] . " " . $timeUnitArray[0] . ")";
			$query = "DATE_ADD(" . $query . ", INTERVAL -" . (int) $timeFrameArray[1] . " " . $timeUnitArray[1] . ")";
			$query = "DATE_ADD(" . $query . ", INTERVAL -" . (int) $timeFrameArray[2] . " " .  $timeUnitArray[2] . ")";
			$query = "SELECT DATE_ADD(" . $query . ", INTERVAL -" . (int) $timeFrameArray[3] . " " . $timeUnitArray[3] . ") AS result";
			$result = $this->commonDb->execRaw( $query )->fetch();;
			$startDate = $result[ 'result' ];

		} else {
			$startDate = '';
		}

		return array( $startDate, $endDate );
	}

}
