<?php
/**
 * @file stamp.class.php
 *	Provides "stamping" functionality ( user details that is )
*/
class STAMP {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
		$this->database['crm'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	function getCompanyName( $accountId, $customerId ) { // Set by the CRM, on the root account
		$companyName = $this->database['ca']->getRowValue( "ca.accounts", "account_company", "account_id = '".(int)$accountId."'" );
		if (EDITION == HOSTED_EDITION && !$companyName) {
                        return iconv( 'UTF-8', 'ISO-8859-1', "" );
                }
		if ( !$companyName ) {
			$masterId = $this->database['ca']->getRowValue( "crm.cst", "master_id", "cst_id = '".(int)$customerId."'" );
			if ( $masterId ) {
				$customerId = (int) $masterId;
			}
			$companyName = $this->database['ca']->getRowValue( "crm.cst", "name", "cst_id = '".(int)$customerId."'" );
		}

		return iconv( 'UTF-8', 'ISO-8859-1', (string)$companyName );
	}

	function getAccountName( $accountId ) {
		$name = $this->database['ca']->getRowValue( 'ca.accounts', 'account_username', "account_id='" . (int) $accountId . "'" );
		return iconv( 'UTF-8', 'ISO-8859-1', (string)$name );
	}

	function getRecipientEmail( $accountId, $recipientId ) {
		$email = $this->database['ca']->getRowValue( 'ca.contact_method', 'contact_method_value', "account_id='" . (int) $accountId . "' AND contact_method_type = 1 AND contact_method_id = '" . (int) $recipientId . "'" );

		return iconv( 'UTF-8', 'ISO-8859-1', (string)$email );
	}

	// TODO - these next 2 functions should also check the account_id
	function getRecipientName( $accountId, $recipientId ) { // From contact_method ( CA )
		$name = $this->database['ca']->getRowValue( "ca.contact_method", "name", "contact_method_type = 1 AND contact_method_id = '".(int)$recipientId."' AND account_id = '" . (int) $accountId . "'" );

		return iconv( 'UTF-8', 'ISO-8859-1', (string)$name );
	}

	function getDepartmentName( $accountId, $recipientId ) { // From contact_method ( CA )
		$departmentId = $this->database['ca']->getRowValue("ca.contact_method", "group_id", "contact_method_type = 1 AND contact_method_id = '".(int)$recipientId."' AND account_id = '" . (int) $accountId . "'" );
		$departmentName = $this->database['ca']->getRowValue("ca.contact_groups", "name", "group_id = '".(int)$departmentId."' AND account_id = '" . (int) $accountId . "'" );

		return iconv( 'UTF-8', 'ISO-8859-1', (string)$departmentName );
	}

	/**
	 * Function for getting the watermark
	*/
	function getWaterMark( $accountId, $customerId, $recipientId ) {
		// Convert values back to 'ISO-8859-1' as this is the PDF charset and not UTF-8, which is the UI charset
		$data = array(
			"company" => $this->getCompanyName( $accountId, $customerId ) // Root account id or customer id
			,"recipient" => $this->getRecipientName( $accountId, $recipientId )
			,"department" => $this->getDepartmentName( $accountId, $recipientId )
		);

		$GLOBALS['debug']->notice( "STAMP Watermark data fetched" );
		return $data;
	}
}
