<?php

  /**
   * @file cpe.class.php
   * Provides the CPE data associated with a product ID
   */

class Cpe {

	const DISTANCE_THRESHOLD = 1000;

	private static $db;

	public static function init( DB $db ) {
		self::$db = $db;
	}

	/*
	 * Determine if a valid CPE Match exists for a given product ID
	 *
	 * @param $productId Integer
	 *
	 * @return Boolean
	 */
	public static function existsMatch( $productId ) {

		$cpeExists = false;

		// Check the cpe_matches_manual table for matches,
		// If a row is found with NULL nvd_id, it means that NO match exist
		// This is a manual way to ignore the matches generated by the automated
		// process ( and stored in cpe_matches )

		$count = self::$db->select()
			->columns( array( 'os_soft_id' ) )
			->from( 'vuln_track.cpe_matches_manual' )
			->where( array( 'os_soft_id' => $productId
							  ,'nvd_id' => Exp::IS_NULL() ) )
			->rowCountIgnoreLimit();


		if ( $count > 0 ) {
			return false;
		}

		// Note - a CPE match existing means two things:
		// 1) There is at least one entry in our cpe_matches DB Table for the product ID
		// 2) The distance score determined by the matching algorithm is below a certain threshold

		// Check the table with automatically generated CPE matches
		// ( vuln_track.cpe_matches )

		$count = self::$db->select()
			->columns( array( 'os_soft_id' ) )
			->from( 'vuln_track.cpe_matches' )
			->join( 'vuln_track.cpe_items' )
			->using( array( 'nvd_id' ) )
			->where( array( 'os_soft_id' => $productId
							,'distance' => Exp::lt( self::DISTANCE_THRESHOLD ) ) )
			->rowCountIgnoreLimit();

		if ( $count > 0 ) {
			return true;
		}

		return false;
	}


	/*
	 * Function for fetching CPE data
	 *
	 * @param $filter Array [Mandatory]
	 *   Mutually exclusive filter:
	 * - os_soft_id,  OR
	 * - cpe_mapped_dtm : The dtm when the mapping was performed i.e. Secunia Product to CPE name mapping,  OR
	 * - cpe_mapped_days : Only look last X number of days when the mapping was done.
	 *
	 * @todo: write about the format
	 * @todo: Make sure that the values in the filters are valid !! e.g. dates etc
	 *
	 */
	public static function fetch( Array $filter ) {

		if ( empty( $filter ) ) {
			return false;
		}

		/*
		 * Initialize ( @todo: and validate ) Mutually Exculsive Filters
		 */

		$osSoftId = false;
		$cpeMappedDtm = false;
		$cpeMappedDays = false;

		if ( isset( $filter['os_soft_id'] ) ) {
			$osSoftId = $filter['os_soft_id'];
		} else if ( isset( $filter['cpe_mapped_dtm'] ) ) {
			$cpeMappedDtm = $filter['cpe_mapped_dtm'];
		} else if ( isset( $filter['cpe_mapped_days'] ) ) {
			$cpeMappedDays = $filter['cpe_mapped_days'];
		} else {
			return false;
		}

		/*
		 * Filters
		 */
		$where = array();

		if ( $osSoftId ) {
			$where['os_soft_id'] = $osSoftId;
		} else if ( $cpeMappedDtm ) {
			$where['cpe_matches_manual.modification_date'] = Exp::ge( $cpeMappedDtm );
		} else if ( $cpeMappedDays ) {
			$where['cpe_matches_manual.modification_date'] = Exp::gt( SqlFn::DATE_SUB( SqlFn::NOW(), $cpeMappedDays, 'DAY' ) );
		} else {
			return false;
		}

		// @todo: handle days

		/*
		 * Check Manual Entries
		 *  - A null NVD_ID means that no match is found
		 */

		$manualMatchesRows = self::$db->select()
			->columns( array( 'nvd_id'
							  ,'name'
							  ,'cpe_items.modification_date'
							  ,'os_soft_id'
							  ,'os_soft_name' ) )
			->from( 'vuln_track.cpe_matches_manual' )
			->join( 'vuln_track.os_soft' )
			->using( array( 'os_soft_id' ) )
			->leftJoin( 'vuln_track.cpe_items' )
			->using( array( 'nvd_id' ) )
			->where( $where )
			->exec();

		/*
		 * Create temporary table that holds the os_soft_ids and the min distance
		 * This is required for performance reasons.
		 */
		$matchesTable = '_matches';
		$tempTableQuery = 'CREATE TEMPORARY TABLE IF NOT EXISTS ' . $matchesTable
			. ' SELECT os_soft_id, min(distance) AS distance'
			. ' FROM vuln_track.cpe_matches WHERE distance < :threshold';

		$tempTableValues = array( ':threshold' => self::DISTANCE_THRESHOLD );

		if ( $osSoftId ) {
			$tempTableQuery .= ' AND os_soft_id = :os_soft_id';
			$tempTableValues[':os_soft_id'] = $osSoftId;
		}
		$tempTableQuery .= ' GROUP BY os_soft_id';
		self::$db->execRaw( $tempTableQuery, $tempTableValues );


		/*
		 * Check matches generated from the automated process:
		 *
		 * A CPE match existing means two things:
		 * 1. There is at least one entry in our cpe_matches DB Table for the product ID
		 * 2. The distance score determined by the matching algorithm is below a certain threshold
		 *
		 * Check the table with automatically generated CPE matches ( vuln_track.cpe_matches )
		 */

		$query = '
			SELECT
				os_soft_id
				,os_soft_name
				,cpe_items.nvd_id
				,cpe_items.name
				,cpe_matches.modification_date
			FROM
				vuln_track.os_soft
			JOIN
				vuln_track.cpe_matches
			USING
				( os_soft_id )
			JOIN '
			. $matchesTable . '
			USING
				( os_soft_id, distance )
			JOIN
				vuln_track.cpe_items
			USING
				( nvd_id )

			LEFT JOIN
				vuln_track.cpe_matches_manual
			USING
				( os_soft_id )
			WHERE
				distance < :threshold
			AND
				cpe_matches_manual.modification_date IS NULL -- Make sure that we do not have a manual entry
			';

		$values = array( ':threshold' => self::DISTANCE_THRESHOLD );
		if ( $osSoftId ) {
			$query .= ' AND os_soft_id = :os_soft_id';
			$values[':os_soft_id'] = $osSoftId;
		} else if ( $cpeMappedDtm ) {
			$values[':cpe_mapped_dtm'] = $cpeMappedDtm;
			$query .= ' AND cpe_matches.modification_date > :cpe_mapped_dtm';
		} else if ( $cpeMappedDays ) {
			$values[':cpe_mapped_days'] = $cpeMappedDays;
			$query .= ' AND cpe_matches.modification_date > DATE_SUB( NOW(), INTERVAL :cpe_mapped_days DAY )';
		} else {
			return false;
		}

		$automaticMatchesRows = self::$db->execRaw( $query, $values )->fetchAll();

		/*
		 * Compile Data
		 *
		 * @todo:
		 *
		 *   Maybe we can use a union instead of two separate queries and merging the data
		 *   in php
		 */
		$rows = array_merge( $manualMatchesRows, $automaticMatchesRows);
		$newCpes = array();
		foreach( $rows as $row ) {
			$id = $row['os_soft_id'];
			$osSoftName = $row['os_soft_name'];
			$cpeName = $row['name'];
			if ( !empty( $cpeName ) ) {
				if ( !isset( $newCpes[ $id ] ) ) {
					$newCpes[ $id ] = array( 'os_soft_name' => $osSoftName );
				}
				$newCpes[ $id ]['cpes'][] = array( 'nvd_id' => $row['nvd_id']
												   ,'name' => $row['name']
												   ,'modification_date' => $row['modification_date'] );
			}
		}
		return $newCpes;
	}

	/*
	 * @param options
	 * array(
	 *   rss => true
	 *   ,filter => array(
	 *                from_dtm => 'xxx' // OR
	 *                days => x
	 *              )
	 * )
	 * @return feed
	 */
	public static function generateFeed( $options ) {

		$rss = false; //isset( $options['rss'] ) ? $options['rss'] : false;
		$filter = isset( $options['filter'] ) ? $options['filter'] : array();

		/*
		 * Create Feed
		 */
		$writer = Feed::writer( $rss ? 'rss' : 'secunia_products_cpes' );
		$subWriter = $writer;
		/*
		if ( $rss ) {
			$writer->attribute( 'version', '2.0' );
			$subWriter = $writer->child( 'channel' );
		}
		*/
		// Asset List Name
		$subWriter->child( 'title', 'Flexera Products and CPE names mapping' );

		// Get Updates
		$mappings = self::fetch( $filter );

		self::writeFeedElements( $subWriter, $rss, $mappings );

		return $writer->asXML();
	}

	/*
	 * @param $writer Feed
	 * @param $rss Boolean @deprecated false
	 * @param $mappings Array
	 */
	private static function writeFeedElements( Feed $writer, $rss , Array $mappings ) {

		foreach( $mappings as $osSoftId => $data ) {
			$elementWriter = $writer->child( $rss ? 'item' : 'product' );
			$elementWriter->child( 'id', $osSoftId );
			$elementWriter->child( $rss ? 'title' : 'name', $data['os_soft_name'] );

			// $cpes->child( $rss ? 'pubDate' : 'release_date', $creationDate );
			// $cpes->child( $rss ? 'lastBuildDate' : 'last_change_datetime', $modificationDate );

			/*
			 * If RSS, we just write the cpe names to the description
			 * If XML, we create child elements for every cpe including details.
			 */
			if ( $rss ) {
				$cpeNames = array();
				foreach( $data['cpes'] as $cpe ) {
					$cpeNames[] = $cpe['name'];
				}
				$elementWriter->child( 'description', implode( "\n", $cpeNames ) );
			} else {
				$cpesWriter = $elementWriter->child( 'cpes' );
				self::writeFeedElement( $cpesWriter, $data['cpes'] );
			}
		}
	}

	/*
	 * @param $writer Feed
	 *
	 * @param $cpes Array
	 * The array should contain: id (nvd_id), name, modDate
	 *
	 * @todo: The modDate ( modification date ) isn't something that we want to include in
	 *  the xmls. The nvd_id should be sufficient. Furthermore, this date can confuse the
	 *  user. The modification date can also relate to the secunia products to cpe mapping
	 *  date. For now we are commenting it out.
	 *
	 */
	public static function writeFeedElement( Feed $writer, $cpes ) {
		foreach( $cpes as $cpe ) {
			$c = $writer->child( 'cpe' );
			$c->child( 'nvd_id', (int) $cpe['nvd_id'] );
			$c->child( 'name', $cpe['name'] );
			// $c->child( 'modified', $cpe['modification_date'] );
		}
	}

}