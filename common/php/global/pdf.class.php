<?php
/**
 * @file pdf.class.php
 * Provides global pdf functions. Requires FPDF 3rd party tool.
 */

/**
 * Class PDF
 * Extension of the external vendor FPDF class. This class contains Secunia's
 * custom additions and changes to the FPDF functionality.
 */
class PDF extends FPDF {

	public $precallback;        //called *before* a new page is added
	public $precallback_arg;    //argument passed to precallback
	public $callback;           //called when a new page is added (useful for headers)
	public $callback_arg;       //argument passed to callback
	public $filename;           //output document filename
	public $Substitutions;      //key/value pairs to substitute before output

	function __construct($orientation='P', $unit='mm', $size='A4') {
		if (!defined('DT_NONE')) {
			define('DT_NONE',     0x0000);
			define('DT_SORTABLE', 0x0001);
			define('DT_TOTAL',    0x0002);
			define('DT_HEADER',   0x0004);
			define('DT_CLICKABLE_TR', 0x0008);
			define('DT_STYLE_REPORT', 0x0010);
			define('DT_COLLAPSED',0x0020);
			define('DT_EXPANDED', 0x0040);
		}

		$this->IMAGE_PATH = ABSOLUTE_UI_PATH . 'gfx/pdf/';

		$this->watermark = new stdClass();
		parent::__construct($orientation, $unit, $size);

		$this->lMarginStart = $this->lMargin;
		$this->rMarginStart = $this->lMargin;
		//No substitutions by default
		$this->Substitutions=array();
		//Default alias for number of pages
		$this->AliasNbPages='{nb}';

		// Base URLs to use for graph creation
		$this->sBarChartBaseURL = GRAPH_SERVER_URL. "?&type=bar&style=report&bmargin=150&pdf_ln=1&pdf_w=180";
		$this->sBarChartBaseURL2 = GRAPH_SERVER_URL . "?&type=bar&width=800&height=250&style=nsi&show_values=1&bmargin=100&pdf_ln=1&pdf_w=185&show_title_line=0";

		$this->sPieChartLegendBaseURL = GRAPH_SERVER_URL . "?&type=basicpie&pdf_w=180&style=report&pdf_ln=1&height=640&pie_radius=220";
		$this->sPieChartBaseURL = GRAPH_SERVER_URL . "?&type=basicpie&pdf_w=90&style=report&width=900&height=600&show_legend=0&lmargin=240&pie_radius=210&font_size_title=28";

		$this->criticalityColors = "ff2400,ff9400,fffc00,affd0a,1bff1b"; // red, orange, yellow, light green, green
		$this->ticketTypeColors = "ff2400,1bff1b,fffc00,0077aa,"; // red/green/yellow/blue == open/hand/wait/irrel.
		$this->solutionStatusColors = "0077aa,ff2400,ff9400,fffc00"; // Blue,Red,Orange,yellow for unpatched, vendor patch, vendor workaround, partial fix

		// TODO - add color schemes for other graph types here as needed
	}

	/**
	 * Loads the watermark
	 *
	 * @param int $accountId
	 * @param int $customerId
	 * @param int $recipientId
	 */
	public function loadWatermark($accountId, $customerId, $recipientId)
	{
		// Load watermarking
		if ( !isset( $GLOBALS['recipient_id'] ) ) {
			$GLOBALS['recipient_id'] = 0;
		}
		$watermark = $GLOBALS['stamp']->getWaterMark( $accountId, $customerId, $recipientId );
		$this->watermark->companyName = $watermark['company'];
		$this->watermark->departmentName = $watermark['department'];
		$this->watermark->recipientName = $watermark['recipient'];
	}

	// Default line spacing, in mm, used for Write (controls the height of each
	// line of text that is output as well as the height of line breaks
	var $ls = 5.0;
	var $font = 'Arial';
	var $font_sz = 10;
	var $font_sz_h = 16;
	var $font_sz_h2 = 40;
	var $font_sz_hx = 24;
	var $max_w = 188;
	var $headline = 'Flexera';
	var $extra_headline = '';
	var $release = FALSE;
	var $gen_date = '';
	var $lMarginStart=0;
	var $rMarginStart=0;

	// FPDF does not have a GetLineWidth method, only a SetLineWidth method
	// we make one by making a SetLineWidth that calls the original and stores the linewith in a property,
	// whereby the GetLineWidth is trivial
	protected $lineWidth = 0.2;

	public function SetLineWidth($w) {
		parent::SetLineWidth($w);
		$this->lineWidth=$w;
	}

	protected function GetLineWidth() {
		return $this->lineWidth;
	}

	/**
	 * Sets the page header
	 *
	 * @see FPDF::Header
	 */
	public function Header()
	{
		$headline = $this->headline . ($this->extra_headline ? ' - ' . $this->extra_headline : '');

		// Set margins to original margins
		$rMargin = $this->rMargin;
		$lMargin = $this->lMargin;
		$this->SetLeftMargin($this->lMarginStart);
		$this->SetRightMargin($this->rMarginStart);
		$this->SetY(5);
		$this->SetTextColor(65,121,153);
		$this->SetDrawColor(65,121,153);
		$this->SetFont($this->font, 'B', $this->font_sz_h);

		// Truncate the headline if necessary (40 is the width of the Secunia logo)
		if ($this->GetStringWidth($headline) > $this->w-$this->lMargin-$this->rMargin-40)
		{
			while($this->GetStringWidth($headline) > $this->w-$this->lMargin-$this->rMargin-40)
				$headline = substr($headline, 0, strlen($headline)-1);

			// Replace the last 3 characters with "..." to indicate that it has been truncated
			$headline = preg_replace("/.{3}$/", "...", $headline);
		}

		$this->Cell($this->max_w, 10, $headline, 'B', 1);
		$this->Image($this->IMAGE_PATH . "new_logo.png", 163, 4, 40);
		$this->Ln();
		$this->SetRightMargin($rMargin);
		$this->SetLeftMargin($lMargin);
	}

	/**
	 * Adds the count of pages to the document.
	 */
	protected function addPageCountText()
	{
		$this->SetFont($this->font, 'I', $this->font_sz);
		if ($this->GetY() < 240) {
			$this->SetY(240);
		}
		$this->Cell(0,$this->font_sz,"{nb} pages",0,0,'C');
	}

	public function CoverSheet()
	{
		// Do Cover Sheet
		$this->AddPage($this->CurOrientation, $this->CurPageSize, false);        // Header on new page

		// Secunia logo
		$this->Image($this->IMAGE_PATH . "logo_big.png", 54, 40, 100);

		// Page Title in blue
		$this->SetTextColor(65,121,153);
		$this->SetFont($this->font, 'B', $this->font_sz_h2);
		$this->SetY(130);
		$this->MultiCell(0, 20, $this->headline, 0, 'C');

		// Display additional headline text
		if ($this->extra_headline)
		{
			$this->SetFont($this->font, 'B', $this->font_sz_hx);
			$this->MultiCell(0, 10, $this->extra_headline, 0, 'C');
		}

		// "Generated by Secunia\n<date>"
		$gen_date = $this->gen_date ? $this->gen_date : date("j F, Y");
		$this->SetTextColor(0,0,0);
		$this->SetFont($this->font, 'B', $this->font_sz_h);
		if ($this->GetY() < 190) {
			$this->SetY(190);
		}
		$this->MultiCell(0, 10, ( $this->release ? "Released" : "Generated" ) . " by Flexera\n$gen_date",0,'C');

		// "n Pages" displayed at the bottom of the page
		$this->addPageCountText();

		// Reset font and draw color
		$this->SetDrawColor(0,0,0);
		$this->SetFont($this->font, '', $this->font_sz);

		$this->AddPage($this->CurOrientation, $this->CurPageSize, true, false);  // Header on new, no Footer on prev

		// Don't include cover sheet in total number of pages
		$this->AliasNbPages("{nb-1}");
	}

	/**
	 * Gets the template text for the page footer, example: 'Page 1 of {nb}'
	 *
	 * @param int $pageno Current page number
	 * @return string
	 */
	protected function getFooterPageCountTemplate($pageno)
	{
		return 'Page '.$pageno. ' of {nb}';
	}

	/**
	 * Sets the page footer
	 *
	 * @see FPDF::Footer
	 */
	public function Footer()
	{
		$pageno = $this->PageNo();
		if ( preg_match("/{nb-([0-9]*)}/i", $this->AliasNbPages, $matches) ) {
			$pageno = $this->PageNo() - $matches[1];
		}

		// Set margins to original margins
		$rMargin = $this->rMargin;
		$lMargin = $this->lMargin;
		$this->SetLeftMargin($this->lMarginStart);
		$this->SetRightMargin($this->rMarginStart);
		$this->SetY(-10);
		$this->SetFont('Arial','I',7);
		$this->SetDrawColor(65,121,153);
		$watermark = ( $this->watermark->companyName != "" ? ', Licensed to: '.$this->watermark->companyName.( $this->watermark->departmentName != "" ? ' Department: '.$this->watermark->departmentName : "" ).( $this->watermark->recipientName != "" ? ' Recipient: '.$this->watermark->recipientName : "" )." Date: ". ( $this->gen_date ? $this->gen_date : date("j F, Y") ) : "" );
		$this->Cell(60,8,'Generated by Flexera'.$watermark,'T',0,'L' );

		if ( $watermark == "" ) {
			$gen_date = $this->gen_date ? $this->gen_date : date("j F, Y");
			$this->Cell(60,8,$gen_date,'T',0,'C');
		} else {
			$this->Cell(60,8," ",'T',0,'C'); // Add empty space.
		}
		$this->Cell($this->max_w-120,8, $this->getFooterPageCountTemplate($pageno),'T',0,'R');
		$this->SetRightMargin($rMargin);
		$this->SetLeftMargin($lMargin);
	}

	function WriteX($h, $str, $x, $link='')
	{
		$this->SetX($x);
		$this->Write($h, $str, $link);
	}

	function SetHeadline($h)
	{
		$this->headline = $h;
	}

	function SetExtraHeadline($h)
	{
		$this->extra_headline = $h;
	}

	function SetFilename($f)
	{
		$this->filename = $f;
	}

	function SubstituteString($old, $new) {
		$this->Substitutions[$old] = $new;
	}

	function SetGenDate($d) {
		$this->gen_date = $d;
	}

	function SetReleaseDate($d) {
		$this->gen_date = $d;
		$this->release = TRUE;
	}

	// Shortcuts to avoid using $this-> all over the application code
	public function PDFLn($i = 1, $ls = true)
	{
		if ($ls) {
			$this->Ln($this->ls * $i);
		} else {
			$this->Ln($i);
		}
	}

	public function PDFBeginInputBox($sHeader='', $width = 0)
	{
		if ($width == 0) {
			$width = $this->max_w - 15;
		}

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_top_left_corner.png");
		$this->PDFMoveX($w);

		$nHeaderWidth = 0;
		if ( strlen($sHeader) > 0 ) {
			$prev_x = $this->PDFGetX();
			$prev_y = $this->PDFGetY();
			$this->PDFSetY($prev_y - 1);
			$this->PDFSetX($prev_x);
			$nHeaderWidth = $this->PDFHTMLOut("  " . $sHeader . "   ");

			$this->PDFSetY($prev_y);
			$this->PDFSetX($prev_x + $nHeaderWidth);
		}

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_top_line.png", $width - $nHeaderWidth, $h);
		$this->PDFMoveX($w);

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_top_right_corner.png");
		$this->PDFLn($h, FALSE);

		// Save state of the input box for when PDFEndInputBox() is called
		$this->input_box_startx = $this->GetX();
		$this->input_box_starty = $this->GetY();
		$this->input_box_width = $width;
		$this->input_box_gw = $w;
		$this->input_box_old_lmargin = $this->lMargin;
		$this->input_box_old_rmargin = $this->rMargin;

		$this->SetLeftMargin($this->lMargin + $w);
		$this->SetRightMargin($this->rMargin + $w);

		$this->PDFAddPagePreCallback(array($this, 'PDFInputBoxPreCallback'));
		$this->PDFAddPageCallback(array($this, 'PDFInputBoxCallback'));
	}

	public function PDFEndInputBox()
	{


		$top_x = $this->input_box_startx;
		$width = $this->input_box_width;
		$height = $this->input_box_starty - $this->GetY();
		$w = $this->input_box_gw;

		// Restore previous left margin
		$this->SetLeftMargin($this->input_box_old_lmargin);
		$this->SetRightMargin($this->input_box_old_rmargin);

		$this->PDFSetX($this->lMargin);
		// Output left and right border images
		$startY = $this->input_box_starty;

		$this->PDFImage($this->IMAGE_PATH . "input_left_line.png", $w, $height, $top_x);
		$this->PDFImage($this->IMAGE_PATH . "input_right_line.png", $w, $height, $top_x + $width + $w);

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_bottom_left_corner.png");
		$this->PDFMoveX($w);

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_bottom_line.png", $width, $h);
		$this->PDFMoveX($w);

		list($w, $h) = $this->PDFImage($this->IMAGE_PATH . "input_bottom_right_corner.png");
		$this->PDFLn($h, FALSE);

		$this->PDFRemovePagePreCallback();
		$this->PDFRemovePageCallback();
	}

	public function PDFSetFont($font, $style, $size=12)
	{
		$this->SetFont($font, $style, $size);
	}

	public function PDFReset()
	{
		$this->PDFFont();
		$this->PDFSetFillColor(255, 255, 255);
		$this->PDFSetDrawColor(0, 0, 0);
		$this->PDFSetTextColor(0, 0, 0);
	}

	public function PDFFont($style = '')
	{
		$this->PDFSetFont($this->font, $style, $this->font_sz);
	}

	public function PDFSetX($x)
	{

		$this->SetX($x);
	}

	public function PDFMoveX($x)
	{
		$this->SetX($this->GetX() + $x);
	}

	public function PDFMoveY($y)
	{
		$this->SetY($this->GetY() + $y);
	}

	public function PDFSetY($y)
	{

		$this->SetY($y);
	}

	public function PDFWrite($str, $link='')
	{
		return $this->PDFWriteX($str, $this->GetX(), 'L', $link);
	}

	public function PDFWriteX($str, $x = 10, $align='L', $link='')
	{
		if ( $align == 'R' ) {
			$x = $x - PDFGetStringWidth($str);
		}
		$this->WriteX($this->ls, $str, $x, $link);
		return $this->PDFGetStringWidth($str);
	}

	public function PDFBulletX( $str, $x = 10, $w=2 )
	{
		$img = $this->IMAGE_PATH . "bulletSq.png";

		// Center bullet vertically on line
		list($img_w, $img_h) = $this->PDFImageDims($img, $w);
		$this->PDFImage($img, $w, 0, $x, $this->PDFGetY()+$img_w/2);

		// Write text one unit to the right of the bullet
		$this->PDFWriteX($str, $x+$w+1);
		$this->PDFLn();
	}

	public function PDFRedRect($x,$y,$w,$h,$bw=1) {
		$this->PDFSetFillColor(0, 0, 0);
		$this->Rect($x,$y,$w,$h,'DF');
		$this->PDFSetFillColor(200, 0, 0);
		$this->Rect($x+$bw,$y+$bw,$w-($bw*2),$h-($bw*2),'DF');
		$this->PDFReset();
	}

	public function PDFMultiCell($str, $x = 10, $w = 0, $border=0, $align='J', $fill=0)
	{

		$this->SetX($x);
		$this->MultiCell($w, $this->ls, $str, $border, $align, $fill);
	}

	public function PDFCell($w=0, $h=0, $txt='', $border=0, $ln=0, $align='L', $fill=0, $link='')
	{


		$this->Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
	}

	// By default, draws a line from the current cursor position up to the right margin
	public function PDFLine($x1=-1, $y1=-1, $x2=-1, $y2=-1)
	{

		$x1 = ($x1 == -1 ? $this->GetX() : $x1);
		$y1 = ($y1 == -1 ? $this->GetY() : $y1);
		$x2 = ($x2 == -1 ? $this->w - $this->rMargin : $x2);
		$y2 = ($y2 == -1 ? $this->GetY() : $y2);
		list($r,$g,$b) = $this->PDFHexToRGB("#417999");
		$this->PDFSetDrawColor($r,$g,$b);
		$this->Line($x1, $y2, $x2, $y2);

	}

	public function PDFImage($file, $width = 0, $height = 0, $x=-1, $y=-1, $type='png', $link='', $out=TRUE, $align='L')
	{


		if ( $align == 'C' ) {
			$x = ( $this->w / 2 ) - ( $width / 2 );
		} else {
			$x = ($x == -1 ? $this->GetX() : $x);
		}
		$y = ($y == -1 ? $this->GetY() : $y);
		return $this->Image($file, $x, $y, $width, $height, $type, '', $out);
	}

	public function PDFImageDims($file, $width = 0, $height = 0)
	{
		return $this->PDFImage($file,$width,$height,0,0,'png','',FALSE);
	}

	public function PDFAddPage($o = false, $h = true, $f = true)
	{


		if ($o) {
			// User specified orientation
			$this->AddPage($o, $this->CurPageSize, $h, $f);
		} else {
			// Use orientation specified in the constructor
			$this->AddPage($this->CurOrientation, $this->CurPageSize, $h, $f);  // Header on new, no Footer on prev
		}
	}

	public function PDFAddPagePreCallback($func)
	{

		$this->AddPagePreCallback($func);
	}

	public function PDFSetPreCallbackArg($arg)
	{

		$this->precallback_arg = $arg;
	}

	public function PDFRemovePagePreCallback()
	{

		$this->RemovePagePreCallback();
	}

	public function PDFAddPageCallback($func)
	{

		$this->AddPageCallback($func);
	}

	public function PDFSetCallbackArg($arg)
	{
		$this->callback_arg = $arg;
	}

	public function PDFRemovePageCallback()
	{

		$this->RemovePageCallback();
	}

	public function PDFHexToRGB($hex)
	{
		$hex = preg_replace("/#/","",$hex);
		if (!preg_match("/[0-9a-f{6}]/i", $hex)) {
			return array(0, 0, 0);
		}
		return array(hexdec(substr($hex, 0, 2)),
			hexdec(substr($hex, 2, 2)),
			hexdec(substr($hex, 4, 2)));
	}

	public function PDFSetTextColor($r=0, $g=0, $b=0)
	{

		$this->SetTextColor($r, $g, $b);
	}

	public function PDFSetDrawColor($r=0, $g=0, $b=0)
	{

		$this->SetDrawColor($r, $g, $b);
	}

	public function PDFSetFillColor($r=0, $g=0, $b=0)
	{

		$this->SetFillColor($r, $g, $b);
	}

	public function PDFSetFilename($filename)
	{

		$this->SetFilename($filename);
	}

	// Write out the elements of $array in the current font starting at $col.
	// Skip to the next line if there's not enough room to print a value
	public function PDFWriteArray($array, $col, $colstop = 35)
	{


		$curpos = $col;
		$max = $this->max_w;

		foreach($array as $i)
		{
			// Check to see if a new row is necessary
			$str_width = $this->PDFGetStringWidth($i);

			if ($curpos + $str_width > $max) {
				$this->PDFLn();
				$curpos = $col;
			}

			$this->PDFWriteX($i, $curpos);

			// Skip to the next column break
			$min = $curpos + $str_width + 10;	// At least this far
			while($curpos < $min)
				$curpos += $colstop;
		}
	}


	// Accessor methods
	public function PDFGetStringWidth($str)
	{
		return $this->GetStringWidth($str);
	}

	public function PDFGetStringHeight($s, $x=-1)
	{
		return $this->GetStringHeight($s, $x);
	}

	public function PDFRect($h,$style='')
	{
		$x = $this->GetX();
		$y = $this->GetY();
		$w = $this->w - $this->rMargin - $this->lMargin;
		$this->Rect($x,$y,$w,$h,$style);
	}

	public function PDFGetX()
	{
		return $this->GetX();
	}

	public function PDFGetY()
	{
		return $this->GetY();
	}

	public function PDFSetColorFromHTML($html)
	{
		if (preg_match("/<font color=\"#([0-9A-Fa-f]{6})/i", $html, $matches)) {
			list($r, $g, $b) = PDFHexToRGB($matches[1]);
		} else {
			$r = $g = $b = 0;
		}

		PDFSetTextColor($r, $g, $b);

		// Caller should call PDFSetTextColor(); to return to black
	}

	public function PDFHeadline($txt, $color = "#C0D8E7")
	{
		list($r,$g,$b) = $this->PDFHexToRGB($color);
		$this->PDFSetFillColor($r,$g,$b);
		$this->PDFFont('B');
		$this->PDFCell(0, $this->ls, $txt, 0, 1, 'L', 1);
		$this->PDFFont();
		$this->PDFSetFillColor(255,255,255);
	}

	public function PDFColWidth($w)
	{
		return $this->ColWidth($w);
	}

	public function PDFHTMLOut($html, $xl = 0, $xr = 0, $w = false)
	{
		// total width of displayed text
		$nWidth = 0;

		if ( $xl ) {
			// Set left margin in case text being output wraps to the next line
			$lMarginSave = $this->lMargin;
			$this->SetLeftMargin($xl);
		}
		if ( $xr ) {
			// Set right margin
			$rMarginSave = $this->rMargin;
			$this->SetRightMargin($this->w - $xr);
		}

		// A *very* basic function to convert HTML data into FPDF function calls
		$tagx = "(<[^>]*>)";
		$FontStyle = "";

		$found = false;
		$link = "";
		$text = "";
		if  (isset($html) && preg_match("/<a href=\"([^\"]*)/i", $html, $matches)  ) {
			$found = true;
			$link = $matches[1];
			preg_match("/>([^<]*)/i", $html, $_matches );
			$text = $_matches[1];
		}

		while (isset($html) && strlen($html) > 0 ) {
			// See if data begins with another tag
			if ( preg_match("/^$tagx/i", $html, $matches) && $found == false ) {
				// Data begins with an HTML tag
				$tag = $matches[1];

				// Remove the tag from the beginning of the content
				$html = substr($html, strlen($tag));

				// What should we do with the tag?
				switch(strtolower($tag))
				{
				case '<p>':
					// Output two line breaks for a <p> tag
					if ( !$w ) {
						$this->PDFLn();
						$this->PDFLn();
					}
					break;

				case '</tr>':
					// Output a line break at the end of a table row
					if ( !$w ) {
						$this->PDFLn();
					}
					break;

				case '<br>':
				case '<br />':
					if ( !$w ) {
						$this->PDFLn();
					}
					break;

				case '</td>':
					// Move forward a couple of spaces at the end of a table cell
					if ( !$w ) {
						$this->PDFMoveX(5);
					}
					break;

				case '<b>':
					// Make the output font bold
					if ( !preg_match("/B/i", $FontStyle) ) {
						$FontStyle = $FontStyle . "B";
					}
					break;

				case '</b>':
					$FontStyle = preg_replace("/B/i", "", $FontStyle);
					break;

				case '<i>':
					// Make the output font italicized
					if ( !preg_match("/I/i", $FontStyle) ) {
						$FontStyle = $FontStyle . "I";
					}
					break;

				case '</i>':
					$FontStyle = preg_replace("/I/i", "", $FontStyle);
					break;
				case '</a>':
					break;
				case '<hr>':
					$this->PDFLine();
					break;
				default:
					if ( preg_match("/<font color/i", $tag) ) {
						$this->PDFSetColorFromHTML($tag);
					} else if ( preg_match("#</font>#i", $tag) ) {
						$this->PDFSetTextColor();
					} else if ( preg_match("/<img src=\"([^\"]*)/i", $tag, $matches) ) {
						$image = $matches[1];

						// Check for explicit pdf_w tag for the image
						$pdf_w = preg_match("/pdf_w=([0-9]*)/i", $image, $matches) ? intval($matches[1]) : 0;
						$pdf_h = preg_match("/pdf_h=([0-9]*)/i", $image, $matches) ? intval($matches[1]) : 0;
						$pdf_ln = preg_match("/pdf_ln=1/i", $image, $matches) ? TRUE : FALSE;

						// Remove pdf_... args used to fine tune image behavior
						$image = preg_replace("/[?&]*pdf_[a-z]*=[0-9]*/", "", $image);

						if ( !$w ) {
							$y_start = $this->PDFGetY();
							$x_start = $this->PDFGetX();
							$this->PDFSetY($y_start + 0.5);

							list($img_w,$img_h,$x,$y) = $this->PDFImage($image, $pdf_w, $pdf_h, $x_start);
							if ( $pdf_ln ) {
								$this->PDFSetY($y+$img_h);
							} else {
								$this->PDFSetY($y_start);
							}

							// Advance X to the rhs of the image
							$this->PDFSetX($x_start + $img_w);
							$nWidth += $img_w;
						} else {
							list($img_w, $img_h) = $this->PDFImageDims($image, $pdf_w, $pdf_h);
							$nWidth += $img_w;
						}
					}
					break;
				}

				$this->PDFSetFont($this->font, $FontStyle, $this->font_sz);
			} else {
				if ( $found == false ) {
					// Output all data up to the next available tag, or everything that's left
					if ( ($p = strpos($html, "<")) != 0 ) {
						// Output until next available tag
						$content = substr($html, 0, $p);

						// Advance past content to the occurrence of the tag
						$html = substr($html, $p);
					} else {
						// No more tags, output everything
						$content = $html;
						$html = "";
					}

					// Newlines in HTML shouldn't cause breaks in PDF output, they must be called for explicitly
					$content = preg_replace("/[\r\n]/", "", $content);
					$content = html_entity_decode($content);
				} else {
					$html = "";
					$content = $text;
				}


				if ( $found == true ) {
					// Set link color, and underline
					$this->PDFSetTextColor(0,0,255);
					$this->PDFSetFont($this->font, 'U', $this->font_sz);
				}

				$nWidth += $this->PDFGetStringWidth($content);

				if ( !$w ) {
					$this->PDFWrite( $content, $link ); // HERE
				}

				if ( $found == true ) {
					// Unset link color and underline
					$this->PDFSetTextColor(0,0,0);
					$this->PDFSetFont($this->font, '', $this->font_sz);
				}

			}
		}

		if ( $xl ) {
			$this->SetLeftMargin($lMarginSave);
		}
		if ( $xr ) {
			$this->SetRightMargin($rMarginSave);
		}
		return $nWidth;
	}

	public function PDFSubstituteString($old, $new) {
		return $this->SubstituteString($old, $new);
	}

	public function PDFGetPage() {
		return $this->page;
	}

	/**
	 * Not used by CSI
	 */
	public function formatBBCode( $test, $type, $args)
	{
		$col1 = $args[0];
		while( preg_match("#\[(x?)url=(.*)\](.*)\[/x?url\]#Us", $text, $regs) ) {
			$text = str_replace($regs[0], $regs[2], $text);
		}

		while( preg_match("#(.*)\[code\][\n]*(.*)\[/code\]#Us", $text, $regs) ) {
			PDFMultiCell(strip_tags(preg_replace("/<br>/", "\n", $regs[1])), $col1);

			$regs[2] = trim($regs[2]);

			list($r,$g,$b) = $this->PDFHexToRGB("#cfcfcf");
			$this->PDFSetFillColor($r,$g,$b);
			list($r,$g,$b) = $this->PDFHexToRGB("#909090");
			$this->PDFSetDrawColor($r,$g,$b);
			$this->PDFSetFont('Courier');
			$this->PDFMultiCell(str_replace("\t", "   ", $regs[2]), $col1, 0, 1, 'J', 1);
			$this->PDFSetFont($this->font);
			$this->PDFSetDrawColor(0,0,0);
			$this->PDFSetFillColor(255,255,255);
			$text = str_replace($regs[0], $code, $text);
		}

		// And output any remaining text
		$this->PDFMultiCell(strip_tags(preg_replace("/<br>/", "\n", $text)), $col1);
	}

	public function PDFCalcColWidths(&$aCols)
	{
		// Calculate maximum width of the area we have to work with as the width of the entire
		// page, minus the margin on each side
		$nPDFPageWidth = $this->w - $this->lMargin - $this->rMargin;

		// Ann attempt at automatically calculating PDF column widths based on supplied HTML widths
		$aFixedCols = array();
		$aPercentageCols = array();
		$aProportionalCols = array();

		$nWidthUsed = 0;

		// Sum of all proportions (*, 2*, 2* would be 5, for instance)
		$nPropSum = 0;

		foreach ( array_keys($aCols) as $sCol ) {
			$aColRef = &$aCols[$sCol];

			if ( isset($aColRef['views']) && !in_array('pdf', $aColRef['views']) ) {
				continue;
			}

			// Allow absolute override ('pdf_width') by caller
			if ( !isset($aColRef['pdf_width']) || ( isset($aColRef['pdf_width']) && !$aColRef['pdf_width'] ) ) {
				if ( !isset($aColRef['width']) ) {
					// No width specified, treat as proportional column (*)
					$aColRef['width'] = "*";
				}

				if ( is_numeric($aColRef['width']) ) {
					// Fixed width, convert pixels to mm
					$nMMPerPixel = 0.26458333;
					$aColRef['pdf_width'] = $nMMPerPixel * $aColRef['width'];
					array_push($aFixedCols, $aColRef);
				} else if ( preg_match("/%$/", $aColRef['width']) ) {
					$aColRef['pdf_width'] = (intval($aColRef['width']) / 100) * $nPDFPageWidth;
					$aPercentageCols[$sCol] = &$aColRef;
				} else if ( preg_match("/\*$/", $aColRef['width']) ) {
					// Proportion is the int portion of the width (*, 1*, 2*,etc.)
					$aColRef['pdf_proportion'] = ($aColRef['width'] == "*" ? 1 : intval($aColRef['width']));
					$nPropSum += $aColRef['pdf_proportion'];
					// Width can only be calculated after all other columns have been handled
					$aProportionalCols[$sCol] = &$aColRef;
				}
			}

			if ( isset($aColRef['pdf_width']) ) {
				$aColRef['pdf_width'] = floor($aColRef['pdf_width']);
				$nWidthUsed += $aColRef['pdf_width'];
			}
		}

		// Calculate proportional columns now that we know the total requested width
		$nRemainingWidth = $nPDFPageWidth - $nWidthUsed;

		if ( !empty($aProportionalCols) ) {
			foreach ( array_keys($aProportionalCols) as $sCol ) {
				$aColRef = &$aProportionalCols[$sCol];
				$aColRef['pdf_width'] = $aColRef['pdf_proportion'] / $nPropSum * $nRemainingWidth;
				//$aColRef['pdf_width'] = floor($aColRef['pdf_width']);
				$nWidthUsed += $aColRef['pdf_width'];
			}
		}

		// Ensure column widths didn't exceed the maximum width allowed, +/- .1mm
		if ( $nWidthUsed > $nPDFPageWidth + 0.1 ) {
			// Columns exceed maximum possible width, find out by how much
			$nOver = $nWidthUsed - $nPDFPageWidth;

			// Steal from proportional columns or from fixed columns (never from percentage columns)
			$nReduceCols = count($aProportionalCols) + count($aFixedCols);
			$nReduce = ceil($nOver / $nReduceCols);

			foreach ( array_keys($aProportionalCols) as $sCol ) {
				$aColRef = &$aProportionalCols[$sCol];
				$aColRef['pdf_width'] -= $nReduce;
				$nWidthUsed -= $nReduce;
			}

			foreach ( array_keys($aFixedCols) as $sCol ) {
				$aColRef = &$aFixedCols[$sCol];
				$aColRef['pdf_width'] -= $nReduce;
				$nWidthUsed -= $nReduce;
			}
		}
	}

	public function buildPDFDataTable($aCols, $aRows, $iFlags)
	{
		// Output all column headings in bold
		$this->PDFSetFont($this->font, 'B', $this->font_sz);

		if ( ($iFlags & DT_STYLE_REPORT) && ($iFlags & DT_HEADER) ) {
			$this->PDFSetFillColor(65,121,153);
			$this->PDFSetTextColor(255,255,255);
		}

		// Calculate column widths
		$this->PDFCalcColWidths($aCols);

		// Display column headers. Unlike the HTML view, for the PDF view we still need
		// to iterate through the headers even if they're not being displayed, as this
		// is where the left and right boundaries for the columns are calculated.

		foreach ( array_keys($aCols) as $sCol ) {
			$aColRef = &$aCols[$sCol];

			if ( isset($aColRef['views']) && !in_array('pdf', $aColRef['views']) ) {
				continue;
			}

			if ( isset( $aColRef['detach'] ) && $aColRef['detach'] ) {
				continue;
			}

			// Use 'desc' if it is available, col key otherwise
			$sHeading = isset($aColRef['desc']) ? $aColRef['desc'] : $sCol;

			// Save position for column heading to line up row data properly
			$aColRef['pdf_x_l'] = $this->PDFGetX();
			$aColRef['pdf_x_r'] = $aColRef['pdf_x_l'] + $aColRef['pdf_width'];

			// Fill in the background first, before moving to the right of the cell
			if ( ($iFlags & DT_STYLE_REPORT) && ($iFlags & DT_HEADER) ) {
				$this->PDFCell($aColRef['pdf_width'], $this->ls, '', 0, 0, 'L', 1);
			}

			if ( isset( $aColRef['align'] ) && ( $aColRef['align'] == "right" ) ) {
				$nWidth = $this->PDFHTMLOut($sHeading, 0, 0, true);
				$this->PDFSetX($aColRef['pdf_x_r'] - $nWidth - 3);
			} else {
				$this->PDFSetX($aColRef['pdf_x_l']);
			}

			if ( $iFlags & DT_HEADER ) {
				$this->PDFHTMLOut($sHeading);
			}
			$this->PDFSetX($aColRef['pdf_x_r']);
		}

		// Back to non-bold font for table data
		$this->PDFSetFont($this->font, '', $this->font_sz);

		// Divider between headings and data
		if ( $iFlags & DT_HEADER ) {
			$this->PDFLn();
			if ( $iFlags & DT_STYLE_REPORT ) {
				$this->PDFSetFillColor(255,255,255);
				$this->PDFSetTextColor(0,0,0);
			} else {
				$this->PDFLine();
			}
		}

		// Output table data
		$bFill = FALSE;
		foreach ( array_keys($aRows) as $sRow ) {
			$aRow = &$aRows[$sRow];

			// Before displaying any data for the row, go through to check whether any single value
			// would cause a break to the next page. If one is found that would, force a page break
			// manually to ensure all data for the row is on the same page
			$nLines = $nMaxLines = 1;
			$nHeight = 0;
			foreach ( array_keys($aCols) as $sCol ) {
				$aColRef = &$aCols[$sCol];

				if ( isset($aColRef['views']) && !in_array('pdf', $aColRef['views']) ) {
					continue;
				}

				if ( isset( $aColRef['detach'] ) && $aColRef['detach'] ) {
					continue;
				}

				if ( !isset( $aRow[$sCol] ) ) {
					continue;
				}

				// Apply function to table cell data, if one is set
				if ( isset($aColRef['func']) ) {
					$aRow[$sCol] = $aColRef['func']($aRow[$sCol]);
				}

				// Calculate the width required to display the cell data
				$nWidth = $this->PDFHTMLOut(@$aRow[$sCol], $aColRef['pdf_x_l'], $aColRef['pdf_x_r'], true);

				// Estimate how many lines this value would occupy
				$nLines = ceil( ( $nWidth + 10 ) / ($aColRef['pdf_x_r'] - $aColRef['pdf_x_l'] ));
				//each <br> <br /> </tr> is making a linebreak and <p> is making 2 linebreaks

				$lineBreaks = substr_count( @$aRow[$sCol], "<br />" )+ substr_count( @$aRow[$sCol], "<br>" )+ substr_count( @$aRow[$sCol], "</tr>" )+ substr_count( @$aRow[$sCol], "<p>" ) *2;

				$nLines += $lineBreaks;

				$nMaxLines = ( $nLines > $nMaxLines ? $nLines : $nMaxLines );

				// Calculate the height to output the required number of lines
				$_nHeight = $this->ls * $nLines;

				// each <hr> also adds to the hight
				 $_nHeight += substr_count( @$aRow[$sCol], "<hr>" )* $this->GetLineWidth();
				if ( $_nHeight > $nHeight ) {
					$nHeight = $_nHeight;
				}
			}

			// Calculate the space needed to output the detached columns
			foreach ( array_keys($aCols) as $sCol ) {
				$aColRef = &$aCols[$sCol];
				// Do the same for detached columns
				if ( isset( $aColRef['detach'] ) && $aColRef['detach'] ) {
					$nWidth = $this->PDFHTMLOut( $sCol . " : " . $aRow[$sCol], $this->w - $this->rMargin, $this->lMargin, true );
					$lineBreaks = substr_count( $aRow[$sCol], "<br />" )+substr_count( $aRow[$sCol], "<br>" )+ substr_count( $aRow[$sCol], "</tr>" ) + substr_count( $aRow[$sCol], "<p>" ) *2 ;
					$nLines = ceil( ( $nWidth + 10 ) / ( $this->w - $this->rMargin - $this->lMargin ));
					$nLines += $lineBreaks;

					// Check if the content is larger than 1 page
					$requiredHeight = $this->ls * $nLines;
					if ( ( $requiredHeight + 2 ) > $this->PageBreakTrigger ) {
						// Do not add an extra page as it messes up the cursor position.
						// In this case, let the wrapper write the content even if it takes more than one page.
						$nMaxLines += 0;
					} else {
						$nMaxLines += $nLines;
					}

					$nHeight = $this->ls * $nMaxLines;
					$aColRef['height'] = $this->ls * $nLines;
				}
			}

			// Trigger a new page, since one of the columns is too big.
			$requiredPageLength = ( $this->PDFGetY() + $nHeight + 2 );
			if ( $requiredPageLength > $this->PageBreakTrigger ) {
				$this->PDFAddPage();
			}

			// Now display the row data
			// Starting Y position for displaying row data
			$y_start = $y_max = $this->PDFGetY();
			$nPage = $this->PDFGetPage();

			// Set fill color if DT_STYLE_REPORT is set
			if ( ($iFlags & DT_STYLE_REPORT) && ($iFlags & DT_HEADER) ) {
				$bFill ? $this->PDFSetFillColor(227,227,227) : $this->PDFSetFillColor(255,255,255);
			}

			/**
 			 * Initiallizing an array of detached columns
			 * Detached columns are not shown as regular columns instead they are shown separately after the whole row is printed
			 *
			 * e.g. consider the following table:
			 *
			 *		| SAID | Title | Comment |
			 *		|------|-------|---------|
			 *		| 1	   | abc   | test .. |
			 *
			 * 	If we want to make 'Comment' a detached column it would be displayed like this:
			 *		| SAID | Title       |
			 *		|------|-------------|
			 *		| 1	   | abc         |
			 *		| Comment: "test .." |
			 *		|--------------------|
			 *
			 */
			$detachedColumns = array();

			// Loop through columns
			foreach ( array_keys($aCols) as $sCol ) {
				$aColRef = &$aCols[$sCol];

				if ( !isset( $aRow[$sCol] ) ) {
					continue;
				}

				if ( isset($aColRef['views']) && !in_array('pdf', $aColRef['views']) ) {
					continue;
				}

				if ( isset( $aColRef['detach'] ) && $aColRef['detach'] ) {
					$detachedColumns[] = array(
											   'desc' => $aColRef['desc']
											   ,'text' => $aRow[$sCol]
											   ,'height' => isset( $aColRef['height'] ) ? $aColRef['height'] : 0
											   );
					continue;
				}

				// Always display row data on the same Y value
				$this->PDFSetY($y_start);

				// Fill in the background first, before moving to the right of the cell
				if ( ($iFlags & DT_STYLE_REPORT) && ($iFlags & DT_HEADER) ) {
					$this->PDFSetX($aColRef['pdf_x_l']);
					$this->PDFCell($aColRef['pdf_width'], $this->ls * $nMaxLines, '', 0, 0, 'L', 1);
				}

				if ( isset( $aColRef['align'] ) && ( $aColRef['align'] == "right" ) ) {
					// Get width of text to be displayed in the column and use it to calculate the
					// left x position where the data should be displayed
					$nWidth = $this->PDFHTMLOut($aRow[$sCol], $aColRef['pdf_x_l'], $aColRef['pdf_x_r'], true);

					// Calculate new left x position
					$x_left = $aColRef['pdf_x_r'] - $nWidth - 3;

					// If new left is beyond the beginning of the column, use columns left edge instead
					if ( $x_left < $aColRef['pdf_x_l'] ) {
						$x_left = $aColRef['pdf_x_l'];
					}
					$this->PDFSetX($x_left);
				} else {
					$this->PDFSetX($aColRef['pdf_x_l']);
				}

				$this->PDFHTMLOut(@$aRow[$sCol], $aColRef['pdf_x_l'], $aColRef['pdf_x_r'] );

				// Track max y value for data being displayed, used when the row is finished
				if ( $this->PDFGetY() > $y_max || $nPage != $this->PDFGetPage() ) {
					$y_max = $this->PDFGetY();
				}


				if ( !isset($aColRef['sum']) ) {
					$aColRef['sum'] = 0;
				}

				// Have to check individually if one of these is set, and if value is 1, set
				// the sum (we don't care which one is set, so can use elseif)
				if ( (isset($aColRef['auto_total']) && $aColRef['auto_total']) ||
					 (isset($aColRef['auto_average']) && $aColRef['auto_average'] ) ) {
					$aColRef['sum'] += intval(str_replace( ',','', strip_tags($aRow[$sCol] )));
				}
			}


 			// handle detached columns
			if ( !empty( $detachedColumns ) ) {
				$size = count( $detachedColumns );

				for( $i = 0; $i < $size; $i++ ) {
					$detachedColumn = $detachedColumns[$i];
					$this->PDFSetY( $y_max + $this->ls );
					$y_max += $detachedColumn['height'];

					$this->PDFSetFont($this->font, 'B', $this->font_sz);
					$this->PDFHTMLOut( $detachedColumn['desc'] . " : " );
					$this->PDFSetFont($this->font, '', $this->font_sz);
					$this->PDFHTMLOut( $detachedColumn['text'] );

				}
				// reset the array of detached columns
				$detachedColumns = array();
			}


			// Start next row at max y value for the current row
			$nPage = $this->PDFGetPage();
			$this->PDFSetY($y_max);
			$this->PDFLn();
			$bFill = !$bFill;
		}

		// Output "Total" row if DT_TOTAL flag was specified
		if ( $iFlags & DT_TOTAL ) {

			$this->PDFLine();

			foreach ( array_keys($aCols) as $sCol ) {
				$aColRef = &$aCols[$sCol];

				if ( isset($aColRef['views']) && !in_array('pdf', $aColRef['views']) ) {
					continue;
				}

				$sBuf = "";
				if ( isset($aColRef['auto_total']) ) {
					$sBuf = "<b>" . number_format($aColRef['sum']) . "</b>";
				} else if ( isset($aColRef['auto_average']) ) {
					$nAvg = count($aRows) ? round($aColRef['sum'] / count($aRows)) : "0";
					$sBuf = "<b>" . number_format($nAvg) . "</b>";
				}

				// Append 'label' if it was set for the column
				$sBuf .= isset($aColRef['label']) ? $aColRef['label'] : "";

				if ( !isset($sBuf) ) {
					$sBuf = "&nbsp;";
				}

				if ( isset($aColRef['align']) && $aColRef['align'] == "right" ) {
					$nWidth = $this->PDFHTMLOut($sBuf, $aColRef['pdf_x_l'], $aColRef['pdf_x_r'], true);
					$x_left = $aColRef['pdf_x_r'] - $nWidth - 3;

					if ( $x_left < $aColRef['pdf_x_l'] ) {
						$x_left = $aColRef['pdf_x_l'];
					}
					$this->PDFSetX($x_left);
				} else {
					$this->PDFSetX($aColRef['pdf_x_l']);
				}

				$this->PDFHTMLOut($sBuf, $aColRef['pdf_x_l'], $aColRef['pdf_x_r']);
			}
			$this->PDFLn();
		}
	}

	protected function generateGraphArray( $data, $type ) {
		$return = array(
			"graph" => array()
		);
		switch ( $type ) {
			case "mm":
				for ( $i = 0; $i < count( $data ); $i++ ) {
					$timestamp = strtotime( $data[$i]['vuln_create_date'] );
					$date = date( "M 'y", $timestamp );
					if ( isset($return['graph'][$date]) ) {
						$return['graph'][$date]++;
					} else {
						$return['graph'][$date] = 1;
					}
					// $return['graph'][$date]++;
				}
				break;
			case "impact":
				for ( $i = 0; $i < count( $data ); $i++ ) {
					$impact = $GLOBALS['advisory']->advisoryImpact( $data[$i]['vuln_id'] );
					for ( $j = 0; $j < count( $impact ); $j++ ) {
						$name = $impact[$j]['impact_type_name'];
						if ( isset($return['graph'][$name]) ) {
							$return['graph'][$name]++;
						} else {
							$return['graph'][$name] = 1;
						}
						// $return['graph'][$impact[$j]['impact_type_name']]++;
					}
				}
				break;
			case "where":
				for ( $i = 0; $i < count( $data ); $i++ ) {
					$where = $GLOBALS['advisory']->advisoryWhere( $data[$i]['vuln_id'] );
					for ( $j = 0; $j < count( $where ); $j++ ) {
						$name = $where['where_type_name'];
						if ( isset($return['graph'][$name]) ) {
							$return['graph'][$name]++;
						} else {
							$return['graph'][$name] = 1;
						}
						// $return['graph'][$where['where_type_name']]++;
					}
				}
				break;
			case "criticality":
				for ( $i = 0; $i < count( $data ); $i++ ) {
					$criticality = $GLOBALS['misc']->fetchCriticalityText( $data[$i]['vuln_critical_boolean'] );
					if ( isset($return['graph'][$criticality]) ) {
						$return['graph'][$criticality]++;
					} else {
						$return['graph'][$criticality] = 1;
					}
					// $return['graph'][$criticality]++;
				}
				break;
			case "ss":
				for ( $i = 0; $i < count( $data ); $i++ ) {
					$status = $GLOBALS['advisory']->advisorySolutionStatusText( $data[$i]['vuln_solution_status'] );
					if ( isset($return['graph'][$status]) ) {
						$return['graph'][$status]++;
					} else {
						$return['graph'][$status] = 1;
					}
					// $return['graph'][$status]++;
				}
				break;
		}
		$i = 0;
		$data = "";
		$graph = "";
		foreach ( $return['graph'] as $key => $value ) {
			if ( $i != 0 ) {
				$graph .= ",";
				$data .= ",";
			}
			$graph .= urlencode( $key );
			$data .= $value;
			$i++;
		}
		return array( 'graph' => $graph, 'data' => $data );
	}

	/**
	 * protected function for generating month by month data.
	*/
	protected function generateMonthByMonthData( $data ) {
		return $this->generateGraphArray( $data, 'mm' );
	}

	protected function generateImpactData( $data ) {
		return $this->generateGraphArray( $data, 'impact' );
	}

	protected function generateWhereData( $data ) {
		return $this->generateGraphArray( $data, 'where' );
	}

	protected function generateCriticalityData( $data ) {
		return $this->generateGraphArray( $data, 'criticality' );
	}

	protected function generateSolutionData( $data ) {
		return $this->generateGraphArray( $data, 'ss' );
	}

	/**
	 * protected function for fetching historic advisories graph charts.
	 * @param type
	 *	String graph type. mm - Month by Month, impact - Impact types, where - Where types
	 * @return
	 *	String URL to the graph chart.
	*/
	protected function fetchGraphImage( $type, $data ) {
		$data = $data['data'];
		$imageUrl = "";
		switch ( $type ) {
			case "mm":
				$height = 250;
				$width = 800;
				$margin = 100;
				$type = "stackedbar";
				$title = urlencode( "Advisories Month by Month (Based on ".count( $data )." advisories)" ); // Advisories Month by Month (Based on 77 advisories)
				$temp = $this->generateMonthByMonthData( $data );
				$labels = $temp['graph'];
				$graph = $temp['data'];
				$imageUrl = "?type=".$type."&width=".$width."&height=".$height."&style=nsi&bmargin=".$margin."&pdf_ln=1&pdf_w=175&show_title_line=0&d[xlabels]=".$labels."&d[a]=".$graph."&title=".$title;
				break;
			case "impact":
				$temp = $this->generateImpactData( $data );
				$type = "basicpie";
				$title = "Impact (Based on ".count( $data )." advisories)";
				$labels = $temp['graph'];
				$graph = $temp['data'];
				break;
			case "where":
				$temp = $this->generateWhereData( $data );
				$type = "basicpie";
				$title = "Where (Based on ".count( $data )." advisories)";
				$labels = $temp['graph'];
				$graph = $temp['data'];
				break;
			case "criticality":
				$temp = $this->generateCriticalityData( $data );
				$type = "basicpie";
				$title = "Criticality (Based on ".count( $data )." advisories)";
				$labels = $temp['graph'];
				$graph = $temp['data'];
				break;
			case "ss":
				$temp = $this->generateSolutionData( $data );
				$type = "basicpie";
				$title = "Solution Status (Based on ".count( $data )." advisories)";
				$labels = $temp['graph'];
				$graph = $temp['data'];
				break;
			default:
				break;
		}
		if ( $imageUrl == "" ) {
			$imageUrl = "?type=".$type."&style=nsi&pdf_ln=1&pdf_w=120&show_title_line=0&d[xlabels]=".$labels."&d[a]=".$graph."&title=".urlencode( $title );
		}
		return $image_url = GRAPH_SERVER_URL.$imageUrl;
	}

	// This is not yet hooked up - will complete when necessary.
	protected function AddPagePreCallback($func)
	{
		if(is_callable($func))
			$this->precallback = $func;
		else
			$this->Error('Function "' . $func . '" is not callable');
	}

	protected function AddPageCallback($func)
	{
		if(is_callable($func))
			$this->callback = $func;
		else
			$this->Error('Function "' . $func . '" is not callable');
	}

	protected function RemovePageCallback()
	{
		$this->callback = null;
	}

	protected function RemovePagePreCallback()
	{
		$this->precallback = null;
	}

	// Given $w (as a percentage), returns the width of a column that will occupy the
	// requested percentage of the current page size (taking margins into consideration)
	protected function ColWidth($w)
	{
		return ($this->w - $this->lMargin - $this->rMargin) * (preg_replace("/%/", "", $w) / 100);
	}

	// Returns the number of lines that will be output by the next call to PDFWrite
	// or PDFWriteX (given $x)
	protected function GetStringHeight($s, $x=-1)
	{
		$sw=$this->GetStringWidth($s . str_repeat("0", 10));
		$l=($x==-1 ? $this->lMargin : $x);
		$r=$this->w-$this->rMargin;
		$w=intval($r-$l);

		$nl=0;
		while($sw > 0)
		{
			$sw -= $w;
			$nl++;
		}
		return $nl;
	}

	/**
	 * Close the previous page. Pages are left open and closed by the new page
	 *
	 * @param bool $footer
	 */
	public function closePreviousPage($footer)
	{
		// See if there's a precallback function defined for AddPage
		if(is_callable($this->precallback)) {
			if ($this->precallback_arg) {
				call_user_func_array($this->precallback, $this->precallback_arg);
			} else {
				call_user_func($this->precallback);
			}
		}

		// Page footer
		if($footer) {
			$this->InFooter = true;
			$this->Footer();
			$this->InFooter = false;
		}
		// Close page
		$this->_endpage();
	}

	protected function isPreviousPageOpen()
	{
		return ($this->page > 0);
	}

	/**
	 * Adds the $header and $footer params and fires a custom callback function
	 *
	 * @see FPDF::AddPage
	 */
	public function AddPage($orientation='', $size='', $header = true, $footer = true)
	{
		// Start a new page
		if($this->state==0)
			$this->Open();
		$family = $this->FontFamily;
		$style = $this->FontStyle.($this->underline ? 'U' : '');
		$fontsize = $this->FontSizePt;
		$lw = $this->LineWidth;
		$dc = $this->DrawColor;
		$fc = $this->FillColor;
		$tc = $this->TextColor;
		$cf = $this->ColorFlag;
		if($this->isPreviousPageOpen()) {
			$this->closePreviousPage($footer);
		}
		// Start new page
		$this->_beginpage($orientation,$size);
		// Set line cap style to square
		$this->_out('2 J');
		// Set line width
		$this->LineWidth = $lw;
		$this->_out(sprintf('%.2F w',$lw*$this->k));
		// Set font
		if($family)
			$this->SetFont($family,$style,$fontsize);
		// Set colors
		$this->DrawColor = $dc;
		if($dc!='0 G')
			$this->_out($dc);
		$this->FillColor = $fc;
		if($fc!='0 g')
			$this->_out($fc);
		$this->TextColor = $tc;
		$this->ColorFlag = $cf;
		// Page header
		if($header) {
			$this->InHeader = true;
			$this->Header();
			$this->InHeader = false;
		}
		// Restore line width
		if($this->LineWidth!=$lw)
		{
			$this->LineWidth = $lw;
			$this->_out(sprintf('%.2F w',$lw*$this->k));
		}
		// Restore font
		if($family)
			$this->SetFont($family,$style,$fontsize);
		// Restore colors
		if($this->DrawColor!=$dc)
		{
			$this->DrawColor = $dc;
			$this->_out($dc);
		}
		if($this->FillColor!=$fc)
		{
			$this->FillColor = $fc;
			$this->_out($fc);
		}
		$this->TextColor = $tc;
		$this->ColorFlag = $cf;

		// See if there's a callback function defined for AddPage
		if(is_callable($this->callback)) {
			if ($this->callback_arg) {
				call_user_func_array($this->callback, $this->callback_arg);
			} else {
				call_user_func($this->callback);
			}
		}
	}

	/**
	 * Adds $out param for returning Image information and alters positioning
	 * vars when an image would be split between pages (required for cell borders)
	 *
	 * @see FPDF::Image
	 */
	public function Image($file, $x=null, $y=null, $w=0, $h=0, $type='', $link='', $out=true)
	{
		// Put an image on the page
		if(!isset($this->images[$file]))
		{
			// First use of this image, get info
			if($type=='')
			{
				$pos = strrpos($file,'.');
				if(!$pos)
					$this->Error('Image file has no extension and no type was specified: '.$file);
				$type = substr($file,$pos+1);
			}
			$type = strtolower($type);
			if($type=='jpeg')
				$type = 'jpg';
			$mtd = '_parse'.$type;
			if(!method_exists($this,$mtd))
				$this->Error('Unsupported image type: '.$type);
			$info = $this->$mtd($file);
			$info['i'] = count($this->images)+1;
			$this->images[$file] = $info;
		}
		else
			$info = $this->images[$file];


		if($w==0 && $h==0) {
		//Put image at 72 dpi
		$w=$info['w']/$this->k;
		$h=$info['h']/$this->k;
		}

		if($w==0)
			$w = $h*$info['w']/$info['h'];
		if($h==0)
			$h = $w*$info['h']/$info['w'];

		// GJN - Allow retrieval of image dimensions without displaying the image
		if(!$out)
			return array($w,$h);

		// Flowing mode
		if($y===null)
		{
			if($this->y+$h>$this->PageBreakTrigger && !$this->InHeader && !$this->InFooter && $this->AcceptPageBreak())
			{
				// Automatic page break
				$x2 = $this->x;
				$this->AddPage($this->CurOrientation,$this->CurPageSize);
				$this->x = $x2;
			}
			$y = $this->y;
			$this->y += $h;
		}

		if($x===null)
			$x = $this->x;


		// GJN - Automatic page break code for Image, copied from Cell
		// (This should probably be put into its own function)

		// Is there room on the page to print out the image?
		if($this->y+$h>$this->PageBreakTrigger && !$this->InFooter && !$this->InHeader && $this->AcceptPageBreak())
		{
			//Automatic page break
			$x_tmp=$this->x;
			$ws=$this->ws;
			if($ws>0)
			{
				$this->ws=0;
				$this->_out('0 Tw');
			}
			$this->AddPage($this->CurOrientation, $this->CurPageSize);
			$this->x=$x_tmp;

			// $y is no longer valid for placing the image, move to the top of the
			// page then down by 20
			$y=$this->tMargin + 20;
			$this->y=$y;

			if($ws>0)
			{
				$this->ws=$ws;
				$this->_out(sprintf('%.3f Tw',$ws*$k));
			}
		}

		$this->_out(sprintf('q %.2F 0 0 %.2F %.2F %.2F cm /I%d Do Q',$w*$this->k,$h*$this->k,$x*$this->k,($this->h-($y+$h))*$this->k,$info['i']));
		if($link)
			$this->Link($x,$y,$w,$h,$link);

		return array($w,$h,$x,$y);
	}

	/**
	 * Allows specifying a Custom name
	 *
	 * @see FPDF::Output()
	 */
	public function Output($name = '', $dest = '')
	{
		if($dest=='') {
			if($name=='') {
				$name=($this->filename ? $this->filename : 'doc.pdf');
				$dest = 'I';
			}
		}
		return parent::Output($name, $dest);
	}

	/**
	 * Handles custom template var substitution matching.
	 *
	 * @see FPDF::_putpages()
	 */
	public function _putpages()
	{
		$nb = $this->page;
		if(!empty($this->AliasNbPages))
		{
			$total_pages=$this->page;

			if(preg_match("/{nb-([0-9]*)}/i", $this->AliasNbPages, $matches)) {
				$total_pages -= $matches[1];
			}
			$this->Substitutions['{nb}'] = $total_pages;

			// Replace template vars in pages
			$replaceKeys = array_keys($this->Substitutions);
			for($n=1;$n<=$nb;$n++) {
				$this->pages[$n]=str_replace($replaceKeys, $this->Substitutions,$this->pages[$n]);
			}
		}
		if($this->DefOrientation=='P')
		{
			$wPt = $this->DefPageSize[0]*$this->k;
			$hPt = $this->DefPageSize[1]*$this->k;
		}
		else
		{
			$wPt = $this->DefPageSize[1]*$this->k;
			$hPt = $this->DefPageSize[0]*$this->k;
		}
		$filter = ($this->compress) ? '/Filter /FlateDecode ' : '';
		for($n=1;$n<=$nb;$n++)
		{
			// Page
			$this->_newobj();
			$this->_out('<</Type /Page');
			$this->_out('/Parent 1 0 R');
			if(isset($this->PageSizes[$n]))
				$this->_out(sprintf('/MediaBox [0 0 %.2F %.2F]',$this->PageSizes[$n][0],$this->PageSizes[$n][1]));
			$this->_out('/Resources 2 0 R');
			if(isset($this->PageLinks[$n]))
			{
				// Links
				$annots = '/Annots [';
				foreach($this->PageLinks[$n] as $pl)
				{
					$rect = sprintf('%.2F %.2F %.2F %.2F',$pl[0],$pl[1],$pl[0]+$pl[2],$pl[1]-$pl[3]);
					$annots .= '<</Type /Annot /Subtype /Link /Rect ['.$rect.'] /Border [0 0 0] ';
					if(is_string($pl[4]))
						$annots .= '/A <</S /URI /URI '.$this->_textstring($pl[4]).'>>>>';
					else
					{
						$l = $this->links[$pl[4]];
						$h = isset($this->PageSizes[$l[0]]) ? $this->PageSizes[$l[0]][1] : $hPt;
						$annots .= sprintf('/Dest [%d 0 R /XYZ 0 %.2F null]>>',1+2*$l[0],$h-$l[1]*$this->k);
					}
				}
				$this->_out($annots.']');
			}
			if($this->PDFVersion>'1.3')
				$this->_out('/Group <</Type /Group /S /Transparency /CS /DeviceRGB>>');
			$this->_out('/Contents '.($this->n+1).' 0 R>>');
			$this->_out('endobj');
			// Page content
			$this->pages[$n] = ($this->compress) ? gzcompress($this->pages[$n]) : $this->pages[$n];

			$this->_newobj();
			$this->_out('<<'.$filter.'/Length '.strlen($this->pages[$n]).'>>');
			$this->_putstream($this->pages[$n]);
			unset($this->pages[$n]);
			$this->_out('endobj');
		}
		// Pages root
		$this->offsets[1] = strlen($this->buffer);
		$this->_out('1 0 obj');
		$this->_out('<</Type /Pages');
		$kids = '/Kids [';
		for($i=0;$i<$nb;$i++)
			$kids .= (3+2*$i).' 0 R ';
		$this->_out($kids.']');
		$this->_out('/Count '.$nb);
		$this->_out(sprintf('/MediaBox [0 0 %.2F %.2F]',$wPt,$hPt));
		$this->_out('>>');
		$this->_out('endobj');
	}

	/**
	 * Removes call to get_magic_quotes_runtime() from FPDF::_dochecks()
	 *
	 * @see FPDF::_dochecks()
	 */
	public function _dochecks()
	{
		// Check availability of %F
		if(sprintf('%.1F',1.0)!='1.0')
			$this->Error('This version of PHP is not supported');
		// Check mbstring overloading
		if(ini_get('mbstring.func_overload') & 2)
			$this->Error('mbstring overloading must be disabled');
	}

	// The following two callback functions are registered in PDFBegin/EndInputBox
	// and are called if the content wraps to a new page.

	// Called by AddPage before the new page is added, used to add the side rails for the input box
	protected function PDFInputBoxPreCallback()
	{
		// New page, output left and right borders
		$top_x = $this->input_box_startx;
		$width = $this->input_box_width;
		$startY = $this->input_box_starty;
		$height = $this->GetY() - $startY;
		$w = $this->input_box_gw;

		// Output left and right border images
		$this->SetAutoPageBreak(FALSE);
		$this->PDFImage( $this->IMAGE_PATH . "input_left_line.png", $w, $height, $top_x, $startY);
		$this->PDFImage( $this->IMAGE_PATH . "input_right_line.png", $w, $height, $top_x + $width + $w, $startY);
		$this->SetAutoPageBreak(TRUE, 2*$this->tMargin);
	}

	// Called after the new page has been added
	protected function PDFInputBoxCallback()
	{
		// New page, reset where the top of the input box rails should be drawn
		$this->input_box_starty = $this->GetY();
	}

	function makeTitlePage( $reportTitle, $genDate, $mainTimeInterval ) {

		if ( !$reportTitle ) {
			$reportTitle = "Flexera Custom Report";
		}

		// Format the genDate the way we want to display it.
		$genTimestamp = strtotime( $genDate );
		$genDisplay = date( "M j Y, G:i", $genTimestamp ) . " GMT"; // Times are UTC, so note them as GMT

		// Create a new PDF object
		$this->SetAuthor("Flexera");
		$this->SetCreator('FPDF 1.7');
		$this->SetHeadline( $reportTitle . "\n" . $mainTimeInterval );
		$this->SetGenDate( $genDisplay );
		$this->AliasNbPages("{nb-1}");
		$this->CoverSheet();
	}

	function getCompletedPdfFile() {
		return $this->Output("", 'S');
	}

	function saveCompletedPdfFile( $path ) {
		if ( !$path ) {
			$GLOBALS['debug']->error( "saveCompletedPdfFile() - path can not be empty" );
			return false;
		}
		return $this->Output($path, 'F');
	}

	public function getSubstitutions()
	{
		return $this->Substitutions;
	}
}
