<?php
/**
 * @file modules.class.php
 * Provides functionality for using application modules.
 */

/**
 * This class provides generic functionality for managing application modules
 * in a consistent matter that allows for customization at customer and user level
 * Note: if this class will be used for another project, it will have to be inherted
 * from and a new src column should be added to diferentiate between projects
 */
class Modules {

	/**
	 * The database handler must be an instance of the DB class
	 * It can however connect to any database that has the 3 tables structure required by the class
	 */
	protected $commonDb;

	/**
	 * Modules management is first of all a customer dependency so this member is mandatory
	 */
	protected $cstId;

	/**
	 * Except for crons this class will mostly be used with an accountId
	 */
	protected $accountId;

	/**
	 * Constant used to define the Account part of the modulesAccess array
	 */
	const ACCOUNT = 'ACCOUNT';
	/**
	 * Constant used to define the Customer part of the modulesAccess array
	 */
	const CUSTOMER = 'CUSTOMER';

	/**
	 * SCCM Plugin constant from the database
	 */
	const CSI_SCCM_PLUGIN = 3;

	/**
	 * ZERO DAY Module constant from the database
	 */
	const CSI_ZERO_DAY = 5;

	/**
	* Threat Intelligence Module constant from the database
	*/
	const CSI_THREAT_BASIC = 6;
	
	/**
	 * VPM Module constant from the database
	 */
	const CSI_VPM = 8;

	/**
	 * SVM - New UI
	 */
	const CSI_NEW_UI = 9;
    
	/**
	 * This is the main element in the class as it indicates where the
	 * customer or account has access to
	 */
	protected $modulesAccess = array();

	protected $cache;

	protected function setupCacheForModules() {
		// Set the CACHE object
		if ( class_exists( 'CACHE' ) ) {
			$this->cache = CACHE::getSelfInstance();
		}
	}


	protected function __construct() {
		$this->setupCacheForModules();
	}

	/**
	 * Create a module access rights array for a specified account
	 */
	protected function buildModulesAccessArrayForAccount( $idAccount ) {
		$cache = null;
		if (class_exists('CACHE')) {
			$cache = CACHE::getSelfInstance();
		}

		if ($cache && $customerID  = $cache->get('customer_id_'.$idAccount)) {
			if (empty($customerID)) {
				return false;
			}
			$this->cstId = $customerID;
		} else {
			$cstId = $this->commonDb->select()
				->columns(array('cst_id'))
				->from('accounts')
				->where(array('account_id' => $idAccount))
				->exec();

			if (empty($cstId)) {
				return false;
			}

			$this->cstId = $cstId[0]['cst_id'];
		}
		$this->accountId = (int)$idAccount;

		//The order of the following two statements is important
		$this->buildModulesAccessArrayForCustomer( $this->cstId );

/*
		//First fetch the cstId associated with the account
		$users = new USERS();
		$user = $users->getDetails( $idAccount );

		if ( empty( $user->cst_id ) ) {
			return false;
		}

		$this->cstId = $user->cst_id;
		$this->accountId = (int)$idAccount;

		//The order of the following two statements is important
		$this->buildModulesAccessArrayForCustomer( $user->cst_id );
		$this->modulesAccess[self::ACCOUNT] = $this->getAccountModules();
*/
	}

	/**
	 * Create a module access rights array for a specified customer
	 * @param int $cstId the id of the customer to build the access array for
	 */
	protected function buildModulesAccessArrayForCustomer( $cstId ) {
		$this->cstId = (int)$cstId;
		$this->modulesAccess[self::CUSTOMER] = $this->getCustomerModules();
		$this->modulesAccess[self::ACCOUNT] = array();
	}

	/**
	 * Get the module access rights array for the account
	 * @deprecated
	 * @return array
	 */
	private function getAccountModules() {
		$modules = $this->commonDb->select()
			->columns( array(
				'id'
				,'hasAccess' => SqlFn::IFF(
					 SqlFn::IS_NULL( Column::tableColumn( 'modules_accounts', 'account_id' ) )
					,0
					,1
				)
			) )
			->from( 'modules' )
			->leftJoin( 'modules_accounts' )
			->on( array(
				'modules_accounts.module_id' => 'modules.id'
				,'modules_accounts.account_id' => $this->accountId
			) )
			->exec();

		$arr = array();
		foreach ( $modules as $module ) {
			$arr[(int)$module['id']] = $module['hasAccess'] === '1' ? true : false;
		}

		return $arr;
	}

	/**
	 * Get the module access rights array for the account
	 * @return array
	 */
	private function getCustomerModules() {
		$cache = null;
		if (class_exists('CACHE')) {
			$cache = CACHE::getSelfInstance();
		}

		if ($cache && $customerModules = $cache->get('customer_modules_' . $this->cstId)) {
			$arr = $customerModules;
		} else {
			$modules = $this->commonDb->select()
				->columns(array(
					'id'
				, 'hasAccess' => SqlFn::IFF(
						SqlFn::IS_NULL(Column::tableColumn('modules_customers', 'cst_id'))
						, 0
						, 1
					)
				))
				->from('modules')
				->leftJoin('modules_customers')
				->on(array(
					'modules_customers.module_id' => 'modules.id'
					,'modules_customers.cst_id' => $this->cstId
				))
				->exec();


			$arr = array();
			foreach ($modules as $module) {
				$arr[(int)$module['id']] = (int)$module['hasAccess'] === 1 ? true : false;
			}
		}

		return $arr;
	}

	/**
	 * Checks if the account has a specified right
	 * @param int $const, a global constant representing the id of the module to check
	 * @return boolean or null if the module is not found
	 */
	public function accountHasModule( $const ) {
		return $this->checkModule( $const, self::ACCOUNT );
	}

	/**
	 * Checks if the customer has a specified right
	 * @param int $const, a global constant representing the id of the module to check
	 * @return boolean or null if the module is not found
	 */
	public function customerHasModule( $const ) {
		return $this->checkModule( $const, self::CUSTOMER );
	}

	/**
	 * Wrapper function for checking if a module is enabled
	 * for a specified entity
	 * @param int $const, a global constant representing the id of the module to check
	 * @param string $type, a class constant representing the main type of the entity to check
	 * @return boolean or null if the module is not found
	 */
	private function checkModule( $const, $type ) {
		if ( !isset( $this->modulesAccess[$type] ) || !isset( $this->modulesAccess[$type][$const] ) ) {
			return false;
		}
		if ( $this->modulesAccess[$type][$const] === true ) {
			return true;
		} else if ( $this->modulesAccess[$type][$const] === false ) {
			return false;
		} else {
			return false;
		}
	}

	/**
	 *  Adds a single module to specified account
	 * @param int $accountId the account for which the module is being added to
	 * @param int $moduleId the module id
	 */
	public function addModuleToAccount( $accountId, $moduleId ) {
		$this->commonDb->insertIgnore()
			->into( 'modules_accounts' )
			->set( array(
				'module_id' => $moduleId
				,'account_id' => $accountId
			))
			->exec();
	}

	/**
	 * Removes a single module from an account
	 * @param int $accountId the id of the account from which the module is being removed from
	 * @param int $moduleId the id of the module to remove
	 */
	public function removeAccountModule( $accountId, $moduleId ) {
		$this->commonDb->delete()
			->from( 'modules_accounts' )
			->where( array(
				'account_id' => $accountId
				,'module_id' => $moduleId
			) )
			->exec();
	}

	/**
	 * Removes all the assigned modules of an account
	 * @param int $accountId the id of the account to remove the modules from
	 */
	public function removeAccountModules( $accountId ) {
		$modules = $this->getAccountAssignedModules( $accountId );
		foreach ( $modules as $module ) {
			$this->removeAccountModule( $accountId, $module['module_id'] );
		}
	}

	/**
	 * Simple function to return the modules assigned to an account. Currently it specificly returns
	 * only the module_id, but it can be extended as needed
	 * @param int $accountId the id for which we want the modules fetched
	 * @return array the modules that are enabled for the account
	 */
	public function getAccountAssignedModules( $accountId ) {
		return $this->commonDb->select()
			->columns( array(
				'module_id'
			) )
			->from( 'modules_accounts' )
			->where( array(
				'account_id' => $accountId
			) )
			->exec();
	}

	/**
	 * Checks if a specified module is active for a specified user
	 * @param int $module constant
	 * @param int $accountId the id of the account
	 * @return boolean true if module is active, false otherwise
	 */
	public function isModuleAssigned( $module, $accountId ) {
		$modules = $this->getAccountAssignedModules( $accountId );
		foreach ( $modules as $userModule ) {
			if ( $module === (int)$userModule['module_id'] ) {
				return true;
			}
		}
		return false;
	}

	/**
	 * @return int $cstId
	 */
	public function getCustomerId() {
		return $this->cstId;
	}
}
