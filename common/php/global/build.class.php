<?php
/**
 * @file build.class.php
 * Provides project building mechanism
 *
 * Requires: debug.class.php and util.class.php.
 *
 * Global configuration options:
 *
 * API_OUTPUT_PATH: output path for API
 *
 * PHP_OUTPUT_PATH: output path for PHP Classes and CRON scripts
 *
 * UI_OUTPUT_PATH: output path for PUBLIC_HTML and UI code
 *
 * GLOBAL_UI_PATH: path to global UI objects
 *
 * TMP_OUTPUT_PATH: temporary output path
 *
 * GLOBAL_CLASSES_PATH: path to global classes folder
 *
 * PROJECT_DIR_NAME: project directory name, to be generated in TMP_OUTPUT_PATH folder
 *
 * PAGE_TITLE: HTML title for template page
 *
 * PPP_PARAMETERS: Parameters to be passed to PPP, except for 'infile' and 'outfile'
 *
 * PPP_PATH: Path to PPP
 *
 * EXTJS_PATH: http path to extjs
*/

class BUILD {
	function __construct( $checkParameters = true ) {
		if ( !$checkParameters ) {
			return;
		}
		$error = "";
		if ( !is_dir( API_OUTPUT_PATH ) ) {
			$error = "( API_OUTPUT_PATH ): ".API_OUTPUT_PATH;
		}
		if ( !is_dir( PHP_OUTPUT_PATH ) ) {
			$error = "( PHP_OUTPUT_PATH ): ".PHP_OUTPUT_PATH;
		}
		if ( !is_dir( UI_OUTPUT_PATH ) ) {
			$error = "( UI_OUTPUT_PATH ): ".UI_OUTPUT_PATH;
		}
		if ( !is_dir( TMP_OUTPUT_PATH ) ) {
			$error = "( TMP_OUTPUT_PATH ): ".TMP_OUTPUT_PATH;
		}
		if ( $error != "" ) {
			die( "Folder does not exist ".$error."\n" );
		}
	}

	function initPaths( $buildOptions ) {

		if ( !$buildOptions ) {
			echo "No build options found.";
			return;
		}

		$this->phpOutputPath = $buildOptions['install_path'] . $buildOptions['php_output_path'];
		$this->uiOutputPath = $buildOptions['install_path'] . $buildOptions['ui_output_path'];
		$this->apiOutputPath = $buildOptions['install_path'] . $buildOptions['api_output_path'];
		$this->sqlOutputPath = $buildOptions['install_path'] . $buildOptions['sql_output_path'];

		$error = false;
		if ( !is_dir( $this->phpOutputPath ) ) {
			echo "Directory " . $this->phpOutputPath . " does not exist\n";
			$error = true;
		}
		if ( !is_dir( $this->uiOutputPath ) ) {
			echo "Directory " . $this->uiOutputPath . " does not exist\n";
			$error = true;
		}
		if ( !is_dir( $this->apiOutputPath ) ) {
			echo "Directory " . $this->apiOutputPath . " does not exist\n";
			$error = true;
		}
		if ( !is_dir( $this->sqlOutputPath ) ) {
			echo "Directory " . $this->sqlOutputPath . " does not exist\n";
			$error = true;
		}
		if ( !is_dir( TMP_OUTPUT_PATH ) ) {
			echo "Directory ( TMP_OUTPUT_PATH ): " . TMP_OUTPUT_PATH . " does not exist\n";
			$error = true;
		}
		if ( $error ) {
			die( "Please create the missing directories\n" );
		}
	}


	/**
	 * Copy files
	 * @param fileArray
	 *	Array of file names
	 * @param filePath
	 *	Source folder relative path
	 * @param destinationFolder
	 *	Destination folder name
	*/
	function copyFiles( $fileArray, $filePath, $destinationFolder ) {
		passthru( "cp " . $filePath . '{' . implode(',', $fileArray) . '} ' . $destinationFolder  );
	}


	/**
	 * Copies the files provided as an argument to the target path.
	 * It also takes care of creating directories if required ( i.e. if the
	 * nested array is provided)
	 *
	 * Does not handle directory names that are numeric.
	 *
	 * @todo:
	 * For replacing the copyGlobalClasses() function use the following arguments
	 *
 	 * $basePath : GLOBAL_CLASSES_PATH
	 * $targetPath : TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/php/global'
	 *
	 * @param string $basePath
	 * @param array $files
	 *  Can be of the format: [ 'file1', 'dir1' => 'file2' ]
	 * @param string $targetPath
	 *
	 */
	function copyFiles_( $basePath, $files, $targetPath ) {
		foreach ( $files as $key => $value ) {
			if ( is_string( $value ) ) {
				$file = $basePath . $value;
				$this->fileExists( $file );
				exec( 'cp -v ' . $file. ' '. $targetPath );
				continue;
			}
			if ( is_array( $value ) ) {
				if ( is_numeric( $key ) ) {
					$key = '';
				}
				$newBasePath = $basePath . $key . '/';
				$newTargetPath = $targetPath . $key . '/';
				if ( !is_dir( $newTargetPath ) ) {
					mkdir( $newTargetPath );
				}
				$this->copyFiles_( $newBasePath, $value, $newTargetPath );
				continue;
			}
		}
	}

	/**
	 * Transfer the required global PHP classes
	 * @param fileArray
	 *	Array of filenames
	 *
	 * @deprecated: use copyFiles_()
	*/
	function copyGlobalClasses( $fileArray ) {
		echo " Transfering global PHP Class files...\n";
		$destinationFolder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php/global";
		$sourceFilePath = GLOBAL_CLASSES_PATH;
		$this->copyFiles( $fileArray, $sourceFilePath, $destinationFolder );
	}

	/**
	 * Tranfer and register ( if requested ) cron files
	*/
	function copyCron( $fileArray, $register = false ) {
		echo " Transfering cron files...\n";
		$destinationFolder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php/cron";
		for ( $i = 0; $i < count( $fileArray ); $i++ ) {
			$file = "./php/cron/" . $fileArray[$i];
			$this->fileExists( $file );
			passthru("cp -Rv ". $file. " ". $destinationFolder);
			if ( $register == true ) {
				passthru("crontab ".$file);
			}
		}
	}

	/**
	 * Transfer the required local PHP classes
	 * @param fileArray
	 *	Array of filenames
	 * @deprecated: use copyFiles_()
	*/
	function copyLocalClasses( $fileArray ) {
		echo " Transfering local PHP Class files...\n";
		$destinationFolder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php/local";
		$sourceFilePath = "./php/local/";
		$this->copyFiles( $fileArray, $sourceFilePath, $destinationFolder );
	}

	/**
	 * Combine select local/global files in to agentCheckRequiredPhpFiles.php
	 * and then copy that file to the tmp/csi/php/local/ directory.
	 * agentCheckRequiredPhpFiles.php is used only for agent_check requests.
	 *
	 * To be consistent it's coded in the same style as the rest of this file
	 * which means it executes count(..) in a loop and uses double quotes and
	 * otherwise blends seamlessly in with this classes' existing code
	 *
	 * @param Array $globalFileArray
	 *	Array of filenames
 	 * @param Array $localFileArray
	 *	Array of filenames
	 * @param Array $externalFileArray
	 *  Array of filenames to be copied from the external lib directory
	*/
	function combineAgentCheckPhpFiles( $globalFileArray, $localFileArray, $externalFileArray ) {
		echo "\n Creating agentCheckRequiredPhpFiles.php (used for agent_check requests)\n";
		$content = '';

		// copy the content of external files in to the buffer
		for ( $i = 0, $size = count( $externalFileArray ); $i < $size; $i++ ) {
			$file = ROOT_PATH . 'external_lib/' . $externalFileArray[$i];
			$this->fileExists( $file );
			//echo "  reading ".$file."\n";
			$content .= "require_once( CLASSES_PATH . '/global/" . $globalFileArray[$i] . "');\n";
		}

		// copy the content of global files in to the buffer
		for ( $i = 0, $size = count( $globalFileArray ); $i < $size; $i++ ) {
			$file = GLOBAL_CLASSES_PATH . $globalFileArray[$i];
			$this->fileExists( $file );
			//echo "  reading ".$file."\n";
			$content .= "require_once( CLASSES_PATH . '/global/" . $globalFileArray[$i] . "');\n";
		}
		// copy the content of local files in to the buffer
		for ( $i = 0, $size = count( $localFileArray ); $i < $size; $i++ ) {
			$file = "./php/local/" . $localFileArray[$i];
			$this->fileExists( $file );
			//echo "  reading ".$file."\n";
			$content .= "require_once( CLASSES_PATH . '/local/" . $localFileArray[$i] . "');\n";
		}
		// make a description to output to the new file to show it's purpose
		$description = "<?php\n/**\n * Scripts used soley by agent_check requests.\n *\n */\n\n\n";
		// write it to file
		$destinationFolder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php/local/";
		echo "  writing ".$destinationFolder."agentCheckRequiredPhpFiles.php\n\n";
		file_put_contents( $destinationFolder."agentCheckRequiredPhpFiles.php", $description.$content );
	}

	/**
	 * Copy the local project files ( public_html/* )
	*/
	function copyPublicHtml() {
		echo " Tranfering 'public_html' ...\n";
		passthru("rsync -hurvz ./public_html ". TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/ --exclude=\".svn\" --exclude=\".empty\"");
	}

	/**
	 * Copy API folder ( as is )
	*/
	function copyApi() {
		echo " Tranfering 'API' ...\n";
		passthru("rsync -hurvz ./api ". TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/ --exclude=\".svn\" --exclude=\".empty\"");
	}

	/**
	 * Output file computed during buil
	 * @param content Output to file
	 * @param name Name of file to store in api/ directory
	 *
	*/
	function storeApiInclude( $content, $filename ) {
		$destinationFile = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php/local/".$filename;
		echo " Storing API include " . $destinationFile . "\n";
		file_put_contents( $destinationFile, $content );
	}

	/**
	 * Transfer the Red Hat Linux scanner files
	 * @param sourceFileArray
	 *	Array of source code file names
	 * @param buildFileArray
	 *	Array of RPM build file names
	*/

	function copyRHLScanner( $sourceFileArray, $buildFileArray ) {
		echo " Transfering Red Hat Linux scanner files...\n";
		$sourceFilePath = "../";
		$destination1Folder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/api/csia_rhlinux/source";
		$destination2Folder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/api/csia_rhlinux/rpmbuild";
		$this->copyFiles( $sourceFileArray, $sourceFilePath, $destination1Folder );
		$this->copyFiles( $buildFileArray, $sourceFilePath, $destination2Folder );
	}


	/**
	 * Create the list of custom CSS files loaded by index.html
	 * @param fileArray
	 *	Array of filenames
	 * @return
	 *	String, HTML css include code
	*/
	function createCustomCssFiles( $fileArray ) {
		echo " Preparing custom CSS files...\n";
		$customCSSFiles = "";
		for ( $i = 0; $i < count( $fileArray ); $i++ ) {
			echo "  ".$fileArray[$i]."\n";
			$customCSSFiles .= "<link rel=\"stylesheet\" type=\"text/css\" href=\"".$fileArray[$i]."\" />\n";
		}
		return $customCSSFiles;
	}

	/**
	 * Create list of custom JavaScript files loaded by index.html
	 * @param fileArray
	 *	Array of filenames
	 * @return
	 *	String, HTML javascript include code
	*/
	function createCustomJavaScriptFiles( $fileArray ) {
		echo " Preparing custom JavaScript files...\n";
		$customJavaScriptFiles = "";
		for ( $i = 0; $i < count( $fileArray ); $i++ ) {
			echo "  ".$fileArray[$i]."\n";
			$customJavaScriptFiles .= "<script type=\"text/javascript\" src=\"".$fileArray[$i]."\"></script>\n";
		}
		return $customJavaScriptFiles;
	}


	/**
	 * Create list of Adapter files loaded by index.html
	 * @param fileArray
	 *	Array of filenames
	 * @return
	 *	String, HTML javascript include code
	*/
	function createAdapterFiles( $fileArray ) {
		echo " Preparing Adapter files...\n";
		$adapterFiles = "";
		for ( $i = 0; $i < count( $fileArray ); $i++ ) {
			echo "  ".$fileArray[$i]."\n";
			$adapterFiles .= "<script type=\"text/javascript\" src=\"".$fileArray[$i]."\"></script>\n";
		}
		return $adapterFiles;
	}

	/**
	 * Generate inline JavaScript code based on Local and Global JS files
	 * @param localFileArray
	 *	Array, list of Local user defined .js files
	 * @param globalFileArray
	 *	Array, list of Global user defined .js files
	 * @return
	 *	String, JavaScript code
	*/
	function generateJavaScript( $localFileArray, $globalFileArray ) {
		$javaScript = "";
		echo " Global JS ";
		for ( $i = 0; $i < count( $globalFileArray ); $i++ ) {
			$file = GLOBAL_UI_PATH . $globalFileArray[$i];
			$this->fileExists( $file );
			//echo "  ".$file."\n";
			echo '.';
			$javaScript .= file_get_contents( $file )."\n";
		}

		echo "\n Local JS ";
		for ( $i = 0; $i < count( $localFileArray ); $i++ ) {
			$file = "./ui/local/" . $localFileArray[$i];
			$this->fileExists( $file );
			//echo "  ".$file."\n";
			echo '.';
			$javaScript .= file_get_contents( $file )."\n";
		}
		echo "\n";

		return $javaScript;
	}

	function uglifyJS( $javascript ) {
		echo "   Uglifying Javascript\n";
		if ( !$javascript ) {
			echo "   Uglifying Javascript failed - no input.\n";
			return false;
		}

		file_put_contents( TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/ui.js', $javascript );
		passthru( UGLIFYJS_PATH . ' -nc -o ' . TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/uglyui.js ' . TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/ui.js 1>/dev/null 2>&1', $ret );

		if ( !$ret ) {
			// Success
			return file_get_contents( TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/uglyui.js' );
		} else {
			echo "   Uglifying Javascript failed.\n";
			return $javascript;
		}
	}

	/**
	 * Generate custom HTML code based on Local HTML files
	 * @param localFileArray
	 *	Array, list of Local user defined .html files
	 * @return
	 *	String, HTML code
	*/
	function generateCustomHtml( $localFileArray ) {
		$html = "";
		echo " Custom HTML...\n";
		for ( $i = 0; $i < count( $localFileArray ); $i++ ) {
			$file = "./ui/local/" . $localFileArray[$i];
			$this->fileExists( $file );
			echo "  ".$file."\n";
			$html .= file_get_contents( $file )."\n";
		}

		return $html;
	}

	/**
	 * Prepare index.html, based on template file, i.e., index.tmpl or static.tmpl
	 * @param pageTitle
	 *	String, HTML page title
	 * @param javaScriptCode
	 *	String, the project's java script code
	 * @param cssFiles
	 *	String, optional HTML css include code
	 * @param javaScriptFiles
	 *	String, optional HTML java script include code
	 * @param adapters
	 *	String, optional ExtJS or 3rd party adapters
	 * @param theme
	 *	String, optional ExtJS or 3rd party themes, default is "gray" theme
	 * @return
	 *	String, index.html content
	*/
	function formatIndexTemplate( $pageTitle
				     ,$javaScriptCode
				     ,$cssFiles = ""
				     ,$javaScriptFiles = ""
				     ,$adapters = ""
				     ,$theme = "gray"
					 ,$customHtml = ""
					 ,$staticFlag = 0 ) {

		$indexPath = GLOBAL_INDEX_PATH . "index.tmpl";
		echo " Building index.html\n";

		if ( defined("EXT_RELEASE") && EXT_RELEASE ) {
			$extLib = "ext-all.js";
		} else {
			$extLib = "ext-all-debug.js";
		}
		$this->fileExists( $indexPath );
		$templateContent = file_get_contents( $indexPath );
		$templateContent = str_replace( "{page_title}", $pageTitle, $templateContent );
		$templateContent = str_replace( "{css_files}", $cssFiles, $templateContent );
		$templateContent = str_replace( "{javascript_files}", $javaScriptFiles, $templateContent );
		$templateContent = str_replace( "{javascript_code}", $javaScriptCode, $templateContent );
		$templateContent = str_replace( "{extjs_path}", EXTJS_PATH, $templateContent );
		$templateContent = str_replace( "{extjs_lib}", $extLib, $templateContent );
		$templateContent = str_replace( "{adapters}", $adapters, $templateContent );
		$templateContent = str_replace( "{theme}", $theme, $templateContent );
		$templateContent = str_replace( "{custom_html}", $customHtml, $templateContent );

		return $templateContent;
	}

	/**
	 * Load build options based on argv
	 * @return
	 *	Array, build options and values
	*/
	function buildOptions() {
		$argv = $GLOBALS['argv'];
		$buildOptions = array(
			"clean" => false
			,"cron" => false
			,"ppp" => false
			,"help" => false
			,"php" => false
			,"javascript" => false
			,"uglifyjs" => false
			,"noguid" => false
			,"guid" => false
			,"install_path" => defined("DEFAULT_INSTALL_PATH")
			? DEFAULT_INSTALL_PATH
			: ( defined("ROOT_PATH") ? ROOT_PATH : '' )
			,"php_output_path" => defined("PHP_OUTPUT_PATH") ? PHP_OUTPUT_PATH : ''
			,"api_output_path" => defined("API_OUTPUT_PATH") ? API_OUTPUT_PATH : ''
			,"ui_output_path" => defined("UI_OUTPUT_PATH") ? UI_OUTPUT_PATH : ''
			,"sql_output_path" => defined("SQL_OUTPUT_PATH") ? SQL_OUTPUT_PATH : ''
		);

		if ( count( $argv ) > 1 ) {
			for ( $i = 1; $i < count( $argv ); $i++ ) {
				switch ( strtolower( $argv[$i] ) ) {
					case "-clean":
						$buildOptions["clean"] = true;
						break;
					case "-cron":
						$buildOptions["cron"] = true;
						break;
					case "-ppp":
						$buildOptions["ppp"] = true;
						break;
					case "-uglifyjs":
						$buildOptions["uglifyjs"] = true;
						break;
					case "--help":
					case "-help":
					case "-h":
						$buildOptions["help"] = true;
						break;
					case "-php-syntax-check":
						$buildOptions['php'] = true;
						break;
					case "-js-syntax-check":
						$buildOptions['javascript'] = true;
						break;
					case "-noguid" :
						$buildOptions['noguid'] = true;
						if ( $buildOptions['guid'] ) {
							echo "Cannot have both -guid and -noguid options\n";
							exit();
						}
						break;
					default :
						if ( substr( strtolower( $argv[$i] ), 0, 6 ) == "-guid:" ) {
							if ( $buildOptions['noguid'] ) {
								echo "Cannot have both -guid and -noguid options\n";
								exit();
							}
							$guid = substr( strtolower( $argv[$i] ), 6 );
							// acdc1337-41a6-d1d5-1e75-fab1edb100d5
							if ( ! preg_match('/^[a-fA-F0-9]{6}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/', $guid ) ) {
								echo "Invalid GUID:'" . $guid . "'\n";
								exit();
							}
							$buildOptions['guid'] = $guid;
						}
				}
			}
		}

		$this->buildOptions = $buildOptions;
		return $buildOptions;
	}

	/**
	 * Clean temp build directory
	*/
	function makeClean() {
		echo "Cleaning temp output folder.\n";
		passthru("rm -rf ".TMP_OUTPUT_PATH.PROJECT_DIR_NAME);
	}

	/**
	 * Update output folders ( distribute all project's files )
	 * @param projectDirName
	 *	String, project folder name ( not path )
	*/
	function updateOutput() {
		echo "Updating output folder\n";
		echo " Updating Classes...\n";
		passthru("rsync -a ".TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/php ".PHP_OUTPUT_PATH);
		echo " Updating UI...\n";
		passthru("rsync -a ".TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/public_html ".UI_OUTPUT_PATH);
		echo " Updating API...\n";
		passthru("rsync -a ".TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/api ".API_OUTPUT_PATH);
		echo " Updating RESOURCES...\n";
		passthru("rsync -a ".TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/resources ".RESOURCES_OUTPUT_PATH);
	}

	/**
	 * Store index.html
	 * @param content
	 *	String, formatted and ready to store index.html content
	*/
	function storeIndexHtml( $content, $optionalFilename = '' ) {
		$fileName = "index.html";
		if ( $optionalFilename ) {
			$fileName = $optionalFilename . '.html';
		}
		$fileName = TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/public_html/' . $fileName;
		file_put_contents( $fileName, $content );
	}

	function checkSumIndexHtml( $optionalFilename = '' ) {
		$fileName = "index.html";
		if ( $optionalFilename ) {
			$fileName = $optionalFilename . '.html';
		}
		$fileName = TMP_OUTPUT_PATH . PROJECT_DIR_NAME . '/public_html/' . $fileName;

		$cmd = "sha512sum $fileName | cut -f1 -d\" \"";
		$ok = exec($cmd, $shaXsum);
		return $shaXsum[0];
	}

	/**
	 * Check if file exists, else exit and display error text
	*/
	function fileExists( $file ) {
		if ( !file_exists( $file ) ) {
			echo "File not found: ".$file."\nBuild canceled.\n";
			die();
		}
	}

	/**
	 * Run PHP syntax checking against all PHP files
	*/
	function phpSyntaxCheck() {
		$success = true;
		$io = $GLOBALS['util'];
		$folder = TMP_OUTPUT_PATH.PROJECT_DIR_NAME."/";
		$this->fileExists( PPP_PATH );
		$phpFiles = $io->find( $folder, "php" );
		$files = $phpFiles;
		echo " Running PHP Syntax check...\n";
		for ( $i = 0; $i < count( $files ); $i++ ) {
			exec( "php -ln ".$files[$i] , $output, $ret);
			if ($ret != 0) {
				echo $files[$i] . " syntax failed\n";
				$success = false;
				return $success;
			}
		}

		return $success;
	}

	/**
	 * Run JSLint syntax checking against all Javascript files
	*/
	function jsSyntaxCheck() {
		$io = $GLOBALS['util'];
		$globalFolder = GLOBAL_UI_PATH;
		$localFolder = "ui/local/";
		$globalFiles = $io->find( $globalFolder, "js" );
		$localFiles = $io->find( $localFolder, "js" );
		$files = array_merge( $localFiles, $globalFiles );
		echo " Running Javascript Syntax check...\n";
		for ( $i = 0; $i < count( $files ); $i++ ) {
			passthru( "jsl -process ". $files[$i] . " | grep -A2 SyntaxError");
		}
	}


	/**
	 * Run PPP against all files ( JS and PHP )
	*/
	function makePPP() {
		$folder = TMP_OUTPUT_PATH . PROJECT_DIR_NAME . "/";
		exec( "grep -rn \"ifdef\" " . $folder . " | awk -F ':' '{print $1}' | sort -u ", $files );
		if ( empty( $files ) ) {
			echo  " No files to PPP\n";
			return;
		}

		$this->fileExists( PPP_PATH );

		echo  " Running PPP...\n";
		foreach ( $files as $thisFile ) {
			passthru( PPP_PATH . " " . PPP_PARAMETERS . " " . $thisFile . " " . $thisFile . ".out" );
			//echo( PPP_PATH . " " . PPP_PARAMETERS . " " . $thisFile . " " . $thisFile . ".out\n" );
		}
		echo  " Cleaning PPP...\n";
		foreach ( $files as $thisFile ) {
			passthru( "mv " . $thisFile . ".out " . $thisFile );
			//echo ( "mv " . $thisFile . ".out " . $thisFile ."\n" );
		}
	}

	function PPPString( $string ) {
		$folder = TMP_OUTPUT_PATH . PROJECT_DIR_NAME . "/";
		$this->fileExists( PPP_PATH );
		echo  " Running PPP on string...\n";
		while( true ) {
			$filename = $folder . uniqid( 'pppFile', true ).'.tmp';
			if ( !file_exists( $filename ) ) {
				break;
			}
		}
		file_put_contents( $filename, $string );
		passthru( PPP_PATH . " " . PPP_PARAMETERS . " " . $filename . " " . $filename . ".out" );
		$out = file_get_contents( $filename . '.out' );
		unlink( $filename );
		unlink( $filename . '.out');
		return $out;
	}

	/**
	 * Build output directory structure
	*/
	function makeDirectoryStructure() {
		echo " Preparing directory structure...\n";

		$root = TMP_OUTPUT_PATH.PROJECT_DIR_NAME;

		exec("mkdir -p ".$root."/php/local");
		exec("mkdir -p ".$root."/php/global");
		exec("mkdir -p ".$root."/php/cron");
		exec("mkdir -p ".$root."/public_html");
		exec("mkdir -p ".$root."/api/csia_rhlinux/rpmbuild");
		exec("mkdir -p ".$root."/api/csia_rhlinux/source");
	}

	/**
	 * Replace all occurrences of the search string with the replacement string
	 *
	 * @param string $filePath Path to file to replace string in
	 * @param string|array $search
	 *	The value being searched for, an array may be used to designate multiple strings
	 * @param string|array $replacement
	 *	The replacement value that replaces found search values, an array may be used to designate multiple replacements.
	 * @return int Size of new file or false on failure
	 */
	public function replaceInFile( $filePath, $search, $replacement ) {
		$buffer = file_get_contents( $filePath );
		return file_put_contents( $filePath, str_replace( $search, $replacement, $buffer ) );
	}

	/**
	 * Creates the directories required by the SC2012 Plugin build
	 */
	function makeSccmDirectoryStructure() {
		// Create private build directories
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'sccm/api' ));
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'sccm/binaries' ));
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'sccm/php/local' ));
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'sccm/php/global' ));
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'sccm/resources' ));
		// Create public build directories
		exec('mkdir -p ' . escapeshellarg( TMP_OUTPUT_PATH . 'public_html/sccm/api' ));
		// Create deploy directories
		exec('mkdir -p ' . escapeshellarg( RESOURCES_OUTPUT_PATH ));
		exec('mkdir -p ' . escapeshellarg( BINARIES_OUTPUT_PATH ));
		exec('mkdir -p ' . escapeshellarg( API_OUTPUT_PATH ));
		exec('mkdir -p ' . escapeshellarg( PHP_OUTPUT_PATH ));
		exec('mkdir -p ' . escapeshellarg( UI_OUTPUT_PATH ));
		exec('mkdir -p ' . escapeshellarg( UI_API_OUTPUT_PATH ));
	}

	/**
	 * Display usage help
	*/
	function usage() {
		$argv = $GLOBALS['argv'];
		echo "php ".$argv[0]." [OPTIONS]\n";
		echo " -ppp - run ppp against all files, using configured parameters (PPP_PARAMETERS and PPP_PATH) \n";
		echo " -clean - remove current project files from temporary folder\n";
		echo " -cron - install cron files\n";
		echo " -help - display current help text\n";
	}
}
