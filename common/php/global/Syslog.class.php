<?php

/**
* A PSR-3 implementation of a Logger that uses the Syslog service
*/
class Syslog extends Psr\Log\AbstractLogger {
	protected $logMessagePrefix = '-';
	protected $identifier = 0;
	protected $options;
	protected $facility;
	protected $depth = 0;
	protected $logLevel = LOG_DEBUG;

	protected function openLog() {
		if ( $this->identifier && $this->options && $this->facility ) {
			closelog();
			openlog( $this->identifier, $this->options, $this->facility );
		}
	}

	public function __destruct() {
		closelog();
	}

	public function setLogLevel( $logLevel ) {
		$this->logLevel = $logLevel;
	}

	public function setIdentifier( $identifier ) {
		$this->identifier = $identifier;
		$this->openLog();
	}

	public function setMessagePrefix( $messagePrefix ) {
		$this->logMessagePrefix = $messagePrefix;
		$this->openLog();
	}

	public function setOptions( $options ) {
		$this->options = $options;
		$this->openLog();
	}

	public function setFacility( $facility ) {
		$this->facility = $facility;
		$this->openLog();
	}

	public function indent() {
		$this->depth++;
	}

	public function unindent() {
		$this->depth--;
	}

	/**
	 * Formats the message with the values provided
	 *
	 * @see
	 * Modified from: https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-3-logger-interface.md
	 *
	 * @todo: This method doesn't take into consideration the log injection
	 * problem.
	 */
	protected function format( $message, array $values = array() ) {
		// build a replacement array with braces around the values keys
		$replace = array();
		foreach ($values as $key => $val) {
			$replace['{' . $key . '}'] = $val;
		}

		// interpolate replacement values into the message and return
		return strtr($message, $replace);
	}

	protected function getContext() {
		$trace = debug_backtrace();
		if ( isset( $trace[3] ) ) {
			$caller = $trace[3];
		} else {
			$caller = end( $trace );
		}

		$context = '';
		$file = null;

		if ( isset( $caller['file'] ) ) {
			$file = $caller['file'];
		}

		$function = $caller['function'];
		if ( isset( $caller['class'] ) ) {
			$function = $caller['class'] . $caller['type'] . $caller['function'];
		}

		if ( isset( $file ) ) {
			$context = str_replace( array('.class','.php'), '', basename( $file ) );
		} else {
			$context = '<unknown file>';
		}
		$context .= ' ' . $function . ':';

		return $context;
	}

	/**
	 * Logs with an arbitrary level.
	 *
	 * @param mixed  $level
	 * @param string $message
	 * @param array  $values
	 *
	 * @throws Exception
	 * @return null
	 */
	public function log( $level, $message, array $values = array() ) {
		if ( empty( $message ) ) {
			return;
		}

		switch( $level ) {
		case Psr\Log\LogLevel::EMERGENCY:
			$syslogLevel = LOG_EMERG;
			break;
		case Psr\Log\LogLevel::ALERT:
			$syslogLevel = LOG_ALERT;
			break;
		case Psr\Log\LogLevel::CRITICAL:
			$syslogLevel = LOG_CRIT;
			break;
		case Psr\Log\LogLevel::ERROR:
			$syslogLevel = LOG_ERR;
			break;
		case Psr\Log\LogLevel::WARNING:
			$syslogLevel = LOG_WARNING;
			break;
		case Psr\Log\LogLevel::NOTICE:
			$syslogLevel = LOG_NOTICE;
			break;
		case Psr\Log\LogLevel::INFO:
			$syslogLevel = LOG_INFO;
			break;
		case Psr\Log\LogLevel::DEBUG:
			$syslogLevel = LOG_DEBUG;
			break;
		default:
			throw new Exception( 'Invalid log level specified' );
		}

		if ( $syslogLevel > $this->logLevel ) {
			return;
		}

		syslog(
			$syslogLevel
			, $this->identifier . ' ' . str_repeat( '  ', $this->depth ) . $this->getContext() . ' ' . $this->logMessagePrefix . ' ' . ( isset( $values ) ? $this->format( $message, $values ) : $message )
		);
	}
}