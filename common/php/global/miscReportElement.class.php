<?php

class miscReportElement implements reportGlobalConfigurator, reportLocalConfigurator {

	// WARNING: Any const strings that are used in the table of contents keyword strings
	// must be short enough names that the keyword strings do not cause a line break in
	// the table.  This is an issue with roots in the base fpdf class and how it replaces
	// strings in the file.  There is not much we can do about it now.
	const TOC_KEYSTRING = "toc_misc_";

	// PARAMETERS
	// ----------
	const FILE_NAME_PARAM = "file_name";
	const REPORT_TITLE_PARAM = "report_title";
	const SHOW_PARAMETERS_PARAM = "show_report_params";

	// Parameters related to User and Asset Lists Details
	const DETAIL_LEVEL_PARAM = "publish_detail_level";
	const GROUP_ALIAS_PARAM = "publish_group_alias";

	// TOKENS
	// ------
	const SHOW_PARAMETERS_TOKEN = "SHOW";

	// Parameters related to User and Asset Lists Details
	// TODO - this is VIM specific - get it out of the common code
	const SHOW_ALL_TOKEN = "MISC_ALL";
	const SHOW_NONE_TOKEN = "MISC_NONE";
	const ALIAS_TOKEN = "MISC_ALIAS";

	function __construct( $name, $modulesConsidered ) {
		$this->name = $name;
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
		$GLOBALS['reporting']->registerReportElement( $this->name, $this );

		// Set up the array of main options our parse function will go through
		$this->possibleMainOptions = array( self::SHOW_PARAMETERS_TOKEN
											,self::SHOW_ALL_TOKEN
											,self::SHOW_NONE_TOKEN
											,self::ALIAS_TOKEN );

		// Note - not all of our main options are content generating. We maintain a separate array for the TOC options.
		$this->tocMainOptions = array( self::SHOW_PARAMETERS_TOKEN );

		// These are the modules that this class needs to consider as having options to look at
		// The order here also defines the order we show each modules options in
		$this->modulesConsidered = $modulesConsidered;
	}

	function getName() {
		return $this->name;
	}

	function getConfigurationRequirements() {

 		// This modules kind of needs everything from the configuration string, as it
		// looks at everything the user selected in order to know what to publish.
	 	$configurationParamsArray = array();

		foreach ( $this->modulesConsidered as $thisModule ) {
			if ( isset($GLOBALS[$thisModule]) ) {
				if ( method_exists( $GLOBALS[$thisModule], 'getName' ) ) {
					$configurationParamsArray[] = $GLOBALS[$thisModule]->getName();
				}
			}
		}

		return $configurationParamsArray;
	}


	function saveConfiguration( $accountId = 0, $reportId = 0 ) {
		if ( !is_numeric( $accountId ) || ( $accountId <= 0 ) ) {
			$GLOBALS['debug']->error( $this->name . " - Unable to save configuration - Account Id is incorrect" );
			return false;
		}
		if ( !is_numeric( $reportId ) || ( $reportId <= 0 ) ) {
			$GLOBALS['debug']->error( $this->name . " - Unable to save configuration - Report Id is incorrect" );
			return false;
		}

 		// Fetch Parameters
		$fileName = $GLOBALS['input']->MPOST( $this->name . "_" . self::FILE_NAME_PARAM );
		$reportTitle = $GLOBALS['input']->MPOST( $this->name . "_" . self::REPORT_TITLE_PARAM );

		// We will update these in the DB if the user has input values for them - build the update values accordingly
		$updateValues = '';

		// If filename is empty, that is ok - the user does not have to set it, and it should not be saved as something as if they did set it.
		// If it is empty and invalid, however, replace it with default name
		if ( $fileName ) {
			//CSIL-9032 corrected UTIL static call 
			$validateFName =  new UTIL() ;
			if ( !$validateFName->validatePdfFilename($fileName) ) {
				$fileName = 'report.pdf';
				$GLOBALS['debug']->error( $this->name . " fileName is invalid - using generic name" );
			}
			$updateValues = " filename = '" . $fileName .  "' ";
		}

		// Do the same for reportTitle - don't enter a value if one doesn't exist
		if ( $reportTitle ) {
			if ( $updateValues ) {
				$updateValues .= " ,";
			}
			$updateValues .= " report_title = '" . $reportTitle .  "' ";
		}

		// If we had values to update here, do so
		if ( $updateValues ) {
			$saveConfigurationQuery = "UPDATE
						ca.enhanced_reporting_schedule
					SET " . $updateValues . "
					WHERE
						account_id = " . (int) $accountId . "
					AND
						id = " . (int) $reportId;

			$this->database['ca']->query( $saveConfigurationQuery );
		}

		return true;
	}

 	function generateConfigurationString() {
		$config = "";

		$showReportParams = $GLOBALS['input']->MPOST( $this->name . "_" . self::SHOW_PARAMETERS_PARAM );
		$detailLevel = $GLOBALS['input']->MPOST( $this->name . "_" . self::DETAIL_LEVEL_PARAM );
		$alias = $GLOBALS['input']->MPOST( $this->name . "_" . self::GROUP_ALIAS_PARAM );

		// if $alias is not alphanum set it to be empty string
		if ( !ctype_alnum($alias) ) {
			$alias = '';
		}
		// TODO: all reporting modules don't have validation of data. That should be implemented, and proper response/error code should
		// be forwarded to the user. For example, in this module we enforce alpha-numeric string for $alias variable, however if they somehow
		// provide string that is not alphanum, we set alias to be empty string. That is safe.
		// In ideal case we should provide user with notification where he entered invalid value, so that he has a chance of fixing it.
		// For now sanitization will work.

		if ( $showReportParams ) {
			$config .= self::SHOW_PARAMETERS_TOKEN;
			if ( $detailLevel != "" ) {
				$config .= ";";
				switch ( $detailLevel ) {
				case 1:
					$config .= self::SHOW_ALL_TOKEN;
					break;
				case 2:
					$config .= self::ALIAS_TOKEN . ":" . $alias;
					break;
				case 3:
					$config .= self::SHOW_NONE_TOKEN;
					break;
				default:
					break;
				}
			}
		}

		if ( $config ) {
			$config = $this->name . ":" . $config;
		}

		return $config;
	}

	// ===========================================================================
	// PDF HANDLING
	// ===========================================================================

	// Parse the options for this module and load the parsedOptions array, which is used
	// in multiple components.
	//
	// Options are as follows: we have the global flag of if we include the report parameter
	// section or not, and if so, we have the additional field of what kind of info we show.
	//
	function parseOptions ( $options ) {

		// For the possible main options, go through and determine if they were requested
		$parsedOptions = array();
		for ( $i=0; $i < count($this->possibleMainOptions); $i++ ) {

			$keyWord = $this->possibleMainOptions[$i];
			$keyLength = strlen($keyWord);

			for ( $j=0; $j < count($options); $j++ ) {

				if ( $keyWord == substr( $options[$j], 0, $keyLength ) ) {

					$parsedOptions[$keyWord] = 1;

					// Deal with any cases that have additional options
					if ( strlen($options[$j]) > $keyLength + 1 ) {
						// then there is extra data after 'keyword:'
						$extraOptions = substr( $options[$j], $keyLength + 1 );

						switch ( $keyWord ) {
						case self::ALIAS_TOKEN:
							// In this case, the alias is now already in extraOptions
							// We only use this when we want to print it in the pdf, so convert
							// it here to the pdf output format
							$parsedOptions['alias'] = iconv('UTF-8', 'ISO-8859-1', $extraOptions);
							break;
						}
					}

					// remove it to shorten the search next time through
					array_splice( $options, $j, 1 );
					break;
				}
			}
		}

		return $parsedOptions;
	}

	function generateTocContent( $optionsArray, $requiredConfigurationsParameters ) {

		$parsedOptions = $this->parseOptions( $optionsArray );

		$tocData = $GLOBALS['reporting']->constructTocData( $parsedOptions, $this->tocMainOptions, self::TOC_KEYSTRING, "" );

		return $tocData;
	}

	function parseModuleData( $requiredConfigurationsParameters ) {

		// Go through the modulesConsidered array and get our results back for each module
		// that exists.  Anything that doesn't exist will get the default value of 0
		$moduleData = array();

		foreach ( $this->modulesConsidered as $thisModule ) {

			$result = 0; // default
			$thisParam = '';
			if ( isset($GLOBALS[$thisModule]) ) {
				$thisParam = $GLOBALS[$thisModule]->getName();
				if ( $thisParam && isset($requiredConfigurationsParameters[$thisParam]) ) {
					$result = $requiredConfigurationsParameters[$thisParam];
				}
			}

			$moduleData[$thisModule] = $result;
		}

		return $moduleData;
	}

}
