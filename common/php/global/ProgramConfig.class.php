<?php

/**
 * @file ProgramConfig.class.php
 * This file contains the ProgramConfig class
 */


/**
 * Class ProgramConfig
 * Handles .ini file configuration and translates name/value pairs from .ini files into defines
 * for compatibility with old code.
 *
 * <AUTHOR>
 * @since 26.05.2015
 */
class ProgramConfig {

	/**
	 * Contains all the actual config values.
	 * @var array
	 */
	private $configValues = array();

	/**
	 * PHP doesn't have a built-in function that recursively merges arrays in a sane way, so we have to write our own,
	 * based on one from the internet, from a comment on : http://php.net/manual/en/function.array-merge-recursive.php
	 * @param array &$merged Passed by reference
	 * @param array &$other Passed by reference
	 */
	private function mergeConfig(&$merged, &$other) {
		foreach ( $other as $key => &$value ) {
			if ( is_array ( $value ) && isset ( $merged [$key] ) && is_array ( $merged [$key] ) ) {
				$this->mergeConfig( $merged [$key], $value );
			} else {
				$merged [$key] = $value;
			}
		}
	}

	/**
	 * Non-Recursively loads .ini files from a directory, merging the returned config into a single array
	 * @param string $dir Directory to search for .ini files without a trailing path slash
	 * @param array &$accum Accumulated configuration property => value pairs - passed by reference
	 * @throws Exception
	 */
	private function loadConfig($dir, &$accum) {
		$files = array();
		foreach (glob("${dir}/*.ini") as $path) {
			if (!is_file($path)) {
				continue;
			}
			$files[] = $path;
		}
		natsort($files); // human readable sorting files by name

		foreach($files as $path){
			$arr = parse_ini_file($path);
			if (false === $arr) {
				throw new Exception('Unable to load .ini file. Settings with non-alphanumeric characters in their value must be surrounded by quotes - ' . $path);
			} else {
				$this->mergeConfig($accum, $arr);
			}
		}
	}

	/**
	 * Constructor
	 * Loads ini files from given directories or a single directory
	 * @param string $dir
	 *	The directory from which to load .config files
	 */
	public function __construct($dir)
	{
		if (is_array($dir)) {
			foreach ($dir as $singleDir) {
				$this->loadConfig($singleDir, $this->configValues);
			}
		} else {
			$this->loadConfig($dir, $this->configValues);
		}
	}

	/**
	 * Default config getter
	 *
	 * @param string $name
	 * @param string|int $default
	 * @return string|null
	 */
	public function getValue($name, $default) {
		if ( array_key_exists($name,$this->configValues) ) {
			return $this->configValues[$name];
		} else {
			return $default;
		}
	}

	/**
	 * Gets the domain + port that Agents will connect to the server on.
	 * @todo Update the Server Edition's settings menu to supply this value
	 * instead of computing it in the PHP code.
	 *
	 * Used by CSI Server Edition only.
	 *
	 * @param string $domain
	 * @param int $agentPort
	 * @return string|int
	 *	Example: 'csi7.server.com:443'
	 */
	public function getAgentDomain($domain, $agentPort)
	{
		// Agents use the same domain as the CA_DOMAIN however the Port used by Agents can be different
		if ($agentPort) {
			$parsedUrl = parse_url($domain);
			if ( empty( $parsedUrl['port'] ) ) { // No port already specified for the CA_DOMAIN
				$agentDomain = $domain. ':' . $agentPort;
			} else { // Port was already specified so replace it
				$agentDomain = preg_replace("(:${parsedUrl['port']})", ':' . $agentPort, $domain);
			}
		} else {
			// If no Agent Port was specified in the installationProcess.sh then use the default URL
			$agentDomain = $domain;
		}
		return $agentDomain;
	}

	/**
	 * Default config setter
	 *
	 * @param string $name
	 * @param string $value
	 */
	public function setValue($name, $value) {
		$this->configValues[$name] = $value;
	}

	/**
	 * Ensures log level values are valid. 
	 * Incorrect log level string will be defaulted to LOG_ERROR
	 * 
	 * TODO: make an error log entry log if log level level value is invalid
	 * @param string $name
	 * @param string $value
	 */
	public function getLogLevelFromString($logLevel, $default) 
 	{ 

 		$userDefinedLogLevel = $this->getValue($logLevel,$default);

		// Return a default LOG_ERR if the string log level doesn\'t match an existing integer LOG_* constants 
		// Comparison must be done with  '==='' because position 0 is falsy and will evaluate incorrecty
		if (
			strpos($userDefinedLogLevel, 'LOG_') === false
			|| ! defined($userDefinedLogLevel)
			|| ! is_int(constant($userDefinedLogLevel) )
		) { 
			return LOG_ERR; 
		} 

		return constant($userDefinedLogLevel); 
	} 
}