<?php
/**
 * @file zip.class.php
 * Provides an extension to php's ZipArchive extension.
*/
class ZIP {
	var $level = 0; // No compression, save CPU and RAM.
	private $zipPath = '/usr/bin/zip';

	/**
	 * Converts a String to a GZIP archive and returns the content in STRING format.
	 * @param fileContent
	 *	String fileContent to be archived
	 * @return
	 *	String ZIP Archive Stream
	*/
	function string2qzip( $fileContent ) {
		return gzencode( $fileContent, $this->level );
	}

	/**
	 * Converts a File to a ZIP archive and returns the content in STRING format.
	 * @param fileName
	 *	String fileName
	 * @return
	 *	String ZIPed file content
	*/
	function file2zip( $fileName, $folder = "/tmp/" ) {
		$oldpath = getcwd();
		chdir( $folder );
		$result = exec( $this->zipPath." ".escapeshellarg( $fileName.".zip" ). " ".escapeshellarg( $fileName ), $output, $return );
		$content = file_get_contents( $fileName.".zip" );
		unlink( $fileName );
		unlink( $fileName.".zip" );
		chdir( $oldpath );
		$GLOBALS['debug']->notice( "ZIP Action Executed" );

		return $content;
	}
}

?>