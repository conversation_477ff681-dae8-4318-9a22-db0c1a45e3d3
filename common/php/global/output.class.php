<?php
/**
 * @file output.class.php
 * Provides UI HTML output functionality
 *
*/

class OUTPUT {

	function __construct() {
	}

	/*
	 * Output Types
	 */
	const XML = 1;
	const RSS = 2;
	const PDF = 3;
	const CSV = 4;
	const PNG = 5;
	const BIN = 6;				/* Binary files e.g. exe etc */

	/**
	 * @var Crypt
	 */
	protected $crypt;

	/**
	 * @var int|null If not null then the cell index to decrypt when outputting CSV
	 */
	protected $cellIndexToDecrypt = null;

	/*
	 * Allowed content types and their headers.
	 */
	private static $contentTypes
		= array( self::XML => 'Content-Type: text/xml; charset: ISO-8859-1'
				 ,self::RSS => 'Content-Type: application/rss+xml; charset: ISO-8859-1'
				 ,self::PDF => 'Content-Type: application/pdf'
				 ,self::CSV => 'Content-type: text/csv; charset: UTF-8'
				 ,self::PNG => 'Content-Type: application/png'
				 ,self::BIN => 'Content-Type: application/octet-stream'
				 );
	/**
	 * Function for setting the charset.
	 * @param charset
	 *	String charset
	*/
	function setCharset( $charset ) {
		header('Content-type: text/html; charset='.$charset);
	}

	/**
	 * @param Crypt $crypt
	 */
	public function setCrypt(Crypt $crypt)
	{
		$this->crypt = $crypt;
	}

	/*
	 * @todo:
	 * Eventhough we set the appropriate header we are still htmlencoding the data, why?
	 *
	 */
	public static function echoCsvFile( $rows, $setHeader = true ) {
		if ( $setHeader ) {
			header( self::$contentTypes[ self::CSV ] );
			header('Content-Disposition: attachment; filename=export.csv');
			// header('Pragma: no-cache');
			header('Expires: 0');
		}

		$outputStream = fopen('php://output', 'w');

		if ( is_array( $rows ) ) {
			foreach( $rows as &$row ) {
				// Array walk isn't working for some reason
				// array_walk( $row, 'JSON::format' );
				foreach( $row as &$value ) {
					$value = JSON::format( $value, '' );
				}
				unset( $value );
				fputcsv( $outputStream, $row );
				// if we want to use /r/n instead of default /n we can do something like this (hasn't been tested):
				// if ( fseek( $outputStream, -1, SEEK_CUR ) === 0 ) {
				// 	fwrite( $outputStream, "\r\n" );
				// }
			}
			unset( $row );
		} else {
			fwrite( $outputStream, 'Error occurred during data extraction.' );
		}
		fclose( $outputStream );
		die();
	}

	/*
	 * Function for outputting the data.
	 *
	 * @param resource
	 * @param isFile
	 *  Flag to determine if the resource is a file or String
	 * @param type
	 *  Content-type i.e. XML, RSS, PDF, CSV
	 * @param $isAttachment
	 *  Specify if the content should be sent as an attachment
	 * @param $attachmentName
	 *  The name of the attachment, if any.
	 *
	 * @return Bool | void
	 *  The function terminates the script on sucessful execution. This might be changed
	 *  in future.
	 */
	public static function output( $resource, $isFile = false, $type = false, $isAttachment = false, $attachmentName = false, $exitOnComplete = true ) {

		if ( $isFile && !file_exists( $resource ) ) {
			return false;
		}

		if ( !isset( self::$contentTypes[ $type ] ) ) {
			return false;
		}


		// Set the appropriate Content-Type
		header( self::$contentTypes[ $type ] );

		// Handle Attachments
		if ( $isAttachment ) {

			if ( $isFile ) {
				// @todo: if not attachment, should we still set the Content-Length?
				// Get the file size
				$fileSize = filesize( $resource );
				if( !$fileSize ) {
					return false;
				}
				header( 'Content-Length: ' . (int) $fileSize );
			}

			$attachment = 'filename=';
			if ( $attachmentName === false
				 || !is_string( $attachmentName )
				 || preg_match( '/[^A-Za-z0-9+\x5F\x2E\x2D]/', urlencode($attachmentName) ) ) {
				$attachment = '';
			} else {
				$attachment .= urlencode($attachmentName);
			}
			header( 'Content-Disposition: attachment; ' . $attachment );
			// header( 'Pragma: no-cache' );
			header( 'Expires: 0' );
		}

		if ( $isFile ) {
			readfile( $resource );
		} else {
			echo $resource;
		}

		if ( $exitOnComplete ) {
			exit();
		}
		return true;
	}

	/**
	 * @param int $value
	 */
	public function setCellIndexToDecrypt($value)
	{
		$this->cellIndexToDecrypt = (int) $value;
	}
	
	/**
	 * Add single quotes for rows starts with characters =, +, -, @, |, %
	 *
	 * @param mixed $csvRow
	 * @return mixed $csvRow
	 */
	//CSIL-8208 CSV sanitization
	public function sanitizeCSV($csvRow)
	{
		$triggers = array( '=', '+', '-', '@', '|', '%');
		if (in_array(substr( trim($csvRow), 0, 1 ), $triggers, true ) ) {
			$csvRow= "'" . trim($csvRow);
		}
		return $csvRow;
	}

	/**
	 * Write an Array to CSV file
	 *
	 * @param array $rows
	 * @param Resource $filePointer
	 */
	
	public function writeArrayToCsvFile($rows, $filePointer)
	{
		// TODO: The cell handling logic is copied from below and can be de-duplicated however it's copied now
		// because we don't have time to analyze the PHP performance penalty with iterating over 100,000s lines atm
		//CSIL-8208 CSV sanitization
		foreach($rows as  $row) {
			foreach($row as $key => $cell) {
				$line[$key] = UTIL::htmlspecialchars($cell, ENT_QUOTES);
				if ((!is_numeric($cell)) && (trim($cell)!='-'))
					$line[$key]= $this->sanitizeCSV($cell) ;
			}
			fputcsv($filePointer, $line, ',', '"');
		}

		fclose($filePointer);
	}


	/**
	 * Write a PDOStatement to CSV file
	 *
	 * @param PDOStatement $pdoStatement
	 * @param Resource $filePointer
	 */
	public function writePdoToCsvFile(PDOStatement $pdoStatement, $filePointer)
	{
		$columnCount = $pdoStatement->columnCount();
		$cellIndexToDecrypt = NULL;
		// Look at the first row to determine if a cell index needs decrypting
		$row = $pdoStatement->fetch(PDO::FETCH_NUM);
		if (!empty($row)) {
			
			if ($this->crypt) {
				$this->crypt->findCellIndexToDecrypt(array_keys($row));
				$cellIndexToDecrypt = $this->cellIndexToDecrypt;
			}
			
			// Write body to CSV file
			do {
				for ($i = 0; $i < $columnCount; $i++) {
					// Decrypt an encrypted cell if necessary
					if ($cellIndexToDecrypt === $i) {
						// Decryption adds 3s to processing for 100,000 100-byte cells
						$row[$i] = UTIL::htmlspecialchars($this->crypt->decrypt($row[$i]), ENT_QUOTES);
					} else {
						$row[$i] = UTIL::htmlspecialchars($row[$i], ENT_QUOTES);
					}
					//CSIL-8208 CSV sanitization
					if ((!is_numeric($row[$i])) && (trim($row[$i]) != '-')) {
						$row[$i] = $this->sanitizeCSV($row[$i]);
					}
				}
				fputcsv($filePointer, $row, ',', '"');
			} while ($row = $pdoStatement->fetch(PDO::FETCH_NUM));
		}
		fclose($filePointer);
	}
}