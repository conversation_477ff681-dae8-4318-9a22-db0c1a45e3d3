<?php
/**
 * @file cookie.class.php
 * Global cookie library.
 *
 * This file provides basic cookie functionality.
 *
 * Requires: debug.class.php
*/

/**
 * The cookie class can not be instantiated. All methods are static, so they can be accessed through COOKIE::method()
 *
 * Global configuration options:
 *
 * COOKIE_EXPIRE: default cookie expiration time
 *
 * COOKIE_PATH: cookie path
 *
 * COOKIE_DOMAIN: cookie domain
*/
class COOKIE {
	/**
	 * Cookie class cannot be instantiated.
	*/
	private function __construct() { }

	/**
	 * Write a cookie
	 * @param cookieName
	 *	String, cookie name
	 * @param cookieValue
	 *	String, cookie value
	 * @param cookieExpire
	 *	String cookie validity time
	*/
	static function setCookieValue( $cookieName, $cookieValue, $cookieExpire = null ) {
		if ( $cookieExpire == null ) {
			$cookieExpire = COOKIE_EXPIRE;
		}
		setcookie( $cookieName, $cookieValue, time() + $cookieExpire, COOKIE_PATH, COOKIE_DOMAIN );
	}

	/**
	 * Delete a cookie
	 * @param cookieName
	 *	String, cookie name
	*/
	static function deleteCookie( $cookieName ) {
		setcookie( $cookieName, "", time() - 36000, COOKIE_PATH, COOKIE_DOMAIN );
	}

	/**
	 * Get cookie value
	 * @param cookieName
	 *	String, cookie name
	 * @return cookie value
	 *	String, cookie value
	*/
	static function getCookieValue( $cookieName ) {
		if ( $GLOBALS['input']->issetCOOKIE( $cookieName ) ) {
			return $GLOBALS['input']->COOKIE( $cookieName );
		}
	}
}
