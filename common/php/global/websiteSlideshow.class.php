<?php
/**
 * @file websiteSlideshow.class.php
 * Provides website slideshow control, for Website and CRM.
 *
 * Optional: FILE_RENDER_PATH: path to the file rendering script
 *
 * NOTE: Database class should be called $database ( same for $util ).
*/
class websiteSlideshow {

	/**
	 * Fetch existing slideshow data
	 * @return
	 *	Resource containing slideshow rows or false
	*/
	function fetchData() {
		$result = $GLOBALS['database']->getRows( "website.slideshow", "", "position ASC", "" );
		$GLOBALS['debug']->notice( "Website slideshow data fetched" );
		if ( count( $result ) == 0 ) {
			return false;
		} else {
			return $result;
		}
	}

	/**
	 * Function for rendering the slideshow
	 * @return
	 *	String containing the slideshow code
	*/
	function renderSlideshow() {
		$result = $this->fetchData();
		$first = $result[0];
		$slide = "";

		if ( $result === false ) {
			return $slide;
		}

		$slide .= "<div style=\"position:relative;left:11px;top:-7px;background: url('".FILE_RENDER_PATH.UTIL::htmlspecialchars( $first['file_name'] )."') no-repeat;\" id=\"divSlideShowIndex\">";

		for ( $i = 0; $i < count( $result ); $i++ ) {
			$slide .= "<img src=\"".FILE_RENDER_PATH.UTIL::htmlspecialchars( $result[$i]['file_name'] )."\"
					id=\"car".( $i + 1 )."\"
					onclick=\"location='".UTIL::htmlspecialchars( $result[$i]['link'] )."';\"
					alt=\"".UTIL::htmlspecialchars( $result[$i]['alt'] )."\"
					style=\"".( ( $result[$i]['link'] != "" ) ? "cursor: pointer; " : "" )."visibility:hidden;\">";
		}

		$slide .= "</div>";
		$slide .= "<script type=\"text/javascript\">
			function _showCarouselImages(){
				for ( i = 1; i <= ".count( $result )."; i++ ){
					document.getElementById(\"car\" + i).style.visibility = \"visible\";
				}
				document.getElementById(\"divSlideShowIndex\").style.background = \"white\";
				document.getElementById(\"divSlideShowIndex\").style.backgroundColor = \"white\";
			}
		</script>";

		return $slide;
	}
}
?>