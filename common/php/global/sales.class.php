<?php
/**
 * @file sales.class.php
 * Provides CRM sales people functionality.
*/
class SALES {
	function __construct() {
		$this->database['crm'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
	}

	/**
	 * Fetch a sales person's data
	 * @param personId
	 *	Integer person id
	 * @return
	 *	Array RAW database row data ( see table crm.salespeople )
	*/
	function fetchSalesPersonInfo( $personId ) {
		$result = $this->database['crm']->getRow( "crm.salespeople", "person_id = '".(int)$personId."'" );
		return $result;
	}
}
?>