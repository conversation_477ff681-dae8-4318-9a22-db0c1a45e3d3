<?php
/**
 * @file caSuggestSoftware.class.php
*/

/**
 * Provides functionality for suggesting new software.
 * Requires misc.class.php and template.class.php
*/
class caSuggestSoftware {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for suggesting new software, will send a mail message and store the values in the database.
	 * @param values
	 *	Array of values to be added ( account_id, cst_id, name, question )
	 * @param isEscaped
	 *	Boolean if values are SQL escaped or not. Default to: false
	 * @return
	 *	Integer new case id
	*/
	function suggestSoftware( $values, $isEscaped = false ) {
		$from = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->suggestSoftwareMail['from'], array() );
		$to = $GLOBALS['template']->makeTemplate( $GLOBALS['misc']->suggestSoftwareMail['to'], array() );
		$subject = $GLOBALS['template']->makeTemplate( $values['software'], array() );
		$message = "Comments: ".$GLOBALS['template']->makeTemplate( $values['question'], array() )."\r\n";
		$name = $GLOBALS['template']->makeTemplate( $values['name'], array() );
		$email = $GLOBALS['template']->makeTemplate( $values['email'], array() );
		$message .= "Name: ".$name."\r\n"."Email: ".$email."\r\n".$GLOBALS['template']->makeTemplate( $values['link'], array() );
		$GLOBALS['misc']->sendMail( $from, $to, $subject, $message );
	}
}
?>