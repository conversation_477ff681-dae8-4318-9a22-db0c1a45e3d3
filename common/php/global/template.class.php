<?php
/**
 * @file template.class.php
*/

/**
 * Provides email sending template mechanism, regardless of which email.
 *
*/
class TEMPLATE {

	/**
	 * Function for generating a template email, or any other kind of template.
	 * @param templateSource
	 *	Template source, see misc.class.php for a sample template
	 * @param values
	 *	Array of values to be replace. Eg. $values['account_username'] will replace {account_username} in the template file. NOTE: htmlspecialchars is applied against the value.
	 * @param noSpecialChars
	 *	Boolean force no htmlspecialchars, by default false
	 * @return
	 *	String formatted template
	*/
	function makeTemplate( $templateSource, $values, $noSpecialChars = false ) {
		foreach ( $values as $key => $value ) {
			if ( $noSpecialChars === true ) {
				$templateSource = str_replace("{".$key."}", $value, $templateSource );
			} else {
				$templateSource = str_replace("{".$key."}", UTIL::htmlspecialchars( $value ), $templateSource );
			}
		}

		return $templateSource;
	}
}

?>