<?php

/**
 * errorHandler : logs the error encountered while execution fo the script.
 *
 * constructor : parameter optional.
 * If your not getting relavent data in $this object in this class, then you can pass the object/variable/array to the constructor.
 * Passed value will be stored in passedData variable.
 *
 * create an object of this function to invoke the callErrorHandle function.
 *
 */
class errorHandler {


	protected $logger = null;
	protected $passedData = null;
	
	
	/**
	* constructs the object
	*/
	public function __construct($data=array()) {
		$debug = Logger::create( 0 );
		$this->setLogger( $debug );
		if(!empty($data)){
			$this->passedData = $data;
		}
		register_shutdown_function(array($this, 'callErrorHandler'));
	}
	
	/*
	* Setter for the logger object
	* @param LoggerInterface $logger Use the logger to debug
	*/
	public function setLogger( Psr\Log\LoggerInterface $logger ) {
		$this->logger = $logger;
	}
	
	public function callErrorHandler() {
		$last_error = error_get_last();
		if(!empty($last_error)){
			//Prints the error that has occured
			$this->logger->notice("ErrorHandle : Error encountered while execution - ".print_r($last_error,true));

			//Print the data availabel in $this object which includes customer details and other passed data.
			$this->logger->notice("ErrorHandle : Error was encountered for this api data - ".print_r($this,true));

		}
	}
	
}
?>
