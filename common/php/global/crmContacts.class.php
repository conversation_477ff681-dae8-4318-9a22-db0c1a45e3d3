<?php
/**
 * @file crmContacts.class.php
 * Provides functionality for CRM contact handling.
 * Required: debug.class.php, database.class.php and connetion to CA database.
*/
class crmContacts {
	protected $database = array();

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
	}

	/**
	 * Function for adding a new contact in the CA database
	 * @param values
	 *	Array of values, see the above function
	 * @param isEscaped
	 *	Boolean true or false to secure the data or not. See database->edit()
	 * @return
	 *	False if the contact already exists
	*/
	function newContact( $values, $isEscaped = false ) {
		$partnerConstraint = "";
		if ( $values['partner_id'] != "" ) {
			$partnerConstraint = " AND partner_id = ".(int)$values['partner_id'];
		}
		$name = $values['name'];
		if ( $isEscaped == false ) {
			$name = $this->database['ca']->escapeString( $values['name'] );
		}
		if ( $this->database['ca']->numRows('ca.contacts', "name = '".$name."'".$partnerConstraint ) != 0 ) {
			return false;
		}
		$this->database['ca']->edit( "ca.contacts", $values, 0, $isEscaped );
		$GLOBALS['debug']->notice( "CA contact added" );
	}

	/**
	 * Function for updating a contact in the CA database. Similar to 'newContact' function, except for:
	 * @param id
	 *	Integer entry id
	 * @param isEscaped
	 *	Boolean true or false to secure the data or not. See database->edit()
	 * @param partner_id
	 *	Integer partner_id, optional person id to update ( OPTIONAL )
	 * @return
	 *	Boolean false if a contact by that name already exists in database
	*/
	function updateContact( $values, $id, $isEscaped = false, $partner_id = "" ) {
		$partnerConstraint = "";
		if ( $partner_id != "" ) {
			$partnerConstraint = " AND partner_id = '".(int)$partner_id."'";
		}
		$name = $values['name'];
		if ( $isEscaped == false ) {
			$name = $this->database['ca']->escapeString( $values['name'] );
		}
		if ( $this->database['ca']->numRows('ca.contacts', "name = '".$name."' AND id != '".(int)$id."'".$partnerConstraint ) != 0 ) {
			return false;
		}
		$this->database['ca']->edit( "ca.contacts", $values, 1, "id = '".$id."'".( $partner_id != "" ? " AND partner_id = '".$partner_id."'" : "" ), $isEscaped );
		$GLOBALS['debug']->notice( "CA contact updated" );
	}

	/**
	 * Function for deleting a contact
	 * @param id
	 *	Integer contact id
	 * @param partner_id
	 *	String partner id ( OPTIONAL )
	*/
	function deleteContact( $id, $partner_id = "" ) {
		$partner_id = ( $partner_id != "" ? "AND partner_id = '".(int)$partner_id."'" : "" );
		$this->database['ca']->query("DELETE FROM ca.contacts WHERE id = '".(int)$id."' ".$partner_id." LIMIT 1");
		$GLOBALS['debug']->notice( "CA contact deleted" );
	}
}

?>