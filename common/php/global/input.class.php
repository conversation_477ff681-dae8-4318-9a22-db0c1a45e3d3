<?php

// Constants required by this class
if ( !defined( 'MYSQL' ) ) {
	define( 'MYSQL', 0x01 );
}
if ( !defined( 'HTML' ) ) {
	define( 'HTML',  0x02 );
}

/**
 * @file input.class.php
 * Provides input securing input data. Somewhat similar to what magic_quotes_gpc would do, except that this operates on the _COOKIE, _POST and _GET arrays.
 * Constructor level configuration options: MYSQL and HTML flags. Not to be changed ( see GET_SERVER_VAR ).
 *
 * As of CSI7 magic quotes is disabled.
 */
class INPUT {
	/**
	 * @var bool
	 * Cache for the result of the {@link #isAgentRequest} method so repeat
	 * calls to it return the cache instead of re-generating the result.
	 * The field is initially set to null and a boolean value indicates a cached
	 * value.
	 */
	private $isAgentRequestCache = null;

	/**
	 * Constructor
	 */
	public function __construct()
	{
		// Repair the $_GET array if broken by special chars in the Distinguished Name
		if (!$this->isMethod('cli')) {
			$this->repairUnencodedDistinguishedName();
		}
	}

	/**
	 * Repair the $_GET array if it is broken by special characters in the
	 * Distinguished Name. OUs have no character restrictions and CSI Agents
	 * created from CSI 7.0.0.4 to 7.0.0.8 send unencoded DNs.
	 *
	 * @issue CSI-6121 Special characters in OU may break Agent Check request
	 * @see http://support.microsoft.com/kb/909264
	 */
	private function repairUnencodedDistinguishedName() {
		$isBrokenRequest =
			// It is an Agent Check request
			'agent_check' === $this->get('action')
			// and the dn= is present
			&& $this->get('dn')
			// and the uid= is missing (do to being eaten by the dn=)
			&& !$this->issetGet('uid');

		if (!$isBrokenRequest) {
			return;
		}

		// Repair the broken query string
		list(,$uri) = explode('?', $_SERVER['REQUEST_URI'], 2);
		if (preg_match('/dn=(.*)&uid=/', $uri, $matches)) {
			$uri = str_replace($matches[1], urlencode($matches[1]), $uri);
		}
		// Populate the $_GET array with new params
		parse_str($uri, $_GET);
	}

	/**
	 * Function for processing a string or array or strings.
	 *
	 * @param       $var    String string or array to be processed
	 * @param int   $nFlags String processing flags: 0 - for no processing, 0x01//MYSQL - for mysql_real_escape_string, 0x02//HTML - for htmlspecialchars
	 * @param array $a      The initial array (note - no reason to pass by reference)
	 * @return string String processed value
	 */
	function GET_SERVER_VAR( $var, $nFlags, $a) {
		if ( isset($a[$var]) ) {
			if ( $nFlags & MYSQL ) {//get_magic_quotes_gpc()
				// magic_quotes_gpc is Off, escape data to be inserted into DB
				$f = function ($v) { return str_replace(array("\\",  "\x00", "\n",  "\r",  "'",  '"', "\x1a"), array("\\\\","\\0","\\n", "\\r", "\'", '\"', "\\Z"), $v); }; //PHP72 mysql_real_escape_string
			} else if ( $nFlags & HTML ) {
				if ( 0 ) { //get_magic_quotes_gpc()
					// Data sanitized - remove slashes before preparing for HTML display
					$f = function ($a) { return htmlspecialchars(stripslashes($a)); }; //PHP72
				} else {
					$f = "htmlspecialchars";
				}
			}

			if ( isset($f) && is_callable($f) ) {
				// If $var[$a] is an array, run the function over all values and keys
				if ( is_array($a[$var]) ) {
					foreach ( array_keys($a[$var]) as $k ) {
						if ( !is_array( $a[$var][$k] ) ) {
							$a[$var][$f($k)] = $f($a[$var][$k]);
						} else {
							foreach ( array_keys( $a[$var][$k] ) as $z ) {
								$a[$var][$f($k)][$f($z)] = $this->GET_SERVER_VAR( $z, $nFlags, $a[$var][$k] );
							}
						}
						if ( $f($k) != $k ) {
							unset($a[$var][$k]);
						}
					}
				} else {
					// Value is not an array, convert it directly
					$a[$var] = $f($a[$var]);
				}
				return $a[$var];
			}
			return $a[$var];
		} else {
			return '';
		}
	}

	/**
	 * Returns a parameter from the superglobal POST variable. If the requested
	 * parameters hasn't been set then $defaultValue is returned.
	 *
	 * This is the non-static version of {@INPUT::RPOST()}
	 *
	 * @param string $var
	 * @param mixed $defaultValue
	 * @return mixed If no $defaultValue is specified and the parameter isn't found then an empty string is returned
	 */
	public function POST( $var, $defaultValue = '') {
		return ( isset( $_POST[$var] ) ? $_POST[$var] : $defaultValue );
	}

    /**
     *
     * Returns a parameter from the superglobal POST variable.
     * Return POST value or bool or int
     * @param $var
     * @param $defaultValue
     * @return $defaultValue if variable is not set or empty
     */
    public function DPOST( $var, $defaultValue) {
        if (isset( $_POST[$var])) {

            if (is_null($defaultValue)) {
                $value = $_POST[$var] !== '' ? $_POST[$var] : $defaultValue;
            }
            elseif (is_int($defaultValue)) {
                $value = (int)$_POST[$var];
            }
            elseif (is_bool($defaultValue)) {
                $value = (bool)$_POST[$var];
            }
            elseif (is_string($defaultValue)) {
                $value = (string)$_POST[$var];
            }

            return $value;
        }

        return $defaultValue;
    }

	/**
	 * Function for _POST
	 *
	 * @param string $var Key withing the $_POST array
	 * @return string String mysql escaped value
	 */
	function MPOST( $var ) {
		return $this->GET_SERVER_VAR( $var, MYSQL, $_POST );
	}

	/**
	 * Function for _POST
	 *
	 * @param string $var Key withing the $_POST array
	 * @return String html processed value
	*/
	function HPOST( $var ) {
		return $this->GET_SERVER_VAR( $var, HTML, $_POST );
	}

	/**
	 * Function for _POST
	 *
	 *
	 * @param string $var Key within the $_POST array
	 * @return Boolean true if parameter is defined, even if no value was passed, and false if not
	*/
	function issetPOST( $var ) {
		return ( isset( $_POST[$var]) ? true : false );
	}

	/**
	 * Checks if a $_POST variable has been set
	 *
	 * @param string $var Variable to look for within the $_POST array
	 * @return boolean True if the variable has been set, false otherwise
	 */
	public static function hasPOST( $var ) {
		return ( isset( $_POST[$var] ) ? true : false );
	}

	/**
	 * Returns a parameter from the superglobal GET variable. If the requested
	 * parameters hasn't been set then $defaultValue is returned.
	 * This is the non-static version of {@INPUT::RGET()}
	 *
	 * @param string $var
	 * @param mixed $defaultValue
	 *
	 * @return mixed
	 *	If no $defaultValue is specified and the parameter isn't found then an empty string is returned
	 */
	public function GET( $var, $defaultValue = '' ) {
		return ( isset( $_GET[$var] ) ? $_GET[$var] : $defaultValue );
	}

	/**
	 * Function for _GET
	 *
	 * @param $var String key withing the $_GET array
	 * @return string String mysql processed value
	 */
	function MGET( $var ) {
		return $this->GET_SERVER_VAR( $var, MYSQL, $_GET );
	}

	/**
	 * Function for _GET
	 *
	 * @param string $var Key within the $_GET array
	 * @return String html processed value
	*/
	function HGET( $var ) {
		return $this->GET_SERVER_VAR( $var, HTML, $_GET );
	}

	/**
	 * Function for _GET
	 *
	 *
	 * @param string $var Key within the $_GET array
	 * @return Boolean true if parameter is defined, even if no value was passed, and false if not
	 */
	function issetGET( $var ) {
		return ( isset( $_GET[$var]) ? true : false );
	}

	/**
	 * Checks if a $_GET variable has been set
	 * @param string $var Variable to look for within the $_GET array
	 * @return boolean True if the variable has been set, false otherwise
	 */
	public static function hasGET( $var ) {
		return ( isset( $_GET[$var] ) ? true : false );
	}

	/**
	 * Returns the ApiVersion requested by the daemon or csi
	 * @return string
	 * 	Returns the version as a string. If the version isn't found then string '0' is returned
	 * @note if you're using version_compare() with this method's output then ensure to account for the '0' return value
	 */
	public function getRequestedApiVersion()
     	{
		$apiVersion = '0';
		if ( !empty($_SERVER['HTTP_ACCEPT']) ) {
			if ( preg_match("/^application\/vnd\.secunia\.(?:csi|sccm)\.v([\d\.]{4,}\d)$/", $_SERVER['HTTP_ACCEPT'], $matches) ) {
				$apiVersion = $matches[1];
			}
		}
		return $apiVersion;
     	}

	/**
	 * Checks whether the current request is using the specific $method.
	 *
	 * @param string $method The Request method being tested for. This is not
	 *	case-sensitive, POST and post are treated equally.
	 *
	 * The valid $method values are below. The CSI only uses GET, POST and CLI.
	 *	<ul>
	 *		<li>GET</li>
	 *		<li>POST</li>
	 *		<li>PUT</li>
	 *		<li>HEAD</li>
	 *		<li>CLI</li>
	 *	</ul>
	 * @return mixed
	 */
	public function isMethod( $method ) {
		if ( 'cli' === PHP_SAPI ) {
			return 'cli' === strtolower( $method );
		}
		return ( strtoupper( $method ) === $_SERVER['REQUEST_METHOD'] );
	}

	/**
	 * Function for _COOKIE
	 *
	 * @param String $var Key withing the $_COOKIE array
	 * @return string String processed value
	 */
	function COOKIE( $var ) {
		return $this->GET_SERVER_VAR( $var, 0, $_COOKIE );
	}

	/**
	 * Function for _COOKIE
	 *
	 * @param string $var Key withing the $_COOKIE array
	 * @return String mysql processed value
	 */
	function MCOOKIE( $var ) {
		return $this->GET_SERVER_VAR( $var, MYSQL, $_COOKIE );
	}

	/**
	 * Function for _COOKIE
	 *
	 * @param String $var Key withing the $_COOKIE array
	 * @return String html processed value
	 */
	function HCOOKIE( $var ) {
		return $this->GET_SERVER_VAR( $var, HTML, $_COOKIE );
	}

	/**
	 * Function for _COOKIE
	 *
	 * @param String $var Key within the $_COOKIE array
	 * @return Boolean true if parameter is defined, even if no value was passed, and false if not
	 */
	function issetCOOKIE( $var ) {
		return ( isset( $_COOKIE[$var]) ? true : false );
	}

	/**
	 * Static function for _POST
	 *
	 * @param String $var Key withing the $_POST array
	 * @return String The actual value unaffected by magic_quotes_gpc. Null is returned if the query parameter does not exist
	 */
	public static function RPOST( $var ) {
		$value = null;
		if ( isset( $_POST[$var] ) ) {
			$value = $_POST[$var];
			if ( 0 ) { //get_magic_quotes_gpc()
				if ( is_array( $value ) ) {
					array_walk_recursive( $value, function( &$var ) {
						$var = stripslashes( $var );
					} );
				} else {
					$value = stripslashes( $value );
				}
			}
		}
		return $value;
	}

	/**
	 * Static function for _POST. Takes a list of keys we want to fetch from the _POST array and returns an array of
	 * the found values (handling magic_quotes_gpc if set).
	 *
	 * @param array $vars Array of String keys to read from $_POST array
	 * @return Array of values unaffected by magic_quotes_gpc
	 */
	public static function RPOSTVARS( $vars ) {
		$values = array();
		if ( is_array( $vars ) || $vars instanceof Traversable ) {
			foreach ( $vars as $var ) {
				if ( is_scalar( $var ) ){
					$val = self::RPOST( $var );
				} else {
					$val = self::RPOSTVARS( $var );
				}
				if ( isset( $val ) ) {
					$values[$var] = $val;
				}
			}
		}
		return $values;
	}
	/**
	 * Static function for _GET
	 *
	 * @param String $var Key withing the $_GET array
	 * @return String The actual value unaffected by magic_quotes_gpc.
	 * Null is returned if the query parameter does not exist
	 */
	public static function RGET( $var ) {
		$value = null;
		if ( isset( $_GET[$var] ) ) {
			$value = $_GET[$var];
			if ( 0 ) { //get_magic_quotes_gpc()
				if ( is_array( $value ) ) {
					array_walk_recursive( $value, function( &$var ) {
						$var = stripslashes( $var );
					} );
				} else {
					$value = stripslashes( $value );
				}
			}
		}
		return $value;
	}

	/**
	 * Static function for _GET. Takes a list of keys we want to fetch from the _GET array and returns an array of
	 * the found values (handling magic_quotes_gpc if set).
	 *
	 * @param Array $vars Array of String keys to read from $_GET array
	 * @return Array of values unaffected by magic_quotes_gpc
	*/
	public static function RGETVARS( $vars ) {
		$values = array();
		// checking if we can use foreach on the vars, which we can if its an array or an object that have inplemented a child interface of Traversable.
		if ( is_array( $vars ) || $vars instanceof Traversable ) {
			foreach ( $vars as $var ) {
				if ( is_scalar( $var ) ){
					$val = self::RGET( $var );
				} else {
					$val = self::RGETVARS( $var );
				}
				if ( isset( $val ) ) {
					$values[$var] = $val;
				}
			}
		}
		return $values;
	}


	const PAYLOAD_RESPONSE_JSON = 0;
	const PAYLOAD_RESPONSE_OBJECT = 1;
	const PAYLOAD_RESPONSE_ARRAY = 2;

	/**
	 * Static function for getting the request payload
	 *
	 * @param int $responseType Flag for json decoding the payload string.
	 * @return Array|String|Boolean
	 *    Failure: FALSE
	 *    Success: The payload string.
	 */
	public static function getPayload( $responseType = self::PAYLOAD_RESPONSE_JSON ) {
		$response = file_get_contents('php://input');

		if ( $response === false ) {
			return false;
		}

		if ( $responseType !== self::PAYLOAD_RESPONSE_JSON ) {
			$assoc = null;
			switch( $responseType ) {
			case self::PAYLOAD_RESPONSE_OBJECT:
				$assoc = false;
				break;
			case self::PAYLOAD_RESPONSE_ARRAY:
				$assoc = true;
				break;
			}
			$data = json_decode( $response, $assoc );
			if ( json_last_error() === JSON_ERROR_NONE ) {
				$response = $data;
			} else {
				$response = false;
			}
		}
		return $response;
	}

	// Identifiers for HTTP methods
	const GET = 0;
	const POST = 1;

	/**
	 * @param array $names  Parameter Names of the allowed columns
	 * @param integer $method Identifier of the HTTP Method: GET/POST
	 * @return Array An array of the GET/POST parameters.
	 */
	public static function getRawParams( array $names, $method = self::GET ) {
		$values = array();

		foreach ( $names as $name ) {
			$value = NULL;
			switch( $method ) {
			case self::GET:
				$value = isset( $_GET[$name] ) ? INPUT::RGET($name) : NULL;
				break;
			case self::POST:
				 $value = isset( $_POST[$name] ) ? INPUT::RPOST($name) : NULL;
				break;
			}
			if ( !is_null( $value ) ) {
				$values[$name] = $value;
			}
		}
		return $values;

	}

	public static function isCsvRequired() {
		if ( isset( $_POST['format'] ) && $_POST['format'] === 'csv' ) {
			return true;
		}
		if ( isset( $_GET['format'] ) && $_GET['format'] === 'csv' ) {
			return true;
		}
		return false;
	}

	/**
	 * Gets the HTTP User-Agent string sent with the current request
	 *
	 * @return string HTTP User-Agent string. If not set then an empty string is returned.
	 */
	public function getUserAgent() {
		return isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
	}

	/**
	 * Gets the HTTP ScanHashSha1 string sent with the current request
	 *
	 * @return string HTTP ScanHashSha1 string. If not set then an empty string is returned.
	 */
	public function getScanHashSHA1() {
		return isset($_SERVER['HTTP_SCANHASHSHA1']) ? $_SERVER['HTTP_SCANHASHSHA1'] : '';
	}

	/**
	 * Checks if the request is an XmlHttpRequest (Ajax) Request
	 *
	 * @return bool True if the request is a XmlHttpRequest request otherwise false
	 */
	public static function isXmlHttpRequest() {
		return ( isset( $_SERVER['HTTP_X_REQUESTED_WITH'] ) && 'XMLHttpRequest' == $_SERVER['HTTP_X_REQUESTED_WITH'] );
	}

	/**
	 * Checks if the request is from a Secunia Agent or not. This check is done by checking the User-Agent,
	 * the action being requested and whether the request comes with an Agent UID or not.
	 *
	 * @return bool True if the request is an Agent request, otherwise false
	 */
	public function isAgentRequest() {
		// A null value indicates the cache hasn't been generated so generate it
		if ( is_null( $this->isAgentRequestCache ) ) {
			// Check the User-Agent
			$currentUserAgent = isset( $_SERVER['HTTP_USER_AGENT'] ) ? substr( $_SERVER['HTTP_USER_AGENT'], 0, 17) : '';
			// User-Agents of our Secunia Agents
			$userAgents = array(
				'Secunia CSI Agent' // CSIA
				,'Secunia MSI' // MSI
			);
			// Actions that our Agents use
			$actions = array(
				'agent_check'
				,'data'
				,'status'
				,'target_request'
				,'progress_report'
				,'host_check'
				,'download'
				,'rules'
			);
			// Check if the request is an Agent request
			$this->isAgentRequestCache =
				// Check if the client has a matching User-Agent
				in_array( $currentUserAgent, $userAgents )
				// Check if the request is for an Agent action
				&& in_array( INPUT::RGET( 'action' ), $actions )
				// Check if the request has an Agent ID
				&& preg_match( '/^[0-9a-z]{64}$/i', INPUT::RGET( 'uid' ) );
		}

		return $this->isAgentRequestCache;
	}

	/**
	 * Checks if the request is from the Secunia Daemon or not. This check is done by checking the User-Agent.
	 *
	 * @return bool True if the request is a Daemon request, otherwise false
	 */
	public function isDaemonRequest() {
		$currentUserAgent = isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '';
		return ( 'Secunia Daemon' === $currentUserAgent || 'Corporate Software Inspector Daemon' == $currentUserAgent || 'Software Vulnerability Manager' == $currentUserAgent || 'Software Vulnerability Manager Patch Daemon' == $currentUserAgent);
	}

	public function isSVMClientToolKitRequest() {
		$currentUserAgent = isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '';
		return 'Software Vulnerability Manager Patch Daemon' == $currentUserAgent;

	}

	/**
	 * Checks if the request is from the SC2012 Plugin or not. This check is done by checking the URL path.
	 *
	 * @return bool True if the request is a SC2012 Plugin request, otherwise false
	 */
	public function isSccmPluginRequest() {
		// Checks for /sccm[0-9A-Za-z_-.] to allow match development paths
		return preg_match('/\/sccm[-\.\w]*\//i', parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
	}

	/**
	 * Checks if the request is issued from the CSI Binary itself via IPsiHttpRequest
	 * or http::Request and not from the web browser itself. Requests from the CSI Binary
	 * can be initiated from inside the binary or via JavaScript using the
	 * fWUICreateHttpRequest() method.
	 *
	 * @return bool True if the request is from the Binary otherwise false
	 */
	public function isCsiBinaryRequest() {
		// Check the User-Agent
		$currentUserAgent = isset( $_SERVER['HTTP_USER_AGENT'] ) ? substr( $_SERVER['HTTP_USER_AGENT'], 0, 17) : '';
		// User-Agents of our Secunia Agents
		$userAgents = array(
			'Secunia CSI' // CSI
			,'Secunia CSI Plugi' // CSI Plugin, it is purposely missing the last char so it's 17 bytes long
			,'Flexera Software ' // Name of CSI Plugin after rebranding, it is purposely missing the last characters so it's 17 bytes long
		);
		// Actions that our Agents use
		$actions = array(
			// From the CSI Binary
				'data'
				,'status'
				,'host_check'
				,'rules'
				,'plugin_check' // CSI Plugin only
				,'sccm' // Used by the ODBCScanner
				,'connection_check'

			// Via the CSI Javascript
				,'download'
				,'database_console'

			// From Network Appliance Agents
			/*
				,'target_request'
				,'progress_report'
				,'agent_check' // All Agents
			*/

			// From PSI
			/*
				,'settings'
				,'new_install'
			*/
		);
		// Check if the request is from the Binary
		return in_array( $currentUserAgent, $userAgents )
		// Check if the request is for an Allowed action
		&& in_array( $this->get( 'action' ), $actions )
		// Check if the request has A Session ID.
		&& preg_match( '/^[0-9a-z]{26,60}$/i', empty( $_SERVER['HTTP_SID'] ) ? '' : $_SERVER['HTTP_SID'] );
	}

	/**
	 * Checks if the request is comming from a PSI
	*/
	public function isPSIRequest() {
		return preg_match('/action=nsiapi_psi_data/i', empty( $_SERVER['REQUEST_URI']) ? '' : $_SERVER['REQUEST_URI']);
	}

	/**
	 * Volatile Sessions only exist in memory for the duration of the request
	 * and are used by requests that don't need requires persistent cookie sessions
	 *
	 * @todo When we implement a framework move this to a request property
	 * @return bool True if the request uses a volatile session otherwise false
	 */
	public function isVolatileSessionRequest() {
	    // Exception is that we want to prolong the session for SCCM or Agent while it's scanning, since that takes time
        // potentially longer than session timeout
		return ($this->isAgentRequest() && 'data' != $this->get('which'))
		|| $this->isPSIRequest()
		|| $this->isDaemonRequest()
		|| 'check_sms_enabled' == $this->get( 'action' )
		|| 'plugin_check' == $this->get( 'action' );
	}

	/**
	 * Function for determining if the CLI argument is a switch
	 *
	 * @param string $arg
	 * @return Bool True if it is a switch, false otherwise
	 */
	private static function isSwitch( $arg ) {
		if ( preg_match( '/^-{1,2}/', $arg ) ) {
			return true;
		}
		return false;
	}

	/**
	 * Function for getting the CLI Switch Values. This function will only return values for switches and will ignore
	 * values that don't have a switch associated with them.
	 *
	 * @return array  An associative array with keys as switch names and their switch values.
	 */
	public static function getCliSwitchValues() {
		global $argv;

		$values = array();
		$switches = array(); // adjency list to mark the switches

		/* Go through the array and determine if the arguments are
		 * switches or values for the switches. As we traverse the
		 * array we keep on updating the array of values in the
		 * following format:
		 *
		 * [ switch-name => value ]
		 *
		 * If no value is found, we use the boolean value for 'true'
		 */
		for ( $i = 0, $numArgs = count( $argv ); $i < $numArgs; $i++ ) {
			$arg = $argv[$i];
			if ( self::isSwitch( $arg ) ) {
				$switches[ $arg ] = true; // Marking this as a switch.
				$values[ $arg ] = true;
			} else {
				$switches[ $arg ] = false; // Marking this as a switch.

				/*
				 * If the argument is a value, we find the switch for
				 * it and update its value.
				 */
				$prevArg = isset( $argv[$i-1] ) ? $argv[$i-1] : null;
				if ( !$prevArg ) {
					continue;
				}

				/*
				 * We don't care if the previous argument isn't a
				 * switch
				 */
				if ( !$switches[ $prevArg ] ) {
					continue;
				}
				$values[ $prevArg ] = $arg;
			}
		}
		return $values;
	}


	/**
	 * Function for checking if the request made was by user or was a backgroud api request.
	 *
	 * @return bool True if user triggered api request, False otherwise
	 */
	public function isUserActionRequest() {
		if ( isset($_GET['action']) && isset($_GET['which']) ) {
			if ( ($_GET['action'] == 'smart_groups' && $_GET['which'] == 'zero_day_overview')
			    || ($_GET['action'] == 'smart_groups' && $_GET['which'] == 'menuSummary')
			    || ($_GET['action'] == 'activitylog' && $_GET['which'] == 'read')
			    || ($_GET['action'] == 'smart_groups' && $_GET['which'] == 'overview')
			    || ($_GET['action'] == 'sps_package' && $_GET['which'] == 'package_deployment')
			) {
				return false;
			}
		}
		return true;
	}
}
