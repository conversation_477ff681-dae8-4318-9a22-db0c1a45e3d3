<?php
/**
 * Created by korff.
 */

class CSVReporting extends REPORTING {

    function emailReport( $fileName, $emailAddress, $filePath, $reportTitle) {
        // Note - we don't validate the mail address at all here - we trust php's internal
        // handling of the 'to' field, which does do various validations. (Enhancement?)
        $to = $emailAddress;
        $from = (SERVER_EDITION == EDITION) ? NO_REPLY_EMAIL : '<EMAIL>';

        $subject = "Scheduled Report Generated - $fileName";
        $message = "<p>Please see the attachment. </p>";

        // A random hash is necessary to send mixed content
        $separator = md5(time());

        // carriage return type - we use a PHP end of line constant
        $eol = PHP_EOL;

        // encode data - put attachment in proper format
        $attachment = chunk_split( base64_encode(file_get_contents($filePath)) );

        // Main Header
        $headers = "From: " . $from . $eol;
        $headers .= "MIME-Version: 1.0" . $eol;
        $headers .= "Content-Type: multipart/mixed; boundary=\"" . $separator . "\"". $eol . $eol;
        
        //CSIL-9031 email not sent for attachment due to malformed headers
        // Message Body
        $mailBody  = "Content-Transfer-Encoding: 7bit" . $eol;
        $mailBody .= "This is a MIME encoded message." . $eol . $eol;
        $mailBody .= "This is a MIME encoded message." . $eol . $eol;
        $mailBody .= "--" . $separator . $eol;
        $mailBody .= "Content-Type: text/html; charset=\"iso-8859-1\"" . $eol;
        $mailBody .= "Content-Transfer-Encoding: 8bit".$eol . $eol;
        $mailBody .= $message . $eol . $eol;
        
        // Attachment
        $mailBody .= "--" . $separator . $eol;
        $mailBody .= "Content-Type: application/zip; name=\"" . $fileName. "\"" . $eol;
        $mailBody .= "Content-Transfer-Encoding: base64" . $eol;
        $mailBody .= "Content-Disposition: attachment" . $eol . $eol;
        $mailBody .= $attachment . $eol . $eol;
        $mailBody .= "--" . $separator . "--";
        
        // Send Message
        if (mail( $to, $subject, $mailBody, $headers )) {
        	$GLOBALS['debug']->info( "Reporting - CSV report with filename ".$fileName." sent successful to email-id ".$to );
        } else {
        	$GLOBALS['debug']->error( "Reporting -CSV report with filename ".$fileName." failed for email-id ".$to." error details: ".print_r(error_get_last(),true) );
        }

    }

    public function generateReport( $accountId, $reportId, $timeNow = false, $cstId = false ) {
        $response = array( "error" => self::UNEXPECTED_ERROR );
        $accountId = (int) $accountId;
        $reportId = (int) $reportId;

        if ( !$reportId || $reportId <= 0) {
            $response['error'] = self::NO_REPORT_ID;
            $GLOBALS['debug']->error( "Reporting - Error - incorrect reportId for generating ReportID:" .$reportId );
            return $response;
        }

        if ( !$accountId || $accountId <= 0) {
            $GLOBALS['debug']->error( "Reporting - Error - incorrect accountId for generating ReportID:" .$reportId );
            return $response;
        }

        // check if configuration_options_id exists
        $configurationId = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'configuration_options_id', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" ) );

        if ( !$configurationId || $configurationId <= 0 ) {
            $response['error'] = self::NO_CONFIGURATION_ID;
            $GLOBALS['debug']->error( "Reporting - ReportID:" .$reportId. " Error - configuration ID not found" );
            return $response;
        }

        if ( false === $timeNow ) {
            $timeNow = $this->database['common']->getCurrentUTCDateTime();
        }

        if ( false === $cstId ) {
            $cstId = $this->database['common']->getRowValue( 'ca.accounts', 'cst_id', "account_id = '" . (int) $accountId . "'" );
        }

        if ( !defined('PHP_PATH') ) {
            $GLOBALS['debug']->critical( "Reporting - Error - constant PHP_PATH is not defined ReportID:" .$reportId );
            return $response;
        }

        if ( !file_exists(PHP_PATH) ) {
            $GLOBALS['debug']->critical( "Reporting - Error - File does not exist (" . PHP_PATH . ") ReportID:" .$reportId );
            return $response;
        }

        if ( !defined('CRONJOBS_PATH') ) {
            $GLOBALS['debug']->critical( "Reporting - Error - constant CRONJOBS_PATH is not defined ReportID:" .$reportId );
            return $response;
        }

        $filePath = '';

        if ( defined('REPORTING_CRONJOBS_PATH') ) {
            $filePath = REPORTING_CRONJOBS_PATH . 'csv_report.php';
        } else {
            $filePath = CRONJOBS_PATH . '/csv_report.php';
        }

        if ( !file_exists($filePath) ) {
            $GLOBALS['debug']->critical( "Reporting - Error - File does not exist ($filePath) ReportID:" .$reportId );
            return $response;
        }

        $genReportCommand = escapeshellcmd( PHP_PATH ) . ' ' . escapeshellcmd( $filePath ) . ' ' . (int) $reportId . ' ' . (int) $accountId . ' ' . escapeshellarg( $timeNow ) . ' ' . (int) $cstId;

        $GLOBALS['debug']->notice( "Running on demand cron: " . $genReportCommand );

        UTIL::tStart();
        $output = shell_exec( $genReportCommand );

        $GLOBALS['debug']->notice( 'Reporting - CSV ReportID:'. $reportId .' AccountID: '. $accountId .'Execution time: ' . round(UTIL::tShow(), 2) .' s' );
        
        $response['error'] = self::SUCCESS;
        return $response;
    }

} 
