<?php
/**
 * @file license.class.php
 * Provides licensing mechanisms.
 * Requires util.class.php, database.class.php and misc.class.php
*/

/**
 * Secunia license management class.
 *
 * Configuration options (copy paste):
 *
 * @code
 *	define('MOD_NONE',    0x0000);
 *	define('MOD_SM',      0x0001);
 *	define('MOD_ESM',     0x0002);
 *	define('MOD_VTS',     0x0004);
 *	define('MOD_VTSE',    0x0008);
 *	define('MOD_VSS',     0x0010);
 *	define('MOD_NSI',     0x0020);
 *	define('MOD_BA',      0x0040);
 *	define('MOD_VDB',     0x0080);
 *	define('MOD_SUPPORT', 0x0200);
 *	define('MOD_ACCOUNT', 0x0400);
 *	define('MOD_UM',      0x0800);
 * @endcode
*/
class LICENSE {
	protected $database = array();

	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );

		if ( defined( 'DB_HOST_CRM' ) && defined( 'DB_USER_CRM' ) && defined( 'DB_PASS_CRM' ) ) {
			$this->database['crm'] = new DATABASE( DB_HOST_CRM, DB_USER_CRM, DB_PASS_CRM );
		} else {
			$this->database['crm'] = new DATABASE( DB_HOST, DB_USER, DB_PASS );
		}
	}

	/**
	 * Function for generating a license key.
	 * @param hash
	 *	String license hash
	 * @return
	 *	String license key
	*/
	function generateKey( $hash ) {
		$key = '';
		$rows = 0;

		// Loop until a unique key is generated
		do {
			for ( $i = 1; $i <= 25; $i++ ) {
				$key .= $hash[rand(0, strlen($hash) - 1)];
				if ( $i % 5 == 0 && $i != 25 ) {
					$key .= "-";
				}
			}
			$rows = $this->database['ca']->numRows("ca.license_keys", "license = '" . $key . "'");
		} while ( $rows );
		$GLOBALS['debug']->notice( "License key generated" );

		return $key;
	}

	/**
	 * Function adding a new license, to an account.
	 * @param values
	 *	Array of values, see DATABASE->edit()
	 * @param isEscaped
	 *	Boolean see DATABASE->edit()
	 * @return
	 *	Boolean true
	*/
	function addLicense( $values, $isEscaped = false ) {
		$this->database['ca']->edit( "ca.license_keys", $values, 0, "", $isEscaped );
		$GLOBALS['debug']->notice( "License key added" );

		return true;
	}

	/**
	 * Fetch user licenses, based on account and type.
	 * @param accountID
	 *	Integer account id
	 * @param type
	 *	Integer license type. See documentation for configuring MOD_*
	 * @param utc
	 *	Boolean Use utc instead of local time
	 * @return
	 *	Array licenses and properties
	*/
	function getAccountLicenses( $accountID, $type ) {

		// Array to return
		$licenses = array();
		$licenses['info'] = array();
		$licenses['info']['used_user'] = 0;
		$licenses['info']['used'] = 0;
		$licenses['info']['total'] = 0;
		$licenses['info']['expires'] = 0;

		// Number of hosts assigned to the user by its parent account (returns 0 for parent account)
		if ( $type == MOD_NSI ) {
			$assigned = $this->database['ca']->getRowValue("ca.nsi_base_settings", "assigned_hosts",
				"account_id = '" . (int)$accountID . "'");
			$licenses['info']['assigned'] = $assigned;
		} else if ( $type == MOD_VTSE ) {

			$assigned = $this->database['ca']->getRowValue("ca.license_assignments", "quantity",
				"account_id = '" . (int)$accountID . "' && type = '" . intval($type) . "'");
			$licenses['info']['assigned'] = $assigned ? $assigned : 0;
		}

		// Licenses can only be bound to root-level ESM accounts, find root-level account's ID
		$account = $this->database['ca']->getRow("ca.accounts", "account_id = '" . (int)$accountID . "'");

		if ( $account['account_esm'] ) {
			// Find root-level account ID
			do {
				$rootAccountID = $account['account_esm'];
				$account = $this->database['ca']->getRow("ca.accounts", "account_id = '" . (int)$rootAccountID . "'");
			} while ( $account['account_esm'] );
			$rows = $this->database['ca']->getRows("ca.license_keys", "account_id = '". (int)$rootAccountID. "' && type = '" . intval($type) . "'", "valid_to DESC");
		} else {
			$rows = $this->database['ca']->getRows("ca.license_keys", "account_id = '". (int)$accountID."' && type = '" . intval($type) . "'", "valid_to DESC");
		}

		// Calculations for root-level ESM account are as follows:
		//   assigned:  0 (root-level esm accounts aren't assigned licenses)
		//   used:      sum of hosts tied to active licenses for any user
		//   used_user: sum of hosts tied to active licenses for the current user
		//   total:     sum of 'quantity' for all active licenses
		//   free:      total - used
		//
		// Calculations for sub-accounts are as follows:
		//   assigned:  assigned_hosts from nsi_base_settings for user
		//   used:      sum of hosts tied to active licenses for any user
		//   used_user: sum of hosts tied to active licenses for the current user
		//   total:     min(parent's total licenses - used, assigned_hosts) + used_user
		//   free:      total - used_user
		//   subuser:   flag to indicate this is a sub-account
		foreach ( $rows as $row ) {

			$utc = $GLOBALS['util']->isUtcTime();
			$row['ts_valid_from'] = $GLOBALS['util']->MySQLDatetimeToTimestamp( $row['valid_from'], $utc );
			$row['ts_valid_to'] = $GLOBALS['util']->MySQLDatetimeToTimestamp( $row['valid_to'], $utc );

			$ts_now = time();

			// Warn for licenses expiring within the next 30 days
			$row['ts_remaining'] = $row['ts_valid_to'] - $ts_now;
			$row['days_remaining'] = intval($row['ts_remaining'] / (60*60*24));

			// Get value of the longest lasting license key
			if ( $row['days_remaining'] > $licenses['info']['expires'] ) {
				$licenses['info']['expires'] = ( $row['days_remaining'] > 0 ? $row['days_remaining'] : 0 );
			}

			if ( $ts_now >= $row['ts_valid_to'] ) {
				// License expired
				$row['expired'] = true;
				$row['status'] = "Inactive";
			} else if ( $ts_now < $row['ts_valid_from'] ) {
				// Licensing not yet active
				$row['pending'] = true;
				$row['status'] = "Pending";
			} else if ( $row['days_remaining'] <= 30 ) {
				// License expires soon
				$row['warn'] = true;
				$row['active'] = true;
				$row['status'] = $row['days_remaining'] . " day" . ($row['days_remaining'] == 1 ? "" : "s");
			} else {
				// License active
				$row['active'] = true;
				$row['status'] = $row['days_remaining'] . " day" . ($row['days_remaining'] == 1 ? "" : "s");
			}

			if ( $type == MOD_NSI ) {
				// Number of hosts bound to this license for all users
				$row['licensed'] = $this->database['ca']->numRows("ca.license_hosts", "license_id = '" . $row['id'] . "'");
			} else if ( $type == MOD_BA ) {
				// Number of used BA licenses
				$licensedExploits = $this->database['ca']->getRows("ca.license_exploits", "license_id = '" . $row['id'] . "'");
				$row['licensed'] = 0;
				foreach ( $licensedExploits as $licensedExploit ) {
					$row['licensed'] += $licensedExploit['credits'];
				}
			} else if ( $type == MOD_VTSE ) {
				// Number of used VTSE licenses
				$licensedAssets = $this->database['ca']->getRows("ca.license_vtse_assets", "license_id = '" . $row['id'] . "'");
				$row['licensed'] = 0;
				foreach ( $licensedAssets as $licensedAssets ) {
					$row['licensed']++;
				}
			}

			if ( isset( $row['active'] ) && $row['active'] ) {
				// Sum up all used for the total number of licenses. For sub-accounts that have a cap on
				// the number of total licenses, this will be calculated after all licenses are processed
				$licenses['info']['total'] += $row['quantity'];

				// 'used' licenses takes all licensed entities into account
				$licenses['info']['used'] += $row['licensed'];

				// 'used_user' licenses takes only current user's licensed items into account
				if ( $type == MOD_NSI ) {
					$licenses['info']['used_user'] += $this->database['ca']->numRows("ca.license_hosts",
						"license_id = '" . $row['id'] . "' && account_id = '" . (int)$accountID . "'");
				} else if ( $type == MOD_VTSE ) {
					$licenses['info']['used_user'] += $this->database['ca']->numRows("ca.license_vtse_assets",
						"license_id = '" . $row['id'] . "' && account_id = '" . (int)$accountID . "'");
				}
			}

			// Copy the license to aLicenses using the license ID as the key
			$licenses[$row['id']] = $row;
		}

		// Free licenses = total licenses - used licenses
		$licenses['info']['free'] = $licenses['info']['total'] - $licenses['info']['used'];

		if ( isset($rootAccountID) ) {  // sub-account
			// Cap 'total' licenses at either the total number of free licenses or the number of
			// licenses assigned to the user, whichever is less
			$licenses['info']['total'] = min($licenses['info']['free'], $licenses['info']['assigned']);

			if ( $licenses['info']['free'] < $licenses['info']['assigned'] ) {
				// Add back to total licenses the number of licenses in use by the current user
				$licenses['info']['total'] += $licenses['info']['used_user'];
			}

			$licenses['info']['subuser'] = true;

			// Recalculate free licenses using new total and licenses in use by the current user
			$licenses['info']['free'] = $licenses['info']['total'] - $licenses['info']['used_user'];
		}

		return $licenses;
	}
}