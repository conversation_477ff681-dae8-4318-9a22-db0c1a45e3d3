<?php
/**
 * @file websiteFileUpload.class.php
 * Provides global functionality for downloading a file, through the website or CMS.
 *
*/
class websiteFileUpload {

	/**
	 * Fetch file data
	 * @param fileName
	 *	String file name
	 * @param grid
	 *	Boolean if true fetch only data needed by the grid ( all columns except file - which has the file content )
	 * @return
	 *	Array containing file(s) data
	*/
	function fetchData( $fileName = "", $grid = false, $isEscaped = false ) {
		if ( $isEscaped !== true ) {
			$fileName = $GLOBALS['database']->escapeString( $fileName );
		}
		$result = $GLOBALS['database']->getRows( "website.files", ( ( $fileName != "" ) ? "file_name = '".$fileName."'" : "" ), ( ( $fileName == "" ) ? "file_name ASC" : ""), ( ( $fileName != "" ) ? "1" : ""), ( ( $grid == true ) ? array( "id", "file_name", "file_type", "file_size", "date", "user" ) : "" ) );
		if ( count( $result ) == 0 ) {
			return false;
		} else {
			return $result;
		}
	}

	/**
	 * Function for downloading a file stored in the database.
	 * @param fileName
	 *	String file name
	*/
	function getFile() {
		$result = $this->fetchData( $GLOBALS['input']->MGET('file'), false, true );
		if ( $result !== false ) {
			$GLOBALS['util']->render64( $result[0]['file_name'], $result[0]['file_type'], $result[0]['file'] );
		} else {
			$GLOBALS['util']->render64( "","","" );
		}
	}
}
?>