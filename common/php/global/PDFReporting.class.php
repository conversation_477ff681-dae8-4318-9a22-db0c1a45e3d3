<?php
/**
 * Created by korff.
 */

class PDFReporting extends REPORTING {
    public $reportType = 'pdf';

    function emailReport( $fileName, $emailAddress, $pdfDocument, $reportTitle) {
        // Note - we don't validate the mail address at all here - we trust php's internal
        // handling of the 'to' field, which does do various validations. (Enhancement?)
        $to = $emailAddress;
        $from = (SERVER_EDITION == EDITION) ? NO_REPLY_EMAIL : '<EMAIL>';

        $subject = "Scheduled Report Generated - $fileName";
        $message = "<p>Please see the attachment. </p>";

        // A random hash is necessary to send mixed content
        $separator = md5(time());

        // carriage return type - we use a PHP end of line constant
        $eol = PHP_EOL;

        // Main Header
        $headers = "From: " . $from . $eol;
        $headers .= "MIME-Version: 1.0" . $eol;
        $headers .= "Content-Type: multipart/mixed; boundary=\"" . $separator . "\"". $eol . $eol;
        
        //CSIL-9031 email not sent for attachment due to malformed headers
        // Message Body
        $mailBody  = "Content-Transfer-Encoding: 7bit" . $eol;
        $mailBody .= "This is a MIME encoded message." . $eol . $eol;
        $mailBody .= "This is a MIME encoded message." . $eol . $eol;
        $mailBody .= "--" . $separator . $eol;
        $mailBody .= "Content-Type: text/html; charset=\"iso-8859-1\"" . $eol;
        $mailBody .= "Content-Transfer-Encoding: 8bit".$eol . $eol;
        $mailBody .= $message . $eol . $eol;
        
        // Attachment
        $mailBody .= "--" . $separator . $eol;

        if ($this->reportType === 'zip') {
            // encode data - put attachment in proper format
            $attachment = chunk_split( base64_encode(file_get_contents($pdfDocument)) );
            $mailBody .= "Content-Type: application/zip; name=\"" . $fileName. "\"" . $eol;
        }else{
            // encode data - put attachment in proper format
            $attachment = chunk_split( base64_encode($pdfDocument) );
            $mailBody .= "Content-Type: application/octet-stream; name=\"" . $fileName. "\"" . $eol;
        }
        $mailBody .= "Content-Transfer-Encoding: base64" . $eol;
        $mailBody .= "Content-Disposition: attachment" . $eol . $eol;
        $mailBody .= $attachment . $eol . $eol;
        $mailBody .= "--" . $separator . "--";
        
        // Send Message
        if (mail( $to, $subject, $mailBody, $headers )) {
        	$GLOBALS['debug']->info( "Reporting - PDF report with filename ".$fileName." sent successful to email-id ".$to );
        } else {
        	$GLOBALS['debug']->error( "Reporting -PDF report with filename ".$fileName." failed for email-id ".$to." error details: ".print_r(error_get_last(),true));
        }

    }

    public function generateReport( $accountId, $reportId, $timeNow = false, $cstId = false ) {
        $response = array( "error" => self::UNEXPECTED_ERROR );
        $accountId = (int) $accountId;
        $reportId = (int) $reportId;

        if ( !$reportId || $reportId <= 0) {
            $response['error'] = self::NO_REPORT_ID;
            $GLOBALS['debug']->error( "Reporting - Error - incorrect reportId for generating ReportID:" .$reportId );
            return $response;
        }

        if ( !$accountId || $accountId <= 0) {
            $GLOBALS['debug']->error( "Reporting - Error - incorrect accountId for generating AccountID:". $accountId. " ReportID:" .$reportId );
            return $response;
        }

        // check if configuration_options_id exists
        $configurationId = $this->database['common']->getRowValue( 'ca.enhanced_reporting_schedule' , 'configuration_options_id', ( " account_id = '" . (int) $accountId . "' AND id = '" . (int) $reportId . "'" ) );

        if ( !$configurationId || $configurationId <= 0 ) {
            $response['error'] = self::NO_CONFIGURATION_ID;
            $GLOBALS['debug']->error( "Reporting - Error - configuration ID not found ReportID:" .$reportId );
            return $response;
        }

        if ( false === $timeNow ) {
            $timeNow = $this->database['common']->getCurrentUTCDateTime();
        }

        if ( false === $cstId ) {
            $cstId = $this->database['common']->getRowValue( 'ca.accounts', 'cst_id', "account_id = '" . (int) $accountId . "'" );
        }

        if ( !defined('PHP_PATH') ) {
            $GLOBALS['debug']->critical( "Reporting - Error - constant PHP_PATH is not defined ReportID:" .$reportId );
            return $response;
        }

        if ( !file_exists(PHP_PATH) ) {
            $GLOBALS['debug']->critical( "Reporting - Error - File does not exist (" . PHP_PATH . ") ReportID:" .$reportId );
            return $response;
        }

        if ( !defined('CRONJOBS_PATH') ) {
            $GLOBALS['debug']->critical( "Reporting - Error - constant CRONJOBS_PATH is not defined ReportID:" .$reportId );
            return $response;
        }

        $filePath = '';

        if ( defined('REPORTING_CRONJOBS_PATH') ) {
            $filePath = REPORTING_CRONJOBS_PATH . 'er_report.php';
        } else {
            $filePath = CRONJOBS_PATH . '/er_report.php';
        }

        if ( !file_exists($filePath) ) {
            $GLOBALS['debug']->critical( "Reporting - Error - File does not exist ($filePath) ReportID:" .$reportId );
            return $response;
        }

        $genReportCommand = escapeshellcmd( PHP_PATH ) . ' ' . escapeshellcmd( $filePath ) . ' ' . (int) $reportId . ' ' . (int) $accountId . ' ' . escapeshellarg( $timeNow ) . ' ' . (int) $cstId;

        $GLOBALS['debug']->notice( "Reporting - ReportID:" .$reportId. " Running on demand cron: " . $genReportCommand );

        UTIL::tStart();
        $output = shell_exec( $genReportCommand );

        $GLOBALS['debug']->notice( 'Reporting - PDF ReportID:'. $reportId .' AccountID: '. $accountId .' Execution time: ' . round(UTIL::tShow(), 2) .' s ') ;

        $response['error'] = self::SUCCESS;
        return $response;
    }

    function generateTocOutput( $pdfInput, $tocData ) {

        $this->pdf = $pdfInput;

        // Note - columns must be big enough to hold any toc keys without line breaking them
        // or else the toc strings/page numbers totally breaks.
        $aCols = array( 'c0' => array( 'width' => '5%' )
        ,'c1' => array( 'width' => '70%' )
        ,'c2' => array( 'width' => '25%' ) );


        // For each row in tocData, it is assumed there is an array with at least one of
        // c0, c1, or c2 set up with data.
        foreach ( $tocData as $thisEntry ) {
            if ( !isset($thisEntry['c0']) ) {
                $thisEntry['c0'] = ' ';
            }
            if ( !isset($thisEntry['c1']) ) {
                $thisEntry['c1'] = ' ';
            }
            if ( !isset($thisEntry['c2']) ) {
                $thisEntry['c2'] = ' ';
            }
        }

        $this->pdf->PDFLn(2);
        $this->pdf->PDFSetFont('', 'B', 20);
        $this->pdf->PDFCell(0, 10, "Table of Contents", 0, 1, 'C');
        $this->pdf->PDFLn(2);

        $this->pdf->PDFBeginInputBox();
        $this->pdf->PDFHTMLOut( $this->pdf->buildPDFDataTable($aCols, $tocData, DT_NONE)."<br>" );
        $this->pdf->PDFEndInputBox();
    }


    // ---------------------------------------------------------
    //  GENERAL UTILITY FUNCTIONS SHARED BY VARIOUS MODULES
    //    Note: Not product specific (eg. VIM, CSI, etc.)
    // ---------------------------------------------------------

    // In the case a section title is too long, enter an optional short version in
    // $contentsEntry to show in the table of contents
    // keyString should be something unique and module specific, but short
    // i.e. toc_adv, toc_tix, etc.
    // Added an option for if we are only printing a TOC entry, and not a section title in the pdf.
    function sectionTitleAndTocEntry( $pdf, $keyString, $sectionTitle, $optionToken, $contentsEntry = '', $skipSectionTitle = false ) {

        // Print the section title (unless specifically requested not to)
        if ( !$skipSectionTitle ) {
            $pdf->PDFHTMLOut( "<b>" . $sectionTitle . "</b><p>" );
        }

        // Set up the section title in the TOC with its page number
        $pageNum = (int) $pdf->PageNo();
        $pageNum = $pageNum - 1;
        $tocPage = $keyString . $optionToken . '_';
        $tocTitle = $keyString . 'st_' . $optionToken . '_'; // section title
        $pdf->PDFSubstituteString( $tocPage, $pageNum );
        if ( $contentsEntry ) {
            $pdf->PDFSubstituteString( $tocTitle, $contentsEntry );
        } else {
            $pdf->PDFSubstituteString( $tocTitle, $sectionTitle );
        }
    }

    // Print the section title and text paragraph included.  Extra space is there as a parameter
    // because sometimes we are showing a graph or table right after the text, in which case we do
    // NOT want extra space.
    // This function is also used specifically when we do NOT want a TOC entry
    function printSectionTitleAndText( $sectionTitle, $introText, $extraSpace = true, $forceLineBreaks = 0 ) {
        $this->pdf->PDFHTMLOut( "<b>" . $sectionTitle . "</b><p>" );
        $this->pdf->PDFHTMLOut( $introText );
        $lineBreaks = $forceLineBreaks ? $forceLineBreaks : ( $extraSpace ? 3 : 2 );
        $this->pdf->PDFLn( $lineBreaks );
    }

    function constructTocData( $parsedOptions, $possibleOptions, $keyString, $sectionHeader ) {

        $tocData = array();

        if ( 0 < count($parsedOptions) ) {
            // If any of the possible options are selected, we want to publish this as a section header in the TOC
            $tocData[0] = array();
            $tocData[0]['c1'] = "<b>" . $sectionHeader . "</b>";
            $i = 0;
            foreach ( $parsedOptions as $keyWord => $value ) {
                if ( in_array( $keyWord, $possibleOptions ) ) {
                    $i += 1;
                    if ( isset($tocData[$i]) ) {
                        $tocData[$i] = array();
                    }

                    $tocPage = $keyString . $keyWord . '_';
                    $tocTitle = $keyString . 'st_' . $keyWord . '_';  // section title
                    $tocData[$i]['c1'] = $tocTitle;
                    $tocData[$i]['c2'] = $tocPage;
                }
            }
            // Always put an extra blank element in after to leave a space
            // before the next section starts
            $i = count($tocData);
            $tocData[$i] = array();
        }
        return $tocData;
    }


} 
