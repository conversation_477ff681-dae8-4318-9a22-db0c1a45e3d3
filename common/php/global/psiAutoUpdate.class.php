<?php
/**
 * @file psiAutoUpdate.class.php
 *
 * PSI auto update functionality shared between PSI and CSI
 *
 */
class psiAutoUpdate {


	/**
	 * isAutoUpdatable checks the criteria for PSI auto update
	 *
	 * @param: aApp - an array containing the appropriate rows from
	 * sr_product_secure (easy_to_patch, truly_direct_download,
	 * sua_test, silent_intall_parameters, direct_download) as well as
	 * 'path' pointing to the install path of the product.
	 *
	 * @return: true if autoupdatable else false
	 */
	function isAutoUpdatable( $aApp ) {
		// Localised versions of the "Program Files" system variable
		$sEasyToPatchPaths = 'arquivos de programas|archivos de programa|program files \\(x86\\)|program files|programmer|programme|programmi|programas|programfiler|programs|programm|program|Ohjelmatiedostot|windows|winnt';

		// Init as not auto-updatable
		$res = false;

		// A program is Auto-Updatable if:
		// 1) 'easy_to_patch' is > 0
		// 2) 'truly_direct_download' is > 0
		// 3) 'sua_tests' is > 0
		// 4) 'silent_install_parameters' is > 0
		// 5) strlen('direct_download') > 5
		// 6) 'sEasyToPatchPaths' is found in path

		if (
			array_key_exists('easy_to_patch',$aApp) &&
			array_key_exists('truly_direct_download',$aApp) &&
			array_key_exists('sua_tests',$aApp) &&
			array_key_exists('silent_install_parameters',$aApp) &&
			array_key_exists('direct_download',$aApp) &&
			array_key_exists('path',$aApp) &&
			$aApp['easy_to_patch'] > 0 &&
			$aApp['truly_direct_download'] > 0 &&
			$aApp['sua_tests'] > 0 &&
			strlen($aApp['silent_install_parameters']) > 0 &&
			strlen($aApp['direct_download']) > 5 &&
			preg_match('/\\\\(' . $sEasyToPatchPaths . ')\\\\/i', $aApp['path'])
		) {
			$res = true;
		}
		return $res;
	}

	// DEPRECATED - function with lower case u and spelling mistake in name is being deprecated - leaving this wrapper here so we don't have to
	// change it in the psi_20 and psi branches (and maybe other places) immediately
	function isAutoupdateble( $aApp ) {
		return $this->isAutoUpdatable( $aApp );
	}

	/* Logic stolen from SmallBusiness. For PSI 3 */
	function psi3WillAutoUpdate( $isSecure, $isEol, $vendorId, $msKbs
								 ,$trulyDirectDownload, $directDownloadLink, $sha1, $silentInstallParameters
								 ,$isMultiLingual) {
		if( $isSecure ) {
			return false;
		}
		if ( $directDownloadLink && ( strpos( $directDownloadLink, 'update.microsoft.com' ) !== false ) ) { // WUA
			return false;
		}
		if( $isMultiLingual ) {
			return false;
		}
		if( $msKbs && !$isEol ) { // WUA
			return false;
		}

		return $directDownloadLink && $trulyDirectDownload && $sha1 && $silentInstallParameters;
	}
}
