<?php
/**
 * @file widgets.class.php
*/

/**
 * Provides basic widget functionality.
 * NOTE: Widget in the website context is the frontpage box. In the ExtJS Based Project context it is a portlet.
*/
class WIDGETS {
	function __construct() {
		$this->database['ca'] = new DATABASE( DB_HOST_CA, DB_USER_CA, DB_PASS_CA );
#ifdef CA_SITE
		$this->database['website'] = new DATABASE( DB_HOST_WEBSITE, DB_USER_WEBSITE, DB_PASS_WEBSITE );
#endif
	}

	/**
	 * Function for fetching highlighted advisories.
	 * @param count
	 *	Integer how many
	 * @return
	 *	Array of vulnerability id's
	*/
	function fetchHighlightedAdvisories( $count ) {
		$data = $this->database['ca']->queryGetRows("SELECT
				top_stories.vuln_id
				,top_stories.image
				,top_stories.image_text
				,vuln.vuln_title
				,vuln.vuln_critical_boolean
				,vuln.vuln_create_date
				,vuln.vuln_released
			FROM
				vuln_track.top_stories
			LEFT JOIN
				vuln_track.vuln
			ON
				vuln.vuln_id = top_stories.vuln_id
			WHERE
				vuln.vuln_status != 4
			ORDER BY
				place ASC
		LIMIT 0,".(int)$count );

		$GLOBALS['debug']->notice( "Highlighted Advisories Widget data fetched" );
		return $data;
	}
}