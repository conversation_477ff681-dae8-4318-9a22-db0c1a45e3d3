<?php
/**
 * @file databaseImporterMysqlLinux.class.php
 */

/**
 * This class is used to specifically import files into the
 * mysql databases under Linux environment
 */
class databaseImporterMysqlLinux implements databaseImporter {
	private $host;
	private $user;
	private $password;
	private $database;
	private $loginConfig='/file/that/doesnt/exist';

	/**
	 * Constructor
	 */
	function __construct() {
		//Provide defaults for constant used in this class to make it fully independent
		//This also serves as reference
		if ( !defined( 'ERR_CODE_BINLOG_IMPORT' ) ) {
			define( 'ERR_CODE_BINLOG_IMPORT', 750395 );
		}
		if ( !defined( 'ERR_CODE_DUMP_IMPORT' ) ) {
			define( 'ERR_CODE_DUMP_IMPORT', 750396 );
		}
		$this->loginConfig = getenv('HOME') . '/.login.cnf';
	}

	/**
	 * Magic method to set-up the server connection details
	 * We use a setter so that the members are not public
	 */
	function __set( $name, $value ) {
		if ( property_exists( 'databaseImporterMysqlLinux', $name ) ) {
			$this->$name = $value;
		}
	}

	/**
	 * Based on a given path this function will try
	 * to import the given path's binlog into the
	 * configured mysql database
	 * @param string $path
     * @throws Exception
	 */
	public function executeBinlog( $path ) {
		$path = escapeshellarg( $path );
		$database = escapeshellarg( $this->database );
		# This file gets created in installation script, and if file doesn't exists, it means we are in VA
		# (debian build) in this case. VA has password in encrypted format, which gets dynamically decrypted in runtime,
		# which we don't have access to while we are installing the application. Therefore, we support both paths with
		# code forks over here.
		# More correct approach would be to always create the file and not pass user and pass to bash commands. From
		# MySQL 5.6 file can be encrypted, which would satisfy VA, since it already encrypts password. So until we have
		# exclusive support for MySQL 5.6+ we cannot support it, since the password would be unencrypted.
		if (file_exists($this->loginConfig)) {
			$output = `mysqlbinlog $path --database=$database 2>& 1| mysql --defaults-file={$this->loginConfig} $database 2>&1`;
		} else {
			$host = escapeshellarg($this->host);
			$user = escapeshellarg($this->user);
			$password = strlen($this->password) > 0 ? ' --password=' . escapeshellarg($this->password) : '';
			$output = `mysqlbinlog $path --database=$database 2>& 1| mysql -h $host -u $user $password $database 2>&1`;
		}

		//Check to see if there are any elements in the array that captures the execution output
		if ( !empty( $output ) ) {
			throw new Exception( 'The binlog import failed for ' . $path . '. The output of the operation is: ' . $output, ERR_CODE_BINLOG_IMPORT );
		}
	}

	/**
	 * Based on a given path this function will try
	 * to import the given path's dump into the
	 * configured mysql database
	 * @param string $path
     * @throws Exception
	 */
	public function executeDump( $path ) {
		$escapedPath = escapeshellarg( $path );
		$database = escapeshellarg( $this->database );
		//Below code gets executed if PAAS platform is selected
		if (defined('DB_PAAS') && DB_PAAS) {
			exec("sed -i -e 's/MyISAM/InnoDB/g' '".$escapedPath."'");
		}
		# view the comment in executeBinlog method
		if (file_exists($this->loginConfig)) {
			$output = `mysql --defaults-file={$this->loginConfig} $database < $escapedPath 2>&1`;
		} else {
			$host = escapeshellarg($this->host);
			$user = escapeshellarg($this->user);
			$password = strlen($this->password) > 0 ? ' --password=' . escapeshellarg($this->password) : '';
			$output = `mysql -h $host -u $user $password $database < $escapedPath 2>&1`;
		}

		if ( !empty( $output ) ) {
			if ( file_exists( $path ) ) {
				unlink( $path );
			}
			throw new Exception( 'The dump import failed for ' . $escapedPath . '. The output of the operation is: ' . $output, ERR_CODE_DUMP_IMPORT );
		}
	}
}
