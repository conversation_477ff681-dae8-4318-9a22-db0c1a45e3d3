<?php

/**
 * The interface will be a common ground for 
 * linux and windows distrs and also allow
 * future database support
 */
interface databaseImporter {

	/**
	 * Import a binlog into the database
	 * @param path {string} the location on disk of the binlog
	 */
	public function executeBinlog( $path );

	/**
	 * Import a dump into the database
	 * @param path {string} the location on disk of the dump
	 */
	public function executeDump( $path );

}
