<?php

/**
 * Class SimpleHTTPRequst
 *
 * Simple class for wrapping HTTP post and get requests using
 * file_get_contents in a simple class interface
 *
 * @since 15.07.14
 * <AUTHOR>
 */

class SimpleHTTPRequest {
	protected $cookie;
	protected $response = "";
	protected $baseurl = "";
	protected $verbose = "";
	protected $ch;

	function __construct($baseurl = "", $verbose = false) {
		$this->ch = curl_init();
		$this->cookieFile = tempnam("/tmp", "http-cookie-");
		$this->baseurl = $baseurl;
		$this->verbose = $verbose;
	}

	function __destruct() {
		curl_close( $this->ch );
		unlink($this->cookieFile);
	}

	protected function buildGetParams($getParams) {
		$func = function($item, $key) {return "$key=$item"; };
		$r = implode("&",array_map($func, $getParams, array_keys($getParams)));
		return $r;
	}

	private function curlLoadPage($method, $url, $getParams, $content = NULL) {
		$url = $this->baseurl . $url . $this->buildGetParams($getParams);
		curl_setopt($this->ch, CURLOPT_URL, $url);
		curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookieFile);
        //to go trough fiddler
        //curl_setopt($this->ch, CURLOPT_PROXY, '127.0.0.1:8888');
		//curl_setopt($this->ch, CURLOPT_VERBOSE, true);

		if ($method == 'POST'){
			curl_setopt( $this->ch, CURLOPT_POST, 1 );
		}
		if ( $content !== NULL ) {
			curl_setopt( $this->ch, CURLOPT_POSTFIELDS, $content );
		}
		if($this->verbose) {
			echo "Requesting ($method) $url\n";
			echo "Post data ";
			var_dump($content);
		}
		$this->response = curl_exec( $this->ch );
		if( $this->response ===  FALSE ) {
			if($this->verbose) {
				echo "Request failed " . curl_error($this->ch) . "\n";
			}
			return 0;
		}
		$httpReturn = curl_getinfo( $this->ch, CURLINFO_HTTP_CODE );
		if($this->verbose) {
		 	echo "Return code $httpReturn\n";
		}
		return $httpReturn;
	}

	/**
	 * Get response from previous request
	 *
	 * @return Raw data from previously executed request
	 */
	public function getResponse() {
		return $this->response;
	}

	/**
	 *
	 * @param array postParams  associative array of post parameters
	 * @param array getParams  associative array of get parameters
	 *
	 * @return Http error code or 0 on failed request
	 */
	public function post($postParams, $getParams = array(), $url = "") {
		return $this->curlLoadPage('POST', $url, $getParams, $postParams);
	}

	/**
	 *
	 * @param array getParams  associative array of get parameters
	 *
	 * @return Http error code or 0 on failed request
	 */
	public function get($getParams = array(), $url = "") {
		return $this->curlLoadPage('GET', $url, $getParams);
	}
}

class LoginHTTPRequest extends SimpleHTTPRequest{
	protected $username;
	protected $password;
	protected $uid = ""; // Session token
	protected $account;

	function __construct($username, $password, $baseurl="", $verbose = false) {
		parent::__construct($baseurl, $verbose);
		$this->username = $username;
		$this->password = $password;
	}

	public function getAccount() {
		return $this->account;
	}
	public function login() {
		$post = array ("username" => "$this->username", "password" => "$this->password");
		$get = array("action" => "manuallogin");
		$httpCode = parent::post($post, $get);

		if ($httpCode != 200) {
			return false;
		}
		$jsonResponse = json_decode(parent::getResponse(), true);
		$this->uid = $jsonResponse["uid"];

		$httpCode = $this->get(array("action"=>"checklogin"));
		if ($httpCode != 200) {
			return false;
		}
		$json = json_decode(parent::getResponse(), true);
		$this->account = $json;

		return true;
	}

	public function post($postParams, $getParams = array(), $url = "") {
		if (! array_key_exists('uid', $getParams)) {
			$getParams['uid'] = $this->uid;
		}
		return parent::post($postParams, $getParams, $url);
	}

	public function get($getParams = array(), $url = "") {
		if (! array_key_exists('uid', $getParams)) {
			$getParams['uid'] = $this->uid;
		}
		return parent::get($getParams, $url);
	}
}
